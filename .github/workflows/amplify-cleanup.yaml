name: Scheduled Amplify Preview Branches Cleanup

on:
  schedule:
    - cron: "0 */6 * * *"

  workflow_dispatch:
    inputs:
      branch-substring:
        description: 'Branch substring to filter remove. Eg. "nmq"'
        default: "nmq"
        required: false

permissions:
  id-token: write
  contents: read

jobs:
  prepare-matrix:
    runs-on: ubuntu-latest
    outputs:
      appIds: ${{ steps.set-appIds.outputs.appIds }}
    steps:
      - id: set-appIds
        run: |
          appIds=$(echo '[
            "dmmns8s1lobsz",
            "d2rhwh5sa9jmhi",
            "d1vlfpei1tktik",
            "d23z2wyvd07g2o",
            "d3sp3eglwcay59",
            "d2gjvnfrie6ud4"
          ]' | jq -c .)
          echo "appIds=$appIds" >> $GITHUB_OUTPUT

  cleanup:
    needs: prepare-matrix
    strategy:
      matrix:
        app-id: ${{ fromJson(needs.prepare-matrix.outputs.appIds) }}
      fail-fast: false

    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github/actions/setup-aws-credentials
            .github/scripts/amplify

      - name: Set up AWS credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"
          cache: pip
          cache-dependency-path: .github/scripts/amplify/requirements.txt

      - name: Install dependencies
        working-directory: .github/scripts/amplify
        shell: bash
        run: |
          pip install -r requirements.txt

      - name: Run branch cleanup
        working-directory: .github/scripts/amplify
        run: |
          python app_cleanup.py --app-id ${{ matrix.app-id }} --branch-substring "${{ github.event.inputs.branch-substring || 'nmq' }}"
