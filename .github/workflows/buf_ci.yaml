# This is a GitHub Actions workflow file that uses the buf-action to run Buf CI.
name: Buf CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened, labeled, unlabeled]

permissions:
  contents: read
  pull-requests: write

jobs:
  check-changes:
    runs-on: ubuntu-latest
    outputs:
      proto_changed: ${{ steps.filter.outputs.proto }}
    steps:
      - uses: actions/checkout@v4

      - name: Check relevant file changes
        id: filter
        uses: dorny/paths-filter@v3
        with:
          filters: |
            proto:
              - 'src/proto/**'
              - '**/buf.yaml'
              - '**/buf.lock'

  buf:
    name: Run Buf CI
    needs: check-changes
    if: needs.check-changes.outputs.proto_changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Run Buf CI
        uses: bufbuild/buf-action@v1
        with:
          push: false
          format: false
          lint: false