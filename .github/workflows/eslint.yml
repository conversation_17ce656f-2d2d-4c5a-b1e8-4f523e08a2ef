name: ESLint & Prettier

permissions:
  contents: read
  pull-requests: write

on:
  pull_request:

jobs:
  changes:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read
    outputs:
      files: ${{ steps.filter.outputs.files }}
    steps:
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            files:
              - src/nirvana/client/packages/**
              - src/nirvana/client/apps/**
              - src/nirvana/client/package.json
              - src/nirvana/client/yarn.lock
              - .github/workflows/eslint.yml
  eslint:
    needs: [changes]
    if: ${{ needs.changes.outputs.files == 'true' }}
    name: ESLint Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            src/nirvana/client
            .github
      - name: Install yarn packages
        uses: ./.github/actions/setup-frontend-apps-node
      - uses: reviewdog/action-eslint@v1
        with:
          fail_level: any
          reporter: github-pr-review
          workdir: src/nirvana/client/
          eslint_flags: "{apps,packages}/**/*.{ts,tsx,js,jsx}"

  prettier:
    needs: [changes]
    if: ${{ needs.changes.outputs.files == 'true' }}
    name: Prettier Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            src/nirvana/client
            .github
      - name: Install yarn packages
        uses: ./.github/actions/setup-frontend-apps-node
      - uses: EPMatt/reviewdog-action-prettier@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          reporter: github-pr-review
          level: warning
          workdir: src/nirvana/client/
          prettier_flags: "{apps,packages}/**/*.{ts,tsx,js,jsx}"
