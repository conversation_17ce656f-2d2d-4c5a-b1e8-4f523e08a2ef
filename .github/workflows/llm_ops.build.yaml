name: Build LLM Ops Docker Image

on:
  workflow_call:
    inputs:
      artifact-name:
        required: true
        type: string

      aws-account-id:
        required: true
        type: string
      
      code-directory:
        required: true
        type: string

jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            ${{ inputs.code-directory }}
            .github

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials
        with:
          aws-account-id: ${{ inputs.aws-account-id }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build image and export
        uses: docker/build-push-action@v6
        with:
          tags: ${{ inputs.artifact-name }}:latest
          build-args: |
            USE_PULL_THROUGH_CACHE=true
            ECR_REGISTRY=${{ steps.login-ecr.outputs.registry }}/public-docker-hub/library
          outputs: type=docker,dest=/tmp/${{ inputs.artifact-name }}.tar
          context: ${{ inputs.code-directory }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.artifact-name }}
          path: /tmp/${{ inputs.artifact-name }}.tar
