name: PDF Create Build

on:
  push:
    branches: [main]
    paths:
      - "src/nirvana/pdfgen/pdf-create/**"
  pull_request:
    branches: [main]
    paths:
      - "src/nirvana/pdfgen/pdf-create/**"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "yarn"
          cache-dependency-path: "src/nirvana/pdfgen/pdf-create/yarn.lock"

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache
            **/node_modules
          key: ${{ runner.os }}-${{ runner.arch }}-modules-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ runner.arch }}-modules-

      - name: Install dependencies
        working-directory: src/nirvana/pdfgen/pdf-create
        run: yarn install --frozen-lockfile

      - name: Build
        working-directory: src/nirvana/pdfgen/pdf-create
        run: yarn build
