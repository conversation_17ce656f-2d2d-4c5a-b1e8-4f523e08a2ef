name: Send workflow metrics to Datadog

permissions: read-all

on:
  workflow_run:
    workflows:
      - "PostgreSQL test"
      - "Go CI"
      - "Infra CI"
      - "Python CI"
      - "Run database migration(s)"
      - "Redeploy application service(s)"
      - "Re-deploy Jobber processor"
      - "Auto-generate CODEOWNERS and raise Pull Request"
    types:
      - completed

jobs:
  send_workflow_metrics_to_datadog:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: int128/datadog-actions-metrics@v1
        with:
          # create an API key in https://docs.datadoghq.com/account_management/api-app-keys/
          datadog-api-key: ${{ secrets.DATADOG_API_KEY }}
          collect-job-metrics: true
          collect-step-metrics: true
