name: Infra CI

on:
  merge_group:
    types: [checks_requested]
  pull_request:
    branches: [main]

permissions:
  actions: read
  checks: read
  contents: read
  deployments: read
  issues: write
  packages: read
  pull-requests: write
  repository-projects: read
  security-events: read
  statuses: read
  id-token: write

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

jobs:
  # JOB to run change detection
  changes:
    runs-on: ubuntu-latest
    outputs:
      core_infra: ${{ steps.filter.outputs.core_infra }}
      app: ${{ steps.filter.outputs.app }}
      cdktf: ${{ steps.filter.outputs.cdktf }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            core_infra:
              - 'src/deployment/core_infra/**/*.tf'
            app:
              - 'src/deployment/app/**/*.tf'
            cdktf:
              - 'src/infra/**'
              - added|deleted: 'src/ds/models/**/*.py'
              - 'src/deployment/common-modules/**'

  tflint:
    name: runner / tflint
    needs: changes
    if: ${{ needs.changes.outputs.core_infra == 'true' || needs.changes.outputs.app == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Clone repo
        uses: actions/checkout@master

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials

      # Set up Terraform manually as Docker-based GitHub Actions are
      # slow due to lack of caching.
      # Note: Terraform is not directly needed for tflint, but to be able to
      # use the --modules flag, we need to run terraform init first.
      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.7.5

      # Set up tflint
      - name: Set up TFLint
        uses: terraform-linters/setup-tflint@v4
        with:
          tflint_version: v0.53.0

      # TODO: add problem matcher support

      # lint core_infra
      - name: Lint Core Infra
        if: needs.changes.outputs.core_infra == 'true'
        uses: ./.github/actions/tf_ci
        with:
          module-directory: "src/deployment/core_infra"

      # - name: Core infra tf plan & post comment
      #   if: needs.changes.outputs.core_infra == 'true'
      #   uses: ./.github/actions/tf-plan-and-post-result
      #   with:
      #     module-directory: "src/deployment/core_infra"
      #     github-token: ${{ secrets.GITHUB_TOKEN }}

      # lint app
      - name: Lint App
        if: needs.changes.outputs.app == 'true'
        uses: ./.github/actions/tf_ci
        with:
          module-directory: "src/deployment/app"

      # - name: app tf plan & post comment
      #   if: needs.changes.outputs.app == 'true'
      #   uses: ./.github/actions/tf-plan-and-post-result
      #   with:
      #     module-directory: "src/deployment/app"
      #     github-token: ${{ secrets.GITHUB_TOKEN }}

  # Change detection for CDKTF
  cdktflint:
    name: run_cdktflint
    needs: changes
    if: ${{ needs.changes.outputs.cdktf == 'true'}}
    runs-on: ubuntu-latest
    steps:
      - name: Clone repo
        uses: actions/checkout@master

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.7.5

      - uses: actions/setup-node@v4
        with:
          node-version: v20.14.0
          cache: "yarn"
          cache-dependency-path: src/infra/yarn.lock

      # Run yarn install
      - name: Yarn Install
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        shell: bash
        working-directory: ./src/infra
        run: "yarn install --immutable"

      # Run Yarn Lint
      - name: Yarn Pre-Commit
        shell: bash
        working-directory: ./src/infra
        run: "yarn precommit"

      # Run cdktf synth for checked-in generated files
      - name: CDKTF Synth
        shell: bash
        working-directory: ./src/infra
        run: yarn synth

      - name: Git status
        id: git-status
        run: |
          echo "num-lines=$(git status --porcelain=v1 | wc -l)" >> $GITHUB_OUTPUT
          git diff
        shell: bash

      # Fail if git worktree is dirty
      - name: Fail if worktree is dirty
        if: ${{ steps.git-status.outputs.num-lines != '0' }}
        shell: bash
        run: |
          git status --porcelain=v1
          exit 1
