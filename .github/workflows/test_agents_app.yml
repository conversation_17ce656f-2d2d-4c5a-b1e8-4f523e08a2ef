name: Agents FE Tests

permissions: read-all

on:
  push:
    paths:
      - 'src/nirvana/client/packages/quoting/**'
      - .github/workflows/test_agents_app.yml

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Cypress run
        uses: cypress-io/github-action@v6.7.2
        with:
          working-directory: src/nirvana/client/packages/quoting
          build: yarn build
          start: yarn start
          wait-on: 'http://localhost:1110'
          record: true
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.AGENTS_CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
