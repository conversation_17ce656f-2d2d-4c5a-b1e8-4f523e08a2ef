name: Run TF Plan
on:
  pull_request:
permissions: write-all

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

env:
  # Note: to release a new version run task release-executable:tftools
  TFTOOLS: terraform_7bca0ddf6c
  TFLOCKKEY: gha_tf_plan

  SNAPSHEET_VPC_GITHUB_ALLOWLIST_SG: "sg-093f22497f355b0bd"
jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      changed_dirs: ${{ steps.process-dirs.outputs.changed_dirs }}
    steps:
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            deployment_dirs_changes:
              - "src/deployment/**/*.tf"
          list-files: json

      - name: Process directory paths
        id: process-dirs
        run: |
          if [[ "${{ steps.filter.outputs.deployment_dirs_changes }}" == "true" ]]; then
            # Get unique directories from the changed files.
            DIRS=$(echo '${{ steps.filter.outputs.deployment_dirs_changes_files }}' | 
                  jq -r '.[]' | 
                  xargs dirname | 
                  sort -u | 
                  jq -R -s -c 'split("\n") | map(select(length > 0))'
                )
          else
            DIRS="[]"
          fi
          echo "changed_dirs=$DIRS" >> $GITHUB_OUTPUT

  tf-plan:
    needs: changes
    if: ${{ needs.changes.outputs.changed_dirs != '[]' }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        directory: ${{ fromJson(needs.changes.outputs.changed_dirs) }}
      max-parallel: 1
    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: get runner ip address
        id: ip
        uses: haythem/public-ip@v1.3

      - name: Add runner ip address to security group
        id: update-sg
        run: |
          aws ec2 authorize-security-group-ingress \
            --group-id $SNAPSHEET_VPC_GITHUB_ALLOWLIST_SG \
            --protocol tcp \
            --port 5432 \
            --cidr ${{ steps.ip.outputs.ipv4 }}/32

      - name: Setup terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.7.5
          terraform_wrapper: false

      - name: Run terraform init
        working-directory: ${{ matrix.directory }}
        run: |
          terraform init

      - name: Setup tftools
        run: |
          aws s3 cp s3://cloud.nirvanatech.com/tools/"$TFTOOLS" $HOME/.local/bin/"$TFTOOLS" 
          chmod +x $HOME/.local/bin/"$TFTOOLS"

      - name: Acquire remote lock
        id: lock
        run: $TFTOOLS lock ${TFLOCKKEY}_${{ github.run_id }}_${{ github.run_attempt }}

      - name: Get planfile
        id: tfplan
        working-directory: ${{ matrix.directory }}
        run: |
          set -e
          planfile=$(mktemp)

          # Calculate the correct relative path to scripts directory
          # Count how many directories deep we are from the repo root
          DIR_DEPTH=$(echo "${{ matrix.directory }}" | tr -cd '/' | wc -c)
          
          # Create a string with the right number of "../" segments
          REL_PATH=$(printf '../%.0s' $(seq 1 $DIR_DEPTH))
          SCRIPTS_PATH="${REL_PATH}scripts"
          
          ${SCRIPTS_PATH}/DONOT_RUN_MANUALLY_terraform plan \
            -input=false -out=$planfile > /dev/null

          terraform show -no-color $planfile > plan_output.txt

          # Escape output for multiline environment variable
          echo "PLAN_OUTPUT<<EOF" >> $GITHUB_ENV
          cat plan_output.txt >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Find Comment
        if: ${{ always() }}
        uses: peter-evans/find-comment@v3
        id: find-comment
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: "github-actions[bot]"
          body-includes: "# Terraform Plan for ${{ matrix.directory }}"

      - name: On success comment
        if: ${{ steps.tfplan.conclusion == 'success' }}
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.find-comment.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          body: |
            # Terraform Plan for ${{ matrix.directory }}
            ```tf
            ${{ env.PLAN_OUTPUT }}
            ```
          edit-mode: replace

      - name: On failure comment
        if: ${{ failure() }}
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.find-comment.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          body: |
            # Terraform Plan for ${{ matrix.directory }}
            
            Couldn't generate the plan file. Please check the logs for more details.
          edit-mode: replace

      - name: Release remote lock
        if: ${{ always() && steps.lock.conclusion == 'success' }}
        run: $TFTOOLS unlock ${TFLOCKKEY}_${{ github.run_id }}_${{ github.run_attempt }}

      - name: Remove runner ip address from security group
        if: ${{ always() && steps.update-sg.conclusion == 'success' }}
        run: |
          aws ec2 revoke-security-group-ingress \
            --group-id $SNAPSHEET_VPC_GITHUB_ALLOWLIST_SG \
            --protocol tcp \
            --port 5432 \
            --cidr ${{ steps.ip.outputs.ipv4 }}/32
