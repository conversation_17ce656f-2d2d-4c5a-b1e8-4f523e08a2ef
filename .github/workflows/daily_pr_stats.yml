name: Report daily merged PR stats to Datadog
on:
  workflow_dispatch:
  schedule:
    - cron: "0 8 * * *"
permissions:
  contents: read

jobs:
  report:
    name: Send daily PR stats to Datadog
    runs-on: ubuntu-latest
    permissions:
      issues: read
      pull-requests: read
    steps:
      - name: Get last day's date
        id: dates
        shell: bash
        run: echo yesterday=$(date -d 'yesterday' '+%Y-%m-%d') >> $GITHUB_OUTPUT

      - name: Run issue-metrics tool
        id: metrics
        uses: github/issue-metrics@v3
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SEARCH_QUERY: "repo:${{ github.repository }} is:pr draft:false created:${{ steps.dates.outputs.yesterday }} -author:app/nirvanamq -author:app/nirvanacodegenbot -author:app/dependabot"
          ENABLE_MENTOR_COUNT: true
          MIN_MENTOR_COUNT: 1

      - name: Send metrics to Datadog
        uses: masci/datadog@v1
        with:
          api-key: ${{ secrets.DATADOG_API_KEY }}
          logs: |
            - hostname: "github.com"
              source: "github-actions"
              service: "pr-daily-stats"            
              message: "{
                \"num_items_opened\":${{ steps.metrics.outputs.metrics && fromJson(steps.metrics.outputs.metrics).num_items_opened || 0 }},
                \"num_items_closed\":${{ steps.metrics.outputs.metrics && fromJson(steps.metrics.outputs.metrics).num_items_closed || 0 }},
                \"num_mentor_count\":${{ steps.metrics.outputs.metrics && fromJson(steps.metrics.outputs.metrics).num_mentor_count || 0 }}
              }"
