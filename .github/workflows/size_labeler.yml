name: labeler

on: [pull_request]

jobs:
  labeler:
    permissions:
      pull-requests: write
      contents: read
      issues: write
    runs-on: ubuntu-latest
    name: Label the PR size
    steps:
      - uses: codelytv/pr-size-labeler@v1
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          xs_label: "size/xs"
          xs_max_size: "20"
          s_label: "size/s"
          s_max_size: "200"
          m_label: "size/m"
          m_max_size: "1000"
          l_label: "size/l"
          l_max_size: "2000"
          xl_label: "size/xl"
          fail_if_xl: "false"
          message_if_xl: >
            This PR exceeds the recommended size of 2000 lines.
            Please make sure you are NOT addressing multiple issues with one PR.
            Note this PR might be rejected due to its size.
          github_api_url: "https://api.github.com"
          files_to_ignore: "*_test.go *.pb.go **/db_models/*.go schema.json BUILD.bazel **/generated/** **/generated.go **/*_generated.go **/mock_*.go **/openapi-specs/**/spec.go **/graphql-types.ts **/client/packages/api/**/*.ts **/yarn.lock"
