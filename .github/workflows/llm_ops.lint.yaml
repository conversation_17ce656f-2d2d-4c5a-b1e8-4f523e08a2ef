name: <PERSON>t with Ruff

on:
  workflow_call:
    inputs:
      artifact-name:
        required: true
        type: string

jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ inputs.artifact-name }}
          path: /tmp

      - name: Load image
        run: docker load --input /tmp/${{ inputs.artifact-name }}.tar

      - name: Lint
        run: docker run ${{ inputs.artifact-name }} uv run ruff check
