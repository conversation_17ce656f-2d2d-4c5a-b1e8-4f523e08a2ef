import os
from datetime import datetime
from github import Github
from datadog_api_client import ApiClient, Configuration
from datadog_api_client.v2.api.metrics_api import MetricsApi
from datadog_api_client.v2.model.metric_intake_type import MetricIntakeType
from datadog_api_client.v2.model.metric_payload import MetricPayload
from datadog_api_client.v2.model.metric_point import MetricPoint
from datadog_api_client.v2.model.metric_resource import MetricResource
from datadog_api_client.v2.model.metric_series import MetricSeries

RUNNER_SHUTDOWN_MESSAGE = "The runner has received a shutdown signal"
METRIC_NAME = "github.actions.runner_shutdown"

def send_dd_metric(api_key, tags, time: datetime):
    body = MetricPayload(
        series=[
            MetricSeries(
                metric=METRIC_NAME,
                type=MetricIntakeType.COUNT,
                points=[
                    MetricPoint(
                        timestamp=int(time.timestamp()),
                        value=1,
                    ),
                ],
                tags=tags,
            ),
        ],
    )

    print(f"Sending metric {METRIC_NAME} to Datadog with tags: {tags}")

    configuration = Configuration()
    configuration.api_key['apiKeyAuth'] = api_key
    with ApiClient(configuration) as api_client:
        api_instance = MetricsApi(api_client)
        response = api_instance.submit_metrics(body=body)

        print("Datadog API reponse", response)

def main():
    github_token = os.environ["GITHUB_TOKEN"]
    datadog_api_key = os.environ["INPUT_DATADOG_API_KEY"]

    github = Github(github_token)
    repo = github.get_repo(os.environ["GITHUB_REPOSITORY"])
    run_id = int(os.environ["GITHUB_RUN_ID"])
    print(f"Checking run {run_id} for runner shutdown.")

    workflow_run = repo.get_workflow_run(run_id)
    workflow_jobs = workflow_run.jobs()
    check_suite = repo.get_check_suite(workflow_run.check_suite_id)
    check_runs = check_suite.get_check_runs()

    for job in workflow_jobs:
        print(f"Checking job: {job.name} for runner shutdown, job node ID: {job.node_id}")
        
        check_runs_for_job = [check_run for check_run in check_runs if check_run.node_id == job.node_id]
        if len(check_runs_for_job) != 1:
            raise Exception(f"Each job must have exactly one check_run, found {len(check_runs_for_job)} for {job.name}.")
        check_run = check_runs_for_job[0]

        print(f"Check run: {check_run.name}. Node ID: {check_run.node_id}. Conclusion: {check_run.conclusion}")
        if check_run.conclusion != "failure":
            continue
        annotations = check_run.get_annotations()
        runner_shutdown_annotations = [a for a in annotations if RUNNER_SHUTDOWN_MESSAGE in a.message]
        if not runner_shutdown_annotations:
            print(f"No runner shutdown annotations found for job {job.name}.")
            continue
        
        print(f"Runner shutdown detected in job {job.name}.")
        tags = [
            f"workflow_id:{run_id}",
            f"workflow_name:{os.environ['GITHUB_WORKFLOW']}",
            f"branch:{workflow_run.head_branch}",
            f"job_name:{job.name}",
            f"job_id:{job.id}",
        ]
        # emit metric
        send_dd_metric(datadog_api_key, tags, job.completed_at)
        print(f"Metric sent for job {job.name} due to runner shutdown.")

    print("Finished checking all jobs for runner shutdowns.")

if __name__ == "__main__":
    main()
