import argparse
import boto3
import time

def remove_branches(app_id: str, branch_substring: str = "nmq") -> None:
    """Remove branches containing 'branch_substring' from Amplify apps"""

    amplify_client = boto3.client("amplify", region_name="us-east-2")


    branches = amplify_client.list_branches(appId=app_id, maxResults=50)["branches"]

    print(f"Found {len(branches)} branches")

    filtered_branches = [
        branch["branchName"]
        for branch in branches
        if branch_substring in branch["branchName"].lower()
    ]

    if len(filtered_branches) == 0:
        print("No branches to delete")

    for branch_name in filtered_branches:
        try:
            print(f"Deleting branch: {branch_name} from app {app_id}")
            amplify_client.delete_branch(appId=app_id, branchName=branch_name)
        except Exception as e:
            print(f"Failed to delete branch {branch_name}: {e}")
        time.sleep(1)



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Remove Amplify branches")
    parser.add_argument("--app-id", required=True, help="Amplify App ID")
    parser.add_argument("--branch-substring", required=True, help="Branch substring matcher. Eg: nmq")

    args = parser.parse_args()
    
    remove_branches(app_id=args.app_id, branch_substring=args.branch_substring)
