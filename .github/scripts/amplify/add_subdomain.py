import argparse
import boto3
from botocore.exceptions import ClientError


class AmplifySubdomainManager:
    def __init__(self, region_name: str):
        self.amplify_client = boto3.client("amplify", region_name=region_name)

    def add_branch_subdomain(
        self, app_id: str, branch_name: str, domain_name: str, subdomain_prefix: str
    ):
        """
        Add a branch-specific subdomain

        Args:
            app_id (str): Amplify App ID
            branch_name (str): Branch name
            domain_name (str): Base domain
            subdomain_prefix (str): Subdomain prefix

        Returns:
            dict: Subdomain details, including branch name and subdomain URL
        """

        existing_domains = self.amplify_client.list_domain_associations(appId=app_id)

        # Find existing subdomain settings for the domain
        existing_settings = []
        for domain in existing_domains.get("domainAssociations", []):
            for subdomain in domain.get("subDomains", []):
                current_setting = subdomain.get("subDomainSetting")
                if current_setting.get("branchName") == "main":
                    current_setting["prefix"] = ""

                if current_setting.get("branchName") == branch_name:
                    continue
                existing_settings.append(current_setting)

        try:
            existing_settings.append(
                {"prefix": subdomain_prefix, "branchName": branch_name}
            )
            # Add subdomain configuration
            self.amplify_client.update_domain_association(
                appId=app_id,
                domainName=domain_name,
                subDomainSettings=existing_settings,
            )

            return {
                "branch_name": branch_name,
                "subdomain_url": f"https://{subdomain_prefix}.{domain_name}",
            }

        except ClientError as e:
            print(f"Error adding subdomain: {e}")
            return None


def main():
    parser = argparse.ArgumentParser(description="Add Amplify Custom Subdomain")
    parser.add_argument("--app-id", required=True, help="Amplify App ID")
    parser.add_argument("--branch-name", required=True, help="Branch name")
    parser.add_argument("--domain-name", required=True, help="Domain name. Eg: <app-name>.nirvanatech.com")
    parser.add_argument("--subdomain-prefix", required=True, help="Custom Subdomain prefix. Eg: PR-XXXXX")

    args = parser.parse_args()

    manager = AmplifySubdomainManager(region_name="us-east-2")

    result = manager.add_branch_subdomain(
        app_id=args.app_id,
        branch_name=args.branch_name,
        domain_name=args.domain_name,
        subdomain_prefix=args.subdomain_prefix,
    )

    if result:
        print(result["subdomain_url"])


if __name__ == "__main__":
    main()
