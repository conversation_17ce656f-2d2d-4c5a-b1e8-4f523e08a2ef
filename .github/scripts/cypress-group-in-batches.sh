#!/bin/bash


# Splits the test files into groups of up to 10 (NUM var) files each and outputs them as a JSON array
# to be used by run-cypress.yml to run the tests in parallel
NUM=10


# Check if we have exactly one argument
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <directory with cypress/e2e folder>"
    echo "Example: $0 src/nirvana/client/packages/quoting"
    exit 1
fi

# Assign arguments to variables
DIR="$1"

# Check if the provided directory exists
if [ ! -d "$DIR" ]; then
    echo "Error: Directory '$DIR' does not exist."
    exit 1
fi

# Find all files in the directory and store them in an array
FILES=()
while IFS= read -r -d '' file; do
    FILES+=("$file")
done < <(find "$DIR/cypress/e2e" -type f -print0)

# Get the total number of files
TOTAL_FILES="${#FILES[@]}"



# Initialize an empty array to store JSON objects
OUTPUT="["

# Split the files into groups of up to 10 files each and build JSON
for ((i = 0; i < TOTAL_FILES; i+=NUM)); do

    GROUP=("${FILES[@]:i:NUM}")
    
    # Join the group files into a comma-separated string, properly escaping special characters
    JOINED_FILES=$(printf "%s," "${GROUP[@]}" | sed 's/,$//' | sed 's/"/\\"/g')

    # REMOVE DIR from the path
    JOINED_FILES=$(echo "$JOINED_FILES" | sed "s|$DIR/||g")

    # Add the joined files to the JSON array
    if [ -n "$JOINED_FILES" ]; then
        # Add a comma if not the first group
        if [ "$OUTPUT" != "[" ]; then
            OUTPUT+=","
        fi
        OUTPUT+="\"$JOINED_FILES\""
    fi
    
done

OUTPUT+="]"

# Print the JSON output
# echo "testfiles=$OUTPUT"
echo "testfiles=$OUTPUT" >> $GITHUB_OUTPUT