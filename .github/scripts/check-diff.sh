echo "========== checking paths of modified files =========="
diff=$(git diff --name-only HEAD^ HEAD)
 for file in $diff
 do
    echo $file
    if [[ $file = src/nirvana/db-api/migrations/* ]]; then
        echo "This modified file is under the 'db-api' folder."
        echo "migration_change=true" >> $GITHUB_OUTPUT
     break
    elif [[ $file = src/nirvana/fmcsa/migrations/* ]]; then
        echo "This modified file is under the 'fmcsa' folder."
        echo "migration_change=true" >> $GITHUB_OUTPUT
     break
    fi
done 

