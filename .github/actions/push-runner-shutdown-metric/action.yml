name: 'Send Runner Shutdown Datadog Metric'
description: 'Send a custom metric to Datadog when a job fails due to runner shutdown'
inputs:
  datadog-api-key:
    description: 'Datadog API key for authentication'
    required: true

runs:
  using: "composite"
  steps:

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.13"
        cache: pip
        cache-dependency-path: .github/scripts/runner-shutdown/requirements.txt

    - name: Install dependencies
      working-directory: .github/scripts/runner-shutdown
      shell: bash
      run: |
        pip install -r requirements.txt
  
    - name: Run script
      shell: bash
      env:
        INPUT_DATADOG_API_KEY: ${{ inputs.datadog-api-key }}
        GITHUB_TOKEN: ${{ github.token }}
      working-directory: .github/scripts/runner-shutdown
      run: |
        python push_runner_shutdown_metric.py
