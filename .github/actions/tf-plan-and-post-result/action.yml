name: "Terraform plan & comment result"
description: "This action runs terraform plan and upserts the result into a PR comment"
inputs:
  module-directory:
    description: "The path to module directory where CI should be run"
    required: true
  github-token:
    description: "GitHub Token for authentication"
    required: true
runs:
  using: "composite"
  steps:
    - id: init-terraform
      shell: bash
      working-directory: ${{ inputs.module-directory }}
      run: |
        terraform init
        tflint --init
    - id: terraform-plan
      name: Generate Terraform Plan
      run: |
        terraform plan -no-color -compact-warnings > plan_output.txt
        plan_output=$(grep -v "Refreshing state...\|Reading...\|Read complete after" plan_output.txt)
        echo "plan_output<<EOF" >> $GITHUB_ENV
        echo "$plan_output" >> $GITHUB_ENV
        echo "EOF" >> $GITHUB_ENV
      shell: bash
      working-directory: ${{ inputs.module-directory }}
    
    - id: upload-terraform-plan
      name: Upload terraform plan artifact
      uses: actions/upload-artifact@v4
      with:
        name: "terraform-plan-artifact"
        path: ${{ inputs.module-directory }}/plan_output.txt
    
    - name: Update or Insert Terraform Plan Comment
      uses: actions/github-script@v7
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}  
      with:
        script: |
          const uniqueIdentifier = "<!-- TERRAFORM_PLAN_COMMENT ${{ inputs.module-directory }} -->";
          const issue_number = context.issue.number;
          const owner = context.repo.owner;
          const repo = context.repo.repo;
          const terraformPlan = `${uniqueIdentifier}\n**Terraform Plan Output for ${{ inputs.module-directory }}**\n\`\`\`\n${process.env.plan_output}\n\`\`\``;

          const comments = await github.rest.issues.listComments({
            owner,
            repo,
            issue_number,
          });

          const existingComment = comments.data.find(comment => comment.body.includes(uniqueIdentifier));

          if (existingComment) {
            await github.rest.issues.updateComment({
              owner,
              repo,
              comment_id: existingComment.id,
              body: terraformPlan,
            });
          } else {
            await github.rest.issues.createComment({
              owner,
              repo,
              issue_number,
              body: terraformPlan,
            });
          }