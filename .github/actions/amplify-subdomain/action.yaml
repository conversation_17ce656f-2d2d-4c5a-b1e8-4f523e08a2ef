name: 'Amplify Subdomain Manager'
description: 'Manage Amplify subdomains for pull requests'

inputs:
  app-id:
    description: 'Amplify app ID'
    required: true
    type: string

  app-name:
    description: 'Nirvana app name (safety, support, quoting, underwriter)'
    required: true
    type: string

  branch-name:
    required: true
    type: string

  pull-request-number:
    description: 'Pull request number'
    required: true
    type: string

outputs:
  subdomain:
    description: Custom subdomain for the pull request
    value: ${{ steps.add-subdomain.outputs.subdomain }}

runs:
  using: "composite"
  steps:
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.13'
        cache: pip
        cache-dependency-path: .github/scripts/amplify/requirements.txt

    - name: Install dependencies
      working-directory: .github/scripts/amplify
      shell: bash
      run: |
        pip install -r requirements.txt

    - name: Add Amplify Subdomain
      shell: bash
      id: add-subdomain
      working-directory: .github/scripts/amplify
      env:
        BRANCH_NAME: ${{ inputs.branch-name }}
        PR_NUMBER: ${{ inputs.pull-request-number }}
        APP_ID: ${{ inputs.app-id }}
        DOMAIN_NAME: ${{ inputs.app-name }}.nirvanatech.com
      run: |
        SUBDOMAIN=$(python add_subdomain.py \
          --app-id $APP_ID \
          --branch-name "${BRANCH_NAME}" \
          --domain-name $DOMAIN_NAME \
          --subdomain-prefix "pr-${PR_NUMBER}")

        echo "subdomain=${SUBDOMAIN}" >> $GITHUB_OUTPUT
