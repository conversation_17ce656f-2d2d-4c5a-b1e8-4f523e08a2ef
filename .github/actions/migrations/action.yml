name: "Get current state of migration"
description: "Must be run from within the bazel workspace"

outputs:
  current_nirvana:
    value: ${{ steps.current-migration.outputs.nirvana }}
  current_fmcsa:
    value: ${{ steps.current-migration.outputs.fmcsa }}
  production_nirvana:
    value: ${{ steps.production-migration.outputs.nirvana }}
  production_fmcsa:
    value: ${{ steps.production-migration.outputs.fmcsa }}

runs:
  using: "composite"
  steps:
    # Note: to release a new version run scripts/release_tftools.sh
    - name: Download dbtools
      shell: bash
      run: |
        DBTOOLS=dbtools_9aa16b0b4
        echo "DBTOOLS=$DBTOOLS" >> $GITHUB_ENV
        aws s3 cp s3://cloud.nirvanatech.com/tools/"$DBTOOLS" $HOME/.local/bin/"$DBTOOLS"
        chmod +x $HOME/.local/bin/"$DBTOOLS"

    - name: Get source (ours) migration version
      id: current-migration
      shell: bash
      working-directory: src
      run: |
        NIRVANA_VER=$($DBTOOLS migrate get nirvana --source-only | jq -e -r .pending_version)
        FMCSA_VER=$($DBTOOLS migrate get fmcsa --source-only | jq -e -r .pending_version)

        echo "nirvana=${NIRVANA_VER}" >> $GITHUB_OUTPUT
        echo "fmcsa=${FMCSA_VER}" >> $GITHUB_OUTPUT

    
    - name: Get production (theirs) migration version
      id: production-migration
      shell: bash
      run: |
        LAMBDA_ARN="arn:aws:lambda:us-east-2:667656038718:function:db_migrate_lambda"
        tempfile=$(mktemp)
        CLI_TIMEOUT=0

        aws lambda invoke \
          --function-name $LAMBDA_ARN --cli-binary-format raw-in-base64-out \
          --payload '{ "action": "get", "dbname": "nirvana" }' \
          --cli-read-timeout $CLI_TIMEOUT \
          $tempfile
        cat $tempfile && echo
        NIRVANA_VER=$(jq -e -r .current_version $tempfile)

        aws lambda invoke \
          --function-name $LAMBDA_ARN --cli-binary-format raw-in-base64-out \
          --payload '{ "action": "get", "dbname": "fmcsa" }' \
          --cli-read-timeout $CLI_TIMEOUT \
          $tempfile
        cat $tempfile && echo
        FMCSA_VER=$(jq -e -r .current_version $tempfile)

        rm $tempfile

        echo "nirvana=${NIRVANA_VER}" >> $GITHUB_OUTPUT
        echo "fmcsa=${FMCSA_VER}" >> $GITHUB_OUTPUT
