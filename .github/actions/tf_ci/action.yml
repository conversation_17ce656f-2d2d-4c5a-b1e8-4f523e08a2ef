name: "CI for terraform"
description: "Run CI for terraform"
inputs:
  module-directory:
    description: "The path to module directory where CI should be run"
    required: true
runs:
  using: "composite"
  steps:
    - id: init-terraform
      shell: bash
      working-directory: ${{ inputs.module-directory }}
      run: |
        terraform init
        tflint --init
    - id: run-tflint
      shell: bash
      working-directory: ${{ inputs.module-directory }}
      run: tflint -f compact --module
