name: Mount cache
description: |
  This action restores a cache for the given parameters. It also eventually
  saves the cache IFF the run is on `main`'s HEAD. This is to avoid cache
  bloat(s) from PRs that end up causing cache evictions for caches created
  on `main`'s HEAD. We first attempt to mount (load) cache with key
  `prefix`-`week`-`main-commit-sha`, if not found we fallback to `prefix`-`week`.
  We only use the `main-commit-sha` (and not the current commit sha) to
  avoid creating a new cache for every branch/PR.
  This action also allows the ability to skip the save step (and only restore
  from  cache) as an optional flag.
inputs:
  prefix:
    description: |
      Prefix for the cache key. This is used to differentiate between
      different caches. Examples include `bazel`, `go`, `go-build`, `lint`, etc
    required: true
  path:
    description: |
      Path to the directory to be cached. This is the same as the `path` input
      for the `actions/cache` action; except that we prefer a single path for a
      single cache.
    required: true
  skipSave:
    description: |
      If set to `true`, the cache will not be saved. This is useful for push
      image actions where we may want to only reuse the cache (even if running
      on `main`'s HEAD).
    required: false
    default: "false"
runs:
  using: "composite"
  steps:
    # gets zero indexed week number of the year, with monday as the first day
    - name: Get Week
      id: get-week
      shell: bash
      run: |
        echo "week=$(/bin/date -u +%W)" >> $GITHUB_OUTPUT
    # Get the commit at the HEAD of main and the current commit sha. We
    # do this here so that we can use the outputs in the `if` condition
    # for the `restore` and `restore-and-save` steps.
    - name: Get commit SHAs
      id: get-commit-shas
      shell: bash
      run: |
        git fetch origin main
        echo "main=$( git rev-parse --short origin/main)" >> $GITHUB_OUTPUT
        echo "current=$( git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

    - name: Restore cache
      uses: actions/cache/restore@v4
      id: restore
      if: ${{(inputs.skipSave == 'true') || (steps.get-commit-shas.outputs.main != steps.get-commit-shas.outputs.current)}}
      with:
        path: ${{ inputs.path }}
        key: ${{ inputs.prefix }}-${{ steps.get-week.outputs.week }}-${{ steps.get-commit-shas.outputs.main }}
        restore-keys: |
          ${{ inputs.prefix }}-${{ steps.get-week.outputs.week }}

    # With current cache key we would create a new cache once a PR gets merged
    # into main (and that would be used by all future CI runs). Since we allow
    # restoration from restore-keys (which can contain stale artifacts), our
    # cache size can keep growing. That's why we have current week in the
    # cache so that all caches are invalidated every week.
    #
    # One PR at the start of each week would have to do a complete build, and
    # so we this action needs to be run automatically every monday night.
    - name: Restore & Save cache
      uses: actions/cache@v4
      id: restore-and-save
      if: ${{(inputs.skipSave == 'false') && (steps.get-commit-shas.outputs.main == steps.get-commit-shas.outputs.current )}}
      with:
        path: ${{ inputs.path }}
        key: ${{ inputs.prefix }}-${{ steps.get-week.outputs.week }}-${{ steps.get-commit-shas.outputs.main }}
        restore-keys: |
          ${{ inputs.prefix }}-${{ steps.get-week.outputs.week }}
