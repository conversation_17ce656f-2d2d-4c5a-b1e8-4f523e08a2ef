name: Setup AWS Credentials
description: This action sets up AWS credentials using OIDC

inputs:
  aws-account-id:
    required: false
    default: ************
  aws-region:
    required: false
    default: us-east-2
  aws-role-name:
    required: false
    default: NirvanaGithubActionRole
  aws-session-duration:
    required: false
    default: 1200 # 20 minutes

runs:
  using: composite
  steps:
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::${{ inputs.aws-account-id }}:role/${{ inputs.aws-role-name }}
        role-session-name: GitHub_to_AWS_via_FederatedOIDC
        role-duration-seconds: ${{ inputs.aws-session-duration }}
        aws-region: ${{ inputs.aws-region }}
