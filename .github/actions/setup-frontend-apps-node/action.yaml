name: Setup Frontend Apps node
description: Installs node and yarn packages with cache

outputs:
  yarn-cache-key:
    description: The yarn cache key
    value: ${{ steps.yarn-cache-key.outputs.value }}

runs:
  using: composite
  steps:
    - name: Set yarn cache key
      id: yarn-cache-key
      shell: bash
      run: echo "value=\${{ runner.os }}-\${{ runner.arch }}-modules-\${{ hashFiles('src/nirvana/client/yarn.lock') }}" >> $GITHUB_OUTPUT

    - uses: actions/cache@v4
      with:
        path: |
          ~/.cache
          **/node_modules
        key: ${{ steps.yarn-cache-key.outputs.value }}

    - uses: actions/setup-node@v4
      with:
        node-version: v22.15.0
        cache: yarn
        cache-dependency-path: "src/nirvana/client/yarn.lock"

    - name: Install dependencies
      working-directory: src/nirvana/client
      run: yarn install --immutable
      shell: bash
