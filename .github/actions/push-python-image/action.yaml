name: Build & Push python docker image to ECR
description: This action builds a fresh docker image packaging python dependencies and publishes it to our ECR
inputs:
  working_directory:
    description: Working directory from which to build docker image
    default: src/ds
    required: false
  repo:
    description: Name of the ECR repo
    default: datascience-repo
    required: false
  image_tag:
    description: The default tag for the image
    required: true
  inline_cache:
    # see: https://docs.docker.com/build/cache/backends/inline/
    description: Whether to embed build cache in-line in the docker image
    required: false
    default: false

outputs:
  image:
    description: Path (with tag) of pushed (ECR) image
    value: ${{ steps.build-image.outputs.image }}
  repo:
    description: Path to ECR repository (without image)
    value: ${{ steps.build-image.outputs.repo }}

runs:
  using: "composite"
  steps:
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build image (without cache)
      if: inputs.inline_cache != 'true'
      working-directory: ${{ inputs.working_directory }}
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: ${{ inputs.repo }}
        IMAGE_TAG: ${{ inputs.image_tag }}
      shell: bash
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG . \
          --cache-from type=registry,ref=$ECR_REGISTRY/$ECR_REPOSITORY:latest

    - name: Build image (with cache)
      if: inputs.inline_cache == 'true'
      working-directory: ${{ inputs.working_directory }}
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: ${{ inputs.repo }}
        IMAGE_TAG: ${{ inputs.image_tag }}
      shell: bash
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG . \
          --cache-from type=registry,ref=$ECR_REGISTRY/$ECR_REPOSITORY:latest \
          --cache-to type=inline

    - name: Push image
      id: build-image
      working-directory: ${{ inputs.working_directory }}
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: ${{ inputs.repo }}
        IMAGE_TAG: ${{ inputs.image_tag }}
      shell: bash
      run: |
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT
        echo "repo=$ECR_REGISTRY/$ECR_REPOSITORY" >> $GITHUB_OUTPUT
