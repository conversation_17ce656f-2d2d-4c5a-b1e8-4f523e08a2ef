name: Setup DS environment
description: This action installs python, poetry, and any other dependencies required to build/test DS code

runs:
  using: "composite"
  steps:
    - name: Setup Python
      id: setup-python
      uses: actions/setup-python@v5
      with:
        python-version: "3.9"

    - name: Set up cache
      uses: actions/cache@v4
      with:
        path: ~/.cache/pypoetry
        key: ${{ runner.os }}-python-${{ steps.setup-python.outputs.python-version }}-pipenv-${{ hashFiles('src/ds/poetry.lock') }}

    - name: Setup task build tool
      uses: arduino/setup-task@v1
      with:
        version: 3.x
        repo-token: ${{ github.token }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: 1.8.5

    - name: Install Dependencies
      shell: bash
      working-directory: src/ds
      run: |
        task install
