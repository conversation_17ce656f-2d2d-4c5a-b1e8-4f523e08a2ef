name: Setup Terraform environment
description: Setup Terraform environment

inputs:
  tftools:
    description: |
      TFTOOLS to use. Example: `terraform_7bca0ddf6c`
      The tftools binary is our custom wrapper around terraform that we
      use to add additional functionality. See `//nirvana/cmd/terraform` for
      the implementation.
    required: true
  account_id:
    description: AWS account ID to use. Defaults to production account (************).
    default: "************"

runs:
  using: "composite"
  steps:
    - name: Configure AWS Credentials
      uses: ./.github/actions/setup-aws-credentials
      with:
        aws-account-id: ${{ inputs.account_id }}

    - name: Setup terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: 1.7.5
        terraform_wrapper: false # ! Removing this will break our scripts which run jq on terraform output

    - name: Run terraform init
      shell: bash
      working-directory: src/deployment/app
      run: |
        terraform init

    - name: Setup tftools
      shell: bash
      run: |
        aws s3 cp s3://cloud.nirvanatech.com/tools/"$TFTOOLS" $HOME/.local/bin/"$TFTOOLS"
        chmod +x $HOME/.local/bin/"$TFTOOLS"
