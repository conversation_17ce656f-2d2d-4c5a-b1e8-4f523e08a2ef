name: 'Push branch and create Pull Request'
description: |
  This composite action automates the process of pushing changes made to a
  branch to the repository, and raising a Pull Request if one does not
  already exist for the branch. The action also adds labels provided in
  the input to the PR  if it creates the PR.

  NOTE: Separate labels with a comma (no spaces), if providing more than one.

  If there is no open pull request or the pull request has not been approved,
  the action commits and pushes the changes to the branch specified by branch-name.
  The commit message is specified by the commit-message input.

  If the pull request has been approved, the action comments on the pull request
  with the skip-push-comment-body input, indicating that the changes have been skipped.
inputs:
  gh-token:
    description: 'GitHub token for authentication.'
    required: true
  branch-name:
    description: 'The name of the branch to push changes to.'
    required: true
  skip-push-comment-body:
    description: 'Customizable comment body.'
    required: false
    default: 'Detected new changes, but skipping push to branch since this PR is already approved.'
  commit-message:
    description: 'Customizable commit message.'
    required: false
    default: 'Automated commit from GitHub Actions'
  pr-title:
    description: 'Customizable PR title.'
    required: false
    default: 'Automated PR from GitHub Actions'
  pr-body:
    description: 'Customizable PR body.'
    required: false
    default: 'This PR was automatically created by a GitHub Action.'
  pr-labels:
    description: 'Comma separated list of labels to add to the PR.'
    required: false
    default: ''

runs:
  using: "composite"
  steps:
    - name: Get existing PR info. Does it exist? Is it approved?
      id: pr-info
      run: |
        pr_data=$(gh pr list --head "${{ inputs.branch-name }}" --json 'state','reviewDecision','number' --limit 1)
        if [[ "$pr_data" == "[]" ]]; then
          echo "pr_exists=false" >> $GITHUB_OUTPUT
          echo "approved_pr=false" >> $GITHUB_OUTPUT
        else
          echo "pr_exists=true" >> $GITHUB_OUTPUT
          approved=$(echo "$pr_data" | jq -r '.[] | select(.reviewDecision=="APPROVED") | .number')
          if [[ "$approved" != "" ]]; then
            echo "approved_pr=true" >> $GITHUB_OUTPUT
            echo "pr_number=$approved" >> $GITHUB_OUTPUT
          else
            echo "approved_pr=false" >> $GITHUB_OUTPUT
          fi
        fi
      env:
        GH_TOKEN: ${{ inputs.gh-token }}
      shell: bash

    - name: Comment if PR exists and is approved
      if: steps.pr-info.outputs.approved_pr == 'true'
      run: |
        gh pr comment "${{ steps.pr-info.outputs.pr_number }}" --body "${{ inputs.skip-push-comment-body }}"
      env:
        GH_TOKEN: ${{ inputs.gh-token }}
      shell: bash

    - name: Commit and push changes if PR is not approved (or doesn't exist)
      if: steps.pr-info.outputs.approved_pr == 'false'
      run: |
        git add .
        git commit -m "${{ inputs.commit-message }}"
        git push --set-upstream origin "${{ inputs.branch-name }}"
      shell: bash

    - name: Create a PR if one doesn't exist already
      if: steps.pr-info.outputs.pr_exists == 'false'
      shell: bash
      # The first line in the below command takes care of setting gh CLI compatible
      # "--label <label>" flags for all labels provided in the input. For eg: if the
      # input is "label1,label2", the LABEL_FLAGS would be:
      # "--label label1 --label label2"
      run: |
        [ -z "$LABELS_CSV" ] || LABEL_FLAGS="--label $(echo $LABELS_CSV | sed 's/,/ --label /g')"
        gh pr create --title "${{ inputs.pr-title }}" --body "${{ inputs.pr-body }}"\
          --head "${{ inputs.branch-name }}" --base main $LABEL_FLAGS
      env:
        GH_TOKEN: ${{ inputs.gh-token }}
        LABELS_CSV: ${{ inputs.pr-labels }}
