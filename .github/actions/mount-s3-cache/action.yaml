name: Mount S3 cache
description: |
  This action is a duplicate of mount-cache, however it uses s3 backed cache instead
  of github cache which will be discontinued after all the workflows have been
  migrated to use self-hosted runners.
inputs:
  prefix:
    description: |
      Prefix for the cache key. This is used to differentiate between
      different caches. Examples include `bazel`, `go`, `go-build`, `lint`, etc
    required: true
  path:
    description: |
      Path to the directory to be cached. This is the same as the `path` input
      for the `actions/cache` action; except that we prefer a single path for a
      single cache.
    required: true
  skipSave:
    description: |
      If set to `true`, the cache will not be saved. This is useful for push
      image actions where we may want to only reuse the cache (even if running
      on `main`'s HEAD).
    required: false
    default: "false"

  skipRestore:
    description: |
      If set to `true`, the cache will not be restored. This should be useful
      for lint actions where we want to start from a fresh slate when running
      on `main`'s HEAD.
    required: false
    default: "false"
runs:
  using: "composite"
  steps:
    # gets zero indexed week number of the year, with monday as the first day
    - name: Get Week
      id: get-week
      shell: bash
      run: |
        current_week=$(/bin/date -u +%W)
        echo "week=$current_week" >> $GITHUB_OUTPUT
        if [ $current_week -eq 0 ]; then
          previous_week=52
        else
          previous_week=$((current_week - 1))
        fi

        echo "previous_week=$previous_week" >> $GITHUB_OUTPUT

    # Get the commit at the HEAD of main and the current commit sha. We
    # do this here so that we can use the outputs in the `if` condition
    # for the `restore` and `restore-and-save` steps.
    - name: Get commit SHAs
      id: get-commit-shas
      shell: bash
      run: |
        git fetch origin main
        echo "main=$( git rev-parse --short origin/main)" >> $GITHUB_OUTPUT
        echo "current=$( git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

    - name: Restore cache
      uses: runs-on/cache/restore@v4
      id: restore
      if: ${{(inputs.skipSave == 'true') || (steps.get-commit-shas.outputs.main != steps.get-commit-shas.outputs.current)}}
      with:
        path: ${{ inputs.path }}
        lookup-only: ${{ inputs.skipRestore }}
        key: ${{ inputs.prefix }}-${{ steps.get-week.outputs.week }}-${{ steps.get-commit-shas.outputs.main }}
        restore-keys: |
          ${{ inputs.prefix }}-${{ steps.get-week.outputs.week }}
          ${{ inputs.prefix }}-${{ steps.get-week.outputs.previous_week }}

    - name: Restore & Save cache
      uses: runs-on/cache@v4
      id: restore-and-save
      if: ${{(inputs.skipSave == 'false') && (steps.get-commit-shas.outputs.main == steps.get-commit-shas.outputs.current )}}
      with:
        path: ${{ inputs.path }}
        lookup-only: ${{ inputs.skipRestore }}
        key: ${{ inputs.prefix }}-${{ steps.get-week.outputs.week }}-${{ steps.get-commit-shas.outputs.main }}
        restore-keys: |
          ${{ inputs.prefix }}-${{ steps.get-week.outputs.week }}
          ${{ inputs.prefix }}-${{ steps.get-week.outputs.previous_week }}
