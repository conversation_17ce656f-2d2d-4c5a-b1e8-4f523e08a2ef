# default test config
test --test_tag_filters=-aws,-snowflake,-RateML,-RateMLMerge
test --test_arg=-test.v # run tests as if running as `go test -v`

# config for RateML tests
test:RateML --test_tag_filters=RateML

# config for RateML tests Post merge
test:RateMLMerge --test_tag_filters=RateMLMerge

# config for tests that require AWS credentials
test:aws --test_tag_filters=aws
test:aws --test_env=AWS_ACCESS_KEY_ID
test:aws --test_env=AWS_SECRET_ACCESS_KEY
test:aws --test_env=AWS_SESSION_TOKEN
test:aws --test_env=AWS_REGION

# config for tests that require Snowflake credentials
test:snowflake --test_tag_filters=snowflake
test:snowflake --test_env=SNOWFLAKE_ADMIN_ACCOUNT_NAME
test:snowflake --test_env=SNOWFLAKE_ADMIN_USER_NAME
test:snowflake --test_env=SNOWFLAKE_ADMIN_PASSWORD

# config for manual tests
test:manual --test_tag_filters=manual

# The host compiler is the compiler used to build tools that run on the host machine.
# protobuf requires at least c++14, but we use c++17 for future compatibility. 
# Note that bastion currently only supports c++17, not c++20.
build --host_cxxopt='-std=c++17'
build --cxxopt='-std=c++17'
build --java_runtime_version=remotejdk_11
