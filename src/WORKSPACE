workspace(name = "nirvana")

load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive", "http_jar")

# zlib version < 1.3.1 is not compatible with xcode cli tools version 16.3,
# so we are explicitly pinning zlib to 1.3.1. This pinning will not be required
# once we upgrade to bazel 7.x or later.
zlib_version = "1.3.1"

zlib_sha256 = "9a93b2b7dfdac77ceba5a558a580e74667dd6fede4585b91eefb60f03b72df23"

http_archive(
    name = "zlib",
    build_file = "@com_google_protobuf//:third_party/zlib.BUILD",
    sha256 = zlib_sha256,
    strip_prefix = "zlib-%s" % zlib_version,
    urls = ["https://github.com/madler/zlib/releases/download/v{v}/zlib-{v}.tar.gz".format(v = zlib_version)],
)

# Setup nodejs builds
# ===================

http_archive(
    name = "build_bazel_rules_nodejs",
    sha256 = "8f5f192ba02319254aaf2cdcca00ec12eaafeb979a80a1e946773c520ae0a2c9",
    urls = ["https://github.com/bazelbuild/rules_nodejs/releases/download/3.7.0/rules_nodejs-3.7.0.tar.gz"],
)

load("@build_bazel_rules_nodejs//:index.bzl", "node_repositories", "npm_install")

node_repositories(
    node_version = "16.1.0",
)

npm_install(
    name = "sms_scraper_npm",
    package_json = "//nirvana/sms_scraper:package.json",
    package_lock_json = "//nirvana/sms_scraper:package-lock.json",
)

npm_install(
    name = "fmcsa_scraper_npm",
    package_json = "//nirvana/scrapers/fmcsa:package.json",
    package_lock_json = "//nirvana/scrapers/fmcsa:package-lock.json",
)

npm_install(
    name = "saferwatch_scraper_npm",
    package_json = "//nirvana/servers/saferwatch_scraper:package.json",
    package_lock_json = "//nirvana/servers/saferwatch_scraper:package-lock.json",
)

# Set up bazel rules to create packages
# =============================

http_archive(
    name = "rules_pkg",
    sha256 = "8f9ee2dc10c1ae514ee599a8b42ed99fa262b757058f65ad3c384289ff70c4b8",
    urls = [
        "https://mirror.bazel.build/github.com/bazelbuild/rules_pkg/releases/download/0.9.1/rules_pkg-0.9.1.tar.gz",
        "https://github.com/bazelbuild/rules_pkg/releases/download/0.9.1/rules_pkg-0.9.1.tar.gz",
    ],
)

load("@rules_pkg//:deps.bzl", "rules_pkg_dependencies")

rules_pkg_dependencies()

# Set up go builds using gazelle
# =============================

http_archive(
    name = "io_bazel_rules_go",
    sha256 = "f4a9314518ca6acfa16cc4ab43b0b8ce1e4ea64b81c38d8a3772883f153346b8",
    urls = [
        "https://mirror.bazel.build/github.com/bazelbuild/rules_go/releases/download/v0.50.1/rules_go-v0.50.1.zip",
        "https://github.com/bazelbuild/rules_go/releases/download/v0.50.1/rules_go-v0.50.1.zip",
    ],
)

http_archive(
    name = "bazel_gazelle",
    integrity = "sha256-12v3pg/YsFBEQJDfooN6Tq+YKeEWVhjuNdzspcvfWNU=",
    urls = [
        "https://mirror.bazel.build/github.com/bazelbuild/bazel-gazelle/releases/download/v0.37.0/bazel-gazelle-v0.37.0.tar.gz",
        "https://github.com/bazelbuild/bazel-gazelle/releases/download/v0.37.0/bazel-gazelle-v0.37.0.tar.gz",
    ],
)

load("@bazel_gazelle//:deps.bzl", "gazelle_dependencies", "go_repository")
load("@io_bazel_rules_go//go:deps.bzl", "go_register_toolchains", "go_rules_dependencies")

############################################################
# Define your own dependencies here using go_repository.
# Else, dependencies declared by rules_go/gazelle will be used.
# The first declaration of an external repository "wins".
############################################################

go_rules_dependencies()

go_register_toolchains(version = "1.24.1")

http_archive(
    name = "rules_proto",
    sha256 = "6fb6767d1bef535310547e03247f7518b03487740c11b6c6adb7952033fe1295",
    strip_prefix = "rules_proto-6.0.2",
    url = "https://github.com/bazelbuild/rules_proto/releases/download/6.0.2/rules_proto-6.0.2.tar.gz",
)

load("@rules_proto//proto:repositories.bzl", "rules_proto_dependencies")

rules_proto_dependencies()

load("@rules_proto//proto:setup.bzl", "rules_proto_setup")

rules_proto_setup()

load("@rules_proto//proto:toolchains.bzl", "rules_proto_toolchains")

rules_proto_toolchains()

############################################################################################
# Required by rules_proto 6.x.x, since it no longer explicitly depends on protobuf.

http_archive(
    name = "com_google_protobuf",
    sha256 = "616bb3536ac1fff3fb1a141450fa28b875e985712170ea7f1bfe5e5fc41e2cd8",
    strip_prefix = "protobuf-24.4",
    urls = ["https://github.com/protocolbuffers/protobuf/archive/v24.4.tar.gz"],
)

load("@com_google_protobuf//:protobuf_deps.bzl", "protobuf_deps")

protobuf_deps()

############################################################################################
# Required by rules_go 0.41.0

http_archive(
    name = "googleapis",
    sha256 = "9d1a930e767c93c825398b8f8692eca3fe353b9aaadedfbcf1fca2282c85df88",
    strip_prefix = "googleapis-64926d52febbf298cb82a8f472ade4a3969ba922",
    urls = [
        "https://github.com/googleapis/googleapis/archive/64926d52febbf298cb82a8f472ade4a3969ba922.zip",
    ],
)

load("@googleapis//:repository_rules.bzl", "switched_rules_by_language")

switched_rules_by_language(
    name = "com_google_googleapis_imports",
)

##############################################################################################

go_repository(
    name = "com_github_alecthomas_participle_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/alecthomas/participle/v2",
    sum = "h1:W/H79S8Sat/krZ3el6sQMvMaahJ+XcM9WSI2naI7w2U=",
    version = "v2.1.4",
)

go_repository(
    name = "com_github_alecthomas_repr",
    build_file_proto_mode = "disable",
    importpath = "github.com/alecthomas/repr",
    sum = "h1:GhI2A8MACjfegCPVq9f1FLvIBS+DrQ2KQBFZP1iFzXc=",
    version = "v0.4.0",
)

go_repository(
    name = "com_github_montanaflynn_stats",
    build_file_proto_mode = "disable",
    importpath = "github.com/montanaflynn/stats",
    sum = "h1:Duep6KMIDpY4Yo11iFsvyqJDyfzLF9+sndUKT+v64GQ=",
    version = "v0.6.6",
)

go_repository(
    name = "com_github_hhrutter_lzw",
    build_file_proto_mode = "disable",
    importpath = "github.com/hhrutter/lzw",
    sum = "h1:laL89Llp86W3rRs83LvKbwYRx6INE8gDn0XNb1oXtm0=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_hhrutter_tiff",
    build_file_proto_mode = "disable",
    importpath = "github.com/hhrutter/tiff",
    sum = "h1:MIus8caHU5U6823gx7C6jrfoEvfSTGtEFRiM8/LOzC0=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_pdfcpu_pdfcpu",
    build_file_proto_mode = "disable",
    importpath = "github.com/pdfcpu/pdfcpu",
    sum = "h1:z4kARP5bcWa39TTYMcN/kjBnm7MvhTWjXgeYmkdAGMI=",
    version = "v0.6.0",
)

go_repository(
    name = "com_github_sebastiaanklippert_go_wkhtmltopdf",
    build_file_proto_mode = "disable",
    importpath = "github.com/SebastiaanKlippert/go-wkhtmltopdf",
    sum = "h1:LORAatv6KuKheYq8HXehiwx3f/VGuzJBNSydUDQ98EM=",
    version = "v1.7.2",
)

go_repository(
    name = "com_github_cloudykit_fastprinter",
    build_file_proto_mode = "disable",
    importpath = "github.com/CloudyKit/fastprinter",
    sum = "h1:sR+/8Yb4slttB4vD+b9btVEnWgL3Q00OBTzVT8B9C0c=",
    version = "v0.0.0-20200109182630-33d98a066a53",
)

go_repository(
    name = "com_github_cockroachdb_datadriven",
    build_file_proto_mode = "disable",
    importpath = "github.com/cockroachdb/datadriven",
    sum = "h1:H9MtNqVoVhvd9nCBwOyDjUEdZCREqbIdCJD93PBm/jA=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_cockroachdb_errors",
    build_directives = [
        "gazelle:resolve proto proto gogoproto/gogo.proto @com_github_gogo_protobuf//gogoproto:gogoproto_proto",
        "gazelle:resolve proto go gogoproto/gogo.proto @com_github_gogo_protobuf//gogoproto",
    ],
    build_file_proto_mode = "disable",
    importpath = "github.com/cockroachdb/errors",
    sum = "h1:5bA+k2Y6r+oz/6Z/RFlNeVCesGARKuC6YymtcDrbC/I=",
    version = "v1.11.3",
)

go_repository(
    name = "com_github_cockroachdb_logtags",
    build_file_proto_mode = "disable",
    importpath = "github.com/cockroachdb/logtags",
    sum = "h1:r6VH0faHjZeQy818SGhaone5OnYfxFR/+AzdY3sf5aE=",
    version = "v0.0.0-20230118201751-21c54148d20b",
)

go_repository(
    name = "com_github_cockroachdb_redact",
    build_file_proto_mode = "disable",
    importpath = "github.com/cockroachdb/redact",
    sum = "h1:u1PMllDkdFfPWaNGMyLD1+so+aq3uUItthCFqzwPJ30=",
    version = "v1.1.5",
)

go_repository(
    name = "com_github_codegangsta_inject",
    build_file_proto_mode = "disable",
    importpath = "github.com/codegangsta/inject",
    sum = "h1:sDMmm+q/3+BukdIpxwO365v/Rbspp2Nt5XntgQRXq8Q=",
    version = "v0.0.0-20150114235600-33e0aa1cb7c0",
)

go_repository(
    name = "com_github_dustin_go_humanize",
    build_file_proto_mode = "disable",
    importpath = "github.com/dustin/go-humanize",
    sum = "h1:GzkhY7T5VNhEkwH0PVJgjz+fX1rhBrR7pRT3mDkpeCY=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_eknkc_amber",
    build_file_proto_mode = "disable",
    importpath = "github.com/eknkc/amber",
    sum = "h1:clC1lXBpe2kTj2VHdaIu9ajZQe4kcEY9j0NsnDDBZ3o=",
    version = "v0.0.0-20171010120322-cdade1c07385",
)

go_repository(
    name = "com_github_fatih_structs",
    build_file_proto_mode = "disable",
    importpath = "github.com/fatih/structs",
    sum = "h1:Q7juDM0QtcnhCpeyLGQKyg4TOIghuNXrkL32pHAUMxo=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_gin_contrib_sse",
    build_file_proto_mode = "disable",
    importpath = "github.com/gin-contrib/sse",
    sum = "h1:Y/yl/+YNO8GZSjAhjMsSuLt29uWRFHdHYUb5lYOV9qE=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_gin_gonic_gin",
    build_file_proto_mode = "disable",
    importpath = "github.com/gin-gonic/gin",
    sum = "h1:4idEAncQnU5cB7BeOkPtxjfCSye0AAm1R0RVIqJ+Jmg=",
    version = "v1.9.1",
)

go_repository(
    name = "com_github_go_errors_errors",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-errors/errors",
    sum = "h1:J6MZopCL4uSllY1OfXM374weqZFFItUbrImctkmUxIA=",
    version = "v1.4.2",
)

go_repository(
    name = "com_github_go_martini_martini",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-martini/martini",
    sum = "h1:xveKWz2iaueeTaUgdetzel+U7exyigDYBryyVfV/rZk=",
    version = "v0.0.0-20170121215854-22fa46961aab",
)

go_repository(
    name = "com_github_gogo_googleapis",
    build_file_proto_mode = "disable",
    importpath = "github.com/gogo/googleapis",
    sum = "h1:1Yx4Myt7BxzvUr5ldGSbwYiZG6t9wGBZ+8/fX3Wvtq0=",
    version = "v1.4.1",
)

go_repository(
    name = "com_github_gogo_status",
    build_file_proto_mode = "disable",
    importpath = "github.com/gogo/status",
    sum = "h1:+eIkrewn5q6b30y+g/BJINVVdi2xH7je5MPJ3ZPK3JA=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_hashicorp_go_version",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-version",
    sum = "h1:O293SZ2Eg+AAYijkVK3jR786Am1bhDEh2GHT0tIVE5E=",
    version = "v1.5.0",
)

go_repository(
    name = "com_github_hydrogen18_memlistener",
    build_file_proto_mode = "disable",
    importpath = "github.com/hydrogen18/memlistener",
    sum = "h1:JR7eDj8HD6eXrc5fWLbSUnfcQFL06PYvCc0DKQnWfaU=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_iris_contrib_schema",
    build_file_proto_mode = "disable",
    importpath = "github.com/iris-contrib/schema",
    sum = "h1:CPSBLyx2e91H2yJzPuhGuifVRnZBBJ3pCOMbOvPZaTw=",
    version = "v0.0.6",
)

go_repository(
    name = "com_github_juju_errors",
    build_file_proto_mode = "disable",
    importpath = "github.com/juju/errors",
    sum = "h1:h7brQJHKsJO28BdmxgKaKVSaZT2bfobmooTf08thYvA=",
    version = "v0.0.0-20160809030848-6f54ff631840",
)

go_repository(
    name = "com_github_kataras_golog",
    build_file_proto_mode = "disable",
    importpath = "github.com/kataras/golog",
    sum = "h1:vLvSDpP7kihFGKFAvBSofYo7qZNULYSHOH2D7rPTKJk=",
    version = "v0.1.9",
)

go_repository(
    name = "com_github_kataras_iris_v12",
    build_file_proto_mode = "disable",
    importpath = "github.com/kataras/iris/v12",
    sum = "h1:R5UzUW4MIByBM6tKMG3UqJ7hL1JCEE+dkqQ8L72f6PU=",
    version = "v12.2.5",
)

go_repository(
    name = "com_github_kataras_pio",
    build_file_proto_mode = "disable",
    importpath = "github.com/kataras/pio",
    sum = "h1:o52SfVYauS3J5X08fNjlGS5arXHjW/ItLkyLcKjoH6w=",
    version = "v0.0.12",
)

go_repository(
    name = "com_github_klauspost_compress",
    build_file_proto_mode = "disable",
    importpath = "github.com/klauspost/compress",
    sum = "h1:2mk3MPGNzKyxErAw8YaohYh69+pa4sIQSC0fPGCFR9I=",
    version = "v1.16.7",
)

go_repository(
    name = "com_github_microcosm_cc_bluemonday",
    build_file_proto_mode = "disable",
    importpath = "github.com/microcosm-cc/bluemonday",
    sum = "h1:4NEwSfiJ+Wva0VxN5B8OwMicaJvD8r9tlJWm9rtloEg=",
    version = "v1.0.25",
)

go_repository(
    name = "com_github_pingcap_errors",
    build_file_proto_mode = "disable",
    importpath = "github.com/pingcap/errors",
    sum = "h1:LllgC9eGfqzkfubMgjKIDyZYaa609nNWAyNZtpy2B3M=",
    version = "v0.11.5-0.20201126102027-b0a155152ca3",
)

go_repository(
    name = "com_github_russross_blackfriday",
    build_file_proto_mode = "disable",
    importpath = "github.com/russross/blackfriday",
    sum = "h1:KqfZb0pUVN2lYqZUYRddxF4OR8ZMURnJIG5Y3VRLtww=",
    version = "v1.6.0",
)

go_repository(
    name = "com_github_ryanuber_columnize",
    build_file_proto_mode = "disable",
    importpath = "github.com/ryanuber/columnize",
    sum = "h1:UFr9zpz4xgTnIE5yIMtWAMngCdZ9p/+q6lTbgelo80M=",
    version = "v0.0.0-20160712163229-9b3edd62028f",
)

go_repository(
    name = "com_github_sergi_go_diff",
    build_file_proto_mode = "disable",
    importpath = "github.com/sergi/go-diff",
    sum = "h1:xkr+Oxo4BOQKmkn/B9eMK0g5Kg/983T9DqqPHwYqD+8=",
    version = "v1.3.1",
)

go_repository(
    name = "com_github_shopify_goreferrer",
    build_file_proto_mode = "disable",
    importpath = "github.com/Shopify/goreferrer",
    sum = "h1:KkH3I3sJuOLP3TjA/dfr4NAY8bghDwnXiU7cTKxQqo0=",
    version = "v0.0.0-20220729165902-8cddb4f5de06",
)

go_repository(
    name = "com_github_ugorji_go_codec",
    build_file_proto_mode = "disable",
    importpath = "github.com/ugorji/go/codec",
    sum = "h1:BMaWp1Bb6fHwEtbplGBGJ498wD+LKlNSl25MjdZY4dU=",
    version = "v1.2.11",
)

go_repository(
    name = "com_github_urfave_negroni",
    build_file_proto_mode = "disable",
    importpath = "github.com/urfave/negroni",
    sum = "h1:kIimOitoypq34K7TG7DUaJ9kq/N4Ofuwi1sjz0KipXc=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_valyala_fasthttp",
    build_file_proto_mode = "disable",
    importpath = "github.com/valyala/fasthttp",
    sum = "h1:CRq/00MfruPGFLTQKY8b+8SfdK60TxNztjRMnH0t1Yc=",
    version = "v1.40.0",
)

go_repository(
    name = "com_github_xeipuuv_gojsonpointer",
    build_file_proto_mode = "disable",
    importpath = "github.com/xeipuuv/gojsonpointer",
    sum = "h1:J9EGpcZtP0E/raorCMxlFGSTBrsSlaDGf3jU/qvAE2c=",
    version = "v0.0.0-20180127040702-4e3ac2762d5f",
)

go_repository(
    name = "com_github_xeipuuv_gojsonreference",
    build_file_proto_mode = "disable",
    importpath = "github.com/xeipuuv/gojsonreference",
    sum = "h1:EzJWgHovont7NscjpAxXsDA8S8BMYve8Y5+7cuRE7R0=",
    version = "v0.0.0-20180127040603-bd5ef7bd5415",
)

go_repository(
    name = "com_github_xeipuuv_gojsonschema",
    build_file_proto_mode = "disable",
    importpath = "github.com/xeipuuv/gojsonschema",
    sum = "h1:LhYJRs+L4fBtjZUfuSZIKGeVu0QRy8e5Xi7D17UxZ74=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_sendgrid_rest",
    build_file_proto_mode = "disable",
    importpath = "github.com/sendgrid/rest",
    sum = "h1:1EyIcsNdn9KIisLW50MKwmSRSK+ekueiEMJ7NEoxJo0=",
    version = "v2.6.9+incompatible",
)

go_repository(
    name = "com_github_sendgrid_sendgrid_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/sendgrid/sendgrid-go",
    sum = "h1:2f/d7odubrZMkwqSupQDU5ad1GkS8syopBapDazh5bM=",
    version = "v3.10.5+incompatible",
)

go_repository(
    name = "com_github_paulmach_go_geojson",
    build_file_proto_mode = "disable",
    importpath = "github.com/paulmach/go.geojson",
    sum = "h1:5x5moCkCtDo5x8af62P9IOAYGQcYHtxz2QJ3x1DoCgY=",
    version = "v1.4.0",
)

go_repository(
    name = "com_github_jszwec_csvutil",
    build_file_proto_mode = "disable",
    importpath = "github.com/jszwec/csvutil",
    sum = "h1:G7vS2LGdpZZDH1HmHeNbxOaJ/ZnJlpwGFvOkTkJzzNk=",
    version = "v1.8.0",
)

go_repository(
    name = "com_github_benbjohnson_clock",
    build_file_proto_mode = "disable",
    importpath = "github.com/benbjohnson/clock",
    sum = "h1:VvXlSJBzZpA/zum6Sj74hxwYI2DIxRWuNIoXAzHZz5o=",
    version = "v1.3.5",
)

go_repository(
    name = "com_github_dmarkham_enumer",
    build_file_proto_mode = "disable",
    importpath = "github.com/dmarkham/enumer",
    sum = "h1:quorLCaEfzjJ23Pf7PB9lyyaHseh91YfTM/sAD/4Mbo=",
    version = "v1.5.11",
)

go_repository(
    name = "com_github_jinzhu_now",
    build_file_proto_mode = "disable",
    importpath = "github.com/jinzhu/now",
    sum = "h1:eVKgfIdy9b6zbWBMgFpfDPoAMifwSZagU9HmEU6zgiI=",
    version = "v1.1.2",
)

go_repository(
    name = "org_uber_go_goleak",
    build_file_proto_mode = "disable",
    importpath = "go.uber.org/goleak",
    sum = "h1:2K3zAYmnTNqV73imy9J1T3WC+gmCePx2hEGkimedGto=",
    version = "v1.3.0",
)

go_repository(
    name = "com_github_samsarahq_thunder",
    build_file_proto_mode = "disable_global",
    importpath = "github.com/samsarahq/thunder",
    sum = "h1:7PcVVbAc5c2CqN/zWsIH6k8DAMsnBP0rPXS3YJCWFVs=",
    version = "v0.5.1-0.20220215002807-e40cdda7927c",
)

go_repository(
    name = "com_github_rakyll_statik",
    build_file_proto_mode = "disable",
    importpath = "github.com/rakyll/statik",
    sum = "h1:Ly2UjURzxnsSYS0zI50fZ+srA+Fu7EbpV5hglvJvJG0=",
    version = "v0.1.5",
)

go_repository(
    name = "com_github_graphql_go_graphql",
    build_file_proto_mode = "disable",
    importpath = "github.com/graphql-go/graphql",
    sum = "h1:5Va/Rt4l5g3YjwDnid3vFfn43faaQBq7rMcIZ0VnV34=",
    version = "v0.7.9",
)

go_repository(
    name = "com_github_samsarahq_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/samsarahq/go",
    sum = "h1:08rld4IAazPOsf7lJ+DpZPUnf/BmF+CPvCN+tC+Kx+k=",
    version = "v0.0.0-20210308200756-be0e1e5b6582",
)

go_repository(
    name = "com_github_kylelemons_godebug",
    build_file_proto_mode = "disable",
    importpath = "github.com/kylelemons/godebug",
    sum = "h1:RPNrshWIDI6G2gRW9EHilWtl7Z6Sb1BR0xunSBf0SNc=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_antihax_optional",
    build_file_proto_mode = "disable",
    importpath = "github.com/antihax/optional",
    sum = "h1:xK2lYat7ZLaVVcIuj82J8kIro4V6kDe0AUDFboUCwcg=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_armon_circbuf",
    build_file_proto_mode = "disable",
    importpath = "github.com/armon/circbuf",
    sum = "h1:QEF07wC0T1rKkctt1RINW/+RMTVmiwxETico2l3gxJA=",
    version = "v0.0.0-20150827004946-bbbad097214e",
)

go_repository(
    name = "com_github_armon_go_metrics",
    build_file_proto_mode = "disable",
    importpath = "github.com/armon/go-metrics",
    sum = "h1:8GUt8eRujhVEGZFFEjBj46YV4rDjvGrNxb0KMWYkL2I=",
    version = "v0.0.0-20180917152333-f0300d1749da",
)

go_repository(
    name = "com_github_armon_go_radix",
    build_file_proto_mode = "disable",
    importpath = "github.com/armon/go-radix",
    sum = "h1:F4z6KzEeeQIMeLFa97iZU6vupzoecKdU5TX24SNppXI=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_bgentry_speakeasy",
    build_file_proto_mode = "disable",
    importpath = "github.com/bgentry/speakeasy",
    sum = "h1:ByYyxL9InA1OWqxJqqp2A5pYHUrCiAL6K3J+LKSsQkY=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_bketelsen_crypt",
    build_file_proto_mode = "disable",
    importpath = "github.com/bketelsen/crypt",
    sum = "h1:w/jqZtC9YD4DS/Vp9GhWfWcCpuAL58oTnLoI8vE9YHU=",
    version = "v0.0.4",
)

go_repository(
    name = "com_github_coreos_go_systemd_v22",
    build_file_proto_mode = "disable",
    importpath = "github.com/coreos/go-systemd/v22",
    sum = "h1:RrqgGjYQKalulkV8NGVIfkXQf6YYmOyiJKk8iXXhfZs=",
    version = "v22.5.0",
)

go_repository(
    name = "com_github_fatih_color",
    build_file_proto_mode = "disable",
    importpath = "github.com/fatih/color",
    sum = "h1:kOqh6YHBtK8aywxGerMG2Eq3H6Qgoqeo13Bk2Mv/nBs=",
    version = "v1.15.0",
)

go_repository(
    name = "com_github_godbus_dbus_v5",
    build_file_proto_mode = "disable",
    importpath = "github.com/godbus/dbus/v5",
    sum = "h1:4KLkAxT3aOY8Li4FRJe/KvhoNFFxo0m6fNuFUO8QJUk=",
    version = "v5.1.0",
)

go_repository(
    name = "com_github_hashicorp_consul_api",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/consul/api",
    sum = "h1:MwZJp86nlnL+6+W1Zly4JUuVn9YHhMggBirMpHGD7kw=",
    version = "v1.10.1",
)

go_repository(
    name = "com_github_hashicorp_consul_sdk",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/consul/sdk",
    sum = "h1:OJtKBtEjboEZvG6AOUdh4Z1Zbyu0WcxQ0qatRrZHTVU=",
    version = "v0.8.0",
)

go_repository(
    name = "com_github_hashicorp_go_cleanhttp",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-cleanhttp",
    sum = "h1:035FKYIWjmULyFRBKPs8TBQoi0x6d9G4xc9neXJWAZQ=",
    version = "v0.5.2",
)

go_repository(
    name = "com_github_hashicorp_go_immutable_radix",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-immutable-radix",
    sum = "h1:AKDB1HM5PWEA7i4nhcpwOrO2byshxBjXVn/J/3+z5/0=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_hashicorp_go_msgpack",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-msgpack",
    sum = "h1:zKjpN5BK/P5lMYrLmBHdBULWbJ0XpYR+7NGzqkZzoD4=",
    version = "v0.5.3",
)

go_repository(
    name = "com_github_hashicorp_go_net",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go.net",
    sum = "h1:sNCoNyDEvN1xa+X0baata4RdcpKwcMS6DH+xwfqPgjw=",
    version = "v0.0.1",
)

go_repository(
    name = "com_github_hashicorp_go_rootcerts",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-rootcerts",
    sum = "h1:jzhAVGtqPKbwpyCPELlgNWhE1znq+qwJtW5Oi2viEzc=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_hashicorp_go_sockaddr",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-sockaddr",
    sum = "h1:GeH6tui99pF4NJgfnhp+L6+FfobzVW3Ah46sLo0ICXs=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_hashicorp_go_syslog",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-syslog",
    sum = "h1:KaodqZuhUoZereWVIYmpUgZysurB1kBLX2j0MwMrUAE=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_hashicorp_go_uuid",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-uuid",
    sum = "h1:cfejS+Tpcp13yd5nYHWDI6qVCny6wyX2Mt5SGur2IGE=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_hashicorp_logutils",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/logutils",
    sum = "h1:dLEQVugN8vlakKOUE3ihGLTZJRB4j+M2cdTm/ORI65Y=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_hashicorp_mdns",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/mdns",
    sum = "h1:XFSOubp8KWB+Jd2PDyaX5xUd5bhSP/+pTDZVDMzZJM8=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_hashicorp_memberlist",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/memberlist",
    sum = "h1:5+RffWKwqJ71YPu9mWsF7ZOscZmwfasdA8kbdC7AO2g=",
    version = "v0.2.2",
)

go_repository(
    name = "com_github_hashicorp_serf",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/serf",
    sum = "h1:EBWvyu9tcRszt3Bxp3KNssBMP1KuHWyO51lz9+786iM=",
    version = "v0.9.5",
)

go_repository(
    name = "com_github_kr_fs",
    build_file_proto_mode = "disable",
    importpath = "github.com/kr/fs",
    sum = "h1:Jskdu9ieNAYnjxsi0LbQp1ulIKZV1LAFgK1tWhpZgl8=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_miekg_dns",
    build_file_proto_mode = "disable",
    importpath = "github.com/miekg/dns",
    sum = "h1:gPxPSwALAeHJSjarOs00QjVdV9QoBvc1D2ujQUr5BzU=",
    version = "v1.1.26",
)

go_repository(
    name = "com_github_mitchellh_cli",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/cli",
    sum = "h1:tEElEatulEHDeedTxwckzyYMA5c86fbmNIUL1hBIiTg=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_mitchellh_go_testing_interface",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/go-testing-interface",
    sum = "h1:jrgshOhYAUVNMAJiKbEu7EqAwgJJ2JqpQmpLJOu07cU=",
    version = "v1.14.1",
)

go_repository(
    name = "com_github_mitchellh_gox",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/gox",
    sum = "h1:lfGJxY7ToLJQjHHwi0EX6uYBdK78egf954SQl13PQJc=",
    version = "v0.4.0",
)

go_repository(
    name = "com_github_mitchellh_iochan",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/iochan",
    sum = "h1:C+X3KsSTLFVBr/tK1eYN/vs4rJcvsiLU338UhYPJWeY=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_pascaldekloe_goe",
    build_file_proto_mode = "disable",
    importpath = "github.com/pascaldekloe/goe",
    sum = "h1:Lgl0gzECD8GnQ5QCWA8o6BtfL6mDH5rQgM4/fX3avOs=",
    version = "v0.0.0-20180627143212-57f6aae5913c",
)

go_repository(
    name = "com_github_pkg_sftp",
    build_file_proto_mode = "disable",
    importpath = "github.com/pkg/sftp",
    sum = "h1:VasscCm72135zRysgrJDKsntdmPN+OuU3+nnHYA9wyc=",
    version = "v1.10.1",
)

go_repository(
    name = "com_github_posener_complete",
    build_file_proto_mode = "disable",
    importpath = "github.com/posener/complete",
    sum = "h1:NP0eAhjcjImqslEwo/1hq7gpajME0fTLTezBKDqfXqo=",
    version = "v1.2.3",
)

go_repository(
    name = "com_github_sean_seed",
    build_file_proto_mode = "disable",
    importpath = "github.com/sean-/seed",
    sum = "h1:nn5Wsu0esKSJiIVhscUtVbo7ada43DJhG55ua/hjS5I=",
    version = "v0.0.0-20170313163322-e2103e2c3529",
)

go_repository(
    name = "com_google_cloud_go_firestore",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/firestore",
    sum = "h1:8aLcKnMPoldYU3YHgu4t2exrKhLQkqaXAGqT0ljrFVw=",
    version = "v1.14.0",
)

go_repository(
    name = "io_etcd_go_etcd_api_v3",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/etcd/api/v3",
    sum = "h1:GsV3S+OfZEOCNXdtNkBSR7kgLobAa/SO6tCxRa0GAYw=",
    version = "v3.5.0",
)

go_repository(
    name = "io_etcd_go_etcd_client_pkg_v3",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/etcd/client/pkg/v3",
    sum = "h1:2aQv6F436YnN7I4VbI8PPYrBhu+SmrTaADcf8Mi/6PU=",
    version = "v3.5.0",
)

go_repository(
    name = "io_etcd_go_etcd_client_v2",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/etcd/client/v2",
    sum = "h1:ftQ0nOOHMcbMS3KIaDQ0g5Qcd6bhaBrQT6b89DfwLTs=",
    version = "v2.305.0",
)

go_repository(
    name = "com_github_jinzhu_copier",
    build_file_proto_mode = "disable",
    importpath = "github.com/jinzhu/copier",
    sum = "h1:w3ciUoD19shMCRargcpm0cm91ytaBhDvuRpz1ODO/U8=",
    version = "v0.4.0",
)

go_repository(
    name = "com_github_casbin_casbin_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/casbin/casbin/v2",
    sum = "h1:tPP1YypfG2fqe7S2zivYuD11yc+/mc0GXJe/sDVbb1U=",
    version = "v2.44.3",
)

go_repository(
    name = "com_github_knetic_govaluate",
    build_file_proto_mode = "disable",
    importpath = "github.com/Knetic/govaluate",
    sum = "h1:1G1pk05UrOh0NlF1oeaaix1x8XzrfjIDK47TY0Zehcw=",
    version = "v3.0.1-0.20171022003610-9aa49832a739+incompatible",
)

go_repository(
    name = "cc_mvdan_xurls_v2",
    build_file_proto_mode = "disable",
    importpath = "mvdan.cc/xurls/v2",
    sum = "h1:59Olnbt67UKpxF1EwVBopJvkSUBmgtb468E4GVWIZ1I=",
    version = "v2.3.0",
)

go_repository(
    name = "com_github_agext_levenshtein",
    build_file_proto_mode = "disable",
    importpath = "github.com/agext/levenshtein",
    sum = "h1:YB2fHEn0UJagG8T1rrWknE3ZQzWM06O8AMAatNn7lmo=",
    version = "v1.2.3",
)

go_repository(
    name = "com_github_apparentlymart_go_dump",
    build_file_proto_mode = "disable",
    importpath = "github.com/apparentlymart/go-dump",
    sum = "h1:ZSTrOEhiM5J5RFxEaFvMZVEAM1KvT1YzbEOwB2EAGjA=",
    version = "v0.0.0-20180507223929-23540a00eaa3",
)

go_repository(
    name = "com_github_apparentlymart_go_textseg",
    build_file_proto_mode = "disable",
    importpath = "github.com/apparentlymart/go-textseg",
    sum = "h1:rRmlIsPEEhUTIKQb7T++Nz/A5Q6C9IuX2wFoYVvnCs0=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_apparentlymart_go_textseg_v13",
    build_file_proto_mode = "disable",
    importpath = "github.com/apparentlymart/go-textseg/v13",
    sum = "h1:Y+KvPE1NYz0xl601PVImeQfFyEy6iT90AvPUL1NNfNw=",
    version = "v13.0.0",
)

go_repository(
    name = "com_github_go_test_deep",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-test/deep",
    sum = "h1:TDsG77qcSprGbC6vTN8OuXp5g+J+b5Pcguhf7Zt61VM=",
    version = "v1.0.8",
)

go_repository(
    name = "com_github_hashicorp_go_hclog",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-hclog",
    sum = "h1:K4ev2ib4LdQETX5cSZBG0DVLk1jwGqSPXBjdah3veNs=",
    version = "v0.16.2",
)

go_repository(
    name = "com_github_hashicorp_go_plugin",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-plugin",
    sum = "h1:DXmvivbWD5qdiBts9TpBC7BYL1Aia5sxbRgQB+v6UZM=",
    version = "v1.4.3",
)

go_repository(
    name = "com_github_hashicorp_hcl_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/hcl/v2",
    sum = "h1:yTyWcXcm9XB0TEkyU/JCRU6rYy4K+mgLtzn2wlrJbcc=",
    version = "v2.11.1",
)

go_repository(
    name = "com_github_hashicorp_yamux",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/yamux",
    sum = "h1:kJCB4vdITiW1eC1vq2e6IsrXKrZit1bv/TDYFGMp4BQ=",
    version = "v0.0.0-20181012175058-2f1d1f20f75d",
)

go_repository(
    name = "com_github_iancoleman_orderedmap",
    build_file_proto_mode = "disable",
    importpath = "github.com/iancoleman/orderedmap",
    sum = "h1:sq1N/TFpYH++aViPcaKjys3bDClUEU7s5B+z6jq8pNA=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_imdario_mergo",
    build_file_proto_mode = "disable",
    importpath = "github.com/imdario/mergo",
    sum = "h1:M8XP7IuFNsqUx6VPK2P9OSmsYsI/YFaGil0uD21V3dM=",
    version = "v0.3.15",
)

go_repository(
    name = "com_github_jhump_protoreflect",
    build_file_proto_mode = "disable",
    importpath = "github.com/jhump/protoreflect",
    sum = "h1:h5jfMVslIg6l29nsMs0D8Wj17RDVdNYti0vDN/PZZoE=",
    version = "v1.6.0",
)

go_repository(
    name = "com_github_mitchellh_go_wordwrap",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/go-wordwrap",
    sum = "h1:6GlHJ/LTGMrIJbwgdqdl2eEH8o+Exx/0m8ir9Gns0u4=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_oklog_run",
    build_file_proto_mode = "disable",
    importpath = "github.com/oklog/run",
    sum = "h1:Ru7dDtJNOyC66gQ5dQmaCa0qIsAUFY3sFpK1Xk8igrw=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_pkg_diff",
    build_file_proto_mode = "disable",
    importpath = "github.com/pkg/diff",
    sum = "h1:aoZm08cpOy4WuID//EZDgcC4zIxODThtZNPirFr42+A=",
    version = "v0.0.0-20210226163009-20ebb0f2a09e",
)

go_repository(
    name = "com_github_terraform_docs_plugin_sdk",
    build_file_proto_mode = "disable",
    importpath = "github.com/terraform-docs/plugin-sdk",
    sum = "h1:WJXjngYRi9rtvzFFKjYuiWpax9R6tEiAA9givVSA4tw=",
    version = "v0.3.1-0.20210512170044-49b620c0a2da",
)

go_repository(
    name = "com_github_terraform_docs_terraform_config_inspect",
    build_file_proto_mode = "disable",
    importpath = "github.com/terraform-docs/terraform-config-inspect",
    sum = "h1:wdyf3TobwYFwsqnUGJcjdNHxKfwHPFbaOknBJehnF1M=",
    version = "v0.0.0-20210728164355-9c1f178932fa",
)

go_repository(
    name = "com_github_terraform_docs_terraform_docs",
    build_file_proto_mode = "disable",
    importpath = "github.com/terraform-docs/terraform-docs",
    sum = "h1:axEYKR5s7dbnhKNibAidOP4V5zu8+gNF9bLIupm1phY=",
    version = "v0.15.0",
)

go_repository(
    name = "com_github_vmihailenco_msgpack",
    build_file_proto_mode = "disable",
    importpath = "github.com/vmihailenco/msgpack",
    sum = "h1:wapg9xDUZDzGCNFlwc5SqI1rvcciqcxEHac4CYj89xI=",
    version = "v3.3.3+incompatible",
)

go_repository(
    name = "com_github_vmihailenco_msgpack_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/vmihailenco/msgpack/v4",
    sum = "h1:07s4sz9IReOgdikxLTKNbBdqDMLsjPKXwvCazn8G65U=",
    version = "v4.3.12",
)

go_repository(
    name = "com_github_vmihailenco_tagparser",
    build_file_proto_mode = "disable",
    importpath = "github.com/vmihailenco/tagparser",
    sum = "h1:quXMXlA39OCbd2wAdTsGDlK9RkOk6Wuw+x37wVyIuWY=",
    version = "v0.1.1",
)

go_repository(
    name = "com_github_zclconf_go_cty",
    build_file_proto_mode = "disable",
    importpath = "github.com/zclconf/go-cty",
    sum = "h1:mp9ZXQeIcN8kAwuqorjH+Q+njbJKjLrvB2yIh4q7U+0=",
    version = "v1.10.0",
)

go_repository(
    name = "com_github_zclconf_go_cty_debug",
    build_file_proto_mode = "disable",
    importpath = "github.com/zclconf/go-cty-debug",
    sum = "h1:FosyBZYxY34Wul7O/MSKey3txpPYyCqVO5ZyceuQJEI=",
    version = "v0.0.0-20191215020915-b22d67c1ba0b",
)

go_repository(
    name = "com_github_stevepartridge_geocodio",
    build_file_proto_mode = "disable",
    importpath = "github.com/stevepartridge/geocodio",
    sum = "h1:5k20BA0oV2wEKhlbzDaphCHQMz0yopJIC2Oq6W1uHdY=",
    version = "v0.0.0-20200810170545-39df88c9e52a",
)

go_repository(
    name = "org_golang_x_crypto",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/crypto",
    sum = "h1:kJNSjF/Xp7kU0iB2Z+9viTPMW4EqqsrywMXLJOOsXSE=",
    version = "v0.37.0",
)

go_repository(
    name = "org_golang_x_net",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/net",
    sum = "h1:vRMAPTMaeGqVhG5QyLJHqNDwecKTomGeqbnfZyKlBI8=",
    version = "v0.38.0",
)

go_repository(
    name = "org_golang_x_sys",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/sys",
    sum = "h1:s77OFDvIQeibCmezSnk/q6iAfkdiQaJi4VzroCFrN20=",
    version = "v0.32.0",
)

go_repository(
    name = "org_golang_x_text",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/text",
    sum = "h1:dd5Bzh4yt5KYA8f9CJHCP4FB4D51c2c6JvN37xJJkJ0=",
    version = "v0.24.0",
)

go_repository(
    name = "com_github_go_resty_resty_v2",
    build_file_proto_mode = "disable",
    build_naming_convention = "go_default_library",  # If we don't specify this, bazel build //:gazelle outputs incorrect reference to the target in BUILD.bazel deps
    importpath = "github.com/go-resty/resty/v2",
    sum = "h1:Z6IefCpUMfnvItVJaJXWv/pMiiD11So35QgwEELsldE=",
    version = "v2.1.0",
)

go_repository(
    name = "com_github_gorilla_mux",
    build_file_proto_mode = "disable",
    importpath = "github.com/gorilla/mux",
    sum = "h1:i40aqfkR1h2SlN9hojwV5ZA91wcXFOvkdNIeFDP5koI=",
    version = "v1.8.0",
)

go_repository(
    name = "com_github_davecgh_go_spew",
    build_file_proto_mode = "disable",
    importpath = "github.com/davecgh/go-spew",
    sum = "h1:vj9j/u1bqnvCEfJOwUhtlOARqs3+rkHYY13jYWTU97c=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_dgrijalva_jwt_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/dgrijalva/jwt-go",
    sum = "h1:7qlOGliEKZXTDg6OTjfoBKDXWrumCAMpl/TFQ4/5kLM=",
    version = "v3.2.0+incompatible",
)

go_repository(
    name = "com_github_getkin_kin_openapi",
    build_file_proto_mode = "disable",
    importpath = "github.com/getkin/kin-openapi",
    sum = "h1:z43njxPmJ7TaPpMSCQb7PN0dEYno4tyBPQcrFdHoLuM=",
    version = "v0.118.0",
)

go_repository(
    name = "com_github_ghodss_yaml",
    build_file_proto_mode = "disable",
    importpath = "github.com/ghodss/yaml",
    sum = "h1:wQHKEahhL6wmXdzwWG11gIVCkOv05bNOh+Rxn0yngAk=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_go_openapi_jsonpointer",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-openapi/jsonpointer",
    sum = "h1:gZr+CIYByUqjcgeLXnQu2gHYQC9o73G2XUeOFYEICuY=",
    version = "v0.19.5",
)

go_repository(
    name = "com_github_go_openapi_swag",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-openapi/swag",
    sum = "h1:yMBqmnQ0gyZvEb/+KzuWZOXgllrXT4SADYbvDaXHv/g=",
    version = "v0.22.3",
)

go_repository(
    name = "com_github_kr_pretty",
    build_file_proto_mode = "disable",
    importpath = "github.com/kr/pretty",
    sum = "h1:flRD4NNwYAUpkphVc1HcthR4KEIFJ65n8Mw5qdRn3LE=",
    version = "v0.3.1",
)

go_repository(
    name = "com_github_kr_pty",
    build_file_proto_mode = "disable",
    importpath = "github.com/kr/pty",
    sum = "h1:AkaSdXYQOWeaO3neb8EM634ahkXXe3jYbVh/F9lq+GI=",
    version = "v1.1.8",
)

go_repository(
    name = "com_github_kr_text",
    build_file_proto_mode = "disable",
    importpath = "github.com/kr/text",
    sum = "h1:5Nx0Ya0ZqY2ygV366QzturHI13Jq95ApcVaJBhpS+AY=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_labstack_echo_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/labstack/echo/v4",
    sum = "h1:vDZmA+qNeh1pd/cCkEicDMrjtrnMGQ1QFI9gWN1zGq8=",
    version = "v4.11.4",
)

go_repository(
    name = "com_github_labstack_gommon",
    build_file_proto_mode = "disable",
    importpath = "github.com/labstack/gommon",
    sum = "h1:F8qTUNXgG1+6WQmqoUWnz8WiEU60mXVVw0P4ht1WRA0=",
    version = "v0.4.2",
)

go_repository(
    name = "com_github_mailru_easyjson",
    build_file_proto_mode = "disable",
    importpath = "github.com/mailru/easyjson",
    sum = "h1:UGYAvKxe3sBsEDzO8ZeWOSlIQfWFlxbzLZe7hwFURr0=",
    version = "v0.7.7",
)

go_repository(
    name = "com_github_mattn_go_colorable",
    build_file_proto_mode = "disable",
    importpath = "github.com/mattn/go-colorable",
    sum = "h1:fFA4WZxdEF4tXPZVKMLwD8oUnCTTo08duU7wxecdEvA=",
    version = "v0.1.13",
)

go_repository(
    name = "com_github_mattn_go_isatty",
    build_file_proto_mode = "disable",
    importpath = "github.com/mattn/go-isatty",
    sum = "h1:xfD0iDuEKnDkl03q4limB+vH+GxLEtL/jb4xVJSWWEY=",
    version = "v0.0.20",
)

go_repository(
    name = "com_github_pkg_errors",
    build_file_proto_mode = "disable",
    importpath = "github.com/pkg/errors",
    sum = "h1:FEBLx1zS214owpjy7qsBeixbURkuhQAwrK5UwLGTwt4=",
    version = "v0.9.1",
)

go_repository(
    name = "com_github_pmezard_go_difflib",
    build_file_proto_mode = "disable",
    importpath = "github.com/pmezard/go-difflib",
    sum = "h1:Jamvg5psRIccs7FGNTlIRMkT8wgtp5eCXdBlqhYGL6U=",
    version = "v1.0.1-0.20181226105442-5d4384ee4fb2",
)

go_repository(
    name = "com_github_stretchr_objx",
    build_file_proto_mode = "disable",
    importpath = "github.com/stretchr/objx",
    sum = "h1:xuMeJ0Sdp5ZMRXx/aWO6RZxdr3beISkG5/G/aIRr3pY=",
    version = "v0.5.2",
)

go_repository(
    name = "com_github_stretchr_testify",
    build_file_proto_mode = "disable",
    importpath = "github.com/stretchr/testify",
    sum = "h1:Xv5erBjTwe/5IxqUQTdXv5kgmIvbHo3QQyRwhJsOfJA=",
    version = "v1.10.0",
)

go_repository(
    name = "com_github_valyala_bytebufferpool",
    build_file_proto_mode = "disable",
    importpath = "github.com/valyala/bytebufferpool",
    sum = "h1:GqA5TC/0021Y/b9FG4Oi9Mr3q7XYx6KllzawFIhcdPw=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_valyala_fasttemplate",
    build_file_proto_mode = "disable",
    importpath = "github.com/valyala/fasttemplate",
    sum = "h1:lxLXG0uE3Qnshl9QyaK6XJxMXlQZELvChBOCmQD0Loo=",
    version = "v1.2.2",
)

go_repository(
    name = "in_gopkg_check_v1",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/check.v1",
    sum = "h1:Hei/4ADfdWqJk1ZMxUNpqntNwaWcugrBjAiHlqqRiVk=",
    version = "v1.0.0-20201130134442-10cb98267c6c",
)

go_repository(
    name = "in_gopkg_yaml_v2",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/yaml.v2",
    sum = "h1:D8xgwECY7CYvx+Y2n4sBz93Jn9JRvxdiyyo8CTfuKaY=",
    version = "v2.4.0",
)

go_repository(
    name = "org_golang_x_sync",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/sync",
    sum = "h1:AauUjRAJ9OSnvULf/ARrrVywoJDy0YS2AwQ98I37610=",
    version = "v0.13.0",
)

go_repository(
    name = "org_golang_x_term",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/term",
    sum = "h1:erwDkOK1Msy6offm1mOgvspSkslFnIGsFnxOKoufg3o=",
    version = "v0.31.0",
)

go_repository(
    name = "org_golang_x_xerrors",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/xerrors",
    sum = "h1:noIWHXmPHxILtqtCOPIhSt0ABwskkZKjD3bXGnZGpNY=",
    version = "v0.0.0-20240903120638-7835f813f4da",
)

go_repository(
    name = "co_honnef_go_tools",
    build_file_proto_mode = "disable",
    importpath = "honnef.co/go/tools",
    sum = "h1:ws8AfbgTX3oIczLPNPCu5166oBg9ST2vNs0rcht+mDE=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_apmckinlay_gsuneido",
    build_file_proto_mode = "disable",
    importpath = "github.com/apmckinlay/gsuneido",
    sum = "h1:GGt+2fBzp83fE4DGSstL0K/crfQyXu7qOEKrvnwJRtU=",
    version = "v0.0.0-20180907175622-1f10244968e3",
)

go_repository(
    name = "com_github_burntsushi_toml",
    build_file_proto_mode = "disable",
    importpath = "github.com/BurntSushi/toml",
    sum = "h1:kuoIxZQy2WRRk1pttg9asf+WVv6tWQuBNVmK8+nqPr0=",
    version = "v1.4.0",
)

go_repository(
    name = "com_github_cespare_xxhash",
    build_file_proto_mode = "disable",
    importpath = "github.com/cespare/xxhash",
    sum = "h1:a6HrQnmkObjyL+Gs60czilIUGqrzKutQD6XZog3p+ko=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_client9_misspell",
    build_file_proto_mode = "disable",
    importpath = "github.com/client9/misspell",
    sum = "h1:ta993UF76GwbvJcIo3Y68y/M3WxlpEHPWIGDkJYwzJI=",
    version = "v0.3.4",
)

go_repository(
    name = "com_github_cockroachdb_apd",
    build_file_proto_mode = "disable",
    importpath = "github.com/cockroachdb/apd",
    sum = "h1:3LFP3629v+1aKXU5Q37mxmRxX/pIu1nijXydLShEq5I=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_coreos_go_semver",
    build_file_proto_mode = "disable",
    importpath = "github.com/coreos/go-semver",
    sum = "h1:wkHLiw0WNATZnSG7epLsujiMCgPAc9xhjJ4tgnAxmfM=",
    version = "v0.3.0",
)

go_repository(
    name = "com_github_coreos_go_systemd",
    build_file_proto_mode = "disable",
    importpath = "github.com/coreos/go-systemd",
    sum = "h1:JOrtw2xFKzlg+cbHpyrpLDmnN1HqhBfnX7WDiW7eG2c=",
    version = "v0.0.0-20190719114852-fd7a80b32e1f",
)

go_repository(
    name = "com_github_cpuguy83_go_md2man_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/cpuguy83/go-md2man/v2",
    sum = "h1:ZtcqGrnekaHpVLArFSe4HK5DoKx1T0rq2DwVB0alcyc=",
    version = "v2.0.5",
)

go_repository(
    name = "com_github_data_dog_go_sqlmock",
    build_file_proto_mode = "disable",
    importpath = "github.com/DATA-DOG/go-sqlmock",
    sum = "h1:ThlnYciV1iM/V0OSF/dtkqWb6xo5qITT1TJBG1MRDJM=",
    version = "v1.4.1",
)

go_repository(
    name = "com_github_denisenkom_go_mssqldb",
    build_file_proto_mode = "disable",
    importpath = "github.com/denisenkom/go-mssqldb",
    sum = "h1:QI6TK0VFv2CED1llq70WQGsUvw+WgWcTgsOtE7rDkUs=",
    version = "v0.10.1",
)

go_repository(
    name = "com_github_ericlagergren_decimal",
    build_file_proto_mode = "disable",
    importpath = "github.com/ericlagergren/decimal",
    sum = "h1:HQGCJNlqt1dUs/BhtEKmqWd6LWS+DWYVxi9+Jo4r0jE=",
    version = "v0.0.0-20181231230500-73749d4874d5",
)

go_repository(
    name = "com_github_friendsofgo_errors",
    build_file_proto_mode = "disable",
    importpath = "github.com/friendsofgo/errors",
    sum = "h1:X6NYxef4efCBdwI7BgS820zFaN7Cphrmb+Pljdzjtgk=",
    version = "v0.9.2",
)

go_repository(
    name = "com_github_fsnotify_fsnotify",
    build_file_proto_mode = "disable",
    importpath = "github.com/fsnotify/fsnotify",
    sum = "h1:n+5WquG0fcWoWp6xPWfHdbskMCQaFnG6PfBrh1Ky4HY=",
    version = "v1.6.0",
)

go_repository(
    name = "com_github_go_kit_kit",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-kit/kit",
    sum = "h1:wDJmvq38kDhkVxi50ni9ykkdUr1PKgqKOoi01fa0Mdk=",
    version = "v0.9.0",
)

go_repository(
    name = "com_github_go_logfmt_logfmt",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-logfmt/logfmt",
    sum = "h1:TrB8swr/68K7m9CcGut2g3UOihhbcbiMAYiuTXdEih4=",
    version = "v0.5.0",
)

go_repository(
    name = "com_github_go_sql_driver_mysql",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-sql-driver/mysql",
    sum = "h1:ozyZYNQW3x3HtqT1jira07DN2PArx2v7/mN66gGcHOs=",
    version = "v1.5.0",
)

go_repository(
    name = "com_github_go_stack_stack",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-stack/stack",
    sum = "h1:5SgMzNM5HxrEjV0ww2lTmX6E2Izsfxas4+YHWRs3Lsk=",
    version = "v1.8.0",
)

go_repository(
    name = "com_github_gofrs_uuid",
    build_file_proto_mode = "disable",
    importpath = "github.com/gofrs/uuid",
    sum = "h1:1SD/1F5pU8p29ybwgQSwpQk+mwdRrXCYuPhW6m+TnJw=",
    version = "v4.0.0+incompatible",
)

go_repository(
    name = "com_github_gogo_protobuf",
    build_file_proto_mode = "disable",
    importpath = "github.com/gogo/protobuf",
    sum = "h1:Ov1cvc58UF3b5XjBnZv7+opcTcQFZebYjWzi34vdm4Q=",
    version = "v1.3.2",
)

go_repository(
    name = "com_github_golang_glog",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang/glog",
    sum = "h1:DVjP2PbBOzHyzA+dn3WhHIq4NdVu3Q+pvivFICf/7fo=",
    version = "v1.1.2",
)

go_repository(
    name = "com_github_golang_groupcache",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang/groupcache",
    sum = "h1:oI5xCqsCo564l8iNU+DwB5epxmsaqB+rhGL0m5jtYqE=",
    version = "v0.0.0-20210331224755-41bb18bfe9da",
)

go_repository(
    name = "com_github_golang_mock",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang/mock",
    sum = "h1:YojYx61/OLFsiv6Rw1Z96LpldJIy31o+UHmwAUMJ6/U=",
    version = "v1.7.0-rc.1",
)

go_repository(
    name = "com_github_golang_protobuf",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang/protobuf",
    sum = "h1:KhyjKVUg7Usr/dYsdSqoFveMYd5ko72D+zANwlG1mmg=",
    version = "v1.5.3",
)

go_repository(
    name = "com_github_golang_sql_civil",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang-sql/civil",
    sum = "h1:lXe2qZdvpiX5WZkZR4hgp4KJVfY3nMkvmwbVkpv1rVY=",
    version = "v0.0.0-20190719163853-cb61b32ac6fe",
)

go_repository(
    name = "com_github_google_btree",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/btree",
    sum = "h1:gK4Kx5IaGY9CD5sPJ36FHiBJ6ZXl0kilRiiCj+jdYp4=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_google_go_cmp",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/go-cmp",
    sum = "h1:ofyhxvXcZhMsU5ulbFiLKl/XBFqE1GSq7atu8tAmTRI=",
    version = "v0.6.0",
)

go_repository(
    name = "com_github_google_gofuzz",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/gofuzz",
    sum = "h1:xRy4A+RhZaiKjJ1bPfwQ8sedCA+YS2YcCHW6ec7JMi0=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_gopherjs_gopherjs",
    build_file_proto_mode = "disable",
    importpath = "github.com/gopherjs/gopherjs",
    sum = "h1:EGx4pi6eqNxGaHF6qqu48+N2wcFQ5qg5FXgOdqsJ5d8=",
    version = "v0.0.0-20181017120253-0766667cb4d1",
)

go_repository(
    name = "com_github_gorilla_websocket",
    build_file_proto_mode = "disable",
    importpath = "github.com/gorilla/websocket",
    sum = "h1:saDtZ6Pbx/0u+bgYQ3q96pZgCzfhKXGPqt7kZ72aNNg=",
    version = "v1.5.3",
)

go_repository(
    name = "com_github_grpc_ecosystem_grpc_gateway",
    build_file_proto_mode = "disable",
    importpath = "github.com/grpc-ecosystem/grpc-gateway",
    sum = "h1:gmcG1KaJ57LophUzW0Hy8NmPhnMZb4M0+kPpLofRdBo=",
    version = "v1.16.0",
)

go_repository(
    name = "com_github_hashicorp_hcl",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/hcl",
    sum = "h1:0Anlzjpi4vEasTeNFn2mLJgTSwt0+6sfsiTG8qcWGx4=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_inconshreveable_mousetrap",
    build_file_proto_mode = "disable",
    importpath = "github.com/inconshreveable/mousetrap",
    sum = "h1:Z8tu5sraLXCXIcARxBp/8cbvlwVa7Z1NHg9XEKhtSvM=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_json_iterator_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/json-iterator/go",
    sum = "h1:PV8peI4a0ysnczrg+LtxykD8LfKY9ML6u2jnxaEnrnM=",
    version = "v1.1.12",
)

go_repository(
    name = "com_github_jtolds_gls",
    build_file_proto_mode = "disable",
    importpath = "github.com/jtolds/gls",
    sum = "h1:xdiiI2gbIgH/gLH7ADydsJ1uDOEzR8yvV7C0MuV77Wo=",
    version = "v4.20.0+incompatible",
)

go_repository(
    name = "com_github_kisielk_errcheck",
    build_file_proto_mode = "disable",
    importpath = "github.com/kisielk/errcheck",
    sum = "h1:e8esj/e4R+SAOwFwN+n3zr0nYeCyeweozKfO23MvHzY=",
    version = "v1.5.0",
)

go_repository(
    name = "com_github_kisielk_gotool",
    build_file_proto_mode = "disable",
    importpath = "github.com/kisielk/gotool",
    sum = "h1:AV2c/EiW3KqPNT9ZKl07ehoAGi4C5/01Cfbblndcapg=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_konsorten_go_windows_terminal_sequences",
    build_file_proto_mode = "disable",
    importpath = "github.com/konsorten/go-windows-terminal-sequences",
    sum = "h1:CE8S1cTafDpPvMhIxNJKvHsGVBgn1xWYf1NbHQhywc8=",
    version = "v1.0.3",
)

go_repository(
    name = "com_github_kr_logfmt",
    build_file_proto_mode = "disable",
    importpath = "github.com/kr/logfmt",
    sum = "h1:T+h1c/A9Gawja4Y9mFVWj2vyii2bbUNDw3kt9VxK2EY=",
    version = "v0.0.0-20140226030751-b84e30acd515",
)

go_repository(
    name = "com_github_lib_pq",
    build_file_proto_mode = "disable",
    importpath = "github.com/lib/pq",
    sum = "h1:Zx5DJFEYQXio93kgXnQ09fXNiUKsqv4OUEu2UtGcB1E=",
    version = "v1.10.0",
)

go_repository(
    name = "com_github_magiconair_properties",
    build_file_proto_mode = "disable",
    importpath = "github.com/magiconair/properties",
    sum = "h1:IeQXZAiQcpL9mgcAe1Nu6cX9LLw6ExEHKjN0VQdvPDY=",
    version = "v1.8.7",
)

go_repository(
    name = "com_github_mitchellh_go_homedir",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/go-homedir",
    sum = "h1:lukF9ziXFxDFPkA1vsr5zpc1XuPDn/wFntq5mG+4E0Y=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_mitchellh_mapstructure",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/mapstructure",
    sum = "h1:jeMsZIYE/09sWLaz43PL7Gy6RuMjD2eJVyuac5Z2hdY=",
    version = "v1.5.0",
)

go_repository(
    name = "com_github_modern_go_concurrent",
    build_file_proto_mode = "disable",
    importpath = "github.com/modern-go/concurrent",
    sum = "h1:TRLaZ9cD/w8PVh93nsPXa1VrQ6jlwL5oN8l14QlcNfg=",
    version = "v0.0.0-20180306012644-bacd9c7ef1dd",
)

go_repository(
    name = "com_github_modern_go_reflect2",
    build_file_proto_mode = "disable",
    importpath = "github.com/modern-go/reflect2",
    sum = "h1:xBagoLtFs94CBntxluKeaWgTMpvLxC4ur3nMaC9Gz0M=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_oneofone_xxhash",
    build_file_proto_mode = "disable",
    importpath = "github.com/OneOfOne/xxhash",
    sum = "h1:KMrpdQIwFcEqXDklaen+P1axHaj9BSKzvpUUfnHldSE=",
    version = "v1.2.2",
)

go_repository(
    name = "com_github_pelletier_go_toml",
    build_file_proto_mode = "disable",
    importpath = "github.com/pelletier/go-toml",
    sum = "h1:4yBQzkHv+7BHq2PQUZF3Mx0IYxG7LsP222s7Agd3ve8=",
    version = "v1.9.5",
)

go_repository(
    name = "com_github_rogpeppe_fastuuid",
    build_file_proto_mode = "disable",
    importpath = "github.com/rogpeppe/fastuuid",
    sum = "h1:Ppwyp6VYCF1nvBTXL3trRso7mXMlRrw9ooo375wvi2s=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_russross_blackfriday_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/russross/blackfriday/v2",
    sum = "h1:JIOH55/0cWyOuilr9/qlrm0BSXldqnqwMsf35Ld67mk=",
    version = "v2.1.0",
)

go_repository(
    name = "com_github_shopspring_decimal",
    build_file_proto_mode = "disable",
    importpath = "github.com/shopspring/decimal",
    sum = "h1:abSATXmQEYyShuxI4/vyW3tV1MrKAJzCZ/0zLUXYbsQ=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_shurcool_sanitized_anchor_name",
    build_file_proto_mode = "disable",
    importpath = "github.com/shurcooL/sanitized_anchor_name",
    sum = "h1:PdmoCO6wvbs+7yrJyMORt4/BmY5IYyJwS/kOiWx8mHo=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_sirupsen_logrus",
    build_file_proto_mode = "disable",
    importpath = "github.com/sirupsen/logrus",
    sum = "h1:dueUQJ1C2q9oE3F7wvmSGAaVtTmUizReu6fjN8uqzbQ=",
    version = "v1.9.3",
)

go_repository(
    name = "com_github_smartystreets_assertions",
    build_file_proto_mode = "disable",
    importpath = "github.com/smartystreets/assertions",
    sum = "h1:zE9ykElWQ6/NYmHa3jpm/yHnI4xSofP+UP6SpjHcSeM=",
    version = "v0.0.0-20180927180507-b2de0cb4f26d",
)

go_repository(
    name = "com_github_smartystreets_goconvey",
    build_file_proto_mode = "disable",
    importpath = "github.com/smartystreets/goconvey",
    sum = "h1:9RBaZCeXEQ3UselpuwUQHltGVXvdwm6cv1hgR6gDIPg=",
    version = "v1.7.2",
)

go_repository(
    name = "com_github_spaolacci_murmur3",
    build_file_proto_mode = "disable",
    importpath = "github.com/spaolacci/murmur3",
    sum = "h1:qLC7fQah7D6K1B0ujays3HV9gkFtllcxhzImRR7ArPQ=",
    version = "v0.0.0-20180118202830-f09979ecbc72",
)

go_repository(
    name = "com_github_spf13_afero",
    build_file_proto_mode = "disable",
    importpath = "github.com/spf13/afero",
    sum = "h1:xoax2sJ2DT8S8xA2paPFjDCScCNeWsg75VG0DLRreiY=",
    version = "v1.6.0",
)

go_repository(
    name = "com_github_spf13_cast",
    build_file_proto_mode = "disable",
    importpath = "github.com/spf13/cast",
    sum = "h1:s0hze+J0196ZfEMTs80N7UlFt0BDuQ7Q+JDnHiMWKdA=",
    version = "v1.4.1",
)

go_repository(
    name = "com_github_spf13_cobra",
    build_file_proto_mode = "disable",
    importpath = "github.com/spf13/cobra",
    sum = "h1:+KmjbUw1hriSNMF55oPrkZcb27aECyrj8V2ytv7kWDw=",
    version = "v1.2.1",
)

go_repository(
    name = "com_github_spf13_jwalterweatherman",
    build_file_proto_mode = "disable",
    importpath = "github.com/spf13/jwalterweatherman",
    sum = "h1:ue6voC5bR5F8YxI5S67j9i582FU4Qvo2bmqnqMYADFk=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_spf13_pflag",
    build_file_proto_mode = "disable",
    importpath = "github.com/spf13/pflag",
    sum = "h1:vN6T9TfwStFPFM5XzjsvmzZkLuaLX+HS+0SeFLRgU6M=",
    version = "v1.0.7",
)

go_repository(
    name = "com_github_spf13_viper",
    build_file_proto_mode = "disable",
    importpath = "github.com/spf13/viper",
    sum = "h1:yR6EXjTp0y0cLN8OZg1CRZmOBdI88UcGkhgyJhu6nZk=",
    version = "v1.9.0",
)

go_repository(
    name = "com_github_subosito_gotenv",
    build_file_proto_mode = "disable",
    importpath = "github.com/subosito/gotenv",
    sum = "h1:Slr1R9HxAlEKefgq5jn9U+DnETlIUa6HfgEzj0g5d7s=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_ugorji_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/ugorji/go",
    sum = "h1:qYhyWUUd6WbiM+C6JZAUkIJt/1WrjzNHY9+KCIjVqTo=",
    version = "v1.2.7",
)

go_repository(
    name = "com_github_volatiletech_inflect",
    build_file_proto_mode = "disable",
    importpath = "github.com/volatiletech/inflect",
    sum = "h1:2a6FcMQyhmPZcLa+uet3VJ8gLn/9svWhJxJYwvE8KsU=",
    version = "v0.0.1",
)

go_repository(
    name = "com_github_volatiletech_null_v8",
    build_file_proto_mode = "disable",
    importpath = "github.com/volatiletech/null/v8",
    sum = "h1:kiTiX1PpwvuugKwfvUNX/SU/5A2KGZMXfGD0DUHdKEI=",
    version = "v8.1.2",
)

go_repository(
    name = "com_github_volatiletech_randomize",
    build_file_proto_mode = "disable",
    importpath = "github.com/volatiletech/randomize",
    sum = "h1:eE5yajattWqTB2/eN8df4dw+8jwAzBtbdo5sbWC4nMk=",
    version = "v0.0.1",
)

go_repository(
    name = "com_github_volatiletech_sqlboiler_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/volatiletech/sqlboiler/v4",
    sum = "h1:TnxVoVsqQ69VFCyBuccAwHqqR5s/Z+3pBsMzGl/nfys=",
    version = "v4.10.2",
)

go_repository(
    name = "com_github_volatiletech_strmangle",
    build_file_proto_mode = "disable",
    importpath = "github.com/volatiletech/strmangle",
    sum = "h1:UZkTDFIjZcL1Lk4BXhGsxcyXxNcWuM5ZwdzZc0sJcWg=",
    version = "v0.0.8",
)

go_repository(
    name = "com_google_cloud_go",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go",
    sum = "h1:e7ITSqGFFk4rbz/JFIqZh3G4VEHguhAL4BQcFlWtU68=",
    version = "v0.110.9",
)

go_repository(
    name = "in_gopkg_inf_v0",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/inf.v0",
    sum = "h1:73M5CoZyi3ZLMOyDlQh031Cx6N9NDJ2Vvfl76EDAgDc=",
    version = "v0.9.1",
)

go_repository(
    name = "in_gopkg_ini_v1",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/ini.v1",
    sum = "h1:Dgnx+6+nfE+IfzjUEISNeydPJh9AXNNsWbGP9KzCsOA=",
    version = "v1.67.0",
)

go_repository(
    name = "org_golang_google_appengine",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/appengine",
    sum = "h1:FZR1q0exgwxzPzp/aF+VccGrSfxfPpkBqjIIEq3ru6c=",
    version = "v1.6.7",
)

go_repository(
    name = "org_golang_google_genproto",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/genproto",
    sum = "h1:I6WNifs6pF9tNdSob2W24JtyxIYjzFB9qDlpUC76q+U=",
    version = "v0.0.0-20231030173426-d783a09b4405",
)

go_repository(
    name = "org_golang_google_grpc",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/grpc",
    sum = "h1:Z5Iec2pjwb+LEOqzpB2MR12/eKFhDPhuqW91O+4bwUk=",
    version = "v1.59.0",
)

go_repository(
    name = "org_golang_x_lint",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/lint",
    sum = "h1:adDmSQyFTCiv19j015EGKJBoaa7ElV0Q1Wovb/4G7NA=",
    version = "v0.0.0-20241112194109-818c5a804067",
)

go_repository(
    name = "org_golang_x_oauth2",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/oauth2",
    sum = "h1:CrgCKl8PPAVtLnU3c+EDw6x11699EWlsDeWNWKdIOkc=",
    version = "v0.28.0",
)

go_repository(
    name = "org_golang_x_time",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/time",
    sum = "h1:/bpjEDfN9tkoN/ryeYHnv5hcMlc8ncjMcM4XBk5NWV0=",
    version = "v0.11.0",
)

go_repository(
    name = "org_uber_go_atomic",
    build_file_proto_mode = "disable",
    importpath = "go.uber.org/atomic",
    sum = "h1:ZvwS0R+56ePWxUNi+Atn9dWONBPp/AUETXlHW0DxSjE=",
    version = "v1.11.0",
)

go_repository(
    name = "org_uber_go_multierr",
    build_file_proto_mode = "disable",
    importpath = "go.uber.org/multierr",
    sum = "h1:blXXJkSxSSfBVBlC76pxqeO+LN3aDfLQo+309xJstO0=",
    version = "v1.11.0",
)

go_repository(
    name = "org_uber_go_zap",
    build_file_proto_mode = "disable",
    importpath = "go.uber.org/zap",
    sum = "h1:aJMhYGrd5QSmlpLMr2MftRKl7t8J8PTZPA732ud/XR8=",
    version = "v1.27.0",
)

go_repository(
    name = "com_github_pascaldekloe_name",
    build_file_proto_mode = "disable",
    importpath = "github.com/pascaldekloe/name",
    sum = "h1:n7LKFgHixETzxpRv2R77YgPUFo85QHGZKrdaYm7eY5U=",
    version = "v1.0.0",
)

go_repository(
    name = "in_gopkg_yaml_v3",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/yaml.v3",
    sum = "h1:fxVm/GzAzEWqLHuvctI91KS9hhNmmWOoWu0XTYJS7CA=",
    version = "v3.0.1",
)

go_repository(
    name = "com_github_apache_arrow_go_arrow",
    build_file_proto_mode = "disable",
    importpath = "github.com/apache/arrow/go/arrow",
    sum = "h1:HGREIyk0QRPt70R69Gm1JFHDgoiyYpCyuGE8E9k/nf0=",
    version = "v0.0.0-20211013220434-5962184e7a30",
)

go_repository(
    name = "com_github_form3tech_oss_jwt_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/form3tech-oss/jwt-go",
    sum = "h1:/l4kBbb4/vGSsdtB5nUe8L7B9mImVMaBPw9L/0TBHU8=",
    version = "v3.2.5+incompatible",
)

go_repository(
    name = "com_github_google_flatbuffers",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/flatbuffers",
    sum = "h1:M9dgRyhJemaM4Sw8+66GHBu8ioaQmyPLg1b8VwK5WJg=",
    version = "v23.5.26+incompatible",
)

go_repository(
    name = "com_github_google_uuid",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/uuid",
    sum = "h1:NIvaJDMOsjHA8n1jAhLSgzrAzy1Hgr+hNrb57e+94F0=",
    version = "v1.6.0",
)

go_repository(
    name = "com_github_pkg_browser",
    build_file_proto_mode = "disable",
    importpath = "github.com/pkg/browser",
    sum = "h1:+mdjkGKdHQG3305AYmdv1U2eRNDiU2ErMBj1gwrq8eQ=",
    version = "v0.0.0-20240102092130-5ac0b6a4141c",
)

go_repository(
    name = "com_github_snowflakedb_gosnowflake",
    build_file_proto_mode = "disable",
    importpath = "github.com/snowflakedb/gosnowflake",
    sum = "h1:eAMsxrCiC6ij5wX3dHx1TQCBOdDmCK062Ir8rndUkRg=",
    version = "v1.11.2",
)

go_repository(
    name = "com_github_aws_aws_sdk_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go",
    sum = "h1:L/aM1QwsqVia9qIqexTHwYN+lgLYuOtf11VDgz0YIyw=",
    version = "v1.42.19",
)

go_repository(
    name = "com_github_azure_go_ansiterm",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/go-ansiterm",
    sum = "h1:L/gRVlceqvL25UVaW/CKtUDjefjrs0SPonmDGUVOYP0=",
    version = "v0.0.0-20230124172434-306776ec8161",
)

go_repository(
    name = "com_github_bitly_go_hostpool",
    build_file_proto_mode = "disable",
    importpath = "github.com/bitly/go-hostpool",
    sum = "h1:mXoPYz/Ul5HYEDvkta6I8/rnYM5gSdSV2tJ6XbZuEtY=",
    version = "v0.0.0-20171023180738-a3a6125de932",
)

go_repository(
    name = "com_github_bkaradzic_go_lz4",
    build_file_proto_mode = "disable",
    importpath = "github.com/bkaradzic/go-lz4",
    sum = "h1:RXc4wYsyz985CkXXeX04y4VnZFGG8Rd43pRaHsOXAKk=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_bmizerany_assert",
    build_file_proto_mode = "disable",
    importpath = "github.com/bmizerany/assert",
    sum = "h1:DDGfHa7BWjL4YnC6+E63dPcxHo2sUxDIu8g3QgEJdRY=",
    version = "v0.0.0-20160611221934-b7ed37b82869",
)

go_repository(
    name = "com_github_burntsushi_xgb",
    build_file_proto_mode = "disable",
    importpath = "github.com/BurntSushi/xgb",
    sum = "h1:1BDTz0u9nC3//pOCMdNH+CiXJVYJh5UQNCOBG7jbELc=",
    version = "v0.0.0-20160522181843-27f122750802",
)

go_repository(
    name = "com_github_cenkalti_backoff_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/cenkalti/backoff/v4",
    sum = "h1:y4OZtCnogmCPw98Zjyt5a6+QwPLGkiQsYW5oUqylYbM=",
    version = "v4.2.1",
)

go_repository(
    name = "com_github_census_instrumentation_opencensus_proto",
    build_file_proto_mode = "disable",
    importpath = "github.com/census-instrumentation/opencensus-proto",
    sum = "h1:iKLQ0xPNFxR/2hzXZMrBo8f1j86j5WHzznCCQxV/b8g=",
    version = "v0.4.1",
)

go_repository(
    name = "com_github_chzyer_logex",
    build_file_proto_mode = "disable",
    importpath = "github.com/chzyer/logex",
    sum = "h1:Swpa1K6QvQznwJRcfTfQJmTE72DqScAa40E+fbHEXEE=",
    version = "v1.1.10",
)

go_repository(
    name = "com_github_chzyer_readline",
    build_file_proto_mode = "disable",
    importpath = "github.com/chzyer/readline",
    sum = "h1:fY5BOSpyZCqRo5OhCuC+XN+r/bBCmeuuJtjz+bCNIf8=",
    version = "v0.0.0-20180603132655-2972be24d48e",
)

go_repository(
    name = "com_github_chzyer_test",
    build_file_proto_mode = "disable",
    importpath = "github.com/chzyer/test",
    sum = "h1:q763qf9huN11kDQavWsoZXJNW3xEE4JJyHa5Q25/sd8=",
    version = "v0.0.0-20180213035817-a1ea475d72b1",
)

go_repository(
    name = "com_github_clickhouse_clickhouse_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/ClickHouse/clickhouse-go",
    sum = "h1:iAFMa2UrQdR5bHJ2/yaSLffZkxpcOYQMCUuKeNXGdqc=",
    version = "v1.4.3",
)

go_repository(
    name = "com_github_cloudflare_golz4",
    build_file_proto_mode = "disable",
    importpath = "github.com/cloudflare/golz4",
    sum = "h1:F1EaeKL/ta07PY/k9Os/UFtwERei2/XzGemhpGnBKNg=",
    version = "v0.0.0-20150217214814-ef862a3cdc58",
)

go_repository(
    name = "com_github_cncf_udpa_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/cncf/udpa/go",
    sum = "h1:QQ3GSy+MqSHxm/d8nCtnAiZdYFd45cYZPs8vOOIYKfk=",
    version = "v0.0.0-20220112060539-c52dc94e7fbe",
)

go_repository(
    name = "com_github_containerd_containerd",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/containerd",
    sum = "h1:cKwYKkP1eTj54bP3wCdXXBymmKRQMrWjkLSWZZJDa8o=",
    version = "v1.7.3",
)

go_repository(
    name = "com_github_creack_pty",
    build_file_proto_mode = "disable",
    importpath = "github.com/creack/pty",
    sum = "h1:n56/Zwd5o6whRC5PMGretI4IdRLlmBXYNjScPaBgsbY=",
    version = "v1.1.18",
)

go_repository(
    name = "com_github_cznic_mathutil",
    build_file_proto_mode = "disable",
    importpath = "github.com/cznic/mathutil",
    sum = "h1:iwZdTE0PVqJCos1vaoKsclOGD3ADKpshg3SRtYBbwso=",
    version = "v0.0.0-20181122101859-297441e03548",
)

go_repository(
    name = "com_github_dhui_dktest",
    build_file_proto_mode = "disable",
    importpath = "github.com/dhui/dktest",
    sum = "h1:0frpeeoM9pHouHjhLeZDuDTJ0PqjDTrycaHaMmkJAo8=",
    version = "v0.3.10",
)

go_repository(
    name = "com_github_docker_distribution",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/distribution",
    sum = "h1:T3de5rq0dB1j30rp0sA2rER+m322EBzniBPB6ZIzuh8=",
    version = "v2.8.2+incompatible",
)

go_repository(
    name = "com_github_docker_docker",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/docker",
    sum = "h1:HPGzNmwfLZWdxHqK9/II92pyi1EpYKsAqcl4G0Of9v0=",
    version = "v24.0.9+incompatible",
)

go_repository(
    name = "com_github_docker_go_connections",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/go-connections",
    sum = "h1:El9xVISelRB7BuFusrZozjnkIM5YnzCViNKohAFqRJQ=",
    version = "v0.4.0",
)

go_repository(
    name = "com_github_docker_go_units",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/go-units",
    sum = "h1:69rxXcBk27SvSaaxTtLh/8llcHD8vYHT7WSdRZ/jvr4=",
    version = "v0.5.0",
)

go_repository(
    name = "com_github_edsrzf_mmap_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/edsrzf/mmap-go",
    sum = "h1:aaQcKT9WumO6JEJcRyTqFVq4XUZiUcKR2/GI31TOcz8=",
    version = "v0.0.0-20170320065105-0bce6a688712",
)

go_repository(
    name = "com_github_envoyproxy_go_control_plane",
    build_file_proto_mode = "disable",
    importpath = "github.com/envoyproxy/go-control-plane",
    sum = "h1:wSUXTlLfiAQRWs2F+p+EKOY9rUyis1MyGqJ2DIk5HpM=",
    version = "v0.11.1",
)

go_repository(
    name = "com_github_envoyproxy_protoc_gen_validate",
    build_file_proto_mode = "disable",
    importpath = "github.com/envoyproxy/protoc-gen-validate",
    sum = "h1:QkIBuU5k+x7/QXPvPPnWXWlCdaBFApVqftFV6k087DA=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_fsouza_fake_gcs_server",
    build_file_proto_mode = "disable",
    importpath = "github.com/fsouza/fake-gcs-server",
    sum = "h1:OeH75kBZcZa3ZE+zz/mFdJ2btt9FgqfjI7gIh9+5fvk=",
    version = "v1.17.0",
)

go_repository(
    name = "com_github_go_gl_glfw",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-gl/glfw",
    sum = "h1:QbL/5oDUmRBzO9/Z7Seo6zf912W/a6Sr4Eu0G/3Jho0=",
    version = "v0.0.0-20190409004039-e6da0acd62b1",
)

go_repository(
    name = "com_github_go_gl_glfw_v3_3_glfw",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-gl/glfw/v3.3/glfw",
    sum = "h1:WtGNWLvXpe6ZudgnXrq0barxBImvnnJoMEhXAzcbM0I=",
    version = "v0.0.0-20200222043503-6f7a984d4dc4",
)

go_repository(
    name = "com_github_gobuffalo_here",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/here",
    sum = "h1:hYrd0a6gDmWxBM4TnrGw8mQg24iSVoIkHEk7FodQcBI=",
    version = "v0.6.0",
)

go_repository(
    name = "com_github_gocql_gocql",
    build_file_proto_mode = "disable",
    importpath = "github.com/gocql/gocql",
    sum = "h1:N/MD/sr6o61X+iZBAT2qEUF023s4KbA8RWfKzl0L6MQ=",
    version = "v0.0.0-20210515062232-b7ef815b4556",
)

go_repository(
    name = "com_github_golang_migrate_migrate_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang-migrate/migrate/v4",
    sum = "h1:vU+M05vs6jWHKDdmE1Ecwj0BznygFc4QsdRe2E/L7kc=",
    version = "v4.15.2",
)

go_repository(
    name = "com_github_golang_snappy",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang/snappy",
    sum = "h1:yAGX7huGHXlcLOEtBnF4w7FQwA26wojNCwOYAEhLjQM=",
    version = "v0.0.4",
)

go_repository(
    name = "com_github_google_go_querystring",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/go-querystring",
    sum = "h1:AnCroh3fv4ZBgVIf1Iwtovgjaw/GiKJo8M8yD/fhyJ8=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_google_martian",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/martian",
    sum = "h1:/CP5g8u/VJHijgedC/Legn3BAbAaWPgecwXBIDzw5no=",
    version = "v2.1.0+incompatible",
)

go_repository(
    name = "com_github_google_martian_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/martian/v3",
    sum = "h1:IqNFLAmvJOgVlpdEBiQbDc2EwKW77amAycfTuWKdfvw=",
    version = "v3.3.2",
)

go_repository(
    name = "com_github_google_pprof",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/pprof",
    sum = "h1:K6RDEckDVWvDI9JAJYCmNdQXq6neHJOYx3V6jnqNEec=",
    version = "v0.0.0-20210720184732-4bb14d4b1be1",
)

go_repository(
    name = "com_github_google_renameio",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/renameio",
    sum = "h1:GOZbcHa3HfsPKPlmyPyN2KEohoMXOhdMbHrvbpl2QaA=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_googleapis_gax_go_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/googleapis/gax-go/v2",
    sum = "h1:A+gCJKdRfqXkr+BIRGtZLibNXf0m1f9E4HG56etFpas=",
    version = "v2.12.0",
)

go_repository(
    name = "com_github_gorilla_handlers",
    build_file_proto_mode = "disable",
    importpath = "github.com/gorilla/handlers",
    sum = "h1:0QniY0USkHQ1RGCLfKxeNHK9bkDHGRYGNDFBCS+YARg=",
    version = "v1.4.2",
)

go_repository(
    name = "com_github_hailocab_go_hostpool",
    build_file_proto_mode = "disable",
    importpath = "github.com/hailocab/go-hostpool",
    sum = "h1:5upAirOpQc1Q53c0bnx2ufif5kANL7bfZWcc6VJWJd8=",
    version = "v0.0.0-20160125115350-e80d13ce29ed",
)

go_repository(
    name = "com_github_hashicorp_errwrap",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/errwrap",
    sum = "h1:OxrOeh75EUXMY8TBjag2fzXGZ40LB6IKw45YeGUDY2I=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_hashicorp_go_multierror",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-multierror",
    sum = "h1:H5DkEtf6CXdFp0N0Em5UCwQpXMWke8IA0+lD48awMYo=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_hashicorp_golang_lru",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/golang-lru",
    sum = "h1:0hERBMJE1eitiLkihrMvRVBYAkpHzc/J3QdDN+dAcgU=",
    version = "v0.5.1",
)

go_repository(
    name = "com_github_hpcloud_tail",
    build_file_proto_mode = "disable",
    importpath = "github.com/hpcloud/tail",
    sum = "h1:nfCOvKYfkgYP8hkirhJocXT2+zOD8yUNjXaWfTlyFKI=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_ianlancetaylor_demangle",
    build_file_proto_mode = "disable",
    importpath = "github.com/ianlancetaylor/demangle",
    sum = "h1:mV02weKRL81bEnm8A0HT1/CAelMQDBuQIfLw8n+d6xI=",
    version = "v0.0.0-20200824232613-28f6c0f3b639",
)

go_repository(
    name = "com_github_jackc_chunkreader",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/chunkreader",
    sum = "h1:4s39bBR8ByfqH+DKm8rQA3E1LHZWB9XWcrz8fqaZbe0=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_jackc_chunkreader_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/chunkreader/v2",
    sum = "h1:i+RDz65UE+mmpjTfyz0MoVTnzeYxroil2G82ki7MGG8=",
    version = "v2.0.1",
)

go_repository(
    name = "com_github_jackc_pgconn",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgconn",
    sum = "h1:FmjZ0rOyXTr1wfWs45i4a9vjnjWUAGpMuQLD9OSs+lw=",
    version = "v1.8.0",
)

go_repository(
    name = "com_github_jackc_pgio",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgio",
    sum = "h1:g12B9UwVnzGhueNavwioyEEpAmqMe1E/BN9ES+8ovkE=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_jackc_pgmock",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgmock",
    sum = "h1:JVX6jT/XfzNqIjye4717ITLaNwV9mWbJx0dLCpcRzdA=",
    version = "v0.0.0-20190831213851-13a1b77aafa2",
)

go_repository(
    name = "com_github_jackc_pgpassfile",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgpassfile",
    sum = "h1:/6Hmqy13Ss2zCq62VdNG8tM1wchn8zjSGOBJ6icpsIM=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_jackc_pgproto3",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgproto3",
    sum = "h1:FYYE4yRw+AgI8wXIinMlNjBbp/UitDJwfj5LqqewP1A=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_jackc_pgproto3_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgproto3/v2",
    sum = "h1:6Pwi1b3QdY65cuv6SyVO0FgPd5J3Bl7wf/nQQjinHMA=",
    version = "v2.0.7",
)

go_repository(
    name = "com_github_jackc_pgtype",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgtype",
    sum = "h1:b3pDeuhbbzBYcg5kwNmNDun4pFUD/0AAr1kLXZLeNt8=",
    version = "v1.6.2",
)

go_repository(
    name = "com_github_jackc_pgx_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgx/v4",
    sum = "h1:/6Q3ye4myIj6AaplUm+eRcz4OhK9HAvFf4ePsG40LJY=",
    version = "v4.10.1",
)

go_repository(
    name = "com_github_jackc_puddle",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/puddle",
    sum = "h1:JnPg/5Q9xVJGfjsO5CPUOjnJps1JaRUm8I9FXVCFK94=",
    version = "v1.1.3",
)

go_repository(
    name = "com_github_jmespath_go_jmespath",
    build_file_proto_mode = "disable",
    importpath = "github.com/jmespath/go-jmespath",
    sum = "h1:BEgLn5cpjn8UN1mAw4NjwDrS35OdebyEtFe+9YPoQUg=",
    version = "v0.4.0",
)

go_repository(
    name = "com_github_jmoiron_sqlx",
    build_file_proto_mode = "disable",
    importpath = "github.com/jmoiron/sqlx",
    sum = "h1:j82X0bf7oQ27XeqxicSZsTU5suPwKElg3oyxNn43iTk=",
    version = "v1.3.3",
)

go_repository(
    name = "com_github_jstemmer_go_junit_report",
    build_file_proto_mode = "disable",
    importpath = "github.com/jstemmer/go-junit-report",
    sum = "h1:6QPYqodiu3GuPL+7mfx+NwDdp2eTkp9IfEUpgAwUN0o=",
    version = "v0.9.1",
)

go_repository(
    name = "com_github_k0kubun_colorstring",
    build_file_proto_mode = "disable",
    importpath = "github.com/k0kubun/colorstring",
    sum = "h1:uC1QfSlInpQF+M0ao65imhwqKnz3Q2z/d8PWZRMQvDM=",
    version = "v0.0.0-20150214042306-9440f1994b88",
)

go_repository(
    name = "com_github_k0kubun_pp",
    build_file_proto_mode = "disable",
    importpath = "github.com/k0kubun/pp",
    sum = "h1:EKhKbi34VQDWJtq+zpsKSEhkHHs9w2P8Izbq8IhLVSo=",
    version = "v2.3.0+incompatible",
)

go_repository(
    name = "com_github_kardianos_osext",
    build_file_proto_mode = "disable",
    importpath = "github.com/kardianos/osext",
    sum = "h1:iQTw/8FWTuc7uiaSepXwyf3o52HaUYcV+Tu66S3F5GA=",
    version = "v0.0.0-20190222173326-2bc1f35cddc0",
)

go_repository(
    name = "com_github_ktrysmt_go_bitbucket",
    build_file_proto_mode = "disable",
    importpath = "github.com/ktrysmt/go-bitbucket",
    sum = "h1:C8dUGp0qkwncKtAnozHCbbqhptefzEd1I0sfnuy9rYQ=",
    version = "v0.6.4",
)

go_repository(
    name = "com_github_markbates_pkger",
    build_file_proto_mode = "disable",
    importpath = "github.com/markbates/pkger",
    sum = "h1:3MPelV53RnGSW07izx5xGxl4e/sdRD6zqseIk0rMASY=",
    version = "v0.15.1",
)

go_repository(
    name = "com_github_mattn_go_sqlite3",
    build_file_proto_mode = "disable",
    importpath = "github.com/mattn/go-sqlite3",
    sum = "h1:MLn+5bFRlWMGoSRmJour3CL1w/qL96mvipqpwQW/Sfk=",
    version = "v1.14.10",
)

go_repository(
    name = "com_github_microsoft_go_winio",
    build_file_proto_mode = "disable",
    importpath = "github.com/Microsoft/go-winio",
    sum = "h1:9/kr64B9VUZrLm5YYwbGtUJnMgqWVOdUAXu6Migciow=",
    version = "v0.6.1",
)

go_repository(
    name = "com_github_morikuni_aec",
    build_file_proto_mode = "disable",
    importpath = "github.com/morikuni/aec",
    sum = "h1:nP9CBfwrvYnBRgY6qfDQkygYDmYwOilePFkwzv4dU8A=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_mutecomm_go_sqlcipher_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/mutecomm/go-sqlcipher/v4",
    sum = "h1:sV1tWCWGAVlPhNGT95Q+z/txFxuhAYWwHD1afF5bMZg=",
    version = "v4.4.0",
)

go_repository(
    name = "com_github_nakagami_firebirdsql",
    build_file_proto_mode = "disable",
    importpath = "github.com/nakagami/firebirdsql",
    sum = "h1:P48LjvUQpTReR3TQRbxSeSBsMXzfK0uol7eRcr7VBYQ=",
    version = "v0.0.0-20190310045651-3c02a58cfed8",
)

go_repository(
    name = "com_github_neo4j_neo4j_go_driver",
    build_file_proto_mode = "disable",
    importpath = "github.com/neo4j/neo4j-go-driver",
    sum = "h1:fhFP5RliM2HW/8XdcO5QngSfFli9GcRIpMXvypTQt6E=",
    version = "v1.8.1-0.20200803113522-b626aa943eba",
)

go_repository(
    name = "com_github_onsi_ginkgo",
    build_file_proto_mode = "disable",
    importpath = "github.com/onsi/ginkgo",
    sum = "h1:29JGrr5oVBm5ulCWet69zQkzWipVXIol6ygQUe/EzNc=",
    version = "v1.16.4",
)

go_repository(
    name = "com_github_onsi_gomega",
    build_file_proto_mode = "disable",
    importpath = "github.com/onsi/gomega",
    sum = "h1:WjP/FQ/sk43MRmnEcT+MlDw2TFvkrXlprrPST/IudjU=",
    version = "v1.15.0",
)

go_repository(
    name = "com_github_opencontainers_go_digest",
    build_file_proto_mode = "disable",
    importpath = "github.com/opencontainers/go-digest",
    sum = "h1:apOUWs51W5PlhuyGyz9FCeeBIOUDA/6nW8Oi/yOhh5U=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_opencontainers_image_spec",
    build_file_proto_mode = "disable",
    importpath = "github.com/opencontainers/image-spec",
    sum = "h1:oOxKUJWnFC4YGHCCMNql1x4YaDfYBTS5Y4x/Cgeo1E0=",
    version = "v1.1.0-rc4",
)

go_repository(
    name = "com_github_pierrec_lz4",
    build_file_proto_mode = "disable",
    importpath = "github.com/pierrec/lz4",
    sum = "h1:2xWsjqPFWcplujydGg4WmhC/6fZqK42wMM8aXeqhl0I=",
    version = "v2.0.5+incompatible",
)

go_repository(
    name = "com_github_remyoudompheng_bigfft",
    build_file_proto_mode = "disable",
    importpath = "github.com/remyoudompheng/bigfft",
    sum = "h1:W09IVJc94icq4NjY3clb7Lk8O1qJ8BdBEF8z0ibU0rE=",
    version = "v0.0.0-20230129092748-24d4a6f8daec",
)

go_repository(
    name = "com_github_rogpeppe_go_internal",
    build_file_proto_mode = "disable",
    importpath = "github.com/rogpeppe/go-internal",
    sum = "h1:exVL4IDcn6na9z1rAb56Vxr+CgyK3nn3O+epU5NdKM8=",
    version = "v1.12.0",
)

go_repository(
    name = "com_github_rs_xid",
    build_file_proto_mode = "disable",
    importpath = "github.com/rs/xid",
    sum = "h1:mhH9Nq+C1fY2l1XIpgxIiUOfNpRBYH1kKcr+qfKgjRc=",
    version = "v1.2.1",
)

go_repository(
    name = "com_github_rs_zerolog",
    build_file_proto_mode = "disable",
    importpath = "github.com/rs/zerolog",
    sum = "h1:uPRuwkWF4J6fGsJ2R0Gn2jB1EQiav9k3S6CSdygQJXY=",
    version = "v1.15.0",
)

go_repository(
    name = "com_github_satori_go_uuid",
    build_file_proto_mode = "disable",
    importpath = "github.com/satori/go.uuid",
    sum = "h1:0uYX9dsZ2yD7q2RtLRtPSdGDWzjeM3TbMJP9utgA0ww=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_tidwall_pretty",
    build_file_proto_mode = "disable",
    importpath = "github.com/tidwall/pretty",
    sum = "h1:qjsOFOWWQl+N3RsoF5/ssm1pHmJJwhjlSbZ51I6wMl4=",
    version = "v1.2.1",
)

go_repository(
    name = "com_github_xanzy_go_gitlab",
    build_file_proto_mode = "disable",
    importpath = "github.com/xanzy/go-gitlab",
    sum = "h1:rWtwKTgEnXyNUGrOArN7yyc3THRkpYcKXIXia9abywQ=",
    version = "v0.15.0",
)

go_repository(
    name = "com_github_yuin_goldmark",
    build_file_proto_mode = "disable",
    importpath = "github.com/yuin/goldmark",
    sum = "h1:fVcFKWvrslecOb/tg+Cc05dkeYx540o0FuFt3nUVDoE=",
    version = "v1.4.13",
)

go_repository(
    name = "com_github_zenazn_goji",
    build_file_proto_mode = "disable",
    importpath = "github.com/zenazn/goji",
    sum = "h1:RSQQAbXGArQ0dIDEq+PI6WqN6if+5KHu6x2Cx/GXLTQ=",
    version = "v0.9.0",
)

go_repository(
    name = "com_gitlab_nyarla_go_crypt",
    build_file_proto_mode = "disable",
    importpath = "gitlab.com/nyarla/go-crypt",
    sum = "h1:7gd+rd8P3bqcn/96gOZa3F5dpJr/vEiDQYlNb/y2uNs=",
    version = "v0.0.0-20160106005555-d9a5dc2b789b",
)

go_repository(
    name = "com_google_cloud_go_bigquery",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/bigquery",
    sum = "h1:LHIc9E7Kw+ftFpQFKzZYBB88IAFz7qONawXXx0F3QBo=",
    version = "v1.56.0",
)

go_repository(
    name = "com_google_cloud_go_datastore",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/datastore",
    sum = "h1:0P9WcsQeTWjuD1H14JIY7XQscIPQ4Laje8ti96IC5vg=",
    version = "v1.15.0",
)

go_repository(
    name = "com_google_cloud_go_pubsub",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/pubsub",
    sum = "h1:6SPCPvWav64tj0sVX/+npCBKhUi/UjJehy9op/V3p2g=",
    version = "v1.33.0",
)

go_repository(
    name = "com_google_cloud_go_spanner",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/spanner",
    sum = "h1:l3exhhsVMKsx1E7Xd1QajYSvHmI1KZoWPW5tRxIIdvQ=",
    version = "v1.51.0",
)

go_repository(
    name = "com_google_cloud_go_storage",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/storage",
    sum = "h1:uOdMxAs8HExqBlnLtnQyP0YkvbiDpdGShGKtx6U/oNM=",
    version = "v1.30.1",
)

go_repository(
    name = "com_shuralyov_dmitri_gpu_mtl",
    build_file_proto_mode = "disable",
    importpath = "dmitri.shuralyov.com/gpu/mtl",
    sum = "h1:VpgP7xuJadIUuKccphEpTJnWhS2jkQyMt6Y7pJCD7fY=",
    version = "v0.0.0-20190408044501-666a987793e9",
)

go_repository(
    name = "in_gopkg_errgo_v2",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/errgo.v2",
    sum = "h1:0vLT13EuvQ0hNvakwLuFZ/jYrLp5F3kcWHXdRggjCE8=",
    version = "v2.1.0",
)

go_repository(
    name = "in_gopkg_fsnotify_v1",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/fsnotify.v1",
    sum = "h1:xOHLXZwVvI9hhs+cLKq5+I5onOuwQLhQwiu63xxlHs4=",
    version = "v1.4.7",
)

go_repository(
    name = "in_gopkg_inconshreveable_log15_v2",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/inconshreveable/log15.v2",
    sum = "h1:RlWgLqCMMIYYEVcAR5MDsuHlVkaIPDAF+5Dehzg8L5A=",
    version = "v2.0.0-20180818164646-67afb5ed74ec",
)

go_repository(
    name = "in_gopkg_tomb_v1",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/tomb.v1",
    sum = "h1:uRGJdciOHaEIrze2W8Q3AKkepLTh2hOroT7a+7czfdQ=",
    version = "v1.0.0-20141024135613-dd632973f1e7",
)

go_repository(
    name = "io_opencensus_go",
    build_file_proto_mode = "disable",
    importpath = "go.opencensus.io",
    sum = "h1:y73uSU6J157QMP2kn2r30vwW1A2W2WFwSCGnAVxeaD0=",
    version = "v0.24.0",
)

go_repository(
    name = "io_rsc_binaryregexp",
    build_file_proto_mode = "disable",
    importpath = "rsc.io/binaryregexp",
    sum = "h1:HfqmD5MEmC0zvwBuF187nq9mdnXjXsSivRiXN7SmRkE=",
    version = "v0.2.0",
)

go_repository(
    name = "org_golang_google_api",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/api",
    sum = "h1:RjPESny5CnQRn9V6siglged+DZCgfu9l6mO9dkX9VOg=",
    version = "v0.128.0",
)

go_repository(
    name = "org_golang_google_protobuf",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/protobuf",
    sum = "h1:6xV6lTsCfpGD21XK49h7MhtcApnLqkfYgPcdHftf6hg=",
    version = "v1.34.2",
)

go_repository(
    name = "org_golang_x_exp",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/exp",
    sum = "h1:nDVHiLt8aIbd/VzvPWN6kSOPE7+F/fNFDSXLVYkE/Iw=",
    version = "v0.0.0-20250305212735-054e65f0b394",
)

go_repository(
    name = "org_golang_x_image",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/image",
    sum = "h1:Y6uW6rH1y5y/LK1J8BPWZtr6yZ7hrsy6hFrXjgsc2fQ=",
    version = "v0.25.0",
)

go_repository(
    name = "org_golang_x_mobile",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/mobile",
    sum = "h1:4+4C/Iv2U4fMZBiMCc98MG1In4gJY5YRhtpDNeDeHWs=",
    version = "v0.0.0-20190719004257-d2bd2a29d028",
)

go_repository(
    name = "org_golang_x_mod",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/mod",
    sum = "h1:ZfthKaKaT4NrhGVZHO1/WDTwGES4De8KtWO0SIbNJMU=",
    version = "v0.24.0",
)

go_repository(
    name = "org_modernc_b",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/b",
    sum = "h1:vpvqeyp17ddcQWF29Czawql4lDdABCDRbXRAS4+aF2o=",
    version = "v1.0.0",
)

go_repository(
    name = "org_modernc_db",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/db",
    sum = "h1:2c6NdCfaLnshSvY7OU09cyAY0gYXUZj4lmg5ItHyucg=",
    version = "v1.0.0",
)

go_repository(
    name = "org_modernc_file",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/file",
    sum = "h1:9/PdvjVxd5+LcWUQIfapAWRGOkDLK90rloa8s/au06A=",
    version = "v1.0.0",
)

go_repository(
    name = "org_modernc_fileutil",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/fileutil",
    sum = "h1:Z1AFLZwl6BO8A5NldQg/xTSjGLetp+1Ubvl4alfGx8w=",
    version = "v1.0.0",
)

go_repository(
    name = "org_modernc_golex",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/golex",
    sum = "h1:wWpDlbK8ejRfSyi0frMyhilD3JBvtcx2AdGDnU+JtsE=",
    version = "v1.0.0",
)

go_repository(
    name = "org_modernc_internal",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/internal",
    sum = "h1:XMDsFDcBDsibbBnHB2xzljZ+B1yrOVLEFkKL2u15Glw=",
    version = "v1.0.0",
)

go_repository(
    name = "org_modernc_lldb",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/lldb",
    sum = "h1:6vjDJxQEfhlOLwl4bhpwIz00uyFK4EmSYcbwqwbynsc=",
    version = "v1.0.0",
)

go_repository(
    name = "org_modernc_mathutil",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/mathutil",
    sum = "h1:rV0Ko/6SfM+8G+yKiyI830l3Wuz1zRutdslNoQ0kfiQ=",
    version = "v1.5.0",
)

go_repository(
    name = "org_modernc_ql",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/ql",
    sum = "h1:bIQ/trWNVjQPlinI6jdOQsi195SIturGo3mp5hsDqVU=",
    version = "v1.0.0",
)

go_repository(
    name = "org_modernc_sortutil",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/sortutil",
    sum = "h1:oP3U4uM+NT/qBQcbg/K2iqAX0Nx7B1b6YZtq3Gk/PjM=",
    version = "v1.1.0",
)

go_repository(
    name = "org_modernc_strutil",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/strutil",
    sum = "h1:fNMm+oJklMGYfU9Ylcywl0CO5O6nTfaowNsh2wpPjzY=",
    version = "v1.1.3",
)

go_repository(
    name = "org_modernc_zappy",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/zappy",
    sum = "h1:dPVaP+3ueIUv4guk8PuZ2wiUGcJ1WUVvIheeSSTD0yk=",
    version = "v1.0.0",
)

go_repository(
    name = "org_mongodb_go_mongo_driver",
    build_file_proto_mode = "disable",
    importpath = "go.mongodb.org/mongo-driver",
    sum = "h1:4ayjakA013OdpGyL2K3ZqylTac/rMjrJOMZ1EHizXas=",
    version = "v1.11.4",
)

go_repository(
    name = "tools_gotest",
    build_file_proto_mode = "disable",
    importpath = "gotest.tools",
    sum = "h1:VsBPFP1AI068pPrMxtb/S8Zkgf9xEmTLJjfM+P5UIEo=",
    version = "v2.2.0+incompatible",
)

go_repository(
    name = "com_github_imroc_req",
    build_file_proto_mode = "disable",
    importpath = "github.com/imroc/req",
    sum = "h1:M/JkeU6RPmX+WYvT2vaaOL0K+q8ufL5LxwvJc4xeB4o=",
    version = "v0.3.2",
)

go_repository(
    name = "com_github_google_go_intervals",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/go-intervals",
    sum = "h1:FGrVEiUnTRKR8yE04qzXYaJMtnIYqobR5QbblK3ixcM=",
    version = "v0.0.2",
)

# Set up docker push using docker client
# ======================================
http_archive(
    name = "io_bazel_rules_docker",
    sha256 = "b1e80761a8a8243d03ebca8845e9cc1ba6c82ce7c5179ce2b295cd36f7e394bf",
    urls = ["https://github.com/bazelbuild/rules_docker/releases/download/v0.25.0/rules_docker-v0.25.0.tar.gz"],
)

load("@io_bazel_rules_docker//container:container.bzl", "container_pull")

container_pull(
    name = "ubuntu_base",
    registry = "index.docker.io",
    repository = "library/ubuntu",
    tag = "18.04",
)

container_pull(
    name = "ubuntu_22",
    registry = "index.docker.io",
    repository = "library/ubuntu",
    tag = "22.04",
)

# for building lambda with golang runtime
container_pull(
    name = "lambda_al2_runtime",
    architecture = "amd64",
    digest = "sha256:0867cccbf781abd2172f2aef69c5516127495aa211fbcaf7ecb9726d50d6d0b7",
    os = "linux",
    registry = "public.ecr.aws",
    repository = "lambda/provided",
    tag = "al2",
)

# for nirvanamq which needs git binary in the container
container_pull(
    name = "alpine_git",
    # tag v2.40.1
    digest = "sha256:1511563848b9c8f9524efa92d5ef184956cd52b3ac886a3204a7d3b421970b92",
    registry = "registry.hub.docker.com",
    repository = "alpine/git",
)

# Set up golang container support
# ===============================

load(
    "@io_bazel_rules_docker//repositories:repositories.bzl",
    container_repositories = "repositories",
)

container_repositories()

load(
    "@io_bazel_rules_docker//go:image.bzl",
    _go_image_repos = "repositories",
)

_go_image_repos()

# Setup nodejs container support
# ==============================

load(
    "@io_bazel_rules_docker//nodejs:image.bzl",
    _nodejs_image_repos = "repositories",
)

_nodejs_image_repos()

# needed for golink
load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository")

http_archive(
    name = "golink",
    sha256 = "c505a82b7180d4315bbaf05848e9b7d2683e80f1b16159af51a0ecae6fb2d54d",
    strip_prefix = "golink-1.1.0",
    urls = ["https://github.com/nikunjy/golink/archive/v1.1.0.tar.gz"],
)

go_repository(
    name = "com_github_cncf_xds_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/cncf/xds/go",
    sum = "h1:/inchEIKaYC1Akx+H+gqO04wryn5h75LSazbRlnya1k=",
    version = "v0.0.0-20230607035331-e9ce68804cb4",
)

go_repository(
    name = "io_opentelemetry_go_proto_otlp",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/proto/otlp",
    sum = "h1:T0TX0tmXU8a3CbNXzEKGeU5mIVOdf0oykP+u2lIVU/I=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_jmespath_go_jmespath_internal_testify",
    build_file_proto_mode = "disable",
    importpath = "github.com/jmespath/go-jmespath/internal/testify",
    sum = "h1:shLQSRRSCCPj3f2gpwzGwWFoC7ycTf1rcQZHOlsJ6N8=",
    version = "v1.5.1",
)

go_repository(
    name = "com_github_apparentlymart_go_cidr",
    build_file_proto_mode = "disable",
    importpath = "github.com/apparentlymart/go-cidr",
    sum = "h1:2mAhrMoF+nhXqxTzSZMUzDHkLjmIHC+Zzn4tdgBZjnU=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_apparentlymart_go_versions",
    build_file_proto_mode = "disable",
    importpath = "github.com/apparentlymart/go-versions",
    sum = "h1:ECIpSn0adcYNsBfSRwdDdz9fWlL+S/6EUd9+irwkBgU=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_bgentry_go_netrc",
    build_file_proto_mode = "disable",
    importpath = "github.com/bgentry/go-netrc",
    sum = "h1:xDfNPAt8lFiC1UJrqV3uuy861HCTo708pDMbjHHdCas=",
    version = "v0.0.0-20140422174119-9fd32a8b3d3d",
)

go_repository(
    name = "com_github_bmatcuk_doublestar",
    build_file_proto_mode = "disable",
    importpath = "github.com/bmatcuk/doublestar",
    sum = "h1:gPypJ5xD31uhX6Tf54sDPUOBXTqKH4c9aPY66CyQrS0=",
    version = "v1.3.4",
)

go_repository(
    name = "com_github_cheggaaa_pb",
    build_file_proto_mode = "disable",
    importpath = "github.com/cheggaaa/pb",
    sum = "h1:wIkZHkNfC7R6GI5w7l/PdAdzXzlrbcI3p8OAlnkTsnc=",
    version = "v1.0.27",
)

go_repository(
    name = "com_github_dave_dst",
    build_file_proto_mode = "disable",
    importpath = "github.com/dave/dst",
    sum = "h1:lnxLAKI3tx7MgLNVDirFCsDTlTG9nKTk7GcptKcWSwY=",
    version = "v0.26.2",
)

go_repository(
    name = "com_github_google_go_github_v35",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/go-github/v35",
    sum = "h1:fU+WBzuukn0VssbayTT+Zo3/ESKX9JYWjbZTLOTEyho=",
    version = "v35.3.0",
)

go_repository(
    name = "com_github_hashicorp_aws_sdk_go_base",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/aws-sdk-go-base",
    sum = "h1:J7MMLOfSoDWkusy+cSzKYG1/aFyCzYJmdE0mod3/WLw=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_hashicorp_go_getter",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-getter",
    sum = "h1:b7ahZW50iQiUek/at3CvZhPK1/jiV6CtKcsJiR6E4R0=",
    version = "v1.5.9",
)

go_repository(
    name = "com_github_hashicorp_go_safetemp",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/go-safetemp",
    sum = "h1:2HR189eFNrjHQyENnQMMpCiBAsRxzbTMIgBhEyExpmo=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_hashicorp_terraform_svchost",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/terraform-svchost",
    sum = "h1:HKLsbzeOsfXmKNpr3GiT18XAblV0BjCbzL8KQAMZGa0=",
    version = "v0.0.0-20200729002733-f050f53b9734",
)

go_repository(
    name = "com_github_jessevdk_go_flags",
    build_file_proto_mode = "disable",
    importpath = "github.com/jessevdk/go-flags",
    sum = "h1:1jKYvbxEjfUl0fmqTCOfonvskHHXMjBySTLW4y9LFvc=",
    version = "v1.5.0",
)

go_repository(
    name = "com_github_masterminds_semver_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/Masterminds/semver/v3",
    sum = "h1:hLg3sBzpNErnxhQtUy/mmLR2I9foDujNK030IGemrRc=",
    version = "v3.1.1",
)

go_repository(
    name = "com_github_mattn_go_runewidth",
    build_file_proto_mode = "disable",
    importpath = "github.com/mattn/go-runewidth",
    sum = "h1:UNAjwbU9l54TA3KzvqLGxwWjHmMgBUVhBiTjelZgg3U=",
    version = "v0.0.15",
)

go_repository(
    name = "com_github_owenrumney_go_sarif",
    build_file_proto_mode = "disable",
    importpath = "github.com/owenrumney/go-sarif",
    sum = "h1:8cgnqe7MbXGDJYEiMc0jeFi7opwgWM8GWBPAAnn2Ut8=",
    version = "v1.0.12",
)

go_repository(
    name = "com_github_serenize_snaker",
    build_file_proto_mode = "disable",
    importpath = "github.com/serenize/snaker",
    sum = "h1:zWKUYT07mGmVBH+9UgnHXd/ekCK99C8EbDSAt5qsjXE=",
    version = "v0.0.0-20201027110005-a7ad2135616e",
)

go_repository(
    name = "com_github_sourcegraph_go_lsp",
    build_file_proto_mode = "disable",
    importpath = "github.com/sourcegraph/go-lsp",
    sum = "h1:afLbh+ltiygTOB37ymZVwKlJwWZn+86syPTbrrOAydY=",
    version = "v0.0.0-20200429204803-219e11d77f5d",
)

go_repository(
    name = "com_github_sourcegraph_jsonrpc2",
    build_file_proto_mode = "disable",
    importpath = "github.com/sourcegraph/jsonrpc2",
    sum = "h1:ohJHjZ+PcaLxDUjqk2NC3tIGsVa5bXThe1ZheSXOjuk=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_terraform_linters_tflint",
    build_file_proto_mode = "disable",
    importpath = "github.com/terraform-linters/tflint",
    sum = "h1:6xN/CnJmuEBoJI9SbHmQJ4aDx7sguq1V9Czk36uVe+4=",
    version = "v0.33.2",
)

go_repository(
    name = "com_github_terraform_linters_tflint_plugin_sdk",
    build_file_proto_mode = "disable",
    importpath = "github.com/terraform-linters/tflint-plugin-sdk",
    sum = "h1:Q7+QmvkxrINjsxQ9iOR9GDIppS7kT2hXQBHNaZ+01ug=",
    version = "v0.9.1",
)

go_repository(
    name = "com_github_terraform_linters_tflint_ruleset_aws",
    build_file_proto_mode = "disable",
    importpath = "github.com/terraform-linters/tflint-ruleset-aws",
    sum = "h1:Ii/VxRKsHv6H7gRouh2ncf+1JLpRR3PD6dCgKAeEXYQ=",
    version = "v0.10.0",
)

go_repository(
    name = "com_github_ulikunitz_xz",
    build_file_proto_mode = "disable",
    importpath = "github.com/ulikunitz/xz",
    sum = "h1:ERv8V6GKqVi23rgu5cj9pVfVzJbOqAY2Ntl88O6c2nQ=",
    version = "v0.5.8",
)

go_repository(
    name = "com_github_zclconf_go_cty_yaml",
    build_file_proto_mode = "disable",
    importpath = "github.com/zclconf/go-cty-yaml",
    sum = "h1:dNyg4QLTrv2IfJpm7Wtxi55ed5gLGOlPrZ6kMd51hY0=",
    version = "v1.0.2",
)

go_repository(
    name = "in_gopkg_cheggaaa_pb_v1",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/cheggaaa/pb.v1",
    sum = "h1:kJdccidYzt3CaHD1crCFTS1hxyhSi059NhOFUf03YFo=",
    version = "v1.0.27",
)

go_repository(
    name = "com_github_mohae_deepcopy",
    build_file_proto_mode = "disable",
    importpath = "github.com/mohae/deepcopy",
    sum = "h1:RWengNIwukTxcDr9M+97sNutRR1RKhG96O6jWumTTnw=",
    version = "v0.0.0-20170929034955-c48cc78d4826",
)

go_repository(
    name = "com_github_richardlehane_mscfb",
    build_file_proto_mode = "disable",
    importpath = "github.com/richardlehane/mscfb",
    sum = "h1:rD8TBkYWkObWO0oLDFCbwMeZ4KoalxQy+QgniCj3nKI=",
    version = "v1.0.3",
)

go_repository(
    name = "com_github_richardlehane_msoleps",
    build_file_proto_mode = "disable",
    importpath = "github.com/richardlehane/msoleps",
    sum = "h1:RfrALnSNXzmXLbGct/P2b4xkFz4e8Gmj/0Vj9M9xC1o=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_xuri_efp",
    build_file_proto_mode = "disable",
    importpath = "github.com/xuri/efp",
    sum = "h1:EpI0bqf/eX9SdZDwlMmahKM+CDBgNbsXMhsN28XrM8o=",
    version = "v0.0.0-20210322160811-ab561f5b45e3",
)

go_repository(
    name = "com_github_xuri_excelize_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/xuri/excelize/v2",
    sum = "h1:veeeFLAJwsNEBPBlDepzPIYS1eLyBVcXNZUW79exZ1E=",
    version = "v2.4.1",
)

go_repository(
    name = "com_github_ajstarks_svgo",
    build_file_proto_mode = "disable",
    importpath = "github.com/ajstarks/svgo",
    sum = "h1:slYM766cy2nI3BwyRiyQj/Ud48djTMtMebDqepE95rw=",
    version = "v0.0.0-20211024235047-1546f124cd8b",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2",
    sum = "h1:cZV+NUS/eGxKXMtmyhtYPJ7Z4YLoI/V8bkTdRZfYhGo=",
    version = "v1.32.8",
)

go_repository(
    name = "com_github_boombuler_barcode",
    build_file_proto_mode = "disable",
    importpath = "github.com/boombuler/barcode",
    sum = "h1:s1TvRnXwL2xJRaccrdcBQMZxq6X7DvsMogtmJeHDdrc=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_go_fonts_liberation",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-fonts/liberation",
    sum = "h1:jAkAWJP4S+OsrPLZM4/eC9iW7CtHy+HBXrEwZXWo5VM=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_go_latex_latex",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-latex/latex",
    sum = "h1:6zl3BbBhdnMkpSj2YY30qV3gDcVBGtFgVsV3+/i+mKQ=",
    version = "v0.0.0-20210823091927-c0d11ff05a81",
)

go_repository(
    name = "com_github_go_logr_logr",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-logr/logr",
    sum = "h1:6pFjapn8bFcIbiKo3XT4j/BhANplGihG6tvd+8rYgrY=",
    version = "v1.4.2",
)

go_repository(
    name = "com_github_golang_freetype",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang/freetype",
    sum = "h1:DACJavvAHhabrF08vX0COfcOBJRhZ8lUbR+ZWIs0Y5g=",
    version = "v0.0.0-20170609003504-e2365dfdc4a0",
)

go_repository(
    name = "io_opentelemetry_go_otel",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel",
    sum = "h1:F2t8sK4qf1fAmY9ua4ohFS/K+FUuOPemHUIXHtktrts=",
    version = "v1.30.0",
)

go_repository(
    name = "io_opentelemetry_go_otel_sdk",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/sdk",
    sum = "h1:cHdik6irO49R5IysVhdn8oaiR9m8XluDaJAs4DfOrYE=",
    version = "v1.30.0",
)

go_repository(
    name = "io_opentelemetry_go_otel_trace",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/trace",
    sum = "h1:7UBkkYzeg3C7kQX8VAidWh2biiQbtAKjyIML8dQ9wmc=",
    version = "v1.30.0",
)

go_repository(
    name = "io_rsc_pdf",
    build_file_proto_mode = "disable",
    importpath = "rsc.io/pdf",
    sum = "h1:k1MczvYDUvJBe93bYd7wrZLLUEcLZAuF824/I4e5Xr4=",
    version = "v0.1.1",
)

go_repository(
    name = "org_gonum_v1_gonum",
    build_file_proto_mode = "disable",
    importpath = "gonum.org/v1/gonum",
    sum = "h1:xKuo6hzt+gMav00meVPUlXwSdoEJP46BR+wdxQEFK2o=",
    version = "v0.12.0",
)

go_repository(
    name = "org_gonum_v1_plot",
    build_file_proto_mode = "disable",
    importpath = "gonum.org/v1/plot",
    sum = "h1:dnifSs43YJuNMDzB7v8wV64O4ABBHReuAVAoBxqBqS4=",
    version = "v0.10.1",
)

go_repository(
    name = "com_github_bradfitz_latlong",
    build_file_proto_mode = "disable",
    importpath = "github.com/bradfitz/latlong",
    sum = "h1:wsnz4B2CSHJ09pwtMReU/GRqWDsI7XSasq7Nphem3Xk=",
    version = "v0.0.0-20170410180902-f3db6d0dff40",
)

go_repository(
    name = "com_github_jonas_p_go_shp",
    build_file_proto_mode = "disable",
    importpath = "github.com/jonas-p/go-shp",
    sum = "h1:LY81nN67DBCz6VNFn2kS64CjmnDo9IP8rmSkTvhO9jE=",
    version = "v0.1.1",
)

go_repository(
    name = "com_github_segmentio_backo_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/segmentio/backo-go",
    sum = "h1:kbOAtGJY2DqOR0jfRkYEorx/b18RgtepGtY3+Cpe6qA=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_xtgo_uuid",
    build_file_proto_mode = "disable",
    importpath = "github.com/xtgo/uuid",
    sum = "h1:3lbZUMbMiGUW/LMkfsEABsc5zNT9+b1CvsJx47JzJ8g=",
    version = "v0.0.0-20140804021211-a0b114877d4c",
)

go_repository(
    name = "in_gopkg_segmentio_analytics_go_v3",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/segmentio/analytics-go.v3",
    sum = "h1:UzxH1uaGZRpMKDhJyBz0pexz6yUoBU3x8bJsRk/HV6U=",
    version = "v3.1.0",
)

go_repository(
    name = "com_github_golang_jwt_jwt",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang-jwt/jwt",
    sum = "h1:IfV12K8xAKAnZqdXVzCZ+TOjboZ2keLg81eXfW3O+oY=",
    version = "v3.2.2+incompatible",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_aws_protocol_eventstream",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream",
    sum = "h1:xDAuZTn4IMm8o1LnBZvmrL8JA1io4o3YWNXgohbf20g=",
    version = "v1.6.5",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_config",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/config",
    sum = "h1:p33fDDihFC390dhhuv8nOmX419wjOSDQRb+USt20RrU=",
    version = "v1.27.43",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_credentials",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/credentials",
    sum = "h1:7gXo+Axmp+R4Z+AK8YFQO0ZV3L0gizGINCOWxSLY9W8=",
    version = "v1.17.41",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_feature_ec2_imds",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/feature/ec2/imds",
    sum = "h1:TMH3f/SCAWdNtXXVPPu5D6wrr4G5hI1rAxbcocKfC7Q=",
    version = "v1.16.17",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_internal_configsources",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/internal/configsources",
    sum = "h1:I/5wmGMffY4happ8NOCuIUEWGUvvFp5NSeQcXl9RHcI=",
    version = "v1.3.26",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_internal_endpoints_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/internal/endpoints/v2",
    sum = "h1:zXFLuEuMMUOvEARXFUVJdfqZ4bvvSgdGRq/ATcrQxzM=",
    version = "v2.6.26",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_internal_ini",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/internal/ini",
    sum = "h1:VaRN3TlFdd6KxX1x3ILT5ynH6HvKgqdiXoTxAF4HQcQ=",
    version = "v1.8.1",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_internal_accept_encoding",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding",
    sum = "h1:iXtILhvDxB6kPvEXgsDhGaZCSC6LQET5ZHSdJozeI0Y=",
    version = "v1.12.1",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_internal_presigned_url",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/internal/presigned-url",
    sum = "h1:s7NA1SOw8q/5c0wr8477yOPp0z+uBaXBnLE0XYb0POA=",
    version = "v1.12.2",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_internal_s3shared",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/internal/s3shared",
    sum = "h1:eb+tFOIl9ZsUe2259/BKPeniKuz4/02zZFH/i4Nf8Rg=",
    version = "v1.17.18",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_s3",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/s3",
    sum = "h1:3zt8qqznMuAZWDTDpcwv9Xr11M/lVj2FsRR7oYBt0OA=",
    version = "v1.63.3",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_sso",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/sso",
    sum = "h1:bSYXVyUzoTHoKalBmwaZxs97HU9DWWI3ehHSAMa7xOk=",
    version = "v1.24.2",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_sts",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/sts",
    sum = "h1:CiS7i0+FUe+/YY1GvIBLLrR/XNGZ4CtM1Ll0XavNuVo=",
    version = "v1.32.2",
)

go_repository(
    name = "com_github_aws_smithy_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/smithy-go",
    sum = "h1:/HPHZQ0g7f4eUeK6HKglFz8uwVfZKgoI25rb/J+dnro=",
    version = "v1.22.1",
)

go_repository(
    name = "com_github_leekchan_accounting",
    build_file_proto_mode = "disable",
    importpath = "github.com/leekchan/accounting",
    sum = "h1:6cIBKG9QngR6tuVV+mWjzcxsJDnoegrc70Ntb3MFqYM=",
    version = "v0.3.1",
)

go_repository(
    name = "com_github_data_dog_go_txdb",
    build_file_proto_mode = "disable",
    importpath = "github.com/DATA-DOG/go-txdb",
    sum = "h1:kKzz+LYk9qw1+fMyo8/9yDQiNXrJ2HbfX/TY61HkkB4=",
    version = "v0.1.5",
)

go_repository(
    name = "io_rsc_quote_v3",
    build_file_proto_mode = "disable",
    importpath = "rsc.io/quote/v3",
    sum = "h1:9JKUTTIUgS6kzR9mK1YuGKv6Nl+DijDNIc0ghT58FaY=",
    version = "v3.1.0",
)

go_repository(
    name = "io_rsc_sampler",
    build_file_proto_mode = "disable",
    importpath = "rsc.io/sampler",
    sum = "h1:7uVkIFmeBqHfdjD+gZwtXXI+RODJ2Wc4O7MPEh/QiW4=",
    version = "v1.3.0",
)

go_repository(
    name = "com_github_blynn_nex",
    build_file_proto_mode = "disable",
    importpath = "github.com/blynn/nex",
    sum = "h1:n52kgfiPyHIfpQxmzlN7twzaKtQy1sgW7RrYhIPxdIk=",
    version = "v0.0.0-**************-1a3320dab988",
)

go_repository(
    name = "com_github_cznic_golex",
    build_file_proto_mode = "disable",
    importpath = "github.com/cznic/golex",
    sum = "h1:G8zTsaqyVfIHpgMFcGgdbhHSFhlNc77rAKkhVbQ9kQg=",
    version = "v0.0.0-20181122101858-9c343928389c",
)

go_repository(
    name = "com_github_cznic_parser",
    build_file_proto_mode = "disable",
    importpath = "github.com/cznic/parser",
    sum = "h1:uWcWCkSP+E1w1z8r082miT+c+9vzg+5UdrgGCo15lMo=",
    version = "v0.0.0-20160622100904-31edd927e5b1",
)

go_repository(
    name = "com_github_cznic_sortutil",
    build_file_proto_mode = "disable",
    importpath = "github.com/cznic/sortutil",
    sum = "h1:LpMLYGyy67BoAFGda1NeOBQwqlv7nUXpm+rIVHGxZZ4=",
    version = "v0.0.0-20181122101858-f5f958428db8",
)

go_repository(
    name = "com_github_cznic_strutil",
    build_file_proto_mode = "disable",
    importpath = "github.com/cznic/strutil",
    sum = "h1:0rkFMAbn5KBKNpJyHQ6Prb95vIKanmAe62KxsrN+sqA=",
    version = "v0.0.0-20171016134553-529a34b1c186",
)

go_repository(
    name = "com_github_cznic_y",
    build_file_proto_mode = "disable",
    importpath = "github.com/cznic/y",
    sum = "h1:N2rDAvHuM46OGscJkGX4Dw4BBqZgg6mGNGLYs5utVVo=",
    version = "v0.0.0-20170802143616-045f81c6662a",
)

go_repository(
    name = "com_github_ngaut_log",
    build_file_proto_mode = "disable",
    importpath = "github.com/ngaut/log",
    sum = "h1:yAdflNJJ0W/AGi5dapdvp9jZHnkGV6ZOlW1A3z/oTY8=",
    version = "v0.0.0-20160810023011-cec23d3e10b0",
)

go_repository(
    name = "com_github_pingcap_check",
    build_file_proto_mode = "disable",
    importpath = "github.com/pingcap/check",
    sum = "h1:USx2/E1bX46VG32FIw034Au6seQ2fY9NEILmNh/UlQg=",
    version = "v0.0.0-20190102082844-67f458068fc8",
)

go_repository(
    name = "com_github_pingcap_log",
    build_file_proto_mode = "disable",
    importpath = "github.com/pingcap/log",
    sum = "h1:ERrF0fTuIOnwfGbt71Ji3DKbOEaP189tjym50u8gpC8=",
    version = "v0.0.0-20210317133921-96f4fcab92a4",
)

go_repository(
    name = "com_github_pingcap_parser",
    build_file_proto_mode = "disable",
    importpath = "github.com/pingcap/parser",
    sum = "h1:FkVEC3Fck3fD16hMObMl/IWs72jR9FmqPn0Bdf728Sk=",
    version = "v0.0.0-20210415081931-48e7f467fd74",
)

go_repository(
    name = "com_github_siddontang_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/siddontang/go",
    sum = "h1:xT+JlYxNGqyT+XcU8iUrN18JYed2TvG9yN5ULG2jATM=",
    version = "v0.0.0-20180604090527-bdc77568d726",
)

go_repository(
    name = "com_github_siddontang_go_log",
    build_file_proto_mode = "disable",
    importpath = "github.com/siddontang/go-log",
    sum = "h1:oI+RNwuC9jF2g2lP0u0cVEEZrc/AYBCuFdvwrLWM/6Q=",
    version = "v0.0.0-20180807004314-8d05993dda07",
)

go_repository(
    name = "com_github_siddontang_go_mysql",
    build_file_proto_mode = "disable",
    importpath = "github.com/siddontang/go-mysql",
    replace = "github.com/go-mysql-org/go-mysql",
    sum = "h1:lpNqkwdPzIrYSZGdqt8HIgAXZaK6VxBNfr8f7Z4FgGg=",
    version = "v1.3.0",
)

go_repository(
    name = "in_gopkg_natefinch_lumberjack_v2",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/natefinch/lumberjack.v2",
    sum = "h1:1Lc07Kr7qY4U2YPouBjpCLxpiyxIVoxqXgkXLknAOE8=",
    version = "v2.0.0",
)

go_repository(
    name = "org_uber_go_tools",
    build_file_proto_mode = "disable",
    importpath = "go.uber.org/tools",
    sum = "h1:0mgffUl7nfd+FpvXMVz4IDEaUSmT1ysygQC7qYo7sG4=",
    version = "v0.0.0-20190618225709-2cfd321de3ee",
)

go_repository(
    name = "com_github_cactus_go_statsd_client_v5",
    build_file_proto_mode = "disable",
    importpath = "github.com/cactus/go-statsd-client/v5",
    sum = "h1:srmsVzS+4K+mIYADzXiYZoXQOwwGHQFqHOh5oDGipEY=",
    version = "v5.0.1-0.20220206212030-1cec3953a711",
)

go_repository(
    name = "com_github_gregjones_httpcache",
    build_file_proto_mode = "disable",
    importpath = "github.com/gregjones/httpcache",
    sum = "h1:pdN6V1QBWetyv/0+wjACpqVH+eVULgEjkurDLq3goeM=",
    version = "v0.0.0-20180305231024-9cad4c3443a7",
)

go_repository(
    name = "com_github_josharian_intern",
    build_file_proto_mode = "disable",
    importpath = "github.com/josharian/intern",
    sum = "h1:vlS4z54oSdjm0bgjRigI+G1HpF+tI+9rE5LLzOg8HmY=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_karlseguin_expect",
    build_file_proto_mode = "disable",
    importpath = "github.com/karlseguin/expect",
    sum = "h1:vJ0Snvo+SLMY72r5J4sEfkuE7AFbixEP2qRbEcum/wA=",
    version = "v1.0.2-0.20190806010014-778a5f0c6003",
)

go_repository(
    name = "com_github_launchdarkly_ccache",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/ccache",
    sum = "h1:voD1M+ZJXR3MREOKtBwgTF9hYHl1jg+vFKS/+VAkR2k=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_launchdarkly_eventsource",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/eventsource",
    sum = "h1:5SbcIqzUomn+/zmJDrkb4LYw7ryoKFzH/0TbR0/3Bdg=",
    version = "v1.6.2",
)

go_repository(
    name = "com_github_launchdarkly_go_ntlm_proxy_auth",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-ntlm-proxy-auth",
    sum = "h1:Iz5cg9mB/0vt5llZE+J0iGQ5+O/U8CWuRUUR7Ou8eNM=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_launchdarkly_go_ntlmssp",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-ntlmssp",
    sum = "h1:snB77118TQvf9tfHrkSyrIop/UX5e5VD2D2mv7Kh3wE=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_launchdarkly_go_semver",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-semver",
    sum = "h1:sYVRnuKyvxlmQCnCUyDkAhtmzSFRoX6rG2Xa21Mhg+w=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_launchdarkly_go_test_helpers_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-test-helpers/v2",
    sum = "h1:L3kGILP/6ewikhzhdNkHy1b5y4zs50LueWenVF0sBbs=",
    version = "v2.2.0",
)

go_repository(
    name = "com_github_patrickmn_go_cache",
    build_file_proto_mode = "disable",
    importpath = "github.com/patrickmn/go-cache",
    sum = "h1:HRMgzkcYKYpi3C8ajMPV8OFXaaRUnok+kx1WdO15EQc=",
    version = "v2.1.0+incompatible",
)

go_repository(
    name = "com_github_wsxiaoys_terminal",
    build_file_proto_mode = "disable",
    importpath = "github.com/wsxiaoys/terminal",
    sum = "h1:3UeQBvD0TFrlVjOeLOBz+CPAI8dnbqNSVwUwRrkp7vQ=",
    version = "v0.0.0-20160513160801-0940f3fc43a0",
)

go_repository(
    name = "in_gopkg_ghodss_yaml_v1",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/ghodss/yaml.v1",
    sum = "h1:JlY4R6oVz+ZSvcDhVfNQ/k/8Xo6yb2s1PBhslPZPX4c=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_huandu_xstrings",
    build_file_proto_mode = "disable",
    importpath = "github.com/huandu/xstrings",
    sum = "h1:4jgBlKK6tLKFvO8u5pmYjG91cqytmDCDvGh7ECVFfFs=",
    version = "v1.3.1",
)

go_repository(
    name = "com_github_kballard_go_shellquote",
    build_file_proto_mode = "disable",
    importpath = "github.com/kballard/go-shellquote",
    sum = "h1:Z9n2FFNUXsshfwJMBgNA0RU6/i7WVaAegv3PtuIHPMs=",
    version = "v0.0.0-20180428030007-95032a82bc51",
)

go_repository(
    name = "com_github_masterminds_goutils",
    build_file_proto_mode = "disable",
    importpath = "github.com/Masterminds/goutils",
    sum = "h1:5nUrii3FMTL5diU80unEVvNevw1nH4+ZV4DSLVJLSYI=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_masterminds_sprig_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/Masterminds/sprig/v3",
    sum = "h1:17jRggJu518dr3QaafizSXOjKYp94wKfABxUmyxvxX8=",
    version = "v3.2.2",
)

go_repository(
    name = "com_github_mitchellh_copystructure",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/copystructure",
    sum = "h1:vpKXTN4ewci03Vljg/q9QvCGUDttBOGBIa15WveJJGw=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_mitchellh_reflectwalk",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/reflectwalk",
    sum = "h1:G2LzWKi524PWgd3mLHV8Y5k7s6XUvT0Gef6zxSIeXaQ=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_sagikazarmark_crypt",
    build_file_proto_mode = "disable",
    importpath = "github.com/sagikazarmark/crypt",
    sum = "h1:AyO7PGna28P9TMH93Bsxd7m9QC4xE6zyGQTXCo7ZrA8=",
    version = "v0.1.0",
)

go_repository(
    name = "com_lukechampine_uint128",
    build_file_proto_mode = "disable",
    importpath = "lukechampine.com/uint128",
    sum = "h1:cDdUVfRwDUDovz610ABgFD17nXD4/uDgVHl2sC3+sbo=",
    version = "v1.3.0",
)

go_repository(
    name = "org_golang_google_grpc_cmd_protoc_gen_go_grpc",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/grpc/cmd/protoc-gen-go-grpc",
    sum = "h1:rNBFJjBCOgVr9pWD7rs/knKL4FRTKgpZmsRfV214zcA=",
    version = "v1.3.0",
)

go_repository(
    name = "org_modernc_cc_v3",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/cc/v3",
    sum = "h1:P3g79IUS/93SYhtoeaHW+kRCIrYaxJ27MFPv+7kaTOw=",
    version = "v3.40.0",
)

go_repository(
    name = "org_modernc_ccgo_v3",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/ccgo/v3",
    sum = "h1:Mkgdzl46i5F/CNR/Kj80Ri59hC8TKAhZrYSaqvkwzUw=",
    version = "v3.16.13",
)

go_repository(
    name = "org_modernc_ccorpus",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/ccorpus",
    sum = "h1:K0qPfpVG1MJh5BYazccnmhywH4zHuOgJXgbjzyp6dWA=",
    version = "v1.11.1",
)

go_repository(
    name = "org_modernc_httpfs",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/httpfs",
    sum = "h1:AAgIpFZRXuYnkjftxTAZwMIiwEqAfk8aVB2/oA6nAeM=",
    version = "v1.0.6",
)

go_repository(
    name = "org_modernc_libc",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/libc",
    sum = "h1:wymSbZb0AlrjdAVX3cjreCHTPCpPARbQXNz6BHPzdwQ=",
    version = "v1.22.4",
)

go_repository(
    name = "org_modernc_memory",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/memory",
    sum = "h1:N+/8c5rE6EqugZwHii4IFsaJ7MUhoWX07J5tC/iI5Ds=",
    version = "v1.5.0",
)

go_repository(
    name = "org_modernc_opt",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/opt",
    sum = "h1:3XOZf2yznlhC+ibLltsDGzABUGVx8J6pnFMS3E4dcq4=",
    version = "v0.1.3",
)

go_repository(
    name = "org_modernc_sqlite",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/sqlite",
    sum = "h1:ixuUG0QS413Vfzyx6FWx6PYTmHaOegTY+hjzhn7L+a0=",
    version = "v1.21.2",
)

go_repository(
    name = "org_modernc_tcl",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/tcl",
    sum = "h1:vux2MNFhSXYqD8Kq4Uc9RjWcgv2c7Atx3da3VpLPPEw=",
    version = "v1.10.0",
)

go_repository(
    name = "org_modernc_token",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/token",
    sum = "h1:Xl7Ap9dKaEs5kLoOQeQmPWevfnk/DM5qcLcYlA8ys6Y=",
    version = "v1.1.0",
)

go_repository(
    name = "org_modernc_z",
    build_file_proto_mode = "disable",
    importpath = "modernc.org/z",
    sum = "h1:4RWULo1Nvaq5ZBhbLe74u8p6tV4Mmm0ZrPBXYPm/xjM=",
    version = "v1.3.0",
)

go_repository(
    name = "com_github_grpc_ecosystem_go_grpc_middleware",
    build_file_proto_mode = "disable",
    importpath = "github.com/grpc-ecosystem/go-grpc-middleware",
    sum = "h1:+9834+KizmvFV7pXQGSXQTsaWhq2GjuNUt0aUU0YBYw=",
    version = "v1.3.0",
)

go_repository(
    name = "com_github_opentracing_opentracing_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/opentracing/opentracing-go",
    sum = "h1:pWlfV3Bxv7k65HYwkikxat0+s3pV4bsqf19k25Ur8rU=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_cespare_xxhash_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/cespare/xxhash/v2",
    sum = "h1:UL815xU9SqsFlibzuggzjXhog7bL6oX9BbNZnL2UFvs=",
    version = "v2.3.0",
)

go_repository(
    name = "com_github_go_logr_stdr",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-logr/stdr",
    sum = "h1:hSWxHoqTgW2S2qGc0LTAI563KZ5YKYRhT3MFKZMbjag=",
    version = "v1.2.2",
)

go_repository(
    name = "io_opentelemetry_go_contrib_instrumentation_github_com_labstack_echo_otelecho",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/contrib/instrumentation/github.com/labstack/echo/otelecho",
    sum = "h1:JJCIHAxGCB5HM3NxeIwFjHc087Xwk96TG9kaZU6TAec=",
    version = "v0.45.0",
)

go_repository(
    name = "io_opentelemetry_go_contrib_propagators_b3",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/contrib/propagators/b3",
    sum = "h1:Yty9Vs4F3D6/liF1o6FNt0PvN85h/BJJ6DQKJ3nrcM0=",
    version = "v1.20.0",
)

go_repository(
    name = "com_github_grpc_ecosystem_grpc_gateway_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/grpc-ecosystem/grpc-gateway/v2",
    sum = "h1:6UKoz5ujsI55KNpsJH3UwCq3T8kKbZwNZBNPuTTje8U=",
    version = "v2.18.1",
)

go_repository(
    name = "io_opentelemetry_go_otel_exporters_otlp_internal_retry",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/exporters/otlp/internal/retry",
    sum = "h1:/fXHZHGvro6MVqV34fJzDhi7sHGpX3Ej/Qjmfn003ho=",
    version = "v1.14.0",
)

go_repository(
    name = "io_opentelemetry_go_otel_exporters_otlp_otlptrace",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/exporters/otlp/otlptrace",
    sum = "h1:Mne5On7VWdx7omSrSSZvM4Kw7cS7NQkOOmLcgscI51U=",
    version = "v1.19.0",
)

go_repository(
    name = "io_opentelemetry_go_otel_exporters_otlp_otlptrace_otlptracegrpc",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc",
    sum = "h1:3d+S281UTjM+AbF31XSOYn1qXn3BgIdWl8HNEpx08Jk=",
    version = "v1.19.0",
)

go_repository(
    name = "com_github_elastic_gosigar",
    build_file_proto_mode = "disable",
    importpath = "github.com/elastic/gosigar",
    sum = "h1:Dg80n8cr90OZ7x+bAax/QjoW/XqTI11RmA79ZwIm9/4=",
    version = "v0.14.2",
)

go_repository(
    name = "io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc",
    sum = "h1:RsQi0qJ2imFfCvZabqzM9cNXBG8k6gXMv1A0cXRmH6A=",
    version = "v0.45.0",
)

go_repository(
    name = "com_github_motoki317_sc",
    build_file_proto_mode = "disable",
    importpath = "github.com/motoki317/sc",
    sum = "h1:rPBrI4I7E/ySG7OLVFu5jm7eOa2zUw4XN1QhKsmYY54=",
    version = "v1.4.2",
)

go_repository(
    name = "com_github_niemeyer_pretty",
    build_file_proto_mode = "disable",
    importpath = "github.com/niemeyer/pretty",
    sum = "h1:fD57ERR4JtEqsWbfPhv4DMiApHyliiK5xCTNVSPiaAs=",
    version = "v0.0.0-20200227124842-a10e7caefd8e",
)

go_repository(
    name = "org_uber_go_dig",
    build_file_proto_mode = "disable",
    importpath = "go.uber.org/dig",
    sum = "h1:imUL1UiY0Mg4bqbFfsRQO5G4CGRBec/ZujWTvSVp3pw=",
    version = "v1.18.0",
)

go_repository(
    name = "org_uber_go_fx",
    build_file_proto_mode = "disable",
    importpath = "go.uber.org/fx",
    sum = "h1:lIr/gYWQGfTwGcSXWXu4vP5Ws6iqnNEIY+F/aFzCKTg=",
    version = "v1.23.0",
)

go_repository(
    name = "com_github_prometheus_client_model",
    build_file_proto_mode = "disable",
    importpath = "github.com/prometheus/client_model",
    sum = "h1:UBgGFHqYdG/TPFD1B1ogZywDqEkwp3fBMvqdiQ7Xew4=",
    version = "v0.3.0",
)

go_repository(
    name = "com_github_robfig_cron_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/robfig/cron/v3",
    sum = "h1:WdRxkvbJztn8LMz/QEvLN5sBU+xKpSqwwUO1Pjr4qDs=",
    version = "v3.0.1",
)

go_repository(
    name = "com_github_adalogics_go_fuzz_headers",
    build_file_proto_mode = "disable",
    importpath = "github.com/AdaLogics/go-fuzz-headers",
    sum = "h1:EKPd1INOIyr5hWOWhvpmQpY6tKjeG0hT1s3AMC/9fic=",
    version = "v0.0.0-20230106234847-43070de90fa1",
)

go_repository(
    name = "com_github_beorn7_perks",
    build_file_proto_mode = "disable",
    importpath = "github.com/beorn7/perks",
    sum = "h1:VlbKKnNfV8bJzeqoa4cOKqO6bYr3WgKZxO8Z16+hsOM=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_checkpoint_restore_go_criu_v5",
    build_file_proto_mode = "disable",
    importpath = "github.com/checkpoint-restore/go-criu/v5",
    sum = "h1:wpFFOoomK3389ue2lAb0Boag6XPht5QYpipxmSNL4d8=",
    version = "v5.3.0",
)

go_repository(
    name = "com_github_cilium_ebpf",
    build_file_proto_mode = "disable",
    importpath = "github.com/cilium/ebpf",
    sum = "h1:64sn2K3UKw8NbP/blsixRpF3nXuyhz/VjRlRzvlBRu4=",
    version = "v0.9.1",
)

go_repository(
    name = "com_github_containerd_aufs",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/aufs",
    sum = "h1:2oeJiwX5HstO7shSrPZjrohJZLzK36wvpdmzDRkL/LY=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_containerd_cgroups",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/cgroups",
    sum = "h1:v8rEWFl6EoqHB+swVNjVoCJE8o3jX7e8nqBGPLaDFBM=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_containerd_console",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/console",
    sum = "h1:lIr7SlA5PxZyMV30bDW0MGbiOPXwc63yRuCP0ARubLw=",
    version = "v1.0.3",
)

go_repository(
    name = "com_github_containerd_continuity",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/continuity",
    sum = "h1:wQnVrjIyQ8vhU2sgOiL5T07jo+ouqc2bnKsv5/EqGhU=",
    version = "v0.4.1",
)

go_repository(
    name = "com_github_containerd_fifo",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/fifo",
    sum = "h1:4I2mbh5stb1u6ycIABlBw9zgtlK8viPI9QkQNRQEEmY=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_containerd_go_cni",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/go-cni",
    sum = "h1:ORi7P1dYzCwVM6XPN4n3CbkuOx/NZ2DOqy+SHRdo9rU=",
    version = "v1.1.9",
)

go_repository(
    name = "com_github_containerd_go_runc",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/go-runc",
    sum = "h1:oU+lLv1ULm5taqgV/CJivypVODI4SUz1znWjv3nNYS0=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_containerd_imgcrypt",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/imgcrypt",
    sum = "h1:WSf9o9EQ0KGHiUx2ESFZ+PKf4nxK9BcvV/nJDX8RkB4=",
    version = "v1.1.7",
)

go_repository(
    name = "com_github_containerd_nri",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/nri",
    sum = "h1:2ZM4WImye1ypSnE7COjOvPAiLv84kaPILBDvb1tbDK8=",
    version = "v0.3.0",
)

go_repository(
    name = "com_github_containerd_ttrpc",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/ttrpc",
    sum = "h1:9vqZr0pxwOF5koz6N0N3kJ0zDHokrcPxIR/ZR2YFtOs=",
    version = "v1.2.2",
)

go_repository(
    name = "com_github_containerd_typeurl",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/typeurl",
    sum = "h1:Chlt8zIieDbzQFzXzAeBEF92KhExuE4p9p92/QmY7aY=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_containerd_zfs",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/zfs",
    sum = "h1:n7OZ7jZumLIqNJqXrEc/paBM840mORnmGdJDmAmJZHM=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_containernetworking_cni",
    build_file_proto_mode = "disable",
    importpath = "github.com/containernetworking/cni",
    sum = "h1:wtRGZVv7olUHMOqouPpn3cXJWpJgM6+EUl31EQbXALQ=",
    version = "v1.1.2",
)

go_repository(
    name = "com_github_containernetworking_plugins",
    build_file_proto_mode = "disable",
    importpath = "github.com/containernetworking/plugins",
    sum = "h1:SWgg3dQG1yzUo4d9iD8cwSVh1VqI+bP7mkPDoSfP9VU=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_containers_ocicrypt",
    build_file_proto_mode = "disable",
    importpath = "github.com/containers/ocicrypt",
    sum = "h1:uoG52u2e91RE4UqmBICZY8dNshgfvkdl3BW6jnxiFaI=",
    version = "v1.1.6",
)

go_repository(
    name = "com_github_cyphar_filepath_securejoin",
    build_file_proto_mode = "disable",
    importpath = "github.com/cyphar/filepath-securejoin",
    sum = "h1:Ugdm7cg7i6ZK6x3xDF1oEu1nfkyfH53EtKeQYTC3kyg=",
    version = "v0.2.4",
)

go_repository(
    name = "com_github_docker_go_events",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/go-events",
    sum = "h1:+pKlWGMw7gf6bQ+oDZB4KHQFypsfjYlq/C4rfL7D3g8=",
    version = "v0.0.0-20190806004212-e31b211e4f1c",
)

go_repository(
    name = "com_github_docker_go_metrics",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/go-metrics",
    sum = "h1:AgB/0SvBxihN0X8OR4SjsblXkbMvalQ8cjmtKQ2rQV8=",
    version = "v0.0.1",
)

go_repository(
    name = "com_github_grpc_ecosystem_go_grpc_prometheus",
    build_file_proto_mode = "disable",
    importpath = "github.com/grpc-ecosystem/go-grpc-prometheus",
    sum = "h1:Ovs26xHkKqVztRpIrF/92BcuyuQ/YW4NSIpoGtfXNho=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_intel_goresctrl",
    build_file_proto_mode = "disable",
    importpath = "github.com/intel/goresctrl",
    sum = "h1:K2D3GOzihV7xSBedGxONSlaw/un1LZgWsc9IfqipN4c=",
    version = "v0.3.0",
)

go_repository(
    name = "com_github_matttproud_golang_protobuf_extensions",
    build_file_proto_mode = "disable",
    importpath = "github.com/matttproud/golang_protobuf_extensions",
    sum = "h1:mmDVorXM7PCGKw94cs5zkfA9PSy5pEvNWRP0ET0TIVo=",
    version = "v1.0.4",
)

go_repository(
    name = "com_github_microsoft_hcsshim",
    build_file_proto_mode = "disable",
    importpath = "github.com/Microsoft/hcsshim",
    sum = "h1:YSZVvlIIDD1UxQpJp0h+dnpLUw+TrY0cx8obKsp3bek=",
    version = "v0.10.0-rc.8",
)

go_repository(
    name = "com_github_miekg_pkcs11",
    build_file_proto_mode = "disable",
    importpath = "github.com/miekg/pkcs11",
    sum = "h1:Ugu9pdy6vAYku5DEpVWVFPYnzV+bxB+iRdbuFSu7TvU=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_moby_locker",
    build_file_proto_mode = "disable",
    importpath = "github.com/moby/locker",
    sum = "h1:fOXqR41zeveg4fFODix+1Ch4mj/gT0NE1XJbp/epuBg=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_moby_spdystream",
    build_file_proto_mode = "disable",
    importpath = "github.com/moby/spdystream",
    sum = "h1:cjW1zVyyoiM0T7b6UoySUFqzXMoqRckQtXwGPiBhOM8=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_moby_sys_mountinfo",
    build_file_proto_mode = "disable",
    importpath = "github.com/moby/sys/mountinfo",
    sum = "h1:BzJjoreD5BMFNmD9Rus6gdd1pLuecOFPt8wC+Vygl78=",
    version = "v0.6.2",
)

go_repository(
    name = "com_github_moby_sys_signal",
    build_file_proto_mode = "disable",
    importpath = "github.com/moby/sys/signal",
    sum = "h1:25RW3d5TnQEoKvRbEKUGay6DCQ46IxAVTT9CUMgmsSI=",
    version = "v0.7.0",
)

go_repository(
    name = "com_github_moby_sys_symlink",
    build_file_proto_mode = "disable",
    importpath = "github.com/moby/sys/symlink",
    sum = "h1:tk1rOM+Ljp0nFmfOIBtlV3rTDlWOwFRhjEeAhZB0nZc=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_moby_term",
    build_file_proto_mode = "disable",
    importpath = "github.com/moby/term",
    sum = "h1:xt8Q1nalod/v7BqbG21f8mQPqH+xAaC9C3N3wfWbVP0=",
    version = "v0.5.0",
)

go_repository(
    name = "com_github_mrunalp_fileutils",
    build_file_proto_mode = "disable",
    importpath = "github.com/mrunalp/fileutils",
    sum = "h1:NKzVxiH7eSk+OQ4M+ZYW1K6h27RUV3MI6NUTsHhU6Z4=",
    version = "v0.5.0",
)

go_repository(
    name = "com_github_opencontainers_runc",
    build_file_proto_mode = "disable",
    importpath = "github.com/opencontainers/runc",
    sum = "h1:y2EZDS8sNng4Ksf0GUYNhKbTShZJPJg1FiXJNH/uoCk=",
    version = "v1.1.7",
)

go_repository(
    name = "com_github_opencontainers_runtime_spec",
    build_file_proto_mode = "disable",
    importpath = "github.com/opencontainers/runtime-spec",
    sum = "h1:wHa9jroFfKGQqFHj0I1fMRKLl0pfj+ynAqBxo3v6u9w=",
    version = "v1.1.0-rc.1",
)

go_repository(
    name = "com_github_opencontainers_runtime_tools",
    build_file_proto_mode = "disable",
    importpath = "github.com/opencontainers/runtime-tools",
    sum = "h1:DmNGcqH3WDbV5k8OJ+esPWbqUOX5rMLR2PMvziDMJi0=",
    version = "v0.9.1-0.20221107090550-2e043c6bd626",
)

go_repository(
    name = "com_github_opencontainers_selinux",
    build_file_proto_mode = "disable",
    importpath = "github.com/opencontainers/selinux",
    sum = "h1:+5Zbo97w3Lbmb3PeqQtpmTkMwsW5nRI3YaLpt7tQ7oU=",
    version = "v1.11.0",
)

go_repository(
    name = "com_github_prometheus_client_golang",
    build_file_proto_mode = "disable",
    importpath = "github.com/prometheus/client_golang",
    sum = "h1:nJdhIvne2eSX/XRAFV9PcvFFRbrjbcTUj0VP62TMhnw=",
    version = "v1.14.0",
)

go_repository(
    name = "com_github_prometheus_common",
    build_file_proto_mode = "disable",
    importpath = "github.com/prometheus/common",
    sum = "h1:ccBbHCgIiT9uSoFY0vX8H3zsNR5eLt17/RQLUvn8pXE=",
    version = "v0.37.0",
)

go_repository(
    name = "com_github_prometheus_procfs",
    build_file_proto_mode = "disable",
    importpath = "github.com/prometheus/procfs",
    sum = "h1:ODq8ZFEaYeCaZOJlZZdJA2AbQR98dSHSM1KW/You5mo=",
    version = "v0.8.0",
)

go_repository(
    name = "com_github_seccomp_libseccomp_golang",
    build_file_proto_mode = "disable",
    importpath = "github.com/seccomp/libseccomp-golang",
    sum = "h1:RpforrEYXWkmGwJHIGnLZ3tTWStkjVVstwzNGqxX2Ds=",
    version = "v0.9.2-0.20220502022130-f33da4d89646",
)

go_repository(
    name = "com_github_stefanberger_go_pkcs11uri",
    build_file_proto_mode = "disable",
    importpath = "github.com/stefanberger/go-pkcs11uri",
    sum = "h1:lIOOHPEbXzO3vnmx2gok1Tfs31Q8GQqKLc8vVqyQq/I=",
    version = "v0.0.0-20201008174630-78d3cae3a980",
)

go_repository(
    name = "com_github_syndtr_gocapability",
    build_file_proto_mode = "disable",
    importpath = "github.com/syndtr/gocapability",
    sum = "h1:kdXcSzyDtseVEc4yCz2qF8ZrQvIDBJLl4S1c3GCXmoI=",
    version = "v0.0.0-20200815063812-42c35b437635",
)

go_repository(
    name = "com_github_testcontainers_testcontainers_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/testcontainers/testcontainers-go",
    sum = "h1:ERYTSikX01QczBLPZpqsETTBO7lInqEP349phDOVJVs=",
    version = "v0.23.0",
)

go_repository(
    name = "com_github_urfave_cli",
    build_file_proto_mode = "disable",
    importpath = "github.com/urfave/cli",
    sum = "h1:igJgVw1JdKH+trcLWLeLwZjU9fEfPesQ+9/e4MQ44S8=",
    version = "v1.22.12",
)

go_repository(
    name = "com_github_vishvananda_netlink",
    build_file_proto_mode = "disable",
    importpath = "github.com/vishvananda/netlink",
    sum = "h1:Llsql0lnQEbHj0I1OuKyp8otXp0r3q0mPkuhwHfStVs=",
    version = "v1.2.1-beta.2",
)

go_repository(
    name = "com_github_vishvananda_netns",
    build_file_proto_mode = "disable",
    importpath = "github.com/vishvananda/netns",
    sum = "h1:p4VB7kIXpOQvVn1ZaTIVp+3vuYAXFe3OJEvjbUYJLaA=",
    version = "v0.0.0-20210104183010-2eb08e3e575f",
)

go_repository(
    name = "in_gopkg_square_go_jose_v2",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/square/go-jose.v2",
    sum = "h1:7odma5RETjNHWJnR32wx8t+Io4djHE1PqxCFx3iiZ2w=",
    version = "v2.5.1",
)

go_repository(
    name = "io_etcd_go_bbolt",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/bbolt",
    sum = "h1:j+zJOnnEjF/kyHlDDgGnVL/AIqIJPq8UoB2GSNfkUfQ=",
    version = "v1.3.7",
)

go_repository(
    name = "io_k8s_api",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/api",
    sum = "h1:dM3cinp3PGB6asOySalOZxEG4CZ0IAdJsrYZXE/ovGQ=",
    version = "v0.26.2",
)

go_repository(
    name = "io_k8s_apimachinery",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/apimachinery",
    sum = "h1:da1u3D5wfR5u2RpLhE/ZtZS2P7QvDgLZTi9wrNZl/tQ=",
    version = "v0.26.2",
)

go_repository(
    name = "io_k8s_apiserver",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/apiserver",
    sum = "h1:Pk8lmX4G14hYqJd1poHGC08G03nIHVqdJMR0SD3IH3o=",
    version = "v0.26.2",
)

go_repository(
    name = "io_k8s_client_go",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/client-go",
    sum = "h1:s1WkVujHX3kTp4Zn4yGNFK+dlDXy1bAAkIl+cFAiuYI=",
    version = "v0.26.2",
)

go_repository(
    name = "io_k8s_component_base",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/component-base",
    sum = "h1:IfWgCGUDzrD6wLLgXEstJKYZKAFS2kO+rBRi0p3LqcI=",
    version = "v0.26.2",
)

go_repository(
    name = "io_k8s_cri_api",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/cri-api",
    sum = "h1:KWO+U8MfI9drXB/P4oU9VchaWYOlwDglJZVHWMpTT3Q=",
    version = "v0.27.1",
)

go_repository(
    name = "io_k8s_klog_v2",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/klog/v2",
    sum = "h1:m4bYOKall2MmOiRaR1J+We67Do7vm9KiQVlT96lnHUw=",
    version = "v2.90.1",
)

go_repository(
    name = "io_k8s_sigs_structured_merge_diff_v4",
    build_file_proto_mode = "disable",
    importpath = "sigs.k8s.io/structured-merge-diff/v4",
    sum = "h1:PRbqxJClWWYMNV1dhaG4NsibJbArud9kFxnAMREiWFE=",
    version = "v4.2.3",
)

go_repository(
    name = "io_k8s_sigs_yaml",
    build_file_proto_mode = "disable",
    importpath = "sigs.k8s.io/yaml",
    sum = "h1:a2VclLzOGrwOHDiV8EfBGhvjHvP46CtW5j6POvhYGGo=",
    version = "v1.3.0",
)

go_repository(
    name = "io_k8s_utils",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/utils",
    sum = "h1:kmDqav+P+/5e1i9tFfHq1qcF3sOrDp+YEkVDAHu7Jwk=",
    version = "v0.0.0-20230220204549-a5ecb0141aa5",
)

go_repository(
    name = "org_mozilla_go_pkcs7",
    build_file_proto_mode = "disable",
    importpath = "go.mozilla.org/pkcs7",
    sum = "h1:A/5uWzF44DlIgdm/PQFwfMkW0JX+cIcQi/SwLAmZP5M=",
    version = "v0.0.0-20200128120323-432b2356ecb1",
)

go_repository(
    name = "tools_gotest_v3",
    build_file_proto_mode = "disable",
    importpath = "gotest.tools/v3",
    sum = "h1:Ljk6PdHdOhAb5aDMWXjDLMMhph+BpztA4v1QdqEW2eY=",
    version = "v3.5.0",
)

go_repository(
    name = "com_github_bazelbuild_rules_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/bazelbuild/rules_go",
    sum = "h1:/BUvuaB8MEiUA2oLPPCGtuw5V+doAYyiGTFyoSWlkrw=",
    version = "v0.50.1",
)

go_repository(
    name = "com_google_cloud_go_asset",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/asset",
    sum = "h1:B3OhouMFZNnUpB26Ex1izZ3OBh9R3iAGWO8raLOZ0jA=",
    version = "v1.15.2",
)

go_repository(
    name = "com_google_cloud_go_security",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/security",
    sum = "h1:RXYyXEtudhyqqNZNzVZWuDW5BkSCfqo2OOgOKcxg1Ho=",
    version = "v1.15.3",
)

go_repository(
    name = "com_github_pagerduty_go_pagerduty",
    build_file_proto_mode = "disable",
    importpath = "github.com/PagerDuty/go-pagerduty",
    sum = "h1:am81SzvG5Pw+s3JZ5yEy6kGvsXXklTNRrGr3d8WKpsU=",
    version = "v1.6.0",
)

go_repository(
    name = "com_github_nyaruka_phonenumbers",
    build_file_proto_mode = "disable",
    importpath = "github.com/nyaruka/phonenumbers",
    sum = "h1:/7bJVqIWLb+5erm10aMlojaKhXoMM6JKmlWLNg5laYc=",
    version = "v1.1.9",
)

go_repository(
    name = "com_github_looplab_fsm",
    build_file_proto_mode = "disable",
    importpath = "github.com/looplab/fsm",
    sum = "h1:qtxBsa2onOs0qFOtkqwf5zE0uP0+Te+wlIvXctPKpcw=",
    version = "v1.0.3",
)

go_repository(
    name = "com_github_adrg_postcode",
    build_file_proto_mode = "disable",
    importpath = "github.com/adrg/postcode",
    sum = "h1:fnzSHovyCtUM9uNf9lGiEhu0qsZNna+ZF09GACZUQfU=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_hashicorp_terraform_json",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/terraform-json",
    sum = "h1:sh9iZ1Y8IFJLx+xQiKHGud6/TSUCM0N8e17dKDpqV7s=",
    version = "v0.14.0",
)

go_repository(
    name = "com_github_sebdah_goldie",
    build_file_proto_mode = "disable",
    importpath = "github.com/sebdah/goldie",
    sum = "h1:9GNhIat69MSlz/ndaBg48vl9dF5fI+NBB6kfOxgfkMc=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_aws_aws_lambda_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-lambda-go",
    sum = "h1:WXkQ/xhIcXZZ2P5ZBEw+bbAKeCEcb5NtiYpSwVVzIXg=",
    version = "v1.37.0",
)

go_repository(
    name = "com_github_hooklift_gowsdl",
    build_file_proto_mode = "disable",
    importpath = "github.com/hooklift/gowsdl",
    sum = "h1:DE8RevqhGPLchumV/V7OwbCzfJ8lcozFg1uWC/ESCBQ=",
    version = "v0.5.0",
)

go_repository(
    name = "com_github_golang_jwt_jwt_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang-jwt/jwt/v4",
    sum = "h1:7cYmW1XlMY7h7ii7UhUyChSgS5wUJEnm9uZVTGqOWzg=",
    version = "v4.5.0",
)

go_repository(
    name = "com_github_rivo_uniseg",
    build_file_proto_mode = "disable",
    importpath = "github.com/rivo/uniseg",
    sum = "h1:WUdvkW8uEhrYfLC4ZzdpI2ztxP1I582+49Oc5Mq64VQ=",
    version = "v0.4.7",
)

go_repository(
    name = "com_github_adrg_strutil",
    build_file_proto_mode = "disable",
    importpath = "github.com/adrg/strutil",
    sum = "h1:IOQnSOAjbE17+7l1lw4rXgX6JuSeJGdZa7BucTMV3Qg=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_adrg_sysfont",
    build_file_proto_mode = "disable",
    importpath = "github.com/adrg/sysfont",
    sum = "h1:l9WKJNHsIpsfOhYIm1oSj+77837r/vls1MH17SH6gp0=",
    version = "v0.1.1",
)

go_repository(
    name = "com_github_adrg_xdg",
    build_file_proto_mode = "disable",
    importpath = "github.com/adrg/xdg",
    sum = "h1:VSVdnH7cQ7V+B33qSJHTCRlNgra1607Q8PzEmnvb2Ic=",
    version = "v0.2.1",
)

go_repository(
    name = "com_github_gorilla_i18n",
    build_file_proto_mode = "disable",
    importpath = "github.com/gorilla/i18n",
    sum = "h1:N+R2A3fGIr5GucoRMu2xpqyQWQlfY31orbofBCdjMz8=",
    version = "v0.0.0-20150820051429-8b358169da46",
)

go_repository(
    name = "com_github_trimmer_io_go_xmp",
    build_file_proto_mode = "disable",
    importpath = "github.com/trimmer-io/go-xmp",
    sum = "h1:zY8bolSga5kOjBAaHS6hrdxLgEoYuT875xTy0QDwZWs=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_unidoc_freetype",
    build_file_proto_mode = "disable",
    importpath = "github.com/unidoc/freetype",
    sum = "h1:Rk4easgDQslR3DK7vwtl6jYMZTF3JqZ3ceUdyT6a3UM=",
    version = "v0.0.0-20220130190903-3efbeefd0c90",
)

go_repository(
    name = "com_github_unidoc_garabic",
    build_file_proto_mode = "disable",
    importpath = "github.com/unidoc/garabic",
    sum = "h1:kExUKrbi429KdVVuAc85z4P+W/Rk4bjGWB5KzZLl/l8=",
    version = "v0.0.0-20220702200334-8c7cb25baa11",
)

go_repository(
    name = "com_github_unidoc_pkcs7",
    build_file_proto_mode = "disable",
    importpath = "github.com/unidoc/pkcs7",
    sum = "h1:9bQfbWMYsIfUP8PyhTcBudOsvbLpNH0MBv4U0P/jDTE=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_unidoc_timestamp",
    build_file_proto_mode = "disable",
    importpath = "github.com/unidoc/timestamp",
    sum = "h1:RLtvUhe4DsUDl66m7MJ8OqBjq8jpWBXPK6/RKtqeTkc=",
    version = "v0.0.0-20200412005513-91597fd3793a",
)

go_repository(
    name = "com_github_unidoc_unichart",
    build_file_proto_mode = "disable",
    importpath = "github.com/unidoc/unichart",
    sum = "h1:GoJ/rxSoOYZsqlG3yOJpKkwgfsIQgb9hHX7bILZHcCg=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_unidoc_unipdf_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/unidoc/unipdf/v3",
    sum = "h1:ao7sWfB04ZLvWit5SSem0N/01pZxStoVxorH8nhCnJk=",
    version = "v3.45.0",
)

go_repository(
    name = "com_github_unidoc_unitype",
    build_file_proto_mode = "disable",
    importpath = "github.com/unidoc/unitype",
    sum = "h1:x0jMn7pB/tNrjEVjy3Ukpxo++HOBQaTCXcTYFA6BH3w=",
    version = "v0.2.1",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_batch",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/batch",
    sum = "h1:PuzCTxVL8d0WXU9KuHpPYPl41dSSIh5rH5Mkt9mOL8A=",
    version = "v1.45.4",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_ec2",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/ec2",
    sum = "h1:rGBv2N0zWvNTKnxOfbBH4mNM8WMdDNkaxdqtz152G40=",
    version = "v1.179.2",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_ecs",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/ecs",
    sum = "h1:KxLLg5EMBZOkUXeBXoQB611VLdGou+wh2XmSSBUkIzA=",
    version = "v1.46.4",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_sqs",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/sqs",
    sum = "h1:/tOrE92KXPF14vdIhwp/06zSYEKQMiwVtI4/qBugwDw=",
    version = "v1.35.4",
)

go_repository(
    name = "com_github_bwesterb_go_ristretto",
    build_file_proto_mode = "disable",
    importpath = "github.com/bwesterb/go-ristretto",
    sum = "h1:1w53tCkGhCQ5djbat3+MH0BAQ5Kfgbt56UZQ/JMzngw=",
    version = "v1.2.3",
)

go_repository(
    name = "com_github_cloudflare_circl",
    build_file_proto_mode = "disable",
    importpath = "github.com/cloudflare/circl",
    # see: https://github.com/bazelbuild/bazel-gazelle/issues/1421
    patch_args = ["-p1"],
    patches = ["//nirvana/bazel-patches:com_github_cloudflare_circl/0001-gazelle.patch"],  # keep
    sum = "h1:fE/Qz0QdIGqeWfnwq0RE0R7MI51s0M2E4Ga9kq5AEMs=",
    version = "v1.3.3",
)

go_repository(
    name = "com_github_google_go_github_v52",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/go-github/v52",
    sum = "h1:uyGWOY+jMQ8GVGSX8dkSwCzlehU3WfdxQ7GweO/JP7M=",
    version = "v52.0.0",
)

go_repository(
    name = "com_github_protonmail_go_crypto",
    build_file_proto_mode = "disable",
    importpath = "github.com/ProtonMail/go-crypto",
    sum = "h1:kkhsdkhsCvIsutKu5zLMgWtgh9YxGCNAw8Ad8hjwfYg=",
    version = "v0.0.0-20230828082145-3c4c8a2d2371",
)

go_repository(
    name = "com_github_alcortesm_tgz",
    build_file_proto_mode = "disable",
    importpath = "github.com/alcortesm/tgz",
    sum = "h1:uSoVVbwJiQipAclBbw+8quDsfcvFjOpI5iCf4p/cqCs=",
    version = "v0.0.0-20161220082320-9c5fe88206d7",
)

go_repository(
    name = "com_github_anmitsu_go_shlex",
    build_file_proto_mode = "disable",
    importpath = "github.com/anmitsu/go-shlex",
    sum = "h1:9AeTilPcZAjCFIImctFaOjnTIavg87rW78vTPkQqLI8=",
    version = "v0.0.0-20200514113438-38f4b401e2be",
)

go_repository(
    name = "com_github_antlr_antlr4_runtime_go_antlr",
    build_file_proto_mode = "disable",
    importpath = "github.com/antlr/antlr4/runtime/Go/antlr",
    sum = "h1:mEzJ8SH4M5wDL8C4a17yX2YeD/FIXV5w8FJekByaBi0=",
    version = "v0.0.0-20220527190237-ee62e23da966",
)

go_repository(
    name = "com_github_armon_go_socks5",
    build_file_proto_mode = "disable",
    importpath = "github.com/armon/go-socks5",
    sum = "h1:0CwZNZbxp69SHPdPJAN/hZIm0C4OItdklCFmMRWYpio=",
    version = "v0.0.0-20160902184237-e75332964ef5",
)

go_repository(
    name = "com_github_emirpasic_gods",
    build_file_proto_mode = "disable",
    importpath = "github.com/emirpasic/gods",
    sum = "h1:FXtiHYKDGKCW2KzwZKx0iC0PQmdlorYgdFG9jPXJ1Bc=",
    version = "v1.18.1",
)

go_repository(
    name = "com_github_flynn_go_shlex",
    build_file_proto_mode = "disable",
    importpath = "github.com/flynn/go-shlex",
    sum = "h1:BHsljHzVlRcyQhjrss6TZTdY2VfCqZPbv5k3iBFa2ZQ=",
    version = "v0.0.0-20150515145356-3f9db97f8568",
)

go_repository(
    name = "com_github_gliderlabs_ssh",
    build_file_proto_mode = "disable",
    importpath = "github.com/gliderlabs/ssh",
    sum = "h1:OcaySEmAQJgyYcArR+gGGTHCyE7nvhEMTlYY+Dp8CpY=",
    version = "v0.3.5",
)

go_repository(
    name = "com_github_hyperjumptech_grule_rule_engine",
    build_file_proto_mode = "disable",
    importpath = "github.com/hyperjumptech/grule-rule-engine",
    sum = "h1:YwPlzazuDSD+gQsOvWINTSJ+EP/rIqCrfSMHhqPwPqI=",
    version = "v1.13.0",
)

go_repository(
    name = "com_github_hyperjumptech_hyper_mux",
    build_file_proto_mode = "disable",
    importpath = "github.com/hyperjumptech/hyper-mux",
    sum = "h1:HIM37If0OBOiiToOw5zZJLzOhZuhMObHwVCnTKiRyGw=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_jbenet_go_context",
    build_file_proto_mode = "disable",
    importpath = "github.com/jbenet/go-context",
    sum = "h1:BQSFePA1RWJOlocH6Fxy8MmwDt+yVQYULKfN0RoTN8A=",
    version = "v0.0.0-20150711004518-d14ea06fba99",
)

go_repository(
    name = "com_github_kevinburke_ssh_config",
    build_file_proto_mode = "disable",
    importpath = "github.com/kevinburke/ssh_config",
    sum = "h1:x584FjTGwHzMwvHx18PXxbBVzfnxogHaAReU4gf13a4=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_nytimes_gziphandler",
    build_file_proto_mode = "disable",
    importpath = "github.com/NYTimes/gziphandler",
    sum = "h1:ZUDjpQae29j0ryrS0u/B8HZfJBtBQHjqw2rQ2cqUQ3I=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_pelletier_go_buffruneio",
    build_file_proto_mode = "disable",
    importpath = "github.com/pelletier/go-buffruneio",
    sum = "h1:U4t4R6YkofJ5xHm3dJzuRpPZ0mr5MMCoAWooScCR7aA=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_rs_cors",
    build_file_proto_mode = "disable",
    importpath = "github.com/rs/cors",
    sum = "h1:P2KMzcFwrPoSjkF1WLRPsp3UMLyql8L4v9hQpVeK5so=",
    version = "v1.8.0",
)

go_repository(
    name = "com_github_src_d_gcfg",
    build_file_proto_mode = "disable",
    importpath = "github.com/src-d/gcfg",
    sum = "h1:xXbNR5AlLSA315x2UO+fTSSAXCDf+Ar38/6oyGbDKQ4=",
    version = "v1.4.0",
)

go_repository(
    name = "com_github_xanzy_ssh_agent",
    build_file_proto_mode = "disable",
    importpath = "github.com/xanzy/ssh-agent",
    sum = "h1:+/15pJfg/RsTxqYcX6fHqOXZwwMP+2VyYWJeWM2qQFM=",
    version = "v0.3.3",
)

go_repository(
    name = "in_gopkg_src_d_go_billy_v4",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/src-d/go-billy.v4",
    sum = "h1:0SQA1pRztfTFx2miS8sA97XvooFeNOmvUenF4o0EcVg=",
    version = "v4.3.2",
)

go_repository(
    name = "in_gopkg_src_d_go_git_fixtures_v3",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/src-d/go-git-fixtures.v3",
    sum = "h1:ivZFOIltbce2Mo8IjzUHAFoq/IylO9WHhNOAJK+LsJg=",
    version = "v3.5.0",
)

go_repository(
    name = "in_gopkg_src_d_go_git_v4",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/src-d/go-git.v4",
    sum = "h1:SRtFyV8Kxc0UP7aCHcijOMQGPxHSmMOPrzulQWolkYE=",
    version = "v4.13.1",
)

go_repository(
    name = "in_gopkg_warnings_v0",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/warnings.v0",
    sum = "h1:wFXVbFY8DY5/xOe1ECiWdKCzZlxgshcYVNkBHstARME=",
    version = "v0.1.2",
)

go_repository(
    name = "com_github_danwakefield_fnmatch",
    build_file_proto_mode = "disable",
    importpath = "github.com/danwakefield/fnmatch",
    sum = "h1:y5HC9v93H5EPKqaS1UYVg1uYah5Xf51mBfIoWehClUQ=",
    version = "v0.0.0-20160403171240-cbb64ac3d964",
)

go_repository(
    name = "com_github_denormal_go_gitignore",
    build_file_proto_mode = "disable",
    importpath = "github.com/denormal/go-gitignore",
    sum = "h1:0nsrg//Dc7xC74H/TZ5sYR8uk4UQRNjsw8zejqH5a4Q=",
    version = "v0.0.0-20180930084346-ae8ad1d07817",
)

go_repository(
    name = "com_github_hashicorp_golang_lru_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/hashicorp/golang-lru/v2",
    sum = "h1:a+bsQ5rvGLjzHuww6tVxozPZFVghXaHOwFs4luLUK2k=",
    version = "v2.0.7",
)

go_repository(
    name = "org_uber_go_mock",
    build_file_proto_mode = "disable",
    importpath = "go.uber.org/mock",
    sum = "h1:LbtPTcP8A5k9WPXj54PPPbjcI4Y6lhyOZXn+VS7wNko=",
    version = "v0.5.2",
)

go_repository(
    name = "org_golang_x_tools",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/tools",
    sum = "h1:0EedkvKDbh+qistFTd0Bcwe/YLh4vHwWEkiI0toFIBU=",
    version = "v0.31.0",
)

go_repository(
    name = "com_github_google_s2a_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/s2a-go",
    sum = "h1:1kZ/sQM3srePvKs3tXAvQzo66XfcReoqFpIpIccE7Oc=",
    version = "v0.1.4",
)

go_repository(
    name = "com_google_cloud_go_dataproc_v2",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/dataproc/v2",
    sum = "h1:XRnxqa08/P2LpXTB+OMmPAfhT7GGyftgslKvzv330gM=",
    version = "v2.2.2",
)

go_repository(
    name = "org_golang_google_genproto_googleapis_bytestream",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/genproto/googleapis/bytestream",
    sum = "h1:g3hIDl0jRNd9PPTs2uBzYuaD5mQuwOkZY0vSc0LR32o=",
    version = "v0.0.0-20230530153820-e85fd2cbaebc",
)

go_repository(
    name = "com_github_brianvoe_gofakeit_v6",
    build_file_proto_mode = "disable",
    importpath = "github.com/brianvoe/gofakeit/v6",
    sum = "h1:74yq7RRz/noddscZHRS2T84oHZisW9muwbb8sRnU52A=",
    version = "v6.24.0",
)

go_repository(
    name = "com_github_deepmap_oapi_codegen_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/deepmap/oapi-codegen/v2",
    sum = "h1:3TS7w3r+XnjKFXcbFbc16pTWzfTy0OLPkCsutEHjWDA=",
    version = "v2.0.0",
)

go_repository(
    name = "com_github_andybalholm_brotli",
    build_file_proto_mode = "disable",
    importpath = "github.com/andybalholm/brotli",
    sum = "h1:8uQZIdzKmjc/iuPu7O2ioW48L81FgatrcpfFmiq/cCs=",
    version = "v1.0.5",
)

go_repository(
    name = "com_github_aymerick_douceur",
    build_file_proto_mode = "disable",
    importpath = "github.com/aymerick/douceur",
    sum = "h1:Mv+mAeH1Q+n9Fr+oyamOlAkUNPWPlA8PPGR0QAaYuPk=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_bytedance_sonic",
    build_file_proto_mode = "disable",
    importpath = "github.com/bytedance/sonic",
    sum = "h1:uNSnscRapXTwUgTyOF0GVljYD08p9X/Lbr9MweSV3V0=",
    version = "v1.10.0-rc3",
)

go_repository(
    name = "com_github_chenzhuoyu_base64x",
    build_file_proto_mode = "disable",
    importpath = "github.com/chenzhuoyu/base64x",
    sum = "h1:77cEq6EriyTZ0g/qfRdp61a3Uu/AWrgIq2s0ClJV1g0=",
    version = "v0.0.0-20230717121745-296ad89f973d",
)

go_repository(
    name = "com_github_chenzhuoyu_iasm",
    build_file_proto_mode = "disable",
    importpath = "github.com/chenzhuoyu/iasm",
    sum = "h1:9fhXjVzq5hUy2gkhhgHl95zG2cEAhw9OSGs8toWWAwo=",
    version = "v0.9.0",
)

go_repository(
    name = "com_github_cloudykit_jet_v6",
    build_file_proto_mode = "disable",
    importpath = "github.com/CloudyKit/jet/v6",
    sum = "h1:EpcZ6SR9n28BUGtNJSvlBqf90IpjeFr36Tizxhn/oME=",
    version = "v6.2.0",
)

go_repository(
    name = "com_github_flosch_pongo2_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/flosch/pongo2/v4",
    sum = "h1:gv+5Pe3vaSVmiJvh/BZa82b7/00YUGm0PIyVVLop0Hw=",
    version = "v4.0.2",
)

go_repository(
    name = "com_github_gabriel_vasile_mimetype",
    build_file_proto_mode = "disable",
    importpath = "github.com/gabriel-vasile/mimetype",
    sum = "h1:w5qFW6JKBz9Y393Y4q372O9A7cUSequkh1Q7OhCmWKU=",
    version = "v1.4.2",
)

go_repository(
    name = "com_github_gomarkdown_markdown",
    build_file_proto_mode = "disable",
    importpath = "github.com/gomarkdown/markdown",
    sum = "h1:uK3X/2mt4tbSGoHvbLBHUny7CKiuwUip3MArtukol4E=",
    version = "v0.0.0-20230716120725-531d2d74bc12",
)

go_repository(
    name = "com_github_gorilla_css",
    build_file_proto_mode = "disable",
    importpath = "github.com/gorilla/css",
    sum = "h1:BQqNyPTi50JCFMTw/b67hByjMVXZRwGha6wxVGkeihY=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_joker_jade",
    build_file_proto_mode = "disable",
    importpath = "github.com/Joker/jade",
    sum = "h1:Qbeh12Vq6BxURXT1qZBRHsDxeURB8ztcL6f3EXSGeHk=",
    version = "v1.1.3",
)

go_repository(
    name = "com_github_kataras_blocks",
    build_file_proto_mode = "disable",
    importpath = "github.com/kataras/blocks",
    sum = "h1:cF3RDY/vxnSRezc7vLFlQFTYXG/yAr1o7WImJuZbzC4=",
    version = "v0.0.7",
)

go_repository(
    name = "com_github_kataras_tunnel",
    build_file_proto_mode = "disable",
    importpath = "github.com/kataras/tunnel",
    sum = "h1:sCAqWuJV7nPzGrlb0os3j49lk2JhILT0rID38NHNLpA=",
    version = "v0.0.4",
)

go_repository(
    name = "com_github_mailgun_raymond_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/mailgun/raymond/v2",
    sum = "h1:5dmlB680ZkFG2RN/0lvTAghrSxIESeu9/2aeDqACtjw=",
    version = "v2.0.48",
)

go_repository(
    name = "com_github_oapi_codegen_runtime",
    build_file_proto_mode = "disable",
    importpath = "github.com/oapi-codegen/runtime",
    sum = "h1:P4rqFX5fMFWqRzY9M/3YF9+aPSPPB06IzP2P7oOxrWo=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_perimeterx_marshmallow",
    build_file_proto_mode = "disable",
    importpath = "github.com/perimeterx/marshmallow",
    sum = "h1:pZLDH9RjlLGGorbXhcaQLhfuV0pFMNfPO55FuFkxqLw=",
    version = "v1.1.4",
)

go_repository(
    name = "com_github_tdewolff_minify_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/tdewolff/minify/v2",
    sum = "h1:Q2BqOTmlMjoutkuD/OPCnJUpIqrzT3nRPkw+q+KpXS0=",
    version = "v2.12.8",
)

go_repository(
    name = "com_github_tdewolff_parse_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/tdewolff/parse/v2",
    sum = "h1:WrFllrqmzAcrKHzoYgMupqgUBIfBVOb0yscFzDf8bBg=",
    version = "v2.6.7",
)

go_repository(
    name = "com_github_twitchyliquid64_golang_asm",
    build_file_proto_mode = "disable",
    importpath = "github.com/twitchyliquid64/golang-asm",
    sum = "h1:SU5vSMR7hnwNxj24w34ZyCi/FmDZTkS4MhqMhdFk5YI=",
    version = "v0.15.1",
)

go_repository(
    name = "com_github_vmihailenco_msgpack_v5",
    build_file_proto_mode = "disable",
    importpath = "github.com/vmihailenco/msgpack/v5",
    sum = "h1:5gO0H1iULLWGhs2H5tbAHIZTV8/cYafcFOr9znI5mJU=",
    version = "v5.3.5",
)

go_repository(
    name = "com_github_vmihailenco_tagparser_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/vmihailenco/tagparser/v2",
    sum = "h1:y09buUbR+b5aycVFQs/g70pqKVZNBmxwAhO7/IwNM9g=",
    version = "v2.0.0",
)

go_repository(
    name = "com_github_yosssi_ace",
    build_file_proto_mode = "disable",
    importpath = "github.com/yosssi/ace",
    sum = "h1:tUkIP/BLdKqrlrPwcmH0shwEEhTRHoGnc1wFIWmaBUA=",
    version = "v0.0.5",
)

go_repository(
    name = "org_golang_x_arch",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/arch",
    sum = "h1:A8WCeEWhLwPBKNbFi5Wv5UTCBx5zzubnXDlMOFAzFMc=",
    version = "v0.4.0",
)

go_repository(
    name = "com_github_golang_jwt_jwt_v5",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang-jwt/jwt/v5",
    sum = "h1:kkGXqQOBSDDWRhWNXTFpqGSCMyh/PLnqUvMGJPDJDs0=",
    version = "v5.2.3",
)

go_repository(
    name = "com_github_g8rswimmer_go_sfdc",
    build_file_proto_mode = "disable",
    importpath = "github.com/g8rswimmer/go-sfdc",
    sum = "h1:YceU6c4QaY35dom6iUhZafuuWZq2ZZtpotB0nyvYlHY=",
    version = "v2.0.1+incompatible",
)

go_repository(
    name = "com_github_golang_geo",
    build_file_proto_mode = "disable",
    importpath = "github.com/golang/geo",
    sum = "h1:HKlyj6in2JV6wVkmQ4XmG/EIm+SCYlPZ+V4GWit7Z+I=",
    version = "v0.0.0-20230421003525-6adc56603217",
)

go_repository(
    name = "com_github_paulmach_orb",
    build_file_proto_mode = "disable",
    importpath = "github.com/paulmach/orb",
    sum = "h1:3koVegMC4X/WeiXYz9iswopaTwMem53NzTJuTF20JzU=",
    version = "v0.11.1",
)

go_repository(
    name = "com_github_paulmach_protoscan",
    build_file_proto_mode = "disable",
    importpath = "github.com/paulmach/protoscan",
    sum = "h1:rM0FpcTjUMvPUNk2BhPJrreDKetq43ChnL+x1sRg8O8=",
    version = "v0.2.1",
)

go_repository(
    name = "com_github_go_ole_go_ole",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-ole/go-ole",
    sum = "h1:/Fpf6oFPoeFik9ty7siob0G6Ke8QvQEuVcuChpwXzpY=",
    version = "v1.2.6",
)

go_repository(
    name = "com_github_shirou_gopsutil",
    build_file_proto_mode = "disable",
    importpath = "github.com/shirou/gopsutil",
    sum = "h1:+1+c1VGhc88SSonWP6foOcLhvnKlUeu/erjjvaPEYiI=",
    version = "v3.21.11+incompatible",
)

go_repository(
    name = "com_github_tklauser_go_sysconf",
    build_file_proto_mode = "disable",
    importpath = "github.com/tklauser/go-sysconf",
    sum = "h1:g5vzr9iPFFz24v2KZXs/pvpvh8/V9Fw6vQK5ZZb78yU=",
    version = "v0.3.14",
)

go_repository(
    name = "com_github_tklauser_numcpus",
    build_file_proto_mode = "disable",
    importpath = "github.com/tklauser/numcpus",
    sum = "h1:Mx4Wwe/FjZLeQsK/6kt2EOepwwSl7SmJrK5bV/dXYgY=",
    version = "v0.8.0",
)

go_repository(
    name = "com_github_yusufpapurcu_wmi",
    build_file_proto_mode = "disable",
    importpath = "github.com/yusufpapurcu/wmi",
    sum = "h1:zFUKzehAFReQwLys1b/iSMl+JQGSCSjtVqQn9bBrPo0=",
    version = "v1.2.4",
)

go_repository(
    name = "com_github_frankban_quicktest",
    build_file_proto_mode = "disable",
    importpath = "github.com/frankban/quicktest",
    sum = "h1:FJKSZTDHjyhriyC81FLQ0LY93eSai0ZyR/ZIkd3ZUKE=",
    version = "v1.14.3",
)

go_repository(
    name = "com_github_knocklabs_knock_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/knocklabs/knock-go",
    sum = "h1:R/PsL+jVIW653nzr43pW7EBJAgHU3vAwLN//C8yfzyc=",
    version = "v0.1.22",
)

go_repository(
    name = "io_moul_http2curl",
    build_file_proto_mode = "disable",
    importpath = "moul.io/http2curl",
    sum = "h1:6XwpyZOYsgZJrU8exnG87ncVkU1FVCcTRpwzOkTDUi8=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_slack_go_slack",
    build_file_proto_mode = "disable",
    importpath = "github.com/slack-go/slack",
    sum = "h1:6c0UTfbRnvRssZUsZ2qe0Iu07VAMPjRqOa6oX8ewF4k=",
    version = "v0.14.0",
)

go_repository(
    name = "com_github_expr_lang_expr",
    build_file_proto_mode = "disable",
    importpath = "github.com/expr-lang/expr",
    sum = "h1:WUAzmR0JNI9JCiF0/ewwHB1gmcGw5wW7nWt8gc6PpCI=",
    version = "v1.16.9",
)

go_repository(
    name = "com_github_alecthomas_assert_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/alecthomas/assert/v2",
    sum = "h1:2Q9r3ki8+JYXvGsDyBXwH3LcJ+WK5D0gc5E8vS6K3D0=",
    version = "v2.11.0",
)

go_repository(
    name = "com_github_apache_arrow_go_v15",
    build_file_proto_mode = "disable",
    importpath = "github.com/apache/arrow/go/v15",
    sum = "h1:1zZACWf85oEZY5/kd9dsQS7i+2G5zVQcbKTHgslqHNA=",
    version = "v15.0.0",
)

go_repository(
    name = "com_github_goccy_go_yaml",
    build_file_proto_mode = "disable",
    importpath = "github.com/goccy/go-yaml",
    sum = "h1:n7Z+zx8S9f9KgzG6KtQKf+kwqXZlLNR2F6018Dgau54=",
    version = "v1.11.0",
)

go_repository(
    name = "com_github_hamba_avro_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/hamba/avro/v2",
    sum = "h1:6PKpEWzJfNnvBgn7m2/8WYaDOUASxfDU+Jyb4ojDgFY=",
    version = "v2.17.2",
)

go_repository(
    name = "com_github_hexops_gotextdiff",
    build_file_proto_mode = "disable",
    importpath = "github.com/hexops/gotextdiff",
    sum = "h1:gitA9+qJrrTCsiCl7+kh75nPqQt1cx4ZkudSTLoUqJM=",
    version = "v1.0.3",
)

go_repository(
    name = "com_github_substrait_io_substrait_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/substrait-io/substrait-go",
    sum = "h1:buDnjsb3qAqTaNbOR7VKmNgXf4lYQxWEcnSGUWBtmN8=",
    version = "v0.4.2",
)

go_repository(
    name = "com_github_tidwall_gjson",
    build_file_proto_mode = "disable",
    importpath = "github.com/tidwall/gjson",
    sum = "h1:bwWLZU7icoKRG+C+0PNwIKC6FCJO/Q3p2pZvuP0jN94=",
    version = "v1.17.3",
)

go_repository(
    name = "com_github_tidwall_match",
    build_file_proto_mode = "disable",
    importpath = "github.com/tidwall/match",
    sum = "h1:+Ho715JplO36QYgwN9PGYNhgZvoUSc9X2c80KVTi+GA=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_tidwall_sjson",
    build_file_proto_mode = "disable",
    importpath = "github.com/tidwall/sjson",
    sum = "h1:kLy8mja+1c9jlljvWTlSazM7cKDRfJuR/bOJhcY5NcY=",
    version = "v1.2.5",
)

go_repository(
    name = "org_golang_x_telemetry",
    build_file_proto_mode = "disable",
    importpath = "golang.org/x/telemetry",
    sum = "h1:zf5N6UOrA487eEFacMePxjXAJctxKmyjKUsjA11Uzuk=",
    version = "v0.0.0-20240521205824-bda55230c457",
)

go_repository(
    name = "com_github_xsam_otelsql",
    build_file_proto_mode = "disable",
    importpath = "github.com/XSAM/otelsql",
    sum = "h1:i9xtxtdcqXV768a5C6SoT/RkG+ue3JTOgkYInzlTOqs=",
    version = "v0.27.0",
)

go_repository(
    name = "io_opentelemetry_go_otel_sdk_metric",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/sdk/metric",
    sum = "h1:smhI5oD714d6jHE6Tie36fPx4WDFIg+Y6RfAY4ICcR0=",
    version = "v1.21.0",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_secretsmanager",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/secretsmanager",
    sum = "h1:EoPbZg+DGTRqKKhwk5uDviV9yvx65r1kyoNNC02ZH4Y=",
    version = "v1.33.4",
)

go_repository(
    name = "com_github_99designs_gqlgen",
    build_file_proto_mode = "disable",
    importpath = "github.com/99designs/gqlgen",
    sum = "h1:3vzrNWYyzSZjGDFo68e5j9sSauLxfKvLp+6ioRokVtM=",
    version = "v0.17.55",
)

go_repository(
    name = "com_github_agnivade_levenshtein",
    build_file_proto_mode = "disable",
    importpath = "github.com/agnivade/levenshtein",
    sum = "h1:U9L4IOT0Y3i0TIlUIDJ7rVUziKi/zPbrJGaFrtYH3SY=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_andreyvit_diff",
    build_file_proto_mode = "disable",
    importpath = "github.com/andreyvit/diff",
    sum = "h1:bvNMNQO63//z+xNgfBlViaCIJKLlCJ6/fmUseuG0wVQ=",
    version = "v0.0.0-20170406064948-c7f18ee00883",
)

go_repository(
    name = "com_github_andybalholm_cascadia",
    build_file_proto_mode = "disable",
    importpath = "github.com/andybalholm/cascadia",
    sum = "h1:3Xi6Dw5lHF15JtdcmAHD3i1+T8plmv7BQ/nsViSLyss=",
    version = "v1.3.2",
)

go_repository(
    name = "com_github_arbovm_levenshtein",
    build_file_proto_mode = "disable",
    importpath = "github.com/arbovm/levenshtein",
    sum = "h1:jfIu9sQUG6Ig+0+Ap1h4unLjW6YQJpKZVmUzxsD4E/Q=",
    version = "v0.0.0-20160628152529-48b4e1c0c4d0",
)

go_repository(
    name = "com_github_dgryski_trifles",
    build_file_proto_mode = "disable",
    importpath = "github.com/dgryski/trifles",
    sum = "h1:SG7nF6SRlWhcT7cNTs5R6Hk4V2lcmLz2NsG2VnInyNo=",
    version = "v0.0.0-20230903005119-f50d829f2e54",
)

go_repository(
    name = "com_github_kevinmbeaulieu_eq_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/kevinmbeaulieu/eq-go",
    sum = "h1:AQgYHURDOmnVJ62jnEk0W/7yFKEn+Lv8RHN6t7mB0Zo=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_logrusorgru_aurora_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/logrusorgru/aurora/v4",
    sum = "h1:sRjfPpun/63iADiSvGGjgA1cAYegEWMPCJdUpJYn9JA=",
    version = "v4.0.0",
)

go_repository(
    name = "com_github_matryer_moq",
    build_file_proto_mode = "disable",
    importpath = "github.com/matryer/moq",
    sum = "h1:HsZIdEsj8+9nE940WW7FFxMgrgSxGfMkNXhVTHUhfMU=",
    version = "v0.4.0",
)

go_repository(
    name = "com_github_puerkitobio_goquery",
    build_file_proto_mode = "disable",
    importpath = "github.com/PuerkitoBio/goquery",
    sum = "h1:mpJr/ikUA9/GNJB/DBZcGeFDXUtosHRyRrwh7KGdTG0=",
    version = "v1.9.3",
)

go_repository(
    name = "com_github_sosodev_duration",
    build_file_proto_mode = "disable",
    importpath = "github.com/sosodev/duration",
    sum = "h1:qtHBDMQ6lvMQsL15g4aopM4HEfOaYuhWBw3NPTtlqq4=",
    version = "v1.3.1",
)

go_repository(
    name = "com_github_urfave_cli_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/urfave/cli/v2",
    sum = "h1:WoHEJLdsXr6dDWoJgMq/CboDmyY/8HMMH1fTECbih+w=",
    version = "v2.27.5",
)

go_repository(
    name = "com_github_vektah_gqlparser_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/vektah/gqlparser/v2",
    sum = "h1:9At7WblLV7/36nulgekUgIaqHZWn5hxqluxrxGUhOmI=",
    version = "v2.5.17",
)

go_repository(
    name = "com_github_xrash_smetrics",
    build_file_proto_mode = "disable",
    importpath = "github.com/xrash/smetrics",
    sum = "h1:gEOO8jv9F4OT7lGCjxCBTO/36wtF6j2nSip77qHd4x4=",
    version = "v0.0.0-20240521201337-686a1a2994c1",
)

go_repository(
    name = "com_github_ctreminiom_go_atlassian",
    build_file_proto_mode = "disable",
    importpath = "github.com/ctreminiom/go-atlassian",
    sum = "h1:thH/oaWlvWLN5a4AcgQ30yPmnn0mQaTiqsq1M6bA9BY=",
    version = "v1.6.1",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_dynamodb",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/dynamodb",
    sum = "h1:AnSNs7Ogi0LXHPMDBx4RE7imU4/JmzWFziqkMKJA2AY=",
    version = "v1.38.1",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_internal_endpoint_discovery",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/internal/endpoint-discovery",
    sum = "h1:EqGlayejoCRXmnVC6lXl6phCm9R2+k35e0gWsO9G5DI=",
    version = "v1.10.7",
)

go_repository(
    name = "com_github_dgraph_io_ristretto_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/dgraph-io/ristretto/v2",
    sum = "h1:59LjpOJLNDULHh8MC4UaegN52lC4JnO2dITsie/Pa8I=",
    version = "v2.1.0",
)

go_repository(
    name = "com_github_dgryski_go_farm",
    build_file_proto_mode = "disable",
    importpath = "github.com/dgryski/go-farm",
    sum = "h1:fAjc9m62+UWV/WAFKLNi6ZS0675eEUC9y3AlwSbQu1Y=",
    version = "v0.0.0-20200201041132-a6ae2369ad13",
)

go_repository(
    name = "com_github_clerk_clerk_sdk_go_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/clerk/clerk-sdk-go/v2",
    sum = "h1:eQ6I7LouzdEvPUwLAYOfSk1Ktc4Ee2UKGMVOKBKtMXo=",
    version = "v2.3.1",
)

go_repository(
    name = "com_github_go_jose_go_jose_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-jose/go-jose/v3",
    sum = "h1:Wp5HA7bLQcKnf6YYao/4kpRpVMp/yf6+pJKV8WFSaNY=",
    version = "v3.0.4",
)

gazelle_dependencies()

go_repository(
    name = "com_github_alecthomas_template",
    build_file_proto_mode = "disable",
    importpath = "github.com/alecthomas/template",
    sum = "h1:JYp7IbQjafoB+tBA3gMyHYHrpOtNuDiK/uB5uXxq5wM=",
    version = "v0.0.0-20190718012654-fb15b899a751",
)

go_repository(
    name = "com_github_alecthomas_units",
    build_file_proto_mode = "disable",
    importpath = "github.com/alecthomas/units",
    sum = "h1:UQZhZ2O0vMHr2cI+DC1Mbh0TJxzA3RcLoMsFw+aXw7E=",
    version = "v0.0.0-20190924025748-f65c72e2690d",
)

go_repository(
    name = "com_github_alexflint_go_filemutex",
    build_file_proto_mode = "disable",
    importpath = "github.com/alexflint/go-filemutex",
    sum = "h1:IAWuUuRYL2hETx5b8vCgwnD+xSdlsTQY6s2JjBsqLdg=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_armon_consul_api",
    build_file_proto_mode = "disable",
    importpath = "github.com/armon/consul-api",
    sum = "h1:G1bPvciwNyF7IUmKXNt9Ak3m6u9DE1rF+RmtIkBpVdA=",
    version = "v0.0.0-20180202201655-eb2c6b5be1b6",
)

go_repository(
    name = "com_github_asaskevich_govalidator",
    build_file_proto_mode = "disable",
    importpath = "github.com/asaskevich/govalidator",
    sum = "h1:idn718Q4B6AGu/h5Sxe66HYVdqdGu2l9Iebqhi/AEoA=",
    version = "v0.0.0-20190424111038-f61b66f89f4a",
)

go_repository(
    name = "com_github_azure_azure_sdk_for_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/azure-sdk-for-go",
    sum = "h1:KnPIugL51v3N3WwvaSmZbxukD1WuWXOiE9fRdu32f2I=",
    version = "v16.2.1+incompatible",
)

go_repository(
    name = "com_github_azure_go_autorest_autorest",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/go-autorest/autorest",
    sum = "h1:90Y4srNYrwOtAgVo3ndrQkTYn6kf1Eg/AjTFJ8Is2aM=",
    version = "v0.11.18",
)

go_repository(
    name = "com_github_bitly_go_simplejson",
    build_file_proto_mode = "disable",
    importpath = "github.com/bitly/go-simplejson",
    sum = "h1:6IH+V8/tVMab511d5bn4M7EwGXZf9Hj6i2xSwkNEM+Y=",
    version = "v0.5.0",
)

go_repository(
    name = "com_github_bits_and_blooms_bitset",
    build_file_proto_mode = "disable",
    importpath = "github.com/bits-and-blooms/bitset",
    sum = "h1:Kn4yilvwNtMACtf1eYDlG8H77R07mZSPbMjLyS07ChA=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_blang_semver",
    build_file_proto_mode = "disable",
    importpath = "github.com/blang/semver",
    sum = "h1:cQNTCjp13qL8KC3Nbxr/y2Bqb63oX6wdnnjpJbkM4JQ=",
    version = "v3.5.1+incompatible",
)

go_repository(
    name = "com_github_bshuster_repo_logrus_logstash_hook",
    build_file_proto_mode = "disable",
    importpath = "github.com/bshuster-repo/logrus-logstash-hook",
    sum = "h1:pgAtgj+A31JBVtEHu2uHuEx0n+2ukqUJnS2vVe5pQNA=",
    version = "v0.4.1",
)

go_repository(
    name = "com_github_buger_jsonparser",
    build_file_proto_mode = "disable",
    importpath = "github.com/buger/jsonparser",
    sum = "h1:2PnMjfWD7wBILjqQbt530v576A/cAbQvEW9gGIpYMUs=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_bugsnag_bugsnag_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/bugsnag/bugsnag-go",
    sum = "h1:rFt+Y/IK1aEZkEHchZRSq9OQbsSzIT/OrI8YFFmRIng=",
    version = "v0.0.0-20141110184014-b1d153021fcd",
)

go_repository(
    name = "com_github_bugsnag_osext",
    build_file_proto_mode = "disable",
    importpath = "github.com/bugsnag/osext",
    sum = "h1:otBG+dV+YK+Soembjv71DPz3uX/V/6MMlSyD9JBQ6kQ=",
    version = "v0.0.0-20130617224835-0dd3f918b21b",
)

go_repository(
    name = "com_github_bugsnag_panicwrap",
    build_file_proto_mode = "disable",
    importpath = "github.com/bugsnag/panicwrap",
    sum = "h1:nvj0OLI3YqYXer/kZD8Ri1aaunCxIEsOst1BVJswV0o=",
    version = "v0.0.0-20151223152923-e2c28503fcd0",
)

go_repository(
    name = "com_github_certifi_gocertifi",
    build_file_proto_mode = "disable",
    importpath = "github.com/certifi/gocertifi",
    sum = "h1:uH66TXeswKn5PW5zdZ39xEwfS9an067BirqA+P4QaLI=",
    version = "v0.0.0-20200922220541-2c3bb06c6054",
)

go_repository(
    name = "com_github_checkpoint_restore_go_criu_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/checkpoint-restore/go-criu/v4",
    sum = "h1:WW2B2uxx9KWF6bGlHqhm8Okiafwwx7Y2kcpn8lCpjgo=",
    version = "v4.1.0",
)

go_repository(
    name = "com_github_containerd_btrfs",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/btrfs",
    sum = "h1:osn1exbzdub9L5SouXO5swW4ea/xVdJZ3wokxN5GrnA=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_containerd_stargz_snapshotter_estargz",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/stargz-snapshotter/estargz",
    sum = "h1:5e7heayhB7CcgdTkqfZqrNaNv15gABwr3Q2jBTbLlt4=",
    version = "v0.4.1",
)

go_repository(
    name = "com_github_coreos_bbolt",
    build_file_proto_mode = "disable",
    importpath = "github.com/coreos/bbolt",
    sum = "h1:wZwiHHUieZCquLkDL0B8UhzreNWsPHooDAG3q34zk0s=",
    version = "v1.3.2",
)

go_repository(
    name = "com_github_coreos_etcd",
    build_file_proto_mode = "disable",
    importpath = "github.com/coreos/etcd",
    sum = "h1:8F3hqu9fGYLBifCmRCJsicFqDx/D68Rt3q1JMazcgBQ=",
    version = "v3.3.13+incompatible",
)

go_repository(
    name = "com_github_coreos_go_iptables",
    build_file_proto_mode = "disable",
    importpath = "github.com/coreos/go-iptables",
    sum = "h1:is9qnZMPYjLd8LYqmm/qlE+wwEgJIkTYdhV3rfZo4jk=",
    version = "v0.6.0",
)

go_repository(
    name = "com_github_coreos_go_oidc",
    build_file_proto_mode = "disable",
    importpath = "github.com/coreos/go-oidc",
    sum = "h1:sdJrfw8akMnCuUlaZU3tE/uYXFgfqom8DBE9so9EBsM=",
    version = "v2.1.0+incompatible",
)

go_repository(
    name = "com_github_coreos_pkg",
    build_file_proto_mode = "disable",
    importpath = "github.com/coreos/pkg",
    sum = "h1:lBNOc5arjvs8E5mO2tbpBpLoyyu8B6e44T7hJy6potg=",
    version = "v0.0.0-20180928190104-399ea9e2e55f",
)

go_repository(
    name = "com_github_d2g_dhcp4",
    build_file_proto_mode = "disable",
    importpath = "github.com/d2g/dhcp4",
    sum = "h1:Xo2rK1pzOm0jO6abTPIQwbAmqBIOj132otexc1mmzFc=",
    version = "v0.0.0-20170904100407-a1d1b6c41b1c",
)

go_repository(
    name = "com_github_d2g_dhcp4client",
    build_file_proto_mode = "disable",
    importpath = "github.com/d2g/dhcp4client",
    sum = "h1:suYBsYZIkSlUMEz4TAYCczKf62IA2UWC+O8+KtdOhCo=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_d2g_dhcp4server",
    build_file_proto_mode = "disable",
    importpath = "github.com/d2g/dhcp4server",
    sum = "h1:+CpLbZIeUn94m02LdEKPcgErLJ347NUwxPKs5u8ieiY=",
    version = "v0.0.0-20181031114812-7d4a0a7f59a5",
)

go_repository(
    name = "com_github_d2g_hardwareaddr",
    build_file_proto_mode = "disable",
    importpath = "github.com/d2g/hardwareaddr",
    sum = "h1:itqmmf1PFpC4n5JW+j4BU7X4MTfVurhYRTjODoPb2Y8=",
    version = "v0.0.0-20190221164911-e7d9fbe030e4",
)

go_repository(
    name = "com_github_denverdino_aliyungo",
    build_file_proto_mode = "disable",
    importpath = "github.com/denverdino/aliyungo",
    sum = "h1:p6poVbjHDkKa+wtC8frBMwQtT3BmqGYBjzMwJ63tuR4=",
    version = "v0.0.0-20190125010748-a747050bb1ba",
)

go_repository(
    name = "com_github_dgryski_go_sip13",
    build_file_proto_mode = "disable",
    importpath = "github.com/dgryski/go-sip13",
    sum = "h1:RMLoZVzv4GliuWafOuPuQDKSm1SJph7uCRnnS61JAn4=",
    version = "v0.0.0-20181026042036-e10d5fee7954",
)

go_repository(
    name = "com_github_docker_cli",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/cli",
    sum = "h1:2HQmlpI3yI9deH18Q6xiSOIjXD4sLI55Y/gfpa8/558=",
    version = "v0.0.0-20191017083524-a8ff7f821017",
)

go_repository(
    name = "com_github_docker_docker_credential_helpers",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/docker-credential-helpers",
    sum = "h1:zI2p9+1NQYdnG6sMU26EX4aVGlqbInSQxQXLvzJ4RPQ=",
    version = "v0.6.3",
)

go_repository(
    name = "com_github_docker_libtrust",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/libtrust",
    sum = "h1:ZClxb8laGDf5arXfYcAtECDFgAgHklGI8CxgjHnXKJ4=",
    version = "v0.0.0-20150114040149-fa567046d9b1",
)

go_repository(
    name = "com_github_docker_spdystream",
    build_file_proto_mode = "disable",
    importpath = "github.com/docker/spdystream",
    sum = "h1:cenwrSVm+Z7QLSV/BsnenAOcDXdX4cMv4wP0B/5QbPg=",
    version = "v0.0.0-20160310174837-449fdfce4d96",
)

go_repository(
    name = "com_github_elazarl_goproxy",
    build_file_proto_mode = "disable",
    importpath = "github.com/elazarl/goproxy",
    sum = "h1:yUdfgN0XgIJw7foRItutHYUIhlcKzcSf5vDpdhQAKTc=",
    version = "v0.0.0-20180725130230-947c36da3153",
)

go_repository(
    name = "com_github_emicklei_go_restful",
    build_file_proto_mode = "disable",
    importpath = "github.com/emicklei/go-restful",
    sum = "h1:spTtZBk5DYEvbxMVutUuTyh1Ao2r4iyvLdACqsl/Ljk=",
    version = "v2.9.5+incompatible",
)

go_repository(
    name = "com_github_evanphx_json_patch",
    build_file_proto_mode = "disable",
    importpath = "github.com/evanphx/json-patch",
    sum = "h1:glyUF9yIYtMHzn8xaKw5rMhdWcwsYV8dZHIq5567/xs=",
    version = "v4.11.0+incompatible",
)

go_repository(
    name = "com_github_felixge_httpsnoop",
    build_file_proto_mode = "disable",
    importpath = "github.com/felixge/httpsnoop",
    sum = "h1:lvB5Jl89CsZtGIWuTcDM1E/vkVs49/Ml7JJe07l8SPQ=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_fogleman_gg",
    build_file_proto_mode = "disable",
    importpath = "github.com/fogleman/gg",
    sum = "h1:/7zJX8F6AaYQc57WQCyN9cAIz+4bCJGO9B+dyW29am8=",
    version = "v1.3.0",
)

go_repository(
    name = "com_github_fullsailor_pkcs7",
    build_file_proto_mode = "disable",
    importpath = "github.com/fullsailor/pkcs7",
    sum = "h1:RDBNVkRviHZtvDvId8XSGPu3rmpmSe+wKRcEWNgsfWU=",
    version = "v0.0.0-20190404230743-d7302db945fa",
)

go_repository(
    name = "com_github_garyburd_redigo",
    build_file_proto_mode = "disable",
    importpath = "github.com/garyburd/redigo",
    sum = "h1:LofdAjjjqCSXMwLGgOgnE+rdPuvX9DxCqaHwKy7i/ko=",
    version = "v0.0.0-20150301180006-535138d7bcd7",
)

go_repository(
    name = "com_github_getsentry_raven_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/getsentry/raven-go",
    sum = "h1:no+xWJRb5ZI7eE8TWgIq1jLulQiIoLG0IfYxv5JYMGs=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_go_fonts_dejavu",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-fonts/dejavu",
    sum = "h1:JSajPXURYqpr+Cu8U9bt8K+XcACIHWqWrvWCKyeFmVQ=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_go_fonts_latin_modern",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-fonts/latin-modern",
    sum = "h1:5/Tv1Ek/QCr20C6ZOz15vw3g7GELYL98KWr8Hgo+3vk=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_go_fonts_stix",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-fonts/stix",
    sum = "h1:UlZlgrvvmT/58o573ot7NFw0vZasZ5I6bcIft/oMdgg=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_go_ini_ini",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-ini/ini",
    sum = "h1:Mujh4R/dH6YL8bxuISne3xX2+qcQ9p0IxKAP6ExWoUo=",
    version = "v1.25.4",
)

go_repository(
    name = "com_github_go_kit_log",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-kit/log",
    sum = "h1:DGJh0Sm43HbOeYDNnVZFl8BvcYVvjD5bqYJvp0REbwQ=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_go_openapi_jsonreference",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-openapi/jsonreference",
    sum = "h1:1WJP/wi4OjB4iV8KVbH73rQaoialJrqv8gitZLxGLtM=",
    version = "v0.19.5",
)

go_repository(
    name = "com_github_go_openapi_spec",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-openapi/spec",
    sum = "h1:0XRyw8kguri6Yw4SxhsQA/atC88yqrk0+G4YhI2wabc=",
    version = "v0.19.3",
)

go_repository(
    name = "com_github_go_task_slim_sprig",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-task/slim-sprig",
    sum = "h1:p104kn46Q8WdvHunIJ9dAyjPVtrBPhSr3KT2yUst43I=",
    version = "v0.0.0-20210107165309-348f09dbbbc0",
)

go_repository(
    name = "com_github_google_go_containerregistry",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/go-containerregistry",
    sum = "h1:/+mFTs4AlwsJ/mJe8NDtKb7BxLtbZFpcn8vDsneEkwQ=",
    version = "v0.5.1",
)

go_repository(
    name = "com_github_google_go_github_v39",
    build_file_proto_mode = "disable",
    importpath = "github.com/google/go-github/v39",
    sum = "h1:rNNM311XtPOz5rDdsJXAp2o8F67X9FnROXTvto3aSnQ=",
    version = "v39.2.0",
)

go_repository(
    name = "com_github_googleapis_gnostic",
    build_file_proto_mode = "disable",
    importpath = "github.com/googleapis/gnostic",
    sum = "h1:9fHAtK0uDfpveeqqo1hkEZJcFvYXAiCN3UutL8F9xHw=",
    version = "v0.5.5",
)

go_repository(
    name = "com_github_iancoleman_strcase",
    build_file_proto_mode = "disable",
    importpath = "github.com/iancoleman/strcase",
    sum = "h1:05I4QRnGpI0m37iZQRuskXh+w77mr6Z41lwQzuHLwW0=",
    version = "v0.2.0",
)

go_repository(
    name = "com_github_j_keck_arping",
    build_file_proto_mode = "disable",
    importpath = "github.com/j-keck/arping",
    sum = "h1:hlLhuXgQkzIJTZuhMigvG/CuSkaspeaD9hRDk2zuiMI=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_joefitzgerald_rainbow_reporter",
    build_file_proto_mode = "disable",
    importpath = "github.com/joefitzgerald/rainbow-reporter",
    sum = "h1:AuMG652zjdzI0YCCnXAqATtRBpGXMcAnrajcaTrSeuo=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_jonboulle_clockwork",
    build_file_proto_mode = "disable",
    importpath = "github.com/jonboulle/clockwork",
    sum = "h1:UOGuzwb1PwsrDAObMuhUnj0p5ULPj8V/xJ7Kx9qUBdQ=",
    version = "v0.2.2",
)

go_repository(
    name = "com_github_jpillora_backoff",
    build_file_proto_mode = "disable",
    importpath = "github.com/jpillora/backoff",
    sum = "h1:uvFg412JmmHBHw7iwprIxkPMI+sGQ4kzOWsMeHnm2EA=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_julienschmidt_httprouter",
    build_file_proto_mode = "disable",
    importpath = "github.com/julienschmidt/httprouter",
    sum = "h1:U0609e9tgbseu3rBINet9P48AI/D3oJs4dN7jwJOQ1U=",
    version = "v1.3.0",
)

go_repository(
    name = "com_github_jung_kurt_gofpdf",
    build_file_proto_mode = "disable",
    importpath = "github.com/jung-kurt/gofpdf",
    sum = "h1:PJr+ZMXIecYc1Ey2zucXdR73SMBtgjPgwa31099IMv0=",
    version = "v1.0.3-0.20190309125859-24315acbbda5",
)

go_repository(
    name = "com_github_linuxkit_virtsock",
    build_file_proto_mode = "disable",
    importpath = "github.com/linuxkit/virtsock",
    sum = "h1:jUp75lepDg0phMUJBCmvaeFDldD2N3S1lBuPwUTszio=",
    version = "v0.0.0-20201010232012-f8cee7dfc7a3",
)

go_repository(
    name = "com_github_lyft_protoc_gen_star",
    build_file_proto_mode = "disable",
    importpath = "github.com/lyft/protoc-gen-star",
    sum = "h1:zSGLzsUew8RT+ZKPHc3jnf8XLaVyHzTcAFBzHtCNR20=",
    version = "v0.5.3",
)

go_repository(
    name = "com_github_marstr_guid",
    build_file_proto_mode = "disable",
    importpath = "github.com/marstr/guid",
    sum = "h1:/M4H/1G4avsieL6BbUwCOBzulmoeKVP5ux/3mQNnbyI=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_mattn_go_shellwords",
    build_file_proto_mode = "disable",
    importpath = "github.com/mattn/go-shellwords",
    sum = "h1:M2zGm7EW6UQJvDeQxo4T51eKPurbeFbe8WtebGE2xrk=",
    version = "v1.0.12",
)

go_repository(
    name = "com_github_maxbrunsfeld_counterfeiter_v6",
    build_file_proto_mode = "disable",
    importpath = "github.com/maxbrunsfeld/counterfeiter/v6",
    sum = "h1:g+4J5sZg6osfvEfkRZxJ1em0VT95/UOZgi/l7zi1/oE=",
    version = "v6.2.2",
)

go_repository(
    name = "com_github_microsoft_hcsshim_test",
    build_file_proto_mode = "disable",
    importpath = "github.com/Microsoft/hcsshim/test",
    sum = "h1:4FA+QBaydEHlwxg0lMN3rhwoDaQy6LKhVWR4qvq4BuA=",
    version = "v0.0.0-20210227013316-43a75bb4edd3",
)

go_repository(
    name = "com_github_mistifyio_go_zfs",
    build_file_proto_mode = "disable",
    importpath = "github.com/mistifyio/go-zfs",
    sum = "h1:aKW/4cBs+yK6gpqU3K/oIwk9Q/XICqd3zOX/UFuvqmk=",
    version = "v2.1.2-0.20190413222219-f784269be439+incompatible",
)

go_repository(
    name = "com_github_mitchellh_osext",
    build_file_proto_mode = "disable",
    importpath = "github.com/mitchellh/osext",
    sum = "h1:2+myh5ml7lgEU/51gbeLHfKGNfgEQQIWrlbdaOsidbQ=",
    version = "v0.0.0-20151018003038-5e2d6d41470f",
)

go_repository(
    name = "com_github_munnerz_goautoneg",
    build_file_proto_mode = "disable",
    importpath = "github.com/munnerz/goautoneg",
    sum = "h1:C3w9PqII01/Oq1c1nUAm88MOHcQC9l5mIlSMApZMrHA=",
    version = "v0.0.0-20191010083416-a7dc8b61c822",
)

go_repository(
    name = "com_github_mwitkow_go_conntrack",
    build_file_proto_mode = "disable",
    importpath = "github.com/mwitkow/go-conntrack",
    sum = "h1:KUppIJq7/+SVif2QVs3tOP0zanoHgBEVAwHxUSIzRqU=",
    version = "v0.0.0-20190716064945-2f068394615f",
)

go_repository(
    name = "com_github_mxk_go_flowrate",
    build_file_proto_mode = "disable",
    importpath = "github.com/mxk/go-flowrate",
    sum = "h1:y5//uYreIhSUg3J1GEMiLbxo1LJaP8RfCpH6pymGZus=",
    version = "v0.0.0-20140419014527-cca7078d478f",
)

go_repository(
    name = "com_github_ncw_swift",
    build_file_proto_mode = "disable",
    importpath = "github.com/ncw/swift",
    sum = "h1:4DQRPj35Y41WogBxyhOXlrI37nzGlyEcsforeudyYPQ=",
    version = "v1.0.47",
)

go_repository(
    name = "com_github_nxadm_tail",
    build_file_proto_mode = "disable",
    importpath = "github.com/nxadm/tail",
    sum = "h1:nPr65rt6Y5JFSKQO7qToXr7pePgD6Gwiw05lkbyAQTE=",
    version = "v1.4.8",
)

go_repository(
    name = "com_github_oklog_ulid",
    build_file_proto_mode = "disable",
    importpath = "github.com/oklog/ulid",
    sum = "h1:EGfNDEx6MqHz8B3uNV6QAib1UR2Lm97sHi3ocA6ESJ4=",
    version = "v1.3.1",
)

go_repository(
    name = "com_github_olekukonko_tablewriter",
    build_file_proto_mode = "disable",
    importpath = "github.com/olekukonko/tablewriter",
    sum = "h1:58+kh9C6jJVXYjt8IE48G2eWl6BjwU5Gj0gqY84fy78=",
    version = "v0.0.0-20170122224234-a0225b3f23b5",
)

go_repository(
    name = "com_github_peterbourgon_diskv",
    build_file_proto_mode = "disable",
    importpath = "github.com/peterbourgon/diskv",
    sum = "h1:UBdAOUP5p4RWqPBg048CAvpKN+vxiaj6gdUUzhl4XmI=",
    version = "v2.0.1+incompatible",
)

go_repository(
    name = "com_github_phpdave11_gofpdf",
    build_file_proto_mode = "disable",
    importpath = "github.com/phpdave11/gofpdf",
    sum = "h1:KPKiIbfwbvC/wOncwhrpRdXVj2CZTCFlw4wnoyjtHfQ=",
    version = "v1.4.2",
)

go_repository(
    name = "com_github_phpdave11_gofpdi",
    build_file_proto_mode = "disable",
    importpath = "github.com/phpdave11/gofpdi",
    sum = "h1:RZb9NG62cw/RW0rHAduVRo+98R8o/G1krcg2ns7DakQ=",
    version = "v1.0.12",
)

go_repository(
    name = "com_github_pquerna_cachecontrol",
    build_file_proto_mode = "disable",
    importpath = "github.com/pquerna/cachecontrol",
    sum = "h1:0XM1XL/OFFJjXsYXlG30spTkV/E9+gmd5GD1w2HE8xM=",
    version = "v0.0.0-20171018203845-0dec1b30a021",
)

go_repository(
    name = "com_github_prometheus_tsdb",
    build_file_proto_mode = "disable",
    importpath = "github.com/prometheus/tsdb",
    sum = "h1:YZcsG11NqnK4czYLrWd9mpEuAJIHVQLwdrleYfszMAA=",
    version = "v0.7.1",
)

go_repository(
    name = "com_github_puerkitobio_purell",
    build_file_proto_mode = "disable",
    importpath = "github.com/PuerkitoBio/purell",
    sum = "h1:WEQqlqaGbrPkxLJWfBwQmfEAE1Z7ONdDLqrN38tNFfI=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_puerkitobio_urlesc",
    build_file_proto_mode = "disable",
    importpath = "github.com/PuerkitoBio/urlesc",
    sum = "h1:d+Bc7a5rLufV/sSk/8dngufqelfh6jnri85riMAaF/M=",
    version = "v0.0.0-20170810143723-de5bf2ad4578",
)

go_repository(
    name = "com_github_ruudk_golang_pdf417",
    build_file_proto_mode = "disable",
    importpath = "github.com/ruudk/golang-pdf417",
    sum = "h1:nlG4Wa5+minh3S9LVFtNoY+GVRiudA2e3EVfcCi3RCA=",
    version = "v0.0.0-20181029194003-1af4ab5afa58",
)

go_repository(
    name = "com_github_safchain_ethtool",
    build_file_proto_mode = "disable",
    importpath = "github.com/safchain/ethtool",
    sum = "h1:ZFfeKAhIQiiOrQaI3/znw0gOmYpO28Tcu1YaqMa/jtQ=",
    version = "v0.0.0-20210803160452-9aa261dae9b1",
)

go_repository(
    name = "com_github_sclevine_agouti",
    build_file_proto_mode = "disable",
    importpath = "github.com/sclevine/agouti",
    sum = "h1:8IBJS6PWz3uTlMP3YBIR5f+KAldcGuOeFkFbUWfBgK4=",
    version = "v3.0.0+incompatible",
)

go_repository(
    name = "com_github_sclevine_spec",
    build_file_proto_mode = "disable",
    importpath = "github.com/sclevine/spec",
    sum = "h1:1Jwdf9jSfDl9NVmt8ndHqbTZ7XCCPbh1jI3hkDBHVYA=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_shopify_logrus_bugsnag",
    build_file_proto_mode = "disable",
    importpath = "github.com/Shopify/logrus-bugsnag",
    sum = "h1:UrqY+r/OJnIp5u0s1SbQ8dVfLCZJsnvazdBP5hS4iRs=",
    version = "v0.0.0-20171204204709-577dee27f20d",
)

go_repository(
    name = "com_github_soheilhy_cmux",
    build_file_proto_mode = "disable",
    importpath = "github.com/soheilhy/cmux",
    sum = "h1:jjzc5WVemNEDTLwv9tlmemhC73tI08BNOIGwBOo10Js=",
    version = "v0.1.5",
)

go_repository(
    name = "com_github_stoewer_go_strcase",
    build_file_proto_mode = "disable",
    importpath = "github.com/stoewer/go-strcase",
    sum = "h1:Z2iHWqGXH00XYgqDmNgQbIBxf3wrNq0F3feEy0ainaU=",
    version = "v1.2.0",
)

go_repository(
    name = "com_github_tchap_go_patricia",
    build_file_proto_mode = "disable",
    importpath = "github.com/tchap/go-patricia",
    sum = "h1:JvoDL7JSoIP2HDE8AbDH3zC8QBPxmzYe32HHy5yQ+Ck=",
    version = "v2.2.6+incompatible",
)

go_repository(
    name = "com_github_tmc_grpc_websocket_proxy",
    build_file_proto_mode = "disable",
    importpath = "github.com/tmc/grpc-websocket-proxy",
    sum = "h1:uruHq4dN7GR16kFc5fp3d1RIYzJW5onx8Ybykw2YQFA=",
    version = "v0.0.0-20201229170055-e5319fda7802",
)

go_repository(
    name = "com_github_tv42_httpunix",
    build_file_proto_mode = "disable",
    importpath = "github.com/tv42/httpunix",
    sum = "h1:u6SKchux2yDvFQnDHS3lPnIRmfVJ5Sxy3ao2SIdysLQ=",
    version = "v0.0.0-20191220191345-2ba4b9c3382c",
)

go_repository(
    name = "com_github_willf_bitset",
    build_file_proto_mode = "disable",
    importpath = "github.com/willf/bitset",
    sum = "h1:N7Z7E9UvjW+sGsEl7k/SJrvY2reP1A07MrGuCjIOjRE=",
    version = "v1.1.11",
)

go_repository(
    name = "com_github_xiang90_probing",
    build_file_proto_mode = "disable",
    importpath = "github.com/xiang90/probing",
    sum = "h1:eY9dn8+vbi4tKz5Qo6v2eYzo7kUS51QINcR5jNpbZS8=",
    version = "v0.0.0-20190116061207-43a291ad63a2",
)

go_repository(
    name = "com_github_xordataexchange_crypt",
    build_file_proto_mode = "disable",
    importpath = "github.com/xordataexchange/crypt",
    sum = "h1:ESFSdwYZvkeru3RtdrYueztKhOBCSAAzS4Gf+k0tEow=",
    version = "v0.0.3-0.20170626215501-b2862e3d0a77",
)

go_repository(
    name = "com_github_yvasiyarov_go_metrics",
    build_file_proto_mode = "disable",
    importpath = "github.com/yvasiyarov/go-metrics",
    sum = "h1:+lm10QQTNSBd8DVTNGHx7o/IKu9HYDvLMffDhbyLccI=",
    version = "v0.0.0-20140926110328-57bccd1ccd43",
)

go_repository(
    name = "com_github_yvasiyarov_gorelic",
    build_file_proto_mode = "disable",
    importpath = "github.com/yvasiyarov/gorelic",
    sum = "h1:hlE8//ciYMztlGpl/VA+Zm1AcTPHYkHJPbHqE6WJUXE=",
    version = "v0.0.0-20141212073537-a9bba5b9ab50",
)

go_repository(
    name = "com_github_yvasiyarov_newrelic_platform_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/yvasiyarov/newrelic_platform_go",
    sum = "h1:ERexzlUfuTvpE74urLSbIQW0Z/6hF9t8U4NsJLaioAY=",
    version = "v0.0.0-20140908184405-b21fdbd4370f",
)

go_repository(
    name = "in_gopkg_airbrake_gobrake_v2",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/airbrake/gobrake.v2",
    sum = "h1:7z2uVWwn7oVeeugY1DtlPAy5H+KYgB1KeKTnqjNatLo=",
    version = "v2.0.9",
)

go_repository(
    name = "in_gopkg_alecthomas_kingpin_v2",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/alecthomas/kingpin.v2",
    sum = "h1:jMFz6MfLP0/4fUyZle81rXUoxOBFi19VUFKVDOQfozc=",
    version = "v2.2.6",
)

go_repository(
    name = "in_gopkg_gemnasium_logrus_airbrake_hook_v2",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/gemnasium/logrus-airbrake-hook.v2",
    sum = "h1:OAj3g0cR6Dx/R07QgQe8wkA9RNjB2u4i700xBkIT4e0=",
    version = "v2.1.2",
)

go_repository(
    name = "in_gopkg_resty_v1",
    build_file_proto_mode = "disable",
    importpath = "gopkg.in/resty.v1",
    sum = "h1:CuXP0Pjfw9rOuY6EP+UvtNvt5DSqHpIxILZKT/quCZI=",
    version = "v1.12.0",
)

go_repository(
    name = "io_etcd_go_etcd",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/etcd",
    sum = "h1:1JFLBqwIgdyHN1ZtgjTBwO+blA6gVOmZurpiMEsETKo=",
    version = "v0.5.0-alpha.5.0.20200910180754-dd1b699fc489",
)

go_repository(
    name = "io_etcd_go_etcd_client_v3",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/etcd/client/v3",
    sum = "h1:62Eh0XOro+rDwkrypAGDfgmNh5Joq+z+W9HZdlXMzek=",
    version = "v3.5.0",
)

go_repository(
    name = "io_etcd_go_etcd_pkg_v3",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/etcd/pkg/v3",
    sum = "h1:ntrg6vvKRW26JRmHTE0iNlDgYK6JX3hg/4cD62X0ixk=",
    version = "v3.5.0",
)

go_repository(
    name = "io_etcd_go_etcd_raft_v3",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/etcd/raft/v3",
    sum = "h1:kw2TmO3yFTgE+F0mdKkG7xMxkit2duBDa2Hu6D/HMlw=",
    version = "v3.5.0",
)

go_repository(
    name = "io_etcd_go_etcd_server_v3",
    build_file_proto_mode = "disable",
    importpath = "go.etcd.io/etcd/server/v3",
    sum = "h1:jk8D/lwGEDlQU9kZXUFMSANkE22Sg5+mW27ip8xcF9E=",
    version = "v3.5.0",
)

go_repository(
    name = "io_k8s_code_generator",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/code-generator",
    sum = "h1:kM/68Y26Z/u//TFc1ggVVcg62te8A2yQh57jBfD0FWQ=",
    version = "v0.19.7",
)

go_repository(
    name = "io_k8s_gengo",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/gengo",
    sum = "h1:JApXBKYyB7l9xx+DK7/+mFjC7A9Bt5A93FPvFD0HIFE=",
    version = "v0.0.0-20201113003025-83324d819ded",
)

go_repository(
    name = "io_k8s_kube_openapi",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/kube-openapi",
    sum = "h1:jvamsI1tn9V0S8jicyX82qaFC0H/NKxv2e5mbqsgR80=",
    version = "v0.0.0-20211109043538-20434351676c",
)

go_repository(
    name = "io_k8s_kubernetes",
    build_file_proto_mode = "disable",
    importpath = "k8s.io/kubernetes",
    sum = "h1:qTfB+u5M92k2fCCCVP2iuhgwwSOv1EkAkvQY1tQODD8=",
    version = "v1.13.0",
)

go_repository(
    name = "io_k8s_sigs_apiserver_network_proxy_konnectivity_client",
    build_file_proto_mode = "disable",
    importpath = "sigs.k8s.io/apiserver-network-proxy/konnectivity-client",
    sum = "h1:fmRfl9WJ4ApJn7LxNuED4m0t18qivVQOxP6aAYG9J6c=",
    version = "v0.0.22",
)

go_repository(
    name = "io_opentelemetry_go_contrib",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/contrib",
    sum = "h1:ubFQUn0VCZ0gPwIoJfBJVpeBlyRMxu8Mm/huKWYd9p0=",
    version = "v0.20.0",
)

go_repository(
    name = "io_opentelemetry_go_contrib_instrumentation_net_http_otelhttp",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp",
    sum = "h1:Q3C9yzW6I9jqEc8sawxzxZmY48fs9u220KXq6d5s3XU=",
    version = "v0.20.0",
)

go_repository(
    name = "io_opentelemetry_go_otel_oteltest",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/oteltest",
    sum = "h1:HiITxCawalo5vQzdHfKeZurV8x7ljcqAgiWzF6Vaeaw=",
    version = "v0.20.0",
)

go_repository(
    name = "org_bazil_fuse",
    build_file_proto_mode = "disable",
    importpath = "bazil.org/fuse",
    sum = "h1:SRsZGA7aFnCZETmov57jwPrWuTmaZK6+4R4v5FUe1/c=",
    version = "v0.0.0-20200407214033-5883e5a4b512",
)

go_repository(
    name = "org_gioui",
    build_file_proto_mode = "disable",
    importpath = "gioui.org",
    sum = "h1:K72hopUosKG3ntOPNG4OzzbuhxGuVf06fa2la1/H/Ho=",
    version = "v0.0.0-20210308172011-57750fc8a0a6",
)

go_repository(
    name = "org_golang_google_cloud",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/cloud",
    sum = "h1:Cpp2P6TPjujNoC5M2KHY6g7wfyLYfIWRZaSdIKfDasA=",
    version = "v0.0.0-20151119220103-975617b05ea8",
)

go_repository(
    name = "org_gonum_v1_netlib",
    build_file_proto_mode = "disable",
    importpath = "gonum.org/v1/netlib",
    sum = "h1:OE9mWmgKkjJyEmDAAtGMPjXu+YNeGvK9VTSHY6+Qihc=",
    version = "v0.0.0-20190313105609-8cb42192e0e0",
)

go_repository(
    name = "com_github_avast_retry_go_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/avast/retry-go/v4",
    sum = "h1:AxIx0HGi4VZ3I02jr78j5lZ3M6x1E0Ivxa6b0pUUh7o=",
    version = "v4.5.1",
)

go_repository(
    name = "com_github_neo4j_neo4j_go_driver_v5",
    build_file_proto_mode = "disable",
    importpath = "github.com/neo4j/neo4j-go-driver/v5",
    sum = "h1:5Tbzmf5uikC8dv2a84uQ+rsXnMg0Bqgw6rtlXYbMi5M=",
    version = "v5.11.0",
)

go_repository(
    name = "com_github_bradleyfalzon_ghinstallation_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/bradleyfalzon/ghinstallation/v2",
    sum = "h1:zYSzkoIwekCQAr6GT6KxISLt4YRS6kd4/ixfzMN+7yc=",
    version = "v2.4.0",
)

go_repository(
    name = "cat_dario_mergo",
    build_file_proto_mode = "disable",
    importpath = "dario.cat/mergo",
    sum = "h1:AGCNq9Evsj31mOgNPcLyXc+4PNABt905YmuqPYYpBWk=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_mistifyio_go_zfs_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/mistifyio/go-zfs/v3",
    sum = "h1:YaoXgBePoMA12+S1u/ddkv+QqxcfiZK4prI6HPnkFiU=",
    version = "v3.0.1",
)

go_repository(
    name = "org_golang_google_genproto_googleapis_api",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/genproto/googleapis/api",
    sum = "h1:JpwMPBpFN3uKhdaekDpiNlImDdkUAyiJ6ez/uxGaUSo=",
    version = "v0.0.0-20231106174013-bbf56f31fb17",
)

go_repository(
    name = "org_golang_google_genproto_googleapis_rpc",
    build_file_proto_mode = "disable",
    importpath = "google.golang.org/genproto/googleapis/rpc",
    sum = "h1:AB/lmRny7e2pLhFEYIbl5qkDAUt2h0ZRO4wGPhZf+ik=",
    version = "v0.0.0-20231030173426-d783a09b4405",
)

go_repository(
    name = "com_github_launchdarkly_go_jsonstream_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-jsonstream/v3",
    sum = "h1:qJF/WI09EUJ7kSpmP5d1Rhc81NQdYUhP17McKfUq17E=",
    version = "v3.0.0",
)

go_repository(
    name = "com_github_launchdarkly_go_sdk_common_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-sdk-common/v3",
    sum = "h1:rVdLusAIViduNvyjNKy06RA+SPwk0Eq+NocNd1opDhk=",
    version = "v3.0.1",
)

go_repository(
    name = "com_github_99designs_go_keychain",
    build_file_proto_mode = "disable",
    importpath = "github.com/99designs/go-keychain",
    sum = "h1:/vQbFIOMbk2FiG/kXiLl8BRyzTWDw7gX/Hz7Dd5eDMs=",
    version = "v0.0.0-20191008050251-8e49817e8af4",
)

go_repository(
    name = "com_github_99designs_keyring",
    build_file_proto_mode = "disable",
    importpath = "github.com/99designs/keyring",
    sum = "h1:pZd3neh/EmUzWONb35LxQfvuY7kiSXAq3HQd97+XBn0=",
    version = "v1.2.2",
)

go_repository(
    name = "com_github_apache_thrift",
    build_file_proto_mode = "disable",
    importpath = "github.com/apache/thrift",
    sum = "h1:cMd2aj52n+8VoAtvSvLn4kDC3aZ6IAkBuqWQ2IDu7wo=",
    version = "v0.17.0",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_internal_v4a",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/internal/v4a",
    sum = "h1:OWYvKL53l1rbsUmW7bQyJVsYU/Ii3bbAAQIIFNbM0Tk=",
    version = "v1.3.18",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_internal_checksum",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/internal/checksum",
    sum = "h1:rTWjG6AvWekO2B1LHeM3ktU7MqyX9rzWQ7hgzneZW7E=",
    version = "v1.3.20",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_service_ssooidc",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/service/ssooidc",
    sum = "h1:AhmO1fHINP9vFYUE0LHzCWg/LfUWUF+zFPEcY9QXb7o=",
    version = "v1.28.2",
)

go_repository(
    name = "com_github_azure_azure_sdk_for_go_sdk_azcore",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/azure-sdk-for-go/sdk/azcore",
    sum = "h1:nyQWyZvwGTvunIMxi1Y9uXkcyr+I7TeNrr/foo4Kpk8=",
    version = "v1.14.0",
)

go_repository(
    name = "com_github_azure_azure_sdk_for_go_sdk_azidentity",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/azure-sdk-for-go/sdk/azidentity",
    sum = "h1:tfLQ34V6F7tVSwoTf/4lH5sE0o6eCJuNDTmH09nDpbc=",
    version = "v1.7.0",
)

go_repository(
    name = "com_github_azure_azure_sdk_for_go_sdk_internal",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/azure-sdk-for-go/sdk/internal",
    sum = "h1:ywEEhmNahHBihViHepv3xPBn1663uRv2t2q/ESv9seY=",
    version = "v1.10.0",
)

go_repository(
    name = "com_github_azure_azure_sdk_for_go_sdk_storage_azblob",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/azure-sdk-for-go/sdk/storage/azblob",
    sum = "h1:u/LLAOFgsMv7HmNL4Qufg58y+qElGOt5qv0z1mURkRY=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_azuread_microsoft_authentication_library_for_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/AzureAD/microsoft-authentication-library-for-go",
    sum = "h1:XHOnouVk1mxXfQidrMEnLlPk9UMeRtyBTnEFtxkV0kU=",
    version = "v1.2.2",
)

go_repository(
    name = "com_github_danieljoos_wincred",
    build_file_proto_mode = "disable",
    importpath = "github.com/danieljoos/wincred",
    sum = "h1:QLdCxFs1/Yl4zduvBdcHB8goaYk9RARS2SgLLRuAyr0=",
    version = "v1.1.2",
)

go_repository(
    name = "com_github_dnaeon_go_vcr",
    build_file_proto_mode = "disable",
    importpath = "github.com/dnaeon/go-vcr",
    sum = "h1:ReYa/UBrRyQdant9B4fNHGoCNKw6qh6P0fsdGmZpR7c=",
    version = "v1.1.0",
)

go_repository(
    name = "com_github_docopt_docopt_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/docopt/docopt-go",
    sum = "h1:bWDMxwH3px2JBh6AyO7hdCn/PkvCZXii8TGj7sbtEbQ=",
    version = "v0.0.0-20180111231733-ee0de3bc6815",
)

go_repository(
    name = "com_github_dvsekhvalnov_jose2go",
    build_file_proto_mode = "disable",
    importpath = "github.com/dvsekhvalnov/jose2go",
    sum = "h1:Y9gnSnP4qEI0+/uQkHvFXeD2PLPJeXEL+ySMEA2EjTY=",
    version = "v1.6.0",
)

go_repository(
    name = "com_github_go_pdf_fpdf",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-pdf/fpdf",
    sum = "h1:MlgtGIfsdMEEQJr2le6b/HNr1ZlQwxyWr77r2aj2U/8=",
    version = "v0.6.0",
)

go_repository(
    name = "com_github_godbus_dbus",
    build_file_proto_mode = "disable",
    importpath = "github.com/godbus/dbus",
    sum = "h1:ZpnhV/YsD2/4cESfV5+Hoeu/iUR3ruzNvZ+yQfO03a0=",
    version = "v0.0.0-20190726142602-4481cbc300e2",
)

go_repository(
    name = "com_github_gsterjov_go_libsecret",
    build_file_proto_mode = "disable",
    importpath = "github.com/gsterjov/go-libsecret",
    sum = "h1:6rhixN/i8ZofjG1Y75iExal34USq5p+wiN1tpie8IrU=",
    version = "v0.0.0-20161001094733-a6f4afe4910c",
)

go_repository(
    name = "com_github_johncgriffin_overflow",
    build_file_proto_mode = "disable",
    importpath = "github.com/JohnCGriffin/overflow",
    sum = "h1:RGWPOewvKIROun94nF7v2cua9qP+thov/7M50KEoeSU=",
    version = "v0.0.0-20211019200055-46fa312c352c",
)

go_repository(
    name = "com_github_klauspost_asmfmt",
    build_file_proto_mode = "disable",
    importpath = "github.com/klauspost/asmfmt",
    sum = "h1:4Ri7ox3EwapiOjCki+hw14RyKk201CN4rzyCJRFLpK4=",
    version = "v1.3.2",
)

go_repository(
    name = "com_github_minio_asm2plan9s",
    build_file_proto_mode = "disable",
    importpath = "github.com/minio/asm2plan9s",
    sum = "h1:AMFGa4R4MiIpspGNG7Z948v4n35fFGB3RR3G/ry4FWs=",
    version = "v0.0.0-20200509001527-cdd76441f9d8",
)

go_repository(
    name = "com_github_minio_c2goasm",
    build_file_proto_mode = "disable",
    importpath = "github.com/minio/c2goasm",
    sum = "h1:+n/aFZefKZp7spd8DFdX7uMikMLXX4oubIzJF4kv/wI=",
    version = "v0.0.0-20190812172519-36a3d3bbc4f3",
)

go_repository(
    name = "com_github_mtibben_percent",
    build_file_proto_mode = "disable",
    importpath = "github.com/mtibben/percent",
    sum = "h1:5gssi8Nqo8QU/r2pynCm+hBQHpkB/uNK7BJCFogWdzs=",
    version = "v0.2.1",
)

go_repository(
    name = "com_github_zeebo_assert",
    build_file_proto_mode = "disable",
    importpath = "github.com/zeebo/assert",
    sum = "h1:g7C04CbJuIDKNPFHmsk4hwZDO5O+kntRxzaUoNXj+IQ=",
    version = "v1.3.0",
)

go_repository(
    name = "com_github_zeebo_xxh3",
    build_file_proto_mode = "disable",
    importpath = "github.com/zeebo/xxh3",
    sum = "h1:xZmwmqxHZA8AI603jOQ0tMqmBr9lPeFwGg6d+xy9DC0=",
    version = "v1.0.2",
)

go_repository(
    name = "ht_sr_git_sbinet_gg",
    build_file_proto_mode = "disable",
    importpath = "git.sr.ht/~sbinet/gg",
    sum = "h1:LNhjNn8DerC8f9DHLz6lS0YYul/b602DUxDgGkd/Aik=",
    version = "v0.3.1",
)

go_repository(
    name = "com_github_launchdarkly_go_sdk_events_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-sdk-events/v2",
    sum = "h1:vnUN2Y7og/5wtOCcCZW7wYpmZcS++GAyclasc7gaTIY=",
    version = "v2.0.1",
)

go_repository(
    name = "com_github_launchdarkly_go_server_sdk_evaluation_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-server-sdk-evaluation/v2",
    sum = "h1:PAM0GvE0nIUBeOkjdiymIEKI+8FFLJ+fEsWTupW1yGU=",
    version = "v2.0.2",
)

go_repository(
    name = "com_github_launchdarkly_go_server_sdk_v6",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-server-sdk/v6",
    sum = "h1:pp/VBIon5JNbtSw7ih7I5MXN9mXHfo6T5xikKVy52dI=",
    version = "v6.1.0",
)

go_repository(
    name = "com_github_launchdarkly_go_test_helpers_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/launchdarkly/go-test-helpers/v3",
    sum = "h1:rh0085g1rVJM5qIukdaQ8z1XTWZztbJ49vRZuveqiuU=",
    version = "v3.0.2",
)

go_repository(
    name = "com_github_apapsch_go_jsonmerge_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/apapsch/go-jsonmerge/v2",
    sum = "h1:axGnT1gRIfimI7gJifB699GoE/oq+F2MU7Dml6nw9rQ=",
    version = "v2.0.0",
)

go_repository(
    name = "com_github_go_playground_locales",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-playground/locales",
    sum = "h1:EWaQ/wswjilfKLTECiXz7Rh+3BjFhfDFKv/oXslEjJA=",
    version = "v0.14.1",
)

go_repository(
    name = "com_github_go_playground_universal_translator",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-playground/universal-translator",
    sum = "h1:Bcnm0ZwsGyWbCzImXv+pAJnYK9S473LQFuzCbDbfSFY=",
    version = "v0.18.1",
)

go_repository(
    name = "com_github_go_playground_validator_v10",
    build_file_proto_mode = "disable",
    importpath = "github.com/go-playground/validator/v10",
    sum = "h1:9c50NUPC30zyuKprjL3vNZ0m5oG+jU0zvx4AqHGnv4k=",
    version = "v10.14.1",
)

go_repository(
    name = "com_github_getsentry_sentry_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/getsentry/sentry-go",
    sum = "h1:Pv98CIbtB3LkMWmXi4Joa5OOcwbmnX88sF5qbK3r3Ps=",
    version = "v0.27.0",
)

go_repository(
    name = "com_github_kataras_sitemap",
    build_file_proto_mode = "disable",
    importpath = "github.com/kataras/sitemap",
    sum = "h1:w71CRMMKYMJh6LR2wTgnk5hSgjVNB9KL60n5e2KHvLY=",
    version = "v0.0.6",
)

go_repository(
    name = "com_github_schollz_closestmatch",
    build_file_proto_mode = "disable",
    importpath = "github.com/schollz/closestmatch",
    sum = "h1:Uel2GXEpJqOWBrlyI+oY9LTiyyjYS17cCYRqP13/SHk=",
    version = "v2.1.0+incompatible",
)

go_repository(
    name = "com_github_goccy_go_json",
    build_file_proto_mode = "disable",
    importpath = "github.com/goccy/go-json",
    sum = "h1:Fq85nIqj+gXn/S5ahsiTlK3TmC85qgirsdTP/+DeaC4=",
    version = "v0.10.5",
)

go_repository(
    name = "com_github_invopop_yaml",
    build_file_proto_mode = "disable",
    importpath = "github.com/invopop/yaml",
    sum = "h1:YW3WGUoJEXYfzWBjn00zIlrw7brGVD0fUKRYDPAPhrc=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_juju_gnuflag",
    build_file_proto_mode = "disable",
    importpath = "github.com/juju/gnuflag",
    sum = "h1:c93kUJDtVAXFEhsCh5jSxyOJmFHuzcihnslQiX8Urwo=",
    version = "v0.0.0-20171113085948-2ce1bb71843d",
)

go_repository(
    name = "com_github_leodido_go_urn",
    build_file_proto_mode = "disable",
    importpath = "github.com/leodido/go-urn",
    sum = "h1:XlAE/cm/ms7TE/VMVoduSpNBoyc2dOxHs5MZSwAN63Q=",
    version = "v1.2.4",
)

go_repository(
    name = "com_github_pelletier_go_toml_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/pelletier/go-toml/v2",
    sum = "h1:uH2qQXheeefCCkuBBSLi7jCiSmj3VRh2+Goq2N7Xxu0=",
    version = "v2.0.9",
)

go_repository(
    name = "com_github_ravenox_go_jsoncommentstrip",
    build_file_proto_mode = "disable",
    importpath = "github.com/RaveNoX/go-jsoncommentstrip",
    sum = "h1:t527LHHE3HmiHrq74QMpNPZpGCIJzTx+apLkMKt4HC0=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_spkg_bom",
    build_file_proto_mode = "disable",
    importpath = "github.com/spkg/bom",
    sum = "h1:fiWzISvDn0Csy5H0iwgAuJGQTUpVfEMJJd4nRFXogbc=",
    version = "v0.0.0-20160624110644-59b7046e48ad",
)

go_repository(
    name = "com_google_cloud_go_compute",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/compute",
    sum = "h1:nWEMDhgbBkBJjfpVySqU4jgWdc22PLR0o4vEexZHers=",
    version = "v1.23.2",
)

go_repository(
    name = "io_opentelemetry_go_otel_metric",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/metric",
    sum = "h1:4xNulvn9gjzo4hjg+wzIKG7iNFEaBMX00Qd4QIZs7+w=",
    version = "v1.30.0",
)

go_repository(
    name = "com_github_aws_aws_sdk_go_v2_feature_s3_manager",
    build_file_proto_mode = "disable",
    importpath = "github.com/aws/aws-sdk-go-v2/feature/s3/manager",
    sum = "h1:DIheXDgLzIUyZNB9BKM+9OGbvwbxitX0N6b6qNbMmNU=",
    version = "v1.17.23",
)

go_repository(
    name = "com_github_azure_azure_pipeline_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/azure-pipeline-go",
    sum = "h1:7U9HBg1JFK3jHl5qmo4CTZKFTVgMwdFHMVtCdfBE21U=",
    version = "v0.2.3",
)

go_repository(
    name = "com_github_azure_azure_storage_blob_go",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/azure-storage-blob-go",
    sum = "h1:1BCg74AmVdYwO3dlKwtFU1V0wU2PZdREkXvAmZJRUlM=",
    version = "v0.14.0",
)

go_repository(
    name = "com_github_azure_go_autorest",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/go-autorest",
    sum = "h1:V5VMDjClD3GiElqLWO7mz2MxNAK/vTfRHdAubSIPRgs=",
    version = "v14.2.0+incompatible",
)

go_repository(
    name = "com_github_azure_go_autorest_autorest_adal",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/go-autorest/autorest/adal",
    sum = "h1:P8An8Z9rH1ldbOLdFpxYorgOt2sywL9V24dAwWHPuGc=",
    version = "v0.9.16",
)

go_repository(
    name = "com_github_azure_go_autorest_autorest_date",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/go-autorest/autorest/date",
    sum = "h1:7gUk1U5M/CQbp9WoqinNzJar+8KY+LPI6wiWrP/myHw=",
    version = "v0.3.0",
)

go_repository(
    name = "com_github_azure_go_autorest_autorest_mocks",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/go-autorest/autorest/mocks",
    sum = "h1:K0laFcLE6VLTOwNgSxaGbUcLPuGXlNkbVvq4cW4nIHk=",
    version = "v0.4.1",
)

go_repository(
    name = "com_github_azure_go_autorest_logger",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/go-autorest/logger",
    sum = "h1:IG7i4p/mDa2Ce4TRyAO8IHnVhAVF3RFU+ZtXWSmf4Tg=",
    version = "v0.2.1",
)

go_repository(
    name = "com_github_azure_go_autorest_tracing",
    build_file_proto_mode = "disable",
    importpath = "github.com/Azure/go-autorest/tracing",
    sum = "h1:TYi4+3m5t6K48TGI9AUdb+IzbnSxvnvUMfuitfgcfuo=",
    version = "v0.6.0",
)

go_repository(
    name = "com_github_cockroachdb_cockroach_go_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/cockroachdb/cockroach-go/v2",
    sum = "h1:3XzfSMuUT0wBe1a3o5C0eOTcArhmmFAg2Jzh/7hhKqo=",
    version = "v2.1.1",
)

go_repository(
    name = "com_github_gobuffalo_attrs",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/attrs",
    sum = "h1:hSkbZ9XSyjyBirMeqSqUrK+9HboWrweVlzRNqoBi2d4=",
    version = "v0.0.0-20190224210810-a9411de4debd",
)

go_repository(
    name = "com_github_gobuffalo_depgen",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/depgen",
    sum = "h1:31atYa/UW9V5q8vMJ+W6wd64OaaTHUrCUXER358zLM4=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_gobuffalo_envy",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/envy",
    sum = "h1:GlXgaiBkmrYMHco6t4j7SacKO4XUjvh5pwXh0f4uxXU=",
    version = "v1.7.0",
)

go_repository(
    name = "com_github_gobuffalo_flect",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/flect",
    sum = "h1:3GQ53z7E3o00C/yy7Ko8VXqQXoJGLkrTQCLTF1EjoXU=",
    version = "v0.1.3",
)

go_repository(
    name = "com_github_gobuffalo_genny",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/genny",
    sum = "h1:iQ0D6SpNXIxu52WESsD+KoQ7af2e3nCfnSBoSF/hKe0=",
    version = "v0.1.1",
)

go_repository(
    name = "com_github_gobuffalo_gitgen",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/gitgen",
    sum = "h1:mSVZ4vj4khv+oThUfS+SQU3UuFIZ5Zo6UNcvK8E8Mz8=",
    version = "v0.0.0-20190315122116-cc086187d211",
)

go_repository(
    name = "com_github_gobuffalo_gogen",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/gogen",
    sum = "h1:dLg+zb+uOyd/mKeQUYIbwbNmfRsr9hd/WtYWepmayhI=",
    version = "v0.1.1",
)

go_repository(
    name = "com_github_gobuffalo_logger",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/logger",
    sum = "h1:8thhT+kUJMTMy3HlX4+y9Da+BNJck+p109tqqKp7WDs=",
    version = "v0.0.0-20190315122211-86e12af44bc2",
)

go_repository(
    name = "com_github_gobuffalo_mapi",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/mapi",
    sum = "h1:fq9WcL1BYrm36SzK6+aAnZ8hcp+SrmnDyAxhNx8dvJk=",
    version = "v1.0.2",
)

go_repository(
    name = "com_github_gobuffalo_packd",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/packd",
    sum = "h1:4sGKOD8yaYJ+dek1FDkwcxCHA40M4kfKgFHx8N2kwbU=",
    version = "v0.1.0",
)

go_repository(
    name = "com_github_gobuffalo_packr_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/packr/v2",
    sum = "h1:Ir9W9XIm9j7bhhkKE9cokvtTl1vBm62A/fene/ZCj6A=",
    version = "v2.2.0",
)

go_repository(
    name = "com_github_gobuffalo_syncx",
    build_file_proto_mode = "disable",
    importpath = "github.com/gobuffalo/syncx",
    sum = "h1:tpom+2CJmpzAWj5/VEHync2rJGi+epHNIeRSWjzGA+4=",
    version = "v0.0.0-20190224160051-33c29581e754",
)

go_repository(
    name = "com_github_jackc_pgerrcode",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgerrcode",
    sum = "h1:WAvSpGf7MsFuzAtK4Vk7R4EVe+liW4x83r4oWu0WHKw=",
    version = "v0.0.0-20201024163028-a0d42d470451",
)

go_repository(
    name = "com_github_jackc_pgservicefile",
    build_file_proto_mode = "disable",
    importpath = "github.com/jackc/pgservicefile",
    sum = "h1:C8S2+VttkHFdOOCXJe+YGfa4vHYwlt4Zx+IVXQ97jYg=",
    version = "v0.0.0-20200714003250-2b9c44734f2b",
)

go_repository(
    name = "com_github_jinzhu_inflection",
    build_file_proto_mode = "disable",
    importpath = "github.com/jinzhu/inflection",
    sum = "h1:K317FqzuhWc8YvSVlFMCCUb36O/S9MCKRDI7QkRKD/E=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_joho_godotenv",
    build_file_proto_mode = "disable",
    importpath = "github.com/joho/godotenv",
    sum = "h1:Zjp+RcGpHhGlrMbJzXTrZZPrWj+1vfm90La1wgB6Bhc=",
    version = "v1.3.0",
)

go_repository(
    name = "com_github_karrick_godirwalk",
    build_file_proto_mode = "disable",
    importpath = "github.com/karrick/godirwalk",
    sum = "h1:lOpSw2vJP0y5eLBW906QwKsUK/fe/QDyoqM5rnnuPDY=",
    version = "v1.10.3",
)

go_repository(
    name = "com_github_markbates_oncer",
    build_file_proto_mode = "disable",
    importpath = "github.com/markbates/oncer",
    sum = "h1:JgVTCPf0uBVcUSWpyXmGpgOc62nK5HWUBKAGc3Qqa5k=",
    version = "v0.0.0-20181203154359-bf2de49a0be2",
)

go_repository(
    name = "com_github_markbates_safe",
    build_file_proto_mode = "disable",
    importpath = "github.com/markbates/safe",
    sum = "h1:yjZkbvRM6IzKj9tlu/zMJLS0n/V351OZWRnF3QfaUxI=",
    version = "v1.0.1",
)

go_repository(
    name = "com_github_mattn_go_ieproxy",
    build_file_proto_mode = "disable",
    importpath = "github.com/mattn/go-ieproxy",
    sum = "h1:qiyop7gCflfhwCzGyeT0gro3sF9AIg9HU98JORTkqfI=",
    version = "v0.0.1",
)

go_repository(
    name = "com_github_pierrec_lz4_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/pierrec/lz4/v4",
    sum = "h1:xaKrnTkyoqfh1YItXl56+6KJNVYWlEEPuAQW9xsplYQ=",
    version = "v4.1.18",
)

go_repository(
    name = "com_github_xdg_go_pbkdf2",
    build_file_proto_mode = "disable",
    importpath = "github.com/xdg-go/pbkdf2",
    sum = "h1:Su7DPu48wXMwC3bs7MCNG+z4FhcyEuz5dlvchbq0B0c=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_xdg_go_scram",
    build_file_proto_mode = "disable",
    importpath = "github.com/xdg-go/scram",
    sum = "h1:VOMT+81stJgXW3CpHyqHN3AXDYIMsx56mEFrB37Mb/E=",
    version = "v1.1.1",
)

go_repository(
    name = "com_github_xdg_go_stringprep",
    build_file_proto_mode = "disable",
    importpath = "github.com/xdg-go/stringprep",
    sum = "h1:kdwGpVNwPFtjs98xCGkHjQtGKh86rDcRZN17QEMCOIs=",
    version = "v1.0.3",
)

go_repository(
    name = "com_github_youmark_pkcs8",
    build_file_proto_mode = "disable",
    importpath = "github.com/youmark/pkcs8",
    sum = "h1:splanxYIlg+5LfHAM6xpdFEAYOk8iySO56hMFq6uLyA=",
    version = "v0.0.0-20181117223130-1be2e3e5546d",
)

go_repository(
    name = "io_gorm_driver_postgres",
    build_file_proto_mode = "disable",
    importpath = "gorm.io/driver/postgres",
    sum = "h1:PAgM+PaHOSAeroTjHkCHCBIHHoBIf9RgPWGo8dF2DA8=",
    version = "v1.0.8",
)

go_repository(
    name = "io_gorm_gorm",
    build_file_proto_mode = "disable",
    importpath = "gorm.io/gorm",
    sum = "h1:J0xfPJMRfHgpVcYLrEAIqY/apdvTIkrltPQNHQLq9Qc=",
    version = "v1.21.4",
)

http_jar(
    name = "bazel_diff",
    sha256 = "9c4546623a8b9444c06370165ea79a897fcb9881573b18fa5c9ee5c8ba0867e2",
    urls = [
        "https://github.com/Tinder/bazel-diff/releases/download/4.3.0/bazel-diff_deploy.jar",
    ],
)

go_repository(
    name = "com_github_adamkorcz_go_118_fuzz_build",
    build_file_proto_mode = "disable",
    importpath = "github.com/AdamKorcz/go-118-fuzz-build",
    sum = "h1:+vTEFqeoeur6XSq06bs+roX3YiT49gUniJK7Zky7Xjg=",
    version = "v0.0.0-20221215162035-5330a85ea652",
)

go_repository(
    name = "com_github_blang_semver_v4",
    build_file_proto_mode = "disable",
    importpath = "github.com/blang/semver/v4",
    sum = "h1:1PFHFE6yCCTv8C1TeyNNarDzntLi7wMI5i/pzqYIsAM=",
    version = "v4.0.0",
)

go_repository(
    name = "com_github_container_orchestrated_devices_container_device_interface",
    build_file_proto_mode = "disable",
    importpath = "github.com/container-orchestrated-devices/container-device-interface",
    sum = "h1:PqQGqJqQttMP5oJ/qNGEg8JttlHqGY3xDbbcKb5T9E8=",
    version = "v0.5.4",
)

go_repository(
    name = "com_github_containerd_btrfs_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/btrfs/v2",
    sum = "h1:FN4wsx7KQrYoLXN7uLP0vBV4oVWHOIKDRQ1G2Z0oL5M=",
    version = "v2.0.0",
)

go_repository(
    name = "com_github_containerd_cgroups_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/cgroups/v3",
    sum = "h1:f5WFqIVSgo5IZmtTT3qVBo6TzI1ON6sycSBKkymb9L0=",
    version = "v3.0.2",
)

go_repository(
    name = "com_github_containerd_typeurl_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/containerd/typeurl/v2",
    sum = "h1:3Q4Pt7i8nYwy2KmQWIw2+1hTvwTE/6w9FqcttATPO/4=",
    version = "v2.1.1",
)

go_repository(
    name = "com_github_cpuguy83_dockercfg",
    build_file_proto_mode = "disable",
    importpath = "github.com/cpuguy83/dockercfg",
    sum = "h1:/FpZ+JaygUR/lZP2NlFI2DVfrOEMAIKP5wWEJdoYe9E=",
    version = "v0.3.1",
)

go_repository(
    name = "com_github_emicklei_go_restful_v3",
    build_file_proto_mode = "disable",
    importpath = "github.com/emicklei/go-restful/v3",
    sum = "h1:rc42Y5YTp7Am7CS630D7JmhRjq4UlEUuEKfrDac4bSQ=",
    version = "v3.10.1",
)

go_repository(
    name = "com_github_googleapis_enterprise_certificate_proxy",
    build_file_proto_mode = "disable",
    importpath = "github.com/googleapis/enterprise-certificate-proxy",
    sum = "h1:uGy6JWR/uMIILU8wbf+OkstIrNiMjGpEIyhx8f6W7s4=",
    version = "v0.2.4",
)

go_repository(
    name = "com_github_klauspost_cpuid_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/klauspost/cpuid/v2",
    sum = "h1:0E5MSMDEoAulmXNFquVs//DdoomxaoTY1kUhbc/qbZg=",
    version = "v2.2.5",
)

go_repository(
    name = "com_github_minio_sha256_simd",
    build_file_proto_mode = "disable",
    importpath = "github.com/minio/sha256-simd",
    sum = "h1:v1ta+49hkWZyvaKwrQB8elexRqm6Y0aMLjCNsrYxo6g=",
    version = "v1.0.0",
)

go_repository(
    name = "com_github_moby_patternmatcher",
    build_file_proto_mode = "disable",
    importpath = "github.com/moby/patternmatcher",
    sum = "h1:YCZgJOeULcxLw1Q+sVR636pmS7sPEn1Qo2iAN6M7DBo=",
    version = "v0.5.0",
)

go_repository(
    name = "com_github_moby_sys_sequential",
    build_file_proto_mode = "disable",
    importpath = "github.com/moby/sys/sequential",
    sum = "h1:OPvI35Lzn9K04PBbCLW0g4LcFAJgHsvXsRyewg5lXtc=",
    version = "v0.5.0",
)

go_repository(
    name = "com_github_tchap_go_patricia_v2",
    build_file_proto_mode = "disable",
    importpath = "github.com/tchap/go-patricia/v2",
    sum = "h1:6rQp39lgIYZ+MHmdEq4xzuk1t7OdC35z/xm0BGhTkes=",
    version = "v2.3.1",
)

go_repository(
    name = "com_google_cloud_go_accessapproval",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/accessapproval",
    sum = "h1:MOfZ2ncDO+nctNmOYqN8kCb8kHkVJU1nqGNGDa7qGQk=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_accesscontextmanager",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/accesscontextmanager",
    sum = "h1:4vUQZ3OqH3YWo18AgQgvked8NSszsZQfURsYurRHoZI=",
    version = "v1.8.3",
)

go_repository(
    name = "com_google_cloud_go_aiplatform",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/aiplatform",
    sum = "h1:DhbEMbUZTbsIQBhErvo5GlQCymVM50aoP3UDcrnxuOo=",
    version = "v1.51.2",
)

go_repository(
    name = "com_google_cloud_go_analytics",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/analytics",
    sum = "h1:ccW5bYKOrgXAbvIT/mxqk2rou7CfrUdl5G5bJDmDeVA=",
    version = "v0.21.5",
)

go_repository(
    name = "com_google_cloud_go_apigateway",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/apigateway",
    sum = "h1:8zRrVIaGC5dQeq3lBUn7Kchea8ao0bUILXeY8kBzF3M=",
    version = "v1.6.3",
)

go_repository(
    name = "com_google_cloud_go_apigeeconnect",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/apigeeconnect",
    sum = "h1:piXKIidKtP/FQ44dnYXGF+WSDsegdPmj/EKk6Hc3sR4=",
    version = "v1.6.3",
)

go_repository(
    name = "com_google_cloud_go_apigeeregistry",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/apigeeregistry",
    sum = "h1:GuexCawKvjXeeLgBYJoS1m7TCWvbYrmb9KNZjf5EdxA=",
    version = "v0.8.1",
)

go_repository(
    name = "com_google_cloud_go_appengine",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/appengine",
    sum = "h1:7hfqb1kKeB/jAyzOiBB00cQ71GYYLal85VH+uJhE5kc=",
    version = "v1.8.3",
)

go_repository(
    name = "com_google_cloud_go_area120",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/area120",
    sum = "h1:wcVVp8EmTVVXJNU8QHDOZOHC5iJYNoU0jw8daZyvPQg=",
    version = "v0.8.3",
)

go_repository(
    name = "com_google_cloud_go_artifactregistry",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/artifactregistry",
    sum = "h1:rE++Rall/sWmbWkkQEfNoXC7GkCP7hqqd7NeDNEfGqI=",
    version = "v1.14.4",
)

go_repository(
    name = "com_google_cloud_go_assuredworkloads",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/assuredworkloads",
    sum = "h1:1eef9dFe2ylYw/6Nv6vP2t4xEeUHGIZDKRBwFbsiQUs=",
    version = "v1.11.3",
)

go_repository(
    name = "com_google_cloud_go_automl",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/automl",
    sum = "h1:8gL+S9PpsYrqa9v7PDGMWEb42EWLGlxjdjMOB7/wU1c=",
    version = "v1.13.3",
)

go_repository(
    name = "com_google_cloud_go_baremetalsolution",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/baremetalsolution",
    sum = "h1:nS60vlA7w2GTXpQ7a6y5z4jjJF46kshx3G2V99WLsbA=",
    version = "v1.2.2",
)

go_repository(
    name = "com_google_cloud_go_batch",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/batch",
    sum = "h1:wa8ZC1wMJLqnwWsGiVsntBIEYcIP+HUyAcKc0DhoDE4=",
    version = "v1.6.1",
)

go_repository(
    name = "com_google_cloud_go_beyondcorp",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/beyondcorp",
    sum = "h1:/TrG41OifURpA7weWrG9CROcBapc0exDiDaL9ZgplXE=",
    version = "v1.0.2",
)

go_repository(
    name = "com_google_cloud_go_billing",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/billing",
    sum = "h1:PIpLB0uqYRKyzbPZNQV+FxvBcKtZH+Io9eK/lC8bRSE=",
    version = "v1.17.3",
)

go_repository(
    name = "com_google_cloud_go_binaryauthorization",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/binaryauthorization",
    sum = "h1:pSb8eiGofANJbRNTwQDiLJhMjng4tvFEVDEbaf2UWwM=",
    version = "v1.7.2",
)

go_repository(
    name = "com_google_cloud_go_certificatemanager",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/certificatemanager",
    sum = "h1:dOD0bZPTwtFo/h8CwCBpu/m3B4ugm2GFhcym/tuoaJg=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_channel",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/channel",
    sum = "h1:LcaJyv6riQA39bvkPigOJvvNJM7Ls5x0hjZAqcuxq60=",
    version = "v1.17.2",
)

go_repository(
    name = "com_google_cloud_go_cloudbuild",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/cloudbuild",
    sum = "h1:Go7wFI+P5QyuSt1sZtb8wx+M+MGjyfR+xgOzjuZMDF8=",
    version = "v1.14.2",
)

go_repository(
    name = "com_google_cloud_go_clouddms",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/clouddms",
    sum = "h1:qxyV8j7ckzzt5gcX1u5qkteKBDMd9Q29TPs7uZVV6bA=",
    version = "v1.7.2",
)

go_repository(
    name = "com_google_cloud_go_cloudtasks",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/cloudtasks",
    sum = "h1:ajm0DZTTaGKeawbwuFXZfPC7IIeLg6eYJFH125i18tE=",
    version = "v1.12.3",
)

go_repository(
    name = "com_google_cloud_go_compute_metadata",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/compute/metadata",
    sum = "h1:Tz+eQXMEqDIKRsmY3cHTL6FVaynIjX2QxYC4trgAKZc=",
    version = "v0.3.0",
)

go_repository(
    name = "com_google_cloud_go_contactcenterinsights",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/contactcenterinsights",
    sum = "h1:xTLvUYWMwNdQeAr9FECdenht43In0s/KAyFHSuKuZR4=",
    version = "v1.11.2",
)

go_repository(
    name = "com_google_cloud_go_container",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/container",
    sum = "h1:vNQsufqH93Df+U1kNNgjQ6UIQ1v4FUdQ9QHdDiiDuss=",
    version = "v1.26.2",
)

go_repository(
    name = "com_google_cloud_go_containeranalysis",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/containeranalysis",
    sum = "h1:AKyhqlO1Cjdrp7Ht0C3uQALliri2RegVRpEZseOKwWo=",
    version = "v0.11.2",
)

go_repository(
    name = "com_google_cloud_go_datacatalog",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/datacatalog",
    sum = "h1:4ydlNOtwjkdXjXWd+SkUBh+DyVmM/bJKiktAHwqaEeU=",
    version = "v1.18.2",
)

go_repository(
    name = "com_google_cloud_go_dataflow",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/dataflow",
    sum = "h1:v+wjNFEugYBYaowmqJuHK1piqNCx9pGvUWPEhspRFEw=",
    version = "v0.9.3",
)

go_repository(
    name = "com_google_cloud_go_dataform",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/dataform",
    sum = "h1:jhr9PQtaH59nfZbI7tP0jjIl9SlfnRgteAQHD+ZT2xA=",
    version = "v0.8.3",
)

go_repository(
    name = "com_google_cloud_go_datafusion",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/datafusion",
    sum = "h1:v5HtQxvFLVHgbJXUdd4zbNZcYgknRUEW9ukOYpwf6d0=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_datalabeling",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/datalabeling",
    sum = "h1:wYqhVo4f5akRW2yZfGQ56fUCi8FCgYzKh1UvBeg7edE=",
    version = "v0.8.3",
)

go_repository(
    name = "com_google_cloud_go_dataplex",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/dataplex",
    sum = "h1:D+UlBA3Z+k/Z93a1Wh3uZrUbc4aX7c7ifF/m/s5H6fQ=",
    version = "v1.10.2",
)

go_repository(
    name = "com_google_cloud_go_dataqna",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/dataqna",
    sum = "h1:Nr5t9MoTDxlOc7lVsydsIZ31ZdxOznCyKf5qFCiPu4o=",
    version = "v0.8.3",
)

go_repository(
    name = "com_google_cloud_go_datastream",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/datastream",
    sum = "h1:94RSCSWUXpbXvTrISGKZWbJqPTTSvMxErCZiHrEspEM=",
    version = "v1.10.2",
)

go_repository(
    name = "com_google_cloud_go_deploy",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/deploy",
    sum = "h1:LoaXLE32tVW/ULfKP8iX3USJZopWUTjVHiIGc/CVcWI=",
    version = "v1.14.1",
)

go_repository(
    name = "com_google_cloud_go_dialogflow",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/dialogflow",
    sum = "h1:y2I9aHjFrszP2mAQNFCkfwzyli1avPiw/OQt1K3YV8Q=",
    version = "v1.44.2",
)

go_repository(
    name = "com_google_cloud_go_dlp",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/dlp",
    sum = "h1:UWS2DOpyUuK/ig3ZWzJ0IcZRYsRcRkgD1NSaoSU1upw=",
    version = "v1.10.3",
)

go_repository(
    name = "com_google_cloud_go_documentai",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/documentai",
    sum = "h1:AQ9/q6mFBpHmWx7k1fB+S1ut8mv03XUo9sdOuEWXxl8=",
    version = "v1.23.4",
)

go_repository(
    name = "com_google_cloud_go_domains",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/domains",
    sum = "h1:/NaEe9O1vhHt+j/VDPNJt8oDgGthsY+hbjbkBBOWorw=",
    version = "v0.9.3",
)

go_repository(
    name = "com_google_cloud_go_edgecontainer",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/edgecontainer",
    sum = "h1:cpoLa8/f2xPQFfoXRztRMJVVsQ/pxY+fra1QGQjxjyw=",
    version = "v1.1.3",
)

go_repository(
    name = "com_google_cloud_go_errorreporting",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/errorreporting",
    sum = "h1:kj1XEWMu8P0qlLhm3FwcaFsUvXChV/OraZwA70trRR0=",
    version = "v0.3.0",
)

go_repository(
    name = "com_google_cloud_go_essentialcontacts",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/essentialcontacts",
    sum = "h1:kCxxUXP61/36M9KjiwvwSDAjtpjiv2apmIbBRdBHNXg=",
    version = "v1.6.4",
)

go_repository(
    name = "com_google_cloud_go_eventarc",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/eventarc",
    sum = "h1:8LX0xmWk8qj4BZJ5rwQosVW/icozTsMxUimgz2Hlw5s=",
    version = "v1.13.2",
)

go_repository(
    name = "com_google_cloud_go_filestore",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/filestore",
    sum = "h1:Rx1EFBco717fTbeQLhAgEdPStqAzlKywMx37SvTOFXI=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_functions",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/functions",
    sum = "h1:5bAA0oK69rdOzSyJGkEod90R9zpok2CRPi1du0gwFtc=",
    version = "v1.15.3",
)

go_repository(
    name = "com_google_cloud_go_gkebackup",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/gkebackup",
    sum = "h1:B1QCKBePacacXFflG3Sc1MP4zWkeN1ZvfAT0AEAEgIc=",
    version = "v1.3.3",
)

go_repository(
    name = "com_google_cloud_go_gkeconnect",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/gkeconnect",
    sum = "h1:Qex3VdGKPM9cRbEIe7FAho8CAMQOx4u+m1pwRU515jY=",
    version = "v0.8.3",
)

go_repository(
    name = "com_google_cloud_go_gkehub",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/gkehub",
    sum = "h1:b7/eKuEEfeQeRIAz//v6Y3ZjNAYi81Eay/a8kqaYSKI=",
    version = "v0.14.3",
)

go_repository(
    name = "com_google_cloud_go_gkemulticloud",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/gkemulticloud",
    sum = "h1:eBkfrcLHG+uaaVVbKMKS0qA1M1yBU3pdiHn1rZUCv0s=",
    version = "v1.0.2",
)

go_repository(
    name = "com_google_cloud_go_gsuiteaddons",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/gsuiteaddons",
    sum = "h1:fjt+72G2ZavxZ4JROTjrvjV6OJs/fHr9Uo+aOGDcH20=",
    version = "v1.6.3",
)

go_repository(
    name = "com_google_cloud_go_iam",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/iam",
    sum = "h1:K6n/GZHFTtEoKT5aUG3l9diPi0VduZNQ1PfdnpkkIFk=",
    version = "v1.1.4",
)

go_repository(
    name = "com_google_cloud_go_iap",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/iap",
    sum = "h1:vuyDICrMq8z6flZaptQWOxN+XBHtGKWKYlAir8LmTgk=",
    version = "v1.9.2",
)

go_repository(
    name = "com_google_cloud_go_ids",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/ids",
    sum = "h1:ZgT7bJIu+7toDJNjX4Uiao/9+82T6ZDGySoMxbvdZcY=",
    version = "v1.4.3",
)

go_repository(
    name = "com_google_cloud_go_iot",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/iot",
    sum = "h1:Cbn4wsDXgIwqAAOYIyto4e6aXJDhlZiqf2wO+9h8pEg=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_kms",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/kms",
    sum = "h1:gEZzC54ZBI+aeW8/jg9tgz9KR4Aa+WEDPbdGIV3iJ7A=",
    version = "v1.15.4",
)

go_repository(
    name = "com_google_cloud_go_language",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/language",
    sum = "h1:7TnnZKy3sspQ3F1mzwEv3eaptTwGrReVXEo8Oozi10w=",
    version = "v1.12.1",
)

go_repository(
    name = "com_google_cloud_go_lifesciences",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/lifesciences",
    sum = "h1:QcPrlyo/F7IXJXk8CkF4yGEwX4CTLAKUE/My4l3KF/8=",
    version = "v0.9.3",
)

go_repository(
    name = "com_google_cloud_go_logging",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/logging",
    sum = "h1:26skQWPeYhvIasWKm48+Eq7oUqdcdbwsCVwz5Ys0FvU=",
    version = "v1.8.1",
)

go_repository(
    name = "com_google_cloud_go_longrunning",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/longrunning",
    sum = "h1:maKa7O9YTzmVzwdlRKr981U1Ys2auup6rpeMt8y3+RU=",
    version = "v0.5.3",
)

go_repository(
    name = "com_google_cloud_go_managedidentities",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/managedidentities",
    sum = "h1:hfWT2pidjP19mYJxTWMbfj8/wj9OZNUtB/oZ3HW0K8g=",
    version = "v1.6.3",
)

go_repository(
    name = "com_google_cloud_go_maps",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/maps",
    sum = "h1:j5AJNj9mbPTwqrqFmAHzMY3UX+Eddj/jC9xu7qFMVhk=",
    version = "v1.5.1",
)

go_repository(
    name = "com_google_cloud_go_mediatranslation",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/mediatranslation",
    sum = "h1:3B75VHLlp4M+W10NHEOAl30q7ZSd0D/i8LqRQqxqMM0=",
    version = "v0.8.3",
)

go_repository(
    name = "com_google_cloud_go_memcache",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/memcache",
    sum = "h1:yD1ZDTXP7YfT5lBLRNioqZ427iaHaoqW5aMKXtM0acQ=",
    version = "v1.10.3",
)

go_repository(
    name = "com_google_cloud_go_metastore",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/metastore",
    sum = "h1:ATz8sWfuHmlqAkPpWGRXQzZFUsgbDkAhZOyNlce83vA=",
    version = "v1.13.2",
)

go_repository(
    name = "com_google_cloud_go_monitoring",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/monitoring",
    sum = "h1:gx7BDZcoRqX5DfuJzw9LdhVjEkqCLmDXScdnrmIy9ik=",
    version = "v1.16.2",
)

go_repository(
    name = "com_google_cloud_go_networkconnectivity",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/networkconnectivity",
    sum = "h1:tbzauwO86MlZ7+7OmxA0+PnEEgK2AXo9lGrUIL0++qc=",
    version = "v1.14.2",
)

go_repository(
    name = "com_google_cloud_go_networkmanagement",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/networkmanagement",
    sum = "h1:v5RWynWBSX91pDnGp/7+5ME0/wLsjIzrC6vlICE/YPs=",
    version = "v1.9.2",
)

go_repository(
    name = "com_google_cloud_go_networksecurity",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/networksecurity",
    sum = "h1:t1QESL7xdq/fIoPHrduMWsMDYnM092UUuuiDvnXgrsk=",
    version = "v0.9.3",
)

go_repository(
    name = "com_google_cloud_go_notebooks",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/notebooks",
    sum = "h1:fW/P76U437Eyzl1qEwqQSPeToiMB1LM1QyRGR0FpsG4=",
    version = "v1.11.1",
)

go_repository(
    name = "com_google_cloud_go_optimization",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/optimization",
    sum = "h1:sY8QjN37zKQfXCZaUSfGvpVGqyFXO2/vNvrhXMj28eI=",
    version = "v1.6.1",
)

go_repository(
    name = "com_google_cloud_go_orchestration",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/orchestration",
    sum = "h1:+oaZtNXbCE9QBV/hCUo8Kf5VFILMfg2KR1n6oh8LB/o=",
    version = "v1.8.3",
)

go_repository(
    name = "com_google_cloud_go_orgpolicy",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/orgpolicy",
    sum = "h1:oqXeClkeznFGEd/rcu4T0WYOTh5ec6OkkXskaShD4hM=",
    version = "v1.11.3",
)

go_repository(
    name = "com_google_cloud_go_osconfig",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/osconfig",
    sum = "h1:PumxpZU71EpygOx0zkrx2HxlMvpyUmENB4e5nQc3+KM=",
    version = "v1.12.3",
)

go_repository(
    name = "com_google_cloud_go_oslogin",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/oslogin",
    sum = "h1:kAQFiAT/+jefVnYYNT4+PxdHrZTFSgeJoE6ykhaGlNo=",
    version = "v1.12.1",
)

go_repository(
    name = "com_google_cloud_go_phishingprotection",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/phishingprotection",
    sum = "h1:ZVYpZWmg8leUAp4y5XzhxropUfkcAmwOwsmr4i1D1Mc=",
    version = "v0.8.3",
)

go_repository(
    name = "com_google_cloud_go_policytroubleshooter",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/policytroubleshooter",
    sum = "h1:TboGw1ua94FjA/Zgj9Gl4Ksgs/TyasQmAkW9w+vyje4=",
    version = "v1.10.1",
)

go_repository(
    name = "com_google_cloud_go_privatecatalog",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/privatecatalog",
    sum = "h1:xSUuUCfA5fo59N1hsQmvzgNispoORTUvqN27XXyWFWg=",
    version = "v0.9.3",
)

go_repository(
    name = "com_google_cloud_go_pubsublite",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/pubsublite",
    sum = "h1:pX+idpWMIH30/K7c0epN6V703xpIcMXWRjKJsz0tYGY=",
    version = "v1.8.1",
)

go_repository(
    name = "com_google_cloud_go_recaptchaenterprise_v2",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/recaptchaenterprise/v2",
    sum = "h1:vbPKIAPiFxHG7uNXZmuivxbox17up/Tav5Y+Z+KA008=",
    version = "v2.8.2",
)

go_repository(
    name = "com_google_cloud_go_recommendationengine",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/recommendationengine",
    sum = "h1:A58sxjTGDJC+a2zkCzbYdLtPG1OQ7WijwCv7ZsTKIP8=",
    version = "v0.8.3",
)

go_repository(
    name = "com_google_cloud_go_recommender",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/recommender",
    sum = "h1:/uQYJ01mKw0cig+DeMTQ+JuG0BEwVfxVh2gGIG93UEw=",
    version = "v1.11.2",
)

go_repository(
    name = "com_google_cloud_go_redis",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/redis",
    sum = "h1:+SH8u5Mrgo8EQG/0/C9fwBnzjYS4CQIm+ZK0nlFtg5I=",
    version = "v1.13.3",
)

go_repository(
    name = "com_google_cloud_go_resourcemanager",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/resourcemanager",
    sum = "h1:jftfMUTNJLM9V+4wvMQfKfJfQVWcPJifWsMQnJyL3Jk=",
    version = "v1.9.3",
)

go_repository(
    name = "com_google_cloud_go_resourcesettings",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/resourcesettings",
    sum = "h1:T3HzhoHhk0VdEdXm5p+KKoC7jh9C4Xpe3BnNf64UfKU=",
    version = "v1.6.3",
)

go_repository(
    name = "com_google_cloud_go_retail",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/retail",
    sum = "h1:pFW+3HJI5qHdE5yprmciAsM6G6cp0h0RH8FELX8K7sw=",
    version = "v1.14.3",
)

go_repository(
    name = "com_google_cloud_go_run",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/run",
    sum = "h1:AiA5O0YTCTkLQaJu+HFAS0W/jktmiNydOUgg/d0xQrs=",
    version = "v1.3.2",
)

go_repository(
    name = "com_google_cloud_go_scheduler",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/scheduler",
    sum = "h1:1UwFQBqNlwnfpjZbPYitdV/GURiVRg4gbhRnKtL5ZRg=",
    version = "v1.10.3",
)

go_repository(
    name = "com_google_cloud_go_secretmanager",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/secretmanager",
    sum = "h1:Xk7NFAUuW9q+eX4AxwS/dDuhlNWQ1MmfxQpsfbgRsEg=",
    version = "v1.11.3",
)

go_repository(
    name = "com_google_cloud_go_securitycenter",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/securitycenter",
    sum = "h1:oJRemSVC122SKmPV1aEkMOTnFHsnHwsQtXBBV55AxoU=",
    version = "v1.24.1",
)

go_repository(
    name = "com_google_cloud_go_servicedirectory",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/servicedirectory",
    sum = "h1:vdo3qTQWhCB408ckNYqS29i8btBSiVDSWDYSLKc0iF8=",
    version = "v1.11.2",
)

go_repository(
    name = "com_google_cloud_go_shell",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/shell",
    sum = "h1:qujhSLWp8XUsFMp+RgGn2w+BBrQ7AnymGd+6TRaJ4HM=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_speech",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/speech",
    sum = "h1:BvK9bjSW3Ut1hNvYXn2nlu/oGE+MUyjfIEZcI3kgbM8=",
    version = "v1.19.2",
)

go_repository(
    name = "com_google_cloud_go_storagetransfer",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/storagetransfer",
    sum = "h1:rlcXNHgZY/1OIsZHAVDUu2roTs1/3UiVUdUSGnjw4aY=",
    version = "v1.10.2",
)

go_repository(
    name = "com_google_cloud_go_talent",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/talent",
    sum = "h1:yLBx78BoR5/sOCE0O6XqzaqL4KLM0+2BEdQBua9Zy1c=",
    version = "v1.6.4",
)

go_repository(
    name = "com_google_cloud_go_texttospeech",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/texttospeech",
    sum = "h1:EgMsIlyacLJPrqFZZrXK1mUztyM4cp8wR4Ia1H51a9s=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_tpu",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/tpu",
    sum = "h1:euDiTIKKnQ3nG1J0KCafMJffxRdPNWQsnRvGJ7lZWSk=",
    version = "v1.6.3",
)

go_repository(
    name = "com_google_cloud_go_trace",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/trace",
    sum = "h1:yiQ2EdegdiZDPwNPoUqGYAMd0wNValT/NRsC1XGyBJ0=",
    version = "v1.10.3",
)

go_repository(
    name = "com_google_cloud_go_translate",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/translate",
    sum = "h1:liPk0EElBpafg4VAlZQwbg786X8/nnboOC4TmAN5sF8=",
    version = "v1.9.2",
)

go_repository(
    name = "com_google_cloud_go_video",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/video",
    sum = "h1:paeXmflm8qH81wzND2V6BrKkS3GuvLuSrktHpdh+fr0=",
    version = "v1.20.2",
)

go_repository(
    name = "com_google_cloud_go_videointelligence",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/videointelligence",
    sum = "h1:PS8HSjrQh6A1XTKfGz83vLfXhHyGGEiOTgmh2WksdIM=",
    version = "v1.11.3",
)

go_repository(
    name = "com_google_cloud_go_vision_v2",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/vision/v2",
    sum = "h1:lTPDW1JogEcR7NSobxlpGAIN1wc2kvUrEfsS/32nm90=",
    version = "v2.7.4",
)

go_repository(
    name = "com_google_cloud_go_vmmigration",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/vmmigration",
    sum = "h1:2RIVjbHdVPXw8dhFLAkbU+DGnR/Egiv+zhb0CmSF58U=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_vmwareengine",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/vmwareengine",
    sum = "h1:JU4HKWkJSfYP9eU3q5VVnmyTjiZSqFC6ZXN752ebZuQ=",
    version = "v1.0.2",
)

go_repository(
    name = "com_google_cloud_go_vpcaccess",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/vpcaccess",
    sum = "h1:d/vObL8itQ5DmG+fh3wExKCl4zuwbInSQOuysfvgShk=",
    version = "v1.7.3",
)

go_repository(
    name = "com_google_cloud_go_webrisk",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/webrisk",
    sum = "h1:9yteSCrAhlZFvu+zb/SlpCKFwij69Il1aIuhk8vq0sg=",
    version = "v1.9.3",
)

go_repository(
    name = "com_google_cloud_go_websecurityscanner",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/websecurityscanner",
    sum = "h1:nIo6koOYifrmNUsuXYzlO39oRrV8aM7NdjKjXxL/xvE=",
    version = "v1.6.3",
)

go_repository(
    name = "com_google_cloud_go_workflows",
    build_file_proto_mode = "disable",
    importpath = "cloud.google.com/go/workflows",
    sum = "h1:dDTsUCgUQbkEFviNMgRV0THXa9VWBNS+35Ru1uadEUw=",
    version = "v1.12.2",
)

go_repository(
    name = "io_k8s_sigs_json",
    build_file_proto_mode = "disable",
    importpath = "sigs.k8s.io/json",
    sum = "h1:iXTIw73aPyC+oRdyqqvVJuloN1p0AC/kzH07hu3NE+k=",
    version = "v0.0.0-20220713155537-f223a00ba0e2",
)

go_repository(
    name = "io_opentelemetry_go_otel_exporters_otlp_otlptrace_otlptracehttp",
    build_file_proto_mode = "disable",
    importpath = "go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp",
    sum = "h1:3jAYbRHQAqzLjd9I4tzxwJ8Pk/N6AqBcF6m1ZHrxG94=",
    version = "v1.14.0",
)
