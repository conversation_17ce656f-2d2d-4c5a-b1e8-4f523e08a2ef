load("@bazel_gazelle//:def.bzl", "DEFAULT_LANGUAGES", "gazelle", "gazelle_binary")
load("@rules_java//java:defs.bzl", "java_binary")

gazelle_binary(
    name = "gazelle_binary",
    languages = DEFAULT_LANGUAGES + ["@golink//gazelle/go_link:go_default_library"],
    visibility = ["//visibility:public"],
)

# gazelle:prefix nirvanatech.com
# gazelle:exclude nirvana/client
# gazelle:exclude ds
# gazelle:exclude nirvana/**/*.pb.go
# gazelle:exclude deployment
# gazelle:exclude infra

gazelle(
    name = "gazelle",
    gazelle = "//:gazelle_binary",
)

java_binary(
    name = "bazel-diff",
    main_class = "com.bazel_diff.Main",
    runtime_deps = ["@bazel_diff//jar"],
)
