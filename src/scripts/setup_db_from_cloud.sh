#!/bin/bash
# This scripts sets up local postgres db using postgres dump stored in
# S3. See Usage details in initial_local_db_setup.sh

set -x
set -e

export POSTGRES_HOST=${POSTGRES_HOST:-'localhost'}
export POSTGRES_PORT=${POSTGRES_PORT:-'5432'}

AWS_BUCKET="s3://nirvana-test-db-dump"
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

NIRVANA_DB_FILENAME=$(cat $SCRIPT_DIR/data/nirvana_db_dump_name)
FMCSA_DB_FILENAME=$(cat $SCRIPT_DIR/data/fmcsa_db_dump_name)

aws s3 cp $AWS_BUCKET/$NIRVANA_DB_FILENAME $SCRIPT_DIR/data/$NIRVANA_DB_FILENAME
aws s3 cp $AWS_BUCKET/$FMCSA_DB_FILENAME $SCRIPT_DIR/data/$FMCSA_DB_FILENAME

export PGPASSWORD=${PGPASSWORD:-'dontuse'}
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U postgres -d postgres -c 'create role thedude;' || true
# password is lebowski
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U postgres -d postgres -c "ALTER ROLE thedude WITH SUPERUSER INHERIT NOCREATEROLE CREATEDB LOGIN NOREPLICATION NOBYPASSRLS PASSWORD 'lebowski';"

psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U postgres -d postgres -c 'drop database if exists nirvana;'
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U postgres -d postgres -c 'drop database if exists fmcsa;'

export PGPASSWORD=lebowski

psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U thedude -d postgres -c 'create database nirvana;'
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U thedude -d postgres -c 'create database fmcsa;'

psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U thedude nirvana < $SCRIPT_DIR/data/$NIRVANA_DB_FILENAME
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U thedude fmcsa < $SCRIPT_DIR/data/$FMCSA_DB_FILENAME
