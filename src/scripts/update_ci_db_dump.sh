#!/bin/bash
# This script updates the S3 dump which is used for CI.
# The databases picked up are `nirvana` and `fmcsa_3`, and they should contain
# very minimal data. If you don't want to use up your main setup then pass
# POSTGRES_HOST and POSTGRES_PORT for some other instance from which
# dump can be created.

set -x
set -e

export POSTGRES_HOST=${POSTGRES_HOST:-'localhost'}
export POSTGRES_PORT=${POSTGRES_PORT:-'5432'}

AWS_BUCKET="s3://nirvana-test-db-dump"
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

NIRVANA_DB_FILENAME=$(date +%s)_$(git rev-parse --short HEAD)_nirvana.sql
FMCSA_DB_FILENAME=$(date +%s)_$(git rev-parse --short HEAD)_fmcsa.sql

export PGPASSWORD=lebowski
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U thedude -d nirvana -c "truncate auth.session;"
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U thedude -d nirvana -c 'truncate policy,application_review,indication_option,application,submission;'

pg_dump -h $POSTGRES_HOST -p $POSTGRES_PORT -U thedude nirvana -f $SCRIPT_DIR/data/$NIRVANA_DB_FILENAME
pg_dump -h $POSTGRES_HOST -p $POSTGRES_PORT -U thedude fmcsa -f $SCRIPT_DIR/data/$FMCSA_DB_FILENAME

aws s3 cp $SCRIPT_DIR/data/$NIRVANA_DB_FILENAME $AWS_BUCKET/$NIRVANA_DB_FILENAME
aws s3 cp $SCRIPT_DIR/data/$FMCSA_DB_FILENAME $AWS_BUCKET/$FMCSA_DB_FILENAME

echo $NIRVANA_DB_FILENAME > $SCRIPT_DIR/data/nirvana_db_dump_name
echo $FMCSA_DB_FILENAME > $SCRIPT_DIR/data/fmcsa_db_dump_name
