#!/bin/bash

set -euo pipefail

script_dir=$(cd -P -- "$(dirname -- "$0")" && pwd -P)
cd "$script_dir"

log_and_exit() {
    echo "$1"
    exit 1
}

[[ -z $NIRVANA_EMAIL ]] && log_and_exit 'NIRVANA_EMAIL is not set'

if [[ $# -eq 0 ]]; then
  bazel run //nirvana/cmd/terraform \
      --ui_event_filters=-info,-stdout,-stderr --noshow_progress \
      -- unlock "user:$NIRVANA_EMAIL"
else
  bazel run //nirvana/cmd/terraform \
      --ui_event_filters=-info,-stdout,-stderr --noshow_progress \
      -- unlock "$1"
fi

echo "Released lock successfully"
