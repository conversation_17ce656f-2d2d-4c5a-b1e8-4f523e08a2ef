#!/bin/bash

set -euo pipefail

log_and_exit() {
    echo "$1"
    exit 1
}

source $HOME/.nirvana_tf_variables
export TF_BINARY

SCRIPT_DIR=$(cd -P -- "$(dirname -- "$0")" && pwd -P)
pushd "$SCRIPT_DIR"

# Test that we are exactly at origin/main
test "$( git status --porcelain | wc -l )" -eq 0 || log_and_exit "You have uncommited changes"
git fetch origin main
git merge-base --is-ancestor origin/main HEAD || log_and_exit "HEAD is behind origin/main"
git merge-base --is-ancestor HEAD origin/main || log_and_exit "HEAD is ahead of origin/main"

# Locking is idempotent so it's okay to always take lock
"$SCRIPT_DIR"/nirvana_tf_lock
trap "$SCRIPT_DIR"/nirvana_tf_unlock EXIT

TF_WRAPPER_SCRIPT="$SCRIPT_DIR"/../DONOT_RUN_MANUALLY_terraform
popd && $TF_WRAPPER_SCRIPT "$@"
