import requests
import time
import os
from getpass import getpass
import json
from datetime import datetime
import argparse
import requests
import json

def load_file_json(file):
    with open(file, "r") as f:
        file_content = json.load(f)
        return file_content
    

def get_fresh_token():
    # set endpoint for login API
    baseUrl = "https://api.prod.nirvanatech.com"

    url = baseUrl + "/auth/login"
    # Request creds for nirvana to get token
    print("What is your login email to Nirvana Api?")
    email = input(" ")
    print("What is your password to Nirvana API?")
    password = getpass("")

    payload = json.dumps({"email": email, "password": password})
    headers = {
      "Content-Type": "application/json", 
      "Accept": "application/json"
    }
    if password == "" or email == "":
        exit()

    url = baseUrl + "/auth/login"
    response = requests.request("POST", url, headers=headers, data=payload)
    passwordIncorrect = "failed to run tx to login user"
    json_response = response.json()
    if passwordIncorrect in json_response:
        print("Incorrect Login Credentials, retry. ")
        return None
    else:
        print("Login successful")

    print("Entire Json Response")
    print(json_response)
    with open(os.getcwd() + "/apiKey.json", "w") as outfile:
        outfile.write(str(response.text))


def nirvana_api_needed():
    baseUrl = "https://api.prod.nirvanatech.com"

    # Pull Token and Date validity from local file
    api_key_file = os.getcwd() + "/apiKey.json"
    os.system("touch {}".format(api_key_file))

    try:
        data = load_file_json(api_key_file)
        print(data)
    except Exception as e:
        print("Incorrect data in {} or empty\n".format(api_key_file), e)
        get_fresh_token()
        data = load_file_json(api_key_file)

    if "failed to run tx to login user" not in json.dumps(data):
        TokenValidTo = (data["expiration"]).split(":")
        TokenValidTo = datetime.strptime(TokenValidTo[0], "%Y-%m-%dT%H")
        if TokenValidTo > datetime.utcnow():
            print("Token still valid")
        else:
            get_fresh_token()
    else:
        get_fresh_token()
    return data

# Grabbing fresh session ID

data = nirvana_api_needed()
sessionId = data["sessionId"]


def generate_policy_docs(appId, coverages):
  #base url stays the same while forms being generated passed through payload
  print("base url stays the same while forms being generated passed through payload\n")
  url = "https://api.prod.nirvanatech.com/forms/compilation"
  print(url)
  FormTypeSignaturePacket_payload = json.dumps({"applicationId":appId,"formType":"FormTypeSignaturePacket","applicationType":"ApplicationTypeFleet"})
  headers = {
    'Content-Type': 'application/json',
    'jsessionid': sessionId
  }
  response = requests.request("POST", url, headers=headers, data=FormTypeSignaturePacket_payload)
  responseJson=response.json()
  print(response)
  print(responseJson)
  sig_packet_form_compilation_id=responseJson['formCompilationId']
  print({"sig_packet_form_compilation_id":sig_packet_form_compilation_id})

  # Make sure CoverageMotorTruckCargo is placed last in coverages list if present

  print("\n\n\n coverages list before manipulation: ", coverages)

  if 'CoverageMotorTruckCargo' in coverages:
    coverages.remove('CoverageMotorTruckCargo')
    coverages.append('CoverageMotorTruckCargo')

  print("coverages list after manipulation: ", coverages, "\n\n\n ")



  for coverage_type in coverages:
    if coverage_type == 'CoverageAutoPhysicalDamage':
      continue
    # form compilation
    print(f"{coverage_type} form:\n")
    print("compilation\n")
    policy_compilation_payload= json.dumps({"applicationId":appId,"coverage":coverage_type,"formType":"FormTypePolicy","applicationType":"ApplicationTypeFleet"})
    print({"policy_compilation_payload":policy_compilation_payload})
    response = requests.request("POST", url, headers=headers, data=policy_compilation_payload)
    responseJson=response.json()
    print(responseJson)
    coverage_compilation_id=responseJson['formCompilationId']
    print({"coverage_compilation_id":coverage_compilation_id})

    #confirmation
    print("confirmation\n")
    confirm_url="https://api.prod.nirvanatech.com/forms/compilations/confirm_forms"
    compilation_confirmation_payload= json.dumps({"signaturePacket":sig_packet_form_compilation_id,"coveragePolicy":coverage_compilation_id})
    print({"compilation_confirmation_payload":compilation_confirmation_payload})

    response = requests.request("PUT", confirm_url, headers=headers, data=compilation_confirmation_payload)
    responseJson=response.json()
    print(response)
    print(responseJson)
    print(response.json())
    
  # fill compilation 

  fill_url = f"https://api.prod.nirvanatech.com/forms/compilation/{sig_packet_form_compilation_id}/fill"
  print(url)
  fill_payload =json.dumps(appId)
  response = requests.request("POST", fill_url, headers=headers, data=fill_payload)
  responseJson=response.json()
  print(response)
  print(responseJson)

  # packet freeze 

  freeze_url = f"https://api.prod.nirvanatech.com/forms/compilation/{sig_packet_form_compilation_id}/freeze"
  print(freeze_url)
  fill_payload =""
  response = requests.request("POST", freeze_url, headers=headers, data=fill_payload)
  responseJson=response.json()
  print(response)
  print(responseJson)

  #packet release
  release_url = f"https://api.prod.nirvanatech.com/forms/compilation/release/{sig_packet_form_compilation_id}"
  print(release_url)
  fill_payload =""
  response = requests.request("POST", release_url, headers=headers, data=fill_payload)
  responseJson=response.json()
  print(response)
  print(responseJson)
  #bind signature packet
  release_url = f"https://api.prod.nirvanatech.com/forms/compilation/bind/{sig_packet_form_compilation_id}"
  print(release_url)
  fill_payload =""
  response = requests.request("POST", release_url, headers=headers, data=fill_payload)
  responseJson=response.json()
  print(response)
  print(responseJson)



def approve_application_reviews(today_company_name):
    app_review_count=0
    app_review_id=""
    while app_review_count==0:
        
      # Get app review id based on name
      url = f"https://api.prod.nirvanatech.com/underwriting/application_review/list?size=25&q={today_company_name}&effectiveDateBefore=2025-04-30&effectiveDateAfter=2023-01-01&tab=ApplicationReviewTabInternal"

      payload = {}
      headers = {
        'Accept': 'application/json',
        'jsessionid': sessionId
      }

      response = requests.request("GET", url, headers=headers, data=payload).json()
      
      if len(response['appReviews'])<1:
          time.sleep(3)
      elif len(response['appReviews'])>1:
          app_review_count=len(response['appReviews'])
          print(f"---\napp name is not unique, cannot choose from the {app_review_count} apps with {today_company_name}\n---")
          exit()
      else:
          app_review_count=len(response['appReviews'])
    app_review_id=response['appReviews'][0]['appReviewID']

    headers = {
      'Content-Type': 'application/json',
      'jsessionid': sessionId
    }

    # Mark each tab as reviewed in UW app
    for tab in ['operations','equipments','drivers','safety','financials','losses','packages']:
      url = f"https://api.prod.nirvanatech.com/underwriting/application_reviews/{app_review_id}"
      print(url)
      payload = json.dumps({
        tab: {
          "isReviewed": True
        }
      })
      print(payload)
      response = requests.request("PATCH", url, headers=headers, data=payload)
      print(response)
      print(response.json)


    # Create bindable submission / approve app 
    url = f"https://api.prod.nirvanatech.com/underwriting/application_reviews/{app_review_id}/bindable-quote"
    print(url)
    payload = ""
    headers = {
      'Content-Type': 'application/json',
      'jsessionid': sessionId
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    print(response)
    print(response.json)

    # check for bindable submission run completion
    while True:
      url = f"https://api.prod.nirvanatech.com/underwriting/application_reviews/{app_review_id}/bindable-quote"
      print(url)
      payload = ""
      headers = {
        'Content-Type': 'application/json',
        'jsessionid': sessionId
      }

      response = requests.request("GET", url, headers=headers, data=payload)
      jsonResponse=response.json()
      print(response)
      print(jsonResponse)
      if jsonResponse['status']!='running':
          print("bindable submission ready!")
          break
      else:
          time.sleep(10)

    #confirm bindable submission / approval
    url = f"https://api.prod.nirvanatech.com/underwriting/application_reviews/{app_review_id}/bindable-quote/confirm"
    print(url)
    payload = ""
    headers = {
      'Content-Type': 'application/json',
      'jsessionid': sessionId
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    jsonResponse=response.json
    print(response)
    print(jsonResponse)

def get_app_id_by_name(name):
  url = f"https://api.prod.nirvanatech.com/application/list?size=25&q={name}&tab=ApplicationTabAll"

  payload = {}
  headers = {
    'Accept': 'application/json',
    'jsessionid': 'e22d0329-81cc-4d12-a850-bf1f90f89880'
  }

  response = requests.request("GET", url, headers=headers, data=payload)
  responseJson=response.json()
  if len(responseJson['apps'])>1:
      print("length of app list is greater than 1, cannot proceed")
  appId=responseJson['apps'][0]['applicationID']
  return appId

def fetch_app_info(id):
  duplicatedAppInfo = {}

  # TODO: use different endpoint for duplicated apps that haven't been submitted, and use in the exception cases
  url = f"https://api.prod.nirvanatech.com/application/{id}/quote_data"
  payload = {}
  headers = {
    'Accept': 'application/json, text/plain, */*',
    'jsessionid': sessionId
  }

  response = {}
  try:
    response = requests.request("GET", url, headers=headers, data=payload).json()
  except:
    print("Looks like the app you are trying to duplicate might have not been submitted - Check the ID & application")
    exit()

  # TODO: verificaiton of response for duplicated app ID - if trying to duplicate up to a point where duplicated hasn't reached, ask for a previous breakpoint
  # TODO: setting of handle ID to duplicated app's

  duplicatedAppInfo["companyName"] = response["userAppDetails"]["summary"]["companyName"]
  duplicatedAppInfo["dotNumber"] = response["userAppDetails"]["summary"]["dotNumber"]
  duplicatedAppInfo["effectiveDate"] = response["userAppDetails"]["summary"]["effectiveDate"]
  duplicatedAppInfo["packageType"] = response["userAppDetails"]["summary"]["packageType"]
  duplicatedAppInfo["driversList"] = response["finalAppDetails"]["additionalInfoForm"]["driverList"]
  duplicatedAppInfo["commodities"] = response["finalAppDetails"]["additionalInfoForm"]["commodities"]
  duplicatedAppInfo["lossRunFiles"] = response["finalAppDetails"]["additionalInfoForm"]["lossRunFiles"]
  duplicatedAppInfo["largeLossComment"] = response["finalAppDetails"]["additionalInfoForm"]["largeLossComment"]
  duplicatedAppInfo["coverageMetadata"] = response["finalAppDetails"]["additionalInfoForm"]["coverageMetadata"]
  duplicatedAppInfo["targetPrices"] = response["finalAppDetails"]["additionalInfoForm"]["targetPrices"]
  duplicatedAppInfo["overallComment"] = response["finalAppDetails"]["additionalInfoForm"]["overallComment"]
  duplicatedAppInfo["numOwnerOperatorUnits"] = response["finalAppDetails"]["additionalInfoForm"]["numOwnerOperatorUnits"]
  duplicatedAppInfo["classesAndCommoditiesForm"] = response["userAppDetails"]["indicationForm"]["classesAndCommoditiesForm"]
  duplicatedAppInfo["lossRunSummaryForm"] = response["userAppDetails"]["indicationForm"]["lossRunSummaryForm"]
  duplicatedAppInfo["operationsForm"] = response["userAppDetails"]["indicationForm"]["operationsForm"]

  duplicatedAppInfo["coverages"] = []
  coverageLosses = response["userAppDetails"]["indicationForm"]["lossRunSummaryForm"]
  for coverageLoss in coverageLosses:
    duplicatedAppInfo["coverages"].append(coverageLoss["coverageType"])
  return duplicatedAppInfo

def create_app_id(today_company_name, dot_number, effective_date, duplicated_app_id, duplicatedAppInfo):
  url = "https://api.prod.nirvanatech.com/application"
  AppCreationPayload = json.dumps({
    "companyName": today_company_name,
    "dotNumber": int(dot_number),
    "effectiveDate": effective_date,
    "hasUndesiredOperations": False
    # For non-fleet apps numberOfPowerUnits 2
  })

  # If duplicating application, overwrite AppCreationPayload
  if duplicated_app_id:
    AppCreationPayload = json.dumps({
      "companyName": today_company_name,
      "dotNumber": duplicatedAppInfo["dotNumber"],
      "effectiveDate": duplicatedAppInfo["effectiveDate"],
      "hasUndesiredOperations": False
      # For non-fleet apps numberOfPowerUnits 2
  })

  print("AppCreationPayload: ", AppCreationPayload)
  headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'jsessionid': sessionId
  }

  response = requests.request("POST", url, headers=headers, data=AppCreationPayload).json()
  print("app creation response", response)

  return response["applicationID"]

def submit_info_for_indication(id, duplicated_app_id, duplicatedAppInfo):
  url = f"https://api.prod.nirvanatech.com/application/{id}/indication"

  reqInfo = json.dumps({
    "classesAndCommoditiesForm":{"commodityDistribution":{"additionalCommodities":{"commodities":"","percentageOfHauls":0},"commodities":[{"avgDollarValueHauled":50000,"category":{"label":"Foods — baked, dry, packaged and canned","type":"CommodityCategoryFoodsBakedDryPackagedCanned"},"commodity":{"label":"baked goods"},"maxDollarValueHauled":75000,"percentageOfHauls":100}]},"operatingClassDistribution":[{"operatingClass":"OperatingClassDryVan","percentageOfFleet":100},{"operatingClass":"OperatingClassRefrigerated","percentageOfFleet":0},{"operatingClass":"OperatingClassFlatbed","percentageOfFleet":0},{"operatingClass":"OperatingClassIntermodal","percentageOfFleet":0},{"operatingClass":"OperatingClassTanker","percentageOfFleet":0},{"operatingClass":"OperatingClassHazmat","percentageOfFleet":0},{"operatingClass":"OperatingClassHeavyHaul","percentageOfFleet":0},{"operatingClass":"OperatingClassDump","percentageOfFleet":0}],"primaryCategory":{"label":"Foods — baked, dry, packaged and canned","type":"CommodityCategoryFoodsBakedDryPackagedCanned"},"primaryOperatingClass":"OperatingClassDryVan"},
    "lossRunSummaryForm":[{"lossRunSummary":[{"numberOfPowerUnits":10,"policyPeriodStartDate":"2022-05-11","policyPeriodEndDate":"2023-05-11","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2021-05-11","policyPeriodEndDate":"2022-05-11","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2020-05-11","policyPeriodEndDate":"2021-05-11","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2019-05-11","policyPeriodEndDate":"2020-05-11","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2018-05-11","policyPeriodEndDate":"2019-05-11","lossIncurred":0,"numberOfClaims":0}],"coverageType":"CoverageAutoLiability"},{"lossRunSummary":[{"numberOfPowerUnits":10,"policyPeriodStartDate":"2022-05-11","policyPeriodEndDate":"2023-05-11","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2021-05-11","policyPeriodEndDate":"2022-05-11","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2020-05-11","policyPeriodEndDate":"2021-05-11","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2019-05-11","policyPeriodEndDate":"2020-05-11","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2018-05-11","policyPeriodEndDate":"2019-05-11","lossIncurred":0,"numberOfClaims":0}],"coverageType":"CoverageAutoPhysicalDamage"},{"lossRunSummary":[{"numberOfPowerUnits":10,"policyPeriodStartDate":"2022-08-01","policyPeriodEndDate":"2023-08-01","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2021-08-01","policyPeriodEndDate":"2022-08-01","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2020-08-01","policyPeriodEndDate":"2021-08-01","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2019-08-01","policyPeriodEndDate":"2020-08-01","lossIncurred":0,"numberOfClaims":0},{"numberOfPowerUnits":10,"policyPeriodStartDate":"2018-08-01","policyPeriodEndDate":"2019-08-01","lossIncurred":0,"numberOfClaims":0}],"coverageType":"CoverageMotorTruckCargo"}],
    "operationsForm":{"coveragesRequired":[{"coverageType":"CoverageAutoLiability","deductible":0,"limit":1000000},{"coverageType":"CoverageAutoPhysicalDamage","deductible":2500,"limit":1000000},{"coverageType":"CoverageMotorTruckCargo","deductible":2500,"limit":100000},{"coverageType":"CoverageGeneralLiability","deductible":0,"limit":1000000}],"equipmentList":{"flatfileMetadata":{"fileMetadata":{"name":"EquipmentList.csv"},"flatfileHandle":"997cd085-6759-4b89-b531-8922859e3bf9"},"info":[{"statedValue":123212,"vin":"3AKJGLD57FSGH6343"},{"statedValue":123122,"vin":"1FUJGLD59FLGE7812"}]},"numberOfPowerUnits":11,"producerId":"9250ebc0-c5e3-4fb3-8ce2-2ce021588e8e","projectedMileage":301200,"radiusOfOperation":[{"mileageRadiusBucket":"MileageRadiusBucketZeroToFifty","percentageOfFleet":100},{"mileageRadiusBucket":"MileageRadiusBucketFiftyToTwoHundred","percentageOfFleet":0},{"mileageRadiusBucket":"MileageRadiusBucketTwoHundredToFiveHundred","percentageOfFleet":0},{"mileageRadiusBucket":"MileageRadiusBucketFiveHundredPlus","percentageOfFleet":0}],"terminalLocations":[{"addressLineOne":"123 test st","addressLineTwo":"","isGated":False,"isGuarded":False,"typeOfTerminal":"Office","usState":"AZ","zipCode":"85355"}]}
  })

  headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'jsessionid': sessionId
  }

  # If duplicating application, overwrite reqInfo
  if duplicated_app_id:
    reqInfo = json.dumps(
      {
        "classesAndCommoditiesForm": duplicatedAppInfo["classesAndCommoditiesForm"],
        "lossRunSummaryForm": duplicatedAppInfo["lossRunSummaryForm"],
        "operationsForm": duplicatedAppInfo["operationsForm"]
      }
    )

  response = requests.request("PUT", url, headers=headers, data=reqInfo).json()

  print("app info submission response", response)


def generate_indication(id):
  url = f"https://api.prod.nirvanatech.com/application/{id}/indication/submit"

  payload = {}
  headers = {
    'Accept': 'application/json',
    'jsessionid': sessionId
  }

  response = requests.request("POST", url, headers=headers, data=payload)


def get_option_id(appId):
  url = f"https://api.prod.nirvanatech.com/application/{appId}/indication/options"

  payload = {}
  headers = {
    'Accept': 'application/json',
    'jsessionid': sessionId
  }

  optionId = ''

  # Giving indication set amount of time to generate
  MAX_MINUTES = 2
  seconds = 0
  sleepSeconds = 3
  while True:
    time.sleep(sleepSeconds)
    seconds += sleepSeconds
    try:
      optionsResponse = requests.request("GET", url, headers=headers, data=payload).json()
      optionId = optionsResponse["options"][1]["id"]
      break
    except:
      if (seconds / 60) >=  MAX_MINUTES:
        print("Indication generation is taking longer than max wait time, exiting")
        exit()
      if seconds % 10 == 0:
        print(f"Looks like Indication hansn't generated yet, retrying every {sleepSeconds} seconds")

  return optionId, optionsResponse


def select_option(appId, duplicated_app_id, duplicatedAppInfo, optionId, optionsResponse):
  url = f"https://api.prod.nirvanatech.com/application/{appId}/indication/options/select"

  payload = json.dumps({
    "id": optionId
  })

  # If duplicating application, overwrite reqInfo
  if duplicated_app_id:
    for option in optionsResponse["options"]:
      if duplicatedAppInfo["packageType"] == option["optionTag"]:
        print(f"Found option {option['optionTag']}")
        payload = json.dumps({
          "id": option["id"]
        })


  headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'jsessionid': sessionId
  }

  response = requests.request("POST", url, headers=headers, data=payload).json()

  print(response)


def submit_additional_info(appId, duplicated_app_id, duplicatedAppInfo):
  url = f"https://api.prod.nirvanatech.com/application/{appId}/additional_information"

  payload = json.dumps({"driverList":{"drivers":[{"dlNumber":"N034121001","usState":"MO","dateHired":"2018-08-22","dateOfBirth":"1954-10-18","yearsOfExperience":None},{"dlNumber":"U034273002","usState":"MO","dateHired":"2018-08-22","dateOfBirth":"1954-10-18","yearsOfExperience":None}],"flatfileMetadata":{"flatfileHandle":"21758593-b6b8-490f-a477-9cd97aeebb28","fileMetadata":{"name":"DriversList.csv"}}},"numOwnerOperatorUnits":0,"commodities":[],"lossRunFiles":[{"handle":"658947c6-d34b-4fd1-8c92-d96e330e91de","name":"LR 2019.2021 IFG .pdf","status":"succeeded"}],"largeLossComment":"","coverageMetadata":{"additionalIncumbentInfo":{"isALIncumbent":False}},"targetPrices":[{"coverageType":"CoverageAutoLiability","totalPremium":100}],"overallComment":""})

  # If duplicating application, overwrite payload
  if duplicated_app_id:
    payload = json.dumps({
      "driverList": duplicatedAppInfo["driversList"],
      "numOwnerOperatorUnits": duplicatedAppInfo["numOwnerOperatorUnits"],
      "commodities": duplicatedAppInfo["commodities"],
      "lossRunFiles": duplicatedAppInfo["lossRunFiles"],
      "largeLossComment": duplicatedAppInfo["largeLossComment"],
      "coverageMetadata": duplicatedAppInfo["coverageMetadata"],
      "targetPrices": duplicatedAppInfo["targetPrices"],
      "overallComment": duplicatedAppInfo["overallComment"]
    })


  headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'jsessionid': sessionId
  }

  response = requests.request("PUT", url, headers=headers, data=payload).json()

  print(response)


def submit_app(appId):
  url = f"https://api.prod.nirvanatech.com/application/{appId}/submit"

  payload = {}
  headers = {
    'Accept': 'application/json',
    'jsessionid': sessionId
  }

  response = requests.request("POST", url, headers=headers, data=payload).json()

  return response


def main():
      
  parser = argparse.ArgumentParser(description="create_app")
  subparser = parser.add_subparsers(dest="command", required=True)
  app_info = subparser.add_parser("app_info")
  app_info.add_argument(
      "--dot_number", required=False, help="dot number of fleet", default=536917
  )
  app_info.add_argument(
      "--app_name", required=False, help="if app name is passed, no app will be created. If app name and -generate_policy_docs are passed, that app name will be used to generate policy docs for the relative app review"
  )
  app_info.add_argument(
      "--duplicated_app_id", required=False, help="if ID of app duplicated is passed, the demo app will mirror that already existing app"
  )
  app_info.add_argument(
      "--company_name", required=False, help="company name of fleet",default="AllTrucking Inc"
  )
  app_info.add_argument(
      "--effective_date", required=False, help="effective date of application", default="2023-08-10"
  )
  app_info.add_argument(
      "-generate_policy_docs", action="store_true",
      help="If used, approves each section in UW app, approves, and generates policy docs"
  )
  app_info.add_argument(
      "--break_after",
      choices=("app_creation", "indication_info", "indication_generation", "option_selection", "additional_info", "app_submission", "app_approval", "policy_docs"),
      default="app_submission",
      help="app creation breakpoints - switches available are 'app_creation', 'indication_info', 'indication_generation', 'option_selection', 'additional_info', 'app_submission', 'app_approval' or 'policy_docs'",
  )
  args = parser.parse_args()

  today_date=datetime.today()
  today_company_name=f"{args.company_name} - {str(today_date)[-15:-5]}"

  # If just generating policy docs for an exisitng application, do just that and exit
  if args.generate_policy_docs and args.app_name:
    name=args.app_name
    approve_application_reviews(name)
    appId=get_app_id_by_name(name)
    generate_policy_docs(appId)
    exit()


  # If duplicating application, grab all necessary info
  duplicatedAppInfo = {}
  if args.duplicated_app_id:
    dupAppId = args.duplicated_app_id
    duplicatedAppInfo = fetch_app_info(dupAppId)

  breakPoint = args.break_after
  print(breakPoint)

  # APP CREATION:
  appId = create_app_id(today_company_name, args.dot_number, args.effective_date, args.duplicated_app_id, duplicatedAppInfo)
  if breakPoint == 'app_creation':
    print({"SUCCESS! appURL": f"https://agents.nirvanatech.com/applications/{appId}/create"})
    exit()

  time.sleep(1)
  
  # submission of all app info at once
  submit_info_for_indication(appId, args.duplicated_app_id, duplicatedAppInfo)
  if breakPoint == 'indication_info':
    print({"SUCCESS! appURL": f"https://agents.nirvanatech.com/applications/{appId}/create"})
    exit()

  # proceed to generate Indication
  generate_indication(appId)
  if breakPoint == 'indication_generation':
    print({"SUCCESS! appURL": f"https://agents.nirvanatech.com/applications/{appId}/create"})
    exit()

  # Getting option ID
  optionId, optionsResponse = get_option_id(appId)
  # Option selection
  select_option(appId, args.duplicated_app_id, duplicatedAppInfo, optionId, optionsResponse)
  if breakPoint == 'option_selection':
    print({"SUCCESS! appURL": f"https://agents.nirvanatech.com/applications/{appId}/create"})
    exit()

  # additional info
  submit_additional_info(appId, args.duplicated_app_id, duplicatedAppInfo)
  if breakPoint == 'additional_info':
    print({"SUCCESS! appURL": f"https://agents.nirvanatech.com/applications/{appId}/create"})
    exit()

  # Submission
  submissionResponse = submit_app(appId)
  if breakPoint == 'app_submission':
    print({"SUCCESS! appURL": f"https://agents.nirvanatech.com/applications/{appId}/submitted"})
    exit()

  # Approving app review
  approve_application_reviews(today_company_name)
  if breakPoint == 'app_approval':
    print({"SUCCESS! appURL": f"https://agents.nirvanatech.com/applications/{appId}/submitted"})
    exit()

  # TODO: ability to select coverages for new, unduplicated app
  coverages = duplicatedAppInfo["coverages"] if args.duplicated_app_id else ['CoverageAutoLiability','CoverageMotorTruckCargo','CoverageGeneralLiability']
  # Generating policy docs
  generate_policy_docs(appId, coverages)
  if breakPoint == 'policy_docs':
    print({"SUCCESS! appURL": f"https://agents.nirvanatech.com/applications/{appId}/submitted"})
    exit()

  print("\n\n\n")
  print("App submission response:", submissionResponse)

  print("SUCCESS! app ID: ", appId)

  print({"today_company_name":today_company_name})

  print({"appURL": f"https://agents.nirvanatech.com/applications/{appId}/submitted"})


if __name__ == "__main__":
    main()

