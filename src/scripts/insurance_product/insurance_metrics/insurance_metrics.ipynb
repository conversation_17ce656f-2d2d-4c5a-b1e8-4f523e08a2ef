{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Load Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import csv\n", "import json\n", "from datetime import datetime\n", "from functools import reduce\n", "\n", "pd.set_option('display.float_format', lambda x: f'{x:.5f}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Inputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_name = \"sample_insurance_metrics\"\n", "input_path_data_file = input_name + \"_data_file.csv\"\n", "input_path_configuration_file = input_name + \"_configuration_file.xlsx\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_flag = False\n", "output_name = input_name + \"_output_file\"\n", "output_path_data_file = output_name + \"-\" + str(datetime.now().isoformat(timespec='seconds')) + \".xlsx\"\n", "output_path_data_file = output_path_data_file.replace(\":\", \"_\") "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Read Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(input_path_data_file)\n", "cf = pd.read_excel(input_path_configuration_file, sheet_name = None)\n", "\n", "df_variables = cf['variables'].copy()\n", "df_variables = df_variables.loc[df_variables['transformation'].isin(['id'])].copy().reset_index()\n", "df_id_column = df_variables['variable_name'][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def f_count(df, group_by, name):\n", "    df_count = df.groupby(group_by, as_index = True).agg({name:'count'}).reset_index()\n", "    return df_count\n", "\n", "def f_ratio(df, group_by, name, dividend, divisor):\n", "    df_agg = df.groupby(group_by, as_index = True).agg({dividend:'sum', divisor:'sum'}).reset_index()\n", "    df_agg[name] = df_agg[dividend] / df_agg[divisor]\n", "    df_agg = df_agg.drop(columns=[dividend, divisor])\n", "    return df_agg\n", "\n", "def f_sum(df, group_by, name):\n", "    df_sum = df.groupby(group_by, as_index = True).agg({name:'sum'}).reset_index()\n", "    return df_sum\n", "\n", "dispatch = {'f_count': f_count, 'f_ratio': f_ratio, 'f_sum': f_sum}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Dates - Yearly/Quarterly/Montlhy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_variables = cf['variables'].copy()\n", "df_variables = df_variables.loc[df_variables['transformation'].isin(['date_y', 'date_m', 'date_q'])].copy()\n", "\n", "for index, row in df_variables.iterrows():\n", "    variable = row['variable_name']\n", "    transformation = row['transformation']\n", "    df[variable] = pd.to_datetime(df[variable]) \n", "    if transformation == 'date_y':\n", "        df[transformation + '_' + variable] = df[variable].dt.to_period('Y')\n", "    if transformation == 'date_m':\n", "        df[transformation + '_' + variable] = df[variable].dt.to_period('M')\n", "    elif transformation == 'date_q':\n", "        df[transformation + '_' + variable] = df[variable].dt.to_period('Q')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Quantiles"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_variables = cf['variables'].copy()\n", "df_variables = df_variables.loc[df_variables['transformation'].isin(['quantile'])].copy()\n", "\n", "for index, row in df_variables.iterrows():\n", "    variable = row['variable_name']\n", "    transformation = row['transformation']\n", "    n = int(row['inputs'])\n", "    df[transformation + '_' + variable] = pd.qcut(x = df[variable], q = n)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Manual Intervals"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "df_variables = cf['variables'].copy()\n", "df_variables = df_variables.loc[df_variables['transformation'].isin(['interval'])].copy()\n", "\n", "for index, row in df_variables.iterrows():\n", "    variable = row['variable_name']\n", "    transformation = row['transformation']\n", "    json_string = row['inputs']\n", "    json_object = json.loads(json_string)\n", "    bins = [df[variable].min()] + json_object['bins'] + [df[variable].max()]\n", "    bins.sort()\n", "    right = json_object['right']\n", "    df[transformation + '_' + variable] = pd.cut(x = df[variable], bins = bins, right = right, include_lowest = True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Manual Clusters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_variables = cf['variables'].copy()\n", "df_variables = df_variables.loc[df_variables['transformation'].isin(['cluster'])].copy()\n", "\n", "for index, row in df_variables.iterrows():\n", "    variable = row['variable_name']\n", "    transformation = row['transformation']\n", "    df_cluster = cf[variable]\n", "    df = pd.merge(df, df_cluster, on = variable, how = 'left')\n", "    df[transformation + '_' + variable] = df[transformation + '_' + variable].fillna('other')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Tables - From Inception"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_variables = cf['variables'].copy()\n", "df_tables = cf['tables'].copy()\n", "\n", "df_variables = df_variables.query(\"transformation != 'id'\")\n", "transformation_variable_table_list = []\n", "\n", "for index, row in df_variables.iterrows():\n", "    \n", "    transformation_variable = row['transformation'] + '_' + row['variable_name']\n", "    table_list = []\n", "\n", "    for index, row in df_tables.iterrows():\n", "        function = row['function']\n", "        inputs = json.loads(row['inputs'])\n", "        f_table = dispatch[function]\n", "        table_list.append(f_table(df, transformation_variable, **inputs))\n", "\n", "    transformation_variable_table = reduce(lambda x, y: pd.merge(x, y, on = transformation_variable), table_list)\n", "    display(transformation_variable_table)\n", "    transformation_variable_table_list.append(transformation_variable_table)\n", "\n", "if output_flag == True:\n", "\n", "    with pd.ExcelWriter(output_path_data_file, engine = 'openpyxl') as writer:    \n", "\n", "        column = 0\n", "        row = 0\n", "\n", "        for transformation_variable_table in transformation_variable_table_list:\n", "            row += 1\n", "            transformation_variable_table.to_excel(writer, sheet_name = 'From Inception', startrow = row, startcol = column, index = False)\n", "            row += transformation_variable_table.shape[0] + 2"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Tables - Yearly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_variables = cf['variables'].copy()\n", "df_tables = cf['tables'].copy()\n", "\n", "if((df_variables['transformation'].eq('date_y')).any()):\n", "\n", "    df_date = df_variables.loc[df_variables['transformation'].isin(['date_y'])].copy().reset_index()\n", "    date_x = df_date['transformation'][0] + '_' + df_date['variable_name'][0]\n", "\n", "    df_variables = df_variables.query(\"transformation not in ('id', 'date_y', 'date_m', 'date_q')\")\n", "    transformation_variable_table_list = []\n", "\n", "    for index, row in df_variables.iterrows():\n", "        \n", "        transformation_variable = row['transformation'] + '_' + row['variable_name']\n", "        table_list = []\n", "\n", "        for index, row in df_tables.iterrows():\n", "            function = row['function']\n", "            inputs = json.loads(row['inputs'])\n", "            f_table = dispatch[function]\n", "            table_list.append(f_table(df, group_by = [transformation_variable, date_x], **inputs))\n", "\n", "        transformation_variable_table = reduce(lambda x, y: pd.merge(x, y, on = [transformation_variable, date_x]), table_list)\n", "        transformation_variable_table = transformation_variable_table.pivot(index = transformation_variable, columns = date_x)\n", "        display(transformation_variable_table)\n", "        transformation_variable_table_list.append(transformation_variable_table)\n", "\n", "    if output_flag == True:\n", "\n", "        with pd.ExcelWriter(output_path_data_file, engine = 'openpyxl', mode = 'a', if_sheet_exists = 'overlay') as writer:    \n", "\n", "            column = 0\n", "            row = 0\n", "\n", "            for transformation_variable_table in transformation_variable_table_list:\n", "                row += 1\n", "                transformation_variable_table.to_excel(writer, sheet_name = 'Yearly', startrow = row, startcol = column)\n", "                row += transformation_variable_table.shape[0] + 4"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Tables - Quarterly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_variables = cf['variables'].copy()\n", "df_tables = cf['tables'].copy()\n", "\n", "if((df_variables['transformation'].eq('date_q')).any()):\n", "\n", "    df_date = df_variables.loc[df_variables['transformation'].isin(['date_q'])].copy().reset_index()\n", "    date_x = df_date['transformation'][0] + '_' + df_date['variable_name'][0]\n", "\n", "    df_variables = df_variables.query(\"transformation not in ('id', 'date_y', 'date_m', 'date_q')\")\n", "    transformation_variable_table_list = []\n", "\n", "    for index, row in df_variables.iterrows():\n", "        \n", "        transformation_variable = row['transformation'] + '_' + row['variable_name']\n", "        table_list = []\n", "\n", "        for index, row in df_tables.iterrows():\n", "            function = row['function']\n", "            inputs = json.loads(row['inputs'])\n", "            f_table = dispatch[function]\n", "            table_list.append(f_table(df, group_by = [transformation_variable, date_x], **inputs))\n", "\n", "        transformation_variable_table = reduce(lambda x, y: pd.merge(x, y, on = [transformation_variable, date_x]), table_list)\n", "        transformation_variable_table = transformation_variable_table.pivot(index = transformation_variable, columns = date_x)\n", "        display(transformation_variable_table)\n", "        transformation_variable_table_list.append(transformation_variable_table)\n", "\n", "    if output_flag == True:\n", "\n", "        with pd.ExcelWriter(output_path_data_file, engine = 'openpyxl', mode = 'a', if_sheet_exists = 'overlay') as writer:    \n", "\n", "            column = 0\n", "            row = 0\n", "\n", "            for transformation_variable_table in transformation_variable_table_list:\n", "                row += 1\n", "                transformation_variable_table.to_excel(writer, sheet_name = 'Quaterly', startrow = row, startcol = column)\n", "                row += transformation_variable_table.shape[0] + 4"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Tables - Monthly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_variables = cf['variables'].copy()\n", "df_tables = cf['tables'].copy()\n", "\n", "if((df_variables['transformation'].eq('date_m')).any()):\n", "\n", "    df_date = df_variables.loc[df_variables['transformation'].isin(['date_m'])].copy().reset_index()\n", "    date_x = df_date['transformation'][0] + '_' + df_date['variable_name'][0]\n", "\n", "    df_variables = df_variables.query(\"transformation not in ('id', 'date_y', 'date_m', 'date_q')\")\n", "    transformation_variable_table_list = []\n", "\n", "    for index, row in df_variables.iterrows():\n", "        \n", "        transformation_variable = row['transformation'] + '_' + row['variable_name']\n", "        table_list = []\n", "\n", "        for index, row in df_tables.iterrows():\n", "            function = row['function']\n", "            inputs = json.loads(row['inputs'])\n", "            f_table = dispatch[function]\n", "            table_list.append(f_table(df, group_by = [transformation_variable, date_x], **inputs))\n", "\n", "        transformation_variable_table = reduce(lambda x, y: pd.merge(x, y, on = [transformation_variable, date_x]), table_list)\n", "        transformation_variable_table = transformation_variable_table.pivot(index = transformation_variable, columns = date_x)\n", "        display(transformation_variable_table)\n", "        transformation_variable_table_list.append(transformation_variable_table)\n", "\n", "    if output_flag == True:\n", "\n", "        with pd.ExcelWriter(output_path_data_file, engine = 'openpyxl', mode = 'a', if_sheet_exists = 'overlay') as writer:    \n", "\n", "            column = 0\n", "            row = 0\n", "\n", "            for transformation_variable_table in transformation_variable_table_list:\n", "                row += 1\n", "                transformation_variable_table.to_excel(writer, sheet_name = 'Monthly', startrow = row, startcol = column)\n", "                row += transformation_variable_table.shape[0] + 4"]}], "metadata": {"interpreter": {"hash": "c9a38c7b044b89eaec5490977eaaef0f0ee3b9ca1d27e396d547b7dd6fcc74c1"}, "kernelspec": {"display_name": "Python 3.9.7 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7 (default, Sep 16 2021, 08:50:36) \n[Clang 10.0.0 ]"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}