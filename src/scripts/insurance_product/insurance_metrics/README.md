# INSURANCE METRICS

## How to use:

### General Setup

* `Pull` latest version of the code on github to local machine.
* Copy Python Notebook file: `insurance_metrics.ipynb` into a local folder outside Nirvana repo.
* Add files `xxx_data_file.csv` and `xxx_configuration_file.xlsx` into **same folder** as above, where `xxx` can be any string that describes your dataset. Examples of these files (_xxx = sample_insurance_metrics_) can be found  here: [Github Insurance Metrics](https://drive.google.com/drive/folders/11pLSkeu_5aSi6Ss4OmNH385TX8MY3B5r?usp=share_link). Description of these tables and how to create them can be found below in their specific subsections.

### Notebook: insurance_metrics.ipynb

* Change the `input_name` to `xxx` (from General Setup: if these files are in a different folder than the Python Notebook you will need to add all the path to the file, i.e. _/Users/<USER>/path/to/file/filename_).
* Optional - `output_flag` boolean: If `True` a xlsx file will be created with the list of tables created by running the Notebook `insurance_metrics.ipynb`. The default value is `False`. 
* Run Notebook:
    * If everything runs successfully a list of tables with metrics will be printed at the end of the Notebook without any error messages.

#### xxx_data_file.csv

This file is created independently and contains the data to be use to create the list of tables with metrics that will be specified on the file: `xxx_configuration_file.xlsx`.

**NOTE:** All variable names must match between files.

#### xxx_configuration_file.xlsx

This files is created based from the columns on `xxx_data_file.csv` and contains the configurations to create the list of tables with metrics. This file need to have two tabs: `variables` and `tables`. Extra tabs are included depending on how the `variables` tab is defined.

The `variables` tab contains three columns: 

| variable_name | transformation | inputs |
| :-----------: | :------------: | :----: |
| DOT | id | NA |

The `variable_names` column contains columns names from `xxx_data_file.csv` that will be modified/transformed by using the `transformation` and `inputs` columns.   

_Type of transformations_ (_inputs_ format):
* _id_: this row specify the id/key column (_inputs_: NA).
* _quantile_: discretize variable into equal-sized buckets based on rank (_inputs_: integer, number of quantiles).
* _interval_: discretize variable using bins from inputs (_inputs_: { "right": boolean, true implies rigth close interval, "bins": [list of breaks] })
* _date_y_ or _date_q_ or _date_m_: modify date column into years, quarters or months respectively (_inputs_: NA).
* _cluster_: uses a tab to create a clusters/group. For each of these cluster rows we will need an extra tab (_inputs_: NA).

The `tables` tab contains three columns: 

| table_name | function | inputs |
| :-----------: | :------------: | :----: |
| DOT_Count | f_count | { "name": "DOT" } |

The `table_names` column contains names that are just informative. Each row will create a table by using the `funtion` and `inputs` columns and they are merge into a single table for each variable from `variables` tab except for the _id_ transformation row.

_Type of functions_ (_inputs_ format):

* _f_count_: count aggregation (_inputs_: { "name": "COLUMN_NAME" })
* _f_sum_: sum aggregation (_inputs_: { "name": "COLUMN_NAME" })
* _f_ratio_: ratio aggregation (_inputs_: { "name": "OUTPUT_NAME", "dividend" :  "COLUMN_DIVIDEND", "divisor" : "COLUMN_DIVISOR"} )
