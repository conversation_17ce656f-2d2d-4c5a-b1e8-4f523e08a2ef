{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Load Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import date\n", "import gspread\n", "from google.auth.transport.requests import AuthorizedSession\n", "from google.oauth2 import service_account"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Credentials"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["googleAPI = 'python_runner_config.json'\n", "scope = ['https://www.googleapis.com/auth/spreadsheets']\n", "credentials = service_account.Credentials.from_service_account_file(googleAPI)\n", "scopedCreds = credentials.with_scopes(scope)\n", "gc = gspread.Client(auth=scopedCreds)\n", "gc.session = AuthorizedSession(scopedCreds)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["google_sheet = gc.open_by_key('1a37h_edt1Vi3qC2XqB9cyKCH03lFMMud3a4-e2F9o7Q')\n", "\n", "tab_data_historical = google_sheet.worksheet('copy_data_historical')\n", "tab_data_new = google_sheet.worksheet('data_new')\n", "\n", "data_historical = pd.DataFrame(tab_data_historical.get_all_records())\n", "data_new = pd.DataFrame(tab_data_new.get_all_records()).drop(columns=['Day Mileage File', 'Under Investigation', 'Sum Of Mileage'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Extra and Cumulative Info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = date.today()\n", "month = date.today().strftime(\"%b\")\n", "first_day_month = date.today().replace(day=1)\n", "\n", "data_new['Created At'] = pd.to_datetime(today)\n", "data_new['Invoice Date'] = month\n", "data_new['Status'] = 'Unpaid'\n", "data_new['Invoice Number'] = ''\n", "data_new['Deposit'] = 'No'\n", "data_new['Effective Date'] = pd.to_datetime(data_new['Effective Date'])\n", "data_new['Cumulative through (not including)'] = pd.to_datetime(first_day_month)\n", "data_new['Cumul Days'] = (data_new['Cumulative through (not including)'] - data_new['Effective Date']).dt.days/365\n", "data_new['Cumul Phys Prem'] = data_new['Cumul Phys Prem'].replace('[\\$,]', '', regex=True).astype(float)\n", "data_new['Cumul GL Prem'] = data_new['Cumul GL Prem'].replace('[\\$,]', '', regex=True).astype(float)\n", "data_new['Cumul Flat Prem'] = data_new['Cumul Flat Prem'].replace('[\\$,]', '', regex=True).astype(float)\n", "data_new['Cumul Total'] =   data_new['Cumul Mileage'] * data_new['Cumul Liab Rate'] \\\n", "                          + data_new['Cumul Phys Prem'] * data_new['Cumul Days']    \\\n", "                          + data_new['Cumul GL Prem'] * data_new['Cumul Days']      \\\n", "                          + data_new['Cumul Mileage'] * data_new['Cumul MTC Rate']  \\\n", "                          + data_new['Cumul Flat Prem'] * data_new['Cumul Days']\n", "data_new['Commission Rate'] = data_new['Commission Rate'].replace('[\\%]', '', regex=True).astype('float') / 100.0\n", "data_new['Cumul Commissions'] =   data_new['Cumul Total'] * data_new['Commission Rate']\n", "data_new['Cumul Total Less Commissions'] = data_new['Cumul Total'] - data_new['Cumul Commissions']\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["DOT - Historical and New"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_historical_last = data_historical.sort_values('Created At').groupby('DOT').tail(1).copy()\n", "\n", "dot_historical = data_historical_last['DOT'].to_list()\n", "dot_new = list(set(data_new['DOT'].to_list()) - set(dot_historical))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Non Cumulative Info"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For New DOT:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_dot_new = data_new.loc[data_new['DOT'].isin(dot_new)].copy()\n", "\n", "data_new_dot_new['Mileage'] = data_new_dot_new['Cumul Mileage']\n", "data_new_dot_new['Liab Rate'] = data_new_dot_new['Cumul Liab Rate']\n", "data_new_dot_new['Phys Prem'] = data_new_dot_new['Cumul Phys Prem']\n", "data_new_dot_new['GL Prem'] = data_new_dot_new['Cumul GL Prem']\n", "data_new_dot_new['MTC Rate'] = data_new_dot_new['Cumul MTC Rate']\n", "data_new_dot_new['Flat Prem'] = data_new_dot_new['Cumul Flat Prem']\n", "data_new_dot_new['Days'] = data_new_dot_new['Cumul Days']\n", "data_new_dot_new['Total'] = data_new_dot_new['Cumul Total']\n", "data_new_dot_new['Commissions'] = data_new_dot_new['Cumul Commissions']\n", "data_new_dot_new['Total Less Commissions'] = data_new_dot_new['Cumul Total Less Commissions']\n", "\n", "data_new_dot_new = data_new_dot_new[data_historical.columns]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For Historical DOT:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_new_dot_old_aux = data_new.loc[data_new['DOT'].isin(dot_historical)].copy()\n", "\n", "data_historical_aux = data_historical_last[['DOT','Cumul Mileage','Cumul Days','Cumul Total','Cumul Commissions','Cumul Total Less Commissions']].copy()\n", "data_historical_aux.columns = data_historical_aux.columns.str.replace(\"Cumul \",\"\")\n", "\n", "data_historical_aux['Days'] = data_historical_aux['Days'].replace('[\\%]', '', regex=True).astype('float') / 100.0\n", "data_historical_aux['Total'] = data_historical_aux['Total'].replace('[\\$,]', '', regex=True).astype(float)\n", "data_historical_aux['Commissions'] = data_historical_aux['Commissions'].replace('[\\$,]', '', regex=True).astype(float)\n", "data_historical_aux['Total Less Commissions'] = data_historical_aux['Total Less Commissions'].replace('[\\$,]', '', regex=True).astype(float)\n", "\n", "data_new_dot_old = pd.merge(data_new_dot_old_aux, data_historical_aux, on='DOT', how='left')\n", "\n", "data_new_dot_old['Mileage'] = data_new_dot_old['Cumul Mileage'] - data_new_dot_old['Mileage'] \n", "data_new_dot_old['Liab Rate'] = data_new_dot_old['Cumul Liab Rate']\n", "data_new_dot_old['Phys Prem'] = data_new_dot_old['Cumul Phys Prem']\n", "data_new_dot_old['GL Prem'] = data_new_dot_old['Cumul GL Prem']\n", "data_new_dot_old['MTC Rate'] = data_new_dot_old['Cumul MTC Rate']\n", "data_new_dot_old['Flat Prem'] = data_new_dot_old['Cumul Flat Prem']\n", "data_new_dot_old['Days'] = data_new_dot_old['Cumul Days'] - data_new_dot_old['Days']\n", "data_new_dot_old['Total'] = data_new_dot_old['Cumul Total'] - data_new_dot_old['Total']\n", "data_new_dot_old['Commissions'] = data_new_dot_old['Cumul Commissions'] - data_new_dot_old['Commissions']\n", "data_new_dot_old['Total Less Commissions'] = data_new_dot_old['Cumul Total Less Commissions'] - data_new_dot_old['Total Less Commissions']\n", "\n", "data_new_dot_old = data_new_dot_old[data_historical.columns]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Final Data Set: Concatenate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_final = pd.concat([data_new_dot_old,data_new_dot_new]).sort_values(by = ['Effective Date', 'Customer'])\n", "data_final['Created At'] = data_final['Created At'].dt.strftime(\"%m/%d/%y\")\n", "data_final['Effective Date'] = data_final['Effective Date'].dt.strftime(\"%m/%d/%y\")\n", "data_final['Cumulative through (not including)'] = data_final['Cumulative through (not including)'].dt.strftime(\"%m/%d/%y\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Write to Google Sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tab_data_final = google_sheet.add_worksheet(title = \"data_final\", rows = data_final.shape[0] + 10, cols = data_final.shape[1] + 10)\n", "tab_data_final.update([data_final.columns.values.tolist()] + data_final.values.tolist())"]}], "metadata": {"interpreter": {"hash": "c9a38c7b044b89eaec5490977eaaef0f0ee3b9ca1d27e396d547b7dd6fcc74c1"}, "kernelspec": {"display_name": "Python 3.9.7 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}