{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Load Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "from IPython.display import display\n", "from datetime import date"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Inputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_path = \"./temp_mileage/20220801_new_data/\"\n", "date_start = \"2020-12-31\"\n", "date_end = \"2022-07-01\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files_names = ! ls {input_path}\n", "files_names = [\"{}{}\".format(input_path,n) for n in files_names]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_path = input_path.replace(\"new_data\", \"new_data_modified\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Loop"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path_file in files_names:\n", "    \n", "    print(path_file)\n", "    df = pd.read_csv(path_file)\n", "    df = df.query(\"day >= '{}'\".format(date_start))\n", "    df = df.query(\"day < '{}'\".format(date_end))\n", "    display(df.shape)\n", "    display(sum(df[\"distance\"]))\n", "\n", "    dict_of_vins = {k: v for k, v in df.groupby(\"vin\")}\n", "\n", "    for vin, vin_data in dict_of_vins.items():\n", "\n", "        if(vin_data.shape[0] <= 10):\n", "            print(\"review data for vin == '{}'\".format(vin))\n", "\n", "        list_of_dates = list(vin_data.query(\"distance >= 1500\")[\"day\"])\n", "\n", "        for date in list_of_dates:\n", "            \n", "            previous_date = max(vin_data.query(\"day < '{}'\".format(date))[\"day\"].append(pd.Series(['2020-12-31'])))\n", "            distance = float(vin_data.query(\"day == '{}'\".format(date))['distance'])\n", "            \n", "            d = pd.date_range(start=previous_date,end=date,closed =\"right\").astype(str)\n", "            ts = pd.DataFrame({\"day\":d, \"vin\":vin, \"distance\":distance/d.size})\n", "\n", "            vin_data = vin_data.query(\"day != '{}'\".format(date))\n", "            vin_data = pd.concat([vin_data, ts])\n", "            vin_data[\"day\"] = vin_data[\"day\"].astype(str)\n", "            vin_data.sort_values(by = [\"day\"])\n", "\n", "        dict_of_vins[vin] = vin_data\n", "\n", "    df = pd.concat([vin_data for vin,vin_data in dict_of_vins.items()])\n", "\n", "    print(\"check:\")\n", "    display(df.shape)\n", "    display(sum(df[\"distance\"]))\n", "    print(\"------------\")\n", "\n", "    path_file = path_file.replace(\"new_data\", \"new_data_modified\")\n", "    is_exist = os.path.exists(output_path)\n", "\n", "    if not is_exist:\n", "        os.makedirs(output_path)\n", "\n", "    df.to_csv(path_file, index = False)"]}], "metadata": {"interpreter": {"hash": "c9a38c7b044b89eaec5490977eaaef0f0ee3b9ca1d27e396d547b7dd6fcc74c1"}, "kernelspec": {"display_name": "Python 3.9.7 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}