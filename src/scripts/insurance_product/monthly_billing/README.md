## MONTHLY BILLING

### How to use:

#### General Setup

* `Pull` latest version of the code to local machine
* Copy files: `mileage_modified`, `mileage_cumulative` and `monthly_billing` and full folder: `temp_mileage` into a local folder outside Nirvana repo.
* Inside `temp_mileage` folder create a new folder: `YYYYMMDD_new_data` and load the new mileage files from the board application to this folder. Example: `20220401_new_data`
* <PERSON> or <PERSON> for the `python_runner_configuration.json` file and copy this file into the same folder created for bullet point two above.

#### mileage_modified

* Change the input_path to the newest one created in the general setup instructions `./temp_mileage/YYYYMMDD_new_data`.
* Change date_start to desired date (it is used to restrict data to dates >= date_start).
* Change date_end to desired date (it is used to restrict data to dates < date_end).
* Run Notebook:
    * It will print on the console information to review a few vins and also for checks for original total mileage vs modified total mileage.
    * If everything runs successfully then there will be a new folder `YYYYMMDD_new_data_modified` with the same number of files than in the folder `YYYYMMDD_new_data`.

#### mileage_cumulative

* Tabs refered below are with respect to this [google spreadsheet](https://docs.google.com/spreadsheets/d/1a37h_edt1Vi3qC2XqB9cyKCH03lFMMud3a4-e2F9o7Q/edit#gid=212242097). 
* Make sure that `data_new` tab has the full information for the corresponding monthly billing run, except cumulative mileage information.
* Make sure that the `data_cum_mil` tab does not exist before running the notebook.
* Change the input_path to the newest one created in the mileage_modified instructions 
`./temp_mileage/YYYYMMDD_new_data_modified`.
* Run Notebook:
    * If everything runs successfully then the `data_cum_mil` tab will be created.
    * Delete `data_cum_mil` tab after cumulative mileage information is copied to `data_new` tab.

#### monthly_billing
* Tabs refered below are with respect to this [google spreadsheet](https://docs.google.com/spreadsheets/d/1a37h_edt1Vi3qC2XqB9cyKCH03lFMMud3a4-e2F9o7Q/edit#gid=212242097).
* Make sure that `data_new` tab has the full information for the corresponding monthly billing run, including cumulative mileage information.
* Make sure that `copy_data_historical` tab has the full information from previous billing cycle without 3 last columns from `data_historical` (Flag, Customer, Previous Flag).
* Make sure that the `data_final` tab does not exist before running the notebook.
* Run Notebook:
    * If everything runs successfully then the `data_final` tab will be created.
    * Delete `data_final` tab after information is copied to `data_historical` tab.
