{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Load Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "from datetime import date\n", "import gspread\n", "from google.auth.transport.requests import AuthorizedSession\n", "from google.oauth2 import service_account"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Inputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_path = \"./temp_mileage/20220701_new_data_modified/\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Credentials"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["googleAPI = 'python_runner_config.json'\n", "scope = ['https://www.googleapis.com/auth/spreadsheets']\n", "credentials = service_account.Credentials.from_service_account_file(googleAPI)\n", "scopedCreds = credentials.with_scopes(scope)\n", "gc = gspread.Client(auth=scopedCreds)\n", "gc.session = AuthorizedSession(scopedCreds)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["google_sheet = gc.open_by_key('1a37h_edt1Vi3qC2XqB9cyKCH03lFMMud3a4-e2F9o7Q')\n", "tab_data_new = google_sheet.worksheet('data_new')\n", "data_new = pd.DataFrame(tab_data_new.get_all_records())[['Customer', 'Effective Date', 'Day Mileage File']]\n", "\n", "data_new['Effective Date'] = pd.to_datetime(data_new['Effective Date'])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First Day of the Month"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = date.today()\n", "month = date.today().strftime(\"%b\")\n", "first_day_month = date.today().replace(day=1)\n", "print(first_day_month)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Loop"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cumulative_distance = [] \n", "\n", "customer_list = data_new['Customer'].to_list()\n", "\n", "for customer in customer_list:\n", "    \n", "    day_mileage_file = data_new.query(\"Customer == '{}'\".format(customer))['Day Mileage File'].values[0]\n", "    effective_date = data_new.query(\"Customer == '{}'\".format(customer))['Effective Date'].values[0]\n", "    effective_date = str(effective_date).replace(\"T00:00:00.000000000\",\"\")\n", "    path_file = input_path + day_mileage_file\n", "    path_file_exist = os.path.exists(path_file)\n", "    if path_file_exist:\n", "        df = pd.read_csv(path_file)\n", "        total_distance = sum(df.query(\"'{}' <= day < '{}'\".format(effective_date,first_day_month))[\"distance\"])\n", "        cumulative_distance.append(total_distance)\n", "    else:\n", "        cumulative_distance.append(-1.0)\n", "\n", "data_new[\"Cumulative Mileage\"] = cumulative_distance\n", "data_new = data_new.drop(columns=['Day Mileage File', \"Effective Date\"])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Write to Google Sheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tab_data_cum_mil = google_sheet.add_worksheet(title = \"data_cum_mil\", rows = data_new.shape[0] + 10, cols = data_new.shape[1] + 10)\n", "tab_data_cum_mil.update([data_new.columns.values.tolist()] + data_new.values.tolist())"]}], "metadata": {"interpreter": {"hash": "c9a38c7b044b89eaec5490977eaaef0f0ee3b9ca1d27e396d547b7dd6fcc74c1"}, "kernelspec": {"display_name": "Python 3.9.7 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}