import requests
import json

# This script gets all the files from S3, given DocumentId -> App Review Id mapping
# One way to get this through metabase prod SQL Query:
# https://metabase.dev.nirvanatech.com/question/683-get-documentid-applicationreviewid-pair
# Download via json and paste it below

# Not reading map JSON from file on local because we can run this script on <PERSON><PERSON><PERSON> too, then.
doc_id_app_review_id_map = '''[
{"document_id":"1a7efd30-a779-4123-bcc9-7c2a97297bc2","app_review_id":"ec3eae8b-58e9-4785-a667-1286eb71dd57"},
{"document_id":"2011b1da-a196-48e7-99f0-2631516a592a","app_review_id":"ec3eae8b-58e9-4785-a667-1286eb71dd57"}
]'''

headers = {
    "Jsessionid": "<session-id>" # Insert session ID here
}

def save_pdf_locally(pdf_url, output_file_path):
    response = requests.get(pdf_url, stream=True)
    if response.status_code == 200:
        with open(output_file_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=1024):
                file.write(chunk)
        print(f"PDF saved successfully to {output_file_path}.")
        return True
    else:
        print(f"Failed to retrieve PDF. Status code: {response.status_code}")
        return False

# Call the API to retrieve the S3 file link
data = json.loads(doc_id_app_review_id_map)
for d in data:
    app_review_id = d["app_review_id"]
    doc_id = d["document_id"]
    api_url = f"https://api.prod.nirvanatech.com/underwriting/application_reviews/{app_review_id}/documents/{doc_id}/link"
    response = requests.get(api_url, headers=headers)
    if response.status_code == 200:
        s3_file_link = response.json().get("link")
        if s3_file_link:
            if not save_pdf_locally(s3_file_link, doc_id + ".pdf"):
                break
        else:
            print("No S3 file link found in the API response.")
            break
    else:
        print(f"Failed to retrieve S3 file link from the API. Status code: {response.status_code}")
        break

