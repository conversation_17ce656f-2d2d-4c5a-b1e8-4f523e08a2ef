# If user exists, return code will be 0, else 2
getent passwd $DEVBOX_USER_UID

if [ $? -ne 0 ]; then
    useradd -d /home/<USER>
        usermod -u $DEVBOX_USER_UID -U $DEVBOX_USER_NAME && \
        usermod -aG sudo $DEVBOX_USER_NAME && \
        groupmod -g $DEVBOX_USER_GUID $DEVBOX_USER_NAME
fi

cat /etc/passwd

echo $DEVBOX_USER_NAME:$DEVBOX_USER_NAME | chpasswd
echo "$DEVBOX_USER_NAME ALL=(ALL) NOPASSWD:ALL" | sudo tee -a /etc/sudoers

