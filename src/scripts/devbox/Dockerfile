ARG DEVBOX_IMAGE
FROM $DEVBOX_IMAGE

ARG DEVBOX_USER_NAME
ARG DEVBOX_USER_UID
ARG DEVBOX_USER_GUID

# Install general dependencies
RUN apt update
RUN apt install -y sudo wget gnupg docker.io python python3 vim unzip curl amazon-ecr-credential-helper

# Setup user
COPY setup_user.sh /tmp/
RUN chmod +x /tmp/setup_user.sh
RUN bash -c /tmp/setup_user.sh

# Setup bazel
COPY setup_bazel.sh /tmp/
RUN chmod +x /tmp/setup_bazel.sh
RUN bash -c /tmp/setup_bazel.sh


