version: '3.6'
services:
  dev-box:
    build:
      context: . # current dir
      args:
        DEVBOX_USER_NAME: ${DEVBOX_USER_NAME:?DEVBOX_USER_NAME}
        DEVBOX_USER_UID: ${DEVBOX_USER_UID:?DEVBOX_USER_UID}
        DEVBOX_USER_GUID: ${DEVBOX_USER_GUID:?DEVBOX_USER_GUID}
        DEVBOX_IMAGE: ${DEVBOX_IMAGE:?DEVBOX_IMAGE}
    image: nirvana-devbox
    command: /bin/bash
    hostname: localhost
    user: ${DEVBOX_USER_NAME:?DEVBOX_USER_NAME}
    env_file: .env
    environment:
      COMPOSE_PROJECT_NAME: dev-box-nirvana
      IN_DEVBOX_CONTAINER: 'true'
    privileged: true
    working_dir: ${DEVBOX_USER_WORKDIR:?DEVBOX_USER_WORKDIR}
    network_mode: host
    volumes:
      - ${DEVBOX_HOST_USER_WORKDIR:?DEVBOX_HOST_USER_WORKDIR}:${DEVBOX_USER_WORKDIR:?DEVBOX_USER_WORKDIR}
      - ${DEVBOX_HOST_USER_HOME:?DEVBOX_HOST_USER_HOME}/.aws:/home/<USER>/.aws
      - ${DEVBOX_HOST_USER_HOME:?DEVBOX_HOST_USER_HOME}/.ssh:/home/<USER>/.ssh
      - ${DEVBOX_HOST_USER_HOME:?DEVBOX_HOST_USER_HOME}/.docker:/home/<USER>/.docker
      - ${DEVBOX_HOST_USER_HOME:?DEVBOX_HOST_USER_HOME}/.gitconfig:/home/<USER>/.gitconfig
      - ${DEVBOX_HOST_USER_HOME:?DEVBOX_HOST_USER_HOME}/.devbox_bash_history:/home/<USER>/.bash_history
      - ${DEVBOX_HOST_USER_HOME:?DEVBOX_HOST_USER_HOME}/.devbox_bashrc:/home/<USER>/.bashrc
      - /var/run/docker.sock:/var/run/docker.sock


