#!/bin/bash

set -euo pipefail

ENV=".env"
echo "# Generated file. DO NOT EDIT" > $ENV

DEVBOX_USER_UID=""
DEVBOX_USER_GUID=""
DEVBOX_USER_NAME=""


if [[ "$OSTYPE" == "darwin"* ]]; then
    DEVBOX_USER_UID=1000
    DEVBOX_USER_GUID=1000
    DEVBOX_USER_NAME=ubuntu
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then  
    DEVBOX_USER_UID=`id -u`
    DEVBOX_USER_GUID=`id -g`
    DEVBOX_USER_NAME=`whoami`
else
  echo "Unsupport OS. Exiting ..."
  exit 1   
fi

DEVBOX_HOST_USER_WORKDIR=$(dirname `pwd`)/../../
DEVBOX_USER_WORKDIR=/home/"$DEVBOX_USER_NAME"/nirvana
DEVBOX_IMAGE="ubuntu:20.04"
DEVBOX_HOST_USER_HOME=$HOME

echo "DEVBOX_USER_UID=""$DEVBOX_USER_UID" >> $ENV
echo "DEVBOX_USER_GUID=""$DEVBOX_USER_GUID" >> $ENV
echo "DEVBOX_USER_NAME=""$DEVBOX_USER_NAME" >> $ENV
echo "DEVBOX_HOST_USER_WORKDIR=""$DEVBOX_HOST_USER_WORKDIR" >> $ENV
echo "DEVBOX_USER_WORKDIR=""$DEVBOX_USER_WORKDIR" >> $ENV
echo "DEVBOX_IMAGE=""$DEVBOX_IMAGE" >> $ENV
echo "DEVBOX_HOST_USER_HOME=""$DEVBOX_HOST_USER_HOME" >> $ENV

# Create bash files if they don't exist to preserve history across runs
touch $DEVBOX_HOST_USER_HOME/.devbox_bash_history
touch $DEVBOX_HOST_USER_HOME/.devbox_bashrc


