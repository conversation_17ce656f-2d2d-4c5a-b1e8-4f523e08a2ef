#!/usr/bin/env bash
set -euo pipefail

# Function to display usage and exit with an error code
print_usage_and_exit() {
    echo "Usage: $0 [quoting|standard]"
    exit 1
}

if [ $# -ne 1 ]; then
	print_usage_and_exit
fi

case "$1" in
  "standard")
	  export STORE_ID="82eae320-21ed-41cc-abf3-5952e435c3e5"
	  export SERVICE_NAME="job-processor-service"
	  export CLUSTER_NAME="default_internal_tools"
	  export IMAGE_TAG_NAME_TF="job_processor_tag"
	  export ECR_REPOSITORY_NAME="job-processor"
    ;;
  "quoting")
	  export STORE_ID="7c954af2-8c21-4c33-b982-ca667889cc33"
	  export SERVICE_NAME="quoting-job-processor-service"
	  export CLUSTER_NAME="default_app_cluster"
	  export IMAGE_TAG_NAME_TF="quoting_job_processor_tag"
	  export ECR_REPOSITORY_NAME="quoting-job-processor"
    ;;
  *)
    print_usage_and_exit
    ;;
esac


echo `date`: "Resetting ecs configuration after deployment"
aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --desired-count 1 --deployment-configuration "minimumHealthyPercent=0,maximumPercent=100" > /dev/null
echo `date`: "Done"
