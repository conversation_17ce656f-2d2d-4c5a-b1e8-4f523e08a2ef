from typing import Optional

BUILD_BUCKET_NAME_S3: str = "cloud.nirvanatech.com"

class SiteConfig(object):
    domain: str
    cloudfront_distribution_id: str

    def __init__(self, domain: str, cloudfront_distribution_id: str):
        super().__init__()
        self.domain = domain
        self.cloudfront_distribution_id = cloudfront_distribution_id

    @property
    def s3_build_path(self) -> str:
        return f'{BUILD_BUCKET_NAME_S3}/private/deployment/builds/frontend/{self.domain}'

    @property
    def s3_deploy_bucket(self) -> str:
        return f'{self.domain}-root'


# New "Sites" to be registered here
SITE_NAME_CONFIG_MAP = {
    "agents": SiteConfig(
        domain="agents.nirvanatech.com",
        cloudfront_distribution_id="ERDXYRGLN56QG",
    )
}


def get_site_config(site_name: str) -> Optional[SiteConfig]:
    return SITE_NAME_CONFIG_MAP.get(site_name, None)
