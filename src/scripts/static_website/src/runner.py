import os
import shutil
from typing import <PERSON><PERSON>, Optional, Type
from types import TracebackType

# A context manager. To be used in `with x as y` statements.
# This manages creation and deletion of temporary local files/folders
# used by CLI commands
class Runner(object):

    LOCAL_FILE_NAME: str = "compressed.tar.gz"

    base_path: str
    commit_id: str

    def __init__(self, base_path: str, commit_id: str) -> None:
        super().__init__()
        self.base_path = base_path
        self.commit_id = commit_id

    # setup local env
    def __enter__(self) -> Tuple[str, str]:
        _work_dir = self.work_dir
        if os.path.exists(_work_dir):
            shutil.rmtree(_work_dir)
        os.mkdir(_work_dir)
        _local_fpath = f'{_work_dir}/{self.LOCAL_FILE_NAME}'
        _touch(_local_fpath)
        return _work_dir, _local_fpath

    # destroy local env
    def __exit__(self,
                 exctype: Optional[Type[BaseException]],
                 excinst: Optional[BaseException], exctb: Optional[TracebackType]
                 ) -> bool:
        _work_dir = self.work_dir
        if os.path.exists(_work_dir):
            shutil.rmtree(_work_dir)
        return True

    @property
    def work_dir(self) -> str:
        return f'{self.base_path}/{self.commit_id}'


def _touch(path: str) -> None:
    with open(path, 'a'):
        os.utime(path, None)