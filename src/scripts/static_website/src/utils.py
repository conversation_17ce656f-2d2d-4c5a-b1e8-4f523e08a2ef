import os
import subprocess
import tarfile


def _s3_cp_file(src_path: str, dest_path: str) -> int:
    proc: subprocess.CompletedProcess = subprocess.run([
        "aws", "s3", "cp",
        src_path, dest_path
    ])
    return proc.returncode

def _make_tarfile(output_filepath: str, source_dir: str) -> None:
    with tarfile.open(output_filepath, "w:gz") as tar:
        tar.add(source_dir, arcname=os.path.basename(source_dir))


def _extract_tarfile(input_filepath: str, destination_dir: str) -> None:
    with tarfile.open(input_filepath, "r:gz") as tar:
        tar.extractall(destination_dir)
