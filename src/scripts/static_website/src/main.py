import os
import click
import subprocess
import tarfile
import enum

from .utils import _s3_cp_file, _extract_tarfile, _make_tarfile
from .sites import SiteConfig, get_site_config
from .runner import Runner


SubcommandBuild: str = "build"
SubcommandDeploy: str = "deploy"


# The main cli entrypoint
@click.group()
@click.pass_context
@click.option("--site-name", required=True, help="Site name")
def cli(ctx: click.Context, site_name: str):
    # TODO: Check if dependencies exist
    config: Optional[SiteConfig] = get_site_config(site_name)
    if not config:
        click.echo("Please specify valid site name")
        raise click.Abort()
    click.echo("Required dependencies:")
    _invoked_subcommand = ctx.invoked_subcommand
    if _invoked_subcommand == SubcommandBuild:
        click.echo("git, yarn, aws-cli")
    elif _invoked_subcommand == SubcommandDeploy:
        click.echo("aws-cli")
    ctx.obj = config


@cli.command(name=SubcommandBuild)
@click.option("--src", required=True, help="Path to source code directory")
@click.pass_obj
def build_and_push(obj: SiteConfig, src: str):
    commit_id: str = subprocess.check_output(
        ["git", "rev-parse", "HEAD"]).strip().decode()

    # build
    click.echo(f'Building: {commit_id}')
    build_proc: subprocess.CompletedProcess = subprocess.run(
        ["yarn", "--cwd", src, "build"])
    if build_proc.returncode != 0:
        click.echo("Failure occured during build, aborting!")
        raise click.Abort()

    work_dir: str
    local_file_path: str
    with Runner("/tmp/", commit_id) as (work_dir, local_file_path):
        # zip
        click.echo(f'Zipping {commit_id}')
        _make_tarfile(local_file_path, f'{src}/build/')

        # upload zip to s3
        click.echo(f'Uploading {local_file_path} to {obj.s3_build_path}')
        if _s3_cp_file(local_file_path, f's3://{obj.s3_build_path}/{commit_id}.tar.gz') != 0:
            click.echo("Failure occured during upload")
            raise click.Abort()

    click.echo(f'Upload commit id: {commit_id}')


@cli.command(name=SubcommandDeploy)
@click.argument("commit_id")
@click.pass_obj
def deploy(obj: SiteConfig, commit_id: str):
    click.echo(f'Attempting to deploy commit id: {commit_id}')
    work_dir: str
    local_file_path: str

    with Runner("/tmp/", commit_id) as (work_dir, local_file_path):
        # download
        click.echo(f'Downloading build to local')
        if _s3_cp_file(f's3://{obj.s3_build_path}/{commit_id}.tar.gz', local_file_path) != 0:
            click.echo("Failure occured in downloading build, aborting!")
            raise click.Abort()

        # extract
        click.echo(f'Extracting build tar')
        _extract_tarfile(
            f'{local_file_path}',
            f'{work_dir}')

        # remove build tar
        click.echo("Removing build tar")
        os.remove(f'{local_file_path}')

        # upload
        click.echo("Uploading to S3")
        upload_proc: subprocess.CompletedProcess = subprocess.run([
            "aws", "s3", "sync",
            f'/tmp/{commit_id}',
            f's3://{obj.s3_deploy_bucket}/',
        ])
        if upload_proc.returncode != 0:
            click.echo("Failure occured during upload")
            raise click.Abort()

    click.echo("Refreshing CDN")
    refresh_proc: subprocess.CompletedProcess = subprocess.run([
        "aws", "cloudfront", "create-invalidation",
        "--distribution-id", obj.cloudfront_distribution_id,
        "--paths", "/*"
    ])
    if refresh_proc.returncode != 0:
        click.echo("Failure refreshing CDN")
        raise click.Abort()


if __name__ == '__main__':
    cli()
