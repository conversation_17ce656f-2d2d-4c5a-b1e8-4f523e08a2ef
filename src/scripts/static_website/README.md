# Static Website

This package exposes functionality to build, push and deploy static websites
on AWS.

## System Dependencies

- `aws` CLI
- `yarn` package manager

## Install

Create a Python virtual environment (if it doesn't exist):

```sh
pyenv virtualenv 3.7.0 <env_name>
```

Activate the virtual environment:

```sh
pyenv activate <env_name>
```

Install the package:

```sh
pip install <path_to_package>
```

## Usage

Executing `static_website --help` should print out the Usage instructions on console.

The script expects a required option `--site-name` (to execute the script for), and
exposes two commands:

- `build`: Builds (and Pushes) the build tarball to build storage (s3 bucket).
  The name of build file is set to `{current_commit_id}.tar.gz`
- `deploy`: Deploys the tarball for the given `commit_id`.

### Example

Build:

```sh
static_website --site-name agents build --src nirvana/frontend
```

Deploy:

```sh
static_website --site-name agents deploy 6ea58391586fb6686790011d20b6687a095607c3
```
