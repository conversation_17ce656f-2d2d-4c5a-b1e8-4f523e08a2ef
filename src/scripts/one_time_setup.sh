# PSQL
# Create test user with test pass
# Ask for user name and pass
echo "WARNING!!"
echo "This is deprecated for MacOS. Please check ansible-setup/setup.sh"
echo.

while true; do
    read -p "Do you still wish to continue " yn
    case $yn in
        [Yy]* ) break;;
        [Nn]* ) break;;
        * ) echo "Please answer y or n.";;
    esac
done

echo "Please enter the username for psql"
read -r username
echo "Please enter your nirvana email for development superuser account"
read -r email
echo "Please enter the user password for psql and development superuser account"
read -r password

# For MacOS use $(whoami) user to connect to psql
if [[ $(uname -s) == 'Darwin' ]]; then
  psql -U $(whoami) -d postgres -c "CREATE USER $username WITH PASSWORD '$password' SUPERUSER;"
else
  psql -U postgres -d postgres -c "CREATE USER $username WITH PASSWORD '$password' SUPERUSER;"
fi

echo "Created user $username with password $password"

# Create nirvana and fmcsa db in psql
psql -U "$username" -d postgres -c "CREATE DATABASE nirvana"
psql -U "$username" -d postgres -c "CREATE DATABASE fmcsa"

# AWS configs
echo "Configuring aws, enter region us-east-2 when prompted"
aws configure

# Add Nirvana database config if file doesn't exist
NIRVANA_CONFIG_FILE=~/.nirvana.config.yaml
if [ ! -f  "$NIRVANA_CONFIG_FILE" ]; then
  touch $NIRVANA_CONFIG_FILE
  echo "databases:" > $NIRVANA_CONFIG_FILE
  {
    echo "  nirvana:"
    echo "    name: \"nirvana\""
    echo "    host: \"localhost\""
    echo "    port: 5432"
    echo "    username: \"$username\""
    echo "    password: \"$password\""
    echo "  fmcsa:"
    echo "    name: \"fmcsa\""
    echo "    host: \"localhost\""
    echo "    port: 5432"
    echo "    username: \"$username\""
    echo "    password: \"$password\""
  } >> $NIRVANA_CONFIG_FILE
  echo "Added Nirvana db configuration to ~/.nirvana.config.yaml"
else
  echo "Nirvana db configuration found in ~/.nirvana.config.yaml"
fi


# Run migrations
bazel run //nirvana/cmd/db -- migrate --yes-to-all up nirvana
bazel run //nirvana/cmd/db -- migrate --yes-to-all up fmcsa

# Create superuser account
echo "Please enter your first name"
read -r first_name
echo "Please enter your last name"
read -r last_name
bazel run //nirvana/db-api/cmd/auth_management_tool -- createSuperuser --user_email="$email" --user_password="$password" --user_first_name="$first_name" --user_last_name="$last_name"
echo "Created development superuser $email with password $password"
