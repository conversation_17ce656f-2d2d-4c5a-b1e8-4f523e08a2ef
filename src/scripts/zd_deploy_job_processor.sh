#!/usr/bin/env bash
set -euo pipefail

# This script is used to do zero downtime deployment
# for job_processors in prod
# TODO: This is very fragile and needs to be written in golang asap.

script_dir=$(cd -P -- "$(dirname -- "$0")" && pwd -P)
NIRVANA_ROOT=$script_dir/../..
PROTO_DIR=$NIRVANA_ROOT/src/proto/
APP_DEPLOYMENT_DIR=$NIRVANA_ROOT/src/deployment/app
cd $NIRVANA_ROOT # cd to nirvana root

# Function to display usage and exit with an error code
print_usage_and_exit() {
    echo "Usage: $0 [quoting|standard] <image_tag>"
    exit 1
}

if [ $# -ne 2 ]; then
	print_usage_and_exit
fi

case "$1" in
  "standard")
	  export STORE_ID="82eae320-21ed-41cc-abf3-5952e435c3e5"
	  export SERVICE_NAME="job-processor-service"
	  export CLUSTER_NAME="default_internal_tools"
	  export IMAGE_TAG_NAME_TF="job_processor_tag"
	  export ECR_REPOSITORY_NAME="job-processor"
	  export NEW_IMAGE_TAG=$2
    ;;
  "quoting")
	  export STORE_ID="7c954af2-8c21-4c33-b982-ca667889cc33"
	  export SERVICE_NAME="quoting-job-processor-service"
	  export CLUSTER_NAME="default_app_cluster"
	  export IMAGE_TAG_NAME_TF="quoting_job_processor_tag"
	  export ECR_REPOSITORY_NAME="quoting-job-processor"
	  export NEW_IMAGE_TAG=$2
    ;;
  *)
    print_usage_and_exit
    ;;
esac

echo STORE_ID=$STORE_ID
echo SERVICE_NAME=$SERVICE_NAME
echo CLUSTER_NAME=$CLUSTER_NAME
echo NEW_IMAGE_TAG=$NEW_IMAGE_TAG

# This acts as proxy for ensuring that there are no external terraform changes
# The reason this works is because `-detailed-exitcode` flag makes plan return
# exitCode 0 only if there are no changes in plan.
# Note that this is not foolproof since we don't persit the plan here
# This can fail in two scenarios:
# - After this check, someone goes in and changes something via AWS
#   console/merges another PR which has terraform change
# - Between this check and taking the tf_lock again couple of lines later
#   there could have been changes to terraform state
# Since this is meant to be temporary, we don't plan to solve any of these issues
echo `date`: "Checking that there are no changes in terraform other than the image tag"
cd $APP_DEPLOYMENT_DIR
terraform plan -detailed-exitcode > /dev/null
cd $NIRVANA_ROOT

nirvana_tf_lock

cleanup() {
# In the happy path we would already release lock at the end
# of terraform apply
  nirvana_tf_unlock || true
}
trap cleanup EXIT

# This variable stores `desired_count minPercent maxPercent`
echo `date`: "Checking if there is another zero downtime deployment in progress for this job processor"
CURRENT_ECS_CONFIGURATION=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME | jq '.services[0] | .desiredCount,  .deploymentConfiguration.minimumHealthyPercent, .deploymentConfiguration.maximumPercent ' | xargs)
# The is necessary and sufficient to detect an ongoing zero downtime
# deployment if tf_lock is held
test "$CURRENT_ECS_CONFIGURATION" = "1 0 100"

# See https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Deployment-at-Nirvana_suoF4#_luL8U
echo `date`: "Checking that image for image tag $NEW_IMAGE_TAG exists in ECR"
aws ecr describe-images --repository-name $ECR_REPOSITORY_NAME | jq -r '.imageDetails[] | select(.imageTags) | .imageTags[]' | grep $NEW_IMAGE_TAG

echo `date`: "Fetching address of currently running processor, also referred as old processor"
OLD_ADDRESS=$(grpcurl -plaintext -d '{"skipKnownProcessorCheck":true, "StoreId":"'$STORE_ID'"}'  -proto src/proto/jobber/jobber.proto jobber-monitor.default.app.nirvana.internal:56666 jobber.Monitor/RunningProcessors | jq -r '.Infos[0].address')
echo `date`: "Old processor address: $OLD_ADDRESS"

echo `date`: "Updating ecs configuration for zero downtime deployment"
aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --deployment-configuration "minimumHealthyPercent=100,maximumPercent=101" > /dev/null > /dev/null

echo `date`: "Deploying new processor, but it will not be started yet"
cd $APP_DEPLOYMENT_DIR
terraform apply -var $IMAGE_TAG_NAME_TF=$NEW_IMAGE_TAG
cd $NIRVANA_ROOT

echo `date`: "Changing configuration so that both new and old processors can run"
aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --desired-count 2 > /dev/null

echo `date`: "Waiting till there are two running processors"
while true ; do
	RUNNING_COUNT=$(grpcurl -plaintext -d '{"skipKnownProcessorCheck":true, "StoreId":"'$STORE_ID'"}' -proto src/proto/jobber/jobber.proto jobber-monitor.default.app.nirvana.internal:56666 jobber.Monitor/RunningProcessors | jq -r '.Infos | length')
	echo `date`: "Currently running processors: $RUNNING_COUNT"
    if [ "$RUNNING_COUNT" -eq "2" ]; then
        break
    fi
	echo `date`: "Sleeping for 20 seconds before checking again"
	sleep 20
done

echo `date`: "Draining the old processor"
grpcurl -plaintext -d '{"NonBlocking":true}' -proto src/proto/jobber/jobber.proto ${OLD_ADDRESS} jobber.ProcessorInternal/DrainAndStopProcessor

echo `date`: "Waiting for the old processor to stop"
while true ; do
	STATUS=$(grpcurl -plaintext  -proto src/proto/jobber/jobber.proto ${OLD_ADDRESS} jobber.ProcessorInternal/GetCurrentStatus | jq -r '.ProcessorStatus')
	echo "Current status of old processor: $STATUS"
    if [ "$STATUS" = "STOPPED" ]; then
        break
    fi
	echo `date`: "Sleeping for 20 seconds before checking again"
	sleep 20
done

echo `date`: "Resetting ecs configuration after deployment"
aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --desired-count 1 --deployment-configuration "minimumHealthyPercent=0,maximumPercent=100" > /dev/null
echo `date`: "Done"
