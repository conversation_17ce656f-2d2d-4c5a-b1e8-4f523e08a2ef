#!/usr/bin/env bash

set -euo pipefail

script_dir=$(cd -P -- "$(dirname -- "$0")" && pwd -P)
cd "$script_dir"

# This is a terrible terrible script to trigger our push-image & deploy service GHA.
# The reason this script exists is because GH limits number of possible inputs to an action
# to 10. This means we can not have a UI prompt for more than 10 services.
# This is a script to hack around the limitation by encoding all service names in a single
# string input to the action.
# The script reads names of services from the terraform file.
# It prompts user to select one or more among those and then triggers the GHA for those.

# break if gh CLI not installed
gh --version >/dev/null || {
    echo "gh CLI not installed, required for triggering deploy workflow"
    exit 1
}

# first get a list of possible service names by scanning terraform variables
# shellcheck disable=SC2207
bazel build //nirvana/cmd/deploy_kit:deploy_kit
possible_services=( $(bazel run //nirvana/cmd/deploy_kit:deploy_kit -- list-apps \
    --catalog "${script_dir}/../infra/deployment/catalog.yml" \
    --env prod \
    --workflow go_push_images) )

# if arguments provided assume them as service names, otherwise prompt user to select
if [[ $# -gt 0 ]]; then
    selected_services=("$@")
else
    # terminate if fzf not installed
    fzf --version >/dev/null || {
        echo "fzf not installed, https://github.com/junegunn/fzf"
        echo "OSX users: brew install fzf"
        exit 1
    }
    # shellcheck disable=SC2207
    selected_services=($(
        # pipe these through fzf to filter
        printf '%s\n' "${possible_services[@]}" | sort | \
        fzf --multi --reverse --prompt 'Press TAB to select multiple > '
    ))
fi

# exit if nothing selected
[[ ${#selected_services[@]} -eq 0 ]] && exit 1

# perform a validation on selected services
for selected_service in "${selected_services[@]}"
do
    [[ " ${possible_services[*]} " =~ " $selected_service " ]] || {
        echo "${selected_service} is not a valid service"
        echo "Try among these: "
        printf '\t%s\n' "${possible_services[@]}"
        exit 1
    }
done

echo "Running GHA go_push_images.yml for ${selected_services[*]}"
branch=$(git branch --show-current)
echo "Services will be deployed from $branch, press ENTER to continue" && read -r

# copied from https://stackoverflow.com/a/26809278
function json_array {
  echo -n '['
  while [ $# -gt 0 ]; do
    x=${1//\\/\\\\}
    echo -n \""${x//\"/\\\"}"\"
    [ $# -gt 1 ] && echo -n ', '
    shift
  done
  echo ']'
}

selected_services_json=$( 
    json_array "${selected_services[@]}"
)

gh workflow run go_push_images.yml \
    -f service="${selected_services_json}" \
    -r "$branch"
sleep 10
gh run list -w go_push_images.yml \
    --limit 1 \
    --json headBranch,url,startedAt \
    --user "$(gh api user | jq -r .login)"
