#!/bin/bash
# This script is intended to run on github workflows
# To run locally, ensure that there are no unstaged or uncommitted changes
# Usage: ./bazel_diff.sh <git-sha-1> <git-sha-2>
# WARNING: After the script is run, the repo will be in a detached HEAD state.
# Specifically, the repo will be at <git-sha-2>
# Make sure to checkout your branch after running this script

set -euo pipefail

script_dir=$(cd -P -- "$(dirname -- "$0")" && pwd -P)

# Path to Bazel WORKSPACE directory
workspace_path=$(dirname "$script_dir")
# Starting Revision SHA
previous_revision=$1
# Final Revision SHA
final_revision=$2
cd "$workspace_path"

# Create temporary files
starting_hashes_json=$(mktemp /tmp/starting_hashes.json.XXXXXXXX)
final_hashes_json=$(mktemp /tmp/final_hashes.json.XXXXXXXX)
bazel_diff=$(mktemp /tmp/bazel_diff.XXXXXXXX)

# Download bazel-diff binary
bazel run //:bazel-diff --script_path="$bazel_diff"

# Checkout previous_revision
git checkout "$previous_revision" --quiet
# Generate hashes
$bazel_diff generate-hashes -w "$workspace_path" -b bazel "$starting_hashes_json"

# Checkout final_revision
git checkout "$final_revision" --quiet
# Generate hashes
$bazel_diff generate-hashes -w "$workspace_path" -b bazel "$final_hashes_json"

# Compare hashes from both revisions to generate impacted targets
$bazel_diff get-impacted-targets -sh "$starting_hashes_json" -fh "$final_hashes_json" -o >(cat)
