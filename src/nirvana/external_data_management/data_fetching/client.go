package data_fetching

import (
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/common-go/grpc/libgrpc"
	"nirvanatech.com/nirvana/common-go/grpc/middleware"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/unwrap_reply_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/wrap_reply_interceptor"
)

const (
	serverAddress = "external_data_management"
)

type FetcherClientFactory func(interceptors ...grpc.UnaryClientInterceptor) (FetcherClient, func() error, error)

func newClientFactory(
	lc fx.Lifecycle,
	metricsClient statsd.Statter,
	fetcherServer FetcherServer,
) (FetcherClientFactory, error) {
	listener := libgrpc.NewPipeListenerWithLifeCycle(lc)

	grpcServer, err := libgrpc.NewServer(
		lc,
		listener,
		middleware.DefaultServerUnaryInterceptors(metricsClient)...,
	)
	if err != nil {
		return nil, err
	}

	// Note that we only register the service if the client factory is required via fx.
	// If no component needs the factory, we assume that they don't need the client either,
	// and in consequence, the service is also not required.
	RegisterFetcherServer(grpcServer, fetcherServer)

	return func(interceptors ...grpc.UnaryClientInterceptor) (FetcherClient, func() error, error) {
		interceptors = buildInterceptorsForFetcherClient(interceptors)

		clientConn, err := libgrpc.NewClientConn(serverAddress, listener, interceptors...)
		if err != nil {
			return nil, nil, err
		}

		return NewFetcherClient(clientConn), func() error { return clientConn.Close() }, nil
	}, nil
}

// buildInterceptorsForFetcherClient combined interceptors in a
// very specific order to ensure the correct behavior of the client.
//
// Be very careful when changing the order of the interceptors.
func buildInterceptorsForFetcherClient(
	customInterceptors []grpc.UnaryClientInterceptor,
) []grpc.UnaryClientInterceptor {
	interceptors := make([]grpc.UnaryClientInterceptor, 0)

	interceptors = append(interceptors, wrap_reply_interceptor.New())

	interceptors = append(interceptors, customInterceptors...)

	interceptors = append(interceptors, unwrap_reply_interceptor.New())

	return interceptors
}
