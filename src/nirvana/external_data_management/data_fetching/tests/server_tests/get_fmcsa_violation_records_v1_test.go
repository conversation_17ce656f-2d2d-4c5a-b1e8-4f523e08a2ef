package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getFMCSAViolationRecordsV1TestEnv struct {
	fx.In

	FetcherClientFactory data_fetching.FetcherClientFactory
}

type getFMCSAViolationRecordsV1TestSuite struct {
	suite.Suite
	ctx              context.Context
	env              getFMCSAViolationRecordsV1TestEnv
	fxapp            *fxtest.App
	mockFMCSAWrapper *fmcsa.MockDataWrapper
}

func TestGetFMCSAViolationRecordsV1(t *testing.T) {
	suite.Run(t, new(getFMCSAViolationRecordsV1TestSuite))
}

func (s *getFMCSAViolationRecordsV1TestSuite) SetupSuite() {
	s.ctx = context.Background()

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockFMCSAWrapper = fmcsa.NewMockDataWrapper(ctrl)

	newMockFMCSAWrapper := func() fmcsa.DataWrapper {
		return s.mockFMCSAWrapper
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockFMCSAWrapper)),
	)
}

func (s *getFMCSAViolationRecordsV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getFMCSAViolationRecordsV1TestSuite) Test_Simple() {
	dotNumber := int32(1460168)

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	mockPullerCompletionDates := builders.BuildDefaultPullerCompletionDatesForTest()
	s.mockFMCSAWrapper.
		EXPECT().
		FetchPullerCompletionDates(gomock.Any(), gomock.Any()).
		Return(mockPullerCompletionDates, nil).
		Times(1)

	mockViolationRecords := builders.BuildDefaultViolationRecordsForTest()
	s.mockFMCSAWrapper.
		EXPECT().
		FetchViolationRecords(gomock.Any(), gomock.Any(), int64(dotNumber)).
		Return(mockViolationRecords, nil).
		Times(1)

	resp, err := client.GetFMCSAViolationRecordsV1(
		s.ctx,
		&data_fetching.FMCSAViolationRecordsRequestV1{
			DotNumber:     dotNumber,
			EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 15).ToTime()),
		},
	)
	s.Require().NoError(err)
	expectedViolationRecords := builders.BuildDefaultFMCSAViolationRecordsForTest()
	s.Require().True(proto.Equal(expectedViolationRecords, resp))
}
