load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "server_tests_test",
    srcs = [
        "get_bipd_active_pending_insurance_v1_test.go",
        "get_bipd_insurance_history_v1_test.go",
        "get_fmcsa_census_info_v1_test.go",
        "get_fmcsa_inspection_records_v1_test.go",
        "get_fmcsa_violation_records_v1_test.go",
        "get_latest_objective_grade_v1_test.go",
        "get_latest_rating_tier_records_dump_date_v1_test.go",
        "get_mvr_attract_score_v1_test.go",
        "get_mvr_report_v1_test.go",
        "get_national_credit_file_v1_test.go",
        "get_nirvana_policies_v1_test.go",
        "get_rating_tier_record_v1_test.go",
        "get_rating_tier_records_for_dump_date_v1_test.go",
        "get_vin_details_v1_test.go",
    ],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/datagov/active_pending_insurance",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/mock",
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_fetching/tests/server_tests/builders",
        "//nirvana/external_data_management/interceptors_management",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/external_data_management/internal/sentry_inputs",
        "//nirvana/external_data_management/mvr",
        "//nirvana/external_data_management/nhtsa",
        "//nirvana/external_data_management/nhtsa/enums",
        "//nirvana/external_data_management/store_management",
        "//nirvana/fmcsa/lni",
        "//nirvana/infra/fx/testloader",
        "//nirvana/safety/scores/objective_grade/enums",
        "//nirvana/safety/scores/objective_grade/manager",
        "//nirvana/servers/vehicles:proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
