package server_tests

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/fmcsa/lni"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getInsuranceHistoryV1TestEnv struct {
	fx.In

	FetcherClientFactory            data_fetching.FetcherClientFactory
	StoreManager                    store_management.StoreManager
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
}

type getInsuranceHistoryV1TestSuite struct {
	suite.Suite
	ctx           context.Context
	env           getInsuranceHistoryV1TestEnv
	fxapp         *fxtest.App
	mockLNIClient *lni.MockClient
}

func TestGetInsuranceHistoryV1(t *testing.T) {
	suite.Run(t, new(getInsuranceHistoryV1TestSuite))
}

func (s *getInsuranceHistoryV1TestSuite) SetupSuite() {
	s.ctx = context.Background()

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockLNIClient = lni.NewMockClient(ctrl)

	newMockLNIClient := func() lni.Client {
		return s.mockLNIClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockLNIClient)),
	)
}

func (s *getInsuranceHistoryV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getInsuranceHistoryV1TestSuite) Test_Simple() {
	dotNumber := int64(123456)
	insuranceId := uuid.New()
	mockInsurance := builders.BuildDefaultRawBIPDInsuranceHistoryForTest(insuranceId, int(dotNumber))

	s.mockLNIClient.
		EXPECT().
		GetBIPDInsuranceHistory(gomock.Any(), dotNumber, nil).
		Return(mockInsurance, nil).
		Times(1)

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	expectedInsurance := builders.BuildDefaultBIPDInsuranceHistoryV1ForTest(insuranceId, dotNumber)
	response, err := client.GetBIPDInsuranceHistoryV1(
		s.ctx,
		&data_fetching.BIPDInsuranceHistoryRequestV1{DotNumber: dotNumber},
	)
	s.Require().NoError(err)
	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*expectedInsurance, *response)
}

func (s *getInsuranceHistoryV1TestSuite) Test_WithStoreInterceptor() {
	dotNumber := int64(123456)
	insuranceId := uuid.New()
	mockInsurance := builders.BuildDefaultRawBIPDInsuranceHistoryForTest(insuranceId, int(dotNumber))

	s.mockLNIClient.
		EXPECT().
		GetBIPDInsuranceHistory(gomock.Any(), dotNumber, nil).
		Return(mockInsurance, nil).
		Times(1)

	contextId := uuid.New()
	request := &data_fetching.BIPDInsuranceHistoryRequestV1{DotNumber: dotNumber}
	expectedInsurance := builders.BuildDefaultBIPDInsuranceHistoryV1ForTest(insuranceId, dotNumber)

	interceptors := interceptors_management.NewWritableStoreFirstInterceptors(
		s.env.ReadFromStoreInterceptorFactory,
		s.env.WriteToStoreInterceptorFactory,
		contextId,
	)

	client, closer, err := s.env.FetcherClientFactory(interceptors...)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	exists, err := s.env.StoreManager.Exists(s.ctx, contextId, request)
	s.Require().NoError(err)
	s.Require().False(exists)

	response, err := client.GetBIPDInsuranceHistoryV1(s.ctx, request)
	s.Require().NoError(err)
	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*expectedInsurance, *response)

	exists, err = s.env.StoreManager.Exists(s.ctx, contextId, request)
	s.Require().NoError(err)
	s.Require().True(exists)

	response, err = client.GetBIPDInsuranceHistoryV1(s.ctx, request)
	s.Require().NoError(err)
	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*expectedInsurance, *response)
}
