package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getFMCSAInspectionRecordsV1TestEnv struct {
	fx.In

	FetcherClientFactory data_fetching.FetcherClientFactory
}

type getFMCSAInspectionRecordsV1TestSuite struct {
	suite.Suite
	ctx              context.Context
	env              getFMCSAInspectionRecordsV1TestEnv
	fxapp            *fxtest.App
	mockFMCSAWrapper *fmcsa.MockDataWrapper
}

func TestGetFMCSAInspectionRecordsV1(t *testing.T) {
	suite.Run(t, new(getFMCSAInspectionRecordsV1TestSuite))
}

func (s *getFMCSAInspectionRecordsV1TestSuite) SetupSuite() {
	s.ctx = context.Background()

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockFMCSAWrapper = fmcsa.NewMockDataWrapper(ctrl)

	newMockFMCSAWrapper := func() fmcsa.DataWrapper {
		return s.mockFMCSAWrapper
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockFMCSAWrapper)),
	)
}

func (s *getFMCSAInspectionRecordsV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getFMCSAInspectionRecordsV1TestSuite) Test_Simple() {
	dotNumber := 1460168
	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	mockPullerCompletionDates := builders.BuildDefaultPullerCompletionDatesForTest()
	s.mockFMCSAWrapper.
		EXPECT().
		FetchPullerCompletionDates(gomock.Any(), gomock.Any()).
		Return(mockPullerCompletionDates, nil).
		Times(1)

	mockInspectionRecords := builders.BuildDefaultInspectionRecordsForTest()
	s.mockFMCSAWrapper.
		EXPECT().
		FetchInspectionRecords(gomock.Any(), gomock.Any(), int64(dotNumber)).
		Return(mockInspectionRecords, nil).
		Times(1)

	resp, err := client.GetFMCSAInspectionRecordsV1(
		s.ctx,
		&data_fetching.FMCSAInspectionRecordsRequestV1{
			DotNumber:     int32(dotNumber),
			EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 15).ToTime()),
		},
	)
	s.Require().NoError(err)
	expectedInspectionRecords := builders.BuildDefaultFMCSAInspectionRecordsV1ForTest()
	s.Require().True(proto.Equal(expectedInspectionRecords, resp))
}
