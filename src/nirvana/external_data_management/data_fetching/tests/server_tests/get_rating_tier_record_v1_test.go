package server_tests

import (
	"context"
	"testing"
	"time"

	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/internal/sentry_inputs"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getRatingTierRecordV1TestEnv struct {
	fx.In

	FetcherClientFactory data_fetching.FetcherClientFactory
}

type getRatingTierRecordV1TestSuite struct {
	suite.Suite

	ctx                        context.Context
	env                        getRatingTierRecordV1TestEnv
	fxapp                      *fxtest.App
	mockRatingTierRecordClient *sentry_inputs.MockClient
}

func TestGetRatingTierRecordV1(t *testing.T) {
	suite.Run(t, new(getRatingTierRecordV1TestSuite))
}

func (s *getRatingTierRecordV1TestSuite) SetupSuite() {
	ctrl, ctx := gomock.WithContext(context.TODO(), s.T())
	s.ctx = ctx
	s.mockRatingTierRecordClient = sentry_inputs.NewMockClient(ctrl)

	newMockRatingTierRecordClient := func() sentry_inputs.Client {
		return s.mockRatingTierRecordClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockRatingTierRecordClient)),
	)
}

func (s *getRatingTierRecordV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getRatingTierRecordV1TestSuite) TestGetRatingTierRecordV1_Simple() {
	dotNumber := int64(1234567)
	now := time.Now()

	request := &data_fetching.RatingTierRecordRequestV1{
		DotNumber:  dotNumber,
		DumpDate:   timestamppb.New(now),
		RecordDate: timestamppb.New(now),
	}
	mockResponse := builders.BuildDefaultRawRatingTierRecordV1ForTest(now)

	s.mockRatingTierRecordClient.EXPECT().
		GetSentryInputsForDOTAndDates(gomock.Any(), request.DotNumber, now.UTC(), now.UTC()).
		Return(mockResponse, nil).
		Times(1)

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	expectedRatingTier := builders.BuildDefaultRatingTierRecordV1ForTest(now)
	response, err := client.GetRatingTierRecordV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().NotNil(response)

	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*expectedRatingTier, *response)
}
