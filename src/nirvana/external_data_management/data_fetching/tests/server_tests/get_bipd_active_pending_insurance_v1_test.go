package server_tests

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov/active_pending_insurance"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/fmcsa/lni"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getBIPDActiveOrPendingInsuranceV1TestEnv struct {
	fx.In

	FetcherClientFactory            data_fetching.FetcherClientFactory
	StoreManager                    store_management.StoreManager
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
}

type getBIPDActiveOrPendingInsuranceV1TestSuite struct {
	suite.Suite
	ctx   context.Context
	env   getBIPDActiveOrPendingInsuranceV1TestEnv
	fxapp *fxtest.App

	mockLNIClient *lni.MockClient
}

func TestGetBIPDActiveOrPendingInsuranceV1(t *testing.T) {
	suite.Run(t, new(getBIPDActiveOrPendingInsuranceV1TestSuite))
}

func (s *getBIPDActiveOrPendingInsuranceV1TestSuite) SetupSuite() {
	s.ctx = context.Background()

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockLNIClient = lni.NewMockClient(ctrl)

	newMockLNIClient := func() lni.Client {
		return s.mockLNIClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockLNIClient)),
	)
}

func (s *getBIPDActiveOrPendingInsuranceV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getBIPDActiveOrPendingInsuranceV1TestSuite) Test_Simple() {
	dotNumber := int64(123456)
	insuranceId1 := uuid.New()
	mockInsurance1 := builders.BuildDefaultRawBIPDActiveOrPendingInsuranceForTest(insuranceId1, int(dotNumber))
	insuranceId2 := uuid.New()
	mockInsurance2 := builders.BuildDefaultRawBIPDActiveOrPendingInsuranceForTest(insuranceId2, int(dotNumber))
	insurance := []active_pending_insurance.ActPendInsRecord{*mockInsurance1, *mockInsurance2}

	s.mockLNIClient.
		EXPECT().
		GetBIPDActiveOrPendingInsurance(gomock.Any(), dotNumber, false).
		Return(insurance, nil).
		Times(1)

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	expectedInsurance := builders.BuildBIPDActiveOrPendingInsuranceV1ForTest(
		dotNumber,
		[]uuid.UUID{insuranceId1, insuranceId2},
	)
	response, err := client.GetBIPDActiveOrPendingInsuranceV1(
		s.ctx,
		&data_fetching.BIPDActiveOrPendingInsuranceRequestV1{DotNumber: dotNumber},
	)
	s.Require().NoError(err)
	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*expectedInsurance, *response)
}

func (s *getBIPDActiveOrPendingInsuranceV1TestSuite) Test_WithStoreInterceptor() {
	dotNumber := int64(123456)
	insuranceId1 := uuid.New()
	mockInsurance1 := builders.BuildDefaultRawBIPDActiveOrPendingInsuranceForTest(insuranceId1, int(dotNumber))
	insuranceId2 := uuid.New()
	mockInsurance2 := builders.BuildDefaultRawBIPDActiveOrPendingInsuranceForTest(insuranceId2, int(dotNumber))
	insurance := []active_pending_insurance.ActPendInsRecord{*mockInsurance1, *mockInsurance2}

	s.mockLNIClient.
		EXPECT().
		GetBIPDActiveOrPendingInsurance(gomock.Any(), dotNumber, false).
		Return(insurance, nil).
		Times(1)

	contextID := uuid.New()
	request := &data_fetching.BIPDActiveOrPendingInsuranceRequestV1{DotNumber: dotNumber}
	expectedInsurance := builders.BuildBIPDActiveOrPendingInsuranceV1ForTest(
		dotNumber,
		[]uuid.UUID{insuranceId1, insuranceId2},
	)

	interceptors := interceptors_management.NewWritableStoreFirstInterceptors(
		s.env.ReadFromStoreInterceptorFactory,
		s.env.WriteToStoreInterceptorFactory,
		contextID,
	)

	client, closer, err := s.env.FetcherClientFactory(interceptors...)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	exists, err := s.env.StoreManager.Exists(s.ctx, contextID, request)
	s.Require().NoError(err)
	s.Require().False(exists)

	response, err := client.GetBIPDActiveOrPendingInsuranceV1(s.ctx, request)
	s.Require().NoError(err)
	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*expectedInsurance, *response)

	exists, err = s.env.StoreManager.Exists(s.ctx, contextID, request)
	s.Require().NoError(err)
	s.Require().True(exists)

	response, err = client.GetBIPDActiveOrPendingInsuranceV1(s.ctx, request)
	s.Require().NoError(err)
	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*expectedInsurance, *response)
}
