package builders

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/safety/scores/objective_grade/enums"
	"nirvanatech.com/nirvana/safety/scores/objective_grade/models"
)

func BuildDefaultRawObjectiveGradeV1ForTest(dotNumber int64, grade enums.ObjectiveGrade, now time.Time) *models.ObjectiveGrade {
	return &models.ObjectiveGrade{
		DotNumber: dotNumber,
		Grade:     grade,
		GradeDate: now,
		Inputs: models.Inputs{
			InspectionCount:    5,
			WeightedBasicScore: 1.0,
		},
	}
}

func BuildDefaultObjectiveGradeV1ForTest(
	dotNumber int64,
	grade data_fetching.ObjectiveGradeScoreV1,
	now time.Time,
) *data_fetching.ObjectiveGradeV1 {
	return &data_fetching.ObjectiveGradeV1{
		DotNumber: dotNumber,
		Date:      timestamppb.New(now),
		Score:     grade,
	}
}
