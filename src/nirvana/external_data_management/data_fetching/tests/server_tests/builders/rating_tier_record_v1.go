package builders

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/fmcsa/models"
)

func BuildDefaultRawRatingTierRecordV1ForTest(now time.Time) *models.SentryInputs {
	return &models.SentryInputs{
		Date:                       pointer_utils.ToPointer(now),
		InspectionIndicator:        "BOTH",
		LargeMachineryIndicator:    true,
		CrashFrequency:             pointer_utils.ToPointer(0.1),
		PowerUnits:                 pointer_utils.ToPointer(int64(25)),
		AverageMiles:               pointer_utils.ToPointer(15_134.2),
		AverageCombinedGrossWeight: pointer_utils.ToPointer(4_329.2),
		MaintenanceViolationsRatio: pointer_utils.ToPointer(0.01),
		UnsafeViolationRatio:       pointer_utils.ToPointer(0.1),
		VehicleInspectionsRatio:    pointer_utils.ToPointer(0.9),
		DateStrInternal:            now.Format("2006-01-02"),
	}
}

func BuildDefaultRatingTierRecordV1ForTest(now time.Time) *data_fetching.RatingTierRecordV1 {
	return &data_fetching.RatingTierRecordV1{
		Date:                       timestamppb.New(now),
		InspectionIndicator:        "BOTH",
		LargeMachineryIndicator:    true,
		CrashFrequency:             pointer_utils.ToPointer(0.1),
		PowerUnits:                 pointer_utils.ToPointer(int64(25)),
		AverageMiles:               pointer_utils.ToPointer(15_134.2),
		AverageCombinedGrossWeight: pointer_utils.ToPointer(4_329.2),
		MaintenanceViolationsRatio: pointer_utils.ToPointer(0.01),
		UnsafeViolationRatio:       pointer_utils.ToPointer(0.1),
		VehicleInspectionRatio:     pointer_utils.ToPointer(0.9),
	}
}
