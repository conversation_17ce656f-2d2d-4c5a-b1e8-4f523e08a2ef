package builders

import (
	"time"

	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/fmcsa/models"
)

func BuildDefaultInspectionRecordsForTest() []models.InspectionRecord {
	return []models.InspectionRecord{
		{
			RowID:                uuid.MustParse("f4ae5018-75fb-5f5d-b655-e2552e5c1b42"),
			PublishedDate:        time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC),
			InspectionID:         71080922,
			DOTNumber:            1460168,
			ReportNumber:         "**********",
			ReportState:          "KY",
			InspectionDate:       time.Date(2020, 10, 21, 0, 0, 0, 0, time.UTC),
			InspectionLevel:      models.Full,
			TimeWeight:           1,
			DriverOOSTotal:       0,
			VehicleOOSTotal:      0,
			TotalOOSViolations:   0,
			HazmatOOSTotal:       0,
			HazmatViolationsSent: 0,
			HazmatPlacardReq:     false,
			//nolint:exhaustive
			BASICCategories: map[basic.Category]bool{
				basic.UnsafeDriving:               true,
				basic.HOSCompliance:               true,
				basic.VehicleMaintenance:          true,
				basic.ControlledSubstancesAlcohol: true,
				basic.HMCompliance:                false,
				basic.DriverFitness:               true,
			},
			//nolint:exhaustive
			BASICViolations: map[basic.Category]int{
				basic.UnsafeDriving:               0,
				basic.HOSCompliance:               0,
				basic.VehicleMaintenance:          0,
				basic.ControlledSubstancesAlcohol: 0,
				basic.HMCompliance:                0,
				basic.DriverFitness:               0,
			},
			TotalBASICViols: 0,
			PublicVINs:      []string{"4V4NC9TJ97N447739", "1GRAP062XKT141397"},
			VINs:            []string{"4V4NC9TJ97N447739", "1GRAP062XKT141397"},
			Vehicles: []models.InspectionVehicle{
				{
					VIN:     "4V4NC9TJ97N447739",
					Company: "0909",
					Make:    "VOLV",
					Type:    "11",
				},
				{
					VIN:     "1GRAP062XKT141397",
					Company: "789",
					Make:    "GDAN",
					Type:    "9",
				},
			},
		},
		{
			RowID:                uuid.MustParse("fe0959d2-0cd6-5381-975c-5e4803a9c569"),
			PublishedDate:        time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC),
			InspectionID:         71262551,
			DOTNumber:            1460168,
			ReportNumber:         "W133003267",
			ReportState:          "MO",
			InspectionDate:       time.Date(2020, 11, 12, 0, 0, 0, 0, time.UTC),
			InspectionLevel:      models.WalkAround,
			TimeWeight:           1,
			DriverOOSTotal:       0,
			VehicleOOSTotal:      0,
			TotalOOSViolations:   0,
			HazmatOOSTotal:       0,
			HazmatViolationsSent: 0,
			HazmatPlacardReq:     false,
			//nolint:exhaustive
			BASICCategories: map[basic.Category]bool{
				basic.UnsafeDriving:               true,
				basic.HOSCompliance:               true,
				basic.VehicleMaintenance:          true,
				basic.ControlledSubstancesAlcohol: true,
				basic.HMCompliance:                false,
				basic.DriverFitness:               true,
			},
			//nolint:exhaustive
			BASICViolations: map[basic.Category]int{
				basic.UnsafeDriving:               0,
				basic.HOSCompliance:               0,
				basic.VehicleMaintenance:          4,
				basic.ControlledSubstancesAlcohol: 0,
				basic.HMCompliance:                0,
				basic.DriverFitness:               0,
			},
			TotalBASICViols: 4,
			PublicVINs:      []string{"3AKJGLDRXHSHD8047", "1GR1A0626MB230510"},
			VINs:            []string{"3AKJGLDRXHSHD8047", "1GR1A0626MB230510"},
			Vehicles: []models.InspectionVehicle{
				{
					VIN:     "3AKJGLDRXHSHD8047",
					Company: "11017",
					Make:    "FRHT",
					Type:    "11",
				},
				{
					VIN:     "1GR1A0626MB230510",
					Company: "306",
					Make:    "GDAN",
					Type:    "9",
				},
			},
		},
		{
			RowID:                uuid.MustParse("dd244277-c3c6-5ad6-9004-7ac48900df64"),
			PublishedDate:        time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC),
			InspectionID:         71284418,
			DOTNumber:            1460168,
			ReportNumber:         "000TT6T92T",
			ReportState:          "IA",
			InspectionDate:       time.Date(2020, 11, 16, 0, 0, 0, 0, time.UTC),
			InspectionLevel:      models.DriverOnly,
			TimeWeight:           1,
			DriverOOSTotal:       0,
			VehicleOOSTotal:      0,
			TotalOOSViolations:   0,
			HazmatOOSTotal:       0,
			HazmatViolationsSent: 0,
			HazmatPlacardReq:     false,
			//nolint:exhaustive
			BASICCategories: map[basic.Category]bool{
				basic.UnsafeDriving:               true,
				basic.HOSCompliance:               true,
				basic.VehicleMaintenance:          false,
				basic.ControlledSubstancesAlcohol: true,
				basic.HMCompliance:                false,
				basic.DriverFitness:               true,
			},
			//nolint:exhaustive
			BASICViolations: map[basic.Category]int{
				basic.UnsafeDriving:               1,
				basic.HOSCompliance:               0,
				basic.VehicleMaintenance:          0,
				basic.ControlledSubstancesAlcohol: 0,
				basic.HMCompliance:                0,
				basic.DriverFitness:               0,
			},
			TotalBASICViols: 1,
			PublicVINs:      []string{"3AKJGLDR6HSHP2572", "1GR1P0623MT231011"},
			VINs:            []string{"3AKJGLDR6HSHP2572", "1GR1P0623MT231011"},
			Vehicles: []models.InspectionVehicle{
				{
					VIN:     "3AKJGLDR6HSHP2572",
					Company: "10417",
					Make:    "FRHT",
					Type:    "11",
				},
				{
					VIN:     "1GR1P0623MT231011",
					Company: "417",
					Make:    "GDAN",
					Type:    "9",
				},
			},
		},
		{
			RowID:                uuid.MustParse("62394fb2-4761-5aa5-affb-a220e557bdee"),
			PublishedDate:        time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC),
			InspectionID:         71331790,
			DOTNumber:            1460168,
			ReportNumber:         "HOLLM02002",
			ReportState:          "MI",
			InspectionDate:       time.Date(2020, 11, 19, 0, 0, 0, 0, time.UTC),
			InspectionLevel:      models.DriverOnly,
			TimeWeight:           1,
			DriverOOSTotal:       0,
			VehicleOOSTotal:      0,
			TotalOOSViolations:   0,
			HazmatOOSTotal:       0,
			HazmatViolationsSent: 0,
			HazmatPlacardReq:     false,
			//nolint:exhaustive
			BASICCategories: map[basic.Category]bool{
				basic.UnsafeDriving:               true,
				basic.HOSCompliance:               true,
				basic.VehicleMaintenance:          false,
				basic.ControlledSubstancesAlcohol: true,
				basic.HMCompliance:                false,
				basic.DriverFitness:               true,
			},
			//nolint:exhaustive
			BASICViolations: map[basic.Category]int{
				basic.UnsafeDriving:               1,
				basic.HOSCompliance:               0,
				basic.VehicleMaintenance:          0,
				basic.ControlledSubstancesAlcohol: 0,
				basic.HMCompliance:                0,
				basic.DriverFitness:               0,
			},
			TotalBASICViols: 1,
			PublicVINs:      []string{"3AKJGLDRXHSHP2574", "1GR1P0627MT231013"},
			VINs:            []string{"3AKJGLDRXHSHP2574", "1GR1P0627MT231013"},
			Vehicles: []models.InspectionVehicle{
				{
					VIN:     "3AKJGLDRXHSHP2574",
					Company: "",
					Make:    "FRHT",
					Type:    "11",
				},
				{
					VIN:     "1GR1P0627MT231013",
					Company: "",
					Make:    "GDAN",
					Type:    "9",
				},
			},
		},
		{
			RowID:                uuid.MustParse("589bd91a-4d05-51a4-a301-0f5f54060d27"),
			PublishedDate:        time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC),
			InspectionID:         71352528,
			DOTNumber:            1460168,
			ReportNumber:         "E953032977",
			ReportState:          "PA",
			InspectionDate:       time.Date(2020, 11, 24, 0, 0, 0, 0, time.UTC),
			InspectionLevel:      models.WalkAround,
			TimeWeight:           1,
			DriverOOSTotal:       0,
			VehicleOOSTotal:      0,
			TotalOOSViolations:   0,
			HazmatOOSTotal:       0,
			HazmatViolationsSent: 0,
			HazmatPlacardReq:     false,
			//nolint:exhaustive
			BASICCategories: map[basic.Category]bool{
				basic.UnsafeDriving:               true,
				basic.HOSCompliance:               true,
				basic.VehicleMaintenance:          true,
				basic.ControlledSubstancesAlcohol: true,
				basic.HMCompliance:                false,
				basic.DriverFitness:               true,
			},
			//nolint:exhaustive
			BASICViolations: map[basic.Category]int{
				basic.UnsafeDriving:               1,
				basic.HOSCompliance:               0,
				basic.VehicleMaintenance:          2,
				basic.ControlledSubstancesAlcohol: 0,
				basic.HMCompliance:                0,
				basic.DriverFitness:               0,
			},
			TotalBASICViols: 3,
			PublicVINs:      []string{"3AKJGLDR6HSHP2569", "1GR1P0623MT231011"},
			VINs:            []string{"3AKJGLDR6HSHP2569", "1GR1P0623MT231011"},
			Vehicles: []models.InspectionVehicle{
				{
					VIN:     "3AKJGLDR6HSHP2569",
					Company: "10417",
					Make:    "FRHT",
					Type:    "11",
				},
				{
					VIN:     "1GR1P0623MT231011",
					Company: "417",
					Make:    "GDAN",
					Type:    "9",
				},
			},
		},
	}
}

func BuildDefaultFMCSAInspectionRecordsV1ForTest() *data_fetching.FMCSAInspectionRecordsV1 {
	return &data_fetching.FMCSAInspectionRecordsV1{
		Records: []*data_fetching.FMCSAInspectionRecordV1{
			{
				RowID:                "f4ae5018-75fb-5f5d-b655-e2552e5c1b42",
				PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
				InspectionID:         71080922,
				DotNumber:            1460168,
				ReportNumber:         "**********",
				ReportState:          "KY",
				InspectionDate:       timestamppb.New(time.Date(2020, 10, 21, 0, 0, 0, 0, time.UTC)),
				InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Full,
				TimeWeight:           1,
				DriverOOSTotal:       0,
				VehicleOOSTotal:      0,
				TotalOOSViolations:   0,
				HazmatOOSTotal:       0,
				HazmatViolationsSent: 0,
				HazmatPlacardReq:     false,
				BasicCategories: map[string]bool{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
				},
				BasicViolations: map[string]int32{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
				},
				TotalBasicViols: 0,
				PublicVINs:      []string{"4V4NC9TJ97N447739", "1GRAP062XKT141397"},
				Vins:            []string{"4V4NC9TJ97N447739", "1GRAP062XKT141397"},
				Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
					{
						Vin:     "4V4NC9TJ97N447739",
						Company: "0909",
						Make:    "VOLV",
						Model:   "11",
					},
					{
						Vin:     "1GRAP062XKT141397",
						Company: "789",
						Make:    "GDAN",
						Model:   "9",
					},
				},
			},
			{
				RowID:                "fe0959d2-0cd6-5381-975c-5e4803a9c569",
				PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
				InspectionID:         71262551,
				DotNumber:            1460168,
				ReportNumber:         "W133003267",
				ReportState:          "MO",
				InspectionDate:       timestamppb.New(time.Date(2020, 11, 12, 0, 0, 0, 0, time.UTC)),
				InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_WalkAround,
				TimeWeight:           1,
				DriverOOSTotal:       0,
				VehicleOOSTotal:      0,
				TotalOOSViolations:   0,
				HazmatOOSTotal:       0,
				HazmatViolationsSent: 0,
				HazmatPlacardReq:     false,
				BasicCategories: map[string]bool{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
				},
				BasicViolations: map[string]int32{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          4,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
				},
				TotalBasicViols: 4,
				PublicVINs:      []string{"3AKJGLDRXHSHD8047", "1GR1A0626MB230510"},
				Vins:            []string{"3AKJGLDRXHSHD8047", "1GR1A0626MB230510"},
				Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
					{
						Vin:     "3AKJGLDRXHSHD8047",
						Company: "11017",
						Make:    "FRHT",
						Model:   "11",
					},
					{
						Vin:     "1GR1A0626MB230510",
						Company: "306",
						Make:    "GDAN",
						Model:   "9",
					},
				},
			},
			{
				RowID:                "dd244277-c3c6-5ad6-9004-7ac48900df64",
				PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
				InspectionID:         71284418,
				DotNumber:            1460168,
				ReportNumber:         "000TT6T92T",
				ReportState:          "IA",
				InspectionDate:       timestamppb.New(time.Date(2020, 11, 16, 0, 0, 0, 0, time.UTC)),
				InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_DriverOnly,
				TimeWeight:           1,
				DriverOOSTotal:       0,
				VehicleOOSTotal:      0,
				TotalOOSViolations:   0,
				HazmatOOSTotal:       0,
				HazmatViolationsSent: 0,
				HazmatPlacardReq:     false,
				BasicCategories: map[string]bool{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
				},
				BasicViolations: map[string]int32{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               1,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
				},
				TotalBasicViols: 1,
				PublicVINs:      []string{"3AKJGLDR6HSHP2572", "1GR1P0623MT231011"},
				Vins:            []string{"3AKJGLDR6HSHP2572", "1GR1P0623MT231011"},
				Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
					{
						Vin:     "3AKJGLDR6HSHP2572",
						Company: "10417",
						Make:    "FRHT",
						Model:   "11",
					},
					{
						Vin:     "1GR1P0623MT231011",
						Company: "417",
						Make:    "GDAN",
						Model:   "9",
					},
				},
			},
			{
				RowID:                "62394fb2-4761-5aa5-affb-a220e557bdee",
				PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
				InspectionID:         71331790,
				DotNumber:            1460168,
				ReportNumber:         "HOLLM02002",
				ReportState:          "MI",
				InspectionDate:       timestamppb.New(time.Date(2020, 11, 19, 0, 0, 0, 0, time.UTC)),
				InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_DriverOnly,
				TimeWeight:           1,
				DriverOOSTotal:       0,
				VehicleOOSTotal:      0,
				TotalOOSViolations:   0,
				HazmatOOSTotal:       0,
				HazmatViolationsSent: 0,
				HazmatPlacardReq:     false,
				BasicCategories: map[string]bool{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
				},
				BasicViolations: map[string]int32{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               1,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
				},
				TotalBasicViols: 1,
				PublicVINs:      []string{"3AKJGLDRXHSHP2574", "1GR1P0627MT231013"},
				Vins:            []string{"3AKJGLDRXHSHP2574", "1GR1P0627MT231013"},
				Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
					{
						Vin:     "3AKJGLDRXHSHP2574",
						Company: "",
						Make:    "FRHT",
						Model:   "11",
					},
					{
						Vin:     "1GR1P0627MT231013",
						Company: "",
						Make:    "GDAN",
						Model:   "9",
					},
				},
			},
			{
				RowID:                "589bd91a-4d05-51a4-a301-0f5f54060d27",
				PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
				InspectionID:         71352528,
				DotNumber:            1460168,
				ReportNumber:         "E953032977",
				ReportState:          "PA",
				InspectionDate:       timestamppb.New(time.Date(2020, 11, 24, 0, 0, 0, 0, time.UTC)),
				InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_WalkAround,
				TimeWeight:           1,
				DriverOOSTotal:       0,
				VehicleOOSTotal:      0,
				TotalOOSViolations:   0,
				HazmatOOSTotal:       0,
				HazmatViolationsSent: 0,
				HazmatPlacardReq:     false,
				BasicCategories: map[string]bool{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
				},
				BasicViolations: map[string]int32{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               1,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          2,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
				},
				TotalBasicViols: 3,
				PublicVINs:      []string{"3AKJGLDR6HSHP2569", "1GR1P0623MT231011"},
				Vins:            []string{"3AKJGLDR6HSHP2569", "1GR1P0623MT231011"},
				Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
					{
						Vin:     "3AKJGLDR6HSHP2569",
						Company: "10417",
						Make:    "FRHT",
						Model:   "11",
					},
					{
						Vin:     "1GR1P0623MT231011",
						Company: "417",
						Make:    "GDAN",
						Model:   "9",
					},
				},
			},
		},
	}
}

func BuildDefaultPullerCompletionDatesForTest() []time.Time {
	return []time.Time{
		time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC),
	}
}
