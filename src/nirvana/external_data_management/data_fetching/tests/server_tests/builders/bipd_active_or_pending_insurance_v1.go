package builders

import (
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov/active_pending_insurance"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/policy/constants"
)

const (
	docketNumber         = "MC123456"
	form                 = "91"
	insuranceType        = "BIPD"
	insuranceCompanyName = "NIRVANA INSURANCE"
	policyNumber         = constants.NirvanaPolicyCarrierALPrefix + "1000001-23"
	bipdUnderlyingLimit  = 10_000.0
	bipdMaxLimit         = 1_000_000.0
	versionId            = 1
)

func BuildDefaultRawBIPDActiveOrPendingInsuranceForTest(id uuid.UUID, dotNumber int) *active_pending_insurance.ActPendInsRecord {
	return &active_pending_insurance.ActPendInsRecord{
		Id:                   id,
		DocketNumber:         docketNumber,
		DOTNumber:            dotNumber,
		Form:                 form,
		InsuranceType:        insuranceType,
		InsuranceCompanyName: insuranceCompanyName,
		PolicyNumber:         pointer_utils.ToPointer(policyNumber),
		PostedDate:           time_utils.DatagovDate{Date: time_utils.NewDate(2024, 1, 2)},
		BIPDUnderlyingLimit:  bipdUnderlyingLimit,
		BIPDMaxLimit:         bipdMaxLimit,
		EffectiveDate:        time_utils.DatagovDate{Date: time_utils.NewDate(2024, 1, 15)},
		CancelEffectiveDate:  nil,
		CreatedAt:            time_utils.NewDate(2024, 1, 1).ToTime(),
		VersionId:            versionId,
	}
}

func BuildBIPDActiveOrPendingInsuranceV1ForTest(dotNumber int64, ids []uuid.UUID) *data_fetching.BIPDActiveOrPendingInsuranceV1 {
	var records []*data_fetching.InsuranceRecordV1
	for _, id := range ids {
		records = append(records, buildDefaultActiveOrPendingInsuranceRecordV1ForTest(id, dotNumber))
	}
	return &data_fetching.BIPDActiveOrPendingInsuranceV1{
		Records: records,
	}
}

func buildDefaultActiveOrPendingInsuranceRecordV1ForTest(id uuid.UUID, dotNumber int64) *data_fetching.InsuranceRecordV1 {
	return &data_fetching.InsuranceRecordV1{
		Id:                   id.String(),
		DocketNumber:         docketNumber,
		DotNumber:            dotNumber,
		Form:                 form,
		InsuranceType:        pointer_utils.ToPointer(insuranceType),
		InsuranceCompanyName: insuranceCompanyName,
		PolicyNumber:         pointer_utils.ToPointer(constants.NirvanaPolicyCarrierALPrefix + "1000001-23"),
		PostedDate:           timestamppb.New(time_utils.NewDate(2024, 1, 2).ToTime()),
		UnderlyingLimit:      pointer_utils.ToPointer(bipdUnderlyingLimit),
		MaxCoverageAmount:    pointer_utils.ToPointer(bipdMaxLimit),
		EffectiveDate:        timestamppb.New(time_utils.NewDate(2024, 1, 15).ToTime()),
		CancelEffectiveDate:  nil,
		CreatedAt:            timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
		VersionId:            versionId,
	}
}
