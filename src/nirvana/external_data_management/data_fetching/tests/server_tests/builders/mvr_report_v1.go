package builders

import (
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/mvr"
)

func BuildDefaultMVRReportRequestV1ForTest(
	idx int,
	baseTime time.Time,
) *data_fetching.MVRReportRequestV1 {
	dlNumber1 := fmt.Sprintf("dl-%d", idx)
	usState1 := fmt.Sprintf("us-state-%d", idx)
	firstName1 := fmt.Sprintf("first-name-%d", idx)
	lastName1 := fmt.Sprintf("last-name-%d", idx)
	middleName1 := fmt.Sprintf("middle-name-%d", idx)
	applicationID1 := fmt.Sprintf("application-id-%d", idx)
	staleness1 := int64(idx*100 + 1)
	dob1 := timestamppb.New(baseTime.AddDate(idx, 0, 0))

	return &data_fetching.MVRReportRequestV1{
		DlNumber:           dlNumber1,
		UsState:            usState1,
		Dob:                dob1,
		FirstName:          firstName1,
		LastName:           lastName1,
		MiddleName:         middleName1,
		Staleness:          staleness1,
		ApplicationID:      applicationID1,
		OnlyFetchFromCache: true,
	}
}

func BuildDefaultRawMVRFetchRequestForTest(
	idx int,
	baseTime time.Time,
) *mvr.FetchMVRRequest {
	dlNumber1 := fmt.Sprintf("dl-%d", idx)
	usState1 := fmt.Sprintf("us-state-%d", idx)
	firstName1 := fmt.Sprintf("first-name-%d", idx)
	lastName1 := fmt.Sprintf("last-name-%d", idx)
	middleName1 := fmt.Sprintf("middle-name-%d", idx)
	applicationID1 := fmt.Sprintf("application-id-%d", idx)
	staleness1 := int64(idx*100 + 1)
	dob1 := timestamppb.New(baseTime.AddDate(idx, 0, 0))

	return &mvr.FetchMVRRequest{
		DlNumber:           dlNumber1,
		UsState:            usState1,
		Dob:                dob1,
		FirstName:          firstName1,
		LastName:           lastName1,
		MiddleName:         middleName1,
		Staleness:          staleness1,
		ApplicationID:      applicationID1,
		OnlyFetchFromCache: true,
	}
}

func BuildDefaultMVRReportV1ForTest(
	idx int,
	baseTime time.Time,
) *data_fetching.MVRReportV1 {
	reportID1 := fmt.Sprintf("report-id-%d", idx)
	dlState1 := fmt.Sprintf("dl-state-%d", idx)
	reportSequenceNumber1 := fmt.Sprintf("report-sequence-number-%d", idx)
	driverName1 := fmt.Sprintf("driver-name-%d", idx)
	driverStreetAddr1 := fmt.Sprintf("driver-street-addr-%d", idx)
	mvrStatus1 := fmt.Sprintf("mvr-status-%d", idx)
	violationCoding1 := fmt.Sprintf("violation-coding-%d", idx)
	mvrFormat1 := fmt.Sprintf("mvr-format-%d", idx)
	driverCityStateZip1 := fmt.Sprintf("driver-city-state-zip-%d", idx)
	clientCode1 := fmt.Sprintf("client-code-%d", idx)
	archiveFlag1 := fmt.Sprintf("archive-flag-%d", idx)
	ssn1 := fmt.Sprintf("ssn-%d", idx)
	dppaFlag1 := fmt.Sprintf("dppa-flag-%d", idx)
	dmvAccountNumber1 := fmt.Sprintf("dmv-account-number-%d", idx)
	gender1 := fmt.Sprintf("gender-%d", idx)
	height1 := fmt.Sprintf("height-%d", idx)
	weight1 := fmt.Sprintf("weight-%d", idx)
	eyeColor1 := fmt.Sprintf("eye-color-%d", idx)
	hairColor1 := fmt.Sprintf("hair-color-%d", idx)
	licClass1 := fmt.Sprintf("lic-class-%d", idx)
	licStatus1 := fmt.Sprintf("lic-status-%d", idx)
	restrictions1 := fmt.Sprintf("restrictions-%d", idx)
	requestDlNumber1 := fmt.Sprintf("request-dl-number-%d", idx)
	requestState1 := fmt.Sprintf("request-state-%d", idx)
	miscDetail1 := []string{
		fmt.Sprintf("misc-detail-1-%da", idx),
		fmt.Sprintf("misc-detail-1-%db", idx),
	}
	dateIssued1 := timestamppb.New(baseTime.AddDate(idx, 3, 1))
	dateExpires1 := timestamppb.New(baseTime.AddDate(idx, 3, 2))
	mvrReportDate1 := timestamppb.New(baseTime.AddDate(idx, 3, 3))
	violationCodeTotal1 := int64(idx*10 + 1)

	violations1 := BuildDefaultMVRViolationV1ForTest(1, baseTime)

	return &data_fetching.MVRReportV1{
		ReportID:             reportID1,
		DlState:              dlState1,
		ReportSequenceNumber: reportSequenceNumber1,
		DriverName:           driverName1,
		DriverStreetAddr:     driverStreetAddr1,
		MvrStatus:            mvrStatus1,
		ViolationCoding:      violationCoding1,
		ViolationCodeTotal:   violationCodeTotal1,
		MvrFormat:            mvrFormat1,
		DriverCityStateZip:   driverCityStateZip1,
		ClientCode:           clientCode1,
		ArchiveFlag:          archiveFlag1,
		Ssn:                  ssn1,
		DppaFlag:             dppaFlag1,
		DmvAccountNumber:     dmvAccountNumber1,
		Gender:               gender1,
		Height:               height1,
		Weight:               weight1,
		EyeColor:             eyeColor1,
		HairColor:            hairColor1,
		LicClass:             licClass1,
		LicStatus:            licStatus1,
		DateIssued:           dateIssued1,
		DateExpires:          dateExpires1,
		MvrReportDate:        mvrReportDate1,
		Restrictions:         restrictions1,
		MiscDetail:           miscDetail1,
		Violations:           violations1,
		RequestDlNumber:      requestDlNumber1,
		RequestState:         requestState1,
	}
}

func BuildDefaultRawMVRReportForTest(
	idx int,
	baseTime time.Time,
) *mvr.Report {
	reportID1 := fmt.Sprintf("report-id-%d", idx)
	dlState1 := fmt.Sprintf("dl-state-%d", idx)
	reportSequenceNumber1 := fmt.Sprintf("report-sequence-number-%d", idx)
	driverName1 := fmt.Sprintf("driver-name-%d", idx)
	driverStreetAddr1 := fmt.Sprintf("driver-street-addr-%d", idx)
	mvrStatus1 := fmt.Sprintf("mvr-status-%d", idx)
	violationCoding1 := fmt.Sprintf("violation-coding-%d", idx)
	mvrFormat1 := fmt.Sprintf("mvr-format-%d", idx)
	driverCityStateZip1 := fmt.Sprintf("driver-city-state-zip-%d", idx)
	clientCode1 := fmt.Sprintf("client-code-%d", idx)
	archiveFlag1 := fmt.Sprintf("archive-flag-%d", idx)
	ssn1 := fmt.Sprintf("ssn-%d", idx)
	dppaFlag1 := fmt.Sprintf("dppa-flag-%d", idx)
	dmvAccountNumber1 := fmt.Sprintf("dmv-account-number-%d", idx)
	gender1 := fmt.Sprintf("gender-%d", idx)
	height1 := fmt.Sprintf("height-%d", idx)
	weight1 := fmt.Sprintf("weight-%d", idx)
	eyeColor1 := fmt.Sprintf("eye-color-%d", idx)
	hairColor1 := fmt.Sprintf("hair-color-%d", idx)
	licClass1 := fmt.Sprintf("lic-class-%d", idx)
	licStatus1 := fmt.Sprintf("lic-status-%d", idx)
	restrictions1 := fmt.Sprintf("restrictions-%d", idx)
	requestDlNumber1 := fmt.Sprintf("request-dl-number-%d", idx)
	requestState1 := fmt.Sprintf("request-state-%d", idx)
	miscDetail1 := []string{
		fmt.Sprintf("misc-detail-1-%da", idx),
		fmt.Sprintf("misc-detail-1-%db", idx),
	}
	dateIssued1 := timestamppb.New(baseTime.AddDate(idx, 3, 1))
	dateExpires1 := timestamppb.New(baseTime.AddDate(idx, 3, 2))
	mvrReportDate1 := timestamppb.New(baseTime.AddDate(idx, 3, 3))
	violationCodeTotal1 := int64(idx*10 + 1)

	rawViolations1 := BuildDefaultRawMVRViolationForTest(1, baseTime)

	return &mvr.Report{
		ReportID:             reportID1,
		DlState:              dlState1,
		ReportSequenceNumber: reportSequenceNumber1,
		DriverName:           driverName1,
		DriverStreetAddr:     driverStreetAddr1,
		MvrStatus:            mvrStatus1,
		ViolationCoding:      violationCoding1,
		ViolationCodeTotal:   violationCodeTotal1,
		MvrFormat:            mvrFormat1,
		DriverCityStateZip:   driverCityStateZip1,
		ClientCode:           clientCode1,
		ArchiveFlag:          archiveFlag1,
		Ssn:                  ssn1,
		DppaFlag:             dppaFlag1,
		DmvAccountNumber:     dmvAccountNumber1,
		Gender:               gender1,
		Height:               height1,
		Weight:               weight1,
		EyeColor:             eyeColor1,
		HairColor:            hairColor1,
		LicClass:             licClass1,
		LicStatus:            licStatus1,
		DateIssued:           dateIssued1,
		DateExpires:          dateExpires1,
		MvrReportDate:        mvrReportDate1,
		Restrictions:         restrictions1,
		MiscDetail:           miscDetail1,
		Violations:           rawViolations1,
		RequestDlNumber:      requestDlNumber1,
		RequestState:         requestState1,
	}
}

func BuildDefaultMVRViolationV1ForTest(
	idx int,
	baseTime time.Time,
) []*data_fetching.MVRViolationV1 {
	violationType1A := fmt.Sprintf("violation-type-%da", idx)
	violationType1B := fmt.Sprintf("violation-type-%db", idx)
	violationCode1A := fmt.Sprintf("violation-code-%da", idx)
	violationCode1B := fmt.Sprintf("violation-code-%db", idx)
	assignedViolationCode1A := fmt.Sprintf("assigned-violation-code-%da", idx)
	assignedViolationCode1B := fmt.Sprintf("assigned-violation-code-%db", idx)
	assignedPoints1A := fmt.Sprintf("assigned-points-%da", idx)
	assignedPoints1B := fmt.Sprintf("assigned-points-%db", idx)
	violationDetail1A := fmt.Sprintf("violation-detail-%da", idx)
	violationDetail1B := fmt.Sprintf("violation-detail-%db", idx)
	violationDate1A := timestamppb.New(baseTime.AddDate(idx, 6, 1))
	violationDate1B := timestamppb.New(baseTime.AddDate(idx, 6, 2))
	convictionDate1A := timestamppb.New(baseTime.AddDate(idx, 6, 3))
	convictionDate1B := timestamppb.New(baseTime.AddDate(idx, 6, 4))
	points1B := int64(100*idx + 1)
	points1A := int64(100*idx + 2)

	return []*data_fetching.MVRViolationV1{
		{
			ViolationType:         violationType1A,
			ViolationDate:         violationDate1A,
			ConvictionDate:        convictionDate1A,
			ViolationCode:         violationCode1A,
			Points:                points1A,
			AssignedViolationCode: assignedViolationCode1A,
			AssignedPoints:        assignedPoints1A,
			ViolationDetail:       violationDetail1A,
		},
		{
			ViolationType:         violationType1B,
			ViolationDate:         violationDate1B,
			ConvictionDate:        convictionDate1B,
			ViolationCode:         violationCode1B,
			Points:                points1B,
			AssignedViolationCode: assignedViolationCode1B,
			AssignedPoints:        assignedPoints1B,
			ViolationDetail:       violationDetail1B,
		},
	}
}

func BuildDefaultRawMVRViolationForTest(
	idx int,
	baseTime time.Time,
) []*mvr.Violation {
	violationType1A := fmt.Sprintf("violation-type-%da", idx)
	violationType1B := fmt.Sprintf("violation-type-%db", idx)
	violationCode1A := fmt.Sprintf("violation-code-%da", idx)
	violationCode1B := fmt.Sprintf("violation-code-%db", idx)
	assignedViolationCode1A := fmt.Sprintf("assigned-violation-code-%da", idx)
	assignedViolationCode1B := fmt.Sprintf("assigned-violation-code-%db", idx)
	assignedPoints1A := fmt.Sprintf("assigned-points-%da", idx)
	assignedPoints1B := fmt.Sprintf("assigned-points-%db", idx)
	violationDetail1A := fmt.Sprintf("violation-detail-%da", idx)
	violationDetail1B := fmt.Sprintf("violation-detail-%db", idx)
	violationDate1A := timestamppb.New(baseTime.AddDate(idx, 6, 1))
	violationDate1B := timestamppb.New(baseTime.AddDate(idx, 6, 2))
	convictionDate1A := timestamppb.New(baseTime.AddDate(idx, 6, 3))
	convictionDate1B := timestamppb.New(baseTime.AddDate(idx, 6, 4))
	points1B := int64(100*idx + 1)
	points1A := int64(100*idx + 2)

	return []*mvr.Violation{
		{
			ViolationType:         violationType1A,
			ViolationDate:         violationDate1A,
			ConvictionDate:        convictionDate1A,
			ViolationCode:         violationCode1A,
			Points:                points1A,
			AssignedViolationCode: assignedViolationCode1A,
			AssignedPoints:        assignedPoints1A,
			ViolationDetail:       violationDetail1A,
		},
		{
			ViolationType:         violationType1B,
			ViolationDate:         violationDate1B,
			ConvictionDate:        convictionDate1B,
			ViolationCode:         violationCode1B,
			Points:                points1B,
			AssignedViolationCode: assignedViolationCode1B,
			AssignedPoints:        assignedPoints1B,
			ViolationDetail:       violationDetail1B,
		},
	}
}
