package builders

import (
	_ "embed"
	"encoding/json"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing/national_credit_file"
	"nirvanatech.com/nirvana/external_data_management/mvr"
)

type requestArgs struct {
	firstName string
	lastName  string
	street    string
	city      string
	state     string
	zip       string
	dob       *timestamppb.Timestamp

	modifiedFirstName string
	modifiedLastName  string
	appID             string
}

//go:embed data/national_credit_file.json
var report []byte

var args = requestArgs{
	firstName:         "John",
	lastName:          "Doe",
	street:            "3521 GOLDENEYE DR",
	city:              "CHARLOTTE",
	state:             "NC",
	zip:               "28216",
	dob:               timestamppb.New(time.Date(1962, 5, 18, 0, 0, 0, 0, time.UTC)),
	modifiedFirstName: "<PERSON>",
	modifiedLastName:  "Bravo",
	appID:             "013dba73-6ace-418d-b8fc-1d32239e6cca",
}

func BuildDefaultNationalCreditFileForTest() (*mvr.GetNationalCreditFileResponse, error) {
	return buildNationalCreditFileResponse(national_credit_file.NCFSuccessStatusCode)
}

func BuildDefaultNoHitNationalCreditFileForTest() (*mvr.GetNationalCreditFileResponse, error) {
	return buildNationalCreditFileResponse(national_credit_file.NCFNoHitStatusCode)
}

func BuildDefaultThinFileNationalCreditFileForTest() (*mvr.GetNationalCreditFileResponse, error) {
	return buildNationalCreditFileResponse(national_credit_file.NCFUnscoredStatusCode)
}

func BuildNCFRequestV1() *data_fetching.NationalCreditFileRequestV1 {
	return &data_fetching.NationalCreditFileRequestV1{
		FirstName: args.firstName,
		LastName:  args.lastName,
		Address: &data_fetching.AddressV1{
			Street: args.street,
			City:   args.city,
			State:  args.state,
			Zip:    args.zip,
		},
		Dob:           args.dob,
		ApplicationID: args.appID,
	}
}

func BuildNCFRequest() *mvr.GetNationalCreditFileRequest {
	return &mvr.GetNationalCreditFileRequest{
		Dob:       args.dob,
		FirstName: args.firstName,
		LastName:  args.lastName,
		Address: &mvr.Address{
			Street: args.street,
			City:   args.city,
			State:  args.state,
			Zip:    args.zip,
		},
		ApplicationID: args.appID,
	}
}

func buildNationalCreditFileResponse(statusCode string) (*mvr.GetNationalCreditFileResponse, error) {
	var ncfReport mvr.NcfReport
	if err := json.Unmarshal(report, &ncfReport); err != nil {
		return nil, err
	}
	return &mvr.GetNationalCreditFileResponse{
		NcfReport: &ncfReport,
		TransactionDetails: &mvr.TransactionDetails{
			ProcessingStatus: statusCode,
		},
	}, nil
}
