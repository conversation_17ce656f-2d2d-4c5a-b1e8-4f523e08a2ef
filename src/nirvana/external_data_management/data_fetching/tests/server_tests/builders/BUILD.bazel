load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "builders",
    srcs = [
        "bipd_active_or_pending_insurance_v1.go",
        "bipd_insurance_history_v1.go",
        "fmcsa_census_info_v1.go",
        "fmcsa_inspection_records_v1.go",
        "fmcsa_violation_records_v1.go",
        "mvr_report_v1.go",
        "national_credit_file.go",
        "objective_grade_v1.go",
        "rating_tier_record_v1.go",
        "rating_tier_records_for_dump_date_v1.go",
    ],
    embedsrcs = ["data/national_credit_file.json"],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/datagov/active_pending_insurance",
        "//nirvana/db-api/db_wrappers/datagov/insurance_history",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing/national_credit_file",
        "//nirvana/external_data_management/mvr",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/models",
        "//nirvana/policy/constants",
        "//nirvana/safety/scores/objective_grade/enums",
        "//nirvana/safety/scores/objective_grade/models",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
