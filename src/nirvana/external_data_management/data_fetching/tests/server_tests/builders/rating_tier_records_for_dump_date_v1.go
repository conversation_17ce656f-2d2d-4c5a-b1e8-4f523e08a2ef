package builders

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/fmcsa/models"
)

func BuildDefaultRawRatingTierRecordsForDumpDateV1ForTest(dotNumber int64, dumpDate time.Time) *models.PerDotSentryFmcsaInput {
	return &models.PerDotSentryFmcsaInput{
		DotNumber: dotNumber,
		IsValid:   true,
		MonthlyInputs: []models.SentryInputs{
			{
				Date:                       pointer_utils.ToPointer(dumpDate),
				InspectionIndicator:        "BOTH",
				LargeMachineryIndicator:    true,
				CrashFrequency:             pointer_utils.ToPointer(0.1),
				PowerUnits:                 pointer_utils.ToPointer(int64(25)),
				AverageMiles:               pointer_utils.ToPointer(15_134.2),
				AverageCombinedGrossWeight: pointer_utils.ToPointer(4_329.2),
				MaintenanceViolationsRatio: pointer_utils.ToPointer(0.01),
				UnsafeViolationRatio:       pointer_utils.ToPointer(0.1),
				VehicleInspectionsRatio:    pointer_utils.ToPointer(0.9),
				DateStrInternal:            dumpDate.Format("2006-01-02"),
			},
			{
				Date:                       pointer_utils.ToPointer(dumpDate.AddDate(0, -1, 0)),
				InspectionIndicator:        "BOTH",
				LargeMachineryIndicator:    true,
				CrashFrequency:             pointer_utils.ToPointer(0.2),
				PowerUnits:                 pointer_utils.ToPointer(int64(50)),
				AverageMiles:               pointer_utils.ToPointer(25_134.2),
				AverageCombinedGrossWeight: pointer_utils.ToPointer(33_329.2),
				MaintenanceViolationsRatio: pointer_utils.ToPointer(0.02),
				UnsafeViolationRatio:       pointer_utils.ToPointer(0.3),
				VehicleInspectionsRatio:    pointer_utils.ToPointer(0.6),
				DateStrInternal:            dumpDate.Format("2006-01-02"),
			},
		},
		SentryDumpDate: models.CustomMarshalTime(dumpDate),
	}
}

func BuildDefaultRatingTierRecordsForDumpDateV1ForTest(dumpDate time.Time) []data_fetching.RatingTierRecordV1 {
	return []data_fetching.RatingTierRecordV1{
		{
			Date:                       timestamppb.New(dumpDate),
			InspectionIndicator:        "BOTH",
			LargeMachineryIndicator:    true,
			CrashFrequency:             pointer_utils.ToPointer(0.1),
			PowerUnits:                 pointer_utils.ToPointer(int64(25)),
			AverageMiles:               pointer_utils.ToPointer(15_134.2),
			AverageCombinedGrossWeight: pointer_utils.ToPointer(4_329.2),
			MaintenanceViolationsRatio: pointer_utils.ToPointer(0.01),
			UnsafeViolationRatio:       pointer_utils.ToPointer(0.1),
			VehicleInspectionRatio:     pointer_utils.ToPointer(0.9),
		}, {
			Date:                       timestamppb.New(dumpDate.AddDate(0, -1, 0)),
			InspectionIndicator:        "BOTH",
			LargeMachineryIndicator:    true,
			CrashFrequency:             pointer_utils.ToPointer(0.2),
			PowerUnits:                 pointer_utils.ToPointer(int64(50)),
			AverageMiles:               pointer_utils.ToPointer(25_134.2),
			AverageCombinedGrossWeight: pointer_utils.ToPointer(33_329.2),
			MaintenanceViolationsRatio: pointer_utils.ToPointer(0.02),
			UnsafeViolationRatio:       pointer_utils.ToPointer(0.3),
			VehicleInspectionRatio:     pointer_utils.ToPointer(0.6),
		},
	}
}
