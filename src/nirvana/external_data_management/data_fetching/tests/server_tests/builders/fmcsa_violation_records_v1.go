package builders

import (
	"time"

	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/fmcsa/models"
)

func BuildDefaultViolationRecordsForTest() []models.ViolationRecord {
	return []models.ViolationRecord{
		{
			RowID:               uuid.MustParse("be8fabda-c4ba-5295-bced-666cb99dc624"),
			PublishedDate:       time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC),
			InspectionID:        76246428,
			InspectionDate:      time.Date(2022, 8, 4, 0, 0, 0, 0, time.UTC),
			DOTNumber:           493219,
			Code:                "39216",
			Category:            basic.UnsafeDriving,
			OOSIndicator:        false,
			OOSWeight:           0,
			SeverityWeight:      7,
			TotalSeverityWeight: 7,
			TimeWeight:          1,
			ViolationID:         pointer_utils.ToPointer(247542809),
			CountyCode:          pointer_utils.ToPointer("115"),
			CountyCodeState:     pointer_utils.ToPointer("MI"),
			VINs:                nil,
		},
		{
			RowID:               uuid.MustParse("31d0ef9b-9ef1-54b2-b98c-6cc0ba7ecc8c"),
			PublishedDate:       time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC),
			InspectionID:        76246428,
			InspectionDate:      time.Date(2022, 8, 4, 0, 0, 0, 0, time.UTC),
			DOTNumber:           493219,
			Code:                "3939",
			Category:            basic.VehicleMaintenance,
			OOSIndicator:        false,
			OOSWeight:           0,
			SeverityWeight:      2,
			TotalSeverityWeight: 2,
			TimeWeight:          1,
			ViolationID:         pointer_utils.ToPointer(247542810),
			CountyCode:          pointer_utils.ToPointer("115"),
			CountyCodeState:     pointer_utils.ToPointer("MI"),
			VINs:                nil,
		},
		{
			RowID:               uuid.MustParse("19ec2c99-f232-5bcf-9439-fa66ce7769ff"),
			PublishedDate:       time.Date(2023, 11, 24, 0, 0, 0, 0, time.UTC),
			InspectionID:        78955413,
			InspectionDate:      time.Date(2023, 6, 21, 0, 0, 0, 0, time.UTC),
			DOTNumber:           397433,
			Code:                "3965b",
			Category:            basic.VehicleMaintenance,
			OOSIndicator:        false,
			OOSWeight:           0,
			SeverityWeight:      3,
			TotalSeverityWeight: 3,
			TimeWeight:          3,
			ViolationID:         pointer_utils.ToPointer(253925582),
			CountyCode:          pointer_utils.ToPointer("67"),
			CountyCodeState:     pointer_utils.ToPointer("KY"),
			VINs:                nil,
		},
		{
			RowID:               uuid.MustParse("566ee783-af09-52d4-b7a9-0f789d230e76"),
			PublishedDate:       time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC),
			InspectionID:        78955413,
			InspectionDate:      time.Date(2023, 6, 21, 0, 0, 0, 0, time.UTC),
			DOTNumber:           397433,
			Code:                "3939",
			Category:            basic.VehicleMaintenance,
			OOSIndicator:        false,
			OOSWeight:           0,
			SeverityWeight:      2,
			TotalSeverityWeight: 2,
			TimeWeight:          3,
			ViolationID:         pointer_utils.ToPointer(253925585),
			CountyCode:          pointer_utils.ToPointer("67"),
			CountyCodeState:     pointer_utils.ToPointer("KY"),
			VINs:                nil,
		},
		{
			RowID:               uuid.MustParse("1a18b94c-0dec-5493-a89f-eb13f86b5341"),
			PublishedDate:       time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC),
			InspectionID:        78955413,
			InspectionDate:      time.Date(2023, 6, 21, 0, 0, 0, 0, time.UTC),
			DOTNumber:           397433,
			Code:                "39347e",
			Category:            basic.VehicleMaintenance,
			OOSIndicator:        false,
			OOSWeight:           0,
			SeverityWeight:      4,
			TotalSeverityWeight: 4,
			TimeWeight:          3,
			ViolationID:         pointer_utils.ToPointer(253925590),
			CountyCode:          pointer_utils.ToPointer("67"),
			CountyCodeState:     pointer_utils.ToPointer("KY"),
			VINs:                nil,
		},
		{
			RowID:               uuid.MustParse("6fcf7b57-42c4-5766-8ab8-893ed6ca7943"),
			PublishedDate:       time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC),
			InspectionID:        77307249,
			InspectionDate:      time.Date(2022, 12, 7, 0, 0, 0, 0, time.UTC),
			DOTNumber:           389374,
			Code:                "3939",
			Category:            basic.VehicleMaintenance,
			OOSIndicator:        false,
			OOSWeight:           0,
			SeverityWeight:      2,
			TotalSeverityWeight: 2,
			TimeWeight:          2,
			ViolationID:         pointer_utils.ToPointer(249968076),
			CountyCode:          pointer_utils.ToPointer("175"),
			CountyCodeState:     pointer_utils.ToPointer("OH"),
			VINs:                nil,
		},
		{
			RowID:               uuid.MustParse("bd665110-d1bb-533f-9c67-f81ecb82209c"),
			PublishedDate:       time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC),
			InspectionID:        77711921,
			InspectionDate:      time.Date(2023, 1, 26, 0, 0, 0, 0, time.UTC),
			DOTNumber:           389374,
			Code:                "39216",
			Category:            basic.UnsafeDriving,
			OOSIndicator:        false,
			OOSWeight:           0,
			SeverityWeight:      7,
			TotalSeverityWeight: 7,
			TimeWeight:          2,
			ViolationID:         pointer_utils.ToPointer(250821595),
			CountyCode:          pointer_utils.ToPointer("39"),
			CountyCodeState:     pointer_utils.ToPointer("OH"),
			VINs:                nil,
		},
		{
			RowID:               uuid.MustParse("32cf3004-0927-521d-8c00-6f09880c9b2f"),
			PublishedDate:       time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC),
			InspectionID:        79283708,
			InspectionDate:      time.Date(2023, 7, 31, 0, 0, 0, 0, time.UTC),
			DOTNumber:           389374,
			Code:                "39347e",
			Category:            basic.VehicleMaintenance,
			OOSIndicator:        false,
			OOSWeight:           0,
			SeverityWeight:      4,
			TotalSeverityWeight: 4,
			TimeWeight:          3,
			ViolationID:         pointer_utils.ToPointer(254510829),
			CountyCode:          pointer_utils.ToPointer("97"),
			CountyCodeState:     pointer_utils.ToPointer("OH"),
			VINs:                nil,
		},
	}
}

func BuildDefaultFMCSAViolationRecordsForTest() *data_fetching.FMCSAViolationRecordsV1 {
	return &data_fetching.FMCSAViolationRecordsV1{
		Records: []*data_fetching.FMCSAViolationRecordV1{
			{
				RowId:               "be8fabda-c4ba-5295-bced-666cb99dc624",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        76246428,
				InspectionDate:      timestamppb.New(time.Date(2022, 8, 4, 0, 0, 0, 0, time.UTC)),
				DotNumber:           493219,
				Code:                "39216",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      7,
				TotalSeverityWeight: 7,
				TimeWeight:          1,
				ViolationId:         pointer_utils.ToPointer(int32(247542809)),
				CountyCode:          pointer_utils.ToPointer("115"),
				CountyCodeState:     pointer_utils.ToPointer("MI"),
				Vins:                nil,
			},
			{
				RowId:               "31d0ef9b-9ef1-54b2-b98c-6cc0ba7ecc8c",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        76246428,
				InspectionDate:      timestamppb.New(time.Date(2022, 8, 4, 0, 0, 0, 0, time.UTC)),
				DotNumber:           493219,
				Code:                "3939",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      2,
				TotalSeverityWeight: 2,
				TimeWeight:          1,
				ViolationId:         pointer_utils.ToPointer(int32(247542810)),
				CountyCode:          pointer_utils.ToPointer("115"),
				CountyCodeState:     pointer_utils.ToPointer("MI"),
				Vins:                nil,
			},
			{
				RowId:               "19ec2c99-f232-5bcf-9439-fa66ce7769ff",
				PublishedDate:       timestamppb.New(time.Date(2023, 11, 24, 0, 0, 0, 0, time.UTC)),
				InspectionId:        78955413,
				InspectionDate:      timestamppb.New(time.Date(2023, 6, 21, 0, 0, 0, 0, time.UTC)),
				DotNumber:           397433,
				Code:                "3965b",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      3,
				TotalSeverityWeight: 3,
				TimeWeight:          3,
				ViolationId:         pointer_utils.ToPointer(int32(253925582)),
				CountyCode:          pointer_utils.ToPointer("67"),
				CountyCodeState:     pointer_utils.ToPointer("KY"),
				Vins:                nil,
			},
			{
				RowId:               "566ee783-af09-52d4-b7a9-0f789d230e76",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        78955413,
				InspectionDate:      timestamppb.New(time.Date(2023, 6, 21, 0, 0, 0, 0, time.UTC)),
				DotNumber:           397433,
				Code:                "3939",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      2,
				TotalSeverityWeight: 2,
				TimeWeight:          3,
				ViolationId:         pointer_utils.ToPointer(int32(253925585)),
				CountyCode:          pointer_utils.ToPointer("67"),
				CountyCodeState:     pointer_utils.ToPointer("KY"),
				Vins:                nil,
			},
			{
				RowId:               "1a18b94c-0dec-5493-a89f-eb13f86b5341",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        78955413,
				InspectionDate:      timestamppb.New(time.Date(2023, 6, 21, 0, 0, 0, 0, time.UTC)),
				DotNumber:           397433,
				Code:                "39347e",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      4,
				TotalSeverityWeight: 4,
				TimeWeight:          3,
				ViolationId:         pointer_utils.ToPointer(int32(253925590)),
				CountyCode:          pointer_utils.ToPointer("67"),
				CountyCodeState:     pointer_utils.ToPointer("KY"),
				Vins:                nil,
			},
			{
				RowId:               "6fcf7b57-42c4-5766-8ab8-893ed6ca7943",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        77307249,
				InspectionDate:      timestamppb.New(time.Date(2022, 12, 7, 0, 0, 0, 0, time.UTC)),
				DotNumber:           389374,
				Code:                "3939",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      2,
				TotalSeverityWeight: 2,
				TimeWeight:          2,
				ViolationId:         pointer_utils.ToPointer(int32(249968076)),
				CountyCode:          pointer_utils.ToPointer("175"),
				CountyCodeState:     pointer_utils.ToPointer("OH"),
				Vins:                nil,
			},
			{
				RowId:               "bd665110-d1bb-533f-9c67-f81ecb82209c",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        77711921,
				InspectionDate:      timestamppb.New(time.Date(2023, 1, 26, 0, 0, 0, 0, time.UTC)),
				DotNumber:           389374,
				Code:                "39216",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      7,
				TotalSeverityWeight: 7,
				TimeWeight:          2,
				ViolationId:         pointer_utils.ToPointer(int32(250821595)),
				CountyCode:          pointer_utils.ToPointer("39"),
				CountyCodeState:     pointer_utils.ToPointer("OH"),
				Vins:                nil,
			},
			{
				RowId:               "32cf3004-0927-521d-8c00-6f09880c9b2f",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        79283708,
				InspectionDate:      timestamppb.New(time.Date(2023, 7, 31, 0, 0, 0, 0, time.UTC)),
				DotNumber:           389374,
				Code:                "39347e",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      4,
				TotalSeverityWeight: 4,
				TimeWeight:          3,
				ViolationId:         pointer_utils.ToPointer(int32(254510829)),
				CountyCode:          pointer_utils.ToPointer("97"),
				CountyCodeState:     pointer_utils.ToPointer("OH"),
				Vins:                nil,
			},
		},
	}
}
