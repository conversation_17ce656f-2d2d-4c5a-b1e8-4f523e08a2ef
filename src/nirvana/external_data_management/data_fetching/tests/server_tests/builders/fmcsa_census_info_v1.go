package builders

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/fmcsa/models"
)

func BuildDefaultMockDotDetailsForTest(dotNumber int64) *models.DotDetails {
	census := models.Census{
		IsActive:                  true,
		DateStr:                   "2023-09-29",
		Date:                      time_utils.NewDate(2023, 9, 29).ToTime(),
		Name:                      pointer_utils.ToPointer("TONY HULS"),
		Dba:                       pointer_utils.ToPointer("OAK GROVE FARMS"),
		TotalMileage:              pointer_utils.ToPointer(int64(265850)),
		Mcs150MileageYear:         pointer_utils.ToPointer(int64(2022)),
		TotalTrucks:               pointer_utils.ToPointer(int64(2)),
		TotalPowerUnits:           pointer_utils.ToPointer(int64(2)),
		TotalDrivers:              pointer_utils.ToPointer(int64(2)),
		TotalCdlDrivers:           pointer_utils.ToPointer(int64(2)),
		CarrierCargoTypesStr:      []string{"USMail", "OilfieldEquipment", "MetalSheet", "CommoditiesDryBulk", "PaperProducts", "Utility", "MotorVehicles", "BuildingMaterials", "Grainfeed", "FarmSupplies", "Construction", "Beverages", "GeneralFreight", "MachineryLargeObjects"},
		CarriesHazmat:             false,
		PhysicalAddressNation:     pointer_utils.ToPointer("US"),
		PhysicalAddressState:      pointer_utils.ToPointer("IL"),
		PhysicalAddressCity:       pointer_utils.ToPointer("CARTHAGE"),
		PhysicalAddressCountyCode: pointer_utils.ToPointer("067"),
		PhysicalAddressStreet:     pointer_utils.ToPointer("2645 EAST CTY RD 1300 N"),
		PhysicalAddressZipCode:    pointer_utils.ToPointer("62321"),
		Rating:                    nil,
		RatingDateStr:             nil,
		Telephone:                 pointer_utils.ToPointer("(*************"),
		Cellphone:                 nil,
		Fax:                       pointer_utils.ToPointer("(*************"),
		Email:                     pointer_utils.ToPointer("<EMAIL>"),
	}

	return &models.DotDetails{
		DotNumber:     dotNumber,
		Census:        census,
		Name:          *census.Name,
		IsValidCensus: true,
	}
}

func BuildDefaultFMCSACensusInfoV1ForTest() *data_fetching.FMCSACensusInfoV1 {
	return &data_fetching.FMCSACensusInfoV1{
		IsActive:                  true,
		DateStr:                   "2023-09-29",
		Date:                      timestamppb.New(time_utils.NewDate(2023, 9, 29).ToTime()),
		Name:                      pointer_utils.ToPointer("TONY HULS"),
		Dba:                       pointer_utils.ToPointer("OAK GROVE FARMS"),
		TotalMileage:              pointer_utils.ToPointer(int64(265850)),
		Mcs150MileageYear:         pointer_utils.ToPointer(int64(2022)),
		TotalTrucks:               pointer_utils.ToPointer(int64(2)),
		TotalPowerUnits:           pointer_utils.ToPointer(int64(2)),
		TotalDrivers:              pointer_utils.ToPointer(int64(2)),
		TotalCdlDrivers:           pointer_utils.ToPointer(int64(2)),
		CarrierCargoTypesStr:      []string{"USMail", "OilfieldEquipment", "MetalSheet", "CommoditiesDryBulk", "PaperProducts", "Utility", "MotorVehicles", "BuildingMaterials", "Grainfeed", "FarmSupplies", "Construction", "Beverages", "GeneralFreight", "MachineryLargeObjects"},
		CarriesHazmat:             false,
		PhysicalAddressNation:     pointer_utils.ToPointer("US"),
		PhysicalAddressState:      pointer_utils.ToPointer("IL"),
		PhysicalAddressCity:       pointer_utils.ToPointer("CARTHAGE"),
		PhysicalAddressCountyCode: pointer_utils.ToPointer("067"),
		PhysicalAddressStreet:     pointer_utils.ToPointer("2645 EAST CTY RD 1300 N"),
		PhysicalAddressZipCode:    pointer_utils.ToPointer("62321"),
		Rating:                    nil,
		RatingDateStr:             nil,
		Telephone:                 pointer_utils.ToPointer("(*************"),
		Cellphone:                 nil,
		Fax:                       pointer_utils.ToPointer("(*************"),
		Email:                     pointer_utils.ToPointer("<EMAIL>"),
	}
}
