package builders

import (
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov/insurance_history"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

// Constants used for building the default BIPD Insurance History
const (
	cancellationMethod = "Standard"
	minCoverageAmount  = 50000
)

func BuildDefaultRawBIPDInsuranceHistoryForTest(id uuid.UUID, dotNumber int) []insurance_history.InsHistRecord {
	return []insurance_history.InsHistRecord{
		{
			Id:                     id,
			DocketNumber:           docketNumber,
			DOTNumber:              dotNumber,
			Form:                   form,
			CancellationMethod:     cancellationMethod,
			InsuranceType:          pointer_utils.ToPointer(insuranceType),
			PolicyNumber:           pointer_utils.ToPointer(policyNumber),
			MinCoverageAmount:      minCoverageAmount,
			BIPDClass:              pointer_utils.ToPointer("P"),
			EffectiveDate:          &time_utils.DatagovDate{Date: time_utils.NewDate(2024, 1, 15)},
			BIPDUnderlyingLimit:    pointer_utils.ToPointer(bipdUnderlyingLimit),
			BIPDMaxCoverageAmount:  pointer_utils.ToPointer(bipdMaxLimit),
			CancelEffectiveDate:    nil,
			InsuranceCompanyBranch: "San Francisco",
			InsuranceCompanyName:   insuranceCompanyName,
			CreatedAt:              time_utils.NewDate(2024, 1, 1).ToTime(),
			VersionId:              versionId,
		},
	}
}

func BuildDefaultBIPDInsuranceHistoryV1ForTest(id uuid.UUID, dotNumber int64) *data_fetching.BIPDInsuranceHistoryV1 {
	return &data_fetching.BIPDInsuranceHistoryV1{
		Records: []*data_fetching.InsuranceRecordV1{
			{
				Id:                     id.String(),
				DocketNumber:           docketNumber,
				DotNumber:              dotNumber,
				Form:                   form,
				CancellationDetails:    &data_fetching.CancellationV1{Method: cancellationMethod},
				InsuranceType:          pointer_utils.ToPointer(insuranceType),
				PolicyNumber:           pointer_utils.ToPointer(policyNumber),
				MinCoverageAmount:      pointer_utils.ToPointer(int32(minCoverageAmount)),
				BipdClass:              pointer_utils.ToPointer("P"),
				EffectiveDate:          timestamppb.New(time_utils.NewDate(2024, 1, 15).ToTime()),
				UnderlyingLimit:        pointer_utils.ToPointer(bipdUnderlyingLimit),
				MaxCoverageAmount:      pointer_utils.ToPointer(bipdMaxLimit),
				CancelEffectiveDate:    nil,
				InsuranceCompanyBranch: pointer_utils.ToPointer("San Francisco"),
				InsuranceCompanyName:   insuranceCompanyName,
				CreatedAt:              timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
				VersionId:              int32(versionId),
				BipdFlag:               pointer_utils.Bool(false),
			},
		},
	}
}
