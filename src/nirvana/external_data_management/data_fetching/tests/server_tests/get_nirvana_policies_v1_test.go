package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	mockpolicy "nirvanatech.com/nirvana/db-api/db_wrappers/policy/mock"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getNirvanaPoliciesV1TestEnv struct {
	fx.In

	FetcherClientFactory            data_fetching.FetcherClientFactory
	StoreManager                    store_management.StoreManager
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
}

type getNirvanaPoliciesV1TestSuite struct {
	suite.Suite
	ctx   context.Context
	env   getNirvanaPoliciesV1TestEnv
	fxapp *fxtest.App

	mockPolicyWrapper *mockpolicy.MockDataWrapper
}

func TestGetNirvanaPoliciesV1(t *testing.T) {
	suite.Run(t, new(getNirvanaPoliciesV1TestSuite))
}

func (s *getNirvanaPoliciesV1TestSuite) SetupSuite() {
	s.ctx = context.Background()

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockPolicyWrapper = mockpolicy.NewMockDataWrapper(ctrl)

	newMockPolicyWrapper := func() policy.DataWrapper {
		return s.mockPolicyWrapper
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockPolicyWrapper)),
	)
}

func (s *getNirvanaPoliciesV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getNirvanaPoliciesV1TestSuite) createMockPolicy(id uuid.UUID) *policy.Policy {
	args := policy.MockPolicyArgs{
		ProgramType:   policyenums.ProgramTypeFleet,
		ID:            &id,
		ApplicationID: pointer_utils.ToPointer(uuid.New()),
		SubmissionID:  pointer_utils.ToPointer(uuid.New()),
		AgencyID:      pointer_utils.ToPointer(uuid.New()),
		Version:       pointer_utils.ToPointer(1),
	}
	mockPolicy, _ := policy.MockPolicyImpl(args)
	return mockPolicy
}

func (s *getNirvanaPoliciesV1TestSuite) createExpectedPolicyV1(p *policy.Policy) *data_fetching.NirvanaPolicyV1 {
	return &data_fetching.NirvanaPolicyV1{
		Id:               p.Id.String(),
		PolicyNumber:     p.PolicyNumber.String(),
		Version:          int32(p.Version),
		InsuredName:      p.InsuredName,
		State:            p.State.String(),
		ApplicationId:    p.ApplicationId.String(),
		SubmissionId:     p.SubmissionId.String(),
		ProgramType:      p.ProgramType.String(),
		EffectiveDate:    timestamppb.New(p.EffectiveDate),
		EffectiveDateTo:  timestamppb.New(p.EffectiveDateTo),
		AgencyId:         p.AgencyID.String(),
		CreatedAt:        timestamppb.New(p.CreatedAt),
		UpdatedAt:        timestamppb.New(p.UpdatedAt),
		InsuranceCarrier: p.InsuranceCarrier.String(),
		IsNonAdmitted:    p.IsNonAdmitted,
	}
}

func (s *getNirvanaPoliciesV1TestSuite) Test_Simple() {
	dotNumber := int64(123456)
	effectiveDate := time.Now()

	policy1 := s.createMockPolicy(uuid.New())
	policy2 := s.createMockPolicy(uuid.New())
	mockPolicies := []*policy.Policy{policy1, policy2}

	s.mockPolicyWrapper.
		EXPECT().
		GetAllPolicies(gomock.Any(), gomock.Any()).
		Return(mockPolicies, nil).
		Times(1)

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	expectedPolicies := &data_fetching.GetNirvanaPoliciesResponseV1{
		Policies: []*data_fetching.NirvanaPolicyV1{
			s.createExpectedPolicyV1(policy1),
			s.createExpectedPolicyV1(policy2),
		},
	}

	response, err := client.GetNirvanaPoliciesV1(
		s.ctx,
		&data_fetching.GetNirvanaPoliciesRequestV1{
			DotNumber:        dotNumber,
			EffectiveDate:    timestamppb.New(effectiveDate),
			Version:          int64(0),
			MainCoverageType: data_fetching.MainCoverageTypeV1_MainCoverageTypeV1_AutoLiability,
		},
	)
	s.Require().NoError(err)
	s.Require().EqualExportedValues(expectedPolicies, response)
}
