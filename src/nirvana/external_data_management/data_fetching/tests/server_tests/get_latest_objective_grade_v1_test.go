package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/safety/scores/objective_grade/enums"
	"nirvanatech.com/nirvana/safety/scores/objective_grade/manager"
)

func Test_Simple(t *testing.T) {
	var env struct {
		fx.In

		FetcherClientFactory data_fetching.FetcherClientFactory
	}

	ctrl, ctx := gomock.WithContext(context.TODO(), t)
	mockObjectiveGradeManager := manager.NewMockObjectiveGradeManager(ctrl)
	newMockObjectiveGradeManager := func() manager.ObjectiveGradeManager {
		return mockObjectiveGradeManager
	}
	testloader.RequireStart(t, &env, testloader.Use(fx.Decorate(newMockObjectiveGradeManager)))

	dotNumber := int64(1234567)
	now := time.Now()
	mockObjectiveGrade := builders.BuildDefaultRawObjectiveGradeV1ForTest(dotNumber, enums.ObjectiveGradeA, now)
	mockObjectiveGradeManager.
		EXPECT().
		FetchLatestObjectiveGradeForDot(gomock.Any(), dotNumber).
		Return(mockObjectiveGrade, nil).
		Times(1)

	client, closer, err := env.FetcherClientFactory()
	require.NoError(t, err)
	defer func() { _ = closer() }()

	request := &data_fetching.LatestObjectiveGradeRequestV1{
		DotNumber: dotNumber,
	}

	response, err := client.GetLatestObjectiveGradeV1(
		ctx,
		request,
	)
	require.NoError(t, err)

	expectedObjectiveGrade := builders.BuildDefaultObjectiveGradeV1ForTest(
		dotNumber,
		data_fetching.ObjectiveGradeScoreV1_A,
		now,
	)
	//goland:noinspection GoVetCopyLock
	require.EqualExportedValues(t, *expectedObjectiveGrade, *response)
}
