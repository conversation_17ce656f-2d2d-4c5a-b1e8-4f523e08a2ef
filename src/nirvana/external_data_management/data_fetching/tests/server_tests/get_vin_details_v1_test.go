package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
	nhtsaEnums "nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/servers/vehicles"
)

type getVINDetailsV1TestEnv struct {
	fx.In
	FetcherClientFactory data_fetching.FetcherClientFactory
	VehicleServiceClient vehicles.VehiclesServiceClient
}

type getVINDetailsV1TestSuite struct {
	suite.Suite
	ctx             context.Context
	env             getVINDetailsV1TestEnv
	fxapp           *fxtest.App
	mockNhtsaClient *nhtsa.MockClient
}

func TestGetVINDetailsV1(t *testing.T) {
	suite.Run(t, new(getVINDetailsV1TestSuite))
}

func (s *getVINDetailsV1TestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.mockNhtsaClient = nhtsa.NewMockClient(s.env.VehicleServiceClient).(*nhtsa.MockClient)

	newMockNHTSAClient := func() nhtsa.Client {
		return s.mockNhtsaClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockNHTSAClient)),
	)
}

func (s *getVINDetailsV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getVINDetailsV1TestSuite) Test_Simple() {
	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	type testCase struct {
		name                  string
		make                  string
		model                 string
		modelYear             string
		manufacturer          string
		trim                  string
		bodyClass             string
		rawVehicleType        nhtsaEnums.VehicleType
		rawWeightClass        nhtsaEnums.WeightClass
		vehicleType           data_fetching.VehicleTypeV1
		weightClass           data_fetching.WeightClassV1
		shouldMockDecodeError bool
		errText               string
		rawErrorCodes         []nhtsaEnums.ErrorCode
		errorCodes            []data_fetching.NhtsaErrorCodeV1
	}

	vin := "4T1BK1EB4EU085617"

	testCases := []testCase{
		{
			name:           "successful decoding",
			make:           "Chevrolet",
			model:          "Silverado",
			modelYear:      "2007",
			manufacturer:   "GM",
			trim:           "LTZ",
			bodyClass:      "Pickup-Truck",
			rawVehicleType: nhtsaEnums.VehicleTypeTruck,
			rawWeightClass: nhtsaEnums.WeightClassA,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Truck,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_A,
		},
		// We test the unsuccessful decoding case (i.e. with decode error).
		// We also test that all error codes are correctly transformed.
		{
			name:                  "unsuccessful decoding",
			rawVehicleType:        nhtsaEnums.VehicleTypeIncompleteVehicle,
			rawWeightClass:        nhtsaEnums.WeightClassNil,
			vehicleType:           data_fetching.VehicleTypeV1_VehicleTypeV1_IncompleteVehicle,
			weightClass:           data_fetching.WeightClassV1_WeightClassV1_Nil,
			shouldMockDecodeError: true,
			errText:               "decode error",
			rawErrorCodes: []nhtsaEnums.ErrorCode{
				nhtsaEnums.ErrorCode0,
				nhtsaEnums.ErrorCode1,
				nhtsaEnums.ErrorCode2,
				nhtsaEnums.ErrorCode3,
				nhtsaEnums.ErrorCode4,
				nhtsaEnums.ErrorCode5,
				nhtsaEnums.ErrorCode6,
				nhtsaEnums.ErrorCode7,
				nhtsaEnums.ErrorCode8,
				nhtsaEnums.ErrorCode9,
				nhtsaEnums.ErrorCode10,
				nhtsaEnums.ErrorCode11,
				nhtsaEnums.ErrorCode12,
				nhtsaEnums.ErrorCode14,
				nhtsaEnums.ErrorCode400,
			},
			errorCodes: []data_fetching.NhtsaErrorCodeV1{
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_0,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_1,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_2,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_3,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_4,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_5,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_6,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_7,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_8,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_9,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_10,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_11,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_12,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_14,
				data_fetching.NhtsaErrorCodeV1_NhtsaErrorCodeV1_400,
			},
		},
		// We test all other vehicle types.
		{
			name:           "vehicle type trailer",
			rawVehicleType: nhtsaEnums.VehicleTypeTrailer,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Trailer,
			rawWeightClass: nhtsaEnums.WeightClassNil,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_Nil,
		},
		{
			name:           "vehicle type motorcycle",
			rawVehicleType: nhtsaEnums.VehicleTypeMotorcycle,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Motorcycle,
			rawWeightClass: nhtsaEnums.WeightClassNil,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_Nil,
		},
		{
			name:           "vehicle type unspecified",
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
			rawWeightClass: nhtsaEnums.WeightClassNil,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_Nil,
		},
		{
			name:           "vehicle type passenger car",
			rawVehicleType: nhtsaEnums.VehicleTypePassengerCar,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_PassengerCar,
			rawWeightClass: nhtsaEnums.WeightClassNil,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_Nil,
		},
		{
			name:           "vehicle type multipurpose passenger vehicle",
			rawVehicleType: nhtsaEnums.VehicleTypeMultipurposePassengerVehicle,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_MultipurposePassengerVehicle,
			rawWeightClass: nhtsaEnums.WeightClassNil,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_Nil,
		},
		{
			name:           "vehicle type bus",
			rawVehicleType: nhtsaEnums.VehicleTypeBus,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Bus,
			rawWeightClass: nhtsaEnums.WeightClassNil,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_Nil,
		},
		// We test all other weight classes.
		{
			name:           "weight class B",
			rawWeightClass: nhtsaEnums.WeightClassB,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_B,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class C",
			rawWeightClass: nhtsaEnums.WeightClassC,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_C,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class D",
			rawWeightClass: nhtsaEnums.WeightClassD,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_D,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class E",
			rawWeightClass: nhtsaEnums.WeightClassE,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_E,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class F",
			rawWeightClass: nhtsaEnums.WeightClassF,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_F,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class G",
			rawWeightClass: nhtsaEnums.WeightClassG,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_G,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class H",
			rawWeightClass: nhtsaEnums.WeightClassH,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_H,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class 3",
			rawWeightClass: nhtsaEnums.WeightClass3,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_3,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class 4",
			rawWeightClass: nhtsaEnums.WeightClass4,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_4,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class 5",
			rawWeightClass: nhtsaEnums.WeightClass5,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_5,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class 6",
			rawWeightClass: nhtsaEnums.WeightClass6,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_6,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class 7",
			rawWeightClass: nhtsaEnums.WeightClass7,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_7,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
		{
			name:           "weight class 8",
			rawWeightClass: nhtsaEnums.WeightClass8,
			weightClass:    data_fetching.WeightClassV1_WeightClassV1_8,
			rawVehicleType: nhtsaEnums.VehicleTypeNil,
			vehicleType:    data_fetching.VehicleTypeV1_VehicleTypeV1_Nil,
		},
	}

	for _, tc := range testCases {
		s.T().Run(tc.name, func(t *testing.T) {
			var mockDecodeError *nhtsa.VinDecodeError
			if tc.shouldMockDecodeError {
				mockDecodeError = &nhtsa.VinDecodeError{
					ErrorCodes: tc.rawErrorCodes,
					ErrText:    tc.errText,
				}
			}
			mockRawDetails := &nhtsa.VINDetails{
				Make:         tc.make,
				Model:        tc.model,
				ModelYear:    tc.modelYear,
				Manufacturer: tc.manufacturer,
				Trim:         tc.trim,
				BodyClass:    tc.bodyClass,
				Type:         tc.rawVehicleType,
				WeightClass:  tc.rawWeightClass,
				DecodeError:  mockDecodeError,
			}
			s.mockNhtsaClient.AddMockVINDetails(vin, mockRawDetails)

			actualDetails, err := client.GetVINDetailsV1(s.ctx, &data_fetching.VINDetailsRequestV1{Vin: vin})
			s.Require().NoError(err)

			var expectedDecodeError *data_fetching.VINDecodeErrorV1
			if tc.shouldMockDecodeError {
				expectedDecodeError = &data_fetching.VINDecodeErrorV1{
					NhtsaErrorCodes: tc.errorCodes,
					ErrText:         tc.errText,
				}
			}
			expectedDetails := &data_fetching.VINDetailsV1{
				Make:         tc.make,
				Model:        tc.model,
				ModelYear:    tc.modelYear,
				Manufacturer: tc.manufacturer,
				Trim:         tc.trim,
				BodyClass:    tc.bodyClass,
				VehicleType:  tc.vehicleType,
				WeightClass:  tc.weightClass,
				DecodeError:  expectedDecodeError,
			}
			//goland:noinspection GoVetCopyLock
			s.Require().EqualExportedValues(*expectedDetails, *actualDetails)
		})
	}
}
