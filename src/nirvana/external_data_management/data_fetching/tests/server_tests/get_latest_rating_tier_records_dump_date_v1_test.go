package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/internal/sentry_inputs"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getLatestRatingTierRecordsDumpDateV1TestEnv struct {
	fx.In

	FetcherClientFactory data_fetching.FetcherClientFactory
}

type getLatestRatingTierRecordsDumpDateV1TestSuite struct {
	suite.Suite
	ctx                    context.Context
	env                    getLatestRatingTierRecordsDumpDateV1TestEnv
	fxapp                  *fxtest.App
	mockSentryInputsClient *sentry_inputs.MockClient
}

func TestGetLatestRatingTierRecordsDumpDateV1(t *testing.T) {
	suite.Run(t, new(getLatestRatingTierRecordsDumpDateV1TestSuite))
}

func (s *getLatestRatingTierRecordsDumpDateV1TestSuite) SetupSuite() {
	ctrl, ctx := gomock.WithContext(context.TODO(), s.T())
	s.ctx = ctx
	s.mockSentryInputsClient = sentry_inputs.NewMockClient(ctrl)

	newMockSentryInputsClient := func() sentry_inputs.Client {
		return s.mockSentryInputsClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockSentryInputsClient)),
	)
}

func (s *getLatestRatingTierRecordsDumpDateV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getLatestRatingTierRecordsDumpDateV1TestSuite) Test_Simple() {
	dotNumber := int64(123456)

	mockDate := time_utils.NewDate(2024, 4, 1).ToTime()

	s.mockSentryInputsClient.
		EXPECT().
		GetLatestSentryDumpDateForDOT(gomock.Any(), dotNumber).
		Return(&mockDate, nil).
		Times(1)

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	request := &data_fetching.LatestRatingTiersDumpDateRequestV1{
		DotNumber: dotNumber,
	}

	response, err := client.GetLatestRatingTierRecordsDumpDateV1(
		s.ctx,
		request,
	)
	s.Require().NoError(err)
	s.Equal(mockDate.Unix(), response.Date.AsTime().Unix())
}
