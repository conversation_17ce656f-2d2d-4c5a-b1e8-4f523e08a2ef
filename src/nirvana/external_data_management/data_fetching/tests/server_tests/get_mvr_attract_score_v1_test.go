package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/mvr"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getMVRAttractScoreV1TestEnv struct {
	fx.In
	FetcherClientFactory data_fetching.FetcherClientFactory
}

type getMVRAttractScoreV1TestSuite struct {
	suite.Suite
	ctx           context.Context
	env           getMVRAttractScoreV1TestEnv
	fxapp         *fxtest.App
	mockMVRClient *mvr.MockMVRClient
}

func TestGetMVRAttractScoreV1(t *testing.T) {
	suite.Run(t, new(getMVRAttractScoreV1TestSuite))
}

func (s *getMVRAttractScoreV1TestSuite) SetupSuite() {
	s.ctx = context.Background()

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockMVRClient = mvr.NewMockMVRClient(ctrl)

	newMockMVRClient := func() mvr.OptionalMVRClient {
		return s.mockMVRClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockMVRClient)),
	)
}

func (s *getMVRAttractScoreV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getMVRAttractScoreV1TestSuite) Test_Simple() {
	dlNumber := "123456"
	mockScore := int64(100)
	s.mockMVRClient.
		EXPECT().
		GetAttractScore(gomock.Any(), &mvr.GetAttractScoreRequest{DlNumber: dlNumber}).
		Return(&mvr.GetAttractScoreResponse{AttractScore: mockScore}, nil).
		Times(1)

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	response, err := client.GetMVRAttractScoreV1(s.ctx, &data_fetching.MVRAttractScoreRequestV1{DlNumber: dlNumber})
	s.Require().NoError(err)
	s.Require().Equal(mockScore, response.Score)
}
