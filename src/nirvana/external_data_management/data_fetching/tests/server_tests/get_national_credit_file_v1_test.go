package server_tests

import (
	"context"
	"testing"

	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"
	"nirvanatech.com/nirvana/external_data_management/mvr"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getNationalCreditFileV1TestEnv struct {
	fx.In
	FetcherClientFactory data_fetching.FetcherClientFactory
}

type getNationalCreditFileV1TestSuite struct {
	suite.Suite
	ctx           context.Context
	env           getNationalCreditFileV1TestEnv
	fxapp         *fxtest.App
	mockMVRClient *mvr.MockMVRClient
}

func TestGetNationalCreditFileV1(t *testing.T) {
	suite.Run(t, new(getNationalCreditFileV1TestSuite))
}

func (s *getNationalCreditFileV1TestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockMVRClient = mvr.NewMockMVRClient(ctrl)

	newMockMVRClient := func() mvr.OptionalMVRClient {
		return s.mockMVRClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockMVRClient)),
	)
}

func (s *getNationalCreditFileV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getNationalCreditFileV1TestSuite) Test_GetNationalCreditFileV1() {
	ssnLastFour := pointer_utils.String("6789")
	encryptedSSN := []byte{63, 127, 3, 1, 1, 16, 101, 110, 99, 114, 121, 112, 116, 101, 100, 68}

	testCases := []struct {
		name           string
		applyOverrides func(req *data_fetching.NationalCreditFileRequestV1, rawReq *mvr.GetNationalCreditFileRequest)
	}{
		{
			name: "With SSN Fields",
			applyOverrides: func(req *data_fetching.NationalCreditFileRequestV1, rawReq *mvr.GetNationalCreditFileRequest) {
				req.EncryptedSSN = encryptedSSN
				req.SsnLastFour = ssnLastFour
				rawReq.EncryptedSSN = encryptedSSN
				rawReq.SsnLastFour = ssnLastFour
			},
		},
		{
			name:           "Without SSN Fields",
			applyOverrides: func(req *data_fetching.NationalCreditFileRequestV1, rawReq *mvr.GetNationalCreditFileRequest) {}, // no-op
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			request := builders.BuildNCFRequestV1()
			rawRequest := builders.BuildNCFRequest()
			tc.applyOverrides(request, rawRequest)

			rawResponse, err := builders.BuildDefaultNationalCreditFileForTest()
			s.Require().NoError(err)

			s.mockMVRClient.
				EXPECT().
				GetNationalCreditFile(gomock.Any(), gomock.Any()).
				DoAndReturn(func(
					ctx context.Context,
					req *mvr.GetNationalCreditFileRequest,
					_ ...grpc.CallOption,
				) (*mvr.GetNationalCreditFileResponse, error) {
					//nolint
					s.Require().EqualExportedValues(*rawRequest, *req)
					return rawResponse, nil
				}).
				Times(1)

			client, closer, err := s.env.FetcherClientFactory()
			s.Require().NoError(err)
			defer func() { _ = closer() }()

			response, err := client.GetNationalCreditFileV1(s.ctx, request)
			s.Require().NoError(err)

			expectedSubject := rawResponse.NcfReport.NcfProductReport.SubjectInfo.Subject
			actualSubject := response.NcfReport.NcfProductReport.SubjectInfo.Subject
			s.Require().Equal(expectedSubject.Name.First, actualSubject.Name.First)
			s.Require().Equal(expectedSubject.Name.Last, actualSubject.Name.Last)
			s.Require().Equal(expectedSubject.Dob.Year, actualSubject.Dob.Year)
		})
	}
}
