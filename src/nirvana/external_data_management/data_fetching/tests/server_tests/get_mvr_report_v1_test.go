package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/mvr"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getMVRReportV1TestEnv struct {
	fx.In

	StoreManager                    store_management.StoreManager
	FetcherClientFactory            data_fetching.FetcherClientFactory
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
}

type getMVRReportV1TestSuite struct {
	suite.Suite
	ctx           context.Context
	env           getMVRReportV1TestEnv
	fxapp         *fxtest.App
	mockMVRClient *mvr.MockMVRClient
	report        *data_fetching.MVRReportV1
	rawReport     *mvr.Report
	request       *data_fetching.MVRReportRequestV1
	rawRequest    *mvr.FetchMVRRequest
}

// In this suite I'm also testing the usage and integration of interceptors, both user provided and predefined,
// with the fetcher client. When adding new methods to the fetcher server, you don't need to test this again.
// Just test behavior that is specific to your method.
func TestGetMVRReportV1(t *testing.T) {
	suite.Run(t, new(getMVRReportV1TestSuite))
}

func (s *getMVRReportV1TestSuite) SetupSuite() {
	s.ctx = context.Background()

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockMVRClient = mvr.NewMockMVRClient(ctrl)

	newMockMVRClient := func() mvr.OptionalMVRClient {
		return s.mockMVRClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockMVRClient)),
	)
}

func (s *getMVRReportV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getMVRReportV1TestSuite) mockMVRClientDefault() {
	now := time.Now()
	s.request = builders.BuildDefaultMVRReportRequestV1ForTest(1, now)
	s.rawRequest = builders.BuildDefaultRawMVRFetchRequestForTest(1, now)
	s.report = builders.BuildDefaultMVRReportV1ForTest(1, now)
	s.rawReport = builders.BuildDefaultRawMVRReportForTest(1, now)

	dlNumber := s.request.DlNumber
	fetchMVRCall := s.mockMVRClient.
		EXPECT().
		FetchMVR(gomock.Any(), s.rawRequest).
		Return(&mvr.FetchMVRResponse{RequestID: dlNumber}, nil).
		Times(1)
	s.mockMVRClient.
		EXPECT().
		PollMVR(gomock.Any(), &mvr.PollMVRRequest{RequestID: dlNumber}).
		After(fetchMVRCall).
		Return(s.rawReport, nil).
		Times(1)
}

func (s *getMVRReportV1TestSuite) TestWithoutInterceptors() {
	s.mockMVRClientDefault()

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	report, err := client.GetMVRReportV1(s.ctx, s.request)
	s.Require().NoError(err)

	//nolint
	s.Require().EqualExportedValues(*report, *s.report)
}

func (s *getMVRReportV1TestSuite) TestWithStoreInterceptor() {
	s.mockMVRClientDefault()

	contextID := uuid.New()

	interceptors := interceptors_management.NewWritableStoreFirstInterceptors(
		s.env.ReadFromStoreInterceptorFactory,
		s.env.WriteToStoreInterceptorFactory,
		contextID,
	)

	client, closer, err := s.env.FetcherClientFactory(interceptors...)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	exists, err := s.env.StoreManager.Exists(s.ctx, contextID, s.request)
	s.Require().NoError(err)
	s.Require().False(exists)

	report, err := client.GetMVRReportV1(s.ctx, s.request)
	s.Require().NoError(err)
	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*report, *s.report)

	exists, err = s.env.StoreManager.Exists(s.ctx, contextID, s.request)
	s.Require().NoError(err)
	s.Require().True(exists)

	report, err = client.GetMVRReportV1(s.ctx, s.request)
	s.Require().NoError(err)
	//goland:noinspection GoVetCopyLock
	s.Require().EqualExportedValues(*report, *s.report)
}

func (s *getMVRReportV1TestSuite) TestWithCustomInterceptor() {
	originalDlNumber := "12345"
	modifiedDlNumber := "987654"
	originalDlState := "OH"
	modifiedDlState := "CA"

	fetchMVRCall := s.mockMVRClient.
		EXPECT().
		FetchMVR(gomock.Any(), &mvr.FetchMVRRequest{DlNumber: modifiedDlNumber}).
		Return(&mvr.FetchMVRResponse{RequestID: modifiedDlNumber}, nil).
		Times(1)
	s.mockMVRClient.
		EXPECT().
		PollMVR(gomock.Any(), &mvr.PollMVRRequest{RequestID: modifiedDlNumber}).
		After(fetchMVRCall).
		Return(&mvr.Report{DlNumber: modifiedDlNumber, DlState: originalDlState}, nil).
		Times(1)

	customInterceptor := func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		rq, ok := req.(*data_fetching.MVRReportRequestV1)
		s.Require().True(ok)
		s.Require().Equal(rq.DlNumber, originalDlNumber)
		rq.DlNumber = modifiedDlNumber
		err := invoker(ctx, method, req, reply, cc, opts...)
		s.Require().NoError(err)
		resource, ok := reply.(*common.Resource)
		s.Require().True(ok)
		rp, ok := resource.Data.(*data_fetching.MVRReportV1)
		s.Require().True(ok)
		s.Require().Equal(rp.DlState, originalDlState)
		rp.DlState = modifiedDlState
		return nil
	}

	client, closer, err := s.env.FetcherClientFactory(customInterceptor)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	request := data_fetching.MVRReportRequestV1{DlNumber: originalDlNumber}
	report, err := client.GetMVRReportV1(s.ctx, &request)
	s.Require().NoError(err)
	s.Require().Equal(modifiedDlNumber, report.DlNumber)
	s.Require().Equal(modifiedDlState, report.DlState)
}
