package server_tests

import (
	"context"
	"testing"
	"time"

	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/internal/sentry_inputs"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getRatingTierRecordsForDumpDateV1TestEnv struct {
	fx.In

	FetcherClientFactory data_fetching.FetcherClientFactory
}

type getRatingTierRecordsForDumpDateV1TestSuite struct {
	suite.Suite

	ctx                    context.Context
	env                    getRatingTierRecordsForDumpDateV1TestEnv
	fxapp                  *fxtest.App
	mockSentryInputsClient *sentry_inputs.MockClient
}

func TestGetRatingTierRecordsForDumpDateV1(t *testing.T) {
	suite.Run(t, new(getRatingTierRecordsForDumpDateV1TestSuite))
}

func (s *getRatingTierRecordsForDumpDateV1TestSuite) SetupSuite() {
	ctrl, ctx := gomock.WithContext(context.TODO(), s.T())
	s.ctx = ctx
	s.mockSentryInputsClient = sentry_inputs.NewMockClient(ctrl)

	newMockSentryInputsClient := func() sentry_inputs.Client {
		return s.mockSentryInputsClient
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockSentryInputsClient)),
	)
}

func (s *getRatingTierRecordsForDumpDateV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getRatingTierRecordsForDumpDateV1TestSuite) TestGetRatingTierRecordsForDumpDateV1_Simple() {
	dotNumber := int64(1234567)
	dumpDate := time.Now().AddDate(0, -1, 0)

	request := &data_fetching.RatingTierRecordsForDumpDateRequestV1{
		DotNumber: dotNumber,
		DumpDate:  timestamppb.New(dumpDate),
	}
	mockResponse := builders.BuildDefaultRawRatingTierRecordsForDumpDateV1ForTest(dotNumber, dumpDate)

	s.mockSentryInputsClient.EXPECT().
		GetMonthlySentryInputsForDOTAndDumpDate(gomock.Any(), request.DotNumber, request.DumpDate.AsTime()).
		Return(mockResponse, nil).
		Times(1)

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	response, err := client.GetRatingTierRecordsForDumpDateV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().NotNil(response)
	records := response.GetRecords()
	s.Require().Len(records, 2)
	expectedResponse := builders.BuildDefaultRatingTierRecordsForDumpDateV1ForTest(dumpDate)
	for idx, record := range records {
		//goland:noinspection GoVetCopyLock
		s.EqualExportedValues(expectedResponse[idx], *record)
	}
}
