package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_fetching/tests/server_tests/builders"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getFMCSACensusInfoV1TestEnv struct {
	fx.In

	FetcherClientFactory data_fetching.FetcherClientFactory
}

type getFMCSACensusInfoV1TestSuite struct {
	suite.Suite
	ctx              context.Context
	env              getFMCSACensusInfoV1TestEnv
	fxapp            *fxtest.App
	mockFMCSAWrapper *fmcsa.MockDataWrapper
}

func TestGetFMCSACensusInfoV1(t *testing.T) {
	suite.Run(t, new(getFMCSACensusInfoV1TestSuite))
}

func (s *getFMCSACensusInfoV1TestSuite) SetupSuite() {
	s.ctx = context.Background()

	ctrl, ctx := gomock.WithContext(s.ctx, s.T())
	s.ctx = ctx
	s.mockFMCSAWrapper = fmcsa.NewMockDataWrapper(ctrl)

	newMockFMCSAWrapper := func() fmcsa.DataWrapper {
		return s.mockFMCSAWrapper
	}

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockFMCSAWrapper)),
	)
}

func (s *getFMCSACensusInfoV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getFMCSACensusInfoV1TestSuite) Test_Simple() {
	dotNumber := 1460168

	client, closer, err := s.env.FetcherClientFactory()
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	mockDotDetails := builders.BuildDefaultMockDotDetailsForTest(int64(dotNumber))
	s.mockFMCSAWrapper.
		EXPECT().
		GetDetailsByDot(gomock.Any(), int64(dotNumber)).
		Return(mockDotDetails, nil).
		Times(1)

	response, err := client.GetFMCSACensusInfoV1(
		s.ctx,
		&data_fetching.FMCSACensusInfoRequestV1{
			DotNumber: int64(dotNumber),
		},
	)
	s.Require().NoError(err)

	expectedDotDetails := builders.BuildDefaultFMCSACensusInfoV1ForTest()
	s.Require().True(proto.Equal(expectedDotDetails, response))
}
