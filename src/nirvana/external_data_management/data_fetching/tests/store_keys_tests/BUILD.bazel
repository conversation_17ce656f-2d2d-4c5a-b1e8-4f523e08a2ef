load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "store_keys_tests_test",
    srcs = [
        "bipd_active_pending_insurance_request_v1_test.go",
        "bipd_insurance_history_request_v1_test.go",
        "fmcsa_census_info_request_v1_test.go",
        "fmcsa_inspection_records_request_v1_test.go",
        "fmcsa_violation_records_request_v1_test.go",
        "latest_objective_grade_request_v1_test.go",
        "mvr_attract_score_request_v1_test.go",
        "mvr_report_request_v1_test.go",
        "national_credit_file_request_v1_test.go",
        "nirvana_policies_request_v1_test.go",
        "vin_details_request_v1_test.go",
    ],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/data_fetching",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
