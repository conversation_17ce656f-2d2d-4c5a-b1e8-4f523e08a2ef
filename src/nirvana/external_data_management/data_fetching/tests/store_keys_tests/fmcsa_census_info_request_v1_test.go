package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestFMCSACensusInfoRequestV1(t *testing.T) {
	request := &data_fetching.FMCSACensusInfoRequestV1{
		DotNumber: 123456789,
	}

	expectedFileNameComponents := []string{"123456789"}
	fileNameComponents, err := request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
