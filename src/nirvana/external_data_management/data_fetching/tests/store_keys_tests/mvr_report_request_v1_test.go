package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestMVRReportRequestV1(t *testing.T) {
	dlNumber := "dl1"
	usState := "OH"
	storeKey := data_fetching.MVRReportRequestV1{DlNumber: dlNumber, UsState: usState}
	fileNameComponents, err := storeKey.GetFileNameComponents()
	require.NoError(t, err)
	expectedFileNameComponents := []string{"OH", "dl1"}
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
