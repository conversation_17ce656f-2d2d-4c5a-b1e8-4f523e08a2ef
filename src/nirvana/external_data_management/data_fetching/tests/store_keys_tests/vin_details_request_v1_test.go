package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestVINDetailsRequestV1(t *testing.T) {
	vin := "whatever"
	storeKey := data_fetching.VINDetailsRequestV1{Vin: vin}
	fileNameComponents, err := storeKey.GetFileNameComponents()
	require.NoError(t, err)
	expectedFileNameComponents := []string{vin}
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
