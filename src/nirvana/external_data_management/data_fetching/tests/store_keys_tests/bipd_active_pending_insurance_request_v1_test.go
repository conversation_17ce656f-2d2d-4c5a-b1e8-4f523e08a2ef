package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestBIPDActiveOrPendingInsuranceRequestV1(t *testing.T) {
	tests := []struct {
		name                       string
		request                    *data_fetching.BIPDActiveOrPendingInsuranceRequestV1
		expectedFileNameComponents []string
	}{
		{
			name: "Valid DOT number",
			request: &data_fetching.BIPDActiveOrPendingInsuranceRequestV1{
				DotNumber: 123456789,
			},
			expectedFileNameComponents: []string{"123456789"},
		},
		{
			name: "Valid DOT number, with additional request for excess coverage records",
			request: &data_fetching.BIPDActiveOrPendingInsuranceRequestV1{
				DotNumber:                   123456789,
				ShouldIncludeExcessCoverage: true,
			},
			expectedFileNameComponents: []string{"123456789", "IncludedExcessCoverage"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fileNameComponents, err := tt.request.GetFileNameComponents()
			require.NoError(t, err)
			require.Equal(t, tt.expectedFileNameComponents, fileNameComponents)
		})
	}
}
