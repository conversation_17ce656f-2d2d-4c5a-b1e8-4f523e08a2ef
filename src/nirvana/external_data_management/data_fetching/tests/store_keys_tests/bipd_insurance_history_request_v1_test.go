package store_keys_tests

import (
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestBIPDInsuranceHistoryRequestV1(t *testing.T) {
	sinceDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	request := &data_fetching.BIPDInsuranceHistoryRequestV1{
		DotNumber: 123456789,
		SinceDate: timestamppb.New(sinceDate),
	}

	expectedFileNameComponents := []string{"123456789", "01012024"}
	fileNameComponents, err := request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
