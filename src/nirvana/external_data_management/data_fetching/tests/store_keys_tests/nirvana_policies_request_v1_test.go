package store_keys_tests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestNirvanaPoliciesForDOTRequestV1(t *testing.T) {
	baseTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name                       string
		request                    *data_fetching.GetNirvanaPoliciesRequestV1
		expectedFileNameComponents []string
	}{
		{
			name: "Valid DOT number with effective date",
			request: &data_fetching.GetNirvanaPoliciesRequestV1{
				DotNumber:     123456789,
				EffectiveDate: timestamppb.New(baseTime),
			},
			expectedFileNameComponents: []string{
				"123456789",
				"01.01.2023",
			},
		},
		{
			name: "Different DOT number and date",
			request: &data_fetching.GetNirvanaPoliciesRequestV1{
				DotNumber:     987654321,
				EffectiveDate: timestamppb.New(baseTime.AddDate(0, 1, 0)), // Add one month
			},
			expectedFileNameComponents: []string{
				"987654321",
				"02.01.2023",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fileNameComponents, err := tt.request.GetFileNameComponents()
			require.NoError(t, err)
			require.Equal(t, tt.expectedFileNameComponents, fileNameComponents)
		})
	}
}
