package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestNationalCreditFileRequestV1(t *testing.T) {
	defaultNCFRequest := func() *data_fetching.NationalCreditFileRequestV1 {
		return &data_fetching.NationalCreditFileRequestV1{
			FirstName:     "John",
			LastName:      "Doe",
			ApplicationID: "1fefd893-2c02-4ca5-a250-3b6b37d6b794",
			Dob:           timestamppb.New(time_utils.NewDate(1962, 5, 18).ToTime()),
			Address: &data_fetching.AddressV1{
				Street: "3521 GOLDENEYE DR",
				City:   "CHARLOTTE",
				State:  "NC",
				Zip:    "28216",
			},
		}
	}

	testCases := []struct {
		name             string
		applyOverrides   func(req *data_fetching.NationalCreditFileRequestV1)
		expectedFileName string
	}{
		{
			name: "With SsnLastFour",
			applyOverrides: func(req *data_fetching.NationalCreditFileRequestV1) {
				req.SsnLastFour = pointer_utils.String("1234")
			},
			expectedFileName: "ebbf3adc056b61d4650f2430f294a1acfc8596f472ff4fdffdb60ace1058144d",
		},
		{
			name:             "Without SsnLastFour",
			applyOverrides:   func(req *data_fetching.NationalCreditFileRequestV1) {},
			expectedFileName: "f27fae5795496d48592d42607377a002e02317bac4ebed6f08812890facf6fba",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ncfRequest := defaultNCFRequest()
			tc.applyOverrides(ncfRequest)

			fileNameComponents, err := ncfRequest.GetFileNameComponents()
			require.NoError(t, err)
			require.Equal(t, []string{tc.expectedFileName}, fileNameComponents)
		})
	}
}
