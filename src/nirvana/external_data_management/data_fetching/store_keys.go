package data_fetching

import (
	"strconv"

	"nirvanatech.com/nirvana/common-go/crypto_utils"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

/*
	These methods and constants are used to build the path on S3 where data will be stored.
	DO NOT MODIFY EXISTING CODE, ONLY ADD NEW.

	If you are adding a method to the fetcher, make sure that your request implements store_management.StoreKey
	interface so your method can be wrapped by the store interceptor.
*/

const (
	mvrReportV1ResourceType                    = "MVRReportV1"
	mvrAttractScoreV1ResourceType              = "MVRAttractScoreV1"
	vinDetailsV1ResourceType                   = "VINDetailsV1"
	nationalCreditFileV1ResourceType           = "NationalCreditFileV1"
	grantedAuthorityHistoryV1ResourceType      = "GrantedAuthorityHistoryV1"
	pricingExperimentV1ResourceType            = "PricingExperimentV1"
	ratingTierRecordV1ResourceType             = "RatingTierRecordV1"
	latestRatingTiersDumpDateV1ResourceType    = "LatestRatingTiersDumpDateV1"
	ratingTierRecordsForDumpDateV1ResourceType = "RatingTierRecordsForDumpDateV1"
	fmcsaViolationRecordsV1ResourceType        = "FMCSAViolationRecordsV1"
	fmcsaInspectionRecordsV1ResourceType       = "FMCSAInspectionRecordsV1"
	fmcsaCensusInfoV1ResourceType              = "FMCSACensusInfoV1"
	bipdInsuranceHistoryV1ResourceType         = "BIPDInsuranceHistoryV1"
	bipcActiveOrPendingInsuranceV1ResourceType = "BIPDActiveOrPendingInsuranceV1"
	latestObjectiveGradeV1ResourceType         = "LatestObjectiveGradeV1"
	involuntaryRevocationsV1ResourceType       = "InvoluntaryRevocationsV1"
	nirvanaPoliciesV1ResourceType              = "NirvanaPoliciesV1"
)

func (r *MVRReportRequestV1) GetResourceType() string {
	return mvrReportV1ResourceType
}

func (r *MVRAttractScoreRequestV1) GetResourceType() string {
	return mvrAttractScoreV1ResourceType
}

func (r *VINDetailsRequestV1) GetResourceType() string {
	return vinDetailsV1ResourceType
}

func (r *NationalCreditFileRequestV1) GetResourceType() string {
	return nationalCreditFileV1ResourceType
}

func (r *GrantedAuthorityHistoryRequestV1) GetResourceType() string {
	return grantedAuthorityHistoryV1ResourceType
}

func (r *RatingTierRecordRequestV1) GetResourceType() string {
	return ratingTierRecordV1ResourceType
}

func (r *LatestRatingTiersDumpDateRequestV1) GetResourceType() string {
	return latestRatingTiersDumpDateV1ResourceType
}

func (r *RatingTierRecordsForDumpDateRequestV1) GetResourceType() string {
	return ratingTierRecordsForDumpDateV1ResourceType
}

func (r *FMCSAViolationRecordsRequestV1) GetResourceType() string {
	return fmcsaViolationRecordsV1ResourceType
}

func (r *FMCSAInspectionRecordsRequestV1) GetResourceType() string {
	return fmcsaInspectionRecordsV1ResourceType
}

func (r *FMCSACensusInfoRequestV1) GetResourceType() string {
	return fmcsaCensusInfoV1ResourceType
}

func (r *BIPDInsuranceHistoryRequestV1) GetResourceType() string {
	return bipdInsuranceHistoryV1ResourceType
}

func (r *BIPDActiveOrPendingInsuranceRequestV1) GetResourceType() string {
	return bipcActiveOrPendingInsuranceV1ResourceType
}

func (r *LatestObjectiveGradeRequestV1) GetResourceType() string {
	return latestObjectiveGradeV1ResourceType
}

func (r *InvoluntaryRevocationsRequestV1) GetResourceType() string {
	return involuntaryRevocationsV1ResourceType
}

func (r *GetNirvanaPoliciesRequestV1) GetResourceType() string {
	return nirvanaPoliciesV1ResourceType
}

func (r *MVRReportRequestV1) GetFileNameComponents() ([]string, error) {
	// We assume that, within a context ID, using the driver's US state and license number, is enough to create
	// and ID that allows us to uniquely identify an MVR report request.
	fileNameComponents := []string{
		r.UsState,
		r.DlNumber,
	}

	return fileNameComponents, nil
}

func (r *MVRAttractScoreRequestV1) GetFileNameComponents() ([]string, error) {
	// We assume that, within a context ID, using the driver's US state and license number, is enough to create
	// and ID that allows us to uniquely identify an MVR attract score request.
	fileNameComponents := []string{
		r.UsState,
		r.DlNumber,
	}

	return fileNameComponents, nil
}

func (r *VINDetailsRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{r.Vin}

	return fileNameComponents, nil
}

func (r *NationalCreditFileRequestV1) GetFileNameComponents() ([]string, error) {
	// We are using a hashed-key here to include all the information that
	// is needed to uniquely identify a request. As change in any of the
	// fields(appId, firstName, lastName, dob, address) would also require us to
	// re-pull the credit file instead of getting it from the cache server
	data := r.ApplicationID + r.FirstName + r.LastName + r.Dob.AsTime().String()

	// Include the address fields if the address is not nil
	if r.Address != nil {
		data += r.Address.Street + r.Address.City + r.Address.State + r.Address.Zip
	}
	if r.SsnLastFour != nil {
		data += *r.SsnLastFour
	}
	hashedVal := crypto_utils.HashStringSecret(data)

	fileNameComponents := []string{hashedVal}

	return fileNameComponents, nil
}

func (r *GrantedAuthorityHistoryRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
	}

	return fileNameComponents, nil
}

func (r *LatestRatingTiersDumpDateRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
	}

	return fileNameComponents, nil
}

func (r *RatingTierRecordsForDumpDateRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
		store_management.FormatDateForStoreKey(r.DumpDate),
	}

	return fileNameComponents, nil
}

func (r *RatingTierRecordRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
		store_management.FormatDateForStoreKey(r.DumpDate),
		store_management.FormatDateForStoreKey(r.RecordDate),
	}

	return fileNameComponents, nil
}

func (r *FMCSAViolationRecordsRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(int64(r.DotNumber), 10),
		r.EffectiveDate.AsTime().Format("02012006"),
	}

	return fileNameComponents, nil
}

func (r *FMCSAInspectionRecordsRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(int64(r.DotNumber), 10),
		r.EffectiveDate.AsTime().Format("02012006"),
	}

	return fileNameComponents, nil
}

func (r *FMCSACensusInfoRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
	}

	return fileNameComponents, nil
}

func (r *BIPDInsuranceHistoryRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
		r.SinceDate.AsTime().Format("02012006"),
	}

	return fileNameComponents, nil
}

func (r *BIPDActiveOrPendingInsuranceRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
	}
	if r.ShouldIncludeExcessCoverage {
		fileNameComponents = append(fileNameComponents, "IncludedExcessCoverage")
	}

	return fileNameComponents, nil
}

func (r *LatestObjectiveGradeRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
	}

	return fileNameComponents, nil
}

func (r *InvoluntaryRevocationsRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
		store_management.FormatDateForStoreKey(r.StartDate),
		store_management.FormatDateForStoreKey(r.EndDate),
	}

	return fileNameComponents, nil
}

func (r *GetNirvanaPoliciesRequestV1) GetFileNameComponents() ([]string, error) {
	fileNameComponents := []string{
		strconv.FormatInt(r.DotNumber, 10),
		store_management.FormatDateForStoreKey(r.EffectiveDate),
	}

	return fileNameComponents, nil
}

var (
	_ store_management.StoreKey = &MVRReportRequestV1{}
	_ store_management.StoreKey = &MVRAttractScoreRequestV1{}
	_ store_management.StoreKey = &VINDetailsRequestV1{}
	_ store_management.StoreKey = &NationalCreditFileRequestV1{}
	_ store_management.StoreKey = &GrantedAuthorityHistoryRequestV1{}
	_ store_management.StoreKey = &LatestRatingTiersDumpDateRequestV1{}
	_ store_management.StoreKey = &RatingTierRecordsForDumpDateRequestV1{}
	_ store_management.StoreKey = &RatingTierRecordRequestV1{}
	_ store_management.StoreKey = &FMCSAViolationRecordsRequestV1{}
	_ store_management.StoreKey = &FMCSAInspectionRecordsRequestV1{}
	_ store_management.StoreKey = &FMCSACensusInfoRequestV1{}
	_ store_management.StoreKey = &BIPDInsuranceHistoryRequestV1{}
	_ store_management.StoreKey = &BIPDActiveOrPendingInsuranceRequestV1{}
	_ store_management.StoreKey = &LatestObjectiveGradeRequestV1{}
	_ store_management.StoreKey = &GetNirvanaPoliciesRequestV1{}
)
