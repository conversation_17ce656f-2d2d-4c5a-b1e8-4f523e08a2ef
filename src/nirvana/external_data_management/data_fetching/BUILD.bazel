load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "data_fetching_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_fetching",
    proto = "//proto/data_fetching:data_fetching_proto",
    visibility = ["//visibility:public"],
)

# keep
go_proto_link(
    name = "data_fetching_go_proto_link",
    dep = ":data_fetching_go_proto",
    version = "v1",
)

go_library(
    name = "data_fetching",
    srcs = [
        "client.go",
        "client_mock.go",
        "doc.go",
        "errors.go",
        "fx.go",
        "server.go",
        "server_mock.go",
        "store_keys.go",
        "transformers.go",
    ],
    embed = [":data_fetching_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/external_data_management/data_fetching",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/grpc/libgrpc",
        "//nirvana/common-go/grpc/middleware",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/datagov/active_pending_insurance",
        "//nirvana/db-api/db_wrappers/datagov/authority_history",
        "//nirvana/db-api/db_wrappers/datagov/insurance_history",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/external_data_management/interceptors_management/unwrap_reply_interceptor",
        "//nirvana/external_data_management/interceptors_management/wrap_reply_interceptor",
        "//nirvana/external_data_management/internal/sentry_inputs",
        "//nirvana/external_data_management/mvr",
        "//nirvana/external_data_management/nhtsa",
        "//nirvana/external_data_management/nhtsa/enums",
        "//nirvana/external_data_management/store_management",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/dot_details/enums",
        "//nirvana/fmcsa/lni",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/safety/scores/objective_grade/manager",
        "//nirvana/safety/scores/objective_grade/models",
        "//nirvana/safety/scores/percentiles",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)
