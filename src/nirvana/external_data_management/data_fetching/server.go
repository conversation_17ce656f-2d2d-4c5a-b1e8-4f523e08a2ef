package data_fetching

import (
	"context"
	"strconv"
	"time"

	"github.com/benb<PERSON><PERSON>son/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"go.uber.org/fx"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/external_data_management/internal/sentry_inputs"
	"nirvanatech.com/nirvana/external_data_management/mvr"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/fmcsa/lni"
	"nirvanatech.com/nirvana/safety/scores/objective_grade/manager"
	"nirvanatech.com/nirvana/safety/scores/percentiles"
)

type serverDeps struct {
	fx.In

	MVRClient          mvr.MVRClient
	NHTSAClient        nhtsa.Client
	LNIClient          lni.Client
	SentryInputsClient sentry_inputs.Client

	FMCSAWrapper  fmcsa.DataWrapper
	PolicyWrapper policy.DataWrapper

	ObjectiveGradeManager manager.ObjectiveGradeManager
	BPM                   percentiles.BASICPercentileManager

	Clk           clock.Clock
	MetricsClient statsd.Statter
}

type serverImpl struct {
	deps serverDeps
}

func newServer(deps serverDeps) FetcherServer {
	return &serverImpl{deps: deps}
}

func (s *serverImpl) GetNationalCreditFileV1(
	ctx context.Context,
	request *NationalCreditFileRequestV1,
) (*NationalCreditFileV1, error) {
	rawRequest := transformNationalCreditFileRequestFromInternalFormatToClientFormatV1(request)
	if s.deps.MVRClient == nil {
		return nil, errors.New("MVR client was nil")
	}

	startTime := time.Now()
	rawReport, err := mvr.FetchNationalCreditFile(ctx, s.deps.MVRClient, rawRequest)
	if err != nil {
		err = emitLNCreditReportPullMetric(ctx, s.deps.MetricsClient, false)
		if err != nil {
			log.Error(ctx, "failed to emit metric for failure", log.Err(err))
		}
		log.Error(ctx, "Failed to fetch national credit file V1", log.AppID(request.ApplicationID), log.Err(err))
		return &NationalCreditFileV1{
			NcfReport:             nil,
			TransactionDetails:    &TransactionDetailsExV1{},
			DateOfCreditReportRun: timestamppb.New(s.deps.Clk.Now()),
		}, errors.Wrap(err, "failed to fetch national credit file V1")
	}

	report := transformNationalCreditReportFromClientFormatToInternalFormatV1(rawReport)
	report.DateOfCreditReportRun = timestamppb.New(s.deps.Clk.Now())

	latency := time.Since(startTime)
	log.Info(ctx,
		"latency for credit report run",
		log.String("application_id", request.ApplicationID),
		log.String("latency", latency.String()))

	latencyMetricName := "credit_report_pull_latency"
	metricsClientErr := s.deps.MetricsClient.TimingDuration(latencyMetricName, latency, 1)
	if metricsClientErr != nil {
		log.Error(
			ctx, "failed to emit latency metric for credit report",
			log.String("metricName", latencyMetricName), log.Err(metricsClientErr),
		)
	}

	err = emitLNCreditReportPullMetric(ctx, s.deps.MetricsClient, true)
	if err != nil {
		log.Error(ctx, "failed to emit metric for failure", log.Err(err))
	}

	return report, nil
}

func emitLNCreditReportPullMetric(
	ctx context.Context,
	metricsClient statsd.Statter,
	isSuccess bool,
) error {
	tags := []statsd.Tag{
		{"success", strconv.FormatBool(isSuccess)},
	}
	metricName := "credit_report_pull"
	err := metricsClient.Inc(metricName, 1, 1, tags...)
	if err != nil {
		log.Error(ctx, "failed to emit metric",
			log.String("metricName", metricName), log.Err(err))
		return err
	}
	return nil
}

func (s *serverImpl) GetMVRReportV1(
	ctx context.Context,
	request *MVRReportRequestV1,
) (*MVRReportV1, error) {
	if s.deps.MVRClient == nil {
		return nil, errors.New("MVR client was nil")
	}
	rawRequest := transformMVRReportRequestFromInternalFormatToClientFormatV1(request)
	rawReport, err := mvr.FetchAndPollMVRReport(ctx, s.deps.MVRClient, rawRequest)
	if err != nil {
		if errors.Is(err, mvr.ErrorDeadlineExceeded) {
			return nil, status.Error(codes.DeadlineExceeded, err.Error())
		}
		return nil, errors.Wrap(err, "failed to fetch and poll MVR report V1")
	}
	report := transformMVRReportFromClientFormatToInternalFormatV1(rawReport)
	return report, nil
}

func (s *serverImpl) GetMVRAttractScoreV1(
	ctx context.Context,
	request *MVRAttractScoreRequestV1,
) (*MVRAttractScoreV1, error) {
	if s.deps.MVRClient == nil {
		return nil, errors.New("MVR client was nil")
	}
	rawRequest := transformMVRAttractScoreRequestFromInternalFormatToClientFormatV1(request)
	rawResponse, err := mvr.FetchAttractScore(ctx, s.deps.MVRClient, rawRequest)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch MVR attract score V1")
	}

	retval := MVRAttractScoreV1{
		Score: rawResponse.AttractScore,
	}
	return &retval, nil
}

func (s *serverImpl) GetVINDetailsV1(
	ctx context.Context,
	request *VINDetailsRequestV1,
) (*VINDetailsV1, error) {
	if s.deps.NHTSAClient == nil {
		return nil, errors.New("NHSTA client was nil")
	}
	rawVINDetails, err := s.deps.NHTSAClient.GetVINRecord(ctx, request.Vin)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get VIN record V1")
	}
	vinDetails := transformVINDetailsFromClientFormatToInternalFormatV1(rawVINDetails)
	return vinDetails, nil
}

func (s *serverImpl) GetGrantedAuthorityHistoryV1(
	ctx context.Context,
	request *GrantedAuthorityHistoryRequestV1,
) (*GrantedAuthorityHistoryV1, error) {
	if s.deps.LNIClient == nil {
		return nil, errors.New("nil LNI client")
	}
	rawGrantedAuthorityHistory, err := s.deps.LNIClient.GetGrantedAuthorityHistoryV1(ctx, request.DotNumber)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get Authority History V1")
	}
	grantedAuthorityHistory := transformGrantedAuthorityHistoryFromClientFormatToInternalFormatV1(
		rawGrantedAuthorityHistory,
	)
	return &grantedAuthorityHistory, nil
}

func (s *serverImpl) GetBIPDInsuranceHistoryV1(
	ctx context.Context,
	request *BIPDInsuranceHistoryRequestV1,
) (*BIPDInsuranceHistoryV1, error) {
	if s.deps.LNIClient == nil {
		return nil, errors.New("nil LNI client")
	}
	var sinceDate *time.Time
	if request.SinceDate != nil {
		sinceDate = pointer_utils.ToPointer(request.SinceDate.AsTime())
	}
	rawBIPDInsuranceHistory, err := s.deps.LNIClient.GetBIPDInsuranceHistory(
		ctx,
		request.DotNumber,
		sinceDate,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get BIPD Insurance History V1")
	}

	bipdInsuranceHistory := transformBIPDInsuranceHistoryFromClientFormatToInternalFormatV1(
		rawBIPDInsuranceHistory,
	)

	return &bipdInsuranceHistory, nil
}

func (s *serverImpl) GetLatestRatingTierRecordsDumpDateV1(
	ctx context.Context,
	request *LatestRatingTiersDumpDateRequestV1,
) (*LatestRatingTiersDumpDateV1, error) {
	rawDate, err := s.deps.SentryInputsClient.GetLatestSentryDumpDateForDOT(
		ctx,
		request.DotNumber,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get latest sentry dump for DOTNumber: %s", request.DotNumber)
	}

	return &LatestRatingTiersDumpDateV1{Date: timestamppb.New(*rawDate)}, nil
}

func (s *serverImpl) GetRatingTierRecordsForDumpDateV1(
	ctx context.Context,
	request *RatingTierRecordsForDumpDateRequestV1,
) (*RatingTierRecordsForDumpDateV1, error) {
	rawRatingTierRecords, err := s.deps.SentryInputsClient.GetMonthlySentryInputsForDOTAndDumpDate(
		ctx,
		request.DotNumber,
		request.DumpDate.AsTime(),
	)
	if err != nil {
		return nil, err
	}
	ratingTierRecords := transformRatingTierRecordsFromClientFormatToInternalFormatV1(
		rawRatingTierRecords.MonthlyInputs,
	)
	return &RatingTierRecordsForDumpDateV1{Records: ratingTierRecords}, nil
}

func (s *serverImpl) GetRatingTierRecordV1(
	ctx context.Context,
	request *RatingTierRecordRequestV1,
) (*RatingTierRecordV1, error) {
	rawRatingTierRecords, err := s.deps.SentryInputsClient.GetSentryInputsForDOTAndDates(
		ctx,
		request.DotNumber,
		request.DumpDate.AsTime(),
		request.RecordDate.AsTime(),
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get rating tier record V1 for DOTNumber: %d", request.DotNumber)
	}

	ratingTierRecord := transformRatingTierRecordFromClientFormatToInternalFormatV1(
		rawRatingTierRecords,
	)

	ratingTierRecord.UnsafeDrivingScore, err = s.computeUnsafeDrivingScore(ctx, request.DotNumber)
	if err != nil {
		log.Error(ctx, "failed to compute unsafe driving score", log.Err(err), log.Int64("dotNumber", request.DotNumber))
	}

	return &ratingTierRecord, nil
}

// computeUnsafeDrivingScore computes the unsafe driving score percentile for a given DOT number
func (s *serverImpl) computeUnsafeDrivingScore(ctx context.Context, dotNumber int64) (*float64, error) {
	if s.deps.BPM == nil || s.deps.FMCSAWrapper == nil {
		return nil, errors.New("BPM or FMCSA wrapper was nil")
	}

	computedMeasures, err := s.deps.FMCSAWrapper.BatchFetchComputedMeasures(ctx, []time.Time{s.deps.BPM.LatestDate()}, dotNumber)
	if err != nil {
		log.Error(ctx, "failed to fetch computed measures for unsafe driving score", log.Err(err), log.Int64("dotNumber", dotNumber))
		return nil, errors.Wrap(err, "failed to fetch computed measures for unsafe driving score")
	}

	if len(computedMeasures) == 0 {
		return nil, errors.New("no computed measures found for unsafe driving score")
	}

	measure := computedMeasures[0].Measure(basic.UnsafeDriving)
	if !measure.IsConclusivePercentile() {
		return nil, errors.New("unsafe driving score is not conclusive")
	}

	_, p, err := s.deps.BPM.PercentileForMeasure(measure)
	if err != nil {
		log.Error(ctx, "failed to get unsafe driving percentile", log.Err(err), log.Int64("dotNumber", dotNumber))
		return nil, errors.Wrap(err, "failed to get unsafe driving percentile")
	}

	return pointer_utils.Float64(p / 100), nil
}

func (s *serverImpl) GetBIPDActiveOrPendingInsuranceV1(
	ctx context.Context,
	request *BIPDActiveOrPendingInsuranceRequestV1,
) (*BIPDActiveOrPendingInsuranceV1, error) {
	if s.deps.LNIClient == nil {
		return nil, errors.New("nil LNI client")
	}
	rawBIPDActiveOrPendingInsurance, err := s.deps.LNIClient.GetBIPDActiveOrPendingInsurance(
		ctx,
		request.DotNumber,
		request.ShouldIncludeExcessCoverage)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get BIPD Active Or Pending Insurance V1")
	}
	bipdActiveOrPendingInsurance := transformBIPDActiveOrPendingInsuranceFromClientToInternalV1(
		rawBIPDActiveOrPendingInsurance,
	)

	return &bipdActiveOrPendingInsurance, nil
}

func (s *serverImpl) GetFMCSAViolationRecordsV1(
	ctx context.Context,
	request *FMCSAViolationRecordsRequestV1,
) (*FMCSAViolationRecordsV1, error) {
	if s.deps.FMCSAWrapper == nil {
		return nil, errors.New("FMCSA wrapper was nil")
	}

	dates, err := s.deps.FMCSAWrapper.FetchPullerCompletionDates(ctx, fmcsa.Violations)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't fetch puller dates for violations")
	}

	vioVersion, err := time_utils.GetDateBefore(dates, request.EffectiveDate.AsTime())
	if err != nil {
		return nil, errors.Wrap(err, "error calculating violation version")
	}

	rawRecords, err := s.deps.FMCSAWrapper.FetchViolationRecords(ctx, vioVersion, int64(request.DotNumber))
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch violation records V1")
	}

	violationRecords := transformFMCSAViolationRecordsFromClientFormatToInternalFormatV1(rawRecords)
	return &FMCSAViolationRecordsV1{Records: violationRecords}, nil
}

func (s *serverImpl) GetFMCSAInspectionRecordsV1(
	ctx context.Context,
	request *FMCSAInspectionRecordsRequestV1,
) (*FMCSAInspectionRecordsV1, error) {
	if s.deps.FMCSAWrapper == nil {
		return nil, errors.New("FMCSA wrapper was nil")
	}

	dates, err := s.deps.FMCSAWrapper.FetchPullerCompletionDates(ctx, fmcsa.Inspections)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't fetch puller dates for inspections")
	}

	inspVersion, err := time_utils.GetDateBefore(dates, request.EffectiveDate.AsTime())
	if err != nil {
		return nil, errors.Wrap(err, "error calculating inspection version")
	}

	rawRecords, err := s.deps.FMCSAWrapper.FetchInspectionRecords(ctx, inspVersion, int64(request.DotNumber))
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch inspection records V1")
	}

	inspectionRecords := transformFMCSAInspectionRecordsFromClientFormatToInternalFormatV1(rawRecords)
	return &FMCSAInspectionRecordsV1{Records: inspectionRecords}, nil
}

func (s *serverImpl) GetFMCSACensusInfoV1(
	ctx context.Context,
	request *FMCSACensusInfoRequestV1,
) (*FMCSACensusInfoV1, error) {
	if s.deps.FMCSAWrapper == nil {
		return nil, errors.New("FMCSA wrapper was nil")
	}

	dotDetails, err := s.deps.FMCSAWrapper.GetDetailsByDot(ctx, request.DotNumber)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch DOT census details V1")
	}

	censusDetails := transformFMCSACensusInfoFromClientFormatToInternalFormatV1(dotDetails)
	return censusDetails, nil
}

func (s *serverImpl) GetLatestObjectiveGradeV1(
	ctx context.Context,
	request *LatestObjectiveGradeRequestV1,
) (*ObjectiveGradeV1, error) {
	rawObjectiveGrade, err := s.deps.ObjectiveGradeManager.FetchLatestObjectiveGradeForDot(ctx, request.DotNumber)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to fetch latest objective grade for DOT Number: %s",
			request.DotNumber,
		)
	}

	objectiveGrade := transformObjectiveGradeFromClientFormatToInteralFormatV1(*rawObjectiveGrade)
	return &objectiveGrade, nil
}

func (s *serverImpl) GetInvoluntaryRevocationsV1(
	ctx context.Context,
	request *InvoluntaryRevocationsRequestV1,
) (*InvoluntaryRevocationsV1, error) {
	startDate := time_utils.DateFromTime(request.StartDate.AsTime())
	endDate := time_utils.DateFromTime(request.EndDate.AsTime())
	rawInvoluntaryRevocations, err := s.deps.LNIClient.GetInvoluntaryRevocations(
		ctx,
		request.DotNumber,
		startDate,
		endDate,
	)
	if err != nil {
		return nil, err
	}

	involuntaryRevocations := transformInvoluntaryRevocationsFromClientFormatToInternalFormatV1(
		rawInvoluntaryRevocations,
	)
	return &involuntaryRevocations, nil
}

func (s *serverImpl) GetNirvanaPoliciesV1(
	ctx context.Context,
	request *GetNirvanaPoliciesRequestV1,
) (*GetNirvanaPoliciesResponseV1, error) {
	if s.deps.PolicyWrapper == nil {
		return nil, errors.New("Policy wrapper was nil")
	}

	mainCoverage, err := transformMainCoverageToPolicyCoverage(request.MainCoverageType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to transform main coverage to policy coverage")
	}

	// Fetch all policies for the given request
	policies, err := s.deps.PolicyWrapper.GetAllPolicies(ctx, &policy.GetRequest{
		Filters: []policy.Filter{
			policy.SkipTestAgencies,
			policy.DOTis{DOTNumber: request.DotNumber},
			policy.PolicyVersionIs(int(request.Version)),
			policy.MainCoverageIs(mainCoverage),
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch policies")
	}

	transformedPolicies := make([]*NirvanaPolicyV1, 0, len(policies))
	for _, p := range policies {
		transformedPolicy := transformPolicyToNirvanaPolicyV1(p)
		transformedPolicies = append(transformedPolicies, transformedPolicy)
	}

	return &GetNirvanaPoliciesResponseV1{
		Policies: transformedPolicies,
	}, nil
}

var _ FetcherServer = &serverImpl{}
