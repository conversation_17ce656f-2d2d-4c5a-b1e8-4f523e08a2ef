// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: data_fetching/api.proto

package data_fetching

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NhtsaErrorCodeV1 int32

const (
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_0   NhtsaErrorCodeV1 = 0
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_1   NhtsaErrorCodeV1 = 1
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_2   NhtsaErrorCodeV1 = 2
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_3   NhtsaErrorCodeV1 = 3
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_4   NhtsaErrorCodeV1 = 4
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_5   NhtsaErrorCodeV1 = 5
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_6   NhtsaErrorCodeV1 = 6
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_7   NhtsaErrorCodeV1 = 7
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_8   NhtsaErrorCodeV1 = 8
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_9   NhtsaErrorCodeV1 = 9
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_10  NhtsaErrorCodeV1 = 10
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_11  NhtsaErrorCodeV1 = 11
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_12  NhtsaErrorCodeV1 = 12
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_14  NhtsaErrorCodeV1 = 13
	NhtsaErrorCodeV1_NhtsaErrorCodeV1_400 NhtsaErrorCodeV1 = 14
)

// Enum value maps for NhtsaErrorCodeV1.
var (
	NhtsaErrorCodeV1_name = map[int32]string{
		0:  "NhtsaErrorCodeV1_0",
		1:  "NhtsaErrorCodeV1_1",
		2:  "NhtsaErrorCodeV1_2",
		3:  "NhtsaErrorCodeV1_3",
		4:  "NhtsaErrorCodeV1_4",
		5:  "NhtsaErrorCodeV1_5",
		6:  "NhtsaErrorCodeV1_6",
		7:  "NhtsaErrorCodeV1_7",
		8:  "NhtsaErrorCodeV1_8",
		9:  "NhtsaErrorCodeV1_9",
		10: "NhtsaErrorCodeV1_10",
		11: "NhtsaErrorCodeV1_11",
		12: "NhtsaErrorCodeV1_12",
		13: "NhtsaErrorCodeV1_14",
		14: "NhtsaErrorCodeV1_400",
	}
	NhtsaErrorCodeV1_value = map[string]int32{
		"NhtsaErrorCodeV1_0":   0,
		"NhtsaErrorCodeV1_1":   1,
		"NhtsaErrorCodeV1_2":   2,
		"NhtsaErrorCodeV1_3":   3,
		"NhtsaErrorCodeV1_4":   4,
		"NhtsaErrorCodeV1_5":   5,
		"NhtsaErrorCodeV1_6":   6,
		"NhtsaErrorCodeV1_7":   7,
		"NhtsaErrorCodeV1_8":   8,
		"NhtsaErrorCodeV1_9":   9,
		"NhtsaErrorCodeV1_10":  10,
		"NhtsaErrorCodeV1_11":  11,
		"NhtsaErrorCodeV1_12":  12,
		"NhtsaErrorCodeV1_14":  13,
		"NhtsaErrorCodeV1_400": 14,
	}
)

func (x NhtsaErrorCodeV1) Enum() *NhtsaErrorCodeV1 {
	p := new(NhtsaErrorCodeV1)
	*p = x
	return p
}

func (x NhtsaErrorCodeV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NhtsaErrorCodeV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_fetching_api_proto_enumTypes[0].Descriptor()
}

func (NhtsaErrorCodeV1) Type() protoreflect.EnumType {
	return &file_data_fetching_api_proto_enumTypes[0]
}

func (x NhtsaErrorCodeV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NhtsaErrorCodeV1.Descriptor instead.
func (NhtsaErrorCodeV1) EnumDescriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{0}
}

type VehicleTypeV1 int32

const (
	VehicleTypeV1_VehicleTypeV1_Unspecified                  VehicleTypeV1 = 0
	VehicleTypeV1_VehicleTypeV1_IncompleteVehicle            VehicleTypeV1 = 1
	VehicleTypeV1_VehicleTypeV1_Motorcycle                   VehicleTypeV1 = 2
	VehicleTypeV1_VehicleTypeV1_Trailer                      VehicleTypeV1 = 3
	VehicleTypeV1_VehicleTypeV1_Truck                        VehicleTypeV1 = 4
	VehicleTypeV1_VehicleTypeV1_Nil                          VehicleTypeV1 = 5
	VehicleTypeV1_VehicleTypeV1_PassengerCar                 VehicleTypeV1 = 6
	VehicleTypeV1_VehicleTypeV1_MultipurposePassengerVehicle VehicleTypeV1 = 7
	VehicleTypeV1_VehicleTypeV1_Bus                          VehicleTypeV1 = 8
)

// Enum value maps for VehicleTypeV1.
var (
	VehicleTypeV1_name = map[int32]string{
		0: "VehicleTypeV1_Unspecified",
		1: "VehicleTypeV1_IncompleteVehicle",
		2: "VehicleTypeV1_Motorcycle",
		3: "VehicleTypeV1_Trailer",
		4: "VehicleTypeV1_Truck",
		5: "VehicleTypeV1_Nil",
		6: "VehicleTypeV1_PassengerCar",
		7: "VehicleTypeV1_MultipurposePassengerVehicle",
		8: "VehicleTypeV1_Bus",
	}
	VehicleTypeV1_value = map[string]int32{
		"VehicleTypeV1_Unspecified":                  0,
		"VehicleTypeV1_IncompleteVehicle":            1,
		"VehicleTypeV1_Motorcycle":                   2,
		"VehicleTypeV1_Trailer":                      3,
		"VehicleTypeV1_Truck":                        4,
		"VehicleTypeV1_Nil":                          5,
		"VehicleTypeV1_PassengerCar":                 6,
		"VehicleTypeV1_MultipurposePassengerVehicle": 7,
		"VehicleTypeV1_Bus":                          8,
	}
)

func (x VehicleTypeV1) Enum() *VehicleTypeV1 {
	p := new(VehicleTypeV1)
	*p = x
	return p
}

func (x VehicleTypeV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleTypeV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_fetching_api_proto_enumTypes[1].Descriptor()
}

func (VehicleTypeV1) Type() protoreflect.EnumType {
	return &file_data_fetching_api_proto_enumTypes[1]
}

func (x VehicleTypeV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleTypeV1.Descriptor instead.
func (VehicleTypeV1) EnumDescriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{1}
}

type WeightClassV1 int32

const (
	WeightClassV1_WeightClassV1_Unspecified WeightClassV1 = 0
	WeightClassV1_WeightClassV1_A           WeightClassV1 = 1
	WeightClassV1_WeightClassV1_B           WeightClassV1 = 2
	WeightClassV1_WeightClassV1_C           WeightClassV1 = 3
	WeightClassV1_WeightClassV1_D           WeightClassV1 = 4
	WeightClassV1_WeightClassV1_E           WeightClassV1 = 5
	WeightClassV1_WeightClassV1_F           WeightClassV1 = 6
	WeightClassV1_WeightClassV1_G           WeightClassV1 = 7
	WeightClassV1_WeightClassV1_H           WeightClassV1 = 8
	WeightClassV1_WeightClassV1_3           WeightClassV1 = 9
	WeightClassV1_WeightClassV1_4           WeightClassV1 = 10
	WeightClassV1_WeightClassV1_5           WeightClassV1 = 11
	WeightClassV1_WeightClassV1_6           WeightClassV1 = 12
	WeightClassV1_WeightClassV1_7           WeightClassV1 = 13
	WeightClassV1_WeightClassV1_8           WeightClassV1 = 14
	WeightClassV1_WeightClassV1_Nil         WeightClassV1 = 15
)

// Enum value maps for WeightClassV1.
var (
	WeightClassV1_name = map[int32]string{
		0:  "WeightClassV1_Unspecified",
		1:  "WeightClassV1_A",
		2:  "WeightClassV1_B",
		3:  "WeightClassV1_C",
		4:  "WeightClassV1_D",
		5:  "WeightClassV1_E",
		6:  "WeightClassV1_F",
		7:  "WeightClassV1_G",
		8:  "WeightClassV1_H",
		9:  "WeightClassV1_3",
		10: "WeightClassV1_4",
		11: "WeightClassV1_5",
		12: "WeightClassV1_6",
		13: "WeightClassV1_7",
		14: "WeightClassV1_8",
		15: "WeightClassV1_Nil",
	}
	WeightClassV1_value = map[string]int32{
		"WeightClassV1_Unspecified": 0,
		"WeightClassV1_A":           1,
		"WeightClassV1_B":           2,
		"WeightClassV1_C":           3,
		"WeightClassV1_D":           4,
		"WeightClassV1_E":           5,
		"WeightClassV1_F":           6,
		"WeightClassV1_G":           7,
		"WeightClassV1_H":           8,
		"WeightClassV1_3":           9,
		"WeightClassV1_4":           10,
		"WeightClassV1_5":           11,
		"WeightClassV1_6":           12,
		"WeightClassV1_7":           13,
		"WeightClassV1_8":           14,
		"WeightClassV1_Nil":         15,
	}
)

func (x WeightClassV1) Enum() *WeightClassV1 {
	p := new(WeightClassV1)
	*p = x
	return p
}

func (x WeightClassV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WeightClassV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_fetching_api_proto_enumTypes[2].Descriptor()
}

func (WeightClassV1) Type() protoreflect.EnumType {
	return &file_data_fetching_api_proto_enumTypes[2]
}

func (x WeightClassV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WeightClassV1.Descriptor instead.
func (WeightClassV1) EnumDescriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{2}
}

type FMCSAViolationCategoryV1 int32

const (
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_Unspecified                 FMCSAViolationCategoryV1 = 0
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving               FMCSAViolationCategoryV1 = 1
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance               FMCSAViolationCategoryV1 = 2
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance          FMCSAViolationCategoryV1 = 3
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol FMCSAViolationCategoryV1 = 4
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance                FMCSAViolationCategoryV1 = 5
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness               FMCSAViolationCategoryV1 = 6
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_CrashIndicator              FMCSAViolationCategoryV1 = 7
	FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_InsuranceOther              FMCSAViolationCategoryV1 = 8
)

// Enum value maps for FMCSAViolationCategoryV1.
var (
	FMCSAViolationCategoryV1_name = map[int32]string{
		0: "FMCSAViolationCategoryV1_Unspecified",
		1: "FMCSAViolationCategoryV1_UnsafeDriving",
		2: "FMCSAViolationCategoryV1_HosCompliance",
		3: "FMCSAViolationCategoryV1_VehicleMaintenance",
		4: "FMCSAViolationCategoryV1_ControlledSubstancesAlcohol",
		5: "FMCSAViolationCategoryV1_HmCompliance",
		6: "FMCSAViolationCategoryV1_DriverFitness",
		7: "FMCSAViolationCategoryV1_CrashIndicator",
		8: "FMCSAViolationCategoryV1_InsuranceOther",
	}
	FMCSAViolationCategoryV1_value = map[string]int32{
		"FMCSAViolationCategoryV1_Unspecified":                 0,
		"FMCSAViolationCategoryV1_UnsafeDriving":               1,
		"FMCSAViolationCategoryV1_HosCompliance":               2,
		"FMCSAViolationCategoryV1_VehicleMaintenance":          3,
		"FMCSAViolationCategoryV1_ControlledSubstancesAlcohol": 4,
		"FMCSAViolationCategoryV1_HmCompliance":                5,
		"FMCSAViolationCategoryV1_DriverFitness":               6,
		"FMCSAViolationCategoryV1_CrashIndicator":              7,
		"FMCSAViolationCategoryV1_InsuranceOther":              8,
	}
)

func (x FMCSAViolationCategoryV1) Enum() *FMCSAViolationCategoryV1 {
	p := new(FMCSAViolationCategoryV1)
	*p = x
	return p
}

func (x FMCSAViolationCategoryV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FMCSAViolationCategoryV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_fetching_api_proto_enumTypes[3].Descriptor()
}

func (FMCSAViolationCategoryV1) Type() protoreflect.EnumType {
	return &file_data_fetching_api_proto_enumTypes[3]
}

func (x FMCSAViolationCategoryV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FMCSAViolationCategoryV1.Descriptor instead.
func (FMCSAViolationCategoryV1) EnumDescriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{3}
}

type FMCSAInspectionLevelV1 int32

const (
	FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Unspecified  FMCSAInspectionLevelV1 = 0
	FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Unknown      FMCSAInspectionLevelV1 = 1
	FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Full         FMCSAInspectionLevelV1 = 2
	FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_WalkAround   FMCSAInspectionLevelV1 = 3
	FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_DriverOnly   FMCSAInspectionLevelV1 = 4
	FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_SpecialStudy FMCSAInspectionLevelV1 = 5
	FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Terminal     FMCSAInspectionLevelV1 = 6
	FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Material     FMCSAInspectionLevelV1 = 7
)

// Enum value maps for FMCSAInspectionLevelV1.
var (
	FMCSAInspectionLevelV1_name = map[int32]string{
		0: "FMCSAInspectionLevelV1_Unspecified",
		1: "FMCSAInspectionLevelV1_Unknown",
		2: "FMCSAInspectionLevelV1_Full",
		3: "FMCSAInspectionLevelV1_WalkAround",
		4: "FMCSAInspectionLevelV1_DriverOnly",
		5: "FMCSAInspectionLevelV1_SpecialStudy",
		6: "FMCSAInspectionLevelV1_Terminal",
		7: "FMCSAInspectionLevelV1_Material",
	}
	FMCSAInspectionLevelV1_value = map[string]int32{
		"FMCSAInspectionLevelV1_Unspecified":  0,
		"FMCSAInspectionLevelV1_Unknown":      1,
		"FMCSAInspectionLevelV1_Full":         2,
		"FMCSAInspectionLevelV1_WalkAround":   3,
		"FMCSAInspectionLevelV1_DriverOnly":   4,
		"FMCSAInspectionLevelV1_SpecialStudy": 5,
		"FMCSAInspectionLevelV1_Terminal":     6,
		"FMCSAInspectionLevelV1_Material":     7,
	}
)

func (x FMCSAInspectionLevelV1) Enum() *FMCSAInspectionLevelV1 {
	p := new(FMCSAInspectionLevelV1)
	*p = x
	return p
}

func (x FMCSAInspectionLevelV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FMCSAInspectionLevelV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_fetching_api_proto_enumTypes[4].Descriptor()
}

func (FMCSAInspectionLevelV1) Type() protoreflect.EnumType {
	return &file_data_fetching_api_proto_enumTypes[4]
}

func (x FMCSAInspectionLevelV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FMCSAInspectionLevelV1.Descriptor instead.
func (FMCSAInspectionLevelV1) EnumDescriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{4}
}

type FMCSACargoTypeV1 int32

const (
	FMCSACargoTypeV1_FMCSACargoTypeV1_Unspecified           FMCSACargoTypeV1 = 0
	FMCSACargoTypeV1_FMCSACargoTypeV1_GeneralFreight        FMCSACargoTypeV1 = 1
	FMCSACargoTypeV1_FMCSACargoTypeV1_Household             FMCSACargoTypeV1 = 2
	FMCSACargoTypeV1_FMCSACargoTypeV1_MetalSheet            FMCSACargoTypeV1 = 3
	FMCSACargoTypeV1_FMCSACargoTypeV1_MotorVehicles         FMCSACargoTypeV1 = 4
	FMCSACargoTypeV1_FMCSACargoTypeV1_DriveawayTowaway      FMCSACargoTypeV1 = 5
	FMCSACargoTypeV1_FMCSACargoTypeV1_LogsPolesBeamsLumber  FMCSACargoTypeV1 = 6
	FMCSACargoTypeV1_FMCSACargoTypeV1_BuildingMaterials     FMCSACargoTypeV1 = 7
	FMCSACargoTypeV1_FMCSACargoTypeV1_MobileHomes           FMCSACargoTypeV1 = 8
	FMCSACargoTypeV1_FMCSACargoTypeV1_MachineryLargeObjects FMCSACargoTypeV1 = 9
	FMCSACargoTypeV1_FMCSACargoTypeV1_Produce               FMCSACargoTypeV1 = 10
	FMCSACargoTypeV1_FMCSACargoTypeV1_LiquidsGases          FMCSACargoTypeV1 = 11
	FMCSACargoTypeV1_FMCSACargoTypeV1_IntermodalContainers  FMCSACargoTypeV1 = 12
	FMCSACargoTypeV1_FMCSACargoTypeV1_Passengers            FMCSACargoTypeV1 = 13
	FMCSACargoTypeV1_FMCSACargoTypeV1_OilfieldEquipment     FMCSACargoTypeV1 = 14
	FMCSACargoTypeV1_FMCSACargoTypeV1_Livestock             FMCSACargoTypeV1 = 15
	FMCSACargoTypeV1_FMCSACargoTypeV1_Grainfeed             FMCSACargoTypeV1 = 16
	FMCSACargoTypeV1_FMCSACargoTypeV1_CoalCoke              FMCSACargoTypeV1 = 17
	FMCSACargoTypeV1_FMCSACargoTypeV1_Meat                  FMCSACargoTypeV1 = 18
	FMCSACargoTypeV1_FMCSACargoTypeV1_Garbage               FMCSACargoTypeV1 = 19
	FMCSACargoTypeV1_FMCSACargoTypeV1_UsMail                FMCSACargoTypeV1 = 20
	FMCSACargoTypeV1_FMCSACargoTypeV1_Chemicals             FMCSACargoTypeV1 = 21
	FMCSACargoTypeV1_FMCSACargoTypeV1_CommoditiesDryBulk    FMCSACargoTypeV1 = 22
	FMCSACargoTypeV1_FMCSACargoTypeV1_RefrigeratedFood      FMCSACargoTypeV1 = 23
	FMCSACargoTypeV1_FMCSACargoTypeV1_Beverages             FMCSACargoTypeV1 = 24
	FMCSACargoTypeV1_FMCSACargoTypeV1_PaperProducts         FMCSACargoTypeV1 = 25
	FMCSACargoTypeV1_FMCSACargoTypeV1_Utility               FMCSACargoTypeV1 = 26
	FMCSACargoTypeV1_FMCSACargoTypeV1_FarmSupplies          FMCSACargoTypeV1 = 27
	FMCSACargoTypeV1_FMCSACargoTypeV1_Construction          FMCSACargoTypeV1 = 28
	FMCSACargoTypeV1_FMCSACargoTypeV1_WaterWell             FMCSACargoTypeV1 = 29
	FMCSACargoTypeV1_FMCSACargoTypeV1_Other                 FMCSACargoTypeV1 = 30
)

// Enum value maps for FMCSACargoTypeV1.
var (
	FMCSACargoTypeV1_name = map[int32]string{
		0:  "FMCSACargoTypeV1_Unspecified",
		1:  "FMCSACargoTypeV1_GeneralFreight",
		2:  "FMCSACargoTypeV1_Household",
		3:  "FMCSACargoTypeV1_MetalSheet",
		4:  "FMCSACargoTypeV1_MotorVehicles",
		5:  "FMCSACargoTypeV1_DriveawayTowaway",
		6:  "FMCSACargoTypeV1_LogsPolesBeamsLumber",
		7:  "FMCSACargoTypeV1_BuildingMaterials",
		8:  "FMCSACargoTypeV1_MobileHomes",
		9:  "FMCSACargoTypeV1_MachineryLargeObjects",
		10: "FMCSACargoTypeV1_Produce",
		11: "FMCSACargoTypeV1_LiquidsGases",
		12: "FMCSACargoTypeV1_IntermodalContainers",
		13: "FMCSACargoTypeV1_Passengers",
		14: "FMCSACargoTypeV1_OilfieldEquipment",
		15: "FMCSACargoTypeV1_Livestock",
		16: "FMCSACargoTypeV1_Grainfeed",
		17: "FMCSACargoTypeV1_CoalCoke",
		18: "FMCSACargoTypeV1_Meat",
		19: "FMCSACargoTypeV1_Garbage",
		20: "FMCSACargoTypeV1_UsMail",
		21: "FMCSACargoTypeV1_Chemicals",
		22: "FMCSACargoTypeV1_CommoditiesDryBulk",
		23: "FMCSACargoTypeV1_RefrigeratedFood",
		24: "FMCSACargoTypeV1_Beverages",
		25: "FMCSACargoTypeV1_PaperProducts",
		26: "FMCSACargoTypeV1_Utility",
		27: "FMCSACargoTypeV1_FarmSupplies",
		28: "FMCSACargoTypeV1_Construction",
		29: "FMCSACargoTypeV1_WaterWell",
		30: "FMCSACargoTypeV1_Other",
	}
	FMCSACargoTypeV1_value = map[string]int32{
		"FMCSACargoTypeV1_Unspecified":           0,
		"FMCSACargoTypeV1_GeneralFreight":        1,
		"FMCSACargoTypeV1_Household":             2,
		"FMCSACargoTypeV1_MetalSheet":            3,
		"FMCSACargoTypeV1_MotorVehicles":         4,
		"FMCSACargoTypeV1_DriveawayTowaway":      5,
		"FMCSACargoTypeV1_LogsPolesBeamsLumber":  6,
		"FMCSACargoTypeV1_BuildingMaterials":     7,
		"FMCSACargoTypeV1_MobileHomes":           8,
		"FMCSACargoTypeV1_MachineryLargeObjects": 9,
		"FMCSACargoTypeV1_Produce":               10,
		"FMCSACargoTypeV1_LiquidsGases":          11,
		"FMCSACargoTypeV1_IntermodalContainers":  12,
		"FMCSACargoTypeV1_Passengers":            13,
		"FMCSACargoTypeV1_OilfieldEquipment":     14,
		"FMCSACargoTypeV1_Livestock":             15,
		"FMCSACargoTypeV1_Grainfeed":             16,
		"FMCSACargoTypeV1_CoalCoke":              17,
		"FMCSACargoTypeV1_Meat":                  18,
		"FMCSACargoTypeV1_Garbage":               19,
		"FMCSACargoTypeV1_UsMail":                20,
		"FMCSACargoTypeV1_Chemicals":             21,
		"FMCSACargoTypeV1_CommoditiesDryBulk":    22,
		"FMCSACargoTypeV1_RefrigeratedFood":      23,
		"FMCSACargoTypeV1_Beverages":             24,
		"FMCSACargoTypeV1_PaperProducts":         25,
		"FMCSACargoTypeV1_Utility":               26,
		"FMCSACargoTypeV1_FarmSupplies":          27,
		"FMCSACargoTypeV1_Construction":          28,
		"FMCSACargoTypeV1_WaterWell":             29,
		"FMCSACargoTypeV1_Other":                 30,
	}
)

func (x FMCSACargoTypeV1) Enum() *FMCSACargoTypeV1 {
	p := new(FMCSACargoTypeV1)
	*p = x
	return p
}

func (x FMCSACargoTypeV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FMCSACargoTypeV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_fetching_api_proto_enumTypes[5].Descriptor()
}

func (FMCSACargoTypeV1) Type() protoreflect.EnumType {
	return &file_data_fetching_api_proto_enumTypes[5]
}

func (x FMCSACargoTypeV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FMCSACargoTypeV1.Descriptor instead.
func (FMCSACargoTypeV1) EnumDescriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{5}
}

type ObjectiveGradeScoreV1 int32

const (
	ObjectiveGradeScoreV1_Undefined ObjectiveGradeScoreV1 = 0
	ObjectiveGradeScoreV1_A         ObjectiveGradeScoreV1 = 1
	ObjectiveGradeScoreV1_B         ObjectiveGradeScoreV1 = 2
	ObjectiveGradeScoreV1_C         ObjectiveGradeScoreV1 = 3
	ObjectiveGradeScoreV1_D         ObjectiveGradeScoreV1 = 4
	ObjectiveGradeScoreV1_E         ObjectiveGradeScoreV1 = 5
	ObjectiveGradeScoreV1_F         ObjectiveGradeScoreV1 = 6
)

// Enum value maps for ObjectiveGradeScoreV1.
var (
	ObjectiveGradeScoreV1_name = map[int32]string{
		0: "Undefined",
		1: "A",
		2: "B",
		3: "C",
		4: "D",
		5: "E",
		6: "F",
	}
	ObjectiveGradeScoreV1_value = map[string]int32{
		"Undefined": 0,
		"A":         1,
		"B":         2,
		"C":         3,
		"D":         4,
		"E":         5,
		"F":         6,
	}
)

func (x ObjectiveGradeScoreV1) Enum() *ObjectiveGradeScoreV1 {
	p := new(ObjectiveGradeScoreV1)
	*p = x
	return p
}

func (x ObjectiveGradeScoreV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ObjectiveGradeScoreV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_fetching_api_proto_enumTypes[6].Descriptor()
}

func (ObjectiveGradeScoreV1) Type() protoreflect.EnumType {
	return &file_data_fetching_api_proto_enumTypes[6]
}

func (x ObjectiveGradeScoreV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ObjectiveGradeScoreV1.Descriptor instead.
func (ObjectiveGradeScoreV1) EnumDescriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{6}
}

type MainCoverageTypeV1 int32

const (
	MainCoverageTypeV1_MainCoverageTypeV1_Unspecified      MainCoverageTypeV1 = 0
	MainCoverageTypeV1_MainCoverageTypeV1_AutoLiability    MainCoverageTypeV1 = 1
	MainCoverageTypeV1_MainCoverageTypeV1_MotorTruckCargo  MainCoverageTypeV1 = 2
	MainCoverageTypeV1_MainCoverageTypeV1_GeneralLiability MainCoverageTypeV1 = 3
)

// Enum value maps for MainCoverageTypeV1.
var (
	MainCoverageTypeV1_name = map[int32]string{
		0: "MainCoverageTypeV1_Unspecified",
		1: "MainCoverageTypeV1_AutoLiability",
		2: "MainCoverageTypeV1_MotorTruckCargo",
		3: "MainCoverageTypeV1_GeneralLiability",
	}
	MainCoverageTypeV1_value = map[string]int32{
		"MainCoverageTypeV1_Unspecified":      0,
		"MainCoverageTypeV1_AutoLiability":    1,
		"MainCoverageTypeV1_MotorTruckCargo":  2,
		"MainCoverageTypeV1_GeneralLiability": 3,
	}
)

func (x MainCoverageTypeV1) Enum() *MainCoverageTypeV1 {
	p := new(MainCoverageTypeV1)
	*p = x
	return p
}

func (x MainCoverageTypeV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MainCoverageTypeV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_fetching_api_proto_enumTypes[7].Descriptor()
}

func (MainCoverageTypeV1) Type() protoreflect.EnumType {
	return &file_data_fetching_api_proto_enumTypes[7]
}

func (x MainCoverageTypeV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MainCoverageTypeV1.Descriptor instead.
func (MainCoverageTypeV1) EnumDescriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{7}
}

type MVRReportV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportID             string                 `protobuf:"bytes,1,opt,name=reportID,proto3" json:"reportID,omitempty"`
	DlState              string                 `protobuf:"bytes,2,opt,name=dlState,proto3" json:"dlState,omitempty"`
	ReportSequenceNumber string                 `protobuf:"bytes,3,opt,name=reportSequenceNumber,proto3" json:"reportSequenceNumber,omitempty"`
	DriverName           string                 `protobuf:"bytes,4,opt,name=driverName,proto3" json:"driverName,omitempty"`
	DriverStreetAddr     string                 `protobuf:"bytes,5,opt,name=driverStreetAddr,proto3" json:"driverStreetAddr,omitempty"`
	MvrStatus            string                 `protobuf:"bytes,6,opt,name=mvrStatus,proto3" json:"mvrStatus,omitempty"`
	ViolationCoding      string                 `protobuf:"bytes,7,opt,name=violationCoding,proto3" json:"violationCoding,omitempty"`
	ViolationCodeTotal   int64                  `protobuf:"varint,8,opt,name=violationCodeTotal,proto3" json:"violationCodeTotal,omitempty"`
	MvrFormat            string                 `protobuf:"bytes,9,opt,name=mvrFormat,proto3" json:"mvrFormat,omitempty"`
	DriverCityStateZip   string                 `protobuf:"bytes,10,opt,name=driverCityStateZip,proto3" json:"driverCityStateZip,omitempty"`
	DlNumber             string                 `protobuf:"bytes,11,opt,name=dlNumber,proto3" json:"dlNumber,omitempty"`
	ClientCode           string                 `protobuf:"bytes,12,opt,name=clientCode,proto3" json:"clientCode,omitempty"`
	ArchiveFlag          string                 `protobuf:"bytes,13,opt,name=archiveFlag,proto3" json:"archiveFlag,omitempty"`
	Ssn                  string                 `protobuf:"bytes,14,opt,name=ssn,proto3" json:"ssn,omitempty"`
	DppaFlag             string                 `protobuf:"bytes,15,opt,name=dppaFlag,proto3" json:"dppaFlag,omitempty"`
	DmvAccountNumber     string                 `protobuf:"bytes,16,opt,name=dmvAccountNumber,proto3" json:"dmvAccountNumber,omitempty"`
	Dob                  *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=dob,proto3" json:"dob,omitempty"`
	Gender               string                 `protobuf:"bytes,18,opt,name=gender,proto3" json:"gender,omitempty"`
	Height               string                 `protobuf:"bytes,19,opt,name=height,proto3" json:"height,omitempty"`
	Weight               string                 `protobuf:"bytes,20,opt,name=weight,proto3" json:"weight,omitempty"`
	EyeColor             string                 `protobuf:"bytes,21,opt,name=eyeColor,proto3" json:"eyeColor,omitempty"`
	HairColor            string                 `protobuf:"bytes,22,opt,name=hairColor,proto3" json:"hairColor,omitempty"`
	LicClass             string                 `protobuf:"bytes,23,opt,name=licClass,proto3" json:"licClass,omitempty"`
	LicStatus            string                 `protobuf:"bytes,24,opt,name=licStatus,proto3" json:"licStatus,omitempty"`
	DateIssued           *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=dateIssued,proto3" json:"dateIssued,omitempty"`
	DateExpires          *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=dateExpires,proto3" json:"dateExpires,omitempty"`
	MvrReportDate        *timestamppb.Timestamp `protobuf:"bytes,27,opt,name=mvrReportDate,proto3" json:"mvrReportDate,omitempty"`
	Restrictions         string                 `protobuf:"bytes,28,opt,name=restrictions,proto3" json:"restrictions,omitempty"`
	MiscDetail           []string               `protobuf:"bytes,29,rep,name=miscDetail,proto3" json:"miscDetail,omitempty"`
	Violations           []*MVRViolationV1      `protobuf:"bytes,30,rep,name=violations,proto3" json:"violations,omitempty"`
	RequestDlNumber      string                 `protobuf:"bytes,31,opt,name=requestDlNumber,proto3" json:"requestDlNumber,omitempty"`
	RequestState         string                 `protobuf:"bytes,32,opt,name=requestState,proto3" json:"requestState,omitempty"`
}

func (x *MVRReportV1) Reset() {
	*x = MVRReportV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MVRReportV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MVRReportV1) ProtoMessage() {}

func (x *MVRReportV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MVRReportV1.ProtoReflect.Descriptor instead.
func (*MVRReportV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{0}
}

func (x *MVRReportV1) GetReportID() string {
	if x != nil {
		return x.ReportID
	}
	return ""
}

func (x *MVRReportV1) GetDlState() string {
	if x != nil {
		return x.DlState
	}
	return ""
}

func (x *MVRReportV1) GetReportSequenceNumber() string {
	if x != nil {
		return x.ReportSequenceNumber
	}
	return ""
}

func (x *MVRReportV1) GetDriverName() string {
	if x != nil {
		return x.DriverName
	}
	return ""
}

func (x *MVRReportV1) GetDriverStreetAddr() string {
	if x != nil {
		return x.DriverStreetAddr
	}
	return ""
}

func (x *MVRReportV1) GetMvrStatus() string {
	if x != nil {
		return x.MvrStatus
	}
	return ""
}

func (x *MVRReportV1) GetViolationCoding() string {
	if x != nil {
		return x.ViolationCoding
	}
	return ""
}

func (x *MVRReportV1) GetViolationCodeTotal() int64 {
	if x != nil {
		return x.ViolationCodeTotal
	}
	return 0
}

func (x *MVRReportV1) GetMvrFormat() string {
	if x != nil {
		return x.MvrFormat
	}
	return ""
}

func (x *MVRReportV1) GetDriverCityStateZip() string {
	if x != nil {
		return x.DriverCityStateZip
	}
	return ""
}

func (x *MVRReportV1) GetDlNumber() string {
	if x != nil {
		return x.DlNumber
	}
	return ""
}

func (x *MVRReportV1) GetClientCode() string {
	if x != nil {
		return x.ClientCode
	}
	return ""
}

func (x *MVRReportV1) GetArchiveFlag() string {
	if x != nil {
		return x.ArchiveFlag
	}
	return ""
}

func (x *MVRReportV1) GetSsn() string {
	if x != nil {
		return x.Ssn
	}
	return ""
}

func (x *MVRReportV1) GetDppaFlag() string {
	if x != nil {
		return x.DppaFlag
	}
	return ""
}

func (x *MVRReportV1) GetDmvAccountNumber() string {
	if x != nil {
		return x.DmvAccountNumber
	}
	return ""
}

func (x *MVRReportV1) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *MVRReportV1) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *MVRReportV1) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *MVRReportV1) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *MVRReportV1) GetEyeColor() string {
	if x != nil {
		return x.EyeColor
	}
	return ""
}

func (x *MVRReportV1) GetHairColor() string {
	if x != nil {
		return x.HairColor
	}
	return ""
}

func (x *MVRReportV1) GetLicClass() string {
	if x != nil {
		return x.LicClass
	}
	return ""
}

func (x *MVRReportV1) GetLicStatus() string {
	if x != nil {
		return x.LicStatus
	}
	return ""
}

func (x *MVRReportV1) GetDateIssued() *timestamppb.Timestamp {
	if x != nil {
		return x.DateIssued
	}
	return nil
}

func (x *MVRReportV1) GetDateExpires() *timestamppb.Timestamp {
	if x != nil {
		return x.DateExpires
	}
	return nil
}

func (x *MVRReportV1) GetMvrReportDate() *timestamppb.Timestamp {
	if x != nil {
		return x.MvrReportDate
	}
	return nil
}

func (x *MVRReportV1) GetRestrictions() string {
	if x != nil {
		return x.Restrictions
	}
	return ""
}

func (x *MVRReportV1) GetMiscDetail() []string {
	if x != nil {
		return x.MiscDetail
	}
	return nil
}

func (x *MVRReportV1) GetViolations() []*MVRViolationV1 {
	if x != nil {
		return x.Violations
	}
	return nil
}

func (x *MVRReportV1) GetRequestDlNumber() string {
	if x != nil {
		return x.RequestDlNumber
	}
	return ""
}

func (x *MVRReportV1) GetRequestState() string {
	if x != nil {
		return x.RequestState
	}
	return ""
}

type MVRViolationV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViolationType         string                 `protobuf:"bytes,1,opt,name=violationType,proto3" json:"violationType,omitempty"`
	ViolationDate         *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=violationDate,proto3" json:"violationDate,omitempty"`
	ConvictionDate        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=convictionDate,proto3" json:"convictionDate,omitempty"`
	ViolationCode         string                 `protobuf:"bytes,4,opt,name=violationCode,proto3" json:"violationCode,omitempty"`
	Points                int64                  `protobuf:"varint,5,opt,name=points,proto3" json:"points,omitempty"`
	AssignedViolationCode string                 `protobuf:"bytes,6,opt,name=assignedViolationCode,proto3" json:"assignedViolationCode,omitempty"`
	AssignedPoints        string                 `protobuf:"bytes,7,opt,name=assignedPoints,proto3" json:"assignedPoints,omitempty"`
	ViolationDetail       string                 `protobuf:"bytes,8,opt,name=violationDetail,proto3" json:"violationDetail,omitempty"`
}

func (x *MVRViolationV1) Reset() {
	*x = MVRViolationV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MVRViolationV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MVRViolationV1) ProtoMessage() {}

func (x *MVRViolationV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MVRViolationV1.ProtoReflect.Descriptor instead.
func (*MVRViolationV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{1}
}

func (x *MVRViolationV1) GetViolationType() string {
	if x != nil {
		return x.ViolationType
	}
	return ""
}

func (x *MVRViolationV1) GetViolationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ViolationDate
	}
	return nil
}

func (x *MVRViolationV1) GetConvictionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ConvictionDate
	}
	return nil
}

func (x *MVRViolationV1) GetViolationCode() string {
	if x != nil {
		return x.ViolationCode
	}
	return ""
}

func (x *MVRViolationV1) GetPoints() int64 {
	if x != nil {
		return x.Points
	}
	return 0
}

func (x *MVRViolationV1) GetAssignedViolationCode() string {
	if x != nil {
		return x.AssignedViolationCode
	}
	return ""
}

func (x *MVRViolationV1) GetAssignedPoints() string {
	if x != nil {
		return x.AssignedPoints
	}
	return ""
}

func (x *MVRViolationV1) GetViolationDetail() string {
	if x != nil {
		return x.ViolationDetail
	}
	return ""
}

type MVRReportRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DlNumber           string                 `protobuf:"bytes,1,opt,name=dlNumber,proto3" json:"dlNumber,omitempty"`
	UsState            string                 `protobuf:"bytes,2,opt,name=usState,proto3" json:"usState,omitempty"`
	Dob                *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
	FirstName          string                 `protobuf:"bytes,4,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName           string                 `protobuf:"bytes,5,opt,name=lastName,proto3" json:"lastName,omitempty"`
	MiddleName         string                 `protobuf:"bytes,6,opt,name=middleName,proto3" json:"middleName,omitempty"`
	Staleness          int64                  `protobuf:"varint,7,opt,name=staleness,proto3" json:"staleness,omitempty"`
	ApplicationID      string                 `protobuf:"bytes,8,opt,name=applicationID,proto3" json:"applicationID,omitempty"`
	OnlyFetchFromCache bool                   `protobuf:"varint,9,opt,name=onlyFetchFromCache,proto3" json:"onlyFetchFromCache,omitempty"`
}

func (x *MVRReportRequestV1) Reset() {
	*x = MVRReportRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MVRReportRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MVRReportRequestV1) ProtoMessage() {}

func (x *MVRReportRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MVRReportRequestV1.ProtoReflect.Descriptor instead.
func (*MVRReportRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{2}
}

func (x *MVRReportRequestV1) GetDlNumber() string {
	if x != nil {
		return x.DlNumber
	}
	return ""
}

func (x *MVRReportRequestV1) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *MVRReportRequestV1) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *MVRReportRequestV1) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *MVRReportRequestV1) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *MVRReportRequestV1) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *MVRReportRequestV1) GetStaleness() int64 {
	if x != nil {
		return x.Staleness
	}
	return 0
}

func (x *MVRReportRequestV1) GetApplicationID() string {
	if x != nil {
		return x.ApplicationID
	}
	return ""
}

func (x *MVRReportRequestV1) GetOnlyFetchFromCache() bool {
	if x != nil {
		return x.OnlyFetchFromCache
	}
	return false
}

type MVRAttractScoreV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score int64 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *MVRAttractScoreV1) Reset() {
	*x = MVRAttractScoreV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MVRAttractScoreV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MVRAttractScoreV1) ProtoMessage() {}

func (x *MVRAttractScoreV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MVRAttractScoreV1.ProtoReflect.Descriptor instead.
func (*MVRAttractScoreV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{3}
}

func (x *MVRAttractScoreV1) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type MVRAttractScoreRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DlNumber      string                 `protobuf:"bytes,1,opt,name=dlNumber,proto3" json:"dlNumber,omitempty"`
	UsState       string                 `protobuf:"bytes,2,opt,name=usState,proto3" json:"usState,omitempty"`
	Dob           *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
	FirstName     string                 `protobuf:"bytes,4,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName      string                 `protobuf:"bytes,5,opt,name=lastName,proto3" json:"lastName,omitempty"`
	MiddleName    string                 `protobuf:"bytes,6,opt,name=middleName,proto3" json:"middleName,omitempty"`
	Staleness     int64                  `protobuf:"varint,7,opt,name=staleness,proto3" json:"staleness,omitempty"`
	ApplicationID string                 `protobuf:"bytes,8,opt,name=applicationID,proto3" json:"applicationID,omitempty"`
}

func (x *MVRAttractScoreRequestV1) Reset() {
	*x = MVRAttractScoreRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MVRAttractScoreRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MVRAttractScoreRequestV1) ProtoMessage() {}

func (x *MVRAttractScoreRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MVRAttractScoreRequestV1.ProtoReflect.Descriptor instead.
func (*MVRAttractScoreRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{4}
}

func (x *MVRAttractScoreRequestV1) GetDlNumber() string {
	if x != nil {
		return x.DlNumber
	}
	return ""
}

func (x *MVRAttractScoreRequestV1) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *MVRAttractScoreRequestV1) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *MVRAttractScoreRequestV1) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *MVRAttractScoreRequestV1) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *MVRAttractScoreRequestV1) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *MVRAttractScoreRequestV1) GetStaleness() int64 {
	if x != nil {
		return x.Staleness
	}
	return 0
}

func (x *MVRAttractScoreRequestV1) GetApplicationID() string {
	if x != nil {
		return x.ApplicationID
	}
	return ""
}

type VINDecodeErrorV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NhtsaErrorCodes []NhtsaErrorCodeV1 `protobuf:"varint,1,rep,packed,name=nhtsaErrorCodes,proto3,enum=data_fetching.NhtsaErrorCodeV1" json:"nhtsaErrorCodes,omitempty"`
	ErrText         string             `protobuf:"bytes,2,opt,name=errText,proto3" json:"errText,omitempty"`
}

func (x *VINDecodeErrorV1) Reset() {
	*x = VINDecodeErrorV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecodeErrorV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecodeErrorV1) ProtoMessage() {}

func (x *VINDecodeErrorV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecodeErrorV1.ProtoReflect.Descriptor instead.
func (*VINDecodeErrorV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{5}
}

func (x *VINDecodeErrorV1) GetNhtsaErrorCodes() []NhtsaErrorCodeV1 {
	if x != nil {
		return x.NhtsaErrorCodes
	}
	return nil
}

func (x *VINDecodeErrorV1) GetErrText() string {
	if x != nil {
		return x.ErrText
	}
	return ""
}

type VINDetailsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Make           string            `protobuf:"bytes,1,opt,name=make,proto3" json:"make,omitempty"`
	Model          string            `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`
	ModelYear      string            `protobuf:"bytes,3,opt,name=modelYear,proto3" json:"modelYear,omitempty"`
	Manufacturer   string            `protobuf:"bytes,4,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	Trim           string            `protobuf:"bytes,5,opt,name=trim,proto3" json:"trim,omitempty"`
	RawVehicleType string            `protobuf:"bytes,6,opt,name=rawVehicleType,proto3" json:"rawVehicleType,omitempty"`
	BodyClass      string            `protobuf:"bytes,7,opt,name=bodyClass,proto3" json:"bodyClass,omitempty"`
	VehicleType    VehicleTypeV1     `protobuf:"varint,8,opt,name=vehicleType,proto3,enum=data_fetching.VehicleTypeV1" json:"vehicleType,omitempty"`
	WeightClass    WeightClassV1     `protobuf:"varint,9,opt,name=weightClass,proto3,enum=data_fetching.WeightClassV1" json:"weightClass,omitempty"`
	DecodeError    *VINDecodeErrorV1 `protobuf:"bytes,10,opt,name=decodeError,proto3,oneof" json:"decodeError,omitempty"`
}

func (x *VINDetailsV1) Reset() {
	*x = VINDetailsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDetailsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDetailsV1) ProtoMessage() {}

func (x *VINDetailsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDetailsV1.ProtoReflect.Descriptor instead.
func (*VINDetailsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{6}
}

func (x *VINDetailsV1) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *VINDetailsV1) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *VINDetailsV1) GetModelYear() string {
	if x != nil {
		return x.ModelYear
	}
	return ""
}

func (x *VINDetailsV1) GetManufacturer() string {
	if x != nil {
		return x.Manufacturer
	}
	return ""
}

func (x *VINDetailsV1) GetTrim() string {
	if x != nil {
		return x.Trim
	}
	return ""
}

func (x *VINDetailsV1) GetRawVehicleType() string {
	if x != nil {
		return x.RawVehicleType
	}
	return ""
}

func (x *VINDetailsV1) GetBodyClass() string {
	if x != nil {
		return x.BodyClass
	}
	return ""
}

func (x *VINDetailsV1) GetVehicleType() VehicleTypeV1 {
	if x != nil {
		return x.VehicleType
	}
	return VehicleTypeV1_VehicleTypeV1_Unspecified
}

func (x *VINDetailsV1) GetWeightClass() WeightClassV1 {
	if x != nil {
		return x.WeightClass
	}
	return WeightClassV1_WeightClassV1_Unspecified
}

func (x *VINDetailsV1) GetDecodeError() *VINDecodeErrorV1 {
	if x != nil {
		return x.DecodeError
	}
	return nil
}

type VINDetailsRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin string `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
}

func (x *VINDetailsRequestV1) Reset() {
	*x = VINDetailsRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDetailsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDetailsRequestV1) ProtoMessage() {}

func (x *VINDetailsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDetailsRequestV1.ProtoReflect.Descriptor instead.
func (*VINDetailsRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{7}
}

func (x *VINDetailsRequestV1) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

type BIPDActiveOrPendingInsuranceV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*InsuranceRecordV1 `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *BIPDActiveOrPendingInsuranceV1) Reset() {
	*x = BIPDActiveOrPendingInsuranceV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BIPDActiveOrPendingInsuranceV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BIPDActiveOrPendingInsuranceV1) ProtoMessage() {}

func (x *BIPDActiveOrPendingInsuranceV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BIPDActiveOrPendingInsuranceV1.ProtoReflect.Descriptor instead.
func (*BIPDActiveOrPendingInsuranceV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{8}
}

func (x *BIPDActiveOrPendingInsuranceV1) GetRecords() []*InsuranceRecordV1 {
	if x != nil {
		return x.Records
	}
	return nil
}

type BIPDActiveOrPendingInsuranceRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber                   int64 `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	ShouldIncludeExcessCoverage bool  `protobuf:"varint,2,opt,name=shouldIncludeExcessCoverage,proto3" json:"shouldIncludeExcessCoverage,omitempty"`
}

func (x *BIPDActiveOrPendingInsuranceRequestV1) Reset() {
	*x = BIPDActiveOrPendingInsuranceRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BIPDActiveOrPendingInsuranceRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BIPDActiveOrPendingInsuranceRequestV1) ProtoMessage() {}

func (x *BIPDActiveOrPendingInsuranceRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BIPDActiveOrPendingInsuranceRequestV1.ProtoReflect.Descriptor instead.
func (*BIPDActiveOrPendingInsuranceRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{9}
}

func (x *BIPDActiveOrPendingInsuranceRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *BIPDActiveOrPendingInsuranceRequestV1) GetShouldIncludeExcessCoverage() bool {
	if x != nil {
		return x.ShouldIncludeExcessCoverage
	}
	return false
}

type NationalCreditFileRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dob           *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=dob,proto3" json:"dob,omitempty"`
	FirstName     string                 `protobuf:"bytes,2,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName      string                 `protobuf:"bytes,3,opt,name=lastName,proto3" json:"lastName,omitempty"`
	MiddleName    string                 `protobuf:"bytes,4,opt,name=middleName,proto3" json:"middleName,omitempty"`
	Address       *AddressV1             `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	Staleness     int64                  `protobuf:"varint,6,opt,name=staleness,proto3" json:"staleness,omitempty"`
	ApplicationID string                 `protobuf:"bytes,7,opt,name=applicationID,proto3" json:"applicationID,omitempty"`
	EncryptedSSN  []byte                 `protobuf:"bytes,8,opt,name=encryptedSSN,proto3,oneof" json:"encryptedSSN,omitempty"`
	SsnLastFour   *string                `protobuf:"bytes,9,opt,name=ssnLastFour,proto3,oneof" json:"ssnLastFour,omitempty"`
}

func (x *NationalCreditFileRequestV1) Reset() {
	*x = NationalCreditFileRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NationalCreditFileRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NationalCreditFileRequestV1) ProtoMessage() {}

func (x *NationalCreditFileRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NationalCreditFileRequestV1.ProtoReflect.Descriptor instead.
func (*NationalCreditFileRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{10}
}

func (x *NationalCreditFileRequestV1) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *NationalCreditFileRequestV1) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *NationalCreditFileRequestV1) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *NationalCreditFileRequestV1) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *NationalCreditFileRequestV1) GetAddress() *AddressV1 {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *NationalCreditFileRequestV1) GetStaleness() int64 {
	if x != nil {
		return x.Staleness
	}
	return 0
}

func (x *NationalCreditFileRequestV1) GetApplicationID() string {
	if x != nil {
		return x.ApplicationID
	}
	return ""
}

func (x *NationalCreditFileRequestV1) GetEncryptedSSN() []byte {
	if x != nil {
		return x.EncryptedSSN
	}
	return nil
}

func (x *NationalCreditFileRequestV1) GetSsnLastFour() string {
	if x != nil && x.SsnLastFour != nil {
		return *x.SsnLastFour
	}
	return ""
}

type AddressV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Street string `protobuf:"bytes,1,opt,name=street,proto3" json:"street,omitempty"`
	City   string `protobuf:"bytes,2,opt,name=city,proto3" json:"city,omitempty"`
	State  string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Zip    string `protobuf:"bytes,4,opt,name=zip,proto3" json:"zip,omitempty"`
}

func (x *AddressV1) Reset() {
	*x = AddressV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressV1) ProtoMessage() {}

func (x *AddressV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressV1.ProtoReflect.Descriptor instead.
func (*AddressV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{11}
}

func (x *AddressV1) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *AddressV1) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *AddressV1) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *AddressV1) GetZip() string {
	if x != nil {
		return x.Zip
	}
	return ""
}

type NationalCreditFileV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NcfReport             *NcfReportV1            `protobuf:"bytes,1,opt,name=NcfReport,proto3" json:"NcfReport,omitempty"`
	TransactionDetails    *TransactionDetailsExV1 `protobuf:"bytes,2,opt,name=TransactionDetails,proto3" json:"TransactionDetails,omitempty"`
	DateOfCreditReportRun *timestamppb.Timestamp  `protobuf:"bytes,3,opt,name=DateOfCreditReportRun,proto3" json:"DateOfCreditReportRun,omitempty"`
}

func (x *NationalCreditFileV1) Reset() {
	*x = NationalCreditFileV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NationalCreditFileV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NationalCreditFileV1) ProtoMessage() {}

func (x *NationalCreditFileV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NationalCreditFileV1.ProtoReflect.Descriptor instead.
func (*NationalCreditFileV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{12}
}

func (x *NationalCreditFileV1) GetNcfReport() *NcfReportV1 {
	if x != nil {
		return x.NcfReport
	}
	return nil
}

func (x *NationalCreditFileV1) GetTransactionDetails() *TransactionDetailsExV1 {
	if x != nil {
		return x.TransactionDetails
	}
	return nil
}

func (x *NationalCreditFileV1) GetDateOfCreditReportRun() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfCreditReportRun
	}
	return nil
}

type NameV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	First  string `protobuf:"bytes,1,opt,name=first,proto3" json:"first,omitempty"`
	Middle string `protobuf:"bytes,2,opt,name=middle,proto3" json:"middle,omitempty"`
	Last   string `protobuf:"bytes,3,opt,name=last,proto3" json:"last,omitempty"`
	Suffix string `protobuf:"bytes,4,opt,name=suffix,proto3" json:"suffix,omitempty"`
}

func (x *NameV1) Reset() {
	*x = NameV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NameV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NameV1) ProtoMessage() {}

func (x *NameV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NameV1.ProtoReflect.Descriptor instead.
func (*NameV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{13}
}

func (x *NameV1) GetFirst() string {
	if x != nil {
		return x.First
	}
	return ""
}

func (x *NameV1) GetMiddle() string {
	if x != nil {
		return x.Middle
	}
	return ""
}

func (x *NameV1) GetLast() string {
	if x != nil {
		return x.Last
	}
	return ""
}

func (x *NameV1) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

type DobV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
}

func (x *DobV1) Reset() {
	*x = DobV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DobV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DobV1) ProtoMessage() {}

func (x *DobV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DobV1.ProtoReflect.Descriptor instead.
func (*DobV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{14}
}

func (x *DobV1) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

type SubjectV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Classification      string  `protobuf:"bytes,1,opt,name=classification,proto3" json:"classification,omitempty"`
	Name                *NameV1 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Ssn                 string  `protobuf:"bytes,3,opt,name=ssn,proto3" json:"ssn,omitempty"`
	Dob                 *DobV1  `protobuf:"bytes,4,opt,name=dob,proto3" json:"dob,omitempty"`
	HeightFeet          string  `protobuf:"bytes,5,opt,name=heightFeet,proto3" json:"heightFeet,omitempty"`
	HeightInches        string  `protobuf:"bytes,6,opt,name=heightInches,proto3" json:"heightInches,omitempty"`
	Weight              string  `protobuf:"bytes,7,opt,name=weight,proto3" json:"weight,omitempty"`
	RelationshipType    string  `protobuf:"bytes,8,opt,name=relationshipType,proto3" json:"relationshipType,omitempty"`
	GroupSequenceNumber string  `protobuf:"bytes,9,opt,name=groupSequenceNumber,proto3" json:"groupSequenceNumber,omitempty"`
}

func (x *SubjectV1) Reset() {
	*x = SubjectV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubjectV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubjectV1) ProtoMessage() {}

func (x *SubjectV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubjectV1.ProtoReflect.Descriptor instead.
func (*SubjectV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{15}
}

func (x *SubjectV1) GetClassification() string {
	if x != nil {
		return x.Classification
	}
	return ""
}

func (x *SubjectV1) GetName() *NameV1 {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *SubjectV1) GetSsn() string {
	if x != nil {
		return x.Ssn
	}
	return ""
}

func (x *SubjectV1) GetDob() *DobV1 {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *SubjectV1) GetHeightFeet() string {
	if x != nil {
		return x.HeightFeet
	}
	return ""
}

func (x *SubjectV1) GetHeightInches() string {
	if x != nil {
		return x.HeightInches
	}
	return ""
}

func (x *SubjectV1) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *SubjectV1) GetRelationshipType() string {
	if x != nil {
		return x.RelationshipType
	}
	return ""
}

func (x *SubjectV1) GetGroupSequenceNumber() string {
	if x != nil {
		return x.GroupSequenceNumber
	}
	return ""
}

type DateFirstAtAddressV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month uint32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
}

func (x *DateFirstAtAddressV1) Reset() {
	*x = DateFirstAtAddressV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateFirstAtAddressV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateFirstAtAddressV1) ProtoMessage() {}

func (x *DateFirstAtAddressV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateFirstAtAddressV1.ProtoReflect.Descriptor instead.
func (*DateFirstAtAddressV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{16}
}

func (x *DateFirstAtAddressV1) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *DateFirstAtAddressV1) GetMonth() uint32 {
	if x != nil {
		return x.Month
	}
	return 0
}

type CurrentAddressV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StreetNumber       string                `protobuf:"bytes,1,opt,name=streetNumber,proto3" json:"streetNumber,omitempty"`
	StreetName         string                `protobuf:"bytes,2,opt,name=streetName,proto3" json:"streetName,omitempty"`
	City               string                `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`
	State              string                `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Zip5               string                `protobuf:"bytes,5,opt,name=zip5,proto3" json:"zip5,omitempty"`
	Zip4               string                `protobuf:"bytes,6,opt,name=zip4,proto3" json:"zip4,omitempty"`
	DateFirstAtAddress *DateFirstAtAddressV1 `protobuf:"bytes,7,opt,name=dateFirstAtAddress,proto3" json:"dateFirstAtAddress,omitempty"`
	AddressId          string                `protobuf:"bytes,8,opt,name=addressId,proto3" json:"addressId,omitempty"`
}

func (x *CurrentAddressV1) Reset() {
	*x = CurrentAddressV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentAddressV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentAddressV1) ProtoMessage() {}

func (x *CurrentAddressV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentAddressV1.ProtoReflect.Descriptor instead.
func (*CurrentAddressV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{17}
}

func (x *CurrentAddressV1) GetStreetNumber() string {
	if x != nil {
		return x.StreetNumber
	}
	return ""
}

func (x *CurrentAddressV1) GetStreetName() string {
	if x != nil {
		return x.StreetName
	}
	return ""
}

func (x *CurrentAddressV1) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *CurrentAddressV1) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *CurrentAddressV1) GetZip5() string {
	if x != nil {
		return x.Zip5
	}
	return ""
}

func (x *CurrentAddressV1) GetZip4() string {
	if x != nil {
		return x.Zip4
	}
	return ""
}

func (x *CurrentAddressV1) GetDateFirstAtAddress() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateFirstAtAddress
	}
	return nil
}

func (x *CurrentAddressV1) GetAddressId() string {
	if x != nil {
		return x.AddressId
	}
	return ""
}

type SubjectInfoV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject        *SubjectV1        `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	CurrentAddress *CurrentAddressV1 `protobuf:"bytes,2,opt,name=currentAddress,proto3" json:"currentAddress,omitempty"`
}

func (x *SubjectInfoV1) Reset() {
	*x = SubjectInfoV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubjectInfoV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubjectInfoV1) ProtoMessage() {}

func (x *SubjectInfoV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubjectInfoV1.ProtoReflect.Descriptor instead.
func (*SubjectInfoV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{18}
}

func (x *SubjectInfoV1) GetSubject() *SubjectV1 {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *SubjectInfoV1) GetCurrentAddress() *CurrentAddressV1 {
	if x != nil {
		return x.CurrentAddress
	}
	return nil
}

type DateCreditFileEstbedV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month uint32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	Day   uint32 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
}

func (x *DateCreditFileEstbedV1) Reset() {
	*x = DateCreditFileEstbedV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateCreditFileEstbedV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateCreditFileEstbedV1) ProtoMessage() {}

func (x *DateCreditFileEstbedV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateCreditFileEstbedV1.ProtoReflect.Descriptor instead.
func (*DateCreditFileEstbedV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{19}
}

func (x *DateCreditFileEstbedV1) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *DateCreditFileEstbedV1) GetMonth() uint32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *DateCreditFileEstbedV1) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

type CurrentStatusAccountV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NumberOfAccounts uint32 `protobuf:"varint,2,opt,name=numberOfAccounts,proto3" json:"numberOfAccounts,omitempty"`
}

func (x *CurrentStatusAccountV1) Reset() {
	*x = CurrentStatusAccountV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentStatusAccountV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentStatusAccountV1) ProtoMessage() {}

func (x *CurrentStatusAccountV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentStatusAccountV1.ProtoReflect.Descriptor instead.
func (*CurrentStatusAccountV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{20}
}

func (x *CurrentStatusAccountV1) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CurrentStatusAccountV1) GetNumberOfAccounts() uint32 {
	if x != nil {
		return x.NumberOfAccounts
	}
	return 0
}

type CurrentStatusAccountsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentStatusAccount []*CurrentStatusAccountV1 `protobuf:"bytes,1,rep,name=currentStatusAccount,proto3" json:"currentStatusAccount,omitempty"`
}

func (x *CurrentStatusAccountsV1) Reset() {
	*x = CurrentStatusAccountsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentStatusAccountsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentStatusAccountsV1) ProtoMessage() {}

func (x *CurrentStatusAccountsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentStatusAccountsV1.ProtoReflect.Descriptor instead.
func (*CurrentStatusAccountsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{21}
}

func (x *CurrentStatusAccountsV1) GetCurrentStatusAccount() []*CurrentStatusAccountV1 {
	if x != nil {
		return x.CurrentStatusAccount
	}
	return nil
}

type HistoryStatusAccountsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HistoryStatusAccount []*CurrentStatusAccountV1 `protobuf:"bytes,1,rep,name=historyStatusAccount,proto3" json:"historyStatusAccount,omitempty"`
}

func (x *HistoryStatusAccountsV1) Reset() {
	*x = HistoryStatusAccountsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HistoryStatusAccountsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryStatusAccountsV1) ProtoMessage() {}

func (x *HistoryStatusAccountsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryStatusAccountsV1.ProtoReflect.Descriptor instead.
func (*HistoryStatusAccountsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{22}
}

func (x *HistoryStatusAccountsV1) GetHistoryStatusAccount() []*CurrentStatusAccountV1 {
	if x != nil {
		return x.HistoryStatusAccount
	}
	return nil
}

type CreditReportSummaryV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateCreditFileEstbed        *DateCreditFileEstbedV1  `protobuf:"bytes,1,opt,name=dateCreditFileEstbed,proto3" json:"dateCreditFileEstbed,omitempty"`
	OldestOpeningDateOfTrade    *DateFirstAtAddressV1    `protobuf:"bytes,2,opt,name=oldestOpeningDateOfTrade,proto3" json:"oldestOpeningDateOfTrade,omitempty"`
	LatestReportingDateOfTrade  *DateFirstAtAddressV1    `protobuf:"bytes,3,opt,name=latestReportingDateOfTrade,proto3" json:"latestReportingDateOfTrade,omitempty"`
	DateOfLatestFileActivity    *DateFirstAtAddressV1    `protobuf:"bytes,4,opt,name=dateOfLatestFileActivity,proto3" json:"dateOfLatestFileActivity,omitempty"`
	ReportIncldsCollectionItems bool                     `protobuf:"varint,5,opt,name=reportIncldsCollectionItems,proto3" json:"reportIncldsCollectionItems,omitempty"`
	HighCreditRangeLowAmount    uint32                   `protobuf:"varint,6,opt,name=highCreditRangeLowAmount,proto3" json:"highCreditRangeLowAmount,omitempty"`
	HighCreditRangeHighAmount   uint32                   `protobuf:"varint,7,opt,name=highCreditRangeHighAmount,proto3" json:"highCreditRangeHighAmount,omitempty"`
	TotalNumberOfTradeLines     uint32                   `protobuf:"varint,8,opt,name=totalNumberOfTradeLines,proto3" json:"totalNumberOfTradeLines,omitempty"`
	CurrentStatusAccounts       *CurrentStatusAccountsV1 `protobuf:"bytes,9,opt,name=currentStatusAccounts,proto3" json:"currentStatusAccounts,omitempty"`
	HistoryStatusAccounts       *HistoryStatusAccountsV1 `protobuf:"bytes,10,opt,name=historyStatusAccounts,proto3" json:"historyStatusAccounts,omitempty"`
	HighCreditTotalRevolving    uint32                   `protobuf:"varint,11,opt,name=highCreditTotalRevolving,proto3" json:"highCreditTotalRevolving,omitempty"`
	HighCreditOpenended         uint32                   `protobuf:"varint,12,opt,name=highCreditOpenended,proto3" json:"highCreditOpenended,omitempty"`
	HighCreditInstallment       uint32                   `protobuf:"varint,13,opt,name=highCreditInstallment,proto3" json:"highCreditInstallment,omitempty"`
	AmountOwedTotalRevolving    uint32                   `protobuf:"varint,14,opt,name=amountOwedTotalRevolving,proto3" json:"amountOwedTotalRevolving,omitempty"`
	AmountOwedTotalOpenended    uint32                   `protobuf:"varint,15,opt,name=amountOwedTotalOpenended,proto3" json:"amountOwedTotalOpenended,omitempty"`
	AmountOwedTotalInstallment  uint32                   `protobuf:"varint,16,opt,name=amountOwedTotalInstallment,proto3" json:"amountOwedTotalInstallment,omitempty"`
	PastDueTotalRevolving       uint32                   `protobuf:"varint,17,opt,name=pastDueTotalRevolving,proto3" json:"pastDueTotalRevolving,omitempty"`
	PastDueTotalOpenended       uint32                   `protobuf:"varint,18,opt,name=pastDueTotalOpenended,proto3" json:"pastDueTotalOpenended,omitempty"`
	PastDueTotalInstallment     uint32                   `protobuf:"varint,19,opt,name=pastDueTotalInstallment,proto3" json:"pastDueTotalInstallment,omitempty"`
	NumberOfRevolvingAccounts   uint32                   `protobuf:"varint,20,opt,name=numberOfRevolvingAccounts,proto3" json:"numberOfRevolvingAccounts,omitempty"`
	NumberOfInstallmentAccounts uint32                   `protobuf:"varint,21,opt,name=numberOfInstallmentAccounts,proto3" json:"numberOfInstallmentAccounts,omitempty"`
}

func (x *CreditReportSummaryV1) Reset() {
	*x = CreditReportSummaryV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditReportSummaryV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditReportSummaryV1) ProtoMessage() {}

func (x *CreditReportSummaryV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditReportSummaryV1.ProtoReflect.Descriptor instead.
func (*CreditReportSummaryV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{23}
}

func (x *CreditReportSummaryV1) GetDateCreditFileEstbed() *DateCreditFileEstbedV1 {
	if x != nil {
		return x.DateCreditFileEstbed
	}
	return nil
}

func (x *CreditReportSummaryV1) GetOldestOpeningDateOfTrade() *DateFirstAtAddressV1 {
	if x != nil {
		return x.OldestOpeningDateOfTrade
	}
	return nil
}

func (x *CreditReportSummaryV1) GetLatestReportingDateOfTrade() *DateFirstAtAddressV1 {
	if x != nil {
		return x.LatestReportingDateOfTrade
	}
	return nil
}

func (x *CreditReportSummaryV1) GetDateOfLatestFileActivity() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateOfLatestFileActivity
	}
	return nil
}

func (x *CreditReportSummaryV1) GetReportIncldsCollectionItems() bool {
	if x != nil {
		return x.ReportIncldsCollectionItems
	}
	return false
}

func (x *CreditReportSummaryV1) GetHighCreditRangeLowAmount() uint32 {
	if x != nil {
		return x.HighCreditRangeLowAmount
	}
	return 0
}

func (x *CreditReportSummaryV1) GetHighCreditRangeHighAmount() uint32 {
	if x != nil {
		return x.HighCreditRangeHighAmount
	}
	return 0
}

func (x *CreditReportSummaryV1) GetTotalNumberOfTradeLines() uint32 {
	if x != nil {
		return x.TotalNumberOfTradeLines
	}
	return 0
}

func (x *CreditReportSummaryV1) GetCurrentStatusAccounts() *CurrentStatusAccountsV1 {
	if x != nil {
		return x.CurrentStatusAccounts
	}
	return nil
}

func (x *CreditReportSummaryV1) GetHistoryStatusAccounts() *HistoryStatusAccountsV1 {
	if x != nil {
		return x.HistoryStatusAccounts
	}
	return nil
}

func (x *CreditReportSummaryV1) GetHighCreditTotalRevolving() uint32 {
	if x != nil {
		return x.HighCreditTotalRevolving
	}
	return 0
}

func (x *CreditReportSummaryV1) GetHighCreditOpenended() uint32 {
	if x != nil {
		return x.HighCreditOpenended
	}
	return 0
}

func (x *CreditReportSummaryV1) GetHighCreditInstallment() uint32 {
	if x != nil {
		return x.HighCreditInstallment
	}
	return 0
}

func (x *CreditReportSummaryV1) GetAmountOwedTotalRevolving() uint32 {
	if x != nil {
		return x.AmountOwedTotalRevolving
	}
	return 0
}

func (x *CreditReportSummaryV1) GetAmountOwedTotalOpenended() uint32 {
	if x != nil {
		return x.AmountOwedTotalOpenended
	}
	return 0
}

func (x *CreditReportSummaryV1) GetAmountOwedTotalInstallment() uint32 {
	if x != nil {
		return x.AmountOwedTotalInstallment
	}
	return 0
}

func (x *CreditReportSummaryV1) GetPastDueTotalRevolving() uint32 {
	if x != nil {
		return x.PastDueTotalRevolving
	}
	return 0
}

func (x *CreditReportSummaryV1) GetPastDueTotalOpenended() uint32 {
	if x != nil {
		return x.PastDueTotalOpenended
	}
	return 0
}

func (x *CreditReportSummaryV1) GetPastDueTotalInstallment() uint32 {
	if x != nil {
		return x.PastDueTotalInstallment
	}
	return 0
}

func (x *CreditReportSummaryV1) GetNumberOfRevolvingAccounts() uint32 {
	if x != nil {
		return x.NumberOfRevolvingAccounts
	}
	return 0
}

func (x *CreditReportSummaryV1) GetNumberOfInstallmentAccounts() uint32 {
	if x != nil {
		return x.NumberOfInstallmentAccounts
	}
	return 0
}

type EmploymentInfoV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PositionDesc         string `protobuf:"bytes,1,opt,name=positionDesc,proto3" json:"positionDesc,omitempty"`
	EmployerName         string `protobuf:"bytes,2,opt,name=employerName,proto3" json:"employerName,omitempty"`
	EmploymentRecordType string `protobuf:"bytes,3,opt,name=employmentRecordType,proto3" json:"employmentRecordType,omitempty"`
	Classification       string `protobuf:"bytes,4,opt,name=classification,proto3" json:"classification,omitempty"`
}

func (x *EmploymentInfoV1) Reset() {
	*x = EmploymentInfoV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentInfoV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentInfoV1) ProtoMessage() {}

func (x *EmploymentInfoV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentInfoV1.ProtoReflect.Descriptor instead.
func (*EmploymentInfoV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{24}
}

func (x *EmploymentInfoV1) GetPositionDesc() string {
	if x != nil {
		return x.PositionDesc
	}
	return ""
}

func (x *EmploymentInfoV1) GetEmployerName() string {
	if x != nil {
		return x.EmployerName
	}
	return ""
}

func (x *EmploymentInfoV1) GetEmploymentRecordType() string {
	if x != nil {
		return x.EmploymentRecordType
	}
	return ""
}

func (x *EmploymentInfoV1) GetClassification() string {
	if x != nil {
		return x.Classification
	}
	return ""
}

type EmploymentInfosV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmploymentInfo []*EmploymentInfoV1 `protobuf:"bytes,1,rep,name=employmentInfo,proto3" json:"employmentInfo,omitempty"`
}

func (x *EmploymentInfosV1) Reset() {
	*x = EmploymentInfosV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentInfosV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentInfosV1) ProtoMessage() {}

func (x *EmploymentInfosV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentInfosV1.ProtoReflect.Descriptor instead.
func (*EmploymentInfosV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{25}
}

func (x *EmploymentInfosV1) GetEmploymentInfo() []*EmploymentInfoV1 {
	if x != nil {
		return x.EmploymentInfo
	}
	return nil
}

type CollectionRecordV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateReported                *DateFirstAtAddressV1 `protobuf:"bytes,1,opt,name=dateReported,proto3" json:"dateReported,omitempty"`
	DateAssigned                *DateFirstAtAddressV1 `protobuf:"bytes,2,opt,name=dateAssigned,proto3" json:"dateAssigned,omitempty"`
	ReportingMemberAgencyNumber string                `protobuf:"bytes,3,opt,name=reportingMemberAgencyNumber,proto3" json:"reportingMemberAgencyNumber,omitempty"`
	ClientNameOrNumber          string                `protobuf:"bytes,4,opt,name=clientNameOrNumber,proto3" json:"clientNameOrNumber,omitempty"`
	EcoaCode                    string                `protobuf:"bytes,5,opt,name=ecoaCode,proto3" json:"ecoaCode,omitempty"`
	DateOfLastActivity          *DateFirstAtAddressV1 `protobuf:"bytes,6,opt,name=dateOfLastActivity,proto3" json:"dateOfLastActivity,omitempty"`
	OriginalAmount              uint32                `protobuf:"varint,7,opt,name=originalAmount,proto3" json:"originalAmount,omitempty"`
	DateOfBalance               *DateFirstAtAddressV1 `protobuf:"bytes,8,opt,name=dateOfBalance,proto3" json:"dateOfBalance,omitempty"`
	BalanceAmount               uint32                `protobuf:"varint,9,opt,name=balanceAmount,proto3" json:"balanceAmount,omitempty"`
	StatusDate                  *DateFirstAtAddressV1 `protobuf:"bytes,10,opt,name=statusDate,proto3" json:"statusDate,omitempty"`
	CollectionItemStatus        string                `protobuf:"bytes,11,opt,name=collectionItemStatus,proto3" json:"collectionItemStatus,omitempty"`
}

func (x *CollectionRecordV1) Reset() {
	*x = CollectionRecordV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionRecordV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionRecordV1) ProtoMessage() {}

func (x *CollectionRecordV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionRecordV1.ProtoReflect.Descriptor instead.
func (*CollectionRecordV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{26}
}

func (x *CollectionRecordV1) GetDateReported() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateReported
	}
	return nil
}

func (x *CollectionRecordV1) GetDateAssigned() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateAssigned
	}
	return nil
}

func (x *CollectionRecordV1) GetReportingMemberAgencyNumber() string {
	if x != nil {
		return x.ReportingMemberAgencyNumber
	}
	return ""
}

func (x *CollectionRecordV1) GetClientNameOrNumber() string {
	if x != nil {
		return x.ClientNameOrNumber
	}
	return ""
}

func (x *CollectionRecordV1) GetEcoaCode() string {
	if x != nil {
		return x.EcoaCode
	}
	return ""
}

func (x *CollectionRecordV1) GetDateOfLastActivity() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateOfLastActivity
	}
	return nil
}

func (x *CollectionRecordV1) GetOriginalAmount() uint32 {
	if x != nil {
		return x.OriginalAmount
	}
	return 0
}

func (x *CollectionRecordV1) GetDateOfBalance() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateOfBalance
	}
	return nil
}

func (x *CollectionRecordV1) GetBalanceAmount() uint32 {
	if x != nil {
		return x.BalanceAmount
	}
	return 0
}

func (x *CollectionRecordV1) GetStatusDate() *DateFirstAtAddressV1 {
	if x != nil {
		return x.StatusDate
	}
	return nil
}

func (x *CollectionRecordV1) GetCollectionItemStatus() string {
	if x != nil {
		return x.CollectionItemStatus
	}
	return ""
}

type CollectionRecordsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectionRecord []*CollectionRecordV1 `protobuf:"bytes,1,rep,name=collectionRecord,proto3" json:"collectionRecord,omitempty"`
}

func (x *CollectionRecordsV1) Reset() {
	*x = CollectionRecordsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionRecordsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionRecordsV1) ProtoMessage() {}

func (x *CollectionRecordsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionRecordsV1.ProtoReflect.Descriptor instead.
func (*CollectionRecordsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{27}
}

func (x *CollectionRecordsV1) GetCollectionRecord() []*CollectionRecordV1 {
	if x != nil {
		return x.CollectionRecord
	}
	return nil
}

type CollectionRecordsInfoV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectionRecords *CollectionRecordsV1 `protobuf:"bytes,1,opt,name=collectionRecords,proto3" json:"collectionRecords,omitempty"`
}

func (x *CollectionRecordsInfoV1) Reset() {
	*x = CollectionRecordsInfoV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionRecordsInfoV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionRecordsInfoV1) ProtoMessage() {}

func (x *CollectionRecordsInfoV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionRecordsInfoV1.ProtoReflect.Descriptor instead.
func (*CollectionRecordsInfoV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{28}
}

func (x *CollectionRecordsInfoV1) GetCollectionRecords() *CollectionRecordsV1 {
	if x != nil {
		return x.CollectionRecords
	}
	return nil
}

type MessageV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *MessageV1) Reset() {
	*x = MessageV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageV1) ProtoMessage() {}

func (x *MessageV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageV1.ProtoReflect.Descriptor instead.
func (*MessageV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{29}
}

func (x *MessageV1) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *MessageV1) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MessagesListV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message []*MessageV1 `protobuf:"bytes,1,rep,name=message,proto3" json:"message,omitempty"`
}

func (x *MessagesListV1) Reset() {
	*x = MessagesListV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessagesListV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessagesListV1) ProtoMessage() {}

func (x *MessagesListV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessagesListV1.ProtoReflect.Descriptor instead.
func (*MessagesListV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{30}
}

func (x *MessagesListV1) GetMessage() []*MessageV1 {
	if x != nil {
		return x.Message
	}
	return nil
}

type CreditTradeHistoryRecordV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportingMemberNumber string                `protobuf:"bytes,1,opt,name=reportingMemberNumber,proto3" json:"reportingMemberNumber,omitempty"`
	MemberName            string                `protobuf:"bytes,2,opt,name=memberName,proto3" json:"memberName,omitempty"`
	TapeSupplierIndicator string                `protobuf:"bytes,3,opt,name=tapeSupplierIndicator,proto3" json:"tapeSupplierIndicator,omitempty"`
	DateReported          *DateFirstAtAddressV1 `protobuf:"bytes,4,opt,name=dateReported,proto3" json:"dateReported,omitempty"`
	DateAccountOpened     *DateFirstAtAddressV1 `protobuf:"bytes,5,opt,name=dateAccountOpened,proto3" json:"dateAccountOpened,omitempty"`
	HighestCreditAmount   uint32                `protobuf:"varint,6,opt,name=highestCreditAmount,proto3" json:"highestCreditAmount,omitempty"`
	AccountBalance        uint32                `protobuf:"varint,7,opt,name=accountBalance,proto3" json:"accountBalance,omitempty"`
	PastDueAmount         uint32                `protobuf:"varint,8,opt,name=pastDueAmount,proto3" json:"pastDueAmount,omitempty"`
	AccountTypeCode       string                `protobuf:"bytes,9,opt,name=accountTypeCode,proto3" json:"accountTypeCode,omitempty"`
	CurrentRateCode       string                `protobuf:"bytes,10,opt,name=currentRateCode,proto3" json:"currentRateCode,omitempty"`
	MonthsReviewed        string                `protobuf:"bytes,11,opt,name=monthsReviewed,proto3" json:"monthsReviewed,omitempty"`
	AccountDesignatorCode string                `protobuf:"bytes,12,opt,name=accountDesignatorCode,proto3" json:"accountDesignatorCode,omitempty"`
	ThirtydayCounter      string                `protobuf:"bytes,13,opt,name=thirtydayCounter,proto3" json:"thirtydayCounter,omitempty"`
	SixtydayCounter       string                `protobuf:"bytes,14,opt,name=sixtydayCounter,proto3" json:"sixtydayCounter,omitempty"`
	NinetydayCounter      string                `protobuf:"bytes,15,opt,name=ninetydayCounter,proto3" json:"ninetydayCounter,omitempty"`
	PrevRateCode1         string                `protobuf:"bytes,16,opt,name=prevRateCode1,proto3" json:"prevRateCode1,omitempty"`
	PrevRateDate1         *DateFirstAtAddressV1 `protobuf:"bytes,17,opt,name=prevRateDate1,proto3" json:"prevRateDate1,omitempty"`
	PrevRateCode2         string                `protobuf:"bytes,18,opt,name=prevRateCode2,proto3" json:"prevRateCode2,omitempty"`
	PrevRateDate2         *DateFirstAtAddressV1 `protobuf:"bytes,19,opt,name=prevRateDate2,proto3" json:"prevRateDate2,omitempty"`
	PrevRateCode3         string                `protobuf:"bytes,20,opt,name=prevRateCode3,proto3" json:"prevRateCode3,omitempty"`
	PrevRateDate3         *DateFirstAtAddressV1 `protobuf:"bytes,21,opt,name=prevRateDate3,proto3" json:"prevRateDate3,omitempty"`
	DateOfLastActivity    *DateFirstAtAddressV1 `protobuf:"bytes,22,opt,name=dateOfLastActivity,proto3" json:"dateOfLastActivity,omitempty"`
	Messages              *MessagesListV1       `protobuf:"bytes,23,opt,name=messages,proto3" json:"messages,omitempty"`
}

func (x *CreditTradeHistoryRecordV1) Reset() {
	*x = CreditTradeHistoryRecordV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditTradeHistoryRecordV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditTradeHistoryRecordV1) ProtoMessage() {}

func (x *CreditTradeHistoryRecordV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditTradeHistoryRecordV1.ProtoReflect.Descriptor instead.
func (*CreditTradeHistoryRecordV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{31}
}

func (x *CreditTradeHistoryRecordV1) GetReportingMemberNumber() string {
	if x != nil {
		return x.ReportingMemberNumber
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetMemberName() string {
	if x != nil {
		return x.MemberName
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetTapeSupplierIndicator() string {
	if x != nil {
		return x.TapeSupplierIndicator
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetDateReported() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateReported
	}
	return nil
}

func (x *CreditTradeHistoryRecordV1) GetDateAccountOpened() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateAccountOpened
	}
	return nil
}

func (x *CreditTradeHistoryRecordV1) GetHighestCreditAmount() uint32 {
	if x != nil {
		return x.HighestCreditAmount
	}
	return 0
}

func (x *CreditTradeHistoryRecordV1) GetAccountBalance() uint32 {
	if x != nil {
		return x.AccountBalance
	}
	return 0
}

func (x *CreditTradeHistoryRecordV1) GetPastDueAmount() uint32 {
	if x != nil {
		return x.PastDueAmount
	}
	return 0
}

func (x *CreditTradeHistoryRecordV1) GetAccountTypeCode() string {
	if x != nil {
		return x.AccountTypeCode
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetCurrentRateCode() string {
	if x != nil {
		return x.CurrentRateCode
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetMonthsReviewed() string {
	if x != nil {
		return x.MonthsReviewed
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetAccountDesignatorCode() string {
	if x != nil {
		return x.AccountDesignatorCode
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetThirtydayCounter() string {
	if x != nil {
		return x.ThirtydayCounter
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetSixtydayCounter() string {
	if x != nil {
		return x.SixtydayCounter
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetNinetydayCounter() string {
	if x != nil {
		return x.NinetydayCounter
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetPrevRateCode1() string {
	if x != nil {
		return x.PrevRateCode1
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetPrevRateDate1() *DateFirstAtAddressV1 {
	if x != nil {
		return x.PrevRateDate1
	}
	return nil
}

func (x *CreditTradeHistoryRecordV1) GetPrevRateCode2() string {
	if x != nil {
		return x.PrevRateCode2
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetPrevRateDate2() *DateFirstAtAddressV1 {
	if x != nil {
		return x.PrevRateDate2
	}
	return nil
}

func (x *CreditTradeHistoryRecordV1) GetPrevRateCode3() string {
	if x != nil {
		return x.PrevRateCode3
	}
	return ""
}

func (x *CreditTradeHistoryRecordV1) GetPrevRateDate3() *DateFirstAtAddressV1 {
	if x != nil {
		return x.PrevRateDate3
	}
	return nil
}

func (x *CreditTradeHistoryRecordV1) GetDateOfLastActivity() *DateFirstAtAddressV1 {
	if x != nil {
		return x.DateOfLastActivity
	}
	return nil
}

func (x *CreditTradeHistoryRecordV1) GetMessages() *MessagesListV1 {
	if x != nil {
		return x.Messages
	}
	return nil
}

type CreditTradeHistoryRecordsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditTradeHistoryRecord []*CreditTradeHistoryRecordV1 `protobuf:"bytes,1,rep,name=creditTradeHistoryRecord,proto3" json:"creditTradeHistoryRecord,omitempty"`
}

func (x *CreditTradeHistoryRecordsV1) Reset() {
	*x = CreditTradeHistoryRecordsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditTradeHistoryRecordsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditTradeHistoryRecordsV1) ProtoMessage() {}

func (x *CreditTradeHistoryRecordsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditTradeHistoryRecordsV1.ProtoReflect.Descriptor instead.
func (*CreditTradeHistoryRecordsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{32}
}

func (x *CreditTradeHistoryRecordsV1) GetCreditTradeHistoryRecord() []*CreditTradeHistoryRecordV1 {
	if x != nil {
		return x.CreditTradeHistoryRecord
	}
	return nil
}

type TradeAccountInfoV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditTradeHistoryRecords *CreditTradeHistoryRecordsV1 `protobuf:"bytes,1,opt,name=creditTradeHistoryRecords,proto3" json:"creditTradeHistoryRecords,omitempty"`
}

func (x *TradeAccountInfoV1) Reset() {
	*x = TradeAccountInfoV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TradeAccountInfoV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TradeAccountInfoV1) ProtoMessage() {}

func (x *TradeAccountInfoV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TradeAccountInfoV1.ProtoReflect.Descriptor instead.
func (*TradeAccountInfoV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{33}
}

func (x *TradeAccountInfoV1) GetCreditTradeHistoryRecords() *CreditTradeHistoryRecordsV1 {
	if x != nil {
		return x.CreditTradeHistoryRecords
	}
	return nil
}

type DateOfInquiryV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month uint32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	Day   uint32 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
}

func (x *DateOfInquiryV1) Reset() {
	*x = DateOfInquiryV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateOfInquiryV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateOfInquiryV1) ProtoMessage() {}

func (x *DateOfInquiryV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateOfInquiryV1.ProtoReflect.Descriptor instead.
func (*DateOfInquiryV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{34}
}

func (x *DateOfInquiryV1) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *DateOfInquiryV1) GetMonth() uint32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *DateOfInquiryV1) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

type InquiryHistoryHeaderRecordV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateOfInquiry *DateOfInquiryV1 `protobuf:"bytes,1,opt,name=dateOfInquiry,proto3" json:"dateOfInquiry,omitempty"`
	InquirerName  string           `protobuf:"bytes,2,opt,name=inquirerName,proto3" json:"inquirerName,omitempty"`
	InquirerId    string           `protobuf:"bytes,3,opt,name=inquirerId,proto3" json:"inquirerId,omitempty"`
}

func (x *InquiryHistoryHeaderRecordV1) Reset() {
	*x = InquiryHistoryHeaderRecordV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InquiryHistoryHeaderRecordV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InquiryHistoryHeaderRecordV1) ProtoMessage() {}

func (x *InquiryHistoryHeaderRecordV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InquiryHistoryHeaderRecordV1.ProtoReflect.Descriptor instead.
func (*InquiryHistoryHeaderRecordV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{35}
}

func (x *InquiryHistoryHeaderRecordV1) GetDateOfInquiry() *DateOfInquiryV1 {
	if x != nil {
		return x.DateOfInquiry
	}
	return nil
}

func (x *InquiryHistoryHeaderRecordV1) GetInquirerName() string {
	if x != nil {
		return x.InquirerName
	}
	return ""
}

func (x *InquiryHistoryHeaderRecordV1) GetInquirerId() string {
	if x != nil {
		return x.InquirerId
	}
	return ""
}

type InquiryHistoryHeaderRecordsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InquiryHistoryHeaderRecord []*InquiryHistoryHeaderRecordV1 `protobuf:"bytes,1,rep,name=inquiryHistoryHeaderRecord,proto3" json:"inquiryHistoryHeaderRecord,omitempty"`
}

func (x *InquiryHistoryHeaderRecordsV1) Reset() {
	*x = InquiryHistoryHeaderRecordsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InquiryHistoryHeaderRecordsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InquiryHistoryHeaderRecordsV1) ProtoMessage() {}

func (x *InquiryHistoryHeaderRecordsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InquiryHistoryHeaderRecordsV1.ProtoReflect.Descriptor instead.
func (*InquiryHistoryHeaderRecordsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{36}
}

func (x *InquiryHistoryHeaderRecordsV1) GetInquiryHistoryHeaderRecord() []*InquiryHistoryHeaderRecordV1 {
	if x != nil {
		return x.InquiryHistoryHeaderRecord
	}
	return nil
}

type InquiryHistoryHeaderV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InquiryHistoryHeaderRecords *InquiryHistoryHeaderRecordsV1 `protobuf:"bytes,1,opt,name=inquiryHistoryHeaderRecords,proto3" json:"inquiryHistoryHeaderRecords,omitempty"`
}

func (x *InquiryHistoryHeaderV1) Reset() {
	*x = InquiryHistoryHeaderV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InquiryHistoryHeaderV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InquiryHistoryHeaderV1) ProtoMessage() {}

func (x *InquiryHistoryHeaderV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InquiryHistoryHeaderV1.ProtoReflect.Descriptor instead.
func (*InquiryHistoryHeaderV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{37}
}

func (x *InquiryHistoryHeaderV1) GetInquiryHistoryHeaderRecords() *InquiryHistoryHeaderRecordsV1 {
	if x != nil {
		return x.InquiryHistoryHeaderRecords
	}
	return nil
}

type NcfProductReportV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubjectInfo           *SubjectInfoV1           `protobuf:"bytes,1,opt,name=subjectInfo,proto3" json:"subjectInfo,omitempty"`
	CreditReportSummary   *CreditReportSummaryV1   `protobuf:"bytes,2,opt,name=creditReportSummary,proto3" json:"creditReportSummary,omitempty"`
	EmploymentInfos       *EmploymentInfosV1       `protobuf:"bytes,3,opt,name=employmentInfos,proto3" json:"employmentInfos,omitempty"`
	CollectionRecordsInfo *CollectionRecordsInfoV1 `protobuf:"bytes,4,opt,name=collectionRecordsInfo,proto3" json:"collectionRecordsInfo,omitempty"`
	TradeAccountInfo      *TradeAccountInfoV1      `protobuf:"bytes,5,opt,name=tradeAccountInfo,proto3" json:"tradeAccountInfo,omitempty"`
	InquiryHistoryHeader  *InquiryHistoryHeaderV1  `protobuf:"bytes,6,opt,name=inquiryHistoryHeader,proto3" json:"inquiryHistoryHeader,omitempty"`
}

func (x *NcfProductReportV1) Reset() {
	*x = NcfProductReportV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NcfProductReportV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NcfProductReportV1) ProtoMessage() {}

func (x *NcfProductReportV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NcfProductReportV1.ProtoReflect.Descriptor instead.
func (*NcfProductReportV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{38}
}

func (x *NcfProductReportV1) GetSubjectInfo() *SubjectInfoV1 {
	if x != nil {
		return x.SubjectInfo
	}
	return nil
}

func (x *NcfProductReportV1) GetCreditReportSummary() *CreditReportSummaryV1 {
	if x != nil {
		return x.CreditReportSummary
	}
	return nil
}

func (x *NcfProductReportV1) GetEmploymentInfos() *EmploymentInfosV1 {
	if x != nil {
		return x.EmploymentInfos
	}
	return nil
}

func (x *NcfProductReportV1) GetCollectionRecordsInfo() *CollectionRecordsInfoV1 {
	if x != nil {
		return x.CollectionRecordsInfo
	}
	return nil
}

func (x *NcfProductReportV1) GetTradeAccountInfo() *TradeAccountInfoV1 {
	if x != nil {
		return x.TradeAccountInfo
	}
	return nil
}

func (x *NcfProductReportV1) GetInquiryHistoryHeader() *InquiryHistoryHeaderV1 {
	if x != nil {
		return x.InquiryHistoryHeader
	}
	return nil
}

type NcfReportV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NcfProductReport *NcfProductReportV1 `protobuf:"bytes,1,opt,name=ncfProductReport,proto3" json:"ncfProductReport,omitempty"`
}

func (x *NcfReportV1) Reset() {
	*x = NcfReportV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NcfReportV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NcfReportV1) ProtoMessage() {}

func (x *NcfReportV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NcfReportV1.ProtoReflect.Descriptor instead.
func (*NcfReportV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{39}
}

func (x *NcfReportV1) GetNcfProductReport() *NcfProductReportV1 {
	if x != nil {
		return x.NcfProductReport
	}
	return nil
}

type AuthorityHistoryRecordV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                       string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DockerNumber             string                 `protobuf:"bytes,2,opt,name=dockerNumber,proto3" json:"dockerNumber,omitempty"`
	DotNumber                int64                  `protobuf:"varint,3,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	AuthorityType            string                 `protobuf:"bytes,4,opt,name=authorityType,proto3" json:"authorityType,omitempty"`
	OriginalAction           string                 `protobuf:"bytes,5,opt,name=originalAction,proto3" json:"originalAction,omitempty"`
	OriginalActionServedDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=originalActionServedDate,proto3" json:"originalActionServedDate,omitempty"`
	FinalAction              *string                `protobuf:"bytes,7,opt,name=finalAction,proto3,oneof" json:"finalAction,omitempty"`
	FinalActionDecisionDate  *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=finalActionDecisionDate,proto3,oneof" json:"finalActionDecisionDate,omitempty"`
	FinalActionServedDate    *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=finalActionServedDate,proto3,oneof" json:"finalActionServedDate,omitempty"`
	CreatedAt                *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty"`
	VersionId                int32                  `protobuf:"varint,11,opt,name=VersionId,proto3" json:"VersionId,omitempty"`
}

func (x *AuthorityHistoryRecordV1) Reset() {
	*x = AuthorityHistoryRecordV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthorityHistoryRecordV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorityHistoryRecordV1) ProtoMessage() {}

func (x *AuthorityHistoryRecordV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorityHistoryRecordV1.ProtoReflect.Descriptor instead.
func (*AuthorityHistoryRecordV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{40}
}

func (x *AuthorityHistoryRecordV1) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuthorityHistoryRecordV1) GetDockerNumber() string {
	if x != nil {
		return x.DockerNumber
	}
	return ""
}

func (x *AuthorityHistoryRecordV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *AuthorityHistoryRecordV1) GetAuthorityType() string {
	if x != nil {
		return x.AuthorityType
	}
	return ""
}

func (x *AuthorityHistoryRecordV1) GetOriginalAction() string {
	if x != nil {
		return x.OriginalAction
	}
	return ""
}

func (x *AuthorityHistoryRecordV1) GetOriginalActionServedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.OriginalActionServedDate
	}
	return nil
}

func (x *AuthorityHistoryRecordV1) GetFinalAction() string {
	if x != nil && x.FinalAction != nil {
		return *x.FinalAction
	}
	return ""
}

func (x *AuthorityHistoryRecordV1) GetFinalActionDecisionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FinalActionDecisionDate
	}
	return nil
}

func (x *AuthorityHistoryRecordV1) GetFinalActionServedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FinalActionServedDate
	}
	return nil
}

func (x *AuthorityHistoryRecordV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AuthorityHistoryRecordV1) GetVersionId() int32 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

type GrantedAuthorityHistoryRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64 `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
}

func (x *GrantedAuthorityHistoryRequestV1) Reset() {
	*x = GrantedAuthorityHistoryRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantedAuthorityHistoryRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantedAuthorityHistoryRequestV1) ProtoMessage() {}

func (x *GrantedAuthorityHistoryRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantedAuthorityHistoryRequestV1.ProtoReflect.Descriptor instead.
func (*GrantedAuthorityHistoryRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{41}
}

func (x *GrantedAuthorityHistoryRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

type GrantedAuthorityHistoryV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*AuthorityHistoryRecordV1 `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *GrantedAuthorityHistoryV1) Reset() {
	*x = GrantedAuthorityHistoryV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantedAuthorityHistoryV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantedAuthorityHistoryV1) ProtoMessage() {}

func (x *GrantedAuthorityHistoryV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantedAuthorityHistoryV1.ProtoReflect.Descriptor instead.
func (*GrantedAuthorityHistoryV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{42}
}

func (x *GrantedAuthorityHistoryV1) GetRecords() []*AuthorityHistoryRecordV1 {
	if x != nil {
		return x.Records
	}
	return nil
}

type TransactionDetailsExV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessingStatus string `protobuf:"bytes,1,opt,name=processingStatus,proto3" json:"processingStatus,omitempty"`
}

func (x *TransactionDetailsExV1) Reset() {
	*x = TransactionDetailsExV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionDetailsExV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetailsExV1) ProtoMessage() {}

func (x *TransactionDetailsExV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetailsExV1.ProtoReflect.Descriptor instead.
func (*TransactionDetailsExV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{43}
}

func (x *TransactionDetailsExV1) GetProcessingStatus() string {
	if x != nil {
		return x.ProcessingStatus
	}
	return ""
}

type BIPDInsuranceHistoryRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64                  `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	SinceDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=sinceDate,proto3,oneof" json:"sinceDate,omitempty"`
}

func (x *BIPDInsuranceHistoryRequestV1) Reset() {
	*x = BIPDInsuranceHistoryRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BIPDInsuranceHistoryRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BIPDInsuranceHistoryRequestV1) ProtoMessage() {}

func (x *BIPDInsuranceHistoryRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BIPDInsuranceHistoryRequestV1.ProtoReflect.Descriptor instead.
func (*BIPDInsuranceHistoryRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{44}
}

func (x *BIPDInsuranceHistoryRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *BIPDInsuranceHistoryRequestV1) GetSinceDate() *timestamppb.Timestamp {
	if x != nil {
		return x.SinceDate
	}
	return nil
}

type InsuranceRecordV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                     string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DocketNumber           string                 `protobuf:"bytes,2,opt,name=docketNumber,proto3" json:"docketNumber,omitempty"`
	DotNumber              int64                  `protobuf:"varint,3,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	Form                   string                 `protobuf:"bytes,4,opt,name=form,proto3" json:"form,omitempty"`
	InsuranceType          *string                `protobuf:"bytes,5,opt,name=insuranceType,proto3,oneof" json:"insuranceType,omitempty"`
	InsuranceCompanyName   string                 `protobuf:"bytes,6,opt,name=insuranceCompanyName,proto3" json:"insuranceCompanyName,omitempty"`
	PolicyNumber           *string                `protobuf:"bytes,7,opt,name=policyNumber,proto3,oneof" json:"policyNumber,omitempty"`
	PostedDate             *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=postedDate,proto3,oneof" json:"postedDate,omitempty"`
	UnderlyingLimit        *float64               `protobuf:"fixed64,9,opt,name=underlyingLimit,proto3,oneof" json:"underlyingLimit,omitempty"`
	MaxCoverageAmount      *float64               `protobuf:"fixed64,10,opt,name=maxCoverageAmount,proto3,oneof" json:"maxCoverageAmount,omitempty"`
	EffectiveDate          *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=effectiveDate,proto3,oneof" json:"effectiveDate,omitempty"`
	CancelEffectiveDate    *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=cancelEffectiveDate,proto3,oneof" json:"cancelEffectiveDate,omitempty"`
	CreatedAt              *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	VersionId              int32                  `protobuf:"varint,14,opt,name=versionId,proto3" json:"versionId,omitempty"`
	BipdFlag               *bool                  `protobuf:"varint,15,opt,name=bipdFlag,proto3,oneof" json:"bipdFlag,omitempty"`
	MinCoverageAmount      *int32                 `protobuf:"varint,16,opt,name=minCoverageAmount,proto3,oneof" json:"minCoverageAmount,omitempty"`
	BipdClass              *string                `protobuf:"bytes,17,opt,name=bipdClass,proto3,oneof" json:"bipdClass,omitempty"`
	InsuranceCompanyBranch *string                `protobuf:"bytes,18,opt,name=insuranceCompanyBranch,proto3,oneof" json:"insuranceCompanyBranch,omitempty"`
	CancellationDetails    *CancellationV1        `protobuf:"bytes,19,opt,name=cancellationDetails,proto3,oneof" json:"cancellationDetails,omitempty"`
}

func (x *InsuranceRecordV1) Reset() {
	*x = InsuranceRecordV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuranceRecordV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuranceRecordV1) ProtoMessage() {}

func (x *InsuranceRecordV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuranceRecordV1.ProtoReflect.Descriptor instead.
func (*InsuranceRecordV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{45}
}

func (x *InsuranceRecordV1) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InsuranceRecordV1) GetDocketNumber() string {
	if x != nil {
		return x.DocketNumber
	}
	return ""
}

func (x *InsuranceRecordV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *InsuranceRecordV1) GetForm() string {
	if x != nil {
		return x.Form
	}
	return ""
}

func (x *InsuranceRecordV1) GetInsuranceType() string {
	if x != nil && x.InsuranceType != nil {
		return *x.InsuranceType
	}
	return ""
}

func (x *InsuranceRecordV1) GetInsuranceCompanyName() string {
	if x != nil {
		return x.InsuranceCompanyName
	}
	return ""
}

func (x *InsuranceRecordV1) GetPolicyNumber() string {
	if x != nil && x.PolicyNumber != nil {
		return *x.PolicyNumber
	}
	return ""
}

func (x *InsuranceRecordV1) GetPostedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PostedDate
	}
	return nil
}

func (x *InsuranceRecordV1) GetUnderlyingLimit() float64 {
	if x != nil && x.UnderlyingLimit != nil {
		return *x.UnderlyingLimit
	}
	return 0
}

func (x *InsuranceRecordV1) GetMaxCoverageAmount() float64 {
	if x != nil && x.MaxCoverageAmount != nil {
		return *x.MaxCoverageAmount
	}
	return 0
}

func (x *InsuranceRecordV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

func (x *InsuranceRecordV1) GetCancelEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.CancelEffectiveDate
	}
	return nil
}

func (x *InsuranceRecordV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *InsuranceRecordV1) GetVersionId() int32 {
	if x != nil {
		return x.VersionId
	}
	return 0
}

func (x *InsuranceRecordV1) GetBipdFlag() bool {
	if x != nil && x.BipdFlag != nil {
		return *x.BipdFlag
	}
	return false
}

func (x *InsuranceRecordV1) GetMinCoverageAmount() int32 {
	if x != nil && x.MinCoverageAmount != nil {
		return *x.MinCoverageAmount
	}
	return 0
}

func (x *InsuranceRecordV1) GetBipdClass() string {
	if x != nil && x.BipdClass != nil {
		return *x.BipdClass
	}
	return ""
}

func (x *InsuranceRecordV1) GetInsuranceCompanyBranch() string {
	if x != nil && x.InsuranceCompanyBranch != nil {
		return *x.InsuranceCompanyBranch
	}
	return ""
}

func (x *InsuranceRecordV1) GetCancellationDetails() *CancellationV1 {
	if x != nil {
		return x.CancellationDetails
	}
	return nil
}

type CancellationV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method         string  `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	Form           *string `protobuf:"bytes,2,opt,name=form,proto3,oneof" json:"form,omitempty"`
	SpecificMethod *string `protobuf:"bytes,3,opt,name=specificMethod,proto3,oneof" json:"specificMethod,omitempty"`
}

func (x *CancellationV1) Reset() {
	*x = CancellationV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancellationV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancellationV1) ProtoMessage() {}

func (x *CancellationV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancellationV1.ProtoReflect.Descriptor instead.
func (*CancellationV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{46}
}

func (x *CancellationV1) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *CancellationV1) GetForm() string {
	if x != nil && x.Form != nil {
		return *x.Form
	}
	return ""
}

func (x *CancellationV1) GetSpecificMethod() string {
	if x != nil && x.SpecificMethod != nil {
		return *x.SpecificMethod
	}
	return ""
}

type BIPDInsuranceHistoryV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*InsuranceRecordV1 `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *BIPDInsuranceHistoryV1) Reset() {
	*x = BIPDInsuranceHistoryV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BIPDInsuranceHistoryV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BIPDInsuranceHistoryV1) ProtoMessage() {}

func (x *BIPDInsuranceHistoryV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BIPDInsuranceHistoryV1.ProtoReflect.Descriptor instead.
func (*BIPDInsuranceHistoryV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{47}
}

func (x *BIPDInsuranceHistoryV1) GetRecords() []*InsuranceRecordV1 {
	if x != nil {
		return x.Records
	}
	return nil
}

type LatestRatingTiersDumpDateRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64 `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
}

func (x *LatestRatingTiersDumpDateRequestV1) Reset() {
	*x = LatestRatingTiersDumpDateRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LatestRatingTiersDumpDateRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LatestRatingTiersDumpDateRequestV1) ProtoMessage() {}

func (x *LatestRatingTiersDumpDateRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LatestRatingTiersDumpDateRequestV1.ProtoReflect.Descriptor instead.
func (*LatestRatingTiersDumpDateRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{48}
}

func (x *LatestRatingTiersDumpDateRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

type LatestRatingTiersDumpDateV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *LatestRatingTiersDumpDateV1) Reset() {
	*x = LatestRatingTiersDumpDateV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LatestRatingTiersDumpDateV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LatestRatingTiersDumpDateV1) ProtoMessage() {}

func (x *LatestRatingTiersDumpDateV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LatestRatingTiersDumpDateV1.ProtoReflect.Descriptor instead.
func (*LatestRatingTiersDumpDateV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{49}
}

func (x *LatestRatingTiersDumpDateV1) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

type RatingTierRecordV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date                       *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	InspectionIndicator        string                 `protobuf:"bytes,2,opt,name=inspectionIndicator,proto3" json:"inspectionIndicator,omitempty"`
	LargeMachineryIndicator    bool                   `protobuf:"varint,3,opt,name=largeMachineryIndicator,proto3" json:"largeMachineryIndicator,omitempty"`
	CrashFrequency             *float64               `protobuf:"fixed64,4,opt,name=crashFrequency,proto3,oneof" json:"crashFrequency,omitempty"`
	PowerUnits                 *int64                 `protobuf:"varint,5,opt,name=powerUnits,proto3,oneof" json:"powerUnits,omitempty"`
	AverageMiles               *float64               `protobuf:"fixed64,6,opt,name=averageMiles,proto3,oneof" json:"averageMiles,omitempty"`
	AverageCombinedGrossWeight *float64               `protobuf:"fixed64,7,opt,name=averageCombinedGrossWeight,proto3,oneof" json:"averageCombinedGrossWeight,omitempty"`
	MaintenanceViolationsRatio *float64               `protobuf:"fixed64,8,opt,name=maintenanceViolationsRatio,proto3,oneof" json:"maintenanceViolationsRatio,omitempty"`
	UnsafeViolationRatio       *float64               `protobuf:"fixed64,9,opt,name=unsafeViolationRatio,proto3,oneof" json:"unsafeViolationRatio,omitempty"`
	VehicleInspectionRatio     *float64               `protobuf:"fixed64,10,opt,name=vehicleInspectionRatio,proto3,oneof" json:"vehicleInspectionRatio,omitempty"`
	UnsafeDrivingScore         *float64               `protobuf:"fixed64,11,opt,name=unsafeDrivingScore,proto3,oneof" json:"unsafeDrivingScore,omitempty"`
}

func (x *RatingTierRecordV1) Reset() {
	*x = RatingTierRecordV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RatingTierRecordV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RatingTierRecordV1) ProtoMessage() {}

func (x *RatingTierRecordV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RatingTierRecordV1.ProtoReflect.Descriptor instead.
func (*RatingTierRecordV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{50}
}

func (x *RatingTierRecordV1) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *RatingTierRecordV1) GetInspectionIndicator() string {
	if x != nil {
		return x.InspectionIndicator
	}
	return ""
}

func (x *RatingTierRecordV1) GetLargeMachineryIndicator() bool {
	if x != nil {
		return x.LargeMachineryIndicator
	}
	return false
}

func (x *RatingTierRecordV1) GetCrashFrequency() float64 {
	if x != nil && x.CrashFrequency != nil {
		return *x.CrashFrequency
	}
	return 0
}

func (x *RatingTierRecordV1) GetPowerUnits() int64 {
	if x != nil && x.PowerUnits != nil {
		return *x.PowerUnits
	}
	return 0
}

func (x *RatingTierRecordV1) GetAverageMiles() float64 {
	if x != nil && x.AverageMiles != nil {
		return *x.AverageMiles
	}
	return 0
}

func (x *RatingTierRecordV1) GetAverageCombinedGrossWeight() float64 {
	if x != nil && x.AverageCombinedGrossWeight != nil {
		return *x.AverageCombinedGrossWeight
	}
	return 0
}

func (x *RatingTierRecordV1) GetMaintenanceViolationsRatio() float64 {
	if x != nil && x.MaintenanceViolationsRatio != nil {
		return *x.MaintenanceViolationsRatio
	}
	return 0
}

func (x *RatingTierRecordV1) GetUnsafeViolationRatio() float64 {
	if x != nil && x.UnsafeViolationRatio != nil {
		return *x.UnsafeViolationRatio
	}
	return 0
}

func (x *RatingTierRecordV1) GetVehicleInspectionRatio() float64 {
	if x != nil && x.VehicleInspectionRatio != nil {
		return *x.VehicleInspectionRatio
	}
	return 0
}

func (x *RatingTierRecordV1) GetUnsafeDrivingScore() float64 {
	if x != nil && x.UnsafeDrivingScore != nil {
		return *x.UnsafeDrivingScore
	}
	return 0
}

type RatingTierRecordsForDumpDateRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64                  `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	DumpDate  *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=dumpDate,proto3" json:"dumpDate,omitempty"`
}

func (x *RatingTierRecordsForDumpDateRequestV1) Reset() {
	*x = RatingTierRecordsForDumpDateRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RatingTierRecordsForDumpDateRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RatingTierRecordsForDumpDateRequestV1) ProtoMessage() {}

func (x *RatingTierRecordsForDumpDateRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RatingTierRecordsForDumpDateRequestV1.ProtoReflect.Descriptor instead.
func (*RatingTierRecordsForDumpDateRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{51}
}

func (x *RatingTierRecordsForDumpDateRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *RatingTierRecordsForDumpDateRequestV1) GetDumpDate() *timestamppb.Timestamp {
	if x != nil {
		return x.DumpDate
	}
	return nil
}

type RatingTierRecordsForDumpDateV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*RatingTierRecordV1 `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *RatingTierRecordsForDumpDateV1) Reset() {
	*x = RatingTierRecordsForDumpDateV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RatingTierRecordsForDumpDateV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RatingTierRecordsForDumpDateV1) ProtoMessage() {}

func (x *RatingTierRecordsForDumpDateV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RatingTierRecordsForDumpDateV1.ProtoReflect.Descriptor instead.
func (*RatingTierRecordsForDumpDateV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{52}
}

func (x *RatingTierRecordsForDumpDateV1) GetRecords() []*RatingTierRecordV1 {
	if x != nil {
		return x.Records
	}
	return nil
}

type RatingTierRecordRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber  int64                  `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	DumpDate   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=dumpDate,proto3" json:"dumpDate,omitempty"`
	RecordDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=recordDate,proto3" json:"recordDate,omitempty"`
}

func (x *RatingTierRecordRequestV1) Reset() {
	*x = RatingTierRecordRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RatingTierRecordRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RatingTierRecordRequestV1) ProtoMessage() {}

func (x *RatingTierRecordRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RatingTierRecordRequestV1.ProtoReflect.Descriptor instead.
func (*RatingTierRecordRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{53}
}

func (x *RatingTierRecordRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *RatingTierRecordRequestV1) GetDumpDate() *timestamppb.Timestamp {
	if x != nil {
		return x.DumpDate
	}
	return nil
}

func (x *RatingTierRecordRequestV1) GetRecordDate() *timestamppb.Timestamp {
	if x != nil {
		return x.RecordDate
	}
	return nil
}

type FMCSAViolationRecordsRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber     int32                  `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	EffectiveDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *FMCSAViolationRecordsRequestV1) Reset() {
	*x = FMCSAViolationRecordsRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSAViolationRecordsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSAViolationRecordsRequestV1) ProtoMessage() {}

func (x *FMCSAViolationRecordsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSAViolationRecordsRequestV1.ProtoReflect.Descriptor instead.
func (*FMCSAViolationRecordsRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{54}
}

func (x *FMCSAViolationRecordsRequestV1) GetDotNumber() int32 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *FMCSAViolationRecordsRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type FMCSAViolationRecordsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*FMCSAViolationRecordV1 `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *FMCSAViolationRecordsV1) Reset() {
	*x = FMCSAViolationRecordsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSAViolationRecordsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSAViolationRecordsV1) ProtoMessage() {}

func (x *FMCSAViolationRecordsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSAViolationRecordsV1.ProtoReflect.Descriptor instead.
func (*FMCSAViolationRecordsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{55}
}

func (x *FMCSAViolationRecordsV1) GetRecords() []*FMCSAViolationRecordV1 {
	if x != nil {
		return x.Records
	}
	return nil
}

type FMCSAViolationRecordV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId               string                   `protobuf:"bytes,1,opt,name=rowId,proto3" json:"rowId,omitempty"`
	PublishedDate       *timestamppb.Timestamp   `protobuf:"bytes,2,opt,name=publishedDate,proto3" json:"publishedDate,omitempty"`
	InspectionId        int64                    `protobuf:"varint,3,opt,name=inspectionId,proto3" json:"inspectionId,omitempty"`
	InspectionDate      *timestamppb.Timestamp   `protobuf:"bytes,4,opt,name=inspectionDate,proto3" json:"inspectionDate,omitempty"`
	DotNumber           int64                    `protobuf:"varint,5,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	Code                string                   `protobuf:"bytes,6,opt,name=code,proto3" json:"code,omitempty"`
	Category            FMCSAViolationCategoryV1 `protobuf:"varint,7,opt,name=category,proto3,enum=data_fetching.FMCSAViolationCategoryV1" json:"category,omitempty"`
	OosIndicator        bool                     `protobuf:"varint,8,opt,name=oosIndicator,proto3" json:"oosIndicator,omitempty"`
	OosWeight           int32                    `protobuf:"varint,9,opt,name=oosWeight,proto3" json:"oosWeight,omitempty"`
	SeverityWeight      int32                    `protobuf:"varint,10,opt,name=severityWeight,proto3" json:"severityWeight,omitempty"`
	TotalSeverityWeight int32                    `protobuf:"varint,11,opt,name=totalSeverityWeight,proto3" json:"totalSeverityWeight,omitempty"`
	TimeWeight          int32                    `protobuf:"varint,12,opt,name=timeWeight,proto3" json:"timeWeight,omitempty"`
	ViolationId         *int32                   `protobuf:"varint,13,opt,name=violationId,proto3,oneof" json:"violationId,omitempty"`
	CountyCode          *string                  `protobuf:"bytes,14,opt,name=countyCode,proto3,oneof" json:"countyCode,omitempty"`
	CountyCodeState     *string                  `protobuf:"bytes,15,opt,name=countyCodeState,proto3,oneof" json:"countyCodeState,omitempty"`
	Vins                []string                 `protobuf:"bytes,16,rep,name=vins,proto3" json:"vins,omitempty"`
}

func (x *FMCSAViolationRecordV1) Reset() {
	*x = FMCSAViolationRecordV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSAViolationRecordV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSAViolationRecordV1) ProtoMessage() {}

func (x *FMCSAViolationRecordV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSAViolationRecordV1.ProtoReflect.Descriptor instead.
func (*FMCSAViolationRecordV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{56}
}

func (x *FMCSAViolationRecordV1) GetRowId() string {
	if x != nil {
		return x.RowId
	}
	return ""
}

func (x *FMCSAViolationRecordV1) GetPublishedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PublishedDate
	}
	return nil
}

func (x *FMCSAViolationRecordV1) GetInspectionId() int64 {
	if x != nil {
		return x.InspectionId
	}
	return 0
}

func (x *FMCSAViolationRecordV1) GetInspectionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.InspectionDate
	}
	return nil
}

func (x *FMCSAViolationRecordV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *FMCSAViolationRecordV1) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *FMCSAViolationRecordV1) GetCategory() FMCSAViolationCategoryV1 {
	if x != nil {
		return x.Category
	}
	return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_Unspecified
}

func (x *FMCSAViolationRecordV1) GetOosIndicator() bool {
	if x != nil {
		return x.OosIndicator
	}
	return false
}

func (x *FMCSAViolationRecordV1) GetOosWeight() int32 {
	if x != nil {
		return x.OosWeight
	}
	return 0
}

func (x *FMCSAViolationRecordV1) GetSeverityWeight() int32 {
	if x != nil {
		return x.SeverityWeight
	}
	return 0
}

func (x *FMCSAViolationRecordV1) GetTotalSeverityWeight() int32 {
	if x != nil {
		return x.TotalSeverityWeight
	}
	return 0
}

func (x *FMCSAViolationRecordV1) GetTimeWeight() int32 {
	if x != nil {
		return x.TimeWeight
	}
	return 0
}

func (x *FMCSAViolationRecordV1) GetViolationId() int32 {
	if x != nil && x.ViolationId != nil {
		return *x.ViolationId
	}
	return 0
}

func (x *FMCSAViolationRecordV1) GetCountyCode() string {
	if x != nil && x.CountyCode != nil {
		return *x.CountyCode
	}
	return ""
}

func (x *FMCSAViolationRecordV1) GetCountyCodeState() string {
	if x != nil && x.CountyCodeState != nil {
		return *x.CountyCodeState
	}
	return ""
}

func (x *FMCSAViolationRecordV1) GetVins() []string {
	if x != nil {
		return x.Vins
	}
	return nil
}

type FMCSAInspectionRecordsRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber     int32                  `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	EffectiveDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *FMCSAInspectionRecordsRequestV1) Reset() {
	*x = FMCSAInspectionRecordsRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSAInspectionRecordsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSAInspectionRecordsRequestV1) ProtoMessage() {}

func (x *FMCSAInspectionRecordsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSAInspectionRecordsRequestV1.ProtoReflect.Descriptor instead.
func (*FMCSAInspectionRecordsRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{57}
}

func (x *FMCSAInspectionRecordsRequestV1) GetDotNumber() int32 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *FMCSAInspectionRecordsRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type FMCSAInspectionRecordsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*FMCSAInspectionRecordV1 `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *FMCSAInspectionRecordsV1) Reset() {
	*x = FMCSAInspectionRecordsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSAInspectionRecordsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSAInspectionRecordsV1) ProtoMessage() {}

func (x *FMCSAInspectionRecordsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSAInspectionRecordsV1.ProtoReflect.Descriptor instead.
func (*FMCSAInspectionRecordsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{58}
}

func (x *FMCSAInspectionRecordsV1) GetRecords() []*FMCSAInspectionRecordV1 {
	if x != nil {
		return x.Records
	}
	return nil
}

type FMCSAInspectionRecordV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowID                         string                      `protobuf:"bytes,1,opt,name=rowID,proto3" json:"rowID,omitempty"`
	PublishedDate                 *timestamppb.Timestamp      `protobuf:"bytes,2,opt,name=publishedDate,proto3" json:"publishedDate,omitempty"`
	InspectionID                  int64                       `protobuf:"varint,3,opt,name=inspectionID,proto3" json:"inspectionID,omitempty"`
	DotNumber                     int64                       `protobuf:"varint,4,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	ReportNumber                  string                      `protobuf:"bytes,5,opt,name=reportNumber,proto3" json:"reportNumber,omitempty"`
	ReportState                   string                      `protobuf:"bytes,6,opt,name=reportState,proto3" json:"reportState,omitempty"`
	InspectionDate                *timestamppb.Timestamp      `protobuf:"bytes,7,opt,name=inspectionDate,proto3" json:"inspectionDate,omitempty"`
	InspectionLevel               FMCSAInspectionLevelV1      `protobuf:"varint,8,opt,name=inspectionLevel,proto3,enum=data_fetching.FMCSAInspectionLevelV1" json:"inspectionLevel,omitempty"`
	TimeWeight                    int32                       `protobuf:"varint,9,opt,name=timeWeight,proto3" json:"timeWeight,omitempty"`
	DriverOOSTotal                int32                       `protobuf:"varint,10,opt,name=driverOOSTotal,proto3" json:"driverOOSTotal,omitempty"`
	VehicleOOSTotal               int32                       `protobuf:"varint,11,opt,name=vehicleOOSTotal,proto3" json:"vehicleOOSTotal,omitempty"`
	TotalOOSViolations            int32                       `protobuf:"varint,12,opt,name=totalOOSViolations,proto3" json:"totalOOSViolations,omitempty"`
	HazmatOOSTotal                int32                       `protobuf:"varint,13,opt,name=hazmatOOSTotal,proto3" json:"hazmatOOSTotal,omitempty"`
	HazmatViolationsSent          int32                       `protobuf:"varint,14,opt,name=hazmatViolationsSent,proto3" json:"hazmatViolationsSent,omitempty"`
	HazmatPlacardReq              bool                        `protobuf:"varint,15,opt,name=hazmatPlacardReq,proto3" json:"hazmatPlacardReq,omitempty"`
	BasicCategories               map[string]bool             `protobuf:"bytes,16,rep,name=basicCategories,proto3" json:"basicCategories,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	BasicViolations               map[string]int32            `protobuf:"bytes,17,rep,name=basicViolations,proto3" json:"basicViolations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TotalBasicViols               int32                       `protobuf:"varint,18,opt,name=totalBasicViols,proto3" json:"totalBasicViols,omitempty"`
	PublicVINs                    []string                    `protobuf:"bytes,19,rep,name=publicVINs,proto3" json:"publicVINs,omitempty"`
	Region                        *string                     `protobuf:"bytes,20,opt,name=region,proto3,oneof" json:"region,omitempty"`
	Location                      *string                     `protobuf:"bytes,21,opt,name=location,proto3,oneof" json:"location,omitempty"`
	CountyCodeState               *string                     `protobuf:"bytes,22,opt,name=countyCodeState,proto3,oneof" json:"countyCodeState,omitempty"`
	CountyCode                    *string                     `protobuf:"bytes,23,opt,name=countyCode,proto3,oneof" json:"countyCode,omitempty"`
	ShipperName                   *string                     `protobuf:"bytes,24,opt,name=shipperName,proto3,oneof" json:"shipperName,omitempty"`
	WasSizeWeightEnforcement      *bool                       `protobuf:"varint,25,opt,name=wasSizeWeightEnforcement,proto3,oneof" json:"wasSizeWeightEnforcement,omitempty"`
	WasTrafficEnforcement         *bool                       `protobuf:"varint,26,opt,name=wasTrafficEnforcement,proto3,oneof" json:"wasTrafficEnforcement,omitempty"`
	WasLocalEnforcement           *bool                       `protobuf:"varint,27,opt,name=wasLocalEnforcement,proto3,oneof" json:"wasLocalEnforcement,omitempty"`
	WasPostAccident               *bool                       `protobuf:"varint,28,opt,name=wasPostAccident,proto3,oneof" json:"wasPostAccident,omitempty"`
	CombinationVehicleGrossWeight *int32                      `protobuf:"varint,29,opt,name=combinationVehicleGrossWeight,proto3,oneof" json:"combinationVehicleGrossWeight,omitempty"`
	Vins                          []string                    `protobuf:"bytes,30,rep,name=vins,proto3" json:"vins,omitempty"`
	Vehicles                      []*FMCSAInspectionVehicleV1 `protobuf:"bytes,31,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
}

func (x *FMCSAInspectionRecordV1) Reset() {
	*x = FMCSAInspectionRecordV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSAInspectionRecordV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSAInspectionRecordV1) ProtoMessage() {}

func (x *FMCSAInspectionRecordV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSAInspectionRecordV1.ProtoReflect.Descriptor instead.
func (*FMCSAInspectionRecordV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{59}
}

func (x *FMCSAInspectionRecordV1) GetRowID() string {
	if x != nil {
		return x.RowID
	}
	return ""
}

func (x *FMCSAInspectionRecordV1) GetPublishedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PublishedDate
	}
	return nil
}

func (x *FMCSAInspectionRecordV1) GetInspectionID() int64 {
	if x != nil {
		return x.InspectionID
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetReportNumber() string {
	if x != nil {
		return x.ReportNumber
	}
	return ""
}

func (x *FMCSAInspectionRecordV1) GetReportState() string {
	if x != nil {
		return x.ReportState
	}
	return ""
}

func (x *FMCSAInspectionRecordV1) GetInspectionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.InspectionDate
	}
	return nil
}

func (x *FMCSAInspectionRecordV1) GetInspectionLevel() FMCSAInspectionLevelV1 {
	if x != nil {
		return x.InspectionLevel
	}
	return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Unspecified
}

func (x *FMCSAInspectionRecordV1) GetTimeWeight() int32 {
	if x != nil {
		return x.TimeWeight
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetDriverOOSTotal() int32 {
	if x != nil {
		return x.DriverOOSTotal
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetVehicleOOSTotal() int32 {
	if x != nil {
		return x.VehicleOOSTotal
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetTotalOOSViolations() int32 {
	if x != nil {
		return x.TotalOOSViolations
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetHazmatOOSTotal() int32 {
	if x != nil {
		return x.HazmatOOSTotal
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetHazmatViolationsSent() int32 {
	if x != nil {
		return x.HazmatViolationsSent
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetHazmatPlacardReq() bool {
	if x != nil {
		return x.HazmatPlacardReq
	}
	return false
}

func (x *FMCSAInspectionRecordV1) GetBasicCategories() map[string]bool {
	if x != nil {
		return x.BasicCategories
	}
	return nil
}

func (x *FMCSAInspectionRecordV1) GetBasicViolations() map[string]int32 {
	if x != nil {
		return x.BasicViolations
	}
	return nil
}

func (x *FMCSAInspectionRecordV1) GetTotalBasicViols() int32 {
	if x != nil {
		return x.TotalBasicViols
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetPublicVINs() []string {
	if x != nil {
		return x.PublicVINs
	}
	return nil
}

func (x *FMCSAInspectionRecordV1) GetRegion() string {
	if x != nil && x.Region != nil {
		return *x.Region
	}
	return ""
}

func (x *FMCSAInspectionRecordV1) GetLocation() string {
	if x != nil && x.Location != nil {
		return *x.Location
	}
	return ""
}

func (x *FMCSAInspectionRecordV1) GetCountyCodeState() string {
	if x != nil && x.CountyCodeState != nil {
		return *x.CountyCodeState
	}
	return ""
}

func (x *FMCSAInspectionRecordV1) GetCountyCode() string {
	if x != nil && x.CountyCode != nil {
		return *x.CountyCode
	}
	return ""
}

func (x *FMCSAInspectionRecordV1) GetShipperName() string {
	if x != nil && x.ShipperName != nil {
		return *x.ShipperName
	}
	return ""
}

func (x *FMCSAInspectionRecordV1) GetWasSizeWeightEnforcement() bool {
	if x != nil && x.WasSizeWeightEnforcement != nil {
		return *x.WasSizeWeightEnforcement
	}
	return false
}

func (x *FMCSAInspectionRecordV1) GetWasTrafficEnforcement() bool {
	if x != nil && x.WasTrafficEnforcement != nil {
		return *x.WasTrafficEnforcement
	}
	return false
}

func (x *FMCSAInspectionRecordV1) GetWasLocalEnforcement() bool {
	if x != nil && x.WasLocalEnforcement != nil {
		return *x.WasLocalEnforcement
	}
	return false
}

func (x *FMCSAInspectionRecordV1) GetWasPostAccident() bool {
	if x != nil && x.WasPostAccident != nil {
		return *x.WasPostAccident
	}
	return false
}

func (x *FMCSAInspectionRecordV1) GetCombinationVehicleGrossWeight() int32 {
	if x != nil && x.CombinationVehicleGrossWeight != nil {
		return *x.CombinationVehicleGrossWeight
	}
	return 0
}

func (x *FMCSAInspectionRecordV1) GetVins() []string {
	if x != nil {
		return x.Vins
	}
	return nil
}

func (x *FMCSAInspectionRecordV1) GetVehicles() []*FMCSAInspectionVehicleV1 {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

type FMCSAInspectionVehicleV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin     string `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	Company string `protobuf:"bytes,2,opt,name=company,proto3" json:"company,omitempty"`
	Make    string `protobuf:"bytes,3,opt,name=make,proto3" json:"make,omitempty"`
	Model   string `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *FMCSAInspectionVehicleV1) Reset() {
	*x = FMCSAInspectionVehicleV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSAInspectionVehicleV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSAInspectionVehicleV1) ProtoMessage() {}

func (x *FMCSAInspectionVehicleV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSAInspectionVehicleV1.ProtoReflect.Descriptor instead.
func (*FMCSAInspectionVehicleV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{60}
}

func (x *FMCSAInspectionVehicleV1) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *FMCSAInspectionVehicleV1) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *FMCSAInspectionVehicleV1) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *FMCSAInspectionVehicleV1) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

type FMCSACensusInfoRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64 `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
}

func (x *FMCSACensusInfoRequestV1) Reset() {
	*x = FMCSACensusInfoRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSACensusInfoRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSACensusInfoRequestV1) ProtoMessage() {}

func (x *FMCSACensusInfoRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSACensusInfoRequestV1.ProtoReflect.Descriptor instead.
func (*FMCSACensusInfoRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{61}
}

func (x *FMCSACensusInfoRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

type FMCSACensusInfoV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsActive                  bool                   `protobuf:"varint,1,opt,name=isActive,proto3" json:"isActive,omitempty"`
	DateStr                   string                 `protobuf:"bytes,2,opt,name=dateStr,proto3" json:"dateStr,omitempty"`
	Date                      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	Name                      *string                `protobuf:"bytes,4,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Dba                       *string                `protobuf:"bytes,5,opt,name=dba,proto3,oneof" json:"dba,omitempty"`
	TotalMileage              *int64                 `protobuf:"varint,6,opt,name=totalMileage,proto3,oneof" json:"totalMileage,omitempty"`
	Mcs150MileageYear         *int64                 `protobuf:"varint,7,opt,name=mcs150MileageYear,proto3,oneof" json:"mcs150MileageYear,omitempty"`
	TotalTrucks               *int64                 `protobuf:"varint,8,opt,name=totalTrucks,proto3,oneof" json:"totalTrucks,omitempty"`
	TotalPowerUnits           *int64                 `protobuf:"varint,9,opt,name=totalPowerUnits,proto3,oneof" json:"totalPowerUnits,omitempty"`
	TotalDrivers              *int64                 `protobuf:"varint,10,opt,name=totalDrivers,proto3,oneof" json:"totalDrivers,omitempty"`
	TotalCdlDrivers           *int64                 `protobuf:"varint,11,opt,name=totalCdlDrivers,proto3,oneof" json:"totalCdlDrivers,omitempty"`
	CarrierCargoTypesStr      []string               `protobuf:"bytes,12,rep,name=carrierCargoTypesStr,proto3" json:"carrierCargoTypesStr,omitempty"`
	CarrierCargoTypes         []FMCSACargoTypeV1     `protobuf:"varint,13,rep,packed,name=carrierCargoTypes,proto3,enum=data_fetching.FMCSACargoTypeV1" json:"carrierCargoTypes,omitempty"`
	CarriesHazmat             bool                   `protobuf:"varint,14,opt,name=carriesHazmat,proto3" json:"carriesHazmat,omitempty"`
	PhysicalAddressNation     *string                `protobuf:"bytes,15,opt,name=physicalAddressNation,proto3,oneof" json:"physicalAddressNation,omitempty"`
	PhysicalAddressState      *string                `protobuf:"bytes,16,opt,name=physicalAddressState,proto3,oneof" json:"physicalAddressState,omitempty"`
	PhysicalAddressCity       *string                `protobuf:"bytes,17,opt,name=physicalAddressCity,proto3,oneof" json:"physicalAddressCity,omitempty"`
	PhysicalAddressCountyCode *string                `protobuf:"bytes,18,opt,name=physicalAddressCountyCode,proto3,oneof" json:"physicalAddressCountyCode,omitempty"`
	PhysicalAddressStreet     *string                `protobuf:"bytes,19,opt,name=physicalAddressStreet,proto3,oneof" json:"physicalAddressStreet,omitempty"`
	PhysicalAddressZipCode    *string                `protobuf:"bytes,20,opt,name=physicalAddressZipCode,proto3,oneof" json:"physicalAddressZipCode,omitempty"`
	Rating                    *string                `protobuf:"bytes,21,opt,name=rating,proto3,oneof" json:"rating,omitempty"`
	RatingDateStr             *string                `protobuf:"bytes,22,opt,name=ratingDateStr,proto3,oneof" json:"ratingDateStr,omitempty"`
	RatingDate                *timestamppb.Timestamp `protobuf:"bytes,23,opt,name=ratingDate,proto3" json:"ratingDate,omitempty"`
	Telephone                 *string                `protobuf:"bytes,24,opt,name=telephone,proto3,oneof" json:"telephone,omitempty"`
	Cellphone                 *string                `protobuf:"bytes,25,opt,name=cellphone,proto3,oneof" json:"cellphone,omitempty"`
	Fax                       *string                `protobuf:"bytes,26,opt,name=fax,proto3,oneof" json:"fax,omitempty"`
	Email                     *string                `protobuf:"bytes,27,opt,name=email,proto3,oneof" json:"email,omitempty"`
}

func (x *FMCSACensusInfoV1) Reset() {
	*x = FMCSACensusInfoV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FMCSACensusInfoV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FMCSACensusInfoV1) ProtoMessage() {}

func (x *FMCSACensusInfoV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FMCSACensusInfoV1.ProtoReflect.Descriptor instead.
func (*FMCSACensusInfoV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{62}
}

func (x *FMCSACensusInfoV1) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *FMCSACensusInfoV1) GetDateStr() string {
	if x != nil {
		return x.DateStr
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *FMCSACensusInfoV1) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetDba() string {
	if x != nil && x.Dba != nil {
		return *x.Dba
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetTotalMileage() int64 {
	if x != nil && x.TotalMileage != nil {
		return *x.TotalMileage
	}
	return 0
}

func (x *FMCSACensusInfoV1) GetMcs150MileageYear() int64 {
	if x != nil && x.Mcs150MileageYear != nil {
		return *x.Mcs150MileageYear
	}
	return 0
}

func (x *FMCSACensusInfoV1) GetTotalTrucks() int64 {
	if x != nil && x.TotalTrucks != nil {
		return *x.TotalTrucks
	}
	return 0
}

func (x *FMCSACensusInfoV1) GetTotalPowerUnits() int64 {
	if x != nil && x.TotalPowerUnits != nil {
		return *x.TotalPowerUnits
	}
	return 0
}

func (x *FMCSACensusInfoV1) GetTotalDrivers() int64 {
	if x != nil && x.TotalDrivers != nil {
		return *x.TotalDrivers
	}
	return 0
}

func (x *FMCSACensusInfoV1) GetTotalCdlDrivers() int64 {
	if x != nil && x.TotalCdlDrivers != nil {
		return *x.TotalCdlDrivers
	}
	return 0
}

func (x *FMCSACensusInfoV1) GetCarrierCargoTypesStr() []string {
	if x != nil {
		return x.CarrierCargoTypesStr
	}
	return nil
}

func (x *FMCSACensusInfoV1) GetCarrierCargoTypes() []FMCSACargoTypeV1 {
	if x != nil {
		return x.CarrierCargoTypes
	}
	return nil
}

func (x *FMCSACensusInfoV1) GetCarriesHazmat() bool {
	if x != nil {
		return x.CarriesHazmat
	}
	return false
}

func (x *FMCSACensusInfoV1) GetPhysicalAddressNation() string {
	if x != nil && x.PhysicalAddressNation != nil {
		return *x.PhysicalAddressNation
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetPhysicalAddressState() string {
	if x != nil && x.PhysicalAddressState != nil {
		return *x.PhysicalAddressState
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetPhysicalAddressCity() string {
	if x != nil && x.PhysicalAddressCity != nil {
		return *x.PhysicalAddressCity
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetPhysicalAddressCountyCode() string {
	if x != nil && x.PhysicalAddressCountyCode != nil {
		return *x.PhysicalAddressCountyCode
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetPhysicalAddressStreet() string {
	if x != nil && x.PhysicalAddressStreet != nil {
		return *x.PhysicalAddressStreet
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetPhysicalAddressZipCode() string {
	if x != nil && x.PhysicalAddressZipCode != nil {
		return *x.PhysicalAddressZipCode
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetRating() string {
	if x != nil && x.Rating != nil {
		return *x.Rating
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetRatingDateStr() string {
	if x != nil && x.RatingDateStr != nil {
		return *x.RatingDateStr
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetRatingDate() *timestamppb.Timestamp {
	if x != nil {
		return x.RatingDate
	}
	return nil
}

func (x *FMCSACensusInfoV1) GetTelephone() string {
	if x != nil && x.Telephone != nil {
		return *x.Telephone
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetCellphone() string {
	if x != nil && x.Cellphone != nil {
		return *x.Cellphone
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetFax() string {
	if x != nil && x.Fax != nil {
		return *x.Fax
	}
	return ""
}

func (x *FMCSACensusInfoV1) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

type LatestObjectiveGradeRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64 `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
}

func (x *LatestObjectiveGradeRequestV1) Reset() {
	*x = LatestObjectiveGradeRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LatestObjectiveGradeRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LatestObjectiveGradeRequestV1) ProtoMessage() {}

func (x *LatestObjectiveGradeRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LatestObjectiveGradeRequestV1.ProtoReflect.Descriptor instead.
func (*LatestObjectiveGradeRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{63}
}

func (x *LatestObjectiveGradeRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

type ObjectiveGradeV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64                  `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	Date      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	Score     ObjectiveGradeScoreV1  `protobuf:"varint,3,opt,name=score,proto3,enum=data_fetching.ObjectiveGradeScoreV1" json:"score,omitempty"`
}

func (x *ObjectiveGradeV1) Reset() {
	*x = ObjectiveGradeV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectiveGradeV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectiveGradeV1) ProtoMessage() {}

func (x *ObjectiveGradeV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectiveGradeV1.ProtoReflect.Descriptor instead.
func (*ObjectiveGradeV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{64}
}

func (x *ObjectiveGradeV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *ObjectiveGradeV1) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ObjectiveGradeV1) GetScore() ObjectiveGradeScoreV1 {
	if x != nil {
		return x.Score
	}
	return ObjectiveGradeScoreV1_Undefined
}

type InvoluntaryRevocationsRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64                  `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	StartDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=startDate,proto3" json:"startDate,omitempty"`
	EndDate   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=endDate,proto3" json:"endDate,omitempty"`
}

func (x *InvoluntaryRevocationsRequestV1) Reset() {
	*x = InvoluntaryRevocationsRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvoluntaryRevocationsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvoluntaryRevocationsRequestV1) ProtoMessage() {}

func (x *InvoluntaryRevocationsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvoluntaryRevocationsRequestV1.ProtoReflect.Descriptor instead.
func (*InvoluntaryRevocationsRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{65}
}

func (x *InvoluntaryRevocationsRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *InvoluntaryRevocationsRequestV1) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *InvoluntaryRevocationsRequestV1) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type InvoluntaryRevocationsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*AuthorityHistoryRecordV1 `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *InvoluntaryRevocationsV1) Reset() {
	*x = InvoluntaryRevocationsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvoluntaryRevocationsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvoluntaryRevocationsV1) ProtoMessage() {}

func (x *InvoluntaryRevocationsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvoluntaryRevocationsV1.ProtoReflect.Descriptor instead.
func (*InvoluntaryRevocationsV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{66}
}

func (x *InvoluntaryRevocationsV1) GetRecords() []*AuthorityHistoryRecordV1 {
	if x != nil {
		return x.Records
	}
	return nil
}

type GetNirvanaPoliciesRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber        int64                  `protobuf:"varint,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	EffectiveDate    *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
	Version          int64                  `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	MainCoverageType MainCoverageTypeV1     `protobuf:"varint,4,opt,name=mainCoverageType,proto3,enum=data_fetching.MainCoverageTypeV1" json:"mainCoverageType,omitempty"`
}

func (x *GetNirvanaPoliciesRequestV1) Reset() {
	*x = GetNirvanaPoliciesRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNirvanaPoliciesRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNirvanaPoliciesRequestV1) ProtoMessage() {}

func (x *GetNirvanaPoliciesRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNirvanaPoliciesRequestV1.ProtoReflect.Descriptor instead.
func (*GetNirvanaPoliciesRequestV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{67}
}

func (x *GetNirvanaPoliciesRequestV1) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *GetNirvanaPoliciesRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

func (x *GetNirvanaPoliciesRequestV1) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *GetNirvanaPoliciesRequestV1) GetMainCoverageType() MainCoverageTypeV1 {
	if x != nil {
		return x.MainCoverageType
	}
	return MainCoverageTypeV1_MainCoverageTypeV1_Unspecified
}

type NirvanaPolicyV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	PolicyNumber     string                 `protobuf:"bytes,2,opt,name=policyNumber,proto3" json:"policyNumber,omitempty"`
	Version          int32                  `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	InsuredName      string                 `protobuf:"bytes,4,opt,name=insuredName,proto3" json:"insuredName,omitempty"`
	State            string                 `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	ApplicationId    string                 `protobuf:"bytes,6,opt,name=applicationId,proto3" json:"applicationId,omitempty"`
	SubmissionId     string                 `protobuf:"bytes,7,opt,name=submissionId,proto3" json:"submissionId,omitempty"`
	ProgramType      string                 `protobuf:"bytes,8,opt,name=programType,proto3" json:"programType,omitempty"`
	EffectiveDate    *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
	EffectiveDateTo  *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=effectiveDateTo,proto3" json:"effectiveDateTo,omitempty"`
	AgencyId         string                 `protobuf:"bytes,11,opt,name=agencyId,proto3" json:"agencyId,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	InsuranceCarrier string                 `protobuf:"bytes,14,opt,name=insuranceCarrier,proto3" json:"insuranceCarrier,omitempty"`
	IsNonAdmitted    bool                   `protobuf:"varint,15,opt,name=isNonAdmitted,proto3" json:"isNonAdmitted,omitempty"`
}

func (x *NirvanaPolicyV1) Reset() {
	*x = NirvanaPolicyV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaPolicyV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaPolicyV1) ProtoMessage() {}

func (x *NirvanaPolicyV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaPolicyV1.ProtoReflect.Descriptor instead.
func (*NirvanaPolicyV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{68}
}

func (x *NirvanaPolicyV1) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *NirvanaPolicyV1) GetPolicyNumber() string {
	if x != nil {
		return x.PolicyNumber
	}
	return ""
}

func (x *NirvanaPolicyV1) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *NirvanaPolicyV1) GetInsuredName() string {
	if x != nil {
		return x.InsuredName
	}
	return ""
}

func (x *NirvanaPolicyV1) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *NirvanaPolicyV1) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *NirvanaPolicyV1) GetSubmissionId() string {
	if x != nil {
		return x.SubmissionId
	}
	return ""
}

func (x *NirvanaPolicyV1) GetProgramType() string {
	if x != nil {
		return x.ProgramType
	}
	return ""
}

func (x *NirvanaPolicyV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

func (x *NirvanaPolicyV1) GetEffectiveDateTo() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDateTo
	}
	return nil
}

func (x *NirvanaPolicyV1) GetAgencyId() string {
	if x != nil {
		return x.AgencyId
	}
	return ""
}

func (x *NirvanaPolicyV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *NirvanaPolicyV1) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *NirvanaPolicyV1) GetInsuranceCarrier() string {
	if x != nil {
		return x.InsuranceCarrier
	}
	return ""
}

func (x *NirvanaPolicyV1) GetIsNonAdmitted() bool {
	if x != nil {
		return x.IsNonAdmitted
	}
	return false
}

type GetNirvanaPoliciesResponseV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Policies []*NirvanaPolicyV1 `protobuf:"bytes,1,rep,name=policies,proto3" json:"policies,omitempty"`
}

func (x *GetNirvanaPoliciesResponseV1) Reset() {
	*x = GetNirvanaPoliciesResponseV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_fetching_api_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNirvanaPoliciesResponseV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNirvanaPoliciesResponseV1) ProtoMessage() {}

func (x *GetNirvanaPoliciesResponseV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_fetching_api_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNirvanaPoliciesResponseV1.ProtoReflect.Descriptor instead.
func (*GetNirvanaPoliciesResponseV1) Descriptor() ([]byte, []int) {
	return file_data_fetching_api_proto_rawDescGZIP(), []int{69}
}

func (x *GetNirvanaPoliciesResponseV1) GetPolicies() []*NirvanaPolicyV1 {
	if x != nil {
		return x.Policies
	}
	return nil
}

var File_data_fetching_api_proto protoreflect.FileDescriptor

var file_data_fetching_api_proto_rawDesc = []byte{
	0x0a, 0x17, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x09, 0x0a, 0x0b, 0x4d, 0x56,
	0x52, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x32, 0x0a, 0x14, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x74, 0x72,
	0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x6d, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a,
	0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x0a, 0x12, 0x76, 0x69, 0x6f, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x12, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x76, 0x72, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x76, 0x72, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5a, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x5a, 0x69, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x46, 0x6c, 0x61, 0x67,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x73, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x73, 0x73, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x70, 0x70, 0x61, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x70, 0x70, 0x61, 0x46, 0x6c, 0x61,
	0x67, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x6d, 0x76, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6d, 0x76,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a,
	0x03, 0x64, 0x6f, 0x62, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x16, 0x0a, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x79, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x79, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x68, 0x61, 0x69, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x68, 0x61, 0x69, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x69, 0x63, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x69, 0x63, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x69,
	0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x49,
	0x73, 0x73, 0x75, 0x65, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x49, 0x73, 0x73,
	0x75, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x73, 0x12, 0x40, 0x0a, 0x0d, 0x6d, 0x76, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6d, 0x76, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x72,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x73, 0x63, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x73,
	0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3d, 0x0a, 0x0a, 0x76, 0x69, 0x6f, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x56, 0x52, 0x56,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x52, 0x0a, 0x76, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x44, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x82, 0x03, 0x0a, 0x0e, 0x4d, 0x56, 0x52, 0x56, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x6f, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a,
	0x0d, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0d, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x42, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x12, 0x34, 0x0a, 0x15, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12,
	0x28, 0x0a, 0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xc6, 0x02, 0x0a, 0x12, 0x4d, 0x56,
	0x52, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x03, 0x64, 0x6f, 0x62, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x44, 0x12, 0x2e, 0x0a, 0x12, 0x6f, 0x6e, 0x6c, 0x79, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46,
	0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x6f, 0x6e, 0x6c, 0x79, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x22, 0x29, 0x0a, 0x11, 0x4d, 0x56, 0x52, 0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x9c, 0x02,
	0x0a, 0x18, 0x4d, 0x56, 0x52, 0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x6c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x2c, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x1c,
	0x0a, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x64,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69,
	0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x6c,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0x77, 0x0a, 0x10,
	0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x56, 0x31,
	0x12, 0x49, 0x0a, 0x0f, 0x6e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x52, 0x0f, 0x6e, 0x68, 0x74, 0x73,
	0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x72, 0x72, 0x54, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x72,
	0x72, 0x54, 0x65, 0x78, 0x74, 0x22, 0xac, 0x03, 0x0a, 0x0c, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x56, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x59, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x59, 0x65, 0x61, 0x72, 0x12, 0x22,
	0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x72, 0x69, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x72, 0x69, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x61, 0x77, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x72, 0x61, 0x77, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x62, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x62, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x3e, 0x0a, 0x0b,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x52,
	0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x0b,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x52,
	0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x46, 0x0a, 0x0b,
	0x64, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x56, 0x31, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x22, 0x27, 0x0a, 0x13, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x22, 0x5c, 0x0a,
	0x1e, 0x42, 0x49, 0x50, 0x44, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x12,
	0x3a, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x56, 0x31, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x25,
	0x42, 0x49, 0x50, 0x44, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x1b, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x49, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x45, 0x78, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64,
	0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x45, 0x78, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x22, 0x8e, 0x03, 0x0a, 0x1b, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x2c, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03,
	0x64, 0x6f, 0x62, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x27, 0x0a, 0x0c, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x53, 0x53, 0x4e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0c, 0x65,
	0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x53, 0x53, 0x4e, 0x88, 0x01, 0x01, 0x12, 0x25,
	0x0a, 0x0b, 0x73, 0x73, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x73, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f,
	0x75, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x65, 0x64, 0x53, 0x53, 0x4e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x73, 0x6e, 0x4c, 0x61,
	0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x22, 0x5f, 0x0a, 0x09, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x56, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x7a, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x7a, 0x69, 0x70, 0x22, 0xf9, 0x01, 0x0a, 0x14, 0x4e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x31,
	0x12, 0x38, 0x0a, 0x09, 0x4e, 0x63, 0x66, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x63, 0x66, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x31, 0x52,
	0x09, 0x4e, 0x63, 0x66, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x55, 0x0a, 0x12, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x78, 0x56, 0x31, 0x52, 0x12, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x50, 0x0a, 0x15, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x15, 0x44, 0x61,
	0x74, 0x65, 0x4f, 0x66, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x75, 0x6e, 0x22, 0x62, 0x0a, 0x06, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x31, 0x12, 0x14, 0x0a,
	0x05, 0x66, 0x69, 0x72, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c,
	0x61, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x22, 0x1b, 0x0a, 0x05, 0x44, 0x6f, 0x62, 0x56, 0x31,
	0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x79, 0x65, 0x61, 0x72, 0x22, 0xd2, 0x02, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x56, 0x31, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x31, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x73, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x73, 0x73, 0x6e, 0x12, 0x26, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x6f, 0x62, 0x56, 0x31, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12,
	0x1e, 0x0a, 0x0a, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x46, 0x65, 0x65, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x46, 0x65, 0x65, 0x74, 0x12,
	0x22, 0x0a, 0x0c, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x49, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x49, 0x6e, 0x63,
	0x68, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x68, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x40, 0x0a, 0x14, 0x44, 0x61, 0x74,
	0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56,
	0x31, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x22, 0x9b, 0x02, 0x0a, 0x10,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31,
	0x12, 0x22, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x69, 0x70, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x69,
	0x70, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x69, 0x70, 0x34, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x69, 0x70, 0x34, 0x12, 0x53, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x12, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72,
	0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x0d, 0x53, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x12, 0x32, 0x0a, 0x07, 0x73,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x56, 0x31, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12,
	0x47, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x54, 0x0a, 0x16, 0x44, 0x61, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x73, 0x74, 0x62, 0x65, 0x64,
	0x56, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03,
	0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x61, 0x79, 0x22, 0x5c,
	0x0a, 0x16, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x74, 0x0a, 0x17,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x56, 0x31, 0x12, 0x59, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x31, 0x52, 0x14, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x74, 0x0a, 0x17, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x56, 0x31, 0x12, 0x59, 0x0a,
	0x14, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x56, 0x31, 0x52, 0x14, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xcd, 0x0b, 0x0a, 0x15, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x56, 0x31, 0x12, 0x59, 0x0a, 0x14, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x45, 0x73, 0x74, 0x62, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x45,
	0x73, 0x74, 0x62, 0x65, 0x64, 0x56, 0x31, 0x52, 0x14, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x73, 0x74, 0x62, 0x65, 0x64, 0x12, 0x5f, 0x0a,
	0x18, 0x6f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61,
	0x74, 0x65, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x56, 0x31, 0x52, 0x18, 0x6f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x12, 0x63,
	0x0a, 0x1a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x1a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x12, 0x5f, 0x0a, 0x18, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x18, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x66, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x12, 0x40, 0x0a, 0x1b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e,
	0x63, 0x6c, 0x64, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x6e, 0x63, 0x6c, 0x64, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3a, 0x0a, 0x18, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x77, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x77, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x19, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x67, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x19, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x67, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x38, 0x0a, 0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f,
	0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x15, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x56,
	0x31, 0x52, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x5c, 0x0a, 0x15, 0x68, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x56, 0x31, 0x52,
	0x15, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x18, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69,
	0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69,
	0x6e, 0x67, 0x12, 0x30, 0x0a, 0x13, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x4f, 0x70, 0x65, 0x6e, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x13, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x15, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x18, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x76,
	0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x76,
	0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x3a, 0x0a, 0x18, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x6e, 0x64,
	0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x6e, 0x64,
	0x65, 0x64, 0x12, 0x3e, 0x0a, 0x1a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x77, 0x65, 0x64,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x77,
	0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x34, 0x0a, 0x15, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x15, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52,
	0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a, 0x15, 0x70, 0x61, 0x73, 0x74,
	0x44, 0x75, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x38,
	0x0a, 0x17, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x17, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x19, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x19, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x40, 0x0a, 0x1b, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x4f, 0x66, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1b, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4f, 0x66, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x10, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x12, 0x22, 0x0a,
	0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73,
	0x63, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x5c, 0x0a, 0x11, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x56, 0x31, 0x12, 0x47, 0x0a, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x52,
	0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x9b, 0x05, 0x0a, 0x12, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x12, 0x47, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56,
	0x31, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12,
	0x47, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x1b, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x12, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4f, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x4f, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x63,
	0x6f, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x63,
	0x6f, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x53, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66,
	0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x12, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c,
	0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52,
	0x0d, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72,
	0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x0a, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x64, 0x0a,
	0x13, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x56, 0x31, 0x12, 0x4d, 0x0a, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56,
	0x31, 0x52, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x22, 0x6b, 0x0a, 0x17, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x12, 0x50,
	0x0a, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x31, 0x52, 0x11, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x22, 0x39, 0x0a, 0x09, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x56, 0x31, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x44, 0x0a, 0x0e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x31, 0x12, 0x32, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x56, 0x31, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0xdb, 0x09, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31,
	0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x74, 0x61, 0x70, 0x65, 0x53, 0x75,
	0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x74, 0x61, 0x70, 0x65, 0x53, 0x75, 0x70, 0x70, 0x6c,
	0x69, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x47, 0x0a, 0x0c,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x51, 0x0a, 0x11, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x11, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x68, 0x69, 0x67, 0x68,
	0x65, 0x73, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x68, 0x69, 0x67, 0x68, 0x65, 0x73, 0x74, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x70, 0x61, 0x73, 0x74, 0x44,
	0x75, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x68,
	0x69, 0x72, 0x74, 0x79, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x68, 0x69, 0x72, 0x74, 0x79, 0x64, 0x61, 0x79, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x69, 0x78, 0x74, 0x79, 0x64,
	0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x73, 0x69, 0x78, 0x74, 0x79, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72,
	0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x69, 0x6e, 0x65, 0x74, 0x79, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x69, 0x6e, 0x65,
	0x74, 0x79, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d,
	0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x31, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x31, 0x12, 0x49, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x31, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x0d,
	0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x31, 0x12, 0x24, 0x0a,
	0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x32, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x32, 0x12, 0x49, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x32, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52,
	0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x32, 0x12, 0x24,
	0x0a, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x33, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x33, 0x12, 0x49, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x33, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31,
	0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x33, 0x12,
	0x53, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31,
	0x52, 0x12, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x31, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x22,
	0x84, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x31, 0x12,
	0x65, 0x0a, 0x18, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x52, 0x18, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x7e, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x64, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x12, 0x68, 0x0a, 0x19,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x31, 0x52, 0x19, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x4d, 0x0a, 0x0f, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66,
	0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x56, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x03, 0x64, 0x61, 0x79, 0x22, 0xa8, 0x01, 0x0a, 0x1c, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72,
	0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x12, 0x44, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66,
	0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x56, 0x31, 0x52, 0x0d, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x0c,
	0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x8c, 0x01, 0x0a, 0x1d, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x56, 0x31, 0x12, 0x6b, 0x0a, 0x1a, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x56, 0x31, 0x52, 0x1a, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22,
	0x88, 0x01, 0x0a, 0x16, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x31, 0x12, 0x6e, 0x0a, 0x1b, 0x69, 0x6e,
	0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x31, 0x52, 0x1b, 0x69,
	0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x80, 0x04, 0x0a, 0x12, 0x4e,
	0x63, 0x66, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56,
	0x31, 0x12, 0x3e, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x56, 0x31, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x56, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x56, 0x31, 0x52, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x4a, 0x0a, 0x0f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x56, 0x31, 0x52, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x5c, 0x0a, 0x15, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x52, 0x15, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x64, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31,
	0x52, 0x10, 0x74, 0x72, 0x61, 0x64, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x59, 0x0a, 0x14, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x31, 0x52, 0x14, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x5c, 0x0a,
	0x0b, 0x4e, 0x63, 0x66, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x31, 0x12, 0x4d, 0x0a, 0x10,
	0x6e, 0x63, 0x66, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x63, 0x66, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x31, 0x52, 0x10, 0x6e, 0x63, 0x66, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x89, 0x05, 0x0a, 0x18,
	0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x6b,
	0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x18, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x18, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x25, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x17, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x17, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x55, 0x0a, 0x15, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x02, 0x52,
	0x15, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x09, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x18, 0x0a,
	0x16, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x40, 0x0a, 0x20, 0x47, 0x72, 0x61, 0x6e, 0x74,
	0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x64,
	0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x5e, 0x0a, 0x19, 0x47, 0x72, 0x61,
	0x6e, 0x74, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x12, 0x41, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31,
	0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x44, 0x0a, 0x16, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45,
	0x78, 0x56, 0x31, 0x12, 0x2a, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x8a, 0x01, 0x0a, 0x1d, 0x42, 0x49, 0x50, 0x44, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x3d, 0x0a, 0x09, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00,
	0x52, 0x09, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x8a, 0x09, 0x0a,
	0x11, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x29, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x14, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52,
	0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x3f, 0x0a, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x48, 0x02, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x2d, 0x0a, 0x0f, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x6c, 0x79, 0x69, 0x6e, 0x67, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x48, 0x03, 0x52, 0x0f, 0x75, 0x6e,
	0x64, 0x65, 0x72, 0x6c, 0x79, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x31, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x48, 0x04, 0x52, 0x11, 0x6d,
	0x61, 0x78, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x05, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x13, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x06, 0x52, 0x13, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x45, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x08, 0x62, 0x69, 0x70, 0x64, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x08, 0x62, 0x69, 0x70, 0x64, 0x46,
	0x6c, 0x61, 0x67, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x11, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x08, 0x52, 0x11, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x62, 0x69, 0x70,
	0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x09,
	0x62, 0x69, 0x70, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x16,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x16,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x13, 0x63, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x56, 0x31, 0x48, 0x0b, 0x52, 0x13, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x88, 0x01, 0x01, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x6c, 0x79, 0x69, 0x6e, 0x67,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x62, 0x69, 0x70, 0x64, 0x46, 0x6c,
	0x61, 0x67, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x62, 0x69, 0x70,
	0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x42, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x0e, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x66, 0x6f, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a,
	0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x66,
	0x6f, 0x72, 0x6d, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0x54, 0x0a, 0x16, 0x42, 0x49, 0x50, 0x44, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x31,
	0x12, 0x3a, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x56, 0x31, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x42, 0x0a, 0x22,
	0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72,
	0x73, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0x4d, 0x0a, 0x1b, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x54, 0x69, 0x65, 0x72, 0x73, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x56, 0x31, 0x12,
	0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22,
	0x9c, 0x06, 0x0a, 0x12, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x17, 0x6c, 0x61, 0x72, 0x67,
	0x65, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x6c, 0x61, 0x72, 0x67, 0x65,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x0e, 0x63, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x72,
	0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x23, 0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x4d,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x0c, 0x61, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a,
	0x1a, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64,
	0x47, 0x72, 0x6f, 0x73, 0x73, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x03, 0x52, 0x1a, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6d, 0x62,
	0x69, 0x6e, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x43, 0x0a, 0x1a, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x61, 0x74, 0x69, 0x6f,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x48, 0x04, 0x52, 0x1a, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x14, 0x75, 0x6e, 0x73, 0x61, 0x66,
	0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x01, 0x48, 0x05, 0x52, 0x14, 0x75, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x56,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x88, 0x01, 0x01,
	0x12, 0x3b, 0x0a, 0x16, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01,
	0x48, 0x06, 0x52, 0x16, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a,
	0x12, 0x75, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x48, 0x07, 0x52, 0x12, 0x75, 0x6e, 0x73,
	0x61, 0x66, 0x65, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x55,
	0x6e, 0x69, 0x74, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x4d, 0x69, 0x6c, 0x65, 0x73, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x75, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x56, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x42, 0x19, 0x0a, 0x17,
	0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x75, 0x6e, 0x73, 0x61,
	0x66, 0x65, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x7d,
	0x0a, 0x25, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x08, 0x64, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x64, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x22, 0x5d, 0x0a,
	0x1e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x56, 0x31, 0x12,
	0x3b, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x56, 0x31, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xad, 0x01, 0x0a,
	0x19, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64,
	0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x08, 0x64, 0x75, 0x6d, 0x70,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x64, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x3a, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x80, 0x01, 0x0a,
	0x1e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12,
	0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x40, 0x0a,
	0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22,
	0x5a, 0x0a, 0x17, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x31, 0x12, 0x3f, 0x0a, 0x07, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x56, 0x31, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xcd, 0x05, 0x0a, 0x16,
	0x46, 0x4d, 0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0d,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x42, 0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41,
	0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x56, 0x31, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x22, 0x0a,
	0x0c, 0x6f, 0x6f, 0x73, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f, 0x6f, 0x73, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x6f, 0x73, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6f, 0x6f, 0x73, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x26, 0x0a, 0x0e, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74,
	0x79, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x65, 0x76, 0x65, 0x72,
	0x69, 0x74, 0x79, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x69, 0x6d,
	0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74,
	0x69, 0x6d, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x25, 0x0a, 0x0b, 0x76, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00,
	0x52, 0x0b, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x23, 0x0a, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02,
	0x52, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x76, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x1f,
	0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12,
	0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x40, 0x0a,
	0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22,
	0x5c, 0x0a, 0x18, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x31, 0x12, 0x40, 0x0a, 0x07, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43,
	0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x56, 0x31, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xe7, 0x0e,
	0x0a, 0x17, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x77,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x44, 0x12,
	0x40, 0x0a, 0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x69, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x69,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a,
	0x0f, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56, 0x31, 0x52, 0x0f, 0x69,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e,
	0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x4f, 0x53, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x4f,
	0x53, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x4f, 0x4f, 0x53, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4f, 0x4f, 0x53, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x2e, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x4f, 0x53, 0x56, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x4f, 0x4f, 0x53, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x26, 0x0a, 0x0e, 0x68, 0x61, 0x7a, 0x6d, 0x61, 0x74, 0x4f, 0x4f, 0x53, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x68, 0x61, 0x7a, 0x6d, 0x61, 0x74,
	0x4f, 0x4f, 0x53, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x32, 0x0a, 0x14, 0x68, 0x61, 0x7a, 0x6d,
	0x61, 0x74, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x65, 0x6e, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x68, 0x61, 0x7a, 0x6d, 0x61, 0x74, 0x56, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10,
	0x68, 0x61, 0x7a, 0x6d, 0x61, 0x74, 0x50, 0x6c, 0x61, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x68, 0x61, 0x7a, 0x6d, 0x61, 0x74, 0x50, 0x6c,
	0x61, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x65, 0x0a, 0x0f, 0x62, 0x61, 0x73, 0x69,
	0x63, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x62, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12,
	0x65, 0x0a, 0x0f, 0x62, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31,
	0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x62, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42,
	0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x6f, 0x6c, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x6f, 0x6c, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x49, 0x4e, 0x73, 0x18, 0x13,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x49, 0x4e, 0x73,
	0x12, 0x1b, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2d,
	0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a,
	0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x03, 0x52, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x73, 0x68, 0x69, 0x70, 0x70, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0b, 0x73, 0x68, 0x69, 0x70, 0x70,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x18, 0x77, 0x61, 0x73,
	0x53, 0x69, 0x7a, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x18, 0x77,
	0x61, 0x73, 0x53, 0x69, 0x7a, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x45, 0x6e, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x15, 0x77, 0x61,
	0x73, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x15, 0x77, 0x61, 0x73,
	0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x13, 0x77, 0x61, 0x73, 0x4c, 0x6f, 0x63, 0x61,
	0x6c, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x07, 0x52, 0x13, 0x77, 0x61, 0x73, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x45, 0x6e,
	0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0f,
	0x77, 0x61, 0x73, 0x50, 0x6f, 0x73, 0x74, 0x41, 0x63, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x08, 0x48, 0x08, 0x52, 0x0f, 0x77, 0x61, 0x73, 0x50, 0x6f, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a, 0x1d, 0x63,
	0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x09, 0x52, 0x1d, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x57, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x1e,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x76, 0x69, 0x6e, 0x73, 0x12, 0x43, 0x0a, 0x08, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43,
	0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x56, 0x31, 0x52, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x1a,
	0x42, 0x0a, 0x14, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x42, 0x61, 0x73, 0x69, 0x63, 0x56, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x77, 0x61, 0x73, 0x53, 0x69, 0x7a, 0x65, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x18, 0x0a, 0x16, 0x5f, 0x77, 0x61, 0x73, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x45, 0x6e,
	0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x77, 0x61,
	0x73, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x77, 0x61, 0x73, 0x50, 0x6f, 0x73, 0x74, 0x41, 0x63, 0x63,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x42, 0x20, 0x0a, 0x1e, 0x5f, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x73,
	0x73, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x70, 0x0a, 0x18, 0x46, 0x4d, 0x43, 0x53, 0x41,
	0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x56, 0x31, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d,
	0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x38, 0x0a, 0x18, 0x46, 0x4d, 0x43,
	0x53, 0x41, 0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0xa8, 0x0c, 0x0a, 0x11, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x65, 0x6e,
	0x73, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x12,
	0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x64, 0x62, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x03, 0x64, 0x62, 0x61, 0x88, 0x01, 0x01, 0x12,
	0x27, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4d, 0x69,
	0x6c, 0x65, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x11, 0x6d, 0x63, 0x73, 0x31,
	0x35, 0x30, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x11, 0x6d, 0x63, 0x73, 0x31, 0x35, 0x30, 0x4d, 0x69, 0x6c,
	0x65, 0x61, 0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x04, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x6f, 0x77, 0x65, 0x72,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x48, 0x05, 0x52, 0x0f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x27, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x06, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x64, 0x6c, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x07, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x64, 0x6c, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x14, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x73, 0x53, 0x74,
	0x72, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x73, 0x53, 0x74, 0x72, 0x12, 0x4d, 0x0a,
	0x11, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61,
	0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x52, 0x11, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x73, 0x48, 0x61, 0x7a, 0x6d, 0x61, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x73, 0x48, 0x61, 0x7a, 0x6d,
	0x61, 0x74, 0x12, 0x39, 0x0a, 0x15, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x08, 0x52, 0x15, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a,
	0x14, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x14, 0x70,
	0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x13, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63,
	0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74, 0x79, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x13, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a,
	0x19, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x0b, 0x52, 0x19, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x39, 0x0a, 0x15, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x72, 0x65, 0x65, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x0c, 0x52, 0x15, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x72, 0x65, 0x65, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x16, 0x70,
	0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5a, 0x69,
	0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0d, 0x52, 0x16, 0x70,
	0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5a, 0x69,
	0x70, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0e, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0d, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x44,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0f, 0x52, 0x0d,
	0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x3a, 0x0a, 0x0a, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x09,
	0x74, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x10, 0x52, 0x09, 0x74, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x21, 0x0a, 0x09, 0x63, 0x65, 0x6c, 0x6c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x11, 0x52, 0x09, 0x63, 0x65, 0x6c, 0x6c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x66, 0x61, 0x78, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x12, 0x52, 0x03, 0x66, 0x61, 0x78, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x13, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x06, 0x0a,
	0x04, 0x5f, 0x64, 0x62, 0x61, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4d,
	0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6d, 0x63, 0x73, 0x31, 0x35,
	0x30, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x73, 0x42, 0x12, 0x0a, 0x10,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x64, 0x6c, 0x44, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63,
	0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x17, 0x0a, 0x15, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x70, 0x68, 0x79,
	0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74, 0x79,
	0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x18,
	0x0a, 0x16, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x72, 0x65, 0x65, 0x74, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x70, 0x68, 0x79,
	0x73, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5a, 0x69, 0x70, 0x43,
	0x6f, 0x64, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x63, 0x65, 0x6c, 0x6c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x42, 0x06, 0x0a, 0x04,
	0x5f, 0x66, 0x61, 0x78, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x3d,
	0x0a, 0x1d, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12,
	0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x9c, 0x01,
	0x0a, 0x10, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65,
	0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x3a, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x56, 0x31, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xaf, 0x01, 0x0a,
	0x1f, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x75, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x52, 0x65, 0x76, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x38,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x5d,
	0x0a, 0x18, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x75, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x52, 0x65, 0x76,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x56, 0x31, 0x12, 0x41, 0x0a, 0x07, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x56, 0x31, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xe6, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x1c, 0x0a,
	0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0d, 0x65,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x10, 0x6d, 0x61, 0x69, 0x6e, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x56, 0x31, 0x52, 0x10, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xed, 0x04, 0x0a, 0x0f, 0x4e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75,
	0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0d,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x44,
	0x0a, 0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x54, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x64,
	0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x12, 0x24, 0x0a, 0x0d, 0x69, 0x73, 0x4e, 0x6f, 0x6e, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4e, 0x6f, 0x6e, 0x41, 0x64,
	0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x22, 0x5a, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x12, 0x3a, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x31, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69,
	0x65, 0x73, 0x2a, 0x80, 0x03, 0x0a, 0x10, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f, 0x30, 0x10, 0x00, 0x12,
	0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x56, 0x31, 0x5f, 0x31, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f, 0x32, 0x10, 0x02, 0x12,
	0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x56, 0x31, 0x5f, 0x33, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f, 0x34, 0x10, 0x04, 0x12,
	0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x56, 0x31, 0x5f, 0x35, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f, 0x36, 0x10, 0x06, 0x12,
	0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x56, 0x31, 0x5f, 0x37, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f, 0x38, 0x10, 0x08, 0x12,
	0x16, 0x0a, 0x12, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x56, 0x31, 0x5f, 0x39, 0x10, 0x09, 0x12, 0x17, 0x0a, 0x13, 0x4e, 0x68, 0x74, 0x73, 0x61,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f, 0x31, 0x30, 0x10, 0x0a,
	0x12, 0x17, 0x0a, 0x13, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x56, 0x31, 0x5f, 0x31, 0x31, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13, 0x4e, 0x68, 0x74,
	0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f, 0x31, 0x32,
	0x10, 0x0c, 0x12, 0x17, 0x0a, 0x13, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f, 0x31, 0x34, 0x10, 0x0d, 0x12, 0x18, 0x0a, 0x14, 0x4e,
	0x68, 0x74, 0x73, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x31, 0x5f,
	0x34, 0x30, 0x30, 0x10, 0x0e, 0x2a, 0xa3, 0x02, 0x0a, 0x0d, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4d, 0x6f, 0x74,
	0x6f, 0x72, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x54, 0x72, 0x61, 0x69, 0x6c,
	0x65, 0x72, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x10, 0x04, 0x12, 0x15, 0x0a,
	0x11, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4e,
	0x69, 0x6c, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x43,
	0x61, 0x72, 0x10, 0x06, 0x12, 0x2e, 0x0a, 0x2a, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x42, 0x75, 0x73, 0x10, 0x08, 0x2a, 0xeb, 0x02, 0x0a, 0x0d,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x12, 0x1d, 0x0a,
	0x19, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x55,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x41, 0x10,
	0x01, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x56, 0x31, 0x5f, 0x42, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x43, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x44, 0x10, 0x04,
	0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56,
	0x31, 0x5f, 0x45, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x46, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x47, 0x10, 0x07, 0x12,
	0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31,
	0x5f, 0x48, 0x10, 0x08, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x33, 0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x34, 0x10, 0x0a, 0x12, 0x13,
	0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f,
	0x35, 0x10, 0x0b, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x56, 0x31, 0x5f, 0x36, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x37, 0x10, 0x0d, 0x12, 0x13, 0x0a,
	0x0f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x31, 0x5f, 0x38,
	0x10, 0x0e, 0x12, 0x15, 0x0a, 0x11, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x56, 0x31, 0x5f, 0x4e, 0x69, 0x6c, 0x10, 0x0f, 0x2a, 0xb8, 0x03, 0x0a, 0x18, 0x46, 0x4d,
	0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x56,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x56, 0x31, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00,
	0x12, 0x2a, 0x0a, 0x26, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x5f, 0x55, 0x6e, 0x73,
	0x61, 0x66, 0x65, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26,
	0x46, 0x4d, 0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x5f, 0x48, 0x6f, 0x73, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x10, 0x02, 0x12, 0x2f, 0x0a, 0x2b, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x56, 0x31, 0x5f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x10, 0x03, 0x12, 0x38, 0x0a, 0x34, 0x46, 0x4d, 0x43,
	0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x56, 0x31, 0x5f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x64,
	0x53, 0x75, 0x62, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x41, 0x6c, 0x63, 0x6f, 0x68, 0x6f,
	0x6c, 0x10, 0x04, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x5f,
	0x48, 0x6d, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x10, 0x05, 0x12, 0x2a,
	0x0a, 0x26, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x5f, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x46, 0x69, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x10, 0x06, 0x12, 0x2b, 0x0a, 0x27, 0x46, 0x4d,
	0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x5f, 0x43, 0x72, 0x61, 0x73, 0x68, 0x49, 0x6e, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x6f, 0x72, 0x10, 0x07, 0x12, 0x2b, 0x0a, 0x27, 0x46, 0x4d, 0x43, 0x53, 0x41,
	0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x56, 0x31, 0x5f, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x74, 0x68,
	0x65, 0x72, 0x10, 0x08, 0x2a, 0xc6, 0x02, 0x0a, 0x16, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56, 0x31, 0x12,
	0x26, 0x0a, 0x22, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56, 0x31, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x4d, 0x43, 0x53, 0x41,
	0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56,
	0x31, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x46,
	0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x56, 0x31, 0x5f, 0x46, 0x75, 0x6c, 0x6c, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21,
	0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x56, 0x31, 0x5f, 0x57, 0x61, 0x6c, 0x6b, 0x41, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56, 0x31, 0x5f, 0x44, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x4f, 0x6e, 0x6c, 0x79, 0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x46, 0x4d,
	0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x56, 0x31, 0x5f, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x75, 0x64,
	0x79, 0x10, 0x05, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56, 0x31, 0x5f, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x06, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x56, 0x31, 0x5f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x10, 0x07, 0x2a, 0xc4, 0x08,
	0x0a, 0x10, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65,
	0x56, 0x31, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f,
	0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72,
	0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c,
	0x46, 0x72, 0x65, 0x69, 0x67, 0x68, 0x74, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x4d, 0x43,
	0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x48, 0x6f,
	0x75, 0x73, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x4d, 0x43,
	0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4d, 0x65,
	0x74, 0x61, 0x6c, 0x53, 0x68, 0x65, 0x65, 0x74, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x4d,
	0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4d,
	0x6f, 0x74, 0x6f, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x10, 0x04, 0x12, 0x25,
	0x0a, 0x21, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65,
	0x56, 0x31, 0x5f, 0x44, 0x72, 0x69, 0x76, 0x65, 0x61, 0x77, 0x61, 0x79, 0x54, 0x6f, 0x77, 0x61,
	0x77, 0x61, 0x79, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61,
	0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4c, 0x6f, 0x67, 0x73, 0x50, 0x6f,
	0x6c, 0x65, 0x73, 0x42, 0x65, 0x61, 0x6d, 0x73, 0x4c, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x10, 0x06,
	0x12, 0x26, 0x0a, 0x22, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79,
	0x70, 0x65, 0x56, 0x31, 0x5f, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26, 0x46, 0x4d,
	0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x72, 0x79, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x10, 0x09, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43,
	0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x65, 0x10, 0x0a, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72,
	0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4c, 0x69, 0x71, 0x75, 0x69, 0x64, 0x73,
	0x47, 0x61, 0x73, 0x65, 0x73, 0x10, 0x0b, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x4d, 0x43, 0x53, 0x41,
	0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73,
	0x10, 0x0c, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f,
	0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72,
	0x73, 0x10, 0x0d, 0x12, 0x26, 0x0a, 0x22, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4f, 0x69, 0x6c, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x0e, 0x12, 0x1e, 0x0a, 0x1a, 0x46,
	0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f,
	0x4c, 0x69, 0x76, 0x65, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x10, 0x0f, 0x12, 0x1e, 0x0a, 0x1a, 0x46,
	0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f,
	0x47, 0x72, 0x61, 0x69, 0x6e, 0x66, 0x65, 0x65, 0x64, 0x10, 0x10, 0x12, 0x1d, 0x0a, 0x19, 0x46,
	0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f,
	0x43, 0x6f, 0x61, 0x6c, 0x43, 0x6f, 0x6b, 0x65, 0x10, 0x11, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x4d,
	0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4d,
	0x65, 0x61, 0x74, 0x10, 0x12, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61,
	0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x47, 0x61, 0x72, 0x62, 0x61, 0x67,
	0x65, 0x10, 0x13, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x55, 0x73, 0x4d, 0x61, 0x69, 0x6c, 0x10, 0x14,
	0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79,
	0x70, 0x65, 0x56, 0x31, 0x5f, 0x43, 0x68, 0x65, 0x6d, 0x69, 0x63, 0x61, 0x6c, 0x73, 0x10, 0x15,
	0x12, 0x27, 0x0a, 0x23, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79,
	0x70, 0x65, 0x56, 0x31, 0x5f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x44, 0x72, 0x79, 0x42, 0x75, 0x6c, 0x6b, 0x10, 0x16, 0x12, 0x25, 0x0a, 0x21, 0x46, 0x4d, 0x43,
	0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x52, 0x65,
	0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x10, 0x17,
	0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79,
	0x70, 0x65, 0x56, 0x31, 0x5f, 0x42, 0x65, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x10, 0x18,
	0x12, 0x22, 0x0a, 0x1e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79,
	0x70, 0x65, 0x56, 0x31, 0x5f, 0x50, 0x61, 0x70, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x73, 0x10, 0x19, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72,
	0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x10, 0x1a, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61, 0x72, 0x67, 0x6f,
	0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x46, 0x61, 0x72, 0x6d, 0x53, 0x75, 0x70, 0x70, 0x6c,
	0x69, 0x65, 0x73, 0x10, 0x1b, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x61,
	0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x1c, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x57, 0x61, 0x74,
	0x65, 0x72, 0x57, 0x65, 0x6c, 0x6c, 0x10, 0x1d, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4f, 0x74, 0x68,
	0x65, 0x72, 0x10, 0x1e, 0x2a, 0x50, 0x0a, 0x15, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x31, 0x12, 0x0d, 0x0a,
	0x09, 0x55, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x10, 0x00, 0x12, 0x05, 0x0a, 0x01,
	0x41, 0x10, 0x01, 0x12, 0x05, 0x0a, 0x01, 0x42, 0x10, 0x02, 0x12, 0x05, 0x0a, 0x01, 0x43, 0x10,
	0x03, 0x12, 0x05, 0x0a, 0x01, 0x44, 0x10, 0x04, 0x12, 0x05, 0x0a, 0x01, 0x45, 0x10, 0x05, 0x12,
	0x05, 0x0a, 0x01, 0x46, 0x10, 0x06, 0x2a, 0xaf, 0x01, 0x0a, 0x12, 0x4d, 0x61, 0x69, 0x6e, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x12, 0x22, 0x0a,
	0x1e, 0x4d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x56, 0x31, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10,
	0x00, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x41, 0x75, 0x74, 0x6f, 0x4c, 0x69, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x61, 0x69, 0x6e, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x4d, 0x6f,
	0x74, 0x6f, 0x72, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x10, 0x02, 0x12,
	0x27, 0x0a, 0x23, 0x4d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x56, 0x31, 0x5f, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x4c, 0x69, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x10, 0x03, 0x32, 0xb3, 0x0e, 0x0a, 0x07, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4d, 0x56, 0x52, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x56, 0x31, 0x12, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x1a, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x56, 0x31, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x56,
	0x52, 0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x31, 0x12,
	0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x4d, 0x56, 0x52, 0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x20, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x56, 0x52, 0x41, 0x74, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x31, 0x12,
	0x22, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x56, 0x49, 0x4e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x1a, 0x1b, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x69, 0x6e, 0x67, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x31,
	0x22, 0x00, 0x12, 0x6c, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x31, 0x12, 0x2a, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x31, 0x22, 0x00,
	0x12, 0x7b, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x41, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x31,
	0x12, 0x2f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x1a, 0x28, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x22, 0x00, 0x12, 0x72, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x42, 0x49, 0x50, 0x44, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x12, 0x2c, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x25, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x22,
	0x00, 0x12, 0x8a, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x42, 0x49, 0x50, 0x44, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x12, 0x34, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x2d, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x49,
	0x50, 0x44, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x87,
	0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x44, 0x75, 0x6d,
	0x70, 0x44, 0x61, 0x74, 0x65, 0x56, 0x31, 0x12, 0x31, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x73, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x2a, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x73, 0x44, 0x75, 0x6d, 0x70,
	0x44, 0x61, 0x74, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x8a, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74,
	0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x46, 0x6f, 0x72, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x56, 0x31, 0x12, 0x34,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x52,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x46, 0x6f, 0x72, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x1a, 0x2d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x46, 0x6f, 0x72, 0x44, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74,
	0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x12, 0x28,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x52,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54,
	0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x22, 0x00, 0x12, 0x75, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x31, 0x12, 0x2d, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x26, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41,
	0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x56, 0x31, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x46, 0x4d, 0x43, 0x53, 0x41,
	0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x56, 0x31, 0x12, 0x2e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12, 0x63,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x65, 0x6e, 0x73, 0x75, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x12, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x65, 0x6e, 0x73,
	0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a,
	0x20, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x46, 0x4d, 0x43, 0x53, 0x41, 0x43, 0x65, 0x6e, 0x73, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x56,
	0x31, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x56, 0x31,
	0x12, 0x2c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x1f,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x56, 0x31, 0x22,
	0x00, 0x12, 0x78, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x75, 0x6e, 0x74,
	0x61, 0x72, 0x79, 0x52, 0x65, 0x76, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x56, 0x31,
	0x12, 0x2e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x75, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x52, 0x65, 0x76, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x75, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x52, 0x65, 0x76, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65,
	0x73, 0x56, 0x31, 0x12, 0x2a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a,
	0x2b, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x22, 0x00, 0x42, 0x40,
	0x5a, 0x3e, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_data_fetching_api_proto_rawDescOnce sync.Once
	file_data_fetching_api_proto_rawDescData = file_data_fetching_api_proto_rawDesc
)

func file_data_fetching_api_proto_rawDescGZIP() []byte {
	file_data_fetching_api_proto_rawDescOnce.Do(func() {
		file_data_fetching_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_data_fetching_api_proto_rawDescData)
	})
	return file_data_fetching_api_proto_rawDescData
}

var file_data_fetching_api_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_data_fetching_api_proto_msgTypes = make([]protoimpl.MessageInfo, 72)
var file_data_fetching_api_proto_goTypes = []interface{}{
	(NhtsaErrorCodeV1)(0),                         // 0: data_fetching.NhtsaErrorCodeV1
	(VehicleTypeV1)(0),                            // 1: data_fetching.VehicleTypeV1
	(WeightClassV1)(0),                            // 2: data_fetching.WeightClassV1
	(FMCSAViolationCategoryV1)(0),                 // 3: data_fetching.FMCSAViolationCategoryV1
	(FMCSAInspectionLevelV1)(0),                   // 4: data_fetching.FMCSAInspectionLevelV1
	(FMCSACargoTypeV1)(0),                         // 5: data_fetching.FMCSACargoTypeV1
	(ObjectiveGradeScoreV1)(0),                    // 6: data_fetching.ObjectiveGradeScoreV1
	(MainCoverageTypeV1)(0),                       // 7: data_fetching.MainCoverageTypeV1
	(*MVRReportV1)(nil),                           // 8: data_fetching.MVRReportV1
	(*MVRViolationV1)(nil),                        // 9: data_fetching.MVRViolationV1
	(*MVRReportRequestV1)(nil),                    // 10: data_fetching.MVRReportRequestV1
	(*MVRAttractScoreV1)(nil),                     // 11: data_fetching.MVRAttractScoreV1
	(*MVRAttractScoreRequestV1)(nil),              // 12: data_fetching.MVRAttractScoreRequestV1
	(*VINDecodeErrorV1)(nil),                      // 13: data_fetching.VINDecodeErrorV1
	(*VINDetailsV1)(nil),                          // 14: data_fetching.VINDetailsV1
	(*VINDetailsRequestV1)(nil),                   // 15: data_fetching.VINDetailsRequestV1
	(*BIPDActiveOrPendingInsuranceV1)(nil),        // 16: data_fetching.BIPDActiveOrPendingInsuranceV1
	(*BIPDActiveOrPendingInsuranceRequestV1)(nil), // 17: data_fetching.BIPDActiveOrPendingInsuranceRequestV1
	(*NationalCreditFileRequestV1)(nil),           // 18: data_fetching.NationalCreditFileRequestV1
	(*AddressV1)(nil),                             // 19: data_fetching.AddressV1
	(*NationalCreditFileV1)(nil),                  // 20: data_fetching.NationalCreditFileV1
	(*NameV1)(nil),                                // 21: data_fetching.NameV1
	(*DobV1)(nil),                                 // 22: data_fetching.DobV1
	(*SubjectV1)(nil),                             // 23: data_fetching.SubjectV1
	(*DateFirstAtAddressV1)(nil),                  // 24: data_fetching.DateFirstAtAddressV1
	(*CurrentAddressV1)(nil),                      // 25: data_fetching.CurrentAddressV1
	(*SubjectInfoV1)(nil),                         // 26: data_fetching.SubjectInfoV1
	(*DateCreditFileEstbedV1)(nil),                // 27: data_fetching.DateCreditFileEstbedV1
	(*CurrentStatusAccountV1)(nil),                // 28: data_fetching.CurrentStatusAccountV1
	(*CurrentStatusAccountsV1)(nil),               // 29: data_fetching.CurrentStatusAccountsV1
	(*HistoryStatusAccountsV1)(nil),               // 30: data_fetching.HistoryStatusAccountsV1
	(*CreditReportSummaryV1)(nil),                 // 31: data_fetching.CreditReportSummaryV1
	(*EmploymentInfoV1)(nil),                      // 32: data_fetching.EmploymentInfoV1
	(*EmploymentInfosV1)(nil),                     // 33: data_fetching.EmploymentInfosV1
	(*CollectionRecordV1)(nil),                    // 34: data_fetching.CollectionRecordV1
	(*CollectionRecordsV1)(nil),                   // 35: data_fetching.CollectionRecordsV1
	(*CollectionRecordsInfoV1)(nil),               // 36: data_fetching.CollectionRecordsInfoV1
	(*MessageV1)(nil),                             // 37: data_fetching.MessageV1
	(*MessagesListV1)(nil),                        // 38: data_fetching.MessagesListV1
	(*CreditTradeHistoryRecordV1)(nil),            // 39: data_fetching.CreditTradeHistoryRecordV1
	(*CreditTradeHistoryRecordsV1)(nil),           // 40: data_fetching.CreditTradeHistoryRecordsV1
	(*TradeAccountInfoV1)(nil),                    // 41: data_fetching.TradeAccountInfoV1
	(*DateOfInquiryV1)(nil),                       // 42: data_fetching.DateOfInquiryV1
	(*InquiryHistoryHeaderRecordV1)(nil),          // 43: data_fetching.InquiryHistoryHeaderRecordV1
	(*InquiryHistoryHeaderRecordsV1)(nil),         // 44: data_fetching.InquiryHistoryHeaderRecordsV1
	(*InquiryHistoryHeaderV1)(nil),                // 45: data_fetching.InquiryHistoryHeaderV1
	(*NcfProductReportV1)(nil),                    // 46: data_fetching.NcfProductReportV1
	(*NcfReportV1)(nil),                           // 47: data_fetching.NcfReportV1
	(*AuthorityHistoryRecordV1)(nil),              // 48: data_fetching.AuthorityHistoryRecordV1
	(*GrantedAuthorityHistoryRequestV1)(nil),      // 49: data_fetching.GrantedAuthorityHistoryRequestV1
	(*GrantedAuthorityHistoryV1)(nil),             // 50: data_fetching.GrantedAuthorityHistoryV1
	(*TransactionDetailsExV1)(nil),                // 51: data_fetching.TransactionDetailsExV1
	(*BIPDInsuranceHistoryRequestV1)(nil),         // 52: data_fetching.BIPDInsuranceHistoryRequestV1
	(*InsuranceRecordV1)(nil),                     // 53: data_fetching.InsuranceRecordV1
	(*CancellationV1)(nil),                        // 54: data_fetching.CancellationV1
	(*BIPDInsuranceHistoryV1)(nil),                // 55: data_fetching.BIPDInsuranceHistoryV1
	(*LatestRatingTiersDumpDateRequestV1)(nil),    // 56: data_fetching.LatestRatingTiersDumpDateRequestV1
	(*LatestRatingTiersDumpDateV1)(nil),           // 57: data_fetching.LatestRatingTiersDumpDateV1
	(*RatingTierRecordV1)(nil),                    // 58: data_fetching.RatingTierRecordV1
	(*RatingTierRecordsForDumpDateRequestV1)(nil), // 59: data_fetching.RatingTierRecordsForDumpDateRequestV1
	(*RatingTierRecordsForDumpDateV1)(nil),        // 60: data_fetching.RatingTierRecordsForDumpDateV1
	(*RatingTierRecordRequestV1)(nil),             // 61: data_fetching.RatingTierRecordRequestV1
	(*FMCSAViolationRecordsRequestV1)(nil),        // 62: data_fetching.FMCSAViolationRecordsRequestV1
	(*FMCSAViolationRecordsV1)(nil),               // 63: data_fetching.FMCSAViolationRecordsV1
	(*FMCSAViolationRecordV1)(nil),                // 64: data_fetching.FMCSAViolationRecordV1
	(*FMCSAInspectionRecordsRequestV1)(nil),       // 65: data_fetching.FMCSAInspectionRecordsRequestV1
	(*FMCSAInspectionRecordsV1)(nil),              // 66: data_fetching.FMCSAInspectionRecordsV1
	(*FMCSAInspectionRecordV1)(nil),               // 67: data_fetching.FMCSAInspectionRecordV1
	(*FMCSAInspectionVehicleV1)(nil),              // 68: data_fetching.FMCSAInspectionVehicleV1
	(*FMCSACensusInfoRequestV1)(nil),              // 69: data_fetching.FMCSACensusInfoRequestV1
	(*FMCSACensusInfoV1)(nil),                     // 70: data_fetching.FMCSACensusInfoV1
	(*LatestObjectiveGradeRequestV1)(nil),         // 71: data_fetching.LatestObjectiveGradeRequestV1
	(*ObjectiveGradeV1)(nil),                      // 72: data_fetching.ObjectiveGradeV1
	(*InvoluntaryRevocationsRequestV1)(nil),       // 73: data_fetching.InvoluntaryRevocationsRequestV1
	(*InvoluntaryRevocationsV1)(nil),              // 74: data_fetching.InvoluntaryRevocationsV1
	(*GetNirvanaPoliciesRequestV1)(nil),           // 75: data_fetching.GetNirvanaPoliciesRequestV1
	(*NirvanaPolicyV1)(nil),                       // 76: data_fetching.NirvanaPolicyV1
	(*GetNirvanaPoliciesResponseV1)(nil),          // 77: data_fetching.GetNirvanaPoliciesResponseV1
	nil,                                           // 78: data_fetching.FMCSAInspectionRecordV1.BasicCategoriesEntry
	nil,                                           // 79: data_fetching.FMCSAInspectionRecordV1.BasicViolationsEntry
	(*timestamppb.Timestamp)(nil),                 // 80: google.protobuf.Timestamp
}
var file_data_fetching_api_proto_depIdxs = []int32{
	80,  // 0: data_fetching.MVRReportV1.dob:type_name -> google.protobuf.Timestamp
	80,  // 1: data_fetching.MVRReportV1.dateIssued:type_name -> google.protobuf.Timestamp
	80,  // 2: data_fetching.MVRReportV1.dateExpires:type_name -> google.protobuf.Timestamp
	80,  // 3: data_fetching.MVRReportV1.mvrReportDate:type_name -> google.protobuf.Timestamp
	9,   // 4: data_fetching.MVRReportV1.violations:type_name -> data_fetching.MVRViolationV1
	80,  // 5: data_fetching.MVRViolationV1.violationDate:type_name -> google.protobuf.Timestamp
	80,  // 6: data_fetching.MVRViolationV1.convictionDate:type_name -> google.protobuf.Timestamp
	80,  // 7: data_fetching.MVRReportRequestV1.dob:type_name -> google.protobuf.Timestamp
	80,  // 8: data_fetching.MVRAttractScoreRequestV1.dob:type_name -> google.protobuf.Timestamp
	0,   // 9: data_fetching.VINDecodeErrorV1.nhtsaErrorCodes:type_name -> data_fetching.NhtsaErrorCodeV1
	1,   // 10: data_fetching.VINDetailsV1.vehicleType:type_name -> data_fetching.VehicleTypeV1
	2,   // 11: data_fetching.VINDetailsV1.weightClass:type_name -> data_fetching.WeightClassV1
	13,  // 12: data_fetching.VINDetailsV1.decodeError:type_name -> data_fetching.VINDecodeErrorV1
	53,  // 13: data_fetching.BIPDActiveOrPendingInsuranceV1.records:type_name -> data_fetching.InsuranceRecordV1
	80,  // 14: data_fetching.NationalCreditFileRequestV1.dob:type_name -> google.protobuf.Timestamp
	19,  // 15: data_fetching.NationalCreditFileRequestV1.address:type_name -> data_fetching.AddressV1
	47,  // 16: data_fetching.NationalCreditFileV1.NcfReport:type_name -> data_fetching.NcfReportV1
	51,  // 17: data_fetching.NationalCreditFileV1.TransactionDetails:type_name -> data_fetching.TransactionDetailsExV1
	80,  // 18: data_fetching.NationalCreditFileV1.DateOfCreditReportRun:type_name -> google.protobuf.Timestamp
	21,  // 19: data_fetching.SubjectV1.name:type_name -> data_fetching.NameV1
	22,  // 20: data_fetching.SubjectV1.dob:type_name -> data_fetching.DobV1
	24,  // 21: data_fetching.CurrentAddressV1.dateFirstAtAddress:type_name -> data_fetching.DateFirstAtAddressV1
	23,  // 22: data_fetching.SubjectInfoV1.subject:type_name -> data_fetching.SubjectV1
	25,  // 23: data_fetching.SubjectInfoV1.currentAddress:type_name -> data_fetching.CurrentAddressV1
	28,  // 24: data_fetching.CurrentStatusAccountsV1.currentStatusAccount:type_name -> data_fetching.CurrentStatusAccountV1
	28,  // 25: data_fetching.HistoryStatusAccountsV1.historyStatusAccount:type_name -> data_fetching.CurrentStatusAccountV1
	27,  // 26: data_fetching.CreditReportSummaryV1.dateCreditFileEstbed:type_name -> data_fetching.DateCreditFileEstbedV1
	24,  // 27: data_fetching.CreditReportSummaryV1.oldestOpeningDateOfTrade:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 28: data_fetching.CreditReportSummaryV1.latestReportingDateOfTrade:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 29: data_fetching.CreditReportSummaryV1.dateOfLatestFileActivity:type_name -> data_fetching.DateFirstAtAddressV1
	29,  // 30: data_fetching.CreditReportSummaryV1.currentStatusAccounts:type_name -> data_fetching.CurrentStatusAccountsV1
	30,  // 31: data_fetching.CreditReportSummaryV1.historyStatusAccounts:type_name -> data_fetching.HistoryStatusAccountsV1
	32,  // 32: data_fetching.EmploymentInfosV1.employmentInfo:type_name -> data_fetching.EmploymentInfoV1
	24,  // 33: data_fetching.CollectionRecordV1.dateReported:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 34: data_fetching.CollectionRecordV1.dateAssigned:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 35: data_fetching.CollectionRecordV1.dateOfLastActivity:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 36: data_fetching.CollectionRecordV1.dateOfBalance:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 37: data_fetching.CollectionRecordV1.statusDate:type_name -> data_fetching.DateFirstAtAddressV1
	34,  // 38: data_fetching.CollectionRecordsV1.collectionRecord:type_name -> data_fetching.CollectionRecordV1
	35,  // 39: data_fetching.CollectionRecordsInfoV1.collectionRecords:type_name -> data_fetching.CollectionRecordsV1
	37,  // 40: data_fetching.MessagesListV1.message:type_name -> data_fetching.MessageV1
	24,  // 41: data_fetching.CreditTradeHistoryRecordV1.dateReported:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 42: data_fetching.CreditTradeHistoryRecordV1.dateAccountOpened:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 43: data_fetching.CreditTradeHistoryRecordV1.prevRateDate1:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 44: data_fetching.CreditTradeHistoryRecordV1.prevRateDate2:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 45: data_fetching.CreditTradeHistoryRecordV1.prevRateDate3:type_name -> data_fetching.DateFirstAtAddressV1
	24,  // 46: data_fetching.CreditTradeHistoryRecordV1.dateOfLastActivity:type_name -> data_fetching.DateFirstAtAddressV1
	38,  // 47: data_fetching.CreditTradeHistoryRecordV1.messages:type_name -> data_fetching.MessagesListV1
	39,  // 48: data_fetching.CreditTradeHistoryRecordsV1.creditTradeHistoryRecord:type_name -> data_fetching.CreditTradeHistoryRecordV1
	40,  // 49: data_fetching.TradeAccountInfoV1.creditTradeHistoryRecords:type_name -> data_fetching.CreditTradeHistoryRecordsV1
	42,  // 50: data_fetching.InquiryHistoryHeaderRecordV1.dateOfInquiry:type_name -> data_fetching.DateOfInquiryV1
	43,  // 51: data_fetching.InquiryHistoryHeaderRecordsV1.inquiryHistoryHeaderRecord:type_name -> data_fetching.InquiryHistoryHeaderRecordV1
	44,  // 52: data_fetching.InquiryHistoryHeaderV1.inquiryHistoryHeaderRecords:type_name -> data_fetching.InquiryHistoryHeaderRecordsV1
	26,  // 53: data_fetching.NcfProductReportV1.subjectInfo:type_name -> data_fetching.SubjectInfoV1
	31,  // 54: data_fetching.NcfProductReportV1.creditReportSummary:type_name -> data_fetching.CreditReportSummaryV1
	33,  // 55: data_fetching.NcfProductReportV1.employmentInfos:type_name -> data_fetching.EmploymentInfosV1
	36,  // 56: data_fetching.NcfProductReportV1.collectionRecordsInfo:type_name -> data_fetching.CollectionRecordsInfoV1
	41,  // 57: data_fetching.NcfProductReportV1.tradeAccountInfo:type_name -> data_fetching.TradeAccountInfoV1
	45,  // 58: data_fetching.NcfProductReportV1.inquiryHistoryHeader:type_name -> data_fetching.InquiryHistoryHeaderV1
	46,  // 59: data_fetching.NcfReportV1.ncfProductReport:type_name -> data_fetching.NcfProductReportV1
	80,  // 60: data_fetching.AuthorityHistoryRecordV1.originalActionServedDate:type_name -> google.protobuf.Timestamp
	80,  // 61: data_fetching.AuthorityHistoryRecordV1.finalActionDecisionDate:type_name -> google.protobuf.Timestamp
	80,  // 62: data_fetching.AuthorityHistoryRecordV1.finalActionServedDate:type_name -> google.protobuf.Timestamp
	80,  // 63: data_fetching.AuthorityHistoryRecordV1.CreatedAt:type_name -> google.protobuf.Timestamp
	48,  // 64: data_fetching.GrantedAuthorityHistoryV1.records:type_name -> data_fetching.AuthorityHistoryRecordV1
	80,  // 65: data_fetching.BIPDInsuranceHistoryRequestV1.sinceDate:type_name -> google.protobuf.Timestamp
	80,  // 66: data_fetching.InsuranceRecordV1.postedDate:type_name -> google.protobuf.Timestamp
	80,  // 67: data_fetching.InsuranceRecordV1.effectiveDate:type_name -> google.protobuf.Timestamp
	80,  // 68: data_fetching.InsuranceRecordV1.cancelEffectiveDate:type_name -> google.protobuf.Timestamp
	80,  // 69: data_fetching.InsuranceRecordV1.createdAt:type_name -> google.protobuf.Timestamp
	54,  // 70: data_fetching.InsuranceRecordV1.cancellationDetails:type_name -> data_fetching.CancellationV1
	53,  // 71: data_fetching.BIPDInsuranceHistoryV1.records:type_name -> data_fetching.InsuranceRecordV1
	80,  // 72: data_fetching.LatestRatingTiersDumpDateV1.date:type_name -> google.protobuf.Timestamp
	80,  // 73: data_fetching.RatingTierRecordV1.date:type_name -> google.protobuf.Timestamp
	80,  // 74: data_fetching.RatingTierRecordsForDumpDateRequestV1.dumpDate:type_name -> google.protobuf.Timestamp
	58,  // 75: data_fetching.RatingTierRecordsForDumpDateV1.records:type_name -> data_fetching.RatingTierRecordV1
	80,  // 76: data_fetching.RatingTierRecordRequestV1.dumpDate:type_name -> google.protobuf.Timestamp
	80,  // 77: data_fetching.RatingTierRecordRequestV1.recordDate:type_name -> google.protobuf.Timestamp
	80,  // 78: data_fetching.FMCSAViolationRecordsRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	64,  // 79: data_fetching.FMCSAViolationRecordsV1.records:type_name -> data_fetching.FMCSAViolationRecordV1
	80,  // 80: data_fetching.FMCSAViolationRecordV1.publishedDate:type_name -> google.protobuf.Timestamp
	80,  // 81: data_fetching.FMCSAViolationRecordV1.inspectionDate:type_name -> google.protobuf.Timestamp
	3,   // 82: data_fetching.FMCSAViolationRecordV1.category:type_name -> data_fetching.FMCSAViolationCategoryV1
	80,  // 83: data_fetching.FMCSAInspectionRecordsRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	67,  // 84: data_fetching.FMCSAInspectionRecordsV1.records:type_name -> data_fetching.FMCSAInspectionRecordV1
	80,  // 85: data_fetching.FMCSAInspectionRecordV1.publishedDate:type_name -> google.protobuf.Timestamp
	80,  // 86: data_fetching.FMCSAInspectionRecordV1.inspectionDate:type_name -> google.protobuf.Timestamp
	4,   // 87: data_fetching.FMCSAInspectionRecordV1.inspectionLevel:type_name -> data_fetching.FMCSAInspectionLevelV1
	78,  // 88: data_fetching.FMCSAInspectionRecordV1.basicCategories:type_name -> data_fetching.FMCSAInspectionRecordV1.BasicCategoriesEntry
	79,  // 89: data_fetching.FMCSAInspectionRecordV1.basicViolations:type_name -> data_fetching.FMCSAInspectionRecordV1.BasicViolationsEntry
	68,  // 90: data_fetching.FMCSAInspectionRecordV1.vehicles:type_name -> data_fetching.FMCSAInspectionVehicleV1
	80,  // 91: data_fetching.FMCSACensusInfoV1.date:type_name -> google.protobuf.Timestamp
	5,   // 92: data_fetching.FMCSACensusInfoV1.carrierCargoTypes:type_name -> data_fetching.FMCSACargoTypeV1
	80,  // 93: data_fetching.FMCSACensusInfoV1.ratingDate:type_name -> google.protobuf.Timestamp
	80,  // 94: data_fetching.ObjectiveGradeV1.date:type_name -> google.protobuf.Timestamp
	6,   // 95: data_fetching.ObjectiveGradeV1.score:type_name -> data_fetching.ObjectiveGradeScoreV1
	80,  // 96: data_fetching.InvoluntaryRevocationsRequestV1.startDate:type_name -> google.protobuf.Timestamp
	80,  // 97: data_fetching.InvoluntaryRevocationsRequestV1.endDate:type_name -> google.protobuf.Timestamp
	48,  // 98: data_fetching.InvoluntaryRevocationsV1.records:type_name -> data_fetching.AuthorityHistoryRecordV1
	80,  // 99: data_fetching.GetNirvanaPoliciesRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	7,   // 100: data_fetching.GetNirvanaPoliciesRequestV1.mainCoverageType:type_name -> data_fetching.MainCoverageTypeV1
	80,  // 101: data_fetching.NirvanaPolicyV1.effectiveDate:type_name -> google.protobuf.Timestamp
	80,  // 102: data_fetching.NirvanaPolicyV1.effectiveDateTo:type_name -> google.protobuf.Timestamp
	80,  // 103: data_fetching.NirvanaPolicyV1.createdAt:type_name -> google.protobuf.Timestamp
	80,  // 104: data_fetching.NirvanaPolicyV1.updatedAt:type_name -> google.protobuf.Timestamp
	76,  // 105: data_fetching.GetNirvanaPoliciesResponseV1.policies:type_name -> data_fetching.NirvanaPolicyV1
	10,  // 106: data_fetching.Fetcher.GetMVRReportV1:input_type -> data_fetching.MVRReportRequestV1
	12,  // 107: data_fetching.Fetcher.GetMVRAttractScoreV1:input_type -> data_fetching.MVRAttractScoreRequestV1
	15,  // 108: data_fetching.Fetcher.GetVINDetailsV1:input_type -> data_fetching.VINDetailsRequestV1
	18,  // 109: data_fetching.Fetcher.GetNationalCreditFileV1:input_type -> data_fetching.NationalCreditFileRequestV1
	49,  // 110: data_fetching.Fetcher.GetGrantedAuthorityHistoryV1:input_type -> data_fetching.GrantedAuthorityHistoryRequestV1
	52,  // 111: data_fetching.Fetcher.GetBIPDInsuranceHistoryV1:input_type -> data_fetching.BIPDInsuranceHistoryRequestV1
	17,  // 112: data_fetching.Fetcher.GetBIPDActiveOrPendingInsuranceV1:input_type -> data_fetching.BIPDActiveOrPendingInsuranceRequestV1
	56,  // 113: data_fetching.Fetcher.GetLatestRatingTierRecordsDumpDateV1:input_type -> data_fetching.LatestRatingTiersDumpDateRequestV1
	59,  // 114: data_fetching.Fetcher.GetRatingTierRecordsForDumpDateV1:input_type -> data_fetching.RatingTierRecordsForDumpDateRequestV1
	61,  // 115: data_fetching.Fetcher.GetRatingTierRecordV1:input_type -> data_fetching.RatingTierRecordRequestV1
	62,  // 116: data_fetching.Fetcher.GetFMCSAViolationRecordsV1:input_type -> data_fetching.FMCSAViolationRecordsRequestV1
	65,  // 117: data_fetching.Fetcher.GetFMCSAInspectionRecordsV1:input_type -> data_fetching.FMCSAInspectionRecordsRequestV1
	69,  // 118: data_fetching.Fetcher.GetFMCSACensusInfoV1:input_type -> data_fetching.FMCSACensusInfoRequestV1
	71,  // 119: data_fetching.Fetcher.GetLatestObjectiveGradeV1:input_type -> data_fetching.LatestObjectiveGradeRequestV1
	73,  // 120: data_fetching.Fetcher.GetInvoluntaryRevocationsV1:input_type -> data_fetching.InvoluntaryRevocationsRequestV1
	75,  // 121: data_fetching.Fetcher.GetNirvanaPoliciesV1:input_type -> data_fetching.GetNirvanaPoliciesRequestV1
	8,   // 122: data_fetching.Fetcher.GetMVRReportV1:output_type -> data_fetching.MVRReportV1
	11,  // 123: data_fetching.Fetcher.GetMVRAttractScoreV1:output_type -> data_fetching.MVRAttractScoreV1
	14,  // 124: data_fetching.Fetcher.GetVINDetailsV1:output_type -> data_fetching.VINDetailsV1
	20,  // 125: data_fetching.Fetcher.GetNationalCreditFileV1:output_type -> data_fetching.NationalCreditFileV1
	50,  // 126: data_fetching.Fetcher.GetGrantedAuthorityHistoryV1:output_type -> data_fetching.GrantedAuthorityHistoryV1
	55,  // 127: data_fetching.Fetcher.GetBIPDInsuranceHistoryV1:output_type -> data_fetching.BIPDInsuranceHistoryV1
	16,  // 128: data_fetching.Fetcher.GetBIPDActiveOrPendingInsuranceV1:output_type -> data_fetching.BIPDActiveOrPendingInsuranceV1
	57,  // 129: data_fetching.Fetcher.GetLatestRatingTierRecordsDumpDateV1:output_type -> data_fetching.LatestRatingTiersDumpDateV1
	60,  // 130: data_fetching.Fetcher.GetRatingTierRecordsForDumpDateV1:output_type -> data_fetching.RatingTierRecordsForDumpDateV1
	58,  // 131: data_fetching.Fetcher.GetRatingTierRecordV1:output_type -> data_fetching.RatingTierRecordV1
	63,  // 132: data_fetching.Fetcher.GetFMCSAViolationRecordsV1:output_type -> data_fetching.FMCSAViolationRecordsV1
	66,  // 133: data_fetching.Fetcher.GetFMCSAInspectionRecordsV1:output_type -> data_fetching.FMCSAInspectionRecordsV1
	70,  // 134: data_fetching.Fetcher.GetFMCSACensusInfoV1:output_type -> data_fetching.FMCSACensusInfoV1
	72,  // 135: data_fetching.Fetcher.GetLatestObjectiveGradeV1:output_type -> data_fetching.ObjectiveGradeV1
	74,  // 136: data_fetching.Fetcher.GetInvoluntaryRevocationsV1:output_type -> data_fetching.InvoluntaryRevocationsV1
	77,  // 137: data_fetching.Fetcher.GetNirvanaPoliciesV1:output_type -> data_fetching.GetNirvanaPoliciesResponseV1
	122, // [122:138] is the sub-list for method output_type
	106, // [106:122] is the sub-list for method input_type
	106, // [106:106] is the sub-list for extension type_name
	106, // [106:106] is the sub-list for extension extendee
	0,   // [0:106] is the sub-list for field type_name
}

func init() { file_data_fetching_api_proto_init() }
func file_data_fetching_api_proto_init() {
	if File_data_fetching_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_data_fetching_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MVRReportV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MVRViolationV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MVRReportRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MVRAttractScoreV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MVRAttractScoreRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecodeErrorV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDetailsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDetailsRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BIPDActiveOrPendingInsuranceV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BIPDActiveOrPendingInsuranceRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NationalCreditFileRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NationalCreditFileV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NameV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DobV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubjectV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateFirstAtAddressV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentAddressV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubjectInfoV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateCreditFileEstbedV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentStatusAccountV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentStatusAccountsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HistoryStatusAccountsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditReportSummaryV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentInfoV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentInfosV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionRecordV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionRecordsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionRecordsInfoV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessagesListV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditTradeHistoryRecordV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditTradeHistoryRecordsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TradeAccountInfoV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateOfInquiryV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InquiryHistoryHeaderRecordV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InquiryHistoryHeaderRecordsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InquiryHistoryHeaderV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NcfProductReportV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NcfReportV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthorityHistoryRecordV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantedAuthorityHistoryRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantedAuthorityHistoryV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionDetailsExV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BIPDInsuranceHistoryRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuranceRecordV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancellationV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BIPDInsuranceHistoryV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LatestRatingTiersDumpDateRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LatestRatingTiersDumpDateV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RatingTierRecordV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RatingTierRecordsForDumpDateRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RatingTierRecordsForDumpDateV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RatingTierRecordRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSAViolationRecordsRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSAViolationRecordsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSAViolationRecordV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSAInspectionRecordsRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSAInspectionRecordsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSAInspectionRecordV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSAInspectionVehicleV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSACensusInfoRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FMCSACensusInfoV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LatestObjectiveGradeRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjectiveGradeV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvoluntaryRevocationsRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvoluntaryRevocationsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNirvanaPoliciesRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaPolicyV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_fetching_api_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNirvanaPoliciesResponseV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_data_fetching_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[40].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[44].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[45].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[46].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[50].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[56].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[59].OneofWrappers = []interface{}{}
	file_data_fetching_api_proto_msgTypes[62].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_data_fetching_api_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   72,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_data_fetching_api_proto_goTypes,
		DependencyIndexes: file_data_fetching_api_proto_depIdxs,
		EnumInfos:         file_data_fetching_api_proto_enumTypes,
		MessageInfos:      file_data_fetching_api_proto_msgTypes,
	}.Build()
	File_data_fetching_api_proto = out.File
	file_data_fetching_api_proto_rawDesc = nil
	file_data_fetching_api_proto_goTypes = nil
	file_data_fetching_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// FetcherClient is the client API for Fetcher service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FetcherClient interface {
	GetMVRReportV1(ctx context.Context, in *MVRReportRequestV1, opts ...grpc.CallOption) (*MVRReportV1, error)
	GetMVRAttractScoreV1(ctx context.Context, in *MVRAttractScoreRequestV1, opts ...grpc.CallOption) (*MVRAttractScoreV1, error)
	GetVINDetailsV1(ctx context.Context, in *VINDetailsRequestV1, opts ...grpc.CallOption) (*VINDetailsV1, error)
	GetNationalCreditFileV1(ctx context.Context, in *NationalCreditFileRequestV1, opts ...grpc.CallOption) (*NationalCreditFileV1, error)
	GetGrantedAuthorityHistoryV1(ctx context.Context, in *GrantedAuthorityHistoryRequestV1, opts ...grpc.CallOption) (*GrantedAuthorityHistoryV1, error)
	GetBIPDInsuranceHistoryV1(ctx context.Context, in *BIPDInsuranceHistoryRequestV1, opts ...grpc.CallOption) (*BIPDInsuranceHistoryV1, error)
	GetBIPDActiveOrPendingInsuranceV1(ctx context.Context, in *BIPDActiveOrPendingInsuranceRequestV1, opts ...grpc.CallOption) (*BIPDActiveOrPendingInsuranceV1, error)
	GetLatestRatingTierRecordsDumpDateV1(ctx context.Context, in *LatestRatingTiersDumpDateRequestV1, opts ...grpc.CallOption) (*LatestRatingTiersDumpDateV1, error)
	GetRatingTierRecordsForDumpDateV1(ctx context.Context, in *RatingTierRecordsForDumpDateRequestV1, opts ...grpc.CallOption) (*RatingTierRecordsForDumpDateV1, error)
	GetRatingTierRecordV1(ctx context.Context, in *RatingTierRecordRequestV1, opts ...grpc.CallOption) (*RatingTierRecordV1, error)
	GetFMCSAViolationRecordsV1(ctx context.Context, in *FMCSAViolationRecordsRequestV1, opts ...grpc.CallOption) (*FMCSAViolationRecordsV1, error)
	GetFMCSAInspectionRecordsV1(ctx context.Context, in *FMCSAInspectionRecordsRequestV1, opts ...grpc.CallOption) (*FMCSAInspectionRecordsV1, error)
	GetFMCSACensusInfoV1(ctx context.Context, in *FMCSACensusInfoRequestV1, opts ...grpc.CallOption) (*FMCSACensusInfoV1, error)
	GetLatestObjectiveGradeV1(ctx context.Context, in *LatestObjectiveGradeRequestV1, opts ...grpc.CallOption) (*ObjectiveGradeV1, error)
	GetInvoluntaryRevocationsV1(ctx context.Context, in *InvoluntaryRevocationsRequestV1, opts ...grpc.CallOption) (*InvoluntaryRevocationsV1, error)
	GetNirvanaPoliciesV1(ctx context.Context, in *GetNirvanaPoliciesRequestV1, opts ...grpc.CallOption) (*GetNirvanaPoliciesResponseV1, error)
}

type fetcherClient struct {
	cc grpc.ClientConnInterface
}

func NewFetcherClient(cc grpc.ClientConnInterface) FetcherClient {
	return &fetcherClient{cc}
}

func (c *fetcherClient) GetMVRReportV1(ctx context.Context, in *MVRReportRequestV1, opts ...grpc.CallOption) (*MVRReportV1, error) {
	out := new(MVRReportV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetMVRReportV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetMVRAttractScoreV1(ctx context.Context, in *MVRAttractScoreRequestV1, opts ...grpc.CallOption) (*MVRAttractScoreV1, error) {
	out := new(MVRAttractScoreV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetMVRAttractScoreV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetVINDetailsV1(ctx context.Context, in *VINDetailsRequestV1, opts ...grpc.CallOption) (*VINDetailsV1, error) {
	out := new(VINDetailsV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetVINDetailsV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetNationalCreditFileV1(ctx context.Context, in *NationalCreditFileRequestV1, opts ...grpc.CallOption) (*NationalCreditFileV1, error) {
	out := new(NationalCreditFileV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetNationalCreditFileV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetGrantedAuthorityHistoryV1(ctx context.Context, in *GrantedAuthorityHistoryRequestV1, opts ...grpc.CallOption) (*GrantedAuthorityHistoryV1, error) {
	out := new(GrantedAuthorityHistoryV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetGrantedAuthorityHistoryV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetBIPDInsuranceHistoryV1(ctx context.Context, in *BIPDInsuranceHistoryRequestV1, opts ...grpc.CallOption) (*BIPDInsuranceHistoryV1, error) {
	out := new(BIPDInsuranceHistoryV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetBIPDInsuranceHistoryV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetBIPDActiveOrPendingInsuranceV1(ctx context.Context, in *BIPDActiveOrPendingInsuranceRequestV1, opts ...grpc.CallOption) (*BIPDActiveOrPendingInsuranceV1, error) {
	out := new(BIPDActiveOrPendingInsuranceV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetBIPDActiveOrPendingInsuranceV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetLatestRatingTierRecordsDumpDateV1(ctx context.Context, in *LatestRatingTiersDumpDateRequestV1, opts ...grpc.CallOption) (*LatestRatingTiersDumpDateV1, error) {
	out := new(LatestRatingTiersDumpDateV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetLatestRatingTierRecordsDumpDateV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetRatingTierRecordsForDumpDateV1(ctx context.Context, in *RatingTierRecordsForDumpDateRequestV1, opts ...grpc.CallOption) (*RatingTierRecordsForDumpDateV1, error) {
	out := new(RatingTierRecordsForDumpDateV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetRatingTierRecordsForDumpDateV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetRatingTierRecordV1(ctx context.Context, in *RatingTierRecordRequestV1, opts ...grpc.CallOption) (*RatingTierRecordV1, error) {
	out := new(RatingTierRecordV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetRatingTierRecordV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetFMCSAViolationRecordsV1(ctx context.Context, in *FMCSAViolationRecordsRequestV1, opts ...grpc.CallOption) (*FMCSAViolationRecordsV1, error) {
	out := new(FMCSAViolationRecordsV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetFMCSAViolationRecordsV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetFMCSAInspectionRecordsV1(ctx context.Context, in *FMCSAInspectionRecordsRequestV1, opts ...grpc.CallOption) (*FMCSAInspectionRecordsV1, error) {
	out := new(FMCSAInspectionRecordsV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetFMCSAInspectionRecordsV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetFMCSACensusInfoV1(ctx context.Context, in *FMCSACensusInfoRequestV1, opts ...grpc.CallOption) (*FMCSACensusInfoV1, error) {
	out := new(FMCSACensusInfoV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetFMCSACensusInfoV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetLatestObjectiveGradeV1(ctx context.Context, in *LatestObjectiveGradeRequestV1, opts ...grpc.CallOption) (*ObjectiveGradeV1, error) {
	out := new(ObjectiveGradeV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetLatestObjectiveGradeV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetInvoluntaryRevocationsV1(ctx context.Context, in *InvoluntaryRevocationsRequestV1, opts ...grpc.CallOption) (*InvoluntaryRevocationsV1, error) {
	out := new(InvoluntaryRevocationsV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetInvoluntaryRevocationsV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fetcherClient) GetNirvanaPoliciesV1(ctx context.Context, in *GetNirvanaPoliciesRequestV1, opts ...grpc.CallOption) (*GetNirvanaPoliciesResponseV1, error) {
	out := new(GetNirvanaPoliciesResponseV1)
	err := c.cc.Invoke(ctx, "/data_fetching.Fetcher/GetNirvanaPoliciesV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FetcherServer is the server API for Fetcher service.
type FetcherServer interface {
	GetMVRReportV1(context.Context, *MVRReportRequestV1) (*MVRReportV1, error)
	GetMVRAttractScoreV1(context.Context, *MVRAttractScoreRequestV1) (*MVRAttractScoreV1, error)
	GetVINDetailsV1(context.Context, *VINDetailsRequestV1) (*VINDetailsV1, error)
	GetNationalCreditFileV1(context.Context, *NationalCreditFileRequestV1) (*NationalCreditFileV1, error)
	GetGrantedAuthorityHistoryV1(context.Context, *GrantedAuthorityHistoryRequestV1) (*GrantedAuthorityHistoryV1, error)
	GetBIPDInsuranceHistoryV1(context.Context, *BIPDInsuranceHistoryRequestV1) (*BIPDInsuranceHistoryV1, error)
	GetBIPDActiveOrPendingInsuranceV1(context.Context, *BIPDActiveOrPendingInsuranceRequestV1) (*BIPDActiveOrPendingInsuranceV1, error)
	GetLatestRatingTierRecordsDumpDateV1(context.Context, *LatestRatingTiersDumpDateRequestV1) (*LatestRatingTiersDumpDateV1, error)
	GetRatingTierRecordsForDumpDateV1(context.Context, *RatingTierRecordsForDumpDateRequestV1) (*RatingTierRecordsForDumpDateV1, error)
	GetRatingTierRecordV1(context.Context, *RatingTierRecordRequestV1) (*RatingTierRecordV1, error)
	GetFMCSAViolationRecordsV1(context.Context, *FMCSAViolationRecordsRequestV1) (*FMCSAViolationRecordsV1, error)
	GetFMCSAInspectionRecordsV1(context.Context, *FMCSAInspectionRecordsRequestV1) (*FMCSAInspectionRecordsV1, error)
	GetFMCSACensusInfoV1(context.Context, *FMCSACensusInfoRequestV1) (*FMCSACensusInfoV1, error)
	GetLatestObjectiveGradeV1(context.Context, *LatestObjectiveGradeRequestV1) (*ObjectiveGradeV1, error)
	GetInvoluntaryRevocationsV1(context.Context, *InvoluntaryRevocationsRequestV1) (*InvoluntaryRevocationsV1, error)
	GetNirvanaPoliciesV1(context.Context, *GetNirvanaPoliciesRequestV1) (*GetNirvanaPoliciesResponseV1, error)
}

// UnimplementedFetcherServer can be embedded to have forward compatible implementations.
type UnimplementedFetcherServer struct {
}

func (*UnimplementedFetcherServer) GetMVRReportV1(context.Context, *MVRReportRequestV1) (*MVRReportV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMVRReportV1 not implemented")
}
func (*UnimplementedFetcherServer) GetMVRAttractScoreV1(context.Context, *MVRAttractScoreRequestV1) (*MVRAttractScoreV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMVRAttractScoreV1 not implemented")
}
func (*UnimplementedFetcherServer) GetVINDetailsV1(context.Context, *VINDetailsRequestV1) (*VINDetailsV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVINDetailsV1 not implemented")
}
func (*UnimplementedFetcherServer) GetNationalCreditFileV1(context.Context, *NationalCreditFileRequestV1) (*NationalCreditFileV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNationalCreditFileV1 not implemented")
}
func (*UnimplementedFetcherServer) GetGrantedAuthorityHistoryV1(context.Context, *GrantedAuthorityHistoryRequestV1) (*GrantedAuthorityHistoryV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGrantedAuthorityHistoryV1 not implemented")
}
func (*UnimplementedFetcherServer) GetBIPDInsuranceHistoryV1(context.Context, *BIPDInsuranceHistoryRequestV1) (*BIPDInsuranceHistoryV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBIPDInsuranceHistoryV1 not implemented")
}
func (*UnimplementedFetcherServer) GetBIPDActiveOrPendingInsuranceV1(context.Context, *BIPDActiveOrPendingInsuranceRequestV1) (*BIPDActiveOrPendingInsuranceV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBIPDActiveOrPendingInsuranceV1 not implemented")
}
func (*UnimplementedFetcherServer) GetLatestRatingTierRecordsDumpDateV1(context.Context, *LatestRatingTiersDumpDateRequestV1) (*LatestRatingTiersDumpDateV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestRatingTierRecordsDumpDateV1 not implemented")
}
func (*UnimplementedFetcherServer) GetRatingTierRecordsForDumpDateV1(context.Context, *RatingTierRecordsForDumpDateRequestV1) (*RatingTierRecordsForDumpDateV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRatingTierRecordsForDumpDateV1 not implemented")
}
func (*UnimplementedFetcherServer) GetRatingTierRecordV1(context.Context, *RatingTierRecordRequestV1) (*RatingTierRecordV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRatingTierRecordV1 not implemented")
}
func (*UnimplementedFetcherServer) GetFMCSAViolationRecordsV1(context.Context, *FMCSAViolationRecordsRequestV1) (*FMCSAViolationRecordsV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFMCSAViolationRecordsV1 not implemented")
}
func (*UnimplementedFetcherServer) GetFMCSAInspectionRecordsV1(context.Context, *FMCSAInspectionRecordsRequestV1) (*FMCSAInspectionRecordsV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFMCSAInspectionRecordsV1 not implemented")
}
func (*UnimplementedFetcherServer) GetFMCSACensusInfoV1(context.Context, *FMCSACensusInfoRequestV1) (*FMCSACensusInfoV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFMCSACensusInfoV1 not implemented")
}
func (*UnimplementedFetcherServer) GetLatestObjectiveGradeV1(context.Context, *LatestObjectiveGradeRequestV1) (*ObjectiveGradeV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestObjectiveGradeV1 not implemented")
}
func (*UnimplementedFetcherServer) GetInvoluntaryRevocationsV1(context.Context, *InvoluntaryRevocationsRequestV1) (*InvoluntaryRevocationsV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvoluntaryRevocationsV1 not implemented")
}
func (*UnimplementedFetcherServer) GetNirvanaPoliciesV1(context.Context, *GetNirvanaPoliciesRequestV1) (*GetNirvanaPoliciesResponseV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNirvanaPoliciesV1 not implemented")
}

func RegisterFetcherServer(s *grpc.Server, srv FetcherServer) {
	s.RegisterService(&_Fetcher_serviceDesc, srv)
}

func _Fetcher_GetMVRReportV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MVRReportRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetMVRReportV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetMVRReportV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetMVRReportV1(ctx, req.(*MVRReportRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetMVRAttractScoreV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MVRAttractScoreRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetMVRAttractScoreV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetMVRAttractScoreV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetMVRAttractScoreV1(ctx, req.(*MVRAttractScoreRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetVINDetailsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VINDetailsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetVINDetailsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetVINDetailsV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetVINDetailsV1(ctx, req.(*VINDetailsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetNationalCreditFileV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NationalCreditFileRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetNationalCreditFileV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetNationalCreditFileV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetNationalCreditFileV1(ctx, req.(*NationalCreditFileRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetGrantedAuthorityHistoryV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantedAuthorityHistoryRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetGrantedAuthorityHistoryV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetGrantedAuthorityHistoryV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetGrantedAuthorityHistoryV1(ctx, req.(*GrantedAuthorityHistoryRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetBIPDInsuranceHistoryV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BIPDInsuranceHistoryRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetBIPDInsuranceHistoryV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetBIPDInsuranceHistoryV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetBIPDInsuranceHistoryV1(ctx, req.(*BIPDInsuranceHistoryRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetBIPDActiveOrPendingInsuranceV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BIPDActiveOrPendingInsuranceRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetBIPDActiveOrPendingInsuranceV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetBIPDActiveOrPendingInsuranceV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetBIPDActiveOrPendingInsuranceV1(ctx, req.(*BIPDActiveOrPendingInsuranceRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetLatestRatingTierRecordsDumpDateV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LatestRatingTiersDumpDateRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetLatestRatingTierRecordsDumpDateV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetLatestRatingTierRecordsDumpDateV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetLatestRatingTierRecordsDumpDateV1(ctx, req.(*LatestRatingTiersDumpDateRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetRatingTierRecordsForDumpDateV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RatingTierRecordsForDumpDateRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetRatingTierRecordsForDumpDateV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetRatingTierRecordsForDumpDateV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetRatingTierRecordsForDumpDateV1(ctx, req.(*RatingTierRecordsForDumpDateRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetRatingTierRecordV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RatingTierRecordRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetRatingTierRecordV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetRatingTierRecordV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetRatingTierRecordV1(ctx, req.(*RatingTierRecordRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetFMCSAViolationRecordsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FMCSAViolationRecordsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetFMCSAViolationRecordsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetFMCSAViolationRecordsV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetFMCSAViolationRecordsV1(ctx, req.(*FMCSAViolationRecordsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetFMCSAInspectionRecordsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FMCSAInspectionRecordsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetFMCSAInspectionRecordsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetFMCSAInspectionRecordsV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetFMCSAInspectionRecordsV1(ctx, req.(*FMCSAInspectionRecordsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetFMCSACensusInfoV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FMCSACensusInfoRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetFMCSACensusInfoV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetFMCSACensusInfoV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetFMCSACensusInfoV1(ctx, req.(*FMCSACensusInfoRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetLatestObjectiveGradeV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LatestObjectiveGradeRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetLatestObjectiveGradeV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetLatestObjectiveGradeV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetLatestObjectiveGradeV1(ctx, req.(*LatestObjectiveGradeRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetInvoluntaryRevocationsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvoluntaryRevocationsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetInvoluntaryRevocationsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetInvoluntaryRevocationsV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetInvoluntaryRevocationsV1(ctx, req.(*InvoluntaryRevocationsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fetcher_GetNirvanaPoliciesV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNirvanaPoliciesRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FetcherServer).GetNirvanaPoliciesV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_fetching.Fetcher/GetNirvanaPoliciesV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FetcherServer).GetNirvanaPoliciesV1(ctx, req.(*GetNirvanaPoliciesRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

var _Fetcher_serviceDesc = grpc.ServiceDesc{
	ServiceName: "data_fetching.Fetcher",
	HandlerType: (*FetcherServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMVRReportV1",
			Handler:    _Fetcher_GetMVRReportV1_Handler,
		},
		{
			MethodName: "GetMVRAttractScoreV1",
			Handler:    _Fetcher_GetMVRAttractScoreV1_Handler,
		},
		{
			MethodName: "GetVINDetailsV1",
			Handler:    _Fetcher_GetVINDetailsV1_Handler,
		},
		{
			MethodName: "GetNationalCreditFileV1",
			Handler:    _Fetcher_GetNationalCreditFileV1_Handler,
		},
		{
			MethodName: "GetGrantedAuthorityHistoryV1",
			Handler:    _Fetcher_GetGrantedAuthorityHistoryV1_Handler,
		},
		{
			MethodName: "GetBIPDInsuranceHistoryV1",
			Handler:    _Fetcher_GetBIPDInsuranceHistoryV1_Handler,
		},
		{
			MethodName: "GetBIPDActiveOrPendingInsuranceV1",
			Handler:    _Fetcher_GetBIPDActiveOrPendingInsuranceV1_Handler,
		},
		{
			MethodName: "GetLatestRatingTierRecordsDumpDateV1",
			Handler:    _Fetcher_GetLatestRatingTierRecordsDumpDateV1_Handler,
		},
		{
			MethodName: "GetRatingTierRecordsForDumpDateV1",
			Handler:    _Fetcher_GetRatingTierRecordsForDumpDateV1_Handler,
		},
		{
			MethodName: "GetRatingTierRecordV1",
			Handler:    _Fetcher_GetRatingTierRecordV1_Handler,
		},
		{
			MethodName: "GetFMCSAViolationRecordsV1",
			Handler:    _Fetcher_GetFMCSAViolationRecordsV1_Handler,
		},
		{
			MethodName: "GetFMCSAInspectionRecordsV1",
			Handler:    _Fetcher_GetFMCSAInspectionRecordsV1_Handler,
		},
		{
			MethodName: "GetFMCSACensusInfoV1",
			Handler:    _Fetcher_GetFMCSACensusInfoV1_Handler,
		},
		{
			MethodName: "GetLatestObjectiveGradeV1",
			Handler:    _Fetcher_GetLatestObjectiveGradeV1_Handler,
		},
		{
			MethodName: "GetInvoluntaryRevocationsV1",
			Handler:    _Fetcher_GetInvoluntaryRevocationsV1_Handler,
		},
		{
			MethodName: "GetNirvanaPoliciesV1",
			Handler:    _Fetcher_GetNirvanaPoliciesV1_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "data_fetching/api.proto",
}
