package data_fetching

import (
	"fmt"

	"github.com/cockroachdb/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov/active_pending_insurance"
	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov/authority_history"
	"nirvanatech.com/nirvana/db-api/db_wrappers/datagov/insurance_history"
	policy_db "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/external_data_management/mvr"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
	nhtsaEnums "nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
	"nirvanatech.com/nirvana/fmcsa/basic"
	cargo_types "nirvanatech.com/nirvana/fmcsa/dot_details/enums"
	"nirvanatech.com/nirvana/fmcsa/models"
	objetive_grade_models "nirvanatech.com/nirvana/safety/scores/objective_grade/models"
)

func transformMVRReportFromClientFormatToInternalFormatV1(mvrReport *mvr.Report) *MVRReportV1 {
	var violations []*MVRViolationV1
	for _, violation := range mvrReport.Violations {
		violations = append(violations, transformMVRViolationFromClientFormatToInternalFormatV1(violation))
	}

	return &MVRReportV1{
		Violations:           violations,
		ReportID:             mvrReport.ReportID,
		DlState:              mvrReport.DlState,
		ReportSequenceNumber: mvrReport.ReportSequenceNumber,
		DriverName:           mvrReport.DriverName,
		DriverStreetAddr:     mvrReport.DriverStreetAddr,
		MvrStatus:            mvrReport.MvrStatus,
		ViolationCoding:      mvrReport.ViolationCoding,
		ViolationCodeTotal:   mvrReport.ViolationCodeTotal,
		MvrFormat:            mvrReport.MvrFormat,
		DriverCityStateZip:   mvrReport.DriverCityStateZip,
		DlNumber:             mvrReport.DlNumber,
		ClientCode:           mvrReport.ClientCode,
		ArchiveFlag:          mvrReport.ArchiveFlag,
		Ssn:                  mvrReport.Ssn,
		DppaFlag:             mvrReport.DppaFlag,
		DmvAccountNumber:     mvrReport.DmvAccountNumber,
		Dob:                  mvrReport.Dob,
		Gender:               mvrReport.Gender,
		Height:               mvrReport.Height,
		Weight:               mvrReport.Weight,
		EyeColor:             mvrReport.EyeColor,
		HairColor:            mvrReport.HairColor,
		LicClass:             mvrReport.LicClass,
		LicStatus:            mvrReport.LicStatus,
		DateIssued:           mvrReport.DateIssued,
		DateExpires:          mvrReport.DateExpires,
		MvrReportDate:        mvrReport.MvrReportDate,
		Restrictions:         mvrReport.Restrictions,
		MiscDetail:           mvrReport.MiscDetail,
		RequestDlNumber:      mvrReport.RequestDlNumber,
		RequestState:         mvrReport.RequestState,
	}
}

func transformNationalCreditReportFromClientFormatToInternalFormatV1(report *mvr.GetNationalCreditFileResponse) *NationalCreditFileV1 {
	if report == nil || report.TransactionDetails == nil {
		return &NationalCreditFileV1{
			NcfReport:          nil,
			TransactionDetails: nil,
		}
	}

	transactionDetails := &TransactionDetailsExV1{
		ProcessingStatus: report.TransactionDetails.ProcessingStatus,
	}
	if report.NcfReport == nil || report.NcfReport.NcfProductReport == nil {
		return &NationalCreditFileV1{
			NcfReport:          nil,
			TransactionDetails: transactionDetails,
		}
	}
	subjectInfo := report.NcfReport.NcfProductReport.SubjectInfo
	creditReportSummary := report.NcfReport.NcfProductReport.CreditReportSummary
	employmentInfos := report.NcfReport.NcfProductReport.EmploymentInfos
	collectionRecordsInfo := report.NcfReport.NcfProductReport.CollectionRecordsInfo
	tradeAccountInfo := report.NcfReport.NcfProductReport.TradeAccountInfo

	return &NationalCreditFileV1{
		NcfReport: &NcfReportV1{
			NcfProductReport: &NcfProductReportV1{
				SubjectInfo:         transformSubjectInfoToInternalFormatV1(subjectInfo),
				CreditReportSummary: transformCreditReportSummaryToInternalFormatV1(creditReportSummary),
				EmploymentInfos: &EmploymentInfosV1{
					EmploymentInfo: transformEmploymentInfoToInternalFormatV1(employmentInfos),
				},
				CollectionRecordsInfo: &CollectionRecordsInfoV1{
					CollectionRecords: &CollectionRecordsV1{
						CollectionRecord: transformCollectionRecordsToInternalFormatV1(collectionRecordsInfo),
					},
				},
				TradeAccountInfo: &TradeAccountInfoV1{
					CreditTradeHistoryRecords: &CreditTradeHistoryRecordsV1{
						CreditTradeHistoryRecord: transformCreditTradeHistoryRecordToInternalFormatV1(tradeAccountInfo),
					},
				},
				InquiryHistoryHeader: &InquiryHistoryHeaderV1{
					InquiryHistoryHeaderRecords: &InquiryHistoryHeaderRecordsV1{
						InquiryHistoryHeaderRecord: transformInquiryHistoryHeaderRecordRecordToInternalFormatV1(report),
					},
				},
			},
		},
		TransactionDetails: transactionDetails,
	}
}

func transformMVRViolationFromClientFormatToInternalFormatV1(violation *mvr.Violation) *MVRViolationV1 {
	return &MVRViolationV1{
		ViolationType:         violation.ViolationType,
		ViolationDate:         violation.ViolationDate,
		ConvictionDate:        violation.ConvictionDate,
		ViolationCode:         violation.ViolationCode,
		Points:                violation.Points,
		AssignedViolationCode: violation.AssignedViolationCode,
		AssignedPoints:        violation.AssignedPoints,
		ViolationDetail:       violation.ViolationDetail,
	}
}

func transformMVRReportRequestFromInternalFormatToClientFormatV1(request *MVRReportRequestV1) *mvr.FetchMVRRequest {
	return &mvr.FetchMVRRequest{
		DlNumber:           request.DlNumber,
		UsState:            request.UsState,
		ApplicationID:      request.ApplicationID,
		OnlyFetchFromCache: request.OnlyFetchFromCache,
		FirstName:          request.FirstName,
		LastName:           request.LastName,
		MiddleName:         request.MiddleName,
		Dob:                request.Dob,
		Staleness:          request.Staleness,
	}
}

func transformMVRAttractScoreRequestFromInternalFormatToClientFormatV1(
	request *MVRAttractScoreRequestV1,
) *mvr.GetAttractScoreRequest {
	return &mvr.GetAttractScoreRequest{
		DlNumber:      request.DlNumber,
		UsState:       request.UsState,
		ApplicationID: request.ApplicationID,
		FirstName:     request.FirstName,
		LastName:      request.LastName,
		MiddleName:    request.MiddleName,
		Dob:           request.Dob,
		Staleness:     request.Staleness,
	}
}

func transformVINDetailsFromClientFormatToInternalFormatV1(details *nhtsa.VINDetails) *VINDetailsV1 {
	return &VINDetailsV1{
		Make:           details.Make,
		Model:          details.Model,
		ModelYear:      details.ModelYear,
		Manufacturer:   details.Manufacturer,
		Trim:           details.Trim,
		RawVehicleType: details.RawVehicleType,
		BodyClass:      details.BodyClass,
		VehicleType:    transformVehicleTypeFromClientFormatToInternalFormatV1(details.Type),
		WeightClass:    transformWeightClassFromClientFormatToInternalFormatV1(details.WeightClass),
		DecodeError:    transformDecodeErrorFromClientFormatToInternalFormatV1(details.DecodeError),
	}
}

func transformVehicleTypeFromClientFormatToInternalFormatV1(vehicleType nhtsaEnums.VehicleType) VehicleTypeV1 {
	switch vehicleType {
	case nhtsaEnums.VehicleTypeIncompleteVehicle:
		return VehicleTypeV1_VehicleTypeV1_IncompleteVehicle
	case nhtsaEnums.VehicleTypeMotorcycle:
		return VehicleTypeV1_VehicleTypeV1_Motorcycle
	case nhtsaEnums.VehicleTypeTrailer:
		return VehicleTypeV1_VehicleTypeV1_Trailer
	case nhtsaEnums.VehicleTypeTruck:
		return VehicleTypeV1_VehicleTypeV1_Truck
	case nhtsaEnums.VehicleTypePassengerCar:
		return VehicleTypeV1_VehicleTypeV1_PassengerCar
	case nhtsaEnums.VehicleTypeMultipurposePassengerVehicle:
		return VehicleTypeV1_VehicleTypeV1_MultipurposePassengerVehicle
	case nhtsaEnums.VehicleTypeBus:
		return VehicleTypeV1_VehicleTypeV1_Bus
	case nhtsaEnums.VehicleTypeNil:
		return VehicleTypeV1_VehicleTypeV1_Nil
	default:
		panic(fmt.Sprintf("Unknown vehicle type %s", vehicleType.String()))
	}
}

func transformWeightClassFromClientFormatToInternalFormatV1(weightClass nhtsaEnums.WeightClass) WeightClassV1 {
	switch weightClass {
	case nhtsaEnums.WeightClassA:
		return WeightClassV1_WeightClassV1_A
	case nhtsaEnums.WeightClassB:
		return WeightClassV1_WeightClassV1_B
	case nhtsaEnums.WeightClassC:
		return WeightClassV1_WeightClassV1_C
	case nhtsaEnums.WeightClassD:
		return WeightClassV1_WeightClassV1_D
	case nhtsaEnums.WeightClassE:
		return WeightClassV1_WeightClassV1_E
	case nhtsaEnums.WeightClassF:
		return WeightClassV1_WeightClassV1_F
	case nhtsaEnums.WeightClassG:
		return WeightClassV1_WeightClassV1_G
	case nhtsaEnums.WeightClassH:
		return WeightClassV1_WeightClassV1_H
	case nhtsaEnums.WeightClass3:
		return WeightClassV1_WeightClassV1_3
	case nhtsaEnums.WeightClass4:
		return WeightClassV1_WeightClassV1_4
	case nhtsaEnums.WeightClass5:
		return WeightClassV1_WeightClassV1_5
	case nhtsaEnums.WeightClass6:
		return WeightClassV1_WeightClassV1_6
	case nhtsaEnums.WeightClass7:
		return WeightClassV1_WeightClassV1_7
	case nhtsaEnums.WeightClass8:
		return WeightClassV1_WeightClassV1_8
	case nhtsaEnums.WeightClassNil:
		return WeightClassV1_WeightClassV1_Nil
	default:
		panic(fmt.Sprintf("Unknown weight class %s", weightClass.String()))
	}
}

func transformDecodeErrorFromClientFormatToInternalFormatV1(decodeErr *nhtsa.VinDecodeError) *VINDecodeErrorV1 {
	if decodeErr == nil {
		return nil
	}

	errorCodes := slice_utils.Map(decodeErr.ErrorCodes, func(errorCode nhtsaEnums.ErrorCode) NhtsaErrorCodeV1 {
		switch errorCode {
		case nhtsaEnums.ErrorCode0:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_0
		case nhtsaEnums.ErrorCode1:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_1
		case nhtsaEnums.ErrorCode2:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_2
		case nhtsaEnums.ErrorCode3:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_3
		case nhtsaEnums.ErrorCode4:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_4
		case nhtsaEnums.ErrorCode5:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_5
		case nhtsaEnums.ErrorCode6:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_6
		case nhtsaEnums.ErrorCode7:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_7
		case nhtsaEnums.ErrorCode8:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_8
		case nhtsaEnums.ErrorCode9:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_9
		case nhtsaEnums.ErrorCode10:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_10
		case nhtsaEnums.ErrorCode11:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_11
		case nhtsaEnums.ErrorCode12:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_12
		case nhtsaEnums.ErrorCode14:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_14
		case nhtsaEnums.ErrorCode400:
			return NhtsaErrorCodeV1_NhtsaErrorCodeV1_400
		default:
			panic(fmt.Sprintf("Unknown error code %s", errorCode.String()))
		}
	})

	return &VINDecodeErrorV1{
		NhtsaErrorCodes: errorCodes,
		ErrText:         decodeErr.ErrText,
	}
}

func transformInquiryHistoryHeaderRecordRecordToInternalFormatV1(report *mvr.GetNationalCreditFileResponse) []*InquiryHistoryHeaderRecordV1 {
	var inquiryHistoryHeaderRecords []*InquiryHistoryHeaderRecordV1
	for _, ir := range report.NcfReport.NcfProductReport.InquiryHistoryHeader.InquiryHistoryHeaderRecords.InquiryHistoryHeaderRecord {
		inquiryHistoryHeaderRecords = append(inquiryHistoryHeaderRecords, &InquiryHistoryHeaderRecordV1{
			DateOfInquiry: &DateOfInquiryV1{
				Year:  ir.DateOfInquiry.Year,
				Month: ir.DateOfInquiry.Month,
				Day:   ir.DateOfInquiry.Day,
			},
			InquirerName: ir.InquirerName,
			InquirerId:   ir.InquirerId,
		})
	}
	return inquiryHistoryHeaderRecords
}

func transformCreditTradeHistoryRecordToInternalFormatV1(tradeAccountInfo *mvr.TradeAccountInfo) []*CreditTradeHistoryRecordV1 {
	var creditTradeHistoryRecords []*CreditTradeHistoryRecordV1
	for _, cr := range tradeAccountInfo.CreditTradeHistoryRecords.CreditTradeHistoryRecord {
		var messages []*MessageV1
		for _, message := range cr.Messages.Message {
			messages = append(messages, &MessageV1{
				Code:    message.Code,
				Message: message.Message,
			})
		}
		creditTradeHistoryRecords = append(creditTradeHistoryRecords, &CreditTradeHistoryRecordV1{
			ReportingMemberNumber: cr.ReportingMemberNumber,
			MemberName:            cr.MemberName,
			TapeSupplierIndicator: cr.TapeSupplierIndicator,
			DateReported:          transformDateFirstAtAddressFormatToInternalFormatV1(cr.DateReported),
			DateAccountOpened:     transformDateFirstAtAddressFormatToInternalFormatV1(cr.DateAccountOpened),
			HighestCreditAmount:   cr.HighestCreditAmount,
			AccountBalance:        cr.AccountBalance,
			PastDueAmount:         cr.PastDueAmount,
			AccountTypeCode:       cr.AccountTypeCode,
			CurrentRateCode:       cr.CurrentRateCode,
			MonthsReviewed:        cr.MonthsReviewed,
			AccountDesignatorCode: cr.AccountDesignatorCode,
			ThirtydayCounter:      cr.ThirtydayCounter,
			SixtydayCounter:       cr.SixtydayCounter,
			NinetydayCounter:      cr.NinetydayCounter,
			PrevRateCode1:         cr.PrevRateCode1,
			PrevRateDate1:         transformDateFirstAtAddressFormatToInternalFormatV1(cr.PrevRateDate1),
			PrevRateCode2:         cr.PrevRateCode2,
			PrevRateDate2:         transformDateFirstAtAddressFormatToInternalFormatV1(cr.PrevRateDate2),
			PrevRateCode3:         cr.PrevRateCode3,
			PrevRateDate3:         transformDateFirstAtAddressFormatToInternalFormatV1(cr.PrevRateDate3),
			DateOfLastActivity:    transformDateFirstAtAddressFormatToInternalFormatV1(cr.DateOfLastActivity),
			Messages: &MessagesListV1{
				Message: messages,
			},
		})
	}
	return creditTradeHistoryRecords
}

func transformEmploymentInfoToInternalFormatV1(employmentInfo *mvr.EmploymentInfos) []*EmploymentInfoV1 {
	var employmentInfos []*EmploymentInfoV1
	for _, e := range employmentInfo.EmploymentInfo {
		employmentInfos = append(employmentInfos, &EmploymentInfoV1{
			PositionDesc:         e.PositionDesc,
			EmployerName:         e.EmployerName,
			EmploymentRecordType: e.EmploymentRecordType,
			Classification:       e.Classification,
		})
	}
	return employmentInfos
}

func transformCollectionRecordsToInternalFormatV1(collectionRecordsInfo *mvr.CollectionRecordsInfo) []*CollectionRecordV1 {
	var collectionRecords []*CollectionRecordV1
	for _, cr := range collectionRecordsInfo.CollectionRecords.CollectionRecord {
		collectionRecords = append(collectionRecords, &CollectionRecordV1{
			DateReported:                transformDateFirstAtAddressFormatToInternalFormatV1(cr.DateReported),
			DateAssigned:                transformDateFirstAtAddressFormatToInternalFormatV1(cr.DateAssigned),
			ReportingMemberAgencyNumber: cr.ReportingMemberAgencyNumber,
			ClientNameOrNumber:          cr.ClientNameOrNumber,
			EcoaCode:                    cr.EcoaCode,
			DateOfLastActivity:          transformDateFirstAtAddressFormatToInternalFormatV1(cr.DateOfLastActivity),
			OriginalAmount:              cr.OriginalAmount,
			DateOfBalance:               transformDateFirstAtAddressFormatToInternalFormatV1(cr.DateOfBalance),
			BalanceAmount:               cr.BalanceAmount,
			StatusDate:                  transformDateFirstAtAddressFormatToInternalFormatV1(cr.StatusDate),
			CollectionItemStatus:        cr.CollectionItemStatus,
		})
	}
	return collectionRecords
}

func transformSubjectInfoToInternalFormatV1(subject *mvr.SubjectInfo) *SubjectInfoV1 {
	return &SubjectInfoV1{
		Subject:        transformSubjectToInternalFormatV1(subject.Subject),
		CurrentAddress: transformCurrentAddressToInternalFormatV1(subject.CurrentAddress),
	}
}

func transformCreditReportSummaryToInternalFormatV1(creditReportSummary *mvr.CreditReportSummary) *CreditReportSummaryV1 {
	dateCreditFileEstbedV1 := &DateCreditFileEstbedV1{}
	if creditReportSummary.DateCreditFileEstbed != nil {
		dateCreditFileEstbedV1 = &DateCreditFileEstbedV1{
			Year:  creditReportSummary.DateCreditFileEstbed.Year,
			Month: creditReportSummary.DateCreditFileEstbed.Month,
			Day:   creditReportSummary.DateCreditFileEstbed.Day,
		}
	}
	return &CreditReportSummaryV1{
		DateCreditFileEstbed:        dateCreditFileEstbedV1,
		OldestOpeningDateOfTrade:    transformDateFirstAtAddressFormatToInternalFormatV1(creditReportSummary.OldestOpeningDateOfTrade),
		LatestReportingDateOfTrade:  transformDateFirstAtAddressFormatToInternalFormatV1(creditReportSummary.LatestReportingDateOfTrade),
		DateOfLatestFileActivity:    transformDateFirstAtAddressFormatToInternalFormatV1(creditReportSummary.DateOfLatestFileActivity),
		ReportIncldsCollectionItems: creditReportSummary.ReportIncldsCollectionItems,
		HighCreditRangeLowAmount:    creditReportSummary.HighCreditRangeHighAmount,
		HighCreditRangeHighAmount:   creditReportSummary.HighCreditRangeHighAmount,
		TotalNumberOfTradeLines:     creditReportSummary.TotalNumberOfTradeLines,
		CurrentStatusAccounts: &CurrentStatusAccountsV1{
			CurrentStatusAccount: transformStatusAccountToInternalFormatV1(creditReportSummary.CurrentStatusAccounts.CurrentStatusAccount),
		},
		HistoryStatusAccounts: &HistoryStatusAccountsV1{
			HistoryStatusAccount: transformStatusAccountToInternalFormatV1(creditReportSummary.HistoryStatusAccounts.HistoryStatusAccount),
		},
		HighCreditTotalRevolving:    creditReportSummary.HighCreditTotalRevolving,
		HighCreditOpenended:         creditReportSummary.HighCreditOpenended,
		HighCreditInstallment:       creditReportSummary.HighCreditInstallment,
		AmountOwedTotalRevolving:    creditReportSummary.AmountOwedTotalRevolving,
		AmountOwedTotalOpenended:    creditReportSummary.AmountOwedTotalOpenended,
		AmountOwedTotalInstallment:  creditReportSummary.AmountOwedTotalInstallment,
		PastDueTotalRevolving:       creditReportSummary.PastDueTotalRevolving,
		PastDueTotalOpenended:       creditReportSummary.PastDueTotalOpenended,
		PastDueTotalInstallment:     creditReportSummary.PastDueTotalInstallment,
		NumberOfRevolvingAccounts:   creditReportSummary.NumberOfRevolvingAccounts,
		NumberOfInstallmentAccounts: creditReportSummary.NumberOfInstallmentAccounts,
	}
}

func transformStatusAccountToInternalFormatV1(statusAccounts []*mvr.CurrentStatusAccount,
) []*CurrentStatusAccountV1 {
	var statusAccount []*CurrentStatusAccountV1
	for _, account := range statusAccounts {
		statusAccount = append(statusAccount, &CurrentStatusAccountV1{
			Status:           account.Status,
			NumberOfAccounts: account.NumberOfAccounts,
		})
	}
	return statusAccount
}

func transformCurrentAddressToInternalFormatV1(currentAddress *mvr.CurrentAddress) *CurrentAddressV1 {
	var dateFirstAtAddress *DateFirstAtAddressV1
	if currentAddress.DateFirstAtAddress != nil {
		dateFirstAtAddress = &DateFirstAtAddressV1{
			Year:  currentAddress.DateFirstAtAddress.Year,
			Month: currentAddress.DateFirstAtAddress.Month,
		}
	}

	return &CurrentAddressV1{
		StreetNumber:       currentAddress.StreetNumber,
		StreetName:         currentAddress.StreetName,
		City:               currentAddress.City,
		State:              currentAddress.State,
		Zip5:               currentAddress.Zip5,
		Zip4:               currentAddress.Zip4,
		DateFirstAtAddress: dateFirstAtAddress,
		AddressId:          currentAddress.AddressId,
	}
}

func transformSubjectToInternalFormatV1(subject *mvr.Subject) *SubjectV1 {
	return &SubjectV1{
		Classification: subject.Classification,
		Name: &NameV1{
			First:  subject.Name.First,
			Middle: subject.Name.Middle,
			Last:   subject.Name.Last,
			Suffix: subject.Name.Suffix,
		},
		Ssn: subject.Ssn,
		Dob: &DobV1{
			Year: subject.Dob.Year,
		},
		HeightFeet:          subject.HeightFeet,
		HeightInches:        subject.HeightInches,
		Weight:              subject.Weight,
		RelationshipType:    subject.RelationshipType,
		GroupSequenceNumber: subject.GroupSequenceNumber,
	}
}

func transformDateFirstAtAddressFormatToInternalFormatV1(date *mvr.DateFirstAtAddress) *DateFirstAtAddressV1 {
	if date == nil {
		return nil
	}
	return &DateFirstAtAddressV1{
		Year:  date.Year,
		Month: date.Month,
	}
}

func transformNationalCreditFileRequestFromInternalFormatToClientFormatV1(
	request *NationalCreditFileRequestV1,
) *mvr.GetNationalCreditFileRequest {
	return &mvr.GetNationalCreditFileRequest{
		Dob:        request.Dob,
		FirstName:  request.FirstName,
		LastName:   request.LastName,
		MiddleName: request.MiddleName,
		Address: &mvr.Address{
			Street: request.Address.Street,
			City:   request.Address.City,
			State:  request.Address.State,
			Zip:    request.Address.Zip,
		},
		Staleness:     request.Staleness,
		ApplicationID: request.ApplicationID,
		EncryptedSSN:  request.EncryptedSSN,
		SsnLastFour:   request.SsnLastFour,
	}
}

func transformGrantedAuthorityHistoryFromClientFormatToInternalFormatV1(
	records []authority_history.AuthHistRecord,
) GrantedAuthorityHistoryV1 {
	grantedAuthorityHistoryRecords := slice_utils.Map(
		records,
		transformAuthorityHistoryFromClientFormatToInternalFormatV1,
	)

	return GrantedAuthorityHistoryV1{
		Records: grantedAuthorityHistoryRecords,
	}
}

func transformBIPDInsuranceHistoryFromClientFormatToInternalFormatV1(
	records []insurance_history.InsHistRecord,
) BIPDInsuranceHistoryV1 {
	bipdInsuranceHistoryRecords := slice_utils.Map(
		records,
		func(r insurance_history.InsHistRecord) *InsuranceRecordV1 {
			record := InsuranceRecordV1{
				Id:                     r.Id.String(),
				DocketNumber:           r.DocketNumber,
				DotNumber:              int64(r.DOTNumber),
				Form:                   r.Form,
				BipdFlag:               pointer_utils.ToPointer(r.BIPDFlag),
				InsuranceType:          r.InsuranceType,
				PolicyNumber:           r.PolicyNumber,
				MinCoverageAmount:      pointer_utils.ToPointer(int32(r.MinCoverageAmount)),
				BipdClass:              r.BIPDClass,
				UnderlyingLimit:        r.BIPDUnderlyingLimit,
				MaxCoverageAmount:      r.BIPDMaxCoverageAmount,
				InsuranceCompanyName:   r.InsuranceCompanyName,
				InsuranceCompanyBranch: pointer_utils.ToPointer(r.InsuranceCompanyBranch),
				CreatedAt:              timestamppb.New(r.CreatedAt),
				VersionId:              int32(r.VersionId),
				CancellationDetails: &CancellationV1{
					Method:         r.CancellationMethod,
					Form:           r.CancellationForm,
					SpecificMethod: r.SpecificCancellationMethod,
				},
			}

			if r.EffectiveDate != nil {
				record.EffectiveDate = timestamppb.New(r.EffectiveDate.ToTime())
			}
			if r.CancelEffectiveDate != nil {
				record.CancelEffectiveDate = timestamppb.New(r.CancelEffectiveDate.ToTime())
			}

			return &record
		})

	return BIPDInsuranceHistoryV1{
		Records: bipdInsuranceHistoryRecords,
	}
}

func transformRatingTierRecordFromClientFormatToInternalFormatV1(
	record *models.SentryInputs,
) RatingTierRecordV1 {
	return RatingTierRecordV1{
		Date:                       timestamppb.New(*record.Date),
		InspectionIndicator:        record.InspectionIndicator,
		LargeMachineryIndicator:    record.LargeMachineryIndicator,
		CrashFrequency:             record.CrashFrequency,
		PowerUnits:                 record.PowerUnits,
		AverageMiles:               record.AverageMiles,
		AverageCombinedGrossWeight: record.AverageCombinedGrossWeight,
		MaintenanceViolationsRatio: record.MaintenanceViolationsRatio,
		UnsafeViolationRatio:       record.UnsafeViolationRatio,
		VehicleInspectionRatio:     record.VehicleInspectionsRatio,
	}
}

func transformRatingTierRecordsFromClientFormatToInternalFormatV1(
	records models.SentryInputsList,
) []*RatingTierRecordV1 {
	return slice_utils.Map(records, func(r models.SentryInputs) *RatingTierRecordV1 {
		return pointer_utils.ToPointer(transformRatingTierRecordFromClientFormatToInternalFormatV1(&r))
	})
}

func transformBIPDActiveOrPendingInsuranceFromClientToInternalV1(
	insurance []active_pending_insurance.ActPendInsRecord,
) BIPDActiveOrPendingInsuranceV1 {
	records := slice_utils.Map(insurance, transformBIPDActiveOrPendingInsuranceRecordFromClientToInternalV1)
	return BIPDActiveOrPendingInsuranceV1{
		Records: records,
	}
}

func transformBIPDActiveOrPendingInsuranceRecordFromClientToInternalV1(
	insuranceRecord active_pending_insurance.ActPendInsRecord,
) *InsuranceRecordV1 {
	var cancelTimestamp *timestamppb.Timestamp
	if insuranceRecord.CancelEffectiveDate != nil {
		cancelTimestamp = timestamppb.New(insuranceRecord.CancelEffectiveDate.ToTime())
	}

	return &InsuranceRecordV1{
		Id:                   insuranceRecord.Id.String(),
		DocketNumber:         insuranceRecord.DocketNumber,
		DotNumber:            int64(insuranceRecord.DOTNumber),
		Form:                 insuranceRecord.Form,
		InsuranceType:        pointer_utils.ToPointer(insuranceRecord.InsuranceType),
		InsuranceCompanyName: insuranceRecord.InsuranceCompanyName,
		PolicyNumber:         insuranceRecord.PolicyNumber,
		PostedDate:           timestamppb.New(insuranceRecord.PostedDate.ToTime()),
		UnderlyingLimit:      pointer_utils.ToPointer(float64(insuranceRecord.BIPDUnderlyingLimit)),
		MaxCoverageAmount:    pointer_utils.ToPointer(float64(insuranceRecord.BIPDMaxLimit)),
		EffectiveDate:        timestamppb.New(insuranceRecord.EffectiveDate.ToTime()),
		CancelEffectiveDate:  cancelTimestamp,
		CreatedAt:            timestamppb.New(insuranceRecord.CreatedAt),
		VersionId:            int32(insuranceRecord.VersionId),
	}
}

func transformFMCSAViolationRecordsFromClientFormatToInternalFormatV1(
	records models.ViolationRecords,
) []*FMCSAViolationRecordV1 {
	transformedRecords := make([]*FMCSAViolationRecordV1, 0)
	for _, record := range records {
		transformedRecord := &FMCSAViolationRecordV1{
			RowId:               record.RowID.String(),
			PublishedDate:       timestamppb.New(record.PublishedDate),
			InspectionId:        record.InspectionID,
			InspectionDate:      timestamppb.New(record.InspectionDate),
			DotNumber:           record.DOTNumber,
			Code:                record.Code,
			Category:            transformFMCSAViolationCategoryFromClientFormatToInternalFormatV1(record.Category),
			OosIndicator:        record.OOSIndicator,
			OosWeight:           int32(record.OOSWeight),
			SeverityWeight:      int32(record.SeverityWeight),
			TotalSeverityWeight: int32(record.TotalSeverityWeight),
			TimeWeight:          int32(record.TimeWeight),
			ViolationId:         convertIntToInt32Ptr(record.ViolationID),
			CountyCode:          record.CountyCode,
			CountyCodeState:     record.CountyCodeState,
			Vins:                record.VINs,
		}
		transformedRecords = append(transformedRecords, transformedRecord)
	}

	return transformedRecords
}

func transformFMCSAInspectionRecordsFromClientFormatToInternalFormatV1(
	records models.InspectionRecords,
) []*FMCSAInspectionRecordV1 {
	transformedRecords := make([]*FMCSAInspectionRecordV1, 0)
	for _, record := range records {
		transformedRecord := &FMCSAInspectionRecordV1{
			RowID:                         record.RowID.String(),
			PublishedDate:                 timestamppb.New(record.PublishedDate),
			InspectionID:                  record.InspectionID,
			DotNumber:                     record.DOTNumber,
			ReportNumber:                  record.ReportNumber,
			ReportState:                   record.ReportState,
			InspectionDate:                timestamppb.New(record.InspectionDate),
			InspectionLevel:               transformFMCSAInspectionLevelFromClientFormatToInternalFormatV1(record.InspectionLevel),
			TimeWeight:                    int32(record.TimeWeight),
			DriverOOSTotal:                int32(record.DriverOOSTotal),
			VehicleOOSTotal:               int32(record.VehicleOOSTotal),
			TotalOOSViolations:            int32(record.TotalOOSViolations),
			HazmatOOSTotal:                int32(record.HazmatOOSTotal),
			HazmatViolationsSent:          int32(record.HazmatViolationsSent),
			HazmatPlacardReq:              record.HazmatPlacardReq,
			BasicCategories:               transformBASICCategoriesFromClientFormatToInternalFormatV1(record.BASICCategories),
			BasicViolations:               transformBASICViolationsFromClientFormatToInternalFormatV1(record.BASICViolations),
			TotalBasicViols:               int32(record.TotalBASICViols),
			PublicVINs:                    record.PublicVINs,
			Region:                        record.Region,
			Location:                      record.Location,
			CountyCodeState:               record.CountyCodeState,
			CountyCode:                    record.CountyCode,
			ShipperName:                   record.ShipperName,
			WasSizeWeightEnforcement:      record.WasSizeWeightEnforcement,
			WasTrafficEnforcement:         record.WasTrafficEnforcement,
			WasLocalEnforcement:           record.WasLocalEnforcement,
			WasPostAccident:               record.WasPostAccident,
			CombinationVehicleGrossWeight: convertIntToInt32Ptr(record.CombinationVehicleGrossWeight),
			Vins:                          record.VINs,
			Vehicles:                      transformFMCSAInspectionVehiclesFromClientFormatToInternalFormatV1(record.Vehicles),
		}
		transformedRecords = append(transformedRecords, transformedRecord)
	}

	return transformedRecords
}

func transformFMCSAInspectionLevelFromClientFormatToInternalFormatV1(level models.
	InspectionLevel,
) FMCSAInspectionLevelV1 {
	switch level {
	case models.Unknown:
		return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Unknown
	case models.Full:
		return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Full
	case models.WalkAround:
		return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_WalkAround
	case models.DriverOnly:
		return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_DriverOnly
	case models.SpecialStudy:
		return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_SpecialStudy
	case models.Terminal:
		return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Terminal
	case models.Material:
		return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Material
	}
	return FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Unspecified
}

func transformBASICCategoriesFromClientFormatToInternalFormatV1(input map[basic.Category]bool) map[string]bool {
	output := make(map[string]bool)
	for k, v := range input {
		output[transformFMCSAViolationCategoryFromClientFormatToInternalFormatV1(k).String()] = v
	}
	return output
}

func transformFMCSAViolationCategoryFromClientFormatToInternalFormatV1(category basic.Category) FMCSAViolationCategoryV1 {
	switch category {
	case basic.Unspecified:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_Unspecified
	case basic.UnsafeDriving:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving
	case basic.HOSCompliance:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance
	case basic.VehicleMaintenance:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance
	case basic.ControlledSubstancesAlcohol:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol
	case basic.HMCompliance:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance
	case basic.DriverFitness:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness
	case basic.CrashIndicator:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_CrashIndicator
	case basic.InsuranceOther:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_InsuranceOther
	default:
		return FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_Unspecified
	}
}

func transformBASICViolationsFromClientFormatToInternalFormatV1(input map[basic.Category]int) map[string]int32 {
	output := make(map[string]int32)
	for k, v := range input {
		output[transformFMCSAViolationCategoryFromClientFormatToInternalFormatV1(k).String()] = int32(v)
	}
	return output
}

func transformFMCSAInspectionVehiclesFromClientFormatToInternalFormatV1(input []models.InspectionVehicle,
) []*FMCSAInspectionVehicleV1 {
	var output []*FMCSAInspectionVehicleV1
	for _, iv := range input {
		output = append(output, &FMCSAInspectionVehicleV1{
			Vin:     iv.VIN,
			Company: iv.Company,
			Make:    iv.Make,
			Model:   iv.Type,
		})
	}
	return output
}

func transformFMCSACensusInfoFromClientFormatToInternalFormatV1(dotDetails *models.DotDetails) *FMCSACensusInfoV1 {
	if dotDetails == nil {
		return nil
	}

	census := dotDetails.Census

	var ratingDate *timestamppb.Timestamp
	if census.RatingDate != nil {
		ratingDate = timestamppb.New(*census.RatingDate)
	}
	return &FMCSACensusInfoV1{
		IsActive:                  census.IsActive,
		DateStr:                   census.DateStr,
		Date:                      timestamppb.New(census.Date),
		Name:                      census.Name,
		Dba:                       census.Dba,
		TotalMileage:              census.TotalMileage,
		Mcs150MileageYear:         census.Mcs150MileageYear,
		TotalTrucks:               census.TotalTrucks,
		TotalPowerUnits:           census.TotalPowerUnits,
		TotalDrivers:              census.TotalDrivers,
		TotalCdlDrivers:           census.TotalCdlDrivers,
		CarrierCargoTypesStr:      census.CarrierCargoTypesStr,
		CarrierCargoTypes:         transformFMCSACargoTypeFromClientFormatToInternalFormatV1(census.CarrierCargoTypes),
		CarriesHazmat:             census.CarriesHazmat,
		PhysicalAddressNation:     census.PhysicalAddressNation,
		PhysicalAddressState:      census.PhysicalAddressState,
		PhysicalAddressCity:       census.PhysicalAddressCity,
		PhysicalAddressCountyCode: census.PhysicalAddressCountyCode,
		PhysicalAddressStreet:     census.PhysicalAddressStreet,
		PhysicalAddressZipCode:    census.PhysicalAddressZipCode,
		Rating:                    census.Rating,
		RatingDateStr:             census.RatingDateStr,
		RatingDate:                ratingDate,
		Telephone:                 census.Telephone,
		Cellphone:                 census.Cellphone,
		Fax:                       census.Fax,
		Email:                     census.Email,
	}
}

func transformObjectiveGradeFromClientFormatToInteralFormatV1(
	rawObjectiveGrade objetive_grade_models.ObjectiveGrade,
) ObjectiveGradeV1 {
	scoreValue := ObjectiveGradeScoreV1_value[rawObjectiveGrade.Grade.String()]
	score := ObjectiveGradeScoreV1(scoreValue)

	return ObjectiveGradeV1{
		DotNumber: rawObjectiveGrade.DotNumber,
		Date:      timestamppb.New(rawObjectiveGrade.GradeDate),
		Score:     score,
	}
}

func transformInvoluntaryRevocationsFromClientFormatToInternalFormatV1(
	rawInvoluntaryRevocations []authority_history.AuthHistRecord,
) InvoluntaryRevocationsV1 {
	records := slice_utils.Map(
		rawInvoluntaryRevocations, transformAuthorityHistoryFromClientFormatToInternalFormatV1,
	)
	return InvoluntaryRevocationsV1{
		Records: records,
	}
}

func transformFMCSACargoTypeFromClientFormatToInternalFormatV1(input []cargo_types.FmcsaCargoType) []FMCSACargoTypeV1 {
	output := make([]FMCSACargoTypeV1, len(input))
	for i, ct := range input {
		switch ct {
		case cargo_types.GeneralFreight:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_GeneralFreight
		case cargo_types.Household:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Household
		case cargo_types.MetalSheet:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_MetalSheet
		case cargo_types.MotorVehicles:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_MotorVehicles
		case cargo_types.DriveawayTowaway:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_DriveawayTowaway
		case cargo_types.LogsPolesBeamsLumber:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_LogsPolesBeamsLumber
		case cargo_types.BuildingMaterials:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_BuildingMaterials
		case cargo_types.MobileHomes:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_MobileHomes
		case cargo_types.MachineryLargeObjects:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_MachineryLargeObjects
		case cargo_types.Produce:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Produce
		case cargo_types.LiquidsGases:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_LiquidsGases
		case cargo_types.IntermodalContainers:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_IntermodalContainers
		case cargo_types.Passengers:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Passengers
		case cargo_types.OilfieldEquipment:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_OilfieldEquipment
		case cargo_types.Livestock:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Livestock
		case cargo_types.Grainfeed:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Grainfeed
		case cargo_types.CoalCoke:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_CoalCoke
		case cargo_types.Meat:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Meat
		case cargo_types.Garbage:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Garbage
		case cargo_types.USMail:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_UsMail
		case cargo_types.Chemicals:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Chemicals
		case cargo_types.CommoditiesDryBulk:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_CommoditiesDryBulk
		case cargo_types.RefrigeratedFood:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_RefrigeratedFood
		case cargo_types.Beverages:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Beverages
		case cargo_types.PaperProducts:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_PaperProducts
		case cargo_types.Utility:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Utility
		case cargo_types.FarmSupplies:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_FarmSupplies
		case cargo_types.Construction:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Construction
		case cargo_types.WaterWell:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_WaterWell
		case cargo_types.Other:
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Other
		default:
			// Handle unexpected values
			output[i] = FMCSACargoTypeV1_FMCSACargoTypeV1_Unspecified
		}
	}
	return output
}

func convertIntToInt32Ptr(input *int) *int32 {
	if input == nil {
		return nil
	}
	output := int32(*input)
	return &output
}

func transformAuthorityHistoryFromClientFormatToInternalFormatV1(
	r authority_history.AuthHistRecord,
) *AuthorityHistoryRecordV1 {
	record := AuthorityHistoryRecordV1{
		Id:             r.Id.String(),
		DockerNumber:   r.DocketNumber,
		DotNumber:      int64(r.DOTNumber),
		AuthorityType:  r.AuthorityType,
		OriginalAction: r.OriginalAction,
		FinalAction:    r.FinalAction,
		CreatedAt:      timestamppb.New(r.CreatedAt),
		VersionId:      int32(r.VersionId),
	}
	if r.OriginalActionServedDate != nil {
		record.OriginalActionServedDate = timestamppb.New(r.OriginalActionServedDate.ToTime())
	}
	if r.FinalActionDecisionDate != nil {
		record.FinalActionDecisionDate = timestamppb.New(r.FinalActionDecisionDate.ToTime())
	}
	if r.FinalActionServedDate != nil {
		record.FinalActionServedDate = timestamppb.New(r.FinalActionServedDate.ToTime())
	}
	return &record
}

func transformPolicyToNirvanaPolicyV1(p *policy_db.Policy) *NirvanaPolicyV1 {
	return &NirvanaPolicyV1{
		Id:               p.Id.String(),
		PolicyNumber:     p.PolicyNumber.String(),
		Version:          int32(p.Version),
		InsuredName:      p.InsuredName,
		State:            p.State.String(),
		ApplicationId:    p.ApplicationId.String(),
		SubmissionId:     p.SubmissionId.String(),
		ProgramType:      p.ProgramType.String(),
		EffectiveDate:    timestamppb.New(p.EffectiveDate),
		EffectiveDateTo:  timestamppb.New(p.EffectiveDateTo),
		AgencyId:         p.AgencyID.String(),
		CreatedAt:        timestamppb.New(p.CreatedAt),
		UpdatedAt:        timestamppb.New(p.UpdatedAt),
		InsuranceCarrier: p.InsuranceCarrier.String(),
		IsNonAdmitted:    p.IsNonAdmitted,
	}
}

func transformMainCoverageToPolicyCoverage(requestCoverage MainCoverageTypeV1) (enums.Coverage, error) {
	switch requestCoverage {
	case MainCoverageTypeV1_MainCoverageTypeV1_AutoLiability:
		return enums.CoverageAutoLiability, nil
	case MainCoverageTypeV1_MainCoverageTypeV1_GeneralLiability:
		return enums.CoverageGeneralLiability, nil
	case MainCoverageTypeV1_MainCoverageTypeV1_MotorTruckCargo:
		return enums.CoverageMotorTruckCargo, nil
	case MainCoverageTypeV1_MainCoverageTypeV1_Unspecified:
		return enums.CoverageAutoLiability, errors.Newf("unspecified coverage type: %v", requestCoverage)
	default:
		return enums.CoverageAutoLiability, errors.Newf("unsupported coverage type: %v", requestCoverage)
	}
}
