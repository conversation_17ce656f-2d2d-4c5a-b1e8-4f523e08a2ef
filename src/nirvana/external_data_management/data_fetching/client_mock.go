// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/data_fetching (interfaces: FetcherClient)
//
// Generated by this command:
//
//	mockgen -destination=client_mock.go -package=data_fetching nirvanatech.com/nirvana/external_data_management/data_fetching FetcherClient
//

// Package data_fetching is a generated GoMock package.
package data_fetching

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockFetcherClient is a mock of FetcherClient interface.
type MockFetcherClient struct {
	ctrl     *gomock.Controller
	recorder *MockFetcherClientMockRecorder
	isgomock struct{}
}

// MockFetcherClientMockRecorder is the mock recorder for MockFetcherClient.
type MockFetcherClientMockRecorder struct {
	mock *MockFetcherClient
}

// NewMockFetcherClient creates a new mock instance.
func NewMockFetcherClient(ctrl *gomock.Controller) *MockFetcherClient {
	mock := &MockFetcherClient{ctrl: ctrl}
	mock.recorder = &MockFetcherClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFetcherClient) EXPECT() *MockFetcherClientMockRecorder {
	return m.recorder
}

// GetBIPDActiveOrPendingInsuranceV1 mocks base method.
func (m *MockFetcherClient) GetBIPDActiveOrPendingInsuranceV1(ctx context.Context, in *BIPDActiveOrPendingInsuranceRequestV1, opts ...grpc.CallOption) (*BIPDActiveOrPendingInsuranceV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBIPDActiveOrPendingInsuranceV1", varargs...)
	ret0, _ := ret[0].(*BIPDActiveOrPendingInsuranceV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBIPDActiveOrPendingInsuranceV1 indicates an expected call of GetBIPDActiveOrPendingInsuranceV1.
func (mr *MockFetcherClientMockRecorder) GetBIPDActiveOrPendingInsuranceV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBIPDActiveOrPendingInsuranceV1", reflect.TypeOf((*MockFetcherClient)(nil).GetBIPDActiveOrPendingInsuranceV1), varargs...)
}

// GetBIPDInsuranceHistoryV1 mocks base method.
func (m *MockFetcherClient) GetBIPDInsuranceHistoryV1(ctx context.Context, in *BIPDInsuranceHistoryRequestV1, opts ...grpc.CallOption) (*BIPDInsuranceHistoryV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBIPDInsuranceHistoryV1", varargs...)
	ret0, _ := ret[0].(*BIPDInsuranceHistoryV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBIPDInsuranceHistoryV1 indicates an expected call of GetBIPDInsuranceHistoryV1.
func (mr *MockFetcherClientMockRecorder) GetBIPDInsuranceHistoryV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBIPDInsuranceHistoryV1", reflect.TypeOf((*MockFetcherClient)(nil).GetBIPDInsuranceHistoryV1), varargs...)
}

// GetFMCSACensusInfoV1 mocks base method.
func (m *MockFetcherClient) GetFMCSACensusInfoV1(ctx context.Context, in *FMCSACensusInfoRequestV1, opts ...grpc.CallOption) (*FMCSACensusInfoV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFMCSACensusInfoV1", varargs...)
	ret0, _ := ret[0].(*FMCSACensusInfoV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFMCSACensusInfoV1 indicates an expected call of GetFMCSACensusInfoV1.
func (mr *MockFetcherClientMockRecorder) GetFMCSACensusInfoV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFMCSACensusInfoV1", reflect.TypeOf((*MockFetcherClient)(nil).GetFMCSACensusInfoV1), varargs...)
}

// GetFMCSAInspectionRecordsV1 mocks base method.
func (m *MockFetcherClient) GetFMCSAInspectionRecordsV1(ctx context.Context, in *FMCSAInspectionRecordsRequestV1, opts ...grpc.CallOption) (*FMCSAInspectionRecordsV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFMCSAInspectionRecordsV1", varargs...)
	ret0, _ := ret[0].(*FMCSAInspectionRecordsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFMCSAInspectionRecordsV1 indicates an expected call of GetFMCSAInspectionRecordsV1.
func (mr *MockFetcherClientMockRecorder) GetFMCSAInspectionRecordsV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFMCSAInspectionRecordsV1", reflect.TypeOf((*MockFetcherClient)(nil).GetFMCSAInspectionRecordsV1), varargs...)
}

// GetFMCSAViolationRecordsV1 mocks base method.
func (m *MockFetcherClient) GetFMCSAViolationRecordsV1(ctx context.Context, in *FMCSAViolationRecordsRequestV1, opts ...grpc.CallOption) (*FMCSAViolationRecordsV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFMCSAViolationRecordsV1", varargs...)
	ret0, _ := ret[0].(*FMCSAViolationRecordsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFMCSAViolationRecordsV1 indicates an expected call of GetFMCSAViolationRecordsV1.
func (mr *MockFetcherClientMockRecorder) GetFMCSAViolationRecordsV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFMCSAViolationRecordsV1", reflect.TypeOf((*MockFetcherClient)(nil).GetFMCSAViolationRecordsV1), varargs...)
}

// GetGrantedAuthorityHistoryV1 mocks base method.
func (m *MockFetcherClient) GetGrantedAuthorityHistoryV1(ctx context.Context, in *GrantedAuthorityHistoryRequestV1, opts ...grpc.CallOption) (*GrantedAuthorityHistoryV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGrantedAuthorityHistoryV1", varargs...)
	ret0, _ := ret[0].(*GrantedAuthorityHistoryV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGrantedAuthorityHistoryV1 indicates an expected call of GetGrantedAuthorityHistoryV1.
func (mr *MockFetcherClientMockRecorder) GetGrantedAuthorityHistoryV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGrantedAuthorityHistoryV1", reflect.TypeOf((*MockFetcherClient)(nil).GetGrantedAuthorityHistoryV1), varargs...)
}

// GetInvoluntaryRevocationsV1 mocks base method.
func (m *MockFetcherClient) GetInvoluntaryRevocationsV1(ctx context.Context, in *InvoluntaryRevocationsRequestV1, opts ...grpc.CallOption) (*InvoluntaryRevocationsV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInvoluntaryRevocationsV1", varargs...)
	ret0, _ := ret[0].(*InvoluntaryRevocationsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvoluntaryRevocationsV1 indicates an expected call of GetInvoluntaryRevocationsV1.
func (mr *MockFetcherClientMockRecorder) GetInvoluntaryRevocationsV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvoluntaryRevocationsV1", reflect.TypeOf((*MockFetcherClient)(nil).GetInvoluntaryRevocationsV1), varargs...)
}

// GetLatestObjectiveGradeV1 mocks base method.
func (m *MockFetcherClient) GetLatestObjectiveGradeV1(ctx context.Context, in *LatestObjectiveGradeRequestV1, opts ...grpc.CallOption) (*ObjectiveGradeV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestObjectiveGradeV1", varargs...)
	ret0, _ := ret[0].(*ObjectiveGradeV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestObjectiveGradeV1 indicates an expected call of GetLatestObjectiveGradeV1.
func (mr *MockFetcherClientMockRecorder) GetLatestObjectiveGradeV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestObjectiveGradeV1", reflect.TypeOf((*MockFetcherClient)(nil).GetLatestObjectiveGradeV1), varargs...)
}

// GetLatestRatingTierRecordsDumpDateV1 mocks base method.
func (m *MockFetcherClient) GetLatestRatingTierRecordsDumpDateV1(ctx context.Context, in *LatestRatingTiersDumpDateRequestV1, opts ...grpc.CallOption) (*LatestRatingTiersDumpDateV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestRatingTierRecordsDumpDateV1", varargs...)
	ret0, _ := ret[0].(*LatestRatingTiersDumpDateV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestRatingTierRecordsDumpDateV1 indicates an expected call of GetLatestRatingTierRecordsDumpDateV1.
func (mr *MockFetcherClientMockRecorder) GetLatestRatingTierRecordsDumpDateV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestRatingTierRecordsDumpDateV1", reflect.TypeOf((*MockFetcherClient)(nil).GetLatestRatingTierRecordsDumpDateV1), varargs...)
}

// GetMVRAttractScoreV1 mocks base method.
func (m *MockFetcherClient) GetMVRAttractScoreV1(ctx context.Context, in *MVRAttractScoreRequestV1, opts ...grpc.CallOption) (*MVRAttractScoreV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMVRAttractScoreV1", varargs...)
	ret0, _ := ret[0].(*MVRAttractScoreV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMVRAttractScoreV1 indicates an expected call of GetMVRAttractScoreV1.
func (mr *MockFetcherClientMockRecorder) GetMVRAttractScoreV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMVRAttractScoreV1", reflect.TypeOf((*MockFetcherClient)(nil).GetMVRAttractScoreV1), varargs...)
}

// GetMVRReportV1 mocks base method.
func (m *MockFetcherClient) GetMVRReportV1(ctx context.Context, in *MVRReportRequestV1, opts ...grpc.CallOption) (*MVRReportV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMVRReportV1", varargs...)
	ret0, _ := ret[0].(*MVRReportV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMVRReportV1 indicates an expected call of GetMVRReportV1.
func (mr *MockFetcherClientMockRecorder) GetMVRReportV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMVRReportV1", reflect.TypeOf((*MockFetcherClient)(nil).GetMVRReportV1), varargs...)
}

// GetNationalCreditFileV1 mocks base method.
func (m *MockFetcherClient) GetNationalCreditFileV1(ctx context.Context, in *NationalCreditFileRequestV1, opts ...grpc.CallOption) (*NationalCreditFileV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNationalCreditFileV1", varargs...)
	ret0, _ := ret[0].(*NationalCreditFileV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNationalCreditFileV1 indicates an expected call of GetNationalCreditFileV1.
func (mr *MockFetcherClientMockRecorder) GetNationalCreditFileV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNationalCreditFileV1", reflect.TypeOf((*MockFetcherClient)(nil).GetNationalCreditFileV1), varargs...)
}

// GetNirvanaPoliciesV1 mocks base method.
func (m *MockFetcherClient) GetNirvanaPoliciesV1(ctx context.Context, in *GetNirvanaPoliciesRequestV1, opts ...grpc.CallOption) (*GetNirvanaPoliciesResponseV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNirvanaPoliciesV1", varargs...)
	ret0, _ := ret[0].(*GetNirvanaPoliciesResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNirvanaPoliciesV1 indicates an expected call of GetNirvanaPoliciesV1.
func (mr *MockFetcherClientMockRecorder) GetNirvanaPoliciesV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNirvanaPoliciesV1", reflect.TypeOf((*MockFetcherClient)(nil).GetNirvanaPoliciesV1), varargs...)
}

// GetRatingTierRecordV1 mocks base method.
func (m *MockFetcherClient) GetRatingTierRecordV1(ctx context.Context, in *RatingTierRecordRequestV1, opts ...grpc.CallOption) (*RatingTierRecordV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRatingTierRecordV1", varargs...)
	ret0, _ := ret[0].(*RatingTierRecordV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRatingTierRecordV1 indicates an expected call of GetRatingTierRecordV1.
func (mr *MockFetcherClientMockRecorder) GetRatingTierRecordV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRatingTierRecordV1", reflect.TypeOf((*MockFetcherClient)(nil).GetRatingTierRecordV1), varargs...)
}

// GetRatingTierRecordsForDumpDateV1 mocks base method.
func (m *MockFetcherClient) GetRatingTierRecordsForDumpDateV1(ctx context.Context, in *RatingTierRecordsForDumpDateRequestV1, opts ...grpc.CallOption) (*RatingTierRecordsForDumpDateV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRatingTierRecordsForDumpDateV1", varargs...)
	ret0, _ := ret[0].(*RatingTierRecordsForDumpDateV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRatingTierRecordsForDumpDateV1 indicates an expected call of GetRatingTierRecordsForDumpDateV1.
func (mr *MockFetcherClientMockRecorder) GetRatingTierRecordsForDumpDateV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRatingTierRecordsForDumpDateV1", reflect.TypeOf((*MockFetcherClient)(nil).GetRatingTierRecordsForDumpDateV1), varargs...)
}

// GetVINDetailsV1 mocks base method.
func (m *MockFetcherClient) GetVINDetailsV1(ctx context.Context, in *VINDetailsRequestV1, opts ...grpc.CallOption) (*VINDetailsV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVINDetailsV1", varargs...)
	ret0, _ := ret[0].(*VINDetailsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVINDetailsV1 indicates an expected call of GetVINDetailsV1.
func (mr *MockFetcherClientMockRecorder) GetVINDetailsV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVINDetailsV1", reflect.TypeOf((*MockFetcherClient)(nil).GetVINDetailsV1), varargs...)
}
