// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/data_fetching (interfaces: FetcherServer)
//
// Generated by this command:
//
//	mockgen -destination=server_mock.go -package=data_fetching nirvanatech.com/nirvana/external_data_management/data_fetching FetcherServer
//

// Package data_fetching is a generated GoMock package.
package data_fetching

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockFetcherServer is a mock of FetcherServer interface.
type MockFetcherServer struct {
	ctrl     *gomock.Controller
	recorder *MockFetcherServerMockRecorder
	isgomock struct{}
}

// MockFetcherServerMockRecorder is the mock recorder for MockFetcherServer.
type MockFetcherServerMockRecorder struct {
	mock *MockFetcherServer
}

// NewMockFetcherServer creates a new mock instance.
func NewMockFetcherServer(ctrl *gomock.Controller) *MockFetcherServer {
	mock := &MockFetcherServer{ctrl: ctrl}
	mock.recorder = &MockFetcherServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFetcherServer) EXPECT() *MockFetcherServerMockRecorder {
	return m.recorder
}

// GetBIPDActiveOrPendingInsuranceV1 mocks base method.
func (m *MockFetcherServer) GetBIPDActiveOrPendingInsuranceV1(arg0 context.Context, arg1 *BIPDActiveOrPendingInsuranceRequestV1) (*BIPDActiveOrPendingInsuranceV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBIPDActiveOrPendingInsuranceV1", arg0, arg1)
	ret0, _ := ret[0].(*BIPDActiveOrPendingInsuranceV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBIPDActiveOrPendingInsuranceV1 indicates an expected call of GetBIPDActiveOrPendingInsuranceV1.
func (mr *MockFetcherServerMockRecorder) GetBIPDActiveOrPendingInsuranceV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBIPDActiveOrPendingInsuranceV1", reflect.TypeOf((*MockFetcherServer)(nil).GetBIPDActiveOrPendingInsuranceV1), arg0, arg1)
}

// GetBIPDInsuranceHistoryV1 mocks base method.
func (m *MockFetcherServer) GetBIPDInsuranceHistoryV1(arg0 context.Context, arg1 *BIPDInsuranceHistoryRequestV1) (*BIPDInsuranceHistoryV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBIPDInsuranceHistoryV1", arg0, arg1)
	ret0, _ := ret[0].(*BIPDInsuranceHistoryV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBIPDInsuranceHistoryV1 indicates an expected call of GetBIPDInsuranceHistoryV1.
func (mr *MockFetcherServerMockRecorder) GetBIPDInsuranceHistoryV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBIPDInsuranceHistoryV1", reflect.TypeOf((*MockFetcherServer)(nil).GetBIPDInsuranceHistoryV1), arg0, arg1)
}

// GetFMCSACensusInfoV1 mocks base method.
func (m *MockFetcherServer) GetFMCSACensusInfoV1(arg0 context.Context, arg1 *FMCSACensusInfoRequestV1) (*FMCSACensusInfoV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFMCSACensusInfoV1", arg0, arg1)
	ret0, _ := ret[0].(*FMCSACensusInfoV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFMCSACensusInfoV1 indicates an expected call of GetFMCSACensusInfoV1.
func (mr *MockFetcherServerMockRecorder) GetFMCSACensusInfoV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFMCSACensusInfoV1", reflect.TypeOf((*MockFetcherServer)(nil).GetFMCSACensusInfoV1), arg0, arg1)
}

// GetFMCSAInspectionRecordsV1 mocks base method.
func (m *MockFetcherServer) GetFMCSAInspectionRecordsV1(arg0 context.Context, arg1 *FMCSAInspectionRecordsRequestV1) (*FMCSAInspectionRecordsV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFMCSAInspectionRecordsV1", arg0, arg1)
	ret0, _ := ret[0].(*FMCSAInspectionRecordsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFMCSAInspectionRecordsV1 indicates an expected call of GetFMCSAInspectionRecordsV1.
func (mr *MockFetcherServerMockRecorder) GetFMCSAInspectionRecordsV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFMCSAInspectionRecordsV1", reflect.TypeOf((*MockFetcherServer)(nil).GetFMCSAInspectionRecordsV1), arg0, arg1)
}

// GetFMCSAViolationRecordsV1 mocks base method.
func (m *MockFetcherServer) GetFMCSAViolationRecordsV1(arg0 context.Context, arg1 *FMCSAViolationRecordsRequestV1) (*FMCSAViolationRecordsV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFMCSAViolationRecordsV1", arg0, arg1)
	ret0, _ := ret[0].(*FMCSAViolationRecordsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFMCSAViolationRecordsV1 indicates an expected call of GetFMCSAViolationRecordsV1.
func (mr *MockFetcherServerMockRecorder) GetFMCSAViolationRecordsV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFMCSAViolationRecordsV1", reflect.TypeOf((*MockFetcherServer)(nil).GetFMCSAViolationRecordsV1), arg0, arg1)
}

// GetGrantedAuthorityHistoryV1 mocks base method.
func (m *MockFetcherServer) GetGrantedAuthorityHistoryV1(arg0 context.Context, arg1 *GrantedAuthorityHistoryRequestV1) (*GrantedAuthorityHistoryV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGrantedAuthorityHistoryV1", arg0, arg1)
	ret0, _ := ret[0].(*GrantedAuthorityHistoryV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGrantedAuthorityHistoryV1 indicates an expected call of GetGrantedAuthorityHistoryV1.
func (mr *MockFetcherServerMockRecorder) GetGrantedAuthorityHistoryV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGrantedAuthorityHistoryV1", reflect.TypeOf((*MockFetcherServer)(nil).GetGrantedAuthorityHistoryV1), arg0, arg1)
}

// GetInvoluntaryRevocationsV1 mocks base method.
func (m *MockFetcherServer) GetInvoluntaryRevocationsV1(arg0 context.Context, arg1 *InvoluntaryRevocationsRequestV1) (*InvoluntaryRevocationsV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvoluntaryRevocationsV1", arg0, arg1)
	ret0, _ := ret[0].(*InvoluntaryRevocationsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvoluntaryRevocationsV1 indicates an expected call of GetInvoluntaryRevocationsV1.
func (mr *MockFetcherServerMockRecorder) GetInvoluntaryRevocationsV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvoluntaryRevocationsV1", reflect.TypeOf((*MockFetcherServer)(nil).GetInvoluntaryRevocationsV1), arg0, arg1)
}

// GetLatestObjectiveGradeV1 mocks base method.
func (m *MockFetcherServer) GetLatestObjectiveGradeV1(arg0 context.Context, arg1 *LatestObjectiveGradeRequestV1) (*ObjectiveGradeV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestObjectiveGradeV1", arg0, arg1)
	ret0, _ := ret[0].(*ObjectiveGradeV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestObjectiveGradeV1 indicates an expected call of GetLatestObjectiveGradeV1.
func (mr *MockFetcherServerMockRecorder) GetLatestObjectiveGradeV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestObjectiveGradeV1", reflect.TypeOf((*MockFetcherServer)(nil).GetLatestObjectiveGradeV1), arg0, arg1)
}

// GetLatestRatingTierRecordsDumpDateV1 mocks base method.
func (m *MockFetcherServer) GetLatestRatingTierRecordsDumpDateV1(arg0 context.Context, arg1 *LatestRatingTiersDumpDateRequestV1) (*LatestRatingTiersDumpDateV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestRatingTierRecordsDumpDateV1", arg0, arg1)
	ret0, _ := ret[0].(*LatestRatingTiersDumpDateV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestRatingTierRecordsDumpDateV1 indicates an expected call of GetLatestRatingTierRecordsDumpDateV1.
func (mr *MockFetcherServerMockRecorder) GetLatestRatingTierRecordsDumpDateV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestRatingTierRecordsDumpDateV1", reflect.TypeOf((*MockFetcherServer)(nil).GetLatestRatingTierRecordsDumpDateV1), arg0, arg1)
}

// GetMVRAttractScoreV1 mocks base method.
func (m *MockFetcherServer) GetMVRAttractScoreV1(arg0 context.Context, arg1 *MVRAttractScoreRequestV1) (*MVRAttractScoreV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMVRAttractScoreV1", arg0, arg1)
	ret0, _ := ret[0].(*MVRAttractScoreV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMVRAttractScoreV1 indicates an expected call of GetMVRAttractScoreV1.
func (mr *MockFetcherServerMockRecorder) GetMVRAttractScoreV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMVRAttractScoreV1", reflect.TypeOf((*MockFetcherServer)(nil).GetMVRAttractScoreV1), arg0, arg1)
}

// GetMVRReportV1 mocks base method.
func (m *MockFetcherServer) GetMVRReportV1(arg0 context.Context, arg1 *MVRReportRequestV1) (*MVRReportV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMVRReportV1", arg0, arg1)
	ret0, _ := ret[0].(*MVRReportV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMVRReportV1 indicates an expected call of GetMVRReportV1.
func (mr *MockFetcherServerMockRecorder) GetMVRReportV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMVRReportV1", reflect.TypeOf((*MockFetcherServer)(nil).GetMVRReportV1), arg0, arg1)
}

// GetNationalCreditFileV1 mocks base method.
func (m *MockFetcherServer) GetNationalCreditFileV1(arg0 context.Context, arg1 *NationalCreditFileRequestV1) (*NationalCreditFileV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNationalCreditFileV1", arg0, arg1)
	ret0, _ := ret[0].(*NationalCreditFileV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNationalCreditFileV1 indicates an expected call of GetNationalCreditFileV1.
func (mr *MockFetcherServerMockRecorder) GetNationalCreditFileV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNationalCreditFileV1", reflect.TypeOf((*MockFetcherServer)(nil).GetNationalCreditFileV1), arg0, arg1)
}

// GetNirvanaPoliciesV1 mocks base method.
func (m *MockFetcherServer) GetNirvanaPoliciesV1(arg0 context.Context, arg1 *GetNirvanaPoliciesRequestV1) (*GetNirvanaPoliciesResponseV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNirvanaPoliciesV1", arg0, arg1)
	ret0, _ := ret[0].(*GetNirvanaPoliciesResponseV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNirvanaPoliciesV1 indicates an expected call of GetNirvanaPoliciesV1.
func (mr *MockFetcherServerMockRecorder) GetNirvanaPoliciesV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNirvanaPoliciesV1", reflect.TypeOf((*MockFetcherServer)(nil).GetNirvanaPoliciesV1), arg0, arg1)
}

// GetRatingTierRecordV1 mocks base method.
func (m *MockFetcherServer) GetRatingTierRecordV1(arg0 context.Context, arg1 *RatingTierRecordRequestV1) (*RatingTierRecordV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRatingTierRecordV1", arg0, arg1)
	ret0, _ := ret[0].(*RatingTierRecordV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRatingTierRecordV1 indicates an expected call of GetRatingTierRecordV1.
func (mr *MockFetcherServerMockRecorder) GetRatingTierRecordV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRatingTierRecordV1", reflect.TypeOf((*MockFetcherServer)(nil).GetRatingTierRecordV1), arg0, arg1)
}

// GetRatingTierRecordsForDumpDateV1 mocks base method.
func (m *MockFetcherServer) GetRatingTierRecordsForDumpDateV1(arg0 context.Context, arg1 *RatingTierRecordsForDumpDateRequestV1) (*RatingTierRecordsForDumpDateV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRatingTierRecordsForDumpDateV1", arg0, arg1)
	ret0, _ := ret[0].(*RatingTierRecordsForDumpDateV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRatingTierRecordsForDumpDateV1 indicates an expected call of GetRatingTierRecordsForDumpDateV1.
func (mr *MockFetcherServerMockRecorder) GetRatingTierRecordsForDumpDateV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRatingTierRecordsForDumpDateV1", reflect.TypeOf((*MockFetcherServer)(nil).GetRatingTierRecordsForDumpDateV1), arg0, arg1)
}

// GetVINDetailsV1 mocks base method.
func (m *MockFetcherServer) GetVINDetailsV1(arg0 context.Context, arg1 *VINDetailsRequestV1) (*VINDetailsV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVINDetailsV1", arg0, arg1)
	ret0, _ := ret[0].(*VINDetailsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVINDetailsV1 indicates an expected call of GetVINDetailsV1.
func (mr *MockFetcherServerMockRecorder) GetVINDetailsV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVINDetailsV1", reflect.TypeOf((*MockFetcherServer)(nil).GetVINDetailsV1), arg0, arg1)
}
