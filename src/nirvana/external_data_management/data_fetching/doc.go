package data_fetching

/*
This package encapsulates all the logic related to fetching "raw" data from "external" sources.

Examples of the external data sources are: LNI, FMCSA, VIN data, and MVR data. Note that data
can live in external servers or in our db (e.g. FMCSA data lives in our production SQL db,
while VIN data lives in the graph service).

This package exposes a factory function to create instances of the FetcherClient implementation.
This ensures that consumers always get a valid fetcher, and hides away some of the complexities
related to instantiation.

The FetcherClient interface is defined as a gRPC client. Therefore, consumers can modify the
behavior of the methods by using gRPC interceptors.

This package doesn't apply any business logic to the data. Having said that, it does act as an
adaptor for request-type and return-type structs. It essentially makes transformations between
structs that are defined out of its domain and structs that are defined inside its domain.
For instance, the mvr package returns a mvr.Report struct, that a certain fetcher method transforms
into the versioned struct data_fetching.MVRReportV1.
*/

// Command to regenerate FetcherServer mock
//go:generate go run go.uber.org/mock/mockgen -destination=server_mock.go -package=data_fetching nirvanatech.com/nirvana/external_data_management/data_fetching FetcherServer

// Command to regenerate FetcherClient mock
//go:generate go run go.uber.org/mock/mockgen -destination=client_mock.go -package=data_fetching nirvanatech.com/nirvana/external_data_management/data_fetching FetcherClient
