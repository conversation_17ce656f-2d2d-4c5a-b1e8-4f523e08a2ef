package nhtsa

import (
	"fmt"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
)

var (
	ErrConnection      = errors.New("Could not connect to database")
	ErrNoData          = errors.New("Stored Procedure did not return any data")
	ErrVinDetailsParse = errors.New("Error parsing vin details from database")
)

type dBError struct {
	Err error
}

func (e *dBError) Error() string {
	return fmt.Sprintf("Database Error: %v", e.Err)
}

func (e *dBError) Unwrap() error {
	return e.Err
}

func (e *dBError) Format(f fmt.State, verb rune) {
	errors.FormatError(e, f, verb)
}

type VinDecodeError struct {
	ErrorCodes []enums.ErrorCode
	ErrText    string
}

func (e *VinDecodeError) Error() string {
	var errorCodesStr string
	for _, code := range e.ErrorCodes {
		errorCodesStr += code.String() + ","
	}
	return fmt.Sprintf("VIN Decode Error: %s. Details: %s", errorCodesStr, e.ErrText)
}

var (
	_ error = &VinDecodeError{}
	_ error = &dBError{}
)

func newDatabaseError(err error) error {
	return &dBError{Err: err}
}
