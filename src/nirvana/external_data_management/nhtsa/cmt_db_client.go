package nhtsa

import (
	"context"
	"database/sql"
	"strconv"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/config"
)

type CmtDbClientImpl struct {
	db *sql.DB
}

func NewCmtDBClientImpl(cfg *config.Config) (*CmtDbClientImpl, error) {
	db, err := getDbFromCfg(cfg)
	if err != nil {
		return nil, err
	}
	return &CmtDbClientImpl{db: db}, nil
}

func (cl *CmtDbClientImpl) GetVINRecord(ctx context.Context, vin string) (*VinDetailsForCmt, error) {
	vinDetailsMap, err := executeSPVinDecode(vin, cl.db)
	if err != nil {
		log.Warn(ctx, "execute SPV Vin Decode", log.Err(err))
		return nil, err
	}
	vinDetails, err := getVINDetailsForCmtFromDBMap(vin, *vinDetailsMap)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get VIN details for CMT from DB map")
	}
	return vinDetails, nil
}

// getVINDetailsForCmtFromDBMap returns VinDetailsForCmt from db map with necessary validations
func getVINDetailsForCmtFromDBMap(vin string, vinDetailsMap map[string]string) (
	details *VinDetailsForCmt, parseErr error,
) {
	defer func() {
		if parseErr == nil {
			return
		}
		parseErr = errors.Mark(parseErr, ErrVinDetailsParse)
	}()
	ret := VinDetailsForCmt{}

	// string fields
	mak, ok := vinDetailsMap["Make"]
	if !ok {
		log.Plain.Debug("Missing Make", log.String("VIN", vin))
	}
	ret.Make = mak

	model, ok := vinDetailsMap["Model"]
	if !ok {
		log.Plain.Debug("Missing Model", log.String("VIN", vin))
	}
	ret.Model = model

	series, ok := vinDetailsMap["Series"]
	if !ok {
		log.Plain.Debug("Missing Series", log.String("VIN", vin))
	}
	ret.Series = series

	vehicleType, ok := vinDetailsMap["VehicleType"]
	if !ok {
		log.Plain.Debug("Missing VehicleType", log.String("VIN", vin))
	}
	ret.VehicleType = vehicleType

	bodyClass, ok := vinDetailsMap["BodyClass"]
	if !ok {
		log.Plain.Debug("Missing BodyClass", log.String("VIN", vin))
	}
	ret.BodyClass = bodyClass

	axleCfg, ok := vinDetailsMap["AxleConfiguration"]
	if !ok {
		log.Plain.Debug("Missing AxleConfiguration", log.String("VIN", vin))
	}
	ret.AxleConfiguration = axleCfg

	driveType, ok := vinDetailsMap["DriveType"]
	if !ok {
		log.Plain.Debug("Missing DriveType", log.String("VIN", vin))
	}
	ret.DriveType = driveType

	gvwr, ok := vinDetailsMap["GVWR"]
	if !ok {
		log.Plain.Debug("Missing GVWR", log.String("VIN", vin))
	}
	ret.GVWR = gvwr

	engineModel, ok := vinDetailsMap["EngineModel"]
	if !ok {
		log.Plain.Debug("Missing EngineModel", log.String("VIN", vin))
	}
	ret.EngineModel = engineModel

	fuelType, ok := vinDetailsMap["FuelTypePrimary"]
	if !ok {
		log.Plain.Debug("Missing FuelTypePrimary", log.String("VIN", vin))
	}
	ret.FuelTypePrimary = fuelType

	engineCfg, ok := vinDetailsMap["EngineConfiguration"]
	if !ok {
		log.Plain.Debug("Missing EngineConfiguration", log.String("VIN", vin))
	}
	ret.EngineConfiguration = engineCfg

	brakeSystem, ok := vinDetailsMap["BrakeSystemType"]
	if !ok {
		log.Plain.Debug("Missing BrakeSystemType", log.String("VIN", vin))
	}
	ret.BrakeSystemType = brakeSystem

	// int fields
	modelYear, ok := vinDetailsMap["ModelYear"]
	if ok && modelYear != "" && modelYear != notApplicable {
		year, err := strconv.Atoi(modelYear)
		if err != nil {
			parseErr = errors.Wrapf(err, "failed to parse model year: %s", modelYear)
			return
		}
		ret.ModelYear = &year
	}

	axles, ok := vinDetailsMap["Axles"]
	if ok && axles != "" && axles != notApplicable {
		count, err := strconv.Atoi(axles)
		if err != nil {
			parseErr = errors.Wrapf(err, "failed to parse axles: %s", axles)
			return
		}
		ret.Axles = &count
	}

	cylinders, ok := vinDetailsMap["EngineCylinders"]
	if ok && cylinders != "" && cylinders != notApplicable {
		count, err := strconv.Atoi(cylinders)
		if err != nil {
			parseErr = errors.Wrapf(err, "failed to parse cylinders: %s", cylinders)
			return
		}
		ret.EngineCylinders = &count
	}

	// float fields
	dispCC, ok := vinDetailsMap["DisplacementCC"]
	if ok && dispCC != "" && dispCC != notApplicable {
		value, err := strconv.ParseFloat(dispCC, 64)
		if err != nil {
			parseErr = errors.Wrapf(err, "failed to parse displacement cc: %s", dispCC)
			return
		}
		ret.DisplacementCC = &value
	}
	return &ret, nil
}
