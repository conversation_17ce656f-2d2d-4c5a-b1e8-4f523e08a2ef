# DB Client

`DBClient` struct implements the `GetVINRecord` method of the `Client` interface
to get `VINDetails` for a particular `VIN`. The NHTSA provides the vehicles' 
information as an SQL-Server backup file along with stored procedures 
(read about SP [here](https://www.w3schools.com/sql/sql_stored_procedures.asp)) 
to decode a VIN. The `DBClient` calls the SQL-Server SP to get the required data.

## Handling incomplete `VINDetails`: 
- SP returns error codes information which provides more information on whether 
  the `VIN`was decoded properly or not.
- For error codes other than `0`, `VINDetails` might have some information missing.
  So, for those cases, we return `VINDetails` (which might be incomplete) 
  along with a custom Error `VinDecodeError` as shown:
  
    ```golang
    type VinDecodeError struct {
        ErrorCodes []ErrorCode
        ErrText    string
    }
    ```
  
- Using the `ErrorCodes` field of the `VinDecodeError`, the user of the `DBClient`
  can decide which `VINDetails` are useful and which are not.

# Steps to set up SQL-Server on Docker 

## Pull and run the container image
1. Pull the SQL Server 2019 Linux container image from Microsoft Container Registry.
```shell
docker pull mcr.microsoft.com/mssql/server:2019-latest
```

2. Run the container image with Docker.
```shell
docker run -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=<YourStrong@Passw0rd>" \
   -p 1433:1433 --name sql-server \
   -d mcr.microsoft.com/mssql/server:2019-latest
```
> Note: The password must be at least 8 characters long and contain characters 
from three of the following four sets: Uppercase letters, Lowercase letters, 
Base 10 digits, and Symbols. If you don't set the password as per the above rules, 
then the container will not be able to set up the SQL server and will stop working.

3. Verify if the container is running
```shell
docker ps -a
```

## Restore the Database using backup file
1. Create a backup folder in SQL Server container
```shell
docker exec -it sql-server mkdir /var/opt/mssql/backup
```

2. Copy the backup file into the container
```shell
docker cp <path/to/backup/file> sql-server:/var/opt/mssql/backup
```

3. Restore the database
```shell
docker exec -it sql-server /opt/mssql-tools/bin/sqlcmd \
   -S localhost -U SA -P "<YourStrong@Passw0rd>" \
   -Q "RESTORE DATABASE vPICList_Data 
   FROM DISK = '/var/opt/mssql/backup/vPICList_lite_202106.bak' 
   WITH MOVE 'vPICList_Data' TO '/var/opt/mssql/data/vPICList_Lite.mdf',
   MOVE 'vPICList_log' TO '/var/opt/mssql/data/vPICList_Lite_log.ldf'"
```

> Note: If the above command does not work, the value of argument `Q` needs changes.
Below references (2nd one) might help.

Some References:

- [Quickstart: Run SQL Server container images with Docker](https://docs.microsoft.com/en-us/sql/linux/quickstart-install-connect-docker?view=sql-server-ver15&pivots=cs1-bash)
- [Restore a SQL Server database in a Linux Docker container](https://docs.microsoft.com/en-us/sql/linux/tutorial-restore-backup-in-sql-server-container?view=sql-server-ver15)

