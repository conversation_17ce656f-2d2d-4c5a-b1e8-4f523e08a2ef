package nhtsa

import (
	"fmt"
	"strings"

	"nirvanatech.com/nirvana/external_data_management/nhtsa/enums"

	"github.com/cockroachdb/errors"
)

// Private helper functions
func getWeightClassFromRawString(raw string) (enums.WeightClass, error) {
	if raw == "" {
		return enums.WeightClassNil, errors.New("Got empty raw weight class string")
	}
	parts := strings.Split(raw, ":")
	if len(parts) != 2 {
		return enums.WeightClassNil, errors.Errorf("Can't parse raw weight string %s", raw)
	}

	classString := strings.ToLower(parts[0])
	classString = strings.Join(strings.Fields(classString), "")
	symbol := strings.TrimLeft(classString, "class")
	// Override symbols if needed. 2G and 1D are used as aliases for G and D
	// and so on
	switch symbol {
	case "1a":
		symbol = "a"
	case "1b":
		symbol = "b"
	case "1c":
		symbol = "c"
	case "1d":
		symbol = "d"
	case "2e":
		symbol = "e"
	case "2f":
		symbol = "f"
	case "2g":
		symbol = "g"
	case "2h":
		symbol = "h"
	default:
	}
	symbol = strings.ToUpper(symbol)
	if len(symbol) != 1 {
		return enums.WeightClassNil, errors.Newf("Couldn't extract symbol from %s", raw)
	}
	return weightClassFromSymbol(symbol)
}

func trim(s string) string {
	return strings.Trim(s, " ")
}

func weightClassFromSymbol(symbol string) (enums.WeightClass, error) {
	enumStr := fmt.Sprintf("WeightClass%s", symbol)
	return enums.WeightClassString(enumStr)
}
