package nhtsa

import (
	"fmt"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
)

type nhtsaVINRecord struct {
	Count          int64                  `json: "Count"`
	Message        string                 `json: "Message"`
	SearchCriteria string                 `json: "SearchCriteria"`
	Results        []nhtsaVINResultRecord `json: "Results"`
}

type nhtsaVINResultRecord struct {
	Value      string `json: "Value"`
	ValueId    string `json: "ValueId"`
	Variable   string `json: "Variable"`
	VariableId int64  `json: "VariableId"`
}

func (r *nhtsaVINRecord) Make() (string, error) {
	return r.getValue(26)
}

func (r *nhtsaVINRecord) Model() (string, error) {
	return r.getValue(28)
}

func (r *nhtsaVINRecord) ModelYear() (string, error) {
	return r.getValue(29)
}

func (r *nhtsaVINRecord) Manufacturer() (string, error) {
	return r.getValue(27)
}

func (r *nhtsaVINRecord) Trim() (string, error) {
	return r.getValue(38)
}

func (r *nhtsaVINRecord) BodyClass() (string, error) {
	return r.getValue(5)
}

func (r *nhtsaVINRecord) WeightClass() (enums.WeightClass, error) {
	wcStr, err := r.getValue(25)
	if err != nil {
		return enums.WeightClassNil, err
	}

	if wcStr == "" {
		return enums.WeightClassNil, nil
	}

	return getWeightClassFromRawString(wcStr)
}

func (r *nhtsaVINRecord) getValue(varID int64) (string, error) {
	for _, r := range r.Results {
		if r.VariableId == varID {
			// sometimes there are extra spaces in beginning or end
			return trim(r.Value), nil
		}
	}
	return "", errors.New("Could not find record " + fmt.Sprint(varID) + " in NHTSA result")
}

func (r *nhtsaVINRecord) RawVehicleType() (string, error) {
	return r.getValue(39)
}

func (r *nhtsaVINRecord) Type() (*enums.VehicleType, error) {
	vehTypeStr, err := r.getValue(39)
	if err != nil {
		return nil, err
	}
	return getVehicleTypeEnum(vehTypeStr)
}
