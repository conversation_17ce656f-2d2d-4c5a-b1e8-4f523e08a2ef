package nhtsa

import (
	"context"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
	"nirvanatech.com/nirvana/servers/vehicles"
)

type MockClient struct {
	vs   vehicles.VehiclesServiceClient
	data map[string]*VINDetails
}

func (cl *MockClient) GetVINRecord(ctx context.Context, vin string) (*VINDetails, error) {
	res, ok := cl.data[vin]
	if !ok {
		return nil, errors.Mark(errors.Newf("record not found for vin %s", vin), ErrNoData)
	}
	if err := cl.storeVINDetails(ctx, res, vin); err != nil {
		log.DPanic(ctx, "failed to write VIN Vehicle node to vehicles service", log.Err(err))
	}
	return res, nil
}

// Stores VIN details in vehicle service
func (cl *MockClient) storeVINDetails(ctx context.Context, details *VINDetails, vin string) error {
	// For performance reasons we are not storing vin_vehicle nodes in Neo4j in unit tests by default.
	return nil
}

// AddMockVINDetails adds a mock VIN record to the client. Used to mock VIN records for testing.
func (cl *MockClient) AddMockVINDetails(vin string, details *VINDetails) {
	cl.data[vin] = details
}

var _ Client = &MockClient{}

func mockVINDetails() map[string]*VINDetails {
	tractors := []string{
		//
		// Standard mock VINs for our API test harness.
		//
		"4V4NC9EH0FN183920",
		"4V4NC9EH0FN183921",
		"4V4NC9EH0FN183922",
		"4V4NC9EH0FN183923",
		"4V4NC9EH0FN183924",
		"4V4NC9EH0FN183925",
		"4V4NC9EH0FN183926",
		"4V4NC9EH0FN183927",
		"4V4NC9EH0FN183928",
		"4V4NC9EH0FN183929",
		"4V4NC9EH0FN183930",
		"4V4NC9EH0FN183931",
		//
		// Nextraq VINs: Required for /telematics/:handleID/fleet_info
		//
		"SM000000000000001",
		"SM000000000000002",
		"SM000000000000003",
		"SM000000000000004",
		"SM000000000000005",
		"SM000000000000009",
		"SM000000000000010",
		//
		// Additional mock API harness VINs: Required for /telematics/:handleID/fleet_info.
		//
		"1DAKSJALDMA22S091",
		"1DAKSJALDMA22S093",
	}

	// We configure the following VINs as Trailers so that we have
	// some Mock VINs decode as Trailers, for testing purposes.
	trailers := []string{
		"1DAKSJALDMA22S090",
		//
		// Additional trailer VINs required for /telematics/:handleID/fleet_info
		//
		"SM000000000000006",
		"SM000000000000007",
		"SM000000000000008",
	}
	retval := make(map[string]*VINDetails)
	for _, vin := range tractors {
		retval[vin] = &VINDetails{
			Make:  "VOLVO TRUCK",
			Model: "VNL",
			// This should get defaulted to 2010
			ModelYear:    "",
			Manufacturer: "VOLVO GROUP NORTH AMERICA, LLC",
			Trim:         "",
			Type:         enums.VehicleTypeTruck,
			BodyClass:    "Truck-Tractor",
			WeightClass:  enums.WeightClass8,
		}
	}

	for _, vin := range trailers {
		retval[vin] = &VINDetails{
			Make:         "DAKOTA MANUFACTURING CO. / TRAIL-EZE",
			Model:        "Dakota Manufacturing Co.",
			ModelYear:    "2021",
			Manufacturer: "DAKOTA MANUFACTURING CO., INC.",
			Trim:         "",
			// This should get defaulted to trailer
			Type:        enums.VehicleTypeNil,
			BodyClass:   "TRAILER",
			WeightClass: enums.WeightClassNil,
		}
	}

	// VIN used for `rating/data_processing/vin_processing/utils_test.go#TestFixVinProblemsForIndication`.
	// We set VINDetails manually using VpicClientImpl.
	retval["1FVACWDT4GHGY6694"] = &VINDetails{
		Make:           "FREIGHTLINER",
		Model:          "M2",
		ModelYear:      "2016",
		Manufacturer:   "DAIMLER TRUCKS NORTH AMERICA LLC",
		RawVehicleType: "INCOMPLETE VEHICLE",
		Type:           enums.VehicleTypeIncompleteVehicle,
		BodyClass:      "TRUCK",
		WeightClass:    enums.WeightClass6,
	}
	retval["1XKAD49X9CJ331625"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T6 Series",
		// This should get defaulted to 2010
		ModelYear:    "2012",
		Manufacturer: "KENWORTH TRUCK COMPANYZ",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XPVDP9X5DD202875"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "384",
		// This should get defaulted to 2010
		ModelYear:    "2013",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1M1AW21YXGM052374"] = &VINDetails{
		Make:  "MACK",
		Model: "CXU",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "MACK TRUCKS INC.",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XP5DB9X62D576724"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "379",
		// This should get defaulted to 2010
		ModelYear:    "2002",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["3AKJHHDR4KSKN1525"] = &VINDetails{
		Make:  "FREIGHTLINER",
		Model: "Cascadia",
		// This should get defaulted to 2010
		ModelYear:    "2019",
		Manufacturer: "DAIMLER TRUCKS NORTH AMERICA LLC",
		Trim:         "126\" sleeper cab",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ489071"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKAD49X9CJ331624"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T6 Series",
		// This should get defaulted to 2010
		ModelYear:    "2012",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XPVDP9X5DD202870"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "384",
		// This should get defaulted to 2010
		ModelYear:    "2013",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1M1AW21YXGM052372"] = &VINDetails{
		Make:  "MACK",
		Model: "CXU",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "MACK TRUCKS INC.",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XP5DB9X62D576722"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "379",
		// This should get defaulted to 2010
		ModelYear:    "2002",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["3AKJHHDR4KSKN1527"] = &VINDetails{
		Make:  "FREIGHTLINER",
		Model: "Cascadia",
		// This should get defaulted to 2010
		ModelYear:    "2019",
		Manufacturer: "PDAIMLER TRUCKS NORTH AMERICA LLC",
		Trim:         "126\" sleeper cab",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ479073"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKAD49X9CJ331623"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T6 Series",
		// This should get defaulted to 2010
		ModelYear:    "2012",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XPVDP9X5DD202873"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "384",
		// This should get defaulted to 2010
		ModelYear:    "2013",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1M1AW21YXGM052373"] = &VINDetails{
		Make:  "MACK",
		Model: "CXU",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "MACK TRUCKS INC.",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XP5DB9X62D576723"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "379",
		// This should get defaulted to 2010
		ModelYear:    "2002",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["3AKJHHDR4KSKN1523"] = &VINDetails{
		Make:  "FREIGHTLINER",
		Model: "Cascadia",
		// This should get defaulted to 2010
		ModelYear:    "2019",
		Manufacturer: "DAIMLER TRUCKS NORTH AMERICA LLC",
		Trim:         "126\" sleeper cab",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ489073"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ489079"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKAD49X9CJ331621"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T6 Series",
		// This should get defaulted to 2010
		ModelYear:    "2012",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XPVDP9X5DD202872"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "384",
		// This should get defaulted to 2010
		ModelYear:    "2013",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1M1AW21YXGM052375"] = &VINDetails{
		Make:  "MACK",
		Model: "CXU",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "MACK TRUCKS INC.",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XP5DB9X62D576729"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "379",
		// This should get defaulted to 2010
		ModelYear:    "2002",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["3AKJHHDR4KSKN1521"] = &VINDetails{
		Make:  "FREIGHTLINER",
		Model: "Cascadia",
		// This should get defaulted to 2010
		ModelYear:    "2019",
		Manufacturer: "DAIMLER TRUCKS NORTH AMERICA LLC",
		Trim:         "126\" sleeper cab",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ489072"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYD09X1GJ489072"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKPDP9X1GJ489072"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck-Tractor",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ479072"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ488072"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ489082"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ489092"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKAD49X9CJ331629"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T6 Series",
		// This should get defaulted to 2010
		ModelYear:    "2012",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XPVDP9X5DD202879"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "384",
		// This should get defaulted to 2010
		ModelYear:    "2013",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["1M1AW21YXGM052371"] = &VINDetails{
		Make:  "MACK",
		Model: "CXU",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "MACK TRUCKS INC.",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XP5DB9X62D576725"] = &VINDetails{
		Make:  "PETERBILT",
		Model: "379",
		// This should get defaulted to 2010
		ModelYear:    "2002",
		Manufacturer: "PETERBILT MOTORS COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["3AKJHHDR4KSKN1526"] = &VINDetails{
		Make:  "FREIGHTLINER",
		Model: "Cascadia",
		// This should get defaulted to 2010
		ModelYear:    "2019",
		Manufacturer: "DAIMLER TRUCKS NORTH AMERICA LLC",
		Trim:         "126\" sleeper cab",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["1XKYDP9X1GJ489078"] = &VINDetails{
		Make:  "KENWORTH",
		Model: "T680",
		// This should get defaulted to 2010
		ModelYear:    "2016",
		Manufacturer: "KENWORTH TRUCK COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTruck,
		BodyClass:    "Truck",
		WeightClass:  enums.WeightClass8,
	}
	retval["13N148200E1565009"] = &VINDetails{
		Make:  "FONTAINE TRAILER CO.",
		Model: "Fontaine Trailer Co.",
		// This should get defaulted to 2010
		ModelYear:    "2014",
		Manufacturer: "FONTAINE COMMERCIAL PLATFORM",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["7SPCC4026PN000785"] = &VINDetails{
		Make:  "PRATT INTERMODAL CHASSIS LLC",
		Model: "PRATT INTERMODAL CHASSIS LLC",
		// This should get defaulted to 2010
		ModelYear:    "2023",
		Manufacturer: "PRATT INTERMODAL CHASSIS LLC",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["5V8VC532XPM311829"] = &VINDetails{
		Make:  "VANGUARD NATIONAL TRAILER CORPORATION (VNTC)",
		Model: "VANGUARD NATIONAL TRAILER CORPORATION (VNTC)",
		// This should get defaulted to 2010
		ModelYear:    "2023",
		Manufacturer: "VANGUARD NATIONAL TRAILER CORPORTATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["5V8VC5328LT014672"] = &VINDetails{
		Make:  "VANGUARD NATIONAL TRAILER CORPORATION (VNTC)",
		Model: "VANGUARD NATIONAL TRAILER CORPORATION (VNTC)",
		// This should get defaulted to 2010
		ModelYear:    "2020",
		Manufacturer: "VANGUARD NATIONAL TRAILER CORPORTATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["527SR5323PM032736"] = &VINDetails{
		Make:  "CIMC REEFER TRAILER,INC",
		Model: "CIMC REEFER TRAILER,INC",
		// This should get defaulted to 2010
		ModelYear:    "2023",
		Manufacturer: "CIMC REEFER TRAILER INC.",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["1UYVS2537L3819137"] = &VINDetails{
		Make:  "UTILITY TRAILER MANUFACTURER",
		Model: "Utility Trailer Manufacturer",
		// This should get defaulted to 2010
		ModelYear:    "2020",
		Manufacturer: "UTILITY TRAILER MANUFACTURING COMPANY",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["1PLE045204PK53503"] = &VINDetails{
		Make:  "PEERLESS",
		Model: "Peerless",
		// This should get defaulted to 2010
		ModelYear:    "2004",
		Manufacturer: "PEERLESS CORPORATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["5V8VC5325LT014659"] = &VINDetails{
		Make:  "VANGUARD NATIONAL TRAILER CORPORATION (VNTC)",
		Model: "VANGUARD NATIONAL TRAILER CORPORATION (VNTC)",
		// This should get defaulted to 2010
		ModelYear:    "2020",
		Manufacturer: "VANGUARD NATIONAL TRAILER CORPORTATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["1JJV532D4HL946063"] = &VINDetails{
		Make:  "WABASH VANS",
		Model: "Dry Van Duraplate",
		// This should get defaulted to 2010
		ModelYear:    "2020",
		Manufacturer: "WABASH NATIONAL CORPORATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["1JJV532D9LL186074"] = &VINDetails{
		Make:  "WABASH VANS",
		Model: "Dry Van Duraplate",
		// This should get defaulted to 2010
		ModelYear:    "2020",
		Manufacturer: "WABASH NATIONAL CORPORATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["1TTF482C1L3197207"] = &VINDetails{
		Make:  "TRANSCRAFT/WABASH",
		Model: "Combo Steel/Alum Flatbed",
		// This should get defaulted to 2010
		ModelYear:    "2020",
		Manufacturer: "TRANSCRAFT CORPORATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClassNil,
	}
	retval["1PLE032204QK54523"] = &VINDetails{
		Make:         "PEERLESS",
		Model:        "Peerless",
		ModelYear:    "2012",
		Manufacturer: "ACME CORPORATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClass3,
	}
	retval["1QMF143204QK54523"] = &VINDetails{
		Make:         "",
		Model:        "Peerless",
		ModelYear:    "2012",
		Manufacturer: "ACME CORPORATION",
		Trim:         "",
		Type:         enums.VehicleTypeTrailer,
		BodyClass:    "TRAILER",
		WeightClass:  enums.WeightClass3,
	}
	retval["1JKK141114QK77723"] = &VINDetails{
		Make:         "",
		Model:        "Peerless",
		ModelYear:    "2014",
		Manufacturer: "ACME CORPORATION",
		Trim:         "",
		Type:         enums.VehicleTypeIncompleteVehicle,
		BodyClass:    "TRUCK",
		WeightClass:  enums.WeightClass6,
	}
	return retval
}

func NewMockClient(vs vehicles.VehiclesServiceClient) Client {
	return &MockClient{data: mockVINDetails(), vs: vs}
}
