package nhtsa

import (
	"context"
	"strconv"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/servers/vehicles"

	"nirvanatech.com/nirvana/infra/config"

	"nirvanatech.com/nirvana/common-go/log"
)

func NewNHTSAClient(cfg *config.Config, vs vehicles.VehiclesServiceClient) (Client, error) {
	ctx := context.Background()
	switch cfg.Env {
	case config.Env_TEST:
		log.Info(ctx, "Using Mock Client for NHTSA vin decoding")
		return NewMockClient(vs), nil
	case config.Env_DEV:
		log.Info(ctx, "Using HTTP Client for NHTSA vin decoding")
		return NewVpicClientImpl(vs), nil
	default:
		log.Info(ctx, "Using DB Client for NHTSA vin decoding")
		return NewDBClientImpl(cfg, vs)
	}
}

// Prepares VIN node for inserttion into Vehicle Service graph
func PrepareVinNode(vin string, details *VINDetails) []*vehicles.Node {
	var nodes []*vehicles.Node
	vinNode := vehicles.VINVehicleNode{
		Key: &vehicles.VINVehicleNode_PrimaryKey{
			VIN: vin,
		},
		VehicleType: pointer_utils.String(details.Type.String()),
	}
	if details.Make != "" {
		vinNode.Make = &details.Make
	}
	if details.Model != "" {
		vinNode.Model = &details.Model
	}
	if details.BodyClass != "" {
		vinNode.BodyClass = &details.BodyClass
	}
	if details.Trim != "" {
		vinNode.Trim = &details.Trim
	}
	if details.WeightClass.IsAWeightClass() {
		vinNode.WeightClass = pointer_utils.String(details.WeightClass.String())
	}
	if details.RawVehicleType != "" {
		vinNode.RawVehicleType = &details.RawVehicleType
	}
	if details.Type.IsAVehicleType() {
		vinNode.VehicleType = pointer_utils.String(details.Type.String())
	}
	if details.ModelYear != "" {
		makeYear, err := strconv.Atoi(details.ModelYear)
		if err == nil {
			vinNode.Year = pointer_utils.Int64(int64(makeYear))
		}
	}

	nodes = append(nodes, &vehicles.Node{
		Selected: &vehicles.Node_VINVehicle{
			VINVehicle: &vinNode,
		},
	})
	return nodes
}
