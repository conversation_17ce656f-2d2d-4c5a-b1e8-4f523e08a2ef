package nhtsa

import (
	"context"
	"database/sql"
	"fmt"
	"net/url"
	"strings"

	"github.com/cockroachdb/errors"
	_ "github.com/denisenkom/go-mssqldb"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/servers/vehicles"
)

const (
	VinOkErrorCode = "0"
)

type DBClientImpl struct {
	db *sql.DB
	vs vehicles.VehiclesServiceClient
}

func NewDBClientImpl(cfg *config.Config, vs vehicles.VehiclesServiceClient) (*DBClientImpl, error) {
	db, err := getDbFromCfg(cfg)
	if err != nil {
		return nil, err
	}
	return &DBClientImpl{db: db, vs: vs}, nil
}

func getDbFromCfg(cfg *config.Config) (*sql.DB, error) {
	dbconfig := cfg.GetDatabases().GetNhtsa()
	db, err := newConn(
		dbconfig.GetUsername(),
		dbconfig.GetPassword(),
		dbconfig.GetHost(),
		dbconfig.GetPort(),
		dbconfig.GetName(),
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to connect to NHTSA database: "+
			"host=%v, port=%v, name=%v, username=%v",
			dbconfig.GetHost(), dbconfig.GetPort(), dbconfig.GetName(), dbconfig.GetUsername())
	}
	return db, nil
}

func newConn(username, password, host string, port int64, dbName string) (*sql.DB, error) {
	ctx := context.Background()
	query := url.Values{}
	query.Add("database", dbName)
	u := &url.URL{
		Scheme:   "sqlserver",
		User:     url.UserPassword(username, password),
		Host:     fmt.Sprintf("%s:%d", host, port),
		RawQuery: query.Encode(),
	}
	conn, err := sql.Open("sqlserver", u.String())
	if err != nil {
		log.Error(ctx, "Error opening sqlserver connection", log.Err(err))
		return nil, newDatabaseError(ErrConnection)
	}
	err = conn.PingContext(ctx)
	if err != nil {
		log.Error(ctx, "Error verifying sqlserver connection", log.Err(err))
		return nil, newDatabaseError(ErrConnection)
	}
	return conn, nil
}

func (cl *DBClientImpl) GetVINRecord(ctx context.Context, vin string) (*VINDetails, error) {
	vinDetailsMap, err := executeSPVinDecode(vin, cl.db)
	if err != nil {
		log.Warn(ctx, "execute SPV Vin Decode", log.Err(err))
		return nil, err
	}
	vinDetails, err := getVINDetailsFromDBMap(vin, *vinDetailsMap)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get VIN details from DB map")
	}

	// Note: Vehicle Service calls are blocking the response of this function
	// This is leading to Indication Jobs or any job for that matter which uses
	// this function to get stalled. Hence, commenting out the call to storeVINDetails
	// for now till further investigation.
	//if e := cl.storeVINDetails(ctx, vin, vinDetails); e != nil {
	//	log.Error(ctx, "failed to write VIN Vehicle node to vehicles service", log.Err(e))
	//}
	return vinDetails, err
}

// Stores VIN details in vehicle service
func (cl *DBClientImpl) storeVINDetails(ctx context.Context, vin string, details *VINDetails) error {
	node := PrepareVinNode(vin, details)
	_, err := cl.vs.UpsertNodesEdges(ctx, &vehicles.UpsertNodesEdgesRequest{Nodes: node})
	return err
}

func executeSPVinDecode(vin string, db *sql.DB) (*map[string]string, error) {
	ctx := context.Background()
	var err error
	// Check if database is alive.
	err = db.PingContext(ctx)
	if err != nil {
		log.Error(ctx, "Error verifying sqlserver connection", log.Err(err))
		return nil, newDatabaseError(ErrConnection)
	}
	rows, err := db.QueryContext(ctx, "[dbo].[spVinDecode]", sql.Named("v", vin))
	if err != nil {
		log.Error(ctx, "Error executing spVinDecode", log.Err(err))
		switch {
		case errors.Cause(err) == sql.ErrNoRows:
			return nil, newDatabaseError(ErrNoData)
		default:
			return nil, newDatabaseError(err)
		}
	}
	defer func(rows *sql.Rows) {
		err = rows.Close()
	}(rows)

	ret := make(map[string]string)
	for rows.Next() {
		var patternId, vinSchemaId, elementId, wmiId sql.NullInt64
		var groupName, variable, value, keys, attributeId, ToBeQCd,
			createdOn, code, dataType, decode, source sql.NullString

		// Get values from row
		err = rows.Scan(&groupName, &variable, &value, &patternId,
			&vinSchemaId, &keys, &elementId, &attributeId, &createdOn,
			&wmiId, &code, &dataType, &decode, &source, &ToBeQCd)
		if err != nil {
			log.Error(ctx, "Error scanning row from DB", log.Err(err))
			return nil, newDatabaseError(err)
		}
		ret[code.String] = trim(value.String)
	}
	return &ret, nil
}

func getVINDetailsFromDBMap(
	vin string, vinDetailsMap map[string]string,
) (details *VINDetails, decErr error) {
	// Create VINDetails
	ret := VINDetails{}

	// Optional
	mak, ok := vinDetailsMap["Make"]
	if !ok {
		log.Plain.Debug("Found empty Make", log.String("VIN", vin))
	}
	ret.Make = strings.ToUpper(mak)

	// Optional
	manufacturer, ok := vinDetailsMap["Manufacturer"]
	if !ok {
		log.Plain.Debug("Found empty Manufacturer", log.String("VIN", vin))
	}
	ret.Manufacturer = strings.ToUpper(manufacturer)

	// Optional
	model, ok := vinDetailsMap["Model"]
	if !ok {
		log.Plain.Debug("Found empty Model", log.String("VIN", vin))
	}
	ret.Model = strings.ToUpper(model)

	// Optional
	modelYear, ok := vinDetailsMap["ModelYear"]
	if !ok {
		log.Plain.Debug("Found empty ModelYear", log.String("VIN", vin))
	}
	ret.ModelYear = modelYear

	// Optional
	trm, ok := vinDetailsMap["Trim"]
	if !ok {
		log.Plain.Debug("Found empty trim", log.String("VIN", vin))
	}
	ret.Trim = strings.ToUpper(trm)

	ret.Type = enums.VehicleTypeNil
	if vehTypeStr, ok := vinDetailsMap["VehicleType"]; ok {
		typ, err := getVehicleTypeEnum(vehTypeStr)
		if err != nil {
			log.Plain.Warn(
				"Failed to fetch vehicle type",
				log.String("VIN", vin), log.Err(err))
			decErr = errors.Wrapf(err, "unable to get vehicle type enum for type %s", vehTypeStr)
		} else {
			ret.Type = *typ
		}
	}

	// Optional
	bodyCls, ok := vinDetailsMap["BodyClass"]
	if !ok {
		log.Plain.Debug("Missing BodyClass", log.String("VIN", vin))
	}
	ret.BodyClass = bodyCls

	// Optional
	ret.WeightClass = enums.WeightClassNil
	if wcStr, ok := vinDetailsMap["GVWR"]; ok {
		wc, err := getWeightClassFromRawString(wcStr)
		if err != nil {
			log.Plain.Warn(
				"Failed to extract weight class",
				log.String("VIN", vin), log.String("weightClassRaw", wcStr), log.Err(err))
			decErr = errors.Wrapf(err, "unable to get weight class from raw string %s", wcStr)
		}
		ret.WeightClass = wc
	}

	isVinOk := false
	errorCodes := strings.Split(vinDetailsMap["ErrorCode"], ",")
	errorCodesEnum := make([]enums.ErrorCode, 0)
	for _, code := range errorCodes {
		if code == VinOkErrorCode {
			isVinOk = true
			break
		}
		errorCodeEnum, err := enums.ErrorCodeString("ErrorCode" + code)
		if err != nil {
			return nil, errors.Wrap(err, "unable to decode error code")
		}
		errorCodesEnum = append(errorCodesEnum, errorCodeEnum)
	}
	if !isVinOk {
		vinError := VinDecodeError{
			ErrorCodes: errorCodesEnum,
			ErrText:    vinDetailsMap["ErrorText"],
		}
		ret.DecodeError = &vinError
	}
	details = &ret
	if decErr != nil {
		decErr = errors.Mark(decErr, ErrVinDetailsParse)
	}
	return
}

func getVehicleTypeEnum(vehTypeStr string) (*enums.VehicleType, error) {
	var vehType enums.VehicleType
	switch vehTypeStr {
	case "INCOMPLETE VEHICLE":
		vehType = enums.VehicleTypeIncompleteVehicle
	case "MOTORCYCLE":
		vehType = enums.VehicleTypeMotorcycle
	case "TRUCK":
		vehType = enums.VehicleTypeTruck
	case "TRAILER":
		vehType = enums.VehicleTypeTrailer
	case "PASSENGER CAR":
		vehType = enums.VehicleTypePassengerCar
	case "MULTIPURPOSE PASSENGER VEHICLE (MPV)":
		vehType = enums.VehicleTypeMultipurposePassengerVehicle
	case "BUS":
		vehType = enums.VehicleTypeBus
	default:
		return nil, errors.Newf("Unknown Vehicle Type %s", vehTypeStr)
	}
	return &vehType, nil
}
