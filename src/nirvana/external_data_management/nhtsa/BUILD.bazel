load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "nhtsa",
    srcs = [
        "client.go",
        "cmt_client.go",
        "cmt_db_client.go",
        "cmt_mock_client.go",
        "db_client.go",
        "errors.go",
        "fx.go",
        "mock_client.go",
        "nhtsa_utils_client.go",
        "utils.go",
        "vpic_client.go",
        "vpic_decode.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/nhtsa",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/external_data_management/nhtsa/enums",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/servers/vehicles:proto",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_denisenkom_go_mssqldb//:go-mssqldb",
        "@com_github_imroc_req//:req",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "nhtsa_test",
    srcs = ["nhtsa_client_test.go"],
    deps = [
        ":nhtsa",
        "//nirvana/common-go/iter_utils",
        "//nirvana/common-go/test_utils",
        "//nirvana/external_data_management/nhtsa/enums",
        "//nirvana/infra/fx/testloader",
        "//nirvana/servers/vehicles:proto",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
