package nhtsa

import (
	"context"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
)

type CMTMockClient struct{}

func (cl *CMTMockClient) GetVINRecord(_ context.Context, _ string) (*VinDetailsForCmt, error) {
	return &VinDetailsForCmt{
		Make:                "MockMake",
		Model:               "MockModel",
		ModelYear:           pointer_utils.Int(2025),
		Series:              "MockSeries",
		VehicleType:         "MockVehicleType",
		BodyClass:           "MockBodyClass",
		Axles:               pointer_utils.Int(3),
		AxleConfiguration:   "MockAxleCfg",
		DriveType:           "MockDriveType",
		GVWR:                "MockGVWR",
		EngineModel:         "MockEngineModel",
		DisplacementCC:      pointer_utils.Float64(1000),
		EngineCylinders:     pointer_utils.Int(10),
		FuelTypePrimary:     "Petrol",
		EngineConfiguration: "V12",
		BrakeSystemType:     "4x4",
	}, nil
}
