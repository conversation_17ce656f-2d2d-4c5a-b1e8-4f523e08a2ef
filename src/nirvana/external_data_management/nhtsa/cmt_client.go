package nhtsa

import (
	"context"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/config"
)

const notApplicable = "Not Applicable"

// VinDetailsForCmt refers to attributes being parsed solely for CMT purposes. As we only need to pass in the actual
// raw values, we are only using string/int/float64 as types, instead of using our enums
type VinDetailsForCmt struct {
	Make                string
	Model               string
	ModelYear           *int
	Series              string
	VehicleType         string
	BodyClass           string
	Axles               *int
	AxleConfiguration   string
	DriveType           string
	GVWR                string
	EngineModel         string
	DisplacementCC      *float64
	EngineCylinders     *int
	FuelTypePrimary     string
	EngineConfiguration string
	BrakeSystemType     string
}

type CmtClient interface {
	GetVINRecord(ctx context.Context, vin string) (*VinDetailsForCmt, error)
}

func NewCmtClient(cfg *config.Config) (CmtClient, error) {
	ctx := context.Background()
	switch cfg.Env {
	case config.Env_TEST:
		log.Info(ctx, "Using Mock Client for CMT NHTSA vin decoding")
		return &CMTMockClient{}, nil
	default:
		log.Info(ctx, "Using DB Client for NHTSA vin decoding")
		return NewCmtDBClientImpl(cfg)
	}
}
