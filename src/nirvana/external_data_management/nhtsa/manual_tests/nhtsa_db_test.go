package manual_tests

import (
	"context"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/common-go/iter_utils"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
	nhtsaEnums "nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/servers/vehicles"
)

var nhtsaPassword = "INVALID"

// To run this test, port forward the NHTSA DB and update `nhtsaPassword`
// - `ssh bastion -L 11433:nhtsa.csx3hx8rg1kh.us-east-2.rds.amazonaws.com:1433 -N`
func TestNhtsaDbTestSuite(t *testing.T) {
	suite.Run(t, new(nhtsaTestSuite))
}

type nhtsaTestSuite struct {
	suite.Suite
	client    nhtsa.Client
	cmtClient nhtsa.CmtClient
	app       *fxtest.App
}

func (s *nhtsaTestSuite) TearDownTest() {
	s.app.RequireStop()
}

func (s *nhtsaTestSuite) SetupTest() {
	var env struct {
		fx.In
		NHTSAClient nhtsa.Client
		CMTClient   nhtsa.CmtClient
	}
	decorator := fx.Decorate(func(cfg *config.Config, nhtsaClient nhtsa.Client, vs vehicles.VehiclesServiceClient) (nhtsa.Client, error) {
		cfg.Databases.Nhtsa.Port = 11433
		cfg.Databases.Nhtsa.Password = nhtsaPassword
		return nhtsa.NewDBClientImpl(cfg, vs)
	})
	cmtDecorator := fx.Decorate(func(cfg *config.Config, cmtClient nhtsa.CmtClient) (nhtsa.CmtClient, error) {
		cfg.Databases.Nhtsa.Port = 11433
		cfg.Databases.Nhtsa.Password = nhtsaPassword
		return nhtsa.NewCmtDBClientImpl(cfg)
	})
	s.app = testloader.RequireStart(s.T(), &env, testloader.Use(decorator, cmtDecorator))
	s.client = env.NHTSAClient
	s.cmtClient = env.CMTClient
}

func (s *nhtsaTestSuite) TestNhtsaDbClient() {
	for _, tc := range cases {
		s.Run(tc.vin, func() {
			compareRecord := func(r *nhtsa.VINDetails) {
				s.Require().Equal(tc.make, r.Make)
				s.Require().Equal(tc.model, r.Model)
				s.Require().Equal(tc.year, r.ModelYear)
			}
			r, err := s.client.GetVINRecord(context.Background(), tc.vin)
			s.Require().NoError(err)
			compareRecord(r)
		})
	}
}

func (s *nhtsaTestSuite) TestInvalidVins() {
	for _, vin := range invalidVins {
		s.Run(vin, func() {
			r, err := s.client.GetVINRecord(context.Background(), vin)
			s.Require().NoError(err)
			s.Require().NotNil(r.DecodeError)
		})
	}
}

func (s *nhtsaTestSuite) TestValidVins() {
	validVinSet := iter_utils.StringSetFromSlice(validVins)
	for vin := range validVinSet {
		s.Run(vin, func() {
			r, err := s.client.GetVINRecord(context.Background(), vin)
			s.Require().NoError(err)
			s.Require().Nil(r.DecodeError)
		})
	}
}

func (s *nhtsaTestSuite) TestValidCMTVins() {
	validVinSet := iter_utils.StringSetFromSlice(validVins)
	for vin := range validVinSet {
		s.Run(vin, func() {
			_, err := s.cmtClient.GetVINRecord(context.Background(), vin)
			s.Require().NoError(err)
		})
	}
}

func (s *nhtsaTestSuite) TestParseErrVins() {
	parseErrVinSet := iter_utils.StringSetFromSlice(parseErrVins)
	for vin := range parseErrVinSet {
		s.Run(vin, func() {
			_, err := s.client.GetVINRecord(context.Background(), vin)
			s.Require().Error(err)
			s.Require().True(errors.Is(err, nhtsa.ErrVinDetailsParse))
		})
	}
}

type vinTestCase struct {
	vin     string
	make    string
	model   string
	year    string
	vehType nhtsaEnums.VehicleType
	wtClass nhtsaEnums.WeightClass
}

var cases = []vinTestCase{
	{
		vin:     "3AKJGLDRXKDKH1902",
		make:    "FREIGHTLINER",
		model:   "CASCADIA",
		year:    "2019",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1FDUF5HT4CED07122",
		make: "FORD",
		// verisk returns F-550'
		model:   "F-550",
		year:    "2012",
		vehType: nhtsaEnums.VehicleTypeIncompleteVehicle,
		wtClass: nhtsaEnums.WeightClass5,
	},
	{
		vin:     "1GB4K0C86DF238472",
		make:    "CHEVROLET",
		model:   "SILVERADO",
		year:    "2013",
		vehType: nhtsaEnums.VehicleTypeIncompleteVehicle,
		wtClass: nhtsaEnums.WeightClass3,
	},
	{
		vin:  "1GDJK39183E331827",
		make: "GMC",
		// verisk returns 'NEW SIERRA'
		model:   "SIERRA",
		year:    "2003",
		vehType: nhtsaEnums.VehicleTypeIncompleteVehicle,
		wtClass: nhtsaEnums.WeightClass3,
	},
	{
		vin:  "3AKJGLD51FSGH8069",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2015",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "3AKJGLD57FSGH8075",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2015",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1M1AW21Y8GM052370",
		make: "MACK",
		// verisk returns 'NEW SIERRA'
		model:   "CXU (PINNACLE)",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	// this case gives error code 1
	{
		vin:  "1M1AW21Y8GM052371",
		make: "MACK",
		// verisk returns 'NEW SIERRA'
		model:   "CXU (PINNACLE)",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1XKYDP9X8GJ489076",
		make: "KENWORTH",
		// verisk returns 'NEW SIERRA'
		model:   "T680",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1XKYDP9X1GJ489078",
		make: "KENWORTH",
		// verisk returns 'NEW SIERRA'
		model:   "T680",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1XKYDP9X4GJ489074",
		make: "KENWORTH",
		// verisk returns 'NEW SIERRA'
		model:   "T680",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1XKYDP9XXGJ489077",
		make: "KENWORTH",
		// verisk returns 'NEW SIERRA'
		model:   "T680",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1XKYDP9X3GJ489079",
		make: "KENWORTH",
		// verisk returns 'NEW SIERRA'
		model:   "T680",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "4V4NC9EH5GN955698",
		make: "VOLVO TRUCK",
		// verisk returns 'NEW SIERRA'
		model:   "VNL",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "4V4NC9EH7GN955699",
		make: "VOLVO TRUCK",
		// verisk returns 'NEW SIERRA'
		model:   "VNL",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "4V4NC9EHXGN955700",
		make: "VOLVO TRUCK",
		// verisk returns 'NEW SIERRA'
		model:   "VNL",
		year:    "2016",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1FUJGLDRXHLHG7530",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2017",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1FUJGLDRXHLHG7527",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2017",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1FUJGLDR1HLHG7531",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2017",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1FUJGLDR1HLHG7528",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2017",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1FUJGLDR3HLHG7532",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2017",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1FUJGLDR3HLHG7529",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2017",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
	{
		vin:  "1FUJGLDR5HLHG75",
		make: "FREIGHTLINER",
		// verisk returns 'NEW SIERRA'
		model:   "CASCADIA",
		year:    "2017",
		vehType: nhtsaEnums.VehicleTypeTruck,
		wtClass: nhtsaEnums.WeightClass8,
	},
}

// From https://docs.google.com/spreadsheets/d/16lklQArfJlm8lRnCMaL5Jo3UOCp-53PPeNv1urSNOxU
var validVins = []string{
	"1FTYE9ZM2JKA15782",
	"5PVNJ8JR4L4S51843",
	"5PVNV8JR8G4S50995",
	"1FUJGEDV7HLJH1054",
	"5PVNJ8JR6L4S51844",
	"5PVNV8JR4J4S51150",
	"5PVNJ8JR7L4S51836",
	"5PVNV8JR9D4S50693",
	"5PVNV8JR8J4S51152",
	"5PVNV8JR5L4S51435",
	"3AKJHLDV4KSKG0898",
	"5PVNV8JR0G4S51011",
	"5PVNV8JR6D4S50652",
	"3AKJHLDV0LSMD8291",
	"5PVNV8JR2G4S50930",
	"5PVNV8JR3E4S50805",
	"5PVNV8JRXF4S50852",
	"5PVNV8JR6G4S50929",
	"5PVNV8JR9K4S51310",
	"5PVNV8JRXK4S51297",
	"5PVNV8JR0J4S51145",
	"5PVNV8JR4F4S50846",
	"5PVNJ8JM8G4S50656",
	"5PVNJ8JM6G4S50641",
	"5PVNV8JR2G4S50927",
	"5PVNV8JR1F4S50853",
	"5PVNJ8JM6E4S50488",
	"5PVNV8JRXF4S50849",
	"5PVNV8JR9L4S51423",
	"5PVNV8JR7J4S51143",
	"5PVNJ8JM4G4S50590",
	"5PVNV8JR9G4S51010",
	"5PVNV8JR6F4S50850",
	"5PVNV8JR2E4S50763",
	"5PVNV8JR3K4S51304",
	"5PVNV8JR2J4S51146",
	"5PVNJ8JR0G4S51022",
	"5PVNJ8JR8L4S51845",
	"NM0LS7E77F1219382",
	"3AKJGEDVXGDHP3378",
	"5PVNJ8JT3K4S58700",
	"5PVNV8JR3J4S51236",
	"5PVNV8JR1G4S51034",
	"NM0LS7E23K1420050",
	"5PVNV8JR9L5S51514",
	"1FUJGEDV7JLJL7657",
	"NM0LS7E75G1263219",
	"5PVNV8JR1J4S51235",
	"5PVNV8JRXD4S50685",
	"5PVNV8JRXG4S51033",
	"1FUJGEDV9HLJH1055",
	"5PVNV8JRXL4S51415",
	"5PVNV8JR7L4S51436",
	"5PVNV8JR5G4S51005",
	"5PVNV8JR5G4S51022",
	"5PVNV8JR2G4S50992",
	"5PVNV8JR1G4S51020",
	"5PVNJ8JR2L4S51842",
	"5PVNJ8JR3E4S50881",
	"5PVNV8JR6G4S51014",
	"5PVNV8JR9G4S50990",
	"5PVNV8JR8G4S51029",
	"1FUJGEDV7HLJC4123",
	"3AKJGEDV8GDHP3377",
	"5PVNJ8JR9K4S51626",
	"5PVNJ8JR4K4S51629",
	"5PVNV8JR6G4S51028",
	"5PVNV8JR6J4S51151",
	"5PVNV8JR3G4S51021",
	"5PVNV8JR1J4S51154",
	"5PVNV8JR6G4S50932",
	"5PVNJ8JJ2E4S50154",
	"5PVNV8JRXL4S51429",
	"5PVNV8JR3J4S51141",
	"5PVNJ8JR4L4S51843",
	"5PVNV8JR2F4S50859",
	"5PVNV8JR0K4S51308",
	"5PVNJ8JR1K4S51622",
	"5PVNV8JR3L4S51434",
	"5PVNJ8JR3K4S51623",
	"5PVNV8JR4G4S51027",
	"5PVNV8JR3J4S51138",
	"5PVNV8JR8L4S51428",
	"5PVNV8JR6H4S51077",
	"NM0LS7E21K1414165",
	"5PVNV8JR9J4S51144",
	"5PVNV8JR8L4S51431",
	"5PVNJ8JR1G4S51160",
	"5PVNV8JR5J4S51237",
	"5PVNV8JR6H4S51080",
	"5PVNV8JRXH4S51079",
	"5PVNV8JR2G4S51009",
	"5PVNV8JRXH4S51082",
	"5PVNV8JR6L4S51430",
	"5PVNV8JR5K4S51305",
	"5PVNV8JR7G4S51006",
	"5PVNV8JR5D4S50688",
	"5PVNV8JR4H4S51076",
	"5PVNV8JR8H4S51078",
	"5PVNJ8JR3G4S51175",
	"5PVNV8JR2K4S51309",
	"5PVNV8JRXL4S51432",
	"5PVNV8JR2G4S51026",
	"4V4NC9EHXEN180375",
	"5PVNV8JR8F4S50848",
	"5PVNV8JR1E4S50804",
	"5PVNJ8JM6G4S50655",
	"5PVNV8JR9G4S51024",
	"5PVNJ8JM4G4S50637",
	"5PVNV8JR1L4S51433",
	"5PVNJ8JM0E4S50485",
	"5PVNV8JR5J4S51142",
	"5PVNJ8JM9F4S50518",
	"5PVNV8JR4G4S51030",
	"5PVNV8JR8G4S51032",
	"5PVNJ8JM0F4S50519",
	"NM0LS7E73J1364458",
	"5PVNV8JR1J4S51137",
	"5PVNV8JR2L4S51425",
	"5PVNV8JJ3D4S50067",
	"NM0LS7E76H1318343",
	"5PVNV8JR5J4S51139",
	"5PVNV8JR1H4S51083",
	"NM0LS7E77F1219382",
	"5PVNV8JR0K4S51311",
	"5PVNJ8JR0G4S51022",
	"5PVNV8JR2K4S51312",
	"5PVNJ8JR0K4S51627",
	"5PVNV8JR1K4S51303",
	"5PVNJ8JR2K4S51628",
	"5PVNV8JR8K4S51301",
	"5PVNV8JRXK4S51302",
	"5PVNV8JR1K4S51298",
	"5PVNJ8JRXK4S51621",
	"5PVNV8JR3K4S51299",
	"5PVNJ8JR7K4S51625",
	"5PVNJ8JR9L4S51837",
	"5PVNJ8JT4L4S59453",
	"5PVNV8JR1G4S51034",
	"5PVNV8JR8G4S50995",
	"3AKJHLDV2LSMD8289",
	"5PVNJ8JR9L4S51840",
	"5PVNJ8JR1G4S51160",
	"3AKJHLDV0LSMD8291",
	"5PVNV8JR8L4S51431",
	"5PVNV8JR6F4S50850",
	"5PVNV8JR2G4S51009",
	"5PVNV8JR8G4S51029",
	"5PVNV8JR4G4S50928",
	"5PVNV8JR6H4S51080",
	"5PVNV8JR5G4S51005",
	"1FDUF5GY8HEC32431",
	"1FDUF5GY3HEC32434",
	"5PVNJ8JM4G4S50637",
	"1FDUF5GY1HEC32433",
	"5PVNV8JR1J4S51137",
	"5PVNJ8JT4L4S59453",
	"NM0LS7E75G1263219",
	"5PVNV8JR1F4S50853",
	"1FUJGEDV7HLJH1054",
	"3AKJHLDV4KSKG0898",
	"5PVNV8JR6H4S51080",
	"5PVNJ8JR3K4S51623",
	"5PVNV8JR1G4S51017",
	"5PVNV8JR4J4S51147",
	"5PVNV8JR2G4S50930",
	"5PVNJ8JR7G4S51163",
	"5PVNV8JR4F4S50846",
	"5PVNV8JR6G4S50932",
	"3HAEUMML2ML853168",
	"5PVNJ8JR8K4S51620",
	"5PVNV8JR1J4S51154",
	"1FUJGEDV7JLJL7657",
	"5PVNV8JR7G4S50924",
	"5PVNV8JR4H4S51076",
	"5PVNV8JR9G4S51007",
	"5PVNV8JR9L4S51423",
	"5PVNV8JR6J4S51148",
	"5PVNJ8JM8G4S50656",
	"5PVNV8JR0G4S51025",
	"5PVNV8JR0J4S51145",
	"5PVNV8JR3J4S51141",
	"5PVNV8JR8J4S51149",
	"5PVNV8JRXF4S50849",
	"5PVNV8JR3G4S51018",
	"5PVNJ8JR5K4S51624",
	"5PVNV8JR4G4S51013",
	"5PVNV8JR0L4S51424",
	"5PVNV8JR6L4S51427",
	"5PVNJ8JR7L4S51836",
	"5PVNV8JR4G4S50931",
	"5PVNV8JRXL4S51429",
	"5PVNV8JR3J4S51138",
	"5PVNV8JR1J4S51235",
	"5PVNJ8JR4K4S51629",
	"5PVNV8JR4G4S50993",
	"5PVNJ8JR0K4S51630",
	"5PVNV8JR6G4S51028",
	"5PVNV8JR4J4S51150",
	"5PVNV8JR8H4S51078",
	"5PVNJ8JR4L4S51843",
	"5PVNV8JR1H4S51083",
	"5PVNJ8JT3K4S58700",
	"5PVNV8JR2L4S51425",
	"5PVNV8JR4L4S51426",
	"5PVNV8JR5J4S51237",
	"NM0LS7E76H1318343",
	"5PVNV8JR0G4S50926",
	"3AKJGEDVXGDHP3378",
	"5PVNV8JR6H4S51077",
	"5PVNJ8JR9L4S51840",
	"5PVNV8JR6L4S51430",
	"5PVNV8JR5J4S51139",
	"NM0LS7E21K1414165",
	"5PVNV8JR3J4S51236",
	"5PVNV8JR1J4S51140",
	"5PVNV8JR6K4S51300",
	"5PVNV8JRXH4S51082",
	"5PVNJ8JR2K4S51628",
	"5PVNJ8JR3G4S51175",
	"5PVNV8JR0G4S51011",
	"5PVNV8JR2G4S51026",
	"5PVNV8JR8F4S50848",
	"5PVNV8JR8L4S51428",
	"5PVNV8JR6J4S51151",
	"5PVNV8JR9G4S51010",
	"5PVNV8JR7J4S51143",
	"NM0LS7E23K1420050",
	"3AKJHLDV0LSMD8291",
	"NM0LS7E73J1364458",
	"NM0LS6E79F1178375",
	"5PVNV8JR8L4S51431",
	"5PVNV8JR8G4S50995",
	"5PVNJ8JM6G4S50641",
	"5PVNV8JR1K4S51303",
	"5PVNV8JR9J4S51144",
	"5PVNV8JRXK4S51302",
	"5PVNJ8JR9K4S51626",
	"5PVNJ8JR0K4S51627",
	"5PVNV8JR6F4S50850",
	"5PVNV8JRXH4S51079",
	"5PVNJ8JR1K4S51622",
	"5PVNJ8JR8L4S51845",
	"5PVNJ8JM6G4S50655",
	"5PVNV8JR6G4S50929",
	"5PVNV8JR2J4S51146",
	"5PVNV8JR2F4S50859",
	"5PVNJ8JM4G4S50590",
	"5PVNV8JR9K4S51310",
	"5PVNV8JR1G4S51020",
	"5PVNV8JRXK4S51297",
	"5PVNV8JR4G4S51027",
	"5PVNV8JR3L4S51434",
	"3HAEUMML8ML860352",
	"5PVNV8JR1L4S51433",
	"5PVNV8JR2K4S51309",
	"1FUJGEDV7HLJC4123",
	"3AKJHLDV9LSMD8290",
	"3HAEUMMLXML860353",
	"1FUJGEDV9HLJH1055",
	"5PVNJ8JR7K4S51625",
	"5PVNV8JRXL4S51432",
	"5PVNV8JR7G4S51006",
	"3HAEUMML7ML207821",
	"3HAEUMML9ML207819",
	"3HAEUMML7ML207818",
	"3HAEUMML5ML207817",
	"3HAEUMML5ML207820",
	"5PVNJ8JR6L4S51844",
	"5PVNV8JR3K4S51299",
	"3HAEUMML4ML853169",
	"5PVNV8JR5K4S51305",
	"5PVNV8JR9G4S51024",
	"5PVNV8JR4G4S51030",
	"5PVNV8JR0K4S51308",
	"5PVNV8JR8G4S51032",
	"5PVNV8JR1K4S51298",
	"5PVNV8JRXF4S50852",
	"3HAEUMMN9ML875542",
	"3HAEUMML8ML858648",
	"5PVNV8JR8K4S51301",
	"5PVNV8JRXL4S51415",
	"5PVNJ8JR2L4S51839",
	"5PVNV8JR2G4S50992",
	"5PVNV8JR5L4S51435",
	"5PVNV8JR7L4S51436",
	"5PVNJ8JR1G4S51160",
	"5PVNV8JR8J4S51152",
	"5PVNV8JRXG4S51033",
	"5PVNV8JR3K4S51304",
	"5PVNV8JR8G4S51029",
	"5PVNV8JR0K4S51311",
	"5PVNV8JR1G4S51034",
	"3AKJGEDV8GDHP3377",
	"5PVNJ8JR0G4S51022",
	"5PVNV8JR6G4S51014",
	"5PVNJ8JM9F4S50518",
	"5PVNV8JR7G4S51006",
	"5PVNJ8JRXK4S51621",
	"5PVNV8JR8K4S51301",
	"5PVNV8JR2K4S51312",
	"5PVNV8JR9G4S51007",
	"5PVNJ8JM0F4S50519",
	"1FD8W3HT1CEB61736",
	"1FDAF56RX8EA57144",
	"1FDAX57R68EA56715",
	"1FDKF37F4VED09155",
	"1FDUF5HT0HEE09766",
	"1FDUF5HT4CEB75589",
	"1FDUF5HT4CED07122",
	"1FDUF5HT6FEA71755",
	"1FDUF5HT9KDA12750",
	"1FDWF35Y29EA85931",
	"1FDWW36S91EA76982",
	"1GB4K0C86DF238472",
	"1GB5YSEY4LF281735",
	"1GBE4C3969F403470",
	"1GBE4C3988F402352",
	"1GBE6H1J3RJ112426",
	"1GBG6H1J9MJ107877",
	"1GBJ6C1345F505044",
	"1GBJ6C1C16F422110",
	"1GBJ6H1C7XJ103967",
	"1GBJ6H1JXVJ108530",
	"1GBJ7H1C42J509940",
	"1GBJ7H1C82J504899",
	"1GBJC34F2YF425213",
	"1GBJK34638E176168",
	"1GBL7H1C2XJ101341",
	"1GBL7H1JXVJ109585",
	"1GD321CG1FF555563",
	"1GD322CL9BF233086",
	"1GD32VCG3HZ225187",
	"1GD32VCG8HF157700",
	"1GD39SEY0LF264992",
	"1GD6K2B62AF108695",
	"1GDG6H1E71J500914",
	"1GDG6H1P0LJ606156",
	"1GDJ6E1B27F424251",
	"1GDJ6E1B28F403921",
	"1GDJ6E1B48F403967",
	"1GDJ6E1BX7F422876",
	"1GDJK34NXSE534985",
	"1GDJK39183E331827",
	"1GDJK746X9E129932",
	"1GDJK746X9F143999",
	"3D6WZ4ET1BG502308",
	"3FDWF36F9YMA22492",
	"1GD07RFG0G1120210",
	"1GD373CLXC1160235",
	"4P5DL1428D1181090",
	"4P5GN182751071215",
	"JH2RD06099K700403",
	"1PTF7ATH019006505",
	"1MT2P392XDH023648",
	"1TDH40022GB153874",
	"1TDH40024CB133264",
	"1TDH40026CB133265",
	"1TDH40028CB133266",
	"1TDH4002XCB133267",
	"1TDH422286B110020",
	"1RF43464482047941",
	"1FDNF80C1VVA33933",
	"1FV6HLAA2WH903454",
	"1FVACWDC77DY75652",
	"1HTMMAAL24H614790",
	"1HTMMAAL53H569360",
	"1HTMMAAL72H547830",
	"1HTMMAAL88H639411",
	"1HTMMAAM05H106733",
	"1HTMMAAM63H575354",
	"1HTMMAAMX4H650414",
	"1HTMMAAN23H578275",
	"1HTMMAAN44H662552",
	"1HTMMAAP24H617255",
	"1HTMNAAM22H538197",
	"1HTMSADR22J035522",
	"1HTMSADR34J015525",
	"1HTSCAAM61H369992",
	"1HTSCAAM6YH322343",
	"1HTSCAAM7WH502699",
	"1HTSCAAP2XH627864",
	"1HTSCAAP9TH363004",
	"1HTSCAAPXWH497671",
	"1HTSCABM3XH596564",
	"1HTSDAAN3VH430940",
	"1HTSDAAN91H309372",
	"1HTSDADR0XH636654",
	"1NKDLB0X07J198583",
	"1NKDLB0X47J198585",
	"1NKDLB0X77J198578",
	"1NKDLB0X96J133908",
	"1NKDXBEX16J133219",
	"1NKZX4TX4HJ171759",
	"1NPALF0X76N657930",
	"1NPCL70X2JD487201",
	"1NPCL70X4JD487202",
	"1NPCX7TX0JD487200",
	"1NPCX7TX6JD487198",
	"1NPCX7TX8JD487199",
	"2NKHHN7X6AM273605",
	"2NKHHN7XXAM273607",
	"2NP2HJ7X0JM497123",
	"2NP2HJ7X3JM497116",
	"2NP2HJ7X5JM497117",
	"2NP2HJ7X5JM497120",
	"2NP2HJ7X7JM497118",
	"2NP2HJ7X7JM497121",
	"2NP2HJ7X9JM497119",
	"2NP2HJ7X9JM497122",
	"2NPNHM6X0KM619724",
	"2NPNHM6X1JM497129",
	"2NPNHM6X2JM497124",
	"2NPNHM6X2KM619725",
	"2NPNHM6X3JM497133",
	"2NPNHM6X4JM497125",
	"2NPNHM6X5JM497134",
	"2NPNHM6X6JM497126",
	"2NPNHM6X8JM497127",
	"2NPNHM6X8JM497130",
	"2NPNHM6XXJM497128",
	"2NPNHM6XXJM497131",
	"2NPNHM6XXKM497132",
	"2NPNHZ7X81M568788",
	"1PMA3421091034922",
	"1PMA3422181034502",
	"1PMA3422381034503",
	"1PMA34224B5009464",
	"1PMA3422881034500",
	"1PMA34228A5008591",
	"1PMA34228B5009466",
	"1PMA3422X81034501",
	"1PMA3422XA5008589",
	"1PMA3422XA5008592",
	"1PMA3432181031677",
	"1PMA3432181033381",
	"1PMA3432381033382",
	"1PMA3432581033383",
	"1PMA3432781033384",
	"1PMA3432981033385",
	"1PMA3432X81031676",
	"1PMA3432X81033380",
	"1FUJA6CK06DW83801",
	"1FUJA6CK26DW83802",
	"1FUJA6CK26LV98786",
	"1FUJA6CK27LZ26712",
	"1FUJA6CK47LZ26713",
	"1FUJA6CK57LX56135",
	"1FUJA6CK57LZ40913",
	"1FUJA6CK66LV44374",
	"1FUJA6CK66LV98807",
	"1FUJA6CK77LY28565",
	"1FUJA6CK77LY84912",
	"1FUJA6CK87LX56145",
	"1FUJA6CK96LV98431",
	"1FUJA6CK97LY16112",
	"1FUJA6CK97LY28566",
	"1FUJA6CKX6DW83806",
	"1FUJA6CKX6LV98793",
	"1FUJA6CKX7LX56146",
	"1FUJA6CKX7LY16104",
	"1FUJA6CKX7LY31153",
	"1FUJA6CKX7LZ26747",
	"1FUJA6CV37LY71438",
	"1FUJBBCG24LM73463",
	"1FUJBBCG54LM73599",
	"1FUJBBCK26LW38998",
	"1FUJBBCK77LX79275",
	"1FUJGEDR1DSFA7593",
	"1FUJGEDR2DSFA6310",
	"1FUJGEDR4DLBZ2626",
	"1FUJGEDR4DSFA3800",
	"1FUJGEDR6DSFA6309",
	"1FUJGLD50ELFR2394",
	"1FUJGLDR8GLGW8661",
	"1FUYDZYB3SH825030",
	"1FVJBGAS83HK88528",
	"1HSHBAAN5TH311434",
	"1HSHCA5R6RH548128",
	"1HSHWAHN96J332701",
	"1M1AA10Y33W027501",
	"1M2AD64Y65M001743",
	"1M2N179Y0EA092017",
	"1M2N277Y8KW011019",
	"1XKAD29X8MJ564455",
	"1XKAD29XXMJ564442",
	"1XKADR9X0PS586201",
	"1XKADR9X9PJ593718",
	"1XKDD29X2JS504089",
	"1XKDD29XXJS504101",
	"1XKDD40X5ER413887",
	"1XKDDB9X14J056261",
	"1XKDDB9X4KS520618",
	"1XKDDB9X6KS530759",
	"1XKDDB9X84R059983",
	"1XKDDB9X8KS530763",
	"1XKDDP9X3DJ344375",
	"1XKDDP9X5DJ344376",
	"1XKDDU9X36R131767",
	"1XKDDU9X36R131770",
	"1XKDDU9X76R131769",
	"1XKDPB0X7TS732368",
	"1XKWDB9XX7R144921",
	"1XKYD49X9HJ143585",
	"1XKYDP9X2EJ419540",
	"1XKYDP9X4EJ419538",
	"1XKYDP9X4EJ419541",
	"1XKYDP9XXEJ419544",
	"1XKZDP9X0FJ438349",
	"1XKZDP9X7FJ438350",
	"1XKZDP9X7FJ451261",
	"1XKZDP9X9FJ438351",
	"1XKZDP9X9FJ451262",
	"1XP5DB8X1RN361534",
	"1XP9D29X5GP194208",
	"1XPBD49X1MD755711",
	"1XPBD49X3MD755712",
	"1XPBD49X6KD262443",
	"1XPBDP9XXJD485777",
	"1XPCD79X0GD370747",
	"1XPCD79X0JD464585",
	"1XPCD79X1JD464594",
	"1XPCD79X2GD369499",
	"1XPCD79X2GD370748",
	"1XPCD79X2JD464586",
	"1XPCD79X3JD464595",
	"1XPCD79X4JD464587",
	"1XPCD79X4JD464590",
	"1XPCD79X5GD369500",
	"1XPCD79X5JD464596",
	"1XPCD79X6JD464588",
	"1XPCD79X6JD464591",
	"1XPCD79X8JD464589",
	"1XPCD79X8JD464592",
	"1XPCD79X9JD464584",
	"1XPCD79XXJD464593",
	"1XPCDP9X3LD637132",
	"1XPCDP9X5LD637133",
	"1XPFDB9X53D598205",
	"1XPGDB9X94D833162",
	"1XPGDU9X26D630192",
	"1XPHD49X99D788831",
	"1XPTD40X8LD688363",
	"1XPTD40XXLD688364",
	"1XPTP4TX0ED229527",
	"1XPTP4TX0ED229530",
	"1XPTP4TX2ED229528",
	"1XPTP4TX2ED229531",
	"1XPTP4TX4ED229529",
	"1XPTP4TX4ED229532",
	"1XPTP4TX6ED229533",
	"1XPTP4TX8ED229534",
	"2HSFBAER7VC025244",
	"2HSFMALR8SC023544",
	"2HSFMALRXRC012054",
	"2HSFMATR8RC092860",
	"3AKJGED60ESFV0266",
	"3AKJGED68ESFV0256",
	"3AKJGEDR4ESFK8734",
	"3AKJGEDR6ESFK8248",
	"3AKJGEDR6ESFK8735",
	"3AKJGLD52FSGD6686",
	"3AKJGLD5XESFY2167",
	"3WKDD49X0HF154879",
	"4V4WC9EH2KN211974",
	"4V4WC9EH4KN211975",
	"5KJJABAV38PY97988",
	"5KJJBHD59GLHH9024",
	"5KJJBHDR0KLKB0099",
	"5KJJBHDR0LLLH9251",
	"5KJJBHDR2LLLH9252",
	"5KJJBHDR4JLJW2190",
	"5KJJBHDRXHLJA5563",
	"1FTYE9ZM2JKA15782",
	"5PVNJ8JR4L4S51843",
	"5PVNV8JR8G4S50995",
	"1FUJGEDV7HLJH1054",
	"3AKJHLDV4KSKG0898",
	"5PVNV8JR0G4S51011",
	"5PVNV8JR6D4S50652",
	"3AKJHLDV0LSMD8291",
	"5PVNV8JR2G4S50930",
	"5PVNV8JRXF4S50849",
	"5PVNV8JR9L4S51423",
	"NM0LS7E21K1414165",
	"1FDUF5GY1HEC32433",
	"1FD8W3HT1CEB61736",
	"1FDAF56RX8EA57144",
	"1FDAX57R68EA56715",
	"1FDWW36S91EA76982",
	"1GBE4C3988F402352",
	"1GDG6H1P0LJ606156",
	"1GDJK746X9E129932",
	"1GDJK746X9F143999",
	"3D6WZ4ET1BG502308",
	"3FDWF36F9YMA22492",
	"4P5DL1428D1181090",
	"4P5GN182751071215",
	"JH2RD06099K700403",
	"1PTF7ATH019006505",
	"1TDH40026CB133265",
	"1XKTD49X1AJ266734",
	"1XKWDB9X5XJ816334",
	"1XP5DB8X7TN386123",
	"5UJFC48288T000412",
	"13N24830461533698",
	"5MC2226289P009959",
	"1FUJGLDR3HLHG7529",
	"1FUJGLDR5HLHG7533",
	"1M1AW07Y6HM083906",
	"3AKJHHDR1JSJP3978",
	"1UYVS25378U434310",
	"1GRAA0627CW701832",
	"1GRAA0626CW701840",
	"1RNF45A22GR038425",
	"1RNF45A21GR039517",
	"1GRDM9022CH714185",
	"1RNF46A467R018959",
	"1FUJGLDR5DSBT8309",
	"3AKNGLDR6DSBS6976",
	"1FUJGLDR0CLBH9063",
	"3AKNGLD53GSGU9695",
	"1GRAP0620DJ638246",
	"1JJV532D6CL712810",
	"3H3V532C7DT111172",
	"5V8VC5324DM305424",
	"1NKYD39X7FJ460282",
	"1FDNF80C9SVA71115",
	"5PVNJ8JV5D4S55155",
	"4V4NC9EJ69N277601",
	"4V4NC9EH7KN203221",
	"1GRAA0626EB702853",
	"1GRAA0624GB711473",
	"1XP5DB9X65D854608",
	"1XKWDB9X86R150537",
	"1PNV532B1YK226899",
	"1JJV533W75L941290",
	"1JJV533W95L941291",
	"3H3V533C7WT093032",
	"1GRAA06237S701341",
	"1JJV532W67L003348",
	"1JJV532WX7L003353",
	"1GRAA06217S701354",
	"4WWBGB6N58N613120",
	"4WWBGB6N78N613121",
}

var invalidVins = []string{
	"4P5U7141892138222",
	"5UJFC48258T000648",
	"4LF34820153527554",
	"4WW5482AJH6625349",
	"5MAPA4524HA037015",
	"1UYVS2531L7768516",
	"1DW1A5325KBA29914",
	"1GRAA0920GE701991",
	"5MAPA4825GA035770",
	"5MAPA4827GA035771",
}

var parseErrVins = []string{
	"3N1CN8AE3LL877941",
	"2G1WD5E37C1310887",
	"3G1TC5CF6BL123916",
}
