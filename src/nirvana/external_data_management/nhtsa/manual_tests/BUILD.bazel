load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "manual_tests_test",
    srcs = ["nhtsa_db_test.go"],
    tags = ["manual"],
    deps = [
        "//nirvana/common-go/iter_utils",
        "//nirvana/external_data_management/nhtsa",
        "//nirvana/external_data_management/nhtsa/enums",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/testloader",
        "//nirvana/servers/vehicles:proto",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
