package nhtsa

import (
	"context"
	"strings"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"

	"nirvanatech.com/nirvana/servers/vehicles"

	"github.com/imroc/req"
)

// VpicClientImpl uses NHTSA vPIC REST APIs to decode VINs
type VpicClientImpl struct {
	vs vehicles.VehiclesServiceClient
}

func (cl *VpicClientImpl) GetVINRecord(ctx context.Context, vin string) (*VINDetails, error) {
	req.SetTimeout(1 * time.Minute)
	r, err := req.Get("https://vpic.nhtsa.dot.gov/api/vehicles/decodevinextended/" + vin + "?format=json")
	if err != nil {
		return nil, errors.Wrap(err, "couldn't fetch from VPIC")
	}
	res := nhtsaVINRecord{}
	err = r.ToJSON(&res)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't parse response")
	}
	vehicleMake, err := res.Make()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching make")
	}
	model, err := res.Model()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching model")
	}
	modelYear, err := res.ModelYear()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching model year")
	}
	manufacturer, err := res.Manufacturer()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching manufacturer")
	}
	trim, err := res.Trim()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching trim")
	}
	rawType, err := res.RawVehicleType()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching raw vehicle type")
	}
	typ, err := res.Type()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching type")
	}
	bodyCls, err := res.BodyClass()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching body class")
	}

	weightCls, err := res.WeightClass()
	if err != nil {
		return nil, errors.Wrap(err, "error fetching weight class")
	}

	vinDetails := &VINDetails{
		Make:           strings.ToUpper(vehicleMake),
		Model:          strings.ToUpper(model),
		ModelYear:      modelYear,
		Manufacturer:   strings.ToUpper(manufacturer),
		Trim:           strings.ToUpper(trim),
		RawVehicleType: rawType,
		Type:           *typ,
		BodyClass:      strings.ToUpper(bodyCls),
		WeightClass:    weightCls,
	}
	if err := cl.storeVINDetails(ctx, vin, vinDetails); err != nil {
		log.Error(ctx, "failed to write VIN Vehicle node to vehicles service", log.Err(err))
	}
	return vinDetails, nil
}

// Stores VIN details in vehicle service
func (cl *VpicClientImpl) storeVINDetails(ctx context.Context, vin string, details *VINDetails) error {
	if cl.vs == nil {
		return nil
	}
	node := PrepareVinNode(vin, details)
	_, err := cl.vs.UpsertNodesEdges(ctx, &vehicles.UpsertNodesEdgesRequest{Nodes: node})
	return err
}

var _ Client = &VpicClientImpl{}

func NewVpicClientImpl(vs vehicles.VehiclesServiceClient) *VpicClientImpl {
	return &VpicClientImpl{vs: vs}
}
