package enums

//go:generate go run github.com/dmarkham/enumer -type=WeightClass -json
type WeightClass int

const (
	WeightClassA WeightClass = iota
	WeightClassB
	WeightClassC
	WeightClassD
	WeightClassE
	WeightClassF
	WeightClassG
	WeightClassH
	WeightClass3
	WeightClass4
	WeightClass5
	WeightClass6
	WeightClass7
	WeightClass8
	WeightClassNil
)

//go:generate go run github.com/dmarkham/enumer -type=VehicleType -json
type VehicleType int

const (
	VehicleTypeIncompleteVehicle VehicleType = iota
	VehicleTypeMotorcycle
	VehicleTypeTrailer
	VehicleTypeTruck
	VehicleTypeNil
	VehicleTypePassengerCar
	VehicleTypeMultipurposePassengerVehicle
	VehicleTypeBus
)

//go:generate go run github.com/dmarkham/enumer -type=ErrorCode
type ErrorCode int

const (
	// ErrorCode0 : VIN decoded clean. Check Digit (9th position) is correct
	ErrorCode0 ErrorCode = iota

	// ErrorCode1 : Check Digit (9th position) does not calculate properly
	ErrorCode1

	// ErrorCode2 : VIN corrected, error in one position
	ErrorCode2

	// ErrorCode3 : VIN corrected, error in one position (assuming Check Digit is correct)
	ErrorCode3

	// ErrorCode4 : VIN corrected, error in one position only (indicated by ! in Suggested VIN), multiple matches found
	ErrorCode4

	// ErrorCode5 : VIN has errors in few positions
	ErrorCode5

	// ErrorCode6 : Incomplete VIN
	ErrorCode6

	// ErrorCode7 : Manufacturer is not registered with NHTSA for sale or importation in the U.S. for use on U.S roads
	ErrorCode7

	// ErrorCode8 : No detailed data available currently
	ErrorCode8

	// ErrorCode9 : Glider Warning - A "Glider" is not a "motor vehicle", as defined in 49 U.S.C. 30102(a)(6),
	// and cannot be assigned a VIN that meets 49 CFR Part 565 requirements
	ErrorCode9

	// ErrorCode10 : Off-road Vehicle Warning – This is not a vehicle identification number (VIN) for a motor vehicle.
	// This indicates that the manufacturer did not certify this product as complying with the Federal motor
	// vehicle safety standards which are applicable to motor vehicles as defined at 49 U.S.C. 30102.
	ErrorCode10

	// ErrorCode11 : Incorrect Model Year - Position 10 does not match valid model year codes (I, O, Q, U, Z, 0).
	// Decoded data may not be accurate.
	ErrorCode11

	// ErrorCode12 : Model Year Warning - Model Year entered for decoding with VIN does not match the model year
	// based on the 10th position in VIN.
	ErrorCode12

	// ErrorCode14 : Unable to provide information for some of the characters in the VIN, based on the manufacturer submission.
	ErrorCode14

	// ErrorCode400 : Invalid Characters Present
	ErrorCode400
)
