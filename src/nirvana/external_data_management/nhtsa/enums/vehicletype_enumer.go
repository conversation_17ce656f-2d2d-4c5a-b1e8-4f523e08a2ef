// Code generated by "enumer -type=VehicleType -json"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _VehicleTypeName = "VehicleTypeIncompleteVehicleVehicleTypeMotorcycleVehicleTypeTrailerVehicleTypeTruckVehicleTypeNilVehicleTypePassengerCarVehicleTypeMultipurposePassengerVehicleVehicleTypeBus"

var _VehicleTypeIndex = [...]uint8{0, 28, 49, 67, 83, 97, 120, 159, 173}

const _VehicleTypeLowerName = "vehicletypeincompletevehiclevehicletypemotorcyclevehicletypetrailervehicletypetruckvehicletypenilvehicletypepassengercarvehicletypemultipurposepassengervehiclevehicletypebus"

func (i VehicleType) String() string {
	if i < 0 || i >= VehicleType(len(_VehicleTypeIndex)-1) {
		return fmt.Sprintf("VehicleType(%d)", i)
	}
	return _VehicleTypeName[_VehicleTypeIndex[i]:_VehicleTypeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _VehicleTypeNoOp() {
	var x [1]struct{}
	_ = x[VehicleTypeIncompleteVehicle-(0)]
	_ = x[VehicleTypeMotorcycle-(1)]
	_ = x[VehicleTypeTrailer-(2)]
	_ = x[VehicleTypeTruck-(3)]
	_ = x[VehicleTypeNil-(4)]
	_ = x[VehicleTypePassengerCar-(5)]
	_ = x[VehicleTypeMultipurposePassengerVehicle-(6)]
	_ = x[VehicleTypeBus-(7)]
}

var _VehicleTypeValues = []VehicleType{VehicleTypeIncompleteVehicle, VehicleTypeMotorcycle, VehicleTypeTrailer, VehicleTypeTruck, VehicleTypeNil, VehicleTypePassengerCar, VehicleTypeMultipurposePassengerVehicle, VehicleTypeBus}

var _VehicleTypeNameToValueMap = map[string]VehicleType{
	_VehicleTypeName[0:28]:         VehicleTypeIncompleteVehicle,
	_VehicleTypeLowerName[0:28]:    VehicleTypeIncompleteVehicle,
	_VehicleTypeName[28:49]:        VehicleTypeMotorcycle,
	_VehicleTypeLowerName[28:49]:   VehicleTypeMotorcycle,
	_VehicleTypeName[49:67]:        VehicleTypeTrailer,
	_VehicleTypeLowerName[49:67]:   VehicleTypeTrailer,
	_VehicleTypeName[67:83]:        VehicleTypeTruck,
	_VehicleTypeLowerName[67:83]:   VehicleTypeTruck,
	_VehicleTypeName[83:97]:        VehicleTypeNil,
	_VehicleTypeLowerName[83:97]:   VehicleTypeNil,
	_VehicleTypeName[97:120]:       VehicleTypePassengerCar,
	_VehicleTypeLowerName[97:120]:  VehicleTypePassengerCar,
	_VehicleTypeName[120:159]:      VehicleTypeMultipurposePassengerVehicle,
	_VehicleTypeLowerName[120:159]: VehicleTypeMultipurposePassengerVehicle,
	_VehicleTypeName[159:173]:      VehicleTypeBus,
	_VehicleTypeLowerName[159:173]: VehicleTypeBus,
}

var _VehicleTypeNames = []string{
	_VehicleTypeName[0:28],
	_VehicleTypeName[28:49],
	_VehicleTypeName[49:67],
	_VehicleTypeName[67:83],
	_VehicleTypeName[83:97],
	_VehicleTypeName[97:120],
	_VehicleTypeName[120:159],
	_VehicleTypeName[159:173],
}

// VehicleTypeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func VehicleTypeString(s string) (VehicleType, error) {
	if val, ok := _VehicleTypeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _VehicleTypeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to VehicleType values", s)
}

// VehicleTypeValues returns all values of the enum
func VehicleTypeValues() []VehicleType {
	return _VehicleTypeValues
}

// VehicleTypeStrings returns a slice of all String values of the enum
func VehicleTypeStrings() []string {
	strs := make([]string, len(_VehicleTypeNames))
	copy(strs, _VehicleTypeNames)
	return strs
}

// IsAVehicleType returns "true" if the value is listed in the enum definition. "false" otherwise
func (i VehicleType) IsAVehicleType() bool {
	for _, v := range _VehicleTypeValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for VehicleType
func (i VehicleType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for VehicleType
func (i *VehicleType) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("VehicleType should be a string, got %s", data)
	}

	var err error
	*i, err = VehicleTypeString(s)
	return err
}
