// Code generated by "enumer -type=ErrorCode"; DO NOT EDIT.

package enums

import (
	"fmt"
	"strings"
)

const _ErrorCodeName = "ErrorCode0ErrorCode1ErrorCode2ErrorCode3ErrorCode4ErrorCode5ErrorCode6ErrorCode7ErrorCode8ErrorCode9ErrorCode10ErrorCode11ErrorCode12ErrorCode14ErrorCode400"

var _ErrorCodeIndex = [...]uint8{0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 111, 122, 133, 144, 156}

const _ErrorCodeLowerName = "errorcode0errorcode1errorcode2errorcode3errorcode4errorcode5errorcode6errorcode7errorcode8errorcode9errorcode10errorcode11errorcode12errorcode14errorcode400"

func (i ErrorCode) String() string {
	if i < 0 || i >= ErrorCode(len(_ErrorCodeIndex)-1) {
		return fmt.Sprintf("ErrorCode(%d)", i)
	}
	return _ErrorCodeName[_ErrorCodeIndex[i]:_ErrorCodeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ErrorCodeNoOp() {
	var x [1]struct{}
	_ = x[ErrorCode0-(0)]
	_ = x[ErrorCode1-(1)]
	_ = x[ErrorCode2-(2)]
	_ = x[ErrorCode3-(3)]
	_ = x[ErrorCode4-(4)]
	_ = x[ErrorCode5-(5)]
	_ = x[ErrorCode6-(6)]
	_ = x[ErrorCode7-(7)]
	_ = x[ErrorCode8-(8)]
	_ = x[ErrorCode9-(9)]
	_ = x[ErrorCode10-(10)]
	_ = x[ErrorCode11-(11)]
	_ = x[ErrorCode12-(12)]
	_ = x[ErrorCode14-(13)]
	_ = x[ErrorCode400-(14)]
}

var _ErrorCodeValues = []ErrorCode{ErrorCode0, ErrorCode1, ErrorCode2, ErrorCode3, ErrorCode4, ErrorCode5, ErrorCode6, ErrorCode7, ErrorCode8, ErrorCode9, ErrorCode10, ErrorCode11, ErrorCode12, ErrorCode14, ErrorCode400}

var _ErrorCodeNameToValueMap = map[string]ErrorCode{
	_ErrorCodeName[0:10]:         ErrorCode0,
	_ErrorCodeLowerName[0:10]:    ErrorCode0,
	_ErrorCodeName[10:20]:        ErrorCode1,
	_ErrorCodeLowerName[10:20]:   ErrorCode1,
	_ErrorCodeName[20:30]:        ErrorCode2,
	_ErrorCodeLowerName[20:30]:   ErrorCode2,
	_ErrorCodeName[30:40]:        ErrorCode3,
	_ErrorCodeLowerName[30:40]:   ErrorCode3,
	_ErrorCodeName[40:50]:        ErrorCode4,
	_ErrorCodeLowerName[40:50]:   ErrorCode4,
	_ErrorCodeName[50:60]:        ErrorCode5,
	_ErrorCodeLowerName[50:60]:   ErrorCode5,
	_ErrorCodeName[60:70]:        ErrorCode6,
	_ErrorCodeLowerName[60:70]:   ErrorCode6,
	_ErrorCodeName[70:80]:        ErrorCode7,
	_ErrorCodeLowerName[70:80]:   ErrorCode7,
	_ErrorCodeName[80:90]:        ErrorCode8,
	_ErrorCodeLowerName[80:90]:   ErrorCode8,
	_ErrorCodeName[90:100]:       ErrorCode9,
	_ErrorCodeLowerName[90:100]:  ErrorCode9,
	_ErrorCodeName[100:111]:      ErrorCode10,
	_ErrorCodeLowerName[100:111]: ErrorCode10,
	_ErrorCodeName[111:122]:      ErrorCode11,
	_ErrorCodeLowerName[111:122]: ErrorCode11,
	_ErrorCodeName[122:133]:      ErrorCode12,
	_ErrorCodeLowerName[122:133]: ErrorCode12,
	_ErrorCodeName[133:144]:      ErrorCode14,
	_ErrorCodeLowerName[133:144]: ErrorCode14,
	_ErrorCodeName[144:156]:      ErrorCode400,
	_ErrorCodeLowerName[144:156]: ErrorCode400,
}

var _ErrorCodeNames = []string{
	_ErrorCodeName[0:10],
	_ErrorCodeName[10:20],
	_ErrorCodeName[20:30],
	_ErrorCodeName[30:40],
	_ErrorCodeName[40:50],
	_ErrorCodeName[50:60],
	_ErrorCodeName[60:70],
	_ErrorCodeName[70:80],
	_ErrorCodeName[80:90],
	_ErrorCodeName[90:100],
	_ErrorCodeName[100:111],
	_ErrorCodeName[111:122],
	_ErrorCodeName[122:133],
	_ErrorCodeName[133:144],
	_ErrorCodeName[144:156],
}

// ErrorCodeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ErrorCodeString(s string) (ErrorCode, error) {
	if val, ok := _ErrorCodeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ErrorCodeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ErrorCode values", s)
}

// ErrorCodeValues returns all values of the enum
func ErrorCodeValues() []ErrorCode {
	return _ErrorCodeValues
}

// ErrorCodeStrings returns a slice of all String values of the enum
func ErrorCodeStrings() []string {
	strs := make([]string, len(_ErrorCodeNames))
	copy(strs, _ErrorCodeNames)
	return strs
}

// IsAErrorCode returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ErrorCode) IsAErrorCode() bool {
	for _, v := range _ErrorCodeValues {
		if i == v {
			return true
		}
	}
	return false
}
