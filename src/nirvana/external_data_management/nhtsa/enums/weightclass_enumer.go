// Code generated by "enumer -type=WeightClass -json"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _WeightClassName = "WeightClassAWeightClassBWeightClassCWeightClassDWeightClassEWeightClassFWeightClassGWeightClassHWeightClass3WeightClass4WeightClass5WeightClass6WeightClass7WeightClass8WeightClassNil"

var _WeightClassIndex = [...]uint8{0, 12, 24, 36, 48, 60, 72, 84, 96, 108, 120, 132, 144, 156, 168, 182}

const _WeightClassLowerName = "weightclassaweightclassbweightclasscweightclassdweightclasseweightclassfweightclassgweightclasshweightclass3weightclass4weightclass5weightclass6weightclass7weightclass8weightclassnil"

func (i WeightClass) String() string {
	if i < 0 || i >= WeightClass(len(_WeightClassIndex)-1) {
		return fmt.Sprintf("WeightClass(%d)", i)
	}
	return _WeightClassName[_WeightClassIndex[i]:_WeightClassIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _WeightClassNoOp() {
	var x [1]struct{}
	_ = x[WeightClassA-(0)]
	_ = x[WeightClassB-(1)]
	_ = x[WeightClassC-(2)]
	_ = x[WeightClassD-(3)]
	_ = x[WeightClassE-(4)]
	_ = x[WeightClassF-(5)]
	_ = x[WeightClassG-(6)]
	_ = x[WeightClassH-(7)]
	_ = x[WeightClass3-(8)]
	_ = x[WeightClass4-(9)]
	_ = x[WeightClass5-(10)]
	_ = x[WeightClass6-(11)]
	_ = x[WeightClass7-(12)]
	_ = x[WeightClass8-(13)]
	_ = x[WeightClassNil-(14)]
}

var _WeightClassValues = []WeightClass{WeightClassA, WeightClassB, WeightClassC, WeightClassD, WeightClassE, WeightClassF, WeightClassG, WeightClassH, WeightClass3, WeightClass4, WeightClass5, WeightClass6, WeightClass7, WeightClass8, WeightClassNil}

var _WeightClassNameToValueMap = map[string]WeightClass{
	_WeightClassName[0:12]:         WeightClassA,
	_WeightClassLowerName[0:12]:    WeightClassA,
	_WeightClassName[12:24]:        WeightClassB,
	_WeightClassLowerName[12:24]:   WeightClassB,
	_WeightClassName[24:36]:        WeightClassC,
	_WeightClassLowerName[24:36]:   WeightClassC,
	_WeightClassName[36:48]:        WeightClassD,
	_WeightClassLowerName[36:48]:   WeightClassD,
	_WeightClassName[48:60]:        WeightClassE,
	_WeightClassLowerName[48:60]:   WeightClassE,
	_WeightClassName[60:72]:        WeightClassF,
	_WeightClassLowerName[60:72]:   WeightClassF,
	_WeightClassName[72:84]:        WeightClassG,
	_WeightClassLowerName[72:84]:   WeightClassG,
	_WeightClassName[84:96]:        WeightClassH,
	_WeightClassLowerName[84:96]:   WeightClassH,
	_WeightClassName[96:108]:       WeightClass3,
	_WeightClassLowerName[96:108]:  WeightClass3,
	_WeightClassName[108:120]:      WeightClass4,
	_WeightClassLowerName[108:120]: WeightClass4,
	_WeightClassName[120:132]:      WeightClass5,
	_WeightClassLowerName[120:132]: WeightClass5,
	_WeightClassName[132:144]:      WeightClass6,
	_WeightClassLowerName[132:144]: WeightClass6,
	_WeightClassName[144:156]:      WeightClass7,
	_WeightClassLowerName[144:156]: WeightClass7,
	_WeightClassName[156:168]:      WeightClass8,
	_WeightClassLowerName[156:168]: WeightClass8,
	_WeightClassName[168:182]:      WeightClassNil,
	_WeightClassLowerName[168:182]: WeightClassNil,
}

var _WeightClassNames = []string{
	_WeightClassName[0:12],
	_WeightClassName[12:24],
	_WeightClassName[24:36],
	_WeightClassName[36:48],
	_WeightClassName[48:60],
	_WeightClassName[60:72],
	_WeightClassName[72:84],
	_WeightClassName[84:96],
	_WeightClassName[96:108],
	_WeightClassName[108:120],
	_WeightClassName[120:132],
	_WeightClassName[132:144],
	_WeightClassName[144:156],
	_WeightClassName[156:168],
	_WeightClassName[168:182],
}

// WeightClassString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func WeightClassString(s string) (WeightClass, error) {
	if val, ok := _WeightClassNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _WeightClassNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to WeightClass values", s)
}

// WeightClassValues returns all values of the enum
func WeightClassValues() []WeightClass {
	return _WeightClassValues
}

// WeightClassStrings returns a slice of all String values of the enum
func WeightClassStrings() []string {
	strs := make([]string, len(_WeightClassNames))
	copy(strs, _WeightClassNames)
	return strs
}

// IsAWeightClass returns "true" if the value is listed in the enum definition. "false" otherwise
func (i WeightClass) IsAWeightClass() bool {
	for _, v := range _WeightClassValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for WeightClass
func (i WeightClass) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for WeightClass
func (i *WeightClass) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("WeightClass should be a string, got %s", data)
	}

	var err error
	*i, err = WeightClassString(s)
	return err
}
