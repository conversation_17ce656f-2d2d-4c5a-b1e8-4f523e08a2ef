package nhtsa_test

import (
	"context"
	"math/rand"
	"testing"

	nhtsaEnums "nirvanatech.com/nirvana/external_data_management/nhtsa/enums"

	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/servers/vehicles"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/iter_utils"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/external_data_management/nhtsa"
)

var shouldSampleValidVinNumbers = true

type vinTestCase struct {
	vin     string
	make    string
	model   string
	year    string
	vehType nhtsaEnums.VehicleType
	wtClass nhtsaEnums.WeightClass
}

// Note: Disabling `TestDecode` & `TestNoErrorsOnValidVins`
// Network tests as they seem to be failing on Github Actions Runner
// due to DNS resolution errors
func DISABLED_TestDecode(t *testing.T) {
	// note that there are some differences from verisk, they are noted in comments inline
	cases := []vinTestCase{
		{
			vin:     "3AKJGLDRXKDKH1902",
			make:    "FREIGHTLINER",
			model:   "CASCADIA",
			year:    "2019",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1FDUF5HT4CED07122",
			make: "FORD",
			// verisk returns 'F550'
			model:   "F-550",
			year:    "2012",
			vehType: nhtsaEnums.VehicleTypeIncompleteVehicle,
			wtClass: nhtsaEnums.WeightClass5,
		},
		{
			vin:     "1GB4K0C86DF238472",
			make:    "CHEVROLET",
			model:   "SILVERADO",
			year:    "2013",
			vehType: nhtsaEnums.VehicleTypeIncompleteVehicle,
			wtClass: nhtsaEnums.WeightClass3,
		},
		{
			vin:  "1GDJK39183E331827",
			make: "GMC",
			// verisk returns 'NEW SIERRA'
			model:   "SIERRA",
			year:    "2003",
			vehType: nhtsaEnums.VehicleTypeIncompleteVehicle,
			wtClass: nhtsaEnums.WeightClass3,
		},
		{
			vin:  "3AKJGLD51FSGH8069",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2015",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "3AKJGLD57FSGH8075",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2015",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1M1AW21Y8GM052370",
			make: "MACK",
			// verisk returns 'NEW SIERRA'
			model:   "CXU",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		// this case gives error code 1
		{
			vin:  "1M1AW21Y8GM052371",
			make: "MACK",
			// verisk returns 'NEW SIERRA'
			model:   "CXU",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1XKYDP9X8GJ489076",
			make: "KENWORTH",
			// verisk returns 'NEW SIERRA'
			model:   "T680",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1XKYDP9X1GJ489078",
			make: "KENWORTH",
			// verisk returns 'NEW SIERRA'
			model:   "T680",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1XKYDP9X4GJ489074",
			make: "KENWORTH",
			// verisk returns 'NEW SIERRA'
			model:   "T680",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1XKYDP9XXGJ489077",
			make: "KENWORTH",
			// verisk returns 'NEW SIERRA'
			model:   "T680",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1XKYDP9X3GJ489079",
			make: "KENWORTH",
			// verisk returns 'NEW SIERRA'
			model:   "T680",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "4V4NC9EH5GN955698",
			make: "VOLVO TRUCK",
			// verisk returns 'NEW SIERRA'
			model:   "VNL",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "4V4NC9EH7GN955699",
			make: "VOLVO TRUCK",
			// verisk returns 'NEW SIERRA'
			model:   "VNL",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "4V4NC9EHXGN955700",
			make: "VOLVO TRUCK",
			// verisk returns 'NEW SIERRA'
			model:   "VNL",
			year:    "2016",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1FUJGLDRXHLHG7530",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2017",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1FUJGLDRXHLHG7527",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2017",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1FUJGLDR1HLHG7531",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2017",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1FUJGLDR1HLHG7528",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2017",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1FUJGLDR3HLHG7532",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2017",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1FUJGLDR3HLHG7529",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2017",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
		{
			vin:  "1FUJGLDR5HLHG75",
			make: "FREIGHTLINER",
			// verisk returns 'NEW SIERRA'
			model:   "CASCADIA",
			year:    "2017",
			vehType: nhtsaEnums.VehicleTypeTruck,
			wtClass: nhtsaEnums.WeightClass8,
		},
	}
	var vehiclesServiceClient vehicles.VehiclesServiceClient
	defer testloader.RequireStart(t, &vehiclesServiceClient).RequireStop()

	cl := nhtsa.NewVpicClientImpl(vehiclesServiceClient)

	for _, tc := range cases {
		t.Run(tc.vin, func(t *testing.T) {
			a := require.New(t)
			compareRecord := func(r *nhtsa.VINDetails) {
				a.Equal(tc.make, r.Make)
				a.Equal(tc.model, r.Model)
				a.Equal(tc.year, r.ModelYear)
			}
			r, err := cl.GetVINRecord(context.Background(), tc.vin)
			a.NoError(err)
			compareRecord(r)
		})
	}
}

func DISABLED_TestNoErrorsOnValidVins(t *testing.T) {
	var vsClient vehicles.VehiclesServiceClient
	defer testloader.RequireStart(t, &vsClient).RequireStop()
	cl := nhtsa.NewVpicClientImpl(vsClient)
	records := make([]nhtsa.VINDetails, 0)

	var vinNumbers []string
	if shouldSampleValidVinNumbers {
		vinNumbers = sampleValidVinNumbers()
	} else {
		vinNumbers = test_utils.ValidVinNumbers
	}

	for vin := range iter_utils.StringSetFromSlice(vinNumbers) {
		t.Run(vin, func(t *testing.T) {
			record, err := cl.GetVINRecord(context.Background(), vin)
			if valid := assert.Nil(t, err); valid {
				records = append(records, *record)
			}
		})
	}
}

func sampleValidVinNumbers() []string {
	sampled := make([]string, 0)
	numSamples := 10
	for i := 0; i < numSamples; i++ {
		// This could double count, but we don't care
		idx := rand.Intn(len(test_utils.ValidVinNumbers))
		sampled = append(sampled, test_utils.ValidVinNumbers[idx])
	}
	return sampled
}
