package nhtsa

import (
	"context"

	"nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
)

type Client interface {
	GetVINRecord(ctx context.Context, vin string) (*VINDetails, error)
}

type VINDetails struct {
	Make  string
	Model string
	// Should we make this an int/date?
	ModelYear      string
	Manufacturer   string
	Trim           string
	RawVehicleType string
	Type           enums.VehicleType
	BodyClass      string

	// Nil for trailers
	WeightClass enums.WeightClass
	// Decoding error
	DecodeError *VinDecodeError
}
