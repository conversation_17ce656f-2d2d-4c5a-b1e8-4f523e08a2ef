package sentry_inputs

import (
	"context"
	"database/sql"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/fmcsa/models"
)

func newClientImpl(deps deps) *clientImpl {
	return &clientImpl{
		FMCSAWrapper: deps.FMCSAWrapper,
	}
}

type clientImpl struct {
	FMCSAWrapper fmcsa.DataWrapper
}

func (c *clientImpl) GetLatestSentryDumpDateForDOT(
	ctx context.Context,
	dotNumber int64,
) (*time.Time, error) {
	resp, err := c.<PERSON><PERSON>Wrapper.GetSentryInputsByDot(ctx, dotNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get latest sentry inputs for dotNumber: %s", dotNumber)
	}
	return pointer_utils.ToPointer(time.Time(resp.SentryDumpDate)), nil
}

func (c *clientImpl) GetMonthlySentryInputsForDOTAndDumpDate(ctx context.Context, dotNumber int64, dumpDate time.Time) (*models.PerDotSentryFmcsaInput, error) {
	resp, err := c.FMCSAWrapper.GetSentryInputsByDotDate(ctx, &dumpDate, dotNumber)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to get sentry inputs for dotNumber: %s and dumpDate: %s",
			dotNumber,
			dumpDate,
		)
	}
	return resp, nil
}

// GetSentryInputsForDOTAndDates returns a record of SentryInputs which is filtered by doing an exact match with
// parameter dumpDate and a Month-Year matching between the records and the parameter inputsDate.
func (c *clientImpl) GetSentryInputsForDOTAndDates(
	ctx context.Context,
	dotNumber int64,
	dumpDate time.Time,
	inputsDate time.Time,
) (*models.SentryInputs, error) {
	record, err := c.GetMonthlySentryInputsForDOTAndDumpDate(ctx, dotNumber, dumpDate)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to fetch monthly sentry inputs for DOTNumber: %s. dumpDate: %s",
			dotNumber,
			dumpDate,
		)
	}

	for _, inputs := range record.MonthlyInputs {
		if inputs.Date != nil && (inputs.Date.Year() == inputsDate.Year() && inputs.Date.Month() == inputsDate.Month()) {
			return pointer_utils.ToPointer(inputs), nil
		}
	}
	return nil, errors.Wrap(sql.ErrNoRows, "no sentry input found for requested dates")
}

var _ Client = &clientImpl{}
