package sentry_inputs

import (
	"context"
	"time"

	"nirvanatech.com/nirvana/fmcsa/models"
)

//go:generate go run go.uber.org/mock/mockgen -destination=client_mock.go -package=sentry_inputs nirvanatech.com/nirvana/external_data_management/internal/sentry_inputs Client
type Client interface {
	GetLatestSentryDumpDateForDOT(
		ctx context.Context,
		dotNumber int64,
	) (*time.Time, error)

	GetMonthlySentryInputsForDOTAndDumpDate(
		ctx context.Context,
		dotNumber int64,
		dumpDate time.Time,
	) (*models.PerDotSentryFmcsaInput, error)

	GetSentryInputsForDOTAndDates(
		ctx context.Context,
		dotNumber int64,
		dumpDate time.Time,
		inputsDate time.Time,
	) (*models.SentryInputs, error)
}
