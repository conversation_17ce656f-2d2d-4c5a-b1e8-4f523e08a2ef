load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "sentry_inputs",
    srcs = [
        "client.go",
        "client_mock.go",
        "deps.go",
        "fx.go",
        "interfaces.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/internal/sentry_inputs",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)
