// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/internal/sentry_inputs (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination=client_mock.go -package=sentry_inputs nirvanatech.com/nirvana/external_data_management/internal/sentry_inputs Client
//

// Package sentry_inputs is a generated GoMock package.
package sentry_inputs

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
	models "nirvanatech.com/nirvana/fmcsa/models"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
	isgomock struct{}
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// GetLatestSentryDumpDateForDOT mocks base method.
func (m *MockClient) GetLatestSentryDumpDateForDOT(ctx context.Context, dotNumber int64) (*time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestSentryDumpDateForDOT", ctx, dotNumber)
	ret0, _ := ret[0].(*time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestSentryDumpDateForDOT indicates an expected call of GetLatestSentryDumpDateForDOT.
func (mr *MockClientMockRecorder) GetLatestSentryDumpDateForDOT(ctx, dotNumber any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestSentryDumpDateForDOT", reflect.TypeOf((*MockClient)(nil).GetLatestSentryDumpDateForDOT), ctx, dotNumber)
}

// GetMonthlySentryInputsForDOTAndDumpDate mocks base method.
func (m *MockClient) GetMonthlySentryInputsForDOTAndDumpDate(ctx context.Context, dotNumber int64, dumpDate time.Time) (*models.PerDotSentryFmcsaInput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonthlySentryInputsForDOTAndDumpDate", ctx, dotNumber, dumpDate)
	ret0, _ := ret[0].(*models.PerDotSentryFmcsaInput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonthlySentryInputsForDOTAndDumpDate indicates an expected call of GetMonthlySentryInputsForDOTAndDumpDate.
func (mr *MockClientMockRecorder) GetMonthlySentryInputsForDOTAndDumpDate(ctx, dotNumber, dumpDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonthlySentryInputsForDOTAndDumpDate", reflect.TypeOf((*MockClient)(nil).GetMonthlySentryInputsForDOTAndDumpDate), ctx, dotNumber, dumpDate)
}

// GetSentryInputsForDOTAndDates mocks base method.
func (m *MockClient) GetSentryInputsForDOTAndDates(ctx context.Context, dotNumber int64, dumpDate, inputsDate time.Time) (*models.SentryInputs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSentryInputsForDOTAndDates", ctx, dotNumber, dumpDate, inputsDate)
	ret0, _ := ret[0].(*models.SentryInputs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSentryInputsForDOTAndDates indicates an expected call of GetSentryInputsForDOTAndDates.
func (mr *MockClientMockRecorder) GetSentryInputsForDOTAndDates(ctx, dotNumber, dumpDate, inputsDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSentryInputsForDOTAndDates", reflect.TypeOf((*MockClient)(nil).GetSentryInputsForDOTAndDates), ctx, dotNumber, dumpDate, inputsDate)
}
