package cacheserver

import (
	"context"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/mvr"
)

const (
	statusMetricTag    = "status"
	failedMetricTag    = "failed"
	succeededMetricTag = "succeeded"
	errorMetricTag     = "error"
)

func MetricsUnaryServerInterceptor(
	metricsClient statsd.Statter,
) func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		resp, err := handler(ctx, req)
		switch info.FullMethod {
		case "/mvr.MVR/FetchMVR":
			var tags []statsd.Tag
			if err != nil {
				tags = append(tags, statsd.Tag{statusMetricTag, failedMetricTag})
				if errors.Is(err, mvr.ErrRequestNotAccepted) {
					tags = append(tags, statsd.Tag{errorMetricTag, "NotAcceptedByVerisk"})
				} else {
					tags = append(tags, statsd.Tag{errorMetricTag, "Other"})
				}
			} else {
				tags = append(tags, statsd.Tag{statusMetricTag, succeededMetricTag})
			}
			_ = metricsClient.Inc(
				"FetchMVR", 1, 1, tags...,
			)
		case "/mvr.MVR/PollMVR":
			var tags []statsd.Tag
			if err != nil {
				tags = append(tags, statsd.Tag{statusMetricTag, failedMetricTag})
				if errors.Is(err, mvr.ErrNotYetAvailable) {
					tags = append(tags, statsd.Tag{errorMetricTag, "NotAvailableYet"})
				} else {
					tags = append(tags, statsd.Tag{errorMetricTag, "Other"})
				}
			} else {
				tags = append(tags, statsd.Tag{statusMetricTag, succeededMetricTag})
			}
			_ = metricsClient.Inc(
				"PollMVR", 1, 1, tags...,
			)
		default:

		}
		return resp, err
	}
}
