// Package cacheserver implements a middleware cache layer for mvr reports.
// It provides an implementation pmvr.MVRServer which fetches entries from
// cache if they exist and forwards them to verisk otherwise.
// This library also follows the same pattern as verisk, in which first a POST
// request is made to load the report and then GET request is used for polling
// for the availability.
// See src/nirvana/db-api/migrations/000040_add_mvr_caching_table.up.sql for the
// cache table.
// We store raw xml report in S3 and store the uri in cache table's column
// report_uri.
// We use cache entry uuid as request_id because verisk request_id may not be
// unique over a very long period.
// The algorithm is:
// on FetchMVR:
//  1. If a valid entry for driver does not exist in cache( which is persistent):
//     - send a request to verisk, and store request_id returned by verisk in cache
//     as a new entry.
//     - return the entry uuid as response.
//  2. If a valid entry exists in the cache but not the report:
//     - if this entry is less than an hour old, return entry uuid else remove this
//     entry from cache and follow 1.
//  3. If a valid entry with report exist in the cache:
//     - return that entry's uuid.
//
// on PollMVR:
// 1. If the report for entry uuid exists in cache:
// - return the report
// 2. If the report for entry uuid does not exist in cache:
// - fetch the report for this entry's verisk_request_id from verisk and:
//   - if verisk returns the report, store it in cache and return 200 with
//     report.
//   - if verisk returns a retry later error, we return DEADLINE_EXCEEDED error
//     code
//   - if verisk returns any other error, return INVALID_ARGUMENT or UNKNOWN
//
// We never remove old reports from cache.
// Staleness is handled when looking up entries in cache and is measured in days.
// eg: if we get a request for mvr report newer than 5 days and we have a report
// 30 days old, that is equivalent to not having the report and we follow 1. on
// POST request.
// Default staleness is 90 days.
// NOTE: We return the latest entry containing the report whenever possible.
// eg: if we have a request for mvr report newer than 30 days and we have a
// report 25 days old and a entry without report(request in progress) 5 days
// old, we'll just return the entry uuid for 25 days old report.
package cacheserver
