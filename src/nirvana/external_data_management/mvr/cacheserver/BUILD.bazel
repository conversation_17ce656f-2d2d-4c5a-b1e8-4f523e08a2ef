load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "cacheserver",
    srcs = [
        "client.go",
        "doc.go",
        "fx.go",
        "helpers.go",
        "lazy_client.go",
        "lexisnexis.go",
        "metrics_interceptor.go",
        "server.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/mvr/cacheserver",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/grpc/middleware",
        "//nirvana/common-go/log",
        "//nirvana/common-go/metering",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/type_utils",
        "//nirvana/db-api/db_models/mvr_cache",
        "//nirvana/external_data_management/mvr",
        "//nirvana/external_data_management/mvr/verisk",
        "//nirvana/infra/config",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_hooklift_gowsdl//soap",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_golang_google_grpc//:grpc",
        "@org_uber_go_atomic//:atomic",
    ],
)

go_test(
    name = "cacheserver_test",
    srcs = ["helpers_test.go"],
    embed = [":cacheserver"],
    deps = ["@com_github_stretchr_testify//assert"],
)
