package cacheserver

import (
	"strconv"

	"github.com/cockroachdb/errors"
)

const attractScoreSuccessProcessingStatus = "501"

func ParseAttractScoreFromLexisResponse(resp *HandleRequestResponse) (score int64, err error) {
	defer func() {
		if r := recover(); r != nil {
			score = -1
			err = errors.Newf("panic while parsing attract score: %v", r)
		}
	}()
	// ProcessingStatus: 501 is "Complete", 503 is "Not Found" & 504 is "Insufficient data to score".
	processingStatus := resp.ResponseEx.Response.ProductResults.ScoreResult.TransactionDetailsEx.ProcessingStatus
	if processingStatus != attractScoreSuccessProcessingStatus {
		return -1, errors.Newf("unsuccessful api response, processing status: %s", processingStatus)
	}
	results := resp.ResponseEx.Response.ProductResults.ScoreResult.Result
	for _, result := range results {
		for _, model := range result.Models.Model {
			if model.Name == lnAttractScoreModel {
				return strconv.ParseInt(model.Score, 10, 0)
			}
		}
	}
	return -1, errors.Newf("couldn't find score in response")
}

type NationalCreditFileReport struct {
	Result *InsNcfProductReport
}

func ParseNCFFromLexisResponse(
	resp *HandleRequestResponse,
) (report *NationalCreditFileReport, td *TransactionDetailsEx, err error) {
	defer func() {
		if r := recover(); r != nil {
			report = nil
			err = errors.Newf("panic while parsing ncf report: %v", r)
		}
	}()

	td = resp.ResponseEx.Response.ProductResults.NationalCreditFileResults.TransactionDetailsEx
	report = &NationalCreditFileReport{}
	if len(resp.ResponseEx.Response.ProductResults.NationalCreditFileResults.Result) > 0 {
		report.Result = resp.ResponseEx.Response.ProductResults.NationalCreditFileResults.Result[0].NcfReport.NcfProductReport
	} else {
		return nil, td, errors.New("no ncf result found in response")
	}

	return report, td, nil
}
