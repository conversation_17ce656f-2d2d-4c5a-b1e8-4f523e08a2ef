package cacheserver

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/hooklift/gowsdl/soap"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"go.uber.org/atomic"

	"nirvanatech.com/nirvana/common-go/crypto_utils"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/metering"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/type_utils"
	mvr_cache_db "nirvanatech.com/nirvana/db-api/db_models/mvr_cache"
	"nirvanatech.com/nirvana/external_data_management/mvr"
	"nirvanatech.com/nirvana/external_data_management/mvr/verisk"
	"nirvanatech.com/nirvana/infra/config"
)

const (
	DefaultVeriskStalenessDays     int64 = 180
	DefaultLexisNexisStalenessDays int64 = 365
	HourlyVeriskRequestQuota             = 300
	HourlyLexisRequestQuota              = 300
	unparseableReportS3Prefix            = "unparseableReport"
)

const (
	lnAttractScoreOptionID = ID("CDA01")
	lnAttractScoreModel    = "C118"

	lnNCFOptionID = ID("NCFO1")

	lnRequesterName                    = "Nirvana"
	lnProdAccountNumberForAttractScore = "491789CDA"
	lnProdAccountNumberForNCF          = "491789NCF"
	lnTestAccountNumberForNCF          = "491789NCT"

	veriskBillableRequestMetric     = "VeriskBillableRequest"
	lexisNexisBillableRequestMetric = "LexisNexisBillableRequest"
	lnNCFBillableRequestMetric      = "LexisNexisNCFBillableRequest"
	NCFNoHitStatusCode              = "M"
)

const (
	meteringEventType   = "fetch-mvr-verisk-call"
	meteringEventSource = "mvr-server"
)

type serverImpl struct {
	veriskClient                               mvr.MVRClient
	db                                         *sql.DB
	fileUploadManager                          file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	metricsClient                              statsd.Statter
	riskXmlOrderHandler                        RiskXMLOrderHandler
	veriskHourlyAvailableQuota                 *atomic.Int64
	lexisNexisAttractScoreHourlyAvailableQuota *atomic.Int64
	lexisNexisNCFHourlyAvailableQuota          *atomic.Int64
	lnAccountNumberForNCF                      string
	ncfMutex                                   sync.Mutex
	meteringClient                             *metering.Client
	cryptoClient                               *crypto_utils.Client
}

func NewServer(
	veriskClient mvr.MVRClient,
	db *sql.DB,
	fileUploadManager file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen],
	lexisNexisClient *soap.Client,
	metricsClient statsd.Statter,
	cfg *config.Config,
	meteringClient *metering.Client,
	cryptoClient *crypto_utils.Client,
) mvr.MVRServer {
	lnAccountNumberForNCF := lnTestAccountNumberForNCF
	if cfg.Env == config.Env_PROD {
		lnAccountNumberForNCF = lnProdAccountNumberForNCF
	}
	s := &serverImpl{
		veriskClient:               veriskClient,
		db:                         db,
		fileUploadManager:          fileUploadManager,
		metricsClient:              metricsClient,
		riskXmlOrderHandler:        NewRiskXMLOrderHandler(lexisNexisClient),
		veriskHourlyAvailableQuota: atomic.NewInt64(HourlyVeriskRequestQuota),
		lexisNexisAttractScoreHourlyAvailableQuota: atomic.NewInt64(HourlyLexisRequestQuota),
		lexisNexisNCFHourlyAvailableQuota:          atomic.NewInt64(HourlyLexisRequestQuota),
		lnAccountNumberForNCF:                      lnAccountNumberForNCF,
		meteringClient:                             meteringClient,
		cryptoClient:                               cryptoClient,
	}
	go s.resetQuotaHourly()
	return s
}

var _ mvr.MVRServer = (*serverImpl)(nil)

func (cl *serverImpl) resetQuotaHourly() {
	for {
		time.Sleep(time.Hour)
		cl.veriskHourlyAvailableQuota.Store(HourlyVeriskRequestQuota)
		cl.lexisNexisAttractScoreHourlyAvailableQuota.Store(HourlyLexisRequestQuota)
		cl.lexisNexisNCFHourlyAvailableQuota.Store(HourlyLexisRequestQuota)
	}
}

func (cl *serverImpl) FetchMVR(ctx context.Context, mvrReq *mvr.FetchMVRRequest) (*mvr.FetchMVRResponse, error) {
	ctx = log.ContextWithFields(
		ctx,
		log.String("FetchMVRUUID", uuid.NewString()),
		log.String("applicationId", mvrReq.ApplicationID),
	)
	stalenessDays := DefaultVeriskStalenessDays
	if mvrReq.Staleness > 0 {
		stalenessDays = mvrReq.Staleness
	}
	earliestValidRequestTime := time.Now().Add(-time.Hour * 24 * time.Duration(stalenessDays))
	query := mvr_cache_db.VeriskReports(
		qm.Where("us_state = ?", mvrReq.UsState),
		qm.Where("dl_number = ?", mvrReq.DlNumber),
		qm.Where("lower(first_name) = lower(?)", mvrReq.FirstName),
		qm.Where("lower(last_name) = lower(?)", mvrReq.LastName),
		mvr_cache_db.VeriskReportWhere.Dob.EQ(null.TimeFrom(mvrReq.Dob.AsTime())),
		qm.Where("request_time > ?", earliestValidRequestTime),
		qm.OrderBy("request_time desc"),
	)
	entries, err := query.All(ctx, cl.db)
	if err != nil {
		log.Error(ctx, "Could not query entries from db", log.Err(err))
		return nil, errors.WithMessage(err, "db error")
	}
	for _, e := range entries {
		if e.ReportURI.Valid {
			log.Info(ctx, "Serving from cached entry", log.String("entry", e.Entry))
			return &mvr.FetchMVRResponse{RequestID: e.Entry}, nil
		}
	}
	for _, e := range entries {
		if time.Now().Sub(e.RequestTime) < time.Hour {
			log.Warn(
				ctx,
				"Serving from cached entry which doesn't contain report yet",
				log.String("entry", e.Entry),
				log.Time("entryRequestTime", e.RequestTime),
			)
			return &mvr.FetchMVRResponse{RequestID: e.Entry}, nil
		}
	}
	// If the fetchFromCache field is true, and we don't have a valid entry in cache
	// then we return an error. This is to prevent us from hitting verisk.
	if mvrReq.GetOnlyFetchFromCache() {
		log.Warn(
			ctx,
			"No valid entry found in mvr cache",
			log.String("applicationId", mvrReq.ApplicationID),
		)
		return nil, errors.Newf("no valid entry found in cache")
	}

	for {
		quota := cl.veriskHourlyAvailableQuota.Load()
		if quota <= 0 {
			return nil, errors.Newf("hourly quota for mvr exceeded, please try again in some time")
		}
		if cl.veriskHourlyAvailableQuota.CompareAndSwap(quota, quota-1) {
			break
		}
	}
	log.Info(ctx, "Forwarding request to verisk")

	resp, err := cl.veriskClient.FetchMVR(ctx, mvrReq)
	if err != nil {
		if err := cl.metricsClient.Inc(
			veriskBillableRequestMetric, 1, 1.0, statsd.Tag{statusMetricTag, failedMetricTag},
		); err != nil {
			log.Error(ctx, "Could not send metric", log.Err(err))
		}
		// It's a temporary solution so we don't handle the corner
		// case of available quota exceeding the limit when request fails
		// at reset boundary
		cl.veriskHourlyAvailableQuota.Inc()
		log.Error(ctx, "Request to verisk failed", log.Err(err))
		return nil, err
	} else {
		if err := cl.metricsClient.Inc(
			veriskBillableRequestMetric, 1, 1.0, statsd.Tag{statusMetricTag, succeededMetricTag},
		); err != nil {
			log.Error(ctx, "Could not send metric", log.Err(err))
		}
	}
	v := mvr_cache_db.VeriskReport{
		Entry:           uuid.NewString(),
		UsState:         mvrReq.UsState,
		DLNumber:        mvrReq.DlNumber,
		RequestTime:     time.Now(),
		Dob:             null.TimeFrom(mvrReq.Dob.AsTime()),
		FirstName:       null.StringFrom(mvrReq.FirstName),
		MiddleName:      null.StringFrom(mvrReq.MiddleName),
		LastName:        null.StringFrom(mvrReq.LastName),
		VeriskRequestID: resp.RequestID,
	}
	if err := v.Insert(ctx, cl.db, boil.Infer()); err != nil {
		log.Error(ctx, "Could not insert entry into db", log.Err(err))
		return nil, errors.WithMessage(err, "db error")
	}
	return &mvr.FetchMVRResponse{RequestID: v.Entry}, nil
}

func (cl *serverImpl) PollRawVeriskResponse(ctx context.Context, req *mvr.PollMVRRequest) (*mvr.RawVeriskResponse, error) {
	ctx = log.ContextWithFields(
		ctx, log.String("PollRawVeriskResponseUUID", uuid.NewString()), log.String("entry", req.RequestID),
	)
	entry, err := mvr_cache_db.FindVeriskReport(
		ctx, cl.db, req.RequestID,
	)
	if errors.Is(err, sql.ErrNoRows) {
		log.Error(ctx, "Entry does not exist in cache")
		return nil, errors.Newf("requestId does not exist")
	}
	if err != nil {
		log.Error(ctx, "Could not get entry from db", log.Err(err))
		return nil, errors.WithMessage(err, "db error")
	}
	if !entry.ReportURI.IsZero() {
		log.Info(ctx, "Serving from cached entry")
		log.Info(ctx, "Downloading raw verisk report from s3")
		reportURI, err := uuid.Parse(entry.ReportURI.String)
		if err != nil {
			log.Error(ctx, "Unable to parse report uri", log.Err(err))
			return nil, errors.WithMessage(err, "unable to parse report uri")
		}
		r, err := cl.fileUploadManager.DownloadFile(ctx, reportURI)
		if err != nil {
			log.Error(ctx, "Could not download file from s3", log.Err(err))
			return nil, errors.WithMessage(err, "s3 download error")
		}
		buf := new(strings.Builder)
		if _, err = io.Copy(buf, r); err != nil {
			return nil, err
		}
		return &mvr.RawVeriskResponse{ContentsXML: buf.String()}, nil
	}
	log.Info(ctx, "Forwarding request to verisk")
	resp, err := cl.veriskClient.PollRawVeriskResponse(ctx, &mvr.PollMVRRequest{RequestID: entry.VeriskRequestID})
	if err != nil {
		log.Error(ctx, "Request to verisk failed", log.Err(err))
		return nil, err
	}
	if _, err := verisk.ParseVeriskResponse(resp.ContentsXML); err != nil {
		if !errors.Is(err, mvr.ErrNotYetAvailable) {
			log.Warn(ctx, "Could not parse verisk response into report", log.Err(err))
			cl.uploadUnparseableReportToS3(ctx, fmt.Sprintf("%s-%s-%s-%s",
				unparseableReportS3Prefix,
				entry.DLNumber,
				entry.FirstName.String,
				entry.LastName.String,
			), resp)
		}
		// If response cannot be parsed into report, just return the response
		return resp, nil
	}
	log.Info(ctx, "Uploading raw verisk report to s3")
	// if response is a valid report then upload it to s3 and update cache
	reportURI := uuid.New()
	if err := cl.fileUploadManager.UploadFile(ctx, strings.NewReader(resp.ContentsXML), reportURI,
		enums.FileTypeVeriskMVR, entry.Entry, reportURI, enums.FileDestinationGroupMVRCache); err != nil {
		return nil, errors.WithMessage(err, "s3 upload error")
	}
	entry.ReportURI = null.StringFrom(reportURI.String())
	if updated, err := entry.Update(
		ctx,
		cl.db,
		boil.Whitelist(mvr_cache_db.VeriskReportColumns.ReportURI),
	); err != nil || updated != 1 {
		log.Error(ctx, "Could not update entry in db", log.Err(err), log.Int64("updatedRows", updated))
		return nil, errors.Newf("Could not update report URI in db. updated = %d, err := %s", updated, err)
	}
	return resp, nil
}

func (cl *serverImpl) uploadUnparseableReportToS3(
	ctx context.Context, fileName string, resp *mvr.RawVeriskResponse,
) {
	handle := uuid.New()
	err := cl.fileUploadManager.UploadFile(
		ctx, strings.NewReader(resp.ContentsXML), handle,
		enums.FileTypeVeriskUnparseableReport, fileName,
		uuid.Nil, enums.FileDestinationGroupMVRCache, // nil uuid as account id
	)
	if err != nil {
		log.Error(ctx, "Could not upload unparseable report to S3", log.Err(err))
	}
}

func (cl *serverImpl) PollMVR(ctx context.Context, req *mvr.PollMVRRequest) (*mvr.Report, error) {
	ctx = log.ContextWithFields(ctx, log.String("PollMVRUUID", uuid.NewString()))
	resp, err := cl.PollRawVeriskResponse(ctx, req)
	if err != nil {
		return nil, err
	}
	report, err := verisk.ParseVeriskResponse(resp.ContentsXML)
	if err != nil {
		return nil, err
	}
	entry, err := mvr_cache_db.FindVeriskReport(
		ctx, cl.db, req.RequestID,
	)
	if errors.Is(err, sql.ErrNoRows) {
		log.Error(ctx, "Entry does not exist in cache but found earlier")
		return nil, errors.Newf("requestId does not exist")
	}
	if err != nil {
		log.Error(ctx, "Could not get entry from db", log.Err(err))
		return nil, errors.WithMessage(err, "db error")
	}
	// sometimes dlNumber in report might be different from one
	// sent in request. One example we was had all the - stripped
	// from dl in report. We saw another case where verisk did not
	// contain dlNumber in report, but it was present in miscDetails
	report.RequestDlNumber = entry.DLNumber
	if report.DlNumber != report.RequestDlNumber {
		log.Warn(ctx, "request and report dlNumbers don't match",
			log.String("entryUuid", entry.Entry),
			log.String("requestDlNumber", report.RequestDlNumber),
			log.String("reportDlNumber", report.DlNumber),
		)
	}
	report.RequestState = entry.UsState
	if report.RequestState != report.DlState {
		log.Warn(ctx, "request and report dlState don't match",
			log.String("entryUuid", entry.Entry),
			log.String("requestState", report.RequestState),
			log.String("reportState", report.DlState),
		)
	}
	return report, nil
}

func (cl *serverImpl) GetAttractScoreRawXml(
	ctx context.Context, in *mvr.GetAttractScoreRequest,
) (*mvr.GetAttractScoreRawXmlResponse, error) {
	stalenessDays := DefaultLexisNexisStalenessDays
	if in.Staleness > 0 {
		stalenessDays = in.Staleness
	}
	earliestValidRequestTime := time.Now().Add(-time.Hour * 24 * time.Duration(stalenessDays))
	query := mvr_cache_db.LexisnexisAttracts(
		mvr_cache_db.LexisnexisAttractWhere.UsState.EQ(in.UsState),
		mvr_cache_db.LexisnexisAttractWhere.DLNumber.EQ(in.DlNumber),
		qm.Where("lower(first_name) = lower(?)", in.FirstName),
		qm.Where("lower(last_name) = lower(?)", in.LastName),
		mvr_cache_db.LexisnexisAttractWhere.Dob.EQ(null.TimeFrom(in.Dob.AsTime())),
		mvr_cache_db.LexisnexisAttractWhere.RequestTime.GT(earliestValidRequestTime),
		qm.OrderBy("request_time desc"),
		qm.Limit(1),
	)
	entries, err := query.All(ctx, cl.db)
	if err != nil {
		log.Error(ctx, "Could not query entries from db", log.Err(err))
		return nil, errors.WithMessage(err, "db error")
	}
	var s3reportUri uuid.UUID
	if len(entries) == 1 {
		s3reportUri, err = uuid.Parse(entries[0].ReportURI)
		if err != nil {
			return nil, errors.WithMessage(err, "could not parse report uri")
		}
	} else {
		for {
			quota := cl.lexisNexisAttractScoreHourlyAvailableQuota.Load()
			if quota <= 0 {
				return nil, errors.Newf("hourly quota for lexis exceeded, please try again in some time")
			}
			if cl.lexisNexisAttractScoreHourlyAvailableQuota.CompareAndSwap(quota, quota-1) {
				break
			}
		}
		s3reportUri, err = cl.getAttractScoreAndAddToCache(ctx, in)
		if err != nil {
			// It's a temporary solution so we don't handle the corner
			// case of available quota exceeding the limit when request fails
			// at reset boundary
			cl.lexisNexisAttractScoreHourlyAvailableQuota.Inc()
			if err := cl.metricsClient.Inc(
				lexisNexisBillableRequestMetric, 1, 1.0, statsd.Tag{statusMetricTag, succeededMetricTag},
			); err != nil {
				log.Error(ctx, "Could not increment lexisNexisBillableRequestMetric", log.Err(err))
			}
			return nil, errors.WithMessage(err, "could not fetch attract score")
		} else {
			if err := cl.metricsClient.Inc(
				lexisNexisBillableRequestMetric, 1, 1.0, statsd.Tag{statusMetricTag, failedMetricTag},
			); err != nil {
				log.Error(ctx, "Could not increment lexisNexisBillableRequestMetric", log.Err(err))
			}
		}
	}
	log.Info(ctx, "Downloading raw attract score from s3")
	r, err := cl.fileUploadManager.DownloadFile(ctx, s3reportUri)
	if err != nil {
		log.Error(ctx, "Could not download file from s3", log.Err(err))
		return nil, errors.WithMessage(err, "s3 download error")
	}
	buf := new(strings.Builder)
	if _, err = io.Copy(buf, r); err != nil {
		return nil, err
	}
	return &mvr.GetAttractScoreRawXmlResponse{ContentsXML: buf.String()}, nil
}

func (cl *serverImpl) GetAttractScore(
	ctx context.Context, in *mvr.GetAttractScoreRequest,
) (*mvr.GetAttractScoreResponse, error) {
	raw, err := cl.GetAttractScoreRawXml(ctx, in)
	if err != nil {
		return nil, err
	}
	resp := new(HandleRequestResponse)
	if err := xml.Unmarshal([]byte(raw.ContentsXML), resp); err != nil {
		return nil, errors.WithMessage(err, "could not parse raw xml attract report")
	}
	score, err := ParseAttractScoreFromLexisResponse(resp)
	if err != nil {
		return nil, errors.WithMessage(err, "could not parse score from report")
	}
	return &mvr.GetAttractScoreResponse{
		AttractScore:       score,
		SerializedMiscInfo: raw.ContentsXML,
	}, nil
}

// getAttractScoreAndAddToCache fetches attract score report from
// lexisnexis, adds it to cache and returns the URI of file uploaded
// to S3.
func (cl *serverImpl) getAttractScoreAndAddToCache(
	ctx context.Context,
	in *mvr.GetAttractScoreRequest,
) (uuid.UUID, error) {
	reportURI := uuid.New()
	v := mvr_cache_db.LexisnexisAttract{
		Entry:            uuid.NewString(),
		UsState:          in.UsState,
		DLNumber:         in.DlNumber,
		RequestTime:      time.Now(),
		Dob:              null.TimeFrom(in.Dob.AsTime()),
		FirstName:        null.StringFrom(in.FirstName),
		MiddleName:       null.StringFrom(in.MiddleName),
		LastName:         null.StringFrom(in.LastName),
		RequestQuoteback: null.StringFrom(fmt.Sprintf("%s_%s-%s", in.ApplicationID, in.DlNumber, in.UsState)),
		ReportURI:        reportURI.String(),
	}
	resp, err := cl.getAttractScoreFromLexisNexis(ctx, in, v.RequestQuoteback.String)
	if err != nil {
		log.Info(ctx, "Could not fetch attract score",
			log.String("applicationId", in.ApplicationID),
			log.Err(err),
		)
	}
	bts, err := xml.MarshalIndent(resp, "", "  ")
	if err != nil {
		return uuid.Nil, err
	}

	if err := cl.fileUploadManager.UploadFile(ctx, bytes.NewBuffer(bts), reportURI,
		enums.FileTypeLexisNexisAttractScore,
		v.Entry, reportURI,
		enums.FileDestinationGroupMVRLexisNexisAttractCache,
	); err != nil {
		return uuid.Nil, errors.WithMessage(err, "s3 upload error")
	}
	if err := v.Insert(ctx, cl.db, boil.Infer()); err != nil {
		log.Error(ctx, "Could not insert entry into db", log.Err(err))
		return uuid.Nil, errors.WithMessage(err, "db error")
	}
	return reportURI, nil
}

func (cl *serverImpl) getAttractScoreFromLexisNexis(
	ctx context.Context, in *mvr.GetAttractScoreRequest, quoteback string,
) (*HandleRequestResponse, error) {
	subjectId := ID("S1")
	stateEnum := StateEnum(in.UsState)
	cdaOptionId := lnAttractScoreOptionID
	qb := []*QBNameValuePair{
		{Name: "S1QB", Value: quoteback},
	}
	req := &Request{
		RequesterInformation: &RequesterInformation{
			AccountNumber: lnProdAccountNumberForAttractScore,
		},
		TransactionDetails: &TransactionDetails{
			RuleplanId: 5200,
			// TODO: Fill in quoteback
		},
		SearchBy: &SearchBy{
			Subjects: &Subjects{
				Subject: []*Subject{
					{
						SubjectID: &subjectId,
						QuoteBack: qb[0].Name,
						Name: &Name{
							First: in.FirstName,
							Last:  in.LastName,
						},
						DOB: &PrecisionDateTime{
							Day:   int16(in.Dob.AsTime().Day()),
							Month: int16(in.Dob.AsTime().Month()),
							Year:  int16(in.Dob.AsTime().Year()),
						},
						DriversLicense: &DriversLicense{
							DriversLicenseNumber: in.DlNumber,
							DriversLicenseState:  in.UsState,
						},
					},
				},
			},
		},
		ProductOptions: &ProductOptions{},

		Products: &ProductChoiceType{
			CommercialDriverAttract: &CommercialDriverAttract{
				InquiryDriver: []*InquiryDriver{
					{
						SearchBy: &CommercialDriverAttractSearchBy{},
						OptionId: &IdentificationType{
							Ref:   string(cdaOptionId),
							Value: string(cdaOptionId),
						},
					},
				},
			},
		},
	}
	req.TransactionDetails.QuoteBacks.QuoteBack = qb
	req.ProductOptions.CommercialDriverAttract.Options = []*CommercialDriverAttractOptions{
		{
			Model:             lnAttractScoreModel,
			PolicyRatingState: &stateEnum,
			CDAOptionId:       &cdaOptionId,
		},
	}
	req.Products.CommercialDriverAttract.InquiryDriver[0].SearchBy.Subjects.Subject = &IdentificationType{
		Ref:   string(subjectId),
		Value: string(subjectId),
	}

	log.Info(ctx, "Requesting Attract score from LexisNexis",
		log.Any("request", req))
	return cl.riskXmlOrderHandler.HandleRequest(&HandleRequest{
		Request: req,
	})
}

// GeNationalCreditFileRawXml fetches raw ncf report from lexisnexis
// This method is mainly for debugging purposes and should not be used
func (cl *serverImpl) GeNationalCreditFileRawXml(
	ctx context.Context,
	in *mvr.GetNationalCreditFileRequest,
) (*mvr.GetNationalCreditFileRawXmlResponse, error) {
	stalenessDays := DefaultLexisNexisStalenessDays
	if in.Staleness > 0 {
		stalenessDays = in.Staleness
	}
	earliestValidRequestTime := time.Now().Add(-time.Hour * 24 * time.Duration(stalenessDays))

	query := []qm.QueryMod{
		mvr_cache_db.NCFReportWhere.UsState.EQ(in.Address.State),
		mvr_cache_db.NCFReportWhere.Dob.EQ(null.TimeFrom(in.Dob.AsTime())),
		mvr_cache_db.NCFReportWhere.Address.IsNotNull(),
		mvr_cache_db.NCFReportWhere.RequestTime.GT(earliestValidRequestTime),
		qm.Where("lower(first_name) = lower(?)", in.FirstName),
		qm.Where("lower(last_name) = lower(?)", in.LastName),
		qm.OrderBy("request_time desc"),
		qm.Limit(1),
	}

	if in.Address != nil {
		query = append(query,
			qm.Where("lower(address->>'street') = lower(?)", in.Address.Street),
			qm.Where("lower(address->>'city') = lower(?)", in.Address.City),
			qm.Where("lower(address->>'state') = lower(?)", in.Address.State),
			qm.Where("lower(address->>'zip') = lower(?)", in.Address.Zip),
		)
	}

	if len(in.EncryptedSSN) != 0 {
		query = append(query, mvr_cache_db.NCFReportWhere.SSN.IsNotNull())
	} else {
		query = append(query, mvr_cache_db.NCFReportWhere.SSN.IsNull())
	}

	cl.ncfMutex.Lock()
	defer cl.ncfMutex.Unlock()
	entries, err := mvr_cache_db.NCFReports(query...).All(ctx, cl.db)
	if err != nil {
		log.Error(ctx, "Could not query entries from db", log.Err(err))
		return nil, errors.WithMessage(err, "db error")
	}

	var reportURI string
	var useCachedReport bool

	// We're currently not handling scenarios where multiple entries exist with different SSNs,
	// which can happen if an agent initially enters an incorrect SSN.
	// In such cases, when the agent later provides an updated SSN, we update the SSN in the app object
	// and remove any existing row(s) from the NCF report
	// that had the incorrect SSN and re-pull the report using the new one.
	if len(entries) == 1 {
		entry := entries[0]
		hasInputSSN := len(in.EncryptedSSN) > 0
		hasCachedSSN := entry.SSN.Valid && len(entry.SSN.Bytes) > 0

		if !hasInputSSN && !hasCachedSSN {
			useCachedReport = true
		} else if hasInputSSN && hasCachedSSN {
			decryptedInputSSN, err := cl.decryptSSN(ctx, in.EncryptedSSN)
			if err != nil {
				return nil, errors.WithMessage(err, "could not decrypt input SSN")
			}

			decryptedDbSSN, err := cl.decryptSSN(ctx, entry.SSN.Bytes)
			if err != nil {
				return nil, errors.WithMessage(err, "could not decrypt db SSN")
			}

			if decryptedInputSSN == decryptedDbSSN {
				useCachedReport = true
			}
		}

		if useCachedReport {
			reportURI = entry.ReportURI
		}
	}

	if !useCachedReport {
		for {
			quota := cl.lexisNexisNCFHourlyAvailableQuota.Load()
			if quota <= 0 {
				return nil, errors.New("hourly quota for ln ncf exceeded, please try again in some time")
			}

			log.Info(ctx, "Quota check",
				log.Any("currentQuota", quota),
				log.Any("cl.lexisNexisNCFHourlyAvailableQuota", cl.lexisNexisNCFHourlyAvailableQuota),
			)

			if swapped := cl.lexisNexisNCFHourlyAvailableQuota.CompareAndSwap(quota, quota-1); swapped {
				log.Info(ctx, "after swapping",
					log.Any("quota", quota),
					log.Bool("swapped", swapped),
				)
				break
			} else {
				// Log a message if CompareAndSwap fails, indicating a possible concurrent update
				log.Warn(ctx, "Failed to update quota, retrying",
					log.Int64("currentQuota", quota))
			}

			log.Warn(ctx, "Failed to update quota, retrying", log.Int64("currentQuota", quota))
		}

		s3ReportURI, err := cl.getNCFAndAddToCache(ctx, in)
		if err != nil {
			// It's a temporary solution so we don't handle the corner
			// case of available quota exceeding the limit when request fails
			// at reset boundary
			cl.lexisNexisNCFHourlyAvailableQuota.Inc()
			if err := cl.metricsClient.Inc(
				lnNCFBillableRequestMetric, 1, 1.0, statsd.Tag{statusMetricTag, failedMetricTag},
			); err != nil {
				log.Error(ctx, "Could not increment lnNCFBillableRequestMetric", log.Err(err))
			}
			return nil, errors.WithMessage(err, "could not fetch ncf report and add to cache")
		}

		if err := cl.metricsClient.Inc(
			lnNCFBillableRequestMetric, 1, 1.0, statsd.Tag{statusMetricTag, succeededMetricTag},
		); err != nil {
			log.Error(ctx, "Could not increment lnNCFBillableRequestMetric", log.Err(err))
		}

		// If the request to fetch and cache the NCF report succeeds,
		// we still increment the quota to ensures that the quota is properly adjusted
		cl.lexisNexisNCFHourlyAvailableQuota.Inc()
		reportURI = s3ReportURI.String()
	}

	reportId, err := uuid.Parse(reportURI)
	if err != nil {
		return nil, errors.WithMessage(err, "could not parse report uri")
	}

	log.Info(ctx, "Downloading raw ncf report from s3")
	r, err := cl.fileUploadManager.DownloadFile(ctx, reportId)
	if err != nil {
		log.Error(ctx, "Could not download file from s3", log.Err(err))
		return nil, errors.WithMessage(err, "s3 download error")
	}

	buf := new(strings.Builder)
	if _, err = io.Copy(buf, r); err != nil {
		return nil, err
	}

	return &mvr.GetNationalCreditFileRawXmlResponse{ContentsXML: buf.String()}, nil
}

func (cl *serverImpl) GetNationalCreditFile(
	ctx context.Context,
	request *mvr.GetNationalCreditFileRequest,
) (*mvr.GetNationalCreditFileResponse, error) {
	raw, err := cl.GeNationalCreditFileRawXml(ctx, request)
	if err != nil {
		return &mvr.GetNationalCreditFileResponse{
			NcfReport:          &mvr.NcfReport{},
			TransactionDetails: &mvr.TransactionDetails{},
		}, err
	}

	resp := new(HandleRequestResponse)
	if err := xml.Unmarshal([]byte(raw.ContentsXML), resp); err != nil {
		return nil, errors.WithMessage(err, "could not parse raw xml ncf report")
	}
	report, transactionDetails, err := cl.getNationalCreditFileResponse(resp)
	if err != nil {
		return &mvr.GetNationalCreditFileResponse{
			NcfReport:          &mvr.NcfReport{},
			TransactionDetails: transactionDetails,
		}, errors.WithMessage(err, "could not parse ncf report from response")
	}
	log.Info(
		ctx,
		"Successfully parsed ncf report from response",
		log.String("applicationId", request.ApplicationID),
		log.Any("transactionDetails", transactionDetails),
	)
	return &mvr.GetNationalCreditFileResponse{
		NcfReport:          report,
		SerializedMiscInfo: raw.GetContentsXML(),
		TransactionDetails: transactionDetails,
	}, nil
}

func (cl *serverImpl) getNCFAndAddToCache(
	ctx context.Context,
	in *mvr.GetNationalCreditFileRequest,
) (uuid.UUID, error) {
	reportURI := uuid.New()
	var address null.JSON
	if in.Address != nil {
		a, err := json.Marshal(in.Address)
		if err != nil {
			return uuid.Nil, errors.WithMessage(err, "could not marshal address")
		}
		address = null.JSONFrom(a)
	}
	quoteback := fmt.Sprintf("%s_%s-%s", in.ApplicationID, in.FirstName, in.LastName)
	if in.SsnLastFour != nil {
		quoteback = fmt.Sprintf("%s-%s", quoteback, *in.SsnLastFour)
	}
	v := mvr_cache_db.NCFReport{
		Entry:            uuid.NewString(),
		UsState:          in.Address.State,
		RequestTime:      time.Now(),
		Dob:              null.TimeFrom(in.Dob.AsTime()),
		FirstName:        null.StringFrom(in.FirstName),
		MiddleName:       null.StringFrom(in.MiddleName),
		LastName:         null.StringFrom(in.LastName),
		RequestQuoteback: null.StringFrom(quoteback),
		ReportURI:        reportURI.String(),
		Address:          address,
		SSN:              null.BytesFrom(in.EncryptedSSN),
	}

	resp, err := cl.getNCFFromLexisNexis(ctx, in, quoteback)
	if err != nil {
		return uuid.Nil, errors.Wrap(err, "could not fetch ncf report")
	}

	bts, err := xml.MarshalIndent(resp, "", "  ")
	if err != nil {
		return uuid.Nil, err
	}

	if err := cl.fileUploadManager.UploadFile(ctx, bytes.NewBuffer(bts), reportURI,
		enums.FileTypeNationalCreditFileReport,
		uuid.NewString(), reportURI,
		enums.FileDestinationGroupLexisNexisNationalCreditFileCache,
	); err != nil {
		return uuid.Nil, errors.WithMessage(err, "s3 upload error")
	}
	if err := v.Insert(ctx, cl.db, boil.Infer()); err != nil {
		log.Error(ctx, "Could not insert entry into db", log.Err(err))
		return uuid.Nil, errors.WithMessage(err, "db error")
	}

	return reportURI, nil
}

func (cl *serverImpl) getNCFFromLexisNexis(
	ctx context.Context, in *mvr.GetNationalCreditFileRequest, quoteback string,
) (*HandleRequestResponse, error) {
	var decrpytedSsn string
	if len(in.EncryptedSSN) > 0 {
		decryptedSsn, err := cl.decryptSSN(ctx, in.EncryptedSSN)
		if err != nil {
			return nil, errors.WithMessage(err, "could not decrypt ssn")
		}
		decrpytedSsn = decryptedSsn
	}

	// In LexisNexis documentation, they reference subject ID as S1
	subjectId := ID("S1")
	addressId := ID("A1")

	// The quoteback value that we provide in the request is returned in the response
	// This acts as a common identifier between the request and response
	qb := []*QBNameValuePair{
		// S1QB just signifies that it is a quoteback for S1 subject
		{Name: "S1QB", Value: quoteback},
	}

	req := &Request{
		RequesterInformation: &RequesterInformation{
			Name:          lnRequesterName,
			AccountNumber: cl.lnAccountNumberForNCF,
		},
		TransactionDetails: &TransactionDetails{
			RuleplanId: 5200,
		},
		SearchBy: &SearchBy{
			Subjects: &Subjects{
				Subject: []*Subject{
					{
						SubjectID: &subjectId,
						QuoteBack: qb[0].Name,
						Name: &Name{
							First: in.FirstName,
							Last:  in.LastName,
						},
						DOB: &PrecisionDateTime{
							Day:   int16(in.Dob.AsTime().Day()),
							Month: int16(in.Dob.AsTime().Month()),
							Year:  int16(in.Dob.AsTime().Year()),
						},
						CurrentAddress: &AddressType{
							AddressId:      &addressId,
							StreetAddress1: in.Address.Street,
							City:           in.Address.City,
							State:          in.Address.State,
							Zip5:           in.Address.Zip,
						},
						// Note: Do not log the decrypted SSN. If logging the request is necessary,
						// use redactedNationalCreditFileRequest to ensure only the last four digits
						// (if available) are included for safe logging.
						SSN: decrpytedSsn,
					},
				},
			},
		},
		ProductOptions: &ProductOptions{},
		Products: &ProductChoiceType{
			NationalCreditFile: &NationalCreditFile{
				InquiryNationalCreditFile: []*InquiryNationalCreditFile{
					{
						SearchBy: &NcfSearchBy{},
						OptionId: &IdentificationType{
							Ref:   string(lnNCFOptionID),
							Value: string(lnNCFOptionID),
						},
					},
				},
			},
		},
	}

	// Set the quoteback value in the request
	req.TransactionDetails.QuoteBacks.QuoteBack = qb

	// Set the options for the National Credit File product
	req.ProductOptions.NationalCreditFileReport.Options = []*NationalCreditFileOptions{
		{
			NcfOptionId: pointer_utils.ToPointer(lnNCFOptionID),
		},
	}

	// Set the primary subject for the request
	req.Products.NationalCreditFile.InquiryNationalCreditFile[0].SearchBy.Subjects.PrimarySubject = &IdentificationType{
		Ref:   string(subjectId),
		Value: string(subjectId),
	}

	log.Info(ctx, "Requesting NCF from LexisNexis",
		log.Any("request", cl.redactedNationalCreditFileRequest(in, req)))
	return cl.riskXmlOrderHandler.HandleRequest(&HandleRequest{
		Request: req,
	})
}

// redactedNationalCreditFileRequest returns a safe-to-log copy of the original LexisNexis Request,
// with sensitive fields such as the SSN redacted. Specifically, it replaces the SSN with the
// last four digits (if available) to avoid exposing SSN in logs. The function performs
// a shallow copy of the request and deep copies the nested subject data to ensure that the
// original request remains unmodified.
func (cl *serverImpl) redactedNationalCreditFileRequest(in *mvr.GetNationalCreditFileRequest, req *Request) *Request {
	if req == nil || req.SearchBy == nil || req.SearchBy.Subjects == nil {
		return req
	}

	redactedReq := *req

	originalSubjects := req.SearchBy.Subjects.Subject
	redactedSubjects := make([]*Subject, len(originalSubjects))
	for i, subj := range originalSubjects {
		if subj == nil {
			continue
		}
		s := *subj
		if in.SsnLastFour != nil {
			s.SSN = *in.SsnLastFour
		}
		redactedSubjects[i] = &s
	}

	sb := *req.SearchBy
	sb.Subjects = &Subjects{
		Subject: redactedSubjects,
	}
	redactedReq.SearchBy = &sb

	return &redactedReq
}

func (cl *serverImpl) getNationalCreditFileResponse(
	resp *HandleRequestResponse,
) (*mvr.NcfReport, *mvr.TransactionDetails, error) {
	ncfReport, td, err := ParseNCFFromLexisResponse(resp)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "could not parse ncf report from LN response")
	}

	transactionDetails := &mvr.TransactionDetails{}
	if td != nil {
		transactionDetails.ProcessingStatus = td.ProcessingStatus
	}
	log.Plain.Info("transaction details",
		log.Any("transactionDetails", transactionDetails))

	if transactionDetails != nil && transactionDetails.ProcessingStatus == NCFNoHitStatusCode {
		log.Plain.Info("returning as No hit found")
		return nil, transactionDetails, nil
	}

	if ncfReport == nil || ncfReport.Result == nil {
		return nil, transactionDetails, errors.New("found nil ncf report in response")
	}

	subjectInfo, err := getSubjectInfo(ncfReport)
	if err != nil {
		return nil, transactionDetails, errors.WithMessage(err, "could not get subject info from ncf report")
	}

	creditReportSummary, err := getCreditReportSummary(ncfReport)
	if err != nil {
		return nil, transactionDetails, errors.WithMessage(err, "could not get credit report summary from ncf report")
	}

	employmentInfo, err := getEmploymentInfo(ncfReport)
	if err != nil {
		return nil, transactionDetails, errors.WithMessage(err, "could not get employment info from ncf report")
	}

	collectionRecord, err := getCollectionRecord(ncfReport)
	if err != nil {
		return nil, transactionDetails, errors.WithMessage(err, "could not get collection record from ncf report")
	}

	creditTradeHistoryRecord, err := getCreditTradeHistoryRecord(ncfReport)
	if err != nil {
		return nil, transactionDetails, errors.WithMessage(err, "could not get credit trade history record from ncf report")
	}

	inquiryHistoryHeaderRecord, err := getInquiryHistoryHeaderRecord(ncfReport)
	if err != nil {
		return nil, transactionDetails, errors.WithMessage(err, "could not get inquiry history header record from ncf report")
	}

	return &mvr.NcfReport{
		NcfProductReport: &mvr.NcfProductReport{
			SubjectInfo:         subjectInfo,
			CreditReportSummary: creditReportSummary,
			EmploymentInfos: &mvr.EmploymentInfos{
				EmploymentInfo: employmentInfo,
			},
			CollectionRecordsInfo: &mvr.CollectionRecordsInfo{
				CollectionRecords: &mvr.CollectionRecords{
					CollectionRecord: collectionRecord,
				},
			},
			TradeAccountInfo: &mvr.TradeAccountInfo{
				CreditTradeHistoryRecords: &mvr.CreditTradeHistoryRecords{
					CreditTradeHistoryRecord: creditTradeHistoryRecord,
				},
			},
			InquiryHistoryHeader: &mvr.InquiryHistoryHeader{
				InquiryHistoryHeaderRecords: &mvr.InquiryHistoryHeaderRecords{
					InquiryHistoryHeaderRecord: inquiryHistoryHeaderRecord,
				},
			},
		},
	}, transactionDetails, nil
}

func (cl *serverImpl) decryptSSN(ctx context.Context, encryptedSsn []byte) (string, error) {
	decryptedBytes, err := crypto_utils.Decrypt(ctx, cl.cryptoClient, encryptedSsn, crypto_utils.KeyIdUserSSN)
	if err != nil {
		return "", errors.WithMessage(err, "could not decrypt ssn")
	}
	return string(decryptedBytes), nil
}

func getInquiryHistoryHeaderRecord(ncfReport *NationalCreditFileReport) ([]*mvr.InquiryHistoryHeaderRecord, error) {
	var inquiryHistoryHeaderRecord []*mvr.InquiryHistoryHeaderRecord
	if ncfReport.Result.InquiryHistoryHeader == nil {
		log.Plain.Debug("inquiry history header not found in ncf report")
		return inquiryHistoryHeaderRecord, nil
	}

	for _, inquiryHistoryHeader := range ncfReport.Result.InquiryHistoryHeader.InquiryHistoryHeaderRecords.InquiryHistoryHeaderRecord {
		var dateOfInquiry *mvr.DateOfInquiry
		if inquiryHistoryHeader.DateOfInquiry != nil {
			dateOfInquiry = &mvr.DateOfInquiry{
				Year:  uint32(inquiryHistoryHeader.DateOfInquiry.Year),
				Month: uint32(inquiryHistoryHeader.DateOfInquiry.Month),
				Day:   uint32(inquiryHistoryHeader.DateOfInquiry.Day),
			}
		}
		inquiryHistoryHeaderRecord = append(inquiryHistoryHeaderRecord, &mvr.InquiryHistoryHeaderRecord{
			DateOfInquiry: dateOfInquiry,
			InquirerName:  inquiryHistoryHeader.InquirerName,
			InquirerId:    inquiryHistoryHeader.InquirerId,
		})
	}
	return inquiryHistoryHeaderRecord, nil
}

func getCreditReportSummary(ncfReport *NationalCreditFileReport) (*mvr.CreditReportSummary, error) {
	if ncfReport.Result.CreditReportSummary == nil {
		return nil, errors.New("credit report summary not found in ncf report")
	}

	creditReportSummary := ncfReport.Result.CreditReportSummary

	currentStatusAccount, err := getCurrentStatusAccount(creditReportSummary)
	if err != nil {
		return nil, errors.WithMessage(err, "could not get current status account from ncf report")
	}
	historyStatusAccount, err := getHistoryStatusAccount(creditReportSummary)
	if err != nil {
		return nil, errors.WithMessage(err, "could not get history status account from ncf report")
	}

	var dateCreditFileEstbed *mvr.DateCreditFileEstbed
	if creditReportSummary.DateCreditFileEstbed != nil {
		dateCreditFileEstbed = &mvr.DateCreditFileEstbed{
			Year:  uint32(creditReportSummary.DateCreditFileEstbed.Year),
			Month: uint32(creditReportSummary.DateCreditFileEstbed.Month),
			Day:   uint32(creditReportSummary.DateCreditFileEstbed.Day),
		}
	}

	return &mvr.CreditReportSummary{
		DateCreditFileEstbed:        dateCreditFileEstbed,
		OldestOpeningDateOfTrade:    convertToDateFirstAtAddress(creditReportSummary.OldestOpeningDateOfTrade),
		LatestReportingDateOfTrade:  convertToDateFirstAtAddress(creditReportSummary.LatestReportingDateOfTrade),
		DateOfLatestFileActivity:    convertToDateFirstAtAddress(creditReportSummary.DateOfLatestFileActivity),
		ReportIncldsCollectionItems: creditReportSummary.ReportIncldsCollectionItems,
		HighCreditRangeLowAmount:    uint32(creditReportSummary.HighCreditRangeLowAmount),
		HighCreditRangeHighAmount:   uint32(creditReportSummary.HighCreditRangeHighAmount),
		TotalNumberOfTradeLines:     uint32(creditReportSummary.TotalNumberOfTradeLines),
		CurrentStatusAccounts: &mvr.CurrentStatusAccounts{
			CurrentStatusAccount: currentStatusAccount,
		},
		HistoryStatusAccounts: &mvr.HistoryStatusAccounts{
			HistoryStatusAccount: historyStatusAccount,
		},
		HighCreditTotalRevolving:    uint32(creditReportSummary.HighCreditTotalRevolving),
		HighCreditInstallment:       uint32(creditReportSummary.HighCreditInstallment),
		AmountOwedTotalRevolving:    uint32(creditReportSummary.AmountOwedTotalRevolving),
		PastDueTotalRevolving:       uint32(creditReportSummary.PastDueTotalRevolving),
		NumberOfRevolvingAccounts:   uint32(creditReportSummary.NumberOfRevolvingAccounts),
		NumberOfInstallmentAccounts: uint32(creditReportSummary.NumberOfInstallmentAccounts),
		AmountOwedTotalInstallment:  uint32(creditReportSummary.AmountOwedTotalInstallment),
		AmountOwedTotalOpenended:    uint32(creditReportSummary.AmountOwedTotalOpenended),
		HighCreditOpenended:         uint32(creditReportSummary.HighCreditOpenended),
	}, nil
}

func getSubjectInfo(ncfReport *NationalCreditFileReport) (*mvr.SubjectInfo, error) {
	if ncfReport.Result.SubjectInfo == nil {
		return nil, errors.New("subject info not found in ncf report")
	}

	if ncfReport.Result.SubjectInfo.Subject == nil {
		return nil, errors.New("subject not found in ncf report")
	}

	subject := ncfReport.Result.SubjectInfo.Subject

	if ncfReport.Result.SubjectInfo.CurrentAddress == nil {
		return nil, errors.New("current address not found in ncf report")
	}

	subDob := &mvr.Dob{}
	if subject.DOB != nil {
		subDob = &mvr.Dob{
			Year: uint32(subject.DOB.Year),
		}
	}

	currentAddress := ncfReport.Result.SubjectInfo.CurrentAddress
	return &mvr.SubjectInfo{
		Subject: &mvr.Subject{
			Classification: string(type_utils.GetValueOrDefault(subject.Classification, "")),
			Name: &mvr.Name{
				First:  subject.Name.First,
				Middle: subject.Name.Middle,
				Last:   subject.Name.Last,
				Suffix: subject.Name.Suffix,
			},
			Ssn:                 subject.SSN,
			Dob:                 subDob,
			HeightFeet:          subject.HeightFeet,
			HeightInches:        subject.HeightInches,
			Weight:              subject.Weight,
			RelationshipType:    string(type_utils.GetValueOrDefault(subject.RelationshipType, "")),
			GroupSequenceNumber: subject.GroupSequenceNumber,
		},
		CurrentAddress: &mvr.CurrentAddress{
			StreetNumber:       currentAddress.StreetNumber,
			StreetName:         currentAddress.StreetName,
			City:               currentAddress.City,
			State:              currentAddress.State,
			Zip5:               currentAddress.Zip5,
			Zip4:               currentAddress.Zip4,
			DateFirstAtAddress: convertToDateFirstAtAddress(currentAddress.DateFirstAtAddress),
			AddressId:          string(type_utils.GetValueOrDefault(currentAddress.AddressId, "")),
		},
	}, nil
}

func getCreditTradeHistoryRecord(ncfReport *NationalCreditFileReport) ([]*mvr.CreditTradeHistoryRecord, error) {
	if ncfReport.Result.TradeAccountInfo == nil ||
		ncfReport.Result.TradeAccountInfo.CreditTradeHistoryRecords.CreditTradeHistoryRecord == nil {
		return nil, errors.New("credit trade history records not found in ncf report")
	}

	var creditTradeHistoryRecord []*mvr.CreditTradeHistoryRecord
	for _, tradeHistoryRecord := range ncfReport.Result.TradeAccountInfo.CreditTradeHistoryRecords.CreditTradeHistoryRecord {
		messages := &mvr.MessagesList{
			Message: []*mvr.Message{},
		}
		if tradeHistoryRecord.Messages.Message != nil {
			for _, message := range tradeHistoryRecord.Messages.Message {
				messages.Message = append(messages.Message, &mvr.Message{
					Code:    message.Code,
					Message: message.Message,
				})
			}
		}

		creditTradeHistoryRecord = append(creditTradeHistoryRecord, &mvr.CreditTradeHistoryRecord{
			ReportingMemberNumber: tradeHistoryRecord.ReportingMemberNumber,
			MemberName:            tradeHistoryRecord.MemberName,
			TapeSupplierIndicator: tradeHistoryRecord.TapeSupplierIndicator,
			DateReported:          convertToDateFirstAtAddress(tradeHistoryRecord.DateReported),
			DateAccountOpened:     convertToDateFirstAtAddress(tradeHistoryRecord.DateAccountOpened),
			HighestCreditAmount:   uint32(tradeHistoryRecord.HighestCreditAmount),
			AccountBalance:        uint32(tradeHistoryRecord.AccountBalance),
			PastDueAmount:         uint32(tradeHistoryRecord.PastDueAmount),
			AccountTypeCode:       tradeHistoryRecord.AccountTypeCode,
			CurrentRateCode:       tradeHistoryRecord.CurrentRateCode,
			MonthsReviewed:        tradeHistoryRecord.MonthsReviewed,
			AccountDesignatorCode: tradeHistoryRecord.AccountDesignatorCode,
			ThirtydayCounter:      tradeHistoryRecord.ThirtydayCounter,
			SixtydayCounter:       tradeHistoryRecord.SixtydayCounter,
			NinetydayCounter:      tradeHistoryRecord.NinetydayCounter,
			PrevRateCode1:         tradeHistoryRecord.PrevRateCode1,
			PrevRateDate1:         convertToDateFirstAtAddress(tradeHistoryRecord.PrevRateDate1),
			PrevRateCode2:         tradeHistoryRecord.PrevRateCode2,
			PrevRateDate2:         convertToDateFirstAtAddress(tradeHistoryRecord.PrevRateDate2),
			PrevRateCode3:         tradeHistoryRecord.PrevRateCode3,
			PrevRateDate3:         convertToDateFirstAtAddress(tradeHistoryRecord.PrevRateDate3),
			DateOfLastActivity:    convertToDateFirstAtAddress(tradeHistoryRecord.DateOfLastActivity),
			Messages:              messages,
		})
	}
	return creditTradeHistoryRecord, nil
}

func getCollectionRecord(ncfReport *NationalCreditFileReport) ([]*mvr.CollectionRecord, error) {
	var collectionRecord []*mvr.CollectionRecord
	if ncfReport.Result.CollectionRecordsInfo == nil ||
		ncfReport.Result.CollectionRecordsInfo.CollectionRecords.CollectionRecord == nil {
		log.Plain.Debug("collection records not found in ncf report")
		return collectionRecord, nil
	}

	for _, collection := range ncfReport.Result.CollectionRecordsInfo.CollectionRecords.CollectionRecord {
		collectionRecord = append(collectionRecord, &mvr.CollectionRecord{
			DateReported:                convertToDateFirstAtAddress(collection.DateReported),
			DateAssigned:                convertToDateFirstAtAddress(collection.DateAssigned),
			ReportingMemberAgencyNumber: collection.ReportingMemberAgencyNumber,
			ClientNameOrNumber:          collection.ClientNameOrNumber,
			EcoaCode:                    collection.EcoaCode,
			DateOfLastActivity:          convertToDateFirstAtAddress(collection.DateOfLastActivity),
			OriginalAmount:              uint32(collection.OriginalAmount),
			DateOfBalance:               convertToDateFirstAtAddress(collection.DateOfBalance),
			BalanceAmount:               uint32(collection.BalanceAmount),
			StatusDate:                  convertToDateFirstAtAddress(collection.StatusDate),
			CollectionItemStatus:        collection.CollectionItemStatus,
		})
	}
	return collectionRecord, nil
}

func convertToDateFirstAtAddress(date *PrecisionDateTime) *mvr.DateFirstAtAddress {
	if date == nil {
		return nil
	}
	return &mvr.DateFirstAtAddress{
		Year:  uint32(date.Year),
		Month: uint32(date.Month),
	}
}

func getEmploymentInfo(ncfReport *NationalCreditFileReport) ([]*mvr.EmploymentInfo, error) {
	var employmentInfo []*mvr.EmploymentInfo
	if ncfReport.Result.EmploymentInfos.EmploymentInfo == nil {
		log.Plain.Debug("employment info not found in ncf report")
		return employmentInfo, nil
	}

	for _, employment := range ncfReport.Result.EmploymentInfos.EmploymentInfo {
		employmentInfo = append(employmentInfo, &mvr.EmploymentInfo{
			PositionDesc:         employment.PositionDesc,
			EmployerName:         employment.EmployerName,
			EmploymentRecordType: employment.EmploymentRecordType,
			Classification:       employment.Classification,
		})
	}
	return employmentInfo, nil
}

func getHistoryStatusAccount(creditReportSummary *InsNCFCreditReportSummary) ([]*mvr.CurrentStatusAccount, error) {
	if creditReportSummary == nil || creditReportSummary.HistoryStatusAccounts.HistoryStatusAccount == nil {
		return nil, errors.New("history status accounts not found in ncf report")
	}
	var historyStatusAccount []*mvr.CurrentStatusAccount
	for _, account := range creditReportSummary.HistoryStatusAccounts.HistoryStatusAccount {
		historyStatusAccount = append(historyStatusAccount, &mvr.CurrentStatusAccount{
			Status:           account.Status,
			NumberOfAccounts: uint32(account.NumberOfAccounts),
		})
	}
	return historyStatusAccount, nil
}

func getCurrentStatusAccount(creditReportSummary *InsNCFCreditReportSummary) ([]*mvr.CurrentStatusAccount, error) {
	if creditReportSummary == nil || creditReportSummary.CurrentStatusAccounts.CurrentStatusAccount == nil {
		return nil, errors.New("current status accounts not found in ncf report")
	}
	var currentStatusAccount []*mvr.CurrentStatusAccount
	for _, account := range creditReportSummary.CurrentStatusAccounts.CurrentStatusAccount {
		currentStatusAccount = append(currentStatusAccount, &mvr.CurrentStatusAccount{
			Status:           account.Status,
			NumberOfAccounts: uint32(account.NumberOfAccounts),
		})
	}
	return currentStatusAccount, nil
}
