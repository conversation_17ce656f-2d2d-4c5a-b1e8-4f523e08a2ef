package cacheserver

import (
	"context"
	"encoding/xml"
	"time"

	"github.com/hooklift/gowsdl/soap"
)

type NonNegativeInteger int

type ID string

type IDREF string

type StateEnum string

type RelationshipType string

const (
	StateEnumAK StateEnum = "AK"

	StateEnumAL StateEnum = "AL"

	StateEnumAR StateEnum = "AR"

	StateEnumAZ StateEnum = "AZ"

	StateEnumBC StateEnum = "BC"

	StateEnumCA StateEnum = "CA"

	StateEnumCO StateEnum = "CO"

	StateEnumCT StateEnum = "CT"

	StateEnumDC StateEnum = "DC"

	StateEnumDE StateEnum = "DE"

	StateEnumFL StateEnum = "FL"

	StateEnumGA StateEnum = "GA"

	StateEnumGU StateEnum = "GU"

	StateEnumHI StateEnum = "HI"

	StateEnumIA StateEnum = "IA"

	StateEnumID StateEnum = "ID"

	StateEnumIL StateEnum = "IL"

	StateEnumIN StateEnum = "IN"

	StateEnumKS StateEnum = "KS"

	StateEnumKY StateEnum = "KY"

	StateEnumLA StateEnum = "LA"

	StateEnumMA StateEnum = "MA"

	StateEnumMD StateEnum = "MD"

	StateEnumME StateEnum = "ME"

	StateEnumMI StateEnum = "MI"

	StateEnumMN StateEnum = "MN"

	StateEnumMO StateEnum = "MO"

	StateEnumMS StateEnum = "MS"

	StateEnumMT StateEnum = "MT"

	StateEnumNB StateEnum = "NB"

	StateEnumNC StateEnum = "NC"

	StateEnumND StateEnum = "ND"

	StateEnumNE StateEnum = "NE"

	StateEnumNH StateEnum = "NH"

	StateEnumNJ StateEnum = "NJ"

	StateEnumNM StateEnum = "NM"

	StateEnumNS StateEnum = "NS"

	StateEnumNV StateEnum = "NV"

	StateEnumNY StateEnum = "NY"

	StateEnumOH StateEnum = "OH"

	StateEnumOK StateEnum = "OK"

	StateEnumON StateEnum = "ON"

	StateEnumOR StateEnum = "OR"

	StateEnumPA StateEnum = "PA"

	StateEnumPE StateEnum = "PE"

	StateEnumPQ StateEnum = "PQ"

	StateEnumPR StateEnum = "PR"

	StateEnumRI StateEnum = "RI"

	StateEnumSC StateEnum = "SC"

	StateEnumSD StateEnum = "SD"

	StateEnumTN StateEnum = "TN"

	StateEnumTX StateEnum = "TX"

	StateEnumUT StateEnum = "UT"

	StateEnumVT StateEnum = "VT"

	StateEnumVA StateEnum = "VA"

	StateEnumVI StateEnum = "VI"

	StateEnumWA StateEnum = "WA"

	StateEnumWV StateEnum = "WV"

	StateEnumWI StateEnum = "WI"

	StateEnumWY StateEnum = "WY"
)

// against "unused imports"
var (
	_ time.Time
	_ xml.Name
)

type AnyType struct {
	InnerXML string `xml:",innerxml"`
}

type AnyURI string

type NCName string

type MaritalStatusTypeEnum string

const (
	MaritalStatusTypeEnumM MaritalStatusTypeEnum = "M"

	MaritalStatusTypeEnumW MaritalStatusTypeEnum = "W"

	MaritalStatusTypeEnumD MaritalStatusTypeEnum = "D"

	MaritalStatusTypeEnumS MaritalStatusTypeEnum = "S"

	MaritalStatusTypeEnumX MaritalStatusTypeEnum = "X"

	MaritalStatusTypeEnumU MaritalStatusTypeEnum = "U"
)

type TelephoneTypeEnum string

const (
	TelephoneTypeEnumCell TelephoneTypeEnum = "Cell"

	TelephoneTypeEnumContact TelephoneTypeEnum = "Contact"

	TelephoneTypeEnumContactFax TelephoneTypeEnum = "Contact Fax"

	TelephoneTypeEnumEmployer TelephoneTypeEnum = "Employer"

	TelephoneTypeEnumFormer TelephoneTypeEnum = "Former"

	TelephoneTypeEnumPreviousEmployer TelephoneTypeEnum = "Previous Employer"

	TelephoneTypeEnumReference TelephoneTypeEnum = "Reference"

	TelephoneTypeEnumResidence TelephoneTypeEnum = "Residence"

	TelephoneTypeEnumResidenceFax TelephoneTypeEnum = "Residence Fax"

	TelephoneTypeEnumWork TelephoneTypeEnum = "Work"

	TelephoneTypeEnumWorkFax TelephoneTypeEnum = "Work Fax"

	TelephoneTypeEnumPhysicianMedical TelephoneTypeEnum = "Physician/Medical"

	TelephoneTypeEnumCurrentEmployer TelephoneTypeEnum = "Current Employer"

	TelephoneTypeEnumPolicyOwner TelephoneTypeEnum = "Policy Owner"

	TelephoneTypeEnumEducator TelephoneTypeEnum = "Educator"
)

type PolicyHolderRelationshipEnum string

const (
	PolicyHolderRelationshipEnumPrimary PolicyHolderRelationshipEnum = "Primary"

	PolicyHolderRelationshipEnumSecondary PolicyHolderRelationshipEnum = "Secondary"

	PolicyHolderRelationshipEnumExcluded PolicyHolderRelationshipEnum = "Excluded"

	PolicyHolderRelationshipEnumEmployee PolicyHolderRelationshipEnum = "Employee"

	PolicyHolderRelationshipEnumListed PolicyHolderRelationshipEnum = "Listed"

	PolicyHolderRelationshipEnumOther PolicyHolderRelationshipEnum = "Other"
)

type PolicyStatusEnum string

const (
	PolicyStatusEnumIneffect PolicyStatusEnum = "In-effect"

	PolicyStatusEnumExpired PolicyStatusEnum = "Expired"

	PolicyStatusEnumCancelled PolicyStatusEnum = "Cancelled"

	PolicyStatusEnumDisputed PolicyStatusEnum = "Disputed"
)

type AddressTypeEnum string

const (
	AddressTypeEnumPrimary AddressTypeEnum = "primary"

	AddressTypeEnumEducation AddressTypeEnum = "education"

	AddressTypeEnumFormer AddressTypeEnum = "former"

	AddressTypeEnumMailing AddressTypeEnum = "mailing"

	AddressTypeEnumResidence AddressTypeEnum = "residence"

	AddressTypeEnumWork AddressTypeEnum = "work"

	AddressTypeEnumProperty AddressTypeEnum = "property"

	AddressTypeEnumRisk AddressTypeEnum = "risk"

	AddressTypeEnumLoss AddressTypeEnum = "loss"
)

type LhrReportType string

const (
	LhrReportTypeSUBJECTANDVINSEARCH LhrReportType = "SUBJECT AND VIN SEARCH"

	LhrReportTypeSUBJECTONLYSEARCH LhrReportType = "SUBJECT ONLY SEARCH"

	LhrReportTypeCURRENTCARRIERCLUESUBJECTANDVIN LhrReportType = "CURRENT CARRIER CLUE SUBJECT AND VIN"

	LhrReportTypeCURRENTCARRIERCLUESUBJECTONLY LhrReportType = "CURRENT CARRIER CLUE SUBJECT ONLY"
)

type RequestingSystem string

const (
	RequestingSystemAUTOINTERNET RequestingSystem = "AUTO INTERNET"

	RequestingSystemNCF RequestingSystem = "NCF"

	RequestingSystemOTHER RequestingSystem = "OTHER"
)

type SubjectTypeEnum string

const (
	SubjectTypeEnumReportSubject SubjectTypeEnum = "Report Subject"

	SubjectTypeEnumReference SubjectTypeEnum = "Reference"

	SubjectTypeEnumEmployee SubjectTypeEnum = "Employee"

	SubjectTypeEnumDependent SubjectTypeEnum = "Dependent"

	SubjectTypeEnumSpouse SubjectTypeEnum = "Spouse"

	SubjectTypeEnumClaimantPolicyholder SubjectTypeEnum = "Claimant - Policyholder"

	SubjectTypeEnumClaimantInsured SubjectTypeEnum = "Claimant - Insured"

	SubjectTypeEnumClaimantThirdParty SubjectTypeEnum = "Claimant - Third Party"

	SubjectTypeEnumAssociatedPolicy SubjectTypeEnum = "Associated Policy"

	SubjectTypeEnumClaimantVehicleOperator SubjectTypeEnum = "Claimant - Vehicle Operator"

	SubjectTypeEnumClaimantSpouse SubjectTypeEnum = "Claimant - Spouse"

	SubjectTypeEnumClaimantDependent SubjectTypeEnum = "Claimant - Dependent"

	SubjectTypeEnumClaimantOther SubjectTypeEnum = "Claimant - Other"

	SubjectTypeEnumAliasAKA SubjectTypeEnum = "Alias (A/K/A)"

	SubjectTypeEnumFormerName SubjectTypeEnum = "Former Name"

	SubjectTypeEnumOther SubjectTypeEnum = "other"

	SubjectTypeEnumPolicyOwner SubjectTypeEnum = "Policy Owner"

	SubjectTypeEnumClaimant SubjectTypeEnum = "Claimant"

	SubjectTypeEnumInsured SubjectTypeEnum = "Insured"

	SubjectTypeEnumPolicyHolder SubjectTypeEnum = "Policy Holder"

	SubjectTypeEnumVehicleOperator SubjectTypeEnum = "Vehicle Operator"

	SubjectTypeEnumSelectedSubject SubjectTypeEnum = "Selected Subject"
)

type ClaimRelationshipTypeEnum string

const (
	ClaimRelationshipTypeEnumSubject ClaimRelationshipTypeEnum = "Subject"

	ClaimRelationshipTypeEnumSubjectsspouse ClaimRelationshipTypeEnum = "Subject's spouse"

	ClaimRelationshipTypeEnumSubjectsdependent ClaimRelationshipTypeEnum = "Subject's dependent"

	ClaimRelationshipTypeEnumInsured ClaimRelationshipTypeEnum = "Insured"

	ClaimRelationshipTypeEnumPolicyholder ClaimRelationshipTypeEnum = "Policyholder"

	ClaimRelationshipTypeEnumClaimant ClaimRelationshipTypeEnum = "Claimant"

	ClaimRelationshipTypeEnumOther ClaimRelationshipTypeEnum = "Other"

	ClaimRelationshipTypeEnumSurnamematch ClaimRelationshipTypeEnum = "Surname match"
)

type NameAssociationIndicatorTypeEnum string

const (
	NameAssociationIndicatorTypeEnumPolicyHolder NameAssociationIndicatorTypeEnum = "Policy Holder"

	NameAssociationIndicatorTypeEnumVehicleOperator NameAssociationIndicatorTypeEnum = "Vehicle Operator"
)

type ClaimMatchIndicatorEnum string

const (
	ClaimMatchIndicatorEnumAddressispartofclaimsrecordbutnotnecessarilythevehicleoperatorsaddress ClaimMatchIndicatorEnum = "Address is part of claims record, but not necessarily the vehicle operator's address"

	ClaimMatchIndicatorEnumClaimresultedfromaddressLexisNexisdevelopedratherthanoriginallyprovidedaddressnotnecessarilythevehicleoperatorsaddress ClaimMatchIndicatorEnum = "Claim resulted from address LexisNexis developed rather than originally provided address; not necessarily the vehicle operator's address"
)

type SearchMatchIndicatorEnum string

const (
	SearchMatchIndicatorEnumAddressonfileforthelossshownmatchesthemailingaddressinthesearchrequest SearchMatchIndicatorEnum = "Address on file for the loss shown matches the mailing address in the search request"

	SearchMatchIndicatorEnumAddressonfileforthelossshownmatchestheformeraddressinthesearchrequest SearchMatchIndicatorEnum = "Address on file for the loss shown matches the former address in the search request"

	SearchMatchIndicatorEnumAddressonfileforthelossshownmatchestheriskaddressinthesearchrequest SearchMatchIndicatorEnum = "Address on file for the loss shown matches the risk address in the search request"

	SearchMatchIndicatorEnumAddressonfileforthelossshownmatchesanaddressdevelopedbyIDENTITYPLUS SearchMatchIndicatorEnum = "Address on file for the loss shown matches an address developed by IDENTITY PLUS"
)

type HistoryTypeEnum string

const (
	HistoryTypeEnumCurrent HistoryTypeEnum = "Current"

	HistoryTypeEnumPrior HistoryTypeEnum = "Prior"

	HistoryTypeEnumAssociated HistoryTypeEnum = "Associated"
)

type ClaimAddressTypeEnum string

const (
	ClaimAddressTypeEnumEducation ClaimAddressTypeEnum = "Education"

	ClaimAddressTypeEnumFormer ClaimAddressTypeEnum = "Former"

	ClaimAddressTypeEnumMailing ClaimAddressTypeEnum = "Mailing"

	ClaimAddressTypeEnumResidence ClaimAddressTypeEnum = "Residence"

	ClaimAddressTypeEnumWork ClaimAddressTypeEnum = "Work"

	ClaimAddressTypeEnumProperty ClaimAddressTypeEnum = "Property"

	ClaimAddressTypeEnumRisk ClaimAddressTypeEnum = "Risk"

	ClaimAddressTypeEnumPolicyowner ClaimAddressTypeEnum = "Policyowner"

	ClaimAddressTypeEnumCrossstreet ClaimAddressTypeEnum = "Crossstreet"

	ClaimAddressTypeEnumBusiness ClaimAddressTypeEnum = "Business"
)

type CCProcessingStatusMessageType string

const (
	CCProcessingStatusMessageTypeRecordsFound CCProcessingStatusMessageType = "Record(s) Found"

	CCProcessingStatusMessageTypeRecordsFoundSecondaryReport CCProcessingStatusMessageType = "Record(s) Found, Secondary Report"

	CCProcessingStatusMessageTypeNoRecordFound CCProcessingStatusMessageType = "No Record Found"

	CCProcessingStatusMessageTypeNoRecordFoundSecondaryReport CCProcessingStatusMessageType = "No Record Found, Secondary Report"

	CCProcessingStatusMessageTypeInvalidAccount CCProcessingStatusMessageType = "Invalid Account"

	CCProcessingStatusMessageTypeInsufficientData CCProcessingStatusMessageType = "Insufficient Data"

	CCProcessingStatusMessageTypeCurrentCarrierUnavailable CCProcessingStatusMessageType = "Current Carrier Unavailable"

	CCProcessingStatusMessageTypeAccessNotPermitted CCProcessingStatusMessageType = "Access Not Permitted"

	CCProcessingStatusMessageTypeStateNotAvailableForThisAccount CCProcessingStatusMessageType = "State Not Available For This Account"

	CCProcessingStatusMessageTypeSecurityFreeze CCProcessingStatusMessageType = "Security Freeze"

	CCProcessingStatusMessageTypeProcessingcompletewithresultssecondaryreportspecialbilling CCProcessingStatusMessageType = "processing complete, with results, secondary report, special billing"

	CCProcessingStatusMessageTypeProcessingcompleteresultsclearsecondaryreportspecialbilling CCProcessingStatusMessageType = "processing complete, results clear, secondary report, special billing"

	CCProcessingStatusMessageTypeNotprocessedinquirysubjectsareunavailableduetoanAlerts CCProcessingStatusMessageType = "not processed; inquiry subject(s) are unavailable due to an Alert(s)"
)

type CCReportType string

const (
	CCReportTypeAutoUnderwritingReport CCReportType = "Auto Underwriting Report"

	CCReportTypeAutoAgentReport CCReportType = "Auto Agent Report"

	CCReportTypePropertyAgentReport CCReportType = "Property Agent Report"

	CCReportTypePropertyUnderwritingReport CCReportType = "Property Underwriting Report"
)

type CCPolicyStatusType string

const (
	CCPolicyStatusTypeIneffect CCPolicyStatusType = "In-effect"

	CCPolicyStatusTypeExpired CCPolicyStatusType = "Expired"

	CCPolicyStatusTypeCancelled CCPolicyStatusType = "Cancelled"

	CCPolicyStatusTypeDisputed CCPolicyStatusType = "Disputed"
)

type CCPolicyRiskType string

const (
	CCPolicyRiskTypeStandard CCPolicyRiskType = "Standard"

	CCPolicyRiskTypeNonStandard CCPolicyRiskType = "Non-Standard"

	CCPolicyRiskTypePreferred CCPolicyRiskType = "Preferred"

	CCPolicyRiskTypeAssigned CCPolicyRiskType = "Assigned"

	CCPolicyRiskTypeMixed CCPolicyRiskType = "Mixed"

	CCPolicyRiskTypeFacility CCPolicyRiskType = "Facility"
)

type InceptionIntervalType string

const (
	InceptionIntervalTypeLESSTHAN6MONTHS InceptionIntervalType = "LESS THAN 6 MONTHS"

	InceptionIntervalTypeGREATERTHAN6MONTHSLESSTHAN12 InceptionIntervalType = "GREATER THAN 6 MONTHS LESS THAN 12"

	InceptionIntervalTypeGREATERTHAN12MONTHSLESSTHAN18 InceptionIntervalType = "GREATER THAN 12 MONTHS LESS THAN 18"

	InceptionIntervalTypeGREATERTHAN18MONTHSLESSTHAN24 InceptionIntervalType = "GREATER THAN 18 MONTHS LESS THAN 24"

	InceptionIntervalTypeGREATERTHAN24MONTHSLESSTHAN30 InceptionIntervalType = "GREATER THAN 24 MONTHS LESS THAN 30"

	InceptionIntervalTypeGREATERTHAN30MONTHSLESSTHAN36 InceptionIntervalType = "GREATER THAN 30 MONTHS LESS THAN 36"

	InceptionIntervalTypeGREATERTHAN36MONTHS InceptionIntervalType = "GREATER THAN 36 MONTHS"
)

type CCRelationshipType string

const (
	CCRelationshipTypePrimary CCRelationshipType = "Primary"

	CCRelationshipTypeListed CCRelationshipType = "Listed"

	CCRelationshipTypeExcluded CCRelationshipType = "Excluded"
)

type CCVehicleType string

const (
	CCVehicleTypePrivatePassenger CCVehicleType = "Private Passenger"

	CCVehicleTypeMotorcycle CCVehicleType = "Motorcycle"

	CCVehicleTypeTruck CCVehicleType = "Truck"

	CCVehicleTypeUnknown CCVehicleType = "Unknown"
)

type CCAutoCoverageType string

const (
	CCAutoCoverageTypeBodilyInjury CCAutoCoverageType = "Bodily Injury"

	CCAutoCoverageTypeCollision CCAutoCoverageType = "Collision"

	CCAutoCoverageTypeComprehensive CCAutoCoverageType = "Comprehensive"

	CCAutoCoverageTypeCombinedSingleLimitsBIandPD CCAutoCoverageType = "Combined Single Limits BI and PD"

	CCAutoCoverageTypeMedicalExpense CCAutoCoverageType = "Medical Expense"

	CCAutoCoverageTypeMedicalPayments CCAutoCoverageType = "Medical Payments"

	CCAutoCoverageTypePropertyDamage CCAutoCoverageType = "Property Damage"

	CCAutoCoverageTypePersonalInjury CCAutoCoverageType = "Personal Injury"

	CCAutoCoverageTypeRentalReimbursement CCAutoCoverageType = "Rental Reimbursement"

	CCAutoCoverageTypeTowingandLabor CCAutoCoverageType = "Towing and Labor"

	CCAutoCoverageTypeUninsuredMotorist CCAutoCoverageType = "Uninsured Motorist"

	CCAutoCoverageTypeUnderinsuredMotorist CCAutoCoverageType = "Underinsured Motorist"

	CCAutoCoverageTypeUninsuredMotoristBodilyInjury CCAutoCoverageType = "Uninsured Motorist/Bodily Injury"

	CCAutoCoverageTypeUninsuredMotoristPropertyDamage CCAutoCoverageType = "Uninsured Motorist/Property Damage"

	CCAutoCoverageTypeUnderinsuredMotoristBodilyInjury CCAutoCoverageType = "Underinsured Motorist/Bodily Injury"

	CCAutoCoverageTypeUnderinsuredMotoristPropertyDamage CCAutoCoverageType = "Underinsured Motorist/Property Damage"

	CCAutoCoverageTypeOther CCAutoCoverageType = "Other"
)

type CCPropertyCoverageType string

const (
	CCPropertyCoverageTypeDwelling CCPropertyCoverageType = "Dwelling"

	CCPropertyCoverageTypeOtherStructures CCPropertyCoverageType = "Other Structures"

	CCPropertyCoverageTypePersonalProperty CCPropertyCoverageType = "Personal Property"

	CCPropertyCoverageTypeLossOfUse CCPropertyCoverageType = "Loss Of Use"

	CCPropertyCoverageTypeLiability CCPropertyCoverageType = "Liability"

	CCPropertyCoverageTypeMedicalPayments CCPropertyCoverageType = "Medical Payments"

	CCPropertyCoverageTypeBusinessPursuits CCPropertyCoverageType = "Business Pursuits"

	CCPropertyCoverageTypeEarthquake CCPropertyCoverageType = "Earthquake"

	CCPropertyCoverageTypeIncidentalFarming CCPropertyCoverageType = "Incidental Farming"

	CCPropertyCoverageTypeFirearms CCPropertyCoverageType = "Firearms"

	CCPropertyCoverageTypeGuaranteedReplacementCost CCPropertyCoverageType = "Guaranteed Replacement Cost"

	CCPropertyCoverageTypeHomeDaycare CCPropertyCoverageType = "Home Daycare"

	CCPropertyCoverageTypePersonalPropertyReplCost CCPropertyCoverageType = "Personal PropertyRepl Cost"

	CCPropertyCoverageTypeScheduledPersonalProperty CCPropertyCoverageType = "Scheduled Personal Property"

	CCPropertyCoverageTypeUmbrella CCPropertyCoverageType = "Umbrella"

	CCPropertyCoverageTypeWatercraft CCPropertyCoverageType = "Watercraft"

	CCPropertyCoverageTypeMoldCoverage CCPropertyCoverageType = "Mold Coverage"

	CCPropertyCoverageTypeOther CCPropertyCoverageType = "Other"
)

type PolicyTypeDefn string

const (
	PolicyTypeDefnPersonalAuto PolicyTypeDefn = "Personal Auto"

	PolicyTypeDefnMotorcycle PolicyTypeDefn = "Motorcycle"

	PolicyTypeDefnMotorhome PolicyTypeDefn = "Motorhome"

	PolicyTypeDefnHomeowners PolicyTypeDefn = "Homeowners"

	PolicyTypeDefnAuto PolicyTypeDefn = "Auto"

	PolicyTypeDefnBoatOwners PolicyTypeDefn = "Boat Owners"

	PolicyTypeDefnCondominium PolicyTypeDefn = "Condominium"

	PolicyTypeDefnFire PolicyTypeDefn = "Fire"

	PolicyTypeDefnFlood PolicyTypeDefn = "Flood"

	PolicyTypeDefnHail PolicyTypeDefn = "Hail"

	PolicyTypeDefnInlandMarine PolicyTypeDefn = "Inland Marine"

	PolicyTypeDefnMobileHome PolicyTypeDefn = "Mobile Home"

	PolicyTypeDefnQuake PolicyTypeDefn = "Quake"

	PolicyTypeDefnRanchFarm PolicyTypeDefn = "Ranch / Farm"

	PolicyTypeDefnTenant PolicyTypeDefn = "Tenant"

	PolicyTypeDefnUmbrella PolicyTypeDefn = "Umbrella"

	PolicyTypeDefnWatercraft PolicyTypeDefn = "Watercraft"

	PolicyTypeDefnOther PolicyTypeDefn = "Other"
)

type CCPolicyTypeDefn string

const (
	CCPolicyTypeDefnAutomobile CCPolicyTypeDefn = "Automobile"

	CCPolicyTypeDefnBoatOwners CCPolicyTypeDefn = "Boat Owners"

	CCPolicyTypeDefnHomeOwners CCPolicyTypeDefn = "Home Owners"

	CCPolicyTypeDefnMotorcycle CCPolicyTypeDefn = "Motorcycle"

	CCPolicyTypeDefnWatercraft CCPolicyTypeDefn = "Watercraft"

	CCPolicyTypeDefnRanchFarm CCPolicyTypeDefn = "Ranch Farm"

	CCPolicyTypeDefnCondominium CCPolicyTypeDefn = "Condominium"

	CCPolicyTypeDefnFire CCPolicyTypeDefn = "Fire"

	CCPolicyTypeDefnFlood CCPolicyTypeDefn = "Flood"

	CCPolicyTypeDefnHail CCPolicyTypeDefn = "Hail"

	CCPolicyTypeDefnInlandMarine CCPolicyTypeDefn = "Inland Marine"

	CCPolicyTypeDefnMobileHome CCPolicyTypeDefn = "Mobile Home"

	CCPolicyTypeDefnEarthquake CCPolicyTypeDefn = "Earthquake"

	CCPolicyTypeDefnTenant CCPolicyTypeDefn = "Tenant"

	CCPolicyTypeDefnUmbrella CCPolicyTypeDefn = "Umbrella"

	CCPolicyTypeDefnOther CCPolicyTypeDefn = "Other"
)

type CCProcessingSummaryStatus string

const (
	CCProcessingSummaryStatusY CCProcessingSummaryStatus = "Y"

	CCProcessingSummaryStatusN CCProcessingSummaryStatus = "N"
)

type LJSourceIndicatorEnum string

const (
	LJSourceIndicatorEnumLexisNexis LJSourceIndicatorEnum = "LexisNexis"

	LJSourceIndicatorEnumVendor LJSourceIndicatorEnum = "Vendor"
)

type LJStatusEnum string

const (
	LJStatusEnumInsufficientDatacouldnotlocateconsumerinLexisNexisdatabase LJStatusEnum = "Insufficient Data - could not locate consumer in LexisNexis database"

	LJStatusEnumNoHit LJStatusEnum = "No Hit"

	LJStatusEnumProductunavailable LJStatusEnum = "Product unavailable"

	LJStatusEnumComplete LJStatusEnum = "Complete"

	LJStatusEnumLandJNotRequested LJStatusEnum = "L and J Not Requested"
)

type EHStatusEnum string

const (
	EHStatusEnumEnhancedVersion EHStatusEnum = "Enhanced Version"

	EHStatusEnumTrendedData EHStatusEnum = "Trended Data"

	EHStatusEnumOriginalVersion EHStatusEnum = "Original Version"
)

type AlertsClassification string

const (
	AlertsClassificationASSISTScoreandReasons AlertsClassification = "ASSIST Score and Reasons"

	AlertsClassificationBeaconMessage AlertsClassification = "Beacon Message"

	AlertsClassificationFICO AlertsClassification = "FICO"

	AlertsClassificationEmpirica AlertsClassification = "Empirica"

	AlertsClassificationFraudDetectionSafescanMessage AlertsClassification = "Fraud Detection/Safescan Message"

	AlertsClassificationFACS AlertsClassification = "FACS+"

	AlertsClassificationHawk AlertsClassification = "Hawk"

	AlertsClassificationLexisNexisCustomModel AlertsClassification = "LexisNexis Custom Model"

	AlertsClassificationLexisNexisModel AlertsClassification = "LexisNexis Model"

	AlertsClassificationNationalRisk AlertsClassification = "National Risk"

	AlertsClassificationGeneralMessage AlertsClassification = "General Message"

	AlertsClassificationAttract3_0Model AlertsClassification = "Attract 3.0 Model"

	AlertsClassificationLexisNexisAttractScore AlertsClassification = "LexisNexis Attract Score"

	AlertsClassificationLexisNexisConsumerStatement AlertsClassification = "LexisNexis Consumer Statement"

	AlertsClassificationVantageScore AlertsClassification = "Vantage Score"

	AlertsClassificationUnknown AlertsClassification = "Unknown"
)

type ScoreStatusEnum string

const (
	ScoreStatusEnumScored ScoreStatusEnum = "Scored"

	ScoreStatusEnumNoScore ScoreStatusEnum = "No Score"

	ScoreStatusEnumNoHit ScoreStatusEnum = "No Hit"

	ScoreStatusEnumNoResult ScoreStatusEnum = "No Result"

	ScoreStatusEnumError ScoreStatusEnum = "Error"

	ScoreStatusEnumUnknown ScoreStatusEnum = "Unknown"

	ScoreStatusEnumDuplicate ScoreStatusEnum = "Duplicate"
)

type CreditVendorsEnum string

const (
	CreditVendorsEnumEquifax CreditVendorsEnum = "Equifax"

	CreditVendorsEnumExperian CreditVendorsEnum = "Experian"

	CreditVendorsEnumTransUnion CreditVendorsEnum = "Trans Union"

	CreditVendorsEnumUnknown CreditVendorsEnum = "Unknown"
)

type AlertsErrorEnum string

const (
	AlertsErrorEnumReprocessCreditVendorAccessedisTemporarilyUnavailable AlertsErrorEnum = "Reprocess - Credit Vendor Accessed is Temporarily Unavailable"

	AlertsErrorEnumDataErrorStopProcessingReadMessage AlertsErrorEnum = "Data Error - Stop Processing - Read Message "

	AlertsErrorEnumFatalError_CallLexisNexisforAdditionalInformation AlertsErrorEnum = "Fatal Error. Call LexisNexis for Additional Information"

	AlertsErrorEnumUnknown AlertsErrorEnum = "Unknown"
)

type NCFNarrativeCodeEnum string

const (
	NCFNarrativeCodeEnumSecurityFreeze NCFNarrativeCodeEnum = "Security Freeze"

	NCFNarrativeCodeEnumAddressDiscrepancyonFile NCFNarrativeCodeEnum = "Address Discrepancy on File"

	NCFNarrativeCodeEnumCurrentaddressdoesnotmatchinputaddressExperian NCFNarrativeCodeEnum = "Current address does not match input address (Experian)"

	NCFNarrativeCodeEnumPrioraddressdoesnotmatchoneinputaddressExperian NCFNarrativeCodeEnum = "Prior address does not match one input address (Experian)"

	NCFNarrativeCodeEnumPrioraddressdoesnotmatchanyinputaddressesExperian NCFNarrativeCodeEnum = "Prior address does not match any input addresses (Experian)"

	NCFNarrativeCodeEnumCurrentaddressdoesnotmatchinputaddressTransUnion NCFNarrativeCodeEnum = "Current address does not match input address (TransUnion)"

	NCFNarrativeCodeEnumPrioraddressdoesnotmatchoneinputaddressTransUnion NCFNarrativeCodeEnum = "Prior address does not match one input address (TransUnion)"

	NCFNarrativeCodeEnumPrioraddressdoesnotmatchanyinputaddressesTransUnion NCFNarrativeCodeEnum = "Prior address does not match any input addresses (TransUnion)"

	NCFNarrativeCodeEnumActiveDutyAlert NCFNarrativeCodeEnum = "Active Duty Alert"

	NCFNarrativeCodeEnumFraudVictim NCFNarrativeCodeEnum = "Fraud Victim"

	NCFNarrativeCodeEnumExtendedFraudAlert NCFNarrativeCodeEnum = "Extended Fraud Alert"

	NCFNarrativeCodeEnumSecurityAlert NCFNarrativeCodeEnum = "Security Alert"

	NCFNarrativeCodeEnumNaturalDisaster NCFNarrativeCodeEnum = "Natural Disaster"

	NCFNarrativeCodeEnumForeclosureActivity NCFNarrativeCodeEnum = "Foreclosure Activity"

	NCFNarrativeCodeEnumSubjectdeceased NCFNarrativeCodeEnum = "Subject deceased"

	NCFNarrativeCodeEnumIdentityTheftVictim NCFNarrativeCodeEnum = "Identity Theft Victim"

	NCFNarrativeCodeEnumLexisNexisIdentityTheft NCFNarrativeCodeEnum = "LexisNexis Identity Theft"

	NCFNarrativeCodeEnumLexisNexisSecurityFraudAlert NCFNarrativeCodeEnum = "LexisNexis Security Fraud Alert"

	NCFNarrativeCodeEnumLexisNexisSecurityFreeze NCFNarrativeCodeEnum = "LexisNexis Security Freeze"

	NCFNarrativeCodeEnumLexisNexisLegalHold NCFNarrativeCodeEnum = "LexisNexis Legal Hold"

	NCFNarrativeCodeEnumConsumerStatement NCFNarrativeCodeEnum = "Consumer Statement"

	NCFNarrativeCodeEnumIdentityNotResolved NCFNarrativeCodeEnum = "Identity Not Resolved"

	NCFNarrativeCodeEnumUnknown NCFNarrativeCodeEnum = "Unknown"
)

type CurrentCarrierReport struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 CurrentCarrierReport"`

	Admin *InsCurrentCarrierReportAdmin `xml:"Admin,omitempty" json:"Admin,omitempty"`

	Report *InsCurrentCarrierReportSection `xml:"Report,omitempty" json:"Report,omitempty"`

	ICIScoreAttributes []*InsCurrentCarrierICIScoreAttributesSection `xml:"ICIScoreAttributes,omitempty" json:"ICIScoreAttributes,omitempty"`

	RiskAddress *InsCurrentCarrierRiskAddressSection `xml:"RiskAddress,omitempty" json:"RiskAddress,omitempty"`
}

type InsCurrentCarrierRiskAddressSection struct {
	ProcessingSummary *CCProcessingSummary `xml:"ProcessingSummary,omitempty" json:"ProcessingSummary,omitempty"`

	DisclaimerInformation struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"DisclaimerInformation,omitempty" json:"DisclaimerInformation,omitempty"`

	PolicyInformation []*CCPolicyInfoType `xml:"PolicyInformation,omitempty" json:"PolicyInformation,omitempty"`
}

type CCPolicyInfoType struct {
	PolicyStatus *CCPolicyStatusType `xml:"PolicyStatus,omitempty" json:"PolicyStatus,omitempty"`

	CarrierName string `xml:"CarrierName,omitempty" json:"CarrierName,omitempty"`

	AmbestNumber string `xml:"AmbestNumber,omitempty" json:"AmbestNumber,omitempty"`

	NAICCode string `xml:"NAICCode,omitempty" json:"NAICCode,omitempty"`

	RiskType *CCPolicyRiskType `xml:"RiskType,omitempty" json:"RiskType,omitempty"`

	InceptionDate *PrecisionDateTime `xml:"InceptionDate,omitempty" json:"InceptionDate,omitempty"`

	InceptionInterval *InceptionIntervalType `xml:"InceptionInterval,omitempty" json:"InceptionInterval,omitempty"`

	PolicyHoldersList *CCPolicyHolderInfoListType `xml:"PolicyHoldersList,omitempty" json:"PolicyHoldersList,omitempty"`

	CoveredVehiclesList *CCCoveredVehicleInfoListType `xml:"CoveredVehiclesList,omitempty" json:"CoveredVehiclesList,omitempty"`

	CoveredPropertiesList *CCCoveredPropertyInfoListType `xml:"CoveredPropertiesList,omitempty" json:"CoveredPropertiesList,omitempty"`

	DisputeStatements *CCDisputeStatementsType `xml:"DisputeStatements,omitempty" json:"DisputeStatements,omitempty"`

	ConsumerStatements *CCConsumerStatementsType `xml:"ConsumerStatements,omitempty" json:"ConsumerStatements,omitempty"`

	PolicyStateCode string `xml:"PolicyStateCode,omitempty" json:"PolicyStateCode,omitempty"`

	RecordStatements struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"RecordStatements,omitempty" json:"RecordStatements,omitempty"`

	CancellationReasonCode string `xml:"CancellationReasonCode,omitempty" json:"CancellationReasonCode,omitempty"`

	ConsumerProvidedIndicator *YesNo `xml:"ConsumerProvidedIndicator,attr,omitempty" json:"ConsumerProvidedIndicator,omitempty"`

	PolicyNumber string `xml:"PolicyNumber,attr,omitempty" json:"PolicyNumber,omitempty"`

	PolicyType *CCPolicyTypeDefn `xml:"PolicyType,attr,omitempty" json:"PolicyType,omitempty"`
}

type CCCoveredPropertyInfoListType struct {
	CoveredPropertyInfo []*CCCoveredPropertyInfoType `xml:"CoveredPropertyInfo,omitempty" json:"CoveredPropertyInfo,omitempty"`
}

type CCCoveredPropertyInfoType struct {
	PropertyStreetAddress string `xml:"PropertyStreetAddress,omitempty" json:"PropertyStreetAddress,omitempty"`

	PropertyCityStateZip string `xml:"PropertyCityStateZip,omitempty" json:"PropertyCityStateZip,omitempty"`

	PropertyCoverageInfo *CCPropertyCoverageInfoListType `xml:"PropertyCoverageInfo,omitempty" json:"PropertyCoverageInfo,omitempty"`

	PropertyCoverageLimitsInfo *CCPropertyCoverageLimitsType `xml:"PropertyCoverageLimitsInfo,omitempty" json:"PropertyCoverageLimitsInfo,omitempty"`

	LienInfoList *LienInfoListType `xml:"LienInfoList,omitempty" json:"LienInfoList,omitempty"`

	PropertyCoverageDeductibleInfo *CCPropertyCoverageDeductibleInfoType `xml:"PropertyCoverageDeductibleInfo,omitempty" json:"PropertyCoverageDeductibleInfo,omitempty"`
}

type LienInfoListType struct {
	LienInfo []*LienInfoType `xml:"LienInfo,omitempty" json:"LienInfo,omitempty"`
}

type LienInfoType struct {
	LienHolderSequenceNumber string `xml:"LienHolderSequenceNumber,omitempty" json:"LienHolderSequenceNumber,omitempty"`

	LienHolderName string `xml:"LienHolderName,omitempty" json:"LienHolderName,omitempty"`

	LienHolderStreetAddress string `xml:"LienHolderStreetAddress,omitempty" json:"LienHolderStreetAddress,omitempty"`

	LienHolderCityStateZip string `xml:"LienHolderCityStateZip,omitempty" json:"LienHolderCityStateZip,omitempty"`
}

type CCPropertyCoverageInfoListType struct {
	PropertyCoverageType []*CCPropertyCoverageType `xml:"PropertyCoverageType,omitempty" json:"PropertyCoverageType,omitempty"`
}

type CCPropertyCoverageLimitsType struct {
	CoverageALimit *NonNegativeInteger `xml:"CoverageALimit,omitempty" json:"CoverageALimit,omitempty"`

	CoverageBLimit *NonNegativeInteger `xml:"CoverageBLimit,omitempty" json:"CoverageBLimit,omitempty"`

	CoverageCLimit *NonNegativeInteger `xml:"CoverageCLimit,omitempty" json:"CoverageCLimit,omitempty"`

	CoverageDLimit *NonNegativeInteger `xml:"CoverageDLimit,omitempty" json:"CoverageDLimit,omitempty"`

	CoverageELimit *NonNegativeInteger `xml:"CoverageELimit,omitempty" json:"CoverageELimit,omitempty"`

	CoverageFLimit *NonNegativeInteger `xml:"CoverageFLimit,omitempty" json:"CoverageFLimit,omitempty"`

	CoverageBUSPLimit *NonNegativeInteger `xml:"CoverageBUSPLimit,omitempty" json:"CoverageBUSPLimit,omitempty"`

	CoverageERQKLimit *NonNegativeInteger `xml:"CoverageERQKLimit,omitempty" json:"CoverageERQKLimit,omitempty"`

	CoverageFARMLimit *NonNegativeInteger `xml:"CoverageFARMLimit,omitempty" json:"CoverageFARMLimit,omitempty"`

	CoverageFIRALimit *NonNegativeInteger `xml:"CoverageFIRALimit,omitempty" json:"CoverageFIRALimit,omitempty"`

	CoverageGRPLimit *NonNegativeInteger `xml:"CoverageGRPLimit,omitempty" json:"CoverageGRPLimit,omitempty"`

	CoverageHDCRLimit *NonNegativeInteger `xml:"CoverageHDCRLimit,omitempty" json:"CoverageHDCRLimit,omitempty"`

	CoveragePPRCLimit *NonNegativeInteger `xml:"CoveragePPRCLimit,omitempty" json:"CoveragePPRCLimit,omitempty"`

	CoverageSCPPLimit *NonNegativeInteger `xml:"CoverageSCPPLimit,omitempty" json:"CoverageSCPPLimit,omitempty"`

	CoverageUMBRLimit *NonNegativeInteger `xml:"CoverageUMBRLimit,omitempty" json:"CoverageUMBRLimit,omitempty"`

	CoverageWTCRLimit *NonNegativeInteger `xml:"CoverageWTCRLimit,omitempty" json:"CoverageWTCRLimit,omitempty"`

	CoverageMOLDLimit *NonNegativeInteger `xml:"CoverageMOLDLimit,omitempty" json:"CoverageMOLDLimit,omitempty"`

	CoverageOTHRLimit *NonNegativeInteger `xml:"CoverageOTHRLimit,omitempty" json:"CoverageOTHRLimit,omitempty"`
}

type CCPropertyCoverageDeductibleInfoType struct {
	AllPerilsLimit *NonNegativeInteger `xml:"AllPerilsLimit,omitempty" json:"AllPerilsLimit,omitempty"`

	TheftLimit *NonNegativeInteger `xml:"TheftLimit,omitempty" json:"TheftLimit,omitempty"`
}

type CCDisputeStatementsType struct {
	DisputeFiledDate *PrecisionDateTime `xml:"DisputeFiledDate,omitempty" json:"DisputeFiledDate,omitempty"`

	FiledByName string `xml:"FiledByName,omitempty" json:"FiledByName,omitempty"`

	FiledByRelationship string `xml:"FiledByRelationship,omitempty" json:"FiledByRelationship,omitempty"`

	ResolutionDate *PrecisionDateTime `xml:"ResolutionDate,omitempty" json:"ResolutionDate,omitempty"`

	DisputeNarrativeMessages *CCDisputeNarrativeType `xml:"DisputeNarrativeMessages,omitempty" json:"DisputeNarrativeMessages,omitempty"`
}

type CCDisputeNarrativeType struct {
	NarrativeStatement []string `xml:"NarrativeStatement,omitempty" json:"NarrativeStatement,omitempty"`
}

type CCConsumerStatementsType struct {
	NarrativeStatement []string `xml:"NarrativeStatement,omitempty" json:"NarrativeStatement,omitempty"`
}

type CCCoveredVehicleInfoListType struct {
	CoveredVehicleInfo []*CCCoveredVehicleInfoType `xml:"CoveredVehicleInfo,omitempty" json:"CoveredVehicleInfo,omitempty"`
}

type CCCoveredVehicleInfoType struct {
	VINNumber string `xml:"VINNumber,omitempty" json:"VINNumber,omitempty"`

	VehicleYear string `xml:"VehicleYear,omitempty" json:"VehicleYear,omitempty"`

	VehicleMake string `xml:"VehicleMake,omitempty" json:"VehicleMake,omitempty"`

	VehicleType *CCVehicleType `xml:"VehicleType,omitempty" json:"VehicleType,omitempty"`

	BusinessUseIndicator *YesNo `xml:"BusinessUseIndicator,omitempty" json:"BusinessUseIndicator,omitempty"`

	AutocoverageInfo *CCAutoCoverageInfoListType `xml:"AutocoverageInfo,omitempty" json:"AutocoverageInfo,omitempty"`

	AutocoverageLimitsInfo *CCAutoCoverageLimitsType `xml:"AutocoverageLimitsInfo,omitempty" json:"AutocoverageLimitsInfo,omitempty"`

	LeasedVehicleIndicator string `xml:"LeasedVehicleIndicator,omitempty" json:"LeasedVehicleIndicator,omitempty"`

	LienInfoList *LienInfoListType `xml:"LienInfoList,omitempty" json:"LienInfoList,omitempty"`
}

type CCAutoCoverageInfoListType struct {
	AutoCoverageType []*CCAutoCoverageType `xml:"AutoCoverageType,omitempty" json:"AutoCoverageType,omitempty"`
}

type CCAutoCoverageLimitsType struct {
	BodilyInjuryLimit *NonNegativeInteger `xml:"BodilyInjuryLimit,omitempty" json:"BodilyInjuryLimit,omitempty"`

	OccurrenceLimit *NonNegativeInteger `xml:"OccurrenceLimit,omitempty" json:"OccurrenceLimit,omitempty"`

	PropertyDamageLimit *NonNegativeInteger `xml:"PropertyDamageLimit,omitempty" json:"PropertyDamageLimit,omitempty"`

	CombinedSingleLimit *NonNegativeInteger `xml:"CombinedSingleLimit,omitempty" json:"CombinedSingleLimit,omitempty"`

	UninsuredMotoristsIndividualLimit *NonNegativeInteger `xml:"UninsuredMotoristsIndividualLimit,omitempty" json:"UninsuredMotoristsIndividualLimit,omitempty"`

	UninsuredMotoristsOccurrenceLimit *NonNegativeInteger `xml:"UninsuredMotoristsOccurrenceLimit,omitempty" json:"UninsuredMotoristsOccurrenceLimit,omitempty"`

	UnderinsuredMotoristsIndividualLimit *NonNegativeInteger `xml:"UnderinsuredMotoristsIndividualLimit,omitempty" json:"UnderinsuredMotoristsIndividualLimit,omitempty"`

	UnderinsuredMotoristsOccurrenceLimit *NonNegativeInteger `xml:"UnderinsuredMotoristsOccurrenceLimit,omitempty" json:"UnderinsuredMotoristsOccurrenceLimit,omitempty"`

	CollisionDeductible *NonNegativeInteger `xml:"CollisionDeductible,omitempty" json:"CollisionDeductible,omitempty"`

	ComprehensiveDeductible *NonNegativeInteger `xml:"ComprehensiveDeductible,omitempty" json:"ComprehensiveDeductible,omitempty"`
}

type CCPolicyHolderInfoListType struct {
	PolicyHolderInfo []*CCPolicyHolderInfoType `xml:"PolicyHolderInfo,omitempty" json:"PolicyHolderInfo,omitempty"`
}

type CCPolicyHolderInfoType struct {
	SubjectUnitNumber string `xml:"SubjectUnitNumber,omitempty" json:"SubjectUnitNumber,omitempty"`

	PolicyHolderName *Name `xml:"PolicyHolderName,omitempty" json:"PolicyHolderName,omitempty"`

	PolicyHolderRelationship *CCRelationshipType `xml:"PolicyHolderRelationship,omitempty" json:"PolicyHolderRelationship,omitempty"`

	PolicyFromDate *PrecisionDateTime `xml:"PolicyFromDate,omitempty" json:"PolicyFromDate,omitempty"`

	PolicyToDate *PrecisionDateTime `xml:"PolicyToDate,omitempty" json:"PolicyToDate,omitempty"`

	StartDate *PrecisionDateTime `xml:"StartDate,omitempty" json:"StartDate,omitempty"`

	Occurrences string `xml:"Occurrences,omitempty" json:"Occurrences,omitempty"`

	LastCancelDate *PrecisionDateTime `xml:"LastCancelDate,omitempty" json:"LastCancelDate,omitempty"`
}

type CCProcessingSummary struct {
	PoliciesReturned *CCProcessingSummaryStatusCount `xml:"PoliciesReturned,omitempty" json:"PoliciesReturned,omitempty"`

	CurrentPolicy *CCProcessingSummaryStatusCount `xml:"CurrentPolicy,omitempty" json:"CurrentPolicy,omitempty"`
}

type CCProcessingSummaryStatusCount struct {
	Status *CCProcessingSummaryStatus `xml:"Status,omitempty" json:"Status,omitempty"`

	Count *NonNegativeInteger `xml:"Count,omitempty" json:"Count,omitempty"`
}

type InsCurrentCarrierICIScoreAttributesSection struct {
	Attribute []*NameValuePair `xml:"Attribute,omitempty" json:"Attribute,omitempty"`

	UnitNumber *NonNegativeInteger `xml:"UnitNumber,attr,omitempty" json:"UnitNumber,omitempty"`
}

type InsCurrentCarrierReportSection struct {
	DisclaimerInformation struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"DisclaimerInformation,omitempty" json:"DisclaimerInformation,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	PolicyInformation []*CCPolicyInfoType `xml:"PolicyInformation,omitempty" json:"PolicyInformation,omitempty"`

	ConsumerStatements struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"ConsumerStatements,omitempty" json:"ConsumerStatements,omitempty"`
}

type InsCurrentCarrierReportAdmin struct {
	*BaseAdminType

	Status *CCProcessingStatusMessageType `xml:"Status,omitempty" json:"Status,omitempty"`

	ReportType *CCReportType `xml:"ReportType,omitempty" json:"ReportType,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type BaseAdminType struct {
	QuoteBacks struct {
		QuoteBack []*QBNameValuePair `xml:"QuoteBack,omitempty" json:"QuoteBack,omitempty"`
	} `xml:"QuoteBacks,omitempty" json:"QuoteBacks,omitempty"`

	PNCAccount struct {
		Value string `xml:",chardata" json:"-,"`

		Name string `xml:"Name,attr,omitempty" json:"Name,omitempty"`
	} `xml:"PNCAccount,omitempty" json:"PNCAccount,omitempty"`

	SpecialBillingId string `xml:"SpecialBillingId,omitempty" json:"SpecialBillingId,omitempty"`

	ProductReference string `xml:"ProductReference,omitempty" json:"ProductReference,omitempty"`

	ReportCode string `xml:"ReportCode,omitempty" json:"ReportCode,omitempty"`

	ReportDescription string `xml:"ReportDescription,omitempty" json:"ReportDescription,omitempty"`

	ReceiptDate *PrecisionDateTime `xml:"ReceiptDate,omitempty" json:"ReceiptDate,omitempty"`

	DateTimeOrdered *PrecisionDateTime `xml:"DateTimeOrdered,omitempty" json:"DateTimeOrdered,omitempty"`

	DateCompleted *PrecisionDateTime `xml:"DateCompleted,omitempty" json:"DateCompleted,omitempty"`

	CustomerOrganizationCodes struct {
		CustomerOrganizationCode []*CustomerOrganizationCode `xml:"CustomerOrganizationCode,omitempty" json:"CustomerOrganizationCode,omitempty"`
	} `xml:"CustomerOrganizationCodes,omitempty" json:"CustomerOrganizationCodes,omitempty"`

	AttachmentStatus struct {
		Status []string `xml:"Status,omitempty" json:"Status,omitempty"`
	} `xml:"AttachmentStatus,omitempty" json:"AttachmentStatus,omitempty"`

	Parameter *ParameterType `xml:"Parameter,omitempty" json:"Parameter,omitempty"`
}

type CustomerOrganizationCode struct {
	Code string `xml:"Code,omitempty" json:"Code,omitempty"`

	Level int32 `xml:"Level,attr,omitempty" json:"Level,omitempty"`
}

type LienHolders struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 LienHolders"`

	LienHolder []*LienHolder `xml:"LienHolder,omitempty" json:"LienHolder,omitempty"`
}

type LienHolder struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 LienHolder"`

	MortgageCompany string `xml:"MortgageCompany,omitempty" json:"MortgageCompany,omitempty"`

	PremiumAmountDue string `xml:"PremiumAmountDue,omitempty" json:"PremiumAmountDue,omitempty"`
}

type FormerAddresses struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 FormerAddresses"`

	FormerAddress []*AddressType `xml:"FormerAddress,omitempty" json:"FormerAddress,omitempty"`
}

type GeneralMessage struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 GeneralMessage"`

	MessageText string `xml:"MessageText,omitempty" json:"MessageText,omitempty"`

	Classification *AlertsClassification `xml:"Classification,attr,omitempty" json:"Classification,omitempty"`

	NarrativeCode string `xml:"NarrativeCode,attr,omitempty" json:"NarrativeCode,omitempty"`

	NarrativeDescription *NCFNarrativeCodeEnum `xml:"NarrativeDescription,attr,omitempty" json:"NarrativeDescription,omitempty"`
}

type InquiryNationalCreditFile struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 InquiryNationalCreditFile"`

	SearchBy *NcfSearchBy `xml:"SearchBy,omitempty" json:"SearchBy,omitempty"`

	OptionId *IdentificationType `xml:"OptionId,omitempty" json:"OptionId,omitempty"`
}

type InternationalTelephone struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 InternationalTelephone"`

	Classification *TelephoneTypeEnum `xml:"Classification,omitempty" json:"Classification,omitempty"`

	CountryCode string `xml:"CountryCode,omitempty" json:"CountryCode,omitempty"`

	Number string `xml:"Number,omitempty" json:"Number,omitempty"`
}

type Mortgage struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 Mortgage"`

	Number string `xml:"Number,omitempty" json:"Number,omitempty"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`

	Company string `xml:"Company,omitempty" json:"Company,omitempty"`

	Borrowers struct {
		Borrower []string `xml:"Borrower,omitempty" json:"Borrower,omitempty"`
	} `xml:"Borrowers,omitempty" json:"Borrowers,omitempty"`

	MortgageType string `xml:"MortgageType,omitempty" json:"MortgageType,omitempty"`

	LoanAmount string `xml:"LoanAmount,omitempty" json:"LoanAmount,omitempty"`

	LoanType string `xml:"LoanType,omitempty" json:"LoanType,omitempty"`

	InterestRate string `xml:"InterestRate,omitempty" json:"InterestRate,omitempty"`

	InterestRateType string `xml:"InterestRateType,omitempty" json:"InterestRateType,omitempty"`
}

type NcfReportTypeEnum string

const (
	NcfReportTypeEnumRegularNCFtransaction NcfReportTypeEnum = "Regular NCF transaction"

	NcfReportTypeEnumFSIFinancialStressIndicator NcfReportTypeEnum = "FSI (Financial Stress Indicator)"

	NcfReportTypeEnumGovernment NcfReportTypeEnum = "Government"

	NcfReportTypeEnumNCFReport NcfReportTypeEnum = "NCF Report"

	NcfReportTypeEnumCommercialInsurance NcfReportTypeEnum = "Commercial Insurance"

	NcfReportTypeEnumAttractwithClaims NcfReportTypeEnum = "Attract with Claims"

	NcfReportTypeEnumIndependentAgent NcfReportTypeEnum = "Independent Agent"

	NcfReportTypeEnumSocialSearch NcfReportTypeEnum = "Social Search"

	NcfReportTypeEnumParallelProductReport NcfReportTypeEnum = "Parallel Product Report"

	NcfReportTypeEnumAddressUpdate NcfReportTypeEnum = "Address Update"

	NcfReportTypeEnumCanadianSearch NcfReportTypeEnum = "Canadian Search"
)

type NcfUsage string

const (
	NcfUsageClaims NcfUsage = "Claims"

	NcfUsageUnderwriting NcfUsage = "Underwriting"
)

type NcfBusinessEnum string

const (
	NcfBusinessEnumAuto NcfBusinessEnum = "Auto"

	NcfBusinessEnumProperty NcfBusinessEnum = "Property"
)

type NcfAddonProductEnum string

const (
	NcfAddonProductEnumCurrentCarrier NcfAddonProductEnum = "Current Carrier"
)

type NcfVendorEnum string

const (
	NcfVendorEnumEquifax NcfVendorEnum = "Equifax"

	NcfVendorEnumExperian NcfVendorEnum = "Experian"

	NcfVendorEnumTransUnion NcfVendorEnum = "TransUnion"
)

type NcfPolicyTypeEnum string

const (
	NcfPolicyTypeEnumProperty1 NcfPolicyTypeEnum = "Property 1"

	NcfPolicyTypeEnumProperty2 NcfPolicyTypeEnum = "Property 2"

	NcfPolicyTypeEnumPropertyAllRisk NcfPolicyTypeEnum = "Property All Risk"

	NcfPolicyTypeEnumPropertyRenter NcfPolicyTypeEnum = "Property Renter"

	NcfPolicyTypeEnumPropertyCondo NcfPolicyTypeEnum = "Property Condo"

	NcfPolicyTypeEnumPropertyDwelling NcfPolicyTypeEnum = "Property Dwelling"

	NcfPolicyTypeEnumAutoNonstandard NcfPolicyTypeEnum = "Auto Non-standard"

	NcfPolicyTypeEnumAutoPrefered NcfPolicyTypeEnum = "Auto Prefered"

	NcfPolicyTypeEnumAutoPreferred NcfPolicyTypeEnum = "Auto Preferred"

	NcfPolicyTypeEnumAutoPreferredaboveminimumlimits NcfPolicyTypeEnum = "Auto Preferred above minimum limits"

	NcfPolicyTypeEnumAutoStandard NcfPolicyTypeEnum = "Auto Standard"

	NcfPolicyTypeEnumAutoStandardaboveminimumlimits NcfPolicyTypeEnum = "Auto Standard above minimum limits"

	NcfPolicyTypeEnumNoscoreordered NcfPolicyTypeEnum = "No score ordered"

	NcfPolicyTypeEnumAttractAuto3_0 NcfPolicyTypeEnum = "Attract Auto 3.0"

	NcfPolicyTypeEnumAttractHome3_0 NcfPolicyTypeEnum = "Attract Home 3.0"

	NcfPolicyTypeEnumAttractOne3_0 NcfPolicyTypeEnum = "Attract One 3.0"

	NcfPolicyTypeEnumOther NcfPolicyTypeEnum = "Other"
)

type SubjectSourceTypeEnum string

const (
	SubjectSourceTypeEnumClueauto SubjectSourceTypeEnum = "clue auto"

	SubjectSourceTypeEnumClueproperty SubjectSourceTypeEnum = "clue property"

	SubjectSourceTypeEnumNcf SubjectSourceTypeEnum = "ncf"

	SubjectSourceTypeEnumIva SubjectSourceTypeEnum = "iva"

	SubjectSourceTypeEnumBoa SubjectSourceTypeEnum = "boa"

	SubjectSourceTypeEnumCda SubjectSourceTypeEnum = "cda"
)

type LineOfBusinessEnum string

const (
	LineOfBusinessEnumAuto LineOfBusinessEnum = "Auto"

	LineOfBusinessEnumPersonalAuto LineOfBusinessEnum = "PersonalAuto"

	LineOfBusinessEnumPersonalProperty LineOfBusinessEnum = "PersonalProperty"

	LineOfBusinessEnumLife LineOfBusinessEnum = "Life"

	LineOfBusinessEnumCommercial LineOfBusinessEnum = "Commercial"
)

type YesNo string

const (
	YesNoYes YesNo = "yes"

	YesNoNo YesNo = "no"
)

type D2COptionsType struct {
	D2COption []*D2COptionType `xml:"D2COption,omitempty" json:"D2COption,omitempty"`
}

type D2COptionType struct {
	D2CCarrier string `xml:"D2CCarrier,omitempty" json:"D2CCarrier,omitempty"`

	CarrierAccountNumber string `xml:"CarrierAccountNumber,omitempty" json:"CarrierAccountNumber,omitempty"`

	ModelId string `xml:"ModelId,omitempty" json:"ModelId,omitempty"`
}

type ParameterType string

type EndUserInfo struct {
	CompanyName string `xml:"CompanyName,omitempty" json:"CompanyName,omitempty"`

	StreetAddress1 string `xml:"StreetAddress1,omitempty" json:"StreetAddress1,omitempty"`

	City string `xml:"City,omitempty" json:"City,omitempty"`

	State string `xml:"State,omitempty" json:"State,omitempty"`

	Zip5 string `xml:"Zip5,omitempty" json:"Zip5,omitempty"`
}

type NameValuePair struct {
	Name string `xml:"Name,omitempty" json:"Name,omitempty"`

	Value string `xml:"Value,omitempty" json:"Value,omitempty"`
}

type CustomerOrganization struct {
	InquiryIds struct {
		InquiryId []*NameValuePair `xml:"InquiryId,omitempty" json:"InquiryId,omitempty"`
	} `xml:"InquiryIds,omitempty" json:"InquiryIds,omitempty"`
}

type ClientIdentification struct {
	UserId string `xml:"UserId,omitempty" json:"UserId,omitempty"`

	TerminalId string `xml:"TerminalId,omitempty" json:"TerminalId,omitempty"`
}

type RequesterInformation struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 RequesterInformation"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`

	Name string `xml:"Name,omitempty" json:"Name,omitempty"`

	AccountNumber string `xml:"AccountNumber,omitempty" json:"AccountNumber,omitempty"`

	CarrierId string `xml:"CarrierId,omitempty" json:"CarrierId,omitempty"`

	CarrierAgencyAcctNo string `xml:"CarrierAgencyAcctNo,omitempty" json:"CarrierAgencyAcctNo,omitempty"`

	BillingCode string `xml:"BillingCode,omitempty" json:"BillingCode,omitempty"`

	LineOfBusiness *LineOfBusinessEnum `xml:"LineOfBusiness,omitempty" json:"LineOfBusiness,omitempty"`

	ReferenceCode string `xml:"ReferenceCode,omitempty" json:"ReferenceCode,omitempty"`

	EndUser *EndUserInfo `xml:"EndUser,omitempty" json:"EndUser,omitempty"`

	CustomerOrganization *CustomerOrganization `xml:"CustomerOrganization,omitempty" json:"CustomerOrganization,omitempty"`

	ClientIdentification *ClientIdentification `xml:"ClientIdentification,omitempty" json:"ClientIdentification,omitempty"`

	QueryId string `xml:"QueryId,omitempty" json:"QueryId,omitempty"`

	GLBPurpose string `xml:"GLBPurpose,omitempty" json:"GLBPurpose,omitempty"`

	DLPurpose string `xml:"DLPurpose,omitempty" json:"DLPurpose,omitempty"`
}

type SidexInfo struct {
	SourceNode string `xml:"SourceNode,omitempty" json:"SourceNode,omitempty"`

	DestinationNode string `xml:"DestinationNode,omitempty" json:"DestinationNode,omitempty"`

	ReturnNode string `xml:"ReturnNode,omitempty" json:"ReturnNode,omitempty"`
}

type PrecisionDateTime struct {
	Year int16 `xml:"Year,omitempty" json:"Year,omitempty"`

	Month int16 `xml:"Month,omitempty" json:"Month,omitempty"`

	Day int16 `xml:"Day,omitempty" json:"Day,omitempty"`

	Hour24 int16 `xml:"Hour24,omitempty" json:"Hour24,omitempty"`

	Minute int16 `xml:"Minute,omitempty" json:"Minute,omitempty"`

	Second int16 `xml:"Second,omitempty" json:"Second,omitempty"`

	MilliSeconds int16 `xml:"MilliSeconds,omitempty" json:"MilliSeconds,omitempty"`
}

type QBNameValuePair struct {
	Name string `xml:"Name,omitempty" json:"Name,omitempty"`

	Value string `xml:"Value,omitempty" json:"Value,omitempty"`
}

type TransactionDetails struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 TransactionDetails"`

	RuleplanId int32 `xml:"RuleplanId,omitempty" json:"RuleplanId,omitempty"`

	MaxWaitSeconds int32 `xml:"MaxWaitSeconds,omitempty" json:"MaxWaitSeconds,omitempty"`

	InquiryVersion string `xml:"InquiryVersion,omitempty" json:"InquiryVersion,omitempty"`

	SidexInfo *SidexInfo `xml:"SidexInfo,omitempty" json:"SidexInfo,omitempty"`

	DateTimeOrdered *PrecisionDateTime `xml:"DateTimeOrdered,omitempty" json:"DateTimeOrdered,omitempty"`

	DateTimeReceived *PrecisionDateTime `xml:"DateTimeReceived,omitempty" json:"DateTimeReceived,omitempty"`

	DateTimeCompleted *PrecisionDateTime `xml:"DateTimeCompleted,omitempty" json:"DateTimeCompleted,omitempty"`

	QuoteBacks struct {
		QuoteBack []*QBNameValuePair `xml:"QuoteBack,omitempty" json:"QuoteBack,omitempty"`
	} `xml:"QuoteBacks,omitempty" json:"QuoteBacks,omitempty"`
}

type ResultReturnFormat string

const (
	ResultReturnFormatPDF ResultReturnFormat = "PDF"

	ResultReturnFormatTIFF ResultReturnFormat = "TIFF"
)

type FsiTypeEnum string

type Name struct {
	Full string `xml:"Full,omitempty" json:"Full,omitempty"`

	First string `xml:"First,omitempty" json:"First,omitempty"`

	Middle string `xml:"Middle,omitempty" json:"Middle,omitempty"`

	Last string `xml:"Last,omitempty" json:"Last,omitempty"`

	Suffix string `xml:"Suffix,omitempty" json:"Suffix,omitempty"`

	Prefix string `xml:"Prefix,omitempty" json:"Prefix,omitempty"`
}

type DriversLicense struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 DriversLicense"`

	DriversLicenseNumber string `xml:"DriversLicenseNumber,omitempty" json:"DriversLicenseNumber,omitempty"`

	DriversLicenseState string `xml:"DriversLicenseState,omitempty" json:"DriversLicenseState,omitempty"`
}

type AddressType struct {
	// XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 Address"`

	StreetNumber string `xml:"StreetNumber,omitempty" json:"StreetNumber,omitempty"`

	StreetPreDirection string `xml:"StreetPreDirection,omitempty" json:"StreetPreDirection,omitempty"`

	StreetName string `xml:"StreetName,omitempty" json:"StreetName,omitempty"`

	StreetSuffix string `xml:"StreetSuffix,omitempty" json:"StreetSuffix,omitempty"`

	StreetPostDirection string `xml:"StreetPostDirection,omitempty" json:"StreetPostDirection,omitempty"`

	UnitDesignation string `xml:"UnitDesignation,omitempty" json:"UnitDesignation,omitempty"`

	UnitNumber string `xml:"UnitNumber,omitempty" json:"UnitNumber,omitempty"`

	StreetAddress1 string `xml:"StreetAddress1,omitempty" json:"StreetAddress1,omitempty"`

	StreetAddress2 string `xml:"StreetAddress2,omitempty" json:"StreetAddress2,omitempty"`

	City string `xml:"City,omitempty" json:"City,omitempty"`

	State string `xml:"State,omitempty" json:"State,omitempty"`

	Zip5 string `xml:"Zip5,omitempty" json:"Zip5,omitempty"`

	Zip4 string `xml:"Zip4,omitempty" json:"Zip4,omitempty"`

	County string `xml:"County,omitempty" json:"County,omitempty"`

	Country string `xml:"Country,omitempty" json:"Country,omitempty"`

	PostalCode string `xml:"PostalCode,omitempty" json:"PostalCode,omitempty"`

	StateCityZip string `xml:"StateCityZip,omitempty" json:"StateCityZip,omitempty"`

	YearsAtAddress int16 `xml:"YearsAtAddress,omitempty" json:"YearsAtAddress,omitempty"`

	MonthsAtAddress int16 `xml:"MonthsAtAddress,omitempty" json:"MonthsAtAddress,omitempty"`

	DateFirstAtAddress *PrecisionDateTime `xml:"DateFirstAtAddress,omitempty" json:"DateFirstAtAddress,omitempty"`

	DateLastAtAddress *PrecisionDateTime `xml:"DateLastAtAddress,omitempty" json:"DateLastAtAddress,omitempty"`

	AddressId *ID `xml:"addressId,attr,omitempty" json:"addressId,omitempty"`
}

type Subject struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 Subject"`

	QuoteBack string `xml:"QuoteBack,omitempty" json:"QuoteBack,omitempty"`

	Name *Name `xml:"Name,omitempty" json:"Name,omitempty"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`

	UniqueId string `xml:"UniqueId,omitempty" json:"UniqueId,omitempty"`

	SSN string `xml:"SSN,omitempty" json:"SSN,omitempty"`

	SSN4 string `xml:"SSN4,omitempty" json:"SSN4,omitempty"`

	LexId string `xml:"LexId,omitempty" json:"LexId,omitempty"`

	Age int32 `xml:"Age,omitempty" json:"Age,omitempty"`

	DOB *PrecisionDateTime `xml:"DOB,omitempty" json:"DOB,omitempty"`

	CurrentAddress *AddressType `xml:"CurrentAddress,omitempty" json:"CurrentAddress,omitempty"`

	DriversLicense *DriversLicense `xml:"DriversLicense,omitempty" json:"DriversLicense,omitempty"`

	SubjectID *ID `xml:"subjectID,attr,omitempty" json:"subjectID,omitempty"`
}

type Subjects struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 Subjects"`

	Subject []*Subject `xml:"Subject,omitempty" json:"Subject,omitempty"`
}

type SearchBy struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 SearchBy"`

	Subjects *Subjects `xml:"Subjects,omitempty" json:"Subjects,omitempty"`
}

type CommercialDriverAttractOptions struct {
	Model string `xml:"Model,omitempty" json:"Model,omitempty"`

	PolicyRatingState *StateEnum `xml:"PolicyRatingState,omitempty" json:"PolicyRatingState,omitempty"`

	CDAOptionId *ID `xml:"CDAOptionId,attr,omitempty" json:"CDAOptionId,omitempty"`
}

type NationalCreditFileOptions struct {
	ReportCode string `xml:"ReportCode,omitempty" json:"ReportCode,omitempty"`

	ReportType *NcfReportTypeEnum `xml:"ReportType,omitempty" json:"ReportType,omitempty"`

	ReportUsage *NcfUsage `xml:"ReportUsage,omitempty" json:"ReportUsage,omitempty"`

	Parameter []*ParameterType `xml:"Parameter,omitempty" json:"Parameter,omitempty"`

	CurrentCarrier *NcfBusinessEnum `xml:"CurrentCarrier,omitempty" json:"CurrentCarrier,omitempty"`

	AdditionalProduct *NcfAddonProductEnum `xml:"AdditionalProduct,omitempty" json:"AdditionalProduct,omitempty"`

	Vendor *NcfVendorEnum `xml:"Vendor,omitempty" json:"Vendor,omitempty"`

	BypassDuplicateOrderCheck *YesNo `xml:"BypassDuplicateOrderCheck,omitempty" json:"BypassDuplicateOrderCheck,omitempty"`

	ModelId []string `xml:"ModelId,omitempty" json:"ModelId,omitempty"`

	PolicyType *NcfPolicyTypeEnum `xml:"PolicyType,omitempty" json:"PolicyType,omitempty"`

	PolicyRatingState string `xml:"PolicyRatingState,omitempty" json:"PolicyRatingState,omitempty"`

	D2COptions *D2COptionsType `xml:"D2COptions,omitempty" json:"D2COptions,omitempty"`

	NcfOptionId *ID `xml:"ncfOptionId,attr,omitempty" json:"ncfOptionId,omitempty"`
}

type ProductOptions struct {
	CommercialDriverAttract struct {
		Options []*CommercialDriverAttractOptions `xml:"Options,omitempty" json:"Options,omitempty"`
	} `xml:"CommercialDriverAttract,omitempty" json:"CommercialDriverAttract,omitempty"`

	NationalCreditFileReport struct {
		Options []*NationalCreditFileOptions `xml:"Options,omitempty" json:"Options,omitempty"`
	} `xml:"NationalCreditFileReport,omitempty" json:"NationalCreditFileReport,omitempty"`
}

type IdentificationType struct {
	Ref   string `xml:"ref,attr,omitempty" json:"ref,omitempty"`
	Value string `xml:",chardata"`
}

type CommercialDriverAttractSearchBy struct {
	Subjects struct {
		Subject *IdentificationType `xml:"Subject,omitempty" json:"Subject,omitempty"`
	} `xml:"Subjects,omitempty" json:"Subjects,omitempty"`
}

type InquiryDriver struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 InquiryDriver"`

	SearchBy *CommercialDriverAttractSearchBy `xml:"SearchBy,omitempty" json:"SearchBy,omitempty"`

	OptionId *IdentificationType `xml:"OptionId,omitempty" json:"OptionId,omitempty"`
}

type CommercialDriverAttract struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 CommercialDriverAttract"`

	InquiryDriver []*InquiryDriver `xml:"InquiryDriver,omitempty" json:"InquiryDriver,omitempty"`
}

type ProductChoiceType struct {
	CommercialDriverAttract *CommercialDriverAttract `xml:"CommercialDriverAttract,omitempty" json:"CommercialDriverAttract,omitempty"`
	NationalCreditFile      *NationalCreditFile      `xml:"NationalCreditFile,omitempty" json:"NationalCreditFile,omitempty"`
}

type NationalCreditFile struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 NationalCreditFile"`

	InquiryNationalCreditFile []*InquiryNationalCreditFile `xml:"InquiryNationalCreditFile,omitempty" json:"InquiryNationalCreditFile,omitempty"`
}

type NcfSearchBy struct {
	Subjects struct {
		PrimarySubject *IdentificationType `xml:"PrimarySubject,omitempty" json:"PrimarySubject,omitempty"`

		JointSubject *IdentificationType `xml:"JointSubject,omitempty" json:"JointSubject,omitempty"`
	} `xml:"Subjects,omitempty" json:"Subjects,omitempty"`
}

type Request struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 Request"`

	RequesterInformation *RequesterInformation `xml:"RequesterInformation,omitempty" json:"RequesterInformation,omitempty"`

	TransactionDetails *TransactionDetails `xml:"TransactionDetails,omitempty" json:"TransactionDetails,omitempty"`

	ReturnFormat *ResultReturnFormat `xml:"ReturnFormat,omitempty" json:"ReturnFormat,omitempty"`

	SearchBy *SearchBy `xml:"SearchBy,omitempty" json:"SearchBy,omitempty"`

	ReportType string `xml:"ReportType,omitempty" json:"ReportType,omitempty"`

	ProductOptions *ProductOptions `xml:"ProductOptions,omitempty" json:"ProductOptions,omitempty"`

	Products *ProductChoiceType `xml:"Products,omitempty" json:"Products,omitempty"`
}

type HandleRequest struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsmultiproduct:wsinsmultiproduct handleRequest"`

	Request *Request `xml:"Request,omitempty" json:"Request,omitempty"`
}

type ScoreSubjectSource struct {
	Source *SubjectSourceTypeEnum `xml:"source,attr,omitempty" json:"source,omitempty"`

	Ref *IDREF `xml:"ref,attr,omitempty" json:"ref,omitempty"`
}

type ScoreSearchDataset struct {
	LexID []string `xml:"LexID,omitempty" json:"LexID,omitempty"`
}

type InsuranceScoreMessage struct {
	MessageType string `xml:"MessageType,omitempty" json:"MessageType,omitempty"`

	Code string `xml:"Code,omitempty" json:"Code,omitempty"`

	Description string `xml:"Description,omitempty" json:"Description,omitempty"`
}

type InsScoreAttributeGroup struct {
	DataSource string `xml:"DataSource,omitempty" json:"DataSource,omitempty"`

	Attributes struct {
		Attribute []*NameValuePair `xml:"Attribute,omitempty" json:"Attribute,omitempty"`
	} `xml:"Attributes,omitempty" json:"Attributes,omitempty"`

	EndDataSource string `xml:"EndDataSource,omitempty" json:"EndDataSource,omitempty"`
}

type InsuranceScoringModelResult struct {
	Id string `xml:"Id,omitempty" json:"Id,omitempty"`

	Name string `xml:"Name,omitempty" json:"Name,omitempty"`

	Description string `xml:"Description,omitempty" json:"Description,omitempty"`

	Score string `xml:"Score,omitempty" json:"Score,omitempty"`

	Messages struct {
		Message []*InsuranceScoreMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	Attributes struct {
		Attribute []*NameValuePair `xml:"Attribute,omitempty" json:"Attribute,omitempty"`
	} `xml:"Attributes,omitempty" json:"Attributes,omitempty"`

	AttributeGroups struct {
		AttributeGroup []*InsScoreAttributeGroup `xml:"AttributeGroup,omitempty" json:"AttributeGroup,omitempty"`
	} `xml:"AttributeGroups,omitempty" json:"AttributeGroups,omitempty"`

	ProcessingStatus string `xml:"ProcessingStatus,omitempty" json:"ProcessingStatus,omitempty"`
}

type InsuranceScoreResult struct {
	SubjectSource []*ScoreSubjectSource `xml:"SubjectSource,omitempty" json:"SubjectSource,omitempty"`

	Id string `xml:"Id,omitempty" json:"Id,omitempty"`

	OrderId string `xml:"OrderId,omitempty" json:"OrderId,omitempty"`

	ReferenceNumber string `xml:"ReferenceNumber,omitempty" json:"ReferenceNumber,omitempty"`

	SearchDataset *ScoreSearchDataset `xml:"SearchDataset,omitempty" json:"SearchDataset,omitempty"`

	Models struct {
		Model []*InsuranceScoringModelResult `xml:"Model,omitempty" json:"Model,omitempty"`
	} `xml:"Models,omitempty" json:"Models,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	TransactionDetailsEx *TransactionDetailsEx `xml:"TransactionDetailsEx,omitempty" json:"TransactionDetailsEx,omitempty"`
}

type FormattedReportInfo struct {
	Encoded bool `xml:"Encoded,omitempty" json:"Encoded,omitempty"`

	Format *ResultReturnFormat `xml:"Format,omitempty" json:"Format,omitempty"`

	Report string `xml:"Report,omitempty" json:"Report,omitempty"`
}

type InsuranceProductResults struct {
	ScoreResult struct {
		Result []*InsuranceScoreResult `xml:"Result,omitempty" json:"Result,omitempty"`

		TransactionDetailsEx *TransactionDetailsEx `xml:"TransactionDetailsEx,omitempty" json:"TransactionDetailsEx,omitempty"`

		FormattedReport []*FormattedReportInfo `xml:"FormattedReport,omitempty" json:"FormattedReport,omitempty"`
	} `xml:"ScoreResult,omitempty" json:"ScoreResult,omitempty"`

	NationalCreditFileResults struct {
		Result []*NationalCreditFileResult `xml:"Result,omitempty" json:"Result,omitempty"`

		TransactionDetailsEx *TransactionDetailsEx `xml:"TransactionDetailsEx,omitempty" json:"TransactionDetailsEx,omitempty"`

		RequesterInformation *RequesterInformation `xml:"RequesterInformation,omitempty" json:"RequesterInformation,omitempty"`
	} `xml:"NationalCreditFileResults,omitempty" json:"NationalCreditFileResults,omitempty"`
}

type NationalCreditFileResult struct {
	Id string `xml:"Id,omitempty" json:"Id,omitempty"`

	OrderId string `xml:"OrderId,omitempty" json:"OrderId,omitempty"`

	ReferenceNumber string `xml:"ReferenceNumber,omitempty" json:"ReferenceNumber,omitempty"`

	NcfReport *InsNcfReport `xml:"NcfReport,omitempty" json:"NcfReport,omitempty"`

	FormattedReport *FormattedReportInfo `xml:"FormattedReport,omitempty" json:"FormattedReport,omitempty"`
}

type InsNcfReport struct {
	NcfProductReport *InsNcfProductReport `xml:"NcfProductReport,omitempty" json:"NcfProductReport,omitempty"`

	Messages *InsNcfReportMessages `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNcfProductReport struct {
	// ReportIdentification *InsNCFReportIdentification `xml:"ReportIdentification,omitempty" json:"ReportIdentification,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	AlertsScoring *InsNCFAlertsScoring `xml:"AlertsScoring,omitempty" json:"AlertsScoring,omitempty"`

	SearchInfo *InsNCFSearchInfoSection `xml:"SearchInfo,omitempty" json:"SearchInfo,omitempty"`

	SubjectInfo *InsNCFSubjectInfoSection `xml:"SubjectInfo,omitempty" json:"SubjectInfo,omitempty"`

	CreditReportSummary *InsNCFCreditReportSummary `xml:"CreditReportSummary,omitempty" json:"CreditReportSummary,omitempty"`

	CreditReportSummaryEnhanced *InsNCFCreditReportSummaryEnhanced `xml:"CreditReportSummaryEnhanced,omitempty" json:"CreditReportSummaryEnhanced,omitempty"`

	EmploymentInfos struct {
		EmploymentInfo []*InsNCFOccupationRecord `xml:"EmploymentInfo,omitempty" json:"EmploymentInfo,omitempty"`

		OtherIncomeInfo []*InsNCFOtherIncomeRecord `xml:"OtherIncomeInfo,omitempty" json:"OtherIncomeInfo,omitempty"`
	} `xml:"EmploymentInfos,omitempty" json:"EmploymentInfos,omitempty"`

	PublicRecordsAndLegal *InsNCFPublicRecordsAndLegalItemsSection `xml:"PublicRecordsAndLegal,omitempty" json:"PublicRecordsAndLegal,omitempty"`

	CollectionRecordsInfo *InsNCFCollectionRecordsSection `xml:"CollectionRecordsInfo,omitempty" json:"CollectionRecordsInfo,omitempty"`

	TradeAccountInfo *InsNCFTradeAccountActivitySection `xml:"TradeAccountInfo,omitempty" json:"TradeAccountInfo,omitempty"`

	ConsumerNarrativeInfo *InsNCFConsumerNarrativeSection `xml:"ConsumerNarrativeInfo,omitempty" json:"ConsumerNarrativeInfo,omitempty"`

	InquiryHistoryHeader *InsNCFInquiryHistoryHeaderSection `xml:"InquiryHistoryHeader,omitempty" json:"InquiryHistoryHeader,omitempty"`

	AdditionalInformation *InsNCFAdditionalInformationSection `xml:"AdditionalInformation,omitempty" json:"AdditionalInformation,omitempty"`

	LexisNexisLienAndJudgment *InsNCFLexisNexisLienAndJudgmentSection `xml:"LexisNexisLienAndJudgment,omitempty" json:"LexisNexisLienAndJudgment,omitempty"`

	AttributeResult *InsAttributeResult `xml:"AttributeResult,omitempty" json:"AttributeResult,omitempty"`
}

type InsAttributeResult struct {
	Result struct {
		ModelId string `xml:"ModelId,omitempty" json:"ModelId,omitempty"`

		ModelLabel string `xml:"ModelLabel,omitempty" json:"ModelLabel,omitempty"`

		RatingState string `xml:"RatingState,omitempty" json:"RatingState,omitempty"`

		Score string `xml:"Score,omitempty" json:"Score,omitempty"`

		Messages struct {
			Message []*InsuranceScoreMessage `xml:"Message,omitempty" json:"Message,omitempty"`
		} `xml:"Messages,omitempty" json:"Messages,omitempty"`

		Attributes struct {
			Attribute []*NameValuePair `xml:"Attribute,omitempty" json:"Attribute,omitempty"`
		} `xml:"Attributes,omitempty" json:"Attributes,omitempty"`
	} `xml:"Result,omitempty" json:"Result,omitempty"`
}

type InsNCFReportIdentification struct {
	SpecialBillId string `xml:"SpecialBillId,omitempty" json:"SpecialBillId,omitempty"`

	ReportType string `xml:"ReportType,omitempty" json:"ReportType,omitempty"`

	ReportCode string `xml:"ReportCode,omitempty" json:"ReportCode,omitempty"`

	ReportUsage string `xml:"ReportUsage,omitempty" json:"ReportUsage,omitempty"`

	ReportSequence string `xml:"ReportSequence,omitempty" json:"ReportSequence,omitempty"`

	ReportCount string `xml:"ReportCount,omitempty" json:"ReportCount,omitempty"`

	ReportDescription string `xml:"ReportDescription,omitempty" json:"ReportDescription,omitempty"`

	ProcessingStatus string `xml:"ProcessingStatus,omitempty" json:"ProcessingStatus,omitempty"`

	ProductGroup string `xml:"ProductGroup,omitempty" json:"ProductGroup,omitempty"`

	Quoteback string `xml:"Quoteback,omitempty" json:"Quoteback,omitempty"`

	AccountNumber string `xml:"AccountNumber,omitempty" json:"AccountNumber,omitempty"`

	AccountSuffix string `xml:"AccountSuffix,omitempty" json:"AccountSuffix,omitempty"`

	DateOfOrder *PrecisionDateTime `xml:"DateOfOrder,omitempty" json:"DateOfOrder,omitempty"`

	DateOfReceipt *PrecisionDateTime `xml:"DateOfReceipt,omitempty" json:"DateOfReceipt,omitempty"`

	DateOfCompletion *PrecisionDateTime `xml:"DateOfCompletion,omitempty" json:"DateOfCompletion,omitempty"`

	ReferenceNumber string `xml:"ReferenceNumber,omitempty" json:"ReferenceNumber,omitempty"`

	TimeOfReport soap.XSDTime `xml:"TimeOfReport,omitempty" json:"TimeOfReport,omitempty"`

	CreditVendor string `xml:"CreditVendor,omitempty" json:"CreditVendor,omitempty"`

	CreditVendorAvailable bool `xml:"CreditVendorAvailable,omitempty" json:"CreditVendorAvailable,omitempty"`

	CreditVendorHit bool `xml:"CreditVendorHit,omitempty" json:"CreditVendorHit,omitempty"`

	AccountName string `xml:"AccountName,omitempty" json:"AccountName,omitempty"`

	LJSource *LJSourceIndicatorEnum `xml:"LJSource,omitempty" json:"LJSource,omitempty"`

	LJStatus *LJStatusEnum `xml:"LJStatus,omitempty" json:"LJStatus,omitempty"`

	EHStatus *EHStatusEnum `xml:"EHStatus,omitempty" json:"EHStatus,omitempty"`
}

type InsNCFAlertsScoring struct {
	Scores *InsNCFAlertsScores `xml:"Scores,omitempty" json:"Scores,omitempty"`

	Errors *InsNCFAlertsErrors `xml:"Errors,omitempty" json:"Errors,omitempty"`

	FACS *InsNCFFACS `xml:"FACS,omitempty" json:"FACS,omitempty"`

	General *InsNCFGeneral `xml:"General,omitempty" json:"General,omitempty"`

	Remarks *InsNCFRemarks `xml:"Remarks,omitempty" json:"Remarks,omitempty"`

	Messages *InsNCFMessages `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFAlertsScores struct {
	Score []*InsNCFAlertsScore `xml:"Score,omitempty" json:"Score,omitempty"`
}

type InsNCFAlertsScore struct {
	Score string `xml:"Score,omitempty" json:"Score,omitempty"`

	D2Carrier string `xml:"D2Carrier,omitempty" json:"D2Carrier,omitempty"`

	CarrierAccountNumber string `xml:"CarrierAccountNumber,omitempty" json:"CarrierAccountNumber,omitempty"`

	ModelId string `xml:"ModelId,omitempty" json:"ModelId,omitempty"`

	ModelLabel string `xml:"ModelLabel,omitempty" json:"ModelLabel,omitempty"`

	RatingState string `xml:"RatingState,omitempty" json:"RatingState,omitempty"`

	Classification *AlertsClassification `xml:"Classification,omitempty" json:"Classification,omitempty"`

	ExclusionCode string `xml:"ExclusionCode,omitempty" json:"ExclusionCode,omitempty"`

	ExclusionMessage string `xml:"ExclusionMessage,omitempty" json:"ExclusionMessage,omitempty"`

	ReasonCodes *InsNCFScoreReasonCodes `xml:"ReasonCodes,omitempty" json:"ReasonCodes,omitempty"`

	DuplicateMessage string `xml:"DuplicateMessage,omitempty" json:"DuplicateMessage,omitempty"`

	DuplicateReferenceNumber string `xml:"DuplicateReferenceNumber,omitempty" json:"DuplicateReferenceNumber,omitempty"`

	Description string `xml:"Description,omitempty" json:"Description,omitempty"`

	Status *ScoreStatusEnum `xml:"Status,omitempty" json:"Status,omitempty"`

	OriginalScoreDate string `xml:"OriginalScoreDate,omitempty" json:"OriginalScoreDate,omitempty"`
}

type InsNCFScoreReasonCodes struct {
	ReasonCode []*InsNCFScoreReasonCode `xml:"ReasonCode,omitempty" json:"ReasonCode,omitempty"`
}

type InsNCFScoreReasonCode struct {
	Code string `xml:"Code,omitempty" json:"Code,omitempty"`

	Description string `xml:"Description,omitempty" json:"Description,omitempty"`
}

type InsNCFAlertsErrors struct {
	Error []*InsNCFAlertsError `xml:"Error,omitempty" json:"Error,omitempty"`
}

type InsNCFAlertsError struct {
	D2Carrier string `xml:"D2Carrier,omitempty" json:"D2Carrier,omitempty"`

	CarrierAccountNumber string `xml:"CarrierAccountNumber,omitempty" json:"CarrierAccountNumber,omitempty"`

	ModelId string `xml:"ModelId,omitempty" json:"ModelId,omitempty"`

	ErrorText string `xml:"ErrorText,omitempty" json:"ErrorText,omitempty"`

	ErrorCode string `xml:"ErrorCode,omitempty" json:"ErrorCode,omitempty"`

	Vendor *CreditVendorsEnum `xml:"Vendor,omitempty" json:"Vendor,omitempty"`

	ExclusionCode *AlertsErrorEnum `xml:"ExclusionCode,omitempty" json:"ExclusionCode,omitempty"`
}

type InsNCFFACS struct {
	FACText []string `xml:"FACText,omitempty" json:"FACText,omitempty"`
}

type InsNCFGeneral struct {
	GeneralMessage []*GeneralMessage `xml:"GeneralMessage,omitempty" json:"GeneralMessage,omitempty"`
}

type InsNCFRemarks struct {
	Remark []string `xml:"Remark,omitempty" json:"Remark,omitempty"`
}

type InsNCFMessages struct {
	Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
}

type ResultSubject struct {
	Id string `xml:"Id,omitempty" json:"Id,omitempty"`

	UnitNumber string `xml:"UnitNumber,omitempty" json:"UnitNumber,omitempty"`

	QuoteBack string `xml:"QuoteBack,omitempty" json:"QuoteBack,omitempty"`

	Classification *SubjectTypeEnum `xml:"Classification,omitempty" json:"Classification,omitempty"`

	Name *Name `xml:"Name,omitempty" json:"Name,omitempty"`

	UniqueId string `xml:"UniqueId,omitempty" json:"UniqueId,omitempty"`

	SSN string `xml:"SSN,omitempty" json:"SSN,omitempty"`

	FsiSSN *FsiTypeEnum `xml:"FsiSSN,omitempty" json:"FsiSSN,omitempty"`

	Age int32 `xml:"Age,omitempty" json:"Age,omitempty"`

	Gender string `xml:"Gender,omitempty" json:"Gender,omitempty"`

	FsiGender *FsiTypeEnum `xml:"FsiGender,omitempty" json:"FsiGender,omitempty"`

	DOB *PrecisionDateTime `xml:"DOB,omitempty" json:"DOB,omitempty"`

	FsiDOB *FsiTypeEnum `xml:"FsiDOB,omitempty" json:"FsiDOB,omitempty"`

	HeightFeet string `xml:"HeightFeet,omitempty" json:"HeightFeet,omitempty"`

	HeightInches string `xml:"HeightInches,omitempty" json:"HeightInches,omitempty"`

	Weight string `xml:"Weight,omitempty" json:"Weight,omitempty"`

	Relationship *RelationshipType `xml:"Relationship,omitempty" json:"Relationship,omitempty"`

	RelationshipDescription string `xml:"RelationshipDescription,omitempty" json:"RelationshipDescription,omitempty"`

	IndividualAddressAssocIndicator string `xml:"IndividualAddressAssocIndicator,omitempty" json:"IndividualAddressAssocIndicator,omitempty"`

	RelationshipType *ClaimRelationshipTypeEnum `xml:"RelationshipType,omitempty" json:"RelationshipType,omitempty"`

	MaritalStatus *MaritalStatusTypeEnum `xml:"MaritalStatus,omitempty" json:"MaritalStatus,omitempty"`

	EyeColor string `xml:"EyeColor,omitempty" json:"EyeColor,omitempty"`

	HairColor string `xml:"HairColor,omitempty" json:"HairColor,omitempty"`

	Race string `xml:"Race,omitempty" json:"Race,omitempty"`

	GroupSequenceNumber string `xml:"GroupSequenceNumber,omitempty" json:"GroupSequenceNumber,omitempty"`

	FieldStatusIndicator *SubjectFieldStatusIndicator `xml:"FieldStatusIndicator,omitempty" json:"FieldStatusIndicator,omitempty"`

	Telephones struct {
		Telephone []*ResultTelephone `xml:"Telephone,omitempty" json:"Telephone,omitempty"`
	} `xml:"Telephones,omitempty" json:"Telephones,omitempty"`

	Mortgages struct {
		Mortgage []*ResultMortgage `xml:"Mortgage,omitempty" json:"Mortgage,omitempty"`
	} `xml:"Mortgages,omitempty" json:"Mortgages,omitempty"`

	DOD *PrecisionDateTime `xml:"DOD,omitempty" json:"DOD,omitempty"`

	NameAssociationIndicator *NameAssociationIndicatorTypeEnum `xml:"NameAssociationIndicator,omitempty" json:"NameAssociationIndicator,omitempty"`

	Address *ClaimAddressType `xml:"Address,omitempty" json:"Address,omitempty"`

	CurrentAddress *ResultAddress `xml:"CurrentAddress,omitempty" json:"CurrentAddress,omitempty"`

	MailingAddress *ResultAddress `xml:"MailingAddress,omitempty" json:"MailingAddress,omitempty"`

	FormerAddresses struct {
		FormerAddress []*ResultAddress `xml:"FormerAddress,omitempty" json:"FormerAddress,omitempty"`
	} `xml:"FormerAddresses,omitempty" json:"FormerAddresses,omitempty"`

	QualifiedResultAddresses struct {
		QualifiedResultAddress []*QualifiedResultAddress `xml:"QualifiedResultAddress,omitempty" json:"QualifiedResultAddress,omitempty"`
	} `xml:"QualifiedResultAddresses,omitempty" json:"QualifiedResultAddresses,omitempty"`

	DriversLicenses struct {
		DriversLicense []*DriversLicense `xml:"DriversLicense,omitempty" json:"DriversLicense,omitempty"`
	} `xml:"DriversLicenses,omitempty" json:"DriversLicenses,omitempty"`

	Policy *ResultPolicy `xml:"Policy,omitempty" json:"Policy,omitempty"`

	NumberOfDependents int32 `xml:"NumberOfDependents,omitempty" json:"NumberOfDependents,omitempty"`

	LexID string `xml:"LexID,omitempty" json:"LexID,omitempty"`

	Reference *ReferenceType `xml:"Reference,omitempty" json:"Reference,omitempty"`
}

type QualifiedResultAddress struct {
	QualifiedAddress *QualifiedAddress `xml:"QualifiedAddress,omitempty" json:"QualifiedAddress,omitempty"`

	FieldStatusIndicator *AddressFieldStatusIndicator `xml:"FieldStatusIndicator,omitempty" json:"FieldStatusIndicator,omitempty"`

	Classification *AddressTypeEnum `xml:"Classification,omitempty" json:"Classification,omitempty"`
}

type AddressFieldStatusIndicator struct {
	HouseNumber *FsiTypeEnum `xml:"HouseNumber,omitempty" json:"HouseNumber,omitempty"`

	StreetName *FsiTypeEnum `xml:"StreetName,omitempty" json:"StreetName,omitempty"`

	ApartmentNumber *FsiTypeEnum `xml:"ApartmentNumber,omitempty" json:"ApartmentNumber,omitempty"`

	City *FsiTypeEnum `xml:"City,omitempty" json:"City,omitempty"`

	State *FsiTypeEnum `xml:"State,omitempty" json:"State,omitempty"`

	Zip5 *FsiTypeEnum `xml:"Zip5,omitempty" json:"Zip5,omitempty"`

	Zip4 *FsiTypeEnum `xml:"Zip4,omitempty" json:"Zip4,omitempty"`

	YearsAtAddress *FsiTypeEnum `xml:"YearsAtAddress,omitempty" json:"YearsAtAddress,omitempty"`

	MonthsAtAddress *FsiTypeEnum `xml:"MonthsAtAddress,omitempty" json:"MonthsAtAddress,omitempty"`

	DateFirstAtAddress *FsiTypeEnum `xml:"DateFirstAtAddress,omitempty" json:"DateFirstAtAddress,omitempty"`

	DateLastAtAddress *FsiTypeEnum `xml:"DateLastAtAddress,omitempty" json:"DateLastAtAddress,omitempty"`
}

type QualifiedAddress struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 QualifiedAddress"`

	StreetNumber string `xml:"StreetNumber,omitempty" json:"StreetNumber,omitempty"`

	StreetPreDirection string `xml:"StreetPreDirection,omitempty" json:"StreetPreDirection,omitempty"`

	StreetName string `xml:"StreetName,omitempty" json:"StreetName,omitempty"`

	StreetSuffix string `xml:"StreetSuffix,omitempty" json:"StreetSuffix,omitempty"`

	StreetPostDirection string `xml:"StreetPostDirection,omitempty" json:"StreetPostDirection,omitempty"`

	UnitDesignation string `xml:"UnitDesignation,omitempty" json:"UnitDesignation,omitempty"`

	UnitNumber string `xml:"UnitNumber,omitempty" json:"UnitNumber,omitempty"`

	StreetAddress1 string `xml:"StreetAddress1,omitempty" json:"StreetAddress1,omitempty"`

	StreetAddress2 string `xml:"StreetAddress2,omitempty" json:"StreetAddress2,omitempty"`

	City string `xml:"City,omitempty" json:"City,omitempty"`

	State string `xml:"State,omitempty" json:"State,omitempty"`

	Zip5 string `xml:"Zip5,omitempty" json:"Zip5,omitempty"`

	Zip4 string `xml:"Zip4,omitempty" json:"Zip4,omitempty"`

	County string `xml:"County,omitempty" json:"County,omitempty"`

	Country string `xml:"Country,omitempty" json:"Country,omitempty"`

	PostalCode string `xml:"PostalCode,omitempty" json:"PostalCode,omitempty"`

	StateCityZip string `xml:"StateCityZip,omitempty" json:"StateCityZip,omitempty"`

	YearsAtAddress int16 `xml:"YearsAtAddress,omitempty" json:"YearsAtAddress,omitempty"`

	MonthsAtAddress int16 `xml:"MonthsAtAddress,omitempty" json:"MonthsAtAddress,omitempty"`

	DateFirstAtAddress *PrecisionDateTime `xml:"DateFirstAtAddress,omitempty" json:"DateFirstAtAddress,omitempty"`

	DateLastAtAddress *PrecisionDateTime `xml:"DateLastAtAddress,omitempty" json:"DateLastAtAddress,omitempty"`

	Classification *AddressTypeEnum `xml:"Classification,omitempty" json:"Classification,omitempty"`

	QualifiedAddrId *ID `xml:"qualifiedAddrId,attr,omitempty" json:"qualifiedAddrId,omitempty"`
}

type QualifiedAddresses struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 QualifiedAddresses"`

	QualifiedAddress []*QualifiedAddress `xml:"QualifiedAddress,omitempty" json:"QualifiedAddress,omitempty"`
}

type ResultPolicy struct {
	InsurancePolicy *InsurancePolicy `xml:"InsurancePolicy,omitempty" json:"InsurancePolicy,omitempty"`

	GroupSequenceNumber string `xml:"GroupSequenceNumber,omitempty" json:"GroupSequenceNumber,omitempty"`

	FieldStatusIndicator *PolicyFieldStatusIndicator `xml:"FieldStatusIndicator,omitempty" json:"FieldStatusIndicator,omitempty"`
}

type PolicyFieldStatusIndicator struct {
	PolicyNumber *FsiTypeEnum `xml:"PolicyNumber,omitempty" json:"PolicyNumber,omitempty"`

	PolicyType *FsiTypeEnum `xml:"PolicyType,omitempty" json:"PolicyType,omitempty"`

	Classification *FsiTypeEnum `xml:"Classification,omitempty" json:"Classification,omitempty"`

	Issuer *FsiTypeEnum `xml:"Issuer,omitempty" json:"Issuer,omitempty"`

	StartDate *FsiTypeEnum `xml:"StartDate,omitempty" json:"StartDate,omitempty"`

	EndDate *FsiTypeEnum `xml:"EndDate,omitempty" json:"EndDate,omitempty"`
}

type InsurancePolicy struct {
	QuoteBack string `xml:"QuoteBack,omitempty" json:"QuoteBack,omitempty"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`

	PolicyNumber string `xml:"PolicyNumber,omitempty" json:"PolicyNumber,omitempty"`

	PolicyType *PolicyTypeDefn `xml:"PolicyType,omitempty" json:"PolicyType,omitempty"`

	Issuer string `xml:"Issuer,omitempty" json:"Issuer,omitempty"`

	StartDate *PrecisionDateTime `xml:"StartDate,omitempty" json:"StartDate,omitempty"`

	EndDate *PrecisionDateTime `xml:"EndDate,omitempty" json:"EndDate,omitempty"`

	UnitNumber string `xml:"UnitNumber,omitempty" json:"UnitNumber,omitempty"`

	GroupSequenceNumber string `xml:"GroupSequenceNumber,omitempty" json:"GroupSequenceNumber,omitempty"`

	PolicyInceptionDate *PrecisionDateTime `xml:"PolicyInceptionDate,omitempty" json:"PolicyInceptionDate,omitempty"`

	PolicyHolderLapse string `xml:"PolicyHolderLapse,omitempty" json:"PolicyHolderLapse,omitempty"`

	PolicyLapseDate *PrecisionDateTime `xml:"PolicyLapseDate,omitempty" json:"PolicyLapseDate,omitempty"`

	LapseCounter *NonNegativeInteger `xml:"LapseCounter,omitempty" json:"LapseCounter,omitempty"`

	PolicyLastCancelDate *PrecisionDateTime `xml:"PolicyLastCancelDate,omitempty" json:"PolicyLastCancelDate,omitempty"`

	SupressSubjectIndicator string `xml:"SupressSubjectIndicator,omitempty" json:"SupressSubjectIndicator,omitempty"`

	PolicyHolders *PolicyHolders `xml:"PolicyHolders,omitempty" json:"PolicyHolders,omitempty"`

	PolicyStatus *PolicyStatusEnum `xml:"PolicyStatus,omitempty" json:"PolicyStatus,omitempty"`

	IssuerId string `xml:"IssuerId,omitempty" json:"IssuerId,omitempty"`

	InsuranceType string `xml:"InsuranceType,omitempty" json:"InsuranceType,omitempty"`

	NaicCode string `xml:"NaicCode,omitempty" json:"NaicCode,omitempty"`

	RiskType string `xml:"RiskType,omitempty" json:"RiskType,omitempty"`

	ConsumerStatementIndicator bool `xml:"ConsumerStatementIndicator,omitempty" json:"ConsumerStatementIndicator,omitempty"`

	ConsumerContributionIndicator bool `xml:"ConsumerContributionIndicator,omitempty" json:"ConsumerContributionIndicator,omitempty"`

	PolicyPremium string `xml:"PolicyPremium,omitempty" json:"PolicyPremium,omitempty"`

	TotalPolicyPremium string `xml:"TotalPolicyPremium,omitempty" json:"TotalPolicyPremium,omitempty"`

	PremiumPaymentPlan string `xml:"PremiumPaymentPlan,omitempty" json:"PremiumPaymentPlan,omitempty"`

	PremiumMethodOfPayment string `xml:"PremiumMethodOfPayment,omitempty" json:"PremiumMethodOfPayment,omitempty"`

	LienHolders *LienHolders `xml:"LienHolders,omitempty" json:"LienHolders,omitempty"`

	PolicyState string `xml:"PolicyState,omitempty" json:"PolicyState,omitempty"`

	PolicyCancellationReason string `xml:"PolicyCancellationReason,omitempty" json:"PolicyCancellationReason,omitempty"`

	PolicyId *ID `xml:"policyId,attr,omitempty" json:"policyId,omitempty"`
}

type PolicyHolders struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 PolicyHolders"`

	PolicyHolder []*PolicyHolder `xml:"PolicyHolder,omitempty" json:"PolicyHolder,omitempty"`
}

type PolicyHolder struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 PolicyHolder"`

	Subject *IdentificationType `xml:"Subject,omitempty" json:"Subject,omitempty"`

	Relationship *PolicyHolderRelationshipEnum `xml:"Relationship,omitempty" json:"Relationship,omitempty"`
}

type ResultAddress struct {
	Address *AddressType `xml:"Address,omitempty" json:"Address,omitempty"`

	GroupSequenceNumber string `xml:"GroupSequenceNumber,omitempty" json:"GroupSequenceNumber,omitempty"`

	FieldStatusIndicator *AddressFieldStatusIndicator `xml:"FieldStatusIndicator,omitempty" json:"FieldStatusIndicator,omitempty"`
}

type ClaimAddressType struct {
	ClaimAddress *AddressType `xml:"ClaimAddress,omitempty" json:"ClaimAddress,omitempty"`

	ClaimMatchIndicator *ClaimMatchIndicatorEnum `xml:"ClaimMatchIndicator,omitempty" json:"ClaimMatchIndicator,omitempty"`

	SearchMatchIndicator *SearchMatchIndicatorEnum `xml:"SearchMatchIndicator,omitempty" json:"SearchMatchIndicator,omitempty"`

	FsiAttributes *FsiAddressAttributes `xml:"FsiAttributes,omitempty" json:"FsiAttributes,omitempty"`

	History *HistoryTypeEnum `xml:"History,omitempty" json:"History,omitempty"`

	Type *ClaimAddressTypeEnum `xml:"Type,omitempty" json:"Type,omitempty"`
}

type FsiAddressAttributes struct {
	FsiHouse *FsiTypeEnum `xml:"FsiHouse,omitempty" json:"FsiHouse,omitempty"`

	FsiStreet1 *FsiTypeEnum `xml:"FsiStreet1,omitempty" json:"FsiStreet1,omitempty"`

	FsiStreet2 *FsiTypeEnum `xml:"FsiStreet2,omitempty" json:"FsiStreet2,omitempty"`

	FsiApartment *FsiTypeEnum `xml:"FsiApartment,omitempty" json:"FsiApartment,omitempty"`

	FsiUnit *FsiTypeEnum `xml:"FsiUnit,omitempty" json:"FsiUnit,omitempty"`

	FsiCity *FsiTypeEnum `xml:"FsiCity,omitempty" json:"FsiCity,omitempty"`

	FsiState *FsiTypeEnum `xml:"FsiState,omitempty" json:"FsiState,omitempty"`

	FsiPostalCode *FsiTypeEnum `xml:"FsiPostalCode,omitempty" json:"FsiPostalCode,omitempty"`

	FsiZip4 *FsiTypeEnum `xml:"FsiZip4,omitempty" json:"FsiZip4,omitempty"`

	FsiCounty *FsiTypeEnum `xml:"FsiCounty,omitempty" json:"FsiCounty,omitempty"`

	FsiCountry *FsiTypeEnum `xml:"FsiCountry,omitempty" json:"FsiCountry,omitempty"`

	FsiProvince *FsiTypeEnum `xml:"FsiProvince,omitempty" json:"FsiProvince,omitempty"`
}

type ResultMortgage struct {
	Number string `xml:"Number,omitempty" json:"Number,omitempty"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`

	Company string `xml:"Company,omitempty" json:"Company,omitempty"`

	Borrowers struct {
		Borrower []string `xml:"Borrower,omitempty" json:"Borrower,omitempty"`
	} `xml:"Borrowers,omitempty" json:"Borrowers,omitempty"`

	MortgageType string `xml:"MortgageType,omitempty" json:"MortgageType,omitempty"`

	LoanAmount string `xml:"LoanAmount,omitempty" json:"LoanAmount,omitempty"`

	LoanType string `xml:"LoanType,omitempty" json:"LoanType,omitempty"`

	InterestRate string `xml:"InterestRate,omitempty" json:"InterestRate,omitempty"`

	InterestRateType string `xml:"InterestRateType,omitempty" json:"InterestRateType,omitempty"`

	GroupSequenceNumber string `xml:"GroupSequenceNumber,omitempty" json:"GroupSequenceNumber,omitempty"`

	FieldStatusInd *MortgageFieldStatusIndicator `xml:"FieldStatusInd,omitempty" json:"FieldStatusInd,omitempty"`
}

type MortgageFieldStatusIndicator struct {
	MortgageLoadNumber *FsiTypeEnum `xml:"MortgageLoadNumber,omitempty" json:"MortgageLoadNumber,omitempty"`

	MortgageCompanyName *FsiTypeEnum `xml:"MortgageCompanyName,omitempty" json:"MortgageCompanyName,omitempty"`
}

type ResultTelephone struct {
	Classification *TelephoneTypeEnum `xml:"Classification,omitempty" json:"Classification,omitempty"`

	AreaCode string `xml:"AreaCode,omitempty" json:"AreaCode,omitempty"`

	Exchange string `xml:"Exchange,omitempty" json:"Exchange,omitempty"`

	Number string `xml:"Number,omitempty" json:"Number,omitempty"`

	Extension string `xml:"Extension,omitempty" json:"Extension,omitempty"`

	FsiAreaCode *FsiTypeEnum `xml:"FsiAreaCode,omitempty" json:"FsiAreaCode,omitempty"`

	FsiExchange *FsiTypeEnum `xml:"FsiExchange,omitempty" json:"FsiExchange,omitempty"`

	FsiNumber *FsiTypeEnum `xml:"FsiNumber,omitempty" json:"FsiNumber,omitempty"`

	GroupSequenceNumber string `xml:"GroupSequenceNumber,omitempty" json:"GroupSequenceNumber,omitempty"`

	FieldStatusIndicator *TelephoneFieldStatusIndicator `xml:"FieldStatusIndicator,omitempty" json:"FieldStatusIndicator,omitempty"`
}

type TelephoneFieldStatusIndicator struct {
	AreaCode *FsiTypeEnum `xml:"AreaCode,omitempty" json:"AreaCode,omitempty"`

	Exchange *FsiTypeEnum `xml:"Exchange,omitempty" json:"Exchange,omitempty"`

	Number *FsiTypeEnum `xml:"Number,omitempty" json:"Number,omitempty"`

	Extension *FsiTypeEnum `xml:"Extension,omitempty" json:"Extension,omitempty"`
}

type SubjectFieldStatusIndicator struct {
	Name *Name `xml:"Name,omitempty" json:"Name,omitempty"`

	DOB *FsiTypeEnum `xml:"DOB,omitempty" json:"DOB,omitempty"`

	Gender *FsiTypeEnum `xml:"Gender,omitempty" json:"Gender,omitempty"`

	SSN *FsiTypeEnum `xml:"SSN,omitempty" json:"SSN,omitempty"`

	HeightFeet *FsiTypeEnum `xml:"HeightFeet,omitempty" json:"HeightFeet,omitempty"`

	HeightInches *FsiTypeEnum `xml:"HeightInches,omitempty" json:"HeightInches,omitempty"`

	Weight *FsiTypeEnum `xml:"Weight,omitempty" json:"Weight,omitempty"`

	Relationship *FsiTypeEnum `xml:"Relationship,omitempty" json:"Relationship,omitempty"`

	MaritalStatus *FsiTypeEnum `xml:"MaritalStatus,omitempty" json:"MaritalStatus,omitempty"`

	EyeColor *FsiTypeEnum `xml:"EyeColor,omitempty" json:"EyeColor,omitempty"`

	HairColor *FsiTypeEnum `xml:"HairColor,omitempty" json:"HairColor,omitempty"`

	Age *FsiTypeEnum `xml:"Age,omitempty" json:"Age,omitempty"`
}

type InsNCFSearchInfoSection struct {
	Subject *ResultSubject `xml:"Subject,omitempty" json:"Subject,omitempty"`

	Spouse *ResultSubject `xml:"Spouse,omitempty" json:"Spouse,omitempty"`

	CurrentAddress *AddressType `xml:"CurrentAddress,omitempty" json:"CurrentAddress,omitempty"`

	FormerAddress *AddressType `xml:"FormerAddress,omitempty" json:"FormerAddress,omitempty"`
}

type InsNCFSubjectInfoSection struct {
	Subject *ResultSubject `xml:"Subject,omitempty" json:"Subject,omitempty"`

	Spouse *ResultSubject `xml:"Spouse,omitempty" json:"Spouse,omitempty"`

	Aliases struct {
		Alias []*ResultSubject `xml:"Alias,omitempty" json:"Alias,omitempty"`
	} `xml:"Aliases,omitempty" json:"Aliases,omitempty"`

	FormerIdentities struct {
		FormerIdentity []*ResultSubject `xml:"FormerIdentity,omitempty" json:"FormerIdentity,omitempty"`
	} `xml:"FormerIdentities,omitempty" json:"FormerIdentities,omitempty"`

	CurrentAddress *AddressType `xml:"CurrentAddress,omitempty" json:"CurrentAddress,omitempty"`

	FormerAddresses struct {
		FormerAddress []*AddressType `xml:"FormerAddress,omitempty" json:"FormerAddress,omitempty"`
	} `xml:"FormerAddresses,omitempty" json:"FormerAddresses,omitempty"`
}

type InsNCFCreditReportSummary struct {
	DateCreditFileEstbed *PrecisionDateTime `xml:"DateCreditFileEstbed,omitempty" json:"DateCreditFileEstbed,omitempty"`

	OldestOpeningDateOfTrade *PrecisionDateTime `xml:"OldestOpeningDateOfTrade,omitempty" json:"OldestOpeningDateOfTrade,omitempty"`

	LatestReportingDateOfTrade *PrecisionDateTime `xml:"LatestReportingDateOfTrade,omitempty" json:"LatestReportingDateOfTrade,omitempty"`

	DateOfLatestFileActivity *PrecisionDateTime `xml:"DateOfLatestFileActivity,omitempty" json:"DateOfLatestFileActivity,omitempty"`

	ReportIncludesBankruptcies bool `xml:"ReportIncludesBankruptcies,omitempty" json:"ReportIncludesBankruptcies,omitempty"`

	DateOfLatestBankruptcyv1 *PrecisionDateTime `xml:"DateOfLatestBankruptcyv1,omitempty" json:"DateOfLatestBankruptcyv1,omitempty"`

	ReportIncludesPublicRecords bool `xml:"ReportIncludesPublicRecords,omitempty" json:"ReportIncludesPublicRecords,omitempty"`

	ReportIncldsCollectionItems bool `xml:"ReportIncldsCollectionItems,omitempty" json:"ReportIncldsCollectionItems,omitempty"`

	RptIncludesConsumerStmnts bool `xml:"RptIncludesConsumerStmnts,omitempty" json:"RptIncludesConsumerStmnts,omitempty"`

	HighCreditRangeLowAmount int32 `xml:"HighCreditRangeLowAmount,omitempty" json:"HighCreditRangeLowAmount,omitempty"`

	HighCreditRangeHighAmount int32 `xml:"HighCreditRangeHighAmount,omitempty" json:"HighCreditRangeHighAmount,omitempty"`

	TotalNumberOfTradeLines int32 `xml:"TotalNumberOfTradeLines,omitempty" json:"TotalNumberOfTradeLines,omitempty"`

	CurrentStatusAccounts struct {
		CurrentStatusAccount []*InsStatusAccount `xml:"CurrentStatusAccount,omitempty" json:"CurrentStatusAccount,omitempty"`
	} `xml:"CurrentStatusAccounts,omitempty" json:"CurrentStatusAccounts,omitempty"`

	HistoryStatusAccounts struct {
		HistoryStatusAccount []*InsStatusAccount `xml:"HistoryStatusAccount,omitempty" json:"HistoryStatusAccount,omitempty"`
	} `xml:"HistoryStatusAccounts,omitempty" json:"HistoryStatusAccounts,omitempty"`

	HighCreditTotalRevolving int32 `xml:"HighCreditTotalRevolving,omitempty" json:"HighCreditTotalRevolving,omitempty"`

	HighCreditOpenended int32 `xml:"HighCreditOpenended,omitempty" json:"HighCreditOpenended,omitempty"`

	HighCreditInstallment int32 `xml:"HighCreditInstallment,omitempty" json:"HighCreditInstallment,omitempty"`

	AmountOwedTotalRevolving int32 `xml:"AmountOwedTotalRevolving,omitempty" json:"AmountOwedTotalRevolving,omitempty"`

	AmountOwedTotalOpenended int32 `xml:"AmountOwedTotalOpenended,omitempty" json:"AmountOwedTotalOpenended,omitempty"`

	AmountOwedTotalInstallment int32 `xml:"AmountOwedTotalInstallment,omitempty" json:"AmountOwedTotalInstallment,omitempty"`

	PastDueTotalRevolving int32 `xml:"PastDueTotalRevolving,omitempty" json:"PastDueTotalRevolving,omitempty"`

	PastDueTotalOpenended int32 `xml:"PastDueTotalOpenended,omitempty" json:"PastDueTotalOpenended,omitempty"`

	PastDueTotalInstallment int32 `xml:"PastDueTotalInstallment,omitempty" json:"PastDueTotalInstallment,omitempty"`

	NinetydayInquiryHistoryCount int32 `xml:"NinetydayInquiryHistoryCount,omitempty" json:"NinetydayInquiryHistoryCount,omitempty"`

	NinetydayInquiryHistoryDate *PrecisionDateTime `xml:"NinetydayInquiryHistoryDate,omitempty" json:"NinetydayInquiryHistoryDate,omitempty"`

	NumberOfRevolvingAccounts int32 `xml:"NumberOfRevolvingAccounts,omitempty" json:"NumberOfRevolvingAccounts,omitempty"`

	NumberOfOpenendedAccounts int32 `xml:"NumberOfOpenendedAccounts,omitempty" json:"NumberOfOpenendedAccounts,omitempty"`

	NumberOfInstallmentAccounts int32 `xml:"NumberOfInstallmentAccounts,omitempty" json:"NumberOfInstallmentAccounts,omitempty"`
}

type InsStatusAccount struct {
	Status string `xml:"Status,omitempty" json:"Status,omitempty"`

	NumberOfAccounts int32 `xml:"NumberOfAccounts,omitempty" json:"NumberOfAccounts,omitempty"`
}

type InsNCFCreditReportSummaryEnhanced struct {
	PresenceOfBankruptcy *YesNo `xml:"PresenceOfBankruptcy,omitempty" json:"PresenceOfBankruptcy,omitempty"`

	PresenceOfPublicRecord *YesNo `xml:"PresenceOfPublicRecord,omitempty" json:"PresenceOfPublicRecord,omitempty"`

	PresenceOfCollection *YesNo `xml:"PresenceOfCollection,omitempty" json:"PresenceOfCollection,omitempty"`

	PresenceOfMortgage *YesNo `xml:"PresenceOfMortgage,omitempty" json:"PresenceOfMortgage,omitempty"`

	MinimumCreditLimit int32 `xml:"MinimumCreditLimit,omitempty" json:"MinimumCreditLimit,omitempty"`

	MaximumCreditLimit int32 `xml:"MaximumCreditLimit,omitempty" json:"MaximumCreditLimit,omitempty"`

	MaximumCreditAmount int32 `xml:"MaximumCreditAmount,omitempty" json:"MaximumCreditAmount,omitempty"`

	LastDateReported *PrecisionDateTime `xml:"LastDateReported,omitempty" json:"LastDateReported,omitempty"`

	NumberOfInquiries int32 `xml:"NumberOfInquiries,omitempty" json:"NumberOfInquiries,omitempty"`

	MostRecentInquiryDate *PrecisionDateTime `xml:"MostRecentInquiryDate,omitempty" json:"MostRecentInquiryDate,omitempty"`

	CreditUtilization int32 `xml:"CreditUtilization,omitempty" json:"CreditUtilization,omitempty"`

	NumberOfOpenTrades int32 `xml:"NumberOfOpenTrades,omitempty" json:"NumberOfOpenTrades,omitempty"`

	TotalOpenTradeBalance int32 `xml:"TotalOpenTradeBalance,omitempty" json:"TotalOpenTradeBalance,omitempty"`

	MostRecentTradeDate *PrecisionDateTime `xml:"MostRecentTradeDate,omitempty" json:"MostRecentTradeDate,omitempty"`

	AgeOfOldestOpenTrade int32 `xml:"AgeOfOldestOpenTrade,omitempty" json:"AgeOfOldestOpenTrade,omitempty"`
}

type InsNCFOccupationRecord struct {
	PositionDesc string `xml:"PositionDesc,omitempty" json:"PositionDesc,omitempty"`

	EmployerName string `xml:"EmployerName,omitempty" json:"EmployerName,omitempty"`

	StateOfEmployment string `xml:"StateOfEmployment,omitempty" json:"StateOfEmployment,omitempty"`

	CityOfEmployment string `xml:"CityOfEmployment,omitempty" json:"CityOfEmployment,omitempty"`

	DateEmployed *PrecisionDateTime `xml:"DateEmployed,omitempty" json:"DateEmployed,omitempty"`

	DateEmploymentVerified *PrecisionDateTime `xml:"DateEmploymentVerified,omitempty" json:"DateEmploymentVerified,omitempty"`

	DateLeft *PrecisionDateTime `xml:"DateLeft,omitempty" json:"DateLeft,omitempty"`

	EmploymentIndirectlyVerified bool `xml:"EmploymentIndirectlyVerified,omitempty" json:"EmploymentIndirectlyVerified,omitempty"`

	MonthlySalary int32 `xml:"MonthlySalary,omitempty" json:"MonthlySalary,omitempty"`

	EmploymentRecordType string `xml:"EmploymentRecordType,omitempty" json:"EmploymentRecordType,omitempty"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`
}

type InsNCFOtherIncomeRecord struct {
	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	DateVerified *PrecisionDateTime `xml:"DateVerified,omitempty" json:"DateVerified,omitempty"`

	OtherIncomeAmount int32 `xml:"OtherIncomeAmount,omitempty" json:"OtherIncomeAmount,omitempty"`

	OtherIncomeSource string `xml:"OtherIncomeSource,omitempty" json:"OtherIncomeSource,omitempty"`

	InformationIndirectlyVerified bool `xml:"InformationIndirectlyVerified,omitempty" json:"InformationIndirectlyVerified,omitempty"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`
}

type InsNCFPublicRecordsAndLegalItemsSection struct {
	BankruptcyRecords struct {
		BankruptcyRecord []*InsNCFBankruptcyRecord `xml:"BankruptcyRecord,omitempty" json:"BankruptcyRecord,omitempty"`
	} `xml:"BankruptcyRecords,omitempty" json:"BankruptcyRecords,omitempty"`

	JudgmentRecords struct {
		JudgmentRecord []*InsNCFJudgmentRecord `xml:"JudgmentRecord,omitempty" json:"JudgmentRecord,omitempty"`
	} `xml:"JudgmentRecords,omitempty" json:"JudgmentRecords,omitempty"`

	GuaranteedLoanRecords struct {
		GuaranteedLoanRecord []*InsNCFGuaranteedLoanRecord `xml:"GuaranteedLoanRecord,omitempty" json:"GuaranteedLoanRecord,omitempty"`
	} `xml:"GuaranteedLoanRecords,omitempty" json:"GuaranteedLoanRecords,omitempty"`

	ForeclosureRecords struct {
		ForeclosureRecord []*InsNCFForeclosureRecord `xml:"ForeclosureRecord,omitempty" json:"ForeclosureRecord,omitempty"`
	} `xml:"ForeclosureRecords,omitempty" json:"ForeclosureRecords,omitempty"`

	NonResponsibilityRecords struct {
		NonResponsibilityRecord []*InsNCFNonResponsibilityRecord `xml:"NonResponsibilityRecord,omitempty" json:"NonResponsibilityRecord,omitempty"`
	} `xml:"NonResponsibilityRecords,omitempty" json:"NonResponsibilityRecords,omitempty"`

	TaxLienRecords struct {
		TaxLienRecord []*InsNCFTaxLienRecord `xml:"TaxLienRecord,omitempty" json:"TaxLienRecord,omitempty"`
	} `xml:"TaxLienRecords,omitempty" json:"TaxLienRecords,omitempty"`

	FinancialCounselorRecords struct {
		FinancialCounselorRecord []*InsNCFFinancialCounselorRecord `xml:"FinancialCounselorRecord,omitempty" json:"FinancialCounselorRecord,omitempty"`
	} `xml:"FinancialCounselorRecords,omitempty" json:"FinancialCounselorRecords,omitempty"`

	GarnishmentRecords struct {
		GarnishmentRecord []*InsNCFGarnishmentRecord `xml:"GarnishmentRecord,omitempty" json:"GarnishmentRecord,omitempty"`
	} `xml:"GarnishmentRecords,omitempty" json:"GarnishmentRecords,omitempty"`
}

type InsNCFBankruptcyRecord struct {
	DateFiled *PrecisionDateTime `xml:"DateFiled,omitempty" json:"DateFiled,omitempty"`

	BankruptcyStatus string `xml:"BankruptcyStatus,omitempty" json:"BankruptcyStatus,omitempty"`

	AssetsReported struct {
		Asset []string `xml:"Asset,omitempty" json:"Asset,omitempty"`
	} `xml:"AssetsReported,omitempty" json:"AssetsReported,omitempty"`

	LiabilitiesReported struct {
		Liability []string `xml:"Liability,omitempty" json:"Liability,omitempty"`
	} `xml:"LiabilitiesReported,omitempty" json:"LiabilitiesReported,omitempty"`

	SatisfieddischargeDate *PrecisionDateTime `xml:"SatisfieddischargeDate,omitempty" json:"SatisfieddischargeDate,omitempty"`

	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	CourtNumber string `xml:"CourtNumber,omitempty" json:"CourtNumber,omitempty"`

	CaseNumber string `xml:"CaseNumber,omitempty" json:"CaseNumber,omitempty"`

	BankruptcyType string `xml:"BankruptcyType,omitempty" json:"BankruptcyType,omitempty"`

	FilingType string `xml:"FilingType,omitempty" json:"FilingType,omitempty"`

	ExemptAmount int32 `xml:"ExemptAmount,omitempty" json:"ExemptAmount,omitempty"`

	ConsumerDisputeFlag string `xml:"ConsumerDisputeFlag,omitempty" json:"ConsumerDisputeFlag,omitempty"`

	BankruptcyStatusCode string `xml:"BankruptcyStatusCode,omitempty" json:"BankruptcyStatusCode,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFForeignBureauRecord struct {
	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	BureauCode string `xml:"BureauCode,omitempty" json:"BureauCode,omitempty"`

	City string `xml:"City,omitempty" json:"City,omitempty"`

	CityDesc string `xml:"CityDesc,omitempty" json:"CityDesc,omitempty"`

	State string `xml:"State,omitempty" json:"State,omitempty"`

	StateDesc string `xml:"StateDesc,omitempty" json:"StateDesc,omitempty"`
}

type InsNCFJudgmentRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateFiled *PrecisionDateTime `xml:"DateFiled,omitempty" json:"DateFiled,omitempty"`

	CourtNumber string `xml:"CourtNumber,omitempty" json:"CourtNumber,omitempty"`

	CaseNumber string `xml:"CaseNumber,omitempty" json:"CaseNumber,omitempty"`

	AmountInvolved int32 `xml:"AmountInvolved,omitempty" json:"AmountInvolved,omitempty"`

	TypeOfRecorditem string `xml:"TypeOfRecorditem,omitempty" json:"TypeOfRecorditem,omitempty"`

	DateSatisfied *PrecisionDateTime `xml:"DateSatisfied,omitempty" json:"DateSatisfied,omitempty"`

	StatusOfRecorditem string `xml:"StatusOfRecorditem,omitempty" json:"StatusOfRecorditem,omitempty"`

	DateVerified *PrecisionDateTime `xml:"DateVerified,omitempty" json:"DateVerified,omitempty"`

	Defendant string `xml:"Defendant,omitempty" json:"Defendant,omitempty"`

	Plaintiff string `xml:"Plaintiff,omitempty" json:"Plaintiff,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	State string `xml:"State,omitempty" json:"State,omitempty"`

	StatusDate *PrecisionDateTime `xml:"StatusDate,omitempty" json:"StatusDate,omitempty"`

	ConsumerDisputeFlag string `xml:"ConsumerDisputeFlag,omitempty" json:"ConsumerDisputeFlag,omitempty"`

	JudgmentType string `xml:"JudgmentType,omitempty" json:"JudgmentType,omitempty"`

	AmountInvolvedEnhanced string `xml:"AmountInvolvedEnhanced,omitempty" json:"AmountInvolvedEnhanced,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`
}

type InsNCFGuaranteedLoanRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateFiled *PrecisionDateTime `xml:"DateFiled,omitempty" json:"DateFiled,omitempty"`

	CourtNumber string `xml:"CourtNumber,omitempty" json:"CourtNumber,omitempty"`

	CaseNumber string `xml:"CaseNumber,omitempty" json:"CaseNumber,omitempty"`

	IndustryCode string `xml:"IndustryCode,omitempty" json:"IndustryCode,omitempty"`

	MaturityDate *PrecisionDateTime `xml:"MaturityDate,omitempty" json:"MaturityDate,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFForeclosureRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	DateChecked *PrecisionDateTime `xml:"DateChecked,omitempty" json:"DateChecked,omitempty"`

	ReportingMemberNumbername string `xml:"ReportingMemberNumbername,omitempty" json:"ReportingMemberNumbername,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFNonResponsibilityRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	TypeOfPersonFiling string `xml:"TypeOfPersonFiling,omitempty" json:"TypeOfPersonFiling,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFTaxLienRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateFiled *PrecisionDateTime `xml:"DateFiled,omitempty" json:"DateFiled,omitempty"`

	CourtNumber string `xml:"CourtNumber,omitempty" json:"CourtNumber,omitempty"`

	CaseNumber string `xml:"CaseNumber,omitempty" json:"CaseNumber,omitempty"`

	AmountOfLien int32 `xml:"AmountOfLien,omitempty" json:"AmountOfLien,omitempty"`

	AcbCreditorClass string `xml:"AcbCreditorClass,omitempty" json:"AcbCreditorClass,omitempty"`

	DateReleased *PrecisionDateTime `xml:"DateReleased,omitempty" json:"DateReleased,omitempty"`

	DateVerified *PrecisionDateTime `xml:"DateVerified,omitempty" json:"DateVerified,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	ConsumerDisputeFlag string `xml:"ConsumerDisputeFlag,omitempty" json:"ConsumerDisputeFlag,omitempty"`

	AmountOfLienEnhanced string `xml:"AmountOfLienEnhanced,omitempty" json:"AmountOfLienEnhanced,omitempty"`

	State string `xml:"State,omitempty" json:"State,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFFinancialCounselorRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	ReportingMemberNumber string `xml:"ReportingMemberNumber,omitempty" json:"ReportingMemberNumber,omitempty"`

	AmountInvolved int32 `xml:"AmountInvolved,omitempty" json:"AmountInvolved,omitempty"`

	DateChecked *PrecisionDateTime `xml:"DateChecked,omitempty" json:"DateChecked,omitempty"`

	DateSettled *PrecisionDateTime `xml:"DateSettled,omitempty" json:"DateSettled,omitempty"`

	CounselingStatusCode string `xml:"CounselingStatusCode,omitempty" json:"CounselingStatusCode,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFGarnishmentRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	DateSatisfied *PrecisionDateTime `xml:"DateSatisfied,omitempty" json:"DateSatisfied,omitempty"`

	DateChecked *PrecisionDateTime `xml:"DateChecked,omitempty" json:"DateChecked,omitempty"`

	CourtNumber string `xml:"CourtNumber,omitempty" json:"CourtNumber,omitempty"`

	CaseNumber string `xml:"CaseNumber,omitempty" json:"CaseNumber,omitempty"`

	AmountOfGarnishment int32 `xml:"AmountOfGarnishment,omitempty" json:"AmountOfGarnishment,omitempty"`

	Plaintiff string `xml:"Plaintiff,omitempty" json:"Plaintiff,omitempty"`

	Garnishee string `xml:"Garnishee,omitempty" json:"Garnishee,omitempty"`

	Defendant string `xml:"Defendant,omitempty" json:"Defendant,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`
}

type InsNCFCollectionRecordsSection struct {
	CollectionRecords struct {
		CollectionRecord []*InsNCFCollectionRecord `xml:"CollectionRecord,omitempty" json:"CollectionRecord,omitempty"`
	} `xml:"CollectionRecords,omitempty" json:"CollectionRecords,omitempty"`
}

type InsNCFCollectionRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	DateAssigned *PrecisionDateTime `xml:"DateAssigned,omitempty" json:"DateAssigned,omitempty"`

	ReportingMemberAgencyNumber string `xml:"ReportingMemberAgencyNumber,omitempty" json:"ReportingMemberAgencyNumber,omitempty"`

	ClientNameOrNumber string `xml:"ClientNameOrNumber,omitempty" json:"ClientNameOrNumber,omitempty"`

	AccountSerialNumber string `xml:"AccountSerialNumber,omitempty" json:"AccountSerialNumber,omitempty"`

	EcoaCode string `xml:"EcoaCode,omitempty" json:"EcoaCode,omitempty"`

	DateOfLastActivity *PrecisionDateTime `xml:"DateOfLastActivity,omitempty" json:"DateOfLastActivity,omitempty"`

	OriginalAmount int32 `xml:"OriginalAmount,omitempty" json:"OriginalAmount,omitempty"`

	DateOfBalance *PrecisionDateTime `xml:"DateOfBalance,omitempty" json:"DateOfBalance,omitempty"`

	BalanceAmount int32 `xml:"BalanceAmount,omitempty" json:"BalanceAmount,omitempty"`

	StatusDate *PrecisionDateTime `xml:"StatusDate,omitempty" json:"StatusDate,omitempty"`

	CollectionItemStatus string `xml:"CollectionItemStatus,omitempty" json:"CollectionItemStatus,omitempty"`

	CollectionItemStatusText string `xml:"CollectionItemStatusText,omitempty" json:"CollectionItemStatusText,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	KOB string `xml:"KOB,omitempty" json:"KOB,omitempty"`

	MemberName string `xml:"MemberName,omitempty" json:"MemberName,omitempty"`

	PastDueAmount int32 `xml:"PastDueAmount,omitempty" json:"PastDueAmount,omitempty"`

	OriginalCreditorClassificationCode string `xml:"OriginalCreditorClassificationCode,omitempty" json:"OriginalCreditorClassificationCode,omitempty"`

	ConsumerDisputeFlag string `xml:"ConsumerDisputeFlag,omitempty" json:"ConsumerDisputeFlag,omitempty"`

	AccountPurposeType string `xml:"AccountPurposeType,omitempty" json:"AccountPurposeType,omitempty"`

	ConsumerInformationIndicator string `xml:"ConsumerInformationIndicator,omitempty" json:"ConsumerInformationIndicator,omitempty"`

	LastPaymentDate *PrecisionDateTime `xml:"LastPaymentDate,omitempty" json:"LastPaymentDate,omitempty"`

	FirstDeliquentDate *PrecisionDateTime `xml:"FirstDeliquentDate,omitempty" json:"FirstDeliquentDate,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFTradeAccountActivitySection struct {
	CreditTradeHistoryRecords struct {
		CreditTradeHistoryRecord []*InsNCFCreditTradeHistoryRecord `xml:"CreditTradeHistoryRecord,omitempty" json:"CreditTradeHistoryRecord,omitempty"`
	} `xml:"CreditTradeHistoryRecords,omitempty" json:"CreditTradeHistoryRecords,omitempty"`

	NonMemberRecords struct {
		NonMemberRecord []*InsNCFNonMemberRecord `xml:"NonMemberRecord,omitempty" json:"NonMemberRecord,omitempty"`
	} `xml:"NonMemberRecords,omitempty" json:"NonMemberRecords,omitempty"`

	CheckingSavingsAccountRecords struct {
		CheckingSavingsAccountRecord []*InsNCFCheckingSavingsAccountRecord `xml:"CheckingSavingsAccountRecord,omitempty" json:"CheckingSavingsAccountRecord,omitempty"`
	} `xml:"CheckingSavingsAccountRecords,omitempty" json:"CheckingSavingsAccountRecords,omitempty"`
}

type InsNCFCreditTradeHistoryRecord struct {
	ReportingMemberNumber string `xml:"ReportingMemberNumber,omitempty" json:"ReportingMemberNumber,omitempty"`

	MemberName string `xml:"MemberName,omitempty" json:"MemberName,omitempty"`

	TapeSupplierIndicator string `xml:"TapeSupplierIndicator,omitempty" json:"TapeSupplierIndicator,omitempty"`

	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	DateAccountOpened *PrecisionDateTime `xml:"DateAccountOpened,omitempty" json:"DateAccountOpened,omitempty"`

	HighestCreditAmount int32 `xml:"HighestCreditAmount,omitempty" json:"HighestCreditAmount,omitempty"`

	Terms string `xml:"Terms,omitempty" json:"Terms,omitempty"`

	AccountBalance int32 `xml:"AccountBalance,omitempty" json:"AccountBalance,omitempty"`

	PastDueAmount int32 `xml:"PastDueAmount,omitempty" json:"PastDueAmount,omitempty"`

	AccountTypeCode string `xml:"AccountTypeCode,omitempty" json:"AccountTypeCode,omitempty"`

	CurrentRateCode string `xml:"CurrentRateCode,omitempty" json:"CurrentRateCode,omitempty"`

	MonthsReviewed string `xml:"MonthsReviewed,omitempty" json:"MonthsReviewed,omitempty"`

	AccountDesignatorCode string `xml:"AccountDesignatorCode,omitempty" json:"AccountDesignatorCode,omitempty"`

	AccountNumber string `xml:"AccountNumber,omitempty" json:"AccountNumber,omitempty"`

	ThirtydayCounter string `xml:"ThirtydayCounter,omitempty" json:"ThirtydayCounter,omitempty"`

	SixtydayCounter string `xml:"SixtydayCounter,omitempty" json:"SixtydayCounter,omitempty"`

	NinetydayCounter string `xml:"NinetydayCounter,omitempty" json:"NinetydayCounter,omitempty"`

	PrevRateCode1 string `xml:"PrevRateCode1,omitempty" json:"PrevRateCode1,omitempty"`

	PrevRateDate1 *PrecisionDateTime `xml:"PrevRateDate1,omitempty" json:"PrevRateDate1,omitempty"`

	PrevRateCode2 string `xml:"PrevRateCode2,omitempty" json:"PrevRateCode2,omitempty"`

	PrevRateDate2 *PrecisionDateTime `xml:"PrevRateDate2,omitempty" json:"PrevRateDate2,omitempty"`

	PrevRateCode3 string `xml:"PrevRateCode3,omitempty" json:"PrevRateCode3,omitempty"`

	PrevRateDate3 *PrecisionDateTime `xml:"PrevRateDate3,omitempty" json:"PrevRateDate3,omitempty"`

	DateOfLastActivity *PrecisionDateTime `xml:"DateOfLastActivity,omitempty" json:"DateOfLastActivity,omitempty"`

	ReservedForInternalScoringUse string `xml:"ReservedForInternalScoringUse,omitempty" json:"ReservedForInternalScoringUse,omitempty"`

	PaymentHistory24Month string `xml:"PaymentHistory24Month,omitempty" json:"PaymentHistory24Month,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	EnhancedHistory *InsNCFCreditTradeHistoryRecordEnhanced `xml:"EnhancedHistory,omitempty" json:"EnhancedHistory,omitempty"`

	PaymentPattern *InsNCFCreditTradeHistoryPaymentPattern `xml:"PaymentPattern,omitempty" json:"PaymentPattern,omitempty"`

	DataTrends *InsNCFCreditTradeHistoryRecordDataTrends `xml:"DataTrends,omitempty" json:"DataTrends,omitempty"`
}

type InsNCFCreditTradeHistoryRecordEnhanced struct {
	KOB string `xml:"KOB,omitempty" json:"KOB,omitempty"`

	AccountPurposeType string `xml:"AccountPurposeType,omitempty" json:"AccountPurposeType,omitempty"`

	CreditLimit string `xml:"CreditLimit,omitempty" json:"CreditLimit,omitempty"`

	ScheduledPaymentAmount int32 `xml:"ScheduledPaymentAmount,omitempty" json:"ScheduledPaymentAmount,omitempty"`

	MonthlyPaymentType string `xml:"MonthlyPaymentType,omitempty" json:"MonthlyPaymentType,omitempty"`

	ActualPaymentAmount int32 `xml:"ActualPaymentAmount,omitempty" json:"ActualPaymentAmount,omitempty"`

	DerogCounter int32 `xml:"DerogCounter,omitempty" json:"DerogCounter,omitempty"`

	OldHistoricalWorstRatingCode string `xml:"OldHistoricalWorstRatingCode,omitempty" json:"OldHistoricalWorstRatingCode,omitempty"`

	OldHistoricalWorstRatingDate *PrecisionDateTime `xml:"OldHistoricalWorstRatingDate,omitempty" json:"OldHistoricalWorstRatingDate,omitempty"`

	StatusDate *PrecisionDateTime `xml:"StatusDate,omitempty" json:"StatusDate,omitempty"`

	LastPaymentDate *PrecisionDateTime `xml:"LastPaymentDate,omitempty" json:"LastPaymentDate,omitempty"`

	ConsumerDisputeFlag string `xml:"ConsumerDisputeFlag,omitempty" json:"ConsumerDisputeFlag,omitempty"`

	PaymentFrequency string `xml:"PaymentFrequency,omitempty" json:"PaymentFrequency,omitempty"`

	ActivityDesignatorCode string `xml:"ActivityDesignatorCode,omitempty" json:"ActivityDesignatorCode,omitempty"`

	ClosedDate *PrecisionDateTime `xml:"ClosedDate,omitempty" json:"ClosedDate,omitempty"`

	ChargeOffAmount int64 `xml:"ChargeOffAmount,omitempty" json:"ChargeOffAmount,omitempty"`

	StatusCode string `xml:"StatusCode,omitempty" json:"StatusCode,omitempty"`

	AccountConditionCode string `xml:"AccountConditionCode,omitempty" json:"AccountConditionCode,omitempty"`

	ConsumerInformationIndicator string `xml:"ConsumerInformationIndicator,omitempty" json:"ConsumerInformationIndicator,omitempty"`

	MortgageID string `xml:"MortgageID,omitempty" json:"MortgageID,omitempty"`

	DeferredPaymentStartDate *PrecisionDateTime `xml:"DeferredPaymentStartDate,omitempty" json:"DeferredPaymentStartDate,omitempty"`

	DeferredPaymentAmount int32 `xml:"DeferredPaymentAmount,omitempty" json:"DeferredPaymentAmount,omitempty"`

	BalloonPaymentAmount int32 `xml:"BalloonPaymentAmount,omitempty" json:"BalloonPaymentAmount,omitempty"`

	BalloonPaymentDueDate *PrecisionDateTime `xml:"BalloonPaymentDueDate,omitempty" json:"BalloonPaymentDueDate,omitempty"`

	CurrentRateCodeEnhanced string `xml:"CurrentRateCodeEnhanced,omitempty" json:"CurrentRateCodeEnhanced,omitempty"`

	LastActivityDateOrFirstDeliquentDate *PrecisionDateTime `xml:"LastActivityDateOrFirstDeliquentDate,omitempty" json:"LastActivityDateOrFirstDeliquentDate,omitempty"`

	MaximumDelinquencyCode string `xml:"MaximumDelinquencyCode,omitempty" json:"MaximumDelinquencyCode,omitempty"`

	MaximumDelinquencyDate *PrecisionDateTime `xml:"MaximumDelinquencyDate,omitempty" json:"MaximumDelinquencyDate,omitempty"`
}

type InsNCFCreditTradeHistoryPaymentPattern struct {
	PaymentPatternStartDate *PrecisionDateTime `xml:"PaymentPatternStartDate,omitempty" json:"PaymentPatternStartDate,omitempty"`

	FortyEightMonthPaymentHistory string `xml:"FortyEightMonthPaymentHistory,omitempty" json:"FortyEightMonthPaymentHistory,omitempty"`

	AdditionalPaymentHistory string `xml:"AdditionalPaymentHistory,omitempty" json:"AdditionalPaymentHistory,omitempty"`
}

type InsNCFCreditTradeHistoryRecordDataTrends struct {
	TrendedData []*InsNCFEnhTrendedData `xml:"TrendedData,omitempty" json:"TrendedData,omitempty"`
}

type InsNCFEnhTrendedData struct {
	Date string `xml:"Date,omitempty" json:"Date,omitempty"`

	BalanceAmount int32 `xml:"BalanceAmount,omitempty" json:"BalanceAmount,omitempty"`

	LoanOrCreditAmount int32 `xml:"LoanOrCreditAmount,omitempty" json:"LoanOrCreditAmount,omitempty"`

	ScheduledPaymentAmount int32 `xml:"ScheduledPaymentAmount,omitempty" json:"ScheduledPaymentAmount,omitempty"`

	ActualPaymentAmount int32 `xml:"ActualPaymentAmount,omitempty" json:"ActualPaymentAmount,omitempty"`

	LastPaymentDate *PrecisionDateTime `xml:"LastPaymentDate,omitempty" json:"LastPaymentDate,omitempty"`
}

type InsNCFNonMemberRecord struct {
	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	AccountTypeCode string `xml:"AccountTypeCode,omitempty" json:"AccountTypeCode,omitempty"`

	CurrentRateCode string `xml:"CurrentRateCode,omitempty" json:"CurrentRateCode,omitempty"`

	DateAccountOpened *PrecisionDateTime `xml:"DateAccountOpened,omitempty" json:"DateAccountOpened,omitempty"`

	CustomerNarrative string `xml:"CustomerNarrative,omitempty" json:"CustomerNarrative,omitempty"`

	HighestCreditAmount int32 `xml:"HighestCreditAmount,omitempty" json:"HighestCreditAmount,omitempty"`

	AccountBalance int32 `xml:"AccountBalance,omitempty" json:"AccountBalance,omitempty"`

	PastDueAmount int32 `xml:"PastDueAmount,omitempty" json:"PastDueAmount,omitempty"`

	RateLessThanZero string `xml:"RateLessThanZero,omitempty" json:"RateLessThanZero,omitempty"`

	Message *InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
}

type InsNCFCheckingSavingsAccountRecord struct {
	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	DateReported *PrecisionDateTime `xml:"DateReported,omitempty" json:"DateReported,omitempty"`

	AccountType string `xml:"AccountType,omitempty" json:"AccountType,omitempty"`

	ReportingMemberNumber string `xml:"ReportingMemberNumber,omitempty" json:"ReportingMemberNumber,omitempty"`

	Amount int32 `xml:"Amount,omitempty" json:"Amount,omitempty"`

	DateOpened *PrecisionDateTime `xml:"DateOpened,omitempty" json:"DateOpened,omitempty"`

	ReasonCode string `xml:"ReasonCode,omitempty" json:"ReasonCode,omitempty"`

	ForeignRecord *InsNCFForeignBureauRecord `xml:"ForeignRecord,omitempty" json:"ForeignRecord,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFConsumerNarrativeSection struct {
	ConsumerNarrativeRecords struct {
		ConsumerNarrativeRecord []*InsNCFConsumerNarrativeRecord `xml:"ConsumerNarrativeRecord,omitempty" json:"ConsumerNarrativeRecord,omitempty"`
	} `xml:"ConsumerNarrativeRecords,omitempty" json:"ConsumerNarrativeRecords,omitempty"`
}

type InsNCFConsumerNarrativeRecord struct {
	DateStatementFiled *PrecisionDateTime `xml:"DateStatementFiled,omitempty" json:"DateStatementFiled,omitempty"`

	DateStatementPurged *PrecisionDateTime `xml:"DateStatementPurged,omitempty" json:"DateStatementPurged,omitempty"`

	NameOfIndFiling string `xml:"NameOfIndFiling,omitempty" json:"NameOfIndFiling,omitempty"`

	RelationshipToClaimant string `xml:"RelationshipToClaimant,omitempty" json:"RelationshipToClaimant,omitempty"`

	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`
}

type InsNCFInquiryHistoryHeaderSection struct {
	InquiryHistoryHeaderRecords struct {
		InquiryHistoryHeaderRecord []*InsNCFInquiryHistoryHeaderRecord `xml:"InquiryHistoryHeaderRecord,omitempty" json:"InquiryHistoryHeaderRecord,omitempty"`
	} `xml:"InquiryHistoryHeaderRecords,omitempty" json:"InquiryHistoryHeaderRecords,omitempty"`
}

type InsNCFInquiryHistoryHeaderRecord struct {
	DateOfInquiry *PrecisionDateTime `xml:"DateOfInquiry,omitempty" json:"DateOfInquiry,omitempty"`

	InquirerName string `xml:"InquirerName,omitempty" json:"InquirerName,omitempty"`

	InquirerId string `xml:"InquirerId,omitempty" json:"InquirerId,omitempty"`

	InformationSource string `xml:"InformationSource,omitempty" json:"InformationSource,omitempty"`

	KOB string `xml:"KOB,omitempty" json:"KOB,omitempty"`

	Amount int32 `xml:"Amount,omitempty" json:"Amount,omitempty"`

	Type string `xml:"Type,omitempty" json:"Type,omitempty"`

	Abbreviation string `xml:"Abbreviation,omitempty" json:"Abbreviation,omitempty"`

	Terms string `xml:"Terms,omitempty" json:"Terms,omitempty"`
}

type InsNCFAdditionalInformationSection struct {
	AdditionalInfo struct {
		Info []*InsuranceReportMessage `xml:"Info,omitempty" json:"Info,omitempty"`
	} `xml:"AdditionalInfo,omitempty" json:"AdditionalInfo,omitempty"`

	CurrentCarrierReport *CurrentCarrierReport `xml:"CurrentCarrierReport,omitempty" json:"CurrentCarrierReport,omitempty"`
}

type InsNCFLexisNexisLienAndJudgmentSection struct {
	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	JudgmentRecords struct {
		JudgmentRecord []*InsNCFJudgmentRecord `xml:"JudgmentRecord,omitempty" json:"JudgmentRecord,omitempty"`
	} `xml:"JudgmentRecords,omitempty" json:"JudgmentRecords,omitempty"`

	TaxLienRecords struct {
		TaxLienRecord []*InsNCFTaxLienRecord `xml:"TaxLienRecord,omitempty" json:"TaxLienRecord,omitempty"`
	} `xml:"TaxLienRecords,omitempty" json:"TaxLienRecords,omitempty"`
}

type InsNcfReportMessages struct {
	General struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"General,omitempty" json:"General,omitempty"`

	Search struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Search,omitempty" json:"Search,omitempty"`

	Result struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Result,omitempty" json:"Result,omitempty"`

	Developed struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Developed,omitempty" json:"Developed,omitempty"`

	Supplementary struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Supplementary,omitempty" json:"Supplementary,omitempty"`
}

type ReferenceTypeEnum string

const (
	ReferenceTypeEnumSubject ReferenceTypeEnum = "Subject"

	ReferenceTypeEnumVehicle ReferenceTypeEnum = "Vehicle"

	ReferenceTypeEnumBusiness ReferenceTypeEnum = "Business"

	ReferenceTypeEnumAddress ReferenceTypeEnum = "Address"
)

type ReferenceType struct {
	Id *IDREF `xml:"Id,omitempty" json:"Id,omitempty"`

	Type *ReferenceTypeEnum `xml:"Type,omitempty" json:"Type,omitempty"`
}

type InsuranceReportMessage struct {
	Classification string `xml:"Classification,omitempty" json:"Classification,omitempty"`

	Type string `xml:"Type,omitempty" json:"Type,omitempty"`

	Code string `xml:"Code,omitempty" json:"Code,omitempty"`

	Message string `xml:"Message,omitempty" json:"Message,omitempty"`

	FailureStatus string `xml:"FailureStatus,omitempty" json:"FailureStatus,omitempty"`

	ApplicationStatus string `xml:"ApplicationStatus,omitempty" json:"ApplicationStatus,omitempty"`

	Reference *ReferenceType `xml:"Reference,omitempty" json:"Reference,omitempty"`

	UnitNumber *NonNegativeInteger `xml:"UnitNumber,omitempty" json:"UnitNumber,omitempty"`

	Id string `xml:"Id,omitempty" json:"Id,omitempty"`

	RecordNumber *NonNegativeInteger `xml:"RecordNumber,omitempty" json:"RecordNumber,omitempty"`

	SequenceNumber *NonNegativeInteger `xml:"SequenceNumber,omitempty" json:"SequenceNumber,omitempty"`
}

type WsException struct {
	Source string `xml:"Source,omitempty" json:"Source,omitempty"`

	Code int32 `xml:"Code,omitempty" json:"Code,omitempty"`

	Location string `xml:"Location,omitempty" json:"Location,omitempty"`

	Message string `xml:"Message,omitempty" json:"Message,omitempty"`
}

type TransactionDetailsEx struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 TransactionDetailsEx"`

	TransactionDetails *TransactionDetails `xml:"TransactionDetails,omitempty" json:"TransactionDetails,omitempty"`

	ProcessingStatus string `xml:"ProcessingStatus,omitempty" json:"ProcessingStatus,omitempty"`

	ProcessingMessage string `xml:"ProcessingMessage,omitempty" json:"ProcessingMessage,omitempty"`

	ProductProcessingStatus string `xml:"ProductProcessingStatus,omitempty" json:"ProductProcessingStatus,omitempty"`

	ProductTransactionStatus string `xml:"ProductTransactionStatus,omitempty" json:"ProductTransactionStatus,omitempty"`

	TransactionReasonCode string `xml:"TransactionReasonCode,omitempty" json:"TransactionReasonCode,omitempty"`

	TransactionReasonDescription string `xml:"TransactionReasonDescription,omitempty" json:"TransactionReasonDescription,omitempty"`

	ReviewStatus string `xml:"ReviewStatus,omitempty" json:"ReviewStatus,omitempty"`

	TransactionId string `xml:"TransactionId,omitempty" json:"TransactionId,omitempty"`

	Exceptions struct {
		Item []*WsException `xml:"Item,omitempty" json:"Item,omitempty"`
	} `xml:"Exceptions,omitempty" json:"Exceptions,omitempty"`
}

type ClientResponse struct {
	Messages struct {
		Message []*InsuranceReportMessage `xml:"Message,omitempty" json:"Message,omitempty"`
	} `xml:"Messages,omitempty" json:"Messages,omitempty"`

	RequesterInformation *RequesterInformation `xml:"RequesterInformation,omitempty" json:"RequesterInformation,omitempty"`

	TransactionDetailsEx *TransactionDetailsEx `xml:"TransactionDetailsEx,omitempty" json:"TransactionDetailsEx,omitempty"`

	SearchBy *SearchBy `xml:"SearchBy,omitempty" json:"SearchBy,omitempty"`

	ProductOptions *ProductOptions `xml:"ProductOptions,omitempty" json:"ProductOptions,omitempty"`

	Products *ProductChoiceType `xml:"Products,omitempty" json:"Products,omitempty"`

	ProductResults *InsuranceProductResults `xml:"ProductResults,omitempty" json:"ProductResults,omitempty"`

	RuleplanCode string `xml:"RuleplanCode,omitempty" json:"RuleplanCode,omitempty"`
}

type ResponseEx struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsurancerules:ruleplan@ver=1 ResponseEx"`

	Response *ClientResponse `xml:"Response,omitempty" json:"Response,omitempty"`
}

type HandleRequestResponse struct {
	XMLName xml.Name `xml:"urn:lnrisk:ws:wsinsmultiproduct:wsinsmultiproduct handleRequestResponse"`

	ResponseEx *ResponseEx `xml:"ResponseEx,omitempty" json:"ResponseEx,omitempty"`
}

type RiskXMLOrderHandler interface {
	// Error can be either of the following types:
	//
	//   - SOAPException
	//   - SecurityFailedException

	HandleRequest(request *HandleRequest) (*HandleRequestResponse, error)

	HandleRequestContext(ctx context.Context, request *HandleRequest) (*HandleRequestResponse, error)
}

type riskXMLOrderHandler struct {
	client *soap.Client
}

func NewRiskXMLOrderHandler(client *soap.Client) RiskXMLOrderHandler {
	return &riskXMLOrderHandler{
		client: client,
	}
}

func (service *riskXMLOrderHandler) HandleRequestContext(ctx context.Context, request *HandleRequest) (*HandleRequestResponse, error) {
	response := new(HandleRequestResponse)
	err := service.client.CallContext(ctx, "''", request, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (service *riskXMLOrderHandler) HandleRequest(request *HandleRequest) (*HandleRequestResponse, error) {
	return service.HandleRequestContext(
		context.Background(),
		request,
	)
}
