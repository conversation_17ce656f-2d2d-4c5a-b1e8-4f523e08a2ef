package cacheserver

import (
	"context"
	"sync"

	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/mvr"
)

// NewLazyMVRClient returns a mvr.MVRClient which connects to server only when the
// first RPC is received. It only tries to connect once and if that fails it
// will directly return the error recived when trying to connect to server.
func NewLazyMVRClient(address string) (*LazyClient, error) {
	return &LazyClient{address: address}, nil
}

type LazyClient struct {
	once      sync.Once
	address   string
	connected bool
	err       error
	client    mvr.MVRClient
}

func (cl *LazyClient) connectToServer() {
	c, err := NewMVRClient(cl.address)
	if err != nil {
		cl.err = err
		cl.connected = false
		return
	}
	cl.client = c
	cl.connected = true
}

func (cl *LazyClient) FetchMVR(
	ctx context.Context, mvrReq *mvr.FetchMVRRequest, opts ...grpc.CallOption,
) (*mvr.FetchMVRResponse, error) {
	cl.once.Do(cl.connectToServer)
	if !cl.connected {
		return nil, cl.err
	}
	return cl.client.FetchMVR(ctx, mvrReq, opts...)
}

func (cl *LazyClient) PollRawVeriskResponse(
	ctx context.Context, req *mvr.PollMVRRequest, opts ...grpc.CallOption,
) (*mvr.RawVeriskResponse, error) {
	cl.once.Do(cl.connectToServer)
	if !cl.connected {
		return nil, cl.err
	}
	return cl.client.PollRawVeriskResponse(ctx, req, opts...)
}

func (cl *LazyClient) PollMVR(
	ctx context.Context, req *mvr.PollMVRRequest, opts ...grpc.CallOption,
) (*mvr.Report, error) {
	cl.once.Do(cl.connectToServer)
	if !cl.connected {
		return nil, cl.err
	}
	return cl.client.PollMVR(ctx, req, opts...)
}

func (cl *LazyClient) GetAttractScoreRawXml(
	ctx context.Context, req *mvr.GetAttractScoreRequest, opts ...grpc.CallOption,
) (*mvr.GetAttractScoreRawXmlResponse, error) {
	cl.once.Do(cl.connectToServer)
	if !cl.connected {
		return nil, cl.err
	}
	return cl.client.GetAttractScoreRawXml(ctx, req, opts...)
}

func (cl *LazyClient) GetAttractScore(
	ctx context.Context,
	req *mvr.GetAttractScoreRequest,
	opts ...grpc.CallOption,
) (*mvr.GetAttractScoreResponse, error) {
	cl.once.Do(cl.connectToServer)
	if !cl.connected {
		return nil, cl.err
	}
	return cl.client.GetAttractScore(ctx, req, opts...)
}

func (cl *LazyClient) GeNationalCreditFileRawXml(
	ctx context.Context,
	req *mvr.GetNationalCreditFileRequest,
	opts ...grpc.CallOption,
) (*mvr.GetNationalCreditFileRawXmlResponse, error) {
	cl.once.Do(cl.connectToServer)
	if !cl.connected {
		return nil, cl.err
	}
	return cl.client.GeNationalCreditFileRawXml(ctx, req, opts...)
}

func (cl *LazyClient) GetNationalCreditFile(
	ctx context.Context,
	req *mvr.GetNationalCreditFileRequest,
	opts ...grpc.CallOption,
) (*mvr.GetNationalCreditFileResponse, error) {
	cl.once.Do(cl.connectToServer)
	if !cl.connected {
		return nil, cl.err
	}
	return cl.client.GetNationalCreditFile(ctx, req, opts...)
}
