package mvr

import (
	"context"

	"github.com/cockroachdb/errors"
	"google.golang.org/grpc"
)

func NewNoopClient() *noopClient {
	return &noopClient{}
}

var errNotImplemented = errors.Newf("method not implemented for noopClient")

type noopClient struct{}

func (cl *noopClient) FetchMVR(
	ctx context.Context, mvrReq *FetchMVRRequest, opts ...grpc.CallOption,
) (*FetchMVRResponse, error) {
	res := FetchMVRResponse{RequestID: mvrReq.DlNumber}
	return &res, nil
}

func (cl *noopClient) PollRawVeriskResponse(
	ctx context.Context, req *PollMVRRequest, opts ...grpc.CallOption,
) (*RawVeriskResponse, error) {
	return nil, errNotImplemented
}

func (cl *noopClient) PollMVR(
	ctx context.Context, req *PollMVRRequest, opts ...grpc.CallOption,
) (*Report, error) {
	report := Report{
		DlNumber:        req.RequestID,
		RequestDlNumber: req.RequestID,
		RequestState:    "WI", // This is a short-term hacky fix since PollMVR doesn't take state as an argument.
	}
	return &report, nil
}

func (cl *noopClient) GetAttractScoreRawXml(
	ctx context.Context, req *GetAttractScoreRequest, opts ...grpc.CallOption,
) (*GetAttractScoreRawXmlResponse, error) {
	return nil, errNotImplemented
}

func (cl *noopClient) GetAttractScore(
	ctx context.Context, in *GetAttractScoreRequest, opts ...grpc.CallOption,
) (*GetAttractScoreResponse, error) {
	return &GetAttractScoreResponse{}, nil
}

func (cl *noopClient) GeNationalCreditFileRawXml(
	ctx context.Context, req *GetNationalCreditFileRequest, opts ...grpc.CallOption,
) (*GetNationalCreditFileRawXmlResponse, error) {
	return nil, errNotImplemented
}

func (cl *noopClient) GetNationalCreditFile(
	ctx context.Context, in *GetNationalCreditFileRequest, opts ...grpc.CallOption,
) (*GetNationalCreditFileResponse, error) {
	return &GetNationalCreditFileResponse{}, nil
}
