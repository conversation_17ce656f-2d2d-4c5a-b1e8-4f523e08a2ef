package mvr

import (
	"context"
	"testing"

	"google.golang.org/protobuf/proto"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/cockroachdb/errors"

	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"
)

type FetchMVRReportsTestSuite struct {
	suite.Suite

	ctx           context.Context
	mockMVRClient *MockMVRClient
	numDrivers    int
	request       *FetchMVRRequest
}

func TestFetchMVRReportsTestSuite(t *testing.T) {
	t.Parallel()
	suite.Run(t, new(FetchMVRReportsTestSuite))
}

func (f *FetchMVRReportsTestSuite) SetupTest() {
	ctx := context.Background()
	ctrl, ctx := gomock.WithContext(ctx, f.T())
	mockMVRClient := NewMockMVRClient(ctrl)

	f.ctx = ctx
	f.mockMVRClient = mockMVRClient
	f.request = &FetchMVRRequest{
		DlNumber: "dl-number",
	}
}

func (f *FetchMVRReportsTestSuite) TestSuccess() {
	fetchMVRCall := f.mockMVRClient.EXPECT().
		FetchMVR(gomock.Any(), f.request).
		Return(&FetchMVRResponse{RequestID: "request-id"}, nil)
	f.mockMVRClient.EXPECT().
		PollMVR(gomock.Any(), &PollMVRRequest{RequestID: "request-id"}).
		After(fetchMVRCall).
		Return(&Report{ReportID: "report-id"}, nil)

	report, err := FetchAndPollMVRReport(f.ctx, f.mockMVRClient, f.request)
	f.NoError(err)
	f.NotNil(report)
}

func (f *FetchMVRReportsTestSuite) TestFetchMVRErrors() {
	tt := []struct {
		name          string
		mvrClientErr  error
		expectedError error
	}{
		{
			name:          "deadline exceeded",
			mvrClientErr:  status.Error(codes.DeadlineExceeded, "request timed out"),
			expectedError: ErrorDeadlineExceeded,
		},
		{
			name:          "cancelled",
			mvrClientErr:  status.Error(codes.Canceled, "request cancelled"),
			expectedError: ErrorDeadlineExceeded,
		},
		{
			name:          "generic error",
			mvrClientErr:  errors.New("report not available"),
			expectedError: ErrorFetchingMVR,
		},
	}

	for _, tc := range tt {
		f.Run(tc.name, func() {
			f.mockMVRClient.EXPECT().
				FetchMVR(gomock.Any(), gomock.Cond(func(req *FetchMVRRequest) bool {
					return proto.Equal(req, f.request)
				})).
				Return(nil, tc.mvrClientErr)

			report, err := FetchAndPollMVRReport(f.ctx, f.mockMVRClient, f.request)
			f.Nil(report)
			f.ErrorIs(err, tc.expectedError)
		})
	}
}

func (f *FetchMVRReportsTestSuite) TestPollMVRErrors() {
	tt := []struct {
		name          string
		mvrClientErr  error
		expectedError error
	}{
		{
			name:          "deadline exceeded",
			mvrClientErr:  status.Error(codes.DeadlineExceeded, "request timed out"),
			expectedError: ErrorDeadlineExceeded,
		},
		{
			name:          "cancelled",
			mvrClientErr:  status.Error(codes.Canceled, "request cancelled"),
			expectedError: ErrorDeadlineExceeded,
		},
		{
			name:          "generic error",
			mvrClientErr:  errors.New("report not available"),
			expectedError: ErrorPollingMVR,
		},
	}

	for _, tc := range tt {
		f.Run(tc.name, func() {
			fetchMVRCall := f.mockMVRClient.EXPECT().
				FetchMVR(gomock.Any(), gomock.Cond(func(req *FetchMVRRequest) bool {
					return proto.Equal(req, f.request)
				})).Return(&FetchMVRResponse{RequestID: "request-id"}, nil)
			f.mockMVRClient.EXPECT().
				PollMVR(gomock.Any(), &PollMVRRequest{RequestID: "request-id"}).
				After(fetchMVRCall).
				Return(nil, tc.mvrClientErr)

			report, err := FetchAndPollMVRReport(f.ctx, f.mockMVRClient, f.request)
			f.Nil(report)
			f.ErrorIs(err, tc.expectedError)
		})
	}
}
