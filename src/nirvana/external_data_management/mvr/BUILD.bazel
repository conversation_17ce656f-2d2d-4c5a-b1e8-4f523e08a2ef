load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "mvr_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/external_data_management/mvr",
    proto = "//proto/mvr:mvr_proto",
    visibility = ["//visibility:public"],
)

# keep
go_library(
    name = "mvr",
    srcs = [
        "doc.go",
        "errors.go",
        "fx.go",
        "helper.go",
        "mock_mvr_client.go",
        "noop_client.go",
    ],
    embed = [":mvr_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/external_data_management/mvr",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/infra/config",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_mock//gomock",
    ],
)

# keep
go_proto_link(
    name = "mvr_go_proto_link",
    dep = ":mvr_go_proto",
    version = "v1",
)

go_test(
    name = "mvr_test",
    srcs = ["helper_test.go"],
    embed = [":mvr"],
    deps = [
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_uber_go_mock//gomock",
    ],
)
