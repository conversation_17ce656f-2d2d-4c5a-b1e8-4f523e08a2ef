// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/mvr (interfaces: MVRClient)
//
// Generated by this command:
//
//	mockgen -destination=./external_data_management/mvr/mock_mvr_client.go -package=mvr nirvanatech.com/nirvana/external_data_management/mvr MVRClient
//

// Package mvr is a generated GoMock package.
package mvr

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockMVRClient is a mock of MVRClient interface.
type MockMVRClient struct {
	ctrl     *gomock.Controller
	recorder *MockMVRClientMockRecorder
}

// MockMVRClientMockRecorder is the mock recorder for MockMVRClient.
type MockMVRClientMockRecorder struct {
	mock *MockMVRClient
}

// NewMockMVRClient creates a new mock instance.
func NewMockMVRClient(ctrl *gomock.Controller) *MockMVRClient {
	mock := &MockMVRClient{ctrl: ctrl}
	mock.recorder = &MockMVRClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMVRClient) EXPECT() *MockMVRClientMockRecorder {
	return m.recorder
}

// FetchMVR mocks base method.
func (m *MockMVRClient) FetchMVR(arg0 context.Context, arg1 *FetchMVRRequest, arg2 ...grpc.CallOption) (*FetchMVRResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchMVR", varargs...)
	ret0, _ := ret[0].(*FetchMVRResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchMVR indicates an expected call of FetchMVR.
func (mr *MockMVRClientMockRecorder) FetchMVR(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchMVR", reflect.TypeOf((*MockMVRClient)(nil).FetchMVR), varargs...)
}

// GeNationalCreditFileRawXml mocks base method.
func (m *MockMVRClient) GeNationalCreditFileRawXml(arg0 context.Context, arg1 *GetNationalCreditFileRequest, arg2 ...grpc.CallOption) (*GetNationalCreditFileRawXmlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeNationalCreditFileRawXml", varargs...)
	ret0, _ := ret[0].(*GetNationalCreditFileRawXmlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeNationalCreditFileRawXml indicates an expected call of GeNationalCreditFileRawXml.
func (mr *MockMVRClientMockRecorder) GeNationalCreditFileRawXml(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeNationalCreditFileRawXml", reflect.TypeOf((*MockMVRClient)(nil).GeNationalCreditFileRawXml), varargs...)
}

// GetAttractScore mocks base method.
func (m *MockMVRClient) GetAttractScore(arg0 context.Context, arg1 *GetAttractScoreRequest, arg2 ...grpc.CallOption) (*GetAttractScoreResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAttractScore", varargs...)
	ret0, _ := ret[0].(*GetAttractScoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttractScore indicates an expected call of GetAttractScore.
func (mr *MockMVRClientMockRecorder) GetAttractScore(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttractScore", reflect.TypeOf((*MockMVRClient)(nil).GetAttractScore), varargs...)
}

// GetAttractScoreRawXml mocks base method.
func (m *MockMVRClient) GetAttractScoreRawXml(arg0 context.Context, arg1 *GetAttractScoreRequest, arg2 ...grpc.CallOption) (*GetAttractScoreRawXmlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAttractScoreRawXml", varargs...)
	ret0, _ := ret[0].(*GetAttractScoreRawXmlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttractScoreRawXml indicates an expected call of GetAttractScoreRawXml.
func (mr *MockMVRClientMockRecorder) GetAttractScoreRawXml(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttractScoreRawXml", reflect.TypeOf((*MockMVRClient)(nil).GetAttractScoreRawXml), varargs...)
}

// GetNationalCreditFile mocks base method.
func (m *MockMVRClient) GetNationalCreditFile(arg0 context.Context, arg1 *GetNationalCreditFileRequest, arg2 ...grpc.CallOption) (*GetNationalCreditFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNationalCreditFile", varargs...)
	ret0, _ := ret[0].(*GetNationalCreditFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNationalCreditFile indicates an expected call of GetNationalCreditFile.
func (mr *MockMVRClientMockRecorder) GetNationalCreditFile(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNationalCreditFile", reflect.TypeOf((*MockMVRClient)(nil).GetNationalCreditFile), varargs...)
}

// PollMVR mocks base method.
func (m *MockMVRClient) PollMVR(arg0 context.Context, arg1 *PollMVRRequest, arg2 ...grpc.CallOption) (*Report, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PollMVR", varargs...)
	ret0, _ := ret[0].(*Report)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PollMVR indicates an expected call of PollMVR.
func (mr *MockMVRClientMockRecorder) PollMVR(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PollMVR", reflect.TypeOf((*MockMVRClient)(nil).PollMVR), varargs...)
}

// PollRawVeriskResponse mocks base method.
func (m *MockMVRClient) PollRawVeriskResponse(arg0 context.Context, arg1 *PollMVRRequest, arg2 ...grpc.CallOption) (*RawVeriskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PollRawVeriskResponse", varargs...)
	ret0, _ := ret[0].(*RawVeriskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PollRawVeriskResponse indicates an expected call of PollRawVeriskResponse.
func (mr *MockMVRClientMockRecorder) PollRawVeriskResponse(arg0, arg1 any, arg2 ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PollRawVeriskResponse", reflect.TypeOf((*MockMVRClient)(nil).PollRawVeriskResponse), varargs...)
}
