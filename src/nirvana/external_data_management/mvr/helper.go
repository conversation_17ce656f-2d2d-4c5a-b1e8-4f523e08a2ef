package mvr

import (
	"context"
	"fmt"
	"time"

	"github.com/cockroachdb/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"nirvanatech.com/nirvana/common-go/log"
)

const (
	numRetries    = 50
	retryInterval = 5 * time.Second
)

var (
	ErrorFetchingMVR      = errors.New("error fetching MVR")
	ErrorPollingMVR       = errors.New("error polling MVR")
	ErrorDeadlineExceeded = errors.New("deadline exceeded while waiting for MVR response")
)

func FetchAndPollMVRReport(
	ctx context.Context,
	client MVRClient,
	request *FetchMVRRequest,
) (*Report, error) {
	fetchMVRResponse, err := client.FetchMVR(ctx, request)
	if err != nil {
		if isDeadlineExceededOrCancelled(err) {
			return nil, ErrorDeadlineExceeded
		}
		return nil, errors.Join(err, ErrorFetchingMVR)
	}

	retryCounter := 0
	for {
		report, err := client.PollMVR(ctx, &PollMVRRequest{RequestID: fetchMVRResponse.RequestID})
		if ShouldPollAgain(err) && retryCounter < numRetries {
			retryCounter = retryCounter + 1
			time.Sleep(retryInterval)
			log.Plain.Info(
				"Retrying PollMVRReport",
				log.Any("retryAttempt", retryCounter),
				log.Any("requestId", fetchMVRResponse.RequestID),
			)
			continue
		}
		if err != nil {
			if isDeadlineExceededOrCancelled(err) {
				return nil, ErrorDeadlineExceeded
			}
			return nil, errors.Join(err, ErrorPollingMVR)
		}
		log.Plain.Info("fetched mvr report", log.String("applicationID", request.ApplicationID))
		return report, nil
	}
}

func FetchAttractScore(
	ctx context.Context,
	client MVRClient,
	request *GetAttractScoreRequest,
) (*GetAttractScoreResponse, error) {
	response, err := client.GetAttractScore(ctx, request)
	if err != nil {
		if isDeadlineExceededOrCancelled(err) {
			// Request has timed-out or cancel on this ctx has been called
			return nil, errors.Newf(
				"Request for DL Number: %s timed-out or cancel called on ctx",
				request.DlNumber,
			)
		}
		return nil, err
	}

	log.Plain.Info(
		"fetched attract score",
		log.String("dlNumber", request.DlNumber),
		log.Int64("attractScore", response.AttractScore),
		log.String("applicationID", request.ApplicationID),
	)

	return response, nil
}

func FetchNationalCreditFile(
	ctx context.Context,
	client MVRClient,
	request *GetNationalCreditFileRequest,
) (*GetNationalCreditFileResponse, error) {
	response, err := client.GetNationalCreditFile(ctx, request)
	if err != nil {
		if isDeadlineExceededOrCancelled(err) {
			// Request has timed-out or cancel on this ctx has been called
			return nil, errors.Newf(
				"Request for Owner: %s timed-out or cancel called on ctx",
				fmt.Sprintf("%s %s %s", request.FirstName, request.MiddleName, request.LastName),
			)
		}
		return response, err
	}

	log.Plain.Info(
		"fetched national credit file",
		log.String("Owner", fmt.Sprintf("%s %s %s", request.FirstName, request.MiddleName, request.LastName)),
		log.String("applicationID", request.ApplicationID),
	)

	return response, nil
}

func isDeadlineExceededOrCancelled(err error) bool {
	grpcErrorCode := status.Code(err)
	return grpcErrorCode == codes.Canceled || grpcErrorCode == codes.DeadlineExceeded
}
