// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: mvr/api.proto

package mvr

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FetchMVRRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DlNumber           string                 `protobuf:"bytes,1,opt,name=dlNumber,proto3" json:"dlNumber,omitempty"`
	UsState            string                 `protobuf:"bytes,2,opt,name=usState,proto3" json:"usState,omitempty"`
	Dob                *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
	FirstName          string                 `protobuf:"bytes,4,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName           string                 `protobuf:"bytes,5,opt,name=lastName,proto3" json:"lastName,omitempty"`
	MiddleName         string                 `protobuf:"bytes,6,opt,name=middleName,proto3" json:"middleName,omitempty"`
	Staleness          int64                  `protobuf:"varint,7,opt,name=staleness,proto3" json:"staleness,omitempty"`
	ApplicationID      string                 `protobuf:"bytes,8,opt,name=applicationID,proto3" json:"applicationID,omitempty"`
	OnlyFetchFromCache bool                   `protobuf:"varint,9,opt,name=onlyFetchFromCache,proto3" json:"onlyFetchFromCache,omitempty"`
}

func (x *FetchMVRRequest) Reset() {
	*x = FetchMVRRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchMVRRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchMVRRequest) ProtoMessage() {}

func (x *FetchMVRRequest) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchMVRRequest.ProtoReflect.Descriptor instead.
func (*FetchMVRRequest) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{0}
}

func (x *FetchMVRRequest) GetDlNumber() string {
	if x != nil {
		return x.DlNumber
	}
	return ""
}

func (x *FetchMVRRequest) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *FetchMVRRequest) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *FetchMVRRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *FetchMVRRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *FetchMVRRequest) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *FetchMVRRequest) GetStaleness() int64 {
	if x != nil {
		return x.Staleness
	}
	return 0
}

func (x *FetchMVRRequest) GetApplicationID() string {
	if x != nil {
		return x.ApplicationID
	}
	return ""
}

func (x *FetchMVRRequest) GetOnlyFetchFromCache() bool {
	if x != nil {
		return x.OnlyFetchFromCache
	}
	return false
}

type FetchMVRResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestID string `protobuf:"bytes,1,opt,name=requestID,proto3" json:"requestID,omitempty"`
}

func (x *FetchMVRResponse) Reset() {
	*x = FetchMVRResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchMVRResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchMVRResponse) ProtoMessage() {}

func (x *FetchMVRResponse) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchMVRResponse.ProtoReflect.Descriptor instead.
func (*FetchMVRResponse) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{1}
}

func (x *FetchMVRResponse) GetRequestID() string {
	if x != nil {
		return x.RequestID
	}
	return ""
}

type Violation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViolationType         string                 `protobuf:"bytes,1,opt,name=violationType,proto3" json:"violationType,omitempty"`
	ViolationDate         *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=violationDate,proto3" json:"violationDate,omitempty"`
	ConvictionDate        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=convictionDate,proto3" json:"convictionDate,omitempty"`
	ViolationCode         string                 `protobuf:"bytes,4,opt,name=violationCode,proto3" json:"violationCode,omitempty"`
	Points                int64                  `protobuf:"varint,5,opt,name=points,proto3" json:"points,omitempty"`
	AssignedViolationCode string                 `protobuf:"bytes,6,opt,name=assignedViolationCode,proto3" json:"assignedViolationCode,omitempty"`
	AssignedPoints        string                 `protobuf:"bytes,7,opt,name=assignedPoints,proto3" json:"assignedPoints,omitempty"`
	ViolationDetail       string                 `protobuf:"bytes,8,opt,name=violationDetail,proto3" json:"violationDetail,omitempty"`
}

func (x *Violation) Reset() {
	*x = Violation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Violation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Violation) ProtoMessage() {}

func (x *Violation) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Violation.ProtoReflect.Descriptor instead.
func (*Violation) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{2}
}

func (x *Violation) GetViolationType() string {
	if x != nil {
		return x.ViolationType
	}
	return ""
}

func (x *Violation) GetViolationDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ViolationDate
	}
	return nil
}

func (x *Violation) GetConvictionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ConvictionDate
	}
	return nil
}

func (x *Violation) GetViolationCode() string {
	if x != nil {
		return x.ViolationCode
	}
	return ""
}

func (x *Violation) GetPoints() int64 {
	if x != nil {
		return x.Points
	}
	return 0
}

func (x *Violation) GetAssignedViolationCode() string {
	if x != nil {
		return x.AssignedViolationCode
	}
	return ""
}

func (x *Violation) GetAssignedPoints() string {
	if x != nil {
		return x.AssignedPoints
	}
	return ""
}

func (x *Violation) GetViolationDetail() string {
	if x != nil {
		return x.ViolationDetail
	}
	return ""
}

type PollMVRRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestID string `protobuf:"bytes,1,opt,name=requestID,proto3" json:"requestID,omitempty"`
}

func (x *PollMVRRequest) Reset() {
	*x = PollMVRRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollMVRRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollMVRRequest) ProtoMessage() {}

func (x *PollMVRRequest) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollMVRRequest.ProtoReflect.Descriptor instead.
func (*PollMVRRequest) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{3}
}

func (x *PollMVRRequest) GetRequestID() string {
	if x != nil {
		return x.RequestID
	}
	return ""
}

type Report struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportID             string                 `protobuf:"bytes,1,opt,name=reportID,proto3" json:"reportID,omitempty"`
	DlState              string                 `protobuf:"bytes,2,opt,name=dlState,proto3" json:"dlState,omitempty"`
	ReportSequenceNumber string                 `protobuf:"bytes,3,opt,name=reportSequenceNumber,proto3" json:"reportSequenceNumber,omitempty"`
	DriverName           string                 `protobuf:"bytes,4,opt,name=driverName,proto3" json:"driverName,omitempty"`
	DriverStreetAddr     string                 `protobuf:"bytes,5,opt,name=driverStreetAddr,proto3" json:"driverStreetAddr,omitempty"`
	MvrStatus            string                 `protobuf:"bytes,6,opt,name=mvrStatus,proto3" json:"mvrStatus,omitempty"`
	ViolationCoding      string                 `protobuf:"bytes,7,opt,name=violationCoding,proto3" json:"violationCoding,omitempty"`
	ViolationCodeTotal   int64                  `protobuf:"varint,8,opt,name=violationCodeTotal,proto3" json:"violationCodeTotal,omitempty"`
	MvrFormat            string                 `protobuf:"bytes,9,opt,name=mvrFormat,proto3" json:"mvrFormat,omitempty"`
	DriverCityStateZip   string                 `protobuf:"bytes,10,opt,name=driverCityStateZip,proto3" json:"driverCityStateZip,omitempty"`
	DlNumber             string                 `protobuf:"bytes,11,opt,name=dlNumber,proto3" json:"dlNumber,omitempty"`
	ClientCode           string                 `protobuf:"bytes,12,opt,name=clientCode,proto3" json:"clientCode,omitempty"`
	ArchiveFlag          string                 `protobuf:"bytes,13,opt,name=archiveFlag,proto3" json:"archiveFlag,omitempty"`
	Ssn                  string                 `protobuf:"bytes,14,opt,name=ssn,proto3" json:"ssn,omitempty"`
	DppaFlag             string                 `protobuf:"bytes,15,opt,name=dppaFlag,proto3" json:"dppaFlag,omitempty"`
	DmvAccountNumber     string                 `protobuf:"bytes,16,opt,name=dmvAccountNumber,proto3" json:"dmvAccountNumber,omitempty"`
	Dob                  *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=dob,proto3" json:"dob,omitempty"`
	Gender               string                 `protobuf:"bytes,18,opt,name=gender,proto3" json:"gender,omitempty"`
	Height               string                 `protobuf:"bytes,19,opt,name=height,proto3" json:"height,omitempty"`
	Weight               string                 `protobuf:"bytes,20,opt,name=weight,proto3" json:"weight,omitempty"`
	EyeColor             string                 `protobuf:"bytes,21,opt,name=eyeColor,proto3" json:"eyeColor,omitempty"`
	HairColor            string                 `protobuf:"bytes,22,opt,name=hairColor,proto3" json:"hairColor,omitempty"`
	LicClass             string                 `protobuf:"bytes,23,opt,name=licClass,proto3" json:"licClass,omitempty"`
	LicStatus            string                 `protobuf:"bytes,24,opt,name=licStatus,proto3" json:"licStatus,omitempty"`
	DateIssued           *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=dateIssued,proto3" json:"dateIssued,omitempty"`
	DateExpires          *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=dateExpires,proto3" json:"dateExpires,omitempty"`
	MvrReportDate        *timestamppb.Timestamp `protobuf:"bytes,27,opt,name=mvrReportDate,proto3" json:"mvrReportDate,omitempty"`
	Restrictions         string                 `protobuf:"bytes,28,opt,name=restrictions,proto3" json:"restrictions,omitempty"`
	MiscDetail           []string               `protobuf:"bytes,29,rep,name=miscDetail,proto3" json:"miscDetail,omitempty"`
	Violations           []*Violation           `protobuf:"bytes,30,rep,name=violations,proto3" json:"violations,omitempty"`
	RequestDlNumber      string                 `protobuf:"bytes,31,opt,name=requestDlNumber,proto3" json:"requestDlNumber,omitempty"`
	RequestState         string                 `protobuf:"bytes,32,opt,name=requestState,proto3" json:"requestState,omitempty"`
}

func (x *Report) Reset() {
	*x = Report{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Report) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report) ProtoMessage() {}

func (x *Report) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report.ProtoReflect.Descriptor instead.
func (*Report) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{4}
}

func (x *Report) GetReportID() string {
	if x != nil {
		return x.ReportID
	}
	return ""
}

func (x *Report) GetDlState() string {
	if x != nil {
		return x.DlState
	}
	return ""
}

func (x *Report) GetReportSequenceNumber() string {
	if x != nil {
		return x.ReportSequenceNumber
	}
	return ""
}

func (x *Report) GetDriverName() string {
	if x != nil {
		return x.DriverName
	}
	return ""
}

func (x *Report) GetDriverStreetAddr() string {
	if x != nil {
		return x.DriverStreetAddr
	}
	return ""
}

func (x *Report) GetMvrStatus() string {
	if x != nil {
		return x.MvrStatus
	}
	return ""
}

func (x *Report) GetViolationCoding() string {
	if x != nil {
		return x.ViolationCoding
	}
	return ""
}

func (x *Report) GetViolationCodeTotal() int64 {
	if x != nil {
		return x.ViolationCodeTotal
	}
	return 0
}

func (x *Report) GetMvrFormat() string {
	if x != nil {
		return x.MvrFormat
	}
	return ""
}

func (x *Report) GetDriverCityStateZip() string {
	if x != nil {
		return x.DriverCityStateZip
	}
	return ""
}

func (x *Report) GetDlNumber() string {
	if x != nil {
		return x.DlNumber
	}
	return ""
}

func (x *Report) GetClientCode() string {
	if x != nil {
		return x.ClientCode
	}
	return ""
}

func (x *Report) GetArchiveFlag() string {
	if x != nil {
		return x.ArchiveFlag
	}
	return ""
}

func (x *Report) GetSsn() string {
	if x != nil {
		return x.Ssn
	}
	return ""
}

func (x *Report) GetDppaFlag() string {
	if x != nil {
		return x.DppaFlag
	}
	return ""
}

func (x *Report) GetDmvAccountNumber() string {
	if x != nil {
		return x.DmvAccountNumber
	}
	return ""
}

func (x *Report) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *Report) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *Report) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *Report) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *Report) GetEyeColor() string {
	if x != nil {
		return x.EyeColor
	}
	return ""
}

func (x *Report) GetHairColor() string {
	if x != nil {
		return x.HairColor
	}
	return ""
}

func (x *Report) GetLicClass() string {
	if x != nil {
		return x.LicClass
	}
	return ""
}

func (x *Report) GetLicStatus() string {
	if x != nil {
		return x.LicStatus
	}
	return ""
}

func (x *Report) GetDateIssued() *timestamppb.Timestamp {
	if x != nil {
		return x.DateIssued
	}
	return nil
}

func (x *Report) GetDateExpires() *timestamppb.Timestamp {
	if x != nil {
		return x.DateExpires
	}
	return nil
}

func (x *Report) GetMvrReportDate() *timestamppb.Timestamp {
	if x != nil {
		return x.MvrReportDate
	}
	return nil
}

func (x *Report) GetRestrictions() string {
	if x != nil {
		return x.Restrictions
	}
	return ""
}

func (x *Report) GetMiscDetail() []string {
	if x != nil {
		return x.MiscDetail
	}
	return nil
}

func (x *Report) GetViolations() []*Violation {
	if x != nil {
		return x.Violations
	}
	return nil
}

func (x *Report) GetRequestDlNumber() string {
	if x != nil {
		return x.RequestDlNumber
	}
	return ""
}

func (x *Report) GetRequestState() string {
	if x != nil {
		return x.RequestState
	}
	return ""
}

type RawVeriskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContentsXML string `protobuf:"bytes,1,opt,name=contentsXML,proto3" json:"contentsXML,omitempty"`
}

func (x *RawVeriskResponse) Reset() {
	*x = RawVeriskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawVeriskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawVeriskResponse) ProtoMessage() {}

func (x *RawVeriskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawVeriskResponse.ProtoReflect.Descriptor instead.
func (*RawVeriskResponse) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{5}
}

func (x *RawVeriskResponse) GetContentsXML() string {
	if x != nil {
		return x.ContentsXML
	}
	return ""
}

type GetAttractScoreRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DlNumber      string                 `protobuf:"bytes,1,opt,name=dlNumber,proto3" json:"dlNumber,omitempty"`
	UsState       string                 `protobuf:"bytes,2,opt,name=usState,proto3" json:"usState,omitempty"`
	Dob           *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
	FirstName     string                 `protobuf:"bytes,4,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName      string                 `protobuf:"bytes,5,opt,name=lastName,proto3" json:"lastName,omitempty"`
	MiddleName    string                 `protobuf:"bytes,6,opt,name=middleName,proto3" json:"middleName,omitempty"`
	Staleness     int64                  `protobuf:"varint,7,opt,name=staleness,proto3" json:"staleness,omitempty"`
	ApplicationID string                 `protobuf:"bytes,8,opt,name=applicationID,proto3" json:"applicationID,omitempty"`
}

func (x *GetAttractScoreRequest) Reset() {
	*x = GetAttractScoreRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAttractScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttractScoreRequest) ProtoMessage() {}

func (x *GetAttractScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttractScoreRequest.ProtoReflect.Descriptor instead.
func (*GetAttractScoreRequest) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetAttractScoreRequest) GetDlNumber() string {
	if x != nil {
		return x.DlNumber
	}
	return ""
}

func (x *GetAttractScoreRequest) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *GetAttractScoreRequest) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *GetAttractScoreRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *GetAttractScoreRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *GetAttractScoreRequest) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *GetAttractScoreRequest) GetStaleness() int64 {
	if x != nil {
		return x.Staleness
	}
	return 0
}

func (x *GetAttractScoreRequest) GetApplicationID() string {
	if x != nil {
		return x.ApplicationID
	}
	return ""
}

type GetAttractScoreRawXmlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContentsXML string `protobuf:"bytes,1,opt,name=contentsXML,proto3" json:"contentsXML,omitempty"`
}

func (x *GetAttractScoreRawXmlResponse) Reset() {
	*x = GetAttractScoreRawXmlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAttractScoreRawXmlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttractScoreRawXmlResponse) ProtoMessage() {}

func (x *GetAttractScoreRawXmlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttractScoreRawXmlResponse.ProtoReflect.Descriptor instead.
func (*GetAttractScoreRawXmlResponse) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetAttractScoreRawXmlResponse) GetContentsXML() string {
	if x != nil {
		return x.ContentsXML
	}
	return ""
}

type GetAttractScoreResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttractScore       int64  `protobuf:"varint,1,opt,name=attractScore,proto3" json:"attractScore,omitempty"`
	SerializedMiscInfo string `protobuf:"bytes,2,opt,name=serializedMiscInfo,proto3" json:"serializedMiscInfo,omitempty"`
}

func (x *GetAttractScoreResponse) Reset() {
	*x = GetAttractScoreResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAttractScoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAttractScoreResponse) ProtoMessage() {}

func (x *GetAttractScoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAttractScoreResponse.ProtoReflect.Descriptor instead.
func (*GetAttractScoreResponse) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetAttractScoreResponse) GetAttractScore() int64 {
	if x != nil {
		return x.AttractScore
	}
	return 0
}

func (x *GetAttractScoreResponse) GetSerializedMiscInfo() string {
	if x != nil {
		return x.SerializedMiscInfo
	}
	return ""
}

type GetNationalCreditFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dob           *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=dob,proto3" json:"dob,omitempty"`
	FirstName     string                 `protobuf:"bytes,2,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName      string                 `protobuf:"bytes,3,opt,name=lastName,proto3" json:"lastName,omitempty"`
	MiddleName    string                 `protobuf:"bytes,4,opt,name=middleName,proto3" json:"middleName,omitempty"`
	Address       *Address               `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	Staleness     int64                  `protobuf:"varint,6,opt,name=staleness,proto3" json:"staleness,omitempty"`
	ApplicationID string                 `protobuf:"bytes,7,opt,name=applicationID,proto3" json:"applicationID,omitempty"`
	EncryptedSSN  []byte                 `protobuf:"bytes,8,opt,name=encryptedSSN,proto3" json:"encryptedSSN,omitempty"`
	SsnLastFour   *string                `protobuf:"bytes,9,opt,name=ssnLastFour,proto3,oneof" json:"ssnLastFour,omitempty"`
}

func (x *GetNationalCreditFileRequest) Reset() {
	*x = GetNationalCreditFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNationalCreditFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNationalCreditFileRequest) ProtoMessage() {}

func (x *GetNationalCreditFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNationalCreditFileRequest.ProtoReflect.Descriptor instead.
func (*GetNationalCreditFileRequest) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetNationalCreditFileRequest) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *GetNationalCreditFileRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *GetNationalCreditFileRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *GetNationalCreditFileRequest) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *GetNationalCreditFileRequest) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *GetNationalCreditFileRequest) GetStaleness() int64 {
	if x != nil {
		return x.Staleness
	}
	return 0
}

func (x *GetNationalCreditFileRequest) GetApplicationID() string {
	if x != nil {
		return x.ApplicationID
	}
	return ""
}

func (x *GetNationalCreditFileRequest) GetEncryptedSSN() []byte {
	if x != nil {
		return x.EncryptedSSN
	}
	return nil
}

func (x *GetNationalCreditFileRequest) GetSsnLastFour() string {
	if x != nil && x.SsnLastFour != nil {
		return *x.SsnLastFour
	}
	return ""
}

type Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Street string `protobuf:"bytes,1,opt,name=street,proto3" json:"street,omitempty"`
	City   string `protobuf:"bytes,2,opt,name=city,proto3" json:"city,omitempty"`
	State  string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Zip    string `protobuf:"bytes,4,opt,name=zip,proto3" json:"zip,omitempty"`
}

func (x *Address) Reset() {
	*x = Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{10}
}

func (x *Address) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *Address) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Address) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Address) GetZip() string {
	if x != nil {
		return x.Zip
	}
	return ""
}

type GetNationalCreditFileRawXmlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContentsXML string `protobuf:"bytes,1,opt,name=contentsXML,proto3" json:"contentsXML,omitempty"`
}

func (x *GetNationalCreditFileRawXmlResponse) Reset() {
	*x = GetNationalCreditFileRawXmlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNationalCreditFileRawXmlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNationalCreditFileRawXmlResponse) ProtoMessage() {}

func (x *GetNationalCreditFileRawXmlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNationalCreditFileRawXmlResponse.ProtoReflect.Descriptor instead.
func (*GetNationalCreditFileRawXmlResponse) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetNationalCreditFileRawXmlResponse) GetContentsXML() string {
	if x != nil {
		return x.ContentsXML
	}
	return ""
}

type GetNationalCreditFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NcfReport          *NcfReport          `protobuf:"bytes,1,opt,name=NcfReport,proto3" json:"NcfReport,omitempty"`
	SerializedMiscInfo string              `protobuf:"bytes,2,opt,name=serializedMiscInfo,proto3" json:"serializedMiscInfo,omitempty"`
	TransactionDetails *TransactionDetails `protobuf:"bytes,3,opt,name=TransactionDetails,proto3" json:"TransactionDetails,omitempty"`
}

func (x *GetNationalCreditFileResponse) Reset() {
	*x = GetNationalCreditFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNationalCreditFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNationalCreditFileResponse) ProtoMessage() {}

func (x *GetNationalCreditFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNationalCreditFileResponse.ProtoReflect.Descriptor instead.
func (*GetNationalCreditFileResponse) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetNationalCreditFileResponse) GetNcfReport() *NcfReport {
	if x != nil {
		return x.NcfReport
	}
	return nil
}

func (x *GetNationalCreditFileResponse) GetSerializedMiscInfo() string {
	if x != nil {
		return x.SerializedMiscInfo
	}
	return ""
}

func (x *GetNationalCreditFileResponse) GetTransactionDetails() *TransactionDetails {
	if x != nil {
		return x.TransactionDetails
	}
	return nil
}

type Name struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	First  string `protobuf:"bytes,1,opt,name=first,proto3" json:"first,omitempty"`
	Middle string `protobuf:"bytes,2,opt,name=middle,proto3" json:"middle,omitempty"`
	Last   string `protobuf:"bytes,3,opt,name=last,proto3" json:"last,omitempty"`
	Suffix string `protobuf:"bytes,4,opt,name=suffix,proto3" json:"suffix,omitempty"`
}

func (x *Name) Reset() {
	*x = Name{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Name) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Name) ProtoMessage() {}

func (x *Name) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Name.ProtoReflect.Descriptor instead.
func (*Name) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{13}
}

func (x *Name) GetFirst() string {
	if x != nil {
		return x.First
	}
	return ""
}

func (x *Name) GetMiddle() string {
	if x != nil {
		return x.Middle
	}
	return ""
}

func (x *Name) GetLast() string {
	if x != nil {
		return x.Last
	}
	return ""
}

func (x *Name) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

type Dob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
}

func (x *Dob) Reset() {
	*x = Dob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dob) ProtoMessage() {}

func (x *Dob) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dob.ProtoReflect.Descriptor instead.
func (*Dob) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{14}
}

func (x *Dob) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

type Subject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Classification      string `protobuf:"bytes,1,opt,name=classification,proto3" json:"classification,omitempty"`
	Name                *Name  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Ssn                 string `protobuf:"bytes,3,opt,name=ssn,proto3" json:"ssn,omitempty"`
	Dob                 *Dob   `protobuf:"bytes,4,opt,name=dob,proto3" json:"dob,omitempty"`
	HeightFeet          string `protobuf:"bytes,5,opt,name=heightFeet,proto3" json:"heightFeet,omitempty"`
	HeightInches        string `protobuf:"bytes,6,opt,name=heightInches,proto3" json:"heightInches,omitempty"`
	Weight              string `protobuf:"bytes,7,opt,name=weight,proto3" json:"weight,omitempty"`
	RelationshipType    string `protobuf:"bytes,8,opt,name=relationshipType,proto3" json:"relationshipType,omitempty"`
	GroupSequenceNumber string `protobuf:"bytes,9,opt,name=groupSequenceNumber,proto3" json:"groupSequenceNumber,omitempty"`
}

func (x *Subject) Reset() {
	*x = Subject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Subject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subject) ProtoMessage() {}

func (x *Subject) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subject.ProtoReflect.Descriptor instead.
func (*Subject) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{15}
}

func (x *Subject) GetClassification() string {
	if x != nil {
		return x.Classification
	}
	return ""
}

func (x *Subject) GetName() *Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Subject) GetSsn() string {
	if x != nil {
		return x.Ssn
	}
	return ""
}

func (x *Subject) GetDob() *Dob {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *Subject) GetHeightFeet() string {
	if x != nil {
		return x.HeightFeet
	}
	return ""
}

func (x *Subject) GetHeightInches() string {
	if x != nil {
		return x.HeightInches
	}
	return ""
}

func (x *Subject) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *Subject) GetRelationshipType() string {
	if x != nil {
		return x.RelationshipType
	}
	return ""
}

func (x *Subject) GetGroupSequenceNumber() string {
	if x != nil {
		return x.GroupSequenceNumber
	}
	return ""
}

type DateFirstAtAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month uint32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
}

func (x *DateFirstAtAddress) Reset() {
	*x = DateFirstAtAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateFirstAtAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateFirstAtAddress) ProtoMessage() {}

func (x *DateFirstAtAddress) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateFirstAtAddress.ProtoReflect.Descriptor instead.
func (*DateFirstAtAddress) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{16}
}

func (x *DateFirstAtAddress) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *DateFirstAtAddress) GetMonth() uint32 {
	if x != nil {
		return x.Month
	}
	return 0
}

type CurrentAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StreetNumber       string              `protobuf:"bytes,1,opt,name=streetNumber,proto3" json:"streetNumber,omitempty"`
	StreetName         string              `protobuf:"bytes,2,opt,name=streetName,proto3" json:"streetName,omitempty"`
	City               string              `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`
	State              string              `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Zip5               string              `protobuf:"bytes,5,opt,name=zip5,proto3" json:"zip5,omitempty"`
	Zip4               string              `protobuf:"bytes,6,opt,name=zip4,proto3" json:"zip4,omitempty"`
	DateFirstAtAddress *DateFirstAtAddress `protobuf:"bytes,7,opt,name=dateFirstAtAddress,proto3" json:"dateFirstAtAddress,omitempty"`
	AddressId          string              `protobuf:"bytes,8,opt,name=addressId,proto3" json:"addressId,omitempty"`
}

func (x *CurrentAddress) Reset() {
	*x = CurrentAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentAddress) ProtoMessage() {}

func (x *CurrentAddress) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentAddress.ProtoReflect.Descriptor instead.
func (*CurrentAddress) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{17}
}

func (x *CurrentAddress) GetStreetNumber() string {
	if x != nil {
		return x.StreetNumber
	}
	return ""
}

func (x *CurrentAddress) GetStreetName() string {
	if x != nil {
		return x.StreetName
	}
	return ""
}

func (x *CurrentAddress) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *CurrentAddress) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *CurrentAddress) GetZip5() string {
	if x != nil {
		return x.Zip5
	}
	return ""
}

func (x *CurrentAddress) GetZip4() string {
	if x != nil {
		return x.Zip4
	}
	return ""
}

func (x *CurrentAddress) GetDateFirstAtAddress() *DateFirstAtAddress {
	if x != nil {
		return x.DateFirstAtAddress
	}
	return nil
}

func (x *CurrentAddress) GetAddressId() string {
	if x != nil {
		return x.AddressId
	}
	return ""
}

type SubjectInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject        *Subject        `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	CurrentAddress *CurrentAddress `protobuf:"bytes,2,opt,name=currentAddress,proto3" json:"currentAddress,omitempty"`
}

func (x *SubjectInfo) Reset() {
	*x = SubjectInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubjectInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubjectInfo) ProtoMessage() {}

func (x *SubjectInfo) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubjectInfo.ProtoReflect.Descriptor instead.
func (*SubjectInfo) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{18}
}

func (x *SubjectInfo) GetSubject() *Subject {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *SubjectInfo) GetCurrentAddress() *CurrentAddress {
	if x != nil {
		return x.CurrentAddress
	}
	return nil
}

type DateCreditFileEstbed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month uint32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	Day   uint32 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
}

func (x *DateCreditFileEstbed) Reset() {
	*x = DateCreditFileEstbed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateCreditFileEstbed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateCreditFileEstbed) ProtoMessage() {}

func (x *DateCreditFileEstbed) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateCreditFileEstbed.ProtoReflect.Descriptor instead.
func (*DateCreditFileEstbed) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{19}
}

func (x *DateCreditFileEstbed) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *DateCreditFileEstbed) GetMonth() uint32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *DateCreditFileEstbed) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

type CurrentStatusAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NumberOfAccounts uint32 `protobuf:"varint,2,opt,name=numberOfAccounts,proto3" json:"numberOfAccounts,omitempty"`
}

func (x *CurrentStatusAccount) Reset() {
	*x = CurrentStatusAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentStatusAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentStatusAccount) ProtoMessage() {}

func (x *CurrentStatusAccount) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentStatusAccount.ProtoReflect.Descriptor instead.
func (*CurrentStatusAccount) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{20}
}

func (x *CurrentStatusAccount) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CurrentStatusAccount) GetNumberOfAccounts() uint32 {
	if x != nil {
		return x.NumberOfAccounts
	}
	return 0
}

type CurrentStatusAccounts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentStatusAccount []*CurrentStatusAccount `protobuf:"bytes,1,rep,name=currentStatusAccount,proto3" json:"currentStatusAccount,omitempty"`
}

func (x *CurrentStatusAccounts) Reset() {
	*x = CurrentStatusAccounts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentStatusAccounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentStatusAccounts) ProtoMessage() {}

func (x *CurrentStatusAccounts) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentStatusAccounts.ProtoReflect.Descriptor instead.
func (*CurrentStatusAccounts) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{21}
}

func (x *CurrentStatusAccounts) GetCurrentStatusAccount() []*CurrentStatusAccount {
	if x != nil {
		return x.CurrentStatusAccount
	}
	return nil
}

type HistoryStatusAccounts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HistoryStatusAccount []*CurrentStatusAccount `protobuf:"bytes,1,rep,name=historyStatusAccount,proto3" json:"historyStatusAccount,omitempty"`
}

func (x *HistoryStatusAccounts) Reset() {
	*x = HistoryStatusAccounts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HistoryStatusAccounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryStatusAccounts) ProtoMessage() {}

func (x *HistoryStatusAccounts) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryStatusAccounts.ProtoReflect.Descriptor instead.
func (*HistoryStatusAccounts) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{22}
}

func (x *HistoryStatusAccounts) GetHistoryStatusAccount() []*CurrentStatusAccount {
	if x != nil {
		return x.HistoryStatusAccount
	}
	return nil
}

type CreditReportSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateCreditFileEstbed        *DateCreditFileEstbed  `protobuf:"bytes,1,opt,name=dateCreditFileEstbed,proto3" json:"dateCreditFileEstbed,omitempty"`
	OldestOpeningDateOfTrade    *DateFirstAtAddress    `protobuf:"bytes,2,opt,name=oldestOpeningDateOfTrade,proto3" json:"oldestOpeningDateOfTrade,omitempty"`
	LatestReportingDateOfTrade  *DateFirstAtAddress    `protobuf:"bytes,3,opt,name=latestReportingDateOfTrade,proto3" json:"latestReportingDateOfTrade,omitempty"`
	DateOfLatestFileActivity    *DateFirstAtAddress    `protobuf:"bytes,4,opt,name=dateOfLatestFileActivity,proto3" json:"dateOfLatestFileActivity,omitempty"`
	ReportIncldsCollectionItems bool                   `protobuf:"varint,5,opt,name=reportIncldsCollectionItems,proto3" json:"reportIncldsCollectionItems,omitempty"`
	HighCreditRangeLowAmount    uint32                 `protobuf:"varint,6,opt,name=highCreditRangeLowAmount,proto3" json:"highCreditRangeLowAmount,omitempty"`
	HighCreditRangeHighAmount   uint32                 `protobuf:"varint,7,opt,name=highCreditRangeHighAmount,proto3" json:"highCreditRangeHighAmount,omitempty"`
	TotalNumberOfTradeLines     uint32                 `protobuf:"varint,8,opt,name=totalNumberOfTradeLines,proto3" json:"totalNumberOfTradeLines,omitempty"`
	CurrentStatusAccounts       *CurrentStatusAccounts `protobuf:"bytes,9,opt,name=currentStatusAccounts,proto3" json:"currentStatusAccounts,omitempty"`
	HistoryStatusAccounts       *HistoryStatusAccounts `protobuf:"bytes,10,opt,name=historyStatusAccounts,proto3" json:"historyStatusAccounts,omitempty"`
	HighCreditTotalRevolving    uint32                 `protobuf:"varint,11,opt,name=highCreditTotalRevolving,proto3" json:"highCreditTotalRevolving,omitempty"`
	HighCreditOpenended         uint32                 `protobuf:"varint,12,opt,name=highCreditOpenended,proto3" json:"highCreditOpenended,omitempty"`
	HighCreditInstallment       uint32                 `protobuf:"varint,13,opt,name=highCreditInstallment,proto3" json:"highCreditInstallment,omitempty"`
	AmountOwedTotalRevolving    uint32                 `protobuf:"varint,14,opt,name=amountOwedTotalRevolving,proto3" json:"amountOwedTotalRevolving,omitempty"`
	AmountOwedTotalOpenended    uint32                 `protobuf:"varint,15,opt,name=amountOwedTotalOpenended,proto3" json:"amountOwedTotalOpenended,omitempty"`
	AmountOwedTotalInstallment  uint32                 `protobuf:"varint,16,opt,name=amountOwedTotalInstallment,proto3" json:"amountOwedTotalInstallment,omitempty"`
	PastDueTotalRevolving       uint32                 `protobuf:"varint,17,opt,name=pastDueTotalRevolving,proto3" json:"pastDueTotalRevolving,omitempty"`
	PastDueTotalOpenended       uint32                 `protobuf:"varint,18,opt,name=pastDueTotalOpenended,proto3" json:"pastDueTotalOpenended,omitempty"`
	PastDueTotalInstallment     uint32                 `protobuf:"varint,19,opt,name=pastDueTotalInstallment,proto3" json:"pastDueTotalInstallment,omitempty"`
	NumberOfRevolvingAccounts   uint32                 `protobuf:"varint,20,opt,name=numberOfRevolvingAccounts,proto3" json:"numberOfRevolvingAccounts,omitempty"`
	NumberOfInstallmentAccounts uint32                 `protobuf:"varint,21,opt,name=numberOfInstallmentAccounts,proto3" json:"numberOfInstallmentAccounts,omitempty"`
}

func (x *CreditReportSummary) Reset() {
	*x = CreditReportSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditReportSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditReportSummary) ProtoMessage() {}

func (x *CreditReportSummary) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditReportSummary.ProtoReflect.Descriptor instead.
func (*CreditReportSummary) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{23}
}

func (x *CreditReportSummary) GetDateCreditFileEstbed() *DateCreditFileEstbed {
	if x != nil {
		return x.DateCreditFileEstbed
	}
	return nil
}

func (x *CreditReportSummary) GetOldestOpeningDateOfTrade() *DateFirstAtAddress {
	if x != nil {
		return x.OldestOpeningDateOfTrade
	}
	return nil
}

func (x *CreditReportSummary) GetLatestReportingDateOfTrade() *DateFirstAtAddress {
	if x != nil {
		return x.LatestReportingDateOfTrade
	}
	return nil
}

func (x *CreditReportSummary) GetDateOfLatestFileActivity() *DateFirstAtAddress {
	if x != nil {
		return x.DateOfLatestFileActivity
	}
	return nil
}

func (x *CreditReportSummary) GetReportIncldsCollectionItems() bool {
	if x != nil {
		return x.ReportIncldsCollectionItems
	}
	return false
}

func (x *CreditReportSummary) GetHighCreditRangeLowAmount() uint32 {
	if x != nil {
		return x.HighCreditRangeLowAmount
	}
	return 0
}

func (x *CreditReportSummary) GetHighCreditRangeHighAmount() uint32 {
	if x != nil {
		return x.HighCreditRangeHighAmount
	}
	return 0
}

func (x *CreditReportSummary) GetTotalNumberOfTradeLines() uint32 {
	if x != nil {
		return x.TotalNumberOfTradeLines
	}
	return 0
}

func (x *CreditReportSummary) GetCurrentStatusAccounts() *CurrentStatusAccounts {
	if x != nil {
		return x.CurrentStatusAccounts
	}
	return nil
}

func (x *CreditReportSummary) GetHistoryStatusAccounts() *HistoryStatusAccounts {
	if x != nil {
		return x.HistoryStatusAccounts
	}
	return nil
}

func (x *CreditReportSummary) GetHighCreditTotalRevolving() uint32 {
	if x != nil {
		return x.HighCreditTotalRevolving
	}
	return 0
}

func (x *CreditReportSummary) GetHighCreditOpenended() uint32 {
	if x != nil {
		return x.HighCreditOpenended
	}
	return 0
}

func (x *CreditReportSummary) GetHighCreditInstallment() uint32 {
	if x != nil {
		return x.HighCreditInstallment
	}
	return 0
}

func (x *CreditReportSummary) GetAmountOwedTotalRevolving() uint32 {
	if x != nil {
		return x.AmountOwedTotalRevolving
	}
	return 0
}

func (x *CreditReportSummary) GetAmountOwedTotalOpenended() uint32 {
	if x != nil {
		return x.AmountOwedTotalOpenended
	}
	return 0
}

func (x *CreditReportSummary) GetAmountOwedTotalInstallment() uint32 {
	if x != nil {
		return x.AmountOwedTotalInstallment
	}
	return 0
}

func (x *CreditReportSummary) GetPastDueTotalRevolving() uint32 {
	if x != nil {
		return x.PastDueTotalRevolving
	}
	return 0
}

func (x *CreditReportSummary) GetPastDueTotalOpenended() uint32 {
	if x != nil {
		return x.PastDueTotalOpenended
	}
	return 0
}

func (x *CreditReportSummary) GetPastDueTotalInstallment() uint32 {
	if x != nil {
		return x.PastDueTotalInstallment
	}
	return 0
}

func (x *CreditReportSummary) GetNumberOfRevolvingAccounts() uint32 {
	if x != nil {
		return x.NumberOfRevolvingAccounts
	}
	return 0
}

func (x *CreditReportSummary) GetNumberOfInstallmentAccounts() uint32 {
	if x != nil {
		return x.NumberOfInstallmentAccounts
	}
	return 0
}

type EmploymentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PositionDesc         string `protobuf:"bytes,1,opt,name=positionDesc,proto3" json:"positionDesc,omitempty"`
	EmployerName         string `protobuf:"bytes,2,opt,name=employerName,proto3" json:"employerName,omitempty"`
	EmploymentRecordType string `protobuf:"bytes,3,opt,name=employmentRecordType,proto3" json:"employmentRecordType,omitempty"`
	Classification       string `protobuf:"bytes,4,opt,name=classification,proto3" json:"classification,omitempty"`
}

func (x *EmploymentInfo) Reset() {
	*x = EmploymentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentInfo) ProtoMessage() {}

func (x *EmploymentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentInfo.ProtoReflect.Descriptor instead.
func (*EmploymentInfo) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{24}
}

func (x *EmploymentInfo) GetPositionDesc() string {
	if x != nil {
		return x.PositionDesc
	}
	return ""
}

func (x *EmploymentInfo) GetEmployerName() string {
	if x != nil {
		return x.EmployerName
	}
	return ""
}

func (x *EmploymentInfo) GetEmploymentRecordType() string {
	if x != nil {
		return x.EmploymentRecordType
	}
	return ""
}

func (x *EmploymentInfo) GetClassification() string {
	if x != nil {
		return x.Classification
	}
	return ""
}

type EmploymentInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmploymentInfo []*EmploymentInfo `protobuf:"bytes,1,rep,name=employmentInfo,proto3" json:"employmentInfo,omitempty"`
}

func (x *EmploymentInfos) Reset() {
	*x = EmploymentInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentInfos) ProtoMessage() {}

func (x *EmploymentInfos) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentInfos.ProtoReflect.Descriptor instead.
func (*EmploymentInfos) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{25}
}

func (x *EmploymentInfos) GetEmploymentInfo() []*EmploymentInfo {
	if x != nil {
		return x.EmploymentInfo
	}
	return nil
}

type CollectionRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateReported                *DateFirstAtAddress `protobuf:"bytes,1,opt,name=dateReported,proto3" json:"dateReported,omitempty"`
	DateAssigned                *DateFirstAtAddress `protobuf:"bytes,2,opt,name=dateAssigned,proto3" json:"dateAssigned,omitempty"`
	ReportingMemberAgencyNumber string              `protobuf:"bytes,3,opt,name=reportingMemberAgencyNumber,proto3" json:"reportingMemberAgencyNumber,omitempty"`
	ClientNameOrNumber          string              `protobuf:"bytes,4,opt,name=clientNameOrNumber,proto3" json:"clientNameOrNumber,omitempty"`
	EcoaCode                    string              `protobuf:"bytes,5,opt,name=ecoaCode,proto3" json:"ecoaCode,omitempty"`
	DateOfLastActivity          *DateFirstAtAddress `protobuf:"bytes,6,opt,name=dateOfLastActivity,proto3" json:"dateOfLastActivity,omitempty"`
	OriginalAmount              uint32              `protobuf:"varint,7,opt,name=originalAmount,proto3" json:"originalAmount,omitempty"`
	DateOfBalance               *DateFirstAtAddress `protobuf:"bytes,8,opt,name=dateOfBalance,proto3" json:"dateOfBalance,omitempty"`
	BalanceAmount               uint32              `protobuf:"varint,9,opt,name=balanceAmount,proto3" json:"balanceAmount,omitempty"`
	StatusDate                  *DateFirstAtAddress `protobuf:"bytes,10,opt,name=statusDate,proto3" json:"statusDate,omitempty"`
	CollectionItemStatus        string              `protobuf:"bytes,11,opt,name=collectionItemStatus,proto3" json:"collectionItemStatus,omitempty"`
}

func (x *CollectionRecord) Reset() {
	*x = CollectionRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionRecord) ProtoMessage() {}

func (x *CollectionRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionRecord.ProtoReflect.Descriptor instead.
func (*CollectionRecord) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{26}
}

func (x *CollectionRecord) GetDateReported() *DateFirstAtAddress {
	if x != nil {
		return x.DateReported
	}
	return nil
}

func (x *CollectionRecord) GetDateAssigned() *DateFirstAtAddress {
	if x != nil {
		return x.DateAssigned
	}
	return nil
}

func (x *CollectionRecord) GetReportingMemberAgencyNumber() string {
	if x != nil {
		return x.ReportingMemberAgencyNumber
	}
	return ""
}

func (x *CollectionRecord) GetClientNameOrNumber() string {
	if x != nil {
		return x.ClientNameOrNumber
	}
	return ""
}

func (x *CollectionRecord) GetEcoaCode() string {
	if x != nil {
		return x.EcoaCode
	}
	return ""
}

func (x *CollectionRecord) GetDateOfLastActivity() *DateFirstAtAddress {
	if x != nil {
		return x.DateOfLastActivity
	}
	return nil
}

func (x *CollectionRecord) GetOriginalAmount() uint32 {
	if x != nil {
		return x.OriginalAmount
	}
	return 0
}

func (x *CollectionRecord) GetDateOfBalance() *DateFirstAtAddress {
	if x != nil {
		return x.DateOfBalance
	}
	return nil
}

func (x *CollectionRecord) GetBalanceAmount() uint32 {
	if x != nil {
		return x.BalanceAmount
	}
	return 0
}

func (x *CollectionRecord) GetStatusDate() *DateFirstAtAddress {
	if x != nil {
		return x.StatusDate
	}
	return nil
}

func (x *CollectionRecord) GetCollectionItemStatus() string {
	if x != nil {
		return x.CollectionItemStatus
	}
	return ""
}

type CollectionRecords struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectionRecord []*CollectionRecord `protobuf:"bytes,1,rep,name=collectionRecord,proto3" json:"collectionRecord,omitempty"`
}

func (x *CollectionRecords) Reset() {
	*x = CollectionRecords{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionRecords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionRecords) ProtoMessage() {}

func (x *CollectionRecords) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionRecords.ProtoReflect.Descriptor instead.
func (*CollectionRecords) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{27}
}

func (x *CollectionRecords) GetCollectionRecord() []*CollectionRecord {
	if x != nil {
		return x.CollectionRecord
	}
	return nil
}

type CollectionRecordsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectionRecords *CollectionRecords `protobuf:"bytes,1,opt,name=collectionRecords,proto3" json:"collectionRecords,omitempty"`
}

func (x *CollectionRecordsInfo) Reset() {
	*x = CollectionRecordsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionRecordsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionRecordsInfo) ProtoMessage() {}

func (x *CollectionRecordsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionRecordsInfo.ProtoReflect.Descriptor instead.
func (*CollectionRecordsInfo) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{28}
}

func (x *CollectionRecordsInfo) GetCollectionRecords() *CollectionRecords {
	if x != nil {
		return x.CollectionRecords
	}
	return nil
}

type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{29}
}

func (x *Message) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Message) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MessagesList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message []*Message `protobuf:"bytes,1,rep,name=message,proto3" json:"message,omitempty"`
}

func (x *MessagesList) Reset() {
	*x = MessagesList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessagesList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessagesList) ProtoMessage() {}

func (x *MessagesList) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessagesList.ProtoReflect.Descriptor instead.
func (*MessagesList) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{30}
}

func (x *MessagesList) GetMessage() []*Message {
	if x != nil {
		return x.Message
	}
	return nil
}

type CreditTradeHistoryRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportingMemberNumber string              `protobuf:"bytes,1,opt,name=reportingMemberNumber,proto3" json:"reportingMemberNumber,omitempty"`
	MemberName            string              `protobuf:"bytes,2,opt,name=memberName,proto3" json:"memberName,omitempty"`
	TapeSupplierIndicator string              `protobuf:"bytes,3,opt,name=tapeSupplierIndicator,proto3" json:"tapeSupplierIndicator,omitempty"`
	DateReported          *DateFirstAtAddress `protobuf:"bytes,4,opt,name=dateReported,proto3" json:"dateReported,omitempty"`
	DateAccountOpened     *DateFirstAtAddress `protobuf:"bytes,5,opt,name=dateAccountOpened,proto3" json:"dateAccountOpened,omitempty"`
	HighestCreditAmount   uint32              `protobuf:"varint,6,opt,name=highestCreditAmount,proto3" json:"highestCreditAmount,omitempty"`
	AccountBalance        uint32              `protobuf:"varint,7,opt,name=accountBalance,proto3" json:"accountBalance,omitempty"`
	PastDueAmount         uint32              `protobuf:"varint,8,opt,name=pastDueAmount,proto3" json:"pastDueAmount,omitempty"`
	AccountTypeCode       string              `protobuf:"bytes,9,opt,name=accountTypeCode,proto3" json:"accountTypeCode,omitempty"`
	CurrentRateCode       string              `protobuf:"bytes,10,opt,name=currentRateCode,proto3" json:"currentRateCode,omitempty"`
	MonthsReviewed        string              `protobuf:"bytes,11,opt,name=monthsReviewed,proto3" json:"monthsReviewed,omitempty"`
	AccountDesignatorCode string              `protobuf:"bytes,12,opt,name=accountDesignatorCode,proto3" json:"accountDesignatorCode,omitempty"`
	ThirtydayCounter      string              `protobuf:"bytes,13,opt,name=thirtydayCounter,proto3" json:"thirtydayCounter,omitempty"`
	SixtydayCounter       string              `protobuf:"bytes,14,opt,name=sixtydayCounter,proto3" json:"sixtydayCounter,omitempty"`
	NinetydayCounter      string              `protobuf:"bytes,15,opt,name=ninetydayCounter,proto3" json:"ninetydayCounter,omitempty"`
	PrevRateCode1         string              `protobuf:"bytes,16,opt,name=prevRateCode1,proto3" json:"prevRateCode1,omitempty"`
	PrevRateDate1         *DateFirstAtAddress `protobuf:"bytes,17,opt,name=prevRateDate1,proto3" json:"prevRateDate1,omitempty"`
	PrevRateCode2         string              `protobuf:"bytes,18,opt,name=prevRateCode2,proto3" json:"prevRateCode2,omitempty"`
	PrevRateDate2         *DateFirstAtAddress `protobuf:"bytes,19,opt,name=prevRateDate2,proto3" json:"prevRateDate2,omitempty"`
	PrevRateCode3         string              `protobuf:"bytes,20,opt,name=prevRateCode3,proto3" json:"prevRateCode3,omitempty"`
	PrevRateDate3         *DateFirstAtAddress `protobuf:"bytes,21,opt,name=prevRateDate3,proto3" json:"prevRateDate3,omitempty"`
	DateOfLastActivity    *DateFirstAtAddress `protobuf:"bytes,22,opt,name=dateOfLastActivity,proto3" json:"dateOfLastActivity,omitempty"`
	Messages              *MessagesList       `protobuf:"bytes,23,opt,name=messages,proto3" json:"messages,omitempty"`
}

func (x *CreditTradeHistoryRecord) Reset() {
	*x = CreditTradeHistoryRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditTradeHistoryRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditTradeHistoryRecord) ProtoMessage() {}

func (x *CreditTradeHistoryRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditTradeHistoryRecord.ProtoReflect.Descriptor instead.
func (*CreditTradeHistoryRecord) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{31}
}

func (x *CreditTradeHistoryRecord) GetReportingMemberNumber() string {
	if x != nil {
		return x.ReportingMemberNumber
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetMemberName() string {
	if x != nil {
		return x.MemberName
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetTapeSupplierIndicator() string {
	if x != nil {
		return x.TapeSupplierIndicator
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetDateReported() *DateFirstAtAddress {
	if x != nil {
		return x.DateReported
	}
	return nil
}

func (x *CreditTradeHistoryRecord) GetDateAccountOpened() *DateFirstAtAddress {
	if x != nil {
		return x.DateAccountOpened
	}
	return nil
}

func (x *CreditTradeHistoryRecord) GetHighestCreditAmount() uint32 {
	if x != nil {
		return x.HighestCreditAmount
	}
	return 0
}

func (x *CreditTradeHistoryRecord) GetAccountBalance() uint32 {
	if x != nil {
		return x.AccountBalance
	}
	return 0
}

func (x *CreditTradeHistoryRecord) GetPastDueAmount() uint32 {
	if x != nil {
		return x.PastDueAmount
	}
	return 0
}

func (x *CreditTradeHistoryRecord) GetAccountTypeCode() string {
	if x != nil {
		return x.AccountTypeCode
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetCurrentRateCode() string {
	if x != nil {
		return x.CurrentRateCode
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetMonthsReviewed() string {
	if x != nil {
		return x.MonthsReviewed
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetAccountDesignatorCode() string {
	if x != nil {
		return x.AccountDesignatorCode
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetThirtydayCounter() string {
	if x != nil {
		return x.ThirtydayCounter
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetSixtydayCounter() string {
	if x != nil {
		return x.SixtydayCounter
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetNinetydayCounter() string {
	if x != nil {
		return x.NinetydayCounter
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetPrevRateCode1() string {
	if x != nil {
		return x.PrevRateCode1
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetPrevRateDate1() *DateFirstAtAddress {
	if x != nil {
		return x.PrevRateDate1
	}
	return nil
}

func (x *CreditTradeHistoryRecord) GetPrevRateCode2() string {
	if x != nil {
		return x.PrevRateCode2
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetPrevRateDate2() *DateFirstAtAddress {
	if x != nil {
		return x.PrevRateDate2
	}
	return nil
}

func (x *CreditTradeHistoryRecord) GetPrevRateCode3() string {
	if x != nil {
		return x.PrevRateCode3
	}
	return ""
}

func (x *CreditTradeHistoryRecord) GetPrevRateDate3() *DateFirstAtAddress {
	if x != nil {
		return x.PrevRateDate3
	}
	return nil
}

func (x *CreditTradeHistoryRecord) GetDateOfLastActivity() *DateFirstAtAddress {
	if x != nil {
		return x.DateOfLastActivity
	}
	return nil
}

func (x *CreditTradeHistoryRecord) GetMessages() *MessagesList {
	if x != nil {
		return x.Messages
	}
	return nil
}

type CreditTradeHistoryRecords struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditTradeHistoryRecord []*CreditTradeHistoryRecord `protobuf:"bytes,1,rep,name=creditTradeHistoryRecord,proto3" json:"creditTradeHistoryRecord,omitempty"`
}

func (x *CreditTradeHistoryRecords) Reset() {
	*x = CreditTradeHistoryRecords{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditTradeHistoryRecords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditTradeHistoryRecords) ProtoMessage() {}

func (x *CreditTradeHistoryRecords) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditTradeHistoryRecords.ProtoReflect.Descriptor instead.
func (*CreditTradeHistoryRecords) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{32}
}

func (x *CreditTradeHistoryRecords) GetCreditTradeHistoryRecord() []*CreditTradeHistoryRecord {
	if x != nil {
		return x.CreditTradeHistoryRecord
	}
	return nil
}

type TradeAccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditTradeHistoryRecords *CreditTradeHistoryRecords `protobuf:"bytes,1,opt,name=creditTradeHistoryRecords,proto3" json:"creditTradeHistoryRecords,omitempty"`
}

func (x *TradeAccountInfo) Reset() {
	*x = TradeAccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TradeAccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TradeAccountInfo) ProtoMessage() {}

func (x *TradeAccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TradeAccountInfo.ProtoReflect.Descriptor instead.
func (*TradeAccountInfo) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{33}
}

func (x *TradeAccountInfo) GetCreditTradeHistoryRecords() *CreditTradeHistoryRecords {
	if x != nil {
		return x.CreditTradeHistoryRecords
	}
	return nil
}

type DateOfInquiry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month uint32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	Day   uint32 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
}

func (x *DateOfInquiry) Reset() {
	*x = DateOfInquiry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateOfInquiry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateOfInquiry) ProtoMessage() {}

func (x *DateOfInquiry) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateOfInquiry.ProtoReflect.Descriptor instead.
func (*DateOfInquiry) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{34}
}

func (x *DateOfInquiry) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *DateOfInquiry) GetMonth() uint32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *DateOfInquiry) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

type InquiryHistoryHeaderRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateOfInquiry *DateOfInquiry `protobuf:"bytes,1,opt,name=dateOfInquiry,proto3" json:"dateOfInquiry,omitempty"`
	InquirerName  string         `protobuf:"bytes,2,opt,name=inquirerName,proto3" json:"inquirerName,omitempty"`
	InquirerId    string         `protobuf:"bytes,3,opt,name=inquirerId,proto3" json:"inquirerId,omitempty"`
}

func (x *InquiryHistoryHeaderRecord) Reset() {
	*x = InquiryHistoryHeaderRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InquiryHistoryHeaderRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InquiryHistoryHeaderRecord) ProtoMessage() {}

func (x *InquiryHistoryHeaderRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InquiryHistoryHeaderRecord.ProtoReflect.Descriptor instead.
func (*InquiryHistoryHeaderRecord) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{35}
}

func (x *InquiryHistoryHeaderRecord) GetDateOfInquiry() *DateOfInquiry {
	if x != nil {
		return x.DateOfInquiry
	}
	return nil
}

func (x *InquiryHistoryHeaderRecord) GetInquirerName() string {
	if x != nil {
		return x.InquirerName
	}
	return ""
}

func (x *InquiryHistoryHeaderRecord) GetInquirerId() string {
	if x != nil {
		return x.InquirerId
	}
	return ""
}

type InquiryHistoryHeaderRecords struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InquiryHistoryHeaderRecord []*InquiryHistoryHeaderRecord `protobuf:"bytes,1,rep,name=inquiryHistoryHeaderRecord,proto3" json:"inquiryHistoryHeaderRecord,omitempty"`
}

func (x *InquiryHistoryHeaderRecords) Reset() {
	*x = InquiryHistoryHeaderRecords{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InquiryHistoryHeaderRecords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InquiryHistoryHeaderRecords) ProtoMessage() {}

func (x *InquiryHistoryHeaderRecords) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InquiryHistoryHeaderRecords.ProtoReflect.Descriptor instead.
func (*InquiryHistoryHeaderRecords) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{36}
}

func (x *InquiryHistoryHeaderRecords) GetInquiryHistoryHeaderRecord() []*InquiryHistoryHeaderRecord {
	if x != nil {
		return x.InquiryHistoryHeaderRecord
	}
	return nil
}

type InquiryHistoryHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InquiryHistoryHeaderRecords *InquiryHistoryHeaderRecords `protobuf:"bytes,1,opt,name=inquiryHistoryHeaderRecords,proto3" json:"inquiryHistoryHeaderRecords,omitempty"`
}

func (x *InquiryHistoryHeader) Reset() {
	*x = InquiryHistoryHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InquiryHistoryHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InquiryHistoryHeader) ProtoMessage() {}

func (x *InquiryHistoryHeader) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InquiryHistoryHeader.ProtoReflect.Descriptor instead.
func (*InquiryHistoryHeader) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{37}
}

func (x *InquiryHistoryHeader) GetInquiryHistoryHeaderRecords() *InquiryHistoryHeaderRecords {
	if x != nil {
		return x.InquiryHistoryHeaderRecords
	}
	return nil
}

type NcfProductReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubjectInfo           *SubjectInfo           `protobuf:"bytes,1,opt,name=subjectInfo,proto3" json:"subjectInfo,omitempty"`
	CreditReportSummary   *CreditReportSummary   `protobuf:"bytes,2,opt,name=creditReportSummary,proto3" json:"creditReportSummary,omitempty"`
	EmploymentInfos       *EmploymentInfos       `protobuf:"bytes,3,opt,name=employmentInfos,proto3" json:"employmentInfos,omitempty"`
	CollectionRecordsInfo *CollectionRecordsInfo `protobuf:"bytes,4,opt,name=collectionRecordsInfo,proto3" json:"collectionRecordsInfo,omitempty"`
	TradeAccountInfo      *TradeAccountInfo      `protobuf:"bytes,5,opt,name=tradeAccountInfo,proto3" json:"tradeAccountInfo,omitempty"`
	InquiryHistoryHeader  *InquiryHistoryHeader  `protobuf:"bytes,6,opt,name=inquiryHistoryHeader,proto3" json:"inquiryHistoryHeader,omitempty"`
}

func (x *NcfProductReport) Reset() {
	*x = NcfProductReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NcfProductReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NcfProductReport) ProtoMessage() {}

func (x *NcfProductReport) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NcfProductReport.ProtoReflect.Descriptor instead.
func (*NcfProductReport) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{38}
}

func (x *NcfProductReport) GetSubjectInfo() *SubjectInfo {
	if x != nil {
		return x.SubjectInfo
	}
	return nil
}

func (x *NcfProductReport) GetCreditReportSummary() *CreditReportSummary {
	if x != nil {
		return x.CreditReportSummary
	}
	return nil
}

func (x *NcfProductReport) GetEmploymentInfos() *EmploymentInfos {
	if x != nil {
		return x.EmploymentInfos
	}
	return nil
}

func (x *NcfProductReport) GetCollectionRecordsInfo() *CollectionRecordsInfo {
	if x != nil {
		return x.CollectionRecordsInfo
	}
	return nil
}

func (x *NcfProductReport) GetTradeAccountInfo() *TradeAccountInfo {
	if x != nil {
		return x.TradeAccountInfo
	}
	return nil
}

func (x *NcfProductReport) GetInquiryHistoryHeader() *InquiryHistoryHeader {
	if x != nil {
		return x.InquiryHistoryHeader
	}
	return nil
}

type NcfReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NcfProductReport *NcfProductReport `protobuf:"bytes,1,opt,name=ncfProductReport,proto3" json:"ncfProductReport,omitempty"`
}

func (x *NcfReport) Reset() {
	*x = NcfReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NcfReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NcfReport) ProtoMessage() {}

func (x *NcfReport) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NcfReport.ProtoReflect.Descriptor instead.
func (*NcfReport) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{39}
}

func (x *NcfReport) GetNcfProductReport() *NcfProductReport {
	if x != nil {
		return x.NcfProductReport
	}
	return nil
}

type TransactionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessingStatus string `protobuf:"bytes,1,opt,name=processingStatus,proto3" json:"processingStatus,omitempty"`
}

func (x *TransactionDetails) Reset() {
	*x = TransactionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mvr_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetails) ProtoMessage() {}

func (x *TransactionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_mvr_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetails.ProtoReflect.Descriptor instead.
func (*TransactionDetails) Descriptor() ([]byte, []int) {
	return file_mvr_api_proto_rawDescGZIP(), []int{40}
}

func (x *TransactionDetails) GetProcessingStatus() string {
	if x != nil {
		return x.ProcessingStatus
	}
	return ""
}

var File_mvr_api_proto protoreflect.FileDescriptor

var file_mvr_api_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6d, 0x76, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x03, 0x6d, 0x76, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x02, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d,
	0x56, 0x52, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x6c, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6c, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x2c, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x1c, 0x0a,
	0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64,
	0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x6c,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x2e, 0x0a, 0x12, 0x6f,
	0x6e, 0x6c, 0x79, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6f, 0x6e, 0x6c, 0x79, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68, 0x65, 0x22, 0x30, 0x0a, 0x10, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x44, 0x22, 0xfd, 0x02,
	0x0a, 0x09, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x76,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x6f, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x34, 0x0a, 0x15, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x2e, 0x0a,
	0x0e, 0x50, 0x6f, 0x6c, 0x6c, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x44, 0x22, 0xa4, 0x09,
	0x0a, 0x06, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x32,
	0x0a, 0x14, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x74, 0x72, 0x65,
	0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x53, 0x74, 0x72, 0x65, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x6d, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f,
	0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x0a, 0x12, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x12, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x76, 0x72, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x76, 0x72, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5a, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x5a, 0x69, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x46, 0x6c,
	0x61, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x73, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x73, 0x73, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x70, 0x70, 0x61, 0x46, 0x6c, 0x61, 0x67,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x70, 0x70, 0x61, 0x46, 0x6c, 0x61, 0x67,
	0x12, 0x2a, 0x0a, 0x10, 0x64, 0x6d, 0x76, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6d, 0x76, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x03,
	0x64, 0x6f, 0x62, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x79, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x79, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x68, 0x61, 0x69, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x68, 0x61, 0x69, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x69, 0x63, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x69, 0x63, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x63, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x69, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x49, 0x73,
	0x73, 0x75, 0x65, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x49, 0x73, 0x73, 0x75,
	0x65, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73,
	0x12, 0x40, 0x0a, 0x0d, 0x6d, 0x76, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6d, 0x76, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x73, 0x63, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x73, 0x63,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2e, 0x0a, 0x0a, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x76, 0x72,
	0x2e, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x76, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x44, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x35, 0x0a, 0x11, 0x52, 0x61, 0x77, 0x56, 0x65, 0x72, 0x69, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x73, 0x58, 0x4d, 0x4c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x58, 0x4d, 0x4c, 0x22, 0x9a, 0x02, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x03,
	0x64, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0x41, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41,
	0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x61, 0x77, 0x58, 0x6d,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x73, 0x58, 0x4d, 0x4c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x58, 0x4d, 0x4c, 0x22, 0x6d, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x74,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x4d, 0x69, 0x73, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x4d, 0x69, 0x73, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xed, 0x02, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x64,
	0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12,
	0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x53, 0x53, 0x4e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64,
	0x53, 0x53, 0x4e, 0x12, 0x25, 0x0a, 0x0b, 0x73, 0x73, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f,
	0x75, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x73, 0x6e, 0x4c,
	0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73,
	0x73, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x22, 0x5d, 0x0a, 0x07, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x7a, 0x69, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x7a, 0x69, 0x70, 0x22, 0x47, 0x0a, 0x23, 0x47, 0x65, 0x74,
	0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x61, 0x77, 0x58, 0x6d, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x58, 0x4d, 0x4c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x58,
	0x4d, 0x4c, 0x22, 0xc6, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x09, 0x4e, 0x63, 0x66, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x4e, 0x63,
	0x66, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x09, 0x4e, 0x63, 0x66, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64,
	0x4d, 0x69, 0x73, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x4d, 0x69, 0x73, 0x63, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x47, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x60, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x72, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x72, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x69, 0x64,
	0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x69, 0x64, 0x64, 0x6c,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x22, 0x19, 0x0a,
	0x03, 0x44, 0x6f, 0x62, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x22, 0xb8, 0x02, 0x0a, 0x07, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x6d, 0x76, 0x72,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x73, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x73, 0x6e, 0x12, 0x1a, 0x0a,
	0x03, 0x64, 0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x6d, 0x76, 0x72,
	0x2e, 0x44, 0x6f, 0x62, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x46, 0x65, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x46, 0x65, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x49, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x49, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x68, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x30, 0x0a, 0x13, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x3e, 0x0a, 0x12, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74,
	0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x22, 0x8d, 0x02, 0x0a, 0x0e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74,
	0x72, 0x65, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74,
	0x72, 0x65, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x69, 0x70, 0x35, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x69, 0x70, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x69, 0x70, 0x34,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x69, 0x70, 0x34, 0x12, 0x47, 0x0a, 0x12,
	0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x12, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x26, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x3b, 0x0a, 0x0e, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x52, 0x0a, 0x14, 0x44, 0x61, 0x74, 0x65, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x73, 0x74, 0x62, 0x65, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x79,
	0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x61, 0x79, 0x22, 0x5a, 0x0a, 0x14, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x66, 0x0a, 0x15, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x12, 0x4d, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x66, 0x0a, 0x15, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x4d, 0x0a, 0x14, 0x68, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x14, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x83, 0x0b, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x4d, 0x0a, 0x14, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x45, 0x73, 0x74, 0x62, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x45, 0x73, 0x74, 0x62, 0x65, 0x64, 0x52, 0x14, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x73, 0x74, 0x62, 0x65, 0x64, 0x12, 0x53,
	0x0a, 0x18, 0x6f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x44,
	0x61, 0x74, 0x65, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74,
	0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x18, 0x6f, 0x6c, 0x64, 0x65, 0x73,
	0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x12, 0x57, 0x0a, 0x1a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x1a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x12, 0x53, 0x0a, 0x18,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x18, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x12, 0x40, 0x0a, 0x1b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x63, 0x6c, 0x64,
	0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e,
	0x63, 0x6c, 0x64, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x3a, 0x0a, 0x18, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x77, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x77, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3c, 0x0a, 0x19, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x48, 0x69, 0x67, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x19, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x48, 0x69, 0x67, 0x68, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a,
	0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x17,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x52, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x50, 0x0a, 0x15, 0x68, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x52, 0x15, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x18, 0x68,
	0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65,
	0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x68,
	0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65,
	0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x13, 0x68, 0x69, 0x67, 0x68, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x4f, 0x70, 0x65, 0x6e, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x68, 0x69, 0x67,
	0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x68, 0x69, 0x67, 0x68, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x3a, 0x0a, 0x18, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x18, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x3a, 0x0a, 0x18, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x70,
	0x65, 0x6e, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x70,
	0x65, 0x6e, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x3e, 0x0a, 0x1a, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1a, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x4f, 0x77, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x15, 0x70, 0x61, 0x73, 0x74, 0x44,
	0x75, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a,
	0x15, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x70, 0x65,
	0x6e, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x70, 0x61,
	0x73, 0x74, 0x44, 0x75, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x17, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x17, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3c, 0x0a,
	0x19, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69,
	0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x19, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76,
	0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x40, 0x0a, 0x1b, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x1b, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0xb4, 0x01,
	0x0a, 0x0e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x73, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4e, 0x0a, 0x0f, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x3b, 0x0a, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0xdd, 0x04, 0x0a, 0x10, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x3b, 0x0a, 0x0c, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d,
	0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x1b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x4f, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x4f, 0x72, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x63, 0x6f, 0x61, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x63, 0x6f, 0x61, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x47, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x61, 0x73, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x12, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x61,
	0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x76, 0x72, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x76,
	0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x32, 0x0a, 0x14, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74,
	0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x56, 0x0a, 0x11, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x10, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x10, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x5d, 0x0a, 0x15,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x37, 0x0a, 0x07, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x36, 0x0a, 0x0c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x85, 0x09, 0x0a,
	0x18, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x34, 0x0a, 0x15, 0x74, 0x61, 0x70, 0x65, 0x53, 0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x49,
	0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x74, 0x61, 0x70, 0x65, 0x53, 0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x76,
	0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x12, 0x45, 0x0a, 0x11, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x11, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x68, 0x69, 0x67,
	0x68, 0x65, 0x73, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x68, 0x69, 0x67, 0x68, 0x65, 0x73, 0x74, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x73, 0x74, 0x44, 0x75, 0x65, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x70, 0x61, 0x73, 0x74,
	0x44, 0x75, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x74,
	0x68, 0x69, 0x72, 0x74, 0x79, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x68, 0x69, 0x72, 0x74, 0x79, 0x64, 0x61, 0x79,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x69, 0x78, 0x74, 0x79,
	0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x73, 0x69, 0x78, 0x74, 0x79, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65,
	0x72, 0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x69, 0x6e, 0x65, 0x74, 0x79, 0x64, 0x61, 0x79, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x69, 0x6e,
	0x65, 0x74, 0x79, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x24, 0x0a,
	0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x31, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x31, 0x12, 0x3d, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x31, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x76, 0x72,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x31, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x32, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x32, 0x12, 0x3d, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x76,
	0x52, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x32, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x32, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x52,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x33, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x33, 0x12, 0x3d, 0x0a,
	0x0d, 0x70, 0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x33, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0d, 0x70,
	0x72, 0x65, 0x76, 0x52, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x33, 0x12, 0x47, 0x0a, 0x12,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x12, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x2d, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x22, 0x76, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x59, 0x0a, 0x18, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x18, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x70, 0x0a, 0x10,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x5c, 0x0a, 0x19, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x52, 0x19, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x4b,
	0x0a, 0x0d, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x79,
	0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x61, 0x79, 0x22, 0x9a, 0x01, 0x0a, 0x1a,
	0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x38, 0x0a, 0x0d, 0x64, 0x61,
	0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e,
	0x71, 0x75, 0x69, 0x72, 0x79, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x71,
	0x75, 0x69, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x49, 0x64, 0x22, 0x7e, 0x0a, 0x1b, 0x49, 0x6e, 0x71, 0x75,
	0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x5f, 0x0a, 0x1a, 0x69, 0x6e, 0x71, 0x75, 0x69,
	0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x76,
	0x72, 0x2e, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x1a, 0x69, 0x6e,
	0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x7a, 0x0a, 0x14, 0x49, 0x6e, 0x71, 0x75,
	0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x62, 0x0a, 0x1b, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x49, 0x6e, 0x71, 0x75,
	0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x1b, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x22, 0xb6, 0x03, 0x0a, 0x10, 0x4e, 0x63, 0x66, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0b, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4a, 0x0a,
	0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x76, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x3e, 0x0a, 0x0f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x50, 0x0a, 0x15, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x15, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x10, 0x74,
	0x72, 0x61, 0x64, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x74, 0x72,
	0x61, 0x64, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d,
	0x0a, 0x14, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d,
	0x76, 0x72, 0x2e, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x14, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x4e, 0x0a,
	0x09, 0x4e, 0x63, 0x66, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x41, 0x0a, 0x10, 0x6e, 0x63,
	0x66, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x4e, 0x63, 0x66, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x10, 0x6e, 0x63, 0x66,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x40, 0x0a,
	0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32,
	0xb2, 0x04, 0x0a, 0x03, 0x4d, 0x56, 0x52, 0x12, 0x39, 0x0a, 0x08, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x4d, 0x56, 0x52, 0x12, 0x14, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4d,
	0x56, 0x52, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x6d, 0x76, 0x72, 0x2e,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x2d, 0x0a, 0x07, 0x50, 0x6f, 0x6c, 0x6c, 0x4d, 0x56, 0x52, 0x12, 0x13, 0x2e,
	0x6d, 0x76, 0x72, 0x2e, 0x50, 0x6f, 0x6c, 0x6c, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x0b, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22,
	0x00, 0x12, 0x46, 0x0a, 0x15, 0x50, 0x6f, 0x6c, 0x6c, 0x52, 0x61, 0x77, 0x56, 0x65, 0x72, 0x69,
	0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x13, 0x2e, 0x6d, 0x76, 0x72,
	0x2e, 0x50, 0x6f, 0x6c, 0x6c, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x52, 0x61, 0x77, 0x56, 0x65, 0x72, 0x69, 0x73, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x61, 0x77, 0x58,
	0x6d, 0x6c, 0x12, 0x1b, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x61, 0x77, 0x58, 0x6d, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x74, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1b, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x1a, 0x47, 0x65, 0x4e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x61, 0x77,
	0x58, 0x6d, 0x6c, 0x12, 0x21, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x61, 0x77, 0x58, 0x6d, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x60, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x21, 0x2e, 0x6d, 0x76,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x6d, 0x76, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_mvr_api_proto_rawDescOnce sync.Once
	file_mvr_api_proto_rawDescData = file_mvr_api_proto_rawDesc
)

func file_mvr_api_proto_rawDescGZIP() []byte {
	file_mvr_api_proto_rawDescOnce.Do(func() {
		file_mvr_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_mvr_api_proto_rawDescData)
	})
	return file_mvr_api_proto_rawDescData
}

var file_mvr_api_proto_msgTypes = make([]protoimpl.MessageInfo, 41)
var file_mvr_api_proto_goTypes = []interface{}{
	(*FetchMVRRequest)(nil),                     // 0: mvr.FetchMVRRequest
	(*FetchMVRResponse)(nil),                    // 1: mvr.FetchMVRResponse
	(*Violation)(nil),                           // 2: mvr.Violation
	(*PollMVRRequest)(nil),                      // 3: mvr.PollMVRRequest
	(*Report)(nil),                              // 4: mvr.Report
	(*RawVeriskResponse)(nil),                   // 5: mvr.RawVeriskResponse
	(*GetAttractScoreRequest)(nil),              // 6: mvr.GetAttractScoreRequest
	(*GetAttractScoreRawXmlResponse)(nil),       // 7: mvr.GetAttractScoreRawXmlResponse
	(*GetAttractScoreResponse)(nil),             // 8: mvr.GetAttractScoreResponse
	(*GetNationalCreditFileRequest)(nil),        // 9: mvr.GetNationalCreditFileRequest
	(*Address)(nil),                             // 10: mvr.Address
	(*GetNationalCreditFileRawXmlResponse)(nil), // 11: mvr.GetNationalCreditFileRawXmlResponse
	(*GetNationalCreditFileResponse)(nil),       // 12: mvr.GetNationalCreditFileResponse
	(*Name)(nil),                                // 13: mvr.Name
	(*Dob)(nil),                                 // 14: mvr.Dob
	(*Subject)(nil),                             // 15: mvr.Subject
	(*DateFirstAtAddress)(nil),                  // 16: mvr.DateFirstAtAddress
	(*CurrentAddress)(nil),                      // 17: mvr.CurrentAddress
	(*SubjectInfo)(nil),                         // 18: mvr.SubjectInfo
	(*DateCreditFileEstbed)(nil),                // 19: mvr.DateCreditFileEstbed
	(*CurrentStatusAccount)(nil),                // 20: mvr.CurrentStatusAccount
	(*CurrentStatusAccounts)(nil),               // 21: mvr.CurrentStatusAccounts
	(*HistoryStatusAccounts)(nil),               // 22: mvr.HistoryStatusAccounts
	(*CreditReportSummary)(nil),                 // 23: mvr.CreditReportSummary
	(*EmploymentInfo)(nil),                      // 24: mvr.EmploymentInfo
	(*EmploymentInfos)(nil),                     // 25: mvr.EmploymentInfos
	(*CollectionRecord)(nil),                    // 26: mvr.CollectionRecord
	(*CollectionRecords)(nil),                   // 27: mvr.CollectionRecords
	(*CollectionRecordsInfo)(nil),               // 28: mvr.CollectionRecordsInfo
	(*Message)(nil),                             // 29: mvr.Message
	(*MessagesList)(nil),                        // 30: mvr.MessagesList
	(*CreditTradeHistoryRecord)(nil),            // 31: mvr.CreditTradeHistoryRecord
	(*CreditTradeHistoryRecords)(nil),           // 32: mvr.CreditTradeHistoryRecords
	(*TradeAccountInfo)(nil),                    // 33: mvr.TradeAccountInfo
	(*DateOfInquiry)(nil),                       // 34: mvr.DateOfInquiry
	(*InquiryHistoryHeaderRecord)(nil),          // 35: mvr.InquiryHistoryHeaderRecord
	(*InquiryHistoryHeaderRecords)(nil),         // 36: mvr.InquiryHistoryHeaderRecords
	(*InquiryHistoryHeader)(nil),                // 37: mvr.InquiryHistoryHeader
	(*NcfProductReport)(nil),                    // 38: mvr.NcfProductReport
	(*NcfReport)(nil),                           // 39: mvr.NcfReport
	(*TransactionDetails)(nil),                  // 40: mvr.TransactionDetails
	(*timestamppb.Timestamp)(nil),               // 41: google.protobuf.Timestamp
}
var file_mvr_api_proto_depIdxs = []int32{
	41, // 0: mvr.FetchMVRRequest.dob:type_name -> google.protobuf.Timestamp
	41, // 1: mvr.Violation.violationDate:type_name -> google.protobuf.Timestamp
	41, // 2: mvr.Violation.convictionDate:type_name -> google.protobuf.Timestamp
	41, // 3: mvr.Report.dob:type_name -> google.protobuf.Timestamp
	41, // 4: mvr.Report.dateIssued:type_name -> google.protobuf.Timestamp
	41, // 5: mvr.Report.dateExpires:type_name -> google.protobuf.Timestamp
	41, // 6: mvr.Report.mvrReportDate:type_name -> google.protobuf.Timestamp
	2,  // 7: mvr.Report.violations:type_name -> mvr.Violation
	41, // 8: mvr.GetAttractScoreRequest.dob:type_name -> google.protobuf.Timestamp
	41, // 9: mvr.GetNationalCreditFileRequest.dob:type_name -> google.protobuf.Timestamp
	10, // 10: mvr.GetNationalCreditFileRequest.address:type_name -> mvr.Address
	39, // 11: mvr.GetNationalCreditFileResponse.NcfReport:type_name -> mvr.NcfReport
	40, // 12: mvr.GetNationalCreditFileResponse.TransactionDetails:type_name -> mvr.TransactionDetails
	13, // 13: mvr.Subject.name:type_name -> mvr.Name
	14, // 14: mvr.Subject.dob:type_name -> mvr.Dob
	16, // 15: mvr.CurrentAddress.dateFirstAtAddress:type_name -> mvr.DateFirstAtAddress
	15, // 16: mvr.SubjectInfo.subject:type_name -> mvr.Subject
	17, // 17: mvr.SubjectInfo.currentAddress:type_name -> mvr.CurrentAddress
	20, // 18: mvr.CurrentStatusAccounts.currentStatusAccount:type_name -> mvr.CurrentStatusAccount
	20, // 19: mvr.HistoryStatusAccounts.historyStatusAccount:type_name -> mvr.CurrentStatusAccount
	19, // 20: mvr.CreditReportSummary.dateCreditFileEstbed:type_name -> mvr.DateCreditFileEstbed
	16, // 21: mvr.CreditReportSummary.oldestOpeningDateOfTrade:type_name -> mvr.DateFirstAtAddress
	16, // 22: mvr.CreditReportSummary.latestReportingDateOfTrade:type_name -> mvr.DateFirstAtAddress
	16, // 23: mvr.CreditReportSummary.dateOfLatestFileActivity:type_name -> mvr.DateFirstAtAddress
	21, // 24: mvr.CreditReportSummary.currentStatusAccounts:type_name -> mvr.CurrentStatusAccounts
	22, // 25: mvr.CreditReportSummary.historyStatusAccounts:type_name -> mvr.HistoryStatusAccounts
	24, // 26: mvr.EmploymentInfos.employmentInfo:type_name -> mvr.EmploymentInfo
	16, // 27: mvr.CollectionRecord.dateReported:type_name -> mvr.DateFirstAtAddress
	16, // 28: mvr.CollectionRecord.dateAssigned:type_name -> mvr.DateFirstAtAddress
	16, // 29: mvr.CollectionRecord.dateOfLastActivity:type_name -> mvr.DateFirstAtAddress
	16, // 30: mvr.CollectionRecord.dateOfBalance:type_name -> mvr.DateFirstAtAddress
	16, // 31: mvr.CollectionRecord.statusDate:type_name -> mvr.DateFirstAtAddress
	26, // 32: mvr.CollectionRecords.collectionRecord:type_name -> mvr.CollectionRecord
	27, // 33: mvr.CollectionRecordsInfo.collectionRecords:type_name -> mvr.CollectionRecords
	29, // 34: mvr.MessagesList.message:type_name -> mvr.Message
	16, // 35: mvr.CreditTradeHistoryRecord.dateReported:type_name -> mvr.DateFirstAtAddress
	16, // 36: mvr.CreditTradeHistoryRecord.dateAccountOpened:type_name -> mvr.DateFirstAtAddress
	16, // 37: mvr.CreditTradeHistoryRecord.prevRateDate1:type_name -> mvr.DateFirstAtAddress
	16, // 38: mvr.CreditTradeHistoryRecord.prevRateDate2:type_name -> mvr.DateFirstAtAddress
	16, // 39: mvr.CreditTradeHistoryRecord.prevRateDate3:type_name -> mvr.DateFirstAtAddress
	16, // 40: mvr.CreditTradeHistoryRecord.dateOfLastActivity:type_name -> mvr.DateFirstAtAddress
	30, // 41: mvr.CreditTradeHistoryRecord.messages:type_name -> mvr.MessagesList
	31, // 42: mvr.CreditTradeHistoryRecords.creditTradeHistoryRecord:type_name -> mvr.CreditTradeHistoryRecord
	32, // 43: mvr.TradeAccountInfo.creditTradeHistoryRecords:type_name -> mvr.CreditTradeHistoryRecords
	34, // 44: mvr.InquiryHistoryHeaderRecord.dateOfInquiry:type_name -> mvr.DateOfInquiry
	35, // 45: mvr.InquiryHistoryHeaderRecords.inquiryHistoryHeaderRecord:type_name -> mvr.InquiryHistoryHeaderRecord
	36, // 46: mvr.InquiryHistoryHeader.inquiryHistoryHeaderRecords:type_name -> mvr.InquiryHistoryHeaderRecords
	18, // 47: mvr.NcfProductReport.subjectInfo:type_name -> mvr.SubjectInfo
	23, // 48: mvr.NcfProductReport.creditReportSummary:type_name -> mvr.CreditReportSummary
	25, // 49: mvr.NcfProductReport.employmentInfos:type_name -> mvr.EmploymentInfos
	28, // 50: mvr.NcfProductReport.collectionRecordsInfo:type_name -> mvr.CollectionRecordsInfo
	33, // 51: mvr.NcfProductReport.tradeAccountInfo:type_name -> mvr.TradeAccountInfo
	37, // 52: mvr.NcfProductReport.inquiryHistoryHeader:type_name -> mvr.InquiryHistoryHeader
	38, // 53: mvr.NcfReport.ncfProductReport:type_name -> mvr.NcfProductReport
	0,  // 54: mvr.MVR.FetchMVR:input_type -> mvr.FetchMVRRequest
	3,  // 55: mvr.MVR.PollMVR:input_type -> mvr.PollMVRRequest
	3,  // 56: mvr.MVR.PollRawVeriskResponse:input_type -> mvr.PollMVRRequest
	6,  // 57: mvr.MVR.GetAttractScoreRawXml:input_type -> mvr.GetAttractScoreRequest
	6,  // 58: mvr.MVR.GetAttractScore:input_type -> mvr.GetAttractScoreRequest
	9,  // 59: mvr.MVR.GeNationalCreditFileRawXml:input_type -> mvr.GetNationalCreditFileRequest
	9,  // 60: mvr.MVR.GetNationalCreditFile:input_type -> mvr.GetNationalCreditFileRequest
	1,  // 61: mvr.MVR.FetchMVR:output_type -> mvr.FetchMVRResponse
	4,  // 62: mvr.MVR.PollMVR:output_type -> mvr.Report
	5,  // 63: mvr.MVR.PollRawVeriskResponse:output_type -> mvr.RawVeriskResponse
	7,  // 64: mvr.MVR.GetAttractScoreRawXml:output_type -> mvr.GetAttractScoreRawXmlResponse
	8,  // 65: mvr.MVR.GetAttractScore:output_type -> mvr.GetAttractScoreResponse
	11, // 66: mvr.MVR.GeNationalCreditFileRawXml:output_type -> mvr.GetNationalCreditFileRawXmlResponse
	12, // 67: mvr.MVR.GetNationalCreditFile:output_type -> mvr.GetNationalCreditFileResponse
	61, // [61:68] is the sub-list for method output_type
	54, // [54:61] is the sub-list for method input_type
	54, // [54:54] is the sub-list for extension type_name
	54, // [54:54] is the sub-list for extension extendee
	0,  // [0:54] is the sub-list for field type_name
}

func init() { file_mvr_api_proto_init() }
func file_mvr_api_proto_init() {
	if File_mvr_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_mvr_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchMVRRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchMVRResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Violation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollMVRRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Report); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawVeriskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAttractScoreRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAttractScoreRawXmlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAttractScoreResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNationalCreditFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNationalCreditFileRawXmlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNationalCreditFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Name); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Dob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Subject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateFirstAtAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubjectInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateCreditFileEstbed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentStatusAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentStatusAccounts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HistoryStatusAccounts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditReportSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionRecords); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionRecordsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessagesList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditTradeHistoryRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditTradeHistoryRecords); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TradeAccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateOfInquiry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InquiryHistoryHeaderRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InquiryHistoryHeaderRecords); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InquiryHistoryHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NcfProductReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NcfReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mvr_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_mvr_api_proto_msgTypes[9].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_mvr_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   41,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_mvr_api_proto_goTypes,
		DependencyIndexes: file_mvr_api_proto_depIdxs,
		MessageInfos:      file_mvr_api_proto_msgTypes,
	}.Build()
	File_mvr_api_proto = out.File
	file_mvr_api_proto_rawDesc = nil
	file_mvr_api_proto_goTypes = nil
	file_mvr_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// MVRClient is the client API for MVR service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MVRClient interface {
	FetchMVR(ctx context.Context, in *FetchMVRRequest, opts ...grpc.CallOption) (*FetchMVRResponse, error)
	PollMVR(ctx context.Context, in *PollMVRRequest, opts ...grpc.CallOption) (*Report, error)
	PollRawVeriskResponse(ctx context.Context, in *PollMVRRequest, opts ...grpc.CallOption) (*RawVeriskResponse, error)
	GetAttractScoreRawXml(ctx context.Context, in *GetAttractScoreRequest, opts ...grpc.CallOption) (*GetAttractScoreRawXmlResponse, error)
	GetAttractScore(ctx context.Context, in *GetAttractScoreRequest, opts ...grpc.CallOption) (*GetAttractScoreResponse, error)
	GeNationalCreditFileRawXml(ctx context.Context, in *GetNationalCreditFileRequest, opts ...grpc.CallOption) (*GetNationalCreditFileRawXmlResponse, error)
	GetNationalCreditFile(ctx context.Context, in *GetNationalCreditFileRequest, opts ...grpc.CallOption) (*GetNationalCreditFileResponse, error)
}

type mVRClient struct {
	cc grpc.ClientConnInterface
}

func NewMVRClient(cc grpc.ClientConnInterface) MVRClient {
	return &mVRClient{cc}
}

func (c *mVRClient) FetchMVR(ctx context.Context, in *FetchMVRRequest, opts ...grpc.CallOption) (*FetchMVRResponse, error) {
	out := new(FetchMVRResponse)
	err := c.cc.Invoke(ctx, "/mvr.MVR/FetchMVR", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mVRClient) PollMVR(ctx context.Context, in *PollMVRRequest, opts ...grpc.CallOption) (*Report, error) {
	out := new(Report)
	err := c.cc.Invoke(ctx, "/mvr.MVR/PollMVR", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mVRClient) PollRawVeriskResponse(ctx context.Context, in *PollMVRRequest, opts ...grpc.CallOption) (*RawVeriskResponse, error) {
	out := new(RawVeriskResponse)
	err := c.cc.Invoke(ctx, "/mvr.MVR/PollRawVeriskResponse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mVRClient) GetAttractScoreRawXml(ctx context.Context, in *GetAttractScoreRequest, opts ...grpc.CallOption) (*GetAttractScoreRawXmlResponse, error) {
	out := new(GetAttractScoreRawXmlResponse)
	err := c.cc.Invoke(ctx, "/mvr.MVR/GetAttractScoreRawXml", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mVRClient) GetAttractScore(ctx context.Context, in *GetAttractScoreRequest, opts ...grpc.CallOption) (*GetAttractScoreResponse, error) {
	out := new(GetAttractScoreResponse)
	err := c.cc.Invoke(ctx, "/mvr.MVR/GetAttractScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mVRClient) GeNationalCreditFileRawXml(ctx context.Context, in *GetNationalCreditFileRequest, opts ...grpc.CallOption) (*GetNationalCreditFileRawXmlResponse, error) {
	out := new(GetNationalCreditFileRawXmlResponse)
	err := c.cc.Invoke(ctx, "/mvr.MVR/GeNationalCreditFileRawXml", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mVRClient) GetNationalCreditFile(ctx context.Context, in *GetNationalCreditFileRequest, opts ...grpc.CallOption) (*GetNationalCreditFileResponse, error) {
	out := new(GetNationalCreditFileResponse)
	err := c.cc.Invoke(ctx, "/mvr.MVR/GetNationalCreditFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MVRServer is the server API for MVR service.
type MVRServer interface {
	FetchMVR(context.Context, *FetchMVRRequest) (*FetchMVRResponse, error)
	PollMVR(context.Context, *PollMVRRequest) (*Report, error)
	PollRawVeriskResponse(context.Context, *PollMVRRequest) (*RawVeriskResponse, error)
	GetAttractScoreRawXml(context.Context, *GetAttractScoreRequest) (*GetAttractScoreRawXmlResponse, error)
	GetAttractScore(context.Context, *GetAttractScoreRequest) (*GetAttractScoreResponse, error)
	GeNationalCreditFileRawXml(context.Context, *GetNationalCreditFileRequest) (*GetNationalCreditFileRawXmlResponse, error)
	GetNationalCreditFile(context.Context, *GetNationalCreditFileRequest) (*GetNationalCreditFileResponse, error)
}

// UnimplementedMVRServer can be embedded to have forward compatible implementations.
type UnimplementedMVRServer struct {
}

func (*UnimplementedMVRServer) FetchMVR(context.Context, *FetchMVRRequest) (*FetchMVRResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchMVR not implemented")
}
func (*UnimplementedMVRServer) PollMVR(context.Context, *PollMVRRequest) (*Report, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PollMVR not implemented")
}
func (*UnimplementedMVRServer) PollRawVeriskResponse(context.Context, *PollMVRRequest) (*RawVeriskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PollRawVeriskResponse not implemented")
}
func (*UnimplementedMVRServer) GetAttractScoreRawXml(context.Context, *GetAttractScoreRequest) (*GetAttractScoreRawXmlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAttractScoreRawXml not implemented")
}
func (*UnimplementedMVRServer) GetAttractScore(context.Context, *GetAttractScoreRequest) (*GetAttractScoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAttractScore not implemented")
}
func (*UnimplementedMVRServer) GeNationalCreditFileRawXml(context.Context, *GetNationalCreditFileRequest) (*GetNationalCreditFileRawXmlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GeNationalCreditFileRawXml not implemented")
}
func (*UnimplementedMVRServer) GetNationalCreditFile(context.Context, *GetNationalCreditFileRequest) (*GetNationalCreditFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNationalCreditFile not implemented")
}

func RegisterMVRServer(s *grpc.Server, srv MVRServer) {
	s.RegisterService(&_MVR_serviceDesc, srv)
}

func _MVR_FetchMVR_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchMVRRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MVRServer).FetchMVR(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mvr.MVR/FetchMVR",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MVRServer).FetchMVR(ctx, req.(*FetchMVRRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MVR_PollMVR_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PollMVRRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MVRServer).PollMVR(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mvr.MVR/PollMVR",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MVRServer).PollMVR(ctx, req.(*PollMVRRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MVR_PollRawVeriskResponse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PollMVRRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MVRServer).PollRawVeriskResponse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mvr.MVR/PollRawVeriskResponse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MVRServer).PollRawVeriskResponse(ctx, req.(*PollMVRRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MVR_GetAttractScoreRawXml_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAttractScoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MVRServer).GetAttractScoreRawXml(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mvr.MVR/GetAttractScoreRawXml",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MVRServer).GetAttractScoreRawXml(ctx, req.(*GetAttractScoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MVR_GetAttractScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAttractScoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MVRServer).GetAttractScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mvr.MVR/GetAttractScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MVRServer).GetAttractScore(ctx, req.(*GetAttractScoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MVR_GeNationalCreditFileRawXml_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNationalCreditFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MVRServer).GeNationalCreditFileRawXml(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mvr.MVR/GeNationalCreditFileRawXml",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MVRServer).GeNationalCreditFileRawXml(ctx, req.(*GetNationalCreditFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MVR_GetNationalCreditFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNationalCreditFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MVRServer).GetNationalCreditFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mvr.MVR/GetNationalCreditFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MVRServer).GetNationalCreditFile(ctx, req.(*GetNationalCreditFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MVR_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mvr.MVR",
	HandlerType: (*MVRServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FetchMVR",
			Handler:    _MVR_FetchMVR_Handler,
		},
		{
			MethodName: "PollMVR",
			Handler:    _MVR_PollMVR_Handler,
		},
		{
			MethodName: "PollRawVeriskResponse",
			Handler:    _MVR_PollRawVeriskResponse_Handler,
		},
		{
			MethodName: "GetAttractScoreRawXml",
			Handler:    _MVR_GetAttractScoreRawXml_Handler,
		},
		{
			MethodName: "GetAttractScore",
			Handler:    _MVR_GetAttractScore_Handler,
		},
		{
			MethodName: "GeNationalCreditFileRawXml",
			Handler:    _MVR_GeNationalCreditFileRawXml_Handler,
		},
		{
			MethodName: "GetNationalCreditFile",
			Handler:    _MVR_GetNationalCreditFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mvr/api.proto",
}
