package mvr

import "github.com/cockroachdb/errors"

var (
	ErrNotYetAvailable    = errors.Newf("report not yet available")
	ErrRequestNotAccepted = errors.Newf("request not accepted by verisk")
)

// ShouldPollAgain returns true if the report is not available yet and may
// be available in some time. This works across network boundaries.
func ShouldPollAgain(err error) bool {
	return errors.Is(err, ErrNotYetAvailable)
}
