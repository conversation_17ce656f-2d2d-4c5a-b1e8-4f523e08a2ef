load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "verisk",
    srcs = [
        "client.go",
        "defs.go",
        "models.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/mvr/verisk",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/us_states",
        "//nirvana/external_data_management/mvr",
        "//nirvana/external_data_management/mvr/verisk/mvr_parser",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_imroc_req//:req",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "verisk_test",
    srcs = ["verisk_client_test.go"],
    embed = [":verisk"],
    tags = ["manual"],
    deps = [
        "//nirvana/common-go/us_states",
        "//nirvana/external_data_management/mvr",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@tools_gotest//assert",
    ],
)
