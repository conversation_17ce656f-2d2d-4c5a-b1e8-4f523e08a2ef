package verisk

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"gotest.tools/assert"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/external_data_management/mvr"
)

// To run locally,
// - port forward from bastion using `ssh bastion -L 8903:expressnet.iix.com:8903 -N`
// Add mapping to /etc/hosts - `127.0.0.1 expressnet.iix.com`
const APIURL = "https://expressnet.iix.com:8903/web-services/Auth"

// TestMVRReqGen1 tests against 1 case in PDF doc
func TestMVRReqGen1(t *testing.T) {
	expectedPayload := "00IIXDEMO-WS3            099901AMSMVRIIL000000000A12345678901       CEACOMMON              MARY                          09091909         AppID1                                   V20                                        "

	cl, err := NewMVRClientWithBasicAuth(APIURL, "IIX", "DEMO-WS3", "099901", "AMS")
	if err != nil {
		t.Error(err)
	}
	dob := time.Date(1909, time.September, 9, 0, 0, 0, 0, time.Now().Location())
	payload, err := cl.createMVRRequestPayload(
		"AppID1",
		us_states.IL,
		"A12345678901",
		"MARY",
		"",
		"CEACOMMON",
		dob,
	)
	if err != nil {
		t.Error(err)
	}
	if payload != expectedPayload {
		t.Errorf("\n'%s' \n!= \n'%s'", payload, expectedPayload)
	}
	if len(payload) != 222 {
		t.Error("Expected getRequest2() payload to be 222 chars")
	}
}

func TestMVRReq1Gen1(t *testing.T) {
	ctx := context.TODO()
	cl, err := NewMVRClientWithBasicAuth(APIURL, "IIX", "DEMO-WS3", "099901", "000")
	if err != nil {
		t.Error(err)
		return
	}
	dob := time.Date(1942, time.April, 9, 0, 0, 0, 0, time.Now().Location())
	resp, err := cl.FetchMVR(ctx, &mvr.FetchMVRRequest{
		ApplicationID: "application-id-1",
		UsState:       "IL",
		DlNumber:      "A12345678901",
		FirstName:     "MARY",
		LastName:      "CEACOMMON",
		MiddleName:    "",
		Dob:           timestamppb.New(dob),
	})
	if err != nil {
		t.Error(err)
		return
	}

	report, err := cl.PollMVR(ctx, &mvr.PollMVRRequest{RequestID: resp.RequestID})
	if err != nil {
		t.Error(err)
		return
	}
	require.Equal(t, report.DlNumber, "A12345678901")
}

func TestUnMarshalXMLGetResponse(t *testing.T) {
	msg := []byte(
		`<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<soapenv:Body>
		<ns1:getResponse2Response soapenv:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:ns1="auth">
			<return xsi:type="xsd:string">HDR0010001795041175795041175MVR099901000QUOTEBACK                               2021030804393620210308043936**********901CEACOMMON, MARY                                   56 DEER AVENUE                         0   0   CV2012082017IIXJAV**********902FANTASY ISLAND                   60750A12345678901             ***********795041175           795041175AMSI4000    **********903         I           03271966F5'04130BRWNBRWND*                  0324201803272025                              00QS**********904REQUESTED AS: MARY                      CEACOMMON                 DOB: 04091942  LICENSE : A12345678901         001**********904IL3239588194PERS:01: ACTIVE          VALID   D              0324201803272025                B                   002**********904LIC CLASS: D                       DESC: SINGLE VEHICLE &lt; 16K,TOW &lt; 26K GCWR                                    003**********904LIC STATUS: VALID                     DESC: VALID - NO STOPS IN EFFECT                                          004**********904LIC ISSUED: 03/24/2018                                                                                          005**********904LIC EXPIRES: 03/27/2025                                                                                         006**********904LIC TYPE: 1                                                                                                     007**********904DRIVER'S EDUCATION COURSE HAS BEEN COMPLETED                                                                    008**********904LIC RENEWAL CONTINUATION DATE: 06/18/2018                                                                       009**********904This report is generated for insurance purposes only and may not be used for any other purpose.                    **********904The use and dissemination of the report and information in it must comply with your iiX agreement and the          **********904Fair Credit Reporting Act, the Driver's Privacy Protection Act, and any applicable state statute(s).               **********904The data in the report from the applicable state agency or service bureau is provided through iiX                  **********904&quot;as is.&quot;                                                                                                           **********904---                                                                                                                **********904Customer-defined MVR scoring has been applied to this record.  Customer                                            **********904is solely responsible for the application and use of the resulting score.                                          **********905ADMI    06142018            VEHICLE EMISSIONS SUSPENSION          1311122       580010      0                   001**********905                            VEHICLE EMISSIONS INSPECTION LAW                                                    002**********905                            TYPE ACTION:18                                                                      003**********905                            REF:001244865899                                                                    004</return>
		</ns1:getResponse2Response>
	</soapenv:Body>
</soapenv:Envelope>`)
	println(msg)

	res := &GetResponseEnvelope{}
	err := xml.Unmarshal(msg, res)
	if err != nil {
		t.Error(err)
		return
	}

	text := res.Body.GetResponseResponse.Return

	_, err = NewMVRClientWithBasicAuth(APIURL, "IIX", "DEMO-WS3", "099901", "000")
	if err != nil {
		t.Error(err)
		return
	}

	_, err = parseTextMVRRecordV2(text)
	if err != nil {
		t.Error(err)
	}
}

func TestMVRRecordTextParser(t *testing.T) {
	wd, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	_ = filepath.Walk(wd+"/mvr_parser/test-data/reports", func(path string, info os.FileInfo, err error) error {
		if err != nil {
			t.Fatalf("filepath.Walk error: %v", err)
		}
		if !info.IsDir() {
			f := strings.Replace(path, wd+"/mvr_parser/test-data/reports/", "", 1)
			state, filename := strings.Split(f, "/")[0], strings.Split(f, "/")[1]
			t.Logf("Test MVR Record: %s/%s", state, filename)

			contents, err := os.ReadFile(path)
			if err != nil {
				t.Fatalf("failed to read file %s: %v", path, err)
			}
			report, err := parseTextMVRRecordV2(string(contents))
			if err != nil {
				t.Error(err)
			}
			parsedDir := wd + "/mvr_parser/test-data/parsed/" + state
			parsedFilename := parsedDir + "/" + filename + ".json"

			// Use to persist parsed report for test cases
			//file, _ := json.MarshalIndent(report, "", " ")
			//_ = os.WriteFile(parsedFilename, file, 0644)

			parsedFile, err := os.ReadFile(parsedFilename)
			if err != nil {
				t.Fatalf("failed to read parsed file %s: %v", parsedFilename, err)
			}
			parsedReport := mvr.Report{}
			err = json.Unmarshal(parsedFile, &parsedReport)
			if err != nil {
				t.Fatalf("failed to unmarshal parsed file %s: %v", parsedFilename, err)
			}

			assert.DeepEqual(t, report, &parsedReport, cmpopts.IgnoreUnexported(mvr.Report{}, mvr.Violation{}), cmp.Comparer(func(x, y *timestamppb.Timestamp) bool {
				return x.AsTime().Equal(y.AsTime())
			}))
		}
		return nil
	})
}
