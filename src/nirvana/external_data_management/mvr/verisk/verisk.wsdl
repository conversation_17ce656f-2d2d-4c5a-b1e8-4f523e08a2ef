<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://com/iix/soap/SoapAuth.wsdl" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:impl="http://com/iix/soap/SoapAuth.wsdl" xmlns:intf="http://com/iix/soap/SoapAuth.wsdl" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<!--WSDL created by Apache Axis version: 1.4.1-SNAPSHOT
Built on Jun 15, 2017 (10:55:06 UTC)-->

   <wsdl:message name="sendRequest2Request">

      <wsdl:part name="msgString" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getResponseRequest">

      <wsdl:part name="msgString" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="sendRequest2Response">

      <wsdl:part name="return" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getResponse2Response">

      <wsdl:part name="return" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getResponseResponse">

      <wsdl:part name="return" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getPdfResponse2Request">

      <wsdl:part name="msgString" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getPdfResponse2Response">

      <wsdl:part name="return" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getResponse2Request">

      <wsdl:part name="msgString" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="sendRequestRequest">

      <wsdl:part name="msgString" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getPdfResponseRequest">

      <wsdl:part name="msgString" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getXmlResponseResponse">

      <wsdl:part name="return" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getPdfResponseResponse">

      <wsdl:part name="return" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getXmlResponseRequest">

      <wsdl:part name="msgString" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getXmlResponse2Request">

      <wsdl:part name="msgString" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="sendRequestResponse">

      <wsdl:part name="return" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="getXmlResponse2Response">

      <wsdl:part name="return" type="xsd:string">

      </wsdl:part>

   </wsdl:message>

   <wsdl:portType name="SoapAuthPortType">

      <wsdl:operation name="sendRequest2" parameterOrder="msgString">

         <wsdl:input message="impl:sendRequest2Request" name="sendRequest2Request">

       </wsdl:input>

         <wsdl:output message="impl:sendRequest2Response" name="sendRequest2Response">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="sendRequest" parameterOrder="msgString">

         <wsdl:input message="impl:sendRequestRequest" name="sendRequestRequest">

       </wsdl:input>

         <wsdl:output message="impl:sendRequestResponse" name="sendRequestResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getResponse" parameterOrder="msgString">

         <wsdl:input message="impl:getResponseRequest" name="getResponseRequest">

       </wsdl:input>

         <wsdl:output message="impl:getResponseResponse" name="getResponseResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getResponse2" parameterOrder="msgString">

         <wsdl:input message="impl:getResponse2Request" name="getResponse2Request">

       </wsdl:input>

         <wsdl:output message="impl:getResponse2Response" name="getResponse2Response">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getXmlResponse" parameterOrder="msgString">

         <wsdl:input message="impl:getXmlResponseRequest" name="getXmlResponseRequest">

       </wsdl:input>

         <wsdl:output message="impl:getXmlResponseResponse" name="getXmlResponseResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getXmlResponse2" parameterOrder="msgString">

         <wsdl:input message="impl:getXmlResponse2Request" name="getXmlResponse2Request">

       </wsdl:input>

         <wsdl:output message="impl:getXmlResponse2Response" name="getXmlResponse2Response">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getPdfResponse" parameterOrder="msgString">

         <wsdl:input message="impl:getPdfResponseRequest" name="getPdfResponseRequest">

       </wsdl:input>

         <wsdl:output message="impl:getPdfResponseResponse" name="getPdfResponseResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getPdfResponse2" parameterOrder="msgString">

         <wsdl:input message="impl:getPdfResponse2Request" name="getPdfResponse2Request">

       </wsdl:input>

         <wsdl:output message="impl:getPdfResponse2Response" name="getPdfResponse2Response">

       </wsdl:output>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="AuthSoapBinding" type="impl:SoapAuthPortType">

      <wsdlsoap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="sendRequest2">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="sendRequest2Request">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="auth" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="sendRequest2Response">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://com/iix/soap/SoapAuth.wsdl" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="sendRequest">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="sendRequestRequest">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="auth" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="sendRequestResponse">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://com/iix/soap/SoapAuth.wsdl" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getResponse">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="getResponseRequest">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="auth" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="getResponseResponse">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://com/iix/soap/SoapAuth.wsdl" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getResponse2">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="getResponse2Request">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="auth" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="getResponse2Response">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://com/iix/soap/SoapAuth.wsdl" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getXmlResponse">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="getXmlResponseRequest">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="auth" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="getXmlResponseResponse">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://com/iix/soap/SoapAuth.wsdl" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getXmlResponse2">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="getXmlResponse2Request">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="auth" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="getXmlResponse2Response">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://com/iix/soap/SoapAuth.wsdl" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getPdfResponse">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="getPdfResponseRequest">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="auth" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="getPdfResponseResponse">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://com/iix/soap/SoapAuth.wsdl" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="getPdfResponse2">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="getPdfResponse2Request">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="auth" use="encoded"/>

         </wsdl:input>

         <wsdl:output name="getPdfResponse2Response">

            <wsdlsoap:body encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" namespace="http://com/iix/soap/SoapAuth.wsdl" use="encoded"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="auth">

      <wsdl:port binding="impl:AuthSoapBinding" name="Auth">

         <wsdlsoap:address location="https://expressnet.iix.com:8903/web-services/Auth"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
