load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mvr_parser",
    srcs = [
        "fields_parser.go",
        "issue_date_parser.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/mvr/verisk/mvr_parser",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/us_states",
        "//nirvana/external_data_management/mvr",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
