package mvr_parser

import (
	"context"
	"errors"
	"math"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/external_data_management/mvr"
)

func (m *MvrResponse) ParseDLState() (string, error) {
	return parseField(m.Type1Record, 1, 3)
}

func (m *MvrResponse) ParseDriverName() (string, error) {
	return parseField(m.Type1Record, 13, 63)
}

func (m *MvrResponse) ParseDriverStreetAddress() (string, error) {
	return parseField(m.Type1Record, 63, 101)
}

func (m *MvrResponse) ParseMvrStatus() (string, error) {
	return parseField(m.Type1Record, 101, 102)
}

func (m *MvrResponse) ParseReportDate() (*time.Time, error) {
	reportDateStr, err := parseField(m.Type1Record, 114, 122)
	if err != nil {
		return nil, err
	}
	reportDate, err := time.Parse(DateFormatMagicString, reportDateStr)
	if err != nil {
		return nil, err
	}
	return &reportDate, nil
}

func (m *MvrResponse) ParseDriverCityStateZip() (string, error) {
	return parseField(m.Type2Record, 13, 51)
}

func (m *MvrResponse) ParseDLNumber(ctx context.Context) (string, error) {
	// Try the standard position first (Type 2 record, positions 51-70)
	dlNumber, err := parseField(m.Type2Record, 51, 70)
	if err != nil {
		return "", err
	}

	// If DLNumber is not empty from standard position, return it
	if dlNumber != "" {
		return dlNumber, nil
	}

	// Fallback: Search for license number in misc details (Type 4 records)
	log.Info(ctx, "DL number not found in standard position, attempting fallback to search Type 4 records")

	if m.Type4Records != nil && len(*m.Type4Records) > 0 {
		for _, record := range *m.Type4Records {
			miscDetail, err := parseField(record, 13, 125)
			if err != nil {
				continue // Skip this record if parsing fails
			}

			// Look for patterns like "LICENSE : *********" or "LICENSE: *********" (case-insensitive)
			miscDetailLower := strings.ToLower(miscDetail)

			// Find "license" followed by optional space and colon
			licenseIndex := strings.Index(miscDetailLower, "license")
			if licenseIndex != -1 {
				// Look for the colon after "license"
				afterLicense := miscDetailLower[licenseIndex+7:] // 7 is len("license")
				colonIndex := -1

				// Check for formats like "LICENSE : " or "LICENSE:"
				if strings.HasPrefix(afterLicense, " :") {
					colonIndex = licenseIndex + 7 + 2 // Position after " :"
				} else if strings.HasPrefix(afterLicense, ":") {
					colonIndex = licenseIndex + 7 + 1 // Position after ":"
				}

				if colonIndex != -1 && colonIndex < len(miscDetail) {
					// Extract the license number from the original string (preserving case)
					licenseSection := strings.TrimSpace(miscDetail[colonIndex:])
					licenseParts := strings.Fields(licenseSection)
					if len(licenseParts) > 0 {
						extractedLicense := strings.TrimSpace(licenseParts[0])
						if extractedLicense != "" {
							log.Info(ctx, "Fallback successful: extracted DL number from Type 4 record",
								log.String("dlNumber", extractedLicense))
							return extractedLicense, nil
						}
					}
				}
			}
		}
	}

	// If no license number found in misc details either, return empty string
	return "", nil
}

func (m *MvrResponse) ParseArchiveFlag() (string, error) {
	return parseField(m.Type2Record, 124, 125)
}

func (m *MvrResponse) ParseSsn() (string, error) {
	return parseField(m.Type3Record, 13, 22)
}

func (m *MvrResponse) ParseDppaFlag() (string, error) {
	return parseField(m.Type3Record, 23, 24)
}

func (m *MvrResponse) ParseDmvAccountNumber() (string, error) {
	return parseField(m.Type3Record, 24, 30)
}

func (m *MvrResponse) ParseGender() (string, error) {
	return parseField(m.Type3Record, 42, 43)
}

func (m *MvrResponse) ParseHeight() (string, error) {
	return parseField(m.Type3Record, 43, 47)
}

func (m *MvrResponse) ParseWeight() (string, error) {
	return parseField(m.Type3Record, 47, 50)
}

func (m *MvrResponse) ParseEyeColor() (string, error) {
	return parseField(m.Type3Record, 50, 54)
}

func (m *MvrResponse) ParseHairColor() (string, error) {
	return parseField(m.Type3Record, 54, 58)
}

func (m *MvrResponse) ParseLicClass() (string, error) {
	return parseField(m.Type3Record, 58, 70)
}

func (m *MvrResponse) ParseLicStatus() (string, error) {
	return parseField(m.Type3Record, 71, 78)
}

func (m *MvrResponse) ParseRestrictions() (string, error) {
	return parseField(m.Type3Record, 94, 124)
}

func (m *MvrResponse) ParseDOB() (*time.Time, error) {
	dobStr, err := parseField(m.Type3Record, 32, 42)
	if err != nil {
		return nil, err
	}
	dob, err := time.Parse(DateFormatMagicString, dobStr)
	if err != nil {
		return nil, err
	}
	return &dob, nil
}

func (m *MvrResponse) ParseDateExpires() (*time.Time, error) {
	dateExpiresStr, err := parseField(m.Type3Record, 86, 94)
	if err != nil {
		return nil, err
	}
	dateExpires, err := time.Parse(DateFormatMagicString, dateExpiresStr)
	if err != nil {
		return nil, err
	}
	return &dateExpires, nil
}

func (m *MvrResponse) ParseMiscDetails() (*[]string, error) {
	if m.Type4Records == nil || len(*m.Type4Records) == 0 {
		return nil, errors.New("no misc details found")
	}
	miscDetails := make([]string, 0)
	for _, record := range *m.Type4Records {
		miscDetail, err := parseField(record, 13, 125)
		if err != nil {
			return nil, err
		}
		if miscDetail != "" {
			miscDetails = append(miscDetails, miscDetail)
		}
	}
	return &miscDetails, nil
}

func (m *MvrResponse) ParseViolations() (*[]*mvr.Violation, error) {
	if m.Type5Records == nil || len(*m.Type5Records) == 0 {
		return nil, errors.New("no violations found")
	}
	violations := make([]*mvr.Violation, 0)
	for _, record := range *m.Type5Records {
		if trim(record[13:19]) != "" {
			violation, err := parseViolation(record)
			if err != nil {
				return nil, err
			}
			violations = append(violations, violation)
		} else {
			if len(violations) == 0 {
				return nil, errors.New("malformed text for MVR record")
			}
			lastRecord := violations[len(violations)-1]
			lastRecord.ViolationDetail += "\n" + trim(record[41:79])
		}
	}
	return &violations, nil
}

func parseViolation(record string) (*mvr.Violation, error) {
	violation := mvr.Violation{}
	violation.ViolationType, _ = parseField(record, 13, 19)
	violation.ViolationCode, _ = parseField(record, 79, 89)
	violation.AssignedViolationCode, _ = parseField(record, 93, 103)
	violation.ViolationDetail = ""

	if len(trim(record[21:29])) == 8 {
		violationDate, err := time.Parse(DateFormatMagicString, trim(record[21:29]))
		if err != nil {
			return nil, err
		}
		violation.ViolationDate = timestamppb.New(violationDate)
	}

	if len(trim(record[32:40])) == 8 {
		convictionDate, err := time.Parse(DateFormatMagicString, trim(record[32:40]))
		if err != nil {
			return nil, err
		}
		violation.ConvictionDate = timestamppb.New(convictionDate)
	}

	if len(trim(record[90:93])) > 0 {
		points, err := strconv.ParseFloat(trim(record[90:93]), 64)
		if err != nil {
			return nil, err
		}
		violation.Points = int64(math.Round(points))
	}

	return &violation, nil
}
