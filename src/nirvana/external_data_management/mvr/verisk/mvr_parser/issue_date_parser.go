package mvr_parser

import (
	"context"
	"strings"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/us_states"
)

var stateToIssuedDateParsingVariantMap = map[us_states.USState]func(*MvrResponse) (*time.Time, error){
	us_states.AL: parseIssuedDateVariant2,
	us_states.AK: parseIssuedDateVariant2,
	us_states.AZ: parseIssuedDateVariant2,
	us_states.AR: parseIssuedDateVariant2,
	us_states.CA: parseIssuedDateVariant1,
	us_states.CO: parseIssuedDateVariant2,
	us_states.CT: parseIssuedDateVariant1,
	us_states.DE: parseIssuedDateVariant2,
	us_states.DC: parseIssuedDateVariant1,
	us_states.FL: parseIssuedDateVariant3,
	us_states.GA: parseIssuedDateVariant3,
	us_states.HI: parseIssuedDateVariant1,
	us_states.ID: parseIssuedDateVariant2,
	us_states.IL: parseIssuedDateVariant2,
	us_states.IN: parseIssuedDateVariant4,
	us_states.IA: parseIssuedDateVariant2,
	us_states.KS: parseIssuedDateVariant2,
	us_states.KY: parseIssuedDateVariant2,
	us_states.LA: parseIssuedDateVariant1,
	us_states.ME: parseIssuedDateVariant2,
	us_states.MD: parseIssuedDateVariant2,
	us_states.MA: parseIssuedDateVariant2,
	us_states.MI: parseIssuedDateVariant2,
	us_states.MN: parseIssuedDateVariant2,
	us_states.MS: parseIssuedDateVariant2,
	us_states.MO: parseIssuedDateVariant2,
	us_states.MT: parseIssuedDateVariant2,
	us_states.NE: parseIssuedDateVariant2,
	us_states.NV: parseIssuedDateVariant2,
	us_states.NH: parseIssuedDateVariant1,
	us_states.NJ: parseIssuedDateVariant1,
	us_states.NM: parseIssuedDateVariant2,
	us_states.NY: parseIssuedDateVariant2,
	us_states.NC: parseIssuedDateVariant2,
	us_states.ND: parseIssuedDateVariant2,
	us_states.OH: parseIssuedDateVariant2,
	us_states.OK: parseIssuedDateVariant2,
	us_states.OR: parseIssuedDateVariant2,
	us_states.PA: parseIssuedDateVariant2,
	us_states.RI: parseIssuedDateVariant2,
	us_states.SC: parseIssuedDateVariant2,
	us_states.SD: parseIssuedDateVariant2,
	us_states.TN: parseIssuedDateVariant2,
	us_states.TX: parseIssuedDateVariant2,
	us_states.UT: parseIssuedDateVariant1,
	us_states.VT: parseIssuedDateVariant1,
	us_states.VA: parseIssuedDateVariant2,
	us_states.WA: parseIssuedDateVariant3,
	us_states.WV: parseIssuedDateVariant2,
	us_states.WI: parseIssuedDateVariant2,
	us_states.WY: parseIssuedDateVariant3,
}

type recordText struct {
	cdlClassIdentifiers   []string
	issuedDateIdentifiers []string
}

var stateIdentifierMap = map[us_states.USState]recordText{
	us_states.AL: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A", "CDL CLASS: AM"},
		issuedDateIdentifiers: []string{"ORIGINAL ISSUE DATE:"},
	},
	us_states.AK: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"ORIGINAL ISSUE DATE:"},
	},
	us_states.AZ: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.AR: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	// TODO: Add this once we have a sample
	us_states.CA: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{""},
	},
	us_states.CO: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A", "CDL CLASS: ADULT CLASS A LICENSE"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	// TODO: Add this once we have a sample
	us_states.CT: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{""},
	},
	us_states.DE: {
		cdlClassIdentifiers:   []string{"CDL CLASS: CA"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.DC: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{""},
	},
	us_states.FL: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{"ORIGINAL CDL ISSUE DATE:"},
	},
	us_states.GA: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{"FIRST GA CDL DATE:"},
	},
	// TODO: Add this once we have a sample
	us_states.HI: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{""},
	},
	us_states.ID: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.IL: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A", "CDL CLASS: AM", "CDL CLASS: CDL-A"},
		issuedDateIdentifiers: []string{"CDL RENEWAL CONTINUATION DATE:"},
	},
	// Uses a different parser that doesn't use the identifier map
	us_states.IN: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{""},
	},
	us_states.IA: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.KS: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.KY: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.LA: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.ME: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.MD: {
		cdlClassIdentifiers:   []string{"CDL CLASS: CDLA", "CDL CLASS: CDLAM"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.MA: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.MI: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.MN: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.MS: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.MO: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.MT: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.NE: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.NV: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	// TODO: Add this once we have a sample
	us_states.NH: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{""},
	},
	us_states.NJ: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{""},
	},
	us_states.NM: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.NY: {
		cdlClassIdentifiers:   []string{"CDL CLASS: CDL *A*"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.NC: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:", "ORIGINAL ISSUE DATE:"},
	},
	us_states.ND: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.OH: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	// Fix null date propagation
	us_states.OK: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"ORIGINAL ISSUE DATE:"},
	},
	us_states.OR: {
		cdlClassIdentifiers:   []string{"CDL CLASS: CDLA"},
		issuedDateIdentifiers: []string{"CDL ISSUED:", "CDL EARLIEST ISSUANCE:"},
	},
	us_states.PA: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.RI: {
		cdlClassIdentifiers:   []string{"CDL CLASS: CDL CLASS A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.SC: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:", "FIRST ISSUED:"},
	},
	us_states.SD: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A", "CDL CLASS: A3"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.TN: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A***", "CDL CLASS: AM**"},
		issuedDateIdentifiers: []string{"ORIGINAL ISSUE DATE:"},
	},
	us_states.TX: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A", "CDL CLASS: AM"},
		issuedDateIdentifiers: []string{"ORIGINAL ISSUE DATE:"},
	},
	// TODO: Add this once we have a sample
	us_states.UT: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{},
	},
	us_states.VT: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.VA: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.WA: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{"CDL ORIGINAL ISSUE DATE:"},
	},
	us_states.WV: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"CDL ISSUED:"},
	},
	us_states.WI: {
		cdlClassIdentifiers:   []string{"CDL CLASS: A"},
		issuedDateIdentifiers: []string{"ORIGINAL ISSUE DATE:"},
	},
	us_states.WY: {
		cdlClassIdentifiers:   []string{},
		issuedDateIdentifiers: []string{"DATE FIRST ISSUED A WY CDL:"},
	},
}

func (m *MvrResponse) ParseDateIssued() (*time.Time, error) {
	dateIssuedStr, err := parseField(m.Type3Record, 78, 86)
	if err != nil {
		return nil, err
	}
	var dateIssued *time.Time
	if dateIssuedStr != "" {
		di, err := time.Parse(DateFormatMagicString, dateIssuedStr)
		if err != nil {
			return nil, err
		}
		dateIssued = &di
	}

	state, err := m.ParseDLState()
	if err != nil {
		return nil, err
	}
	usState, err := us_states.StrToUSState(state)
	if err != nil {
		return nil, err
	}

	if stateToIssuedDateParsingVariantMap[usState] != nil {
		updatedDateIssued, err := stateToIssuedDateParsingVariantMap[usState](m)
		if err != nil {
			return nil, err
		}
		if updatedDateIssued != nil {
			dateIssued = updatedDateIssued
		}
	}

	return dateIssued, nil
}

func parseIssuedDateVariant1(m *MvrResponse) (*time.Time, error) {
	if m.Type4Records == nil || len(*m.Type4Records) == 0 {
		return nil, errors.New("no type 4 records found")
	}

	miscDetails, err := m.ParseMiscDetails()
	if err != nil {
		return nil, err
	}

	var updatedDateIssued *time.Time
	for _, miscDetail := range *miscDetails {
		switch {
		case strings.HasPrefix(miscDetail, "ORIGINAL ISSUE DATE:") && len(miscDetail) >= 31:
			dateIssued, err := time.Parse("01/02/2006", miscDetail[21:31])
			if err != nil {
				return nil, err
			}
			updatedDateIssued = &dateIssued
		case strings.HasPrefix(miscDetail, "CDL EARLIEST ISSUANCE:") && len(miscDetail) >= 33:
			dateIssued, err := time.Parse("01/02/2006", miscDetail[23:33])
			if err != nil {
				return nil, err
			}
			updatedDateIssued = &dateIssued
		case strings.HasPrefix(miscDetail, "ORIGINAL LICENSE ISSUED") && len(miscDetail) >= 35:
			dateIssued, err := time.Parse("01/02/2006", miscDetail[25:35])
			if err != nil {
				return nil, err
			}
			updatedDateIssued = &dateIssued
		case strings.HasPrefix(miscDetail, "ORIGINAL ISSUE:") && len(miscDetail) >= 24:
			dateIssued, err := time.Parse("01/02/06", miscDetail[16:24])
			if err != nil {
				return nil, err
			}
			updatedDateIssued = &dateIssued
		case strings.HasPrefix(miscDetail, "CDL RENEWAL CONTINUATION DATE:") && len(miscDetail) >= 41:
			dateIssued, err := time.Parse("01/02/2006", miscDetail[31:41])
			if err != nil {
				return nil, err
			}
			updatedDateIssued = &dateIssued
		}
	}

	return updatedDateIssued, nil
}

func parseIssuedDateVariant2(m *MvrResponse) (*time.Time, error) {
	if m.Type4Records == nil || len(*m.Type4Records) == 0 {
		return nil, errors.New("no type 4 records found")
	}

	miscDetails, err := m.ParseMiscDetails()
	if err != nil {
		return nil, err
	}

	state, err := m.ParseDLState()
	if err != nil {
		return nil, err
	}
	usState, err := us_states.StrToUSState(state)
	if err != nil {
		return nil, err
	}

	var updatedDateIssued *time.Time
	classRecords := getClassRecords(*miscDetails)
	for _, record := range classRecords {
		if classARecordExists(record, stateIdentifierMap[usState].cdlClassIdentifiers) {
			for _, issuedDateIdentifier := range stateIdentifierMap[usState].issuedDateIdentifiers {
				dateBeginIndex := len(issuedDateIdentifier) + 1
				dateEndIndex := dateBeginIndex + 10
				i := strings.Index(record, issuedDateIdentifier)
				if i == -1 || len(record) < i+dateEndIndex {
					continue
				}
				dateIssued, e := time.Parse(
					"01/02/2006",
					record[i+dateBeginIndex:i+dateEndIndex],
				)
				if e != nil {
					log.Warn(context.Background(), "error parsing date issued", log.Err(e))
					continue
				}
				if updatedDateIssued == nil || dateIssued.Before(*updatedDateIssued) {
					updatedDateIssued = &dateIssued
				}
			}
		}
	}
	return updatedDateIssued, nil
}

func parseIssuedDateVariant3(m *MvrResponse) (*time.Time, error) {
	if m.Type4Records == nil || len(*m.Type4Records) == 0 {
		return nil, errors.New("no type 4 records found")
	}

	miscDetails, err := m.ParseMiscDetails()
	if err != nil {
		return nil, err
	}

	state, err := m.ParseDLState()
	if err != nil {
		return nil, err
	}
	usState, err := us_states.StrToUSState(state)
	if err != nil {
		return nil, err
	}

	var updatedDateIssued *time.Time
	for _, miscDetail := range *miscDetails {
		for _, issuedDateIdentifier := range stateIdentifierMap[usState].issuedDateIdentifiers {
			dateBeginIndex := len(issuedDateIdentifier) + 1
			dateEndIndex := dateBeginIndex + 10
			if strings.HasPrefix(miscDetail, issuedDateIdentifier) && len(miscDetail) >= dateEndIndex {
				dateIssued, err := time.Parse("01/02/2006", miscDetail[dateBeginIndex:dateEndIndex])
				if err != nil {
					return nil, err
				}
				updatedDateIssued = &dateIssued
			}
		}
	}

	return updatedDateIssued, nil
}

func parseIssuedDateVariant4(m *MvrResponse) (*time.Time, error) {
	if m.Type4Records == nil || len(*m.Type4Records) == 0 {
		return nil, errors.New("no type 4 records found")
	}

	miscDetails, err := m.ParseMiscDetails()
	if err != nil {
		return nil, err
	}

	var updatedDateIssued *time.Time
	for _, miscDetail := range *miscDetails {
		if strings.HasPrefix(miscDetail, "ISSUE DATE:") && strings.Contains(miscDetail, "CDL CLASS A,") && len(miscDetail) >= 22 {
			dateIssued, err := time.Parse("01/02/2006", miscDetail[12:22])
			if err != nil {
				return nil, err
			}
			updatedDateIssued = &dateIssued
		}
	}

	return updatedDateIssued, nil
}

func getClassRecords(miscDetails []string) []string {
	if len(miscDetails) == 0 {
		return []string{}
	}
	var classRecords []string
	buffer := ""
	for _, miscDetail := range miscDetails {
		if strings.HasPrefix(miscDetail, "PERS:") || strings.HasPrefix(miscDetail, "COMM:") {
			if buffer != "" {
				classRecords = append(classRecords, buffer)
			}
			buffer = miscDetail
		} else {
			buffer = buffer + miscDetail
		}
	}
	classRecords = append(classRecords, buffer)
	return classRecords
}

func classARecordExists(record string, matchStrings []string) bool {
	for _, matchString := range matchStrings {
		i := strings.Index(record, matchString)
		if i == -1 {
			continue
		}
		return true
	}
	return false
}
