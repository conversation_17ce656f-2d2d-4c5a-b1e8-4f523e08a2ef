<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
 xmlns="http://www.iix.com/mvr"
 targetNamespace="http://www.iix.com/mvr"
 elementFormDefault="qualified">
 <xsd:element name="MVR">
 <xsd:complexType>
 <xsd:sequence>
 <xsd:element name="MVRRequest" type="MVRRequestT"/>
 <xsd:element name="MVRReports">
 <xsd:complexType>
 <xsd:sequence>
 <xsd:element name="MVRReport" type="MVRReportT"
maxOccurs="unbounded"/>
 </xsd:sequence>
 </xsd:complexType>
 </xsd:element>
 </xsd:sequence>
 </xsd:complexType>
 </xsd:element>
 <xsd:complexType name="MVRRequestT">
 <xsd:sequence>
 <xsd:element name="RequestID"/>
 <xsd:element name="Account"/>
 <xsd:element name="BillCode"/>
 <xsd:element name="UserBatchCode"/>
 <xsd:element name="RequestorInit" minOccurs="0"/>
 <xsd:element name="OrderPurpose" minOccurs="0"/>
 <xsd:element name="UserRefNo" minOccurs="0"/>
 <xsd:element name="Quoteback" minOccurs="0"/>
 <xsd:element name="DLState"/>
 <xsd:element name="ReportType" minOccurs="0">
 <xsd:complexType>
 <xsd:sequence>
 <xsd:element name="ReportTypeCode"/>
 <xsd:element name="ReportTypeDescription"/>
 </xsd:sequence>
 </xsd:complexType>
 </xsd:element>
 <xsd:element name="DLNumber" minOccurs="0"/>
 <xsd:element name="DOB" minOccurs="0"/>
 <xsd:element name="LastName" minOccurs="0"/>
 <xsd:element name="NameSuffix" minOccurs="0"/>
 <xsd:element name="FirstName" minOccurs="0"/>
 <xsd:element name="MiddleName" minOccurs="0"/>
 <xsd:element name="Gender" minOccurs="0"/>
 <xsd:element name="SSN" minOccurs="0"/>
 </xsd:sequence>
 </xsd:complexType>
 <xsd:complexType name="MVRReportT">
 <xsd:sequence>
 <xsd:element name="ReportID"/>
 <xsd:element name="DLState"/>
 <xsd:element name="ReportSequenceNumber"/>
 <xsd:element name="DriverName" minOccurs="0"/>
 <xsd:element name="DriverStreetAddr" minOccurs="0"/>
 <xsd:element name="MVRStatus"/>
 <xsd:element name="ViolationCoding" minOccurs="0"/>
 <xsd:element name="ViolationCodeTotal" minOccurs="0"/>
 <xsd:element name="MVRFormat" minOccurs="0"/>
 <xsd:element name="MVRReportDate"/>
 <xsd:element name="DriverCityStateZip" minOccurs="0"/>
 <xsd:element name="DLNumber" minOccurs="0"/>
 <xsd:element name="ClientCode" minOccurs="0"/>
 <xsd:element name="ArchiveFlag" minOccurs="0"/>
 <xsd:element name="SSN" minOccurs="0"/>
 <xsd:element name="DPPAFlag" minOccurs="0"/>
 <xsd:element name="DMVAccountNumber" minOccurs="0"/>
 <xsd:element name="DOB" minOccurs="0"/>
 <xsd:element name="Gender" minOccurs="0"/>
 <xsd:element name="Height" minOccurs="0"/>
 <xsd:element name="Weight" minOccurs="0"/>
 <xsd:element name="EyeColor" minOccurs="0"/>
 <xsd:element name="HairColor" minOccurs="0"/>
 <xsd:element name="LicClass" minOccurs="0"/>
 <xsd:element name="LicStatus" minOccurs="0"/>
 <xsd:element name="DateIssued" minOccurs="0"/>
 <xsd:element name="DateExpires" minOccurs="0"/>
 <xsd:element name="Restrictions" minOccurs="0"/>
 <xsd:element name="MiscDetail" maxOccurs="unbounded" minOccurs="0">
 <xsd:complexType>
 <xsd:sequence>
 <xsd:element name="Detail" maxOccurs="unbounded"/>
 </xsd:sequence>
 </xsd:complexType>
 </xsd:element>
 <xsd:element name="Violations" maxOccurs="unbounded" minOccurs="0">
 <xsd:complexType>
 <xsd:sequence>
 <xsd:element name="Violation" maxOccurs="unbounded" minOccurs="0">
 <xsd:complexType>
 <xsd:sequence>
 <xsd:element name="ViolationType"/>
 <xsd:element name="ViolationDate" minOccurs="0"/>
<xsd:element name="ConvictionDate" minOccurs="0"/>
<xsd:element name="ViolationCode" minOccurs="0"/>
 <xsd:element name="Points" minOccurs="0"/>
 <xsd:element name="AssignedViolationCode" minOccurs="0"/>
<xsd:element name="AssignedPoints" minOccurs="0"/>
<xsd:element name="ViolationDetail" minOccurs="0">
 <xsd:complexType>
 <xsd:sequence>
 <xsd:element name="Detail" maxOccurs="unbounded"
minOccurs="0"/>
 </xsd:sequence>
 </xsd:complexType>
 </xsd:element>
 </xsd:sequence>
 </xsd:complexType>
 </xsd:element>
 </xsd:sequence>
 </xsd:complexType>
 </xsd:element>
 </xsd:sequence>
 </xsd:complexType>
</xsd:schema>