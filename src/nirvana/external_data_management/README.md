# How to add new methods to Fetcher/Processor

## Intro

### Context

This guide is intended to help other engineers add methods to the Fetcher and Processor components.
It consists on a concrete list of steps required.
There might be some things missing or that aren't clear enough.
If so, please let us know through Slack (`#infra` channel).
We are more than happy to improve this guide.
Thank you!

Everytime we refer to files, we are assuming you are standing on `external_data_management/` (where this README is located).

> If you need to add a new pre-defined interceptor, let us know and we can also help.
> For now, we don't have a guide for that, as the `#infra` team should be responsible for adding new pre-defined interceptors.

### When can this guide be useful?

- When you want to add a versioned method that doesn't exist in the Fetcher or Processor components.
- When you want to add a new version of a method that already exists in these components, because something changed in the way that data is fetched or processed.
- When you need to start versioning a certain non-versioned method, so you need to decompose it into versioned methods on the new Fetcher and/or Processor components.

> Remember that versioned methods are immutable! We enforce this through strict PR reviews.

### Directory structure

The `v2/` directory contains new packages related to data fetching and processing.
As of 21/07/2024, there are four directories:

1. `data_fetching`
2. `data_processing`
3. `store_management`
4. `interceptors`
5. `common`

You can read more details of each package in their own doc.go or README files.

On thing to note here is how the `data_fetching` and `data_processing` packages relate to our programs (i.e. Fleet and Non-Fleet).
It's very likely that Fleet and Non-Fleet use cases will end up using almost the same methods exposed by the data_fetching package (as both programs use similar raw data).
Nonetheless, they will probably end up using different methods exposed by the data_processing package, as their data "massaging" logic is different.
Having said that, there is no real limitation.
So it's perfectly possible that Fleet and Non-Fleet use the same method in the processor (not all methods have to be specific to a program).
Likewise, it could also happen that some methods in the data_fetching package are only used by one program, because that data retrieved by those methods is only relevant for one program.

Additionally, it's relevant to comment on the overall design of the Fetcher and Processor components.
These components are implemented using gRPC and proto definitions.
We start a new local gRPC server for each component.
These servers have a "buffered listener", so calling a gRPC method is practically the same as calling a normal method.
Doing this allows us to take advantage of gRPC interceptors, so we don't have to "reinvent the wheel".

There are two types of interceptors:
1. Pre-defined: implemented and maintained by the `#infra` team. E.g. store interceptor.
2. User-provided: implemented by the consumer of the Fetcher or Processor. E.g. endorsement interceptor.

You can pass user-provided interceptors when instantiating a Fetcher or Processor, via config options.

On the other hand, when using pre-defined interceptors, you can configure them via config options when instantiating your Fetcher or Processor.
Alternatively, you can also use gRPC options passed to the gRPC client when calling a method to override the config options.

## Steps

### 1. Decide where should the method you want to migrate live

There are two possibilities: Fetcher or Processor.

The rule of thumb is:
- Choose fetcher if your method only applies structure transformation to data fetched from an external source (e.g. database, server, etc.).
See examples of other fetcher methods in `data_fetching/server.go`.
- Choose processor if there’s any kind of “business logic” that needs to be applied to data in order to compute a certain result.
See examples of other processor methods in `data_processing/server.go`.

> In some cases a single method that is used today might need to be split into two: one that does the fetching, and one that does the processing (the latter receives the data fetched through the former, and the consumer of both does the orchestration).

### 2. Decide whether the method is program specific or not

You might be adding a method that is only used by a single program.
This doesn't mean that your method is program specific, it just means that *currently* is only used by one program.

When making this decision, think if your method has an inherently program-specific logic.
An example of this is the method `GetFleetMovingViolationCountV1` which uses a list of violations that are specific to the Fleet program.

If your method is program-specific, then please add the corresponding prefix to indicate so (note the "Fleet" prefix in `GetFleetMovingViolationCountV1`). 

### 3. Add method and types (request and response) to proto interface

You need to modify the proto file(s) in `/nirvana/src/proto/data_fetching` and/or `/nirvana/src/proto/data_processing`, and then run `task go:protogen` to update the generated code.

Note that methods on proto interfaces (a.k.a. “services”) only receive one parameter (besides ctx and grpc options) and only return one value (besides error).
So if you need multiple params or return values you need to encapsulate them inside a single proto message (which will map to a single go struct in the generated code).
If your method doesn't receive any params, there's a special proto message called `Empty` that you can use.

You might need to use nested messages, and that’s totally fine.
You might also need to define enums, that’s also totally fine.

> Just don't forget that everything (e.g. messages, enums, etc.) needs to be versioned! 

### 4. Implement method on fetcher or processor server implementation structs

Take a look at how other methods are being implemented.
They all follow the same structure, it's just the transformation logic (for fetcher) or computation logic (for processor) that changes.
Examples can be found either in `data_fetching/server.go` or `data_fetching/server.go`.

Besides implementing the method itself, you will need to do add any new dependencies that you need to use in your server (e.g. a db wrapper or client).
We have a “serverDeps” structs for this.

> Don't forget to add tests!
> The tests are in a separate directories, and we have one test suite per method.
> You can find them at `data_fetching/tests/server_tests` and `data_processing/tests/server_tests`.

#### 4.1 About predefined interceptors

Right now there’s only one predefined interceptor: the store interceptor.
To allow consumers to use your method with this interceptor, you need to make sure that the request type that your method receives implement the `store_management.StoreKey` interface.
Don't worry, this interface is super simple, with only two methods.
In the case of the `GetFileNameComponents` method, make sure that whatever you return in that method actually identifies a request uniquely.

You can find multiple examples in `data_fetching/store_keys.go` and `data_processing/store_keys.go`.

> Don't forget to write tests for the `StoreKey` implementations!
> Take a look at `data_fetching/tests/store_keys_tests` and `data_processing/tests/store_keys_tests`.

#### 4.2 About processor's static data

In the case of the processor, you’ll notice that methods sometimes use static data.
For example, to calculate Moving Violation Count (MVC) of a driver, we need determine if the violations on his MVR report are moving or not.
To do so, we have a map with all violation where we store if they are considered a "moving violation".
This map is static data, as it's not dependent on the request received by the processor.

> Static data is stored in S3 within a special data context that is hardcoded in our code base.

If the method you are adding uses static data, you will need to do some additional things:
- Add a script to manually upload the static data to S3.
We have helper functions for this that you can find in `data_processing/static_data_utils/`.
You can also find examples of upload scripts in `data_processing/mvr/cmd/` and `data_processing/vin/cmd/` (for uploading fleet violations and ISO/NHTSA mapping, respectively).
- Create a test fixture and register it for testing.
The idea is that the test fixture automatically populates the static data on the mocked S3 storage we use in tests, when it is loaded by fx (the population logic should go inside the `onStart` hook of fx lifecycle).
Note that tests that require the static data to be populated need to explicitly include the fixture as a dependency to be loaded by fx.
You can find an example of this in `data_processing/vin/testing/testfixtures/nhtsa_to_iso_mapping_v1_fixture.go` (for populating ISO/NHTSA mapping) and `data_processing/mvr/testing/testfixtures/fleet_violations_data_v1_fixture.go` (for populating Fleet violations).
- [OPTIONAL] Cache the static data on the processor, so you don't need to download it for each request.

> Static data should never change (i.e. should be immutable).
> If you need to modify it, upload a new version of it, and create a new method on the processor that uses it.

### 5. Run `gomock` to update the mock fetcher/processor server implementation

You can do this through the Goland IDE, by clicking on the green arrow found in the files `data_xx/doc.go`.

### 6. Start using new method in code

The idea is that no one should be using clients or db wrappers directly to obtain external data.
Instead, everyone should be using the fetcher.
Therefore, you'll need to update old usages, so that they stop using the client/wrapper directly, and instead, they use the new method you added on the fetcher.

Similarly, all processing data should be versioned and live in the processor as a method.

There might be exceptions to these of course, like maybe a command script that is run manually by support.
But generally speaking, fetcher and processor should be the standard way of accessing and processing data.
If you think you have a use where you feel you shouldn't use the fetcher/processor please let us know.

To start using the new method, you will potentially need to instantiate a fetcher or processor in your code.
You can find several example of this across our code, particularly in adaptors and drivers/equipment list.

Some important points to have in mind:
- We inject through `fx` factories to create gRPC clients for these components, not the clients themselves.
The reason for this, is that each use case might require a unique interceptors' configuration.
For example, a specific consumer might need an interceptor that other consumer doesn't, or might need an interceptor with a different config (e.g. store interceptor enabled with a specific data context ID).
Given that we use gRPC interceptors, and that these can only be added when creating the gRPC client (and can't be changed after the client is created), we can't inject the client directly.
- You don’t always need to use the store interceptor.
To decide on this, think if reproducibility or tracking is relevant for your use case.
- To use the store interceptor you will need to define a “data context ID”.
A "data context" is a scope within which data is read and written. 
Usually this ID will come from the most recent submission associated to your use case.
- In the case of the fetcher, you might need a function that gets data for a collection of inputs, where each input can be mapped to a request for the fetcher.
For example, you can see how a collection of mvr reports is fetched in `rating/data_fetching/mvr_fetching/get_mvr_reports_v1.go`.
In this function you might also want to apply further transformations, like mapping enums or errors returned by the fetcher to types defined in your specific domain.
We call this type of functions "decorators".
Note that these functions are also versioned.
- In the case of the processor, things are similar.
You should try to put all processing logic that is related to our core business inside processor methods.
Even if currently there's only one consumer of the method you are implementing. 
Note though, that presentation logic and other logics that are very use-case specific should not be in the processor.
For example, the business logic related to application `Problems` should not live in the processor.
The reason for this is that this logic is only relevant to the rating domain, and the professor is not specific to that domain.
If the method you are migrating contains `Problems` logic, you will need to create a wrapper function that uses the processor behind the scenes.
Or alternatively, you can create a custom interceptor to wrap the processor method.
For example, you can see how we do it for “moving violation count” or “iso fields from nhtsa fields” in `rating/data_processing/mvr_processing/get_fleet_moving_violation_count_for_drivers_v1.go` and `rating/data_processing/vin_processing/fetch_vin_data_with_defaults_v1.go`, respectively.

## Example PRs:
- TODO: we had many, but with the old design.
After implementing a method with the new design, I'll add a link here.
