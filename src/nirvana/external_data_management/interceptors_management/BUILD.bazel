load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "interceptors_management",
    srcs = ["utils.go"],
    importpath = "nirvanatech.com/nirvana/external_data_management/interceptors_management",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_grpc//:grpc",
    ],
)
