package common

import (
	"strconv"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"

	"nirvanatech.com/nirvana/common-go/log"
)

const (
	metricNamespace    = "external_data_client_interceptor"
	durationMetricName = metricNamespace + ".duration"
	countMetricName    = metricNamespace + ".count"
)

const metricsSampleRate = float32(1)

const (
	requestSuccessTagKey  = "success"
	requestMethodTagKey   = "method"
	interceptorNameTagKey = "interceptor_name"
)

func EmitMetrics(
	client statsd.StatSender,
	interceptorName string,
	method string,
	startTime time.Time,
	retErrPtr *error,
	additionalTags ...statsd.Tag,
) {
	// These panics should never be triggered due to how we call this function.
	if retErrPtr == nil {
		log.Plain.DPanic("retErrPtr must not be nil")
		return
	}

	if client == nil {
		log.Plain.Warn("no statsd client provided, skipping store interceptor metrics")
		return
	}

	reqSuccess := *retErrPtr == nil

	tags := []statsd.Tag{
		{requestMethodTag<PERSON>ey, method},
		{interceptorNameTagKey, interceptorName},
		{requestSuccessTagKey, strconv.FormatBool(reqSuccess)},
	}
	tags = append(tags, additionalTags...)

	duration := time.Since(startTime)
	err := client.TimingDuration(
		durationMetricName,
		duration,
		metricsSampleRate,
		tags...,
	)
	if err != nil {
		log.Plain.Error("error sending interceptor duration metric", log.Err(err))
	}

	err = client.Inc(
		countMetricName,
		1,
		metricsSampleRate,
		tags...,
	)
	if err != nil {
		log.Plain.Error("error sending interceptor count metric", log.Err(err))
	}
}
