load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "unwrap_reply_interceptor",
    srcs = ["interceptor.go"],
    importpath = "nirvanatech.com/nirvana/external_data_management/interceptors_management/unwrap_reply_interceptor",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/common",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_grpc//:grpc",
    ],
)
