package unwrap_reply_interceptor

import (
	"context"

	"github.com/cockroachdb/errors"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/common"
)

// New creates an interceptor that unwraps the common.Resource
// it receives in its reply param, and passes the unwrapped object
// to the invoker, which is of the type expected by the server.
//
// Note: for this to work this interceptor must be placed at
// the end of the interceptor chain, right before the invoker
// that calls the server.
func New() grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		resource, ok := reply.(*common.Resource)
		if !ok {
			return errors.Newf("expected reply of type *common.Resource, type was %T", reply)
		}

		return invoker(ctx, method, req, resource.Data, cc, opts...)
	}
}
