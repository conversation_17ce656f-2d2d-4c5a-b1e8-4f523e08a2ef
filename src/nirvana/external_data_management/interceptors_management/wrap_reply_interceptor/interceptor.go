package wrap_reply_interceptor

import (
	"context"

	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/common"
)

// New creates an interceptor that wraps the reply param
// it receives with a common.Resource struct, and passes
// the wrapped object to the invoker.
//
// This interceptor must be put at the start of the chain,
// so that all other interceptors work with a reply param
// of the type common.Resource.
//
// Note: this approach works because each interceptor, as
// well as the server, modifies the reply param by reference,
// and because the wrapper object holds a reference to the
// original `reply` object, which is the container that the
// client expects to be populated.
func New() grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		wrappedReply := &common.Resource{Data: reply}
		return invoker(ctx, method, req, wrappedReply, cc, opts...)
	}
}
