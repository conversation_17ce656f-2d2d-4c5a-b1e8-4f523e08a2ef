package method_specific_write_to_store_interceptor

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	testingutils "nirvanatech.com/nirvana/external_data_management/store_management/testing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type testEnv struct {
	fx.In

	Factory Factory
}

type interceptorTestSuite struct {
	suite.Suite

	fxApp          *fxtest.App
	env            testEnv
	ctx            context.Context
	storeContextID uuid.UUID
	method         string
	cc             *grpc.ClientConn
	actualRequest  any
	mockRequest    testingutils.MockKey
}

func TestInterceptor(t *testing.T) {
	suite.Run(t, new(interceptorTestSuite))
}

func (s *interceptorTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.storeContextID = uuid.New()
	s.method = "myMethod"
	s.cc = &grpc.ClientConn{}
	s.mockRequest = testingutils.MockKey{ID: uuid.New()}

	s.fxApp = testloader.RequireStart(s.T(), &s.env)
}

func (s *interceptorTestSuite) TearDownSuite() {
	s.fxApp.RequireStop()
}

func (s *interceptorTestSuite) Test_Interceptor() {
	// case 1
	overridingConfig := write_to_store_interceptor.NewDisabledConfig()
	cfg := NewConfig(s.method, overridingConfig)
	interceptor := s.env.Factory(cfg)

	expectedCallOptionsCount := 1
	successInvoker := s.buildMockInvoker(expectedCallOptionsCount, nil)
	s.actualRequest = &s.mockRequest
	reply := testingutils.MockData{}
	err := interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&reply,
		s.cc,
		successInvoker,
	)
	s.Require().NoError(err)

	// case 2
	overridingConfig = write_to_store_interceptor.NewDisabledConfig()
	cfg = NewConfig("otherMethod", overridingConfig)
	interceptor = s.env.Factory(cfg)

	expectedCallOptionsCount = 0
	successInvoker = s.buildMockInvoker(expectedCallOptionsCount, nil)
	s.actualRequest = &s.mockRequest
	reply = testingutils.MockData{}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&reply,
		s.cc,
		successInvoker,
	)
	s.Require().NoError(err)

	// case 3
	overridingConfig = write_to_store_interceptor.NewDisabledConfig()
	cfg = NewConfig(s.method, overridingConfig)
	interceptor = s.env.Factory(cfg)

	expectedCallOptionsCount = 1
	mockedInvokerError := errors.New("unexpected invoker error")
	failureInvoker := s.buildMockInvoker(expectedCallOptionsCount, mockedInvokerError)
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&reply,
		s.cc,
		failureInvoker,
	)
	s.Require().ErrorIs(err, mockedInvokerError)
}

func (s *interceptorTestSuite) buildMockInvoker(expectedCallOptionsCount int, mockErr error) grpc.UnaryInvoker {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		opts ...grpc.CallOption,
	) error {
		s.Require().Equal(s.ctx, ctx)
		s.Require().Equal(s.method, method)
		s.Require().Equal(s.cc, cc)
		s.Require().Equal(s.actualRequest, req)
		s.Require().Equal(expectedCallOptionsCount, len(opts))

		return mockErr
	}
}
