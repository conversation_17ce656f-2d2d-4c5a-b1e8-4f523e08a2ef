package write_to_store_interceptor

import (
	"context"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"google.golang.org/grpc"

	external_data_management "nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/context_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/common"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

const interceptorName = "WriteToStoreInterceptor"

type deps struct {
	fx.In

	ContextManager context_management.ContextManager
	MetricsClient  statsd.Statter
}

// newInterceptor creates an WriteToStore interceptor with an specific configuration (e.g. data context ID,
// enabled, etc.).
//
// Methods that want to be intercepted by this interceptor must be aware that if an error is received
// from the intercepted method, data will not be saved to store. Instead, that error will bubble up
// with a nil data value. Therefore, if a certain use case requires that not nil data is returned along
// an error, then the error should be encapsulated as part of the data returned.
func newInterceptor(deps *deps, cfg *Config) grpc.UnaryClientInterceptor {
	contextManager := deps.ContextManager

	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) (retErr error) {
		var err error

		startTime := time.Now()
		defer func() {
			common.EmitMetrics(
				deps.MetricsClient,
				interceptorName,
				method,
				startTime,
				&retErr,
			)
		}()

		resource, ok := reply.(*external_data_management.Resource)
		if !ok {
			return errors.Newf("expected reply of type *common.Resource, type was %T", reply)
		}

		filteredCallOpt, err := filterCallOptions(opts)
		if err != nil {
			return errors.Wrap(err, "error filtering call options")
		}

		localCfg := cfg
		if filteredCallOpt != nil {
			localCfg = filteredCallOpt.apply(cfg)
		}

		err = localCfg.validate()
		if err != nil {
			return errors.Wrap(err, "error validating write to store interceptor config")
		}

		err = invoker(ctx, method, req, reply, cc, opts...)
		if err != nil {
			return err
		}

		if !localCfg.Enabled {
			return nil
		}

		storeKey, ok := req.(store_management.StoreKey)
		if !ok {
			return errors.Newf("request of type %T does not implement StoreKey interface", req)
		}

		contextID, err := uuid.Parse(*localCfg.ContextID)
		if err != nil {
			return errors.Wrap(err, "error parsing context ID")
		}

		resourceOriginCtxID := resource.Metadata.OriginDataContextID
		resourceComesFromSameCtx := resourceOriginCtxID != nil && *resourceOriginCtxID == contextID.String()
		resourceIsDirty := resource.Metadata.Dirty

		// We don't re-write the resource to the same context ID,
		// because we want to avoid unnecessary writes to our storage
		// layer.
		//
		// In the case of S3, not doing it leads to multiple
		// versions of the same object, which increases the amount of
		// data stored, and therefore costs.
		//
		// For other storages, it might only mean unnecessary write
		// requests, which typically also cost money.
		if resourceComesFromSameCtx && !resourceIsDirty {
			return nil
		}

		// Note that at this point `resource` should have the value gotten
		// from the rest of the interceptor chain.
		err = contextManager.Save(ctx, contextID, storeKey, resource)
		if err != nil {
			return errors.Wrapf(err, "error saving data to store contextID: %s", contextID)
		}

		return nil
	}
}

// filterCallOption will pick the grpc.CallOption that corresponds to a CallOption.
// If there's more than one CallOption, it will return an error.
func filterCallOptions(grpcCallOptions []grpc.CallOption) (*CallOption, error) {
	var internalCallOptions []CallOption

	for _, opt := range grpcCallOptions {
		if co, ok := opt.(CallOption); ok {
			if len(internalCallOptions) > 0 {
				return nil, errors.New("multiple CallOptions found")
			}
			internalCallOptions = append(internalCallOptions, co)
		}
	}

	var retval *CallOption
	if len(internalCallOptions) > 0 {
		retval = &internalCallOptions[0]
	}

	return retval, nil
}
