package write_to_store_interceptor

import (
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
)

func (cfg *Config) validate() error {
	if cfg == nil {
		return errors.New("config is nil")
	}

	if cfg.Enabled && cfg.ContextID == nil {
		return errors.New("context ID is required when enabled")
	}

	return nil
}

// NewEnabledConfig returns a new Config with the enabled flag set to true.
// This means that the write operation is enabled. It will write data to the store.
func NewEnabledConfig(contextID uuid.UUID) *Config {
	return &Config{
		ContextID: pointer_utils.String(contextID.String()),
		Enabled:   true,
	}
}

// NewDisabledConfig returns a new Config with the enabled flag set to false.
// This means that the write operation is disabled. It will not write data to the store.
func NewDisabledConfig() *Config {
	return &Config{
		ContextID: nil,
		Enabled:   false,
	}
}

type CallOption struct {
	grpc.EmptyCallOption
	apply func(config *Config) *Config
}

func WithOverridingConfig(overridingConfig *Config) CallOption {
	return CallOption{
		apply: func(_ *Config) *Config {
			return proto.Clone(overridingConfig).(*Config)
		},
	}
}
