package write_to_store_interceptor

import (
	"context"
	"encoding/json"
	"regexp"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/context_management"
	testingutils "nirvanatech.com/nirvana/external_data_management/store_management/testing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type testEnv struct {
	fx.In

	Factory Factory
}

type interceptorTestSuite struct {
	suite.Suite
	fxApp               *fxtest.App
	env                 testEnv
	ctx                 context.Context
	storeContextID      uuid.UUID
	ctrl                *gomock.Controller
	mockContextManager  *context_management.MockContextManager
	method              string
	cc                  *grpc.ClientConn
	actualRequest       any
	mockRequest         testingutils.MockKey
	mockDataInSource    testingutils.MockData
	mockBytesInSource   []byte
	successInvoker      grpc.UnaryInvoker
	failureInvoker      grpc.UnaryInvoker
	failureInvokerError error
}

func (s *interceptorTestSuite) buildMockInvoker(
	mockErr error,
	mockDirty bool,
	mockOriginDataContextID *string,
) grpc.UnaryInvoker {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		_ ...grpc.CallOption,
	) error {
		s.Require().Equal(s.ctx, ctx)
		s.Require().Equal(s.method, method)
		s.Require().Equal(s.cc, cc)

		s.Require().Equal(s.actualRequest, req)

		resource, ok := reply.(*common.Resource)
		s.Require().True(ok)

		data, ok := resource.Data.(*testingutils.MockData)
		s.Require().True(ok)

		if mockErr == nil {
			err := json.Unmarshal(s.mockBytesInSource, data)
			s.Require().NoError(err)
		}

		resource.Metadata.Dirty = mockDirty
		resource.Metadata.OriginDataContextID = mockOriginDataContextID

		return mockErr
	}
}

func (s *interceptorTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.storeContextID = uuid.New()
	s.method = "myMethod"
	s.cc = &grpc.ClientConn{}

	s.mockRequest = testingutils.MockKey{ID: uuid.New()}

	var err error
	s.mockDataInSource = testingutils.MockData{ID: uuid.New()}
	s.mockBytesInSource, err = json.Marshal(s.mockDataInSource)
	s.Require().NoError(err)

	s.successInvoker = s.buildMockInvoker(nil, false, nil)
	s.failureInvokerError = errors.New("error loading from source")
	s.failureInvoker = s.buildMockInvoker(s.failureInvokerError, false, nil)

	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())
	s.mockContextManager = context_management.NewMockContextManager(s.ctrl)
	newMockContextManager := func(actualManager context_management.ContextManager) context_management.ContextManager {
		return s.mockContextManager
	}

	s.fxApp = testloader.RequireStart(s.T(), &s.env, testloader.Use(fx.Decorate(newMockContextManager)))
}

func (s *interceptorTestSuite) TearDownSuite() {
	s.fxApp.RequireStop()
}

func (s *interceptorTestSuite) mockContextManagerSaveMethod(err error) {
	s.mockContextManager.
		EXPECT().
		Save(s.ctx, s.storeContextID, &s.mockRequest, gomock.Any()).
		Return(err).
		Times(1)
}

func (s *interceptorTestSuite) Test_WithStoreEnabledConfig() {
	cfg := NewEnabledConfig(s.storeContextID)
	interceptor := s.env.Factory(cfg)

	// CASE 1: when reply isn't of type *common.Resource
	unwrappedReply := testingutils.MockData{}
	err := interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&unwrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	rx := regexp.QuoteMeta("expected reply of type *common.Resource")
	s.Require().Regexp(rx, err.Error())

	// CASE 2: when req doesn't implement store_management.StoreKey
	s.actualRequest = s.mockRequest
	wrappedReply := common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	s.Require().Regexp("request of type testing.MockKey does not implement StoreKey interface", err.Error())

	// CASE 3: when data can be saved to store,
	// and resource doesn't have an origin data
	// context ID (i.e., comes from source).
	s.mockContextManagerSaveMethod(nil)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	mockInvoker := s.buildMockInvoker(nil, false, nil)
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		mockInvoker,
	)
	s.Require().NoError(err)

	data, ok := wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)

	// CASE 4: when data can be saved to store,
	// and resource has an origin data context
	// ID, but it's different from the one in
	// the interceptor's config.
	s.mockContextManagerSaveMethod(nil)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	originDataContextID := uuid.New().String()
	mockInvoker = s.buildMockInvoker(nil, false, &originDataContextID)
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		mockInvoker,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)

	// CASE 5: when data can be saved to store,
	// resource has the same origin data context
	// ID as the one in the interceptor's config,
	// but the resource is dirty.
	s.mockContextManagerSaveMethod(nil)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	originDataContextID = s.storeContextID.String()
	mockInvoker = s.buildMockInvoker(nil, true, &originDataContextID)
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		mockInvoker,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)

	// CASE 6: when data can be saved to store,
	// resource has the same origin data context
	// ID as the one in the interceptor's config,
	// and the resource is not dirty.
	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	originDataContextID = s.storeContextID.String()
	mockInvoker = s.buildMockInvoker(nil, false, &originDataContextID)
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		mockInvoker,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)

	// CASE 7: when data cannot be saved to store: unexpected error.
	unexpectedSaveError := errors.New("error saving to store")
	s.mockContextManagerSaveMethod(unexpectedSaveError)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().ErrorIs(err, unexpectedSaveError)

	// CASE 8: with nil context ID,
	// AND data can be saved to store.
	cfg = NewEnabledConfig(s.storeContextID)
	cfg.ContextID = nil
	interceptor = s.env.Factory(cfg)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	s.Require().Regexp("context ID is required when enabled", err.Error())

	// CASE 9: when config is overwritten by a CallOption,
	// AND data can be saved to store.
	cfg = NewEnabledConfig(s.storeContextID)
	interceptor = s.env.Factory(cfg)

	overridingConfig := NewDisabledConfig()
	co := WithOverridingConfig(overridingConfig)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
		co,
	)
	s.Require().NoError(err)
}

func (s *interceptorTestSuite) Test_WithStoreDisabledConfig() {
	cfg := NewDisabledConfig()
	interceptor := s.env.Factory(cfg)

	// CASE 1: when reply isn't of type *common.Resource
	unwrappedReply := testingutils.MockData{}
	err := interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&unwrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	rx := regexp.QuoteMeta("expected reply of type *common.Resource")
	s.Require().Regexp(rx, err.Error())

	// CASE 2: when data cannot be saved to store.
	unexpectedErrorFromSource := errors.New("error loading from source")
	failureInvoker := s.buildMockInvoker(unexpectedErrorFromSource, false, nil)

	s.actualRequest = &s.mockRequest
	wrappedReply := common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		failureInvoker,
	)
	s.Require().ErrorIs(err, unexpectedErrorFromSource)

	// CASE 3: when data can be saved to source,
	// AND req doesn't implement store_management.StoreKey.
	// Note that we don't care if it implements the StoreKey interface,
	// because the store is disabled.
	s.actualRequest = s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().NoError(err)

	data, ok := wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)

	// CASE 4: when config is overwritten by a CallOption,
	// AND data can be saved to store.
	cfg = NewDisabledConfig()
	interceptor = s.env.Factory(cfg)

	s.mockContextManagerSaveMethod(nil)

	overridingConfig := NewEnabledConfig(s.storeContextID)
	co := WithOverridingConfig(overridingConfig)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
		co,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)
}

func TestInterceptor(t *testing.T) {
	suite.Run(t, new(interceptorTestSuite))
}
