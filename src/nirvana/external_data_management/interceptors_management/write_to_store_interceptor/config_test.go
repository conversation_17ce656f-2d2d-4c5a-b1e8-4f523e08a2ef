package write_to_store_interceptor

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func Test_Config_Validate(t *testing.T) {
	// CASE 1: when config is nil
	var cfg *Config
	err := cfg.validate()
	require.Error(t, err)
	require.Regexp(t, "config is nil", err.Error())

	// CASE 2: when store is disabled, config is valid
	cfg = NewDisabledConfig()
	err = cfg.validate()
	require.NoError(t, err)

	// CASE 3: when store is enabled but context ID is nil
	cfg = NewEnabledConfig(uuid.Nil)
	cfg.ContextID = nil
	err = cfg.validate()
	require.Error(t, err)
	require.Regexp(t, "context ID is required when enabled", err.Error())

	// CASE 4: when store is enabled and context ID is not nil
	contextID := uuid.New()
	cfg = NewEnabledConfig(contextID)
	err = cfg.validate()
	require.NoError(t, err)
}
