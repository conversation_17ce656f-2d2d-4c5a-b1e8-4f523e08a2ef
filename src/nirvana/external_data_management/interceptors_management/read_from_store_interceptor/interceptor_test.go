package read_from_store_interceptor

import (
	"context"
	"encoding/json"
	"regexp"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/context_management"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	testingutils "nirvanatech.com/nirvana/external_data_management/store_management/testing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type testEnv struct {
	fx.In

	Factory Factory
}

type interceptorTestSuite struct {
	suite.Suite
	fxApp                *fxtest.App
	env                  testEnv
	ctx                  context.Context
	storeContextID       uuid.UUID
	storeParentContextID uuid.UUID
	ctrl                 *gomock.Controller
	mockContextManager   *context_management.MockContextManager
	method               string
	cc                   *grpc.ClientConn
	actualRequest        any
	mockRequest          testingutils.MockKey
	mockDataInStore      testingutils.MockData
	mockDataInSource     testingutils.MockData
	mockBytesInStore     []byte
	mockBytesInSource    []byte
	successInvoker       grpc.UnaryInvoker
	failureInvoker       grpc.UnaryInvoker
	failureInvokerError  error
}

func (s *interceptorTestSuite) buildMockInvoker(mockErr error) grpc.UnaryInvoker {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		_ ...grpc.CallOption,
	) error {
		s.Require().Equal(s.ctx, ctx)
		s.Require().Equal(s.method, method)
		s.Require().Equal(s.cc, cc)

		s.Require().Equal(s.actualRequest, req)

		resource, ok := reply.(*common.Resource)
		s.Require().True(ok)

		data, ok := resource.Data.(*testingutils.MockData)
		s.Require().True(ok)

		if mockErr == nil {
			err := json.Unmarshal(s.mockBytesInSource, data)
			s.Require().NoError(err)
		}

		return mockErr
	}
}

func (s *interceptorTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.storeContextID = uuid.New()
	s.storeParentContextID = uuid.New()
	s.method = "myMethod"
	s.cc = &grpc.ClientConn{}

	s.mockRequest = testingutils.MockKey{ID: uuid.New()}

	var err error
	s.mockDataInStore = testingutils.MockData{ID: uuid.New()}
	s.mockBytesInStore, err = json.Marshal(s.mockDataInStore)
	s.Require().NoError(err)

	s.mockDataInSource = testingutils.MockData{ID: uuid.New()}
	s.mockBytesInSource, err = json.Marshal(s.mockDataInSource)
	s.Require().NoError(err)

	s.successInvoker = s.buildMockInvoker(nil)
	s.failureInvokerError = errors.New("error loading from source")
	s.failureInvoker = s.buildMockInvoker(s.failureInvokerError)

	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())
	s.mockContextManager = context_management.NewMockContextManager(s.ctrl)
	newMockContextManager := func(actualManager context_management.ContextManager) context_management.ContextManager {
		return s.mockContextManager
	}

	s.fxApp = testloader.RequireStart(s.T(), &s.env, testloader.Use(fx.Decorate(newMockContextManager)))
}

func (s *interceptorTestSuite) TearDownSuite() {
	s.fxApp.RequireStop()
}

func (s *interceptorTestSuite) mockContextManagerLoadRecursivelyMethodWithSuccess() {
	s.mockContextManager.
		EXPECT().
		LoadRecursively(s.ctx, s.storeContextID, &s.mockRequest, gomock.Any(), gomock.Any()).
		DoAndReturn(
			func(
				ctx context.Context,
				storeContextID uuid.UUID,
				storeKey store_management.StoreKey,
				lookupDepth int,
				v *common.Resource,
			) error {
				return json.Unmarshal(s.mockBytesInStore, v.Data)
			},
		)
}

func (s *interceptorTestSuite) mockContextManagerLoadRecursivelyMethodWithError(err error) {
	s.mockContextManager.
		EXPECT().
		LoadRecursively(s.ctx, s.storeContextID, &s.mockRequest, gomock.Any(), gomock.Any()).
		Return(err)
}

func (s *interceptorTestSuite) Test_WithStoreFirstConfig() {
	cfg := NewStoreFirstConfig(s.storeContextID, 1)
	interceptor := s.env.Factory(cfg)

	// CASE 1: when reply isn't of type *common.Resource
	unwrappedReply := testingutils.MockData{}
	err := interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&unwrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	rx := regexp.QuoteMeta("expected reply of type *common.Resource")
	s.Require().Regexp(rx, err.Error())

	// CASE 2: when req doesn't implement store_management.StoreKey
	s.actualRequest = s.mockRequest
	wrappedReply := common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	s.Require().Regexp("request of type testing.MockKey does not implement StoreKey interface", err.Error())

	// CASE 3: when data can be loaded from store.
	s.mockContextManagerLoadRecursivelyMethodWithSuccess()
	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().NoError(err)

	data, ok := wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInStore, *data)

	// CASE 4: when data cannot be loaded from store: unexpected error.
	unexpectedLoadErrorFromStore := errors.New("error loading from store")
	s.mockContextManagerLoadRecursivelyMethodWithError(unexpectedLoadErrorFromStore)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().ErrorIs(err, unexpectedLoadErrorFromStore)

	// CASE 5: when data cannot be loaded from store: ResourceNotFoundError,
	// AND data cannot be loaded from source.
	s.mockContextManagerLoadRecursivelyMethodWithError(store_management.ResourceNotFoundError)
	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.failureInvoker,
	)
	s.Require().ErrorIs(err, s.failureInvokerError)

	// CASE 6: when data cannot be loaded from store: ResourceNotFoundError,
	// BUT can be loaded from source.
	s.mockContextManagerLoadRecursivelyMethodWithError(store_management.ResourceNotFoundError)
	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)

	// CASE 7: with nil context ID,
	// AND data can be loaded from store.
	cfg = NewStoreFirstConfig(s.storeContextID, 1)
	cfg.ContextID = nil
	interceptor = s.env.Factory(cfg)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	s.Require().Regexp("context ID is required when read mode is not disabled", err.Error())

	// CASE 8: when config is overwritten by a CallOption,
	// AND data can be loaded from store.
	cfg = NewStoreDisabledConfig()
	interceptor = s.env.Factory(cfg)

	s.mockContextManagerLoadRecursivelyMethodWithSuccess()

	newConfig := NewStoreFirstConfig(s.storeContextID, 1)
	co := WithOverridingConfig(newConfig)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
		co,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInStore, *data)
}

func (s *interceptorTestSuite) Test_WithStoreOnlyConfig() {
	cfg := NewStoreOnlyConfig(s.storeContextID, 1)
	interceptor := s.env.Factory(cfg)

	// CASE 1: when reply isn't of type *common.Resource
	unwrappedReply := testingutils.MockData{}
	err := interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&unwrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	rx := regexp.QuoteMeta("expected reply of type *common.Resource")
	s.Require().Regexp(rx, err.Error())

	// CASE 2: when req doesn't implement store_management.StoreKey
	s.actualRequest = s.mockRequest
	wrappedReply := common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	s.Require().Regexp("request of type testing.MockKey does not implement StoreKey interface", err.Error())

	// CASE 3: when data can be loaded from store.
	s.mockContextManagerLoadRecursivelyMethodWithSuccess()

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().NoError(err)

	data, ok := wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInStore, *data)

	// CASE 4: when data cannot be loaded from store: unexpected error.
	unexpectedLoadErrorFromStore := errors.New("error loading from store")
	s.mockContextManagerLoadRecursivelyMethodWithError(unexpectedLoadErrorFromStore)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().ErrorIs(err, unexpectedLoadErrorFromStore)

	// CASE 5: when data cannot be loaded from store: ResourceNotFoundError.
	s.mockContextManagerLoadRecursivelyMethodWithError(store_management.ResourceNotFoundError)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().ErrorIs(err, store_management.ResourceNotFoundError)

	// CASE 6: with nil context ID,
	// AND data can be loaded from store.
	cfg = NewStoreOnlyConfig(s.storeContextID, 1)
	cfg.ContextID = nil
	interceptor = s.env.Factory(cfg)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	s.Require().Regexp("context ID is required when read mode is not disabled", err.Error())

	// CASE 7: when config is overwritten by a CallOption,
	// AND data can be loaded from store.
	cfg = NewStoreDisabledConfig()
	interceptor = s.env.Factory(cfg)

	s.mockContextManagerLoadRecursivelyMethodWithSuccess()

	newConfig := NewStoreOnlyConfig(s.storeContextID, 1)
	co := WithOverridingConfig(newConfig)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
		co,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInStore, *data)
}

func (s *interceptorTestSuite) Test_WithStoreDisabledConfig() {
	cfg := NewStoreDisabledConfig()
	interceptor := s.env.Factory(cfg)

	// CASE 1: when reply isn't of type *common.Resource
	unwrappedReply := testingutils.MockData{}
	err := interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&unwrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().Error(err)
	rx := regexp.QuoteMeta("expected reply of type *common.Resource")
	s.Require().Regexp(rx, err.Error())

	// CASE 2: when data cannot be loaded from source.
	s.actualRequest = &s.mockRequest
	wrappedReply := common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.failureInvoker,
	)
	s.Require().ErrorIs(err, s.failureInvokerError)

	// CASE 3: when data can be loaded from source,
	// AND req doesn't implement store_management.StoreKey.
	// Note that we don't care if it implements the StoreKey interface,
	// because the store is disabled.
	s.actualRequest = s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
	)
	s.Require().NoError(err)

	data, ok := wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)

	// CASE 4: when config is overwritten by a CallOption,
	// AND data can be loaded from source.
	cfg = NewStoreOnlyConfig(s.storeContextID, 1)
	interceptor = s.env.Factory(cfg)

	newConfig := NewStoreDisabledConfig()
	co := WithOverridingConfig(newConfig)

	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.successInvoker,
		co,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInSource, *data)
}

// We had a bug before where if you called the interceptor with a
// CallOption that modified the config, that
// modification was also applied to any subsequent call of the interceptor.
// This test was added to ensure that this bug is not reintroduced.
func (s *interceptorTestSuite) Test_WithSequenceOfCallOptions() {
	// We create an interceptor with an initial config that retrieves from store.
	cfg := NewStoreFirstConfig(s.storeContextID, 1)
	interceptor := s.env.Factory(cfg)

	// We call the interceptor, expecting no error, and that data is loaded
	// into the reply param.
	s.mockContextManagerLoadRecursivelyMethodWithSuccess()
	s.actualRequest = &s.mockRequest
	wrappedReply := common.Resource{Data: &testingutils.MockData{}}
	err := interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.failureInvoker,
	)
	s.Require().NoError(err)

	data, ok := wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInStore, *data)

	// We call the interceptor again, with a CallOption
	// that tells it to retrieve data from source. This should return an error,
	// because the underlying invoker should be called.
	newConfig := NewStoreDisabledConfig()
	co := WithOverridingConfig(newConfig)
	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.failureInvoker,
		co,
	)
	s.Require().ErrorIs(err, s.failureInvokerError)

	// We call the interceptor again, without any CallOption.
	// This should not return an error, as the config used should be the original one.
	// Before the bug was fixed, the interceptor would have used the one
	// passed via a call option in the past request.
	s.mockContextManagerLoadRecursivelyMethodWithSuccess()
	s.actualRequest = &s.mockRequest
	wrappedReply = common.Resource{Data: &testingutils.MockData{}}
	err = interceptor(
		s.ctx,
		s.method,
		s.actualRequest,
		&wrappedReply,
		s.cc,
		s.failureInvoker,
	)
	s.Require().NoError(err)

	data, ok = wrappedReply.Data.(*testingutils.MockData)
	s.Require().True(ok)
	s.Require().Equal(s.mockDataInStore, *data)
}

func TestInterceptor(t *testing.T) {
	suite.Run(t, new(interceptorTestSuite))
}
