package read_from_store_interceptor

import (
	"slices"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
)

func (cfg *Config) validate() error {
	if cfg == nil {
		return errors.New("config cannot be nil")
	}

	if !slices.Contains(supportedReadModes, cfg.ReadMode) {
		return errors.Newf("received unsupported store read mode: %s", cfg.ReadMode.String())
	}

	if cfg.ReadMode != ReadMode_ReadMode_StoreDisabled {
		if cfg.ContextID == nil {
			return errors.New("context ID is required when read mode is not disabled")
		}

		if cfg.LookupDepth < 1 {
			return errors.New("lookup depth must be greater than or equal to 1")
		}
	}

	return nil
}

// NewStoreFirstConfig returns a Config for which the interceptor first tries
// to retrieve from store, and if not there, it calls the following interceptor.
func NewStoreFirstConfig(contextID uuid.UUID, lookupDepth int) *Config {
	return &Config{
		ReadMode:    ReadMode_ReadMode_StoreFirst,
		ContextID:   pointer_utils.String(contextID.String()),
		LookupDepth: int64(lookupDepth),
	}
}

// NewStoreOnlyConfig returns a Config for which the interceptor only looks for
// data in store.
func NewStoreOnlyConfig(contextID uuid.UUID, lookupDepth int) *Config {
	return &Config{
		ReadMode:    ReadMode_ReadMode_StoreOnly,
		ContextID:   pointer_utils.String(contextID.String()),
		LookupDepth: int64(lookupDepth),
	}
}

// NewStoreDisabledConfig returns a Config for which the interceptor always
// calls the following interceptor, without interacting with the underlying store.
func NewStoreDisabledConfig() *Config {
	return &Config{
		ReadMode:    ReadMode_ReadMode_StoreDisabled,
		ContextID:   nil,
		LookupDepth: 0,
	}
}

type CallOption struct {
	grpc.EmptyCallOption

	apply func(*Config) *Config
}

func WithOverridingConfig(overridingConfig *Config) CallOption {
	return CallOption{
		apply: func(_ *Config) *Config {
			return proto.Clone(overridingConfig).(*Config)
		},
	}
}
