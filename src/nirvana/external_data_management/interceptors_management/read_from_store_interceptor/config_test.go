package read_from_store_interceptor

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func Test_Config_Validate(t *testing.T) {
	contextID := uuid.New()
	// CASE 1: when config is nil
	var cfg *Config
	err := cfg.validate()
	require.Error(t, err)
	require.Regexp(t, "config cannot be nil", err.Error())

	// CASE 2: when store is disabled, config is valid
	cfg = NewStoreDisabledConfig()
	err = cfg.validate()
	require.NoError(t, err)

	// CASE 3: when store is enabled (store only mode), config is valid
	cfg = NewStoreOnlyConfig(contextID, 1)
	err = cfg.validate()
	require.NoError(t, err)

	// CASE 4: when store is enabled (store first mode), config is valid
	cfg = NewStoreFirstConfig(contextID, 1)
	err = cfg.validate()
	require.NoError(t, err)

	// CASE 5: when store is enabled but context ID is empty
	cfg = NewStoreOnlyConfig(uuid.Nil, 1)
	cfg.ContextID = nil
	err = cfg.validate()
	require.Error(t, err)
	require.Regexp(t, "context ID is required when read mode is not disabled", err.Error())

	// CASE 6: when store is enabled but lookup depth is invalid
	cfg = NewStoreOnlyConfig(contextID, -1)
	err = cfg.validate()
	require.Error(t, err)
	require.Regexp(t, "lookup depth must be greater than or equal to 1", err.Error())
}
