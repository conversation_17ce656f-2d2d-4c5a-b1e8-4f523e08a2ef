package read_from_store_interceptor

import (
	"context"
	"strconv"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"google.golang.org/grpc"

	external_data_management "nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/context_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/common"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

const (
	interceptorName             = "ReadFromStoreInterceptor"
	dataObtainedFromStoreTagKey = "data_obtained_from_store"
)

var supportedReadModes = []ReadMode{
	ReadMode_ReadMode_StoreFirst,
	ReadMode_ReadMode_StoreOnly,
	ReadMode_ReadMode_StoreDisabled,
}

type deps struct {
	fx.In

	ContextManager context_management.ContextManager
	MetricsClient  statsd.Statter
}

// newInterceptor creates a ReadFromStore interceptor with a specific configuration (e.g. data context ID,
// read modes, etc.).
func newInterceptor(deps *deps, cfg *Config) grpc.UnaryClientInterceptor {
	contextManager := deps.ContextManager
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) (retErr error) {
		dataObtainedFromStore := true

		startTime := time.Now()
		defer func() {
			common.EmitMetrics(
				deps.MetricsClient,
				interceptorName,
				method,
				startTime,
				&retErr,
				statsd.Tag{
					dataObtainedFromStoreTagKey,
					strconv.FormatBool(dataObtainedFromStore),
				},
			)
		}()

		resource, ok := reply.(*external_data_management.Resource)
		if !ok {
			return errors.Newf("expected reply of type *common.Resource, type was %T", reply)
		}

		filteredCallOpt, err := filterCallOptions(opts)
		if err != nil {
			return errors.Wrap(err, "error filtering call options")
		}

		localCfg := cfg
		if filteredCallOpt != nil {
			localCfg = filteredCallOpt.apply(localCfg)
		}

		err = localCfg.validate()
		if err != nil {
			return errors.Wrap(err, "error validating read from store interceptor config")
		}

		callNextInterceptor := func() error {
			dataObtainedFromStore = false
			return invoker(ctx, method, req, reply, cc, opts...)
		}

		readMode := localCfg.ReadMode
		if readMode == ReadMode_ReadMode_StoreDisabled {
			err = callNextInterceptor()
			if err != nil {
				return errors.Wrap(err, "error calling next interceptor")
			}
		} else {
			storeKey, ok := req.(store_management.StoreKey)
			if !ok {
				return errors.Newf("request of type %T does not implement StoreKey interface", req)
			}

			var contextID uuid.UUID
			contextID, err = uuid.Parse(*localCfg.ContextID)
			if err != nil {
				return errors.Wrap(err, "error parsing context ID")
			}

			lookupDepth := int(localCfg.LookupDepth)

			err = contextManager.LoadRecursively(ctx, contextID, storeKey, lookupDepth, resource)
			if err != nil {
				if !errors.Is(err, store_management.ResourceNotFoundError) || readMode == ReadMode_ReadMode_StoreOnly {
					return errors.Wrapf(err, "error loading data from store contextID: %s", contextID)
				}
				err = callNextInterceptor()
				if err != nil {
					return err
				}
			}
		}

		return nil
	}
}

// filterCallOption will pick the grpc.CallOption that corresponds to a CallOption.
// If there's more than one CallOption, it will return an error.
func filterCallOptions(grpcCallOptions []grpc.CallOption) (*CallOption, error) {
	var internalCallOptions []CallOption
	for _, opt := range grpcCallOptions {
		if co, ok := opt.(CallOption); ok {
			if len(internalCallOptions) > 0 {
				return nil, errors.New("multiple CallOptions found")
			}
			internalCallOptions = append(internalCallOptions, co)
		}
	}

	var retval *CallOption
	if len(internalCallOptions) > 0 {
		retval = &internalCallOptions[0]
	}

	return retval, nil
}
