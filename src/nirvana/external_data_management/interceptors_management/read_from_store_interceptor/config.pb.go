// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: interceptors_management/read_from_store_interceptor/config.proto

package read_from_store_interceptor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReadMode int32

const (
	ReadMode_ReadMode_Invalid       ReadMode = 0
	ReadMode_ReadMode_StoreFirst    ReadMode = 1
	ReadMode_ReadMode_StoreOnly     ReadMode = 2
	ReadMode_ReadMode_StoreDisabled ReadMode = 3
)

// Enum value maps for ReadMode.
var (
	ReadMode_name = map[int32]string{
		0: "ReadMode_Invalid",
		1: "ReadMode_StoreFirst",
		2: "ReadMode_StoreOnly",
		3: "ReadMode_StoreDisabled",
	}
	ReadMode_value = map[string]int32{
		"ReadMode_Invalid":       0,
		"ReadMode_StoreFirst":    1,
		"ReadMode_StoreOnly":     2,
		"ReadMode_StoreDisabled": 3,
	}
)

func (x ReadMode) Enum() *ReadMode {
	p := new(ReadMode)
	*p = x
	return p
}

func (x ReadMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReadMode) Descriptor() protoreflect.EnumDescriptor {
	return file_interceptors_management_read_from_store_interceptor_config_proto_enumTypes[0].Descriptor()
}

func (ReadMode) Type() protoreflect.EnumType {
	return &file_interceptors_management_read_from_store_interceptor_config_proto_enumTypes[0]
}

func (x ReadMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReadMode.Descriptor instead.
func (ReadMode) EnumDescriptor() ([]byte, []int) {
	return file_interceptors_management_read_from_store_interceptor_config_proto_rawDescGZIP(), []int{0}
}

type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReadMode    ReadMode `protobuf:"varint,1,opt,name=readMode,proto3,enum=read_from_store_interceptor.ReadMode" json:"readMode,omitempty"`
	ContextID   *string  `protobuf:"bytes,2,opt,name=contextID,proto3,oneof" json:"contextID,omitempty"`
	LookupDepth int64    `protobuf:"varint,3,opt,name=lookupDepth,proto3" json:"lookupDepth,omitempty"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_interceptors_management_read_from_store_interceptor_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_interceptors_management_read_from_store_interceptor_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_interceptors_management_read_from_store_interceptor_config_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetReadMode() ReadMode {
	if x != nil {
		return x.ReadMode
	}
	return ReadMode_ReadMode_Invalid
}

func (x *Config) GetContextID() string {
	if x != nil && x.ContextID != nil {
		return *x.ContextID
	}
	return ""
}

func (x *Config) GetLookupDepth() int64 {
	if x != nil {
		return x.LookupDepth
	}
	return 0
}

var File_interceptors_management_read_from_store_interceptor_config_proto protoreflect.FileDescriptor

var file_interceptors_management_read_from_store_interceptor_config_proto_rawDesc = []byte{
	0x0a, 0x40, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63,
	0x65, 0x70, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x22,
	0x9e, 0x01, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x41, 0x0a, 0x08, 0x72, 0x65,
	0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x72,
	0x65, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x44, 0x88, 0x01, 0x01,
	0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x44, 0x65, 0x70, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x44, 0x65, 0x70,
	0x74, 0x68, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x44,
	0x2a, 0x6d, 0x0a, 0x08, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x10,
	0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x5f, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x46, 0x69, 0x72, 0x73, 0x74, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x52,
	0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4f, 0x6e, 0x6c,
	0x79, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x65, 0x61, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x5f,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x03, 0x42,
	0x5b, 0x5a, 0x59, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x73,
	0x2f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_interceptors_management_read_from_store_interceptor_config_proto_rawDescOnce sync.Once
	file_interceptors_management_read_from_store_interceptor_config_proto_rawDescData = file_interceptors_management_read_from_store_interceptor_config_proto_rawDesc
)

func file_interceptors_management_read_from_store_interceptor_config_proto_rawDescGZIP() []byte {
	file_interceptors_management_read_from_store_interceptor_config_proto_rawDescOnce.Do(func() {
		file_interceptors_management_read_from_store_interceptor_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_interceptors_management_read_from_store_interceptor_config_proto_rawDescData)
	})
	return file_interceptors_management_read_from_store_interceptor_config_proto_rawDescData
}

var file_interceptors_management_read_from_store_interceptor_config_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_interceptors_management_read_from_store_interceptor_config_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_interceptors_management_read_from_store_interceptor_config_proto_goTypes = []interface{}{
	(ReadMode)(0),  // 0: read_from_store_interceptor.ReadMode
	(*Config)(nil), // 1: read_from_store_interceptor.Config
}
var file_interceptors_management_read_from_store_interceptor_config_proto_depIdxs = []int32{
	0, // 0: read_from_store_interceptor.Config.readMode:type_name -> read_from_store_interceptor.ReadMode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_interceptors_management_read_from_store_interceptor_config_proto_init() }
func file_interceptors_management_read_from_store_interceptor_config_proto_init() {
	if File_interceptors_management_read_from_store_interceptor_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_interceptors_management_read_from_store_interceptor_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_interceptors_management_read_from_store_interceptor_config_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_interceptors_management_read_from_store_interceptor_config_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_interceptors_management_read_from_store_interceptor_config_proto_goTypes,
		DependencyIndexes: file_interceptors_management_read_from_store_interceptor_config_proto_depIdxs,
		EnumInfos:         file_interceptors_management_read_from_store_interceptor_config_proto_enumTypes,
		MessageInfos:      file_interceptors_management_read_from_store_interceptor_config_proto_msgTypes,
	}.Build()
	File_interceptors_management_read_from_store_interceptor_config_proto = out.File
	file_interceptors_management_read_from_store_interceptor_config_proto_rawDesc = nil
	file_interceptors_management_read_from_store_interceptor_config_proto_goTypes = nil
	file_interceptors_management_read_from_store_interceptor_config_proto_depIdxs = nil
}
