# About interceptors

Interceptors functions are gRPC interceptors that we use to wrap the behavior
of methods exposed on the FetcherClient / ProcessorClient interfaces.

There are two types of interceptors: pre-defined and consumer-specific. Predefined interceptors live inside this
directory, as they offer common functionality needed by multiple consumers. On the other hand, consumer-specific
interceptors are defined outside this directory, and are passed to the factory functions when instantiating a
FetcherClient or ProcessorClient.