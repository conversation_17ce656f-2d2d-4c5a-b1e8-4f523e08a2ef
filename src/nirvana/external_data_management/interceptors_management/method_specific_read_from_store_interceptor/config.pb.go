// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: interceptors_management/method_specific_read_from_store_interceptor/config.proto

package method_specific_read_from_store_interceptor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	read_from_store_interceptor "nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MethodName       string                              `protobuf:"bytes,1,opt,name=methodName,proto3" json:"methodName,omitempty"`
	OverridingConfig *read_from_store_interceptor.Config `protobuf:"bytes,2,opt,name=overridingConfig,proto3" json:"overridingConfig,omitempty"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetMethodName() string {
	if x != nil {
		return x.MethodName
	}
	return ""
}

func (x *Config) GetOverridingConfig() *read_from_store_interceptor.Config {
	if x != nil {
		return x.OverridingConfig
	}
	return nil
}

var File_interceptors_management_method_specific_read_from_store_interceptor_config_proto protoreflect.FileDescriptor

var file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDesc = []byte{
	0x0a, 0x50, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63,
	0x65, 0x70, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x2b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x1a,
	0x40, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65,
	0x70, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x79, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x10, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70,
	0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x76, 0x5a, 0x74,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65,
	0x70, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDescOnce sync.Once
	file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDescData = file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDesc
)

func file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDescGZIP() []byte {
	file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDescOnce.Do(func() {
		file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDescData)
	})
	return file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDescData
}

var file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_goTypes = []interface{}{
	(*Config)(nil), // 0: method_specific_read_from_store_interceptor.Config
	(*read_from_store_interceptor.Config)(nil), // 1: read_from_store_interceptor.Config
}
var file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_depIdxs = []int32{
	1, // 0: method_specific_read_from_store_interceptor.Config.overridingConfig:type_name -> read_from_store_interceptor.Config
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() {
	file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_init()
}
func file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_init() {
	if File_interceptors_management_method_specific_read_from_store_interceptor_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_goTypes,
		DependencyIndexes: file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_depIdxs,
		MessageInfos:      file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_msgTypes,
	}.Build()
	File_interceptors_management_method_specific_read_from_store_interceptor_config_proto = out.File
	file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_rawDesc = nil
	file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_goTypes = nil
	file_interceptors_management_method_specific_read_from_store_interceptor_config_proto_depIdxs = nil
}
