load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "method_specific_read_from_store_interceptor_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor",
    proto = "//proto/interceptors_management/method_specific_read_from_store_interceptor:method_specific_read_from_store_interceptor_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
    ],
)

# keep
go_proto_link(
    name = "method_specific_read_from_store_interceptor_go_proto_link",
    dep = ":method_specific_read_from_store_interceptor_go_proto",
    version = "v1",
)

go_library(
    name = "method_specific_read_from_store_interceptor",
    srcs = [
        "config.go",
        "factory.go",
        "fx.go",
        "interceptor.go",
    ],
    embed = [":method_specific_read_from_store_interceptor_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/interceptors_management/common",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@org_golang_google_grpc//:grpc",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "method_specific_read_from_store_interceptor_test",
    srcs = ["interceptor_test.go"],
    embed = [":method_specific_read_from_store_interceptor"],
    deps = [
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/store_management/testing",
        "//nirvana/infra/fx/testloader",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_grpc//:grpc",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
