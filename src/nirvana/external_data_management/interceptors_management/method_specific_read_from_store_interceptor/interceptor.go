package method_specific_read_from_store_interceptor

import (
	"context"
	"time"

	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/interceptors_management/common"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
)

const interceptorName = "method_specific_read_from_store_interceptor"

// newInterceptor creates a new method_specific_read_from_store_interceptor.
// This interceptor configures the behavior of the read-from-store interceptor
// through a CallOption. It will only add the CallOption if the method name matches
// the method name in the interceptor config.
func newInterceptor(deps *deps, cfg *Config) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) (retErr error) {
		startTime := time.Now()
		defer func() {
			common.EmitMetrics(
				deps.MetricsClient,
				interceptorName,
				method,
				startTime,
				&retErr,
			)
		}()

		if cfg.MethodName == method {
			callOpt := read_from_store_interceptor.WithOverridingConfig(cfg.OverridingConfig)
			opts = append(opts, callOpt)
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}
