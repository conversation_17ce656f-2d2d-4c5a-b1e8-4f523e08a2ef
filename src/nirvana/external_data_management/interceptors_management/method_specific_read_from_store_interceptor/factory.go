package method_specific_read_from_store_interceptor

import (
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"google.golang.org/grpc"
)

type deps struct {
	fx.In

	MetricsClient statsd.Statter
}

type Factory func(cfg *Config) grpc.UnaryClientInterceptor

func newFactory(deps deps) Factory {
	return func(cfg *Config) grpc.UnaryClientInterceptor {
		return newInterceptor(&deps, cfg)
	}
}
