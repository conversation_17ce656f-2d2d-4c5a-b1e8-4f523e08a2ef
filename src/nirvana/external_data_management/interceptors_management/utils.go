package interceptors_management

import (
	"github.com/google/uuid"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
)

const defaultContextLookupDepth = 10

func NewWritableStoreFirstInterceptors(
	readFromStoreInterceptorFactory read_from_store_interceptor.Factory,
	writeToStoreInterceptorFactory write_to_store_interceptor.Factory,
	dataContextID uuid.UUID,
) []grpc.UnaryClientInterceptor {
	writeToStoreInterceptor := writeToStoreInterceptorFactory(
		write_to_store_interceptor.NewEnabledConfig(dataContextID),
	)
	readFromStoreInterceptor := readFromStoreInterceptorFactory(
		read_from_store_interceptor.NewStoreFirstConfig(dataContextID, defaultContextLookupDepth),
	)
	return []grpc.UnaryClientInterceptor{
		writeToStoreInterceptor,
		readFromStoreInterceptor,
	}
}
