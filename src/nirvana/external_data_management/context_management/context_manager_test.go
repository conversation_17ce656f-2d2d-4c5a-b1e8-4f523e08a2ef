package context_management

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	store_testing_utils "nirvanatech.com/nirvana/external_data_management/store_management/testing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type contextManagerTestEnv struct {
	fx.In

	ContextManager ContextManager
}

type contextManagerTestSuite struct {
	suite.Suite
	ctx   context.Context
	env   contextManagerTestEnv
	fxapp *fxtest.App
}

func TestContextManager(t *testing.T) {
	suite.Run(t, new(contextManagerTestSuite))
}

func (s *contextManagerTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)
}

func (s *contextManagerTestSuite) TestLoadRecursively_LoadResource() {
	// Create a chain of contexts: context4 -> context3 -> context2 -> context1 (root)
	context1ID := uuid.New() // root
	context2ID := uuid.New()
	context3ID := uuid.New()
	context4ID := uuid.New()

	// Setup context chain
	s.Require().NoError(s.env.ContextManager.SetParentContextID(s.ctx, context2ID, context1ID))
	s.Require().NoError(s.env.ContextManager.SetParentContextID(s.ctx, context3ID, context2ID))
	s.Require().NoError(s.env.ContextManager.SetParentContextID(s.ctx, context4ID, context3ID))

	// Save test data in root context
	context1Data := store_testing_utils.MockData{ID: uuid.New()}
	context1Key := store_testing_utils.MockKey{ID: uuid.New(), Param: &store_testing_utils.MockParam{ID: uuid.New()}}
	resource := &common.Resource{Data: &context1Data}
	s.Require().NoError(s.env.ContextManager.Save(s.ctx, context1ID, &context1Key, resource))

	// Save test data in context3
	context3Data := store_testing_utils.MockData{ID: uuid.New()}
	context3Key := store_testing_utils.MockKey{ID: uuid.New(), Param: &store_testing_utils.MockParam{ID: uuid.New()}}
	resource = &common.Resource{Data: &context3Data}
	s.Require().NoError(s.env.ContextManager.Save(s.ctx, context3ID, &context3Key, resource))

	testCases := []struct {
		name        string
		contextID   uuid.UUID
		resourceKey store_testing_utils.MockKey
		lookupDepth int
		wantData    *store_testing_utils.MockData
		wantErr     error
	}{
		{
			name:        "success - data found in root context after traversing chain",
			contextID:   context4ID,
			resourceKey: context1Key,
			lookupDepth: 4,
			wantData:    &context1Data,
			wantErr:     nil,
		},
		{
			name:        "success - data found in context3",
			contextID:   context4ID,
			resourceKey: context3Key,
			lookupDepth: 4,
			wantData:    &context3Data,
			wantErr:     nil,
		},
		{
			name:      "error - resource not found in any context",
			contextID: context4ID,
			resourceKey: store_testing_utils.MockKey{
				ID: uuid.New(),
				Param: &store_testing_utils.MockParam{
					ID: uuid.New(),
				},
			}, // Key doesn't exist in any context
			lookupDepth: 4,
			wantErr:     store_management.ResourceNotFoundError,
		},
		{
			name:        "error - resource not found with lookup depth 1",
			contextID:   context4ID,
			resourceKey: context1Key, // Key can't be reached
			lookupDepth: 1,
			wantErr:     store_management.ResourceNotFoundError,
		},
	}

	dataZeroVal := &store_testing_utils.MockData{}
	for _, tc := range testCases {
		s.Run(tc.name, func() {
			res := common.Resource{Data: &store_testing_utils.MockData{}}
			err := s.env.ContextManager.LoadRecursively(s.ctx, tc.contextID, &tc.resourceKey, tc.lookupDepth, &res)
			if tc.wantErr != nil {
				s.Require().ErrorIs(err, tc.wantErr)
				s.Require().Equal(dataZeroVal, res.Data)
				return
			}
			s.Require().Nil(err)
			s.Equal(tc.wantData, res.Data)
		})
	}
}

func (s *contextManagerTestSuite) TestLoadRecursively_ContextChaining() {
	// Create a chain of contexts: context4 -> context3 -> context2 -> context1 (root)
	context1ID := uuid.New() // root
	context2ID := uuid.New()
	context3ID := uuid.New()
	context4ID := uuid.New()

	// Setup context chain
	s.Require().NoError(s.env.ContextManager.SetParentContextID(s.ctx, context2ID, context1ID))
	s.Require().NoError(s.env.ContextManager.SetParentContextID(s.ctx, context3ID, context2ID))
	s.Require().NoError(s.env.ContextManager.SetParentContextID(s.ctx, context4ID, context3ID))

	testCases := []struct {
		name             string
		currentContextID uuid.UUID
		wantParents      []uuid.UUID
		wantError        error
	}{
		{
			name:             "context4 should have 3 parents",
			currentContextID: context4ID,
			wantParents:      []uuid.UUID{context3ID, context2ID, context1ID},
		},
		{
			name:             "context3 should have 2 parents",
			currentContextID: context3ID,
			wantParents:      []uuid.UUID{context2ID, context1ID},
		},
		{
			name:             "context2 should have 1 parent",
			currentContextID: context2ID,
			wantParents:      []uuid.UUID{context1ID},
		},
		{
			name:             "context1 (root) should have no parents",
			currentContextID: context1ID,
			wantParents:      []uuid.UUID{},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			parents := make([]uuid.UUID, 0)
			currentID := tc.currentContextID
			for {
				parentID, err := s.env.ContextManager.GetParentContextID(s.ctx, currentID)
				if err != nil {
					s.Require().ErrorIs(err, NoParentContextError)
					break
				}
				parents = append(parents, *parentID)
				currentID = *parentID
			}
			s.Equal(tc.wantParents, parents)
		})
	}
}

func (s *contextManagerTestSuite) TestGetParentContextID() {
	parentContextID := uuid.New()
	parentContextMetadata := &ContextMetadata{}
	resource := &common.Resource{Data: parentContextMetadata}
	s.Require().NoError(s.env.ContextManager.Save(s.ctx, parentContextID, parentContextMetadata, resource))

	childContextID := uuid.New()
	childContextMetadata := &ContextMetadata{ParentContextID: pointer_utils.ToPointer(parentContextID.String())}
	resource = &common.Resource{Data: childContextMetadata}
	s.Require().NoError(s.env.ContextManager.Save(s.ctx, childContextID, childContextMetadata, resource))

	testCases := []struct {
		name                string
		contextID           uuid.UUID
		wantParentContextID uuid.UUID
		wantError           error
	}{
		{
			name:                "successfully found ParentContextID",
			contextID:           childContextID,
			wantParentContextID: parentContextID,
			wantError:           nil,
		},
		{
			name:                "couldn't find ContextMetadata resource within context",
			contextID:           uuid.New(),
			wantParentContextID: uuid.Nil,
			wantError:           NoParentContextError,
		},
		{
			name:                "no parent context",
			contextID:           parentContextID,
			wantParentContextID: uuid.Nil,
			wantError:           NoParentContextError,
		},
	}

	for _, testCase := range testCases {
		s.Run(testCase.name, func() {
			gotParentContextID, err := s.env.ContextManager.GetParentContextID(s.ctx, testCase.contextID)
			s.Require().ErrorIs(err, testCase.wantError)
			if testCase.wantError != nil {
				return
			}
			s.Equal(testCase.wantParentContextID, *gotParentContextID)
		})
	}
}

func (s *contextManagerTestSuite) TestSetParentContextID() {
	parentContextID := uuid.New()
	parentContextMetadata := &ContextMetadata{}
	resource := &common.Resource{Data: parentContextMetadata}
	s.Require().NoError(s.env.ContextManager.Save(s.ctx, parentContextID, parentContextMetadata, resource))

	childContextID := uuid.New()
	childContextMetadata := &ContextMetadata{}
	resource = &common.Resource{Data: childContextMetadata}
	s.Require().NoError(s.env.ContextManager.Save(s.ctx, childContextID, childContextMetadata, resource))

	// We verify that the parent context iD in the child context hasn't been set.
	_, err := s.env.ContextManager.GetParentContextID(s.ctx, childContextID)
	s.Require().ErrorIs(err, NoParentContextError)

	// We set the parent context ID in the child context.
	err = s.env.ContextManager.SetParentContextID(s.ctx, childContextID, parentContextID)
	s.NoError(err)

	// We verify that the parent context ID in the child context has been set.
	storeParentContextID, err := s.env.ContextManager.GetParentContextID(s.ctx, childContextID)
	s.Require().NoError(err)
	s.Equal(parentContextID, *storeParentContextID)

	// Trying to set the parent again should return an error.
	err = s.env.ContextManager.SetParentContextID(s.ctx, childContextID, uuid.New())
	s.ErrorIs(err, ParentAlreadySetError)
}
