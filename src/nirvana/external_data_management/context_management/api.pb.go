// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: context_management/api.proto

package context_management

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ContextMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParentContextID *string `protobuf:"bytes,1,opt,name=parentContextID,proto3,oneof" json:"parentContextID,omitempty"`
}

func (x *ContextMetadata) Reset() {
	*x = ContextMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_context_management_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContextMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContextMetadata) ProtoMessage() {}

func (x *ContextMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_context_management_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContextMetadata.ProtoReflect.Descriptor instead.
func (*ContextMetadata) Descriptor() ([]byte, []int) {
	return file_context_management_api_proto_rawDescGZIP(), []int{0}
}

func (x *ContextMetadata) GetParentContextID() string {
	if x != nil && x.ParentContextID != nil {
		return *x.ParentContextID
	}
	return ""
}

var File_context_management_api_proto protoreflect.FileDescriptor

var file_context_management_api_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x22, 0x54, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49,
	0x44, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x49, 0x44, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_context_management_api_proto_rawDescOnce sync.Once
	file_context_management_api_proto_rawDescData = file_context_management_api_proto_rawDesc
)

func file_context_management_api_proto_rawDescGZIP() []byte {
	file_context_management_api_proto_rawDescOnce.Do(func() {
		file_context_management_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_context_management_api_proto_rawDescData)
	})
	return file_context_management_api_proto_rawDescData
}

var file_context_management_api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_context_management_api_proto_goTypes = []interface{}{
	(*ContextMetadata)(nil), // 0: context_management.ContextMetadata
}
var file_context_management_api_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_context_management_api_proto_init() }
func file_context_management_api_proto_init() {
	if File_context_management_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_context_management_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContextMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_context_management_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_context_management_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_context_management_api_proto_goTypes,
		DependencyIndexes: file_context_management_api_proto_depIdxs,
		MessageInfos:      file_context_management_api_proto_msgTypes,
	}.Build()
	File_context_management_api_proto = out.File
	file_context_management_api_proto_rawDesc = nil
	file_context_management_api_proto_goTypes = nil
	file_context_management_api_proto_depIdxs = nil
}
