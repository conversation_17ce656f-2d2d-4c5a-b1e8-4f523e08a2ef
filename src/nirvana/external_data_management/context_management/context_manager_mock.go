// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/context_management (interfaces: ContextManager)
//
// Generated by this command:
//
//	mockgen -destination=context_manager_mock.go -package=context_management nirvanatech.com/nirvana/external_data_management/context_management ContextManager
//

// Package context_management is a generated GoMock package.
package context_management

import (
	context "context"
	reflect "reflect"

	uuid "github.com/google/uuid"
	gomock "go.uber.org/mock/gomock"
	common "nirvanatech.com/nirvana/external_data_management/common"
	store_management "nirvanatech.com/nirvana/external_data_management/store_management"
)

// MockContextManager is a mock of ContextManager interface.
type MockContextManager struct {
	ctrl     *gomock.Controller
	recorder *MockContextManagerMockRecorder
	isgomock struct{}
}

// MockContextManagerMockRecorder is the mock recorder for MockContextManager.
type MockContextManagerMockRecorder struct {
	mock *MockContextManager
}

// NewMockContextManager creates a new mock instance.
func NewMockContextManager(ctrl *gomock.Controller) *MockContextManager {
	mock := &MockContextManager{ctrl: ctrl}
	mock.recorder = &MockContextManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockContextManager) EXPECT() *MockContextManagerMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockContextManager) Delete(ctx context.Context, contextID uuid.UUID, storeKey store_management.StoreKey) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, contextID, storeKey)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockContextManagerMockRecorder) Delete(ctx, contextID, storeKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockContextManager)(nil).Delete), ctx, contextID, storeKey)
}

// Exists mocks base method.
func (m *MockContextManager) Exists(ctx context.Context, contextID uuid.UUID, storeKey store_management.StoreKey) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exists", ctx, contextID, storeKey)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exists indicates an expected call of Exists.
func (mr *MockContextManagerMockRecorder) Exists(ctx, contextID, storeKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exists", reflect.TypeOf((*MockContextManager)(nil).Exists), ctx, contextID, storeKey)
}

// GetParentContextID mocks base method.
func (m *MockContextManager) GetParentContextID(ctx context.Context, contextID uuid.UUID) (*uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParentContextID", ctx, contextID)
	ret0, _ := ret[0].(*uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParentContextID indicates an expected call of GetParentContextID.
func (mr *MockContextManagerMockRecorder) GetParentContextID(ctx, contextID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParentContextID", reflect.TypeOf((*MockContextManager)(nil).GetParentContextID), ctx, contextID)
}

// Load mocks base method.
func (m *MockContextManager) Load(ctx context.Context, contextID uuid.UUID, storeKey store_management.StoreKey, r *common.Resource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Load", ctx, contextID, storeKey, r)
	ret0, _ := ret[0].(error)
	return ret0
}

// Load indicates an expected call of Load.
func (mr *MockContextManagerMockRecorder) Load(ctx, contextID, storeKey, r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Load", reflect.TypeOf((*MockContextManager)(nil).Load), ctx, contextID, storeKey, r)
}

// LoadRecursively mocks base method.
func (m *MockContextManager) LoadRecursively(ctx context.Context, contextID uuid.UUID, storeKey store_management.StoreKey, lookupDepth int, r *common.Resource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadRecursively", ctx, contextID, storeKey, lookupDepth, r)
	ret0, _ := ret[0].(error)
	return ret0
}

// LoadRecursively indicates an expected call of LoadRecursively.
func (mr *MockContextManagerMockRecorder) LoadRecursively(ctx, contextID, storeKey, lookupDepth, r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadRecursively", reflect.TypeOf((*MockContextManager)(nil).LoadRecursively), ctx, contextID, storeKey, lookupDepth, r)
}

// Save mocks base method.
func (m *MockContextManager) Save(ctx context.Context, contextID uuid.UUID, storeKey store_management.StoreKey, r *common.Resource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, contextID, storeKey, r)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockContextManagerMockRecorder) Save(ctx, contextID, storeKey, r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockContextManager)(nil).Save), ctx, contextID, storeKey, r)
}

// SetParentContextID mocks base method.
func (m *MockContextManager) SetParentContextID(ctx context.Context, contextID, parentContextID uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetParentContextID", ctx, contextID, parentContextID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetParentContextID indicates an expected call of SetParentContextID.
func (mr *MockContextManagerMockRecorder) SetParentContextID(ctx, contextID, parentContextID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetParentContextID", reflect.TypeOf((*MockContextManager)(nil).SetParentContextID), ctx, contextID, parentContextID)
}
