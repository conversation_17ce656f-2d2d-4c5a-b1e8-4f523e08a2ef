package context_management

import (
	"context"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

type ContextManager interface {
	store_management.StoreManager

	// LoadRecursively loads the data recursively from the context defined by contextID
	// It loads the data retrieved to variable v. Note that v needs to be a pointer in order to change its contents.
	LoadRecursively(
		ctx context.Context,
		contextID uuid.UUID,
		storeKey store_management.StoreKey,
		lookupDepth int,
		r *common.Resource,
	) error

	// GetParentContextID returns the parent context ID for a given context ID.
	GetParentContextID(ctx context.Context, contextID uuid.UUID) (*uuid.UUID, error)

	// SetParentContextID sets the parent context ID for a given context ID.
	SetParentContextID(ctx context.Context, contextID, parentContextID uuid.UUID) error
}
