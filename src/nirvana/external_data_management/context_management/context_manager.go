package context_management

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

var (
	NoParentContextError  = errors.New("Parent context does not exist")
	ParentAlreadySetError = errors.New("Parent context is already set")
)

type contextManagerDeps struct {
	fx.In
}

type contextManagerImpl struct {
	store_management.StoreManager

	deps *contextManagerDeps
}

var _ ContextManager = (*contextManagerImpl)(nil)

// LoadRecursively will try to load a certain resource from store,
// and if it's not found, it will try to load it from the parent context.
// It will keep doing this until it reaches the lookupDepth limit or no
// parent context is found. If the resource is not found in any of the context
// within the chain, ResourceNotFoundError will be returned.
func (m *contextManagerImpl) LoadRecursively(
	ctx context.Context,
	contextID uuid.UUID,
	storeKey store_management.StoreKey,
	lookupDepth int,
	r *common.Resource,
) error {
	var err error
	for i := 0; i < lookupDepth; i++ {
		err = m.Load(ctx, contextID, storeKey, r)
		if err == nil {
			return nil
		}

		if !errors.Is(err, store_management.ResourceNotFoundError) {
			return errors.Wrapf(err, "Error loading file for lookup depth %d", i)
		}

		// check parent
		parentCtxID, parentCtxErr := m.GetParentContextID(ctx, contextID)
		if parentCtxErr != nil {
			if errors.Is(parentCtxErr, NoParentContextError) {
				return err
			}
			return parentCtxErr
		}

		// parent exists
		contextID = *parentCtxID
	}
	return err
}

func (m *contextManagerImpl) GetParentContextID(ctx context.Context, contextID uuid.UUID) (*uuid.UUID, error) {
	contextMetadata := &ContextMetadata{}
	resource := &common.Resource{Data: contextMetadata}
	err := m.Load(ctx, contextID, contextMetadata, resource)
	if err != nil {
		if errors.Is(err, store_management.ResourceNotFoundError) {
			return nil, NoParentContextError
		}
		return nil, err
	}

	if contextMetadata.ParentContextID == nil {
		return nil, NoParentContextError
	}

	parentCtxID, err := uuid.Parse(contextMetadata.GetParentContextID())
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"Error while parsing the parent context ID %s",
			*contextMetadata.ParentContextID,
		)
	}

	return &parentCtxID, nil
}

func (m *contextManagerImpl) SetParentContextID(ctx context.Context, contextID, parentContextID uuid.UUID) error {
	contextMetadata := &ContextMetadata{}
	resource := &common.Resource{Data: contextMetadata}
	err := m.Load(ctx, contextID, contextMetadata, resource)
	if err != nil && !errors.Is(err, store_management.ResourceNotFoundError) {
		return errors.Wrapf(err, "Error while loading the context %s", contextID)
	}

	if contextMetadata.ParentContextID != nil {
		return ParentAlreadySetError
	}

	contextMetadata.ParentContextID = pointer_utils.ToPointer(parentContextID.String())

	resource = &common.Resource{Data: contextMetadata}
	return m.Save(ctx, contextID, contextMetadata, resource)
}
