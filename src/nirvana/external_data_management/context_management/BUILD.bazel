load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "context_management_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/external_data_management/context_management",
    proto = "//proto/context_management:context_management_proto",
    visibility = ["//visibility:public"],
)

# keep
go_proto_link(
    name = "context_management_go_proto_link",
    dep = ":context_management_go_proto",
    version = "v1",
)

go_library(
    name = "context_management",
    srcs = [
        "context_manager.go",
        "context_manager_mock.go",
        "context_metadata.go",
        "doc.go",
        "fx.go",
        "interfaces.go",
        "new.go",
    ],
    embed = [":context_management_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/external_data_management/context_management",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "context_management_test",
    srcs = ["context_manager_test.go"],
    embed = [":context_management"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/store_management",
        "//nirvana/external_data_management/store_management/testing",
        "//nirvana/infra/fx/testloader",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
