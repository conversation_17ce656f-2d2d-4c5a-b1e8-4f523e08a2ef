package context_management

import (
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

const contextMetadataResourceType = "ContextMetadata"

var _ store_management.StoreKey = &ContextMetadata{}

func (p *ContextMetadata) GetResourceType() string {
	return contextMetadataResourceType
}

func (p *ContextMetadata) GetFileNameComponents() ([]string, error) {
	return []string{"values"}, nil
}
