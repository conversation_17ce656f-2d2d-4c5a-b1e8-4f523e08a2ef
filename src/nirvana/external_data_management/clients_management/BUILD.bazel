load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "clients_management_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/external_data_management/clients_management",
    proto = "//proto/clients_management:clients_management_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/method_specific_write_to_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
    ],
)

# keep
go_proto_link(
    name = "clients_management_go_proto_link",
    dep = ":clients_management_go_proto",
    version = "v1",
)

go_library(
    name = "clients_management",
    srcs = [
        "clients_manager.go",
        "clients_manager_mock.go",
        "configs.go",
        "fx.go",
        "interfaces.go",
        "new.go",
    ],
    embed = [":clients_management_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/external_data_management/clients_management",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/context_management",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/method_specific_write_to_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
        "@org_uber_go_multierr//:multierr",
    ],
)

go_test(
    name = "clients_management_test",
    srcs = ["clients_manager_test.go"],
    embed = [":clients_management"],
    deps = [
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/context_management",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/infra/fx/testloader",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//proto",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
