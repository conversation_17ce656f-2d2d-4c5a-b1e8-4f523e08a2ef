package clients_management

import (
	"context"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

//go:generate go run go.uber.org/mock/mockgen -destination=clients_manager_mock.go -package=clients_management nirvanatech.com/nirvana/external_data_management/clients_management ClientsManager
type ClientsManager interface {
	// WriteClientsConfigToContext takes in a contextID and a ClientsConfig and writes the clients config to the context.
	WriteClientsConfigToContext(
		ctx context.Context,
		contextID uuid.UUID,
		clientsConfig *ClientsConfig,
	) error

	// BuildClientsFromContext takes in a contextID and builds the data fetching and processing clients
	// using the interceptors' configs saved in the context.
	BuildClientsFromContext(
		ctx context.Context,
		contextID uuid.UUID,
	) (data_fetching.FetcherClient, data_processing.ProcessorClient, func() error, error)
}
