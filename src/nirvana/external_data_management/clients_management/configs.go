package clients_management

import (
	"google.golang.org/protobuf/encoding/protojson"

	"nirvanatech.com/nirvana/external_data_management/store_management"
)

const clientsConfigResourceType = "ClientsConfig"

var _ store_management.StoreKey = &ClientsConfig{}

func (p *ClientsConfig) GetResourceType() string {
	return clientsConfigResourceType
}

func (p *ClientsConfig) GetFileNameComponents() ([]string, error) {
	return []string{"values"}, nil
}

func (p *ClientsConfig) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(p)
}

func (p *ClientsConfig) UnmarshalJSON(data []byte) error {
	return protojson.Unmarshal(data, p)
}
