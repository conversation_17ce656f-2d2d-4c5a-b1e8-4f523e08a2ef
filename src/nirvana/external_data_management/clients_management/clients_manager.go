package clients_management

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"go.uber.org/multierr"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/context_management"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/method_specific_write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

type clientsManagerDeps struct {
	fx.In

	ContextManager context_management.ContextManager

	FetcherClientFactory   data_fetching.FetcherClientFactory
	ProcessorClientFactory data_processing.ProcessorClientFactory

	WriteToStoreInterceptorFactory                write_to_store_interceptor.Factory
	ReadFromStoreInterceptorFactory               read_from_store_interceptor.Factory
	MethodSpecificReadFromStoreInterceptorFactory method_specific_read_from_store_interceptor.Factory
	MethodSpecificWriteToStoreInterceptorFactory  method_specific_write_to_store_interceptor.Factory
}

type clientsManagerImpl struct {
	deps *clientsManagerDeps
}

var _ ClientsManager = &clientsManagerImpl{}

func (m *clientsManagerImpl) WriteClientsConfigToContext(
	ctx context.Context,
	contextID uuid.UUID,
	clientsConfig *ClientsConfig,
) error {
	if clientsConfig == nil {
		return errors.New("clients config is nil")
	}

	resource := &common.Resource{Data: clientsConfig}
	return m.deps.ContextManager.Save(ctx, contextID, clientsConfig, resource)
}

func (m *clientsManagerImpl) BuildClientsFromContext(
	ctx context.Context,
	contextID uuid.UUID,
) (data_fetching.FetcherClient, data_processing.ProcessorClient, func() error, error) {
	fetcherClientInterceptors, processorClientInterceptors, err := m.buildClientsInterceptorsFromContext(
		ctx,
		contextID,
	)
	if err != nil {
		return nil, nil, nil, errors.Wrap(err, "failed to build clients interceptors")
	}

	fetcherClient, fCloser, err := m.deps.FetcherClientFactory(fetcherClientInterceptors...)
	if err != nil {
		return nil, nil, nil, errors.Wrap(err, "failed to create data fetcher client")
	}

	processorClient, pCloser, err := m.deps.ProcessorClientFactory(fetcherClient, processorClientInterceptors...)
	if err != nil {
		return nil, nil, nil, errors.Wrap(err, "failed to create data processor client")
	}

	return fetcherClient, processorClient, func() error { return multierr.Combine(pCloser(), fCloser()) }, nil
}

func (m *clientsManagerImpl) buildClientsInterceptorsFromContext(
	ctx context.Context,
	contextID uuid.UUID,
) ([]grpc.UnaryClientInterceptor, []grpc.UnaryClientInterceptor, error) {
	clientsConfig, err := m.readClientsConfigFromContext(ctx, contextID)
	if err != nil && !errors.Is(err, store_management.ResourceNotFoundError) {
		return nil, nil, errors.Wrap(err, "failed to read clients config from store")
	}

	fetcherConfigs := clientsConfig.GetFetcherConfig()
	processorConfigs := clientsConfig.GetProcessorConfig()

	fetcherInterceptors := m.buildClientInterceptorsFromClientConfig(fetcherConfigs)
	processorInterceptors := m.buildClientInterceptorsFromClientConfig(processorConfigs)

	return fetcherInterceptors, processorInterceptors, nil
}

func (m *clientsManagerImpl) readClientsConfigFromContext(
	ctx context.Context,
	contextID uuid.UUID,
) (*ClientsConfig, error) {
	clientsConfig := &ClientsConfig{}
	resource := &common.Resource{Data: clientsConfig}
	err := m.deps.ContextManager.Load(ctx, contextID, clientsConfig, resource)
	if err != nil && !errors.Is(err, store_management.ResourceNotFoundError) {
		return nil, errors.Wrapf(err, "Error while loading the clients config for context %s", contextID)
	}
	return clientsConfig, nil
}

func (m *clientsManagerImpl) buildClientInterceptorsFromClientConfig(
	clientConfig *ClientConfig,
) []grpc.UnaryClientInterceptor {
	interceptorsConfigs := clientConfig.GetInterceptorsConfigs()

	interceptors := make([]grpc.UnaryClientInterceptor, 0)

	// Find method specific write interceptor
	methodSpecificWriteToStoreInterceptorFound := false
	for _, interceptorConfig := range interceptorsConfigs {
		methodSpecificWriteToStoreConfig := interceptorConfig.GetMethodSpecificWriteToStoreInterceptorConfig()
		if methodSpecificWriteToStoreConfig != nil {
			interceptors = append(interceptors, m.deps.MethodSpecificWriteToStoreInterceptorFactory(methodSpecificWriteToStoreConfig))
			methodSpecificWriteToStoreInterceptorFound = true
		}
	}

	// Find write interceptor
	writeToStoreInterceptorFound := false
	for _, interceptorConfig := range interceptorsConfigs {
		writeToStoreConfig := interceptorConfig.GetWriteToStoreInterceptorConfig()
		if writeToStoreConfig != nil {
			interceptors = append(interceptors, m.deps.WriteToStoreInterceptorFactory(writeToStoreConfig))
			writeToStoreInterceptorFound = true
			break
		}
	}

	// If no WriteToStoreInterceptor is found but there's a MethodSpecificWriteToStoreInterceptor, we should add a
	// WriteToStoreInterceptor with disabled StoreDisabledConfig in order for the former interceptor to work
	if !writeToStoreInterceptorFound && methodSpecificWriteToStoreInterceptorFound {
		cfg := write_to_store_interceptor.NewDisabledConfig()
		interceptors = append(interceptors, m.deps.WriteToStoreInterceptorFactory(cfg))
	}

	// Find method specific read interceptor
	methodSpecificReadFromStoreInterceptorFound := false
	for _, interceptorConfig := range interceptorsConfigs {
		methodSpecificReadFromStoreConfig := interceptorConfig.GetMethodSpecificReadFromStoreInterceptorConfig()
		if methodSpecificReadFromStoreConfig != nil {
			interceptors = append(interceptors, m.deps.MethodSpecificReadFromStoreInterceptorFactory(methodSpecificReadFromStoreConfig))
			methodSpecificReadFromStoreInterceptorFound = true
			break
		}
	}

	// Find read interceptor
	readFromStoreInterceptorFound := false
	for _, interceptorConfig := range interceptorsConfigs {
		readFromStoreConfig := interceptorConfig.GetReadFromStoreInterceptorConfig()
		if readFromStoreConfig != nil {
			interceptors = append(interceptors, m.deps.ReadFromStoreInterceptorFactory(readFromStoreConfig))
			readFromStoreInterceptorFound = true
		}
	}

	// If no ReadFromStoreInterceptor is found but there's a MethodSpecificReadFromStoreInterceptor, we should add a
	// ReadFromStoreInterceptor with disabled StoreDisabledConfig in order for the former interceptor to work
	if !readFromStoreInterceptorFound && methodSpecificReadFromStoreInterceptorFound {
		cfg := read_from_store_interceptor.NewStoreDisabledConfig()
		interceptors = append(interceptors, m.deps.ReadFromStoreInterceptorFactory(cfg))
	}

	return interceptors
}
