// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: clients_management/configs.proto

package clients_management

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	method_specific_read_from_store_interceptor "nirvanatech.com/nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor"
	method_specific_write_to_store_interceptor "nirvanatech.com/nirvana/external_data_management/interceptors_management/method_specific_write_to_store_interceptor"
	read_from_store_interceptor "nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	write_to_store_interceptor "nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClientsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FetcherConfig   *ClientConfig `protobuf:"bytes,1,opt,name=fetcherConfig,proto3" json:"fetcherConfig,omitempty"`
	ProcessorConfig *ClientConfig `protobuf:"bytes,2,opt,name=processorConfig,proto3" json:"processorConfig,omitempty"`
}

func (x *ClientsConfig) Reset() {
	*x = ClientsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_clients_management_configs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientsConfig) ProtoMessage() {}

func (x *ClientsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_clients_management_configs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientsConfig.ProtoReflect.Descriptor instead.
func (*ClientsConfig) Descriptor() ([]byte, []int) {
	return file_clients_management_configs_proto_rawDescGZIP(), []int{0}
}

func (x *ClientsConfig) GetFetcherConfig() *ClientConfig {
	if x != nil {
		return x.FetcherConfig
	}
	return nil
}

func (x *ClientsConfig) GetProcessorConfig() *ClientConfig {
	if x != nil {
		return x.ProcessorConfig
	}
	return nil
}

type ClientConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InterceptorsConfigs []*InterceptorConfig `protobuf:"bytes,1,rep,name=interceptorsConfigs,proto3" json:"interceptorsConfigs,omitempty"`
}

func (x *ClientConfig) Reset() {
	*x = ClientConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_clients_management_configs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConfig) ProtoMessage() {}

func (x *ClientConfig) ProtoReflect() protoreflect.Message {
	mi := &file_clients_management_configs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConfig.ProtoReflect.Descriptor instead.
func (*ClientConfig) Descriptor() ([]byte, []int) {
	return file_clients_management_configs_proto_rawDescGZIP(), []int{1}
}

func (x *ClientConfig) GetInterceptorsConfigs() []*InterceptorConfig {
	if x != nil {
		return x.InterceptorsConfigs
	}
	return nil
}

type InterceptorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ConcreteConfig:
	//
	//	*InterceptorConfig_ReadFromStoreInterceptorConfig
	//	*InterceptorConfig_WriteToStoreInterceptorConfig
	//	*InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig
	//	*InterceptorConfig_MethodSpecificWriteToStoreInterceptorConfig
	ConcreteConfig isInterceptorConfig_ConcreteConfig `protobuf_oneof:"concreteConfig"`
}

func (x *InterceptorConfig) Reset() {
	*x = InterceptorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_clients_management_configs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InterceptorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterceptorConfig) ProtoMessage() {}

func (x *InterceptorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_clients_management_configs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterceptorConfig.ProtoReflect.Descriptor instead.
func (*InterceptorConfig) Descriptor() ([]byte, []int) {
	return file_clients_management_configs_proto_rawDescGZIP(), []int{2}
}

func (m *InterceptorConfig) GetConcreteConfig() isInterceptorConfig_ConcreteConfig {
	if m != nil {
		return m.ConcreteConfig
	}
	return nil
}

func (x *InterceptorConfig) GetReadFromStoreInterceptorConfig() *read_from_store_interceptor.Config {
	if x, ok := x.GetConcreteConfig().(*InterceptorConfig_ReadFromStoreInterceptorConfig); ok {
		return x.ReadFromStoreInterceptorConfig
	}
	return nil
}

func (x *InterceptorConfig) GetWriteToStoreInterceptorConfig() *write_to_store_interceptor.Config {
	if x, ok := x.GetConcreteConfig().(*InterceptorConfig_WriteToStoreInterceptorConfig); ok {
		return x.WriteToStoreInterceptorConfig
	}
	return nil
}

func (x *InterceptorConfig) GetMethodSpecificReadFromStoreInterceptorConfig() *method_specific_read_from_store_interceptor.Config {
	if x, ok := x.GetConcreteConfig().(*InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig); ok {
		return x.MethodSpecificReadFromStoreInterceptorConfig
	}
	return nil
}

func (x *InterceptorConfig) GetMethodSpecificWriteToStoreInterceptorConfig() *method_specific_write_to_store_interceptor.Config {
	if x, ok := x.GetConcreteConfig().(*InterceptorConfig_MethodSpecificWriteToStoreInterceptorConfig); ok {
		return x.MethodSpecificWriteToStoreInterceptorConfig
	}
	return nil
}

type isInterceptorConfig_ConcreteConfig interface {
	isInterceptorConfig_ConcreteConfig()
}

type InterceptorConfig_ReadFromStoreInterceptorConfig struct {
	ReadFromStoreInterceptorConfig *read_from_store_interceptor.Config `protobuf:"bytes,2,opt,name=readFromStoreInterceptorConfig,proto3,oneof"`
}

type InterceptorConfig_WriteToStoreInterceptorConfig struct {
	WriteToStoreInterceptorConfig *write_to_store_interceptor.Config `protobuf:"bytes,3,opt,name=writeToStoreInterceptorConfig,proto3,oneof"`
}

type InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig struct {
	MethodSpecificReadFromStoreInterceptorConfig *method_specific_read_from_store_interceptor.Config `protobuf:"bytes,4,opt,name=methodSpecificReadFromStoreInterceptorConfig,proto3,oneof"`
}

type InterceptorConfig_MethodSpecificWriteToStoreInterceptorConfig struct {
	MethodSpecificWriteToStoreInterceptorConfig *method_specific_write_to_store_interceptor.Config `protobuf:"bytes,5,opt,name=methodSpecificWriteToStoreInterceptorConfig,proto3,oneof"`
}

func (*InterceptorConfig_ReadFromStoreInterceptorConfig) isInterceptorConfig_ConcreteConfig() {}

func (*InterceptorConfig_WriteToStoreInterceptorConfig) isInterceptorConfig_ConcreteConfig() {}

func (*InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig) isInterceptorConfig_ConcreteConfig() {
}

func (*InterceptorConfig_MethodSpecificWriteToStoreInterceptorConfig) isInterceptorConfig_ConcreteConfig() {
}

var File_clients_management_configs_proto protoreflect.FileDescriptor

var file_clients_management_configs_proto_rawDesc = []byte{
	0x0a, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x12, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x3f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70,
	0x74, 0x6f, 0x72, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65,
	0x70, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x50, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x01, 0x0a,
	0x0d, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x46,
	0x0a, 0x0d, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4a, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x67, 0x0a, 0x0c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x57, 0x0a, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f,
	0x72, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70,
	0x74, 0x6f, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x22, 0xb5, 0x04, 0x0a, 0x11,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x6d, 0x0a, 0x1e, 0x72, 0x65, 0x61, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x65, 0x61, 0x64,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00,
	0x52, 0x1e, 0x72, 0x65, 0x61, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x6a, 0x0a, 0x1d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x54, 0x6f, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f,
	0x74, 0x6f, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65,
	0x70, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x1d, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x54, 0x6f, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x99, 0x01, 0x0a,
	0x2c, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x52,
	0x65, 0x61, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f,
	0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x2c, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x52, 0x65, 0x61, 0x64, 0x46, 0x72,
	0x6f, 0x6d, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x96, 0x01, 0x0a, 0x2b, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x57, 0x72, 0x69, 0x74, 0x65,
	0x54, 0x6f, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x2b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x57, 0x72, 0x69, 0x74, 0x65, 0x54, 0x6f, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x10, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_clients_management_configs_proto_rawDescOnce sync.Once
	file_clients_management_configs_proto_rawDescData = file_clients_management_configs_proto_rawDesc
)

func file_clients_management_configs_proto_rawDescGZIP() []byte {
	file_clients_management_configs_proto_rawDescOnce.Do(func() {
		file_clients_management_configs_proto_rawDescData = protoimpl.X.CompressGZIP(file_clients_management_configs_proto_rawDescData)
	})
	return file_clients_management_configs_proto_rawDescData
}

var file_clients_management_configs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_clients_management_configs_proto_goTypes = []interface{}{
	(*ClientsConfig)(nil),                                      // 0: clients_management.ClientsConfig
	(*ClientConfig)(nil),                                       // 1: clients_management.ClientConfig
	(*InterceptorConfig)(nil),                                  // 2: clients_management.InterceptorConfig
	(*read_from_store_interceptor.Config)(nil),                 // 3: read_from_store_interceptor.Config
	(*write_to_store_interceptor.Config)(nil),                  // 4: write_to_store_interceptor.Config
	(*method_specific_read_from_store_interceptor.Config)(nil), // 5: method_specific_read_from_store_interceptor.Config
	(*method_specific_write_to_store_interceptor.Config)(nil),  // 6: method_specific_write_to_store_interceptor.Config
}
var file_clients_management_configs_proto_depIdxs = []int32{
	1, // 0: clients_management.ClientsConfig.fetcherConfig:type_name -> clients_management.ClientConfig
	1, // 1: clients_management.ClientsConfig.processorConfig:type_name -> clients_management.ClientConfig
	2, // 2: clients_management.ClientConfig.interceptorsConfigs:type_name -> clients_management.InterceptorConfig
	3, // 3: clients_management.InterceptorConfig.readFromStoreInterceptorConfig:type_name -> read_from_store_interceptor.Config
	4, // 4: clients_management.InterceptorConfig.writeToStoreInterceptorConfig:type_name -> write_to_store_interceptor.Config
	5, // 5: clients_management.InterceptorConfig.methodSpecificReadFromStoreInterceptorConfig:type_name -> method_specific_read_from_store_interceptor.Config
	6, // 6: clients_management.InterceptorConfig.methodSpecificWriteToStoreInterceptorConfig:type_name -> method_specific_write_to_store_interceptor.Config
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_clients_management_configs_proto_init() }
func file_clients_management_configs_proto_init() {
	if File_clients_management_configs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_clients_management_configs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_clients_management_configs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_clients_management_configs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InterceptorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_clients_management_configs_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*InterceptorConfig_ReadFromStoreInterceptorConfig)(nil),
		(*InterceptorConfig_WriteToStoreInterceptorConfig)(nil),
		(*InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig)(nil),
		(*InterceptorConfig_MethodSpecificWriteToStoreInterceptorConfig)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_clients_management_configs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_clients_management_configs_proto_goTypes,
		DependencyIndexes: file_clients_management_configs_proto_depIdxs,
		MessageInfos:      file_clients_management_configs_proto_msgTypes,
	}.Build()
	File_clients_management_configs_proto = out.File
	file_clients_management_configs_proto_rawDesc = nil
	file_clients_management_configs_proto_goTypes = nil
	file_clients_management_configs_proto_depIdxs = nil
}
