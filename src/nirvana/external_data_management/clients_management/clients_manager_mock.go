// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/clients_management (interfaces: ClientsManager)
//
// Generated by this command:
//
//	mockgen -destination=clients_manager_mock.go -package=clients_management nirvanatech.com/nirvana/external_data_management/clients_management ClientsManager
//

// Package clients_management is a generated GoMock package.
package clients_management

import (
	context "context"
	reflect "reflect"

	uuid "github.com/google/uuid"
	gomock "go.uber.org/mock/gomock"
	data_fetching "nirvanatech.com/nirvana/external_data_management/data_fetching"
	data_processing "nirvanatech.com/nirvana/external_data_management/data_processing"
)

// MockClientsManager is a mock of ClientsManager interface.
type MockClientsManager struct {
	ctrl     *gomock.Controller
	recorder *MockClientsManagerMockRecorder
	isgomock struct{}
}

// MockClientsManagerMockRecorder is the mock recorder for MockClientsManager.
type MockClientsManagerMockRecorder struct {
	mock *MockClientsManager
}

// NewMockClientsManager creates a new mock instance.
func NewMockClientsManager(ctrl *gomock.Controller) *MockClientsManager {
	mock := &MockClientsManager{ctrl: ctrl}
	mock.recorder = &MockClientsManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientsManager) EXPECT() *MockClientsManagerMockRecorder {
	return m.recorder
}

// BuildClientsFromContext mocks base method.
func (m *MockClientsManager) BuildClientsFromContext(ctx context.Context, contextID uuid.UUID) (data_fetching.FetcherClient, data_processing.ProcessorClient, func() error, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuildClientsFromContext", ctx, contextID)
	ret0, _ := ret[0].(data_fetching.FetcherClient)
	ret1, _ := ret[1].(data_processing.ProcessorClient)
	ret2, _ := ret[2].(func() error)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// BuildClientsFromContext indicates an expected call of BuildClientsFromContext.
func (mr *MockClientsManagerMockRecorder) BuildClientsFromContext(ctx, contextID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildClientsFromContext", reflect.TypeOf((*MockClientsManager)(nil).BuildClientsFromContext), ctx, contextID)
}

// WriteClientsConfigToContext mocks base method.
func (m *MockClientsManager) WriteClientsConfigToContext(ctx context.Context, contextID uuid.UUID, clientsConfig *ClientsConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteClientsConfigToContext", ctx, contextID, clientsConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// WriteClientsConfigToContext indicates an expected call of WriteClientsConfigToContext.
func (mr *MockClientsManagerMockRecorder) WriteClientsConfigToContext(ctx, contextID, clientsConfig any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteClientsConfigToContext", reflect.TypeOf((*MockClientsManager)(nil).WriteClientsConfigToContext), ctx, contextID, clientsConfig)
}
