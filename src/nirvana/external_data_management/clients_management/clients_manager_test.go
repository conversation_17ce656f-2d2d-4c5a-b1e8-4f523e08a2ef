package clients_management

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/cockroachdb/errors"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/context_management"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/method_specific_read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type clientsManagerTestEnv struct {
	fx.In

	ClientsManager ClientsManager
}

type clientsManagerTestSuite struct {
	suite.Suite

	ctx  context.Context
	ctrl *gomock.Controller

	env clientsManagerTestEnv

	fxApp *fxtest.App

	mockContextManager *context_management.MockContextManager

	mockFetcherClient   *data_fetching.MockFetcherClient
	mockProcessorClient *data_processing.MockProcessorClient

	fetcherCloserTimesCalled   int
	processorCloserTimesCalled int

	mockFetcherClientFactory   data_fetching.FetcherClientFactory
	mockProcessorClientFactory data_processing.ProcessorClientFactory

	// Store the interceptors that were passed to the factories
	capturedFetcherInterceptors   []grpc.UnaryClientInterceptor
	capturedProcessorInterceptors []grpc.UnaryClientInterceptor
}

func TestClientsManager(t *testing.T) {
	suite.Run(t, new(clientsManagerTestSuite))
}

func (s *clientsManagerTestSuite) SetupSuite() {
	s.ctrl, s.ctx = gomock.WithContext(context.Background(), s.T())

	s.mockContextManager = context_management.NewMockContextManager(s.ctrl)
	newMockContextManager := func(actualManager context_management.ContextManager) context_management.ContextManager {
		return s.mockContextManager
	}

	// Setup mock factories that capture the interceptors
	s.mockFetcherClient = data_fetching.NewMockFetcherClient(s.ctrl)
	s.mockFetcherClientFactory = func(
		interceptors ...grpc.UnaryClientInterceptor,
	) (data_fetching.FetcherClient, func() error, error) {
		s.capturedFetcherInterceptors = interceptors
		closerFn := func() error {
			s.fetcherCloserTimesCalled++
			return nil
		}
		return s.mockFetcherClient, closerFn, nil
	}
	newMockFetcherClientFactory := func(
		actualFactory data_fetching.FetcherClientFactory,
	) data_fetching.FetcherClientFactory {
		return s.mockFetcherClientFactory
	}

	s.mockProcessorClient = data_processing.NewMockProcessorClient(s.ctrl)
	s.mockProcessorClientFactory = func(
		fetcherClient data_fetching.FetcherClient,
		interceptors ...grpc.UnaryClientInterceptor,
	) (data_processing.ProcessorClient, func() error, error) {
		s.capturedProcessorInterceptors = interceptors
		closerFn := func() error {
			s.processorCloserTimesCalled++
			return nil
		}
		return s.mockProcessorClient, closerFn, nil
	}
	newMockProcessorClientFactory := func(
		actualFactory data_processing.ProcessorClientFactory,
	) data_processing.ProcessorClientFactory {
		return s.mockProcessorClientFactory
	}

	s.fxApp = testloader.RequireStart(s.T(), &s.env,
		testloader.Use(fx.Decorate(newMockContextManager)),
		testloader.Use(fx.Decorate(newMockFetcherClientFactory)),
		testloader.Use(fx.Decorate(newMockProcessorClientFactory)),
	)
}

func (s *clientsManagerTestSuite) TearDownTest() {
	s.fxApp.RequireStop()
}

func (s *clientsManagerTestSuite) SetupTest() {
	s.fetcherCloserTimesCalled = 0
	s.processorCloserTimesCalled = 0
}

func (s *clientsManagerTestSuite) Test_BuildClientsFromContext_WithSuccessLoadingConfigFromStore() {
	contextID := uuid.New()
	grpcMethod := "MyMethod"

	// CASE 1: with all interceptors for each client.
	clientsConfig := &ClientsConfig{
		FetcherConfig: &ClientConfig{
			InterceptorsConfigs: []*InterceptorConfig{
				{
					ConcreteConfig: &InterceptorConfig_WriteToStoreInterceptorConfig{
						WriteToStoreInterceptorConfig: write_to_store_interceptor.NewDisabledConfig(),
					},
				},
				{
					ConcreteConfig: &InterceptorConfig_ReadFromStoreInterceptorConfig{
						ReadFromStoreInterceptorConfig: read_from_store_interceptor.NewStoreDisabledConfig(),
					},
				},
				{
					ConcreteConfig: &InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig{
						MethodSpecificReadFromStoreInterceptorConfig: method_specific_read_from_store_interceptor.NewConfig(
							grpcMethod,
							read_from_store_interceptor.NewStoreDisabledConfig(),
						),
					},
				},
			},
		},
		ProcessorConfig: &ClientConfig{
			InterceptorsConfigs: []*InterceptorConfig{
				{
					ConcreteConfig: &InterceptorConfig_WriteToStoreInterceptorConfig{
						WriteToStoreInterceptorConfig: write_to_store_interceptor.NewDisabledConfig(),
					},
				},
				{
					ConcreteConfig: &InterceptorConfig_ReadFromStoreInterceptorConfig{
						ReadFromStoreInterceptorConfig: read_from_store_interceptor.NewStoreDisabledConfig(),
					},
				},
				{
					ConcreteConfig: &InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig{
						MethodSpecificReadFromStoreInterceptorConfig: method_specific_read_from_store_interceptor.NewConfig(
							grpcMethod,
							read_from_store_interceptor.NewStoreDisabledConfig(),
						),
					},
				},
			},
		},
	}
	s.mockSuccessfulContextManagerLoad(contextID, clientsConfig)

	fetcherClient, processorClient, closer, err := s.env.ClientsManager.BuildClientsFromContext(s.ctx, contextID)

	s.Require().NoError(err)
	s.Require().NotNil(closer)
	s.Require().Equal(s.mockFetcherClient, fetcherClient)
	s.Require().Equal(s.mockProcessorClient, processorClient)

	s.Require().Equal(0, s.fetcherCloserTimesCalled)
	s.Require().Equal(0, s.processorCloserTimesCalled)
	s.Require().NoError(closer())
	s.Require().Equal(1, s.fetcherCloserTimesCalled)
	s.Require().Equal(1, s.processorCloserTimesCalled)

	// There isn't much we can test here, because interceptors
	// are functions, and there isn't a good way to test function
	// equality in Go.
	s.Require().Len(s.capturedFetcherInterceptors, 3)
	s.Require().Len(s.capturedProcessorInterceptors, 3)

	// CASE 2: when method-specific-read-from-store interceptor is
	// added explicitly, but the read-from-store is not.
	clientsConfig = &ClientsConfig{
		FetcherConfig: &ClientConfig{
			InterceptorsConfigs: []*InterceptorConfig{
				{
					ConcreteConfig: &InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig{
						MethodSpecificReadFromStoreInterceptorConfig: method_specific_read_from_store_interceptor.NewConfig(
							grpcMethod,
							read_from_store_interceptor.NewStoreDisabledConfig(),
						),
					},
				},
			},
		},
		ProcessorConfig: &ClientConfig{
			InterceptorsConfigs: []*InterceptorConfig{
				{
					ConcreteConfig: &InterceptorConfig_MethodSpecificReadFromStoreInterceptorConfig{
						MethodSpecificReadFromStoreInterceptorConfig: method_specific_read_from_store_interceptor.NewConfig(
							grpcMethod,
							read_from_store_interceptor.NewStoreDisabledConfig(),
						),
					},
				},
			},
		},
	}
	s.mockSuccessfulContextManagerLoad(contextID, clientsConfig)

	fetcherClient, processorClient, closer, err = s.env.ClientsManager.BuildClientsFromContext(s.ctx, contextID)

	s.Require().NoError(err)
	s.Require().NotNil(closer)
	s.Require().Equal(s.mockFetcherClient, fetcherClient)
	s.Require().Equal(s.mockProcessorClient, processorClient)

	s.Require().Equal(1, s.fetcherCloserTimesCalled)
	s.Require().Equal(1, s.processorCloserTimesCalled)
	s.Require().NoError(closer())
	s.Require().Equal(2, s.fetcherCloserTimesCalled)
	s.Require().Equal(2, s.processorCloserTimesCalled)

	// Note: we expect 2 processor interceptors, because when the
	// method-specific-read-from-store interceptor is added, the
	// read-from-store is also added automatically (even if the
	// client didn't explicitly request it).
	s.Require().Len(s.capturedFetcherInterceptors, 2)
	s.Require().Len(s.capturedProcessorInterceptors, 2)

	// CASE 3: with different number of interceptors for each client.
	// CASE 3a: with only fetcher interceptors.
	clientsConfig = &ClientsConfig{
		FetcherConfig: &ClientConfig{
			InterceptorsConfigs: []*InterceptorConfig{
				{
					ConcreteConfig: &InterceptorConfig_WriteToStoreInterceptorConfig{
						WriteToStoreInterceptorConfig: write_to_store_interceptor.NewDisabledConfig(),
					},
				},
			},
		},
	}
	s.mockSuccessfulContextManagerLoad(contextID, clientsConfig)

	fetcherClient, processorClient, closer, err = s.env.ClientsManager.BuildClientsFromContext(s.ctx, contextID)

	s.Require().NoError(err)
	s.Require().NotNil(closer)
	s.Require().Equal(s.mockFetcherClient, fetcherClient)
	s.Require().Equal(s.mockProcessorClient, processorClient)

	s.Require().Equal(2, s.fetcherCloserTimesCalled)
	s.Require().Equal(2, s.processorCloserTimesCalled)
	s.Require().NoError(closer())
	s.Require().Equal(3, s.fetcherCloserTimesCalled)
	s.Require().Equal(3, s.processorCloserTimesCalled)

	s.Require().Len(s.capturedFetcherInterceptors, 1)
	s.Require().Len(s.capturedProcessorInterceptors, 0)

	// CASE 3b: with only processor interceptors.
	clientsConfig = &ClientsConfig{
		ProcessorConfig: &ClientConfig{
			InterceptorsConfigs: []*InterceptorConfig{
				{
					ConcreteConfig: &InterceptorConfig_WriteToStoreInterceptorConfig{
						WriteToStoreInterceptorConfig: write_to_store_interceptor.NewDisabledConfig(),
					},
				},
			},
		},
	}
	s.mockSuccessfulContextManagerLoad(contextID, clientsConfig)

	fetcherClient, processorClient, closer, err = s.env.ClientsManager.BuildClientsFromContext(s.ctx, contextID)

	s.Require().NoError(err)
	s.Require().NotNil(closer)
	s.Require().Equal(s.mockFetcherClient, fetcherClient)
	s.Require().Equal(s.mockProcessorClient, processorClient)

	s.Require().Equal(3, s.fetcherCloserTimesCalled)
	s.Require().Equal(3, s.processorCloserTimesCalled)
	s.Require().NoError(closer())
	s.Require().Equal(4, s.fetcherCloserTimesCalled)
	s.Require().Equal(4, s.processorCloserTimesCalled)

	s.Require().Len(s.capturedFetcherInterceptors, 0)
	s.Require().Len(s.capturedProcessorInterceptors, 1)
}

func (s *clientsManagerTestSuite) mockSuccessfulContextManagerLoad(
	contextID uuid.UUID,
	mockedClientsConfig *ClientsConfig,
) {
	s.mockContextManager.EXPECT().
		Load(s.ctx, contextID, gomock.Any(), gomock.Any()).
		DoAndReturn(
			func(
				_ context.Context,
				_ uuid.UUID,
				_ *ClientsConfig,
				r *common.Resource,
			) error {
				cfg, ok := r.Data.(*ClientsConfig)
				s.Require().True(ok)
				proto.Merge(cfg, mockedClientsConfig)
				return nil
			})
}

func (s *clientsManagerTestSuite) Test_BuildClientsFromContext_WithErrorLoadingConfigFromStore() {
	contextID := uuid.New()

	mockLoadError := errors.New("load error")
	s.mockContextManager.EXPECT().
		Load(s.ctx, contextID, gomock.Any(), gomock.Any()).
		Return(mockLoadError)

	fetcherClient, processorClient, closer, err := s.env.ClientsManager.BuildClientsFromContext(s.ctx, contextID)
	s.Require().Error(err)
	s.Require().ErrorIs(err, mockLoadError)
	s.Require().Nil(fetcherClient)
	s.Require().Nil(processorClient)
	s.Require().Nil(closer)
}
