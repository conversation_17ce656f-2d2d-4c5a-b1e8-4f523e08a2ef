package common

type ResourceMetadata struct {
	// ResourceType will store the name of the struct that was marshaled into the Resource.Data field.
	// This allows consumers of Resource instances to know how to unmarshall the bytes on that field into
	// a specific struct. An example value for ResourceType would be data_fetching.MVRReportV1.
	ResourceType string

	// OriginDataContextID is the ID of the data context from which the resource is obtained. When the
	// resource is obtained from source, this field is nil.
	//
	// When the resource is obtained from the store, the read-from-store interceptor will set this field
	// to the ID of the data context from which the resource was obtained (either from the child data
	// context or one of its ancestors). Therefore, the value stored is not really used (it might be
	// useful for manual debugging though).
	OriginDataContextID *string

	// Dirty is a boolean flag used to indicate whether the value retrieved from the store has been changed
	// by one of the interceptors. It only makes sense if OriginDataContextID is not nil.
	//
	// When the resource is obtained from the store, the read-from-store interceptor will set this flag
	// to false when it retrieves the value from the store. Therefore, the value stored is not really
	// used (it might be useful for manual debugging though).
	//
	// This flag signals the write-to-store interceptor to write the data, even if the data comes from the
	// same data context.
	//
	// Note that the write-to-store interceptor could potentially be overwriting the data in the store,
	// so consumers should be careful to configure the interceptor chain appropriately.
	Dirty bool

	// Extras is an unstructured map that can hold any ResourceType-specific value, that we decide to persist
	// in the store or that we need to communicate between interceptors. Note that if the field was common
	// to all ResourceTypes, we would have added it as a field to ResourceMetadata (e.g., Dirty).
	//
	// For example, if for a given ResourceType we have more than one external source, and we use one or the
	// other based on certain rules, we might want to store in Extras which source was used to retrieve the
	// data being stored.
	Extras map[string]any
}

// Resource is a struct used to standardize the `reply` argument received by gRPC interceptors used in
// fetcher/processor clients.
//
// More concretely, every interceptor chain that a fetcher/processor request
// goes through has at least two interceptors:
//  1. An interceptor at the beginning of the chain that: wraps the original `reply` object in a
//     Resource struct. More concretely, the Resource instance holds in its Data field the original
//     `reply` object (which needs to be, and is, a pointer).
//  2. An interceptor at the end of the chain that: unwraps the Resource struct and passes the pointer
//     to the original `reply` to the server (so that the server populates it).
//
// Additionally, all files that are persisted in the store also follow this structure. This facilitates
// the operation of store-related interceptors, which either read a value from the store and use it to
// set the `reply` arg that they received, or they read the value set in their `reply` arg and write it
// to the store.
type Resource struct {
	// It's worth mentioning that even though Data doesn't have any explicit type constraints, it must be a
	// pointer to a JSON serializable struct.
	Data any

	Metadata ResourceMetadata
}
