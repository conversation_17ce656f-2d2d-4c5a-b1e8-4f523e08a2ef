package store_management

import (
	"context"

	"github.com/dgraph-io/ristretto/v2"
	"go.uber.org/fx"
)

func NewStoreManager(lc fx.Lifecycle, deps storeManagerS3ImplDeps) (StoreManager, error) {
	manager, cacheCloser, err := NewStoreManagerS3Impl(deps)
	if err != nil {
		return nil, err
	}

	lc.Append(
		fx.Hook{
			OnStop: func(ctx context.Context) error {
				cacheCloser()
				return nil
			},
		},
	)

	return manager, nil
}

func NewStoreManagerS3Impl(deps storeManagerS3ImplDeps) (StoreManager, func(), error) {
	cache, err := ristretto.NewCache(&ristretto.Config[string, []byte]{
		NumCounters: 1e6,     // number of keys to track frequency of (1M).
		MaxCost:     1 << 27, // maximum cost of cache (128MB).
		BufferItems: 64,      // number of keys per Get buffer.
	})
	if err != nil {
		return nil, nil, err
	}

	manager := &storeManagerS3Impl{
		deps:  deps,
		cache: cache,
	}

	return manager, cache.Close, nil
}
