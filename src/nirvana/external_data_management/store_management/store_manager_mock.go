// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/store_management (interfaces: StoreManager)
//
// Generated by this command:
//
//	mockgen -destination=store_manager_mock.go -package=store_management nirvanatech.com/nirvana/external_data_management/store_management StoreManager
//

// Package store_management is a generated GoMock package.
package store_management

import (
	context "context"
	reflect "reflect"

	uuid "github.com/google/uuid"
	gomock "go.uber.org/mock/gomock"
	common "nirvanatech.com/nirvana/external_data_management/common"
)

// MockStoreManager is a mock of StoreManager interface.
type MockStoreManager struct {
	ctrl     *gomock.Controller
	recorder *MockStoreManagerMockRecorder
	isgomock struct{}
}

// MockStoreManagerMockRecorder is the mock recorder for MockStoreManager.
type MockStoreManagerMockRecorder struct {
	mock *MockStoreManager
}

// NewMockStoreManager creates a new mock instance.
func NewMockStoreManager(ctrl *gomock.Controller) *MockStoreManager {
	mock := &MockStoreManager{ctrl: ctrl}
	mock.recorder = &MockStoreManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStoreManager) EXPECT() *MockStoreManagerMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockStoreManager) Delete(ctx context.Context, contextID uuid.UUID, storeKey StoreKey) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, contextID, storeKey)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockStoreManagerMockRecorder) Delete(ctx, contextID, storeKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockStoreManager)(nil).Delete), ctx, contextID, storeKey)
}

// Exists mocks base method.
func (m *MockStoreManager) Exists(ctx context.Context, contextID uuid.UUID, storeKey StoreKey) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exists", ctx, contextID, storeKey)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exists indicates an expected call of Exists.
func (mr *MockStoreManagerMockRecorder) Exists(ctx, contextID, storeKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exists", reflect.TypeOf((*MockStoreManager)(nil).Exists), ctx, contextID, storeKey)
}

// Load mocks base method.
func (m *MockStoreManager) Load(ctx context.Context, contextID uuid.UUID, storeKey StoreKey, r *common.Resource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Load", ctx, contextID, storeKey, r)
	ret0, _ := ret[0].(error)
	return ret0
}

// Load indicates an expected call of Load.
func (mr *MockStoreManagerMockRecorder) Load(ctx, contextID, storeKey, r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Load", reflect.TypeOf((*MockStoreManager)(nil).Load), ctx, contextID, storeKey, r)
}

// Save mocks base method.
func (m *MockStoreManager) Save(ctx context.Context, contextID uuid.UUID, storeKey StoreKey, r *common.Resource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, contextID, storeKey, r)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockStoreManagerMockRecorder) Save(ctx, contextID, storeKey, r any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockStoreManager)(nil).Save), ctx, contextID, storeKey, r)
}
