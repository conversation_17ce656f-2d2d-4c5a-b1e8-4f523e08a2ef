package store_management

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/dgraph-io/ristretto/v2"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/aws_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/s3_utils"
	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/store_management/metrics"
)

const (
	// Do not modify separator. That change would not be backwards compatible.
	fileNameSeparator = "-"

	// If this keeps being one of the main memory allocators,
	// we can consider decreasing it to 1KB.
	defaultDownloadBufferSize = 10 * 1024 // 10KB
)

var (
	// We make it a variable, so we can obtain its address later. Do not modify this value.
	s3Bucket = "nirvana-data-context-store"

	NilLoadTargetError = errors.New("variable where data will be load cannot be nil")
	NilSaveSourceError = errors.New("variable from where data will be read cannot be nil")

	ResourceNotFoundError = errors.New("file doesn't exist within store context")
)

type storeManagerS3ImplDeps struct {
	fx.In

	S3Client      s3_utils.Client
	MetricsClient statsd.Statter
}

// storeManagerS3Impl is an implementation of StoreManager, that stored data in S3. It offers
// methods to read and write logic to/from store. This component contains the logic related to
// serialization/deserialization of objects, and interaction with the external storage system.
type storeManagerS3Impl struct {
	deps  storeManagerS3ImplDeps
	cache *ristretto.Cache[string, []byte]
}

var _ StoreManager = &storeManagerS3Impl{}

func (m *storeManagerS3Impl) Load(
	ctx context.Context,
	contextID uuid.UUID,
	storeKey StoreKey,
	r *common.Resource,
) error {
	if r == nil {
		return NilLoadTargetError
	}

	cacheHit := false
	defer func() {
		if err := metrics.EmitCachePerformanceMetric(m.deps.MetricsClient, cacheHit); err != nil {
			log.Error(ctx, "error emitting store cache hit metric", log.Err(err))
		}
	}()

	filePath, err := m.getFilePath(contextID, storeKey)
	if err != nil {
		return errors.Wrap(err, "error getting file's path")
	}

	var data []byte
	if m.cache != nil {
		cachedData, found := m.cache.Get(filePath)
		if found && cachedData != nil {
			data = cachedData
			cacheHit = true
		}
	}

	if data == nil {
		buf := make([]byte, 0, defaultDownloadBufferSize)
		downloadBuff := aws.NewWriteAtBuffer(buf)
		downloadBuff.GrowthCoeff = 2
		_, err = m.deps.S3Client.DownloadWithContext(ctx, downloadBuff, &s3.GetObjectInput{
			Bucket: &s3Bucket,
			Key:    &filePath,
		})
		if err != nil {
			if aws_utils.IsErrCodeNoSuchKey(err) {
				return errors.Wrapf(ResourceNotFoundError, "file doesn't exist within store (bucket=%s, filePath=%s)", s3Bucket, filePath)
			}

			return errors.Wrapf(err, "error downloading file from store (bucket=%s, filePath=%s)", s3Bucket, filePath)
		}
		data = downloadBuff.Bytes()

		success := m.cache.SetWithTTL(filePath, data, 0, 10*time.Minute)
		if success {
			// If set was successful, we need to wait for the cache to sync.
			defer m.cache.Wait()
		} else {
			log.Info(ctx, "failed to set cache", log.String("cacheKey", filePath))
		}
	}

	reader := bytes.NewReader(data)
	decoder := json.NewDecoder(reader)

	err = decoder.Decode(r)
	if err != nil {
		return errors.Wrapf(err, "error decoding file from S3 (bucket=%s, filePath=%s)", s3Bucket, filePath)
	}

	r.Metadata.OriginDataContextID = pointer_utils.ToPointer(contextID.String())
	r.Metadata.Dirty = false

	return nil
}

func (m *storeManagerS3Impl) Save(
	ctx context.Context,
	contextID uuid.UUID,
	storeKey StoreKey,
	r *common.Resource,
) error {
	if r == nil {
		return NilSaveSourceError
	}

	filePath, err := m.getFilePath(contextID, storeKey)
	if err != nil {
		return errors.Wrap(err, "error getting file's path")
	}

	resourceType := storeKey.GetResourceType()
	resourceTypeTmp := reflect.TypeOf(r.Data).Elem().Name()
	if resourceType != resourceTypeTmp {
		log.Warn(
			ctx,
			"resource type mismatch",
			log.String("actual", resourceType),
			log.String("expected", resourceTypeTmp),
			log.String("contextID", contextID.String()),
		)
	}

	r.Metadata.ResourceType = storeKey.GetResourceType()

	jsonBytes, err := json.MarshalIndent(r, "", "  ")
	if err != nil {
		return err
	}

	reader := bytes.NewReader(jsonBytes)

	_, err = m.deps.S3Client.UploadWithContext(ctx, &s3manager.UploadInput{
		Bucket: &s3Bucket,
		Key:    &filePath,
		Body:   reader,
	})
	if err != nil {
		return errors.Wrapf(err, "error uploading file from s3 (bucket=%s, filePath=%s)", s3Bucket, filePath)
	}

	// Cache invalidation: we shall delete the cache entry given that it
	// might be updated. If the entry is not found, it would be a no-op.
	if m.cache != nil {
		m.cache.Del(filePath)
	}

	return nil
}

func (m *storeManagerS3Impl) Exists(
	ctx context.Context,
	contextID uuid.UUID,
	storeKey StoreKey,
) (bool, error) {
	filePath, err := m.getFilePath(contextID, storeKey)
	if err != nil {
		return false, errors.Wrap(err, "error getting file's path")
	}

	if m.cache != nil {
		_, found := m.cache.Get(filePath)
		if found {
			return true, nil
		}
	}

	_, err = m.deps.S3Client.HeadObjectWithContext(ctx, &s3.HeadObjectInput{
		Bucket: &s3Bucket,
		Key:    &filePath,
	})
	if err != nil {
		if aws_utils.IsErrCodeNotFound(err) {
			return false, nil
		}
		return false, errors.Wrap(err, "error calling HeadObjectWithContext")
	}

	return true, nil
}

func (m *storeManagerS3Impl) Delete(
	ctx context.Context,
	contextID uuid.UUID,
	storeKey StoreKey,
) error {
	filePath, err := m.getFilePath(contextID, storeKey)
	if err != nil {
		return errors.Wrap(err, "error getting file's path")
	}

	if m.cache != nil {
		m.cache.Del(filePath)
	}

	_, err = m.deps.S3Client.DeleteObjectWithContext(ctx, &s3.DeleteObjectInput{
		Bucket: &s3Bucket,
		Key:    &filePath,
	})
	return err
}

// getFilePath is the standard way in which we create file paths for store resources.
// It gets from the store key object a slice of strings which are joined to form the file
// name with a JSON extension. It also adds an intermediary based on the resource type.
//
// Do not modify method as this would not be backwards compatible.
func (m *storeManagerS3Impl) getFilePath(contextID uuid.UUID, storeKey StoreKey) (string, error) {
	fileNameComponents, err := storeKey.GetFileNameComponents()
	if err != nil {
		return "", err
	}

	fileName := fmt.Sprintf("%s.json", strings.Join(fileNameComponents, fileNameSeparator))
	return filepath.Join(
		contextID.String(),
		storeKey.GetResourceType(),
		fileName,
	), nil
}
