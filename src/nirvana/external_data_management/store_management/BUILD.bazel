load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "store_management",
    srcs = [
        "doc.go",
        "fx.go",
        "interfaces.go",
        "new.go",
        "store_keys_utils.go",
        "store_manager_mock.go",
        "store_manager_s3_impl.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/store_management",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/aws_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/store_management/metrics",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//service/s3",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_dgraph_io_ristretto_v2//:ristretto",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "store_management_test",
    srcs = ["store_manager_s3_impl_test.go"],
    embed = [":store_management"],
    deps = [
        "//nirvana/common-go/aws_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/store_management/testing",
        "//nirvana/infra/fx/testloader",
        "@com_github_aws_aws_sdk_go//service/s3",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_dgraph_io_ristretto_v2//:ristretto",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
