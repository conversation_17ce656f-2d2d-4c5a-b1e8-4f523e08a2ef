package store_management

import (
	"context"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/external_data_management/common"
)

// StoreManager interface is a collection of methods used to interact with a certain store.
type StoreManager interface {
	// Load method reads from the scope defined by contextID.
	// It loads the data retrieved to variable r.
	// Note that r.Data needs to be a pointer in order to change its contents.
	// This method should set r.Metadata.OriginDataContextID and r.Metadata.Dirty fields.
	Load(ctx context.Context, contextID uuid.UUID, storeKey StoreKey, r *common.Resource) error

	// Save method stores the value passed in variable r, in the scope defined by contextID.
	Save(ctx context.Context, contextID uuid.UUID, storeKey StoreKey, r *common.Resource) error

	// Exists checks if a certain resource is in the scope defined by contextID.
	Exists(ctx context.Context, contextID uuid.UUID, storeKey StoreKey) (bool, error)

	// Delete removes a certain resource in the scope defined by contextID.
	Delete(ctx context.Context, contextID uuid.UUID, store<PERSON><PERSON> StoreKey) error
}

type StoreKey interface {
	// GetResourceType returns the type of the resource that this store key represents. This is directly related to the
	// directory of the resource in the store.
	GetResourceType() string

	// GetFileNameComponents returns the components of the file name that this store key represents. This is directly
	// related to the file name within the resource directory in the store.
	GetFileNameComponents() ([]string, error)
}
