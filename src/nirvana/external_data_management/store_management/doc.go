package store_management

/*
This package exposes a simple StoreManager interface for basic store related operations (e.g. read, write, delete).

As of 15/05/2024, there is only one implementation of such interface, which consists on a thin wrapper on top of our
more basic S3Client.
*/

// Command to regenerate StoreManager mock
//go:generate go run go.uber.org/mock/mockgen -destination=store_manager_mock.go -package=store_management nirvanatech.com/nirvana/external_data_management/store_management StoreManager
