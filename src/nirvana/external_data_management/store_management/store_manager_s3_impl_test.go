package store_management

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/dgraph-io/ristretto/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/common-go/aws_utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/s3_utils"
	"nirvanatech.com/nirvana/external_data_management/common"
	testingUtils "nirvanatech.com/nirvana/external_data_management/store_management/testing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type storeManagerTestEnv struct {
	fx.In

	StoreManager StoreManager
	S3Client     s3_utils.Client
}

type storeManagerTestSuite struct {
	suite.Suite

	ctx   context.Context
	env   storeManagerTestEnv
	fxapp *fxtest.App
}

func (s *storeManagerTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)
}

func (s *storeManagerTestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *storeManagerTestSuite) Test_Roundtrip() {
	contextID1 := uuid.New()
	storeKey1 := testingUtils.MockKey{ID: uuid.New(), Param: &testingUtils.MockParam{ID: uuid.New()}}

	// Calling with empty store should return ResourceNotFoundError.
	target := testingUtils.MockData{}
	resource := common.Resource{Data: &target}
	err := s.env.StoreManager.Load(s.ctx, contextID1, &storeKey1, &resource)
	s.Require().ErrorIs(err, ResourceNotFoundError)
	s.Require().Zero(target)

	// Calling with empty store should return false.
	exists, err := s.env.StoreManager.Exists(s.ctx, contextID1, &storeKey1)
	s.Require().Nil(err)
	s.Require().False(exists)

	// Now we write to store.
	data := testingUtils.MockData{ID: uuid.New()}
	resource = common.Resource{Data: &data}
	err = s.env.StoreManager.Save(s.ctx, contextID1, &storeKey1, &resource)
	s.Require().NoError(err)

	// Calling with populated store should load data to target.
	target = testingUtils.MockData{}
	resource = common.Resource{Data: &target}
	err = s.env.StoreManager.Load(s.ctx, contextID1, &storeKey1, &resource)
	s.Require().NoError(err)
	s.Require().Equal(data, target)
	s.Require().Equal(contextID1, uuid.MustParse(*resource.Metadata.OriginDataContextID))
	s.Require().False(resource.Metadata.Dirty)

	// Calling with populated store should return true.
	exists, err = s.env.StoreManager.Exists(s.ctx, contextID1, &storeKey1)
	s.Require().Nil(err)
	s.Require().True(exists)

	// Calling with populated store, but different key generator should return ResourceNotFoundError.
	storeKey2 := testingUtils.MockKey{ID: uuid.New(), Param: &testingUtils.MockParam{ID: uuid.New()}}
	target = testingUtils.MockData{}
	mockOriginContextID := pointer_utils.ToPointer("some-context-id")
	resource = common.Resource{
		Data: &target,
		Metadata: common.ResourceMetadata{
			Dirty:               true,
			OriginDataContextID: mockOriginContextID,
		},
	}
	err = s.env.StoreManager.Load(s.ctx, contextID1, &storeKey2, &resource)
	s.Require().ErrorIs(err, ResourceNotFoundError)
	s.Require().Zero(target)
	s.Require().Equal(mockOriginContextID, resource.Metadata.OriginDataContextID)
	s.Require().True(resource.Metadata.Dirty)

	// Calling with same key generator, but different context ID, should return ResourceNotFoundError.
	contextID2 := uuid.New()
	target = testingUtils.MockData{}
	mockOriginContextID = pointer_utils.ToPointer("some-other-context-id")
	resource = common.Resource{
		Data: &target,
		Metadata: common.ResourceMetadata{
			Dirty:               true,
			OriginDataContextID: mockOriginContextID,
		},
	}
	err = s.env.StoreManager.Load(s.ctx, contextID2, &storeKey1, &resource)
	s.Require().ErrorIs(err, ResourceNotFoundError)
	s.Require().Zero(target)
	s.Require().Equal(mockOriginContextID, resource.Metadata.OriginDataContextID)
	s.Require().True(resource.Metadata.Dirty)

	// Now we delete the data from store.
	err = s.env.StoreManager.Delete(s.ctx, contextID1, &storeKey1)
	s.Require().Nil(err)

	// Calling with de-populated store should return false.
	exists, err = s.env.StoreManager.Exists(s.ctx, contextID1, &storeKey1)
	s.Require().Nil(err)
	s.Require().False(exists)
}

func (s *storeManagerTestSuite) Test_FilePath() {
	contextID := uuid.New()

	keyID := uuid.New()
	ParamID := uuid.New()

	storeKey := testingUtils.MockKey{ID: keyID, Param: &testingUtils.MockParam{ID: ParamID}}

	storeManagerImpl := s.env.StoreManager.(*storeManagerS3Impl)
	storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
	s.Require().NoError(err)

	expectedFilePath := fmt.Sprintf(
		"%s/MockData/%s-%s.json",
		contextID.String(),
		keyID.String(),
		ParamID.String(),
	)
	s.Require().Equal(expectedFilePath, storeFilePath)
}

func (s *storeManagerTestSuite) Test_Load_WithNilValue() {
	err := s.env.StoreManager.Load(s.ctx, uuid.New(), &testingUtils.MockKey{}, nil)
	s.Require().Equal(NilLoadTargetError, err)
}

func (s *storeManagerTestSuite) Test_Save_WithNilValue() {
	err := s.env.StoreManager.Save(s.ctx, uuid.New(), &testingUtils.MockKey{}, nil)
	s.Require().Equal(NilSaveSourceError, err)
}

func (s *storeManagerTestSuite) uploadToStore(
	fileKey string,
	data *common.Resource,
) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	_, err = s.env.S3Client.UploadWithContext(s.ctx, &s3manager.UploadInput{
		Bucket: &s3Bucket,
		Key:    &fileKey,
		Body:   bytes.NewReader(dataBytes),
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *storeManagerTestSuite) uploadToCache(
	cache *ristretto.Cache[string, []byte],
	cacheKey string,
	data *common.Resource,
) {
	dataBytes, err := json.Marshal(data)
	s.Require().NoError(err)

	ok := cache.Set(cacheKey, dataBytes, 0)
	s.Require().True(ok)
	cache.Wait()
}

func (s *storeManagerTestSuite) getDataFromStore(
	fileKey string,
	target *common.Resource,
) error {
	downloadBuff := s3_utils.NewWriteAtBuffer()
	_, err := s.env.S3Client.DownloadWithContext(s.ctx, downloadBuff, &s3.GetObjectInput{
		Bucket: &s3Bucket,
		Key:    &fileKey,
	})
	if err != nil {
		return err
	}

	data := downloadBuff.Bytes()
	err = json.Unmarshal(data, target)
	if err != nil {
		return err
	}
	return nil
}

func (s *storeManagerTestSuite) Test_StoreManager_Load() {
	contextID := uuid.New()

	storeManagerImpl := s.env.StoreManager.(*storeManagerS3Impl)
	cache := storeManagerImpl.cache

	s.Run("No data in cache, no data in store", func() {
		storeKey := testingUtils.MockKey{ID: uuid.New(), Param: &testingUtils.MockParam{ID: uuid.New()}}

		target := &common.Resource{
			Data: &testingUtils.MockData{},
		}
		err := s.env.StoreManager.Load(s.ctx, contextID, &storeKey, target)
		s.Require().ErrorIs(err, ResourceNotFoundError)
		s.Require().Equal(target.Data, &testingUtils.MockData{})
	})

	s.Run("Data in cache, no data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		cacheData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToCache(cache, storeFilePath, cacheData)

		target := &common.Resource{
			Data: &testingUtils.MockData{},
		}
		err = s.env.StoreManager.Load(s.ctx, contextID, &storeKey, target)
		s.Require().NoError(err)
		s.Require().Equal(cacheData.Data, target.Data)
	})

	s.Run("No data in cache, data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToStore(storeFilePath, storeData)

		target := &common.Resource{
			Data: &testingUtils.MockData{},
		}
		err = s.env.StoreManager.Load(s.ctx, contextID, &storeKey, target)
		s.Require().NoError(err)
		s.Require().Equal(storeData.Data, target.Data)
	})

	s.Run("Data in cache, different data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToStore(storeFilePath, storeData)

		cacheData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToCache(cache, storeFilePath, cacheData)

		target := &common.Resource{
			Data: &testingUtils.MockData{},
		}
		err = s.env.StoreManager.Load(s.ctx, contextID, &storeKey, target)
		s.Require().NoError(err)
		s.Require().Equal(cacheData.Data, target.Data)
	})
}

func (s *storeManagerTestSuite) Test_StoreManager_Exists() {
	contextID := uuid.New()

	storeManagerImpl := s.env.StoreManager.(*storeManagerS3Impl)
	cache := storeManagerImpl.cache

	s.Run("No data in cache, no data in store", func() {
		storeKey := testingUtils.MockKey{ID: uuid.New(), Param: &testingUtils.MockParam{ID: uuid.New()}}
		exists, err := s.env.StoreManager.Exists(s.ctx, contextID, &storeKey)
		s.Require().NoError(err)
		s.Require().False(exists)
	})

	s.Run("Data in cache, no data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		cacheData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToCache(cache, storeFilePath, cacheData)

		exists, err := s.env.StoreManager.Exists(s.ctx, contextID, &storeKey)
		s.Require().NoError(err)
		s.Require().True(exists)
	})

	s.Run("No data in cache, data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToStore(storeFilePath, storeData)

		exists, err := s.env.StoreManager.Exists(s.ctx, contextID, &storeKey)
		s.Require().NoError(err)
		s.Require().True(exists)
	})

	s.Run("Data in cache, different data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		cacheData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToCache(cache, storeFilePath, cacheData)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToStore(storeFilePath, storeData)

		exists, err := s.env.StoreManager.Exists(s.ctx, contextID, &storeKey)
		s.Require().NoError(err)
		s.Require().True(exists)
	})
}

func (s *storeManagerTestSuite) Test_StoreManager_Save() {
	contextID := uuid.New()

	storeManagerImpl := s.env.StoreManager.(*storeManagerS3Impl)
	cache := storeManagerImpl.cache

	s.Run("No data in cache, no data in store", func() {
		storeKey := testingUtils.MockKey{ID: uuid.New(), Param: &testingUtils.MockParam{ID: uuid.New()}}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		err = s.env.StoreManager.Save(s.ctx, contextID, &storeKey, storeData)
		s.Require().NoError(err)

		target := &common.Resource{
			Data: &testingUtils.MockData{},
		}
		err = s.getDataFromStore(storeFilePath, target)
		s.Require().NoError(err)
		s.Require().Equal(storeData.Data, target.Data)

		_, found := cache.Get(storeFilePath)
		s.Require().False(found)
	})

	s.Run("Data in cache, no data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		cacheData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToCache(cache, storeFilePath, cacheData)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		err = s.env.StoreManager.Save(s.ctx, contextID, &storeKey, storeData)
		s.Require().NoError(err)

		target := &common.Resource{
			Data: &testingUtils.MockData{},
		}
		err = s.getDataFromStore(storeFilePath, target)
		s.Require().NoError(err)
		s.Require().Equal(storeData.Data, target.Data)

		_, found := cache.Get(storeFilePath)
		s.Require().False(found)
	})

	s.Run("No data in cache, data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToStore(storeFilePath, storeData)

		newData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		err = s.env.StoreManager.Save(s.ctx, contextID, &storeKey, newData)
		s.Require().NoError(err)

		target := &common.Resource{
			Data: &testingUtils.MockData{},
		}
		err = s.getDataFromStore(storeFilePath, target)
		s.Require().NoError(err)
		s.Require().Equal(newData.Data, target.Data)

		_, found := cache.Get(storeFilePath)
		s.Require().False(found)
	})

	s.Run("Data in cache, different data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		cacheData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToCache(cache, storeFilePath, cacheData)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToStore(storeFilePath, storeData)

		newData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		err = s.env.StoreManager.Save(s.ctx, contextID, &storeKey, newData)
		s.Require().NoError(err)

		target := &common.Resource{
			Data: &testingUtils.MockData{},
		}
		err = s.getDataFromStore(storeFilePath, target)
		s.Require().NoError(err)
		s.Require().Equal(newData.Data, target.Data)
	})
}

func (s *storeManagerTestSuite) Test_StoreManager_Delete() {
	contextID := uuid.New()

	storeManagerImpl := s.env.StoreManager.(*storeManagerS3Impl)
	cache := storeManagerImpl.cache

	s.Run("No data in cache, no data in store", func() {
		storeKey := testingUtils.MockKey{ID: uuid.New(), Param: &testingUtils.MockParam{ID: uuid.New()}}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		err = s.env.StoreManager.Delete(s.ctx, contextID, &storeKey)
		s.Require().NoError(err)

		_, err = s.env.S3Client.HeadObjectWithContext(s.ctx, &s3.HeadObjectInput{
			Bucket: &s3Bucket,
			Key:    &storeFilePath,
		})
		s.Require().True(aws_utils.IsErrCodeNotFound(err))

		_, found := cache.Get(storeFilePath)
		s.Require().False(found)
	})

	s.Run("Data in cache, no data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		cacheData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToCache(cache, storeFilePath, cacheData)

		err = s.env.StoreManager.Delete(s.ctx, contextID, &storeKey)
		s.Require().NoError(err)

		_, err = s.env.S3Client.HeadObjectWithContext(s.ctx, &s3.HeadObjectInput{
			Bucket: &s3Bucket,
			Key:    &storeFilePath,
		})
		s.Require().True(aws_utils.IsErrCodeNotFound(err))

		_, found := cache.Get(storeFilePath)
		s.Require().False(found)
	})

	s.Run("No data in cache, data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}
		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToStore(storeFilePath, storeData)

		err = s.env.StoreManager.Delete(s.ctx, contextID, &storeKey)
		s.Require().NoError(err)

		_, err = s.env.S3Client.HeadObjectWithContext(s.ctx, &s3.HeadObjectInput{
			Bucket: &s3Bucket,
			Key:    &storeFilePath,
		})
		s.Require().True(aws_utils.IsErrCodeNotFound(err))

		_, found := cache.Get(storeFilePath)
		s.Require().False(found)
	})

	s.Run("Data in cache, different data in store", func() {
		storeKey := testingUtils.MockKey{
			ID: uuid.New(),
			Param: &testingUtils.MockParam{
				ID: uuid.New(),
			},
		}

		storeFilePath, err := storeManagerImpl.getFilePath(contextID, &storeKey)
		s.Require().NoError(err)

		cacheData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToCache(cache, storeFilePath, cacheData)

		storeData := &common.Resource{
			Data: &testingUtils.MockData{ID: uuid.New()},
		}
		s.uploadToStore(storeFilePath, storeData)

		err = s.env.StoreManager.Delete(s.ctx, contextID, &storeKey)
		s.Require().NoError(err)

		_, err = s.env.S3Client.HeadObjectWithContext(s.ctx, &s3.HeadObjectInput{
			Bucket: &s3Bucket,
			Key:    &storeFilePath,
		})
		s.Require().True(aws_utils.IsErrCodeNotFound(err))

		_, found := cache.Get(storeFilePath)
		s.Require().False(found)
	})
}

func TestStoreManager(t *testing.T) {
	suite.Run(t, new(storeManagerTestSuite))
}
