package data_processing

import (
	"sync"
)

type cacheKey string

const (
	fleetViolationsDataV1CacheKey cacheKey = "fleet_violations_data_v1"
	nhtsaToIsoMappingV1           cacheKey = "nhtsa_to_iso_mapping_v1"
)

// staticDataCache stores static data that can be used by any processor instance,
// to avoid re-fetching from source which can be expensive as some of the static data
// is pretty large.
type staticDataCache struct {
	data map[cacheKey]any
	mu   sync.Mutex
}

// load gets a key and unmarshalls the bytes stored under that key into target.
// We assume target is a pointer to a struct of the correct type.
// If the key is not found, fetcherFn is called to get the data,
// which is then stored under the key, and unmarshalled into target.
func (c *staticDataCache) load(key cacheKey, fetcherFn func() (any, error)) (any, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	var err error
	var data any

	data, ok := c.data[key]
	if !ok {
		data, err = fetcherFn()
		if err != nil {
			return nil, err
		}

		c.data[key] = data
	}

	return data, nil
}

var sharedCache = &staticDataCache{data: make(map[cacheKey]any)}
