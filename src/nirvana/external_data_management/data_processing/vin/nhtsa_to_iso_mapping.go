package vin

import (
	"context"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

func GetNhtsaToIsoMappingV1(
	ctx context.Context,
	storeManager store_management.StoreManager,
) (*NhtsaToIsoMappingV1, error) {
	storeKey := &NhtsaToIsoMappingV1StoreKey{}
	mapping := &NhtsaToIsoMappingV1{}
	resource := &common.Resource{Data: mapping}

	err := static_data_utils.LoadStaticData(ctx, storeManager, resource, storeKey)
	if err != nil {
		return nil, err
	}

	return mapping, nil
}
