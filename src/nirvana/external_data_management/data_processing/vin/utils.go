package vin

import (
	"fmt"
	"strings"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func CreateNhtsaToIsoKeyV1(
	vehicleType data_fetching.VehicleTypeV1,
	vehicleBodyClass string,
	vehicleWeightClass data_fetching.WeightClassV1,
) (string, error) {
	sanitize := func(s string) string {
		if s == "" {
			return "EMPTY"
		}
		return strings.ToUpper(s)
	}

	nhtsaVehicleType, err := parseVehicleTypeToNhtsa(vehicleType)
	if err != nil {
		return "", err
	}

	nhtsaVehicleWeightClass, err := parseVehicleWeightClassToNhtsa(vehicleWeightClass)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf(
		"%s::%s::%s",
		sanitize(nhtsaVehicleType),
		sanitize(vehicleBodyClass),
		sanitize(nhtsaVehicleWeightClass),
	), nil
}

func parseVehicleTypeToNhtsa(vehicleType data_fetching.VehicleTypeV1) (string, error) {
	switch vehicleType {
	case data_fetching.VehicleTypeV1_VehicleTypeV1_Unspecified:
		return "", nil
	case data_fetching.VehicleTypeV1_VehicleTypeV1_Motorcycle:
		return "MOTORCYCLE", nil
	case data_fetching.VehicleTypeV1_VehicleTypeV1_Trailer:
		return "TRAILER", nil
	case data_fetching.VehicleTypeV1_VehicleTypeV1_Truck:
		return "TRUCK", nil
	case data_fetching.VehicleTypeV1_VehicleTypeV1_PassengerCar:
		return "PASSENGERCAR", nil
	case data_fetching.VehicleTypeV1_VehicleTypeV1_MultipurposePassengerVehicle:
		return "MULTIPURPOSEPASSENGERVEHICLE", nil
	case data_fetching.VehicleTypeV1_VehicleTypeV1_Bus:
		return "BUS", nil
	case data_fetching.VehicleTypeV1_VehicleTypeV1_Nil:
		return "NIL", nil
	case data_fetching.VehicleTypeV1_VehicleTypeV1_IncompleteVehicle:
		return "INCOMPLETE VEHICLE", nil
	default:
		return "", errors.New("unknown vehicle type")
	}
}

func parseVehicleWeightClassToNhtsa(vehicleWeightClass data_fetching.WeightClassV1) (string, error) {
	switch vehicleWeightClass {
	case data_fetching.WeightClassV1_WeightClassV1_Unspecified:
		return "", nil
	case data_fetching.WeightClassV1_WeightClassV1_A:
		return "A", nil
	case data_fetching.WeightClassV1_WeightClassV1_B:
		return "B", nil
	case data_fetching.WeightClassV1_WeightClassV1_C:
		return "C", nil
	case data_fetching.WeightClassV1_WeightClassV1_D:
		return "D", nil
	case data_fetching.WeightClassV1_WeightClassV1_E:
		return "E", nil
	case data_fetching.WeightClassV1_WeightClassV1_F:
		return "F", nil
	case data_fetching.WeightClassV1_WeightClassV1_G:
		return "G", nil
	case data_fetching.WeightClassV1_WeightClassV1_H:
		return "H", nil
	case data_fetching.WeightClassV1_WeightClassV1_3:
		return "3", nil
	case data_fetching.WeightClassV1_WeightClassV1_4:
		return "4", nil
	case data_fetching.WeightClassV1_WeightClassV1_5:
		return "5", nil
	case data_fetching.WeightClassV1_WeightClassV1_6:
		return "6", nil
	case data_fetching.WeightClassV1_WeightClassV1_7:
		return "7", nil
	case data_fetching.WeightClassV1_WeightClassV1_8:
		return "8", nil
	case data_fetching.WeightClassV1_WeightClassV1_Nil:
		return "", nil
	default:
		return "", errors.New("unknown weight class")
	}
}
