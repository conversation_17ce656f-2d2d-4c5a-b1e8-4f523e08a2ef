package vin

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestCreateNhtsaToIsoKeyV1(t *testing.T) {
	vehicleType := data_fetching.VehicleTypeV1_VehicleTypeV1_Truck
	vehicleBodyClass := "BODYCLASS1"
	vehicleWeightClass := data_fetching.WeightClassV1_WeightClassV1_A

	// When all inputs are not empty
	result, err := CreateNhtsaToIsoKeyV1(
		vehicleType,
		vehicleBodyClass,
		vehicleWeightClass,
	)
	require.NoError(t, err)
	require.Equal(t, "TRUCK::BODYCLASS1::A", result)

	// When a single input is empty
	result, err = CreateNhtsaToIsoKeyV1(
		data_fetching.VehicleTypeV1_VehicleTypeV1_Unspecified,
		"BODYCLASS1",
		data_fetching.WeightClassV1_WeightClassV1_A,
	)
	require.NoError(t, err)
	require.Equal(t, "EMPTY::BODYCLASS1::A", result)

	result, err = CreateNhtsaToIsoKeyV1(
		data_fetching.VehicleTypeV1_VehicleTypeV1_IncompleteVehicle,
		"",
		data_fetching.WeightClassV1_WeightClassV1_A,
	)
	require.NoError(t, err)
	require.Equal(t, "INCOMPLETE VEHICLE::EMPTY::A", result)

	result, err = CreateNhtsaToIsoKeyV1(
		data_fetching.VehicleTypeV1_VehicleTypeV1_Bus,
		"BODYCLASS1",
		data_fetching.WeightClassV1_WeightClassV1_Unspecified,
	)
	require.NoError(t, err)
	require.Equal(t, "BUS::BODYCLASS1::EMPTY", result)

	// When all inputs are empty
	result, err = CreateNhtsaToIsoKeyV1(
		data_fetching.VehicleTypeV1_VehicleTypeV1_Unspecified,
		"",
		data_fetching.WeightClassV1_WeightClassV1_Unspecified,
	)
	require.NoError(t, err)
	require.Equal(t, "EMPTY::EMPTY::EMPTY", result)
}
