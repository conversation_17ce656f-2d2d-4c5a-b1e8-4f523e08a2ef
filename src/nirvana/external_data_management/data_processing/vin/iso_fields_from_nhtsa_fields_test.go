package vin

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestIsoFieldsFromNhtsaFieldsV1(t *testing.T) {
	vehicleType1 := data_fetching.VehicleTypeV1_VehicleTypeV1_Truck
	bodyClass1 := "bodyClass1"
	weightClass1 := data_fetching.WeightClassV1_WeightClassV1_A
	mapKey1 := "TRUCK::BODYCLASS1::A"

	vehicleType2 := data_fetching.VehicleTypeV1_VehicleTypeV1_IncompleteVehicle
	bodyClass2 := "bodyClass2"
	weightClass2 := data_fetching.WeightClassV1_WeightClassV1_B
	mapKey2 := "INCOMPLETE VEHICLE::BODYCLASS2::B"

	vehicleType3 := data_fetching.VehicleTypeV1_VehicleTypeV1_Bus
	bodyClass3 := "bodyClass3"
	weightClass3 := data_fetching.WeightClassV1_WeightClassV1_C
	mapKey3 := "BUS::BODYCLASS3::C"

	vehicleType4 := data_fetching.VehicleTypeV1_VehicleTypeV1_Trailer
	bodyClass4 := "bodyClass4"
	weightClass4 := data_fetching.WeightClassV1_WeightClassV1_D
	mapKey4 := "TRAILER::BODYCLASS4::D"

	vehicleType5 := data_fetching.VehicleTypeV1_VehicleTypeV1_MultipurposePassengerVehicle
	bodyClass5 := "bodyClass5"
	weightClass5 := data_fetching.WeightClassV1_WeightClassV1_E
	mapKey5 := "MULTIPURPOSEPASSENGERVEHICLE::BODYCLASS5::E"

	mockNhtsaToIsoMapping := &NhtsaToIsoMappingV1{
		Data: map[string]*NhtsaToIsoEntryV1{
			mapKey1: {
				ShouldBeKept:   false,
				IsProblematic:  false,
				IsoVehicleType: IsoVehicleTypeV1_IsoVehicleTypeV1Truck,
				IsoWeightGroup: IsoWeightGroupV1_IsoWeightGroupV1ExtraHeavy,
			},
			mapKey2: {
				ShouldBeKept:   true,
				IsProblematic:  false,
				IsoVehicleType: IsoVehicleTypeV1_IsoVehicleTypeV1Empty,
				IsoWeightGroup: IsoWeightGroupV1_IsoWeightGroupV1ExtraHeavy,
			},
			mapKey3: {
				ShouldBeKept:   true,
				IsProblematic:  false,
				IsoVehicleType: IsoVehicleTypeV1_IsoVehicleTypeV1Truck,
				IsoWeightGroup: IsoWeightGroupV1_IsoWeightGroupV1Empty,
			},
			mapKey4: {
				ShouldBeKept:   true,
				IsProblematic:  true,
				IsoVehicleType: IsoVehicleTypeV1_IsoVehicleTypeV1SemiTrailer,
				IsoWeightGroup: IsoWeightGroupV1_IsoWeightGroupV1SemiTrailer,
			},
			mapKey5: {
				ShouldBeKept:   true,
				IsProblematic:  false,
				IsoVehicleType: IsoVehicleTypeV1_IsoVehicleTypeV1Tractor,
				IsoWeightGroup: IsoWeightGroupV1_IsoWeightGroupV1Light,
			},
		},
	}

	testCases := []struct {
		vehicleType            data_fetching.VehicleTypeV1
		vehicleBodyClass       string
		vehicleWeightClass     data_fetching.WeightClassV1
		expectedErr            error
		expectedIsoVehicleType *IsoVehicleTypeV1
		expectedIsoWeightGroup *IsoWeightGroupV1
	}{
		{
			vehicleType:        vehicleType1,
			vehicleBodyClass:   "NotFound",
			vehicleWeightClass: weightClass1,
			expectedErr:        EntryNotFoundInMappingErr,
		},
		{
			vehicleType:        vehicleType1,
			vehicleBodyClass:   bodyClass1,
			vehicleWeightClass: weightClass1,
			expectedErr:        EntryShouldNotBeKeptErr,
		},
		{
			vehicleType:        vehicleType2,
			vehicleBodyClass:   bodyClass2,
			vehicleWeightClass: weightClass2,
			expectedErr:        EntryVehicleTypeIsEmptyErr,
		},
		{
			vehicleType:        vehicleType3,
			vehicleBodyClass:   bodyClass3,
			vehicleWeightClass: weightClass3,
			expectedErr:        EntryWeightGroupIsEmptyErr,
		},
		{
			vehicleType:            vehicleType4,
			vehicleBodyClass:       bodyClass4,
			vehicleWeightClass:     weightClass4,
			expectedErr:            EntryIsProblematicErr,
			expectedIsoVehicleType: pointer_utils.ToPointer(IsoVehicleTypeV1_IsoVehicleTypeV1SemiTrailer),
			expectedIsoWeightGroup: pointer_utils.ToPointer(IsoWeightGroupV1_IsoWeightGroupV1SemiTrailer),
		},
		{
			vehicleType:            vehicleType5,
			vehicleBodyClass:       bodyClass5,
			vehicleWeightClass:     weightClass5,
			expectedIsoVehicleType: pointer_utils.ToPointer(IsoVehicleTypeV1_IsoVehicleTypeV1Tractor),
			expectedIsoWeightGroup: pointer_utils.ToPointer(IsoWeightGroupV1_IsoWeightGroupV1Light),
		},
	}

	for _, tc := range testCases {
		vt, wg, err := IsoFieldsFromNhtsaFieldsV1(
			tc.vehicleType,
			tc.vehicleBodyClass,
			tc.vehicleWeightClass,
			mockNhtsaToIsoMapping,
		)

		require.Equal(t, tc.expectedErr, err)
		require.Equal(t, tc.expectedIsoVehicleType, vt)
		require.Equal(t, tc.expectedIsoWeightGroup, wg)
	}
}
