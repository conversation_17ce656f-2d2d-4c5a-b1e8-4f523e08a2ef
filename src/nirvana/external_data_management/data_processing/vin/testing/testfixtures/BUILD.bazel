load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "testfixtures",
    srcs = ["nhtsa_to_iso_mapping_v1_fixture.go"],
    embedsrcs = ["nhtsa_to_iso_mapping_v1.json.gz"],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/vin/testing/testfixtures",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/data_processing/static_data_utils",
        "//nirvana/external_data_management/data_processing/vin",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/fx/fxregistry",
        "@org_uber_go_fx//:fx",
    ],
)
