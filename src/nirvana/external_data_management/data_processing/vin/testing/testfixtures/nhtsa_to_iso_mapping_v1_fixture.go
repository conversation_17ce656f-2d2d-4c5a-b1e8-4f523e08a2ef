package testfixtures

import (
	"context"
	_ "embed"

	"go.uber.org/fx"

	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
)

//go:embed nhtsa_to_iso_mapping_v1.json.gz
var nhtsaToIsoMappingV1 []byte

// The NhtsaToIsoMappingV1Fixture is required to populate the store used by the processor with
// certain static data. You have to require it as a dependency provided by fx in your tests.
type NhtsaToIsoMappingV1Fixture struct {
	storeManager store_management.StoreManager
}

func (f *NhtsaToIsoMappingV1Fixture) apply(_ context.Context) error {
	return static_data_utils.PopulateStaticDataForTest[vin.NhtsaToIsoMappingV1, vin.NhtsaToIsoMappingV1StoreKey](
		nhtsaToIsoMappingV1,
		f.storeManager,
	)
}

func newNhtsaToIsoMappingV1Fixture(
	lc fx.Lifecycle,
	storeManager store_management.StoreManager,
) (*NhtsaToIsoMappingV1Fixture, error) {
	fixture := &NhtsaToIsoMappingV1Fixture{storeManager}

	lc.Append(fx.Hook{OnStart: fixture.apply})

	return fixture, nil
}

var _ = fxregistry.RegisterForTest(fx.Provide(newNhtsaToIsoMappingV1Fixture))
