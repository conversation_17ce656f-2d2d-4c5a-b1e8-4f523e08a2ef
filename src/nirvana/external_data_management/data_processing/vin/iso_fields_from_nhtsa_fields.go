package vin

import (
	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

var (
	EntryNotFoundInMappingErr  = errors.New("entry not found in mapping")
	EntryShouldNotBeKeptErr    = errors.New("entry should not be kept")
	EntryVehicleTypeIsEmptyErr = errors.New("entry vehicle type is empty")
	EntryWeightGroupIsEmptyErr = errors.New("entry weight group is empty")
	EntryIsProblematicErr      = errors.New("entry is problematic")
)

func IsoFieldsFromNhtsaFieldsV1(
	vehicleType data_fetching.VehicleTypeV1,
	vehicleBodyClass string,
	vehicleWeightClass data_fetching.WeightClassV1,
	nhtsaToIsoMapping *NhtsaToIsoMappingV1,
) (*IsoVehicleTypeV1, *IsoWeightGroupV1, error) {
	key, err := CreateNhtsaToIsoKeyV1(
		vehicleType,
		vehicleBodyClass,
		vehicleWeightClass,
	)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to create nhtsa to iso key")
	}

	val, exists := nhtsaToIsoMapping.Data[key]
	if !exists {
		return nil, nil, EntryNotFoundInMappingErr
	}

	if !val.ShouldBeKept {
		return nil, nil, EntryShouldNotBeKeptErr
	}

	if val.IsoVehicleType == IsoVehicleTypeV1_IsoVehicleTypeV1Empty {
		return nil, nil, EntryVehicleTypeIsEmptyErr
	}

	if val.IsoWeightGroup == IsoWeightGroupV1_IsoWeightGroupV1Empty {
		return nil, nil, EntryWeightGroupIsEmptyErr
	}

	if val.IsProblematic {
		return &val.IsoVehicleType, &val.IsoWeightGroup, EntryIsProblematicErr
	}

	return &val.IsoVehicleType, &val.IsoWeightGroup, nil
}
