// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: data_processing/vin/api.proto

package vin

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IsoVehicleTypeV1 int32

const (
	IsoVehicleTypeV1_IsoVehicleTypeV1Empty               IsoVehicleTypeV1 = 0
	IsoVehicleTypeV1_IsoVehicleTypeV1Tractor             IsoVehicleTypeV1 = 1
	IsoVehicleTypeV1_IsoVehicleTypeV1Truck               IsoVehicleTypeV1 = 2
	IsoVehicleTypeV1_IsoVehicleTypeV1SemiTrailer         IsoVehicleTypeV1 = 3
	IsoVehicleTypeV1_IsoVehicleTypeV1SparedTrailer       IsoVehicleTypeV1 = 4
	IsoVehicleTypeV1_IsoVehicleTypeV1NonOwnedSemiTrailer IsoVehicleTypeV1 = 5
)

// Enum value maps for IsoVehicleTypeV1.
var (
	IsoVehicleTypeV1_name = map[int32]string{
		0: "IsoVehicleTypeV1Empty",
		1: "IsoVehicleTypeV1Tractor",
		2: "IsoVehicleTypeV1Truck",
		3: "IsoVehicleTypeV1SemiTrailer",
		4: "IsoVehicleTypeV1SparedTrailer",
		5: "IsoVehicleTypeV1NonOwnedSemiTrailer",
	}
	IsoVehicleTypeV1_value = map[string]int32{
		"IsoVehicleTypeV1Empty":               0,
		"IsoVehicleTypeV1Tractor":             1,
		"IsoVehicleTypeV1Truck":               2,
		"IsoVehicleTypeV1SemiTrailer":         3,
		"IsoVehicleTypeV1SparedTrailer":       4,
		"IsoVehicleTypeV1NonOwnedSemiTrailer": 5,
	}
)

func (x IsoVehicleTypeV1) Enum() *IsoVehicleTypeV1 {
	p := new(IsoVehicleTypeV1)
	*p = x
	return p
}

func (x IsoVehicleTypeV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IsoVehicleTypeV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_processing_vin_api_proto_enumTypes[0].Descriptor()
}

func (IsoVehicleTypeV1) Type() protoreflect.EnumType {
	return &file_data_processing_vin_api_proto_enumTypes[0]
}

func (x IsoVehicleTypeV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IsoVehicleTypeV1.Descriptor instead.
func (IsoVehicleTypeV1) EnumDescriptor() ([]byte, []int) {
	return file_data_processing_vin_api_proto_rawDescGZIP(), []int{0}
}

type IsoWeightGroupV1 int32

const (
	IsoWeightGroupV1_IsoWeightGroupV1Empty       IsoWeightGroupV1 = 0
	IsoWeightGroupV1_IsoWeightGroupV1Light       IsoWeightGroupV1 = 1
	IsoWeightGroupV1_IsoWeightGroupV1Medium      IsoWeightGroupV1 = 2
	IsoWeightGroupV1_IsoWeightGroupV1Heavy       IsoWeightGroupV1 = 3
	IsoWeightGroupV1_IsoWeightGroupV1ExtraHeavy  IsoWeightGroupV1 = 4
	IsoWeightGroupV1_IsoWeightGroupV1SemiTrailer IsoWeightGroupV1 = 5
)

// Enum value maps for IsoWeightGroupV1.
var (
	IsoWeightGroupV1_name = map[int32]string{
		0: "IsoWeightGroupV1Empty",
		1: "IsoWeightGroupV1Light",
		2: "IsoWeightGroupV1Medium",
		3: "IsoWeightGroupV1Heavy",
		4: "IsoWeightGroupV1ExtraHeavy",
		5: "IsoWeightGroupV1SemiTrailer",
	}
	IsoWeightGroupV1_value = map[string]int32{
		"IsoWeightGroupV1Empty":       0,
		"IsoWeightGroupV1Light":       1,
		"IsoWeightGroupV1Medium":      2,
		"IsoWeightGroupV1Heavy":       3,
		"IsoWeightGroupV1ExtraHeavy":  4,
		"IsoWeightGroupV1SemiTrailer": 5,
	}
)

func (x IsoWeightGroupV1) Enum() *IsoWeightGroupV1 {
	p := new(IsoWeightGroupV1)
	*p = x
	return p
}

func (x IsoWeightGroupV1) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IsoWeightGroupV1) Descriptor() protoreflect.EnumDescriptor {
	return file_data_processing_vin_api_proto_enumTypes[1].Descriptor()
}

func (IsoWeightGroupV1) Type() protoreflect.EnumType {
	return &file_data_processing_vin_api_proto_enumTypes[1]
}

func (x IsoWeightGroupV1) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IsoWeightGroupV1.Descriptor instead.
func (IsoWeightGroupV1) EnumDescriptor() ([]byte, []int) {
	return file_data_processing_vin_api_proto_rawDescGZIP(), []int{1}
}

type NhtsaToIsoEntryV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShouldBeKept   bool             `protobuf:"varint,1,opt,name=shouldBeKept,proto3" json:"shouldBeKept,omitempty"`
	IsProblematic  bool             `protobuf:"varint,2,opt,name=isProblematic,proto3" json:"isProblematic,omitempty"`
	IsoVehicleType IsoVehicleTypeV1 `protobuf:"varint,3,opt,name=isoVehicleType,proto3,enum=data_processing_vin.IsoVehicleTypeV1" json:"isoVehicleType,omitempty"`
	IsoWeightGroup IsoWeightGroupV1 `protobuf:"varint,4,opt,name=isoWeightGroup,proto3,enum=data_processing_vin.IsoWeightGroupV1" json:"isoWeightGroup,omitempty"`
}

func (x *NhtsaToIsoEntryV1) Reset() {
	*x = NhtsaToIsoEntryV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_vin_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NhtsaToIsoEntryV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NhtsaToIsoEntryV1) ProtoMessage() {}

func (x *NhtsaToIsoEntryV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_vin_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NhtsaToIsoEntryV1.ProtoReflect.Descriptor instead.
func (*NhtsaToIsoEntryV1) Descriptor() ([]byte, []int) {
	return file_data_processing_vin_api_proto_rawDescGZIP(), []int{0}
}

func (x *NhtsaToIsoEntryV1) GetShouldBeKept() bool {
	if x != nil {
		return x.ShouldBeKept
	}
	return false
}

func (x *NhtsaToIsoEntryV1) GetIsProblematic() bool {
	if x != nil {
		return x.IsProblematic
	}
	return false
}

func (x *NhtsaToIsoEntryV1) GetIsoVehicleType() IsoVehicleTypeV1 {
	if x != nil {
		return x.IsoVehicleType
	}
	return IsoVehicleTypeV1_IsoVehicleTypeV1Empty
}

func (x *NhtsaToIsoEntryV1) GetIsoWeightGroup() IsoWeightGroupV1 {
	if x != nil {
		return x.IsoWeightGroup
	}
	return IsoWeightGroupV1_IsoWeightGroupV1Empty
}

type NhtsaToIsoMappingV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[string]*NhtsaToIsoEntryV1 `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *NhtsaToIsoMappingV1) Reset() {
	*x = NhtsaToIsoMappingV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_vin_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NhtsaToIsoMappingV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NhtsaToIsoMappingV1) ProtoMessage() {}

func (x *NhtsaToIsoMappingV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_vin_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NhtsaToIsoMappingV1.ProtoReflect.Descriptor instead.
func (*NhtsaToIsoMappingV1) Descriptor() ([]byte, []int) {
	return file_data_processing_vin_api_proto_rawDescGZIP(), []int{1}
}

func (x *NhtsaToIsoMappingV1) GetData() map[string]*NhtsaToIsoEntryV1 {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_data_processing_vin_api_proto protoreflect.FileDescriptor

var file_data_processing_vin_api_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x69, 0x6e, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x69, 0x6e, 0x22, 0xfb, 0x01, 0x0a, 0x11, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x54, 0x6f,
	0x49, 0x73, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x56, 0x31, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x68,
	0x6f, 0x75, 0x6c, 0x64, 0x42, 0x65, 0x4b, 0x65, 0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x42, 0x65, 0x4b, 0x65, 0x70, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x12, 0x4d, 0x0a, 0x0e, 0x69, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x76,
	0x69, 0x6e, 0x2e, 0x49, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x56, 0x31, 0x52, 0x0e, 0x69, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0e, 0x69, 0x73, 0x6f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69,
	0x6e, 0x2e, 0x49, 0x73, 0x6f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x56, 0x31, 0x52, 0x0e, 0x69, 0x73, 0x6f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x22, 0xbe, 0x01, 0x0a, 0x13, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x54, 0x6f, 0x49, 0x73,
	0x6f, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x56, 0x31, 0x12, 0x46, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x6e, 0x2e, 0x4e,
	0x68, 0x74, 0x73, 0x61, 0x54, 0x6f, 0x49, 0x73, 0x6f, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x56, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x5f, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x5f, 0x76, 0x69, 0x6e, 0x2e, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x54, 0x6f, 0x49, 0x73,
	0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x56, 0x31, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x2a, 0xd2, 0x01, 0x0a, 0x10, 0x49, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x73, 0x6f, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x10, 0x01,
	0x12, 0x19, 0x0a, 0x15, 0x49, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x56, 0x31, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x49,
	0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31, 0x53,
	0x65, 0x6d, 0x69, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d,
	0x49, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x31,
	0x53, 0x70, 0x61, 0x72, 0x65, 0x64, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x10, 0x04, 0x12,
	0x27, 0x0a, 0x23, 0x49, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x56, 0x31, 0x4e, 0x6f, 0x6e, 0x4f, 0x77, 0x6e, 0x65, 0x64, 0x53, 0x65, 0x6d, 0x69, 0x54,
	0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x10, 0x05, 0x2a, 0xc0, 0x01, 0x0a, 0x10, 0x49, 0x73, 0x6f,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x31, 0x12, 0x19, 0x0a,
	0x15, 0x49, 0x73, 0x6f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56,
	0x31, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x73, 0x6f, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x31, 0x4c, 0x69, 0x67, 0x68,
	0x74, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x73, 0x6f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x31, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x10, 0x02, 0x12,
	0x19, 0x0a, 0x15, 0x49, 0x73, 0x6f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x56, 0x31, 0x48, 0x65, 0x61, 0x76, 0x79, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x73,
	0x6f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x31, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x48, 0x65, 0x61, 0x76, 0x79, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x73,
	0x6f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x31, 0x53, 0x65,
	0x6d, 0x69, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x10, 0x05, 0x42, 0x46, 0x5a, 0x44, 0x6e,
	0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e,
	0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x69, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_data_processing_vin_api_proto_rawDescOnce sync.Once
	file_data_processing_vin_api_proto_rawDescData = file_data_processing_vin_api_proto_rawDesc
)

func file_data_processing_vin_api_proto_rawDescGZIP() []byte {
	file_data_processing_vin_api_proto_rawDescOnce.Do(func() {
		file_data_processing_vin_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_data_processing_vin_api_proto_rawDescData)
	})
	return file_data_processing_vin_api_proto_rawDescData
}

var file_data_processing_vin_api_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_data_processing_vin_api_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_data_processing_vin_api_proto_goTypes = []interface{}{
	(IsoVehicleTypeV1)(0),       // 0: data_processing_vin.IsoVehicleTypeV1
	(IsoWeightGroupV1)(0),       // 1: data_processing_vin.IsoWeightGroupV1
	(*NhtsaToIsoEntryV1)(nil),   // 2: data_processing_vin.NhtsaToIsoEntryV1
	(*NhtsaToIsoMappingV1)(nil), // 3: data_processing_vin.NhtsaToIsoMappingV1
	nil,                         // 4: data_processing_vin.NhtsaToIsoMappingV1.DataEntry
}
var file_data_processing_vin_api_proto_depIdxs = []int32{
	0, // 0: data_processing_vin.NhtsaToIsoEntryV1.isoVehicleType:type_name -> data_processing_vin.IsoVehicleTypeV1
	1, // 1: data_processing_vin.NhtsaToIsoEntryV1.isoWeightGroup:type_name -> data_processing_vin.IsoWeightGroupV1
	4, // 2: data_processing_vin.NhtsaToIsoMappingV1.data:type_name -> data_processing_vin.NhtsaToIsoMappingV1.DataEntry
	2, // 3: data_processing_vin.NhtsaToIsoMappingV1.DataEntry.value:type_name -> data_processing_vin.NhtsaToIsoEntryV1
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_data_processing_vin_api_proto_init() }
func file_data_processing_vin_api_proto_init() {
	if File_data_processing_vin_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_data_processing_vin_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NhtsaToIsoEntryV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_vin_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NhtsaToIsoMappingV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_data_processing_vin_api_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_data_processing_vin_api_proto_goTypes,
		DependencyIndexes: file_data_processing_vin_api_proto_depIdxs,
		EnumInfos:         file_data_processing_vin_api_proto_enumTypes,
		MessageInfos:      file_data_processing_vin_api_proto_msgTypes,
	}.Build()
	File_data_processing_vin_api_proto = out.File
	file_data_processing_vin_api_proto_rawDesc = nil
	file_data_processing_vin_api_proto_goTypes = nil
	file_data_processing_vin_api_proto_depIdxs = nil
}
