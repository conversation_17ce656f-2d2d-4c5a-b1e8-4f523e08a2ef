load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "data_processing_vin_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/vin",
    proto = "//proto/data_processing/vin:data_processing_vin_proto",
    visibility = ["//visibility:public"],
)

# keep
go_proto_link(
    name = "data_processing_vin_go_proto_link",
    dep = ":data_processing_vin_go_proto",
    version = "v1",
)

go_library(
    name = "vin",
    srcs = [
        "iso_fields_from_nhtsa_fields.go",
        "nhtsa_to_iso_mapping.go",
        "store_keys.go",
        "utils.go",
    ],
    embed = [":data_processing_vin_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/vin",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing/static_data_utils",
        "//nirvana/external_data_management/store_management",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "vin_test",
    srcs = [
        "iso_fields_from_nhtsa_fields_test.go",
        "store_keys_test.go",
        "utils_test.go",
    ],
    embed = [":vin"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/external_data_management/data_fetching",
        "@com_github_stretchr_testify//require",
    ],
)
