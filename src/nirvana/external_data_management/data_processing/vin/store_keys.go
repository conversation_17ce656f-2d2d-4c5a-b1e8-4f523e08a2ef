package vin

import "nirvanatech.com/nirvana/external_data_management/store_management"

/*
	These methods and constants are used to build the path on S3 where data will be stored.
	DO NOT MODIFY EXISTING CODE, ONLY ADD NEW.

	If you are adding a method to the processor, make sure that your request implements store_management.StoreKey
	interface so your method can be wrapped by the store interceptor.
*/

const nhtsaToIsoMappingV1ResourceType = "NhtsaToIsoMappingV1"

type NhtsaToIsoMappingV1StoreKey struct{}

func (k *NhtsaToIsoMappingV1StoreKey) GetResourceType() string {
	return nhtsaToIsoMappingV1ResourceType
}

func (k *NhtsaToIsoMappingV1StoreKey) GetFileNameComponents() ([]string, error) {
	return []string{"data"}, nil
}

var _ store_management.StoreKey = &NhtsaToIsoMappingV1StoreKey{}
