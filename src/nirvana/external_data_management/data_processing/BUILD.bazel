load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "data_processing_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing",
    proto = "//proto/data_processing:data_processing_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing/vin",
    ],
)

# keep
go_proto_link(
    name = "data_processing_go_proto_link",
    dep = ":data_processing_go_proto",
    version = "v1",
)

go_library(
    name = "data_processing",
    srcs = [
        "cache.go",
        "client.go",
        "client_mock.go",
        "doc.go",
        "fx.go",
        "interceptors.go",
        "server.go",
        "server_mock.go",
        "store_keys.go",
    ],
    embed = [":data_processing_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/grpc/libgrpc",
        "//nirvana/common-go/grpc/middleware",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing/fmcsa",
        "//nirvana/external_data_management/data_processing/lni",
        "//nirvana/external_data_management/data_processing/metrics",
        "//nirvana/external_data_management/data_processing/mvr",
        "//nirvana/external_data_management/data_processing/national_credit_file",
        "//nirvana/external_data_management/data_processing/vin",
        "//nirvana/external_data_management/interceptors_management/unwrap_reply_interceptor",
        "//nirvana/external_data_management/interceptors_management/wrap_reply_interceptor",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "data_processing_test",
    srcs = ["interceptors_test.go"],
    embed = [":data_processing"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/data_fetching",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
