package lni

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func TestGetPriorCarrierYearsRetainedV1(t *testing.T) {
	effectiveDate := time.Date(2024, 6, 15, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name          string
		effectiveDate time.Time
		records       []*data_fetching.InsuranceRecordV1
		expectedYears int32
		expectError   bool
	}{
		{
			name:          "empty records",
			effectiveDate: effectiveDate,
			records:       []*data_fetching.InsuranceRecordV1{},
			expectedYears: 0,
			expectError:   false,
		},
		{
			name:          "nil records",
			effectiveDate: effectiveDate,
			records:       nil,
			expectedYears: 0,
			expectError:   false,
		},
		{
			name:          "only target carriers",
			effectiveDate: effectiveDate,
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "SIRIUSPOINT AMERICA INSURANCE COMPANY",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expectedYears: 0,
			expectError:   false,
		},
		{
			name:          "single valid carrier",
			effectiveDate: effectiveDate,
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "ABC Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expectedYears: 3, // 2021-2024 = 3 years
			expectError:   false,
		},
		{
			name:          "multiple carriers - most recent selected",
			effectiveDate: effectiveDate,
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "Old Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					InsuranceCompanyName: "Recent Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2023, 6, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expectedYears: 2, // Most recent: 2021-2023.5 = ~2.5 years, floored to 2
			expectError:   false,
		},
		{
			name:          "record without cancel date",
			effectiveDate: effectiveDate,
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "No Cancel Date Company",
					EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  nil,
				},
			},
			expectedYears: 3, // 2021-2024.5 = ~3.5 years, floored to 3
			expectError:   false,
		},
		{
			name:          "end date after effective date",
			effectiveDate: effectiveDate,
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "Future Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expectedYears: 2, // 2022-2024.5 = ~2.5 years, floored to 2
			expectError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetPriorCarrierYearsRetainedV1(tt.effectiveDate, tt.records)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedYears, result)
			}
		})
	}
}

func TestFilterNonTargetCarriers(t *testing.T) {
	tests := []struct {
		name     string
		records  []*data_fetching.InsuranceRecordV1
		expected int
	}{
		{
			name:     "empty records",
			records:  []*data_fetching.InsuranceRecordV1{},
			expected: 0,
		},
		{
			name:     "nil records",
			records:  nil,
			expected: 0,
		},
		{
			name: "all target carriers",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "SIRIUSPOINT AMERICA INSURANCE COMPANY",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					InsuranceCompanyName: "FALLS LAKE NATIONAL INSURANCE COMPANY",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expected: 0,
		},
		{
			name: "mixed carriers",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "SIRIUSPOINT AMERICA INSURANCE COMPANY",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					InsuranceCompanyName: "Valid Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expected: 1,
		},
		{
			name: "record without effective date",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "Valid Insurance Company",
					EffectiveDate:        nil,
					CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expected: 0, // Should be filtered out due to missing start date
		},
		{
			name: "nil record",
			records: []*data_fetching.InsuranceRecordV1{
				nil,
				{
					InsuranceCompanyName: "Valid Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := filterNonTargetCarriers(tt.records)
			assert.Len(t, result, tt.expected)
		})
	}
}

func TestIsTargetCarrier(t *testing.T) {
	tests := []struct {
		name        string
		carrierName string
		isTarget    bool
	}{
		{
			name:        "exact match - SIRIUSPOINT",
			carrierName: "SIRIUSPOINT AMERICA INSURANCE COMPANY",
			isTarget:    true,
		},
		{
			name:        "exact match - FALLS LAKE",
			carrierName: "FALLS LAKE NATIONAL INSURANCE COMPANY",
			isTarget:    true,
		},
		{
			name:        "exact match - MS TRANSVERSE",
			carrierName: "MS TRANSVERSE INSURANCE COMPANY",
			isTarget:    true,
		},
		{
			name:        "exact match - MS TRANSVERSE SPECIALTY",
			carrierName: "MS TRANSVERSE SPECIALTY INSURANCE COMPANY",
			isTarget:    true,
		},
		{
			name:        "case insensitive - lowercase",
			carrierName: "siriuspoint america insurance company",
			isTarget:    true,
		},
		{
			name:        "case insensitive - mixed case",
			carrierName: "SiriusPoint America Insurance Company",
			isTarget:    true,
		},
		{
			name:        "with extra spaces",
			carrierName: "  SIRIUSPOINT AMERICA INSURANCE COMPANY  ",
			isTarget:    true,
		},
		{
			name:        "non-target carrier",
			carrierName: "ABC Insurance Company",
			isTarget:    false,
		},
		{
			name:        "empty string",
			carrierName: "",
			isTarget:    false,
		},
		{
			name:        "partial match",
			carrierName: "SIRIUSPOINT",
			isTarget:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isTargetCarrier(tt.carrierName)
			assert.Equal(t, tt.isTarget, result)
		})
	}
}

func TestGetEndDate(t *testing.T) {
	tests := []struct {
		name     string
		record   *data_fetching.InsuranceRecordV1
		expected *time.Time
	}{
		{
			name:     "nil record",
			record:   nil,
			expected: nil,
		},
		{
			name: "with cancel date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			expected: func() *time.Time { t := time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC); return &t }(),
		},
		{
			name: "without cancel date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: nil,
			},
			expected: nil,
		},
		{
			name: "both dates nil",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       nil,
				CancelEffectiveDate: nil,
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getEndDate(tt.record)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestGetStartDate(t *testing.T) {
	tests := []struct {
		name     string
		record   *data_fetching.InsuranceRecordV1
		expected *time.Time
	}{
		{
			name:     "nil record",
			record:   nil,
			expected: nil,
		},
		{
			name: "with effective date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate: timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			expected: func() *time.Time { t := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC); return &t }(),
		},
		{
			name: "without effective date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate: nil,
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getStartDate(tt.record)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestHasValidDates(t *testing.T) {
	tests := []struct {
		name     string
		record   *data_fetching.InsuranceRecordV1
		expected bool
	}{
		{
			name:     "nil record",
			record:   nil,
			expected: false,
		},
		{
			name: "with effective date only",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: nil,
			},
			expected: true,
		},
		{
			name: "with both dates",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			expected: true,
		},
		{
			name: "without effective date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       nil,
				CancelEffectiveDate: timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			expected: false,
		},
		{
			name: "no dates",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       nil,
				CancelEffectiveDate: nil,
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := hasValidDates(tt.record)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCalculateYearsRetained(t *testing.T) {
	effectiveDate := time.Date(2024, 6, 15, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name          string
		record        *data_fetching.InsuranceRecordV1
		effectiveDate time.Time
		expectedYears int32
		expectError   bool
		errorContains string
	}{
		{
			name:          "nil record",
			record:        nil,
			effectiveDate: effectiveDate,
			expectedYears: 0,
			expectError:   true,
			errorContains: "record is nil",
		},
		{
			name: "missing start date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       nil,
				CancelEffectiveDate: timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			effectiveDate: effectiveDate,
			expectedYears: 0,
			expectError:   true,
			errorContains: "missing start date",
		},
		{
			name: "with cancel date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			effectiveDate: effectiveDate,
			expectedYears: 3, // 2021-2024 = 3 years
			expectError:   false,
		},
		{
			name: "without cancel date - uses effective date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: nil,
			},
			effectiveDate: effectiveDate,
			expectedYears: 3, // 2021-2024.5 = ~3.5 years, floored to 3
			expectError:   false,
		},
		{
			name: "end date after effective date",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			effectiveDate: effectiveDate,
			expectedYears: 2, // 2022-2024.5 = ~2.5 years, floored to 2
			expectError:   false,
		},
		{
			name: "fractional years - should be floored",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2021, 6, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			effectiveDate: effectiveDate,
			expectedYears: 2, // 2021.5-2024 = ~2.5 years, floored to 2
			expectError:   false,
		},
		{
			name: "negative years - should return 0",
			record: &data_fetching.InsuranceRecordV1{
				EffectiveDate:       timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate: timestamppb.New(time.Date(2026, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			effectiveDate: effectiveDate, // effective date is 2024-06-15, which is before start date
			expectedYears: 0,             // negative years should be clamped to 0
			expectError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := calculateYearsRetained(tt.record, tt.effectiveDate)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorContains)
				assert.Equal(t, int32(0), result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedYears, result)
			}
		})
	}
}

func TestCompareRecordsByRecency(t *testing.T) {
	tests := []struct {
		name     string
		recordI  *data_fetching.InsuranceRecordV1
		recordJ  *data_fetching.InsuranceRecordV1
		expected bool // true if recordI is more recent than recordJ
	}{
		{
			name: "no cancel date vs with cancel date - no cancel wins",
			recordI: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "No Cancel Company",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  nil,
			},
			recordJ: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "With Cancel Company",
				EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			expected: true,
		},
		{
			name: "with cancel date vs no cancel date - no cancel wins",
			recordI: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "With Cancel Company",
				EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			recordJ: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "No Cancel Company",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  nil,
			},
			expected: false,
		},
		{
			name: "both no cancel dates - later start date wins",
			recordI: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Later Start",
				EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  nil,
			},
			recordJ: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Earlier Start",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  nil,
			},
			expected: true,
		},
		{
			name: "both have cancel dates - later cancel date wins",
			recordI: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Later Cancel",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			recordJ: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Earlier Cancel",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			expected: true,
		},
		{
			name: "same cancel dates - later start date wins",
			recordI: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Later Start Same Cancel",
				EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			recordJ: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Earlier Start Same Cancel",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			expected: true,
		},
		{
			name: "same dates - alphabetically earlier company name wins",
			recordI: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Alpha Insurance Company",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			recordJ: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Zebra Insurance Company",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
			},
			expected: true,
		},
		{
			name: "same no cancel dates and start dates - alphabetically earlier wins",
			recordI: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Alpha No Cancel",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  nil,
			},
			recordJ: &data_fetching.InsuranceRecordV1{
				InsuranceCompanyName: "Beta No Cancel",
				EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				CancelEffectiveDate:  nil,
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := compareRecordsByRecency(tt.recordI, tt.recordJ)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFindMostRecentPriorCarrier(t *testing.T) {
	tests := []struct {
		name     string
		records  []*data_fetching.InsuranceRecordV1
		expected string // Insurance company name of expected result
	}{
		{
			name:     "empty records",
			records:  []*data_fetching.InsuranceRecordV1{},
			expected: "",
		},
		{
			name:     "nil records",
			records:  nil,
			expected: "",
		},
		{
			name: "single record",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "ABC Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expected: "ABC Insurance Company",
		},
		{
			name: "multiple records - most recent by cancel date",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "Old Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					InsuranceCompanyName: "Recent Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expected: "Recent Insurance Company",
		},
		{
			name: "record without cancel date should be most recent",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "With Cancel Date",
					EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					InsuranceCompanyName: "Without Cancel Date",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  nil,
				},
			},
			expected: "Without Cancel Date",
		},
		{
			name: "multiple records without cancel dates - sorted by start date",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "Earlier Start Date",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  nil,
				},
				{
					InsuranceCompanyName: "Later Start Date",
					EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  nil,
				},
			},
			expected: "Later Start Date", // Most recent start date when no cancel dates
		},
		{
			name: "records with same end dates - sorted by start date",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "Earlier Start Same End",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					InsuranceCompanyName: "Later Start Same End",
					EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expected: "Later Start Same End", // Most recent start date when end dates are equal
		},
		{
			name: "records with same dates - sorted by company name",
			records: []*data_fetching.InsuranceRecordV1{
				{
					InsuranceCompanyName: "Zebra Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					InsuranceCompanyName: "Alpha Insurance Company",
					EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
					CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			expected: "Alpha Insurance Company", // Alphabetically first when all dates are equal
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := findMostRecentPriorCarrier(tt.records)

			if tt.expected == "" {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tt.expected, result.InsuranceCompanyName)
			}
		})
	}
}
