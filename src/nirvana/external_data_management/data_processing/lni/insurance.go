package lni

import (
	"sort"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

// GetInsuranceStartDateV1 calculates the start date of the most recent continuous coverage period
// based on the cancellation dates and allowed gaps between consecutive insurance records.
func GetInsuranceStartDateV1(
	insuranceRecords []*data_fetching.InsuranceRecordV1,
	allowedWindowInDays time.Duration,
) (*time.Time, error) {
	if len(insuranceRecords) == 0 {
		return nil, errors.New("no insurance records found")
	}

	startDate := insuranceRecords[0].EffectiveDate.AsTime()

	for i := 1; i < len(insuranceRecords); i++ {
		nextRecord := insuranceRecords[i]

		// Some edge cases have records without a cancel effective date
		// We've decided to skip those records since the next one
		// will have the exact same details with a cancel effective date
		if nextRecord.CancelEffectiveDate == nil {
			continue
		}

		if startDate.Sub(nextRecord.CancelEffectiveDate.AsTime()) <= allowedWindowInDays &&
			nextRecord.EffectiveDate.AsTime().Before(startDate) {
			startDate = nextRecord.EffectiveDate.AsTime()
		}
	}

	return &startDate, nil
}

// HasActiveInsuranceV1 checks if the insurance records are not empty and that
// the first record is active on the effective date.
func HasActiveInsuranceV1(
	insuranceRecords []*data_fetching.InsuranceRecordV1,
	effectiveDate time.Time,
) bool {
	if len(insuranceRecords) == 0 {
		return false
	}
	if insuranceRecords[0].CancelEffectiveDate != nil &&
		insuranceRecords[0].CancelEffectiveDate.AsTime().Before(effectiveDate) {
		return false
	}
	return true
}

// FilterAndSortInsuranceRecordsByEffectiveDateV1 processes a list of insurance records by:
// 1. Filtering out records with effective dates on or after the provided effectiveDate
// 2. Sorting the remaining records in descending order by effective date
func FilterAndSortInsuranceRecordsByEffectiveDateV1(
	insuranceRecords []*data_fetching.InsuranceRecordV1,
	effectiveDate time.Time,
) []*data_fetching.InsuranceRecordV1 {
	filteredRecords := filterInsuranceRecordsV1(insuranceRecords, effectiveDate)
	SortInsuranceRecordsByEffectiveDateV1(filteredRecords)
	return filteredRecords
}

func SortInsuranceRecordsByEffectiveDateV1(insuranceRecords []*data_fetching.InsuranceRecordV1) {
	sort.Slice(insuranceRecords, func(i, j int) bool {
		if insuranceRecords[i].EffectiveDate.AsTime() == insuranceRecords[j].EffectiveDate.AsTime() {
			if insuranceRecords[i].CancelEffectiveDate != nil && insuranceRecords[j].CancelEffectiveDate != nil {
				return insuranceRecords[i].CancelEffectiveDate.AsTime().After(insuranceRecords[j].CancelEffectiveDate.AsTime())
			}
		}
		return insuranceRecords[i].EffectiveDate.AsTime().After(insuranceRecords[j].EffectiveDate.AsTime())
	})
}

func filterInsuranceRecordsV1(
	insuranceRecords []*data_fetching.InsuranceRecordV1,
	effectiveDate time.Time,
) []*data_fetching.InsuranceRecordV1 {
	return slice_utils.Filter(insuranceRecords, func(record *data_fetching.InsuranceRecordV1) bool {
		return record.EffectiveDate.AsTime().Before(effectiveDate)
	})
}
