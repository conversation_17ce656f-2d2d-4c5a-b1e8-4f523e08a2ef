package lni

import (
	"math"
	"sort"
	"strings"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

// targetCarriers defines the list of current carriers that should be filtered out
// when calculating prior carrier years retained.
var targetCarriers = []string{
	"SIRIUSPOINT AMERICA INSURANCE COMPANY",
	"FALLS LAKE NATIONAL INSURANCE COMPANY",
	"MS TRANSVERSE INSURANCE COMPANY",
	"MS TRANSVERSE SPECIALTY INSURANCE COMPANY",
}

// GetPriorCarrierYearsRetainedV1 calculates the years retained with the most recent prior carrier
// (excluding target carriers) based on BIPD insurance history.
func GetPriorCarrierYearsRetainedV1(effectiveDate time.Time, records []*data_fetching.InsuranceRecordV1) (int32, error) {
	if len(records) == 0 {
		return 0, nil
	}

	// Filter out target carriers and keep only records with valid dates
	priorCarrierRecords := filterNonTargetCarriers(records)
	if len(priorCarrierRecords) == 0 {
		return 0, nil
	}

	// Find the most recent prior carrier
	mostRecentCarrier := findMostRecentPriorCarrier(priorCarrierRecords)
	if mostRecentCarrier == nil {
		return 0, nil
	}

	// Calculate years retained
	years, err := calculateYearsRetained(mostRecentCarrier, effectiveDate)
	if err != nil {
		return 0, errors.Wrap(err, "failed to calculate years retained")
	}

	return years, nil
}

// filterNonTargetCarriers filters out target carriers and returns only valid prior carrier records.
func filterNonTargetCarriers(records []*data_fetching.InsuranceRecordV1) []*data_fetching.InsuranceRecordV1 {
	var filtered []*data_fetching.InsuranceRecordV1
	for _, record := range records {
		if record == nil {
			continue
		}

		// Skip if it's a target carrier
		if isTargetCarrier(record.InsuranceCompanyName) {
			continue
		}

		// Skip if doesn't have valid dates
		if !hasValidDates(record) {
			continue
		}

		filtered = append(filtered, record)
	}

	return filtered
}

// findMostRecentPriorCarrier finds the carrier with the latest end date.
// Uses deterministic sorting to ensure consistent results.
func findMostRecentPriorCarrier(records []*data_fetching.InsuranceRecordV1) *data_fetching.InsuranceRecordV1 {
	if len(records) == 0 {
		return nil
	}

	// Use stable sort for deterministic results
	sort.SliceStable(records, func(i, j int) bool {
		return compareRecordsByRecency(records[i], records[j])
	})

	return records[0]
}

// compareRecordsByRecency returns true if record i is more recent than record j.
// Sorting criteria (in order of priority):
// 1. Records without cancel dates are most recent
// 2. Records with later cancel dates are more recent
// 3. Records with later start dates are more recent (tie-breaker)
// 4. Alphabetically earlier company names are preferred (determinism)
func compareRecordsByRecency(recordI, recordJ *data_fetching.InsuranceRecordV1) bool {
	endDateI := getEndDate(recordI)
	endDateJ := getEndDate(recordJ)

	// 1. Records without cancel dates are considered most recent
	if endDateI == nil && endDateJ != nil {
		return true
	}
	if endDateI != nil && endDateJ == nil {
		return false
	}

	// 2. Compare by end date if both have cancel dates
	if endDateI != nil && endDateJ != nil {
		if !endDateI.Equal(*endDateJ) {
			return endDateI.After(*endDateJ)
		}
	}

	// 3. Tie-breaker: compare by start date
	startDateI := getStartDate(recordI)
	startDateJ := getStartDate(recordJ)
	if startDateI != nil && startDateJ != nil && !startDateI.Equal(*startDateJ) {
		return startDateI.After(*startDateJ)
	}

	// 4. Final tie-breaker: company name (for determinism)
	return strings.Compare(recordI.InsuranceCompanyName, recordJ.InsuranceCompanyName) < 0
}

// calculateYearsRetained calculates the number of years from start date to end date,
// using effective date if end date is later than effective date or if no end date is available.
func calculateYearsRetained(record *data_fetching.InsuranceRecordV1, effectiveDate time.Time) (int32, error) {
	if record == nil {
		return 0, errors.New("record is nil")
	}

	startDate := getStartDate(record)
	if startDate == nil {
		return 0, errors.New("missing start date")
	}

	endDate := getEndDate(record)
	var finalEndDate time.Time

	if endDate == nil {
		// No cancel date - use effective date as end date
		finalEndDate = effectiveDate
	} else {
		// Use effective date if end date is later than effective date
		finalEndDate = *endDate
		if endDate.After(effectiveDate) {
			finalEndDate = effectiveDate
		}
	}

	// Calculate years and round down
	duration := finalEndDate.Sub(*startDate)
	years := time_utils.Years(duration)

	// Ensure we never return negative years
	if years < 0 {
		return 0, nil
	}

	return int32(math.Floor(years)), nil
}

// isTargetCarrier checks if the carrier name matches any of the target carriers (case-insensitive).
func isTargetCarrier(carrierName string) bool {
	normalizedCarrierName := strings.TrimSpace(strings.ToUpper(carrierName))

	for _, target := range targetCarriers {
		if strings.ToUpper(target) == normalizedCarrierName {
			return true
		}
	}

	return false
}

// getEndDate extracts the end date from an insurance record, preferring cancelEffectiveDate.
// If no cancelEffectiveDate is available, returns nil (caller should handle this case).
func getEndDate(record *data_fetching.InsuranceRecordV1) *time.Time {
	if record == nil {
		return nil
	}

	// Prefer cancelEffectiveDate if available
	if record.CancelEffectiveDate != nil {
		endTime := record.CancelEffectiveDate.AsTime()
		return &endTime
	}

	// If no cancel date, return nil - caller should handle this case
	return nil
}

// getStartDate extracts the start date from an insurance record.
func getStartDate(record *data_fetching.InsuranceRecordV1) *time.Time {
	if record == nil || record.EffectiveDate == nil {
		return nil
	}

	startTime := record.EffectiveDate.AsTime()
	return &startTime
}

// hasValidDates checks if the record has a start date.
// Records without cancel dates are still considered valid.
func hasValidDates(record *data_fetching.InsuranceRecordV1) bool {
	return getStartDate(record) != nil
}
