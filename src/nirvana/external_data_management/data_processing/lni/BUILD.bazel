load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "lni",
    srcs = [
        "get_prior_carrier_years_retained_v1.go",
        "insurance.go",
        "years_in_business_from_authority_history_v1.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/lni",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/data_fetching",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_volatiletech_null_v8//:null",
    ],
)

go_test(
    name = "lni_test",
    srcs = ["get_prior_carrier_years_retained_v1_test.go"],
    embed = [":lni"],
    deps = [
        "//nirvana/external_data_management/data_fetching",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
