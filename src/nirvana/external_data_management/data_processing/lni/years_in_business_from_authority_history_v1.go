package lni

import (
	"context"
	"sort"
	"time"

	"github.com/volatiletech/null/v8"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

const allowedWindowInDays = 30

// YearsInBusinessFromAuthorityHistoryV1 receives a slice of authority history records and effective date, it
// returns the latest window of operation in years. If the fleet is active, then it calculates the time difference
// considering the request effective date instead of the most recent record FinalActionServedDate.
func YearsInBusinessFromAuthorityHistoryV1(
	_ context.Context,
	records []*data_fetching.AuthorityHistoryRecordV1,
	effectiveDate time.Time,
) (null.Float64, error) {
	if len(records) == 0 {
		return null.Float64{}, errors.New("empty authority history records")
	}

	// We are sorting the records to be sure that we retrieve the windows of operation in order
	sort.Slice(records, func(i, j int) bool {
		iOriginalServedDate := records[i].OriginalActionServedDate.AsTime()
		jOriginalServedDate := records[j].OriginalActionServedDate.AsTime()
		return iOriginalServedDate.After(jOriginalServedDate)
	})

	var startDate *time.Time
	var endDate *time.Time
	isActive := false
	var combinedErr error
	for _, record := range records {
		if record.OriginalActionServedDate == nil {
			continue
		}
		recordStartDate := record.OriginalActionServedDate.AsTime()
		recordEndDate := record.FinalActionServedDate.AsTime()
		// If the FinalActionServedDate is null, we check for the FinalActionDecisionDate
		// This is due to some edge cases where the FinalActionServedDate is null
		// but the FinalActionDecisionDate is not
		if time_utils.IsZero(recordEndDate) {
			recordEndDate = record.FinalActionDecisionDate.AsTime()
		}

		if startDate == nil {
			startDate = &recordStartDate
		}

		if recordStartDate.Before(*startDate) {
			// Earlier record is still active
			if time_utils.IsZero(recordEndDate) {
				startDate = &recordStartDate
			}
			// We allow an arbitrary days as a window in operations when calculating time in business
			if !time_utils.IsZero(recordEndDate) && recordEndDate.AddDate(
				0,
				0,
				allowedWindowInDays,
			).After(*startDate) {
				startDate = &recordStartDate
			}
		}
		if time_utils.IsZero(recordEndDate) {
			isActive = true
			continue
		}
		if recordStartDate.After(recordEndDate) {
			newErr := errors.Newf(
				"authority record has invalid dates. Original action date %s is after final action served date %s.",
				recordStartDate,
				recordEndDate,
			)
			combinedErr = errors.CombineErrors(combinedErr, newErr)
		}
		if endDate == nil || recordEndDate.After(*endDate) {
			endDate = &recordEndDate
		}
	}

	if startDate == nil {
		return null.Float64{}, errors.New("no valid start date found for calculating YIB")
	}

	years := time_utils.Years(effectiveDate.Sub(*startDate))
	if !isActive && endDate != nil {
		years = time_utils.Years(endDate.Sub(*startDate))
	}
	return null.NewFloat64(years, true), nil
}
