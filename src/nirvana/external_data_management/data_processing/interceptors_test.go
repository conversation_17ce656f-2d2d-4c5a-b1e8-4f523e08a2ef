package data_processing

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func Test_YearsInBusinessFromAuthorityHistoryRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	now := time.Now()
	mockCC := &grpc.ClientConn{}
	mockReply := &YearsInBusinessFromAuthorityHistoryV1{}

	mockAuthHistory := &data_fetching.GrantedAuthorityHistoryV1{
		Records: []*data_fetching.AuthorityHistoryRecordV1{
			{
				Id:                       uuid.New().String(),
				DockerNumber:             "MC1234567",
				DotNumber:                1234567,
				AuthorityType:            "BROKER",
				OriginalAction:           "GRANTED",
				OriginalActionServedDate: timestamppb.New(now),
				CreatedAt:                timestamppb.New(now),
				VersionId:                1,
			},
		},
	}

	mockFetcherRequest := &data_fetching.GrantedAuthorityHistoryRequestV1{
		DotNumber: 1234567,
	}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		authorityHistoryRecords := []*data_fetching.AuthorityHistoryRecordV1{
			{
				DotNumber:     1234567,
				AuthorityType: "EXISTING",
			},
		}

		mockRequest := &YearsInBusinessFromAuthorityHistoryRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_{
				FetchedData: &YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData{
					AuthorityHistoryRecords: authorityHistoryRecords,
				},
			},
		}

		interceptor := newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*YearsInBusinessFromAuthorityHistoryRequestV1)
			require.True(t, ok)
			require.Equal(t, authorityHistoryRecords, request.GetFetchedData().AuthorityHistoryRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("with empty records in fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		authorityHistoryRecords := []*data_fetching.AuthorityHistoryRecordV1{}

		mockRequest := &YearsInBusinessFromAuthorityHistoryRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_{
				FetchedData: &YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData{
					AuthorityHistoryRecords: authorityHistoryRecords,
				},
			},
		}

		interceptor := newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*YearsInBusinessFromAuthorityHistoryRequestV1)
			require.True(t, ok)
			require.Equal(t, authorityHistoryRecords, request.GetFetchedData().AuthorityHistoryRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		interceptor := newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(mockFetcherClient)

		mockRequest := &YearsInBusinessFromAuthorityHistoryRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockFetcherClient.EXPECT().
			GetGrantedAuthorityHistoryV1(
				ctx,
				mockFetcherRequest,
			).
			Return(mockAuthHistory, nil)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*YearsInBusinessFromAuthorityHistoryRequestV1)
			require.True(t, ok)
			require.Equal(t, mockAuthHistory.Records, request.GetFetchedData().AuthorityHistoryRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher client: should leave fetched data nil", func(t *testing.T) {
		interceptor := newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(nil)

		mockRequest := &YearsInBusinessFromAuthorityHistoryRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*YearsInBusinessFromAuthorityHistoryRequestV1)
			require.True(t, ok)
			require.Nil(t, request.GetFetchedData())

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &YearsInBusinessFromAuthorityHistoryRequestV1{
			FetcherSpec: &YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest{
				FetcherRequest: nil,
			},
		}

		interceptor := newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Equal(t, "fetcher request is nil", err.Error())
	})

	t.Run("without fetched data and with error fetching data: should return error", func(t *testing.T) {
		unexpectedFetchingError := errors.New("unexpected fetching error")
		mockFetcherClient.EXPECT().
			GetGrantedAuthorityHistoryV1(
				ctx,
				mockFetcherRequest,
			).
			Return(nil, unexpectedFetchingError)

		interceptor := newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(mockFetcherClient)

		mockRequest := &YearsInBusinessFromAuthorityHistoryRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedFetchingError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(nil)

		type differentRequest struct {
			DotNumber int
		}

		mockRequest := &differentRequest{
			DotNumber: 1234567,
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_FleetMovingViolationCountRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	now := time.Now()
	mockCC := &grpc.ClientConn{}
	mockReply := &FleetMovingViolationCountV1{}

	mockMVRReport := &data_fetching.MVRReportV1{
		ReportID: uuid.New().String(),
		Violations: []*data_fetching.MVRViolationV1{
			{
				AssignedViolationCode: "1",
				ViolationDate:         timestamppb.New(now.Add(-5 * time_utils.Day)),
			},
			{
				AssignedViolationCode: "1",
				ViolationDate:         timestamppb.New(now.Add(-5 * time_utils.Day)),
			},
		},
	}

	mockFetcherRequest := &data_fetching.MVRReportRequestV1{
		DlNumber: "dl1",
		UsState:  "OH",
	}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		mockRequest := &FleetMovingViolationCountRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &FleetMovingViolationCountRequestV1_FetchedData_{
				FetchedData: &FleetMovingViolationCountRequestV1_FetchedData{
					Report: mockMVRReport,
				},
			},
		}

		interceptor := newFleetMovingViolationCountRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*FleetMovingViolationCountRequestV1)
			require.True(t, ok)
			require.Equal(t, mockMVRReport, request.GetFetchedData().Report)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockRequest := &FleetMovingViolationCountRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &FleetMovingViolationCountRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockFetcherClient.EXPECT().
			GetMVRReportV1(gomock.Any(), mockFetcherRequest).
			Return(mockMVRReport, nil)

		interceptor := newFleetMovingViolationCountRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*FleetMovingViolationCountRequestV1)
			require.True(t, ok)
			require.Equal(t, mockMVRReport, request.GetFetchedData().Report)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &FleetMovingViolationCountRequestV1{
			FetcherSpec: &FleetMovingViolationCountRequestV1_FetcherRequest{
				FetcherRequest: nil,
			},
		}

		interceptor := newFleetMovingViolationCountRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Equal(t, "fetcher request is nil", err.Error())
	})

	t.Run("without fetched data and with error fetching data: should return error", func(t *testing.T) {
		mockRequest := &FleetMovingViolationCountRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &FleetMovingViolationCountRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		unexpectedFetchingError := errors.New("unexpected error")
		mockFetcherClient.EXPECT().
			GetMVRReportV1(gomock.Any(), mockFetcherRequest).
			Return(nil, unexpectedFetchingError)

		interceptor := newFleetMovingViolationCountRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedFetchingError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newFleetMovingViolationCountRequestV1Interceptor(nil)

		type differentRequest struct {
			DotNumber int
		}

		mockRequest := &differentRequest{
			DotNumber: 1234567,
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_ContinuousCoverageYearsRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	now := time.Now()
	mockCC := &grpc.ClientConn{}
	mockReply := &ContinuousCoverageYearsV1{}

	dotNumber := int64(1234567)

	mockActiveInsurance := &data_fetching.BIPDActiveOrPendingInsuranceV1{
		Records: []*data_fetching.InsuranceRecordV1{
			{
				Id:                  uuid.New().String(),
				DotNumber:           dotNumber,
				EffectiveDate:       timestamppb.New(now),
				CancelEffectiveDate: timestamppb.New(now.Add(30 * time_utils.Day)),
			},
		},
	}

	mockInsuranceHistory := &data_fetching.BIPDInsuranceHistoryV1{
		Records: []*data_fetching.InsuranceRecordV1{
			{
				Id:                  uuid.New().String(),
				DotNumber:           dotNumber,
				EffectiveDate:       timestamppb.New(now.Add(-60 * time_utils.Day)),
				CancelEffectiveDate: timestamppb.New(now.Add(-30 * time_utils.Day)),
			},
		},
	}

	mockFetcherRequests := &ContinuousCoverageYearsRequestV1_FetcherRequests{
		ActiveOrPendingInsuranceRequest: &data_fetching.BIPDActiveOrPendingInsuranceRequestV1{
			DotNumber: dotNumber,
		},
		InsuranceHistoryRequest: &data_fetching.BIPDInsuranceHistoryRequestV1{
			DotNumber: dotNumber,
		},
	}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		existingRecords := []*data_fetching.InsuranceRecordV1{
			{
				DotNumber:     1234567,
				EffectiveDate: timestamppb.New(now),
			},
		}

		mockRequest := &ContinuousCoverageYearsRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &ContinuousCoverageYearsRequestV1_FetchedData_{
				FetchedData: &ContinuousCoverageYearsRequestV1_FetchedData{
					InsuranceRecords: existingRecords,
				},
			},
		}

		interceptor := newContinuousCoverageYearsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*ContinuousCoverageYearsRequestV1)
			require.True(t, ok)
			require.Equal(t, existingRecords, request.GetFetchedData().InsuranceRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockRequest := &ContinuousCoverageYearsRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &ContinuousCoverageYearsRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(mockActiveInsurance, nil)

		mockFetcherClient.EXPECT().
			GetBIPDInsuranceHistoryV1(
				ctx,
				mockFetcherRequests.InsuranceHistoryRequest,
			).
			Return(mockInsuranceHistory, nil)

		interceptor := newContinuousCoverageYearsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*ContinuousCoverageYearsRequestV1)
			require.True(t, ok)

			// Verify that records are combined and sorted
			allRecords := append(mockActiveInsurance.Records, mockInsuranceHistory.Records...)
			require.Equal(t, allRecords, request.GetFetchedData().InsuranceRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher client: should leave fetched data nil", func(t *testing.T) {
		interceptor := newContinuousCoverageYearsRequestV1Interceptor(nil)

		mockRequest := &ContinuousCoverageYearsRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &ContinuousCoverageYearsRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*ContinuousCoverageYearsRequestV1)
			require.True(t, ok)
			require.Nil(t, request.GetFetchedData())

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &ContinuousCoverageYearsRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &ContinuousCoverageYearsRequestV1_FetcherRequests_{
				FetcherRequests: nil,
			},
		}

		interceptor := newContinuousCoverageYearsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Contains(t, err.Error(), "fetcher requests are nil")
	})

	t.Run("without fetched data and with error fetching active insurance: should return error", func(t *testing.T) {
		mockRequest := &ContinuousCoverageYearsRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &ContinuousCoverageYearsRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		unexpectedError := errors.New("unexpected error")
		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(nil, unexpectedError)

		interceptor := newContinuousCoverageYearsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedError)
	})

	t.Run("without fetched data and with error fetching insurance history: should return error", func(t *testing.T) {
		mockRequest := &ContinuousCoverageYearsRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &ContinuousCoverageYearsRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		// First call to get active insurance succeeds
		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(mockActiveInsurance, nil)

		// Second call to get insurance history fails
		unexpectedError := errors.New("unexpected error")
		mockFetcherClient.EXPECT().
			GetBIPDInsuranceHistoryV1(
				ctx,
				mockFetcherRequests.InsuranceHistoryRequest,
			).
			Return(nil, unexpectedError)

		interceptor := newContinuousCoverageYearsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newContinuousCoverageYearsRequestV1Interceptor(nil)

		type differentRequest struct {
			DotNumber int
		}

		mockRequest := &differentRequest{
			DotNumber: 1234567,
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_IsoFieldsFromNhtsaFieldsRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	mockCC := &grpc.ClientConn{}
	mockReply := &IsoFieldsFromNhtsaFieldsRequestV1{}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	mockFetcherRequest := &data_fetching.VINDetailsRequestV1{
		Vin: "1HGCM82633A123456",
	}

	mockVinDetails := &data_fetching.VINDetailsV1{
		VehicleType: data_fetching.VehicleTypeV1_VehicleTypeV1_Trailer,
		BodyClass:   "TRUCK",
		WeightClass: data_fetching.WeightClassV1_WeightClassV1_B,
	}

	mockFetchedData := &IsoFieldsFromNhtsaFieldsRequestV1_FetchedData{
		VehicleType:        data_fetching.VehicleTypeV1_VehicleTypeV1_Truck,
		VehicleBodyClass:   "TRUCK",
		VehicleWeightClass: data_fetching.WeightClassV1_WeightClassV1_A,
	}

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		mockRequest := &IsoFieldsFromNhtsaFieldsRequestV1{
			FetcherSpec: &IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_{
				FetchedData: mockFetchedData,
			},
		}

		interceptor := newIsoFieldsFromNhtsaFieldsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*IsoFieldsFromNhtsaFieldsRequestV1)
			require.True(t, ok)
			require.Equal(t, mockFetchedData, request.GetFetchedData())

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockFetcherClient.EXPECT().
			GetVINDetailsV1(gomock.Any(), mockFetcherRequest).
			Return(mockVinDetails, nil)

		mockRequest := &IsoFieldsFromNhtsaFieldsRequestV1{
			FetcherSpec: &IsoFieldsFromNhtsaFieldsRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		interceptor := newIsoFieldsFromNhtsaFieldsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*IsoFieldsFromNhtsaFieldsRequestV1)
			require.True(t, ok)
			fetchedData := request.GetFetchedData()
			require.Equal(t, mockVinDetails.VehicleType, fetchedData.VehicleType)
			require.Equal(t, mockVinDetails.BodyClass, fetchedData.VehicleBodyClass)
			require.Equal(t, mockVinDetails.WeightClass, fetchedData.VehicleWeightClass)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &IsoFieldsFromNhtsaFieldsRequestV1{
			FetcherSpec: &IsoFieldsFromNhtsaFieldsRequestV1_FetcherRequest{
				FetcherRequest: nil,
			},
		}

		interceptor := newIsoFieldsFromNhtsaFieldsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Equal(t, "fetcher request is nil", err.Error())
	})

	t.Run("without fetched data and with error fetching data: should return error", func(t *testing.T) {
		unexpectedFetchingError := errors.New("unexpected fetching error")
		mockFetcherClient.EXPECT().
			GetVINDetailsV1(
				ctx,
				mockFetcherRequest,
			).
			Return(nil, unexpectedFetchingError)

		interceptor := newIsoFieldsFromNhtsaFieldsRequestV1Interceptor(mockFetcherClient)

		mockRequest := &IsoFieldsFromNhtsaFieldsRequestV1{
			FetcherSpec: &IsoFieldsFromNhtsaFieldsRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedFetchingError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newIsoFieldsFromNhtsaFieldsRequestV1Interceptor(nil)

		type differentRequest struct {
			Vin string
		}

		mockRequest := &differentRequest{
			Vin: "1HGCM82633A123456",
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_NFInspectionCountRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	mockCC := &grpc.ClientConn{}
	mockReply := &NFInspectionCountRequestV1{}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	mockFetcherRequest := &data_fetching.FMCSAInspectionRecordsRequestV1{
		DotNumber:     1234567,
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	mockFMCSAInspectionRecords := &data_fetching.FMCSAInspectionRecordsV1{
		Records: []*data_fetching.FMCSAInspectionRecordV1{
			{
				RowID:                "dd244277-c3c6-5ad6-9004-7ac48900df64",
				PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
				InspectionID:         71284418,
				DotNumber:            1460168,
				ReportNumber:         "000TT6T92T",
				ReportState:          "IA",
				InspectionDate:       timestamppb.New(time.Date(2020, 11, 16, 0, 0, 0, 0, time.UTC)),
				InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_DriverOnly,
				TimeWeight:           1,
				DriverOOSTotal:       0,
				VehicleOOSTotal:      0,
				TotalOOSViolations:   0,
				HazmatOOSTotal:       0,
				HazmatViolationsSent: 0,
				HazmatPlacardReq:     false,
				BasicCategories: map[string]bool{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
				},
				BasicViolations: map[string]int32{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               1,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
				},
				TotalBasicViols: 1,
				PublicVINs:      []string{"3AKJGLDR6HSHP2572", "1GR1P0623MT231011"},
				Vins:            []string{"3AKJGLDR6HSHP2572", "1GR1P0623MT231011"},
				Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
					{
						Vin:     "3AKJGLDR6HSHP2572",
						Company: "10417",
						Make:    "FRHT",
						Model:   "11",
					},
					{
						Vin:     "1GR1P0623MT231011",
						Company: "417",
						Make:    "GDAN",
						Model:   "9",
					},
				},
			},
			{
				RowID:                "f4ae5018-75fb-5f5d-b655-e2552e5c1b42",
				PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
				InspectionID:         71080922,
				DotNumber:            1460168,
				ReportNumber:         "**********",
				ReportState:          "KY",
				InspectionDate:       timestamppb.New(time.Date(2020, 10, 21, 0, 0, 0, 0, time.UTC)),
				InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Full,
				TimeWeight:           1,
				DriverOOSTotal:       0,
				VehicleOOSTotal:      0,
				TotalOOSViolations:   0,
				HazmatOOSTotal:       0,
				HazmatViolationsSent: 0,
				HazmatPlacardReq:     false,
				BasicCategories: map[string]bool{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
				},
				BasicViolations: map[string]int32{
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
					data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
				},
				TotalBasicViols: 0,
				PublicVINs:      []string{"4V4NC9TJ97N447739", "1GRAP062XKT141397"},
				Vins:            []string{"4V4NC9TJ97N447739", "1GRAP062XKT141397"},
				Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
					{
						Vin:     "4V4NC9TJ97N447739",
						Company: "0909",
						Make:    "VOLV",
						Model:   "11",
					},
					{
						Vin:     "1GRAP062XKT141397",
						Company: "789",
						Make:    "GDAN",
						Model:   "9",
					},
				},
			},
		},
	}

	mockFetchedData := &NFInspectionCountRequestV1_FetchedData{
		InspectionRecords: &data_fetching.FMCSAInspectionRecordsV1{
			Records: []*data_fetching.FMCSAInspectionRecordV1{
				{
					RowID:                "b8c23f91-a4d5-5e76-8c14-9d32ef4b8a15",
					PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
					InspectionID:         71284418,
					DotNumber:            1460168,
					ReportNumber:         "000TT6T92T",
					ReportState:          "IA",
					InspectionDate:       timestamppb.New(time.Date(2020, 11, 16, 0, 0, 0, 0, time.UTC)),
					InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_DriverOnly,
					TimeWeight:           1,
					DriverOOSTotal:       0,
					VehicleOOSTotal:      0,
					TotalOOSViolations:   0,
					HazmatOOSTotal:       0,
					HazmatViolationsSent: 0,
					HazmatPlacardReq:     false,
					BasicCategories: map[string]bool{
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          false,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
					},
					BasicViolations: map[string]int32{
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               1,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
					},
					TotalBasicViols: 1,
					PublicVINs:      []string{"2HSCEAPR8YC057391", "1XKWDB0X57J211458"},
					Vins:            []string{"2HSCEAPR8YC057391", "1XKWDB0X57J211458"},
					Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
						{
							Vin:     "2HSCEAPR8YC057391",
							Company: "10417",
							Make:    "INTL",
							Model:   "11",
						},
						{
							Vin:     "1XKWDB0X57J211458",
							Company: "417",
							Make:    "KW",
							Model:   "9",
						},
					},
				},
				{
					RowID:                "c7d12e84-b3f9-5a12-9e45-8f67cd3b4e29",
					PublishedDate:        timestamppb.New(time.Date(2022, 9, 30, 0, 0, 0, 0, time.UTC)),
					InspectionID:         71080922,
					DotNumber:            1460168,
					ReportNumber:         "**********",
					ReportState:          "KY",
					InspectionDate:       timestamppb.New(time.Date(2020, 10, 21, 0, 0, 0, 0, time.UTC)),
					InspectionLevel:      data_fetching.FMCSAInspectionLevelV1_FMCSAInspectionLevelV1_Full,
					TimeWeight:           1,
					DriverOOSTotal:       0,
					VehicleOOSTotal:      0,
					TotalOOSViolations:   0,
					HazmatOOSTotal:       0,
					HazmatViolationsSent: 0,
					HazmatPlacardReq:     false,
					BasicCategories: map[string]bool{
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               true,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               true,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          true,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): true,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                false,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               true,
					},
					BasicViolations: map[string]int32{
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving.String():               0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HosCompliance.String():               0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance.String():          0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_ControlledSubstancesAlcohol.String(): 0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_HmCompliance.String():                0,
						data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_DriverFitness.String():               0,
					},
					TotalBasicViols: 0,
					PublicVINs:      []string{"1FUJGLDR7CSBU8461", "2HSCNAPR7KC269004"},
					Vins:            []string{"1FUJGLDR7CSBU8461", "2HSCNAPR7KC269004"},
					Vehicles: []*data_fetching.FMCSAInspectionVehicleV1{
						{
							Vin:     "1FUJGLDR7CSBU8461",
							Company: "0909",
							Make:    "FRHT",
							Model:   "11",
						},
						{
							Vin:     "2HSCNAPR7KC269004",
							Company: "789",
							Make:    "INTL",
							Model:   "9",
						},
					},
				},
			},
		},
	}

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		mockRequest := &NFInspectionCountRequestV1{
			FetcherSpec: &NFInspectionCountRequestV1_FetchedData_{
				FetchedData: mockFetchedData,
			},
		}

		interceptor := newNFInspectionCountRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*NFInspectionCountRequestV1)
			require.True(t, ok)
			require.Equal(t, mockFetchedData, request.GetFetchedData())

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockFetcherClient.EXPECT().
			GetFMCSAInspectionRecordsV1(gomock.Any(), mockFetcherRequest).
			Return(mockFMCSAInspectionRecords, nil)

		mockRequest := &NFInspectionCountRequestV1{
			FetcherSpec: &NFInspectionCountRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		interceptor := newNFInspectionCountRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*NFInspectionCountRequestV1)
			require.True(t, ok)
			fetchedData := request.GetFetchedData()
			require.Equal(t, mockFMCSAInspectionRecords, fetchedData.InspectionRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &NFInspectionCountRequestV1{
			FetcherSpec: nil,
		}

		interceptor := newNFInspectionCountRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Equal(t, "fetcher request is nil", err.Error())
	})

	t.Run("without fetched data and with error fetching data: should return error", func(t *testing.T) {
		unexpectedFetchingError := errors.New("unexpected fetching error")
		mockFetcherClient.EXPECT().
			GetFMCSAInspectionRecordsV1(
				ctx,
				mockFetcherRequest,
			).
			Return(nil, unexpectedFetchingError)

		interceptor := newNFInspectionCountRequestV1Interceptor(mockFetcherClient)

		mockRequest := &NFInspectionCountRequestV1{
			FetcherSpec: &NFInspectionCountRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedFetchingError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newNFInspectionCountRequestV1Interceptor(nil)

		type differentRequest struct{}

		mockRequest := &differentRequest{}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_NFViolationsMetadataRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	mockCC := &grpc.ClientConn{}
	mockReply := &NFViolationsMetadataRequestV1{}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	mockFetcherRequest := &data_fetching.FMCSAViolationRecordsRequestV1{
		DotNumber:     1234567,
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	mockFMCSAViolationRecords := &data_fetching.FMCSAViolationRecordsV1{
		Records: []*data_fetching.FMCSAViolationRecordV1{
			{
				RowId:               "31d0ef9b-9ef1-54b2-b98c-6cc0ba7ecc8c",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        76246428,
				InspectionDate:      timestamppb.New(time.Date(2022, 8, 4, 0, 0, 0, 0, time.UTC)),
				DotNumber:           493219,
				Code:                "3939",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      2,
				TotalSeverityWeight: 2,
				TimeWeight:          1,
				ViolationId:         pointer_utils.ToPointer(int32(247542810)),
				CountyCode:          pointer_utils.ToPointer("115"),
				CountyCodeState:     pointer_utils.ToPointer("MI"),
				Vins:                nil,
			},
			{
				RowId:               "be8fabda-c4ba-5295-bced-666cb99dc624",
				PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
				InspectionId:        76246428,
				InspectionDate:      timestamppb.New(time.Date(2022, 8, 4, 0, 0, 0, 0, time.UTC)),
				DotNumber:           493219,
				Code:                "39216",
				Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving,
				OosIndicator:        false,
				OosWeight:           0,
				SeverityWeight:      7,
				TotalSeverityWeight: 7,
				TimeWeight:          1,
				ViolationId:         pointer_utils.ToPointer(int32(247542809)),
				CountyCode:          pointer_utils.ToPointer("115"),
				CountyCodeState:     pointer_utils.ToPointer("MI"),
				Vins:                nil,
			},
		},
	}

	mockFetchedData := &NFViolationsMetadataRequestV1_FetchedData{
		ViolationRecords: &data_fetching.FMCSAViolationRecordsV1{
			Records: []*data_fetching.FMCSAViolationRecordV1{
				{
					RowId:               "31d0ef9b-9ef1-54b2-b98c-6cc0ba7ecc8c",
					PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
					InspectionId:        76246428,
					InspectionDate:      timestamppb.New(time.Date(2022, 8, 4, 0, 0, 0, 0, time.UTC)),
					DotNumber:           493219,
					Code:                "3939",
					Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
					OosIndicator:        true,
					OosWeight:           2,
					SeverityWeight:      5,
					TotalSeverityWeight: 4,
					TimeWeight:          3,
					ViolationId:         pointer_utils.ToPointer(int32(247542811)),
					CountyCode:          pointer_utils.ToPointer("116"),
					CountyCodeState:     pointer_utils.ToPointer("CA"),
					Vins:                nil,
				},
				{
					RowId:               "be8fabda-c4ba-5295-bced-666cb99dc624",
					PublishedDate:       timestamppb.New(time.Date(2023, 8, 25, 0, 0, 0, 0, time.UTC)),
					InspectionId:        76246429,
					InspectionDate:      timestamppb.New(time.Date(2022, 8, 4, 0, 0, 0, 0, time.UTC)),
					DotNumber:           493220,
					Code:                "39217",
					Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving,
					OosIndicator:        true,
					OosWeight:           1,
					SeverityWeight:      3,
					TotalSeverityWeight: 6,
					TimeWeight:          2,
					ViolationId:         pointer_utils.ToPointer(int32(247542812)),
					CountyCode:          pointer_utils.ToPointer("117"),
					CountyCodeState:     pointer_utils.ToPointer("NY"),
					Vins:                nil,
				},
			},
		},
	}

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		mockRequest := &NFViolationsMetadataRequestV1{
			FetcherSpec: &NFViolationsMetadataRequestV1_FetchedData_{
				FetchedData: mockFetchedData,
			},
		}

		interceptor := newNFViolationsMetadataRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*NFViolationsMetadataRequestV1)
			require.True(t, ok)
			require.Equal(t, mockFetchedData, request.GetFetchedData())

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockFetcherClient.EXPECT().
			GetFMCSAViolationRecordsV1(gomock.Any(), mockFetcherRequest).
			Return(mockFMCSAViolationRecords, nil)

		mockRequest := &NFViolationsMetadataRequestV1{
			FetcherSpec: &NFViolationsMetadataRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		interceptor := newNFViolationsMetadataRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*NFViolationsMetadataRequestV1)
			require.True(t, ok)
			fetchedData := request.GetFetchedData()
			require.Equal(t, mockFMCSAViolationRecords, fetchedData.ViolationRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &NFViolationsMetadataRequestV1{
			FetcherSpec: nil,
		}

		interceptor := newNFViolationsMetadataRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Equal(t, "fetcher request is nil", err.Error())
	})

	t.Run("without fetched data and with error fetching data: should return error", func(t *testing.T) {
		unexpectedFetchingError := errors.New("unexpected fetching error")
		mockFetcherClient.EXPECT().
			GetFMCSAViolationRecordsV1(
				ctx,
				mockFetcherRequest,
			).
			Return(nil, unexpectedFetchingError)

		interceptor := newNFViolationsMetadataRequestV1Interceptor(mockFetcherClient)

		mockRequest := &NFViolationsMetadataRequestV1{
			FetcherSpec: &NFViolationsMetadataRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedFetchingError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newNFViolationsMetadataRequestV1Interceptor(nil)

		type differentRequest struct {
			Vin string
		}

		mockRequest := &differentRequest{}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_NFCreditFeaturesRequestV2Interceptor(t *testing.T) {
	methodName := "myMethod"
	mockCC := &grpc.ClientConn{}
	mockReply := &NFCreditFeaturesRequestV2{}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	mockFetcherRequest := &data_fetching.NationalCreditFileRequestV1{
		Dob:        timestamppb.New(time.Date(1985, 3, 12, 0, 0, 0, 0, time.UTC)),
		FirstName:  "SARAH",
		LastName:   "PARKER",
		MiddleName: "JANE",
		Address: &data_fetching.AddressV1{
			Street: "742 MAPLE AVE",
			City:   "PHOENIX",
			State:  "AZ",
			Zip:    "85001",
		},
		Staleness:     0,
		ApplicationID: uuid.New().String(),
	}

	mockNationalCreditFile := &data_fetching.NationalCreditFileV1{
		NcfReport:             nil,
		TransactionDetails:    nil,
		DateOfCreditReportRun: timestamppb.New(time.Date(2022, 7, 12, 0, 0, 0, 0, time.UTC)),
	}

	mockFetchedData := &NFCreditFeaturesRequestV2_FetchedData{
		NationalCreditFile: &data_fetching.NationalCreditFileV1{
			NcfReport:             nil,
			TransactionDetails:    nil,
			DateOfCreditReportRun: timestamppb.New(time.Date(2023, 8, 14, 0, 0, 0, 0, time.UTC)),
		},
	}

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		mockRequest := &NFCreditFeaturesRequestV2{
			FetcherSpec: &NFCreditFeaturesRequestV2_FetchedData_{
				FetchedData: mockFetchedData,
			},
		}

		interceptor := newNFCreditFeaturesRequestV2Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*NFCreditFeaturesRequestV2)
			require.True(t, ok)
			require.Equal(t, mockFetchedData, request.GetFetchedData())

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockFetcherClient.EXPECT().
			GetNationalCreditFileV1(gomock.Any(), mockFetcherRequest).
			Return(mockNationalCreditFile, nil)

		mockRequest := &NFCreditFeaturesRequestV2{
			FetcherSpec: &NFCreditFeaturesRequestV2_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		interceptor := newNFCreditFeaturesRequestV2Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*NFCreditFeaturesRequestV2)
			require.True(t, ok)
			fetchedData := request.GetFetchedData()
			require.Equal(t, mockNationalCreditFile, fetchedData.NationalCreditFile)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &NFCreditFeaturesRequestV2{
			FetcherSpec: nil,
		}

		interceptor := newNFCreditFeaturesRequestV2Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Equal(t, "fetcher request is nil", err.Error())
	})

	t.Run("without fetched data and with error fetching data: should return error", func(t *testing.T) {
		unexpectedFetchingError := errors.New("unexpected fetching error")
		mockFetcherClient.EXPECT().
			GetNationalCreditFileV1(
				ctx,
				mockFetcherRequest,
			).
			Return(nil, unexpectedFetchingError)

		interceptor := newNFCreditFeaturesRequestV2Interceptor(mockFetcherClient)

		mockRequest := &NFCreditFeaturesRequestV2{
			FetcherSpec: &NFCreditFeaturesRequestV2_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedFetchingError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newNFCreditFeaturesRequestV2Interceptor(nil)

		type differentRequest struct {
			Vin string
		}

		mockRequest := &differentRequest{}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_InsuranceHistoryYearsInBusinessRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	now := time.Now()
	mockCC := &grpc.ClientConn{}
	mockReply := &InsuranceHistoryYearsInBusinessV1{}

	dotNumber := int64(1234567)

	mockActiveInsurance := &data_fetching.BIPDActiveOrPendingInsuranceV1{
		Records: []*data_fetching.InsuranceRecordV1{
			{
				Id:                  uuid.New().String(),
				DotNumber:           dotNumber,
				EffectiveDate:       timestamppb.New(now),
				CancelEffectiveDate: timestamppb.New(now.Add(30 * time_utils.Day)),
			},
		},
	}

	mockInsuranceHistory := &data_fetching.BIPDInsuranceHistoryV1{
		Records: []*data_fetching.InsuranceRecordV1{
			{
				Id:                  uuid.New().String(),
				DotNumber:           dotNumber,
				EffectiveDate:       timestamppb.New(now.Add(-60 * time_utils.Day)),
				CancelEffectiveDate: timestamppb.New(now.Add(-30 * time_utils.Day)),
			},
		},
	}

	mockFetcherRequests := &InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests{
		ActiveOrPendingInsuranceRequest: &data_fetching.BIPDActiveOrPendingInsuranceRequestV1{
			DotNumber: dotNumber,
		},
		InsuranceHistoryRequest: &data_fetching.BIPDInsuranceHistoryRequestV1{
			DotNumber: dotNumber,
		},
	}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		existingRecords := []*data_fetching.InsuranceRecordV1{
			{
				DotNumber:     1234567,
				EffectiveDate: timestamppb.New(now),
			},
		}

		mockRequest := &InsuranceHistoryYearsInBusinessRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &InsuranceHistoryYearsInBusinessRequestV1_FetchedData_{
				FetchedData: &InsuranceHistoryYearsInBusinessRequestV1_FetchedData{
					InsuranceRecords: existingRecords,
				},
			},
		}

		interceptor := newInsuranceHistoryYearsInBusinessRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*InsuranceHistoryYearsInBusinessRequestV1)
			require.True(t, ok)
			require.Equal(t, existingRecords, request.GetFetchedData().InsuranceRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockRequest := &InsuranceHistoryYearsInBusinessRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(mockActiveInsurance, nil)

		mockFetcherClient.EXPECT().
			GetBIPDInsuranceHistoryV1(
				ctx,
				mockFetcherRequests.InsuranceHistoryRequest,
			).
			Return(mockInsuranceHistory, nil)

		interceptor := newInsuranceHistoryYearsInBusinessRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*InsuranceHistoryYearsInBusinessRequestV1)
			require.True(t, ok)

			// Verify that records are combined and sorted
			allRecords := append(mockActiveInsurance.Records, mockInsuranceHistory.Records...)
			require.Equal(t, allRecords, request.GetFetchedData().InsuranceRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher client: should leave fetched data nil", func(t *testing.T) {
		mockRequest := &InsuranceHistoryYearsInBusinessRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		interceptor := newInsuranceHistoryYearsInBusinessRequestV1Interceptor(nil)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*InsuranceHistoryYearsInBusinessRequestV1)
			require.True(t, ok)
			require.Nil(t, request.GetFetchedData())

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &InsuranceHistoryYearsInBusinessRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_{
				FetcherRequests: nil,
			},
		}

		interceptor := newInsuranceHistoryYearsInBusinessRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Contains(t, err.Error(), "fetcher requests are nil")
	})

	t.Run("without fetched data and with error fetching active insurance: should return error", func(t *testing.T) {
		mockRequest := &InsuranceHistoryYearsInBusinessRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		unexpectedError := errors.New("unexpected error")
		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(nil, unexpectedError)

		interceptor := newInsuranceHistoryYearsInBusinessRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedError)
	})

	t.Run("without fetched data and with error fetching insurance history: should return error", func(t *testing.T) {
		mockRequest := &InsuranceHistoryYearsInBusinessRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		// First call to get active insurance succeeds
		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(mockActiveInsurance, nil)

		// Second call to get insurance history fails
		unexpectedError := errors.New("unexpected error")
		mockFetcherClient.EXPECT().
			GetBIPDInsuranceHistoryV1(
				ctx,
				mockFetcherRequests.InsuranceHistoryRequest,
			).
			Return(nil, unexpectedError)

		interceptor := newInsuranceHistoryYearsInBusinessRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newInsuranceHistoryYearsInBusinessRequestV1Interceptor(nil)

		type differentRequest struct {
			DotNumber int
		}

		mockRequest := &differentRequest{
			DotNumber: 1234567,
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_RetainedYearsRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	now := time.Now()
	mockCC := &grpc.ClientConn{}
	mockReply := &RetainedYearsV1{}

	dotNumber := int64(123456)

	mockPolicies := &data_fetching.GetNirvanaPoliciesResponseV1{
		Policies: []*data_fetching.NirvanaPolicyV1{
			{
				Id:              uuid.New().String(),
				PolicyNumber:    "POL-001",
				Version:         1,
				InsuredName:     "Test Insured",
				State:           "CA",
				ApplicationId:   uuid.New().String(),
				SubmissionId:    uuid.New().String(),
				ProgramType:     "Fleet",
				EffectiveDate:   timestamppb.New(now.AddDate(-1, 0, 0)),
				EffectiveDateTo: timestamppb.New(now),
				AgencyId:        uuid.New().String(),
				CreatedAt:       timestamppb.New(now.AddDate(-1, 0, 0)),
				UpdatedAt:       timestamppb.New(now),
			},
		},
	}

	mockFetcherRequest := &data_fetching.GetNirvanaPoliciesRequestV1{
		DotNumber:     dotNumber,
		EffectiveDate: timestamppb.New(now),
	}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		mockRequest := &RetainedYearsRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &RetainedYearsRequestV1_FetchedData_{
				FetchedData: &RetainedYearsRequestV1_FetchedData{
					Policies: mockPolicies,
				},
			},
		}

		interceptor := newRetainedYearsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*RetainedYearsRequestV1)
			require.True(t, ok)
			require.Equal(t, mockPolicies, request.GetFetchedData().Policies)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockRequest := &RetainedYearsRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &RetainedYearsRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockFetcherClient.EXPECT().
			GetNirvanaPoliciesV1(
				ctx,
				mockFetcherRequest,
			).
			Return(mockPolicies, nil)

		interceptor := newRetainedYearsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*RetainedYearsRequestV1)
			require.True(t, ok)
			require.Equal(t, mockPolicies, request.GetFetchedData().Policies)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &RetainedYearsRequestV1{
			FetcherSpec: &RetainedYearsRequestV1_FetcherRequest{
				FetcherRequest: nil,
			},
		}

		interceptor := newRetainedYearsRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Equal(t, "fetcher request is nil", err.Error())
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newRetainedYearsRequestV1Interceptor(nil)

		type differentRequest struct {
			DotNumber int
		}

		mockRequest := &differentRequest{
			DotNumber: 1234567,
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

func Test_FleetModifiedMVRScoreRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	now := time.Now()
	mockCC := &grpc.ClientConn{}
	mockReply := &FleetModifiedMVRScoreV1{}

	mockMVRReport := &data_fetching.MVRReportV1{
		ReportID: uuid.New().String(),
		Violations: []*data_fetching.MVRViolationV1{
			{
				AssignedViolationCode: "1",
				ViolationDate:         timestamppb.New(now.Add(-5 * time_utils.Day)),
			},
			{
				AssignedViolationCode: "1",
				ViolationDate:         timestamppb.New(now.Add(-5 * time_utils.Day)),
			},
		},
	}

	mockFetcherRequest := &data_fetching.MVRReportRequestV1{
		DlNumber: "dl1",
		UsState:  "OH",
	}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		mockRequest := &FleetModifiedMVRScoreRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &FleetModifiedMVRScoreRequestV1_FetchedData_{
				FetchedData: &FleetModifiedMVRScoreRequestV1_FetchedData{
					Report: mockMVRReport,
				},
			},
		}

		interceptor := newFleetModifiedMVRScoreRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*FleetModifiedMVRScoreRequestV1)
			require.True(t, ok)
			require.Equal(t, mockMVRReport, request.GetFetchedData().Report)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		mockRequest := &FleetModifiedMVRScoreRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &FleetModifiedMVRScoreRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		mockFetcherClient.EXPECT().
			GetMVRReportV1(gomock.Any(), mockFetcherRequest).
			Return(mockMVRReport, nil)

		interceptor := newFleetModifiedMVRScoreRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*FleetModifiedMVRScoreRequestV1)
			require.True(t, ok)
			require.Equal(t, mockMVRReport, request.GetFetchedData().Report)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &FleetModifiedMVRScoreRequestV1{
			FetcherSpec: &FleetModifiedMVRScoreRequestV1_FetcherRequest{
				FetcherRequest: nil,
			},
		}

		interceptor := newFleetModifiedMVRScoreRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Equal(t, "fetcher request is nil", err.Error())
	})

	t.Run("without fetched data and with error fetching data: should return error", func(t *testing.T) {
		mockRequest := &FleetModifiedMVRScoreRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &FleetModifiedMVRScoreRequestV1_FetcherRequest{
				FetcherRequest: mockFetcherRequest,
			},
		}

		unexpectedFetchingError := errors.New("unexpected error")
		mockFetcherClient.EXPECT().
			GetMVRReportV1(gomock.Any(), mockFetcherRequest).
			Return(nil, unexpectedFetchingError)

		interceptor := newFleetModifiedMVRScoreRequestV1Interceptor(mockFetcherClient)

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedFetchingError)
	})

	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newFleetModifiedMVRScoreRequestV1Interceptor(nil)

		type differentRequest struct {
			DotNumber int
		}

		mockRequest := &differentRequest{
			DotNumber: 1234567,
		}

		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}

// Test_PriorCarrierYearsRetainedRequestV1Interceptor tests the gRPC interceptor that handles
// data fetching for PriorCarrierYearsRetainedRequestV1. This interceptor is responsible for:
// 1. Checking if insurance data is already present in the request (cached/pre-fetched)
// 2. If no data is present, fetching both active/pending insurance and insurance history
// 3. Combining the fetched data and populating the request before passing it to the next handler
// 4. Handling various error scenarios and edge cases
func Test_PriorCarrierYearsRetainedRequestV1Interceptor(t *testing.T) {
	methodName := "myMethod"
	now := time.Now()
	mockCC := &grpc.ClientConn{}
	mockReply := &PriorCarrierYearsRetainedV1{}

	dotNumber := int64(1234567)

	// Mock data representing active or pending insurance records
	mockActiveInsurance := &data_fetching.BIPDActiveOrPendingInsuranceV1{
		Records: []*data_fetching.InsuranceRecordV1{
			{
				Id:                  uuid.New().String(),
				DotNumber:           dotNumber,
				EffectiveDate:       timestamppb.New(now),
				CancelEffectiveDate: timestamppb.New(now.Add(30 * time_utils.Day)),
			},
		},
	}

	// Mock data representing historical insurance records
	mockInsuranceHistory := &data_fetching.BIPDInsuranceHistoryV1{
		Records: []*data_fetching.InsuranceRecordV1{
			{
				Id:                  uuid.New().String(),
				DotNumber:           dotNumber,
				EffectiveDate:       timestamppb.New(now.Add(-60 * time_utils.Day)),
				CancelEffectiveDate: timestamppb.New(now.Add(-30 * time_utils.Day)),
			},
		},
	}

	// Mock fetcher requests for both active insurance and insurance history
	mockFetcherRequests := &PriorCarrierYearsRetainedRequestV1_FetcherRequests{
		ActiveOrPendingInsuranceRequest: &data_fetching.BIPDActiveOrPendingInsuranceRequestV1{
			DotNumber: dotNumber,
		},
		InsuranceHistoryRequest: &data_fetching.BIPDInsuranceHistoryRequestV1{
			DotNumber: dotNumber,
		},
	}

	ctrl, ctx := gomock.WithContext(context.Background(), t)
	mockFetcherClient := data_fetching.NewMockFetcherClient(ctrl)

	// Test case 1: Data is already present in the request (pre-fetched or cached)
	// The interceptor should not make any external calls and pass the existing data through
	t.Run("with fetched data and fetcher client present: should not fetch data using client", func(t *testing.T) {
		existingRecords := []*data_fetching.InsuranceRecordV1{
			{
				DotNumber:     1234567,
				EffectiveDate: timestamppb.New(now),
			},
		}

		// Request with pre-existing fetched data
		mockRequest := &PriorCarrierYearsRetainedRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &PriorCarrierYearsRetainedRequestV1_FetchedData_{
				FetchedData: &PriorCarrierYearsRetainedRequestV1_FetchedData{
					InsuranceRecords: existingRecords,
				},
			},
		}

		interceptor := newPriorCarrierYearsRetainedRequestV1Interceptor(mockFetcherClient)

		// Verify that the interceptor passes through the existing data without modification
		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*PriorCarrierYearsRetainedRequestV1)
			require.True(t, ok)
			require.Equal(t, existingRecords, request.GetFetchedData().InsuranceRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	// Test case 2: No data is present, interceptor should fetch from external sources
	// The interceptor should make two API calls and combine the results
	t.Run("without fetched data and fetcher client present: should fetch data using client", func(t *testing.T) {
		// Request with fetcher specifications but no pre-fetched data
		mockRequest := &PriorCarrierYearsRetainedRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &PriorCarrierYearsRetainedRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		// Set expectations for both external API calls
		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(mockActiveInsurance, nil)

		mockFetcherClient.EXPECT().
			GetBIPDInsuranceHistoryV1(
				ctx,
				mockFetcherRequests.InsuranceHistoryRequest,
			).
			Return(mockInsuranceHistory, nil)

		interceptor := newPriorCarrierYearsRetainedRequestV1Interceptor(mockFetcherClient)

		// Verify that the interceptor fetches data and combines the results
		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*PriorCarrierYearsRetainedRequestV1)
			require.True(t, ok)

			// Verify that records from both sources are combined
			allRecords := append(mockActiveInsurance.Records, mockInsuranceHistory.Records...)
			require.Equal(t, allRecords, request.GetFetchedData().InsuranceRecords)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	// Test case 3: No fetcher client available
	// The interceptor should not attempt to fetch data and leave the request unchanged
	t.Run("without fetched data nor fetcher client: should leave fetched data nil", func(t *testing.T) {
		interceptor := newPriorCarrierYearsRetainedRequestV1Interceptor(nil)

		mockRequest := &PriorCarrierYearsRetainedRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &PriorCarrierYearsRetainedRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		// Verify that no data is populated when fetcher client is unavailable
		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockCC, cc)
			require.Equal(t, mockReply, reply)
			require.Len(t, opts, 0)

			request, ok := req.(*PriorCarrierYearsRetainedRequestV1)
			require.True(t, ok)
			require.Nil(t, request.GetFetchedData())

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})

	// Test case 4: Invalid request - fetcher requests are nil
	// The interceptor should return an error when required fetcher requests are missing
	t.Run("without fetched data nor fetcher request and fetcher client present: should return error", func(t *testing.T) {
		mockRequest := &PriorCarrierYearsRetainedRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &PriorCarrierYearsRetainedRequestV1_FetcherRequests_{
				FetcherRequests: nil, // Invalid: requests are nil
			},
		}

		interceptor := newPriorCarrierYearsRetainedRequestV1Interceptor(mockFetcherClient)

		// The invoker should not be called when validation fails
		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.Contains(t, err.Error(), "fetcher requests are nil")
	})

	// Test case 5: Error during active insurance fetch
	// The interceptor should propagate errors from the first external API call
	t.Run("without fetched data and with error fetching active insurance: should return error", func(t *testing.T) {
		mockRequest := &PriorCarrierYearsRetainedRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &PriorCarrierYearsRetainedRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		unexpectedError := errors.New("unexpected error")
		// First API call fails
		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(nil, unexpectedError)

		interceptor := newPriorCarrierYearsRetainedRequestV1Interceptor(mockFetcherClient)

		// The invoker should not be called when fetch fails
		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedError)
	})

	// Test case 6: Error during insurance history fetch
	// The interceptor should propagate errors from the second external API call
	t.Run("without fetched data and with error fetching insurance history: should return error", func(t *testing.T) {
		mockRequest := &PriorCarrierYearsRetainedRequestV1{
			EffectiveDate: timestamppb.New(now),
			FetcherSpec: &PriorCarrierYearsRetainedRequestV1_FetcherRequests_{
				FetcherRequests: mockFetcherRequests,
			},
		}

		// First call to get active insurance succeeds
		mockFetcherClient.EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				mockFetcherRequests.ActiveOrPendingInsuranceRequest,
			).
			Return(mockActiveInsurance, nil)

		// Second call to get insurance history fails
		unexpectedError := errors.New("unexpected error")
		mockFetcherClient.EXPECT().
			GetBIPDInsuranceHistoryV1(
				ctx,
				mockFetcherRequests.InsuranceHistoryRequest,
			).
			Return(nil, unexpectedError)

		interceptor := newPriorCarrierYearsRetainedRequestV1Interceptor(mockFetcherClient)

		// The invoker should not be called when fetch fails
		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			t.Fatal("invoker should not be called when fetch fails")
			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.Error(t, err)
		require.ErrorIs(t, err, unexpectedError)
	})

	// Test case 7: Request type mismatch
	// The interceptor should pass through requests that don't match the expected type
	t.Run("with different request type: shouldn't raise any error and call next invoker", func(t *testing.T) {
		interceptor := newPriorCarrierYearsRetainedRequestV1Interceptor(nil)

		// Request with a different type that the interceptor doesn't handle
		type differentRequest struct {
			DotNumber int
		}

		mockRequest := &differentRequest{
			DotNumber: 1234567,
		}

		// Verify that the interceptor passes through unhandled request types unchanged
		mockedInvoker := func(
			ctx context.Context,
			method string,
			req any,
			reply any,
			cc *grpc.ClientConn,
			opts ...grpc.CallOption,
		) error {
			require.Equal(t, methodName, method)
			require.Equal(t, mockRequest, req)
			require.Equal(t, mockReply, reply)
			require.Equal(t, mockCC, cc)
			require.Len(t, opts, 0)

			return nil
		}

		err := interceptor(
			ctx,
			methodName,
			mockRequest,
			mockReply,
			mockCC,
			mockedInvoker,
		)
		require.NoError(t, err)
	})
}
