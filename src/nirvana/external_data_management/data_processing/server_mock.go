// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/data_processing (interfaces: ProcessorServer)
//
// Generated by this command:
//
//	mockgen -destination=server_mock.go -package=data_processing nirvanatech.com/nirvana/external_data_management/data_processing ProcessorServer
//

// Package data_processing is a generated GoMock package.
package data_processing

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	data_fetching "nirvanatech.com/nirvana/external_data_management/data_fetching"
)

// MockProcessorServer is a mock of ProcessorServer interface.
type MockProcessorServer struct {
	ctrl     *gomock.Controller
	recorder *MockProcessorServerMockRecorder
	isgomock struct{}
}

// MockProcessorServerMockRecorder is the mock recorder for MockProcessorServer.
type MockProcessorServerMockRecorder struct {
	mock *MockProcessorServer
}

// NewMockProcessorServer creates a new mock instance.
func NewMockProcessorServer(ctrl *gomock.Controller) *MockProcessorServer {
	mock := &MockProcessorServer{ctrl: ctrl}
	mock.recorder = &MockProcessorServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProcessorServer) EXPECT() *MockProcessorServerMockRecorder {
	return m.recorder
}

// GetContinuousCoverageYearsV1 mocks base method.
func (m *MockProcessorServer) GetContinuousCoverageYearsV1(arg0 context.Context, arg1 *ContinuousCoverageYearsRequestV1) (*ContinuousCoverageYearsV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContinuousCoverageYearsV1", arg0, arg1)
	ret0, _ := ret[0].(*ContinuousCoverageYearsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContinuousCoverageYearsV1 indicates an expected call of GetContinuousCoverageYearsV1.
func (mr *MockProcessorServerMockRecorder) GetContinuousCoverageYearsV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContinuousCoverageYearsV1", reflect.TypeOf((*MockProcessorServer)(nil).GetContinuousCoverageYearsV1), arg0, arg1)
}

// GetFleetModifiedMVRScoreV1 mocks base method.
func (m *MockProcessorServer) GetFleetModifiedMVRScoreV1(arg0 context.Context, arg1 *FleetModifiedMVRScoreRequestV1) (*FleetModifiedMVRScoreV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFleetModifiedMVRScoreV1", arg0, arg1)
	ret0, _ := ret[0].(*FleetModifiedMVRScoreV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFleetModifiedMVRScoreV1 indicates an expected call of GetFleetModifiedMVRScoreV1.
func (mr *MockProcessorServerMockRecorder) GetFleetModifiedMVRScoreV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFleetModifiedMVRScoreV1", reflect.TypeOf((*MockProcessorServer)(nil).GetFleetModifiedMVRScoreV1), arg0, arg1)
}

// GetFleetMovingViolationCountV1 mocks base method.
func (m *MockProcessorServer) GetFleetMovingViolationCountV1(arg0 context.Context, arg1 *FleetMovingViolationCountRequestV1) (*FleetMovingViolationCountV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFleetMovingViolationCountV1", arg0, arg1)
	ret0, _ := ret[0].(*FleetMovingViolationCountV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFleetMovingViolationCountV1 indicates an expected call of GetFleetMovingViolationCountV1.
func (mr *MockProcessorServerMockRecorder) GetFleetMovingViolationCountV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFleetMovingViolationCountV1", reflect.TypeOf((*MockProcessorServer)(nil).GetFleetMovingViolationCountV1), arg0, arg1)
}

// GetIsoFieldsFromNhtsaFieldsV1 mocks base method.
func (m *MockProcessorServer) GetIsoFieldsFromNhtsaFieldsV1(arg0 context.Context, arg1 *IsoFieldsFromNhtsaFieldsRequestV1) (*IsoFieldsFromNhtsaFieldsV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIsoFieldsFromNhtsaFieldsV1", arg0, arg1)
	ret0, _ := ret[0].(*IsoFieldsFromNhtsaFieldsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIsoFieldsFromNhtsaFieldsV1 indicates an expected call of GetIsoFieldsFromNhtsaFieldsV1.
func (mr *MockProcessorServerMockRecorder) GetIsoFieldsFromNhtsaFieldsV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIsoFieldsFromNhtsaFieldsV1", reflect.TypeOf((*MockProcessorServer)(nil).GetIsoFieldsFromNhtsaFieldsV1), arg0, arg1)
}

// GetLatestValidRatingTierRecordV1 mocks base method.
func (m *MockProcessorServer) GetLatestValidRatingTierRecordV1(arg0 context.Context, arg1 *LatestValidRatingTierRecordRequestV1) (*data_fetching.RatingTierRecordV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestValidRatingTierRecordV1", arg0, arg1)
	ret0, _ := ret[0].(*data_fetching.RatingTierRecordV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestValidRatingTierRecordV1 indicates an expected call of GetLatestValidRatingTierRecordV1.
func (mr *MockProcessorServerMockRecorder) GetLatestValidRatingTierRecordV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestValidRatingTierRecordV1", reflect.TypeOf((*MockProcessorServer)(nil).GetLatestValidRatingTierRecordV1), arg0, arg1)
}

// GetNFCreditFeaturesV1 mocks base method.
func (m *MockProcessorServer) GetNFCreditFeaturesV1(arg0 context.Context, arg1 *NFCreditFeaturesRequestV1) (*NFCreditFeaturesV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNFCreditFeaturesV1", arg0, arg1)
	ret0, _ := ret[0].(*NFCreditFeaturesV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFCreditFeaturesV1 indicates an expected call of GetNFCreditFeaturesV1.
func (mr *MockProcessorServerMockRecorder) GetNFCreditFeaturesV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFCreditFeaturesV1", reflect.TypeOf((*MockProcessorServer)(nil).GetNFCreditFeaturesV1), arg0, arg1)
}

// GetNFCreditFeaturesV2 mocks base method.
func (m *MockProcessorServer) GetNFCreditFeaturesV2(arg0 context.Context, arg1 *NFCreditFeaturesRequestV2) (*NFCreditFeaturesV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNFCreditFeaturesV2", arg0, arg1)
	ret0, _ := ret[0].(*NFCreditFeaturesV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFCreditFeaturesV2 indicates an expected call of GetNFCreditFeaturesV2.
func (mr *MockProcessorServerMockRecorder) GetNFCreditFeaturesV2(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFCreditFeaturesV2", reflect.TypeOf((*MockProcessorServer)(nil).GetNFCreditFeaturesV2), arg0, arg1)
}

// GetNFInspectionCountV1 mocks base method.
func (m *MockProcessorServer) GetNFInspectionCountV1(arg0 context.Context, arg1 *NFInspectionCountRequestV1) (*NFInspectionCountV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNFInspectionCountV1", arg0, arg1)
	ret0, _ := ret[0].(*NFInspectionCountV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFInspectionCountV1 indicates an expected call of GetNFInspectionCountV1.
func (mr *MockProcessorServerMockRecorder) GetNFInspectionCountV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFInspectionCountV1", reflect.TypeOf((*MockProcessorServer)(nil).GetNFInspectionCountV1), arg0, arg1)
}

// GetNFViolationsMetadataV1 mocks base method.
func (m *MockProcessorServer) GetNFViolationsMetadataV1(arg0 context.Context, arg1 *NFViolationsMetadataRequestV1) (*NFViolationsMetadataV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNFViolationsMetadataV1", arg0, arg1)
	ret0, _ := ret[0].(*NFViolationsMetadataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFViolationsMetadataV1 indicates an expected call of GetNFViolationsMetadataV1.
func (mr *MockProcessorServerMockRecorder) GetNFViolationsMetadataV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFViolationsMetadataV1", reflect.TypeOf((*MockProcessorServer)(nil).GetNFViolationsMetadataV1), arg0, arg1)
}

// GetPriorCarrierYearsRetainedV1 mocks base method.
func (m *MockProcessorServer) GetPriorCarrierYearsRetainedV1(arg0 context.Context, arg1 *PriorCarrierYearsRetainedRequestV1) (*PriorCarrierYearsRetainedV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPriorCarrierYearsRetainedV1", arg0, arg1)
	ret0, _ := ret[0].(*PriorCarrierYearsRetainedV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPriorCarrierYearsRetainedV1 indicates an expected call of GetPriorCarrierYearsRetainedV1.
func (mr *MockProcessorServerMockRecorder) GetPriorCarrierYearsRetainedV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPriorCarrierYearsRetainedV1", reflect.TypeOf((*MockProcessorServer)(nil).GetPriorCarrierYearsRetainedV1), arg0, arg1)
}

// GetRetainedYearsV1 mocks base method.
func (m *MockProcessorServer) GetRetainedYearsV1(arg0 context.Context, arg1 *RetainedYearsRequestV1) (*RetainedYearsV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRetainedYearsV1", arg0, arg1)
	ret0, _ := ret[0].(*RetainedYearsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRetainedYearsV1 indicates an expected call of GetRetainedYearsV1.
func (mr *MockProcessorServerMockRecorder) GetRetainedYearsV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRetainedYearsV1", reflect.TypeOf((*MockProcessorServer)(nil).GetRetainedYearsV1), arg0, arg1)
}

// GetYearsInBusinessFromAuthorityHistoryV1 mocks base method.
func (m *MockProcessorServer) GetYearsInBusinessFromAuthorityHistoryV1(arg0 context.Context, arg1 *YearsInBusinessFromAuthorityHistoryRequestV1) (*YearsInBusinessFromAuthorityHistoryV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYearsInBusinessFromAuthorityHistoryV1", arg0, arg1)
	ret0, _ := ret[0].(*YearsInBusinessFromAuthorityHistoryV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYearsInBusinessFromAuthorityHistoryV1 indicates an expected call of GetYearsInBusinessFromAuthorityHistoryV1.
func (mr *MockProcessorServerMockRecorder) GetYearsInBusinessFromAuthorityHistoryV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYearsInBusinessFromAuthorityHistoryV1", reflect.TypeOf((*MockProcessorServer)(nil).GetYearsInBusinessFromAuthorityHistoryV1), arg0, arg1)
}

// GetYearsInBusinessFromInsuranceHistoryV1 mocks base method.
func (m *MockProcessorServer) GetYearsInBusinessFromInsuranceHistoryV1(arg0 context.Context, arg1 *InsuranceHistoryYearsInBusinessRequestV1) (*InsuranceHistoryYearsInBusinessV1, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYearsInBusinessFromInsuranceHistoryV1", arg0, arg1)
	ret0, _ := ret[0].(*InsuranceHistoryYearsInBusinessV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYearsInBusinessFromInsuranceHistoryV1 indicates an expected call of GetYearsInBusinessFromInsuranceHistoryV1.
func (mr *MockProcessorServerMockRecorder) GetYearsInBusinessFromInsuranceHistoryV1(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYearsInBusinessFromInsuranceHistoryV1", reflect.TypeOf((*MockProcessorServer)(nil).GetYearsInBusinessFromInsuranceHistoryV1), arg0, arg1)
}
