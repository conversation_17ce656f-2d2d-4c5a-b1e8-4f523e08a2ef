load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "static_data_utils",
    srcs = [
        "constants.go",
        "doc.go",
        "test_utils.go",
        "upload_static_data.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/uuid_utils",
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/store_management",
        "@com_github_cockroachdb_errors//:errors",
    ],
)
