package static_data_utils

/*
This package provides utility functions to upload, download, delete, etc. static data from the store used by the
processor component.

Static data is data that does not depend on the call made to one of the processor methods. In other words, this data
is the same for all calls made to a given method. Therefore, it is not expected to be passed as an argument.

Instead, the processor component is expected to fetch it from the store when needed, and potentially cache it for
future use if it is expensive to fetch.

In order to ensure reproducibility of the values returned by the methods in the processor, static data shouldn't be
changed. If it needs to be changed, a new version of it should be uploaded to the store, and a new method on the
processor should be created that uses the new version.

Example of static data are:
- Violations data used to calculate MVC
- NHTSA to ISO mapping used to calculate vehicle type and weight group
*/
