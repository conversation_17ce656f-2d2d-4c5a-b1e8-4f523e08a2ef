package static_data_utils

import (
	"context"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

var ResourceAlreadyExistsErr = errors.New("you can't upload resource, as it already exists in S3. Delete it first if you really want to modify it.")

// UploadStaticData will upload the given resource to the store using a store manager.
// It was created to encapsulate logic shared by multiple scripts that upload static data.
func UploadStaticData(
	ctx context.Context,
	storeManager store_management.StoreManager,
	resource *common.Resource,
	storeKey store_management.StoreKey,
) error {
	resourceAlreadyExists, err := storeManager.Exists(ctx, S3DefaultContextID, storeKey)
	if err != nil {
		return errors.Wrap(err, "error calling Exists")
	}

	if resourceAlreadyExists {
		return ResourceAlreadyExistsErr
	}

	err = storeManager.Save(ctx, S3DefaultContextID, storeKey, resource)
	if err != nil {
		return err
	}

	return nil
}

func LoadStaticData(
	ctx context.Context,
	storeManager store_management.StoreManager,
	resource *common.Resource,
	storeKey store_management.StoreKey,
) error {
	return storeManager.Load(ctx, S3DefaultContextID, storeKey, resource)
}

func DeleteStaticData(
	ctx context.Context,
	storeManager store_management.StoreManager,
	storeKey store_management.StoreKey,
) error {
	return storeManager.Delete(ctx, S3DefaultContextID, storeKey)
}
