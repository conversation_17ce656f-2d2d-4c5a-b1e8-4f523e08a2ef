package static_data_utils

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"io"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

type storeKeyPtr[T any] interface {
	*T
	store_management.StoreKey
}

func PopulateStaticDataForTest[T, U any, PT storeKeyPtr[U]](
	data []byte,
	storeManager store_management.StoreManager,
) error {
	gzipReader, err := gzip.NewReader(bytes.NewBuffer(data))
	if err != nil {
		return err
	}

	defer func() { _ = gzipReader.Close() }()

	var buffer bytes.Buffer
	_, err = io.Copy(&buffer, gzipReader)
	if err != nil {
		return err
	}

	var mapping T

	err = json.Unmarshal(buffer.Bytes(), &mapping)
	if err != nil {
		return err
	}

	ctx := context.Background()
	var storeKey PT

	resource := &common.Resource{Data: &mapping}
	err = UploadStaticData(ctx, storeManager, resource, storeKey)
	if errors.Is(err, ResourceAlreadyExistsErr) {
		return nil
	}
	if err != nil {
		return err
	}

	return nil
}
