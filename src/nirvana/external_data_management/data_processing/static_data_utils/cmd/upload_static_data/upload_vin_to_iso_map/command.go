package upload_vin_to_iso_map

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"os"
	"strings"

	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/aws_utils"
	"nirvanatech.com/nirvana/common-go/gworkspace_utils/gsheets_utils"
	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
)

const (
	nhtsaToIsoMappingGsheetID  = "1Io3BfdgnXCD92cr4BGQsvbIzww9_bSUX24naLrqbanI"
	nhtsaToIsoMappingSheetName = "Combinations"
)

type env struct {
	fx.In

	StoreManager store_management.StoreManager
}

// This command can be run to upload the current version of the vin to iso mapping into the store.
//
// That mapping lives on a Google Sheets file. This script downloads that file, transforms it into a structured
// and versioned struct, and then serializes it into JSON before uploading to the store.
//
// WARNING: even if the map structure didn't change since the last version, and only rows were added, modified or
// deleted, you still have to create a new versioned struct (just embed the older one). The reason being is that
// these structs are stored in version specific directories.
//
// Also, you might need to use a new transformation function if the underlying map or the versioned struct change.
var Command = cobrafx.NewCLI(
	&cobra.Command{
		Use:   "vin-to-iso-map",
		Short: "Upload the VIN to ISO map to the store",
	},
	execute,
)

type nhtsaToIsoValue struct {
	shouldBeKept   bool
	isProblematic  bool
	isoVehicleType vin.IsoVehicleTypeV1
	isoWeightGroup vin.IsoWeightGroupV1
}

type nhtsaToIsoMap map[string]nhtsaToIsoValue

func execute(cmd *cobra.Command, _ []string, env env) error {
	ctx := cmd.Context()
	gsheetsClient, err := newGsheetsClient(ctx)
	if err != nil {
		return err
	}

	sheet, err := gsheets_utils.GetSheetFromSpreadsheet(
		gsheetsClient,
		nhtsaToIsoMappingGsheetID,
		nhtsaToIsoMappingSheetName,
	)
	if err != nil {
		return err
	}

	rawMappings := make(nhtsaToIsoMap)
	for row := gsheets_utils.GetRowFromSheet(sheet, 3); true; row = row.Next() {
		if gsheets_utils.IsRowEmpty(row) {
			break
		}

		vehType := row.Cell(0)
		bodyClass := row.Cell(1)
		weightRating := row.Cell(2)

		hash := buildNhtsaToIsoKey(
			vehType,
			bodyClass,
			weightRating,
		)

		if _, exists := rawMappings[hash]; exists {
			return errors.New("repeated entry found while populating rawMappings")
		}

		isoVehicleType := convertVehicleTypeFromGsheetToProto(row.Cell(6))
		isoWeightGroup := convertWeightGroupFromGsheetToProto(row.Cell(7))
		rawMappings[hash] = nhtsaToIsoValue{
			shouldBeKept:   row.Cell(4) == "keep",
			isProblematic:  row.Cell(5) == "problem",
			isoVehicleType: isoVehicleType,
			isoWeightGroup: isoWeightGroup,
		}
	}

	storeKey := &vin.NhtsaToIsoMappingV1StoreKey{}
	versionedMappings := transformMappingFromGsheetsFormatToVersionedProto(rawMappings)
	resource := &common.Resource{Data: versionedMappings}

	err = static_data_utils.UploadStaticData(ctx, env.StoreManager, resource, storeKey)
	if err != nil {
		return err
	}

	return nil
}

func newGsheetsClient(ctx context.Context) (*gsheets_utils.GoogleSheetsClient, error) {
	_ = os.Setenv("AWS_SDK_LOAD_CONFIG", "true")

	// TODO: use FX
	secretsHelper, err := aws_utils.DeprecatedNewSecretsHelper()
	if err != nil {
		return nil, err
	}
	secrets, err := secretsHelper.GetJsonSecret(ctx, "rateml-google-sheets-service-account-creds")
	if err != nil {
		return nil, err
	}

	privateKey, err := base64.StdEncoding.DecodeString(secrets["private_key_base64"])
	if err != nil {
		return nil, err
	}

	return gsheets_utils.NewGoogleSheetsClient(
		gsheets_utils.GoogleSheetsClientCredentials{
			Email:        secrets["client_email"],
			PrivateKeyID: secrets["private_key_id"],
			PrivateKey:   privateKey,
		},
	)
}

func buildNhtsaToIsoKey(vehicleType, bodyClass, weightClass string) string {
	sanitize := func(s string) string {
		if s == "" {
			return "EMPTY"
		}
		return strings.ToUpper(s)
	}

	return fmt.Sprintf(
		"%s::%s::%s",
		sanitize(vehicleType),
		sanitize(bodyClass),
		sanitize(weightClass),
	)
}
