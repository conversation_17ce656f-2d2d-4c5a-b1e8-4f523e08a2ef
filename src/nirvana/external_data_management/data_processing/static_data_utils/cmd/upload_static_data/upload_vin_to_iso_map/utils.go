package upload_vin_to_iso_map

import (
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin"
)

func transformMappingFromGsheetsFormatToVersionedProto(
	rawData nhtsaToIsoMap,
) *vin.NhtsaToIsoMappingV1 {
	data := make(map[string]*vin.NhtsaToIsoEntryV1)
	for k, v := range rawData {
		data[k] = &vin.NhtsaToIsoEntryV1{
			ShouldBeKept:   v.shouldBeKept,
			IsProblematic:  v.isProblematic,
			IsoVehicleType: v.isoVehicleType,
			IsoWeightGroup: v.isoWeightGroup,
		}
	}

	return &vin.NhtsaToIsoMappingV1{
		Data: data,
	}
}

func convertVehicleTypeFromGsheetToProto(vehicleType string) vin.IsoVehicleTypeV1 {
	switch vehicleType {
	case "EMPTY":
		return vin.IsoVehicleTypeV1_IsoVehicleTypeV1Empty
	case "truck":
		return vin.IsoVehicleTypeV1_IsoVehicleTypeV1Truck
	case "tractor":
		return vin.IsoVehicleTypeV1_IsoVehicleTypeV1Tractor
	case "semi_trailer":
		return vin.IsoVehicleTypeV1_IsoVehicleTypeV1SemiTrailer
	default:
		panic("Unknown vehicle type")
	}
}

func convertWeightGroupFromGsheetToProto(weightGroup string) vin.IsoWeightGroupV1 {
	switch weightGroup {
	case "EMPTY":
		return vin.IsoWeightGroupV1_IsoWeightGroupV1Empty
	case "light":
		return vin.IsoWeightGroupV1_IsoWeightGroupV1Light
	case "medium":
		return vin.IsoWeightGroupV1_IsoWeightGroupV1Medium
	case "heavy":
		return vin.IsoWeightGroupV1_IsoWeightGroupV1Heavy
	case "xheavy":
		return vin.IsoWeightGroupV1_IsoWeightGroupV1ExtraHeavy
	case "semi_trailer":
		return vin.IsoWeightGroupV1_IsoWeightGroupV1SemiTrailer
	default:
		panic("Unknown weight group")
	}
}
