package main

import (
	"github.com/spf13/cobra"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils/cmd/upload_static_data/upload_transit_violations_map"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils/cmd/upload_static_data/upload_vin_to_iso_map"
)

var rootCmd = &cobra.Command{
	Use:   "upload",
	Short: "The parent command for all operations related to uploading static data to the store",
}

func init() {
	rootCmd.AddCommand(upload_transit_violations_map.Command)
	rootCmd.AddCommand(upload_vin_to_iso_map.Command)
}
