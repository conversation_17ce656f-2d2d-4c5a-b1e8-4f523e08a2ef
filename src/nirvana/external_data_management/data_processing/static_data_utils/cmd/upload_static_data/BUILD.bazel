load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "upload_static_data_lib",
    srcs = [
        "main.go",
        "root.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils/cmd/upload_static_data",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/data_processing/static_data_utils/cmd/upload_static_data/upload_transit_violations_map",
        "//nirvana/external_data_management/data_processing/static_data_utils/cmd/upload_static_data/upload_vin_to_iso_map",
        "@com_github_spf13_cobra//:cobra",
    ],
)

go_binary(
    name = "upload_static_data",
    embed = [":upload_static_data_lib"],
    visibility = ["//visibility:public"],
)
