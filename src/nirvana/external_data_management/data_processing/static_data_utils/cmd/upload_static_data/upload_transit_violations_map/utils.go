package upload_transit_violations_map

import (
	"nirvanatech.com/nirvana/external_data_management/data_processing/mvr"
	rating_mvr "nirvanatech.com/nirvana/rating/mvr"
)

func transformFleetViolationCountToFleetViolationDataV1(rawData map[int]rating_mvr.FleetViolationCount) *mvr.FleetViolationsDataV1 {
	data := make(map[int64]*mvr.FleetViolationV1)
	for k, v := range rawData {
		protoExcludedStates := make(map[string]bool)
		for state, val := range v.ExcludedStates {
			protoExcludedStates[state.String()] = val
		}
		data[int64(k)] = &mvr.FleetViolationV1{
			Description:        v.Description,
			IsMovingViolation:  v.IsMovingViolation,
			DurationToConsider: int64(v.DurationToConsider),
			ExcludedStates:     protoExcludedStates,
			ModifiedMVRScore:   int64(v.ModifiedMVRScore),
		}
	}

	return &mvr.FleetViolationsDataV1{
		Data: data,
	}
}
