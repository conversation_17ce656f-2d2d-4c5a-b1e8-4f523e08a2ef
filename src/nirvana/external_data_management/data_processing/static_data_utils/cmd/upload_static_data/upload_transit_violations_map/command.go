package upload_transit_violations_map

import (
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_processing/mvr"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	rating_mvr "nirvanatech.com/nirvana/rating/mvr"
)

type env struct {
	fx.In

	StoreManager store_management.StoreManager
}

// This command can be run to upload the current version of the transition violations map into the store.
// It transforms that map into a structured and versioned struct, and then serializes it into JSON before uploading.
//
// WARNING: even if the map structure didn't change since the last version, and only rows were added, modified or
// deleted, you still have to create a new versioned struct (just embed the older one). The reason being is that
// these structs are stored in version specific directories.
//
// Also, you might need to use a new transformation function if the underlying map or the versioned struct change.
var Command = cobrafx.NewCLI(
	&cobra.Command{
		Use:   "transit-violations-map",
		Short: "Uploads the Transit Violations Map static data to the store",
	},
	execute,
)

func execute(cmd *cobra.Command, _ []string, env env) error {
	ctx := cmd.Context()

	storeKey := &mvr.FleetViolationsDataV1StoreKey{}
	rawData := rating_mvr.FleetViolationCountData
	versionedData := transformFleetViolationCountToFleetViolationDataV1(rawData)
	resource := &common.Resource{Data: versionedData}

	err := static_data_utils.UploadStaticData(ctx, env.StoreManager, resource, storeKey)
	if err != nil {
		return err
	}

	return nil
}
