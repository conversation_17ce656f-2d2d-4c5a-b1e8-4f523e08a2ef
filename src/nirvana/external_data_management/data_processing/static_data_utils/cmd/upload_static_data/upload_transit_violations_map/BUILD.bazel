load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "upload_transit_violations_map",
    srcs = [
        "command.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils/cmd/upload_static_data/upload_transit_violations_map",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/data_processing/mvr",
        "//nirvana/external_data_management/data_processing/static_data_utils",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/fx/appfx/cobrafx",
        "//nirvana/rating/mvr",
        "@com_github_spf13_cobra//:cobra",
        "@org_uber_go_fx//:fx",
    ],
)
