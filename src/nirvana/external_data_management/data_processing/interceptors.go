package data_processing

import (
	"context"

	"github.com/cockroachdb/errors"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

func newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*YearsInBusinessFromAuthorityHistoryRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequest := request.GetFetcherRequest()
			if fetcherRequest == nil {
				return errors.New("fetcher request is nil")
			}

			response, err := fetcherClient.GetGrantedAuthorityHistoryV1(
				ctx,
				fetcherRequest,
			)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get granted authority history for request: %v",
					fetcherRequest,
				)
			}

			request.FetcherSpec = &YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_{
				FetchedData: &YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData{
					AuthorityHistoryRecords: response.Records,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newFleetMovingViolationCountRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*FleetMovingViolationCountRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequest := request.GetFetcherRequest()
			if fetcherRequest == nil {
				return errors.New("fetcher request is nil")
			}

			report, err := fetcherClient.GetMVRReportV1(ctx, fetcherRequest)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get MVR report for request: %v",
					fetcherRequest,
				)
			}

			request.FetcherSpec = &FleetMovingViolationCountRequestV1_FetchedData_{
				FetchedData: &FleetMovingViolationCountRequestV1_FetchedData{
					Report: report,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newContinuousCoverageYearsRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*ContinuousCoverageYearsRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequests := request.GetFetcherRequests()
			if fetcherRequests == nil {
				return errors.New("fetcher requests are nil")
			}

			activeOrPendingInsurance, err := fetcherClient.GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				fetcherRequests.ActiveOrPendingInsuranceRequest,
			)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get active or pending insurance for request: %v",
					fetcherRequests.ActiveOrPendingInsuranceRequest,
				)
			}

			bipdInsuranceHistory, err := fetcherClient.GetBIPDInsuranceHistoryV1(
				ctx,
				fetcherRequests.InsuranceHistoryRequest,
			)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get insurance history for request: %v",
					fetcherRequests.InsuranceHistoryRequest,
				)
			}

			insuranceRecords := append(activeOrPendingInsurance.GetRecords(), bipdInsuranceHistory.GetRecords()...)

			request.FetcherSpec = &ContinuousCoverageYearsRequestV1_FetchedData_{
				FetchedData: &ContinuousCoverageYearsRequestV1_FetchedData{
					InsuranceRecords: insuranceRecords,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newIsoFieldsFromNhtsaFieldsRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*IsoFieldsFromNhtsaFieldsRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequest := request.GetFetcherRequest()
			if fetcherRequest == nil {
				return errors.New("fetcher request is nil")
			}

			vinDetails, err := fetcherClient.GetVINDetailsV1(ctx, fetcherRequest)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get VIN details for request: %v",
					fetcherRequest,
				)
			}

			request.FetcherSpec = &IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_{
				FetchedData: &IsoFieldsFromNhtsaFieldsRequestV1_FetchedData{
					VehicleType:        vinDetails.VehicleType,
					VehicleBodyClass:   vinDetails.BodyClass,
					VehicleWeightClass: vinDetails.WeightClass,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newNFInspectionCountRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*NFInspectionCountRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequest := request.GetFetcherRequest()
			if fetcherRequest == nil {
				return errors.New("fetcher request is nil")
			}

			inspectionRecords, err := fetcherClient.GetFMCSAInspectionRecordsV1(ctx, fetcherRequest)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get FMCSA inspection records for request: %v",
					fetcherRequest,
				)
			}

			request.FetcherSpec = &NFInspectionCountRequestV1_FetchedData_{
				FetchedData: &NFInspectionCountRequestV1_FetchedData{
					InspectionRecords: inspectionRecords,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newNFViolationsMetadataRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*NFViolationsMetadataRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequest := request.GetFetcherRequest()
			if fetcherRequest == nil {
				return errors.New("fetcher request is nil")
			}

			violationRecords, err := fetcherClient.GetFMCSAViolationRecordsV1(ctx, fetcherRequest)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get FMCSA violation records for request: %v",
					fetcherRequest,
				)
			}

			request.FetcherSpec = &NFViolationsMetadataRequestV1_FetchedData_{
				FetchedData: &NFViolationsMetadataRequestV1_FetchedData{
					ViolationRecords: violationRecords,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newNFCreditFeaturesRequestV2Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*NFCreditFeaturesRequestV2)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequest := request.GetFetcherRequest()
			if fetcherRequest == nil {
				return errors.New("fetcher request is nil")
			}

			ncf, err := fetcherClient.GetNationalCreditFileV1(ctx, fetcherRequest)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get national credit file for request: %v",
					fetcherRequest,
				)
			}

			request.FetcherSpec = &NFCreditFeaturesRequestV2_FetchedData_{
				FetchedData: &NFCreditFeaturesRequestV2_FetchedData{
					NationalCreditFile: ncf,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newInsuranceHistoryYearsInBusinessRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*InsuranceHistoryYearsInBusinessRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequests := request.GetFetcherRequests()
			if fetcherRequests == nil {
				return errors.New("fetcher requests are nil")
			}

			activeOrPendingInsurance, err := fetcherClient.GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				fetcherRequests.ActiveOrPendingInsuranceRequest,
			)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get active or pending insurance for request: %v",
					fetcherRequests.ActiveOrPendingInsuranceRequest,
				)
			}

			bipdInsuranceHistory, err := fetcherClient.GetBIPDInsuranceHistoryV1(
				ctx,
				fetcherRequests.InsuranceHistoryRequest,
			)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get insurance history for request: %v",
					fetcherRequests.InsuranceHistoryRequest,
				)
			}

			insuranceRecords := append(activeOrPendingInsurance.GetRecords(), bipdInsuranceHistory.GetRecords()...)

			request.FetcherSpec = &InsuranceHistoryYearsInBusinessRequestV1_FetchedData_{
				FetchedData: &InsuranceHistoryYearsInBusinessRequestV1_FetchedData{
					InsuranceRecords: insuranceRecords,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newRetainedYearsRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*RetainedYearsRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequest := request.GetFetcherRequest()
			if fetcherRequest == nil {
				return errors.New("fetcher request is nil")
			}

			policies, err := fetcherClient.GetNirvanaPoliciesV1(ctx, fetcherRequest)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get Nirvana policies for request: %v",
					fetcherRequest,
				)
			}

			request.FetcherSpec = &RetainedYearsRequestV1_FetchedData_{
				FetchedData: &RetainedYearsRequestV1_FetchedData{
					Policies: policies,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newFleetModifiedMVRScoreRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*FleetModifiedMVRScoreRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequest := request.GetFetcherRequest()
			if fetcherRequest == nil {
				return errors.New("fetcher request is nil")
			}

			report, err := fetcherClient.GetMVRReportV1(ctx, fetcherRequest)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get MVR report for request: %v",
					fetcherRequest,
				)
			}

			request.FetcherSpec = &FleetModifiedMVRScoreRequestV1_FetchedData_{
				FetchedData: &FleetModifiedMVRScoreRequestV1_FetchedData{
					Report: report,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newPriorCarrierYearsRetainedRequestV1Interceptor(
	fetcherClient data_fetching.FetcherClient,
) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		request, ok := req.(*PriorCarrierYearsRetainedRequestV1)
		if ok && fetcherClient != nil && request.GetFetchedData() == nil {
			fetcherRequests := request.GetFetcherRequests()
			if fetcherRequests == nil {
				return errors.New("fetcher requests are nil")
			}

			activeOrPendingInsurance, err := fetcherClient.GetBIPDActiveOrPendingInsuranceV1(
				ctx,
				fetcherRequests.ActiveOrPendingInsuranceRequest,
			)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get active or pending insurance for request: %v",
					fetcherRequests.ActiveOrPendingInsuranceRequest,
				)
			}

			bipdInsuranceHistory, err := fetcherClient.GetBIPDInsuranceHistoryV1(
				ctx,
				fetcherRequests.InsuranceHistoryRequest,
			)
			if err != nil {
				return errors.Wrapf(
					err,
					"unable to get insurance history for request: %v",
					fetcherRequests.InsuranceHistoryRequest,
				)
			}

			insuranceRecords := append(activeOrPendingInsurance.GetRecords(), bipdInsuranceHistory.GetRecords()...)

			request.FetcherSpec = &PriorCarrierYearsRetainedRequestV1_FetchedData_{
				FetchedData: &PriorCarrierYearsRetainedRequestV1_FetchedData{
					InsuranceRecords: insuranceRecords,
				},
			}
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}
