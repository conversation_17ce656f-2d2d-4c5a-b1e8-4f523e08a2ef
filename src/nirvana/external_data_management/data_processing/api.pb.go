// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: data_processing/api.proto

package data_processing

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	data_fetching "nirvanatech.com/nirvana/external_data_management/data_fetching"
	vin "nirvanatech.com/nirvana/external_data_management/data_processing/vin"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FleetMovingViolationCountRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*FleetMovingViolationCountRequestV1_FetcherRequest
	//	*FleetMovingViolationCountRequestV1_FetchedData_
	FetcherSpec          isFleetMovingViolationCountRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	IsExcludedMVCEnabled bool                                             `protobuf:"varint,3,opt,name=isExcludedMVCEnabled,proto3" json:"isExcludedMVCEnabled,omitempty"`
	EffectiveDate        *timestamppb.Timestamp                           `protobuf:"bytes,4,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
	UsState              *string                                          `protobuf:"bytes,5,opt,name=usState,proto3,oneof" json:"usState,omitempty"`
}

func (x *FleetMovingViolationCountRequestV1) Reset() {
	*x = FleetMovingViolationCountRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetMovingViolationCountRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetMovingViolationCountRequestV1) ProtoMessage() {}

func (x *FleetMovingViolationCountRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetMovingViolationCountRequestV1.ProtoReflect.Descriptor instead.
func (*FleetMovingViolationCountRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{0}
}

func (m *FleetMovingViolationCountRequestV1) GetFetcherSpec() isFleetMovingViolationCountRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *FleetMovingViolationCountRequestV1) GetFetcherRequest() *data_fetching.MVRReportRequestV1 {
	if x, ok := x.GetFetcherSpec().(*FleetMovingViolationCountRequestV1_FetcherRequest); ok {
		return x.FetcherRequest
	}
	return nil
}

func (x *FleetMovingViolationCountRequestV1) GetFetchedData() *FleetMovingViolationCountRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*FleetMovingViolationCountRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *FleetMovingViolationCountRequestV1) GetIsExcludedMVCEnabled() bool {
	if x != nil {
		return x.IsExcludedMVCEnabled
	}
	return false
}

func (x *FleetMovingViolationCountRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

func (x *FleetMovingViolationCountRequestV1) GetUsState() string {
	if x != nil && x.UsState != nil {
		return *x.UsState
	}
	return ""
}

type isFleetMovingViolationCountRequestV1_FetcherSpec interface {
	isFleetMovingViolationCountRequestV1_FetcherSpec()
}

type FleetMovingViolationCountRequestV1_FetcherRequest struct {
	FetcherRequest *data_fetching.MVRReportRequestV1 `protobuf:"bytes,1,opt,name=fetcherRequest,proto3,oneof"`
}

type FleetMovingViolationCountRequestV1_FetchedData_ struct {
	FetchedData *FleetMovingViolationCountRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*FleetMovingViolationCountRequestV1_FetcherRequest) isFleetMovingViolationCountRequestV1_FetcherSpec() {
}

func (*FleetMovingViolationCountRequestV1_FetchedData_) isFleetMovingViolationCountRequestV1_FetcherSpec() {
}

type FleetMovingViolationCountV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *FleetMovingViolationCountV1) Reset() {
	*x = FleetMovingViolationCountV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetMovingViolationCountV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetMovingViolationCountV1) ProtoMessage() {}

func (x *FleetMovingViolationCountV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetMovingViolationCountV1.ProtoReflect.Descriptor instead.
func (*FleetMovingViolationCountV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{1}
}

func (x *FleetMovingViolationCountV1) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type FleetModifiedMVRScoreRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*FleetModifiedMVRScoreRequestV1_FetcherRequest
	//	*FleetModifiedMVRScoreRequestV1_FetchedData_
	FetcherSpec   isFleetModifiedMVRScoreRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	EffectiveDate *timestamppb.Timestamp                       `protobuf:"bytes,3,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
	UsState       *string                                      `protobuf:"bytes,4,opt,name=usState,proto3,oneof" json:"usState,omitempty"`
}

func (x *FleetModifiedMVRScoreRequestV1) Reset() {
	*x = FleetModifiedMVRScoreRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetModifiedMVRScoreRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetModifiedMVRScoreRequestV1) ProtoMessage() {}

func (x *FleetModifiedMVRScoreRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetModifiedMVRScoreRequestV1.ProtoReflect.Descriptor instead.
func (*FleetModifiedMVRScoreRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{2}
}

func (m *FleetModifiedMVRScoreRequestV1) GetFetcherSpec() isFleetModifiedMVRScoreRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *FleetModifiedMVRScoreRequestV1) GetFetcherRequest() *data_fetching.MVRReportRequestV1 {
	if x, ok := x.GetFetcherSpec().(*FleetModifiedMVRScoreRequestV1_FetcherRequest); ok {
		return x.FetcherRequest
	}
	return nil
}

func (x *FleetModifiedMVRScoreRequestV1) GetFetchedData() *FleetModifiedMVRScoreRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*FleetModifiedMVRScoreRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *FleetModifiedMVRScoreRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

func (x *FleetModifiedMVRScoreRequestV1) GetUsState() string {
	if x != nil && x.UsState != nil {
		return *x.UsState
	}
	return ""
}

type isFleetModifiedMVRScoreRequestV1_FetcherSpec interface {
	isFleetModifiedMVRScoreRequestV1_FetcherSpec()
}

type FleetModifiedMVRScoreRequestV1_FetcherRequest struct {
	FetcherRequest *data_fetching.MVRReportRequestV1 `protobuf:"bytes,1,opt,name=fetcherRequest,proto3,oneof"`
}

type FleetModifiedMVRScoreRequestV1_FetchedData_ struct {
	FetchedData *FleetModifiedMVRScoreRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*FleetModifiedMVRScoreRequestV1_FetcherRequest) isFleetModifiedMVRScoreRequestV1_FetcherSpec() {
}

func (*FleetModifiedMVRScoreRequestV1_FetchedData_) isFleetModifiedMVRScoreRequestV1_FetcherSpec() {}

type FleetModifiedMVRScoreV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score int64 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *FleetModifiedMVRScoreV1) Reset() {
	*x = FleetModifiedMVRScoreV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetModifiedMVRScoreV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetModifiedMVRScoreV1) ProtoMessage() {}

func (x *FleetModifiedMVRScoreV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetModifiedMVRScoreV1.ProtoReflect.Descriptor instead.
func (*FleetModifiedMVRScoreV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{3}
}

func (x *FleetModifiedMVRScoreV1) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type IsoFieldsFromNhtsaFieldsRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*IsoFieldsFromNhtsaFieldsRequestV1_FetcherRequest
	//	*IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_
	FetcherSpec isIsoFieldsFromNhtsaFieldsRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1) Reset() {
	*x = IsoFieldsFromNhtsaFieldsRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsoFieldsFromNhtsaFieldsRequestV1) ProtoMessage() {}

func (x *IsoFieldsFromNhtsaFieldsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsoFieldsFromNhtsaFieldsRequestV1.ProtoReflect.Descriptor instead.
func (*IsoFieldsFromNhtsaFieldsRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{4}
}

func (m *IsoFieldsFromNhtsaFieldsRequestV1) GetFetcherSpec() isIsoFieldsFromNhtsaFieldsRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1) GetFetcherRequest() *data_fetching.VINDetailsRequestV1 {
	if x, ok := x.GetFetcherSpec().(*IsoFieldsFromNhtsaFieldsRequestV1_FetcherRequest); ok {
		return x.FetcherRequest
	}
	return nil
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1) GetFetchedData() *IsoFieldsFromNhtsaFieldsRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

type isIsoFieldsFromNhtsaFieldsRequestV1_FetcherSpec interface {
	isIsoFieldsFromNhtsaFieldsRequestV1_FetcherSpec()
}

type IsoFieldsFromNhtsaFieldsRequestV1_FetcherRequest struct {
	FetcherRequest *data_fetching.VINDetailsRequestV1 `protobuf:"bytes,1,opt,name=fetcherRequest,proto3,oneof"`
}

type IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_ struct {
	FetchedData *IsoFieldsFromNhtsaFieldsRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*IsoFieldsFromNhtsaFieldsRequestV1_FetcherRequest) isIsoFieldsFromNhtsaFieldsRequestV1_FetcherSpec() {
}

func (*IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_) isIsoFieldsFromNhtsaFieldsRequestV1_FetcherSpec() {
}

type IsoFieldsFromNhtsaFieldsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VehicleType   vin.IsoVehicleTypeV1 `protobuf:"varint,1,opt,name=vehicleType,proto3,enum=data_processing_vin.IsoVehicleTypeV1" json:"vehicleType,omitempty"`
	WeightGroup   vin.IsoWeightGroupV1 `protobuf:"varint,2,opt,name=weightGroup,proto3,enum=data_processing_vin.IsoWeightGroupV1" json:"weightGroup,omitempty"`
	IsProblematic bool                 `protobuf:"varint,3,opt,name=isProblematic,proto3" json:"isProblematic,omitempty"`
}

func (x *IsoFieldsFromNhtsaFieldsV1) Reset() {
	*x = IsoFieldsFromNhtsaFieldsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsoFieldsFromNhtsaFieldsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsoFieldsFromNhtsaFieldsV1) ProtoMessage() {}

func (x *IsoFieldsFromNhtsaFieldsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsoFieldsFromNhtsaFieldsV1.ProtoReflect.Descriptor instead.
func (*IsoFieldsFromNhtsaFieldsV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{5}
}

func (x *IsoFieldsFromNhtsaFieldsV1) GetVehicleType() vin.IsoVehicleTypeV1 {
	if x != nil {
		return x.VehicleType
	}
	return vin.IsoVehicleTypeV1(0)
}

func (x *IsoFieldsFromNhtsaFieldsV1) GetWeightGroup() vin.IsoWeightGroupV1 {
	if x != nil {
		return x.WeightGroup
	}
	return vin.IsoWeightGroupV1(0)
}

func (x *IsoFieldsFromNhtsaFieldsV1) GetIsProblematic() bool {
	if x != nil {
		return x.IsProblematic
	}
	return false
}

type NFCreditFeaturesRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OwnerDOB           *timestamppb.Timestamp              `protobuf:"bytes,1,opt,name=ownerDOB,proto3" json:"ownerDOB,omitempty"`
	OwnerName          string                              `protobuf:"bytes,2,opt,name=ownerName,proto3" json:"ownerName,omitempty"`
	NationalCreditFile *data_fetching.NationalCreditFileV1 `protobuf:"bytes,3,opt,name=nationalCreditFile,proto3" json:"nationalCreditFile,omitempty"`
}

func (x *NFCreditFeaturesRequestV1) Reset() {
	*x = NFCreditFeaturesRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFCreditFeaturesRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFCreditFeaturesRequestV1) ProtoMessage() {}

func (x *NFCreditFeaturesRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFCreditFeaturesRequestV1.ProtoReflect.Descriptor instead.
func (*NFCreditFeaturesRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{6}
}

func (x *NFCreditFeaturesRequestV1) GetOwnerDOB() *timestamppb.Timestamp {
	if x != nil {
		return x.OwnerDOB
	}
	return nil
}

func (x *NFCreditFeaturesRequestV1) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *NFCreditFeaturesRequestV1) GetNationalCreditFile() *data_fetching.NationalCreditFileV1 {
	if x != nil {
		return x.NationalCreditFile
	}
	return nil
}

type NFCreditFeaturesV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateOfOldestTradeline                                       *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=dateOfOldestTradeline,proto3" json:"dateOfOldestTradeline,omitempty"`
	OwnerAgeAtEarliestTradelineInYears                          int32                  `protobuf:"varint,2,opt,name=ownerAgeAtEarliestTradelineInYears,proto3" json:"ownerAgeAtEarliestTradelineInYears,omitempty"`
	DateOfMostRecentAutoTrade                                   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dateOfMostRecentAutoTrade,proto3" json:"dateOfMostRecentAutoTrade,omitempty"`
	NumberOfCurrentSatisfactoryTrades                           int32                  `protobuf:"varint,4,opt,name=numberOfCurrentSatisfactoryTrades,proto3" json:"numberOfCurrentSatisfactoryTrades,omitempty"`
	NumberOfTradesWithDelinquency                               int32                  `protobuf:"varint,5,opt,name=numberOfTradesWithDelinquency,proto3" json:"numberOfTradesWithDelinquency,omitempty"`
	MonthsSinceMostRecentWriteOff                               int32                  `protobuf:"varint,6,opt,name=monthsSinceMostRecentWriteOff,proto3" json:"monthsSinceMostRecentWriteOff,omitempty"`
	NumberOfDerogatoryInstallmentTrades                         int32                  `protobuf:"varint,7,opt,name=numberOfDerogatoryInstallmentTrades,proto3" json:"numberOfDerogatoryInstallmentTrades,omitempty"`
	RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades float32                `protobuf:"fixed32,8,opt,name=ratioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades,proto3" json:"ratioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades,omitempty"`
	RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades  float32                `protobuf:"fixed32,9,opt,name=ratioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades,proto3" json:"ratioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades,omitempty"`
	NumberOfTradeLinesOpenedInLast12Months                      int32                  `protobuf:"varint,10,opt,name=numberOfTradeLinesOpenedInLast12Months,proto3" json:"numberOfTradeLinesOpenedInLast12Months,omitempty"`
	MonthsSinceMostRecentBankRevolvingTradeOpened               int32                  `protobuf:"varint,11,opt,name=monthsSinceMostRecentBankRevolvingTradeOpened,proto3" json:"monthsSinceMostRecentBankRevolvingTradeOpened,omitempty"`
	NumberOfNonInsuranceInquiries                               int32                  `protobuf:"varint,12,opt,name=numberOfNonInsuranceInquiries,proto3" json:"numberOfNonInsuranceInquiries,omitempty"`
	DateOfEarliestTradeline                                     *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=dateOfEarliestTradeline,proto3" json:"dateOfEarliestTradeline,omitempty"`
	AgeOfOldestTradelineInMonths                                int32                  `protobuf:"varint,14,opt,name=ageOfOldestTradelineInMonths,proto3" json:"ageOfOldestTradelineInMonths,omitempty"`
	AgeOfMostRecentAutoTradeInMonths                            int32                  `protobuf:"varint,15,opt,name=ageOfMostRecentAutoTradeInMonths,proto3" json:"ageOfMostRecentAutoTradeInMonths,omitempty"`
	NoHit                                                       bool                   `protobuf:"varint,16,opt,name=noHit,proto3" json:"noHit,omitempty"`
	ThinFile                                                    bool                   `protobuf:"varint,17,opt,name=thinFile,proto3" json:"thinFile,omitempty"`
	IsOwnerAgeAllowed                                           bool                   `protobuf:"varint,18,opt,name=isOwnerAgeAllowed,proto3" json:"isOwnerAgeAllowed,omitempty"`
	DateOfCreditReportRun                                       *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=dateOfCreditReportRun,proto3,oneof" json:"dateOfCreditReportRun,omitempty"`
	OwnerAgeAtCreditReportRunInYears                            *int32                 `protobuf:"varint,20,opt,name=ownerAgeAtCreditReportRunInYears,proto3,oneof" json:"ownerAgeAtCreditReportRunInYears,omitempty"`
}

func (x *NFCreditFeaturesV1) Reset() {
	*x = NFCreditFeaturesV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFCreditFeaturesV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFCreditFeaturesV1) ProtoMessage() {}

func (x *NFCreditFeaturesV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFCreditFeaturesV1.ProtoReflect.Descriptor instead.
func (*NFCreditFeaturesV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{7}
}

func (x *NFCreditFeaturesV1) GetDateOfOldestTradeline() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfOldestTradeline
	}
	return nil
}

func (x *NFCreditFeaturesV1) GetOwnerAgeAtEarliestTradelineInYears() int32 {
	if x != nil {
		return x.OwnerAgeAtEarliestTradelineInYears
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetDateOfMostRecentAutoTrade() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfMostRecentAutoTrade
	}
	return nil
}

func (x *NFCreditFeaturesV1) GetNumberOfCurrentSatisfactoryTrades() int32 {
	if x != nil {
		return x.NumberOfCurrentSatisfactoryTrades
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetNumberOfTradesWithDelinquency() int32 {
	if x != nil {
		return x.NumberOfTradesWithDelinquency
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetMonthsSinceMostRecentWriteOff() int32 {
	if x != nil {
		return x.MonthsSinceMostRecentWriteOff
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetNumberOfDerogatoryInstallmentTrades() int32 {
	if x != nil {
		return x.NumberOfDerogatoryInstallmentTrades
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetRatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades() float32 {
	if x != nil {
		return x.RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetRatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades() float32 {
	if x != nil {
		return x.RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetNumberOfTradeLinesOpenedInLast12Months() int32 {
	if x != nil {
		return x.NumberOfTradeLinesOpenedInLast12Months
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetMonthsSinceMostRecentBankRevolvingTradeOpened() int32 {
	if x != nil {
		return x.MonthsSinceMostRecentBankRevolvingTradeOpened
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetNumberOfNonInsuranceInquiries() int32 {
	if x != nil {
		return x.NumberOfNonInsuranceInquiries
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetDateOfEarliestTradeline() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfEarliestTradeline
	}
	return nil
}

func (x *NFCreditFeaturesV1) GetAgeOfOldestTradelineInMonths() int32 {
	if x != nil {
		return x.AgeOfOldestTradelineInMonths
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetAgeOfMostRecentAutoTradeInMonths() int32 {
	if x != nil {
		return x.AgeOfMostRecentAutoTradeInMonths
	}
	return 0
}

func (x *NFCreditFeaturesV1) GetNoHit() bool {
	if x != nil {
		return x.NoHit
	}
	return false
}

func (x *NFCreditFeaturesV1) GetThinFile() bool {
	if x != nil {
		return x.ThinFile
	}
	return false
}

func (x *NFCreditFeaturesV1) GetIsOwnerAgeAllowed() bool {
	if x != nil {
		return x.IsOwnerAgeAllowed
	}
	return false
}

func (x *NFCreditFeaturesV1) GetDateOfCreditReportRun() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfCreditReportRun
	}
	return nil
}

func (x *NFCreditFeaturesV1) GetOwnerAgeAtCreditReportRunInYears() int32 {
	if x != nil && x.OwnerAgeAtCreditReportRunInYears != nil {
		return *x.OwnerAgeAtCreditReportRunInYears
	}
	return 0
}

type YearsInBusinessFromAuthorityHistoryRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest
	//	*YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_
	FetcherSpec   isYearsInBusinessFromAuthorityHistoryRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	EffectiveDate *timestamppb.Timestamp                                     `protobuf:"bytes,3,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1) Reset() {
	*x = YearsInBusinessFromAuthorityHistoryRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearsInBusinessFromAuthorityHistoryRequestV1) ProtoMessage() {}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearsInBusinessFromAuthorityHistoryRequestV1.ProtoReflect.Descriptor instead.
func (*YearsInBusinessFromAuthorityHistoryRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{8}
}

func (m *YearsInBusinessFromAuthorityHistoryRequestV1) GetFetcherSpec() isYearsInBusinessFromAuthorityHistoryRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1) GetFetcherRequest() *data_fetching.GrantedAuthorityHistoryRequestV1 {
	if x, ok := x.GetFetcherSpec().(*YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest); ok {
		return x.FetcherRequest
	}
	return nil
}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1) GetFetchedData() *YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type isYearsInBusinessFromAuthorityHistoryRequestV1_FetcherSpec interface {
	isYearsInBusinessFromAuthorityHistoryRequestV1_FetcherSpec()
}

type YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest struct {
	FetcherRequest *data_fetching.GrantedAuthorityHistoryRequestV1 `protobuf:"bytes,1,opt,name=fetcherRequest,proto3,oneof"`
}

type YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_ struct {
	FetchedData *YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest) isYearsInBusinessFromAuthorityHistoryRequestV1_FetcherSpec() {
}

func (*YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_) isYearsInBusinessFromAuthorityHistoryRequestV1_FetcherSpec() {
}

type YearsInBusinessFromAuthorityHistoryV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Years float64 `protobuf:"fixed64,1,opt,name=years,proto3" json:"years,omitempty"`
}

func (x *YearsInBusinessFromAuthorityHistoryV1) Reset() {
	*x = YearsInBusinessFromAuthorityHistoryV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearsInBusinessFromAuthorityHistoryV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearsInBusinessFromAuthorityHistoryV1) ProtoMessage() {}

func (x *YearsInBusinessFromAuthorityHistoryV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearsInBusinessFromAuthorityHistoryV1.ProtoReflect.Descriptor instead.
func (*YearsInBusinessFromAuthorityHistoryV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{9}
}

func (x *YearsInBusinessFromAuthorityHistoryV1) GetYears() float64 {
	if x != nil {
		return x.Years
	}
	return 0
}

type NFCreditFeaturesRequestV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*NFCreditFeaturesRequestV2_FetcherRequest
	//	*NFCreditFeaturesRequestV2_FetchedData_
	FetcherSpec isNFCreditFeaturesRequestV2_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	OwnerDOB    *timestamppb.Timestamp                  `protobuf:"bytes,3,opt,name=ownerDOB,proto3" json:"ownerDOB,omitempty"`
	OwnerName   string                                  `protobuf:"bytes,4,opt,name=ownerName,proto3" json:"ownerName,omitempty"`
	Address     *data_fetching.AddressV1                `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	SsnLastFour *string                                 `protobuf:"bytes,6,opt,name=ssnLastFour,proto3,oneof" json:"ssnLastFour,omitempty"`
}

func (x *NFCreditFeaturesRequestV2) Reset() {
	*x = NFCreditFeaturesRequestV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFCreditFeaturesRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFCreditFeaturesRequestV2) ProtoMessage() {}

func (x *NFCreditFeaturesRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFCreditFeaturesRequestV2.ProtoReflect.Descriptor instead.
func (*NFCreditFeaturesRequestV2) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{10}
}

func (m *NFCreditFeaturesRequestV2) GetFetcherSpec() isNFCreditFeaturesRequestV2_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *NFCreditFeaturesRequestV2) GetFetcherRequest() *data_fetching.NationalCreditFileRequestV1 {
	if x, ok := x.GetFetcherSpec().(*NFCreditFeaturesRequestV2_FetcherRequest); ok {
		return x.FetcherRequest
	}
	return nil
}

func (x *NFCreditFeaturesRequestV2) GetFetchedData() *NFCreditFeaturesRequestV2_FetchedData {
	if x, ok := x.GetFetcherSpec().(*NFCreditFeaturesRequestV2_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *NFCreditFeaturesRequestV2) GetOwnerDOB() *timestamppb.Timestamp {
	if x != nil {
		return x.OwnerDOB
	}
	return nil
}

func (x *NFCreditFeaturesRequestV2) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *NFCreditFeaturesRequestV2) GetAddress() *data_fetching.AddressV1 {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *NFCreditFeaturesRequestV2) GetSsnLastFour() string {
	if x != nil && x.SsnLastFour != nil {
		return *x.SsnLastFour
	}
	return ""
}

type isNFCreditFeaturesRequestV2_FetcherSpec interface {
	isNFCreditFeaturesRequestV2_FetcherSpec()
}

type NFCreditFeaturesRequestV2_FetcherRequest struct {
	FetcherRequest *data_fetching.NationalCreditFileRequestV1 `protobuf:"bytes,1,opt,name=fetcherRequest,proto3,oneof"`
}

type NFCreditFeaturesRequestV2_FetchedData_ struct {
	FetchedData *NFCreditFeaturesRequestV2_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*NFCreditFeaturesRequestV2_FetcherRequest) isNFCreditFeaturesRequestV2_FetcherSpec() {}

func (*NFCreditFeaturesRequestV2_FetchedData_) isNFCreditFeaturesRequestV2_FetcherSpec() {}

type ContinuousCoverageYearsRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*ContinuousCoverageYearsRequestV1_FetcherRequests_
	//	*ContinuousCoverageYearsRequestV1_FetchedData_
	FetcherSpec   isContinuousCoverageYearsRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	EffectiveDate *timestamppb.Timestamp                         `protobuf:"bytes,3,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *ContinuousCoverageYearsRequestV1) Reset() {
	*x = ContinuousCoverageYearsRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinuousCoverageYearsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinuousCoverageYearsRequestV1) ProtoMessage() {}

func (x *ContinuousCoverageYearsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinuousCoverageYearsRequestV1.ProtoReflect.Descriptor instead.
func (*ContinuousCoverageYearsRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{11}
}

func (m *ContinuousCoverageYearsRequestV1) GetFetcherSpec() isContinuousCoverageYearsRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *ContinuousCoverageYearsRequestV1) GetFetcherRequests() *ContinuousCoverageYearsRequestV1_FetcherRequests {
	if x, ok := x.GetFetcherSpec().(*ContinuousCoverageYearsRequestV1_FetcherRequests_); ok {
		return x.FetcherRequests
	}
	return nil
}

func (x *ContinuousCoverageYearsRequestV1) GetFetchedData() *ContinuousCoverageYearsRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*ContinuousCoverageYearsRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *ContinuousCoverageYearsRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type isContinuousCoverageYearsRequestV1_FetcherSpec interface {
	isContinuousCoverageYearsRequestV1_FetcherSpec()
}

type ContinuousCoverageYearsRequestV1_FetcherRequests_ struct {
	FetcherRequests *ContinuousCoverageYearsRequestV1_FetcherRequests `protobuf:"bytes,1,opt,name=fetcherRequests,proto3,oneof"`
}

type ContinuousCoverageYearsRequestV1_FetchedData_ struct {
	FetchedData *ContinuousCoverageYearsRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*ContinuousCoverageYearsRequestV1_FetcherRequests_) isContinuousCoverageYearsRequestV1_FetcherSpec() {
}

func (*ContinuousCoverageYearsRequestV1_FetchedData_) isContinuousCoverageYearsRequestV1_FetcherSpec() {
}

type ContinuousCoverageYearsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Years float64 `protobuf:"fixed64,1,opt,name=years,proto3" json:"years,omitempty"`
}

func (x *ContinuousCoverageYearsV1) Reset() {
	*x = ContinuousCoverageYearsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinuousCoverageYearsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinuousCoverageYearsV1) ProtoMessage() {}

func (x *ContinuousCoverageYearsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinuousCoverageYearsV1.ProtoReflect.Descriptor instead.
func (*ContinuousCoverageYearsV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{12}
}

func (x *ContinuousCoverageYearsV1) GetYears() float64 {
	if x != nil {
		return x.Years
	}
	return 0
}

type InsuranceHistoryYearsInBusinessRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_
	//	*InsuranceHistoryYearsInBusinessRequestV1_FetchedData_
	FetcherSpec   isInsuranceHistoryYearsInBusinessRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	EffectiveDate *timestamppb.Timestamp                                 `protobuf:"bytes,3,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *InsuranceHistoryYearsInBusinessRequestV1) Reset() {
	*x = InsuranceHistoryYearsInBusinessRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuranceHistoryYearsInBusinessRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuranceHistoryYearsInBusinessRequestV1) ProtoMessage() {}

func (x *InsuranceHistoryYearsInBusinessRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuranceHistoryYearsInBusinessRequestV1.ProtoReflect.Descriptor instead.
func (*InsuranceHistoryYearsInBusinessRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{13}
}

func (m *InsuranceHistoryYearsInBusinessRequestV1) GetFetcherSpec() isInsuranceHistoryYearsInBusinessRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *InsuranceHistoryYearsInBusinessRequestV1) GetFetcherRequests() *InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests {
	if x, ok := x.GetFetcherSpec().(*InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_); ok {
		return x.FetcherRequests
	}
	return nil
}

func (x *InsuranceHistoryYearsInBusinessRequestV1) GetFetchedData() *InsuranceHistoryYearsInBusinessRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*InsuranceHistoryYearsInBusinessRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *InsuranceHistoryYearsInBusinessRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type isInsuranceHistoryYearsInBusinessRequestV1_FetcherSpec interface {
	isInsuranceHistoryYearsInBusinessRequestV1_FetcherSpec()
}

type InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_ struct {
	FetcherRequests *InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests `protobuf:"bytes,1,opt,name=fetcherRequests,proto3,oneof"`
}

type InsuranceHistoryYearsInBusinessRequestV1_FetchedData_ struct {
	FetchedData *InsuranceHistoryYearsInBusinessRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_) isInsuranceHistoryYearsInBusinessRequestV1_FetcherSpec() {
}

func (*InsuranceHistoryYearsInBusinessRequestV1_FetchedData_) isInsuranceHistoryYearsInBusinessRequestV1_FetcherSpec() {
}

type InsuranceHistoryYearsInBusinessV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Years float64 `protobuf:"fixed64,1,opt,name=years,proto3" json:"years,omitempty"`
}

func (x *InsuranceHistoryYearsInBusinessV1) Reset() {
	*x = InsuranceHistoryYearsInBusinessV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuranceHistoryYearsInBusinessV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuranceHistoryYearsInBusinessV1) ProtoMessage() {}

func (x *InsuranceHistoryYearsInBusinessV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuranceHistoryYearsInBusinessV1.ProtoReflect.Descriptor instead.
func (*InsuranceHistoryYearsInBusinessV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{14}
}

func (x *InsuranceHistoryYearsInBusinessV1) GetYears() float64 {
	if x != nil {
		return x.Years
	}
	return 0
}

type LatestValidRatingTierRecordRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*data_fetching.RatingTierRecordV1 `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *LatestValidRatingTierRecordRequestV1) Reset() {
	*x = LatestValidRatingTierRecordRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LatestValidRatingTierRecordRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LatestValidRatingTierRecordRequestV1) ProtoMessage() {}

func (x *LatestValidRatingTierRecordRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LatestValidRatingTierRecordRequestV1.ProtoReflect.Descriptor instead.
func (*LatestValidRatingTierRecordRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{15}
}

func (x *LatestValidRatingTierRecordRequestV1) GetRecords() []*data_fetching.RatingTierRecordV1 {
	if x != nil {
		return x.Records
	}
	return nil
}

type NFInspectionCountRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*NFInspectionCountRequestV1_FetcherRequest
	//	*NFInspectionCountRequestV1_FetchedData_
	FetcherSpec   isNFInspectionCountRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	EffectiveDate *timestamppb.Timestamp                   `protobuf:"bytes,3,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *NFInspectionCountRequestV1) Reset() {
	*x = NFInspectionCountRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFInspectionCountRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFInspectionCountRequestV1) ProtoMessage() {}

func (x *NFInspectionCountRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFInspectionCountRequestV1.ProtoReflect.Descriptor instead.
func (*NFInspectionCountRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{16}
}

func (m *NFInspectionCountRequestV1) GetFetcherSpec() isNFInspectionCountRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *NFInspectionCountRequestV1) GetFetcherRequest() *data_fetching.FMCSAInspectionRecordsRequestV1 {
	if x, ok := x.GetFetcherSpec().(*NFInspectionCountRequestV1_FetcherRequest); ok {
		return x.FetcherRequest
	}
	return nil
}

func (x *NFInspectionCountRequestV1) GetFetchedData() *NFInspectionCountRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*NFInspectionCountRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *NFInspectionCountRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type isNFInspectionCountRequestV1_FetcherSpec interface {
	isNFInspectionCountRequestV1_FetcherSpec()
}

type NFInspectionCountRequestV1_FetcherRequest struct {
	FetcherRequest *data_fetching.FMCSAInspectionRecordsRequestV1 `protobuf:"bytes,1,opt,name=fetcherRequest,proto3,oneof"`
}

type NFInspectionCountRequestV1_FetchedData_ struct {
	FetchedData *NFInspectionCountRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*NFInspectionCountRequestV1_FetcherRequest) isNFInspectionCountRequestV1_FetcherSpec() {}

func (*NFInspectionCountRequestV1_FetchedData_) isNFInspectionCountRequestV1_FetcherSpec() {}

type NFInspectionCountV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *NFInspectionCountV1) Reset() {
	*x = NFInspectionCountV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFInspectionCountV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFInspectionCountV1) ProtoMessage() {}

func (x *NFInspectionCountV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFInspectionCountV1.ProtoReflect.Descriptor instead.
func (*NFInspectionCountV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{17}
}

func (x *NFInspectionCountV1) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type NFViolationsMetadataRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*NFViolationsMetadataRequestV1_FetcherRequest
	//	*NFViolationsMetadataRequestV1_FetchedData_
	FetcherSpec   isNFViolationsMetadataRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	EffectiveDate *timestamppb.Timestamp                      `protobuf:"bytes,3,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *NFViolationsMetadataRequestV1) Reset() {
	*x = NFViolationsMetadataRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFViolationsMetadataRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFViolationsMetadataRequestV1) ProtoMessage() {}

func (x *NFViolationsMetadataRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFViolationsMetadataRequestV1.ProtoReflect.Descriptor instead.
func (*NFViolationsMetadataRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{18}
}

func (m *NFViolationsMetadataRequestV1) GetFetcherSpec() isNFViolationsMetadataRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *NFViolationsMetadataRequestV1) GetFetcherRequest() *data_fetching.FMCSAViolationRecordsRequestV1 {
	if x, ok := x.GetFetcherSpec().(*NFViolationsMetadataRequestV1_FetcherRequest); ok {
		return x.FetcherRequest
	}
	return nil
}

func (x *NFViolationsMetadataRequestV1) GetFetchedData() *NFViolationsMetadataRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*NFViolationsMetadataRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *NFViolationsMetadataRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type isNFViolationsMetadataRequestV1_FetcherSpec interface {
	isNFViolationsMetadataRequestV1_FetcherSpec()
}

type NFViolationsMetadataRequestV1_FetcherRequest struct {
	FetcherRequest *data_fetching.FMCSAViolationRecordsRequestV1 `protobuf:"bytes,1,opt,name=fetcherRequest,proto3,oneof"`
}

type NFViolationsMetadataRequestV1_FetchedData_ struct {
	FetchedData *NFViolationsMetadataRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*NFViolationsMetadataRequestV1_FetcherRequest) isNFViolationsMetadataRequestV1_FetcherSpec() {}

func (*NFViolationsMetadataRequestV1_FetchedData_) isNFViolationsMetadataRequestV1_FetcherSpec() {}

type NFViolationsMetadataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OosViolations        int64 `protobuf:"varint,1,opt,name=oosViolations,proto3" json:"oosViolations,omitempty"`
	OosSeverityWeight    int64 `protobuf:"varint,2,opt,name=oosSeverityWeight,proto3" json:"oosSeverityWeight,omitempty"`
	NonoosViolations     int64 `protobuf:"varint,3,opt,name=nonoosViolations,proto3" json:"nonoosViolations,omitempty"`
	NosoosSeverityWeight int64 `protobuf:"varint,4,opt,name=nosoosSeverityWeight,proto3" json:"nosoosSeverityWeight,omitempty"`
}

func (x *NFViolationsMetadataV1) Reset() {
	*x = NFViolationsMetadataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFViolationsMetadataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFViolationsMetadataV1) ProtoMessage() {}

func (x *NFViolationsMetadataV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFViolationsMetadataV1.ProtoReflect.Descriptor instead.
func (*NFViolationsMetadataV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{19}
}

func (x *NFViolationsMetadataV1) GetOosViolations() int64 {
	if x != nil {
		return x.OosViolations
	}
	return 0
}

func (x *NFViolationsMetadataV1) GetOosSeverityWeight() int64 {
	if x != nil {
		return x.OosSeverityWeight
	}
	return 0
}

func (x *NFViolationsMetadataV1) GetNonoosViolations() int64 {
	if x != nil {
		return x.NonoosViolations
	}
	return 0
}

func (x *NFViolationsMetadataV1) GetNosoosSeverityWeight() int64 {
	if x != nil {
		return x.NosoosSeverityWeight
	}
	return 0
}

type RetainedYearsRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*RetainedYearsRequestV1_FetcherRequest
	//	*RetainedYearsRequestV1_FetchedData_
	FetcherSpec   isRetainedYearsRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	EffectiveDate *timestamppb.Timestamp               `protobuf:"bytes,3,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *RetainedYearsRequestV1) Reset() {
	*x = RetainedYearsRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetainedYearsRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetainedYearsRequestV1) ProtoMessage() {}

func (x *RetainedYearsRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetainedYearsRequestV1.ProtoReflect.Descriptor instead.
func (*RetainedYearsRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{20}
}

func (m *RetainedYearsRequestV1) GetFetcherSpec() isRetainedYearsRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *RetainedYearsRequestV1) GetFetcherRequest() *data_fetching.GetNirvanaPoliciesRequestV1 {
	if x, ok := x.GetFetcherSpec().(*RetainedYearsRequestV1_FetcherRequest); ok {
		return x.FetcherRequest
	}
	return nil
}

func (x *RetainedYearsRequestV1) GetFetchedData() *RetainedYearsRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*RetainedYearsRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *RetainedYearsRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type isRetainedYearsRequestV1_FetcherSpec interface {
	isRetainedYearsRequestV1_FetcherSpec()
}

type RetainedYearsRequestV1_FetcherRequest struct {
	FetcherRequest *data_fetching.GetNirvanaPoliciesRequestV1 `protobuf:"bytes,1,opt,name=fetcherRequest,proto3,oneof"`
}

type RetainedYearsRequestV1_FetchedData_ struct {
	FetchedData *RetainedYearsRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*RetainedYearsRequestV1_FetcherRequest) isRetainedYearsRequestV1_FetcherSpec() {}

func (*RetainedYearsRequestV1_FetchedData_) isRetainedYearsRequestV1_FetcherSpec() {}

type RetainedYearsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Years int32 `protobuf:"varint,1,opt,name=years,proto3" json:"years,omitempty"`
}

func (x *RetainedYearsV1) Reset() {
	*x = RetainedYearsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetainedYearsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetainedYearsV1) ProtoMessage() {}

func (x *RetainedYearsV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetainedYearsV1.ProtoReflect.Descriptor instead.
func (*RetainedYearsV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{21}
}

func (x *RetainedYearsV1) GetYears() int32 {
	if x != nil {
		return x.Years
	}
	return 0
}

type PriorCarrierYearsRetainedRequestV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FetcherSpec:
	//
	//	*PriorCarrierYearsRetainedRequestV1_FetcherRequests_
	//	*PriorCarrierYearsRetainedRequestV1_FetchedData_
	FetcherSpec   isPriorCarrierYearsRetainedRequestV1_FetcherSpec `protobuf_oneof:"fetcherSpec"`
	EffectiveDate *timestamppb.Timestamp                           `protobuf:"bytes,3,opt,name=effectiveDate,proto3" json:"effectiveDate,omitempty"`
}

func (x *PriorCarrierYearsRetainedRequestV1) Reset() {
	*x = PriorCarrierYearsRetainedRequestV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriorCarrierYearsRetainedRequestV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriorCarrierYearsRetainedRequestV1) ProtoMessage() {}

func (x *PriorCarrierYearsRetainedRequestV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriorCarrierYearsRetainedRequestV1.ProtoReflect.Descriptor instead.
func (*PriorCarrierYearsRetainedRequestV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{22}
}

func (m *PriorCarrierYearsRetainedRequestV1) GetFetcherSpec() isPriorCarrierYearsRetainedRequestV1_FetcherSpec {
	if m != nil {
		return m.FetcherSpec
	}
	return nil
}

func (x *PriorCarrierYearsRetainedRequestV1) GetFetcherRequests() *PriorCarrierYearsRetainedRequestV1_FetcherRequests {
	if x, ok := x.GetFetcherSpec().(*PriorCarrierYearsRetainedRequestV1_FetcherRequests_); ok {
		return x.FetcherRequests
	}
	return nil
}

func (x *PriorCarrierYearsRetainedRequestV1) GetFetchedData() *PriorCarrierYearsRetainedRequestV1_FetchedData {
	if x, ok := x.GetFetcherSpec().(*PriorCarrierYearsRetainedRequestV1_FetchedData_); ok {
		return x.FetchedData
	}
	return nil
}

func (x *PriorCarrierYearsRetainedRequestV1) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

type isPriorCarrierYearsRetainedRequestV1_FetcherSpec interface {
	isPriorCarrierYearsRetainedRequestV1_FetcherSpec()
}

type PriorCarrierYearsRetainedRequestV1_FetcherRequests_ struct {
	FetcherRequests *PriorCarrierYearsRetainedRequestV1_FetcherRequests `protobuf:"bytes,1,opt,name=fetcherRequests,proto3,oneof"`
}

type PriorCarrierYearsRetainedRequestV1_FetchedData_ struct {
	FetchedData *PriorCarrierYearsRetainedRequestV1_FetchedData `protobuf:"bytes,2,opt,name=fetchedData,proto3,oneof"`
}

func (*PriorCarrierYearsRetainedRequestV1_FetcherRequests_) isPriorCarrierYearsRetainedRequestV1_FetcherSpec() {
}

func (*PriorCarrierYearsRetainedRequestV1_FetchedData_) isPriorCarrierYearsRetainedRequestV1_FetcherSpec() {
}

type PriorCarrierYearsRetainedV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Years int32 `protobuf:"varint,1,opt,name=years,proto3" json:"years,omitempty"`
}

func (x *PriorCarrierYearsRetainedV1) Reset() {
	*x = PriorCarrierYearsRetainedV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriorCarrierYearsRetainedV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriorCarrierYearsRetainedV1) ProtoMessage() {}

func (x *PriorCarrierYearsRetainedV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriorCarrierYearsRetainedV1.ProtoReflect.Descriptor instead.
func (*PriorCarrierYearsRetainedV1) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{23}
}

func (x *PriorCarrierYearsRetainedV1) GetYears() int32 {
	if x != nil {
		return x.Years
	}
	return 0
}

type FleetMovingViolationCountRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Report *data_fetching.MVRReportV1 `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
}

func (x *FleetMovingViolationCountRequestV1_FetchedData) Reset() {
	*x = FleetMovingViolationCountRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetMovingViolationCountRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetMovingViolationCountRequestV1_FetchedData) ProtoMessage() {}

func (x *FleetMovingViolationCountRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetMovingViolationCountRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*FleetMovingViolationCountRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{0, 0}
}

func (x *FleetMovingViolationCountRequestV1_FetchedData) GetReport() *data_fetching.MVRReportV1 {
	if x != nil {
		return x.Report
	}
	return nil
}

type FleetModifiedMVRScoreRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Report *data_fetching.MVRReportV1 `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
}

func (x *FleetModifiedMVRScoreRequestV1_FetchedData) Reset() {
	*x = FleetModifiedMVRScoreRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetModifiedMVRScoreRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetModifiedMVRScoreRequestV1_FetchedData) ProtoMessage() {}

func (x *FleetModifiedMVRScoreRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetModifiedMVRScoreRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*FleetModifiedMVRScoreRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{2, 0}
}

func (x *FleetModifiedMVRScoreRequestV1_FetchedData) GetReport() *data_fetching.MVRReportV1 {
	if x != nil {
		return x.Report
	}
	return nil
}

type IsoFieldsFromNhtsaFieldsRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VehicleType        data_fetching.VehicleTypeV1 `protobuf:"varint,1,opt,name=vehicleType,proto3,enum=data_fetching.VehicleTypeV1" json:"vehicleType,omitempty"`
	VehicleBodyClass   string                      `protobuf:"bytes,2,opt,name=vehicleBodyClass,proto3" json:"vehicleBodyClass,omitempty"`
	VehicleWeightClass data_fetching.WeightClassV1 `protobuf:"varint,3,opt,name=vehicleWeightClass,proto3,enum=data_fetching.WeightClassV1" json:"vehicleWeightClass,omitempty"`
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1_FetchedData) Reset() {
	*x = IsoFieldsFromNhtsaFieldsRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsoFieldsFromNhtsaFieldsRequestV1_FetchedData) ProtoMessage() {}

func (x *IsoFieldsFromNhtsaFieldsRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsoFieldsFromNhtsaFieldsRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*IsoFieldsFromNhtsaFieldsRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1_FetchedData) GetVehicleType() data_fetching.VehicleTypeV1 {
	if x != nil {
		return x.VehicleType
	}
	return data_fetching.VehicleTypeV1(0)
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1_FetchedData) GetVehicleBodyClass() string {
	if x != nil {
		return x.VehicleBodyClass
	}
	return ""
}

func (x *IsoFieldsFromNhtsaFieldsRequestV1_FetchedData) GetVehicleWeightClass() data_fetching.WeightClassV1 {
	if x != nil {
		return x.VehicleWeightClass
	}
	return data_fetching.WeightClassV1(0)
}

type YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthorityHistoryRecords []*data_fetching.AuthorityHistoryRecordV1 `protobuf:"bytes,1,rep,name=authorityHistoryRecords,proto3" json:"authorityHistoryRecords,omitempty"`
}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData) Reset() {
	*x = YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData) ProtoMessage() {}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData) GetAuthorityHistoryRecords() []*data_fetching.AuthorityHistoryRecordV1 {
	if x != nil {
		return x.AuthorityHistoryRecords
	}
	return nil
}

type NFCreditFeaturesRequestV2_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NationalCreditFile *data_fetching.NationalCreditFileV1 `protobuf:"bytes,1,opt,name=nationalCreditFile,proto3" json:"nationalCreditFile,omitempty"`
}

func (x *NFCreditFeaturesRequestV2_FetchedData) Reset() {
	*x = NFCreditFeaturesRequestV2_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFCreditFeaturesRequestV2_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFCreditFeaturesRequestV2_FetchedData) ProtoMessage() {}

func (x *NFCreditFeaturesRequestV2_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFCreditFeaturesRequestV2_FetchedData.ProtoReflect.Descriptor instead.
func (*NFCreditFeaturesRequestV2_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{10, 0}
}

func (x *NFCreditFeaturesRequestV2_FetchedData) GetNationalCreditFile() *data_fetching.NationalCreditFileV1 {
	if x != nil {
		return x.NationalCreditFile
	}
	return nil
}

type ContinuousCoverageYearsRequestV1_FetcherRequests struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceHistoryRequest         *data_fetching.BIPDInsuranceHistoryRequestV1         `protobuf:"bytes,1,opt,name=insuranceHistoryRequest,proto3" json:"insuranceHistoryRequest,omitempty"`
	ActiveOrPendingInsuranceRequest *data_fetching.BIPDActiveOrPendingInsuranceRequestV1 `protobuf:"bytes,2,opt,name=activeOrPendingInsuranceRequest,proto3" json:"activeOrPendingInsuranceRequest,omitempty"`
}

func (x *ContinuousCoverageYearsRequestV1_FetcherRequests) Reset() {
	*x = ContinuousCoverageYearsRequestV1_FetcherRequests{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinuousCoverageYearsRequestV1_FetcherRequests) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinuousCoverageYearsRequestV1_FetcherRequests) ProtoMessage() {}

func (x *ContinuousCoverageYearsRequestV1_FetcherRequests) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinuousCoverageYearsRequestV1_FetcherRequests.ProtoReflect.Descriptor instead.
func (*ContinuousCoverageYearsRequestV1_FetcherRequests) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{11, 0}
}

func (x *ContinuousCoverageYearsRequestV1_FetcherRequests) GetInsuranceHistoryRequest() *data_fetching.BIPDInsuranceHistoryRequestV1 {
	if x != nil {
		return x.InsuranceHistoryRequest
	}
	return nil
}

func (x *ContinuousCoverageYearsRequestV1_FetcherRequests) GetActiveOrPendingInsuranceRequest() *data_fetching.BIPDActiveOrPendingInsuranceRequestV1 {
	if x != nil {
		return x.ActiveOrPendingInsuranceRequest
	}
	return nil
}

type ContinuousCoverageYearsRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceRecords []*data_fetching.InsuranceRecordV1 `protobuf:"bytes,1,rep,name=insuranceRecords,proto3" json:"insuranceRecords,omitempty"`
}

func (x *ContinuousCoverageYearsRequestV1_FetchedData) Reset() {
	*x = ContinuousCoverageYearsRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinuousCoverageYearsRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinuousCoverageYearsRequestV1_FetchedData) ProtoMessage() {}

func (x *ContinuousCoverageYearsRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinuousCoverageYearsRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*ContinuousCoverageYearsRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{11, 1}
}

func (x *ContinuousCoverageYearsRequestV1_FetchedData) GetInsuranceRecords() []*data_fetching.InsuranceRecordV1 {
	if x != nil {
		return x.InsuranceRecords
	}
	return nil
}

type InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceHistoryRequest         *data_fetching.BIPDInsuranceHistoryRequestV1         `protobuf:"bytes,1,opt,name=insuranceHistoryRequest,proto3" json:"insuranceHistoryRequest,omitempty"`
	ActiveOrPendingInsuranceRequest *data_fetching.BIPDActiveOrPendingInsuranceRequestV1 `protobuf:"bytes,2,opt,name=activeOrPendingInsuranceRequest,proto3" json:"activeOrPendingInsuranceRequest,omitempty"`
}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests) Reset() {
	*x = InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests) ProtoMessage() {}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests.ProtoReflect.Descriptor instead.
func (*InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{13, 0}
}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests) GetInsuranceHistoryRequest() *data_fetching.BIPDInsuranceHistoryRequestV1 {
	if x != nil {
		return x.InsuranceHistoryRequest
	}
	return nil
}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests) GetActiveOrPendingInsuranceRequest() *data_fetching.BIPDActiveOrPendingInsuranceRequestV1 {
	if x != nil {
		return x.ActiveOrPendingInsuranceRequest
	}
	return nil
}

type InsuranceHistoryYearsInBusinessRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceRecords []*data_fetching.InsuranceRecordV1 `protobuf:"bytes,1,rep,name=insuranceRecords,proto3" json:"insuranceRecords,omitempty"`
}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetchedData) Reset() {
	*x = InsuranceHistoryYearsInBusinessRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuranceHistoryYearsInBusinessRequestV1_FetchedData) ProtoMessage() {}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuranceHistoryYearsInBusinessRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*InsuranceHistoryYearsInBusinessRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{13, 1}
}

func (x *InsuranceHistoryYearsInBusinessRequestV1_FetchedData) GetInsuranceRecords() []*data_fetching.InsuranceRecordV1 {
	if x != nil {
		return x.InsuranceRecords
	}
	return nil
}

type NFInspectionCountRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InspectionRecords *data_fetching.FMCSAInspectionRecordsV1 `protobuf:"bytes,1,opt,name=inspectionRecords,proto3" json:"inspectionRecords,omitempty"`
}

func (x *NFInspectionCountRequestV1_FetchedData) Reset() {
	*x = NFInspectionCountRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFInspectionCountRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFInspectionCountRequestV1_FetchedData) ProtoMessage() {}

func (x *NFInspectionCountRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFInspectionCountRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*NFInspectionCountRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{16, 0}
}

func (x *NFInspectionCountRequestV1_FetchedData) GetInspectionRecords() *data_fetching.FMCSAInspectionRecordsV1 {
	if x != nil {
		return x.InspectionRecords
	}
	return nil
}

type NFViolationsMetadataRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViolationRecords *data_fetching.FMCSAViolationRecordsV1 `protobuf:"bytes,1,opt,name=violationRecords,proto3" json:"violationRecords,omitempty"`
}

func (x *NFViolationsMetadataRequestV1_FetchedData) Reset() {
	*x = NFViolationsMetadataRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFViolationsMetadataRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFViolationsMetadataRequestV1_FetchedData) ProtoMessage() {}

func (x *NFViolationsMetadataRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFViolationsMetadataRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*NFViolationsMetadataRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{18, 0}
}

func (x *NFViolationsMetadataRequestV1_FetchedData) GetViolationRecords() *data_fetching.FMCSAViolationRecordsV1 {
	if x != nil {
		return x.ViolationRecords
	}
	return nil
}

type RetainedYearsRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Policies *data_fetching.GetNirvanaPoliciesResponseV1 `protobuf:"bytes,1,opt,name=policies,proto3" json:"policies,omitempty"`
}

func (x *RetainedYearsRequestV1_FetchedData) Reset() {
	*x = RetainedYearsRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetainedYearsRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetainedYearsRequestV1_FetchedData) ProtoMessage() {}

func (x *RetainedYearsRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetainedYearsRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*RetainedYearsRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{20, 0}
}

func (x *RetainedYearsRequestV1_FetchedData) GetPolicies() *data_fetching.GetNirvanaPoliciesResponseV1 {
	if x != nil {
		return x.Policies
	}
	return nil
}

type PriorCarrierYearsRetainedRequestV1_FetcherRequests struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceHistoryRequest         *data_fetching.BIPDInsuranceHistoryRequestV1         `protobuf:"bytes,1,opt,name=insuranceHistoryRequest,proto3" json:"insuranceHistoryRequest,omitempty"`
	ActiveOrPendingInsuranceRequest *data_fetching.BIPDActiveOrPendingInsuranceRequestV1 `protobuf:"bytes,2,opt,name=activeOrPendingInsuranceRequest,proto3" json:"activeOrPendingInsuranceRequest,omitempty"`
}

func (x *PriorCarrierYearsRetainedRequestV1_FetcherRequests) Reset() {
	*x = PriorCarrierYearsRetainedRequestV1_FetcherRequests{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriorCarrierYearsRetainedRequestV1_FetcherRequests) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriorCarrierYearsRetainedRequestV1_FetcherRequests) ProtoMessage() {}

func (x *PriorCarrierYearsRetainedRequestV1_FetcherRequests) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriorCarrierYearsRetainedRequestV1_FetcherRequests.ProtoReflect.Descriptor instead.
func (*PriorCarrierYearsRetainedRequestV1_FetcherRequests) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{22, 0}
}

func (x *PriorCarrierYearsRetainedRequestV1_FetcherRequests) GetInsuranceHistoryRequest() *data_fetching.BIPDInsuranceHistoryRequestV1 {
	if x != nil {
		return x.InsuranceHistoryRequest
	}
	return nil
}

func (x *PriorCarrierYearsRetainedRequestV1_FetcherRequests) GetActiveOrPendingInsuranceRequest() *data_fetching.BIPDActiveOrPendingInsuranceRequestV1 {
	if x != nil {
		return x.ActiveOrPendingInsuranceRequest
	}
	return nil
}

type PriorCarrierYearsRetainedRequestV1_FetchedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceRecords []*data_fetching.InsuranceRecordV1 `protobuf:"bytes,1,rep,name=insuranceRecords,proto3" json:"insuranceRecords,omitempty"`
}

func (x *PriorCarrierYearsRetainedRequestV1_FetchedData) Reset() {
	*x = PriorCarrierYearsRetainedRequestV1_FetchedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriorCarrierYearsRetainedRequestV1_FetchedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriorCarrierYearsRetainedRequestV1_FetchedData) ProtoMessage() {}

func (x *PriorCarrierYearsRetainedRequestV1_FetchedData) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriorCarrierYearsRetainedRequestV1_FetchedData.ProtoReflect.Descriptor instead.
func (*PriorCarrierYearsRetainedRequestV1_FetchedData) Descriptor() ([]byte, []int) {
	return file_data_processing_api_proto_rawDescGZIP(), []int{22, 1}
}

func (x *PriorCarrierYearsRetainedRequestV1_FetchedData) GetInsuranceRecords() []*data_fetching.InsuranceRecordV1 {
	if x != nil {
		return x.InsuranceRecords
	}
	return nil
}

var File_data_processing_api_proto protoreflect.FileDescriptor

var file_data_processing_api_proto_rawDesc = []byte{
	0x0a, 0x19, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x1a, 0x17, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x69, 0x6e, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc9, 0x03, 0x0a, 0x22, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4d,
	0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x4b, 0x0a, 0x0e,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x63, 0x0a, 0x0b, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32,
	0x0a, 0x14, 0x69, 0x73, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x4d, 0x56, 0x43, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73,
	0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x4d, 0x56, 0x43, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x40, 0x0a, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x1a, 0x41, 0x0a, 0x0b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x32, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x2e, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x31, 0x52, 0x06,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x53, 0x70, 0x65, 0x63, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x22, 0x33, 0x0a, 0x1b, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67,
	0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x31,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8d, 0x03, 0x0a, 0x1e, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x56, 0x52, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x4b, 0x0a, 0x0e, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5f, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c,
	0x65, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x56, 0x52, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x2e, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x07, 0x75, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x75, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x1a, 0x41, 0x0a, 0x0b, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x56, 0x52, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x56, 0x31, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x75,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x2f, 0x0a, 0x17, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x56, 0x52, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x56,
	0x31, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xae, 0x03, 0x0a, 0x21, 0x49, 0x73, 0x6f, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x4c, 0x0a,
	0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x62, 0x0a, 0x0b, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x49, 0x73, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x46, 0x72, 0x6f, 0x6d,
	0x4e, 0x68, 0x74, 0x73, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x1a,
	0xc7, 0x01, 0x0a, 0x0b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x3e, 0x0a, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x56, 0x31, 0x52, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2a, 0x0a, 0x10, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x4c, 0x0a, 0x12, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x56, 0x31, 0x52, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x22, 0xd4, 0x01, 0x0a, 0x1a, 0x49, 0x73, 0x6f,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x68, 0x74, 0x73, 0x61, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x56, 0x31, 0x12, 0x47, 0x0a, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x76,
	0x69, 0x6e, 0x2e, 0x49, 0x73, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x56, 0x31, 0x52, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x47, 0x0a, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x6e, 0x2e, 0x49, 0x73, 0x6f, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x31, 0x52, 0x0b, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x73, 0x50,
	0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x22,
	0xc6, 0x01, 0x0a, 0x19, 0x4e, 0x46, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x36, 0x0a,
	0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x44, 0x4f, 0x42, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x44, 0x4f, 0x42, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x53, 0x0a, 0x12, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x56, 0x31, 0x52, 0x12, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x22, 0xf0, 0x0c, 0x0a, 0x12, 0x4e, 0x46, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x56, 0x31, 0x12,
	0x50, 0x0a, 0x15, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x15, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x66, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x4e, 0x0a, 0x22, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x67, 0x65, 0x41, 0x74, 0x45,
	0x61, 0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x22, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x41, 0x67, 0x65, 0x41, 0x74, 0x45, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x59, 0x65, 0x61, 0x72,
	0x73, 0x12, 0x58, 0x0a, 0x19, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x63, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x19, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65,
	0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x12, 0x4c, 0x0a, 0x21, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x61,
	0x74, 0x69, 0x73, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x21, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x44, 0x0a, 0x1d, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44,
	0x65, 0x6c, 0x69, 0x6e, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x1d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73,
	0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x44, 0x0a, 0x1d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x4d, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x53, 0x69,
	0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x4f, 0x66, 0x66, 0x12, 0x50, 0x0a, 0x23, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f,
	0x66, 0x44, 0x65, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x23, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x44, 0x65, 0x72, 0x6f,
	0x67, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x3b, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x54, 0x6f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x48, 0x69, 0x67, 0x68, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e,
	0x67, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x3b, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x54, 0x6f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x48, 0x69,
	0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x6f, 0x6c,
	0x76, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x7e, 0x0a, 0x3a, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x4f, 0x66, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x54, 0x6f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x48, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x4e, 0x6f, 0x6e, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x3a,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x4f, 0x66, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x54, 0x6f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x48, 0x69, 0x67, 0x68, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x4e, 0x6f, 0x6e, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x64, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x26, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x73,
	0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x31, 0x32, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x26, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x4f, 0x70,
	0x65, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x31, 0x32, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x73, 0x12, 0x64, 0x0a, 0x2d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x53, 0x69, 0x6e, 0x63,
	0x65, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4f, 0x70, 0x65,
	0x6e, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x2d, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x73, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74,
	0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x1d, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x4e, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x69, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x1d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x4e, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x69, 0x65, 0x73, 0x12, 0x54,
	0x0a, 0x17, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x45, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x17, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x66, 0x45, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x42, 0x0a, 0x1c, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x4f, 0x6c, 0x64,
	0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1c, 0x61, 0x67, 0x65, 0x4f,
	0x66, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x4a, 0x0a, 0x20, 0x61, 0x67, 0x65, 0x4f,
	0x66, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x20, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x48, 0x69, 0x74, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x6e, 0x6f, 0x48, 0x69, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x68,
	0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x68,
	0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x73, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x41, 0x67, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x69, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x67, 0x65, 0x41, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x12, 0x55, 0x0a, 0x15, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48,
	0x00, 0x52, 0x15, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a, 0x20, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x41, 0x67, 0x65, 0x41, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x73, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x20, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x67,
	0x65, 0x41, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x75, 0x6e, 0x49, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x73, 0x88, 0x01, 0x01, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x42, 0x23, 0x0a, 0x21, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x41, 0x67, 0x65, 0x41, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x73, 0x22, 0xbb, 0x03, 0x0a, 0x2c,
	0x59, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x46,
	0x72, 0x6f, 0x6d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x59, 0x0a, 0x0e,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6d, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x59,
	0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x72,
	0x6f, 0x6d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x1a, 0x70, 0x0a, 0x0b, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x61, 0x0a, 0x17, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56,
	0x31, 0x52, 0x17, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x22, 0x3d, 0x0a, 0x25, 0x59, 0x65, 0x61,
	0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x72, 0x6f, 0x6d,
	0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x56, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73, 0x22, 0x81, 0x04, 0x0a, 0x19, 0x4e, 0x46, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x12, 0x54, 0x0a, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5a, 0x0a, 0x0b,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x44, 0x4f, 0x42, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x44, 0x4f, 0x42,
	0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x56, 0x31, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x25, 0x0a, 0x0b, 0x73, 0x73, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x73, 0x6e, 0x4c, 0x61,
	0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x88, 0x01, 0x01, 0x1a, 0x62, 0x0a, 0x0b, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x53, 0x0a, 0x12, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x31, 0x52, 0x12, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x42, 0x0d, 0x0a,
	0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x73, 0x73, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x22, 0x9e, 0x05, 0x0a,
	0x20, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x12, 0x6d, 0x0a, 0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x59,
	0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x2e, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x48, 0x00, 0x52,
	0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x12, 0x61, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f,
	0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x1a, 0xf9, 0x01, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x66, 0x0a, 0x17, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x52, 0x17, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x7e, 0x0a, 0x1f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x52, 0x1f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x5b, 0x0a, 0x0b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x4c, 0x0a, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x52, 0x10, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x42, 0x0d,
	0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x22, 0x31, 0x0a,
	0x19, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x73, 0x56, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x79, 0x65,
	0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73,
	0x22, 0xb6, 0x05, 0x0a, 0x28, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x59, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x75, 0x0a,
	0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x59, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x12, 0x69, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x59, 0x65, 0x61, 0x72,
	0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x40, 0x0a, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x1a, 0xf9, 0x01, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x66, 0x0a, 0x17, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x52, 0x17, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x7e, 0x0a,
	0x1f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x52, 0x1f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x5b, 0x0a,
	0x0b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4c, 0x0a, 0x10,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x22, 0x39, 0x0a, 0x21, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x59, 0x65, 0x61,
	0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x56, 0x31, 0x12, 0x14,
	0x0a, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x79,
	0x65, 0x61, 0x72, 0x73, 0x22, 0x63, 0x0a, 0x24, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x3b, 0x0a, 0x07,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31,
	0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x8a, 0x03, 0x0a, 0x1a, 0x4e, 0x46,
	0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x58, 0x0a, 0x0e, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x5b, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x49, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x40, 0x0a, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x1a, 0x64, 0x0a, 0x0b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x55, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x56, 0x31, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x22, 0x2b, 0x0a, 0x13, 0x4e, 0x46, 0x49, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x31, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x8c, 0x03, 0x0a, 0x1d, 0x4e, 0x46, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x57, 0x0a, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d,
	0x43, 0x53, 0x41, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x48, 0x00, 0x52, 0x0e,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5e,
	0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40,
	0x0a, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x1a, 0x61, 0x0a, 0x0b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x52, 0x0a, 0x10, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x56,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x56,
	0x31, 0x52, 0x10, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x53, 0x70,
	0x65, 0x63, 0x22, 0xcc, 0x01, 0x0a, 0x16, 0x4e, 0x46, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x24, 0x0a,
	0x0d, 0x6f, 0x6f, 0x73, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f, 0x6f, 0x73, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x6f, 0x6f, 0x73, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69,
	0x74, 0x79, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11,
	0x6f, 0x6f, 0x73, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x6f, 0x6e, 0x6f, 0x6f, 0x73, 0x56, 0x69, 0x6f, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6e, 0x6f, 0x6e,
	0x6f, 0x6f, 0x73, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x0a,
	0x14, 0x6e, 0x6f, 0x73, 0x6f, 0x6f, 0x73, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x6e, 0x6f, 0x73,
	0x6f, 0x6f, 0x73, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x22, 0xf0, 0x02, 0x0a, 0x16, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x59, 0x65,
	0x61, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x12, 0x54, 0x0a, 0x0e,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x48, 0x00, 0x52, 0x0e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x57, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x64, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0d, 0x65,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x1a, 0x56, 0x0a,
	0x0b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x08,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x31, 0x52, 0x08, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x69, 0x65, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x53, 0x70, 0x65, 0x63, 0x22, 0x27, 0x0a, 0x0f, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64,
	0x59, 0x65, 0x61, 0x72, 0x73, 0x56, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73, 0x22, 0xa4, 0x05,
	0x0a, 0x22, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65,
	0x61, 0x72, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x12, 0x6f, 0x0a, 0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x50, 0x72, 0x69, 0x6f, 0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61, 0x72,
	0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x63, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x69,
	0x6f, 0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x2e,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0d, 0x65, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x65,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x1a, 0xf9, 0x01, 0x0a,
	0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x12, 0x66, 0x0a, 0x17, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x52,
	0x17, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x7e, 0x0a, 0x1f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x4f, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x49, 0x50, 0x44, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x72, 0x50, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x52, 0x1f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f,
	0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x5b, 0x0a, 0x0b, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4c, 0x0a, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x56, 0x31, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x53, 0x70, 0x65, 0x63, 0x22, 0x33, 0x0a, 0x1b, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x43, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x56, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73, 0x32, 0x82, 0x0d, 0x0a, 0x09, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x12, 0x85, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x46,
	0x6c, 0x65, 0x65, 0x74, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x31, 0x12, 0x33, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a,
	0x2c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x31, 0x22, 0x00, 0x12,
	0x79, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x4d, 0x56, 0x52, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x31, 0x12, 0x2f, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x56, 0x52,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x28,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x56,
	0x52, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x31, 0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x1d, 0x47,
	0x65, 0x74, 0x49, 0x73, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x4e,
	0x68, 0x74, 0x73, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x56, 0x31, 0x12, 0x32, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x49,
	0x73, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x68, 0x74, 0x73,
	0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x2b, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x49, 0x73, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x46, 0x72, 0x6f, 0x6d,
	0x4e, 0x68, 0x74, 0x73, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12,
	0x6a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x46, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x56, 0x31, 0x12, 0x2a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x31, 0x1a, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12, 0xa3, 0x01, 0x0a, 0x28,
	0x47, 0x65, 0x74, 0x59, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x12, 0x3d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x59, 0x65, 0x61, 0x72, 0x73,
	0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x36, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x59, 0x65, 0x61, 0x72, 0x73, 0x49,
	0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x22,
	0x00, 0x12, 0x6a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x46, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x56, 0x32, 0x12, 0x2a, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x1a, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12, 0x7f, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x73, 0x56, 0x31, 0x12, 0x31, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31,
	0x1a, 0x2a, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x59, 0x65, 0x61, 0x72, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12, 0x9b,
	0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x59, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x31, 0x12, 0x39, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x59, 0x65,
	0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x32, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x59, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x20,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31,
	0x12, 0x35, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x2e, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x21, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69,
	0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x31, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x4e, 0x46, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x56, 0x31, 0x12, 0x2b, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x49, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x31, 0x1a, 0x24, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x31, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x4e, 0x46, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x2e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x46, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x56,
	0x31, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x64, 0x59, 0x65, 0x61, 0x72, 0x73, 0x56, 0x31, 0x12, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x56, 0x31, 0x1a, 0x20, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x59, 0x65, 0x61,
	0x72, 0x73, 0x56, 0x31, 0x22, 0x00, 0x12, 0x85, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x69, 0x6f, 0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52,
	0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x56, 0x31, 0x12, 0x33, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x69, 0x6f,
	0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x31, 0x1a, 0x2c,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61,
	0x72, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x56, 0x31, 0x22, 0x00, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_data_processing_api_proto_rawDescOnce sync.Once
	file_data_processing_api_proto_rawDescData = file_data_processing_api_proto_rawDesc
)

func file_data_processing_api_proto_rawDescGZIP() []byte {
	file_data_processing_api_proto_rawDescOnce.Do(func() {
		file_data_processing_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_data_processing_api_proto_rawDescData)
	})
	return file_data_processing_api_proto_rawDescData
}

var file_data_processing_api_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_data_processing_api_proto_goTypes = []interface{}{
	(*FleetMovingViolationCountRequestV1)(nil),                       // 0: data_processing.FleetMovingViolationCountRequestV1
	(*FleetMovingViolationCountV1)(nil),                              // 1: data_processing.FleetMovingViolationCountV1
	(*FleetModifiedMVRScoreRequestV1)(nil),                           // 2: data_processing.FleetModifiedMVRScoreRequestV1
	(*FleetModifiedMVRScoreV1)(nil),                                  // 3: data_processing.FleetModifiedMVRScoreV1
	(*IsoFieldsFromNhtsaFieldsRequestV1)(nil),                        // 4: data_processing.IsoFieldsFromNhtsaFieldsRequestV1
	(*IsoFieldsFromNhtsaFieldsV1)(nil),                               // 5: data_processing.IsoFieldsFromNhtsaFieldsV1
	(*NFCreditFeaturesRequestV1)(nil),                                // 6: data_processing.NFCreditFeaturesRequestV1
	(*NFCreditFeaturesV1)(nil),                                       // 7: data_processing.NFCreditFeaturesV1
	(*YearsInBusinessFromAuthorityHistoryRequestV1)(nil),             // 8: data_processing.YearsInBusinessFromAuthorityHistoryRequestV1
	(*YearsInBusinessFromAuthorityHistoryV1)(nil),                    // 9: data_processing.YearsInBusinessFromAuthorityHistoryV1
	(*NFCreditFeaturesRequestV2)(nil),                                // 10: data_processing.NFCreditFeaturesRequestV2
	(*ContinuousCoverageYearsRequestV1)(nil),                         // 11: data_processing.ContinuousCoverageYearsRequestV1
	(*ContinuousCoverageYearsV1)(nil),                                // 12: data_processing.ContinuousCoverageYearsV1
	(*InsuranceHistoryYearsInBusinessRequestV1)(nil),                 // 13: data_processing.InsuranceHistoryYearsInBusinessRequestV1
	(*InsuranceHistoryYearsInBusinessV1)(nil),                        // 14: data_processing.InsuranceHistoryYearsInBusinessV1
	(*LatestValidRatingTierRecordRequestV1)(nil),                     // 15: data_processing.LatestValidRatingTierRecordRequestV1
	(*NFInspectionCountRequestV1)(nil),                               // 16: data_processing.NFInspectionCountRequestV1
	(*NFInspectionCountV1)(nil),                                      // 17: data_processing.NFInspectionCountV1
	(*NFViolationsMetadataRequestV1)(nil),                            // 18: data_processing.NFViolationsMetadataRequestV1
	(*NFViolationsMetadataV1)(nil),                                   // 19: data_processing.NFViolationsMetadataV1
	(*RetainedYearsRequestV1)(nil),                                   // 20: data_processing.RetainedYearsRequestV1
	(*RetainedYearsV1)(nil),                                          // 21: data_processing.RetainedYearsV1
	(*PriorCarrierYearsRetainedRequestV1)(nil),                       // 22: data_processing.PriorCarrierYearsRetainedRequestV1
	(*PriorCarrierYearsRetainedV1)(nil),                              // 23: data_processing.PriorCarrierYearsRetainedV1
	(*FleetMovingViolationCountRequestV1_FetchedData)(nil),           // 24: data_processing.FleetMovingViolationCountRequestV1.FetchedData
	(*FleetModifiedMVRScoreRequestV1_FetchedData)(nil),               // 25: data_processing.FleetModifiedMVRScoreRequestV1.FetchedData
	(*IsoFieldsFromNhtsaFieldsRequestV1_FetchedData)(nil),            // 26: data_processing.IsoFieldsFromNhtsaFieldsRequestV1.FetchedData
	(*YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData)(nil), // 27: data_processing.YearsInBusinessFromAuthorityHistoryRequestV1.FetchedData
	(*NFCreditFeaturesRequestV2_FetchedData)(nil),                    // 28: data_processing.NFCreditFeaturesRequestV2.FetchedData
	(*ContinuousCoverageYearsRequestV1_FetcherRequests)(nil),         // 29: data_processing.ContinuousCoverageYearsRequestV1.FetcherRequests
	(*ContinuousCoverageYearsRequestV1_FetchedData)(nil),             // 30: data_processing.ContinuousCoverageYearsRequestV1.FetchedData
	(*InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests)(nil), // 31: data_processing.InsuranceHistoryYearsInBusinessRequestV1.FetcherRequests
	(*InsuranceHistoryYearsInBusinessRequestV1_FetchedData)(nil),     // 32: data_processing.InsuranceHistoryYearsInBusinessRequestV1.FetchedData
	(*NFInspectionCountRequestV1_FetchedData)(nil),                   // 33: data_processing.NFInspectionCountRequestV1.FetchedData
	(*NFViolationsMetadataRequestV1_FetchedData)(nil),                // 34: data_processing.NFViolationsMetadataRequestV1.FetchedData
	(*RetainedYearsRequestV1_FetchedData)(nil),                       // 35: data_processing.RetainedYearsRequestV1.FetchedData
	(*PriorCarrierYearsRetainedRequestV1_FetcherRequests)(nil),       // 36: data_processing.PriorCarrierYearsRetainedRequestV1.FetcherRequests
	(*PriorCarrierYearsRetainedRequestV1_FetchedData)(nil),           // 37: data_processing.PriorCarrierYearsRetainedRequestV1.FetchedData
	(*data_fetching.MVRReportRequestV1)(nil),                         // 38: data_fetching.MVRReportRequestV1
	(*timestamppb.Timestamp)(nil),                                    // 39: google.protobuf.Timestamp
	(*data_fetching.VINDetailsRequestV1)(nil),                        // 40: data_fetching.VINDetailsRequestV1
	(vin.IsoVehicleTypeV1)(0),                                        // 41: data_processing_vin.IsoVehicleTypeV1
	(vin.IsoWeightGroupV1)(0),                                        // 42: data_processing_vin.IsoWeightGroupV1
	(*data_fetching.NationalCreditFileV1)(nil),                       // 43: data_fetching.NationalCreditFileV1
	(*data_fetching.GrantedAuthorityHistoryRequestV1)(nil),           // 44: data_fetching.GrantedAuthorityHistoryRequestV1
	(*data_fetching.NationalCreditFileRequestV1)(nil),                // 45: data_fetching.NationalCreditFileRequestV1
	(*data_fetching.AddressV1)(nil),                                  // 46: data_fetching.AddressV1
	(*data_fetching.RatingTierRecordV1)(nil),                         // 47: data_fetching.RatingTierRecordV1
	(*data_fetching.FMCSAInspectionRecordsRequestV1)(nil),            // 48: data_fetching.FMCSAInspectionRecordsRequestV1
	(*data_fetching.FMCSAViolationRecordsRequestV1)(nil),             // 49: data_fetching.FMCSAViolationRecordsRequestV1
	(*data_fetching.GetNirvanaPoliciesRequestV1)(nil),                // 50: data_fetching.GetNirvanaPoliciesRequestV1
	(*data_fetching.MVRReportV1)(nil),                                // 51: data_fetching.MVRReportV1
	(data_fetching.VehicleTypeV1)(0),                                 // 52: data_fetching.VehicleTypeV1
	(data_fetching.WeightClassV1)(0),                                 // 53: data_fetching.WeightClassV1
	(*data_fetching.AuthorityHistoryRecordV1)(nil),                   // 54: data_fetching.AuthorityHistoryRecordV1
	(*data_fetching.BIPDInsuranceHistoryRequestV1)(nil),              // 55: data_fetching.BIPDInsuranceHistoryRequestV1
	(*data_fetching.BIPDActiveOrPendingInsuranceRequestV1)(nil),      // 56: data_fetching.BIPDActiveOrPendingInsuranceRequestV1
	(*data_fetching.InsuranceRecordV1)(nil),                          // 57: data_fetching.InsuranceRecordV1
	(*data_fetching.FMCSAInspectionRecordsV1)(nil),                   // 58: data_fetching.FMCSAInspectionRecordsV1
	(*data_fetching.FMCSAViolationRecordsV1)(nil),                    // 59: data_fetching.FMCSAViolationRecordsV1
	(*data_fetching.GetNirvanaPoliciesResponseV1)(nil),               // 60: data_fetching.GetNirvanaPoliciesResponseV1
}
var file_data_processing_api_proto_depIdxs = []int32{
	38, // 0: data_processing.FleetMovingViolationCountRequestV1.fetcherRequest:type_name -> data_fetching.MVRReportRequestV1
	24, // 1: data_processing.FleetMovingViolationCountRequestV1.fetchedData:type_name -> data_processing.FleetMovingViolationCountRequestV1.FetchedData
	39, // 2: data_processing.FleetMovingViolationCountRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	38, // 3: data_processing.FleetModifiedMVRScoreRequestV1.fetcherRequest:type_name -> data_fetching.MVRReportRequestV1
	25, // 4: data_processing.FleetModifiedMVRScoreRequestV1.fetchedData:type_name -> data_processing.FleetModifiedMVRScoreRequestV1.FetchedData
	39, // 5: data_processing.FleetModifiedMVRScoreRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	40, // 6: data_processing.IsoFieldsFromNhtsaFieldsRequestV1.fetcherRequest:type_name -> data_fetching.VINDetailsRequestV1
	26, // 7: data_processing.IsoFieldsFromNhtsaFieldsRequestV1.fetchedData:type_name -> data_processing.IsoFieldsFromNhtsaFieldsRequestV1.FetchedData
	41, // 8: data_processing.IsoFieldsFromNhtsaFieldsV1.vehicleType:type_name -> data_processing_vin.IsoVehicleTypeV1
	42, // 9: data_processing.IsoFieldsFromNhtsaFieldsV1.weightGroup:type_name -> data_processing_vin.IsoWeightGroupV1
	39, // 10: data_processing.NFCreditFeaturesRequestV1.ownerDOB:type_name -> google.protobuf.Timestamp
	43, // 11: data_processing.NFCreditFeaturesRequestV1.nationalCreditFile:type_name -> data_fetching.NationalCreditFileV1
	39, // 12: data_processing.NFCreditFeaturesV1.dateOfOldestTradeline:type_name -> google.protobuf.Timestamp
	39, // 13: data_processing.NFCreditFeaturesV1.dateOfMostRecentAutoTrade:type_name -> google.protobuf.Timestamp
	39, // 14: data_processing.NFCreditFeaturesV1.dateOfEarliestTradeline:type_name -> google.protobuf.Timestamp
	39, // 15: data_processing.NFCreditFeaturesV1.dateOfCreditReportRun:type_name -> google.protobuf.Timestamp
	44, // 16: data_processing.YearsInBusinessFromAuthorityHistoryRequestV1.fetcherRequest:type_name -> data_fetching.GrantedAuthorityHistoryRequestV1
	27, // 17: data_processing.YearsInBusinessFromAuthorityHistoryRequestV1.fetchedData:type_name -> data_processing.YearsInBusinessFromAuthorityHistoryRequestV1.FetchedData
	39, // 18: data_processing.YearsInBusinessFromAuthorityHistoryRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	45, // 19: data_processing.NFCreditFeaturesRequestV2.fetcherRequest:type_name -> data_fetching.NationalCreditFileRequestV1
	28, // 20: data_processing.NFCreditFeaturesRequestV2.fetchedData:type_name -> data_processing.NFCreditFeaturesRequestV2.FetchedData
	39, // 21: data_processing.NFCreditFeaturesRequestV2.ownerDOB:type_name -> google.protobuf.Timestamp
	46, // 22: data_processing.NFCreditFeaturesRequestV2.address:type_name -> data_fetching.AddressV1
	29, // 23: data_processing.ContinuousCoverageYearsRequestV1.fetcherRequests:type_name -> data_processing.ContinuousCoverageYearsRequestV1.FetcherRequests
	30, // 24: data_processing.ContinuousCoverageYearsRequestV1.fetchedData:type_name -> data_processing.ContinuousCoverageYearsRequestV1.FetchedData
	39, // 25: data_processing.ContinuousCoverageYearsRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	31, // 26: data_processing.InsuranceHistoryYearsInBusinessRequestV1.fetcherRequests:type_name -> data_processing.InsuranceHistoryYearsInBusinessRequestV1.FetcherRequests
	32, // 27: data_processing.InsuranceHistoryYearsInBusinessRequestV1.fetchedData:type_name -> data_processing.InsuranceHistoryYearsInBusinessRequestV1.FetchedData
	39, // 28: data_processing.InsuranceHistoryYearsInBusinessRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	47, // 29: data_processing.LatestValidRatingTierRecordRequestV1.records:type_name -> data_fetching.RatingTierRecordV1
	48, // 30: data_processing.NFInspectionCountRequestV1.fetcherRequest:type_name -> data_fetching.FMCSAInspectionRecordsRequestV1
	33, // 31: data_processing.NFInspectionCountRequestV1.fetchedData:type_name -> data_processing.NFInspectionCountRequestV1.FetchedData
	39, // 32: data_processing.NFInspectionCountRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	49, // 33: data_processing.NFViolationsMetadataRequestV1.fetcherRequest:type_name -> data_fetching.FMCSAViolationRecordsRequestV1
	34, // 34: data_processing.NFViolationsMetadataRequestV1.fetchedData:type_name -> data_processing.NFViolationsMetadataRequestV1.FetchedData
	39, // 35: data_processing.NFViolationsMetadataRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	50, // 36: data_processing.RetainedYearsRequestV1.fetcherRequest:type_name -> data_fetching.GetNirvanaPoliciesRequestV1
	35, // 37: data_processing.RetainedYearsRequestV1.fetchedData:type_name -> data_processing.RetainedYearsRequestV1.FetchedData
	39, // 38: data_processing.RetainedYearsRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	36, // 39: data_processing.PriorCarrierYearsRetainedRequestV1.fetcherRequests:type_name -> data_processing.PriorCarrierYearsRetainedRequestV1.FetcherRequests
	37, // 40: data_processing.PriorCarrierYearsRetainedRequestV1.fetchedData:type_name -> data_processing.PriorCarrierYearsRetainedRequestV1.FetchedData
	39, // 41: data_processing.PriorCarrierYearsRetainedRequestV1.effectiveDate:type_name -> google.protobuf.Timestamp
	51, // 42: data_processing.FleetMovingViolationCountRequestV1.FetchedData.report:type_name -> data_fetching.MVRReportV1
	51, // 43: data_processing.FleetModifiedMVRScoreRequestV1.FetchedData.report:type_name -> data_fetching.MVRReportV1
	52, // 44: data_processing.IsoFieldsFromNhtsaFieldsRequestV1.FetchedData.vehicleType:type_name -> data_fetching.VehicleTypeV1
	53, // 45: data_processing.IsoFieldsFromNhtsaFieldsRequestV1.FetchedData.vehicleWeightClass:type_name -> data_fetching.WeightClassV1
	54, // 46: data_processing.YearsInBusinessFromAuthorityHistoryRequestV1.FetchedData.authorityHistoryRecords:type_name -> data_fetching.AuthorityHistoryRecordV1
	43, // 47: data_processing.NFCreditFeaturesRequestV2.FetchedData.nationalCreditFile:type_name -> data_fetching.NationalCreditFileV1
	55, // 48: data_processing.ContinuousCoverageYearsRequestV1.FetcherRequests.insuranceHistoryRequest:type_name -> data_fetching.BIPDInsuranceHistoryRequestV1
	56, // 49: data_processing.ContinuousCoverageYearsRequestV1.FetcherRequests.activeOrPendingInsuranceRequest:type_name -> data_fetching.BIPDActiveOrPendingInsuranceRequestV1
	57, // 50: data_processing.ContinuousCoverageYearsRequestV1.FetchedData.insuranceRecords:type_name -> data_fetching.InsuranceRecordV1
	55, // 51: data_processing.InsuranceHistoryYearsInBusinessRequestV1.FetcherRequests.insuranceHistoryRequest:type_name -> data_fetching.BIPDInsuranceHistoryRequestV1
	56, // 52: data_processing.InsuranceHistoryYearsInBusinessRequestV1.FetcherRequests.activeOrPendingInsuranceRequest:type_name -> data_fetching.BIPDActiveOrPendingInsuranceRequestV1
	57, // 53: data_processing.InsuranceHistoryYearsInBusinessRequestV1.FetchedData.insuranceRecords:type_name -> data_fetching.InsuranceRecordV1
	58, // 54: data_processing.NFInspectionCountRequestV1.FetchedData.inspectionRecords:type_name -> data_fetching.FMCSAInspectionRecordsV1
	59, // 55: data_processing.NFViolationsMetadataRequestV1.FetchedData.violationRecords:type_name -> data_fetching.FMCSAViolationRecordsV1
	60, // 56: data_processing.RetainedYearsRequestV1.FetchedData.policies:type_name -> data_fetching.GetNirvanaPoliciesResponseV1
	55, // 57: data_processing.PriorCarrierYearsRetainedRequestV1.FetcherRequests.insuranceHistoryRequest:type_name -> data_fetching.BIPDInsuranceHistoryRequestV1
	56, // 58: data_processing.PriorCarrierYearsRetainedRequestV1.FetcherRequests.activeOrPendingInsuranceRequest:type_name -> data_fetching.BIPDActiveOrPendingInsuranceRequestV1
	57, // 59: data_processing.PriorCarrierYearsRetainedRequestV1.FetchedData.insuranceRecords:type_name -> data_fetching.InsuranceRecordV1
	0,  // 60: data_processing.Processor.GetFleetMovingViolationCountV1:input_type -> data_processing.FleetMovingViolationCountRequestV1
	2,  // 61: data_processing.Processor.GetFleetModifiedMVRScoreV1:input_type -> data_processing.FleetModifiedMVRScoreRequestV1
	4,  // 62: data_processing.Processor.GetIsoFieldsFromNhtsaFieldsV1:input_type -> data_processing.IsoFieldsFromNhtsaFieldsRequestV1
	6,  // 63: data_processing.Processor.GetNFCreditFeaturesV1:input_type -> data_processing.NFCreditFeaturesRequestV1
	8,  // 64: data_processing.Processor.GetYearsInBusinessFromAuthorityHistoryV1:input_type -> data_processing.YearsInBusinessFromAuthorityHistoryRequestV1
	10, // 65: data_processing.Processor.GetNFCreditFeaturesV2:input_type -> data_processing.NFCreditFeaturesRequestV2
	11, // 66: data_processing.Processor.GetContinuousCoverageYearsV1:input_type -> data_processing.ContinuousCoverageYearsRequestV1
	13, // 67: data_processing.Processor.GetYearsInBusinessFromInsuranceHistoryV1:input_type -> data_processing.InsuranceHistoryYearsInBusinessRequestV1
	15, // 68: data_processing.Processor.GetLatestValidRatingTierRecordV1:input_type -> data_processing.LatestValidRatingTierRecordRequestV1
	16, // 69: data_processing.Processor.GetNFInspectionCountV1:input_type -> data_processing.NFInspectionCountRequestV1
	18, // 70: data_processing.Processor.GetNFViolationsMetadataV1:input_type -> data_processing.NFViolationsMetadataRequestV1
	20, // 71: data_processing.Processor.GetRetainedYearsV1:input_type -> data_processing.RetainedYearsRequestV1
	22, // 72: data_processing.Processor.GetPriorCarrierYearsRetainedV1:input_type -> data_processing.PriorCarrierYearsRetainedRequestV1
	1,  // 73: data_processing.Processor.GetFleetMovingViolationCountV1:output_type -> data_processing.FleetMovingViolationCountV1
	3,  // 74: data_processing.Processor.GetFleetModifiedMVRScoreV1:output_type -> data_processing.FleetModifiedMVRScoreV1
	5,  // 75: data_processing.Processor.GetIsoFieldsFromNhtsaFieldsV1:output_type -> data_processing.IsoFieldsFromNhtsaFieldsV1
	7,  // 76: data_processing.Processor.GetNFCreditFeaturesV1:output_type -> data_processing.NFCreditFeaturesV1
	9,  // 77: data_processing.Processor.GetYearsInBusinessFromAuthorityHistoryV1:output_type -> data_processing.YearsInBusinessFromAuthorityHistoryV1
	7,  // 78: data_processing.Processor.GetNFCreditFeaturesV2:output_type -> data_processing.NFCreditFeaturesV1
	12, // 79: data_processing.Processor.GetContinuousCoverageYearsV1:output_type -> data_processing.ContinuousCoverageYearsV1
	14, // 80: data_processing.Processor.GetYearsInBusinessFromInsuranceHistoryV1:output_type -> data_processing.InsuranceHistoryYearsInBusinessV1
	47, // 81: data_processing.Processor.GetLatestValidRatingTierRecordV1:output_type -> data_fetching.RatingTierRecordV1
	17, // 82: data_processing.Processor.GetNFInspectionCountV1:output_type -> data_processing.NFInspectionCountV1
	19, // 83: data_processing.Processor.GetNFViolationsMetadataV1:output_type -> data_processing.NFViolationsMetadataV1
	21, // 84: data_processing.Processor.GetRetainedYearsV1:output_type -> data_processing.RetainedYearsV1
	23, // 85: data_processing.Processor.GetPriorCarrierYearsRetainedV1:output_type -> data_processing.PriorCarrierYearsRetainedV1
	73, // [73:86] is the sub-list for method output_type
	60, // [60:73] is the sub-list for method input_type
	60, // [60:60] is the sub-list for extension type_name
	60, // [60:60] is the sub-list for extension extendee
	0,  // [0:60] is the sub-list for field type_name
}

func init() { file_data_processing_api_proto_init() }
func file_data_processing_api_proto_init() {
	if File_data_processing_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_data_processing_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetMovingViolationCountRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetMovingViolationCountV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetModifiedMVRScoreRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetModifiedMVRScoreV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsoFieldsFromNhtsaFieldsRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsoFieldsFromNhtsaFieldsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFCreditFeaturesRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFCreditFeaturesV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearsInBusinessFromAuthorityHistoryRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearsInBusinessFromAuthorityHistoryV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFCreditFeaturesRequestV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinuousCoverageYearsRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinuousCoverageYearsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuranceHistoryYearsInBusinessRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuranceHistoryYearsInBusinessV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LatestValidRatingTierRecordRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFInspectionCountRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFInspectionCountV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFViolationsMetadataRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFViolationsMetadataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetainedYearsRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetainedYearsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriorCarrierYearsRetainedRequestV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriorCarrierYearsRetainedV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetMovingViolationCountRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetModifiedMVRScoreRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsoFieldsFromNhtsaFieldsRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFCreditFeaturesRequestV2_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinuousCoverageYearsRequestV1_FetcherRequests); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinuousCoverageYearsRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuranceHistoryYearsInBusinessRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFInspectionCountRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFViolationsMetadataRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetainedYearsRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriorCarrierYearsRetainedRequestV1_FetcherRequests); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriorCarrierYearsRetainedRequestV1_FetchedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_data_processing_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*FleetMovingViolationCountRequestV1_FetcherRequest)(nil),
		(*FleetMovingViolationCountRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*FleetModifiedMVRScoreRequestV1_FetcherRequest)(nil),
		(*FleetModifiedMVRScoreRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*IsoFieldsFromNhtsaFieldsRequestV1_FetcherRequest)(nil),
		(*IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_data_processing_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*YearsInBusinessFromAuthorityHistoryRequestV1_FetcherRequest)(nil),
		(*YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*NFCreditFeaturesRequestV2_FetcherRequest)(nil),
		(*NFCreditFeaturesRequestV2_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*ContinuousCoverageYearsRequestV1_FetcherRequests_)(nil),
		(*ContinuousCoverageYearsRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*InsuranceHistoryYearsInBusinessRequestV1_FetcherRequests_)(nil),
		(*InsuranceHistoryYearsInBusinessRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*NFInspectionCountRequestV1_FetcherRequest)(nil),
		(*NFInspectionCountRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*NFViolationsMetadataRequestV1_FetcherRequest)(nil),
		(*NFViolationsMetadataRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*RetainedYearsRequestV1_FetcherRequest)(nil),
		(*RetainedYearsRequestV1_FetchedData_)(nil),
	}
	file_data_processing_api_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*PriorCarrierYearsRetainedRequestV1_FetcherRequests_)(nil),
		(*PriorCarrierYearsRetainedRequestV1_FetchedData_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_data_processing_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_data_processing_api_proto_goTypes,
		DependencyIndexes: file_data_processing_api_proto_depIdxs,
		MessageInfos:      file_data_processing_api_proto_msgTypes,
	}.Build()
	File_data_processing_api_proto = out.File
	file_data_processing_api_proto_rawDesc = nil
	file_data_processing_api_proto_goTypes = nil
	file_data_processing_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// ProcessorClient is the client API for Processor service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ProcessorClient interface {
	GetFleetMovingViolationCountV1(ctx context.Context, in *FleetMovingViolationCountRequestV1, opts ...grpc.CallOption) (*FleetMovingViolationCountV1, error)
	GetFleetModifiedMVRScoreV1(ctx context.Context, in *FleetModifiedMVRScoreRequestV1, opts ...grpc.CallOption) (*FleetModifiedMVRScoreV1, error)
	GetIsoFieldsFromNhtsaFieldsV1(ctx context.Context, in *IsoFieldsFromNhtsaFieldsRequestV1, opts ...grpc.CallOption) (*IsoFieldsFromNhtsaFieldsV1, error)
	GetNFCreditFeaturesV1(ctx context.Context, in *NFCreditFeaturesRequestV1, opts ...grpc.CallOption) (*NFCreditFeaturesV1, error)
	GetYearsInBusinessFromAuthorityHistoryV1(ctx context.Context, in *YearsInBusinessFromAuthorityHistoryRequestV1, opts ...grpc.CallOption) (*YearsInBusinessFromAuthorityHistoryV1, error)
	GetNFCreditFeaturesV2(ctx context.Context, in *NFCreditFeaturesRequestV2, opts ...grpc.CallOption) (*NFCreditFeaturesV1, error)
	GetContinuousCoverageYearsV1(ctx context.Context, in *ContinuousCoverageYearsRequestV1, opts ...grpc.CallOption) (*ContinuousCoverageYearsV1, error)
	GetYearsInBusinessFromInsuranceHistoryV1(ctx context.Context, in *InsuranceHistoryYearsInBusinessRequestV1, opts ...grpc.CallOption) (*InsuranceHistoryYearsInBusinessV1, error)
	GetLatestValidRatingTierRecordV1(ctx context.Context, in *LatestValidRatingTierRecordRequestV1, opts ...grpc.CallOption) (*data_fetching.RatingTierRecordV1, error)
	GetNFInspectionCountV1(ctx context.Context, in *NFInspectionCountRequestV1, opts ...grpc.CallOption) (*NFInspectionCountV1, error)
	GetNFViolationsMetadataV1(ctx context.Context, in *NFViolationsMetadataRequestV1, opts ...grpc.CallOption) (*NFViolationsMetadataV1, error)
	GetRetainedYearsV1(ctx context.Context, in *RetainedYearsRequestV1, opts ...grpc.CallOption) (*RetainedYearsV1, error)
	GetPriorCarrierYearsRetainedV1(ctx context.Context, in *PriorCarrierYearsRetainedRequestV1, opts ...grpc.CallOption) (*PriorCarrierYearsRetainedV1, error)
}

type processorClient struct {
	cc grpc.ClientConnInterface
}

func NewProcessorClient(cc grpc.ClientConnInterface) ProcessorClient {
	return &processorClient{cc}
}

func (c *processorClient) GetFleetMovingViolationCountV1(ctx context.Context, in *FleetMovingViolationCountRequestV1, opts ...grpc.CallOption) (*FleetMovingViolationCountV1, error) {
	out := new(FleetMovingViolationCountV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetFleetMovingViolationCountV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetFleetModifiedMVRScoreV1(ctx context.Context, in *FleetModifiedMVRScoreRequestV1, opts ...grpc.CallOption) (*FleetModifiedMVRScoreV1, error) {
	out := new(FleetModifiedMVRScoreV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetFleetModifiedMVRScoreV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetIsoFieldsFromNhtsaFieldsV1(ctx context.Context, in *IsoFieldsFromNhtsaFieldsRequestV1, opts ...grpc.CallOption) (*IsoFieldsFromNhtsaFieldsV1, error) {
	out := new(IsoFieldsFromNhtsaFieldsV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetIsoFieldsFromNhtsaFieldsV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetNFCreditFeaturesV1(ctx context.Context, in *NFCreditFeaturesRequestV1, opts ...grpc.CallOption) (*NFCreditFeaturesV1, error) {
	out := new(NFCreditFeaturesV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetNFCreditFeaturesV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetYearsInBusinessFromAuthorityHistoryV1(ctx context.Context, in *YearsInBusinessFromAuthorityHistoryRequestV1, opts ...grpc.CallOption) (*YearsInBusinessFromAuthorityHistoryV1, error) {
	out := new(YearsInBusinessFromAuthorityHistoryV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetYearsInBusinessFromAuthorityHistoryV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetNFCreditFeaturesV2(ctx context.Context, in *NFCreditFeaturesRequestV2, opts ...grpc.CallOption) (*NFCreditFeaturesV1, error) {
	out := new(NFCreditFeaturesV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetNFCreditFeaturesV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetContinuousCoverageYearsV1(ctx context.Context, in *ContinuousCoverageYearsRequestV1, opts ...grpc.CallOption) (*ContinuousCoverageYearsV1, error) {
	out := new(ContinuousCoverageYearsV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetContinuousCoverageYearsV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetYearsInBusinessFromInsuranceHistoryV1(ctx context.Context, in *InsuranceHistoryYearsInBusinessRequestV1, opts ...grpc.CallOption) (*InsuranceHistoryYearsInBusinessV1, error) {
	out := new(InsuranceHistoryYearsInBusinessV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetYearsInBusinessFromInsuranceHistoryV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetLatestValidRatingTierRecordV1(ctx context.Context, in *LatestValidRatingTierRecordRequestV1, opts ...grpc.CallOption) (*data_fetching.RatingTierRecordV1, error) {
	out := new(data_fetching.RatingTierRecordV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetLatestValidRatingTierRecordV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetNFInspectionCountV1(ctx context.Context, in *NFInspectionCountRequestV1, opts ...grpc.CallOption) (*NFInspectionCountV1, error) {
	out := new(NFInspectionCountV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetNFInspectionCountV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetNFViolationsMetadataV1(ctx context.Context, in *NFViolationsMetadataRequestV1, opts ...grpc.CallOption) (*NFViolationsMetadataV1, error) {
	out := new(NFViolationsMetadataV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetNFViolationsMetadataV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetRetainedYearsV1(ctx context.Context, in *RetainedYearsRequestV1, opts ...grpc.CallOption) (*RetainedYearsV1, error) {
	out := new(RetainedYearsV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetRetainedYearsV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorClient) GetPriorCarrierYearsRetainedV1(ctx context.Context, in *PriorCarrierYearsRetainedRequestV1, opts ...grpc.CallOption) (*PriorCarrierYearsRetainedV1, error) {
	out := new(PriorCarrierYearsRetainedV1)
	err := c.cc.Invoke(ctx, "/data_processing.Processor/GetPriorCarrierYearsRetainedV1", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProcessorServer is the server API for Processor service.
type ProcessorServer interface {
	GetFleetMovingViolationCountV1(context.Context, *FleetMovingViolationCountRequestV1) (*FleetMovingViolationCountV1, error)
	GetFleetModifiedMVRScoreV1(context.Context, *FleetModifiedMVRScoreRequestV1) (*FleetModifiedMVRScoreV1, error)
	GetIsoFieldsFromNhtsaFieldsV1(context.Context, *IsoFieldsFromNhtsaFieldsRequestV1) (*IsoFieldsFromNhtsaFieldsV1, error)
	GetNFCreditFeaturesV1(context.Context, *NFCreditFeaturesRequestV1) (*NFCreditFeaturesV1, error)
	GetYearsInBusinessFromAuthorityHistoryV1(context.Context, *YearsInBusinessFromAuthorityHistoryRequestV1) (*YearsInBusinessFromAuthorityHistoryV1, error)
	GetNFCreditFeaturesV2(context.Context, *NFCreditFeaturesRequestV2) (*NFCreditFeaturesV1, error)
	GetContinuousCoverageYearsV1(context.Context, *ContinuousCoverageYearsRequestV1) (*ContinuousCoverageYearsV1, error)
	GetYearsInBusinessFromInsuranceHistoryV1(context.Context, *InsuranceHistoryYearsInBusinessRequestV1) (*InsuranceHistoryYearsInBusinessV1, error)
	GetLatestValidRatingTierRecordV1(context.Context, *LatestValidRatingTierRecordRequestV1) (*data_fetching.RatingTierRecordV1, error)
	GetNFInspectionCountV1(context.Context, *NFInspectionCountRequestV1) (*NFInspectionCountV1, error)
	GetNFViolationsMetadataV1(context.Context, *NFViolationsMetadataRequestV1) (*NFViolationsMetadataV1, error)
	GetRetainedYearsV1(context.Context, *RetainedYearsRequestV1) (*RetainedYearsV1, error)
	GetPriorCarrierYearsRetainedV1(context.Context, *PriorCarrierYearsRetainedRequestV1) (*PriorCarrierYearsRetainedV1, error)
}

// UnimplementedProcessorServer can be embedded to have forward compatible implementations.
type UnimplementedProcessorServer struct {
}

func (*UnimplementedProcessorServer) GetFleetMovingViolationCountV1(context.Context, *FleetMovingViolationCountRequestV1) (*FleetMovingViolationCountV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFleetMovingViolationCountV1 not implemented")
}
func (*UnimplementedProcessorServer) GetFleetModifiedMVRScoreV1(context.Context, *FleetModifiedMVRScoreRequestV1) (*FleetModifiedMVRScoreV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFleetModifiedMVRScoreV1 not implemented")
}
func (*UnimplementedProcessorServer) GetIsoFieldsFromNhtsaFieldsV1(context.Context, *IsoFieldsFromNhtsaFieldsRequestV1) (*IsoFieldsFromNhtsaFieldsV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIsoFieldsFromNhtsaFieldsV1 not implemented")
}
func (*UnimplementedProcessorServer) GetNFCreditFeaturesV1(context.Context, *NFCreditFeaturesRequestV1) (*NFCreditFeaturesV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNFCreditFeaturesV1 not implemented")
}
func (*UnimplementedProcessorServer) GetYearsInBusinessFromAuthorityHistoryV1(context.Context, *YearsInBusinessFromAuthorityHistoryRequestV1) (*YearsInBusinessFromAuthorityHistoryV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetYearsInBusinessFromAuthorityHistoryV1 not implemented")
}
func (*UnimplementedProcessorServer) GetNFCreditFeaturesV2(context.Context, *NFCreditFeaturesRequestV2) (*NFCreditFeaturesV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNFCreditFeaturesV2 not implemented")
}
func (*UnimplementedProcessorServer) GetContinuousCoverageYearsV1(context.Context, *ContinuousCoverageYearsRequestV1) (*ContinuousCoverageYearsV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContinuousCoverageYearsV1 not implemented")
}
func (*UnimplementedProcessorServer) GetYearsInBusinessFromInsuranceHistoryV1(context.Context, *InsuranceHistoryYearsInBusinessRequestV1) (*InsuranceHistoryYearsInBusinessV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetYearsInBusinessFromInsuranceHistoryV1 not implemented")
}
func (*UnimplementedProcessorServer) GetLatestValidRatingTierRecordV1(context.Context, *LatestValidRatingTierRecordRequestV1) (*data_fetching.RatingTierRecordV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestValidRatingTierRecordV1 not implemented")
}
func (*UnimplementedProcessorServer) GetNFInspectionCountV1(context.Context, *NFInspectionCountRequestV1) (*NFInspectionCountV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNFInspectionCountV1 not implemented")
}
func (*UnimplementedProcessorServer) GetNFViolationsMetadataV1(context.Context, *NFViolationsMetadataRequestV1) (*NFViolationsMetadataV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNFViolationsMetadataV1 not implemented")
}
func (*UnimplementedProcessorServer) GetRetainedYearsV1(context.Context, *RetainedYearsRequestV1) (*RetainedYearsV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRetainedYearsV1 not implemented")
}
func (*UnimplementedProcessorServer) GetPriorCarrierYearsRetainedV1(context.Context, *PriorCarrierYearsRetainedRequestV1) (*PriorCarrierYearsRetainedV1, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPriorCarrierYearsRetainedV1 not implemented")
}

func RegisterProcessorServer(s *grpc.Server, srv ProcessorServer) {
	s.RegisterService(&_Processor_serviceDesc, srv)
}

func _Processor_GetFleetMovingViolationCountV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FleetMovingViolationCountRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetFleetMovingViolationCountV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetFleetMovingViolationCountV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetFleetMovingViolationCountV1(ctx, req.(*FleetMovingViolationCountRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetFleetModifiedMVRScoreV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FleetModifiedMVRScoreRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetFleetModifiedMVRScoreV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetFleetModifiedMVRScoreV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetFleetModifiedMVRScoreV1(ctx, req.(*FleetModifiedMVRScoreRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetIsoFieldsFromNhtsaFieldsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsoFieldsFromNhtsaFieldsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetIsoFieldsFromNhtsaFieldsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetIsoFieldsFromNhtsaFieldsV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetIsoFieldsFromNhtsaFieldsV1(ctx, req.(*IsoFieldsFromNhtsaFieldsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetNFCreditFeaturesV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NFCreditFeaturesRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetNFCreditFeaturesV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetNFCreditFeaturesV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetNFCreditFeaturesV1(ctx, req.(*NFCreditFeaturesRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetYearsInBusinessFromAuthorityHistoryV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YearsInBusinessFromAuthorityHistoryRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetYearsInBusinessFromAuthorityHistoryV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetYearsInBusinessFromAuthorityHistoryV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetYearsInBusinessFromAuthorityHistoryV1(ctx, req.(*YearsInBusinessFromAuthorityHistoryRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetNFCreditFeaturesV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NFCreditFeaturesRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetNFCreditFeaturesV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetNFCreditFeaturesV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetNFCreditFeaturesV2(ctx, req.(*NFCreditFeaturesRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetContinuousCoverageYearsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContinuousCoverageYearsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetContinuousCoverageYearsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetContinuousCoverageYearsV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetContinuousCoverageYearsV1(ctx, req.(*ContinuousCoverageYearsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetYearsInBusinessFromInsuranceHistoryV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsuranceHistoryYearsInBusinessRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetYearsInBusinessFromInsuranceHistoryV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetYearsInBusinessFromInsuranceHistoryV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetYearsInBusinessFromInsuranceHistoryV1(ctx, req.(*InsuranceHistoryYearsInBusinessRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetLatestValidRatingTierRecordV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LatestValidRatingTierRecordRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetLatestValidRatingTierRecordV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetLatestValidRatingTierRecordV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetLatestValidRatingTierRecordV1(ctx, req.(*LatestValidRatingTierRecordRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetNFInspectionCountV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NFInspectionCountRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetNFInspectionCountV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetNFInspectionCountV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetNFInspectionCountV1(ctx, req.(*NFInspectionCountRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetNFViolationsMetadataV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NFViolationsMetadataRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetNFViolationsMetadataV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetNFViolationsMetadataV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetNFViolationsMetadataV1(ctx, req.(*NFViolationsMetadataRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetRetainedYearsV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetainedYearsRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetRetainedYearsV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetRetainedYearsV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetRetainedYearsV1(ctx, req.(*RetainedYearsRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

func _Processor_GetPriorCarrierYearsRetainedV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PriorCarrierYearsRetainedRequestV1)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorServer).GetPriorCarrierYearsRetainedV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/data_processing.Processor/GetPriorCarrierYearsRetainedV1",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorServer).GetPriorCarrierYearsRetainedV1(ctx, req.(*PriorCarrierYearsRetainedRequestV1))
	}
	return interceptor(ctx, in, info, handler)
}

var _Processor_serviceDesc = grpc.ServiceDesc{
	ServiceName: "data_processing.Processor",
	HandlerType: (*ProcessorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFleetMovingViolationCountV1",
			Handler:    _Processor_GetFleetMovingViolationCountV1_Handler,
		},
		{
			MethodName: "GetFleetModifiedMVRScoreV1",
			Handler:    _Processor_GetFleetModifiedMVRScoreV1_Handler,
		},
		{
			MethodName: "GetIsoFieldsFromNhtsaFieldsV1",
			Handler:    _Processor_GetIsoFieldsFromNhtsaFieldsV1_Handler,
		},
		{
			MethodName: "GetNFCreditFeaturesV1",
			Handler:    _Processor_GetNFCreditFeaturesV1_Handler,
		},
		{
			MethodName: "GetYearsInBusinessFromAuthorityHistoryV1",
			Handler:    _Processor_GetYearsInBusinessFromAuthorityHistoryV1_Handler,
		},
		{
			MethodName: "GetNFCreditFeaturesV2",
			Handler:    _Processor_GetNFCreditFeaturesV2_Handler,
		},
		{
			MethodName: "GetContinuousCoverageYearsV1",
			Handler:    _Processor_GetContinuousCoverageYearsV1_Handler,
		},
		{
			MethodName: "GetYearsInBusinessFromInsuranceHistoryV1",
			Handler:    _Processor_GetYearsInBusinessFromInsuranceHistoryV1_Handler,
		},
		{
			MethodName: "GetLatestValidRatingTierRecordV1",
			Handler:    _Processor_GetLatestValidRatingTierRecordV1_Handler,
		},
		{
			MethodName: "GetNFInspectionCountV1",
			Handler:    _Processor_GetNFInspectionCountV1_Handler,
		},
		{
			MethodName: "GetNFViolationsMetadataV1",
			Handler:    _Processor_GetNFViolationsMetadataV1_Handler,
		},
		{
			MethodName: "GetRetainedYearsV1",
			Handler:    _Processor_GetRetainedYearsV1_Handler,
		},
		{
			MethodName: "GetPriorCarrierYearsRetainedV1",
			Handler:    _Processor_GetPriorCarrierYearsRetainedV1_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "data_processing/api.proto",
}
