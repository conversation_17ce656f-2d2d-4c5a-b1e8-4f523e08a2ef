package data_processing

/*
This package encapsulates all the logic related to processing "raw" data that comes from "external" sources,
by applying "business logic" to it.

Method on the processor component don't fetch any dynamic data (i.e. data that is specific to that method call).
Most likely, the data passed to these methods will have been obtained by the consumer through the fetcher
component defined in the package data_fetching (read that package's documentation for more detail).

For example, the consumer can use the fetcher component to get an MVR report for a given driver, and then pass that
report to the processor in order to calculate the moving violation count for that driver.

The processor component might need static data for some methods. For example, it needs the violation list, with all
the codes and violation info to calculate the moving violation count. Static data is not passed as argument to the
processor's methods, but instead is fetched by the processor internally.

This package exposes a factory function to create instances of the ProcessorClient implementation.
This ensures that consumers always get a valid fetcher, and hides away some of the complexities
related to instantiation.

The ProcessorClient interface is defined as a gRPC client. Therefore, consumers can modify the
behavior of the methods by using gRPC interceptors.
*/

// Command to regenerate ProcessorServer mock
//go:generate go run go.uber.org/mock/mockgen -destination=server_mock.go -package=data_processing nirvanatech.com/nirvana/external_data_management/data_processing ProcessorServer

// Command to regenerate ProcessorClient mock
//go:generate go run go.uber.org/mock/mockgen -destination=client_mock.go -package=data_processing nirvanatech.com/nirvana/external_data_management/data_processing ProcessorClient
