package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_GetNFInspectionCountV1_Simple(t *testing.T) {
	var env struct {
		fx.In

		ProcessorClientFactory data_processing.ProcessorClientFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	processorClient, closer, err := env.ProcessorClientFactory(nil)
	assert.NoError(t, err)
	defer func() { _ = closer() }()

	ctx := context.Background()

	records := []*data_fetching.FMCSAInspectionRecordV1{
		{
			PublishedDate:  timestamppb.New(time_utils.NewDate(2022, 9, 30).ToTime()),
			InspectionDate: timestamppb.New(time_utils.NewDate(2020, 10, 21).ToTime()),
		},
		{
			PublishedDate:  timestamppb.New(time_utils.NewDate(2022, 9, 30).ToTime()),
			InspectionDate: timestamppb.New(time_utils.NewDate(2020, 11, 12).ToTime()),
		},
		{
			PublishedDate:  timestamppb.New(time_utils.NewDate(2022, 9, 30).ToTime()),
			InspectionDate: timestamppb.New(time_utils.NewDate(2020, 11, 16).ToTime()),
		},
		{
			PublishedDate:  timestamppb.New(time_utils.NewDate(2022, 9, 30).ToTime()),
			InspectionDate: timestamppb.New(time_utils.NewDate(2020, 11, 24).ToTime()),
		},
		// Should be ignored: Inspection date (2019-11-24) is before valid period start (2020-01-15)
		// Valid period: 2020-01-15 (3years before effectiveDate) to 2023-01-15 (effectiveDate)
		{
			PublishedDate:  timestamppb.New(time_utils.NewDate(2022, 9, 30).ToTime()),
			InspectionDate: timestamppb.New(time_utils.NewDate(2019, 11, 24).ToTime()),
		},
		// Should be ignored: Inspection date (2024-11-24) is after effectiveDate (2023-01-15)
		// Note: This case should not occur in production as published date should not exceed effective date
		// and inspection date should not exceed published date
		{
			PublishedDate:  timestamppb.New(time_utils.NewDate(2022, 9, 30).ToTime()),
			InspectionDate: timestamppb.New(time_utils.NewDate(2024, 11, 24).ToTime()),
		},
	}

	request := &data_processing.NFInspectionCountRequestV1{
		FetcherSpec: &data_processing.NFInspectionCountRequestV1_FetchedData_{
			FetchedData: &data_processing.NFInspectionCountRequestV1_FetchedData{
				InspectionRecords: &data_fetching.FMCSAInspectionRecordsV1{
					Records: records,
				},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2023, 1, 15).ToTime()),
	}

	inspectionCount, err := processorClient.GetNFInspectionCountV1(
		ctx,
		request,
	)
	assert.NoError(t, err)
	assert.Equal(t, int64(4), inspectionCount.Count)
}
