package server_tests

import (
	"context"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getLatestValidRatingTierRecordV1TestEnv struct {
	fx.In

	StoreManager           store_management.StoreManager
	ProcessorClientFactory data_processing.ProcessorClientFactory
}

type getLatestValidRatingTierRecordV1TestSuite struct {
	suite.Suite
	ctx   context.Context
	env   getLatestValidRatingTierRecordV1TestEnv
	fxapp *fxtest.App
}

func TestGetLatestValidRatingTierRecordV1(t *testing.T) {
	suite.Run(t, new(getLatestValidRatingTierRecordV1TestSuite))
}

func (s *getLatestValidRatingTierRecordV1TestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)
}

func (s *getLatestValidRatingTierRecordV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getLatestValidRatingTierRecordV1TestSuite) Test_Simple() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	dumpDate := time.Now().AddDate(0, -1, 0)
	/*
		The test data will be defined in order, from most recent to least.
	*/
	record1 := &data_fetching.RatingTierRecordV1{
		Date:                       timestamppb.New(dumpDate),
		InspectionIndicator:        "BOTH",
		LargeMachineryIndicator:    true,
		CrashFrequency:             pointer_utils.ToPointer(0.1),
		PowerUnits:                 pointer_utils.ToPointer(int64(25)),
		AverageMiles:               pointer_utils.ToPointer(15_134.2),
		AverageCombinedGrossWeight: pointer_utils.ToPointer(4_329.2),
		MaintenanceViolationsRatio: pointer_utils.ToPointer(0.01),
		UnsafeViolationRatio:       pointer_utils.ToPointer(0.1),
		VehicleInspectionRatio:     pointer_utils.ToPointer(0.9),
	}
	record2 := &data_fetching.RatingTierRecordV1{
		Date:                       timestamppb.New(dumpDate.AddDate(0, -1, 0)),
		InspectionIndicator:        "BOTH",
		LargeMachineryIndicator:    true,
		CrashFrequency:             pointer_utils.ToPointer(0.1),
		PowerUnits:                 pointer_utils.ToPointer(int64(25)),
		AverageMiles:               pointer_utils.ToPointer(15_134.2),
		AverageCombinedGrossWeight: pointer_utils.ToPointer(4_329.2),
		MaintenanceViolationsRatio: pointer_utils.ToPointer(0.01),
		UnsafeViolationRatio:       pointer_utils.ToPointer(0.1),
		VehicleInspectionRatio:     pointer_utils.ToPointer(0.9),
	}
	record3 := &data_fetching.RatingTierRecordV1{
		Date:                       timestamppb.New(dumpDate.AddDate(0, -2, 0)),
		InspectionIndicator:        "BOTH",
		LargeMachineryIndicator:    false,
		CrashFrequency:             pointer_utils.ToPointer(0.2),
		PowerUnits:                 pointer_utils.ToPointer(int64(50)),
		AverageMiles:               pointer_utils.ToPointer(25_134.2),
		AverageCombinedGrossWeight: pointer_utils.ToPointer(33_329.2),
		MaintenanceViolationsRatio: pointer_utils.ToPointer(0.02),
		UnsafeViolationRatio:       pointer_utils.ToPointer(0.3),
		VehicleInspectionRatio:     pointer_utils.ToPointer(0.6),
	}
	invalidRecord := &data_fetching.RatingTierRecordV1{
		Date:                       timestamppb.New(dumpDate),
		InspectionIndicator:        "BOTH",
		LargeMachineryIndicator:    false,
		CrashFrequency:             pointer_utils.ToPointer(0.2),
		PowerUnits:                 pointer_utils.ToPointer(int64(50)),
		AverageMiles:               nil, // missing average miles which is required
		AverageCombinedGrossWeight: pointer_utils.ToPointer(33_329.2),
		MaintenanceViolationsRatio: pointer_utils.ToPointer(0.02),
		UnsafeViolationRatio:       pointer_utils.ToPointer(0.3),
		VehicleInspectionRatio:     pointer_utils.ToPointer(0.6),
	}

	testCases := []struct {
		name      string
		inputs    []*data_fetching.RatingTierRecordV1
		wantInput *data_fetching.RatingTierRecordV1
		wantErr   bool
	}{
		{
			name: "un-ordered list, returns correct sentry inputs",
			inputs: []*data_fetching.RatingTierRecordV1{
				record2,
				record3,
				record1,
			},
			wantInput: record1,
		},
		{
			name: "ordered list, returns correct sentry inputs",
			inputs: []*data_fetching.RatingTierRecordV1{
				record2,
				record3,
			},
			wantInput: record2,
		},
		{
			name:    "empty monthly sentry inputs, returns error",
			inputs:  nil,
			wantErr: true,
		},
		{
			name: "no valid monthly sentry inputs, returns error",
			inputs: []*data_fetching.RatingTierRecordV1{
				invalidRecord,
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			request := &data_processing.LatestValidRatingTierRecordRequestV1{
				Records: tc.inputs,
			}
			resp, err := client.GetLatestValidRatingTierRecordV1(context.TODO(), request)
			if tc.wantErr {
				s.Error(err)
				return
			}
			s.Require().NoError(err)
			//nolint
			s.EqualExportedValues(*tc.wantInput, *resp)
		})
	}
}
