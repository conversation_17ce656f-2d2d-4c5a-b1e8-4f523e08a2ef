package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getFleetModifiedMVRScoreV1TestEnv struct {
	fx.In
	StoreManager                    store_management.StoreManager
	ProcessorClientFactory          data_processing.ProcessorClientFactory
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
}

type getFleetModifiedMVRScoreV1TestSuite struct {
	suite.Suite
	ctx   context.Context
	env   getFleetModifiedMVRScoreV1TestEnv
	fxapp *fxtest.App
}

func TestGetFleetModifiedMVRScoreV1(t *testing.T) {
	suite.Run(t, new(getFleetModifiedMVRScoreV1TestSuite))
}

func (s *getFleetModifiedMVRScoreV1TestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)
	err := prePopulateViolationStaticDataInStore(s.ctx, s.env.StoreManager)
	s.Require().NoError(err)
}

func (s *getFleetModifiedMVRScoreV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getFleetModifiedMVRScoreV1TestSuite) TestWithoutInterceptors() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	// case 1: Test with valid data
	request := s.buildFleetModifiedMVRScoreRequestV1()
	score, err := client.GetFleetModifiedMVRScoreV1(s.ctx, request)
	s.Require().NoError(err)
	// The score should be greater than 0 since we have violations with ModifiedMVRScore values
	s.Require().Greater(score.Score, int64(0))

	// case 2: Test with nil fetched data
	request.FetcherSpec = nil
	response, err := client.GetFleetModifiedMVRScoreV1(s.ctx, request)
	s.Require().Error(err)
	s.Require().Contains(err.Error(), "fetched data is nil")
	s.Require().Nil(response)
}

func (s *getFleetModifiedMVRScoreV1TestSuite) TestWithStoreInterceptor() {
	contextID := uuid.New()

	interceptors := interceptors_management.NewWritableStoreFirstInterceptors(
		s.env.ReadFromStoreInterceptorFactory,
		s.env.WriteToStoreInterceptorFactory,
		contextID,
	)

	client, closer, err := s.env.ProcessorClientFactory(nil, interceptors...)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	request := s.buildFleetModifiedMVRScoreRequestV1()
	modifiedMVRScore := &data_processing.FleetModifiedMVRScoreV1{Score: 150}

	resource := &common.Resource{Data: modifiedMVRScore}
	err = s.env.StoreManager.Save(s.ctx, contextID, request, resource)
	s.Require().NoError(err)

	exists, err := s.env.StoreManager.Exists(s.ctx, contextID, request)
	s.Require().NoError(err)
	s.Require().True(exists)

	report, err := client.GetFleetModifiedMVRScoreV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().Equal(report.Score, int64(150))
}

func (s *getFleetModifiedMVRScoreV1TestSuite) TestWithCustomInterceptor() {
	customInterceptor := func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		rq, ok := req.(*data_processing.FleetModifiedMVRScoreRequestV1)
		s.Require().True(ok)
		s.Require().Nil(rq.UsState)
		// By changing the state to one that excludes certain violations, we should receive a different score
		rq.UsState = pointer_utils.ToPointer("IL")
		err := invoker(ctx, method, req, reply, cc, opts...)
		s.Require().NoError(err)
		return nil
	}
	client, closer, err := s.env.ProcessorClientFactory(nil, customInterceptor)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	request := s.buildFleetModifiedMVRScoreRequestV1()
	report, err := client.GetFleetModifiedMVRScoreV1(s.ctx, request)
	s.Require().NoError(err)
	// The score should be valid (non-negative)
	s.Require().GreaterOrEqual(report.Score, int64(0))
}

func (s *getFleetModifiedMVRScoreV1TestSuite) buildFleetModifiedMVRScoreRequestV1() *data_processing.FleetModifiedMVRScoreRequestV1 {
	effectiveDate, err := time.Parse(
		"2006-01-02",
		"2024-02-01",
	)
	s.Require().NoError(err)

	report := &data_fetching.MVRReportV1{
		Violations: []*data_fetching.MVRViolationV1{
			{
				// This violation should be included in the score calculation
				// Using violation code 111105 which has ModifiedMVRScore of 9
				AssignedViolationCode: "111105",
				ViolationDate:         timestamppb.New(effectiveDate.Add(-30 * time_utils.Day)),
			},
			{
				// This violation should also be included
				// Using violation code 111110 which has ModifiedMVRScore of 9
				AssignedViolationCode: "111110",
				ViolationDate:         timestamppb.New(effectiveDate.Add(-60 * time_utils.Day)),
			},
			{
				// This violation should be excluded due to age (too old)
				AssignedViolationCode: "111105",
				ViolationDate:         timestamppb.New(effectiveDate.Add(-10 * time_utils.Year)),
			},
		},
	}

	return &data_processing.FleetModifiedMVRScoreRequestV1{
		EffectiveDate: timestamppb.New(effectiveDate),
		FetcherSpec: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData_{
			FetchedData: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData{
				Report: report,
			},
		},
	}
}
