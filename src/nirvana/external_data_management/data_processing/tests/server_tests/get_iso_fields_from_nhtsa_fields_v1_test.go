package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getIsoFieldsFromNhtsaFieldsV1TestEnv struct {
	fx.In
	StoreManager           store_management.StoreManager
	ProcessorClientFactory data_processing.ProcessorClientFactory
}

type getIsoFieldsFromNhtsaFieldsV1TestSuite struct {
	suite.Suite
	ctx   context.Context
	env   getIsoFieldsFromNhtsaFieldsV1TestEnv
	fxapp *fxtest.App
}

func TestGetIsoFieldsFromNhtsaFieldsV1(t *testing.T) {
	suite.Run(t, new(getIsoFieldsFromNhtsaFieldsV1TestSuite))
}

func (s *getIsoFieldsFromNhtsaFieldsV1TestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)
	s.prePopulateViolationStaticDataInStore()
}

func (s *getIsoFieldsFromNhtsaFieldsV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getIsoFieldsFromNhtsaFieldsV1TestSuite) Test_Simple_WhenNoError() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	// This values must match with the ones used to populate the static data
	request := &data_processing.IsoFieldsFromNhtsaFieldsRequestV1{
		FetcherSpec: &data_processing.IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_{
			FetchedData: &data_processing.IsoFieldsFromNhtsaFieldsRequestV1_FetchedData{
				VehicleType:        data_fetching.VehicleTypeV1_VehicleTypeV1_Truck,
				VehicleBodyClass:   "BodyClass1",
				VehicleWeightClass: data_fetching.WeightClassV1_WeightClassV1_A,
			},
		},
	}
	isoFields, err := client.GetIsoFieldsFromNhtsaFieldsV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().Equal(vin.IsoVehicleTypeV1_IsoVehicleTypeV1SemiTrailer, isoFields.VehicleType)
	s.Require().Equal(vin.IsoWeightGroupV1_IsoWeightGroupV1SemiTrailer, isoFields.WeightGroup)
	s.Require().Equal(false, isoFields.IsProblematic)
}

func (s *getIsoFieldsFromNhtsaFieldsV1TestSuite) Test_Simple_WhenIsProblematic() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	// This values must match with the ones used to populate the static data
	request := &data_processing.IsoFieldsFromNhtsaFieldsRequestV1{
		FetcherSpec: &data_processing.IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_{
			FetchedData: &data_processing.IsoFieldsFromNhtsaFieldsRequestV1_FetchedData{
				VehicleType:        data_fetching.VehicleTypeV1_VehicleTypeV1_Trailer,
				VehicleBodyClass:   "BodyClass2",
				VehicleWeightClass: data_fetching.WeightClassV1_WeightClassV1_B,
			},
		},
	}
	isoFields, err := client.GetIsoFieldsFromNhtsaFieldsV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().Equal(vin.IsoVehicleTypeV1_IsoVehicleTypeV1Truck, isoFields.VehicleType)
	s.Require().Equal(vin.IsoWeightGroupV1_IsoWeightGroupV1Heavy, isoFields.WeightGroup)
	s.Require().Equal(true, isoFields.IsProblematic)
}

func (s *getIsoFieldsFromNhtsaFieldsV1TestSuite) Test_Simple_WhenOtherError() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	// This values must match with the ones used to populate the static data
	request := &data_processing.IsoFieldsFromNhtsaFieldsRequestV1{
		FetcherSpec: &data_processing.IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_{
			FetchedData: &data_processing.IsoFieldsFromNhtsaFieldsRequestV1_FetchedData{
				VehicleType:        data_fetching.VehicleTypeV1_VehicleTypeV1_Bus,
				VehicleBodyClass:   "BodyClass3",
				VehicleWeightClass: data_fetching.WeightClassV1_WeightClassV1_C,
			},
		},
	}
	isoFields, err := client.GetIsoFieldsFromNhtsaFieldsV1(s.ctx, request)
	s.Require().Error(err)
	s.Require().Regexp(vin.EntryShouldNotBeKeptErr.Error(), err.Error())
	s.Require().Nil(isoFields)
}

func (s *getIsoFieldsFromNhtsaFieldsV1TestSuite) Test_Simple_WithoutFetchedData() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	request := &data_processing.IsoFieldsFromNhtsaFieldsRequestV1{
		FetcherSpec: nil,
	}
	isoFields, err := client.GetIsoFieldsFromNhtsaFieldsV1(s.ctx, request)
	s.Require().Error(err)
	s.Require().Regexp("fetched data is nil", err.Error())
	s.Require().Nil(isoFields)
}

// In production this should be done manually, everytime the static data changes.
// Because these tests are mostly smoke tests, we aren't testing the exact behavior
// of nhtsa to iso mapping. That was tested in the vin package.
func (s *getIsoFieldsFromNhtsaFieldsV1TestSuite) prePopulateViolationStaticDataInStore() {
	storeKey := &vin.NhtsaToIsoMappingV1StoreKey{}
	mapping := &vin.NhtsaToIsoMappingV1{
		Data: map[string]*vin.NhtsaToIsoEntryV1{
			// This entry should return no error.
			"TRUCK::BODYCLASS1::A": {
				ShouldBeKept:   true,
				IsProblematic:  false,
				IsoVehicleType: vin.IsoVehicleTypeV1_IsoVehicleTypeV1SemiTrailer,
				IsoWeightGroup: vin.IsoWeightGroupV1_IsoWeightGroupV1SemiTrailer,
			},
			// This entry should return vin.EntryIsProblematicErr.
			"TRAILER::BODYCLASS2::B": {
				ShouldBeKept:   true,
				IsProblematic:  true,
				IsoVehicleType: vin.IsoVehicleTypeV1_IsoVehicleTypeV1Truck,
				IsoWeightGroup: vin.IsoWeightGroupV1_IsoWeightGroupV1Heavy,
			},
			// This entry should return vin.EntryShouldNotBeKeptErr.
			"BUS::BODYCLASS3::C": {
				ShouldBeKept:   false,
				IsProblematic:  false,
				IsoVehicleType: vin.IsoVehicleTypeV1_IsoVehicleTypeV1Empty,
				IsoWeightGroup: vin.IsoWeightGroupV1_IsoWeightGroupV1Empty,
			},
		},
	}

	err := static_data_utils.DeleteStaticData(s.ctx, s.env.StoreManager, storeKey)
	s.Require().NoError(err)

	resource := &common.Resource{Data: mapping}
	err = static_data_utils.UploadStaticData(s.ctx, s.env.StoreManager, resource, storeKey)
	s.Require().NoError(err)
}
