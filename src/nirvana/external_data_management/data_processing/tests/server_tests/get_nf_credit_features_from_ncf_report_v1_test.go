package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/protobuf/types/known/timestamppb"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/data_processing/national_credit_file"
	"nirvanatech.com/nirvana/external_data_management/data_processing/tests/server_tests/builders"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type requestArgs struct {
	ncfReport             *data_fetching.NcfReportV1
	dateOfCreditReportRun time.Time
	ownerDOB              time.Time
	ownerName             string

	modifiedOwnerDOB time.Time
}

type getNFCreditFeaturesV1TestEnv struct {
	fx.In
	StoreManager           store_management.StoreManager
	ProcessorClientFactory data_processing.ProcessorClientFactory
}

type getNFCreditFeaturesV1TestSuite struct {
	suite.Suite
	ctx   context.Context
	env   getNFCreditFeaturesV1TestEnv
	fxapp *fxtest.App
	args  requestArgs
}

func TestGetNFCreditFeaturesV1(t *testing.T) {
	suite.Run(t, new(getNFCreditFeaturesV1TestSuite))
}

func (s *getNFCreditFeaturesV1TestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)
	s.args = requestArgs{
		ncfReport:             &data_fetching.NcfReportV1{NcfProductReport: builders.BuildDefaultNationalCreditFileV1ForTest()},
		ownerDOB:              time.Date(1962, 5, 18, 0, 0, 0, 0, time.UTC),
		modifiedOwnerDOB:      time.Date(1960, 5, 18, 0, 0, 0, 0, time.UTC),
		dateOfCreditReportRun: time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC),
		ownerName:             "John Doe",
	}
}

func (s *getNFCreditFeaturesV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getNFCreditFeaturesV1TestSuite) Test_Simple() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	creditFile := &data_fetching.NationalCreditFileV1{
		NcfReport: s.args.ncfReport,
		TransactionDetails: &data_fetching.TransactionDetailsExV1{
			ProcessingStatus: national_credit_file.NCFSuccessStatusCode,
		},
		DateOfCreditReportRun: timestamppb.New(s.args.dateOfCreditReportRun),
	}
	request := &data_processing.NFCreditFeaturesRequestV1{
		OwnerDOB:           timestamppb.New(s.args.ownerDOB),
		OwnerName:          s.args.ownerName,
		NationalCreditFile: creditFile,
	}
	nfCreditFeatures, err := client.GetNFCreditFeaturesV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().NotNil(nfCreditFeatures)

	expected := &data_processing.NFCreditFeaturesV1{
		DateOfOldestTradeline:                                       timestamppb.New(time.Date(2012, 4, 1, 0, 0, 0, 0, time.UTC)),
		OwnerAgeAtEarliestTradelineInYears:                          49,
		DateOfMostRecentAutoTrade:                                   timestamppb.New(time.Date(2021, 6, 1, 0, 0, 0, 0, time.UTC)),
		NumberOfCurrentSatisfactoryTrades:                           12,
		NumberOfTradesWithDelinquency:                               7,
		MonthsSinceMostRecentWriteOff:                               93,
		NumberOfDerogatoryInstallmentTrades:                         0,
		RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades: 0.4868421,
		RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades:  0.********,
		NumberOfTradeLinesOpenedInLast12Months:                      0,
		MonthsSinceMostRecentBankRevolvingTradeOpened:               53,
		NumberOfNonInsuranceInquiries:                               2,
		AgeOfOldestTradelineInMonths:                                146,
		AgeOfMostRecentAutoTradeInMonths:                            36,
		DateOfEarliestTradeline:                                     timestamppb.New(time.Date(2012, 4, 1, 0, 0, 0, 0, time.UTC)),
		NoHit:                                                       false,
		ThinFile:                                                    false,
		IsOwnerAgeAllowed:                                           true,
		OwnerAgeAtCreditReportRunInYears:                            pointer_utils.ToPointer(int32(62)),
		DateOfCreditReportRun:                                       timestamppb.New(time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC)),
	}
	//nolint
	s.Require().EqualExportedValues(*expected, *nfCreditFeatures)
}

func (s *getNFCreditFeaturesV1TestSuite) Test_NoHit() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	creditFile := &data_fetching.NationalCreditFileV1{
		NcfReport: nil,
		TransactionDetails: &data_fetching.TransactionDetailsExV1{
			ProcessingStatus: national_credit_file.NCFNoHitStatusCode,
		},
		DateOfCreditReportRun: timestamppb.New(s.args.dateOfCreditReportRun),
	}
	request := &data_processing.NFCreditFeaturesRequestV1{
		OwnerDOB:           timestamppb.New(s.args.ownerDOB),
		OwnerName:          s.args.ownerName,
		NationalCreditFile: creditFile,
	}
	nfCreditFeatures, err := client.GetNFCreditFeaturesV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().NotNil(nfCreditFeatures)
	s.Require().True(nfCreditFeatures.NoHit)
}
