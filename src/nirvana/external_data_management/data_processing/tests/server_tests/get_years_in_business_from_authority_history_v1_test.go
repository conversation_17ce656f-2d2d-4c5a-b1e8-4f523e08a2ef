package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_GetYearsInBusinessFromAuthorityHistoryV1(t *testing.T) {
	var env struct {
		fx.In

		ProcessorClientFactory data_processing.ProcessorClientFactory
	}

	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()

	processorClient, closerP, err := env.ProcessorClientFactory(nil)
	require.NoError(t, err)
	defer func() { _ = closerP() }()

	testCases := []struct {
		name       string
		request    *data_processing.YearsInBusinessFromAuthorityHistoryRequestV1
		want       float64
		wantErr    bool
		wantErrMsg string
	}{
		{
			name: "nil fetched data: should return error",
			request: &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1{
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
				FetcherSpec:   nil,
			},
			wantErr:    true,
			wantErrMsg: "fetched data is nil",
		},
		{
			name: "empty authority history records: should return error",
			request: &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1{
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
				FetcherSpec: &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_{
					FetchedData: &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData{
						AuthorityHistoryRecords: []*data_fetching.AuthorityHistoryRecordV1{},
					},
				},
			},
			wantErr:    true,
			wantErrMsg: "empty authority history records",
		},
		{
			name: "authority history with no gaps and effective date at the end of first record: should return 1.0 yib",
			request: buildRequest(
				time_utils.NewDate(2024, 1, 1),
				buildRecordsFromIntervals(
					1234567,
					[]time_utils.DateInterval{
						{
							Start: time_utils.NewDate(2023, 1, 1),
							End:   time_utils.NewDate(2024, 1, 1),
						},
						{
							Start: time_utils.NewDate(2024, 1, 1),
							End:   time_utils.DateFromTime(time.Time{}),
						},
					}),
			),
			want: 1.0,
		},
		{
			name: "authority history with no gaps and effective date within last record: should return 4.5 yib",
			request: buildRequest(
				time_utils.NewDate(2024, 7, 1),
				buildRecordsFromIntervals(
					1234568,
					[]time_utils.DateInterval{
						{
							Start: time_utils.NewDate(2020, 1, 1),
							End:   time_utils.NewDate(2020, 6, 1),
						},
						{
							Start: time_utils.NewDate(2020, 6, 1),
							End:   time_utils.NewDate(2021, 1, 1),
						},
						{
							Start: time_utils.NewDate(2021, 1, 1),
							End:   time_utils.NewDate(2022, 1, 1),
						},
						{
							Start: time_utils.NewDate(2022, 1, 1),
							End:   time_utils.NewDate(2023, 1, 1),
						},
						{
							Start: time_utils.NewDate(2023, 1, 1),
							End:   time_utils.NewDate(2024, 1, 1),
						},
						{
							Start: time_utils.NewDate(2024, 1, 1),
							End:   time_utils.DateFromTime(time.Time{}),
						},
					}),
			),
			want: 4.5013, // 1463/365
		},
		{
			name: "authority history with short gaps (<=30 days): should consider complete window of operation",
			request: buildRequest(
				time_utils.NewDate(2024, 6, 1),
				buildRecordsFromIntervals(1234569, []time_utils.DateInterval{
					{
						Start: time_utils.NewDate(2022, 6, 1),
						End:   time_utils.NewDate(2023, 5, 15),
					},
					// Gap 1 of 15 days
					{
						Start: time_utils.NewDate(2023, 6, 1),
						End:   time_utils.NewDate(2024, 1, 1),
					},
					{
						Start: time_utils.NewDate(2024, 1, 1),
						End:   time_utils.DateFromTime(time.Time{}),
					},
				}),
			),
			want: 2.0027, // (355 + 366) / 365
		},
		{
			name: "authority history with large gap (>30 day) and inactive: should consider only the window of operation",
			request: buildRequest(
				time_utils.NewDate(2024, 7, 1),
				buildRecordsFromIntervals(
					1234561,
					[]time_utils.DateInterval{
						{
							Start: time_utils.NewDate(2022, 6, 1),
							End:   time_utils.NewDate(2023, 6, 1),
						},
						{
							Start: time_utils.NewDate(2023, 6, 1),
							End:   time_utils.NewDate(2024, 5, 1),
						},
					}),
			),
			want: 1.9178, // (365 + 335) / 365
		},
		{
			name: "authority history with large gap (>30 day) and active: should consider second window of operation",
			request: buildRequest(
				time_utils.NewDate(2024, 7, 1),
				buildRecordsFromIntervals(
					3214561,
					[]time_utils.DateInterval{
						// first 3-year window of operation
						{
							Start: time_utils.NewDate(2019, 2, 1),
							End:   time_utils.NewDate(2020, 2, 1),
						},
						{
							Start: time_utils.NewDate(2020, 2, 1),
							End:   time_utils.NewDate(2021, 2, 1),
						},
						{
							Start: time_utils.NewDate(2021, 2, 1),
							End:   time_utils.NewDate(2022, 2, 1),
						},
						// gap in authority history; second window of active operation
						{
							Start: time_utils.NewDate(2022, 6, 1),
							End:   time_utils.NewDate(2023, 6, 1),
						},
						{
							Start: time_utils.NewDate(2023, 6, 1),
							End:   time_utils.DateFromTime(time.Time{}),
						},
					}),
			),
			want: 2.0849, // (365 + 396) / 365
		},
		{
			name: "authority history with large gap (>30 day) and inactive: should consider second window of operation",
			request: buildRequest(
				time_utils.NewDate(2024, 7, 1),
				buildRecordsFromIntervals(
					3224561,
					[]time_utils.DateInterval{
						// first 5-year window of operation
						{
							Start: time_utils.NewDate(2015, 2, 1),
							End:   time_utils.NewDate(2020, 2, 1),
						},
						// second 3-year window of operation
						{
							Start: time_utils.NewDate(2021, 2, 1),
							End:   time_utils.NewDate(2022, 2, 1),
						},
						{
							Start: time_utils.NewDate(2022, 2, 1),
							End:   time_utils.NewDate(2023, 2, 1),
						},
					}),
			),
			want: 2.0, // 730 / 365
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := processorClient.GetYearsInBusinessFromAuthorityHistoryV1(ctx, tc.request)
			if tc.wantErr {
				require.Error(t, err)
				require.Contains(t, err.Error(), tc.wantErrMsg)
				return
			}
			require.NoError(t, err)
			assert.InDelta(t, tc.want, got.Years, 0.001)
		})
	}
}

// Helper functions to build the request and records
func buildRequest(effectiveDate time_utils.Date, records []*data_fetching.AuthorityHistoryRecordV1) *data_processing.YearsInBusinessFromAuthorityHistoryRequestV1 {
	return &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1{
		EffectiveDate: timestamppb.New(effectiveDate.ToTime()),
		FetcherSpec: &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_{
			FetchedData: &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData{
				AuthorityHistoryRecords: records,
			},
		},
	}
}

func buildRecordsFromIntervals(dotNumber int, intervals []time_utils.DateInterval) []*data_fetching.AuthorityHistoryRecordV1 {
	var records []*data_fetching.AuthorityHistoryRecordV1
	for _, interval := range intervals {
		record := &data_fetching.AuthorityHistoryRecordV1{
			DotNumber:                int64(dotNumber),
			OriginalActionServedDate: timestamppb.New(interval.Start.ToTime()),
		}
		if !time_utils.IsZero(interval.End.ToTime()) {
			record.FinalActionServedDate = timestamppb.New(interval.End.ToTime())
		}
		records = append(records, record)
	}
	return records
}
