load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "server_tests_test",
    srcs = [
        "get_continuos_coverage_years_v1_test.go",
        "get_fleet_modified_mvr_score_v1_test.go",
        "get_fleet_moving_violation_count_v1_test.go",
        "get_iso_fields_from_nhtsa_fields_v1_test.go",
        "get_latest_valid_rating_tier_record_v1_test.go",
        "get_nf_credit_features_from_ncf_report_v1_test.go",
        "get_nf_inspection_count_v1_test.go",
        "get_nf_violations_metadata_v1_test.go",
        "get_prior_carrier_years_retained_v1_test.go",
        "get_retained_years_v1_test.go",
        "get_years_in_business_from_authority_history_v1_test.go",
        "get_years_in_business_from_insurance_history_v1_test.go",
    ],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/data_processing/mvr",
        "//nirvana/external_data_management/data_processing/national_credit_file",
        "//nirvana/external_data_management/data_processing/static_data_utils",
        "//nirvana/external_data_management/data_processing/tests/server_tests/builders",
        "//nirvana/external_data_management/data_processing/vin",
        "//nirvana/external_data_management/interceptors_management",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/fx/testloader",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
