package server_tests

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_GetNFViolationsMetadataV1_Simple(t *testing.T) {
	var env struct {
		fx.In

		ProcessorClientFactory data_processing.ProcessorClientFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	processorClient, closer, err := env.ProcessorClientFactory(nil)
	assert.NoError(t, err)
	defer func() { _ = closer() }()

	ctx := context.Background()

	records := []*data_fetching.FMCSAViolationRecordV1{
		{
			RowId:               "be8fabda-c4ba-5295-bced-666cb99dc624",
			PublishedDate:       timestamppb.New(time_utils.NewDate(2023, 8, 25).ToTime()),
			InspectionId:        76246428,
			InspectionDate:      timestamppb.New(time_utils.NewDate(2022, 8, 4).ToTime()),
			DotNumber:           1460168,
			OosIndicator:        true,
			Code:                "39216",
			Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving,
			SeverityWeight:      7,
			TotalSeverityWeight: 7,
			TimeWeight:          1,
			ViolationId:         pointer_utils.ToPointer(int32(247542809)),
			CountyCode:          pointer_utils.ToPointer("115"),
			CountyCodeState:     pointer_utils.ToPointer("MI"),
		},
		{
			RowId:               "31d0ef9b-9ef1-54b2-b98c-6cc0ba7ecc8c",
			PublishedDate:       timestamppb.New(time_utils.NewDate(2023, 8, 25).ToTime()),
			InspectionId:        76246428,
			InspectionDate:      timestamppb.New(time_utils.NewDate(2022, 8, 4).ToTime()),
			DotNumber:           1460168,
			OosIndicator:        false,
			Code:                "3939",
			Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
			SeverityWeight:      2,
			TotalSeverityWeight: 2,
			TimeWeight:          1,
			ViolationId:         pointer_utils.ToPointer(int32(247542810)),
			CountyCode:          pointer_utils.ToPointer("115"),
			CountyCodeState:     pointer_utils.ToPointer("MI"),
		},
		{
			RowId:               "19ec2c99-f232-5bcf-9439-fa66ce7769ff",
			PublishedDate:       timestamppb.New(time_utils.NewDate(2023, 11, 24).ToTime()),
			InspectionId:        78955413,
			InspectionDate:      timestamppb.New(time_utils.NewDate(2023, 6, 21).ToTime()),
			DotNumber:           1460168,
			OosIndicator:        false,
			Code:                "3965b",
			Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
			SeverityWeight:      3,
			TotalSeverityWeight: 3,
			TimeWeight:          3,
			ViolationId:         pointer_utils.ToPointer(int32(253925582)),
			CountyCode:          pointer_utils.ToPointer("67"),
			CountyCodeState:     pointer_utils.ToPointer("KY"),
		},
		{
			RowId:               "566ee783-af09-52d4-b7a9-0f789d230e76",
			PublishedDate:       timestamppb.New(time_utils.NewDate(2023, 8, 25).ToTime()),
			InspectionId:        78955413,
			InspectionDate:      timestamppb.New(time_utils.NewDate(2023, 6, 21).ToTime()),
			DotNumber:           1460168,
			OosIndicator:        false,
			Code:                "3939",
			Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
			SeverityWeight:      2,
			TotalSeverityWeight: 2,
			TimeWeight:          3,
			ViolationId:         pointer_utils.ToPointer(int32(253925585)),
			CountyCode:          pointer_utils.ToPointer("67"),
			CountyCodeState:     pointer_utils.ToPointer("KY"),
		},
		{
			RowId:               "1a18b94c-0dec-5493-a89f-eb13f86b5341",
			PublishedDate:       timestamppb.New(time_utils.NewDate(2023, 8, 25).ToTime()),
			InspectionId:        78955413,
			InspectionDate:      timestamppb.New(time_utils.NewDate(2023, 6, 21).ToTime()),
			DotNumber:           1460168,
			OosIndicator:        false,
			Code:                "39347e",
			Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
			SeverityWeight:      4,
			TotalSeverityWeight: 4,
			TimeWeight:          3,
			ViolationId:         pointer_utils.ToPointer(int32(253925590)),
			CountyCode:          pointer_utils.ToPointer("67"),
			CountyCodeState:     pointer_utils.ToPointer("KY"),
		},
		{
			RowId:               "6fcf7b57-42c4-5766-8ab8-893ed6ca7943",
			PublishedDate:       timestamppb.New(time_utils.NewDate(2023, 8, 25).ToTime()),
			InspectionId:        77307249,
			InspectionDate:      timestamppb.New(time_utils.NewDate(2022, 12, 7).ToTime()),
			DotNumber:           1460168,
			OosIndicator:        true,
			Code:                "3939",
			Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
			SeverityWeight:      2,
			TotalSeverityWeight: 2,
			TimeWeight:          2,
			ViolationId:         pointer_utils.ToPointer(int32(249968076)),
			CountyCode:          pointer_utils.ToPointer("175"),
			CountyCodeState:     pointer_utils.ToPointer("OH"),
		},
		{
			RowId:               "bd665110-d1bb-533f-9c67-f81ecb82209c",
			PublishedDate:       timestamppb.New(time_utils.NewDate(2023, 8, 25).ToTime()),
			InspectionId:        77711921,
			InspectionDate:      timestamppb.New(time_utils.NewDate(2023, 1, 26).ToTime()),
			DotNumber:           1460168,
			OosIndicator:        false,
			Code:                "39216",
			Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_UnsafeDriving,
			SeverityWeight:      7,
			TotalSeverityWeight: 7,
			TimeWeight:          2,
			ViolationId:         pointer_utils.ToPointer(int32(250821595)),
			CountyCode:          pointer_utils.ToPointer("39"),
			CountyCodeState:     pointer_utils.ToPointer("OH"),
		},
		// Should be ignored: Inspection date (2020-07-21) is before valid period start (2021-01-15)
		// Valid period: 2021-01-15 (3years before effectiveDate) to 2024-01-15 (effectiveDate)
		{
			RowId:               "32cf3004-0927-521d-8c00-6f09880c9b2f",
			PublishedDate:       timestamppb.New(time_utils.NewDate(2023, 8, 25).ToTime()),
			InspectionId:        79283708,
			InspectionDate:      timestamppb.New(time_utils.NewDate(2020, 7, 31).ToTime()),
			DotNumber:           1460168,
			OosIndicator:        true,
			Code:                "39347e",
			Category:            data_fetching.FMCSAViolationCategoryV1_FMCSAViolationCategoryV1_VehicleMaintenance,
			SeverityWeight:      4,
			TotalSeverityWeight: 4,
			TimeWeight:          3,
			ViolationId:         pointer_utils.ToPointer(int32(254510829)),
			CountyCode:          pointer_utils.ToPointer("97"),
			CountyCodeState:     pointer_utils.ToPointer("OH"),
		},
	}

	request := &data_processing.NFViolationsMetadataRequestV1{
		FetcherSpec: &data_processing.NFViolationsMetadataRequestV1_FetchedData_{
			FetchedData: &data_processing.NFViolationsMetadataRequestV1_FetchedData{
				ViolationRecords: &data_fetching.FMCSAViolationRecordsV1{Records: records},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 15).ToTime()),
	}

	violationsMetadata, err := processorClient.GetNFViolationsMetadataV1(
		ctx,
		request,
	)
	assert.NoError(t, err)
	assert.Equal(t, int64(2), violationsMetadata.OosViolations)
	assert.Equal(t, int64(5), violationsMetadata.NonoosViolations)
	assert.Equal(t, int64(9), violationsMetadata.OosSeverityWeight)
	assert.Equal(t, int64(18), violationsMetadata.NosoosSeverityWeight)
}
