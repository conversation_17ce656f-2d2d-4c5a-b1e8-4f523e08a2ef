package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/data_processing/mvr"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type getFleetMovingViolationCountV1TestEnv struct {
	fx.In
	StoreManager                    store_management.StoreManager
	ProcessorClientFactory          data_processing.ProcessorClientFactory
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
}

type getFleetMovingViolationCountV1TestSuite struct {
	suite.Suite
	ctx   context.Context
	env   getFleetMovingViolationCountV1TestEnv
	fxapp *fxtest.App
}

// In this suite I'm also testing the usage and integration of interceptors, both user provided and predefined,
// with the processor client. When adding new methods to the processor server, you don't need to test this again.
// Just test behavior that is specific to your method.
func TestGetFleetMovingViolationCountV1(t *testing.T) {
	suite.Run(t, new(getFleetMovingViolationCountV1TestSuite))
}

func (s *getFleetMovingViolationCountV1TestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)
	err := prePopulateViolationStaticDataInStore(s.ctx, s.env.StoreManager)
	s.Require().NoError(err)
}

func (s *getFleetMovingViolationCountV1TestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *getFleetMovingViolationCountV1TestSuite) TestWithoutInterceptors() {
	client, closer, err := s.env.ProcessorClientFactory(nil)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	// case 1
	request := s.buildFleetMovingViolationCountRequestV1()
	count, err := client.GetFleetMovingViolationCountV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().Equal(count.Count, int64(1))

	// case 2
	request.FetcherSpec = nil
	response, err := client.GetFleetMovingViolationCountV1(s.ctx, request)
	s.Require().Error(err)
	s.Require().Contains(err.Error(), "fetched data is nil")
	s.Require().Nil(response)
}

// Note that in this test we use ReadModeStoreOnly.
func (s *getFleetMovingViolationCountV1TestSuite) TestWithStoreInterceptor() {
	contextID := uuid.New()

	interceptors := interceptors_management.NewWritableStoreFirstInterceptors(
		s.env.ReadFromStoreInterceptorFactory,
		s.env.WriteToStoreInterceptorFactory,
		contextID,
	)

	client, closer, err := s.env.ProcessorClientFactory(nil, interceptors...)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	request := s.buildFleetMovingViolationCountRequestV1()
	movingViolationCount := &data_processing.FleetMovingViolationCountV1{Count: 101}

	resource := &common.Resource{Data: movingViolationCount}
	err = s.env.StoreManager.Save(s.ctx, contextID, request, resource)
	s.Require().NoError(err)

	exists, err := s.env.StoreManager.Exists(s.ctx, contextID, request)
	s.Require().NoError(err)
	s.Require().True(exists)

	report, err := client.GetFleetMovingViolationCountV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().Equal(report.Count, int64(101))
}

func (s *getFleetMovingViolationCountV1TestSuite) TestWithCustomInterceptor() {
	customInterceptor := func(
		ctx context.Context,
		method string,
		req any,
		reply any,
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		rq, ok := req.(*data_processing.FleetMovingViolationCountRequestV1)
		s.Require().True(ok)
		s.Require().Nil(rq.UsState)
		// By changing the state, we should receive a zero count.
		rq.UsState = pointer_utils.ToPointer("IL")
		rq.IsExcludedMVCEnabled = true
		err := invoker(ctx, method, req, reply, cc, opts...)
		s.Require().NoError(err)
		return nil
	}
	client, closer, err := s.env.ProcessorClientFactory(nil, customInterceptor)
	s.Require().NoError(err)
	defer func() { _ = closer() }()

	request := s.buildFleetMovingViolationCountRequestV1()
	report, err := client.GetFleetMovingViolationCountV1(s.ctx, request)
	s.Require().NoError(err)
	s.Require().Equal(int64(0), report.Count)
}

func (s *getFleetMovingViolationCountV1TestSuite) buildFleetMovingViolationCountRequestV1() *data_processing.FleetMovingViolationCountRequestV1 {
	effectiveDate, err := time.Parse(
		"2006-01-02",
		"2024-02-01",
	)
	s.Require().NoError(err)

	report := &data_fetching.MVRReportV1{
		Violations: []*data_fetching.MVRViolationV1{
			{
				// This violation should not be considered, because even though it's a moving violation,
				// its date is such that effectiveDate - durationToConsider > violationDate.
				AssignedViolationCode: "1",
				ViolationDate:         timestamppb.New(effectiveDate.Add(-5 * time_utils.Day)),
			},
			{
				// This violation should not be considered, because it's not a moving violation.
				AssignedViolationCode: "2",
				ViolationDate:         timestamppb.New(effectiveDate.Add(-5 * time_utils.Day)),
			},
		},
	}

	return &data_processing.FleetMovingViolationCountRequestV1{
		EffectiveDate: timestamppb.New(effectiveDate),
		FetcherSpec: &data_processing.FleetMovingViolationCountRequestV1_FetchedData_{
			FetchedData: &data_processing.FleetMovingViolationCountRequestV1_FetchedData{
				Report: report,
			},
		},
	}
}

// In production this should be done manually, everytime the static data changes.
// Because these tests are mostly smoke tests, we aren't testing the exact behavior
// of violation count. That was tested in the mvr package.
func prePopulateViolationStaticDataInStore(ctx context.Context, storeManager store_management.StoreManager) error {
	storeKey := &mvr.FleetViolationsDataV1StoreKey{}
	violationData := &mvr.FleetViolationsDataV1{
		Data: map[int64]*mvr.FleetViolationV1{
			1: {
				IsMovingViolation:  true,
				DurationToConsider: int64(5 * time_utils.Year),
				ExcludedStates: map[string]bool{
					"IL": true,
				},
			},
			2: {
				IsMovingViolation:  false,
				DurationToConsider: int64(5 * time_utils.Year),
			},
			111105: { // DRIVE WHILE LICENSE SUSPENDED
				Description:        "DRIVE WHILE LICENSE IS SUSP/REVO/DENI/DISQ/CANC RESULTING IN ACCI",
				IsMovingViolation:  true,
				DurationToConsider: int64(5 * time_utils.Year),
				ExcludedStates:     map[string]bool{},
				ModifiedMVRScore:   9,
			},
			111110: { // RECKLESS DRIVING
				Description:        "RECKLESS DRIVING, WILLFUL AND WANTON DISREGARD",
				IsMovingViolation:  true,
				DurationToConsider: int64(5 * time_utils.Year),
				ExcludedStates: map[string]bool{
					"TX": true, // Excluded in Texas for testing state exclusion logic
				},
				ModifiedMVRScore: 9,
			},
			111210: { // NEGLIGENT DRIVING
				Description:        "NEGLIGENT DRIVING",
				IsMovingViolation:  true,
				DurationToConsider: int64(5 * time_utils.Year),
				ExcludedStates: map[string]bool{
					"TX": true, // Excluded in Texas for testing state exclusion logic
				},
				ModifiedMVRScore: 8,
			},
			111300: { // DUI - GENERAL
				Description:        "DUI - GENERAL",
				IsMovingViolation:  true,
				DurationToConsider: int64(5 * time_utils.Year),
				ExcludedStates:     map[string]bool{}, // No exclusions for testing
				ModifiedMVRScore:   10,                // Highest score for testing
			},
		},
	}

	err := static_data_utils.DeleteStaticData(ctx, storeManager, storeKey)
	if err != nil {
		return err
	}

	resource := &common.Resource{Data: violationData}
	err = static_data_utils.UploadStaticData(ctx, storeManager, resource, storeKey)
	if err != nil {
		return err
	}

	return nil
}
