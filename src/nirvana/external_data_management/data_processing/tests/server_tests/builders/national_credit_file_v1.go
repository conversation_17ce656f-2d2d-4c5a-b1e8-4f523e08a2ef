package builders

import (
	_ "embed"
	"encoding/json"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

//go:embed data/national_credit_file.json
var report []byte

func BuildDefaultNationalCreditFileV1ForTest() *data_fetching.NcfProductReportV1 {
	var ncfReport data_fetching.NcfReportV1
	err := json.Unmarshal(report, &ncfReport)
	if err != nil {
		return ncfReport.NcfProductReport
	}
	return ncfReport.NcfProductReport
}
