package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_GetRetainedYearsV1_Scenarios(t *testing.T) {
	var env struct {
		fx.In
		ProcessorClientFactory data_processing.ProcessorClientFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	processorClient, closer, err := env.ProcessorClientFactory(nil)
	assert.NoError(t, err)
	defer func() { _ = closer() }()

	ctx := context.Background()
	effectiveDate := timestamppb.New(time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC))

	id1 := "a73d0d05-89ba-4c17-bb77-863a6fb3955b"
	id2 := "caf16224-de3e-412d-9e70-82bc2fa5bda1"
	id3 := "45b8ef33-e186-4a2c-80d8-db63d65d746c"

	tests := []struct {
		name          string
		request       *data_processing.RetainedYearsRequestV1
		expectedYears int32
		expectError   bool
		errorContains string
	}{
		{
			name: "nil fetcher spec",
			request: &data_processing.RetainedYearsRequestV1{
				FetcherSpec:   nil,
				EffectiveDate: effectiveDate,
			},
			expectError:   true,
			errorContains: "invalid request data",
		},
		{
			name: "empty policies",
			request: &data_processing.RetainedYearsRequestV1{
				FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
					FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
						Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
							Policies: []*data_fetching.NirvanaPolicyV1{},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 0,
		},
		{
			name: "overlapping policies",
			request: &data_processing.RetainedYearsRequestV1{
				FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
					FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
						Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
							Policies: []*data_fetching.NirvanaPolicyV1{
								{
									Id:              id1,
									PolicyNumber:    "POL-001",
									EffectiveDate:   timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
									EffectiveDateTo: timestamppb.New(time.Date(2022, 6, 30, 0, 0, 0, 0, time.UTC)),
								},
								{
									Id:              id2,
									PolicyNumber:    "POL-002",
									EffectiveDate:   timestamppb.New(time.Date(2022, 6, 29, 0, 0, 0, 0, time.UTC)), // Overlapping
									EffectiveDateTo: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
								},
								{
									Id:              id3,
									PolicyNumber:    "POL-003",
									EffectiveDate:   timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
									EffectiveDateTo: timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
								},
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 4, // Should count from 2020 to mid2024
		},
		{
			name: "end date after effective date",
			request: &data_processing.RetainedYearsRequestV1{
				FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
					FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
						Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
							Policies: []*data_fetching.NirvanaPolicyV1{

								{
									Id:              id2,
									PolicyNumber:    "POL-005",
									EffectiveDate:   timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)), // 1 year gap
									EffectiveDateTo: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
								},
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 0, // Should not count any years due to the gap
		},
		{
			name: "gap between policies",
			request: &data_processing.RetainedYearsRequestV1{
				FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
					FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
						Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
							Policies: []*data_fetching.NirvanaPolicyV1{
								{
									Id:              id1,
									PolicyNumber:    "POL-006",
									EffectiveDate:   timestamppb.New(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
									EffectiveDateTo: timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
								},
								{
									Id:              id2,
									PolicyNumber:    "POL-007",
									EffectiveDate:   timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
									EffectiveDateTo: timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
								},
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 1, // 2023-mid2024
		},
		{
			name: "policy active at effective date",
			request: &data_processing.RetainedYearsRequestV1{
				FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
					FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
						Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
							Policies: []*data_fetching.NirvanaPolicyV1{
								{
									Id:              id1,
									PolicyNumber:    "POL-010",
									EffectiveDate:   timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
									EffectiveDateTo: timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
								},
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 1, // Active policy spanning 2023-mid2024
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			retainedYears, err := processorClient.GetRetainedYearsV1(ctx, tt.request)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorContains)
				assert.Nil(t, retainedYears)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedYears, retainedYears.Years)
			}
		})
	}
}
