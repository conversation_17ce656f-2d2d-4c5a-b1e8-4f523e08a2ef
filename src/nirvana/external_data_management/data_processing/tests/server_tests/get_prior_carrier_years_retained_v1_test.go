package server_tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_GetPriorCarrierYearsRetainedV1_Scenarios(t *testing.T) {
	var env struct {
		fx.In
		ProcessorClientFactory data_processing.ProcessorClientFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	processorClient, closer, err := env.ProcessorClientFactory(nil)
	assert.NoError(t, err)
	defer func() { _ = closer() }()

	ctx := context.Background()
	effectiveDate := timestamppb.New(time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC))

	tests := []struct {
		name          string
		request       *data_processing.PriorCarrierYearsRetainedRequestV1
		expectedYears int32
		expectError   bool
		errorContains string
	}{
		{
			name: "nil fetched data",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec:   nil,
				EffectiveDate: effectiveDate,
			},
			expectError:   true,
			errorContains: "fetched data is nil",
		},
		{
			name: "empty insurance records",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 0,
		},
		{
			name: "only target carriers (should be filtered out)",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "SIRIUSPOINT AMERICA INSURANCE COMPANY", // Target carrier
								EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
							},
							{
								Id:                   "record2",
								DotNumber:            123456,
								InsuranceCompanyName: "FALLS LAKE NATIONAL INSURANCE COMPANY", // Target carrier
								EffectiveDate:        timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 0, // All target carriers should be filtered out
		},
		{
			name: "single prior carrier",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "ABC Insurance Company",
								EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 3, // 2020-2023 = 3 years
		},
		{
			name: "multiple carriers - most recent should be selected",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "Old Insurance Company",
								EffectiveDate:        timestamppb.New(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
							},
							{
								Id:                   "record2",
								DotNumber:            123456,
								InsuranceCompanyName: "Recent Insurance Company",
								EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2023, 6, 1, 0, 0, 0, 0, time.UTC)),
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 2, // Most recent carrier: 2021-2023.5 = ~2.5 years, floored to 2
		},
		{
			name: "end date after effective date - should use effective date",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "Future Insurance Company",
								EffectiveDate:        timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)), // After effective date
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 2, // 2022-2024.5 = ~2.5 years, floored to 2
		},
		{
			name: "mixed target and non-target carriers",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "MS TRANSVERSE INSURANCE COMPANY", // Target carrier - should be filtered
								EffectiveDate:        timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC)),
							},
							{
								Id:                   "record2",
								DotNumber:            123456,
								InsuranceCompanyName: "Valid Prior Carrier",
								EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 2, // Only non-target carrier should be considered: 2020-2022 = 2 years
		},
		{
			name: "case insensitive target carrier matching",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "siriuspoint america insurance company", // Lowercase target carrier
								EffectiveDate:        timestamppb.New(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 0, // Should be filtered out due to case-insensitive matching
		},
		{
			name: "record with no cancel date - uses effective date as end date",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "No Cancel Date Company",
								EffectiveDate:        timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
								CancelEffectiveDate:  nil, // No cancel date
							},
						},
					},
				},
				EffectiveDate: effectiveDate,
			},
			expectedYears: 3, // 2021-2024.5 = ~3.5 years, floored to 3
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processorClient.GetPriorCarrierYearsRetainedV1(ctx, tt.request)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorContains)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedYears, result.Years)
			}
		})
	}
}
