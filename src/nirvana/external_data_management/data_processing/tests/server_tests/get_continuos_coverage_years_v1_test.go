package server_tests

import (
	"context"
	"math"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_GetContinuousCoverageYearsV1_Simple(t *testing.T) {
	var env struct {
		fx.In

		ProcessorClientFactory data_processing.ProcessorClientFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	processorClient, closer, err := env.ProcessorClientFactory(nil)
	assert.NoError(t, err)
	defer func() { _ = closer() }()

	ctx := context.Background()
	effectiveDate := time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC)

	// case 1
	request := &data_processing.ContinuousCoverageYearsRequestV1{
		FetcherSpec:   nil,
		EffectiveDate: timestamppb.New(effectiveDate),
	}
	continuousCoverageYears, err := processorClient.GetContinuousCoverageYearsV1(
		ctx,
		request,
	)
	require.Error(t, err)
	require.Contains(t, err.Error(), "fetched data is nil")
	require.Nil(t, continuousCoverageYears)

	// case 2
	request = &data_processing.ContinuousCoverageYearsRequestV1{
		FetcherSpec: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData{
				InsuranceRecords: []*data_fetching.InsuranceRecordV1{},
			},
		},
		EffectiveDate: timestamppb.New(effectiveDate),
	}
	continuousCoverageYears, err = processorClient.GetContinuousCoverageYearsV1(
		ctx,
		request,
	)
	require.Error(t, err)
	require.Contains(t, err.Error(), "empty insurance records")
	require.Nil(t, continuousCoverageYears)

	// case 3
	records := []*data_fetching.InsuranceRecordV1{
		{
			EffectiveDate: timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
		},
		{
			EffectiveDate:       timestamppb.New(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
			CancelEffectiveDate: timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
		},
	}

	request = &data_processing.ContinuousCoverageYearsRequestV1{
		FetcherSpec: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData{
				InsuranceRecords: records,
			},
		},
		EffectiveDate: timestamppb.New(effectiveDate),
	}

	continuousCoverageYears, err = processorClient.GetContinuousCoverageYearsV1(
		ctx,
		request,
	)
	assert.NoError(t, err)
	assert.Equal(t, int64(5), int64(math.Floor(continuousCoverageYears.GetYears())))
}

func Test_GetContinuousCoverageYearsV1_WindowInOperation(t *testing.T) {
	var env struct {
		fx.In

		ProcessorClientFactory data_processing.ProcessorClientFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	processorClient, closer, err := env.ProcessorClientFactory(nil)
	assert.NoError(t, err)
	defer func() { _ = closer() }()

	ctx := context.Background()

	records := []*data_fetching.InsuranceRecordV1{
		{
			EffectiveDate: timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
		},
		{
			EffectiveDate:       timestamppb.New(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
			CancelEffectiveDate: timestamppb.New(time.Date(2021, 12, 31, 0, 0, 0, 0, time.UTC)),
		},
	}

	request := &data_processing.ContinuousCoverageYearsRequestV1{
		FetcherSpec: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData{
				InsuranceRecords: records,
			},
		},
		EffectiveDate: timestamppb.New(time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC)),
	}

	continuousCoverageYears, err := processorClient.GetContinuousCoverageYearsV1(
		ctx,
		request,
	)
	assert.NoError(t, err)
	assert.Equal(t, int64(2), int64(math.Floor(continuousCoverageYears.GetYears())))
}

func Test_GetContinuousCoverageYearsV1_NonActive(t *testing.T) {
	var env struct {
		fx.In

		ProcessorClientFactory data_processing.ProcessorClientFactory
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	processorClient, closer, err := env.ProcessorClientFactory(nil)
	assert.NoError(t, err)
	defer func() { _ = closer() }()

	ctx := context.Background()

	records := []*data_fetching.InsuranceRecordV1{
		{
			EffectiveDate:       timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
			CancelEffectiveDate: timestamppb.New(time.Date(2023, 12, 31, 0, 0, 0, 0, time.UTC)),
		},
		{
			EffectiveDate:       timestamppb.New(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
			CancelEffectiveDate: timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)),
		},
	}

	request := &data_processing.ContinuousCoverageYearsRequestV1{
		FetcherSpec: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData{
				InsuranceRecords: records,
			},
		},
		EffectiveDate: timestamppb.New(time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC)),
	}

	continuousCoverageYears, err := processorClient.GetContinuousCoverageYearsV1(
		ctx,
		request,
	)
	assert.NoError(t, err)
	assert.Equal(t, int64(0), int64(math.Floor(continuousCoverageYears.GetYears())))
}

func Test_GetContinuousCoverageYearsV1_WithStoreInterceptor(t *testing.T) {
	var env struct {
		fx.In

		ProcessorClientFactory          data_processing.ProcessorClientFactory
		ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
		WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
		StoreManager                    store_management.StoreManager
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()
	contextID := uuid.New()

	interceptors := interceptors_management.NewWritableStoreFirstInterceptors(
		env.ReadFromStoreInterceptorFactory,
		env.WriteToStoreInterceptorFactory,
		contextID,
	)
	client, closer, err := env.ProcessorClientFactory(nil, interceptors...)
	assert.NoError(t, err)
	defer func() { _ = closer() }()

	request := &data_processing.ContinuousCoverageYearsRequestV1{
		FetcherSpec: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData{
				InsuranceRecords: []*data_fetching.InsuranceRecordV1{
					{
						InsuranceCompanyName: "Test",
						DotNumber:            12345,
						EffectiveDate:        timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
					},
				},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	continuousCoverageYears := &data_processing.ContinuousCoverageYearsV1{Years: 4}

	resource := &common.Resource{Data: continuousCoverageYears}
	err = env.StoreManager.Save(ctx, contextID, request, resource)
	assert.NoError(t, err)

	exists, err := env.StoreManager.Exists(ctx, contextID, request)
	assert.NoError(t, err)
	assert.True(t, exists)

	report, err := client.GetContinuousCoverageYearsV1(ctx, request)
	assert.NoError(t, err)
	assert.Equal(t, report.Years, float64(4))
}
