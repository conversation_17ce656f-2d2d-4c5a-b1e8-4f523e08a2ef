package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestFleetModifiedMVRScoreRequestV1_GetFileNameComponents(t *testing.T) {
	testCases := []struct {
		name                       string
		storeKey                   *data_processing.FleetModifiedMVRScoreRequestV1
		expectedFileNameComponents []string
		expectedError              bool
		expectedErrorMessage       string
		shouldPanic                bool
	}{
		{
			name: "all fields present with multiple violations",
			storeKey: &data_processing.FleetModifiedMVRScoreRequestV1{
				FetcherSpec: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData_{
					FetchedData: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData{
						Report: &data_fetching.MVRReportV1{
							DlNumber: "dl123",
							DlState:  "TX",
							Violations: []*data_fetching.MVRViolationV1{
								{
									ViolationCode:   "SP001",
									ViolationDetail: "Speeding violation",
								},
								{
									ViolationCode:   "RLV001",
									ViolationDetail: "Red light violation",
								},
							},
						},
					},
				},
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 6, 15).ToTime()),
				UsState:       pointer_utils.ToPointer("TX"),
			},
			expectedFileNameComponents: []string{"TX", "dl123", "2", "15062024", "TX"},
			expectedError:              false,
		},
		{
			name: "optional fields nil with single violation",
			storeKey: &data_processing.FleetModifiedMVRScoreRequestV1{
				FetcherSpec: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData_{
					FetchedData: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData{
						Report: &data_fetching.MVRReportV1{
							DlNumber: "dl456",
							DlState:  "CA",
							Violations: []*data_fetching.MVRViolationV1{
								{
									ViolationCode:   "DUI001",
									ViolationDetail: "DUI violation",
								},
							},
						},
					},
				},
				EffectiveDate: nil,
				UsState:       nil,
			},
			expectedFileNameComponents: []string{"CA", "dl456", "1"},
			expectedError:              false,
		},
		{
			name: "no violations with all fields present",
			storeKey: &data_processing.FleetModifiedMVRScoreRequestV1{
				FetcherSpec: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData_{
					FetchedData: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData{
						Report: &data_fetching.MVRReportV1{
							DlNumber:   "dl789",
							DlState:    "FL",
							Violations: []*data_fetching.MVRViolationV1{}, // Empty violations slice
						},
					},
				},
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime()),
				UsState:       pointer_utils.ToPointer("FL"),
			},
			expectedFileNameComponents: []string{"FL", "dl789", "0", "01122024", "FL"},
			expectedError:              false,
		},
		{
			name: "nil violations slice with effective date only",
			storeKey: &data_processing.FleetModifiedMVRScoreRequestV1{
				FetcherSpec: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData_{
					FetchedData: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData{
						Report: &data_fetching.MVRReportV1{
							DlNumber:   "dl000",
							DlState:    "NY",
							Violations: nil, // Nil violations slice
						},
					},
				},
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 3, 10).ToTime()),
				UsState:       nil,
			},
			expectedFileNameComponents: []string{"NY", "dl000", "0", "10032024"},
			expectedError:              false,
		},
		{
			name: "fetched data is nil",
			storeKey: &data_processing.FleetModifiedMVRScoreRequestV1{
				FetcherSpec:   nil,
				EffectiveDate: nil,
				UsState:       nil,
			},
			expectedFileNameComponents: nil,
			expectedError:              true,
			expectedErrorMessage:       "nil fetched data can't be used to build store key",
		},
		{
			name: "report is nil within fetched data",
			storeKey: &data_processing.FleetModifiedMVRScoreRequestV1{
				FetcherSpec: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData_{
					FetchedData: &data_processing.FleetModifiedMVRScoreRequestV1_FetchedData{
						Report: nil,
					},
				},
				EffectiveDate: nil,
				UsState:       nil,
			},
			expectedFileNameComponents: nil,
			expectedError:              false,
			shouldPanic:                true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.shouldPanic {
				require.Panics(t, func() {
					_, _ = tc.storeKey.GetFileNameComponents()
				})
				return
			}

			fileNameComponents, err := tc.storeKey.GetFileNameComponents()

			if tc.expectedError {
				require.Error(t, err)
				require.Contains(t, err.Error(), tc.expectedErrorMessage)
				require.Nil(t, fileNameComponents)
			} else {
				require.NoError(t, err)
				require.Equal(t, tc.expectedFileNameComponents, fileNameComponents)
			}
		})
	}
}
