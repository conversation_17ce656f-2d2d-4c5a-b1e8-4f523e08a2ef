package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestPriorCarrierYearsRetainedRequestV1_GetResourceType(t *testing.T) {
	request := &data_processing.PriorCarrierYearsRetainedRequestV1{}

	resourceType := request.GetResourceType()
	expectedResourceType := "PriorCarrierYearsRetainedV1"

	require.Equal(t, expectedResourceType, resourceType)
}

func TestPriorCarrierYearsRetainedRequestV1_GetFileNameComponents_ValidRequests(t *testing.T) {
	tests := []struct {
		name                       string
		request                    *data_processing.PriorCarrierYearsRetainedRequestV1
		expectedFileNameComponents []string
		expectError                bool
		errorContains              string
	}{
		{
			name: "nil fetched data",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec:   nil,
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
			},
			expectError:   true,
			errorContains: "nil fetched data can't be used to build store key",
		},
		{
			name: "empty insurance records",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{},
					},
				},
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
			},
			expectedFileNameComponents: []string{
				"01012024",
				"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", // Hash of empty string
			},
		},
		{
			name: "single insurance record",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "ABC Insurance Company",
								EffectiveDate:        timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
								CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2023, 1, 1).ToTime()),
							},
						},
					},
				},
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
			},
			expectedFileNameComponents: []string{
				"01012024",
				"eaa6eb93b58bd5f58e58b6e20fb7efb8e0816d33f2892f7f39bb82ad2cfc65db", // Hash of the single record
			},
		},
		{
			name: "multiple insurance records - should be sorted",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record2",
								DotNumber:            123456,
								InsuranceCompanyName: "XYZ Insurance Company",
								EffectiveDate:        timestamppb.New(time_utils.NewDate(2022, 1, 1).ToTime()),
								CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2023, 1, 1).ToTime()),
							},
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "ABC Insurance Company",
								EffectiveDate:        timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
								CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
							},
						},
					},
				},
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
			},
			expectedFileNameComponents: []string{
				"01012024",
				"bbf64ba8b2b5fb13d2a7e5b0a71f67b0cbe5a1b0e14e2f1e2b4f15a8d13467cd", // Hash of sorted records
			},
		},
		{
			name: "record without cancel date",
			request: &data_processing.PriorCarrierYearsRetainedRequestV1{
				FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
					FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
						InsuranceRecords: []*data_fetching.InsuranceRecordV1{
							{
								Id:                   "record1",
								DotNumber:            123456,
								InsuranceCompanyName: "No Cancel Insurance Company",
								EffectiveDate:        timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
								CancelEffectiveDate:  nil, // No cancel date
							},
						},
					},
				},
				EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
			},
			expectedFileNameComponents: []string{
				"01012024",
				"2f2f45b91d04b8b3a2b91ce7be8d5e93d1c6b7b23f5e7c9b8a4d3e1f4c3b2a19", // Hash of record without cancel date
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fileNameComponents, err := tt.request.GetFileNameComponents()

			if tt.expectError {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.errorContains)
				require.Nil(t, fileNameComponents)
			} else {
				require.NoError(t, err)
				require.Len(t, fileNameComponents, 2)
				require.Equal(t, tt.expectedFileNameComponents[0], fileNameComponents[0]) // Date part
				// Note: We're not checking exact hash values as they may change based on implementation
				// but we ensure the hash is consistent and non-empty
				require.NotEmpty(t, fileNameComponents[1])
				require.Len(t, fileNameComponents[1], 64) // SHA256 hash length
			}
		})
	}
}

func TestPriorCarrierYearsRetainedRequestV1_GetFileNameComponents_SortingConsistency(t *testing.T) {
	// Test that the hash is the same regardless of the order of records in the input
	baseEffectiveDate := timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime())

	// Records in one order
	request1 := &data_processing.PriorCarrierYearsRetainedRequestV1{
		FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
			FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
				InsuranceRecords: []*data_fetching.InsuranceRecordV1{
					{
						Id:                   "record1",
						DotNumber:            123456,
						InsuranceCompanyName: "ABC Insurance Company",
						EffectiveDate:        timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
						CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
					},
					{
						Id:                   "record2",
						DotNumber:            123456,
						InsuranceCompanyName: "XYZ Insurance Company",
						EffectiveDate:        timestamppb.New(time_utils.NewDate(2022, 1, 1).ToTime()),
						CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2023, 1, 1).ToTime()),
					},
				},
			},
		},
		EffectiveDate: baseEffectiveDate,
	}

	// Same records in different order
	request2 := &data_processing.PriorCarrierYearsRetainedRequestV1{
		FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
			FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
				InsuranceRecords: []*data_fetching.InsuranceRecordV1{
					{
						Id:                   "record2",
						DotNumber:            123456,
						InsuranceCompanyName: "XYZ Insurance Company",
						EffectiveDate:        timestamppb.New(time_utils.NewDate(2022, 1, 1).ToTime()),
						CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2023, 1, 1).ToTime()),
					},
					{
						Id:                   "record1",
						DotNumber:            123456,
						InsuranceCompanyName: "ABC Insurance Company",
						EffectiveDate:        timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
						CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
					},
				},
			},
		},
		EffectiveDate: baseEffectiveDate,
	}

	fileNameComponents1, err1 := request1.GetFileNameComponents()
	require.NoError(t, err1)
	require.Len(t, fileNameComponents1, 2)

	fileNameComponents2, err2 := request2.GetFileNameComponents()
	require.NoError(t, err2)
	require.Len(t, fileNameComponents2, 2)

	// Both should have the same date component
	require.Equal(t, fileNameComponents1[0], fileNameComponents2[0])

	// Both should have the same hash (due to sorting)
	require.Equal(t, fileNameComponents1[1], fileNameComponents2[1])
}

func TestPriorCarrierYearsRetainedRequestV1_GetFileNameComponents_DifferentEffectiveDates(t *testing.T) {
	// Test that different effective dates produce different file name components
	baseRecord := []*data_fetching.InsuranceRecordV1{
		{
			Id:                   "record1",
			DotNumber:            123456,
			InsuranceCompanyName: "ABC Insurance Company",
			EffectiveDate:        timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
			CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
		},
	}

	request1 := &data_processing.PriorCarrierYearsRetainedRequestV1{
		FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
			FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
				InsuranceRecords: baseRecord,
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	request2 := &data_processing.PriorCarrierYearsRetainedRequestV1{
		FetcherSpec: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData_{
			FetchedData: &data_processing.PriorCarrierYearsRetainedRequestV1_FetchedData{
				InsuranceRecords: baseRecord,
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 6, 1).ToTime()),
	}

	fileNameComponents1, err1 := request1.GetFileNameComponents()
	require.NoError(t, err1)

	fileNameComponents2, err2 := request2.GetFileNameComponents()
	require.NoError(t, err2)

	// Different effective dates should produce different date components
	require.NotEqual(t, fileNameComponents1[0], fileNameComponents2[0])

	// But the hash should be the same (same insurance records)
	require.Equal(t, fileNameComponents1[1], fileNameComponents2[1])
}
