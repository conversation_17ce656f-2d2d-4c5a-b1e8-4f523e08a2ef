package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestNFViolationsMetadataRequestV1_GetFileNameComponents(t *testing.T) {
	// case 1: without fetched data: it should return an error
	request := &data_processing.NFViolationsMetadataRequestV1{
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
		FetcherSpec:   nil,
	}

	fileNameComponents, err := request.GetFileNameComponents()
	require.Error(t, err)
	require.Nil(t, fileNameComponents)

	// case 2: with fetched data: it should return the correct file name components
	violationRecords := []*data_fetching.FMCSAViolationRecordV1{
		{
			RowId: "ViolationRecord1",
		},
		{
			RowId: "ViolationRecord2",
		},
	}

	request = &data_processing.NFViolationsMetadataRequestV1{
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
		FetcherSpec: &data_processing.NFViolationsMetadataRequestV1_FetchedData_{
			FetchedData: &data_processing.NFViolationsMetadataRequestV1_FetchedData{
				ViolationRecords: &data_fetching.FMCSAViolationRecordsV1{
					Records: violationRecords,
				},
			},
		},
	}

	expectedFileNameComponents := []string{
		"01012024",
		"61d2ae3b08e0fa75bdb78cc587f006d7d64ae8165403919fc2e8aa63a1d44f21",
	}

	fileNameComponents, err = request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
