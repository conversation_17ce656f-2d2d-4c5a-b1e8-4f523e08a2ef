package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

// We don't test very thoroughly this method, because it really only delegates to another function.
// And we trust that that other function is well tested.
func TestIsoFieldsFromNhtsaFieldsRequestV1(t *testing.T) {
	// case 1
	storeKey := data_processing.IsoFieldsFromNhtsaFieldsRequestV1{
		FetcherSpec: nil,
	}
	fileNameComponents, err := storeKey.GetFileNameComponents()
	require.Error(t, err)
	require.Contains(t, err.Error(), "nil fetched data can't be used to build store key")
	require.Nil(t, fileNameComponents)

	// case 2
	storeKey = data_processing.IsoFieldsFromNhtsaFieldsRequestV1{
		FetcherSpec: &data_processing.IsoFieldsFromNhtsaFieldsRequestV1_FetchedData_{
			FetchedData: &data_processing.IsoFieldsFromNhtsaFieldsRequestV1_FetchedData{
				VehicleType:        data_fetching.VehicleTypeV1_VehicleTypeV1_Unspecified,
				VehicleBodyClass:   "BodyClass2",
				VehicleWeightClass: data_fetching.WeightClassV1_WeightClassV1_Unspecified,
			},
		},
	}

	fileNameComponents, err = storeKey.GetFileNameComponents()
	require.NoError(t, err)
	expectedFileNameComponents := []string{"EMPTY::BODYCLASS2::EMPTY"}
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
