package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestContinuousCoverageYearsRequestV1_GetFileNameComponents(t *testing.T) {
	// case 1
	request := &data_processing.ContinuousCoverageYearsRequestV1{
		FetcherSpec:   nil,
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}
	fileNameComponents, err := request.GetFileNameComponents()
	require.Error(t, err)
	require.Nil(t, fileNameComponents)

	// case 2
	request = &data_processing.ContinuousCoverageYearsRequestV1{
		FetcherSpec: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.ContinuousCoverageYearsRequestV1_FetchedData{
				InsuranceRecords: []*data_fetching.InsuranceRecordV1{
					{
						InsuranceCompanyName: "Test",
						DotNumber:            12345,
						EffectiveDate:        timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
					},
					{
						InsuranceCompanyName: "Test",
						DotNumber:            12345,
						EffectiveDate:        timestamppb.New(time_utils.NewDate(2019, 1, 1).ToTime()),
						CancelEffectiveDate:  timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
					},
				},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	expectedFileNameComponents := []string{"01012024", "141e4f3c8f45a0f968364a7d3ddc4891895d0ce1d080aa065163706ff6058841"}
	fileNameComponents, err = request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
