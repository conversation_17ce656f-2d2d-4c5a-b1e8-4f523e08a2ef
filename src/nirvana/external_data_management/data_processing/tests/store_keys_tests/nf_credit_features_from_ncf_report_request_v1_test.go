package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestNFCreditFeaturesRequestV1(t *testing.T) {
	// case 1
	storeKey := data_processing.NFCreditFeaturesRequestV1{
		OwnerDOB:           timestamppb.New(time_utils.NewDate(1998, 9, 3).ToTime()),
		NationalCreditFile: nil,
		OwnerName:          "John <PERSON>e",
	}

	fileNameComponents, err := storeKey.GetFileNameComponents()
	require.Error(t, err)
	require.Contains(t, err.Error(), "national credit file is nil")
	require.Nil(t, fileNameComponents)

	// case 2
	storeKey = data_processing.NFCreditFeaturesRequestV1{
		OwnerDOB: timestamppb.New(time_utils.NewDate(1998, 9, 3).ToTime()),
		NationalCreditFile: &data_fetching.NationalCreditFileV1{
			DateOfCreditReportRun: nil,
		},
		OwnerName: "John Doe",
	}

	fileNameComponents, err = storeKey.GetFileNameComponents()
	require.Error(t, err)
	require.Contains(t, err.Error(), "date of credit report run is nil")
	require.Nil(t, fileNameComponents)

	// case 3
	storeKey = data_processing.NFCreditFeaturesRequestV1{
		OwnerDOB: timestamppb.New(time_utils.NewDate(1998, 9, 3).ToTime()),
		NationalCreditFile: &data_fetching.NationalCreditFileV1{
			DateOfCreditReportRun: timestamppb.New(time_utils.NewDate(2024, 7, 1).ToTime()),
		},
		OwnerName: "John Doe",
	}

	expectedFileNameComponents := []string{"John Doe::2024-07-01 00:00:00 +0000 UTC::1998-09-03 00:00:00 +0000 UTC"}
	fileNameComponents, err = storeKey.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
