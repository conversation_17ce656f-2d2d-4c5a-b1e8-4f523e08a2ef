package store_keys_tests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestFleetMovingViolationCountRequestV1_WithAllFieldsPresent(t *testing.T) {
	dlNumber := "dl1"
	dlState := "OH"
	report := data_fetching.MVRReportV1{DlNumber: dlNumber, DlState: dlState}

	datetime, err := time.Parse("2006-01-02T15:04:05-07:00", "2024-04-23T09:31:45-07:00")
	require.NoError(t, err)
	effectiveDate := timestamppb.New(datetime)

	usState := "CA"

	storeKey := data_processing.FleetMovingViolationCountRequestV1{
		FetcherSpec: &data_processing.FleetMovingViolationCountRequestV1_FetchedData_{
			FetchedData: &data_processing.FleetMovingViolationCountRequestV1_FetchedData{
				Report: &report,
			},
		},
		IsExcludedMVCEnabled: true,
		EffectiveDate:        effectiveDate,
		UsState:              &usState,
	}

	fileNameComponents, err := storeKey.GetFileNameComponents()
	require.NoError(t, err)
	expectedFileNameComponents := []string{"OH", "dl1", "true", "23042024", "CA"}
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}

func TestFleetMovingViolationCountRequestV1_WithOptionalFieldsNil(t *testing.T) {
	dlNumber := "dl2"
	dlState := "MI"
	report := data_fetching.MVRReportV1{DlNumber: dlNumber, DlState: dlState}

	storeKey := data_processing.FleetMovingViolationCountRequestV1{
		FetcherSpec: &data_processing.FleetMovingViolationCountRequestV1_FetchedData_{
			FetchedData: &data_processing.FleetMovingViolationCountRequestV1_FetchedData{
				Report: &report,
			},
		},
		IsExcludedMVCEnabled: false,
		EffectiveDate:        nil,
		UsState:              nil,
	}

	fileNameComponents, err := storeKey.GetFileNameComponents()
	require.NoError(t, err)
	expectedFileNameComponents := []string{"MI", "dl2", "false"}
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}

func TestFleetMovingViolationCountRequestV1_WithFetchedDataNil(t *testing.T) {
	storeKey := data_processing.FleetMovingViolationCountRequestV1{
		FetcherSpec:          nil,
		IsExcludedMVCEnabled: false,
		EffectiveDate:        nil,
		UsState:              nil,
	}

	_, err := storeKey.GetFileNameComponents()
	require.Error(t, err)
	require.Contains(t, err.Error(), "nil fetched data can't be used to build store key")
}
