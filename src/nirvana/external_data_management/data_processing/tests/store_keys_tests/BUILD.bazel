load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "store_keys_tests_test",
    srcs = [
        "continuos_coverage_years_request_v1_test.go",
        "fleet_modified_mvr_score_request_v1_test.go",
        "fleet_moving_violation_count_request_v1_test.go",
        "iso_fields_from_nhtsa_fields_request_v1_test.go",
        "latest_valid_rating_tier_record_request_v1_test.go",
        "nf_credit_features_from_ncf_report_request_v1_test.go",
        "nf_inspection_count_request_v1_test.go",
        "nf_violations_metadata_request_v1_test.go",
        "prior_carrier_years_retained_request_v1_test.go",
        "retained_years_request_v1_test.go",
        "years_in_business_from_authority_history_request_v1_test.go",
        "years_in_business_from_insurance_history_request_v1_test.go",
    ],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
