package store_keys_tests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestLatestValidRatingTierRecordRequestV1_GetFileNameComponents(t *testing.T) {
	now := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	ratingTierRecords := []*data_fetching.RatingTierRecordV1{
		{
			Date:                       timestamppb.New(now),
			InspectionIndicator:        "BOTH",
			LargeMachineryIndicator:    true,
			CrashFrequency:             pointer_utils.ToPointer(0.1),
			PowerUnits:                 pointer_utils.ToPointer(int64(25)),
			AverageMiles:               pointer_utils.ToPointer(15_134.2),
			AverageCombinedGrossWeight: pointer_utils.ToPointer(4_329.2),
			MaintenanceViolationsRatio: pointer_utils.ToPointer(0.01),
			UnsafeViolationRatio:       pointer_utils.ToPointer(0.1),
			VehicleInspectionRatio:     pointer_utils.ToPointer(0.9),
		},
		{
			Date:                       timestamppb.New(now),
			InspectionIndicator:        "BOTH",
			LargeMachineryIndicator:    true,
			CrashFrequency:             pointer_utils.ToPointer(0.1),
			PowerUnits:                 pointer_utils.ToPointer(int64(50)),
			AverageMiles:               pointer_utils.ToPointer(17_134.2),
			AverageCombinedGrossWeight: pointer_utils.ToPointer(40_329.2),
			MaintenanceViolationsRatio: pointer_utils.ToPointer(0.21),
			UnsafeViolationRatio:       pointer_utils.ToPointer(0.2),
			VehicleInspectionRatio:     pointer_utils.ToPointer(0.8),
		},
	}

	request := &data_processing.LatestValidRatingTierRecordRequestV1{
		Records: ratingTierRecords,
	}
	expectedFileNameComponents := []string{
		"e02d331f74879dc4243a232aa3e767b797e760fb3949706111d8cac6efdc03e3",
	}

	fileNameComponents, err := request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
