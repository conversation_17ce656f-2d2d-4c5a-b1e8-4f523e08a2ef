package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestNFInspectionCountsRequestV1_GetFileNameComponents(t *testing.T) {
	// case 1: without fetched data: it should return an error
	request := &data_processing.NFInspectionCountRequestV1{
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	_, err := request.GetFileNameComponents()
	require.Error(t, err)

	// case 2: with fetched data: it should return the correct file name components
	inspectionRecords := []*data_fetching.FMCSAInspectionRecordV1{
		{
			RowID: "InspectionRecord1",
		},
		{
			RowID: "InspectionRecord2",
		},
	}

	request = &data_processing.NFInspectionCountRequestV1{
		FetcherSpec: &data_processing.NFInspectionCountRequestV1_FetchedData_{
			FetchedData: &data_processing.NFInspectionCountRequestV1_FetchedData{
				InspectionRecords: &data_fetching.FMCSAInspectionRecordsV1{
					Records: inspectionRecords,
				},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}
	expectedFileNameComponents := []string{
		"01012024",
		"f04c42c27e08a0f32e8a8b42105ba72ae4010dfc627571e10528f2a95b3b7937",
	}

	fileNameComponents, err := request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
