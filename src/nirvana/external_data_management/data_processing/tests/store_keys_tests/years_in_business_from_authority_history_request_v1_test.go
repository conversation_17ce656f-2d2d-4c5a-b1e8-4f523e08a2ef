package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestYearsInBusinessFromAuthorityHistoryRequestV1_GetFileNameComponents(t *testing.T) {
	// case 1: without fetched data: it should return an error
	request := &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1{
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	_, err := request.GetFileNameComponents()
	require.Error(t, err)

	// case 2: with fetched data: it should return the correct file name components
	request = &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1{
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 2, 1).ToTime()),
		FetcherSpec: &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData_{
			FetchedData: &data_processing.YearsInBusinessFromAuthorityHistoryRequestV1_FetchedData{
				AuthorityHistoryRecords: []*data_fetching.AuthorityHistoryRecordV1{
					{
						DotNumber: 12345,
					},
				},
			},
		},
	}

	expectedFileNameComponents := []string{"01022024", "e46372d7ba3b58a29e2b4edc65e18405221a327463ec02d09dbf35f62d5830b1"}
	fileNameComponents, err := request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
