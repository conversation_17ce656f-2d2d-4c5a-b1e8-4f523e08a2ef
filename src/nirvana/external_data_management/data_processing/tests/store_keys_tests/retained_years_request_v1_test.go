package store_keys_tests

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestRetainedYearsRequestV1_GetFileNameComponents_WithSortedPolicies(t *testing.T) {
	// case 1: nil fetcher spec
	request := &data_processing.RetainedYearsRequestV1{
		FetcherSpec:   nil,
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}
	fileNameComponents, err := request.GetFileNameComponents()
	require.Error(t, err)
	require.Nil(t, fileNameComponents)

	// case 2: valid request with policies
	policy1ID := uuid.MustParse("ca5f91c0-93d8-4bcc-a1de-f65967cc4518")
	policy2ID := uuid.MustParse("53c30e4b-8568-41aa-b3eb-26d082ade71b")
	request = &data_processing.RetainedYearsRequestV1{
		FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
				Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
					Policies: []*data_fetching.NirvanaPolicyV1{
						{
							Id:              policy1ID.String(),
							PolicyNumber:    "POL-001",
							EffectiveDate:   timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
							EffectiveDateTo: timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
						},
						{
							Id:              policy2ID.String(),
							PolicyNumber:    "POL-002",
							EffectiveDate:   timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
							EffectiveDateTo: timestamppb.New(time_utils.NewDate(2022, 1, 1).ToTime()),
						},
					},
				},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	fileNameComponents, err = request.GetFileNameComponents()
	require.NoError(t, err)

	expectedFileNameComponents := []string{"01012024", "5849329b959a7a0f4e09d0af0caab0520e4631d43a41cf7b54caf7c3d9c433d6"}
	require.Equal(t, expectedFileNameComponents, fileNameComponents)

	// case 3: request with empty policies
	request = &data_processing.RetainedYearsRequestV1{
		FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
				Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
					Policies: []*data_fetching.NirvanaPolicyV1{},
				},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	expectedFileNameComponents = []string{"01012024", "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}
	fileNameComponents, err = request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}

func TestRetainedYearsRequestV1_GetFileNameComponents_WithUnsortedPolicies(t *testing.T) {
	// case 1: request with unsorted policies
	policy3ID := uuid.MustParse("ca5f91c0-93d8-4bcc-a1de-f65967cc4518")
	policy4ID := uuid.MustParse("53c30e4b-8568-41aa-b3eb-26d082ade71b")
	request := &data_processing.RetainedYearsRequestV1{
		FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
				Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
					Policies: []*data_fetching.NirvanaPolicyV1{
						{
							Id:              policy4ID.String(), // Note: policy4 comes before policy3
							PolicyNumber:    "POL-004",
							EffectiveDate:   timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
							EffectiveDateTo: timestamppb.New(time_utils.NewDate(2022, 1, 1).ToTime()),
						},
						{
							Id:              policy3ID.String(),
							PolicyNumber:    "POL-003",
							EffectiveDate:   timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
							EffectiveDateTo: timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
						},
					},
				},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	fileNameComponents, err := request.GetFileNameComponents()
	require.NoError(t, err)
	// The hash should be the same regardless of the order of policies
	expectedFileNameComponents := []string{"01012024", "5849329b959a7a0f4e09d0af0caab0520e4631d43a41cf7b54caf7c3d9c433d6"}
	require.Equal(t, expectedFileNameComponents, fileNameComponents)

	// case 2: create the same policies in sorted order and verify the hash is the same
	sortedRequest := &data_processing.RetainedYearsRequestV1{
		FetcherSpec: &data_processing.RetainedYearsRequestV1_FetchedData_{
			FetchedData: &data_processing.RetainedYearsRequestV1_FetchedData{
				Policies: &data_fetching.GetNirvanaPoliciesResponseV1{
					Policies: []*data_fetching.NirvanaPolicyV1{
						{
							Id:              policy3ID.String(),
							PolicyNumber:    "POL-003",
							EffectiveDate:   timestamppb.New(time_utils.NewDate(2020, 1, 1).ToTime()),
							EffectiveDateTo: timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
						},
						{
							Id:              policy4ID.String(),
							PolicyNumber:    "POL-004",
							EffectiveDate:   timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
							EffectiveDateTo: timestamppb.New(time_utils.NewDate(2022, 1, 1).ToTime()),
						},
					},
				},
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	sortedFileNameComponents, err := sortedRequest.GetFileNameComponents()
	require.NoError(t, err)
	// Verify that the hash is the same regardless of policy order
	require.Equal(t, fileNameComponents[1], sortedFileNameComponents[1])
}
