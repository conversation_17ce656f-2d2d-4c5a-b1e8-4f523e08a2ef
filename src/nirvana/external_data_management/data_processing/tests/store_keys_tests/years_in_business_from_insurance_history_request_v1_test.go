package store_keys_tests

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
)

func TestYearsInBusinessFromInsuranceHistoryRequestV1_GetFileNameComponents(t *testing.T) {
	// case 1
	request := &data_processing.InsuranceHistoryYearsInBusinessRequestV1{
		FetcherSpec: &data_processing.InsuranceHistoryYearsInBusinessRequestV1_FetchedData_{
			FetchedData: nil,
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}
	fileNameComponents, err := request.GetFileNameComponents()
	require.Error(t, err)
	require.Contains(t, err.Error(), "nil fetched data can't be used to build store key")
	require.Nil(t, fileNameComponents)

	// case 2
	insurnaceRecords := []*data_fetching.InsuranceRecordV1{
		{
			InsuranceCompanyName: "Test",
			DotNumber:            12345,
			EffectiveDate:        timestamppb.New(time_utils.NewDate(2022, 1, 1).ToTime()),
		},
		{
			InsuranceCompanyName: "Test",
			DotNumber:            12345,
			EffectiveDate:        timestamppb.New(time_utils.NewDate(2021, 1, 1).ToTime()),
		},
		{
			InsuranceCompanyName: "Test",
			DotNumber:            12345,
			EffectiveDate:        timestamppb.New(time_utils.NewDate(2023, 1, 1).ToTime()),
		},
	}
	request = &data_processing.InsuranceHistoryYearsInBusinessRequestV1{
		FetcherSpec: &data_processing.InsuranceHistoryYearsInBusinessRequestV1_FetchedData_{
			FetchedData: &data_processing.InsuranceHistoryYearsInBusinessRequestV1_FetchedData{
				InsuranceRecords: insurnaceRecords,
			},
		},
		EffectiveDate: timestamppb.New(time_utils.NewDate(2024, 1, 1).ToTime()),
	}

	expectedFileNameComponents := []string{"01012024", "1a25a8a9c47cbd230478ed56ba70799df1fc0b8510f08180358d18d1a90dedd1"}
	fileNameComponents, err = request.GetFileNameComponents()
	require.NoError(t, err)
	require.Equal(t, expectedFileNameComponents, fileNameComponents)
}
