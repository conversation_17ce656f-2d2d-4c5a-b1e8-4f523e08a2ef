package mvr

import "nirvanatech.com/nirvana/external_data_management/store_management"

/*
	These methods and constants are used to build the path on S3 where data will be stored.
	DO NOT MODIFY EXISTING CODE, ONLY ADD NEW.

	If you are adding a method to the processor, make sure that your request implements store_management.StoreKey
	interface so your method can be wrapped by the store interceptor.
*/

const fleetViolationsDataV1ResourceType = "FleetViolationsDataV1"

type FleetViolationsDataV1StoreKey struct{}

func (k *FleetViolationsDataV1StoreKey) GetResourceType() string {
	return fleetViolationsDataV1ResourceType
}

func (k *FleetViolationsDataV1StoreKey) GetFileNameComponents() ([]string, error) {
	return []string{"data"}, nil
}

var _ store_management.StoreKey = &FleetViolationsDataV1StoreKey{}
