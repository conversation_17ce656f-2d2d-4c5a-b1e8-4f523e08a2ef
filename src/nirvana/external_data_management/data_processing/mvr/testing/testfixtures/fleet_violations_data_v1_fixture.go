package testfixtures

import (
	"context"
	_ "embed"

	"go.uber.org/fx"

	"nirvanatech.com/nirvana/external_data_management/data_processing/mvr"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
)

//go:embed fleet_violations_data_v1.json.gz
var fleetViolationDataV1 []byte

// The FleetViolationDataV1Fixture is required to populate the store used by the processor with
// certain static data. You have to require it as a dependency provided by fx in your tests.
type FleetViolationDataV1Fixture struct {
	storeManager store_management.StoreManager
}

func (f *FleetViolationDataV1Fixture) apply(_ context.Context) error {
	return static_data_utils.PopulateStaticDataForTest[mvr.FleetViolationsDataV1, mvr.FleetViolationsDataV1StoreKey](
		fleetViolationDataV1,
		f.storeManager,
	)
}

func newFleetViolationDataV1Fixture(
	lc fx.Lifecycle,
	storeManager store_management.StoreManager,
) (*FleetViolationDataV1Fixture, error) {
	fixture := &FleetViolationDataV1Fixture{storeManager}

	lc.Append(fx.Hook{OnStart: fixture.apply})

	return fixture, nil
}

var _ = fxregistry.RegisterForTest(fx.Provide(newFleetViolationDataV1Fixture))
