load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "testfixtures",
    srcs = ["fleet_violations_data_v1_fixture.go"],
    embedsrcs = ["fleet_violations_data_v1.json.gz"],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/mvr/testing/testfixtures",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/data_processing/mvr",
        "//nirvana/external_data_management/data_processing/static_data_utils",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/fx/fxregistry",
        "@org_uber_go_fx//:fx",
    ],
)
