package mvr

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx/fxtest"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/mvr"
)

type fetcherClientTestSuite struct {
	suite.Suite
	ctx           context.Context
	fxapp         *fxtest.App
	mockMVRClient *mvr.MockMVRClient
	report        *data_fetching.MVRReportV1
	violationData map[int64]*FleetViolationV1
	effectiveDate time.Time
}

func TestFetcherClient(t *testing.T) {
	suite.Run(t, new(fetcherClientTestSuite))
}

func (s *fetcherClientTestSuite) SetupTest() {
	s.ctx = context.Background()

	effectiveDate, err := time.Parse(
		"2006-01-02",
		"2024-02-01",
	)
	s.Require().NoError(err)
	s.effectiveDate = effectiveDate

	s.report = &data_fetching.MVRReportV1{
		Violations: []*data_fetching.MVRViolationV1{
			{
				// This violation should not be considered, because even though it's a moving violation,
				// its date is such that effectiveDate - durationToConsider > violationDate.
				AssignedViolationCode: "1",
				ViolationDate:         createNewTimestamp(s, "2024-01-25"),
			},
			{
				// This violation should be considered.
				AssignedViolationCode: "1",
				ViolationDate:         createNewTimestamp(s, "2024-01-27"),
			},
			{
				// This violation should not be considered, because it's not a moving violation.
				AssignedViolationCode: "2",
				ViolationDate:         createNewTimestamp(s, "2024-01-01"),
			},
			{
				// This violation should be considered only in tests where we don't specify excluded state.
				AssignedViolationCode: "3",
				ViolationDate:         createNewTimestamp(s, "2024-01-01"),
			},
		},
	}

	s.violationData = map[int64]*FleetViolationV1{
		1: {
			IsMovingViolation:  true,
			DurationToConsider: int64(5 * time_utils.Day),
			ExcludedStates: map[string]bool{
				"IL": true,
			},
		},
		2: {
			IsMovingViolation:  false,
			DurationToConsider: int64(5 * time_utils.Year),
			ExcludedStates: map[string]bool{
				"IL": true,
			},
		},
		3: {
			IsMovingViolation:  true,
			DurationToConsider: int64(5 * time_utils.Year),
			ExcludedStates: map[string]bool{
				"OH": true,
			},
		},
	}
}

func (s *fetcherClientTestSuite) TestGetMovingViolationCountV1_WithoutViolations() {
	s.report = &data_fetching.MVRReportV1{}
	violationCount, err := GetFleetMovingViolationCountV1(
		s.effectiveDate,
		s.report,
		nil,
		false,
		s.violationData,
	)
	s.Require().NoError(err)
	s.Require().Zero(violationCount)
}

func (s *fetcherClientTestSuite) TestGetMovingViolationCountV1_WithNonIntViolationCode() {
	s.report.Violations = append(s.report.Violations, &data_fetching.MVRViolationV1{
		AssignedViolationCode: "A",
	})
	violationCount, err := GetFleetMovingViolationCountV1(
		s.effectiveDate,
		s.report,
		nil,
		false,
		s.violationData,
	)
	s.Require().Error(err)
	s.Require().Equal(-1, violationCount)
}

func (s *fetcherClientTestSuite) TestGetMovingViolationCountV1_WithUnknownViolationCode() {
	s.report.Violations = append(s.report.Violations, &data_fetching.MVRViolationV1{
		AssignedViolationCode: "100",
	})
	violationCount, err := GetFleetMovingViolationCountV1(
		s.effectiveDate,
		s.report,
		nil,
		false,
		s.violationData,
	)
	s.Require().Error(err)
	s.Require().Regexp("Unknown violation code", err.Error())
	s.Require().Equal(-1, violationCount)
}

func (s *fetcherClientTestSuite) TestGetMovingViolationCountV1_WithSomeViolations() {
	violationCount, err := GetFleetMovingViolationCountV1(
		s.effectiveDate,
		s.report,
		nil,
		false,
		s.violationData,
	)
	s.Require().NoError(err)
	s.Require().Equal(2, violationCount)
}

func (s *fetcherClientTestSuite) TestGetMovingViolationCountV1_WithExcludedState() {
	violationCount, err := GetFleetMovingViolationCountV1(
		s.effectiveDate,
		s.report,
		pointer_utils.ToPointer("OH"),
		true,
		s.violationData,
	)
	s.Require().NoError(err)
	s.Require().Equal(1, violationCount)
}

func createNewTimestamp(s *fetcherClientTestSuite, dateStr string) *timestamppb.Timestamp {
	datetime, err := time.Parse("2006-01-02", dateStr)
	s.Require().NoError(err)
	return timestamppb.New(datetime)
}
