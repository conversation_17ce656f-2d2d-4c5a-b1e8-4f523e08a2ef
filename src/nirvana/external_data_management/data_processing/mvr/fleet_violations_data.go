package mvr

import (
	"context"

	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/data_processing/static_data_utils"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

func GetFleetViolationsDataV1(
	ctx context.Context,
	storeManager store_management.StoreManager,
) (*FleetViolationsDataV1, error) {
	storeKey := &FleetViolationsDataV1StoreKey{}
	violationsData := &FleetViolationsDataV1{}
	resource := &common.Resource{Data: violationsData}

	err := static_data_utils.LoadStaticData(ctx, storeManager, resource, storeKey)
	if err != nil {
		return nil, err
	}

	return violationsData, nil
}
