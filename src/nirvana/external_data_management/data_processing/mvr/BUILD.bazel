load("@golink//proto:proto.bzl", "go_proto_link")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

# keep
go_proto_library(
    name = "data_processing_mvr_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/mvr",
    proto = "//proto/data_processing/mvr:data_processing_mvr_proto",
    visibility = ["//visibility:public"],
)

# keep
go_proto_link(
    name = "data_processing_mvr_go_proto_link",
    dep = ":data_processing_mvr_go_proto",
    version = "v1",
)

go_library(
    name = "mvr",
    srcs = [
        "fleet_violations_data.go",
        "modified_mvr_score.go",
        "moving_violation_count.go",
        "store_keys.go",
    ],
    embed = [":data_processing_mvr_go_proto"],  # keep
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/mvr",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing/static_data_utils",
        "//nirvana/external_data_management/store_management",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "mvr_test",
    srcs = [
        "modified_mvr_score_test.go",
        "moving_violation_count_test.go",
        "store_keys_test.go",
    ],
    embed = [":mvr"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/mvr",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//fxtest",
    ],
)
