package mvr

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

// getTestViolationsData returns test violation data for testing
func getTestViolationsData() map[int64]*FleetViolationV1 {
	return map[int64]*FleetViolationV1{
		111105: { // DRIVE WHILE LICENSE SUSPENDED
			Description:        "DRIVE WHILE LICENSE IS SUSP/REVO/DENI/DISQ/CANC RESULTING IN ACCI",
			IsMovingViolation:  true,
			DurationToConsider: int64(5 * time_utils.Year),
			ExcludedStates:     map[string]bool{},
			ModifiedMVRScore:   9,
		},
		111110: { // RECKLESS DRIVING
			Description:        "RECK<PERSON><PERSON> DRIVING, WILLFUL AND WANTON DISREGARD",
			IsMovingViolation:  true,
			DurationToConsider: int64(5 * time_utils.Year),
			ExcludedStates: map[string]bool{
				"TX": true, // Excluded in Texas for testing state exclusion logic
			},
			ModifiedMVRScore: 9,
		},
		111210: { // NEGLIGENT DRIVING
			Description:        "NEGLIGENT DRIVING",
			IsMovingViolation:  true,
			DurationToConsider: int64(5 * time_utils.Year),
			ExcludedStates: map[string]bool{
				"TX": true, // Excluded in Texas for testing state exclusion logic
			},
			ModifiedMVRScore: 8,
		},
		111300: { // DUI - GENERAL
			Description:        "DUI - GENERAL",
			IsMovingViolation:  true,
			DurationToConsider: int64(5 * time_utils.Year),
			ExcludedStates:     map[string]bool{}, // No exclusions for testing
			ModifiedMVRScore:   10,                // Highest score for testing
		},
	}
}

func TestGetFleetModifiedMVRScoreV1(t *testing.T) {
	effectiveDate := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)

	// Use test violations data
	violationsData := getTestViolationsData()

	tests := []struct {
		name          string
		report        *data_fetching.MVRReportV1
		state         *string
		expectedScore int64
		expectedError bool
	}{
		{
			name: "No violations",
			report: &data_fetching.MVRReportV1{
				Violations: []*data_fetching.MVRViolationV1{},
			},
			state:         nil,
			expectedScore: 0,
			expectedError: false,
		},
		{
			name: "Single DUI violation",
			report: &data_fetching.MVRReportV1{
				Violations: []*data_fetching.MVRViolationV1{
					{
						AssignedViolationCode: "111300", // DUI - GENERAL (score: 10)
						ViolationDate:         timestamppb.New(time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			state:         nil,
			expectedScore: 10,
			expectedError: false,
		},
		{
			name: "Multiple violations with different scores",
			report: &data_fetching.MVRReportV1{
				Violations: []*data_fetching.MVRViolationV1{
					{
						AssignedViolationCode: "111300", // DUI - GENERAL (score: 10)
						ViolationDate:         timestamppb.New(time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC)),
					},
					{
						AssignedViolationCode: "111210", // NEGLIGENT DRIVING (score: 8)
						ViolationDate:         timestamppb.New(time.Date(2022, 8, 1, 0, 0, 0, 0, time.UTC)),
					},
					{
						AssignedViolationCode: "111105", // DRIVE WHILE LICENSE SUSPENDED (score: 9)
						ViolationDate:         timestamppb.New(time.Date(2022, 10, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			state:         nil,
			expectedScore: 27, // 10 + 8 + 9
			expectedError: false,
		},
		{
			name: "Violation too old (outside duration)",
			report: &data_fetching.MVRReportV1{
				Violations: []*data_fetching.MVRViolationV1{
					{
						AssignedViolationCode: "111300",                                                     // DUI - GENERAL (5 year duration)
						ViolationDate:         timestamppb.New(time.Date(2017, 6, 1, 0, 0, 0, 0, time.UTC)), // 6 years ago
					},
				},
			},
			state:         nil,
			expectedScore: 0, // Too old, should be excluded
			expectedError: false,
		},
		{
			name: "State exclusion - TX state excludes certain violations",
			report: &data_fetching.MVRReportV1{
				Violations: []*data_fetching.MVRViolationV1{
					{
						AssignedViolationCode: "111110", // RECKLESS DRIVING (excluded in TX)
						ViolationDate:         timestamppb.New(time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC)),
					},
					{
						AssignedViolationCode: "111300", // DUI - GENERAL (not excluded in TX)
						ViolationDate:         timestamppb.New(time.Date(2022, 8, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			state:         pointer_utils.String("TX"),
			expectedScore: 10, // Only DUI counts, reckless driving excluded in TX
			expectedError: false,
		},
		{
			name: "Unknown violation code",
			report: &data_fetching.MVRReportV1{
				Violations: []*data_fetching.MVRViolationV1{
					{
						AssignedViolationCode: "999999", // Unknown code
						ViolationDate:         timestamppb.New(time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC)),
					},
					{
						AssignedViolationCode: "111300", // Known code
						ViolationDate:         timestamppb.New(time.Date(2022, 8, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			state:         nil,
			expectedScore: 10, // Only known violation counts
			expectedError: false,
		},
		{
			name: "Invalid violation code format",
			report: &data_fetching.MVRReportV1{
				Violations: []*data_fetching.MVRViolationV1{
					{
						AssignedViolationCode: "invalid", // Invalid format
						ViolationDate:         timestamppb.New(time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
			},
			state:         nil,
			expectedScore: 0,
			expectedError: true, // Should return error for invalid format
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			score, err := GetFleetModifiedMVRScoreV1(effectiveDate, tt.report, tt.state, violationsData)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedScore, score)
			}
		})
	}
}

func TestFilterFleetModifiedMVRScoreV1(t *testing.T) {
	effectiveDate := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)

	// Use test violations data
	violationsData := getTestViolationsData()

	tests := []struct {
		name            string
		violationCode   int64
		state           *string
		violationDate   time.Time
		expectedScore   int64
		expectedInclude bool
	}{
		{
			name:            "Valid DUI violation",
			violationCode:   111300, // DUI - GENERAL
			state:           nil,
			violationDate:   time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC),
			expectedScore:   10,
			expectedInclude: true,
		},
		{
			name:            "Unknown violation code",
			violationCode:   999999,
			state:           nil,
			violationDate:   time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC),
			expectedScore:   0,
			expectedInclude: false,
		},
		{
			name:            "Violation too old",
			violationCode:   111300, // DUI - GENERAL (5 year duration)
			state:           nil,
			violationDate:   time.Date(2017, 6, 1, 0, 0, 0, 0, time.UTC), // 6 years ago
			expectedScore:   0,
			expectedInclude: false,
		},
		{
			name:            "State exclusion - TX excludes reckless driving",
			violationCode:   111110, // RECKLESS DRIVING
			state:           pointer_utils.String("TX"),
			violationDate:   time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC),
			expectedScore:   0,
			expectedInclude: false,
		},
		{
			name:            "State exclusion - CA does not exclude reckless driving",
			violationCode:   111110, // RECKLESS DRIVING
			state:           pointer_utils.String("CA"),
			violationDate:   time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC),
			expectedScore:   9,
			expectedInclude: true,
		},
		{
			name:            "Invalid state code - should not exclude",
			violationCode:   111110, // RECKLESS DRIVING
			state:           pointer_utils.String("INVALID"),
			violationDate:   time.Date(2022, 6, 1, 0, 0, 0, 0, time.UTC),
			expectedScore:   9,
			expectedInclude: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			score, include := filterFleetModifiedMVRScoreV1(
				tt.violationCode,
				tt.state,
				effectiveDate,
				tt.violationDate,
				violationsData,
			)

			assert.Equal(t, tt.expectedScore, score)
			assert.Equal(t, tt.expectedInclude, include)
		})
	}
}
