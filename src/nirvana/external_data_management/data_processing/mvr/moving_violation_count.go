package mvr

import (
	"strconv"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

type ViolationType int

const (
	MovingViolation ViolationType = iota
	NonMovingViolation
	UncountedViolation
	UnknownViolation
)

// GetFleetMovingViolationCountV1 is the same as verisk.GetMovingViolationCount but takes versioned input to compute the
// moving violation count. Eventually, only versioned methods should be used.
func GetFleetMovingViolationCountV1(
	effectiveDate time.Time,
	report *data_fetching.MVRReportV1,
	state *string,
	isExcludedMVCEnabled bool,
	violationsData map[int64]*FleetViolationV1,
) (int, error) {
	var numMovingViolations,
		uncountedMovingViolations,
		nonMovingViolations int

	for _, violation := range report.Violations {
		code, err := strconv.ParseInt(violation.AssignedViolationCode, 10, 32)
		if err != nil {
			return -1, err
		}

		violationType := FilterFleetMVCV1(
			code,
			state,
			effectiveDate,
			violation.ViolationDate.AsTime(),
			isExcludedMVCEnabled,
			violationsData,
		)
		switch violationType {
		case MovingViolation:
			numMovingViolations++
		case NonMovingViolation:
			nonMovingViolations++
		case UncountedViolation:
			uncountedMovingViolations++
		case UnknownViolation:
			return -1, errors.Errorf("Unknown violation code %d", code)
		}
	}
	return numMovingViolations, nil
}

// FilterFleetMVCV1 is the same as verisk.FilterMVC but takes in the violations data as an input.
// Also, this method is versioned, as it's used by the new data fetcher. More concretely,
// the violations data input is a versioned struct. Eventually, only versioned methods should be used.
func FilterFleetMVCV1(
	violationCode int64,
	state *string,
	effectiveDate, violationDate time.Time,
	isExcludedMVCEnabled bool,
	violationsData map[int64]*FleetViolationV1,
) ViolationType {
	val, ok := violationsData[violationCode]
	// If the violation code is not in the fleet violation code set, return
	// unknown violation.
	if !ok {
		return UnknownViolation
	}
	durationToConsider := time.Duration(val.DurationToConsider)
	cutOffTime := effectiveDate.Add(-durationToConsider)
	// If the violation date is before the cut-off time, return non moving.
	if violationDate.Before(cutOffTime) {
		return NonMovingViolation
	}
	if val.IsMovingViolation {
		// If either the state is nil or excluded mvc is not enabled, return
		// moving violation.
		if state == nil || !isExcludedMVCEnabled {
			return MovingViolation
		}
		// If the violation code is in the fleet violation code set, check if the
		// state is in the excluded states set.
		if val.ExcludedStates[*state] {
			return UncountedViolation
		}
		return MovingViolation
	}
	return NonMovingViolation
}
