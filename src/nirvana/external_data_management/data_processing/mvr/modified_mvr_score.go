package mvr

import (
	"strconv"
	"time"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

const (
	// modifiedMVRScoreLookbackYears defines the number of years to look back when calculating Modified MVR scores
	modifiedMVRScoreLookbackYears = 3
)

// GetFleetModifiedMVRScoreV1 calculates the Modified MVR score for a driver based on their violation history.
// It follows the same pattern as GetFleetMovingViolationCountV1 but sums the ModifiedMVRScore values
// from the versioned violationsData instead of counting violations.
func GetFleetModifiedMVRScoreV1(
	effectiveDate time.Time,
	report *data_fetching.MVRReportV1,
	state *string,
	violationsData map[int64]*FleetViolationV1,
) (int64, error) {
	var totalModifiedMVRScore int64

	for _, violation := range report.Violations {
		code, err := strconv.ParseInt(violation.AssignedViolationCode, 10, 32)
		if err != nil {
			return 0, err
		}

		score, shouldInclude := filterFleetModifiedMVRScoreV1(
			code,
			state,
			effectiveDate,
			violation.ViolationDate.AsTime(),
			violationsData,
		)

		if shouldInclude {
			totalModifiedMVRScore += int64(score)
		}
	}

	return totalModifiedMVRScore, nil
}

// filterFleetModifiedMVRScoreV1 determines if a violation should be included in the Modified MVR score
// calculation and returns the score value if it should be included.
// It follows similar filtering logic as FilterFleetMVCV1 but focuses on the ModifiedMVRScore field.
func filterFleetModifiedMVRScoreV1(
	violationCode int64,
	state *string,
	effectiveDate, violationDate time.Time,
	violationsData map[int64]*FleetViolationV1,
) (int64, bool) {
	// Look up the violation in the versioned violationsData map
	violationData, exists := violationsData[violationCode]
	if !exists {
		// If violation code is not found, don't include it in the score
		return 0, false
	}

	// Check if the violation is within the duration to consider
	cutOffTime := effectiveDate.AddDate(-modifiedMVRScoreLookbackYears, 0, 0)
	if violationDate.Before(cutOffTime) {
		// Violation is too old, don't include it
		return 0, false
	}

	// Check if the state is excluded for this violation type
	if state != nil {
		if violationData.ExcludedStates[*state] {
			// State is excluded for this violation, don't include it
			return 0, false
		}
	}

	// Include the violation and return its ModifiedMVRScore
	return violationData.ModifiedMVRScore, true
}
