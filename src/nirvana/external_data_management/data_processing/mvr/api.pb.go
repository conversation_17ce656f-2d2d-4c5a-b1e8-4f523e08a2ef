// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: data_processing/mvr/api.proto

package mvr

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FleetViolationV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description        string          `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	IsMovingViolation  bool            `protobuf:"varint,2,opt,name=isMovingViolation,proto3" json:"isMovingViolation,omitempty"`
	DurationToConsider int64           `protobuf:"varint,3,opt,name=durationToConsider,proto3" json:"durationToConsider,omitempty"`
	ExcludedStates     map[string]bool `protobuf:"bytes,4,rep,name=excludedStates,proto3" json:"excludedStates,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ModifiedMVRScore   int64           `protobuf:"varint,5,opt,name=modifiedMVRScore,proto3" json:"modifiedMVRScore,omitempty"`
}

func (x *FleetViolationV1) Reset() {
	*x = FleetViolationV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_mvr_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetViolationV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetViolationV1) ProtoMessage() {}

func (x *FleetViolationV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_mvr_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetViolationV1.ProtoReflect.Descriptor instead.
func (*FleetViolationV1) Descriptor() ([]byte, []int) {
	return file_data_processing_mvr_api_proto_rawDescGZIP(), []int{0}
}

func (x *FleetViolationV1) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FleetViolationV1) GetIsMovingViolation() bool {
	if x != nil {
		return x.IsMovingViolation
	}
	return false
}

func (x *FleetViolationV1) GetDurationToConsider() int64 {
	if x != nil {
		return x.DurationToConsider
	}
	return 0
}

func (x *FleetViolationV1) GetExcludedStates() map[string]bool {
	if x != nil {
		return x.ExcludedStates
	}
	return nil
}

func (x *FleetViolationV1) GetModifiedMVRScore() int64 {
	if x != nil {
		return x.ModifiedMVRScore
	}
	return 0
}

type FleetViolationsDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[int64]*FleetViolationV1 `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FleetViolationsDataV1) Reset() {
	*x = FleetViolationsDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_data_processing_mvr_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetViolationsDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetViolationsDataV1) ProtoMessage() {}

func (x *FleetViolationsDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_data_processing_mvr_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetViolationsDataV1.ProtoReflect.Descriptor instead.
func (*FleetViolationsDataV1) Descriptor() ([]byte, []int) {
	return file_data_processing_mvr_api_proto_rawDescGZIP(), []int{1}
}

func (x *FleetViolationsDataV1) GetData() map[int64]*FleetViolationV1 {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_data_processing_mvr_api_proto protoreflect.FileDescriptor

var file_data_processing_mvr_api_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x2f, 0x6d, 0x76, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x76, 0x72, 0x22, 0xe4, 0x02, 0x0a, 0x10, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x56, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x69,
	0x73, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67,
	0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x73, 0x69, 0x64, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x43, 0x6f, 0x6e, 0x73, 0x69, 0x64, 0x65, 0x72, 0x12, 0x61, 0x0a, 0x0e, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x39, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x76, 0x72, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x31, 0x2e, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x10,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x56, 0x52, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x4d, 0x56, 0x52, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x1a, 0x41, 0x0a, 0x13, 0x45, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc1, 0x01, 0x0a, 0x15,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x48, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x76, 0x72, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x56,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a,
	0x5e, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f,
	0x6d, 0x76, 0x72, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x56, 0x31, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_data_processing_mvr_api_proto_rawDescOnce sync.Once
	file_data_processing_mvr_api_proto_rawDescData = file_data_processing_mvr_api_proto_rawDesc
)

func file_data_processing_mvr_api_proto_rawDescGZIP() []byte {
	file_data_processing_mvr_api_proto_rawDescOnce.Do(func() {
		file_data_processing_mvr_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_data_processing_mvr_api_proto_rawDescData)
	})
	return file_data_processing_mvr_api_proto_rawDescData
}

var file_data_processing_mvr_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_data_processing_mvr_api_proto_goTypes = []interface{}{
	(*FleetViolationV1)(nil),      // 0: data_processing_mvr.FleetViolationV1
	(*FleetViolationsDataV1)(nil), // 1: data_processing_mvr.FleetViolationsDataV1
	nil,                           // 2: data_processing_mvr.FleetViolationV1.ExcludedStatesEntry
	nil,                           // 3: data_processing_mvr.FleetViolationsDataV1.DataEntry
}
var file_data_processing_mvr_api_proto_depIdxs = []int32{
	2, // 0: data_processing_mvr.FleetViolationV1.excludedStates:type_name -> data_processing_mvr.FleetViolationV1.ExcludedStatesEntry
	3, // 1: data_processing_mvr.FleetViolationsDataV1.data:type_name -> data_processing_mvr.FleetViolationsDataV1.DataEntry
	0, // 2: data_processing_mvr.FleetViolationsDataV1.DataEntry.value:type_name -> data_processing_mvr.FleetViolationV1
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_data_processing_mvr_api_proto_init() }
func file_data_processing_mvr_api_proto_init() {
	if File_data_processing_mvr_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_data_processing_mvr_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetViolationV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_data_processing_mvr_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetViolationsDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_data_processing_mvr_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_data_processing_mvr_api_proto_goTypes,
		DependencyIndexes: file_data_processing_mvr_api_proto_depIdxs,
		MessageInfos:      file_data_processing_mvr_api_proto_msgTypes,
	}.Build()
	File_data_processing_mvr_api_proto = out.File
	file_data_processing_mvr_api_proto_rawDesc = nil
	file_data_processing_mvr_api_proto_goTypes = nil
	file_data_processing_mvr_api_proto_depIdxs = nil
}
