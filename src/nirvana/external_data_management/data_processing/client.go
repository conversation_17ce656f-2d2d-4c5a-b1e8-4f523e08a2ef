package data_processing

import (
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"google.golang.org/grpc"

	"nirvanatech.com/nirvana/common-go/grpc/libgrpc"
	"nirvanatech.com/nirvana/common-go/grpc/middleware"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/unwrap_reply_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/wrap_reply_interceptor"
)

const (
	serverAddress = "data-processor"
)

type ProcessorClientFactory func(fetcherClient data_fetching.FetcherClient, interceptors ...grpc.UnaryClientInterceptor) (ProcessorClient, func() error, error)

func newClientFactory(
	lc fx.Lifecycle,
	metricsClient statsd.Statter,
	processorServer ProcessorServer,
) (ProcessorClientFactory, error) {
	listener := libgrpc.NewPipeListenerWithLifeCycle(lc)

	grpcServer, err := libgrpc.NewServer(
		lc,
		listener,
		middleware.DefaultServerUnaryInterceptors(metricsClient)...,
	)
	if err != nil {
		return nil, err
	}
	// Note that we only register the service if the client factory is required via fx.
	// If no component needs the factory, we assume that they don't need the client either,
	// and in consequence, the service is also not required.
	RegisterProcessorServer(grpcServer, processorServer)

	return func(
		fetcherClient data_fetching.FetcherClient,
		interceptors ...grpc.UnaryClientInterceptor,
	) (ProcessorClient, func() error, error) {
		interceptors = buildInterceptorsForProcessorClient(fetcherClient, interceptors)

		clientConn, err := libgrpc.NewClientConn(serverAddress, listener, interceptors...)
		if err != nil {
			return nil, nil, err
		}

		return NewProcessorClient(clientConn), func() error { return clientConn.Close() }, nil
	}, nil
}

// buildInterceptorsForProcessorClient combined interceptors in a
// very specific order to ensure the correct behavior of the client.
//
// Be very careful when changing the order of the interceptors.
func buildInterceptorsForProcessorClient(
	fetcherClient data_fetching.FetcherClient,
	customInterceptors []grpc.UnaryClientInterceptor,
) []grpc.UnaryClientInterceptor {
	interceptors := make([]grpc.UnaryClientInterceptor, 0)

	interceptors = append(interceptors, wrap_reply_interceptor.New())

	// Request interceptors should go before the store interceptors because they are required in order to form the
	// store keys which is a key parameter for the store interceptors.
	interceptors = append(interceptors, newYearsInBusinessFromAuthorityHistoryRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newFleetMovingViolationCountRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newContinuousCoverageYearsRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newIsoFieldsFromNhtsaFieldsRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newNFInspectionCountRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newNFViolationsMetadataRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newNFCreditFeaturesRequestV2Interceptor(fetcherClient))
	interceptors = append(interceptors, newInsuranceHistoryYearsInBusinessRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newRetainedYearsRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newFleetModifiedMVRScoreRequestV1Interceptor(fetcherClient))
	interceptors = append(interceptors, newPriorCarrierYearsRetainedRequestV1Interceptor(fetcherClient))

	interceptors = append(interceptors, customInterceptors...)

	interceptors = append(interceptors, unwrap_reply_interceptor.New())

	return interceptors
}
