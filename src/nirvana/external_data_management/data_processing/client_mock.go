// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/external_data_management/data_processing (interfaces: ProcessorClient)
//
// Generated by this command:
//
//	mockgen -destination=client_mock.go -package=data_processing nirvanatech.com/nirvana/external_data_management/data_processing ProcessorClient
//

// Package data_processing is a generated GoMock package.
package data_processing

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
	data_fetching "nirvanatech.com/nirvana/external_data_management/data_fetching"
)

// MockProcessorClient is a mock of ProcessorClient interface.
type MockProcessorClient struct {
	ctrl     *gomock.Controller
	recorder *MockProcessorClientMockRecorder
	isgomock struct{}
}

// MockProcessorClientMockRecorder is the mock recorder for MockProcessorClient.
type MockProcessorClientMockRecorder struct {
	mock *MockProcessorClient
}

// NewMockProcessorClient creates a new mock instance.
func NewMockProcessorClient(ctrl *gomock.Controller) *MockProcessorClient {
	mock := &MockProcessorClient{ctrl: ctrl}
	mock.recorder = &MockProcessorClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProcessorClient) EXPECT() *MockProcessorClientMockRecorder {
	return m.recorder
}

// GetContinuousCoverageYearsV1 mocks base method.
func (m *MockProcessorClient) GetContinuousCoverageYearsV1(ctx context.Context, in *ContinuousCoverageYearsRequestV1, opts ...grpc.CallOption) (*ContinuousCoverageYearsV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContinuousCoverageYearsV1", varargs...)
	ret0, _ := ret[0].(*ContinuousCoverageYearsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContinuousCoverageYearsV1 indicates an expected call of GetContinuousCoverageYearsV1.
func (mr *MockProcessorClientMockRecorder) GetContinuousCoverageYearsV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContinuousCoverageYearsV1", reflect.TypeOf((*MockProcessorClient)(nil).GetContinuousCoverageYearsV1), varargs...)
}

// GetFleetModifiedMVRScoreV1 mocks base method.
func (m *MockProcessorClient) GetFleetModifiedMVRScoreV1(ctx context.Context, in *FleetModifiedMVRScoreRequestV1, opts ...grpc.CallOption) (*FleetModifiedMVRScoreV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFleetModifiedMVRScoreV1", varargs...)
	ret0, _ := ret[0].(*FleetModifiedMVRScoreV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFleetModifiedMVRScoreV1 indicates an expected call of GetFleetModifiedMVRScoreV1.
func (mr *MockProcessorClientMockRecorder) GetFleetModifiedMVRScoreV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFleetModifiedMVRScoreV1", reflect.TypeOf((*MockProcessorClient)(nil).GetFleetModifiedMVRScoreV1), varargs...)
}

// GetFleetMovingViolationCountV1 mocks base method.
func (m *MockProcessorClient) GetFleetMovingViolationCountV1(ctx context.Context, in *FleetMovingViolationCountRequestV1, opts ...grpc.CallOption) (*FleetMovingViolationCountV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFleetMovingViolationCountV1", varargs...)
	ret0, _ := ret[0].(*FleetMovingViolationCountV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFleetMovingViolationCountV1 indicates an expected call of GetFleetMovingViolationCountV1.
func (mr *MockProcessorClientMockRecorder) GetFleetMovingViolationCountV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFleetMovingViolationCountV1", reflect.TypeOf((*MockProcessorClient)(nil).GetFleetMovingViolationCountV1), varargs...)
}

// GetIsoFieldsFromNhtsaFieldsV1 mocks base method.
func (m *MockProcessorClient) GetIsoFieldsFromNhtsaFieldsV1(ctx context.Context, in *IsoFieldsFromNhtsaFieldsRequestV1, opts ...grpc.CallOption) (*IsoFieldsFromNhtsaFieldsV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIsoFieldsFromNhtsaFieldsV1", varargs...)
	ret0, _ := ret[0].(*IsoFieldsFromNhtsaFieldsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIsoFieldsFromNhtsaFieldsV1 indicates an expected call of GetIsoFieldsFromNhtsaFieldsV1.
func (mr *MockProcessorClientMockRecorder) GetIsoFieldsFromNhtsaFieldsV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIsoFieldsFromNhtsaFieldsV1", reflect.TypeOf((*MockProcessorClient)(nil).GetIsoFieldsFromNhtsaFieldsV1), varargs...)
}

// GetLatestValidRatingTierRecordV1 mocks base method.
func (m *MockProcessorClient) GetLatestValidRatingTierRecordV1(ctx context.Context, in *LatestValidRatingTierRecordRequestV1, opts ...grpc.CallOption) (*data_fetching.RatingTierRecordV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestValidRatingTierRecordV1", varargs...)
	ret0, _ := ret[0].(*data_fetching.RatingTierRecordV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestValidRatingTierRecordV1 indicates an expected call of GetLatestValidRatingTierRecordV1.
func (mr *MockProcessorClientMockRecorder) GetLatestValidRatingTierRecordV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestValidRatingTierRecordV1", reflect.TypeOf((*MockProcessorClient)(nil).GetLatestValidRatingTierRecordV1), varargs...)
}

// GetNFCreditFeaturesV1 mocks base method.
func (m *MockProcessorClient) GetNFCreditFeaturesV1(ctx context.Context, in *NFCreditFeaturesRequestV1, opts ...grpc.CallOption) (*NFCreditFeaturesV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNFCreditFeaturesV1", varargs...)
	ret0, _ := ret[0].(*NFCreditFeaturesV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFCreditFeaturesV1 indicates an expected call of GetNFCreditFeaturesV1.
func (mr *MockProcessorClientMockRecorder) GetNFCreditFeaturesV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFCreditFeaturesV1", reflect.TypeOf((*MockProcessorClient)(nil).GetNFCreditFeaturesV1), varargs...)
}

// GetNFCreditFeaturesV2 mocks base method.
func (m *MockProcessorClient) GetNFCreditFeaturesV2(ctx context.Context, in *NFCreditFeaturesRequestV2, opts ...grpc.CallOption) (*NFCreditFeaturesV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNFCreditFeaturesV2", varargs...)
	ret0, _ := ret[0].(*NFCreditFeaturesV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFCreditFeaturesV2 indicates an expected call of GetNFCreditFeaturesV2.
func (mr *MockProcessorClientMockRecorder) GetNFCreditFeaturesV2(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFCreditFeaturesV2", reflect.TypeOf((*MockProcessorClient)(nil).GetNFCreditFeaturesV2), varargs...)
}

// GetNFInspectionCountV1 mocks base method.
func (m *MockProcessorClient) GetNFInspectionCountV1(ctx context.Context, in *NFInspectionCountRequestV1, opts ...grpc.CallOption) (*NFInspectionCountV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNFInspectionCountV1", varargs...)
	ret0, _ := ret[0].(*NFInspectionCountV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFInspectionCountV1 indicates an expected call of GetNFInspectionCountV1.
func (mr *MockProcessorClientMockRecorder) GetNFInspectionCountV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFInspectionCountV1", reflect.TypeOf((*MockProcessorClient)(nil).GetNFInspectionCountV1), varargs...)
}

// GetNFViolationsMetadataV1 mocks base method.
func (m *MockProcessorClient) GetNFViolationsMetadataV1(ctx context.Context, in *NFViolationsMetadataRequestV1, opts ...grpc.CallOption) (*NFViolationsMetadataV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNFViolationsMetadataV1", varargs...)
	ret0, _ := ret[0].(*NFViolationsMetadataV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFViolationsMetadataV1 indicates an expected call of GetNFViolationsMetadataV1.
func (mr *MockProcessorClientMockRecorder) GetNFViolationsMetadataV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFViolationsMetadataV1", reflect.TypeOf((*MockProcessorClient)(nil).GetNFViolationsMetadataV1), varargs...)
}

// GetPriorCarrierYearsRetainedV1 mocks base method.
func (m *MockProcessorClient) GetPriorCarrierYearsRetainedV1(ctx context.Context, in *PriorCarrierYearsRetainedRequestV1, opts ...grpc.CallOption) (*PriorCarrierYearsRetainedV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPriorCarrierYearsRetainedV1", varargs...)
	ret0, _ := ret[0].(*PriorCarrierYearsRetainedV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPriorCarrierYearsRetainedV1 indicates an expected call of GetPriorCarrierYearsRetainedV1.
func (mr *MockProcessorClientMockRecorder) GetPriorCarrierYearsRetainedV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPriorCarrierYearsRetainedV1", reflect.TypeOf((*MockProcessorClient)(nil).GetPriorCarrierYearsRetainedV1), varargs...)
}

// GetRetainedYearsV1 mocks base method.
func (m *MockProcessorClient) GetRetainedYearsV1(ctx context.Context, in *RetainedYearsRequestV1, opts ...grpc.CallOption) (*RetainedYearsV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRetainedYearsV1", varargs...)
	ret0, _ := ret[0].(*RetainedYearsV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRetainedYearsV1 indicates an expected call of GetRetainedYearsV1.
func (mr *MockProcessorClientMockRecorder) GetRetainedYearsV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRetainedYearsV1", reflect.TypeOf((*MockProcessorClient)(nil).GetRetainedYearsV1), varargs...)
}

// GetYearsInBusinessFromAuthorityHistoryV1 mocks base method.
func (m *MockProcessorClient) GetYearsInBusinessFromAuthorityHistoryV1(ctx context.Context, in *YearsInBusinessFromAuthorityHistoryRequestV1, opts ...grpc.CallOption) (*YearsInBusinessFromAuthorityHistoryV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetYearsInBusinessFromAuthorityHistoryV1", varargs...)
	ret0, _ := ret[0].(*YearsInBusinessFromAuthorityHistoryV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYearsInBusinessFromAuthorityHistoryV1 indicates an expected call of GetYearsInBusinessFromAuthorityHistoryV1.
func (mr *MockProcessorClientMockRecorder) GetYearsInBusinessFromAuthorityHistoryV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYearsInBusinessFromAuthorityHistoryV1", reflect.TypeOf((*MockProcessorClient)(nil).GetYearsInBusinessFromAuthorityHistoryV1), varargs...)
}

// GetYearsInBusinessFromInsuranceHistoryV1 mocks base method.
func (m *MockProcessorClient) GetYearsInBusinessFromInsuranceHistoryV1(ctx context.Context, in *InsuranceHistoryYearsInBusinessRequestV1, opts ...grpc.CallOption) (*InsuranceHistoryYearsInBusinessV1, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetYearsInBusinessFromInsuranceHistoryV1", varargs...)
	ret0, _ := ret[0].(*InsuranceHistoryYearsInBusinessV1)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYearsInBusinessFromInsuranceHistoryV1 indicates an expected call of GetYearsInBusinessFromInsuranceHistoryV1.
func (mr *MockProcessorClientMockRecorder) GetYearsInBusinessFromInsuranceHistoryV1(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYearsInBusinessFromInsuranceHistoryV1", reflect.TypeOf((*MockProcessorClient)(nil).GetYearsInBusinessFromInsuranceHistoryV1), varargs...)
}
