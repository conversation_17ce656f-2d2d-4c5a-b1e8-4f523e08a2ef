package data_processing

import (
	"context"
	"math"
	"sort"
	"time"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/external_data_management/data_processing/metrics"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing/fmcsa"
	"nirvanatech.com/nirvana/external_data_management/data_processing/lni"
	"nirvanatech.com/nirvana/external_data_management/data_processing/mvr"
	"nirvanatech.com/nirvana/external_data_management/data_processing/national_credit_file"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

type serverDeps struct {
	fx.In
	StoreManager  store_management.StoreManager
	MetricsClient statsd.Statter
}

type serverImpl struct {
	deps serverDeps
}

func newServer(deps serverDeps) ProcessorServer {
	return &serverImpl{deps: deps}
}

var _ ProcessorServer = &serverImpl{}

// GetFleetMovingViolationCountV1 is a Fleet specific method (don't use for Non Fleet).
// The UsState field in request should be the string version of the USState interface.
// In other words, call String() on the USState.
func (p *serverImpl) GetFleetMovingViolationCountV1(
	ctx context.Context,
	request *FleetMovingViolationCountRequestV1,
) (*FleetMovingViolationCountV1, error) {
	staticDataFetcherFn := func() (any, error) {
		return mvr.GetFleetViolationsDataV1(ctx, p.deps.StoreManager)
	}

	cachedData, err := sharedCache.load(fleetViolationsDataV1CacheKey, staticDataFetcherFn)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get fleet violations data V1")
	}

	fleetViolationData, ok := cachedData.(*mvr.FleetViolationsDataV1)
	if !ok {
		return nil, errors.New("failed to cast cached data to FleetViolationsDataV1")
	}

	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	count, err := mvr.GetFleetMovingViolationCountV1(
		request.EffectiveDate.AsTime(),
		fetchedData.Report,
		request.UsState,
		request.IsExcludedMVCEnabled,
		fleetViolationData.Data,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to calculate fleet moving violation count V1")
	}

	return &FleetMovingViolationCountV1{Count: int64(count)}, nil
}

// GetFleetModifiedMVRScoreV1 is a Fleet specific method (don't use for Non Fleet).
// The UsState field in request should be the string version of the USState interface.
// In other words, call String() on the USState.
func (p *serverImpl) GetFleetModifiedMVRScoreV1(
	ctx context.Context,
	request *FleetModifiedMVRScoreRequestV1,
) (*FleetModifiedMVRScoreV1, error) {
	staticDataFetcherFn := func() (any, error) {
		return mvr.GetFleetViolationsDataV1(ctx, p.deps.StoreManager)
	}

	cachedData, err := sharedCache.load(fleetViolationsDataV1CacheKey, staticDataFetcherFn)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get fleet violations data V1")
	}

	fleetViolationData, ok := cachedData.(*mvr.FleetViolationsDataV1)
	if !ok {
		return nil, errors.New("failed to cast cached data to FleetViolationsDataV1")
	}

	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	score, err := mvr.GetFleetModifiedMVRScoreV1(
		request.EffectiveDate.AsTime(),
		fetchedData.Report,
		request.UsState,
		fleetViolationData.Data,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to calculate fleet modified MVR score V1")
	}

	return &FleetModifiedMVRScoreV1{Score: score}, nil
}

func (p *serverImpl) GetIsoFieldsFromNhtsaFieldsV1(
	ctx context.Context,
	request *IsoFieldsFromNhtsaFieldsRequestV1,
) (*IsoFieldsFromNhtsaFieldsV1, error) {
	staticDataFetcherFn := func() (any, error) {
		return vin.GetNhtsaToIsoMappingV1(ctx, p.deps.StoreManager)
	}

	cachedData, err := sharedCache.load(nhtsaToIsoMappingV1, staticDataFetcherFn)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get NHTSA to ISO mapping V1")
	}

	nhtsaToIsoMapping, ok := cachedData.(*vin.NhtsaToIsoMappingV1)
	if !ok {
		return nil, errors.New("failed to cast cached data to NhtsaToIsoMappingV1")
	}

	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	isoVehicleType, iseWeightGroup, err := vin.IsoFieldsFromNhtsaFieldsV1(
		fetchedData.VehicleType,
		fetchedData.VehicleBodyClass,
		fetchedData.VehicleWeightClass,
		nhtsaToIsoMapping,
	)
	isProblematic := errors.Is(err, vin.EntryIsProblematicErr)
	if err != nil && !isProblematic {
		return nil, errors.Wrap(err, "failed to map NHTSA fields to ISO fields V1")
	}

	retval := IsoFieldsFromNhtsaFieldsV1{
		VehicleType:   *isoVehicleType,
		WeightGroup:   *iseWeightGroup,
		IsProblematic: isProblematic,
	}

	return &retval, nil
}

// Deprecated: Use GetNFCreditFeaturesV2 instead.
func (p *serverImpl) GetNFCreditFeaturesV1(
	ctx context.Context,
	request *NFCreditFeaturesRequestV1,
) (retval *NFCreditFeaturesV1, err error) {
	nationalCreditFile := request.GetNationalCreditFile()
	if nationalCreditFile == nil {
		return nil, errors.New("national credit file is nil")
	}

	creditReportDate := nationalCreditFile.DateOfCreditReportRun
	if creditReportDate == nil {
		return nil, errors.New("report date of credit report run is nil")
	}

	creditScoreFeatures, err := national_credit_file.GetCreditScoreFeaturesV1(
		ctx,
		p.deps.MetricsClient,
		request.OwnerDOB.AsTime(),
		nationalCreditFile,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get credit score features from NCF report V1")
	}

	var dateOfMostRecentAutoTrade *timestamppb.Timestamp
	if creditScoreFeatures.DateOfMostRecentAutoTrade != nil {
		dateOfMostRecentAutoTrade = timestamppb.New(*creditScoreFeatures.DateOfMostRecentAutoTrade)
	}

	var dateOfOldestTradeline *timestamppb.Timestamp
	if creditScoreFeatures.DateOfOldestTradeline != nil {
		dateOfOldestTradeline = timestamppb.New(*creditScoreFeatures.DateOfOldestTradeline)
	}

	var dateOfEarliestTradeline *timestamppb.Timestamp
	if creditScoreFeatures.DateOfEarliestTradeline != nil {
		dateOfEarliestTradeline = timestamppb.New(*creditScoreFeatures.DateOfEarliestTradeline)
	}

	ownerAgeAtCreditReportRunInYears := time_utils.Years(creditReportDate.AsTime().Sub(request.OwnerDOB.AsTime()))

	retval = &NFCreditFeaturesV1{
		DateOfOldestTradeline:                                       dateOfOldestTradeline,
		OwnerAgeAtEarliestTradelineInYears:                          int32(creditScoreFeatures.OwnerAgeAtEarliestTradelineInYears),
		DateOfMostRecentAutoTrade:                                   dateOfMostRecentAutoTrade,
		NumberOfCurrentSatisfactoryTrades:                           int32(creditScoreFeatures.NumberOfCurrentSatisfactoryTrades),
		NumberOfTradesWithDelinquency:                               int32(creditScoreFeatures.NumberOfTradesWithDelinquency),
		MonthsSinceMostRecentWriteOff:                               int32(creditScoreFeatures.MonthsSinceMostRecentWriteOff),
		NumberOfDerogatoryInstallmentTrades:                         int32(creditScoreFeatures.NumberOfDerogatoryInstallmentTrades),
		RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades: float32(creditScoreFeatures.RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades),
		RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades:  float32(creditScoreFeatures.RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades),
		NumberOfTradeLinesOpenedInLast12Months:                      int32(creditScoreFeatures.NumberOfTradeLinesOpenedInLast12Months),
		MonthsSinceMostRecentBankRevolvingTradeOpened:               int32(creditScoreFeatures.MonthsSinceMostRecentBankRevolvingTradeOpened),
		NumberOfNonInsuranceInquiries:                               int32(creditScoreFeatures.NumberOfNonInsuranceInquiries),
		AgeOfOldestTradelineInMonths:                                int32(creditScoreFeatures.AgeOfOldestTradelineInMonths),
		AgeOfMostRecentAutoTradeInMonths:                            int32(creditScoreFeatures.AgeOfMostRecentAutoTradeInMonths),
		DateOfEarliestTradeline:                                     dateOfEarliestTradeline,
		NoHit:                                                       creditScoreFeatures.NoHit,
		ThinFile:                                                    creditScoreFeatures.ThinFile,
		IsOwnerAgeAllowed:                                           creditScoreFeatures.IsOwnerAgeAllowed,
		DateOfCreditReportRun:                                       creditReportDate,
		OwnerAgeAtCreditReportRunInYears:                            pointer_utils.ToPointer(int32(ownerAgeAtCreditReportRunInYears)),
	}

	return retval, nil
}

func (p *serverImpl) GetYearsInBusinessFromAuthorityHistoryV1(
	ctx context.Context,
	request *YearsInBusinessFromAuthorityHistoryRequestV1,
) (*YearsInBusinessFromAuthorityHistoryV1, error) {
	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	yearsInBusiness, err := lni.YearsInBusinessFromAuthorityHistoryV1(
		ctx,
		fetchedData.AuthorityHistoryRecords,
		request.EffectiveDate.AsTime(),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to calculate years in business from authority history v1")
	}

	return &YearsInBusinessFromAuthorityHistoryV1{Years: yearsInBusiness.Float64}, nil
}

// GetNFCreditFeaturesV2 is a wrapper around GetNFCreditFeaturesV1.
// GetNFCreditFeaturesV1 is the main method to get credit features but it did not take into account the address
// of the owner, so if any changes are made to the address we would still be fetching the older file from the cache.
// Address is not part of the request because it is not part of the cache key.
func (p *serverImpl) GetNFCreditFeaturesV2(
	ctx context.Context,
	request *NFCreditFeaturesRequestV2,
) (retval *NFCreditFeaturesV1, err error) {
	defer func() {
		success := err == nil
		ssnIncluded := request.SsnLastFour != nil && *request.SsnLastFour != ""

		var hit bool
		if retval != nil {
			hit = !retval.NoHit
		}

		err := metrics.EmitCreditReportFeaturesV1Metric(ctx, p.deps.MetricsClient, success, hit, ssnIncluded)
		if err != nil {
			log.Error(ctx, "failed to emit credit report feature metric", log.Err(err))
		}
	}()

	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	req := &NFCreditFeaturesRequestV1{
		OwnerName:          request.OwnerName,
		OwnerDOB:           request.OwnerDOB,
		NationalCreditFile: fetchedData.NationalCreditFile,
	}

	retval, err = p.GetNFCreditFeaturesV1(ctx, req)
	return retval, err
}

func (p *serverImpl) GetContinuousCoverageYearsV1(
	_ context.Context,
	request *ContinuousCoverageYearsRequestV1,
) (*ContinuousCoverageYearsV1, error) {
	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	records := lni.FilterAndSortInsuranceRecordsByEffectiveDateV1(
		fetchedData.InsuranceRecords,
		request.EffectiveDate.AsTime(),
	)
	if len(records) == 0 {
		return nil, errors.New("empty insurance records")
	}

	hasActiveInsurance := lni.HasActiveInsuranceV1(
		records,
		request.EffectiveDate.AsTime(),
	)
	if !hasActiveInsurance {
		return &ContinuousCoverageYearsV1{
			Years: 0,
		}, nil
	}

	startDate, err := lni.GetInsuranceStartDateV1(
		records,
		0,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get insurance start date")
	}

	years := time_utils.Years(request.EffectiveDate.AsTime().Sub(*startDate))

	return &ContinuousCoverageYearsV1{
		Years: years,
	}, nil
}

func (p *serverImpl) GetYearsInBusinessFromInsuranceHistoryV1(
	_ context.Context,
	request *InsuranceHistoryYearsInBusinessRequestV1,
) (*InsuranceHistoryYearsInBusinessV1, error) {
	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	records := fetchedData.InsuranceRecords
	if len(records) == 0 {
		return nil, errors.New("empty insurance records")
	}

	lni.SortInsuranceRecordsByEffectiveDateV1(records)

	startDate, err := lni.GetInsuranceStartDateV1(
		records,
		30*time_utils.Day,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get insurance start date")
	}

	years := time_utils.Years(request.EffectiveDate.AsTime().Sub(*startDate))

	return &InsuranceHistoryYearsInBusinessV1{
		Years: years,
	}, nil
}

func (p *serverImpl) GetLatestValidRatingTierRecordV1(
	_ context.Context,
	request *LatestValidRatingTierRecordRequestV1,
) (*data_fetching.RatingTierRecordV1, error) {
	records := request.GetRecords()
	if len(records) == 0 {
		return nil, errors.New("empty rating tier records")
	}
	sort.Slice(records, func(i, j int) bool {
		return records[i].Date.AsTime().After(records[j].Date.AsTime())
	})

	for _, record := range records {
		// This filter of having not-nil PowerUnits, AverageMiles and Date exists because the puller has a bug, where
		// the latest record within a certain row, which date is for the same month as the dump date's month, has nil
		// data. Therefore, we look for the most-recent but valid record which has not-nil PowerUnits, AverageMiles and
		// Date.
		if record.PowerUnits != nil && record.AverageMiles != nil && record.Date != nil {
			return record, nil
		}
	}
	return nil, errors.New("no valid rating tier record")
}

func (p *serverImpl) GetNFInspectionCountV1(
	_ context.Context,
	request *NFInspectionCountRequestV1,
) (*NFInspectionCountV1, error) {
	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	inspections := fetchedData.InspectionRecords.Records
	effectiveDate := request.EffectiveDate.AsTime()

	inspectionCount, err := fmcsa.CalculateNFInspectionCountV1(inspections, effectiveDate)
	if err != nil {
		return nil, errors.Wrap(err, "failed to calculate inspection count V1")
	}

	return &NFInspectionCountV1{
		Count: inspectionCount,
	}, nil
}

func (p *serverImpl) GetNFViolationsMetadataV1(
	_ context.Context,
	request *NFViolationsMetadataRequestV1,
) (*NFViolationsMetadataV1, error) {
	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	violations := fetchedData.ViolationRecords.Records
	effectiveDate := request.EffectiveDate.AsTime()

	oosViolations, oosSeverityWeight, nonOosViolations, nonOosSeverityWeight, err := fmcsa.CalculateNFViolationsMetadataV1(
		violations,
		effectiveDate,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to calculate violations metadata V1")
	}

	return &NFViolationsMetadataV1{
		OosViolations:        oosViolations,
		OosSeverityWeight:    oosSeverityWeight,
		NonoosViolations:     nonOosViolations,
		NosoosSeverityWeight: nonOosSeverityWeight,
	}, nil
}

func (p *serverImpl) GetRetainedYearsV1(
	_ context.Context,
	request *RetainedYearsRequestV1,
) (*RetainedYearsV1, error) {
	if request.GetFetchedData() == nil || request.GetFetchedData().Policies == nil {
		return nil, errors.New("invalid request data")
	}

	policies := request.GetFetchedData().Policies.Policies
	if len(policies) == 0 {
		return &RetainedYearsV1{Years: 0}, nil
	}

	effectiveDate := request.GetEffectiveDate().AsTime()

	// Sort policies by start date in descending order (newest first)
	sort.Slice(policies, func(i, j int) bool {
		return policies[i].EffectiveDate.AsTime().After(policies[j].EffectiveDate.AsTime())
	})

	// Check for continuous coverage up to the effective date
	if len(policies) > 0 && effectiveDate.After(policies[0].EffectiveDateTo.AsTime()) {
		return &RetainedYearsV1{Years: 0}, nil
	}

	// Initialize continuous coverage period with the most recent policy
	start := policies[0].EffectiveDate.AsTime()

	for i := 1; i < len(policies); i++ {
		policyToUse := policies[i]

		// Break if gap between policies is more than 24 hours
		if start.Sub(policyToUse.EffectiveDateTo.AsTime()) > 24*time.Hour {
			break
		}

		start = policyToUse.EffectiveDate.AsTime()
	}

	return &RetainedYearsV1{Years: int32(math.Floor(time_utils.Years(effectiveDate.Sub(start))))}, nil
}

func (p *serverImpl) GetPriorCarrierYearsRetainedV1(
	ctx context.Context,
	request *PriorCarrierYearsRetainedRequestV1,
) (*PriorCarrierYearsRetainedV1, error) {
	fetchedData := request.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("fetched data is nil")
	}

	if len(fetchedData.InsuranceRecords) == 0 {
		return &PriorCarrierYearsRetainedV1{Years: 0}, nil
	}

	effectiveDate := request.EffectiveDate.AsTime()

	years, err := lni.GetPriorCarrierYearsRetainedV1(effectiveDate, fetchedData.InsuranceRecords)
	if err != nil {
		return nil, errors.Wrap(err, "failed to calculate prior carrier years retained")
	}

	return &PriorCarrierYearsRetainedV1{Years: years}, nil
}
