load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "metrics",
    srcs = [
        "constants.go",
        "metrics.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/metrics",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/external_data_management/metrics",
        "@com_github_cactus_go_statsd_client_v5//statsd",
    ],
)
