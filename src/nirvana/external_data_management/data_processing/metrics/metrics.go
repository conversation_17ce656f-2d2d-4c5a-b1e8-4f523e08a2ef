package metrics

import (
	"context"
	"strconv"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"nirvanatech.com/nirvana/common-go/log"
)

const (
	metricNameCreditReportFeature   = Namespace + ".credit_report_feature"
	metricNameCreditScoreProcessing = Namespace + ".credit_score_processing"
)

func EmitCreditReportFeaturesV1Metric(
	ctx context.Context,
	metricsClient statsd.Statter,
	success bool,
	hit bool,
	ssnIncluded bool,
) error {
	tags := []statsd.Tag{
		{"success", strconv.FormatBool(success)},
		{"hit", strconv.FormatBool(hit)},
		{"ssn_included", strconv.FormatBool(ssnIncluded)},
	}
	err := metricsClient.Inc(metricNameCreditReportFeature, 1, defaultSampleRate, tags...)
	if err != nil {
		log.Error(
			ctx,
			"failed to emit metric",
			log.String("metricName", metricNameCreditReportFeature),
			log.Err(err),
		)
		return err
	}
	return nil
}

func EmitCreditScoreProcessingStatusMetric(
	ctx context.Context,
	metricsClient statsd.Statter,
	processingStatus string,
) error {
	tags := []statsd.Tag{
		{"processing_status", processingStatus},
	}
	err := metricsClient.Inc(metricNameCreditScoreProcessing, 1, defaultSampleRate, tags...)
	if err != nil {
		log.Error(
			ctx,
			"failed to emit credit score processing metric",
			log.String("metricName", metricNameCreditScoreProcessing),
			log.Err(err),
		)
		return err
	}
	return nil
}
