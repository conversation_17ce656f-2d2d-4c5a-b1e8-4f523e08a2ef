package data_processing

import (
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/crypto_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing/lni"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin"
	"nirvanatech.com/nirvana/external_data_management/store_management"
)

/*
	These methods and constants are used to build the path on S3 where data will be stored.
	DO NOT MODIFY EXISTING CODE, ONLY ADD NEW.

	If you are adding a method to the processor, make sure that your request implements store_management.StoreKey
	interface so your method can be wrapped by the store interceptor.
*/

const (
	fleetMovingViolationCountV1ResourceType           = "FleetMovingViolationCountV1"
	fleetModifiedMVRScoreV1ResourceType               = "FleetModifiedMVRScoreV1"
	isoFieldsFromNhtsaFieldsV1ResourceType            = "IsoFieldsFromNhtsaFieldsV1"
	nfCreditFeaturesV1ResourceType                    = "NFCreditFeaturesV1"
	yearsInBusinessFromAuthorityHistoryV1ResourceType = "YearsInBusinessFromAuthorityHistoryV1"
	latestValidRatingTierRecordV1ResourceType         = "LatestValidRatingTierRecordV1"
	nfCreditFeaturesV2ResourceType                    = "NFCreditFeaturesV2"
	nfInspectionCountV1ResourceType                   = "NFInspectionCountV1"
	nfViolationsMetadataV1ResourceType                = "NFViolationsMetadataV1"
	yearsInBusinessFromInsuranceHistoryV1ResourceType = "YearsInBusinessFromInsuranceHistoryV1"
	continuousCoverageYearsV1ResourceType             = "ContinuousCoverageYearsV1"
	nfCreditFeaturesV3ResourceType                    = "NFCreditFeaturesV3"
	retainedYearsV1ResourceType                       = "RetainedYearsV1"
	priorCarrierYearsRetainedV1ResourceType           = "PriorCarrierYearsRetainedV1"
)

func (r *FleetMovingViolationCountRequestV1) GetResourceType() string {
	return fleetMovingViolationCountV1ResourceType
}

func (r *FleetModifiedMVRScoreRequestV1) GetResourceType() string {
	return fleetModifiedMVRScoreV1ResourceType
}

func (r *IsoFieldsFromNhtsaFieldsRequestV1) GetResourceType() string {
	return isoFieldsFromNhtsaFieldsV1ResourceType
}

func (r *NFCreditFeaturesRequestV1) GetResourceType() string {
	return nfCreditFeaturesV1ResourceType
}

func (r *YearsInBusinessFromAuthorityHistoryRequestV1) GetResourceType() string {
	return yearsInBusinessFromAuthorityHistoryV1ResourceType
}

func (r *NFCreditFeaturesRequestV2) GetResourceType() string {
	return nfCreditFeaturesV2ResourceType
}

func (r *LatestValidRatingTierRecordRequestV1) GetResourceType() string {
	return latestValidRatingTierRecordV1ResourceType
}

func (r *NFInspectionCountRequestV1) GetResourceType() string {
	return nfInspectionCountV1ResourceType
}

func (r *NFViolationsMetadataRequestV1) GetResourceType() string {
	return nfViolationsMetadataV1ResourceType
}

func (r *InsuranceHistoryYearsInBusinessRequestV1) GetResourceType() string {
	return yearsInBusinessFromInsuranceHistoryV1ResourceType
}

func (r *ContinuousCoverageYearsRequestV1) GetResourceType() string {
	return continuousCoverageYearsV1ResourceType
}

func (r *RetainedYearsRequestV1) GetResourceType() string {
	return retainedYearsV1ResourceType
}

func (r *PriorCarrierYearsRetainedRequestV1) GetResourceType() string {
	return priorCarrierYearsRetainedV1ResourceType
}

func (k *FleetMovingViolationCountRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := k.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	fileNameComponents := []string{
		fetchedData.Report.DlState,
		fetchedData.Report.DlNumber,
		strconv.FormatBool(k.IsExcludedMVCEnabled),
	}

	if k.EffectiveDate != nil {
		fileNameComponents = append(fileNameComponents, k.EffectiveDate.AsTime().Format("02012006"))
	}

	if k.UsState != nil {
		fileNameComponents = append(fileNameComponents, *k.UsState)
	}

	return fileNameComponents, nil
}

func (k *FleetModifiedMVRScoreRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := k.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	fileNameComponents := []string{
		fetchedData.Report.DlState,
		fetchedData.Report.DlNumber,
		strconv.Itoa(len(fetchedData.Report.Violations)),
	}

	if k.EffectiveDate != nil {
		fileNameComponents = append(fileNameComponents, k.EffectiveDate.AsTime().Format("02012006"))
	}

	if k.UsState != nil {
		fileNameComponents = append(fileNameComponents, *k.UsState)
	}

	return fileNameComponents, nil
}

func (r *IsoFieldsFromNhtsaFieldsRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := r.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	nhtsaToIsoKey, err := vin.CreateNhtsaToIsoKeyV1(
		fetchedData.VehicleType,
		fetchedData.VehicleBodyClass,
		fetchedData.VehicleWeightClass,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create nhtsa to iso key")
	}

	fileNameComponents := []string{
		nhtsaToIsoKey,
	}

	return fileNameComponents, nil
}

func (r *NFCreditFeaturesRequestV1) GetFileNameComponents() ([]string, error) {
	nationalCreditFile := r.GetNationalCreditFile()
	if nationalCreditFile == nil {
		return nil, errors.New("national credit file is nil")
	}

	dateOfCreditReportRun := nationalCreditFile.GetDateOfCreditReportRun()
	if dateOfCreditReportRun == nil {
		return nil, errors.New("date of credit report run is nil")
	}

	fileNameComponents := []string{
		fmt.Sprintf(
			"%s::%s::%s",
			r.OwnerName,
			dateOfCreditReportRun.AsTime(),
			r.OwnerDOB.AsTime(),
		),
	}

	return fileNameComponents, nil
}

func (r *YearsInBusinessFromAuthorityHistoryRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := r.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	keys := slice_utils.Map(fetchedData.AuthorityHistoryRecords, authorityRecordV1ToKey)
	joinedKeys := strings.Join(keys, "-")
	hashedKeys := crypto_utils.HashStringSecret(joinedKeys)

	fileNameComponents := []string{
		r.EffectiveDate.AsTime().Format("02012006"),
		hashedKeys,
	}

	return fileNameComponents, nil
}

func (r *LatestValidRatingTierRecordRequestV1) GetFileNameComponents() ([]string, error) {
	keys, err := slice_utils.MapErr(r.GetRecords(), ratingTierRecordV1ToKey)
	if err != nil {
		return nil, err
	}
	joinedKeys := strings.Join(keys, "-")
	hashedKeys := crypto_utils.HashStringSecret(joinedKeys)

	fileNameComponents := []string{
		hashedKeys,
	}

	return fileNameComponents, nil
}

func (r *NFCreditFeaturesRequestV2) GetFileNameComponents() ([]string, error) {
	// We are using a hashed-key here to include all the information that
	// is needed to uniquely identify a request. As change in any of the
	// fields(name, dob, address) would also require us to
	// re-process the credit file instead of getting it from the cache server

	data := r.OwnerName + r.OwnerDOB.AsTime().String()
	if r.Address != nil {
		data += r.Address.Street + r.Address.City + r.Address.State + r.Address.Zip
	}
	if r.SsnLastFour != nil {
		data += *r.SsnLastFour
	}
	hashedVal := crypto_utils.HashStringSecret(data)

	fileNameComponents := []string{
		hashedVal,
	}

	return fileNameComponents, nil
}

func (r *NFInspectionCountRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := r.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	keys := slice_utils.Map(
		fetchedData.InspectionRecords.Records,
		func(r *data_fetching.FMCSAInspectionRecordV1) string {
			return r.RowID
		},
	)
	joinedKeys := strings.Join(keys, "-")
	hashedKeys := crypto_utils.HashStringSecret(joinedKeys)

	fileNameComponents := []string{
		r.EffectiveDate.AsTime().Format("02012006"),
		hashedKeys,
	}

	return fileNameComponents, nil
}

func (r *NFViolationsMetadataRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := r.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	keys := slice_utils.Map(
		fetchedData.ViolationRecords.Records,
		func(r *data_fetching.FMCSAViolationRecordV1) string {
			return r.RowId
		},
	)
	joinedKeys := strings.Join(keys, "-")
	hashedKeys := crypto_utils.HashStringSecret(joinedKeys)

	fileNameComponents := []string{
		r.EffectiveDate.AsTime().Format("02012006"),
		hashedKeys,
	}

	return fileNameComponents, nil
}

func (r *InsuranceHistoryYearsInBusinessRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := r.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	records := fetchedData.InsuranceRecords
	lni.SortInsuranceRecordsByEffectiveDateV1(records)

	keys := slice_utils.Map(records, insuranceRecordV1ToKey)
	joinedKeys := strings.Join(keys, "-")
	hashedKeys := crypto_utils.HashStringSecret(joinedKeys)

	fileNameComponents := []string{
		r.EffectiveDate.AsTime().Format("02012006"),
		hashedKeys,
	}

	return fileNameComponents, nil
}

func (r *ContinuousCoverageYearsRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := r.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	records := lni.FilterAndSortInsuranceRecordsByEffectiveDateV1(
		fetchedData.InsuranceRecords,
		r.EffectiveDate.AsTime(),
	)

	keys := slice_utils.Map(records, insuranceRecordV1ToKey)
	joinedKeys := strings.Join(keys, "-")
	hashedKeys := crypto_utils.HashStringSecret(joinedKeys)

	fileNameComponents := []string{
		r.EffectiveDate.AsTime().Format("02012006"),
		hashedKeys,
	}

	return fileNameComponents, nil
}

func (r *PriorCarrierYearsRetainedRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := r.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	records := fetchedData.InsuranceRecords
	lni.SortInsuranceRecordsByEffectiveDateV1(records)

	keys := slice_utils.Map(records, insuranceRecordV1ToKey)
	joinedKeys := strings.Join(keys, "-")
	hashedKeys := crypto_utils.HashStringSecret(joinedKeys)

	fileNameComponents := []string{
		r.EffectiveDate.AsTime().Format("02012006"),
		hashedKeys,
	}

	return fileNameComponents, nil
}

func insuranceRecordV1ToKey(record *data_fetching.InsuranceRecordV1) string {
	recordKey := []string{
		fmt.Sprint(record.DotNumber),
		record.EffectiveDate.AsTime().Format("02012006"),
	}

	if record.CancelEffectiveDate != nil {
		recordKey = append(recordKey, record.CancelEffectiveDate.AsTime().Format("02012006"))
	}

	recordKey = append(recordKey, record.InsuranceCompanyName)

	return strings.Join(recordKey, "-")
}

func authorityRecordV1ToKey(record *data_fetching.AuthorityHistoryRecordV1) string {
	recordKey := []string{
		fmt.Sprint(record.DotNumber),
		record.AuthorityType,
		record.OriginalActionServedDate.AsTime().Format("02012006"),
	}

	if record.FinalActionServedDate != nil {
		recordKey = append(recordKey, record.FinalActionServedDate.AsTime().Format("02012006"))
	}

	return strings.Join(recordKey, "-")
}

func (r *RetainedYearsRequestV1) GetFileNameComponents() ([]string, error) {
	fetchedData := r.GetFetchedData()
	if fetchedData == nil {
		return nil, errors.New("nil fetched data can't be used to build store key")
	}

	keys := slice_utils.Map(
		fetchedData.Policies.Policies,
		policyV1ToKey,
	)
	sort.Strings(keys)
	joinedKeys := strings.Join(keys, "-")
	hashedKeys := crypto_utils.HashStringSecret(joinedKeys)

	fileNameComponents := []string{
		r.EffectiveDate.AsTime().Format("02012006"),
		hashedKeys,
	}

	return fileNameComponents, nil
}

func policyV1ToKey(policy *data_fetching.NirvanaPolicyV1) string {
	if policy == nil {
		return ""
	}
	return policy.Id
}

func ratingTierRecordV1ToKey(record *data_fetching.RatingTierRecordV1) (string, error) {
	bytes, err := json.Marshal(record)
	if err != nil {
		return "", errors.Wrapf(err, "failed to marshal rating tier record v1: %v", record)
	}

	return string(bytes), nil
}

var (
	_ store_management.StoreKey = &FleetMovingViolationCountRequestV1{}
	_ store_management.StoreKey = &IsoFieldsFromNhtsaFieldsRequestV1{}
	_ store_management.StoreKey = &NFCreditFeaturesRequestV1{}
	_ store_management.StoreKey = &YearsInBusinessFromAuthorityHistoryRequestV1{}
	_ store_management.StoreKey = &NFCreditFeaturesRequestV2{}
	_ store_management.StoreKey = &LatestValidRatingTierRecordRequestV1{}
	_ store_management.StoreKey = &NFInspectionCountRequestV1{}
	_ store_management.StoreKey = &NFViolationsMetadataRequestV1{}
	_ store_management.StoreKey = &InsuranceHistoryYearsInBusinessRequestV1{}
	_ store_management.StoreKey = &ContinuousCoverageYearsRequestV1{}
	_ store_management.StoreKey = &RetainedYearsRequestV1{}
	_ store_management.StoreKey = &PriorCarrierYearsRetainedRequestV1{}
)
