load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fmcsa",
    srcs = [
        "inspections.go",
        "violations.go",
    ],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/fmcsa",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/external_data_management/data_fetching",
        "@com_github_cockroachdb_errors//:errors",
    ],
)
