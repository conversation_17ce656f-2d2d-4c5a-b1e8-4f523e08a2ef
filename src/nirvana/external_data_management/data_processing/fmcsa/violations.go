package fmcsa

import (
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

const (
	violationValidityPeriodV1 = 3 // years

)

func CalculateNFViolationsMetadataV1(
	violations []*data_fetching.FMCSAViolationRecordV1,
	effectiveDate time.Time,
) (int64, int64, int64, int64, error) {
	dateValidTill := effectiveDate.AddDate(-violationValidityPeriodV1, 0, 0)
	var oosViolations, nonOosViolations, oosSeverityWeight, nonOosSeverityWeight int

	for _, violation := range violations {
		isValid, err := isValidViolation(violation, dateValidTill, effectiveDate)
		if err != nil {
			return 0, 0, 0, 0, errors.Wrap(err, "failed to validate violation")
		}

		if !isValid {
			continue
		}

		if violation.OosIndicator {
			oosViolations++
			oosSeverityWeight += int(violation.SeverityWeight)
		} else {
			nonOosViolations++
			nonOosSeverityWeight += int(violation.SeverityWeight)
		}
	}

	return int64(oosViolations), int64(oosSeverityWeight),
		int64(nonOosViolations), int64(nonOosSeverityWeight), nil
}

func isValidViolation(
	violation *data_fetching.FMCSAViolationRecordV1,
	dateValidTill, effectiveDate time.Time,
) (bool, error) {
	if violation == nil {
		return false, errors.New("nil violation record")
	}

	return violation.InspectionDate.AsTime().Before(effectiveDate) &&
		violation.InspectionDate.AsTime().After(dateValidTill), nil
}
