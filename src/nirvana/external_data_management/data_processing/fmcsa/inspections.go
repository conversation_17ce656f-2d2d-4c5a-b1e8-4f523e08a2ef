package fmcsa

import (
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

const (
	inspectionValidityPeriodV1 = 3 // years
)

func CalculateNFInspectionCountV1(
	inspections []*data_fetching.FMCSAInspectionRecordV1,
	effectiveDate time.Time,
) (int64, error) {
	inspectionCount := 0
	dateValidTill := effectiveDate.AddDate(-inspectionValidityPeriodV1, 0, 0)

	for _, inspection := range inspections {
		isValid, err := isValidInspection(inspection, dateValidTill, effectiveDate)
		if err != nil {
			return 0, errors.Wrap(err, "failed to validate inspection")
		}

		if isValid {
			inspectionCount++
		}
	}

	return int64(inspectionCount), nil
}

func isValidInspection(
	inspection *data_fetching.FMCSAInspectionRecordV1,
	dateValidTill, effectiveDate time.Time,
) (bool, error) {
	if inspection == nil {
		return false, errors.New("nil inspection record")
	}
	return inspection.InspectionDate.AsTime().Before(effectiveDate) &&
		inspection.InspectionDate.AsTime().After(dateValidTill), nil
}
