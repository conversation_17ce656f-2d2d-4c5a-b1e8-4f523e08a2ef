package national_credit_file

import (
	"context"
	_ "embed"
	"encoding/json"
	"reflect"
	"testing"
	"time"

	"nirvanatech.com/nirvana/common-go/metrics"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

//go:embed test_data/report.json
var report []byte

func Test_GetCreditScoreFeaturesFromNCFReportV1(t *testing.T) {
	var ncfReport data_fetching.NcfReportV1
	err := json.Unmarshal(report, &ncfReport)
	require.NoError(t, err)

	nationalCreditFile := &data_fetching.NationalCreditFileV1{
		NcfReport: &ncfReport,
		TransactionDetails: &data_fetching.TransactionDetailsExV1{
			ProcessingStatus: NCFSuccessStatusCode,
		},
		DateOfCreditReportRun: timestamppb.New(time.Date(2024, 7, 1, 0, 0, 0, 0, time.UTC)),
	}

	mockedMetricsClient, err := metrics.NewMockClient("test")
	require.NoError(t, err)

	type args struct {
		ctx                context.Context
		ownerDOB           time.Time
		nationalCreditFile *data_fetching.NationalCreditFileV1
	}
	tests := []struct {
		name    string
		args    args
		want    *NFCreditFeaturesV1
		wantErr bool
	}{
		{
			name: "with complete national credit file",
			args: args{
				ctx:                context.Background(),
				ownerDOB:           time.Date(1962, 5, 18, 0, 0, 0, 0, time.UTC),
				nationalCreditFile: nationalCreditFile,
			},
			want: &NFCreditFeaturesV1{
				DateOfOldestTradeline:                                       pointer_utils.Time(time.Date(2012, 4, 1, 0, 0, 0, 0, time.UTC)),
				OwnerAgeAtEarliestTradelineInYears:                          49,
				DateOfMostRecentAutoTrade:                                   pointer_utils.Time(time.Date(2021, 6, 1, 0, 0, 0, 0, time.UTC)),
				NumberOfCurrentSatisfactoryTrades:                           12,
				NumberOfTradesWithDelinquency:                               7,
				MonthsSinceMostRecentWriteOff:                               93,
				NumberOfDerogatoryInstallmentTrades:                         0,
				RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades: 0.****************,
				RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades:  0.*****************,
				NumberOfTradeLinesOpenedInLast12Months:                      0,
				MonthsSinceMostRecentBankRevolvingTradeOpened:               53,
				NumberOfNonInsuranceInquiries:                               2,
				DateOfEarliestTradeline:                                     pointer_utils.Time(time.Date(2012, 4, 1, 0, 0, 0, 0, time.UTC)),
				AgeOfOldestTradelineInMonths:                                146,
				AgeOfMostRecentAutoTradeInMonths:                            36,
				NoHit:                                                       false,
				ThinFile:                                                    false,
				IsOwnerAgeAllowed:                                           true,
			},
			wantErr: false,
		},
		{
			name: "national credit file",
			args: args{
				ctx:                context.Background(),
				ownerDOB:           time.Date(1962, 5, 18, 0, 0, 0, 0, time.UTC),
				nationalCreditFile: nil,
			},
			wantErr: true,
		},
		{
			name: "with nil transaction details file",
			args: args{
				ctx:      context.Background(),
				ownerDOB: time.Date(1962, 5, 18, 0, 0, 0, 0, time.UTC),
				nationalCreditFile: &data_fetching.NationalCreditFileV1{
					TransactionDetails: nil,
				},
			},
			wantErr: true,
		},
		{
			name: "with nil NCF report",
			args: args{
				ctx:      context.Background(),
				ownerDOB: time.Date(1962, 5, 18, 0, 0, 0, 0, time.UTC),
				nationalCreditFile: &data_fetching.NationalCreditFileV1{
					TransactionDetails: &data_fetching.TransactionDetailsExV1{},
					NcfReport:          nil,
				},
			},
			wantErr: true,
		},
		{
			name: "with nil date of credit report run",
			args: args{
				ctx:      context.Background(),
				ownerDOB: time.Date(1962, 5, 18, 0, 0, 0, 0, time.UTC),
				nationalCreditFile: &data_fetching.NationalCreditFileV1{
					TransactionDetails:    &data_fetching.TransactionDetailsExV1{},
					NcfReport:             &data_fetching.NcfReportV1{},
					DateOfCreditReportRun: nil,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetCreditScoreFeaturesV1(
				tt.args.ctx,
				mockedMetricsClient,
				tt.args.ownerDOB,
				tt.args.nationalCreditFile,
			)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCreditScoreFeaturesFromNcfReport() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCreditScoreFeaturesFromNcfReport() got = %v, want %v", got, tt.want)
			}
		})
	}
}
