package national_credit_file

import (
	"context"
	"errors"
	"slices"
	"strings"
	"time"

	"nirvanatech.com/nirvana/external_data_management/data_processing/metrics"

	"github.com/cactus/go-statsd-client/v5/statsd"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
)

var ClosedAccountCodes = []string{"BE", "FA", "CW", "HF", "IR", "KP", "BD", "CF", "CG", "DA", "A3"}

const (
	NCFUnscoredStatusCode = "U"
	NCFNoHitStatusCode    = "M"
	NCFSuccessStatusCode  = "C"
	justBelow1            = 0.9999
)

type NFCreditFeaturesV1 struct {
	DateOfOldestTradeline                                       *time.Time
	DateOfEarliestTradeline                                     *time.Time
	DateOfMostRecentAutoTrade                                   *time.Time
	NumberOfCurrentSatisfactoryTrades                           int
	NumberOfTradesWithDelinquency                               int
	MonthsSinceMostRecentWriteOff                               int
	NumberOfDerogatoryInstallmentTrades                         int
	RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades float64
	RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades  float64
	NumberOfTradeLinesOpenedInLast12Months                      int
	MonthsSinceMostRecentBankRevolvingTradeOpened               int
	NumberOfNonInsuranceInquiries                               int
	AgeOfOldestTradelineInMonths                                int
	AgeOfMostRecentAutoTradeInMonths                            int
	OwnerAgeAtEarliestTradelineInYears                          int
	NoHit                                                       bool
	ThinFile                                                    bool
	IsOwnerAgeAllowed                                           bool
}

func GetCreditScoreFeaturesV1(
	ctx context.Context,
	metricsClient statsd.Statter,
	ownerDOB time.Time,
	nationalCreditFile *data_fetching.NationalCreditFileV1,
) (*NFCreditFeaturesV1, error) {
	if nationalCreditFile == nil {
		return nil, errors.New("national credit file is nil")
	}

	if nationalCreditFile.TransactionDetails == nil {
		return nil, errors.New("transaction details are nil")
	}

	processingStatus := nationalCreditFile.TransactionDetails.ProcessingStatus
	err := metrics.EmitCreditScoreProcessingStatusMetric(ctx, metricsClient, processingStatus)
	if err != nil {
		log.Error(ctx, "failed to emit credit score processing status metric", log.Err(err))
	}

	if processingStatus == NCFNoHitStatusCode {
		return &NFCreditFeaturesV1{NoHit: true}, nil
	}

	if nationalCreditFile.NcfReport == nil {
		return nil, errors.New("ncf report is nil")
	}

	if nationalCreditFile.DateOfCreditReportRun == nil {
		return nil, errors.New("date of credit report run is nil")
	}

	nfProductReport := nationalCreditFile.NcfReport.NcfProductReport
	creditReportSummary := nfProductReport.CreditReportSummary
	oldestOpeningDateOfTrade := creditReportSummary.OldestOpeningDateOfTrade

	filteredCreditTradeHistoryRecords := make([]*data_fetching.CreditTradeHistoryRecordV1, 0)
	for _, creditTradeHistoryRecord := range nfProductReport.TradeAccountInfo.CreditTradeHistoryRecords.CreditTradeHistoryRecord {
		if creditTradeHistoryRecord.DateAccountOpened != nil {
			filteredCreditTradeHistoryRecords = append(filteredCreditTradeHistoryRecords, creditTradeHistoryRecord)
		}
	}
	// Sort the records by DateAccountOpened
	slices.SortFunc(filteredCreditTradeHistoryRecords, func(a, b *data_fetching.CreditTradeHistoryRecordV1) int {
		if getTimeFromDateFirstAtAddress(a.DateAccountOpened).Before(getTimeFromDateFirstAtAddress(b.DateAccountOpened)) {
			return 1
		}
		return -1
	})
	currentStatusAccounts := creditReportSummary.CurrentStatusAccounts.CurrentStatusAccount
	historyStatusAccount := creditReportSummary.HistoryStatusAccounts.HistoryStatusAccount
	inquiryRecords := nfProductReport.InquiryHistoryHeader.InquiryHistoryHeaderRecords.InquiryHistoryHeaderRecord
	// Sort the records by DateOfInquiry
	slices.SortFunc(inquiryRecords, func(a, b *data_fetching.InquiryHistoryHeaderRecordV1) int {
		if getTimeFromDateOfInquiry(a.DateOfInquiry).Before(getTimeFromDateOfInquiry(b.DateOfInquiry)) {
			return 1
		}
		return -1
	})

	creditReportDate := nationalCreditFile.DateOfCreditReportRun.AsTime()

	dateOfOldestTradeline, ageOfOldestTradelineInMonths := getDateAndAgeInMonthsOfOldestTradeline(
		oldestOpeningDateOfTrade,
		creditReportDate,
	)

	dateOfMostRecentAutoTrade, ageOfMostRecentAutoTradeInMonths := getDateAndAgeInMonthsOfMostRecentAutoTrade(
		ctx,
		filteredCreditTradeHistoryRecords,
		creditReportDate,
	)

	dateOfEarliestTradeline, ageOfEarliestTradeline := getDateAndAgeOfEarliestTrade(oldestOpeningDateOfTrade, ownerDOB)

	return &NFCreditFeaturesV1{
		DateOfOldestTradeline:                                       dateOfOldestTradeline,
		DateOfEarliestTradeline:                                     dateOfEarliestTradeline,
		DateOfMostRecentAutoTrade:                                   dateOfMostRecentAutoTrade,
		NumberOfCurrentSatisfactoryTrades:                           getNumberOfCurrentSatisfactoryTrades(currentStatusAccounts),
		NumberOfTradesWithDelinquency:                               getNumberOfTradesWithDelinquency(historyStatusAccount),
		MonthsSinceMostRecentWriteOff:                               getMonthsSinceMostRecentWriteOff(filteredCreditTradeHistoryRecords, creditReportDate),
		NumberOfDerogatoryInstallmentTrades:                         getNumberOfDerogatoryInstallmentTrades(filteredCreditTradeHistoryRecords),
		RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades: getRatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades(creditReportSummary),
		RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades:  getRatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades(filteredCreditTradeHistoryRecords),
		NumberOfTradeLinesOpenedInLast12Months:                      getNumberOfTradeLinesOpenedInLast12Months(filteredCreditTradeHistoryRecords, creditReportDate),
		MonthsSinceMostRecentBankRevolvingTradeOpened:               getMonthsSinceMostRecentBankRevolvingTradeOpened(filteredCreditTradeHistoryRecords, creditReportDate),
		NumberOfNonInsuranceInquiries:                               getNumberOfNonInsuranceInquiries(inquiryRecords),
		AgeOfOldestTradelineInMonths:                                ageOfOldestTradelineInMonths,
		AgeOfMostRecentAutoTradeInMonths:                            ageOfMostRecentAutoTradeInMonths,
		OwnerAgeAtEarliestTradelineInYears:                          ageOfEarliestTradeline,
		NoHit:                                                       processingStatus == NCFNoHitStatusCode,
		ThinFile:                                                    processingStatus == NCFUnscoredStatusCode,
		// This is always true for the states we currently support
		IsOwnerAgeAllowed: true,
	}, nil
}

func getNumberOfNonInsuranceInquiries(inquiryRecords []*data_fetching.InquiryHistoryHeaderRecordV1) int {
	if len(inquiryRecords) == 0 {
		return -1
	}
	uniqueBatches := 0
	var batchStartDate time.Time
	for idx, ir := range inquiryRecords {
		if idx == 0 {
			batchStartDate = getTimeFromDateOfInquiry(ir.DateOfInquiry)
		} else {
			if (batchStartDate.Sub(getTimeFromDateOfInquiry(ir.DateOfInquiry))) > 30*time_utils.Day {
				uniqueBatches++
				batchStartDate = getTimeFromDateOfInquiry(ir.DateOfInquiry)
			}
		}
	}

	return uniqueBatches + 1
}

func getMonthsSinceMostRecentBankRevolvingTradeOpened(
	creditTradeHistoryRecords []*data_fetching.CreditTradeHistoryRecordV1,
	effectiveDate time.Time,
) int {
	var dateToUse *time.Time
	for _, creditTradeHistoryRecord := range creditTradeHistoryRecords {
		if creditTradeHistoryRecord.AccountTypeCode == "R" {
			dateToUse = pointer_utils.ToPointer(getTimeFromDateFirstAtAddress(creditTradeHistoryRecord.DateAccountOpened))
			break
		}
	}
	if dateToUse != nil {
		return int(time_utils.Months(effectiveDate.Sub(*dateToUse)))
	}
	return -1
}

func getNumberOfTradeLinesOpenedInLast12Months(
	creditTradeHistoryRecords []*data_fetching.CreditTradeHistoryRecordV1,
	effectiveDate time.Time,
) int {
	if len(creditTradeHistoryRecords) == 0 {
		return -1
	}
	numberOfTradeLinesOpenedInLast12Months := 0
	for _, creditTradeHistoryRecord := range creditTradeHistoryRecords {
		if effectiveDate.Sub(
			getTimeFromDateFirstAtAddress(creditTradeHistoryRecord.DateAccountOpened)) < time_utils.Year {
			numberOfTradeLinesOpenedInLast12Months++
		}
	}
	return numberOfTradeLinesOpenedInLast12Months
}

func getRatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades(
	creditReportSummary *data_fetching.CreditReportSummaryV1,
) float64 {
	if creditReportSummary.HighCreditTotalRevolving != 0 {
		revRatio := float64(creditReportSummary.AmountOwedTotalRevolving) / float64(creditReportSummary.HighCreditTotalRevolving)
		// This is a temporary fix to handle the case when the ratio is >= 1 since RateML does not accept
		// ratios >= 1.
		if revRatio >= 1 {
			revRatio = justBelow1
		}
		return revRatio
	}
	return -1
}

func getRatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades(creditTradeHistoryRecords []*data_fetching.CreditTradeHistoryRecordV1) float64 {
	totalCreditAmount := 0
	totalOwedAmount := 0
	for _, creditTradeHistoryRecord := range creditTradeHistoryRecords {
		messages := creditTradeHistoryRecord.GetMessages()
		if messages == nil {
			continue
		}
		for _, m := range messages.Message {
			if !slice_utils.Contains(ClosedAccountCodes, m.Code) {
				totalCreditAmount += int(creditTradeHistoryRecord.HighestCreditAmount)
				totalOwedAmount += int(creditTradeHistoryRecord.AccountBalance)
			}
		}
	}
	if totalCreditAmount != 0 {
		ratio := float64(totalOwedAmount) / float64(totalCreditAmount)
		// This is a temporary fix to handle the case when the ratio is >= 1 since RateML does not accept
		// ratios >= 1.
		if ratio >= 1 {
			ratio = justBelow1
		}
		return ratio
	}
	return -1
}

func getNumberOfDerogatoryInstallmentTrades(creditTradeHistoryRecords []*data_fetching.CreditTradeHistoryRecordV1) int {
	if len(creditTradeHistoryRecords) == 0 {
		return -1
	}
	var numberOfDerogatoryInstallmentTrades int
	for _, creditTradeHistoryRecord := range creditTradeHistoryRecords {
		if slices.Contains([]string{"I9"},
			getTradelineStatus(creditTradeHistoryRecord.AccountTypeCode, creditTradeHistoryRecord.CurrentRateCode)) {
			numberOfDerogatoryInstallmentTrades++
		}
	}
	return numberOfDerogatoryInstallmentTrades
}

func getMonthsSinceMostRecentWriteOff(
	creditTradeHistoryRecords []*data_fetching.CreditTradeHistoryRecordV1,
	effectiveDate time.Time,
) int {
	var dateToUse *time.Time
	for _, creditTradeHistoryRecord := range creditTradeHistoryRecords {
		if slices.Contains([]string{"R9", "I9", "O9"},
			getTradelineStatus(creditTradeHistoryRecord.AccountTypeCode, creditTradeHistoryRecord.CurrentRateCode)) {
			dateToUse = pointer_utils.ToPointer(getTimeFromDateFirstAtAddress(creditTradeHistoryRecord.DateAccountOpened))
			break
		}
	}
	if dateToUse != nil {
		return int(time_utils.Months(effectiveDate.Sub(*dateToUse)))
	}
	return -1
}

func getNumberOfCurrentSatisfactoryTrades(currentStatusAccounts []*data_fetching.CurrentStatusAccountV1) int {
	numberOfCurrentSatisfactoryTrades := -1
	for _, currentStatusAccount := range currentStatusAccounts {
		if currentStatusAccount.Status == "1" {
			numberOfCurrentSatisfactoryTrades = int(currentStatusAccount.NumberOfAccounts)
			break
		}
	}
	return numberOfCurrentSatisfactoryTrades
}

func getNumberOfTradesWithDelinquency(historyStatusAccounts []*data_fetching.CurrentStatusAccountV1) int {
	if len(historyStatusAccounts) == 0 {
		return -1
	}
	numberOfTradesWithDelinquency := 0
	for _, historyStatusAccount := range historyStatusAccounts {
		numberOfTradesWithDelinquency += int(historyStatusAccount.NumberOfAccounts)
	}
	return numberOfTradesWithDelinquency
}

func getDateAndAgeInMonthsOfMostRecentAutoTrade(
	ctx context.Context,
	creditTradeHistoryRecords []*data_fetching.CreditTradeHistoryRecordV1,
	effectiveDate time.Time,
) (*time.Time, int) {
	// Filter out the records that are Auto
	var creditTradeHistoryRecordToUse *data_fetching.CreditTradeHistoryRecordV1
	var dateToUse *time_utils.Date
	for _, creditTradeHistoryRecord := range creditTradeHistoryRecords {
		if creditTradeHistoryRecord.Messages == nil {
			continue
		}
		shouldUseCreditTrade := false
		for _, m := range creditTradeHistoryRecord.Messages.Message {
			if m.Code == "AO" || strings.Contains(m.Message, "AUTO") {
				shouldUseCreditTrade = true
				break
			}
		}

		if !shouldUseCreditTrade {
			continue
		}
		currentOpenedDate := time_utils.NewDate(
			int(creditTradeHistoryRecord.DateAccountOpened.Year),
			time.Month(creditTradeHistoryRecord.DateAccountOpened.Month), 1)
		if dateToUse == nil || currentOpenedDate.After(*dateToUse) {
			dateToUse = &currentOpenedDate
			creditTradeHistoryRecordToUse = creditTradeHistoryRecord
		}
	}

	if creditTradeHistoryRecordToUse == nil {
		log.Info(ctx, "no auto trade records found")
	} else {
		date := getTimeFromDateFirstAtAddress(creditTradeHistoryRecordToUse.DateAccountOpened)
		return pointer_utils.ToPointer(date),
			int(time_utils.Months(effectiveDate.Sub(date)))
	}
	return nil, -1
}

func getDateAndAgeOfEarliestTrade(
	oldestOpeningDateOfTrade *data_fetching.DateFirstAtAddressV1,
	ownerDOB time.Time,
) (*time.Time, int) {
	if oldestOpeningDateOfTrade == nil {
		return nil, -1
	}
	dateOfEarliestTradeline := getTimeFromDateFirstAtAddress(oldestOpeningDateOfTrade)
	return &dateOfEarliestTradeline, int(time_utils.Years(dateOfEarliestTradeline.Sub(ownerDOB)))
}

func getDateAndAgeInMonthsOfOldestTradeline(
	oldestOpeningDateOfTrade *data_fetching.DateFirstAtAddressV1,
	effectiveDate time.Time,
) (*time.Time, int) {
	if oldestOpeningDateOfTrade == nil {
		return nil, -1
	}
	dateOfOldestTradeline := getTimeFromDateFirstAtAddress(oldestOpeningDateOfTrade)
	return &dateOfOldestTradeline, int(time_utils.Months(effectiveDate.Sub(dateOfOldestTradeline)))
}

func getTimeFromDateFirstAtAddress(oldestOpeningDateOfTrade *data_fetching.DateFirstAtAddressV1) time.Time {
	return time.Date(
		int(oldestOpeningDateOfTrade.Year),
		time.Month(oldestOpeningDateOfTrade.Month),
		1,
		0,
		0,
		0,
		0,
		time.UTC,
	)
}

func getTimeFromDateOfInquiry(doi *data_fetching.DateOfInquiryV1) time.Time {
	return time.Date(
		int(doi.Year),
		time.Month(doi.Month),
		int(doi.Day),
		0,
		0,
		0,
		0,
		time.UTC,
	)
}

func getTradelineStatus(accountTypeCode, currentRateCode string) string {
	return accountTypeCode + currentRateCode
}
