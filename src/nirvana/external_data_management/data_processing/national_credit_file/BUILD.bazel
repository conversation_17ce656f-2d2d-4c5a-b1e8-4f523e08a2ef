load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "national_credit_file",
    srcs = ["nf_credit_features.go"],
    importpath = "nirvanatech.com/nirvana/external_data_management/data_processing/national_credit_file",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing/metrics",
        "@com_github_cactus_go_statsd_client_v5//statsd",
    ],
)

go_test(
    name = "national_credit_file_test",
    srcs = ["nf_credit_features_test.go"],
    embed = [":national_credit_file"],
    embedsrcs = ["test_data/report.json"],
    deps = [
        "//nirvana/common-go/metrics",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/external_data_management/data_fetching",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
