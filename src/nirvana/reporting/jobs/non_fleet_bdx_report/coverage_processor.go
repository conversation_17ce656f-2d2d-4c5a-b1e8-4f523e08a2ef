package non_fleet_bdx_report

import (
	"context"
	"slices"
	"strconv"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	"nirvanatech.com/nirvana/common-go/type_utils"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	nfapp "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	ibmodel "nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-core/coverage"
	"nirvanatech.com/nirvana/policy/non_fleet"
)

func processCoverage(
	ctx context.Context,
	nfAppWrapper nfapp.Wrapper[*admitted.AdmittedApp],
	processor endorsement.CoverageProcessor,
	coverageCriteria *ibmodel.CoverageCriteria,
	subCoverageID, terminalState, puCount, tiv string,
	preDistribution, postDistribution *ibmodel.ChargeDistribution,
	isEndorsement bool,
	appReview application_review.ApplicationReview,
	indicationOption nfapp.IndicationOption,
	application *nfapp.Application[*admitted.AdmittedApp],
	primaryPolicyNumber string,
) (*subCoverageDetails, error) {
	details := subCoverageDetails{
		EachOccurenceCoverageLimit: "1000000",
		CombinedUnitTractorTrailer: "250000",
	}

	deductible := processor.GetDeductibleForSubCoverage(subCoverageID, coverageCriteria)
	limits := processor.GetLimitsForSubCoverage(subCoverageID, coverageCriteria)

	limit := ""
	if len(limits) > 0 {
		limit = limits[0]
	}

	deductible = cleanString(deductible)
	limit = cleanString(limit)

	surcharge := postDistribution.Surcharges
	feeCharge := postDistribution.FeeCharges
	if isEndorsement {
		surcharge = surcharge.Sub(preDistribution.Surcharges)
		feeCharge = feeCharge.Sub(preDistribution.FeeCharges)
	}

	coverageDiffs := make(map[string]decimal.Decimal)

	// Process pre-distribution charges
	preCharges := make(map[appenums.Coverage]decimal.Decimal)
	if isEndorsement {
		for subCoverage, charge := range preDistribution.ChargesBySubCoverage {
			appCoverage, err := coverage.GetAppCoverageFromPricingSubCoverage(subCoverage)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to get app coverage enum for sub coverage %s", subCoverage)
			}
			preCharges[*appCoverage] = charge
		}
	}

	// Calculate diffs using post-distribution charges
	for subCoverage, postCharge := range postDistribution.ChargesBySubCoverage {
		appCoverage, err := coverage.GetAppCoverageFromPricingSubCoverage(subCoverage)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get app coverage enum for sub coverage %s", subCoverage)
		}

		if isEndorsement {
			preCharge := preCharges[*appCoverage]
			coverageDiffs[appCoverage.String()] = postCharge.Sub(preCharge)
		} else {
			coverageDiffs[appCoverage.String()] = postCharge
		}
	}

	gwp := coverageDiffs[subCoverageID]

	var alSchMod, apdSchMod, mtcSchMod, glSchMod float64
	if appReview != nil {
		alSchMod = 1 + float64(type_utils.GetValueOrDefault(appReview.GetOverrides().ALPercent, 0))/100.0
		mtcSchMod = 1 + float64(type_utils.GetValueOrDefault(appReview.GetOverrides().MTCPercent, 0))/100.0
		glSchMod = 1 + float64(type_utils.GetValueOrDefault(appReview.GetOverrides().GLPercent, 0))/100.0
		apdSchMod = 1 + float64(type_utils.GetValueOrDefault(appReview.GetOverrides().APDPercent, 0))/100.0
	}

	isAPDMTCDeductibleCombined := false
	for _, c := range coverageCriteria.CombinedDeductibles {
		if c != nil && slices.Equal(c.SubCoverageIds,
			[]string{
				appenums.CoverageCollision.String(),
				appenums.CoverageComprehensive.String(),
				appenums.CoverageMotorTruckCargo.String(),
			},
		) {
			isAPDMTCDeductibleCombined = true
		}
	}

	if application.IsRenewal() {
		// Parse the policy number to extract the coverage type
		policyNumberImpl, err := policy.GetPolicyNumberImplFromString(primaryPolicyNumber)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse policy number %s", primaryPolicyNumber)
		}

		primaryCoverages := policyNumberImpl.PrimaryCoverages()
		if len(primaryCoverages) == 0 {
			return nil, errors.Newf("no primary coverages found for policy number %s", primaryPolicyNumber)
		}

		primaryCov := primaryCoverages[0]

		originalApplicationId, err := uuid.Parse(application.RenewalMetadata.OriginalApplicationId)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse original application ID %s",
				application.RenewalMetadata.OriginalApplicationId)
		}

		originalApp, err := nfAppWrapper.GetAppById(ctx, originalApplicationId)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get original application by ID %s", originalApplicationId)
		}

		newPolicyNumber, err := non_fleet.GeneratePolicyNumber(
			ctx,
			nfAppWrapper,
			*application,
			primaryCov,
			enums.ProgramTypeNonFleetAdmitted,
			originalApp.EffectiveDate,
		)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to generate policy number for application ID %s", originalApplicationId)
		}

		// Set the previous policy number to the generated policy number
		details.PreviousPolicyNumber = newPolicyNumber.String()
	}

	switch subCoverageID {
	case appenums.CoverageBodilyInjury.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.Deductible = deductible
		details.DeductibleType = "Dollar amount"
		details.BIPDCombinedSingleLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)
		if !isEndorsement {
			if indicationOption != nil {
				if indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusLinesTax != nil {
					surplusTax := indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusLinesTax
					details.SLTax = strconv.FormatFloat(*surplusTax, 'g', -1, 64)
				}

				if indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusStampingFee != nil {
					stampingFee := indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusStampingFee
					details.StampingFee = strconv.FormatFloat(*stampingFee, 'g', -1, 64)
				}
			}
		}
		details.Surcharge = surcharge.String()
		details.SurchargeType = getSurchargeType(terminalState)
		details.FeeCharge = feeCharge.String()

	case appenums.CoveragePropertyDamage.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.Deductible = deductible
		details.DeductibleType = "Dollar amount"
		details.BIPDCombinedSingleLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageCollision.String(), appenums.CoverageComprehensive.String():
		details.Exposure = tiv
		details.ExposureBasis = "TIV"
		details.Deductible = deductible
		details.DeductibleType = "Dollar amount"
		details.EachOccurenceCoverageLimit = "1000000"
		details.CombinedUnitTractorTrailer = "250000"
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(apdSchMod, 'g', -1, 64)
		if isAPDMTCDeductibleCombined {
			details.CombinedDeductible = "MotorTruckCargo"
		}

	case appenums.CoverageRentalReimbursement.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.RentalReimbursementLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(apdSchMod, 'g', -1, 64)

	case appenums.CoverageTowingLaborAndStorage.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.EachOccurenceCoverageLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(apdSchMod, 'g', -1, 64)

	case appenums.CoverageTrailerInterchange.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.Deductible = deductible
		details.DeductibleType = "Dollar amount"
		details.EachOccurenceCoverageLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.TrailerInterchangeLimit = limit
		details.ScheduleMod = strconv.FormatFloat(apdSchMod, 'g', -1, 64)

	case appenums.CoverageUM.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.UMLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageUIM.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.UIMLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageUMUIM.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.UMUIMLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageUninsuredMotoristPropertyDamage.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.Deductible = deductible
		details.DeductibleType = "Dollar amount"
		details.UMPDLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageUninsuredMotoristBodilyInjury.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.UMBILimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageUnderinsuredMotoristPropertyDamage.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.UIMPDLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageUnderinsuredMotoristBodilyInjury.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.UIMBILimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoveragePersonalInjuryProtection.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.PIPLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoveragePIPExcessAttendantCare.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.PIPAttendantLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoveragePIPWorkLossAndRPLService.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.PIPWorkLossAndReplacementLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoveragePropertyProtectionInsurance.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.PPILimit = "1000000"
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageMedicalPayments.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.MedpayLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageMotorTruckCargo.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.Deductible = deductible
		details.DeductibleType = "Dollar amount"
		details.EachOccurenceCoverageLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(mtcSchMod, 'g', -1, 64)
		if !isEndorsement {
			if indicationOption != nil {
				if indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusLinesTax != nil {
					surplusTax := indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusLinesTax
					details.SLTax = strconv.FormatFloat(*surplusTax, 'g', -1, 64)
				}

				if indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusStampingFee != nil {
					stampingFee := indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusStampingFee
					details.StampingFee = strconv.FormatFloat(*stampingFee, 'g', -1, 64)
				}
			}
		}
		if isAPDMTCDeductibleCombined {
			details.CombinedDeductible = "AutoPhysicalDamage"
		}

	case appenums.CoverageReefer.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.Deductible = deductible
		details.DeductibleType = "Dollar amount"
		details.EachOccurenceCoverageLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(mtcSchMod, 'g', -1, 64)

	case appenums.CoverageReeferWithHumanError.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.Deductible = deductible
		details.DeductibleType = "Dollar amount"
		details.EachOccurenceCoverageLimit = limit
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(mtcSchMod, 'g', -1, 64)

	case appenums.CoverageGeneralLiability.String():
		details.ExposureBasis = "Per Limit"
		details.Deductible = "0"
		details.DeductibleType = "Dollar amount"
		details.EachOccurenceCoverageLimit = "1000000"
		details.GeneralAggregateLimit = "2000000"
		details.GrossWrittenPremium = gwp.String()
		details.ScheduleMod = strconv.FormatFloat(glSchMod, 'g', -1, 64)
		if !isEndorsement {
			if indicationOption != nil {
				if indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusLinesTax != nil {
					surplusTax := indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusLinesTax
					details.SLTax = strconv.FormatFloat(*surplusTax, 'g', -1, 64)
				}

				if indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusStampingFee != nil {
					stampingFee := indicationOption.GetSurplusTaxInfo().LiabAPDPlusFlatSurplusStampingFee
					details.StampingFee = strconv.FormatFloat(*stampingFee, 'g', -1, 64)
				}
			}
		}

	case appenums.CoverageNonOwnedTrailer.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.DeductibleType = "Dollar amount"
		details.GrossWrittenPremium = gwp.String()
		details.NOTLimit = limit
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageHiredAuto.String():
		details.Exposure = puCount
		details.ExposureBasis = "No Vehicles"
		details.DeductibleType = "Dollar amount"
		details.GrossWrittenPremium = gwp.String()
		details.HiredAutoLimit = limit
		details.ScheduleMod = strconv.FormatFloat(alSchMod, 'g', -1, 64)

	case appenums.CoverageBlanketAdditional.String():
		// This is a hack since in charge distribution we have the combined value
		// and do not have any breakdown.
		details.GrossWrittenPremium = feeCharge.Div(decimal.NewFromInt(2)).String()

	case appenums.CoverageBlanketWaiverOfSubrogation.String():
		// This is a hack since in charge distribution we have the combined value
		// and do not have any breakdown.
		details.GrossWrittenPremium = feeCharge.Div(decimal.NewFromInt(2)).String()
	}

	return &details, nil
}
