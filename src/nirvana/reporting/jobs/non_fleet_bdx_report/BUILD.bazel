load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "non_fleet_bdx_report",
    srcs = [
        "constants.go",
        "coverage_processor.go",
        "headers.go",
        "job.go",
        "lob_codes.go",
        "report_generator.go",
        "types.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/reporting/jobs/non_fleet_bdx_report",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/handlers/common/application",
        "//nirvana/api-server/handlers/common/endorsement",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/type_utils",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/coverage",
        "//nirvana/insurance-core/proto",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/nonfleet/model",
        "//nirvana/policy/constants",
        "//nirvana/policy/non_fleet",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/reporting/enums",
        "//nirvana/reporting/jobs/common",
        "//nirvana/reporting/jobs/deps",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
    ],
)
