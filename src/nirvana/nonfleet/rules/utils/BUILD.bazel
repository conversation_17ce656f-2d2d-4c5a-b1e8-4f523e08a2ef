load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "utils",
    srcs = [
        "driver_utils.go",
        "equipment_utils.go",
        "fmcsa_utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/rules/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/rule_runs",
        "//nirvana/fmcsa/models",
        "//nirvana/nonfleet/rule_engine/registry",
        "//nirvana/nonfleet/rules/common",
    ],
)
