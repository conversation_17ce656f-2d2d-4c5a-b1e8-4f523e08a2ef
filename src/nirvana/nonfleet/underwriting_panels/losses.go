package underwriting_panels

import (
	"context"

	file_upload_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

type LossesPanel[T application.AppInfo] struct {
	deps Deps[T]
}

func NewLossesPanel[T application.AppInfo](deps Deps[T]) *LossesPanel[T] {
	return &LossesPanel[T]{deps: deps}
}

func (l *LossesPanel[T]) SetIsReviewed(ctx context.Context, appReviewID string, isReviewed bool) error {
	if err := l.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		appReviewID,
		func(review nf_app_review.ApplicationReview) (nf_app_review.ApplicationReview, error) {
			err := review.SetPanelReview(nf_app_review.LossesPanelType, isReviewed)
			if err != nil {
				return review, errors.Wrapf(err, "failed to set panel review")
			}
			return review, nil
		},
	); err != nil {
		return errors.Wrapf(err, "unable to update application review %v", appReviewID)
	}
	return nil
}

func (l *LossesPanel[T]) IsReviewed(input *PanelInput[T]) bool {
	return input.applicationReview.IsPanelReviewed(nf_app_review.LossesPanelType)
}

func (*LossesPanel[T]) PanelKey() enums.FlagPanel {
	return enums.Losses
}

func (l *LossesPanel[T]) LossRuns(
	input *PanelInput[T],
) []oapi_common.FileMetadata {
	var lrfs []oapi_common.FileMetadata
	for _, lrf := range input.application.Info.GetLossRunFiles() {
		lrfs = append(lrfs, oapi_common.FileMetadata{
			Handle: pointer_utils.UUIDStringOrNil(lrf.Handle),
			Name:   lrf.Name,
		})
	}
	for _, lrf := range input.applicationReview.GetReviewDocuments().Files {
		if lrf.FileType != nil && *lrf.FileType == file_upload_enums.FileTypeLossRun {
			lrfs = append(lrfs, oapi_common.FileMetadata{
				Handle: pointer_utils.UUIDStringOrNil(lrf.Handle),
				Name:   lrf.Name,
			})
		}
	}
	return lrfs
}

func (l *LossesPanel[T]) GetLossInfo(
	input *PanelInput[T],
) *oapi_uw.ApplicationReviewLosses {
	retval := input.application.Info.GetLossPanelInfo()
	retval.IsReviewed = l.IsReviewed(input)
	retval.LossRunFiles = pointer_utils.ToPointer(l.LossRuns(input))

	return &retval
}
