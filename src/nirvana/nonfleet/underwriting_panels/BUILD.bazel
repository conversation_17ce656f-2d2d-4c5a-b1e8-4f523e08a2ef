load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "underwriting_panels",
    srcs = [
        "admitted_panels_manager.go",
        "base_panel.go",
        "deps.go",
        "driver.go",
        "equipments.go",
        "fx.go",
        "interface.go",
        "losses.go",
        "operations.go",
        "packages.go",
        "safety.go",
        "summary.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/underwriting_panels",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/handlers/utils",
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/application/experiments/non_fleet",
        "//nirvana/application/experiments/non_fleet/pre_telematics_quote",
        "//nirvana/common-go/application-util",
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/insurance_carriers_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/type_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/common",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/utils",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/experiments/models",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/interceptors_management",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/external_data_management/mvr",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/application",
        "//nirvana/nonfleet/calculators",
        "//nirvana/nonfleet/driver",
        "//nirvana/nonfleet/model",
        "//nirvana/nonfleet/rule_engine",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/nonfleet/underwriting_panels/enums",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/rating/data_fetching/lni_fetching",
        "//nirvana/rating/data_processing/lni_processing",
        "//nirvana/rating/mvr",
        "//nirvana/servers/quote_scraper/enums",
        "//nirvana/underwriting/app_review/utils",
        "//nirvana/underwriting/app_review/widgets/global",
        "//nirvana/underwriting/app_review/widgets/safety/scorev2",
        "//nirvana/underwriting/common",
        "//nirvana/underwriting/scheduler",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "underwriting_panels_test",
    srcs = ["driver_test.go"],
    embed = [":underwriting_panels"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/infra/fx/testloader",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
        "@tools_gotest//assert",
    ],
)
