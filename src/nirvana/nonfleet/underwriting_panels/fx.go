package underwriting_panels

import (
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
)

var _ = fxregistry.Register(
	fx.Provide(
		NewEquipmentsPanel[*admitted_app.AdmittedApp],
		NewDriversPanel[*admitted_app.AdmittedApp],
		NewOperationsPanel[*admitted_app.AdmittedApp],
		NewPackagesPanel[*admitted_app.AdmittedApp],
		NewBasePanel[*admitted_app.AdmittedApp],
		NewLossesPanel[*admitted_app.AdmittedApp],
		NewSummaryPanel[*admitted_app.AdmittedApp],
		NewSafetyPanel[*admitted_app.AdmittedApp],
	),
)
