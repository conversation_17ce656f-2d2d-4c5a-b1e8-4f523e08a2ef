load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_test(
    name = "tests_test",
    srcs = [
        "decline_test.go",
        "general_flow_test.go",
        "tsp_test.go",
    ],
    embed = [":tests"],
    deps = [
        "//nirvana/api-server/handlers/nonfleet",
        "//nirvana/api-server/quoting_jobber/jobs",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/random_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/sharing",
        "//nirvana/infra/fx/testfixtures/admitted_application_fixture",
        "//nirvana/infra/fx/testfixtures/api_server_fixture",
        "//nirvana/infra/fx/testfixtures/fixture_utils",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/nonfleet/rules/admitted/fmcsa_rules",
        "//nirvana/openapi-specs/components/application",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/sharing",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)

go_library(
    name = "tests",
    srcs = [
        "admitted_utils.go",
        "common_utils.go",
        "consts.go",
        "nrb_utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/tests",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/handlers/nonfleet",
        "//nirvana/api-server/handlers/nonfleet_underwriting",
        "//nirvana/api-server/interceptors/nonfleet_application/deps",
        "//nirvana/api-server/interceptors/nonfleet_underwriting/deps",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/random_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/agency_bd_mapping",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testfixtures/agency_fixture",
        "//nirvana/infra/fx/testfixtures/basic_fixture",
        "//nirvana/infra/fx/testfixtures/datagov_fixture",
        "//nirvana/infra/fx/testfixtures/fmcsa_fixture",
        "//nirvana/infra/fx/testfixtures/lni_fixture",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/nonfleet/coverages/admitted",
        "//nirvana/nonfleet/pdf-jobs",
        "//nirvana/nonfleet/rating-jobs",
        "//nirvana/nonfleet/rules/admitted",
        "//nirvana/openapi-specs/components/application",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/quoting/jobs/impl",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
