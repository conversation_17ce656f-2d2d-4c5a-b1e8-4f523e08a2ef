package tests

import (
	"context"
	"net/http"
	"sort"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	nonfleet_handlers "nirvanatech.com/nirvana/api-server/handlers/nonfleet"
	"nirvanatech.com/nirvana/api-server/handlers/nonfleet_underwriting"
	nf_app_deps "nirvanatech.com/nirvana/api-server/interceptors/nonfleet_application/deps"
	nf_uw_deps "nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/agency_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/basic_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/datagov_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/lni_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	_ "nirvanatech.com/nirvana/nonfleet/pdf-jobs"
	_ "nirvanatech.com/nirvana/nonfleet/rating-jobs"
	"nirvanatech.com/nirvana/nonfleet/rules/admitted"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	_ "nirvanatech.com/nirvana/quoting/jobs/impl"
)

type TestDeps struct {
	deps          nf_app_deps.Deps
	uwDeps        nf_uw_deps.Deps
	deferFunc     func()
	agencyID      uuid.UUID
	agencyAdminID uuid.UUID
	superUserID   uuid.UUID
	canopiusUWID  uuid.UUID
}

func SetupTest(t *testing.T) TestDeps {
	var env struct {
		fx.In

		AgencyFixture *agency_fixture.AgencyFixture
		UserFixture   *users_fixture.UsersFixture
		*fmcsa_fixture.FmcsaFixture
		*basic_fixture.BasicFixture
		*lni_fixture.InsuranceHistoryFixture
		*lni_fixture.ActiveOrPendingInsuranceFixture
		*lni_fixture.AuthorityHistoryFixture
		Deps   nf_app_deps.Deps
		UWDeps nf_uw_deps.Deps
		*datagov_fixture.DataGovFixture
	}

	deferFunc := testloader.RequireStart(t, &env, testloader.Use(
		admitted.Rules,
	)).RequireStop

	return TestDeps{
		deps:          env.Deps,
		uwDeps:        env.UWDeps,
		deferFunc:     deferFunc,
		agencyID:      env.AgencyFixture.Agency.ID,
		agencyAdminID: env.UserFixture.AgencyAdmin.ID,
		superUserID:   env.UserFixture.Superuser.ID,
		canopiusUWID:  env.UserFixture.CanopiusUW.ID,
	}
}

//nolint:unused
func getTestAppReview(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	superUser uuid.UUID,
) (oapi_uw.ApplicationReviewDetails, error) {
	user, err := uwDeps.AuthWrapper.FetchAuthzUser(ctx, superUser)
	a.Nil(err)
	nCtx := authz.WithUser(ctx, *user)
	getARsResp := nonfleet_underwriting.HandleGetApplicationReviews(
		nCtx,
		uwDeps,
		nonfleet_underwriting.GetApplicationReviewsRequest{},
	)
	a.Nil(getARsResp.Error)
	a.Nil(getARsResp.ServerError)
	a.Equal(200, getARsResp.StatusCode())
	a.NotNil(getARsResp.Success)
	a.Equal(1, len(*getARsResp.Success))
	a.Equal(oapi_uw.UnderUWReview, (*getARsResp.Success)[0].Status)
	return (*getARsResp.Success)[0], nil
}

//nolint:unused
func getAppReviewPackages(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) (oapi_uw.ApplicationReviewPackages, error) {
	packagesResp := nonfleet_underwriting.HandleGetApplicationReviewPackages(
		ctx,
		uwDeps,
		nonfleet_underwriting.GetApplicationReviewPackagesRequest{AppReviewID: appReviewID},
	)
	a.Nil(packagesResp.Error)
	a.Nil(packagesResp.ServerError)
	a.Equal(200, packagesResp.StatusCode())
	a.NotNil(packagesResp.Success)
	return *packagesResp.Success, nil
}

// nolint:unused
type AppInput struct {
	dot                                                    *int
	restrictedClasses, restrictedCommodities               *bool
	recentBankruptcy, hasLossOver20k, hasSeriousViolations *bool
	vehicleDetails                                         *[]nonfleet.VehicleDetails
	vehYear                                                *int
	driverDOB                                              *time.Time
	commsDistribution                                      *[]nonfleet.CommodityRecord
	state                                                  *string
}

// nolint:unused
func submitUWQuote(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) error {
	quoteResp := nonfleet_underwriting.HandlePostUWQuoteSubmit(
		ctx,
		uwDeps,
		nonfleet_underwriting.PostUWQuoteSubmitRequest{AppReviewID: appReviewID},
	)
	a.Nil(quoteResp.Error)
	a.Nil(quoteResp.ServerError)
	a.Equal(200, quoteResp.StatusCode())
	a.NotNil(quoteResp.Success)
	return nil
}

// nolint:unused
func getAppReviewLosses(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) (*oapi_uw.ApplicationReviewLosses, error) {
	lossesResp := nonfleet_underwriting.HandleGetApplicationReviewLosses(
		ctx,
		uwDeps,
		nonfleet_underwriting.GetApplicationReviewLossesRequest{AppReviewID: appReviewID},
	)
	a.Nil(lossesResp.Error)
	a.Nil(lossesResp.ServerError)
	a.Equal(200, lossesResp.StatusCode())
	a.NotNil(lossesResp.Success)
	return lossesResp.Success, nil
}

// nolint:unused
func getAppReviewEquipments(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) (*oapi_uw.ApplicationReviewEquipment, error) {
	equipmentsResp := nonfleet_underwriting.HandleGetApplicationReviewEquipments(
		ctx,
		uwDeps,
		nonfleet_underwriting.GetApplicationReviewEquipmentsRequest{AppReviewID: appReviewID},
	)
	a.Nil(equipmentsResp.Error)
	a.Nil(equipmentsResp.ServerError)
	a.Equal(200, equipmentsResp.StatusCode())
	a.NotNil(equipmentsResp.Success)
	return equipmentsResp.Success, nil
}

// nolint:unused
func getAppReviewSafety(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) (*oapi_uw.ApplicationReviewSafety, error) {
	safetyResp := nonfleet_underwriting.HandleGetApplicationReviewSafety(
		ctx,
		uwDeps,
		nonfleet_underwriting.GetApplicationReviewSafetyRequest{AppReviewID: appReviewID},
	)
	a.Nil(safetyResp.Error)
	a.Nil(safetyResp.ServerError)
	a.Equal(200, safetyResp.StatusCode())
	a.NotNil(safetyResp.Success)
	return safetyResp.Success, nil
}

// nolint:unused
func getAppReviewSummary(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) (*oapi_uw.ApplicationReviewSummary, error) {
	summaryResp := nonfleet_underwriting.HandleGetApplicationReviewSummary(
		ctx,
		uwDeps,
		nonfleet_underwriting.GetApplicationReviewSummaryRequest{AppReviewID: appReviewID},
	)
	a.Nil(summaryResp.Error)
	a.Nil(summaryResp.ServerError)
	a.Equal(200, summaryResp.StatusCode())
	a.NotNil(summaryResp.Success)
	return summaryResp.Success, nil
}

// nolint:unused
func updateAppReviewSafety(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
	isReviewed bool,
	safetyCredits *int,
) (nonfleet_underwriting.UpdateApplicationReviewSafetyResponse, error) {
	updateResp := nonfleet_underwriting.HandleUpdateApplicationReviewSafety(
		ctx,
		uwDeps,
		nonfleet_underwriting.UpdateApplicationReviewSafetyRequest{
			AppReviewID: appReviewID,
			Form: &oapi_uw.UpdateApplicationReviewSafetyForm{
				IsReviewed:    isReviewed,
				SafetyCredits: safetyCredits,
			},
		},
	)
	a.Nil(updateResp.Error)
	a.Nil(updateResp.ServerError)
	a.Equal(200, updateResp.StatusCode())
	return updateResp, nil
}

// nolint:unused
func getAppReviewDrivers(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) (oapi_uw.ApplicationReviewDriver, error) {
	safetyResp, err := nonfleet_underwriting.GetDrivers(
		ctx,
		uwDeps.AdmittedBasePanel,
		uwDeps.AdmittedDriversPanel,
		nonfleet_underwriting.GetApplicationReviewDriversRequest{AppReviewID: appReviewID},
		uwDeps.FetcherClientFactory,
	)
	a.Nil(err)
	a.NotNil(safetyResp)
	return *safetyResp, nil
}

// nolint:unused
func getAppReviewOperations(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) (nonfleet_underwriting.GetApplicationReviewOperationsResponse, error) {
	operationsResp := nonfleet_underwriting.HandleGetApplicationReviewOperations(
		ctx,
		uwDeps,
		nonfleet_underwriting.GetApplicationReviewOperationsRequest{AppReviewID: appReviewID},
	)
	a.Nil(operationsResp.Error)
	a.Nil(operationsResp.ServerError)
	a.Equal(200, operationsResp.StatusCode())
	a.NotNil(operationsResp.Success)
	return operationsResp, nil
}

// nolint:unused
func updateAppReviewOperations(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
	isReviewed bool,
	bizOwnerScore *oapi_uw.BusinessOwnerCreditScore,
	usDotScore *oapi_uw.USDotScore,
) (nonfleet_underwriting.UpdateApplicationReviewOperationsResponse, error) {
	updateResp := nonfleet_underwriting.HandleUpdateApplicationReviewOperations(
		ctx,
		uwDeps,
		nonfleet_underwriting.UpdateApplicationReviewOperationsRequest{
			AppReviewID: appReviewID,
			Form: &oapi_uw.UpdateApplicationReviewOperationForm{
				IsReviewed:          isReviewed,
				BizOwnerCreditScore: bizOwnerScore,
				UsDotScore:          usDotScore,
			},
		},
	)
	a.Nil(updateResp.Error)
	a.Nil(updateResp.ServerError)
	a.Equal(200, updateResp.StatusCode())
	return updateResp, nil
}

// nolint:unused
func getBindableQuote(
	ctx context.Context,
	a *require.Assertions,
	deps nf_app_deps.Deps,
	appID uuid.UUID,
) (nonfleet_handlers.GetBindableQuoteResponse, error) {
	getQuoteOptionResp := nonfleet_handlers.HandleGetBindableQuote(
		ctx,
		deps,
		nonfleet_handlers.GetBindableQuoteRequest{ApplicationID: appID},
	)
	a.Nil(getQuoteOptionResp.Error)
	a.Nil(getQuoteOptionResp.ServerError)
	a.Equal(http.StatusOK, getQuoteOptionResp.StatusCode())
	return getQuoteOptionResp, nil
}

// nolint:unused
func setAppReviewMVRPull(
	ctx context.Context,
	a *require.Assertions,
	uwDeps nf_uw_deps.Deps,
	appReviewID string,
) (nonfleet_underwriting.SetApplicationReviewMVRPullResponse, error) {
	updateResp := nonfleet_underwriting.HandleSetApplicationReviewMVRPull(
		ctx,
		uwDeps,
		nonfleet_underwriting.SetApplicationReviewMVRPullRequest{
			AppReviewID: appReviewID,
		},
	)
	a.Nil(updateResp.Error)
	a.Nil(updateResp.ServerError)
	a.Equal(200, updateResp.StatusCode())
	return updateResp, nil
}

// nolint:unused
func extractCoverageTypesFromDetails(coverages []nonfleet.CoverageDetails) []oapi_common.CoverageType {
	types := make([]oapi_common.CoverageType, len(coverages))
	for i, coverage := range coverages {
		types[i] = coverage.CoverageType
	}
	return types
}

// nolint:unused
func areCoverageArraysEqual(details []nonfleet.CoverageDetails, types []oapi_common.CoverageType) bool {
	extractedTypes := extractCoverageTypesFromDetails(details)

	sort.Slice(extractedTypes, func(i, j int) bool {
		return extractedTypes[i] < extractedTypes[j]
	})
	sort.Slice(types, func(i, j int) bool {
		return types[i] < types[j]
	})

	if len(extractedTypes) != len(types) {
		return false
	}

	for i := range extractedTypes {
		if extractedTypes[i] != types[i] {
			return false
		}
	}
	return true
}

// nolint:unused
func getEnabledAncillaryCoverages(ancillaryCovs oapi_common.AncillaryCoverages) []nonfleet.CoverageDetails {
	var enabledAncillaryCovs []nonfleet.CoverageDetails
	for _, cov := range ancillaryCovs {
		if cov.IsEnabled {
			enabledAncillaryCovs = append(enabledAncillaryCovs, nonfleet.CoverageDetails{
				CoverageType: cov.CoverageName,
			})
		}
	}
	return enabledAncillaryCovs
}
