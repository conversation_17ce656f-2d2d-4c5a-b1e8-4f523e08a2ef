// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_bundle/model/insurance_bundle.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	proto "nirvanatech.com/nirvana/common-go/proto"
	proto1 "nirvanatech.com/nirvana/insurance-core/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LimitGrouping int32

const (
	LimitGrouping_LimitGrouping_Invalid  LimitGrouping = 0
	LimitGrouping_LimitGrouping_Single   LimitGrouping = 1
	LimitGrouping_LimitGrouping_Combined LimitGrouping = 2
)

// Enum value maps for LimitGrouping.
var (
	LimitGrouping_name = map[int32]string{
		0: "LimitGrouping_Invalid",
		1: "LimitGrouping_Single",
		2: "LimitGrouping_Combined",
	}
	LimitGrouping_value = map[string]int32{
		"LimitGrouping_Invalid":  0,
		"LimitGrouping_Single":   1,
		"LimitGrouping_Combined": 2,
	}
)

func (x LimitGrouping) Enum() *LimitGrouping {
	p := new(LimitGrouping)
	*p = x
	return p
}

func (x LimitGrouping) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LimitGrouping) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_bundle_model_insurance_bundle_proto_enumTypes[0].Descriptor()
}

func (LimitGrouping) Type() protoreflect.EnumType {
	return &file_insurance_bundle_model_insurance_bundle_proto_enumTypes[0]
}

func (x LimitGrouping) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LimitGrouping.Descriptor instead.
func (LimitGrouping) EnumDescriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{0}
}

type SortOrder int32

const (
	SortOrder_SortOrder_Invalid SortOrder = 0
	SortOrder_SortOrder_Asc     SortOrder = 1
	SortOrder_SortOrder_Desc    SortOrder = 2
)

// Enum value maps for SortOrder.
var (
	SortOrder_name = map[int32]string{
		0: "SortOrder_Invalid",
		1: "SortOrder_Asc",
		2: "SortOrder_Desc",
	}
	SortOrder_value = map[string]int32{
		"SortOrder_Invalid": 0,
		"SortOrder_Asc":     1,
		"SortOrder_Desc":    2,
	}
)

func (x SortOrder) Enum() *SortOrder {
	p := new(SortOrder)
	*p = x
	return p
}

func (x SortOrder) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortOrder) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_bundle_model_insurance_bundle_proto_enumTypes[1].Descriptor()
}

func (SortOrder) Type() protoreflect.EnumType {
	return &file_insurance_bundle_model_insurance_bundle_proto_enumTypes[1]
}

func (x SortOrder) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortOrder.Descriptor instead.
func (SortOrder) EnumDescriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{1}
}

type ExposureEntityType int32

const (
	ExposureEntityType_EXPOSURE_ENTITY_TYPE_INVALID             ExposureEntityType = 0
	ExposureEntityType_EXPOSURE_ENTITY_TYPE_VEHICLE             ExposureEntityType = 1
	ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO      ExposureEntityType = 2
	ExposureEntityType_EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO ExposureEntityType = 3
)

// Enum value maps for ExposureEntityType.
var (
	ExposureEntityType_name = map[int32]string{
		0: "EXPOSURE_ENTITY_TYPE_INVALID",
		1: "EXPOSURE_ENTITY_TYPE_VEHICLE",
		2: "EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO",
		3: "EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO",
	}
	ExposureEntityType_value = map[string]int32{
		"EXPOSURE_ENTITY_TYPE_INVALID":             0,
		"EXPOSURE_ENTITY_TYPE_VEHICLE":             1,
		"EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO":      2,
		"EXPOSURE_ENTITY_TYPE_NAMED_SHIPPER_CARGO": 3,
	}
)

func (x ExposureEntityType) Enum() *ExposureEntityType {
	p := new(ExposureEntityType)
	*p = x
	return p
}

func (x ExposureEntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExposureEntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_bundle_model_insurance_bundle_proto_enumTypes[2].Descriptor()
}

func (ExposureEntityType) Type() protoreflect.EnumType {
	return &file_insurance_bundle_model_insurance_bundle_proto_enumTypes[2]
}

func (x ExposureEntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExposureEntityType.Descriptor instead.
func (ExposureEntityType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{2}
}

type InsuranceBundleState int32

const (
	InsuranceBundleState_InsuranceBundleState_Invalid             InsuranceBundleState = 0
	InsuranceBundleState_InsuranceBundleState_Active              InsuranceBundleState = 1
	InsuranceBundleState_InsuranceBundleState_Inactive            InsuranceBundleState = 2
	InsuranceBundleState_InsuranceBundleState_Expired             InsuranceBundleState = 3
	InsuranceBundleState_InsuranceBundleState_PendingCancellation InsuranceBundleState = 4
	InsuranceBundleState_InsuranceBundleState_Cancelled           InsuranceBundleState = 5
	InsuranceBundleState_InsuranceBundleState_Stale               InsuranceBundleState = 6
)

// Enum value maps for InsuranceBundleState.
var (
	InsuranceBundleState_name = map[int32]string{
		0: "InsuranceBundleState_Invalid",
		1: "InsuranceBundleState_Active",
		2: "InsuranceBundleState_Inactive",
		3: "InsuranceBundleState_Expired",
		4: "InsuranceBundleState_PendingCancellation",
		5: "InsuranceBundleState_Cancelled",
		6: "InsuranceBundleState_Stale",
	}
	InsuranceBundleState_value = map[string]int32{
		"InsuranceBundleState_Invalid":             0,
		"InsuranceBundleState_Active":              1,
		"InsuranceBundleState_Inactive":            2,
		"InsuranceBundleState_Expired":             3,
		"InsuranceBundleState_PendingCancellation": 4,
		"InsuranceBundleState_Cancelled":           5,
		"InsuranceBundleState_Stale":               6,
	}
)

func (x InsuranceBundleState) Enum() *InsuranceBundleState {
	p := new(InsuranceBundleState)
	*p = x
	return p
}

func (x InsuranceBundleState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InsuranceBundleState) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_bundle_model_insurance_bundle_proto_enumTypes[3].Descriptor()
}

func (InsuranceBundleState) Type() protoreflect.EnumType {
	return &file_insurance_bundle_model_insurance_bundle_proto_enumTypes[3]
}

func (x InsuranceBundleState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InsuranceBundleState.Descriptor instead.
func (InsuranceBundleState) EnumDescriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{3}
}

type Pagination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cursor    *string    `protobuf:"bytes,4,opt,name=cursor,proto3,oneof" json:"cursor,omitempty"`
	PageSize  *int32     `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	SortOrder *SortOrder `protobuf:"varint,6,opt,name=sort_order,json=sortOrder,proto3,enum=model.SortOrder,oneof" json:"sort_order,omitempty"`
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{0}
}

func (x *Pagination) GetCursor() string {
	if x != nil && x.Cursor != nil {
		return *x.Cursor
	}
	return ""
}

func (x *Pagination) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *Pagination) GetSortOrder() SortOrder {
	if x != nil && x.SortOrder != nil {
		return *x.SortOrder
	}
	return SortOrder_SortOrder_Invalid
}

type Limit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DisplayName      string            `protobuf:"bytes,2,opt,name=displayName,proto3" json:"displayName,omitempty"`
	SubCoverageIds   []string          `protobuf:"bytes,3,rep,name=subCoverageIds,proto3" json:"subCoverageIds,omitempty"`
	Amount           float64           `protobuf:"fixed64,4,opt,name=amount,proto3" json:"amount,omitempty"`
	Grouping         LimitGrouping     `protobuf:"varint,5,opt,name=grouping,proto3,enum=model.LimitGrouping" json:"grouping,omitempty"`
	ExposureEntities []*ExposureEntity `protobuf:"bytes,6,rep,name=exposureEntities,proto3" json:"exposureEntities,omitempty"`
}

func (x *Limit) Reset() {
	*x = Limit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Limit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Limit) ProtoMessage() {}

func (x *Limit) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Limit.ProtoReflect.Descriptor instead.
func (*Limit) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{1}
}

func (x *Limit) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Limit) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Limit) GetSubCoverageIds() []string {
	if x != nil {
		return x.SubCoverageIds
	}
	return nil
}

func (x *Limit) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Limit) GetGrouping() LimitGrouping {
	if x != nil {
		return x.Grouping
	}
	return LimitGrouping_LimitGrouping_Invalid
}

func (x *Limit) GetExposureEntities() []*ExposureEntity {
	if x != nil {
		return x.ExposureEntities
	}
	return nil
}

type InsuranceBundleMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RootBindableSubmissionId      string   `protobuf:"bytes,1,opt,name=rootBindableSubmissionId,proto3" json:"rootBindableSubmissionId,omitempty"`
	RootApplicationId             string   `protobuf:"bytes,2,opt,name=rootApplicationId,proto3" json:"rootApplicationId,omitempty"`
	EndorsementApplicationIDs     []string `protobuf:"bytes,5,rep,name=endorsementApplicationIDs,proto3" json:"endorsementApplicationIDs,omitempty"`
	EndorsementApplicationIDDelta *string  `protobuf:"bytes,6,opt,name=endorsementApplicationIDDelta,proto3,oneof" json:"endorsementApplicationIDDelta,omitempty"`
}

func (x *InsuranceBundleMetadata) Reset() {
	*x = InsuranceBundleMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuranceBundleMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuranceBundleMetadata) ProtoMessage() {}

func (x *InsuranceBundleMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuranceBundleMetadata.ProtoReflect.Descriptor instead.
func (*InsuranceBundleMetadata) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{2}
}

func (x *InsuranceBundleMetadata) GetRootBindableSubmissionId() string {
	if x != nil {
		return x.RootBindableSubmissionId
	}
	return ""
}

func (x *InsuranceBundleMetadata) GetRootApplicationId() string {
	if x != nil {
		return x.RootApplicationId
	}
	return ""
}

func (x *InsuranceBundleMetadata) GetEndorsementApplicationIDs() []string {
	if x != nil {
		return x.EndorsementApplicationIDs
	}
	return nil
}

func (x *InsuranceBundleMetadata) GetEndorsementApplicationIDDelta() string {
	if x != nil && x.EndorsementApplicationIDDelta != nil {
		return *x.EndorsementApplicationIDDelta
	}
	return ""
}

type InsuranceBundleSegment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Interval         *proto.Interval    `protobuf:"bytes,1,opt,name=interval,proto3" json:"interval,omitempty"`
	PrimaryInsured   *proto1.Insured    `protobuf:"bytes,2,opt,name=primaryInsured,proto3" json:"primaryInsured,omitempty"`
	Policies         map[string]*Policy `protobuf:"bytes,3,rep,name=policies,proto3" json:"policies,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CoverageCriteria *CoverageCriteria  `protobuf:"bytes,4,opt,name=coverageCriteria,proto3" json:"coverageCriteria,omitempty"`
	Id               string             `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *InsuranceBundleSegment) Reset() {
	*x = InsuranceBundleSegment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuranceBundleSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuranceBundleSegment) ProtoMessage() {}

func (x *InsuranceBundleSegment) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuranceBundleSegment.ProtoReflect.Descriptor instead.
func (*InsuranceBundleSegment) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{3}
}

func (x *InsuranceBundleSegment) GetInterval() *proto.Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *InsuranceBundleSegment) GetPrimaryInsured() *proto1.Insured {
	if x != nil {
		return x.PrimaryInsured
	}
	return nil
}

func (x *InsuranceBundleSegment) GetPolicies() map[string]*Policy {
	if x != nil {
		return x.Policies
	}
	return nil
}

func (x *InsuranceBundleSegment) GetCoverageCriteria() *CoverageCriteria {
	if x != nil {
		return x.CoverageCriteria
	}
	return nil
}

func (x *InsuranceBundleSegment) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type Deductible struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageIds   []string          `protobuf:"bytes,1,rep,name=subCoverageIds,proto3" json:"subCoverageIds,omitempty"`
	Amount           float64           `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	ExposureEntities []*ExposureEntity `protobuf:"bytes,3,rep,name=exposureEntities,proto3" json:"exposureEntities,omitempty"`
}

func (x *Deductible) Reset() {
	*x = Deductible{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deductible) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deductible) ProtoMessage() {}

func (x *Deductible) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deductible.ProtoReflect.Descriptor instead.
func (*Deductible) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{4}
}

func (x *Deductible) GetSubCoverageIds() []string {
	if x != nil {
		return x.SubCoverageIds
	}
	return nil
}

func (x *Deductible) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Deductible) GetExposureEntities() []*ExposureEntity {
	if x != nil {
		return x.ExposureEntities
	}
	return nil
}

type ExposureEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type ExposureEntityType `protobuf:"varint,2,opt,name=type,proto3,enum=model.ExposureEntityType" json:"type,omitempty"`
}

func (x *ExposureEntity) Reset() {
	*x = ExposureEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureEntity) ProtoMessage() {}

func (x *ExposureEntity) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureEntity.ProtoReflect.Descriptor instead.
func (*ExposureEntity) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{5}
}

func (x *ExposureEntity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExposureEntity) GetType() ExposureEntityType {
	if x != nil {
		return x.Type
	}
	return ExposureEntityType_EXPOSURE_ENTITY_TYPE_INVALID
}

type CombinedDeductible struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageIds []string `protobuf:"bytes,1,rep,name=subCoverageIds,proto3" json:"subCoverageIds,omitempty"`
}

func (x *CombinedDeductible) Reset() {
	*x = CombinedDeductible{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CombinedDeductible) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CombinedDeductible) ProtoMessage() {}

func (x *CombinedDeductible) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CombinedDeductible.ProtoReflect.Descriptor instead.
func (*CombinedDeductible) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{6}
}

func (x *CombinedDeductible) GetSubCoverageIds() []string {
	if x != nil {
		return x.SubCoverageIds
	}
	return nil
}

type CoverageCriteria struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Limits              []*Limit              `protobuf:"bytes,1,rep,name=limits,proto3" json:"limits,omitempty"`
	Deductibles         []*Deductible         `protobuf:"bytes,2,rep,name=deductibles,proto3" json:"deductibles,omitempty"`
	CombinedDeductibles []*CombinedDeductible `protobuf:"bytes,3,rep,name=combinedDeductibles,proto3" json:"combinedDeductibles,omitempty"`
}

func (x *CoverageCriteria) Reset() {
	*x = CoverageCriteria{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoverageCriteria) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoverageCriteria) ProtoMessage() {}

func (x *CoverageCriteria) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoverageCriteria.ProtoReflect.Descriptor instead.
func (*CoverageCriteria) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{7}
}

func (x *CoverageCriteria) GetLimits() []*Limit {
	if x != nil {
		return x.Limits
	}
	return nil
}

func (x *CoverageCriteria) GetDeductibles() []*Deductible {
	if x != nil {
		return x.Deductibles
	}
	return nil
}

func (x *CoverageCriteria) GetCombinedDeductibles() []*CombinedDeductible {
	if x != nil {
		return x.CombinedDeductibles
	}
	return nil
}

type InsuranceBundle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExternalId               string                     `protobuf:"bytes,1,opt,name=externalId,proto3" json:"externalId,omitempty"`
	InternalId               string                     `protobuf:"bytes,2,opt,name=internalId,proto3" json:"internalId,omitempty"`
	Version                  int64                      `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	DefaultCarrier           proto1.InsuranceCarrier    `protobuf:"varint,4,opt,name=defaultCarrier,proto3,enum=insurance_core.InsuranceCarrier" json:"defaultCarrier,omitempty"`
	DefaultSeller            *proto1.SellerInfo         `protobuf:"bytes,5,opt,name=defaultSeller,proto3" json:"defaultSeller,omitempty"`
	DefaultEffectiveDuration *proto.Interval            `protobuf:"bytes,6,opt,name=defaultEffectiveDuration,proto3" json:"defaultEffectiveDuration,omitempty"`
	ProgramType              proto1.ProgramType         `protobuf:"varint,7,opt,name=programType,proto3,enum=insurance_core.ProgramType" json:"programType,omitempty"`
	Metadata                 *InsuranceBundleMetadata   `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	FormInfo                 *proto1.FormInfo           `protobuf:"bytes,9,opt,name=formInfo,proto3" json:"formInfo,omitempty"`
	Segments                 []*InsuranceBundleSegment  `protobuf:"bytes,10,rep,name=segments,proto3" json:"segments,omitempty"`
	CreatedAt                *timestamppb.Timestamp     `protobuf:"bytes,11,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt                *timestamppb.Timestamp     `protobuf:"bytes,12,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	State                    InsuranceBundleState       `protobuf:"varint,13,opt,name=state,proto3,enum=model.InsuranceBundleState" json:"state,omitempty"`
	CarrierAdmittedType      proto1.CarrierAdmittedType `protobuf:"varint,14,opt,name=carrierAdmittedType,proto3,enum=insurance_core.CarrierAdmittedType" json:"carrierAdmittedType,omitempty"`
}

func (x *InsuranceBundle) Reset() {
	*x = InsuranceBundle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuranceBundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuranceBundle) ProtoMessage() {}

func (x *InsuranceBundle) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuranceBundle.ProtoReflect.Descriptor instead.
func (*InsuranceBundle) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{8}
}

func (x *InsuranceBundle) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *InsuranceBundle) GetInternalId() string {
	if x != nil {
		return x.InternalId
	}
	return ""
}

func (x *InsuranceBundle) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *InsuranceBundle) GetDefaultCarrier() proto1.InsuranceCarrier {
	if x != nil {
		return x.DefaultCarrier
	}
	return proto1.InsuranceCarrier(0)
}

func (x *InsuranceBundle) GetDefaultSeller() *proto1.SellerInfo {
	if x != nil {
		return x.DefaultSeller
	}
	return nil
}

func (x *InsuranceBundle) GetDefaultEffectiveDuration() *proto.Interval {
	if x != nil {
		return x.DefaultEffectiveDuration
	}
	return nil
}

func (x *InsuranceBundle) GetProgramType() proto1.ProgramType {
	if x != nil {
		return x.ProgramType
	}
	return proto1.ProgramType(0)
}

func (x *InsuranceBundle) GetMetadata() *InsuranceBundleMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *InsuranceBundle) GetFormInfo() *proto1.FormInfo {
	if x != nil {
		return x.FormInfo
	}
	return nil
}

func (x *InsuranceBundle) GetSegments() []*InsuranceBundleSegment {
	if x != nil {
		return x.Segments
	}
	return nil
}

func (x *InsuranceBundle) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *InsuranceBundle) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *InsuranceBundle) GetState() InsuranceBundleState {
	if x != nil {
		return x.State
	}
	return InsuranceBundleState_InsuranceBundleState_Invalid
}

func (x *InsuranceBundle) GetCarrierAdmittedType() proto1.CarrierAdmittedType {
	if x != nil {
		return x.CarrierAdmittedType
	}
	return proto1.CarrierAdmittedType(0)
}

type CondensedInsuranceBundle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExternalID               string                 `protobuf:"bytes,1,opt,name=externalID,proto3" json:"externalID,omitempty"`
	InternalID               string                 `protobuf:"bytes,2,opt,name=internalID,proto3" json:"internalID,omitempty"`
	PrimaryInsured           *proto1.Insured        `protobuf:"bytes,3,opt,name=primaryInsured,proto3" json:"primaryInsured,omitempty"`
	DefaultEffectiveDuration *proto.Interval        `protobuf:"bytes,4,opt,name=defaultEffectiveDuration,proto3" json:"defaultEffectiveDuration,omitempty"`
	State                    InsuranceBundleState   `protobuf:"varint,5,opt,name=state,proto3,enum=model.InsuranceBundleState" json:"state,omitempty"`
	CreatedAt                *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt                *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
}

func (x *CondensedInsuranceBundle) Reset() {
	*x = CondensedInsuranceBundle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CondensedInsuranceBundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CondensedInsuranceBundle) ProtoMessage() {}

func (x *CondensedInsuranceBundle) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_insurance_bundle_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CondensedInsuranceBundle.ProtoReflect.Descriptor instead.
func (*CondensedInsuranceBundle) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP(), []int{9}
}

func (x *CondensedInsuranceBundle) GetExternalID() string {
	if x != nil {
		return x.ExternalID
	}
	return ""
}

func (x *CondensedInsuranceBundle) GetInternalID() string {
	if x != nil {
		return x.InternalID
	}
	return ""
}

func (x *CondensedInsuranceBundle) GetPrimaryInsured() *proto1.Insured {
	if x != nil {
		return x.PrimaryInsured
	}
	return nil
}

func (x *CondensedInsuranceBundle) GetDefaultEffectiveDuration() *proto.Interval {
	if x != nil {
		return x.DefaultEffectiveDuration
	}
	return nil
}

func (x *CondensedInsuranceBundle) GetState() InsuranceBundleState {
	if x != nil {
		return x.State
	}
	return InsuranceBundleState_InsuranceBundleState_Invalid
}

func (x *CondensedInsuranceBundle) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CondensedInsuranceBundle) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_insurance_bundle_model_insurance_bundle_proto protoreflect.FileDescriptor

var file_insurance_bundle_model_insurance_bundle_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x23, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f,
	0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xa9, 0x01, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1b, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x88, 0x01, 0x01, 0x12, 0x34,
	0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x48, 0x02, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xee, 0x01, 0x0a,
	0x05, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x6e, 0x67,
	0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x41, 0x0a, 0x10, 0x65, 0x78,
	0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x10, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x22, 0xae, 0x02,
	0x0a, 0x17, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x18, 0x72, 0x6f, 0x6f,
	0x74, 0x42, 0x69, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x72, 0x6f, 0x6f,
	0x74, 0x42, 0x69, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x6f, 0x6f, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x72, 0x6f, 0x6f, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x19, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x19, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44,
	0x73, 0x12, 0x49, 0x0a, 0x1d, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x44, 0x65, 0x6c,
	0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x1d, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x88, 0x01, 0x01, 0x42, 0x20, 0x0a, 0x1e,
	0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x22, 0xf1,
	0x02, 0x0a, 0x16, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x08, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x08, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x3f, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65,
	0x73, 0x12, 0x43, 0x0a, 0x10, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x72, 0x69, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x52, 0x10, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x1a, 0x4a, 0x0a, 0x0d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x8f, 0x01, 0x0a, 0x0a, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x41, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x10, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x22, 0x4f, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x3c, 0x0a, 0x12, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65,
	0x64, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73,
	0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x10, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x24, 0x0a, 0x06, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x06, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x33,
	0x0a, 0x0b, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62,
	0x6c, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x44,
	0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65,
	0x64, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x13, 0x63, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x64, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x73,
	0x22, 0xaf, 0x06, 0x0a, 0x0f, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x48,
	0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x18, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x18,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x08, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x08, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38,
	0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x31, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x13, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x90, 0x03, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x73, 0x65, 0x64,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x44, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x44, 0x12,
	0x3f, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x12, 0x4c, 0x0a, 0x18, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x18, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x60, 0x0a, 0x0d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x19, 0x0a, 0x15, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x43, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x64, 0x10, 0x02, 0x2a, 0x49, 0x0a, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53,
	0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x41, 0x73, 0x63, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x53, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x44, 0x65, 0x73, 0x63,
	0x10, 0x02, 0x2a, 0xaf, 0x01, 0x0a, 0x12, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x58, 0x50,
	0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x45,
	0x58, 0x50, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x27, 0x0a,
	0x23, 0x45, 0x58, 0x50, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43,
	0x41, 0x52, 0x47, 0x4f, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x45, 0x58, 0x50, 0x4f, 0x53, 0x55,
	0x52, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x44, 0x5f, 0x53, 0x48, 0x49, 0x50, 0x50, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x52,
	0x47, 0x4f, 0x10, 0x03, 0x2a, 0x90, 0x02, 0x0a, 0x14, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a,
	0x1c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12,
	0x1f, 0x0a, 0x1b, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x10, 0x01,
	0x12, 0x21, 0x0a, 0x1d, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x10, 0x03, 0x12, 0x2c, 0x0a, 0x28, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x50, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x65, 0x64, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5f,
	0x53, 0x74, 0x61, 0x6c, 0x65, 0x10, 0x06, 0x42, 0x30, 0x5a, 0x2e, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_insurance_bundle_model_insurance_bundle_proto_rawDescOnce sync.Once
	file_insurance_bundle_model_insurance_bundle_proto_rawDescData = file_insurance_bundle_model_insurance_bundle_proto_rawDesc
)

func file_insurance_bundle_model_insurance_bundle_proto_rawDescGZIP() []byte {
	file_insurance_bundle_model_insurance_bundle_proto_rawDescOnce.Do(func() {
		file_insurance_bundle_model_insurance_bundle_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_bundle_model_insurance_bundle_proto_rawDescData)
	})
	return file_insurance_bundle_model_insurance_bundle_proto_rawDescData
}

var file_insurance_bundle_model_insurance_bundle_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_insurance_bundle_model_insurance_bundle_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_insurance_bundle_model_insurance_bundle_proto_goTypes = []interface{}{
	(LimitGrouping)(0),               // 0: model.LimitGrouping
	(SortOrder)(0),                   // 1: model.SortOrder
	(ExposureEntityType)(0),          // 2: model.ExposureEntityType
	(InsuranceBundleState)(0),        // 3: model.InsuranceBundleState
	(*Pagination)(nil),               // 4: model.Pagination
	(*Limit)(nil),                    // 5: model.Limit
	(*InsuranceBundleMetadata)(nil),  // 6: model.InsuranceBundleMetadata
	(*InsuranceBundleSegment)(nil),   // 7: model.InsuranceBundleSegment
	(*Deductible)(nil),               // 8: model.Deductible
	(*ExposureEntity)(nil),           // 9: model.ExposureEntity
	(*CombinedDeductible)(nil),       // 10: model.CombinedDeductible
	(*CoverageCriteria)(nil),         // 11: model.CoverageCriteria
	(*InsuranceBundle)(nil),          // 12: model.InsuranceBundle
	(*CondensedInsuranceBundle)(nil), // 13: model.CondensedInsuranceBundle
	nil,                              // 14: model.InsuranceBundleSegment.PoliciesEntry
	(*proto.Interval)(nil),           // 15: common.Interval
	(*proto1.Insured)(nil),           // 16: insurance_core.Insured
	(proto1.InsuranceCarrier)(0),     // 17: insurance_core.InsuranceCarrier
	(*proto1.SellerInfo)(nil),        // 18: insurance_core.SellerInfo
	(proto1.ProgramType)(0),          // 19: insurance_core.ProgramType
	(*proto1.FormInfo)(nil),          // 20: insurance_core.FormInfo
	(*timestamppb.Timestamp)(nil),    // 21: google.protobuf.Timestamp
	(proto1.CarrierAdmittedType)(0),  // 22: insurance_core.CarrierAdmittedType
	(*Policy)(nil),                   // 23: model.Policy
}
var file_insurance_bundle_model_insurance_bundle_proto_depIdxs = []int32{
	1,  // 0: model.Pagination.sort_order:type_name -> model.SortOrder
	0,  // 1: model.Limit.grouping:type_name -> model.LimitGrouping
	9,  // 2: model.Limit.exposureEntities:type_name -> model.ExposureEntity
	15, // 3: model.InsuranceBundleSegment.interval:type_name -> common.Interval
	16, // 4: model.InsuranceBundleSegment.primaryInsured:type_name -> insurance_core.Insured
	14, // 5: model.InsuranceBundleSegment.policies:type_name -> model.InsuranceBundleSegment.PoliciesEntry
	11, // 6: model.InsuranceBundleSegment.coverageCriteria:type_name -> model.CoverageCriteria
	9,  // 7: model.Deductible.exposureEntities:type_name -> model.ExposureEntity
	2,  // 8: model.ExposureEntity.type:type_name -> model.ExposureEntityType
	5,  // 9: model.CoverageCriteria.limits:type_name -> model.Limit
	8,  // 10: model.CoverageCriteria.deductibles:type_name -> model.Deductible
	10, // 11: model.CoverageCriteria.combinedDeductibles:type_name -> model.CombinedDeductible
	17, // 12: model.InsuranceBundle.defaultCarrier:type_name -> insurance_core.InsuranceCarrier
	18, // 13: model.InsuranceBundle.defaultSeller:type_name -> insurance_core.SellerInfo
	15, // 14: model.InsuranceBundle.defaultEffectiveDuration:type_name -> common.Interval
	19, // 15: model.InsuranceBundle.programType:type_name -> insurance_core.ProgramType
	6,  // 16: model.InsuranceBundle.metadata:type_name -> model.InsuranceBundleMetadata
	20, // 17: model.InsuranceBundle.formInfo:type_name -> insurance_core.FormInfo
	7,  // 18: model.InsuranceBundle.segments:type_name -> model.InsuranceBundleSegment
	21, // 19: model.InsuranceBundle.createdAt:type_name -> google.protobuf.Timestamp
	21, // 20: model.InsuranceBundle.updatedAt:type_name -> google.protobuf.Timestamp
	3,  // 21: model.InsuranceBundle.state:type_name -> model.InsuranceBundleState
	22, // 22: model.InsuranceBundle.carrierAdmittedType:type_name -> insurance_core.CarrierAdmittedType
	16, // 23: model.CondensedInsuranceBundle.primaryInsured:type_name -> insurance_core.Insured
	15, // 24: model.CondensedInsuranceBundle.defaultEffectiveDuration:type_name -> common.Interval
	3,  // 25: model.CondensedInsuranceBundle.state:type_name -> model.InsuranceBundleState
	21, // 26: model.CondensedInsuranceBundle.createdAt:type_name -> google.protobuf.Timestamp
	21, // 27: model.CondensedInsuranceBundle.updatedAt:type_name -> google.protobuf.Timestamp
	23, // 28: model.InsuranceBundleSegment.PoliciesEntry.value:type_name -> model.Policy
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_insurance_bundle_model_insurance_bundle_proto_init() }
func file_insurance_bundle_model_insurance_bundle_proto_init() {
	if File_insurance_bundle_model_insurance_bundle_proto != nil {
		return
	}
	file_insurance_bundle_model_policy_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pagination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Limit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuranceBundleMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuranceBundleSegment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deductible); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CombinedDeductible); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoverageCriteria); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuranceBundle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_insurance_bundle_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CondensedInsuranceBundle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_insurance_bundle_model_insurance_bundle_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_insurance_bundle_model_insurance_bundle_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_bundle_model_insurance_bundle_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_bundle_model_insurance_bundle_proto_goTypes,
		DependencyIndexes: file_insurance_bundle_model_insurance_bundle_proto_depIdxs,
		EnumInfos:         file_insurance_bundle_model_insurance_bundle_proto_enumTypes,
		MessageInfos:      file_insurance_bundle_model_insurance_bundle_proto_msgTypes,
	}.Build()
	File_insurance_bundle_model_insurance_bundle_proto = out.File
	file_insurance_bundle_model_insurance_bundle_proto_rawDesc = nil
	file_insurance_bundle_model_insurance_bundle_proto_goTypes = nil
	file_insurance_bundle_model_insurance_bundle_proto_depIdxs = nil
}
