package manual_forms

import (
	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/non_fleet_admitted/manual_forms/IL_12_09_08_23"
)

func GenIL12090823Req(_ string, inputs ManualFormsInputs) (*IL_12_09_08_23.Request, error) {
	return &IL_12_09_08_23.Request{
		PolicyNumber:             fields.StringM(inputs.PolicyNumber),
		PolicyChangeNumber:       fields.StringM(inputs.PolicyChangeNumber),
		Company:                  fields.InsuranceCarrier(inputs.Company),
		FromMM:                   fields.StringS(inputs.EffectiveDateFromMM),
		FromDD:                   fields.StringS(inputs.EffectiveDateFromDD),
		FromYYYY:                 fields.StringS(inputs.EffectiveDateFromYYYY),
		ToMM:                     fields.StringS(inputs.EffectiveDateToMM),
		ToDD:                     fields.StringS(inputs.EffectiveDateToDD),
		ToYYYY:                   fields.StringS(inputs.EffectiveDateToYYYY),
		NamedInsured:             fields.StringXXL(inputs.NamedInsured),
		AuthorizedRepresentative: fields.StringL(inputs.AuthorizedRepresentative),
		InsuredMailingAddress:    fields.StringXXL(inputs.InsuredMailingAddress),
		CoverageAffected:         fields.OptionalStringXXL(inputs.EndorsementCoverageAffected),
		Changes:                  fields.OptionalStringXXXL(inputs.EndorsementChanges),
		AdditionalPremium:        fields.OptionalStringS(inputs.EndorsementAdditionalPremium),
		ReturnPremium:            fields.OptionalStringS(inputs.EndorsementReturnedPremium),
		Date:                     fields.NonZeroDate{Time: inputs.Date},
	}, nil
}
