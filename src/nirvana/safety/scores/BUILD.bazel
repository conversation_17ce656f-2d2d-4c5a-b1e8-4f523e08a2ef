load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "scores",
    srcs = [
        "calculator.go",
        "controlled_substances_alcohol.go",
        "crash_indicator.go",
        "driver_fitness.go",
        "hazmat_compliance.go",
        "hos_compliance.go",
        "iss.go",
        "scores.go",
        "segment_type.go",
        "stats.go",
        "unsafe_driving.go",
        "utilization_factor.go",
        "vehicle_maintenance.go",
    ],
    importpath = "nirvanatech.com/nirvana/safety/scores",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/models",
        "//nirvana/safety/common",
        "//nirvana/safety/scores/percentiles",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "scores_test",
    srcs = [
        "percentiles_db_test.go",
        "segment_type_test.go",
        "stats_test.go",
        "vehicle_maintenance_test.go",
    ],
    data = glob(["testdata/**"]),
    embed = [":scores"],
    embedsrcs = [
        "BASIC_SCORES_CAB_2021_12_31.csv",
        "BASIC_SCORES_CAB_2022_01_28.csv",
        "BASIC_SCORES_CAB_2022_02_25.csv",
    ],
    tags = ["aws"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/feature_store/persistent",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/fx/testfixtures/basic_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/safety/common",
        "//nirvana/safety/scores/percentiles",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
