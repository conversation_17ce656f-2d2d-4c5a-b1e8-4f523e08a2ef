load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "tests_test",
    srcs = [
        "baseline_test.go",
        "filtered_test.go",
        "utils_test.go",
    ],
    data = glob(["testdata/**"]),
    deps = [
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/feature_store/persistent",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/models",
        "//nirvana/safety/scores",
        "//nirvana/safety/scores/projections",
        "@com_github_samsarahq_go//snapshotter",
        "@com_github_stretchr_testify//assert",
    ],
)
