load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "reporter",
    srcs = [
        "basic_score_result.go",
        "datagov.go",
        "driver_vehicle_assignments.go",
        "errors.go",
        "fleet.go",
        "fmcsa_data.go",
        "fx.go",
        "invite.go",
        "location_stats.go",
        "pdf.go",
        "reporter.go",
        "safety_report.go",
        "share.go",
        "telematics.go",
        "telematics_risk_fleet_explainability.go",
        "telematics_risk_fleet_percentiles.go",
        "telematics_risk_vin_percentiles.go",
        "telematics_vehicle_mileage.go",
        "users.go",
        "vehicle_stats.go",
        "violation_stats.go",
    ],
    data = glob(["data/**"]),
    embedsrcs = [
        "data/mock_insurance_summary.json",
        "data/mock_general_trends.json",
        "data/mock_oos_summary.json",
        "data/mock_shippers.json",
        "data/mock_inspection_data.json",
        "data/mock_violations_data.json",
    ],
    importpath = "nirvanatech.com/nirvana/safety/reporter",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/common-go/errgroup",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/interval",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/risk_score_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/tracing",
        "//nirvana/common-go/url_util",
        "//nirvana/common-go/vin_utils",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/driver_vehicle_assignments",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/fleet_telematics",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/safety",
        "//nirvana/db-api/db_wrappers/safety/cached_ds_mileage",
        "//nirvana/db-api/db_wrappers/sharing",
        "//nirvana/db-api/db_wrappers/vehicle_driver_assignment_stats",
        "//nirvana/emailer",
        "//nirvana/emailer/models",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/feature_store",
        "//nirvana/feature_store/grpc",
        "//nirvana/features",
        "//nirvana/fmcsa/basic",
        "//nirvana/fmcsa/basic/generated/registry",
        "//nirvana/fmcsa/datagov/models",
        "//nirvana/fmcsa/db_models/datagov",
        "//nirvana/fmcsa/models",
        "//nirvana/fmcsa/safety",
        "//nirvana/gqlschema/models",
        "//nirvana/infra/authz",
        "//nirvana/infra/authz/checker",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/oauth",
        "//nirvana/pdfgen",
        "//nirvana/rating/data_fetching/lni_fetching",
        "//nirvana/safety",
        "//nirvana/safety/common",
        "//nirvana/safety/enums",
        "//nirvana/safety/flags",
        "//nirvana/safety/recommendations/factory",
        "//nirvana/safety/scores",
        "//nirvana/safety/scores/oos_rates",
        "//nirvana/servers/telematicsv2",
        "//nirvana/sharing",
        "//nirvana/telematics",
        "//nirvana/telematics/connection_selector",
        "//nirvana/telematics/data_platform/workflows/jobs",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_dustin_go_humanize//:go-humanize",
        "@com_github_google_uuid//:uuid",
        "@com_github_samsarahq_thunder//graphql",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@io_opentelemetry_go_otel//attribute",
        "@io_opentelemetry_go_otel_trace//:trace",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_golang_x_text//cases",
        "@org_golang_x_text//language",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "reporter_test",
    srcs = [
        "driver_vehicle_assignments_test.go",
        "fmcsa_data_test.go",
        "pdf_test.go",
        "telematics_risk_fleet_explainability_test.go",
        "telematics_test.go",
        "telematics_vehicle_mileage_test.go",
        "vehicle_stats_test.go",
    ],
    embed = [":reporter"],
    tags = ["aws"],
    deps = [
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/test_utils/builders/safety/cached_ds_mileage",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/safety",
        "//nirvana/db-api/db_wrappers/safety/cached_ds_mileage",
        "//nirvana/feature_store/grpc",
        "//nirvana/feature_store/persistent",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/testfixtures/feature_store_fixture",
        "//nirvana/infra/fx/testfixtures/fleet_fixture",
        "//nirvana/infra/fx/testfixtures/oauth_fixture",
        "//nirvana/infra/fx/testfixtures/safety_report_fixture",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/pdfgen",
        "//nirvana/safety/common",
        "//nirvana/servers/telematicsv2",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_dustin_go_humanize//:go-humanize",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
