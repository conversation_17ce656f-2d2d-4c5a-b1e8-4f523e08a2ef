// Package application provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package application

import (
	"encoding/json"
	"time"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/common"
	externalRef1 "nirvanatech.com/nirvana/openapi-specs/components/forms"
)

// Defines values for AdditionalInformationCommodity.
const (
	AddlInfoDoubleOrTripleTrailers       AdditionalInformationCommodity = "AddlInfoDoubleOrTripleTrailers"
	AddlInfoHazardousMaterialsInclClass9 AdditionalInformationCommodity = "AddlInfoHazardousMaterialsInclClass9"
	AddlInfoLiftGateOrWhiteGloveService  AdditionalInformationCommodity = "AddlInfoLiftGateOrWhiteGloveService"
	AddlInfoMeatOnHook                   AdditionalInformationCommodity = "AddlInfoMeatOnHook"
	AddlInfoResidentialDelivery          AdditionalInformationCommodity = "AddlInfoResidentialDelivery"
)

// Defines values for AppetiteCheckRule.
const (
	AppetiteCheckRuleCarrierLoyalty         AppetiteCheckRule = "AppetiteCheckRuleCarrierLoyalty"
	AppetiteCheckRuleCompanyAddress         AppetiteCheckRule = "AppetiteCheckRuleCompanyAddress"
	AppetiteCheckRuleDotRating              AppetiteCheckRule = "AppetiteCheckRuleDotRating"
	AppetiteCheckRuleHasUndesiredOperations AppetiteCheckRule = "AppetiteCheckRuleHasUndesiredOperations"
	AppetiteCheckRuleIsValidOrActive        AppetiteCheckRule = "AppetiteCheckRuleIsValidOrActive"
	AppetiteCheckRuleLapsesInOperation      AppetiteCheckRule = "AppetiteCheckRuleLapsesInOperation"
	AppetiteCheckRuleUnitMeasure            AppetiteCheckRule = "AppetiteCheckRuleUnitMeasure"
	AppetiteCheckRuleYearsInOperation       AppetiteCheckRule = "AppetiteCheckRuleYearsInOperation"
)

// Defines values for ApplicationState.
const (
	ApplicationStateApproved                 ApplicationState = "ApplicationStateApproved"
	ApplicationStateBound                    ApplicationState = "ApplicationStateBound"
	ApplicationStateClosed                   ApplicationState = "ApplicationStateClosed"
	ApplicationStateCreated                  ApplicationState = "ApplicationStateCreated"
	ApplicationStateDeclined                 ApplicationState = "ApplicationStateDeclined"
	ApplicationStateInProgress               ApplicationState = "ApplicationStateInProgress"
	ApplicationStateIndicationDelay          ApplicationState = "ApplicationStateIndicationDelay"
	ApplicationStateIndicationGenerated      ApplicationState = "ApplicationStateIndicationGenerated"
	ApplicationStateIndicationSelected       ApplicationState = "ApplicationStateIndicationSelected"
	ApplicationStatePanic                    ApplicationState = "ApplicationStatePanic"
	ApplicationStatePendingELDTelematics     ApplicationState = "ApplicationStatePendingELDTelematics"
	ApplicationStateProcessingELDTelematics  ApplicationState = "ApplicationStateProcessingELDTelematics"
	ApplicationStateQuoteGenerated           ApplicationState = "ApplicationStateQuoteGenerated"
	ApplicationStateUnderReviewForIndication ApplicationState = "ApplicationStateUnderReviewForIndication"
	ApplicationStateUnderReviewForQuote      ApplicationState = "ApplicationStateUnderReviewForQuote"
)

// Defines values for ApplicationSummaryTelematicsDataStatus.
const (
	TelematicsDataStatusFailed  ApplicationSummaryTelematicsDataStatus = "TelematicsDataStatusFailed"
	TelematicsDataStatusSuccess ApplicationSummaryTelematicsDataStatus = "TelematicsDataStatusSuccess"
)

// Defines values for ApplicationTab.
const (
	ApplicationTabAll                  ApplicationTab = "ApplicationTabAll"
	ApplicationTabBound                ApplicationTab = "ApplicationTabBound"
	ApplicationTabClosed               ApplicationTab = "ApplicationTabClosed"
	ApplicationTabDeclined             ApplicationTab = "ApplicationTabDeclined"
	ApplicationTabInProgress           ApplicationTab = "ApplicationTabInProgress"
	ApplicationTabPendingELDTelematics ApplicationTab = "ApplicationTabPendingELDTelematics"
	ApplicationTabReadyToBind          ApplicationTab = "ApplicationTabReadyToBind"
	ApplicationTabReadyToQuote         ApplicationTab = "ApplicationTabReadyToQuote"
	ApplicationTabUnderUWReview        ApplicationTab = "ApplicationTabUnderUWReview"
)

// Defines values for BillingContactRole.
const (
	BillingContactRoleAgent          BillingContactRole = "Agent"
	BillingContactRoleBillingManager BillingContactRole = "BillingManager"
	BillingContactRoleOther          BillingContactRole = "Other"
	BillingContactRoleOwner          BillingContactRole = "Owner"
	BillingContactRoleProducer       BillingContactRole = "Producer"
)

// Defines values for ContactType.
const (
	ContactTypeBilling ContactType = "ContactTypeBilling"
	ContactTypeSafety  ContactType = "ContactTypeSafety"
)

// Defines values for CreditReportStatus.
const (
	Failed     CreditReportStatus = "Failed"
	InProgress CreditReportStatus = "InProgress"
	NoHit      CreditReportStatus = "NoHit"
	Success    CreditReportStatus = "Success"
	ThinFile   CreditReportStatus = "ThinFile"
)

// Defines values for IndicationOptionTag.
const (
	IndicationOptionTagBasic    IndicationOptionTag = "IndicationOptionTagBasic"
	IndicationOptionTagComplete IndicationOptionTag = "IndicationOptionTagComplete"
	IndicationOptionTagCustom   IndicationOptionTag = "IndicationOptionTagCustom"
	IndicationOptionTagStandard IndicationOptionTag = "IndicationOptionTagStandard"
)

// Defines values for MileageRadiusBucket.
const (
	MileageRadiusBucketFiftyToTwoHundred       MileageRadiusBucket = "MileageRadiusBucketFiftyToTwoHundred"
	MileageRadiusBucketFiveHundredPlus         MileageRadiusBucket = "MileageRadiusBucketFiveHundredPlus"
	MileageRadiusBucketTwoHundredToFiveHundred MileageRadiusBucket = "MileageRadiusBucketTwoHundredToFiveHundred"
	MileageRadiusBucketZeroToFifty             MileageRadiusBucket = "MileageRadiusBucketZeroToFifty"
)

// Defines values for POCType.
const (
	POCTypePrimary POCType = "POCTypePrimary"
	POCTypeRegular POCType = "POCTypeRegular"
)

// Defines values for PaymentOption.
const (
	PaymentOptionMonthlyReporter PaymentOption = "PaymentOptionMonthlyReporter"
	PaymentOptionPaidInFull      PaymentOption = "PaymentOptionPaidInFull"
)

// Defines values for PaymentOptionType.
const (
	Annual  PaymentOptionType = "annual"
	Monthly PaymentOptionType = "monthly"
)

// Defines values for PotentialClearanceStatus.
const (
	ClearedApplicationExists           PotentialClearanceStatus = "ClearedApplicationExists"
	ClearedApplicationExistsSameAgency PotentialClearanceStatus = "ClearedApplicationExistsSameAgency"
	DeclinedClearedApplicationExists   PotentialClearanceStatus = "DeclinedClearedApplicationExists"
)

// Defines values for PreApplicationCheckRule.
const (
	PreApplicationRuleDuplicateApplication PreApplicationCheckRule = "PreApplicationRuleDuplicateApplication"
	PreApplicationRulePossibleMidTermMove  PreApplicationCheckRule = "PreApplicationRulePossibleMidTermMove"
	PreApplicationRulePossibleRenewal      PreApplicationCheckRule = "PreApplicationRulePossibleRenewal"
	PreApplicationRulePotentialClearance   PreApplicationCheckRule = "PreApplicationRulePotentialClearance"
	PreApplicationRuleSameAgencyRenewal    PreApplicationCheckRule = "PreApplicationRuleSameAgencyRenewal"
)

// Defines values for SafetyContactRole.
const (
	SafetyContactRoleFleetManager  SafetyContactRole = "FleetManager"
	SafetyContactRoleOther         SafetyContactRole = "Other"
	SafetyContactRoleOwner         SafetyContactRole = "Owner"
	SafetyContactRoleSafetyManager SafetyContactRole = "SafetyManager"
)

// Defines values for TSP.
const (
	TSP3MD                              TSP = "TSP3MD"
	TSP888ELD                           TSP = "TSP888ELD"
	TSPARIFleet                         TSP = "TSPARIFleet"
	TSPATAndTFleet                      TSP = "TSPATAndTFleet"
	TSPATAndTFleetComplete              TSP = "TSPATAndTFleetComplete"
	TSPActsoftEncore                    TSP = "TSPActsoftEncore"
	TSPAdvantageAssetTracking           TSP = "TSPAdvantageAssetTracking"
	TSPAgilisLinxup                     TSP = "TSPAgilisLinxup"
	TSPAlfaELD                          TSP = "TSPAlfaELD"
	TSPApolloELD                        TSP = "TSPApolloELD"
	TSPArgosConnectedSolutions          TSP = "TSPArgosConnectedSolutions"
	TSPAssuredTelematics                TSP = "TSPAssuredTelematics"
	TSPAttriX                           TSP = "TSPAttriX"
	TSPAwareGPS                         TSP = "TSPAwareGPS"
	TSPAzuga                            TSP = "TSPAzuga"
	TSPBELLFAMELD                       TSP = "TSPBELLFAMELD"
	TSPBadgerFleetSolutions             TSP = "TSPBadgerFleetSolutions"
	TSPBigRoad                          TSP = "TSPBigRoad"
	TSPBlackBearELD                     TSP = "TSPBlackBearELD"
	TSPBlueArrow                        TSP = "TSPBlueArrow"
	TSPBlueInkTechnology                TSP = "TSPBlueInkTechnology"
	TSPBlueStarELD                      TSP = "TSPBlueStarELD"
	TSPCNELD                            TSP = "TSPCNELD"
	TSPCTELogELD                        TSP = "TSPCTELogELD"
	TSPCarrierHQ                        TSP = "TSPCarrierHQ"
	TSPClutchELD                        TSP = "TSPClutchELD"
	TSPColumbusELD                      TSP = "TSPColumbusELD"
	TSPCommandGPS                       TSP = "TSPCommandGPS"
	TSPCoretex                          TSP = "TSPCoretex"
	TSPCyntrXELDPlus                    TSP = "TSPCyntrXELDPlus"
	TSPDSGELOGS                         TSP = "TSPDSGELOGS"
	TSPDailyELD                         TSP = "TSPDailyELD"
	TSPDigitalELD                       TSP = "TSPDigitalELD"
	TSPDreamELD                         TSP = "TSPDreamELD"
	TSPDriveEDR                         TSP = "TSPDriveEDR"
	TSPELDFleet                         TSP = "TSPELDFleet"
	TSPELDMandatePlus                   TSP = "TSPELDMandatePlus"
	TSPELDMandatePro                    TSP = "TSPELDMandatePro"
	TSPELDOne                           TSP = "TSPELDOne"
	TSPELDRider                         TSP = "TSPELDRider"
	TSPELDTab                           TSP = "TSPELDTab"
	TSPELOG42                           TSP = "TSPELOG42"
	TSPEROAD                            TSP = "TSPEROAD"
	TSPEVOELD                           TSP = "TSPEVOELD"
	TSPEZELDSolutions                   TSP = "TSPEZELDSolutions"
	TSPEZFleet                          TSP = "TSPEZFleet"
	TSPEZLogz                           TSP = "TSPEZLogz"
	TSPEagleWireless                    TSP = "TSPEagleWireless"
	TSPEnVueTelematics                  TSP = "TSPEnVueTelematics"
	TSPExpressWayELD                    TSP = "TSPExpressWayELD"
	TSPFACTORELD                        TSP = "TSPFACTORELD"
	TSPFleetBossGPS                     TSP = "TSPFleetBossGPS"
	TSPFleetComplete                    TSP = "TSPFleetComplete"
	TSPFleetLocate21                    TSP = "TSPFleetLocate21"
	TSPFleetLocateAdvancedAndCompliance TSP = "TSPFleetLocateAdvancedAndCompliance"
	TSPFleetLocateELD                   TSP = "TSPFleetLocateELD"
	TSPFleetNavSystems                  TSP = "TSPFleetNavSystems"
	TSPFleetProfitCenter                TSP = "TSPFleetProfitCenter"
	TSPFleetSharp                       TSP = "TSPFleetSharp"
	TSPFleetistics                      TSP = "TSPFleetistics"
	TSPFleetmaster                      TSP = "TSPFleetmaster"
	TSPFleetmatics                      TSP = "TSPFleetmatics"
	TSPFlexport                         TSP = "TSPFlexport"
	TSPForceByMojio                     TSP = "TSPForceByMojio"
	TSPForwardThinkingELD               TSP = "TSPForwardThinkingELD"
	TSPGPSCommander                     TSP = "TSPGPSCommander"
	TSPGPSFleetFinder                   TSP = "TSPGPSFleetFinder"
	TSPGPSInsight                       TSP = "TSPGPSInsight"
	TSPGPSSolutions                     TSP = "TSPGPSSolutions"
	TSPGPSTab                           TSP = "TSPGPSTab"
	TSPGPSTrackingCanada                TSP = "TSPGPSTrackingCanada"
	TSPGPSTrackit                       TSP = "TSPGPSTrackit"
	TSPGarmin                           TSP = "TSPGarmin"
	TSPGeoforce                         TSP = "TSPGeoforce"
	TSPGeotab                           TSP = "TSPGeotab"
	TSPGlobalELD                        TSP = "TSPGlobalELD"
	TSPGlostone                         TSP = "TSPGlostone"
	TSPGoFleet                          TSP = "TSPGoFleet"
	TSPGoGPS                            TSP = "TSPGoGPS"
	TSPGoodDealGPS                      TSP = "TSPGoodDealGPS"
	TSPGorillaSafety                    TSP = "TSPGorillaSafety"
	TSPGrayboxSolutions                 TSP = "TSPGrayboxSolutions"
	TSPGridline                         TSP = "TSPGridline"
	TSPHighPointGPS                     TSP = "TSPHighPointGPS"
	TSPHorizonPathELD                   TSP = "TSPHorizonPathELD"
	TSPIDELD                            TSP = "TSPIDELD"
	TSPISAACInstruments                 TSP = "TSPISAACInstruments"
	TSPInTouchGPS                       TSP = "TSPInTouchGPS"
	TSPInsightMobileData                TSP = "TSPInsightMobileData"
	TSPIntellishift                     TSP = "TSPIntellishift"
	TSPIntouchELD                       TSP = "TSPIntouchELD"
	TSPIoTab                            TSP = "TSPIoTab"
	TSPJJKeller                         TSP = "TSPJJKeller"
	TSPKSKELD                           TSP = "TSPKSKELD"
	TSPKeepTruckin                      TSP = "TSPKeepTruckin"
	TSPKonexial                         TSP = "TSPKonexial"
	TSPLBTechnology                     TSP = "TSPLBTechnology"
	TSPLEGACYELD                        TSP = "TSPLEGACYELD"
	TSPLogPlusELD                       TSP = "TSPLogPlusELD"
	TSPLookTruckELD                     TSP = "TSPLookTruckELD"
	TSPLynx                             TSP = "TSPLynx"
	TSPLytXDriveCam                     TSP = "TSPLytXDriveCam"
	TSPMOONLIGHTELD                     TSP = "TSPMOONLIGHTELD"
	TSPMTELD                            TSP = "TSPMTELD"
	TSPMasterELD                        TSP = "TSPMasterELD"
	TSPMonarchGPS                       TSP = "TSPMonarchGPS"
	TSPMondo                            TSP = "TSPMondo"
	TSPMy20ELD                          TSP = "TSPMy20ELD"
	TSPNetradyneInc                     TSP = "TSPNetradyneInc"
	TSPNextraq                          TSP = "TSPNextraq"
	TSPOaneELD                          TSP = "TSPOaneELD"
	TSPOmnitracs                        TSP = "TSPOmnitracs"
	TSPOmnitracsXRS                     TSP = "TSPOmnitracsXRS"
	TSPOnTrakSolutions                  TSP = "TSPOnTrakSolutions"
	TSPOnePlusELD                       TSP = "TSPOnePlusELD"
	TSPOneStepGPS                       TSP = "TSPOneStepGPS"
	TSPOptimaELD                        TSP = "TSPOptimaELD"
	TSPOrbcomm                          TSP = "TSPOrbcomm"
	TSPOrion                            TSP = "TSPOrion"
	TSPOther                            TSP = "TSPOther"
	TSPPeopleNet                        TSP = "TSPPeopleNet"
	TSPPhoenixELD                       TSP = "TSPPhoenixELD"
	TSPPlatformScience                  TSP = "TSPPlatformScience"
	TSPPositrace                        TSP = "TSPPositrace"
	TSPPowerELD                         TSP = "TSPPowerELD"
	TSPPowerFleet                       TSP = "TSPPowerFleet"
	TSPPrePassELD                       TSP = "TSPPrePassELD"
	TSPProLogs                          TSP = "TSPProLogs"
	TSPQualityGPS                       TSP = "TSPQualityGPS"
	TSPRMJTechnologies                  TSP = "TSPRMJTechnologies"
	TSPRandMcNally                      TSP = "TSPRandMcNally"
	TSPRealELD                          TSP = "TSPRealELD"
	TSPReliableELD                      TSP = "TSPReliableELD"
	TSPRigbot                           TSP = "TSPRigbot"
	TSPRightTruckingELD                 TSP = "TSPRightTruckingELD"
	TSPRoadReadySolutions               TSP = "TSPRoadReadySolutions"
	TSPRoadStarELD                      TSP = "TSPRoadStarELD"
	TSPRushEnterpises                   TSP = "TSPRushEnterpises"
	TSPSFELD                            TSP = "TSPSFELD"
	TSPSMARTCHOICELOGSELD               TSP = "TSPSMARTCHOICELOGSELD"
	TSPSRELD                            TSP = "TSPSRELD"
	TSPSTATEELOGS                       TSP = "TSPSTATEELOGS"
	TSPSafetyVision                     TSP = "TSPSafetyVision"
	TSPSamsara                          TSP = "TSPSamsara"
	TSPSimpleELOG                       TSP = "TSPSimpleELOG"
	TSPSimpleTruckELD                   TSP = "TSPSimpleTruckELD"
	TSPSmartDrive                       TSP = "TSPSmartDrive"
	TSPSmartWitness                     TSP = "TSPSmartWitness"
	TSPStreetEagle                      TSP = "TSPStreetEagle"
	TSPSwiftELD                         TSP = "TSPSwiftELD"
	TSPTFMELD                           TSP = "TSPTFMELD"
	TSPTMELD                            TSP = "TSPTMELD"
	TSPTMobile                          TSP = "TSPTMobile"
	TSPTRUSTELD                         TSP = "TSPTRUSTELD"
	TSPTTELD                            TSP = "TSPTTELD"
	TSPTangerine                        TSP = "TSPTangerine"
	TSPTeletracNavman                   TSP = "TSPTeletracNavman"
	TSPTrackEnsureInc                   TSP = "TSPTrackEnsureInc"
	TSPTrackOnHOS                       TSP = "TSPTrackOnHOS"
	TSPTransflo                         TSP = "TSPTransflo"
	TSPTraxxisGPS                       TSP = "TSPTraxxisGPS"
	TSPTrendyELD                        TSP = "TSPTrendyELD"
	TSPTrimble                          TSP = "TSPTrimble"
	TSPTruPathSystems                   TSP = "TSPTruPathSystems"
	TSPTruckXELD                        TSP = "TSPTruckXELD"
	TSPTruckerPathELDPro                TSP = "TSPTruckerPathELDPro"
	TSPUnityELD                         TSP = "TSPUnityELD"
	TSPVLogELD                          TSP = "TSPVLogELD"
	TSPVTS                              TSP = "TSPVTS"
	TSPVerizonConnect                   TSP = "TSPVerizonConnect"
	TSPVerizonConnectFleet              TSP = "TSPVerizonConnectFleet"
	TSPVerizonConnectReveal             TSP = "TSPVerizonConnectReveal"
	TSPVertrax                          TSP = "TSPVertrax"
	TSPVistaELD                         TSP = "TSPVistaELD"
	TSPWebfleet                         TSP = "TSPWebfleet"
	TSPWorldTruckingELD                 TSP = "TSPWorldTruckingELD"
	TSPXELD                             TSP = "TSPXELD"
	TSPZELD                             TSP = "TSPZELD"
	TSPZenduit                          TSP = "TSPZenduit"
	TSPZippyELD                         TSP = "TSPZippyELD"
	TSPZonar                            TSP = "TSPZonar"
	TSPeasiTrack                        TSP = "TSPeasiTrack"
	TspContiGO                          TSP = "TspContiGO"
	TspMountainEld                      TSP = "TspMountainEld"
	TspProRideEld                       TSP = "TspProRideEld"
)

// Defines values for TelematicsConnectionStatus.
const (
	TelematicsConnectionStatusAuthorized         TelematicsConnectionStatus = "TelematicsConnectionStatusAuthorized"
	TelematicsConnectionStatusConnected          TelematicsConnectionStatus = "TelematicsConnectionStatusConnected"
	TelematicsConnectionStatusDisconnected       TelematicsConnectionStatus = "TelematicsConnectionStatusDisconnected"
	TelematicsConnectionStatusInitiated          TelematicsConnectionStatus = "TelematicsConnectionStatusInitiated"
	TelematicsConnectionStatusInternallyDisabled TelematicsConnectionStatus = "TelematicsConnectionStatusInternallyDisabled"
	TelematicsConnectionStatusPermanentlyDeleted TelematicsConnectionStatus = "TelematicsConnectionStatusPermanentlyDeleted"
	TelematicsConnectionStatusPermanentlyLost    TelematicsConnectionStatus = "TelematicsConnectionStatusPermanentlyLost"
	TelematicsConnectionStatusRejected           TelematicsConnectionStatus = "TelematicsConnectionStatusRejected"
	TelematicsConnectionStatusUnknown            TelematicsConnectionStatus = "TelematicsConnectionStatusUnknown"
)

// Defines values for TelematicsConsentKind.
const (
	TelematicsConsentKindApiKey                TelematicsConsentKind = "TelematicsConsentKindApiKey"
	TelematicsConsentKindBasicAuth             TelematicsConsentKind = "TelematicsConsentKindBasicAuth"
	TelematicsConsentKindBasicAuthWithDatabase TelematicsConsentKind = "TelematicsConsentKindBasicAuthWithDatabase"
	TelematicsConsentKindBasicAuthWithLoginId  TelematicsConsentKind = "TelematicsConsentKindBasicAuthWithLoginId"
	TelematicsConsentKindOAuth                 TelematicsConsentKind = "TelematicsConsentKindOAuth"
	TelematicsConsentKindSpeedgauge            TelematicsConsentKind = "TelematicsConsentKindSpeedgauge"
	TelematicsConsentKindSpeedgaugeGeotab      TelematicsConsentKind = "TelematicsConsentKindSpeedgaugeGeotab"
)

// Defines values for TelematicsFleetInfoResponseValidationErrors.
const (
	InvalidDOTNumber          TelematicsFleetInfoResponseValidationErrors = "InvalidDOTNumber"
	InvalidFleetName          TelematicsFleetInfoResponseValidationErrors = "InvalidFleetName"
	MissingTelematicsVehicles TelematicsFleetInfoResponseValidationErrors = "MissingTelematicsVehicles"
)

// Defines values for TypeOfTerminal.
const (
	Dock     TypeOfTerminal = "Dock"
	DropLot  TypeOfTerminal = "DropLot"
	Office   TypeOfTerminal = "Office"
	Terminal TypeOfTerminal = "Terminal"
)

// AdditionalCommoditiyRecord defines model for AdditionalCommoditiyRecord.
type AdditionalCommoditiyRecord struct {
	Commodities       string `json:"commodities"`
	PercentageOfHauls int32  `json:"percentageOfHauls"`
}

// AdditionalIncumbentInfo defines model for AdditionalIncumbentInfo.
type AdditionalIncumbentInfo struct {
	IsALIncumbent bool `json:"isALIncumbent"`
}

// AdditionalInfoCoverageMetadata defines model for AdditionalInfoCoverageMetadata.
type AdditionalInfoCoverageMetadata struct {
	AdditionalIncumbentInfo AdditionalIncumbentInfo `json:"additionalIncumbentInfo"`
}

// AdditionalInformationCommodity defines model for AdditionalInformationCommodity.
type AdditionalInformationCommodity string

// AdditionalInformationForm defines model for AdditionalInformationForm.
type AdditionalInformationForm struct {
	AdditionalAgentFiles  *[]externalRef0.FileMetadata      `json:"additionalAgentFiles,omitempty"`
	Commodities           *[]AdditionalInformationCommodity `json:"commodities,omitempty"`
	CommoditiesComment    *string                           `json:"commoditiesComment,omitempty"`
	CoverageMetadata      *AdditionalInfoCoverageMetadata   `json:"coverageMetadata,omitempty"`
	DriverList            *DriverList                       `json:"driverList,omitempty"`
	LargeLossComment      *string                           `json:"largeLossComment,omitempty"`
	LossRunFiles          *[]externalRef0.FileMetadata      `json:"lossRunFiles,omitempty"`
	NumOwnerOperatorUnits *int                              `json:"numOwnerOperatorUnits,omitempty"`
	OverallComment        *string                           `json:"overallComment,omitempty"`
	PercentageOfSubhaul   *float32                          `json:"percentageOfSubhaul,omitempty"`
	TargetPrices          *[]TargetPrice                    `json:"targetPrices,omitempty"`
}

// AgencyBDMapping defines model for AgencyBDMapping.
type AgencyBDMapping struct {
	Agency      externalRef0.Agency      `json:"agency"`
	Id          openapi_types.UUID       `json:"id"`
	ProgramType externalRef0.ProgramType `json:"programType"`
	User        externalRef0.User        `json:"user"`
	UserOptions []externalRef0.User      `json:"userOptions"`
}

// AgencyBDMappingRequest defines model for AgencyBDMappingRequest.
type AgencyBDMappingRequest struct {
	AgencyId    openapi_types.UUID       `json:"agencyId"`
	ProgramType externalRef0.ProgramType `json:"programType"`
	UserId      openapi_types.UUID       `json:"userId"`
}

// AgencyBDMappingsResponse defines model for AgencyBDMappingsResponse.
type AgencyBDMappingsResponse struct {
	AgencyBDMappings []AgencyBDMapping `json:"agencyBDMappings"`
}

// ApiKeyAuthConnData defines model for ApiKeyAuthConnData.
type ApiKeyAuthConnData struct {
	ApiKey string `json:"apiKey"`
}

// AppetiteCheckRule defines model for AppetiteCheckRule.
type AppetiteCheckRule string

// AppetiteCheckRuleResponse defines model for AppetiteCheckRuleResponse.
type AppetiteCheckRuleResponse struct {
	Details map[string]interface{} `json:"details"`
	Rule    AppetiteCheckRule      `json:"rule"`
}

// AppetiteForm defines model for AppetiteForm.
type AppetiteForm struct {
	CompanyName            string             `json:"companyName"`
	DotNumber              int64              `json:"dotNumber"`
	EffectiveDate          openapi_types.Date `json:"effectiveDate"`
	HasUndesiredOperations bool               `json:"hasUndesiredOperations"`
	MarketerId             *string            `json:"marketerId,omitempty"`
	NumPowerUnits          int                `json:"numPowerUnits"`
	ProducerId             *string            `json:"producerId,omitempty"`
}

// ApplicationBasicInfo defines model for ApplicationBasicInfo.
type ApplicationBasicInfo struct {
	AgencyID                string             `json:"AgencyID"`
	CompanyName             string             `json:"CompanyName"`
	CreatedBy               string             `json:"CreatedBy"`
	EffectiveDateOfCoverage openapi_types.Date `json:"EffectiveDateOfCoverage"`
	InsuredEmail            string             `json:"InsuredEmail"`
	InsuredName             string             `json:"InsuredName"`
	ProducerID              *string            `json:"ProducerID,omitempty"`
}

// ApplicationBasicInfoForm defines model for ApplicationBasicInfoForm.
type ApplicationBasicInfoForm struct {
	AgencyID                *string              `json:"AgencyID,omitempty"`
	CompanyName             *string              `json:"CompanyName,omitempty"`
	CreatedBy               *string              `json:"CreatedBy,omitempty"`
	EffectiveDateOfCoverage *openapi_types.Date  `json:"EffectiveDateOfCoverage,omitempty"`
	InsuredEmail            *openapi_types.Email `json:"InsuredEmail,omitempty"`
	InsuredName             *string              `json:"InsuredName,omitempty"`
	ProducerID              *string              `json:"ProducerID,omitempty"`
}

// ApplicationDetail defines model for ApplicationDetail.
type ApplicationDetail struct {
	AdditionalInfoForm *AdditionalInformationForm `json:"additionalInfoForm,omitempty"`
	IndicationForm     *IndicationForm            `json:"indicationForm,omitempty"`
	SelectedIndication *IndicationOption          `json:"selectedIndication,omitempty"`
	Summary            ApplicationSummary         `json:"summary"`
	TelematicsInfo     *TelematicsInfo            `json:"telematicsInfo,omitempty"`
}

// ApplicationListResponse defines model for ApplicationListResponse.
type ApplicationListResponse struct {
	AppListItems []ApplicationListResponseItem `json:"appListItems"`
	Apps         []ApplicationSummary          `json:"apps"`
	Cursor       *string                       `json:"cursor,omitempty"`
	TotalCount   int                           `json:"totalCount"`
}

// ApplicationListResponseItem defines model for ApplicationListResponseItem.
type ApplicationListResponseItem struct {
	ApplicationID   string             `json:"applicationID"`
	CompanyName     string             `json:"companyName"`
	EffectiveDate   openapi_types.Date `json:"effectiveDate"`
	ProducerName    string             `json:"producerName"`
	RenewalMetadata *RenewalMetadata   `json:"renewalMetadata,omitempty"`
	ShortID         string             `json:"shortID"`
	State           ApplicationState   `json:"state"`
}

// ApplicationPublicInfo defines model for ApplicationPublicInfo.
type ApplicationPublicInfo struct {
	AgencyName    string               `json:"agencyName"`
	CompanyName   string               `json:"companyName"`
	DotNumber     int64                `json:"dotNumber"`
	InsuredEmail  *openapi_types.Email `json:"insuredEmail,omitempty"`
	InsuredName   *string              `json:"insuredName,omitempty"`
	ProducerEmail openapi_types.Email  `json:"producerEmail"`
	ProducerName  string               `json:"producerName"`
}

// ApplicationState defines model for ApplicationState.
type ApplicationState string

// ApplicationSummary defines model for ApplicationSummary.
type ApplicationSummary struct {
	AgencyID               string                    `json:"agencyID"`
	ApplicationID          string                    `json:"applicationID"`
	BdEmail                *openapi_types.Email      `json:"bdEmail,omitempty"`
	BdName                 *string                   `json:"bdName,omitempty"`
	BindableSubmissionInfo *BindableSubmissionInfo   `json:"bindableSubmissionInfo,omitempty"`
	ClearanceStatus        *PotentialClearanceStatus `json:"clearanceStatus,omitempty"`
	CompanyName            string                    `json:"companyName"`
	CreatedAt              time.Time                 `json:"createdAt"`
	CreatedBy              string                    `json:"createdBy"`
	DotNumber              int64                     `json:"dotNumber"`
	EffectiveDate          openapi_types.Date        `json:"effectiveDate"`
	HidePrice              *bool                     `json:"hidePrice,omitempty"`

	// MtcVersion MTC Rating Model Version - None (Not Applicable), V1 (Sentry Model) or V2 (GWCC Model)
	MtcVersion           *externalRef0.MTCVersion                `json:"mtcVersion,omitempty"`
	PackageType          *IndicationOptionTag                    `json:"packageType,omitempty"`
	ProducerID           *string                                 `json:"producerID,omitempty"`
	ProducerName         string                                  `json:"producerName"`
	RenewalMetadata      *RenewalMetadata                        `json:"renewalMetadata,omitempty"`
	ShortID              string                                  `json:"shortID"`
	State                ApplicationState                        `json:"state"`
	TelematicsDataStatus *ApplicationSummaryTelematicsDataStatus `json:"telematicsDataStatus,omitempty"`
	UnderwriterEmail     openapi_types.Email                     `json:"underwriterEmail"`
	UnderwriterName      string                                  `json:"underwriterName"`
	UpdatedAt            time.Time                               `json:"updatedAt"`
}

// ApplicationSummaryTelematicsDataStatus defines model for ApplicationSummaryTelematicsDataStatus.
type ApplicationSummaryTelematicsDataStatus string

// ApplicationTab defines model for ApplicationTab.
type ApplicationTab string

// AvailableProducersResponse defines model for AvailableProducersResponse.
type AvailableProducersResponse = []Producer

// BasicAuthConnData defines model for BasicAuthConnData.
type BasicAuthConnData struct {
	Password string `json:"password"`
	Username string `json:"username"`
}

// BasicAuthWithDatabaseConnData defines model for BasicAuthWithDatabaseConnData.
type BasicAuthWithDatabaseConnData struct {
	Database string `json:"database"`
	Password string `json:"password"`
	Username string `json:"username"`
}

// BasicAuthWithLoginIdConnData defines model for BasicAuthWithLoginIdConnData.
type BasicAuthWithLoginIdConnData struct {
	LoginId  string `json:"loginId"`
	Password string `json:"password"`
	Username string `json:"username"`
}

// BillingContactRole defines model for BillingContactRole.
type BillingContactRole string

// BindableSubmissionInfo defines model for BindableSubmissionInfo.
type BindableSubmissionInfo struct {
	AncillaryCoveragesRequired *[]CoverageRecord `json:"ancillaryCoveragesRequired,omitempty"`
	CoveragesRequired          *[]CoverageRecord `json:"coveragesRequired,omitempty"`
	SubmissionId               string            `json:"submissionId"`
}

// ClassesAndCommoditiesForm defines model for ClassesAndCommoditiesForm.
type ClassesAndCommoditiesForm struct {
	CommodityDistribution      *CommodityDistribution               `json:"commodityDistribution,omitempty"`
	OperatingClassDistribution OperatingClassDistribution           `json:"operatingClassDistribution"`
	PrimaryCategory            *externalRef0.CommodityCategory      `json:"primaryCategory,omitempty"`
	PrimaryCommodity           *externalRef0.PrimaryCommodityHauled `json:"primaryCommodity,omitempty"`
	PrimaryOperatingClass      externalRef0.OperatingClass          `json:"primaryOperatingClass"`
}

// CommodityDistribution defines model for CommodityDistribution.
type CommodityDistribution struct {
	AdditionalCommodities *AdditionalCommoditiyRecord `json:"additionalCommodities,omitempty"`
	Commodities           []WeightedCommodityRecord   `json:"commodities"`
}

// Contact defines model for Contact.
type Contact struct {
	BillingContactRole *BillingContactRole `json:"billingContactRole,omitempty"`
	Email              string              `json:"email"`
	FirstName          string              `json:"firstName"`
	LastName           string              `json:"lastName"`
	OriginalSignatory  *bool               `json:"originalSignatory,omitempty"`
	PhoneNumber        *string             `json:"phoneNumber,omitempty"`
	PocType            POCType             `json:"pocType"`
	SafetyContactRole  *SafetyContactRole  `json:"safetyContactRole,omitempty"`
}

// ContactRecordsUpdate defines model for ContactRecordsUpdate.
type ContactRecordsUpdate struct {
	// Contacts List of Contacts and their details
	Contacts []Contact   `json:"contacts"`
	Type     ContactType `json:"type"`
}

// ContactRecordsUpdateResponse defines model for ContactRecordsUpdateResponse.
type ContactRecordsUpdateResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// ContactType defines model for ContactType.
type ContactType string

// CoverageDetails defines model for CoverageDetails.
type CoverageDetails struct {
	ApprovedCoverages                *[]CoverageRecord                 `json:"approvedCoverages,omitempty"`
	CoveragesWithCombinedDeductibles *[]externalRef0.CombinedCoverages `json:"coveragesWithCombinedDeductibles,omitempty"`
	RejectedCoverages                *[]RejectedCoverage               `json:"rejectedCoverages,omitempty"`
}

// CoverageRecord defines model for CoverageRecord.
type CoverageRecord struct {
	// TIVPercentage What percentage of the total value of the equipment is insured - mostly relates to Physical Damage. For example if a fleet is valuated at 100k and the total premium is 30k, TIV% = 30%.
	TIVPercentage          *float32                  `json:"TIVPercentage,omitempty"`
	CoverageType           externalRef0.CoverageType `json:"coverageType"`
	Deductible             *int32                    `json:"deductible,omitempty"`
	Limit                  *int32                    `json:"limit,omitempty"`
	Premium                *int32                    `json:"premium,omitempty"`
	PremiumPerHundredMiles *float32                  `json:"premiumPerHundredMiles,omitempty"`
	PremiumPerUnit         *int32                    `json:"premiumPerUnit,omitempty"`
	SymbolsAndDefinitions  *[]SymbolAndDefinition    `json:"symbolsAndDefinitions,omitempty"`
}

// CoverageVariablesOptions defines model for CoverageVariablesOptions.
type CoverageVariablesOptions struct {
	Coverage    externalRef0.CoverageType                    `json:"coverage"`
	Deductibles *externalRef0.CoverageVariablesOptionNumeric `json:"deductibles,omitempty"`
	Limits      *externalRef0.CoverageVariablesOptionNumeric `json:"limits,omitempty"`
}

// CreatedApplicationResponse defines model for CreatedApplicationResponse.
type CreatedApplicationResponse struct {
	ApplicationID string `json:"applicationID"`
	Flags         *Flags `json:"flags,omitempty"`
}

// CreditReportStatus defines model for CreditReportStatus.
type CreditReportStatus string

// CurrentDeductibleRecord defines model for CurrentDeductibleRecord.
type CurrentDeductibleRecord struct {
	Amount   int32                     `json:"amount"`
	Coverage externalRef0.CoverageType `json:"coverage"`
}

// DeclineAppForm defines model for DeclineAppForm.
type DeclineAppForm struct {
	Description string `json:"description"`
}

// DeductibleRecord defines model for DeductibleRecord.
type DeductibleRecord struct {
	Amounts  []int32                   `json:"amounts"`
	Coverage externalRef0.CoverageType `json:"coverage"`
}

// DocumentSummary defines model for DocumentSummary.
type DocumentSummary struct {
	SignaturePacketHandleId    string  `json:"signaturePacketHandleId"`
	SignaturePacketZipHandleId *string `json:"signaturePacketZipHandleId,omitempty"`
}

// DotPrefillResponse defines model for DotPrefillResponse.
type DotPrefillResponse struct {
	Address       string             `json:"address"`
	EffectiveDate openapi_types.Date `json:"effectiveDate"`

	// IsStateActive Whether nirvana is operating in the state or not (based on the state of the address associated to the DOT) - used for appetite check
	IsStateActive              *bool  `json:"isStateActive,omitempty"`
	LastReportedMileage        *int64 `json:"last_reported_mileage,omitempty"`
	LastReportedPowerUnitCount int64  `json:"last_reported_power_unit_count"`
	Name                       string `json:"name"`

	// UsState Two character short code for the US state the driver is licensed in.
	UsState externalRef0.USState `json:"usState"`
}

// DriverList defines model for DriverList.
type DriverList struct {
	Drivers          []DriverListRecord             `json:"drivers"`
	FlatfileMetadata *externalRef0.FlatfileMetadata `json:"flatfileMetadata,omitempty"`
	ImplerMetadata   *externalRef0.ImplerMetadata   `json:"implerMetadata,omitempty"`
}

// DriverListRecord defines model for DriverListRecord.
type DriverListRecord struct {
	DateHired   openapi_types.Date  `json:"dateHired"`
	DateOfBirth *openapi_types.Date `json:"dateOfBirth,omitempty"`
	DlNumber    string              `json:"dlNumber"`
	FirstName   *string             `json:"firstName,omitempty"`
	LastName    *string             `json:"lastName,omitempty"`

	// UsState Two character short code for the US state the driver is licensed in.
	UsState           externalRef0.USState `json:"usState"`
	YearsOfExperience *float32             `json:"yearsOfExperience,omitempty"`
}

// DuplicateApplicationDetails defines model for DuplicateApplicationDetails.
type DuplicateApplicationDetails struct {
	ExistingApplicationId     string `json:"existingApplicationId"`
	ExistingApplicationStatus string `json:"existingApplicationStatus"`
	IsRenewal                 bool   `json:"isRenewal"`
}

// Email defines model for Email.
type Email struct {
	Email openapi_types.Email `json:"email"`
	Name  *string             `json:"name,omitempty"`
}

// EquipmentList defines model for EquipmentList.
type EquipmentList struct {
	FileHandle       *string                        `json:"fileHandle,omitempty"`
	FlatfileMetadata *externalRef0.FlatfileMetadata `json:"flatfileMetadata,omitempty"`
	ImplerMetadata   *externalRef0.ImplerMetadata   `json:"implerMetadata,omitempty"`
	Info             []EquipmentListRecord          `json:"info"`
}

// EquipmentListRecord defines model for EquipmentListRecord.
type EquipmentListRecord struct {
	StatedValue *int32 `json:"statedValue,omitempty"`
	Vin         string `json:"vin"`
}

// Flags defines model for Flags.
type Flags struct {
	ClearanceConflict bool `json:"clearanceConflict"`
}

// FleetInfo defines model for FleetInfo.
type FleetInfo struct {
	DotNumber *int64              `json:"dotNumber,omitempty"`
	FleetName *string             `json:"fleetName,omitempty"`
	Vehicles  *[]FleetInfoVehicle `json:"vehicles,omitempty"`
}

// FleetInfoVehicle defines model for FleetInfoVehicle.
type FleetInfoVehicle struct {
	Name *string `json:"name,omitempty"`
	Vin  *string `json:"vin,omitempty"`
}

// GenerateSafetyReportURIResponse defines model for GenerateSafetyReportURIResponse.
type GenerateSafetyReportURIResponse struct {
	Url string `json:"url"`
}

// GeoTabConnData defines model for GeoTabConnData.
type GeoTabConnData struct {
	DbName string              `json:"dbName"`
	Email  openapi_types.Email `json:"email"`
	Name   string              `json:"name"`
}

// GetContactsResponse defines model for GetContactsResponse.
type GetContactsResponse struct {
	// Billing List of Contacts and their details
	Billing []Contact `json:"billing"`

	// Safety List of Contacts and their details
	Safety []Contact `json:"safety"`
}

// GetCreditReportStatusResponse defines model for GetCreditReportStatusResponse.
type GetCreditReportStatusResponse struct {
	Status CreditReportStatus `json:"status"`
}

// GetPoliciesResponse List of policies and its information
type GetPoliciesResponse = []PolicyRecord

// IndicationForm defines model for IndicationForm.
type IndicationForm struct {
	ClassesAndCommoditiesForm *ClassesAndCommoditiesForm `json:"classesAndCommoditiesForm,omitempty"`
	LossRunSummaryForm        *LossRunSummaryForm        `json:"lossRunSummaryForm,omitempty"`
	OperationsForm            *OperationsForm            `json:"operationsForm,omitempty"`
}

// IndicationOption defines model for IndicationOption.
type IndicationOption struct {
	TIV             int32               `json:"TIV"`
	Coverages       []CoverageRecord    `json:"coverages"`
	CreatedAt       *openapi_types.Date `json:"createdAt,omitempty"`
	Id              string              `json:"id"`
	IsRecommended   bool                `json:"isRecommended"`
	OptionTag       IndicationOptionTag `json:"optionTag"`
	TotalMiles      int32               `json:"totalMiles"`
	TotalPowerUnits int32               `json:"totalPowerUnits"`
	TotalPremium    int32               `json:"totalPremium"`
}

// IndicationOptionTag defines model for IndicationOptionTag.
type IndicationOptionTag string

// IndicationOptions defines model for IndicationOptions.
type IndicationOptions struct {
	CoverageVariablesOptions         []CoverageVariablesOptions                     `json:"coverageVariablesOptions"`
	CoveragesWithCombinedDeductibles []externalRef0.CombinedCoverages               `json:"coveragesWithCombinedDeductibles"`
	CurrentDeductibles               []CurrentDeductibleRecord                      `json:"currentDeductibles"`
	DeductiblesOptions               []DeductibleRecord                             `json:"deductiblesOptions"`
	LimitsOptions                    *[]externalRef0.CoverageVariablesOptionNumeric `json:"limitsOptions,omitempty"`
	Options                          []IndicationOption                             `json:"options"`
}

// LossRunSummaryForm defines model for LossRunSummaryForm.
type LossRunSummaryForm = []LossRunSummaryPerCoverage

// LossRunSummaryPerCoverage defines model for LossRunSummaryPerCoverage.
type LossRunSummaryPerCoverage struct {
	CoverageType   externalRef0.CoverageType `json:"coverageType"`
	LossRunSummary []LossRunSummaryRecord    `json:"lossRunSummary"`
}

// LossRunSummaryRecord defines model for LossRunSummaryRecord.
type LossRunSummaryRecord struct {
	IsNirvanaPeriod       *bool              `json:"isNirvanaPeriod,omitempty"`
	LossIncurred          int32              `json:"lossIncurred"`
	NumberOfClaims        int32              `json:"numberOfClaims"`
	NumberOfPowerUnits    int32              `json:"numberOfPowerUnits"`
	PolicyPeriodEndDate   openapi_types.Date `json:"policyPeriodEndDate"`
	PolicyPeriodStartDate openapi_types.Date `json:"policyPeriodStartDate"`
}

// MileageRadiusBucket defines model for MileageRadiusBucket.
type MileageRadiusBucket string

// MileageRadiusRecord defines model for MileageRadiusRecord.
type MileageRadiusRecord struct {
	MileageRadiusBucket MileageRadiusBucket `json:"mileageRadiusBucket"`
	PercentageOfFleet   int32               `json:"percentageOfFleet"`
}

// OAuthConnection defines model for OAuthConnection.
type OAuthConnection = bool

// OperatingClassDistribution defines model for OperatingClassDistribution.
type OperatingClassDistribution = []OperatingClassDistributionRecord

// OperatingClassDistributionRecord defines model for OperatingClassDistributionRecord.
type OperatingClassDistributionRecord struct {
	OperatingClass    externalRef0.OperatingClass `json:"operatingClass"`
	PercentageOfFleet int32                       `json:"percentageOfFleet"`
}

// OperationsForm defines model for OperationsForm.
type OperationsForm struct {
	// AncillaryCoveragesRequired List of ancillary coverages required (This is combined with the ones that already attach during indication, if both have the same coverage type, the one in this list will be the one that will be used)
	AncillaryCoveragesRequired       *[]CoverageRecord                 `json:"ancillaryCoveragesRequired,omitempty"`
	CoveragesRequired                *[]CoverageRecord                 `json:"coveragesRequired,omitempty"`
	CoveragesWithCombinedDeductibles *[]externalRef0.CombinedCoverages `json:"coveragesWithCombinedDeductibles,omitempty"`
	EquipmentList                    EquipmentList                     `json:"equipmentList"`
	NumberOfPowerUnits               int                               `json:"numberOfPowerUnits"`
	ProducerId                       string                            `json:"producerId"`
	ProjectedMileage                 int                               `json:"projectedMileage"`
	RadiusOfOperation                []MileageRadiusRecord             `json:"radiusOfOperation"`
	RetailerInfo                     *RetailerInfo                     `json:"retailerInfo,omitempty"`
	TerminalLocations                *[]TerminalLocation               `json:"terminalLocations,omitempty"`
}

// POCType defines model for POCType.
type POCType string

// PatchModelVersionInLatestPendingAppReviewForm defines model for PatchModelVersionInLatestPendingAppReviewForm.
type PatchModelVersionInLatestPendingAppReviewForm struct {
	Version string `json:"version"`
}

// PatchModelVersionInLatestPendingAppReviewResponse defines model for PatchModelVersionInLatestPendingAppReviewResponse.
type PatchModelVersionInLatestPendingAppReviewResponse struct {
	Message string `json:"message"`
	Updated bool   `json:"updated"`
}

// PatchRenewalApplicationForm defines model for PatchRenewalApplicationForm.
type PatchRenewalApplicationForm struct {
	RenewalAdditionalForm            *RenewalAdditionalForm            `json:"renewalAdditionalForm,omitempty"`
	RenewalClassesAndCommoditiesForm *RenewalClassesAndCommoditiesForm `json:"renewalClassesAndCommoditiesForm,omitempty"`
	RenewalCoverageForm              *RenewalCoverageForm              `json:"renewalCoverageForm,omitempty"`
	RenewalFilesForm                 *RenewalFilesForm                 `json:"renewalFilesForm,omitempty"`
	RenewalLossForm                  *RenewalLossForm                  `json:"renewalLossForm,omitempty"`
	RenewalOperationsForm            *RenewalOperationsForm            `json:"renewalOperationsForm,omitempty"`
	SectionCompletionMap             *SectionCompletionMap             `json:"sectionCompletionMap,omitempty"`
}

// PaymentOption defines model for PaymentOption.
type PaymentOption string

// PaymentOptionRecord defines model for PaymentOptionRecord.
type PaymentOptionRecord struct {
	Collateral              *int               `json:"collateral,omitempty"`
	Description             string             `json:"description"`
	EstimatedMonthlyPayment *int32             `json:"estimatedMonthlyPayment,omitempty"`
	Name                    PaymentOption      `json:"name"`
	PaymentDueDate          openapi_types.Date `json:"paymentDueDate"`
	ToBind                  int32              `json:"toBind"`
	Type                    PaymentOptionType  `json:"type"`
}

// PaymentOptionType defines model for PaymentOptionType.
type PaymentOptionType string

// PaymentOptions defines model for PaymentOptions.
type PaymentOptions = []PaymentOptionRecord

// PhoneNumber defines model for PhoneNumber.
type PhoneNumber struct {
	Name        *string `json:"name,omitempty"`
	PhoneNumber string  `json:"phoneNumber"`
}

// PolicyChangeResponse defines model for PolicyChangeResponse.
type PolicyChangeResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// PolicyCommissionRecordUpdate defines model for PolicyCommissionRecordUpdate.
type PolicyCommissionRecordUpdate struct {
	CommissionRate   float32 `json:"commissionRate"`
	PolicyIdentifier string  `json:"policyIdentifier"`
}

// PolicyDetails defines model for PolicyDetails.
type PolicyDetails struct {
	PolicyId     string `json:"policyId"`
	PolicyNumber string `json:"policyNumber"`
}

// PolicyRecord defines model for PolicyRecord.
type PolicyRecord struct {
	AgencyName           string                    `json:"agencyName"`
	ApplicationID        string                    `json:"applicationID"`
	DotNumber            int64                     `json:"dotNumber"`
	InsuredName          string                    `json:"insuredName"`
	PolicyExpirationDate openapi_types.Date        `json:"policyExpirationDate"`
	PolicyID             string                    `json:"policyID"`
	PolicyIdentifier     string                    `json:"policyIdentifier"`
	PolicyIssuanceDate   openapi_types.Date        `json:"policyIssuanceDate"`
	PolicyNumber         string                    `json:"policyNumber"`
	ProducerName         string                    `json:"producerName"`
	Status               externalRef0.PolicyStatus `json:"status"`
}

// PostClearanceProtectedApplicationsRequestBody defines model for PostClearanceProtectedApplicationsRequestBody.
type PostClearanceProtectedApplicationsRequestBody struct {
	Applications []PostClearanceProtectedApplicationsRequestBodyItem `json:"applications"`
}

// PostClearanceProtectedApplicationsRequestBodyItem defines model for PostClearanceProtectedApplicationsRequestBodyItem.
type PostClearanceProtectedApplicationsRequestBodyItem struct {
	DotNumber              int64               `json:"dotNumber"`
	EffectiveDate          openapi_types.Date  `json:"effectiveDate"`
	HasUndesiredOperations bool                `json:"hasUndesiredOperations"`
	NumberOfPowerUnits     int64               `json:"numberOfPowerUnits"`
	ProducerEmail          openapi_types.Email `json:"producerEmail"`
}

// PotentialClearanceDetails defines model for PotentialClearanceDetails.
type PotentialClearanceDetails struct {
	IsSameAgency bool                     `json:"isSameAgency"`
	Status       PotentialClearanceStatus `json:"status"`
}

// PotentialClearanceStatus defines model for PotentialClearanceStatus.
type PotentialClearanceStatus string

// PotentialRenewalDetails defines model for PotentialRenewalDetails.
type PotentialRenewalDetails struct {
	ApplicationExpiryDate openapi_types.Date `json:"applicationExpiryDate"`
}

// PreApplicationCheckRule defines model for PreApplicationCheckRule.
type PreApplicationCheckRule string

// PreApplicationChecksRuleRequest defines model for PreApplicationChecksRuleRequest.
type PreApplicationChecksRuleRequest struct {
	DotNumber     int                `json:"dotNumber"`
	EffectiveDate openapi_types.Date `json:"effectiveDate"`
	ProducerId    openapi_types.UUID `json:"producerId"`
}

// PreApplicationChecksRuleResponse defines model for PreApplicationChecksRuleResponse.
type PreApplicationChecksRuleResponse struct {
	Details PreApplicationChecksRuleResponse_Details `json:"details"`
	Rule    PreApplicationCheckRule                  `json:"rule"`
}

// PreApplicationChecksRuleResponse_Details defines model for PreApplicationChecksRuleResponse.Details.
type PreApplicationChecksRuleResponse_Details struct {
	union json.RawMessage
}

// PremiumDetails defines model for PremiumDetails.
type PremiumDetails struct {
	FlatCharges              int32  `json:"flatCharges"`
	PreDiscountTotalPremium  int32  `json:"preDiscountTotalPremium"`
	SafetyDiscountPercentage int32  `json:"safetyDiscountPercentage"`
	SafetyDiscountPremium    int32  `json:"safetyDiscountPremium"`
	SubtotalPremium          int32  `json:"subtotalPremium"`
	Tiv                      int32  `json:"tiv"`
	TotalMiles               int32  `json:"totalMiles"`
	TotalPowerUnits          int32  `json:"totalPowerUnits"`
	TotalPremium             int32  `json:"totalPremium"`
	TotalStampingFee         *int32 `json:"totalStampingFee,omitempty"`
	TotalSurchargePremium    *int32 `json:"totalSurchargePremium,omitempty"`
	TotalSurplusLinesTax     *int32 `json:"totalSurplusLinesTax,omitempty"`
}

// Producer defines model for Producer.
type Producer struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

// QuoteData defines model for QuoteData.
type QuoteData struct {
	FieldsChangedByUW UWChangesToApp    `json:"fieldsChangedByUW"`
	FinalAppDetails   ApplicationDetail `json:"finalAppDetails"`
	UserAppDetails    ApplicationDetail `json:"userAppDetails"`
}

// QuoteDetails defines model for QuoteDetails.
type QuoteDetails struct {
	AppSummary      ApplicationSummary        `json:"appSummary"`
	CoverageDetails CoverageDetails           `json:"coverageDetails"`
	DocumentSummary DocumentSummary           `json:"documentSummary"`
	FormsDetails    externalRef1.FormsDetails `json:"formsDetails"`
	PaymentOptions  PaymentOptions            `json:"paymentOptions"`
	PremiumDetails  PremiumDetails            `json:"premiumDetails"`
	QuoteSummary    QuoteSummary              `json:"quoteSummary"`
}

// QuoteSummary defines model for QuoteSummary.
type QuoteSummary struct {
	EffectiveDate openapi_types.Date  `json:"effectiveDate"`
	OptionTag     IndicationOptionTag `json:"optionTag"`
	TotalPremium  int32               `json:"totalPremium"`
	UpdatedAt     openapi_types.Date  `json:"updatedAt"`
}

// RejectedApplicationResponse defines model for RejectedApplicationResponse.
type RejectedApplicationResponse struct {
	DeclinedRules []AppetiteCheckRuleResponse `json:"declinedRules"`
}

// RejectedCoverage defines model for RejectedCoverage.
type RejectedCoverage struct {
	CoverageType externalRef0.CoverageType `json:"coverageType"`
	Reason       string                    `json:"reason"`
}

// RenewalAdditionalForm defines model for RenewalAdditionalForm.
type RenewalAdditionalForm struct {
	DriverList            DriverList                  `json:"driverList"`
	IftaFiles             []externalRef0.FileMetadata `json:"iftaFiles"`
	LargeLossComment      string                      `json:"largeLossComment"`
	LossRunFiles          []externalRef0.FileMetadata `json:"lossRunFiles"`
	NumOwnerOperatorUnits int                         `json:"numOwnerOperatorUnits"`
	OverallComment        string                      `json:"overallComment"`
}

// RenewalClassesAndCommoditiesForm defines model for RenewalClassesAndCommoditiesForm.
type RenewalClassesAndCommoditiesForm struct {
	AdditionalCommodities      *[]AdditionalInformationCommodity    `json:"additionalCommodities,omitempty"`
	CommoditiesComment         *string                              `json:"commoditiesComment,omitempty"`
	CommodityDistribution      *CommodityDistribution               `json:"commodityDistribution,omitempty"`
	OperatingClassDistribution OperatingClassDistribution           `json:"operatingClassDistribution"`
	PrimaryCategory            *externalRef0.CommodityCategory      `json:"primaryCategory,omitempty"`
	PrimaryCommodity           *externalRef0.PrimaryCommodityHauled `json:"primaryCommodity,omitempty"`
	PrimaryOperatingClass      externalRef0.OperatingClass          `json:"primaryOperatingClass"`
}

// RenewalCoverageForm defines model for RenewalCoverageForm.
type RenewalCoverageForm struct {
	AncillaryCoveragesRequired       *[]CoverageRecord                 `json:"ancillaryCoveragesRequired,omitempty"`
	CoveragesRequired                []CoverageRecord                  `json:"coveragesRequired"`
	CoveragesWithCombinedDeductibles *[]externalRef0.CombinedCoverages `json:"coveragesWithCombinedDeductibles,omitempty"`
	PackageType                      IndicationOptionTag               `json:"packageType"`
	ProducerId                       string                            `json:"producerId"`
}

// RenewalFilesForm defines model for RenewalFilesForm.
type RenewalFilesForm struct {
	DriverList    DriverList                  `json:"driverList"`
	EquipmentList EquipmentList               `json:"equipmentList"`
	IftaFiles     []externalRef0.FileMetadata `json:"iftaFiles"`
	LossRunFiles  []externalRef0.FileMetadata `json:"lossRunFiles"`
}

// RenewalLossForm defines model for RenewalLossForm.
type RenewalLossForm struct {
	LossRunSummaryForm LossRunSummaryForm `json:"lossRunSummaryForm"`
}

// RenewalMetadata defines model for RenewalMetadata.
type RenewalMetadata struct {
	SectionCompletionMap  *SectionCompletionMap        `json:"SectionCompletionMap,omitempty"`
	BelongsToRedList      *bool                        `json:"belongsToRedList,omitempty"`
	IftaFiles             *[]externalRef0.FileMetadata `json:"iftaFiles,omitempty"`
	OriginalApplicationId string                       `json:"originalApplicationId"`
}

// RenewalOperationsForm defines model for RenewalOperationsForm.
type RenewalOperationsForm struct {
	EquipmentList      EquipmentList       `json:"equipmentList"`
	NumberOfPowerUnits int                 `json:"numberOfPowerUnits"`
	ProjectedMileage   int                 `json:"projectedMileage"`
	TerminalLocations  *[]TerminalLocation `json:"terminalLocations,omitempty"`
}

// RetailerInfo defines model for RetailerInfo.
type RetailerInfo struct {
	// Agency Retailer's agency name
	Agency *string `json:"agency,omitempty"`

	// Email Retailer's email address
	Email *openapi_types.Email `json:"email,omitempty"`

	// FirstName Retailer's first name
	FirstName *string `json:"firstName,omitempty"`

	// LastName Retailer's last name
	LastName *string `json:"lastName,omitempty"`
}

// SSNPostRequestBody defines model for SSNPostRequestBody.
type SSNPostRequestBody struct {
	Ssn string `json:"ssn"`
}

// SafetyContactRole defines model for SafetyContactRole.
type SafetyContactRole string

// SectionCompletionMap defines model for SectionCompletionMap.
type SectionCompletionMap struct {
	AdditionalInformation bool `json:"additionalInformation"`
	ClassesAndCommodities bool `json:"classesAndCommodities"`
	Coverages             bool `json:"coverages"`
	LossHistory           bool `json:"lossHistory"`
	Operations            bool `json:"operations"`
}

// SelectedIndication defines model for SelectedIndication.
type SelectedIndication struct {
	Id string `json:"id"`
}

// SpeedgaugeBaseConnData defines model for SpeedgaugeBaseConnData.
type SpeedgaugeBaseConnData struct {
	Email openapi_types.Email `json:"email"`
	Name  string              `json:"name"`
}

// StatusTracker defines model for StatusTracker.
type StatusTracker struct {
	StatusSubmitted                   StatusTrackerRecord `json:"statusSubmitted"`
	StatusUnderReviewForQuote         StatusTrackerRecord `json:"statusUnderReviewForQuote"`
	StatusWaitingELDTelematicsConsent StatusTrackerRecord `json:"statusWaitingELDTelematicsConsent"`
	StatusWaitingELDTelematicsData    StatusTrackerRecord `json:"statusWaitingELDTelematicsData"`
}

// StatusTrackerRecord defines model for StatusTrackerRecord.
type StatusTrackerRecord struct {
	FinalizedDate    *openapi_types.Date `json:"finalizedDate,omitempty"`
	MaxEstimatedDays *int                `json:"maxEstimatedDays,omitempty"`
	MinEstimatedDays *int                `json:"minEstimatedDays,omitempty"`
}

// SupportedOperations defines model for SupportedOperations.
type SupportedOperations = externalRef0.SupportedOperationsRecordV2

// SupportedTSPs defines model for SupportedTSPs.
type SupportedTSPs = []TSPRecord

// SymbolAndDefinition defines model for SymbolAndDefinition.
type SymbolAndDefinition struct {
	Definition string `json:"definition"`
	Symbol     string `json:"symbol"`
}

// TSP defines model for TSP.
type TSP string

// TSPConnection defines model for TSPConnection.
type TSPConnection struct {
	AdditionalInfo        *TSPConnectionAdditionalInfo   `json:"additionalInfo,omitempty"`
	ApiKeyAuth            *ApiKeyAuthConnData            `json:"apiKeyAuth,omitempty"`
	BasicAuth             *BasicAuthConnData             `json:"basicAuth,omitempty"`
	BasicAuthWithDatabase *BasicAuthWithDatabaseConnData `json:"basicAuthWithDatabase,omitempty"`
	BasicAuthWithLoginId  *BasicAuthWithLoginIdConnData  `json:"basicAuthWithLoginId,omitempty"`
	ConsentKind           TelematicsConsentKind          `json:"consentKind"`
	ProgramType           *externalRef0.ProgramType      `json:"programType,omitempty"`
	Speedgauge            *SpeedgaugeBaseConnData        `json:"speedgauge,omitempty"`
	SpeedgaugeGeotab      *GeoTabConnData                `json:"speedgaugeGeotab,omitempty"`
	Tsp                   TSP                            `json:"tsp"`
}

// TSPConnectionAdditionalInfo defines model for TSPConnectionAdditionalInfo.
type TSPConnectionAdditionalInfo struct {
	FreeTextTSPName string `json:"freeTextTSPName"`
}

// TSPConnectionResponse defines model for TSPConnectionResponse.
type TSPConnectionResponse struct {
	HandleID *string `json:"handleID,omitempty"`
	Url      *string `json:"url,omitempty"`
}

// TSPLinkGenerationResponse defines model for TSPLinkGenerationResponse.
type TSPLinkGenerationResponse struct {
	Url *string `json:"url,omitempty"`
}

// TSPLinkOpenedResponse defines model for TSPLinkOpenedResponse.
type TSPLinkOpenedResponse struct {
	Opened *bool `json:"opened,omitempty"`
}

// TSPRecord defines model for TSPRecord.
type TSPRecord struct {
	ConsentKind TelematicsConsentKind `json:"consentKind"`
	LogoUrl     *string               `json:"logoUrl,omitempty"`
	Name        string                `json:"name"`
	Tsp         TSP                   `json:"tsp"`
}

// TargetPrice defines model for TargetPrice.
type TargetPrice struct {
	CoverageType externalRef0.CoverageType `json:"coverageType"`
	TotalPremium *float32                  `json:"totalPremium,omitempty"`
}

// TelematicsApplicationConsentLinkRequest defines model for TelematicsApplicationConsentLinkRequest.
type TelematicsApplicationConsentLinkRequest struct {
	ApplicationId      openapi_types.UUID `json:"applicationId"`
	InsuredInformation *struct {
		Email openapi_types.Email `json:"email"`
		Name  string              `json:"name"`
	} `json:"insuredInformation,omitempty"`
	ProgramType externalRef0.ProgramType `json:"programType"`
}

// TelematicsApplicationConsentLinkResponse defines model for TelematicsApplicationConsentLinkResponse.
type TelematicsApplicationConsentLinkResponse struct {
	// Link link that redirects you to the consent flow
	Link string `json:"link"`
}

// TelematicsConnectionInfo defines model for TelematicsConnectionInfo.
type TelematicsConnectionInfo struct {
	Error    *string                    `json:"error,omitempty"`
	HandleID openapi_types.UUID         `json:"handleID"`
	Status   TelematicsConnectionStatus `json:"status"`
	Tsp      TSP                        `json:"tsp"`
	// Deprecated:
	ValidationErrors *[]string `json:"validationErrors,omitempty"`
}

// TelematicsConnectionStatus defines model for TelematicsConnectionStatus.
type TelematicsConnectionStatus string

// TelematicsConsentInfo defines model for TelematicsConsentInfo.
type TelematicsConsentInfo struct {
	AgencyId      *openapi_types.UUID  `json:"agencyId,omitempty"`
	AgencyName    *string              `json:"agencyName,omitempty"`
	CompanyName   string               `json:"companyName"`
	CreatorId     *openapi_types.UUID  `json:"creatorId,omitempty"`
	DotNumber     int64                `json:"dotNumber"`
	InsuredEmail  *openapi_types.Email `json:"insuredEmail,omitempty"`
	InsuredName   *string              `json:"insuredName,omitempty"`
	ProducerEmail *openapi_types.Email `json:"producerEmail,omitempty"`
	ProducerName  *string              `json:"producerName,omitempty"`
}

// TelematicsConsentInfoResponse defines model for TelematicsConsentInfoResponse.
type TelematicsConsentInfoResponse struct {
	IsExpired             bool                   `json:"isExpired"`
	IsRevoked             bool                   `json:"isRevoked"`
	TelematicsConsentInfo *TelematicsConsentInfo `json:"telematicsConsentInfo,omitempty"`
}

// TelematicsConsentKind defines model for TelematicsConsentKind.
type TelematicsConsentKind string

// TelematicsConsentRequestEmail defines model for TelematicsConsentRequestEmail.
type TelematicsConsentRequestEmail struct {
	// Cc List of email addresses to CC the consent link email to.
	Cc *[]Email `json:"cc,omitempty"`

	// To List of email addresses to send the consent link email to.
	To []Email `json:"to"`
}

// TelematicsConsentRequestEmailData Mark the consent request email task as completed, and optionally send a request email to the insured.
type TelematicsConsentRequestEmailData struct {
	Completed   bool                           `json:"completed"`
	Email       *TelematicsConsentRequestEmail `json:"email,omitempty"`
	ProgramType *externalRef0.ProgramType      `json:"programType,omitempty"`
}

// TelematicsConsentRequestEmailResponse defines model for TelematicsConsentRequestEmailResponse.
type TelematicsConsentRequestEmailResponse struct {
	Completed   bool       `json:"completed"`
	CompletedAt *time.Time `json:"completedAt,omitempty"`
}

// TelematicsConsentRequestSMS defines model for TelematicsConsentRequestSMS.
type TelematicsConsentRequestSMS struct {
	// To List of phone numbers to send the consent link SMS to.
	To []PhoneNumber `json:"to"`
}

// TelematicsConsentRequestSMSData Mark the consent request SMS task as completed, and optionally send a request SMS to the insured.
type TelematicsConsentRequestSMSData struct {
	Completed   bool                         `json:"completed"`
	ProgramType *externalRef0.ProgramType    `json:"programType,omitempty"`
	Sms         *TelematicsConsentRequestSMS `json:"sms,omitempty"`
}

// TelematicsConsentRequestSMSResponse defines model for TelematicsConsentRequestSMSResponse.
type TelematicsConsentRequestSMSResponse struct {
	Completed   bool       `json:"completed"`
	CompletedAt *time.Time `json:"completedAt,omitempty"`
}

// TelematicsFleetApplicationInfo defines model for TelematicsFleetApplicationInfo.
type TelematicsFleetApplicationInfo struct {
	DotNumber     *int64              `json:"dotNumber,omitempty"`
	FleetName     *string             `json:"fleetName,omitempty"`
	NumPowerUnits int                 `json:"numPowerUnits"`
	Vehicles      *[]FleetInfoVehicle `json:"vehicles,omitempty"`
}

// TelematicsFleetInfoResponse defines model for TelematicsFleetInfoResponse.
type TelematicsFleetInfoResponse struct {
	ApplicationInfo  TelematicsFleetApplicationInfo                 `json:"applicationInfo"`
	HandleID         openapi_types.UUID                             `json:"handleID"`
	TelematicsInfo   TelematicsFleetTelematicsInfo                  `json:"telematicsInfo"`
	Tsp              TSP                                            `json:"tsp"`
	ValidationErrors *[]TelematicsFleetInfoResponseValidationErrors `json:"validationErrors,omitempty"`
}

// TelematicsFleetInfoResponseValidationErrors defines model for TelematicsFleetInfoResponse.ValidationErrors.
type TelematicsFleetInfoResponseValidationErrors string

// TelematicsFleetTelematicsInfo defines model for TelematicsFleetTelematicsInfo.
type TelematicsFleetTelematicsInfo struct {
	DotNumber          *int64              `json:"dotNumber,omitempty"`
	FleetName          *string             `json:"fleetName,omitempty"`
	MatchingPowerUnits int                 `json:"matchingPowerUnits"`
	Vehicles           *[]FleetInfoVehicle `json:"vehicles,omitempty"`
}

// TelematicsInfo defines model for TelematicsInfo.
type TelematicsInfo struct {
	Email         *openapi_types.Email `json:"email,omitempty"`
	Link          string               `json:"link"`
	LinkEmailedAt *time.Time           `json:"linkEmailedAt,omitempty"`
	Name          string               `json:"name"`
}

// TerminalLocation defines model for TerminalLocation.
type TerminalLocation struct {
	AddressLineOne        string                              `json:"addressLineOne"`
	AddressLineTwo        *string                             `json:"addressLineTwo,omitempty"`
	CargoTerminalSchedule *externalRef0.CargoTerminalSchedule `json:"cargoTerminalSchedule,omitempty"`
	IsGated               bool                                `json:"isGated"`
	IsGuarded             bool                                `json:"isGuarded"`
	TypeOfTerminal        TypeOfTerminal                      `json:"typeOfTerminal"`

	// UsState Two character short code for the US state the driver is licensed in.
	UsState externalRef0.USState `json:"usState"`
	ZipCode string               `json:"zipCode"`
}

// TypeOfTerminal defines model for TypeOfTerminal.
type TypeOfTerminal string

// UWChangesToApp defines model for UWChangesToApp.
type UWChangesToApp = []string

// UpdateApplicationRedListForm defines model for UpdateApplicationRedListForm.
type UpdateApplicationRedListForm struct {
	BelongsToRedList bool `json:"belongsToRedList"`
}

// WeightedCommodityRecord defines model for WeightedCommodityRecord.
type WeightedCommodityRecord struct {
	AvgDollarValueHauled int64                          `json:"avgDollarValueHauled"`
	Category             externalRef0.CommodityCategory `json:"category"`

	// Commodity Only label is set as required field, because we accept free form text from agent
	Commodity            externalRef0.CommodityHauled `json:"commodity"`
	MaxDollarValueHauled int64                        `json:"maxDollarValueHauled"`
	PercentageOfHauls    int32                        `json:"percentageOfHauls"`
}

// ZipcodeDecodeResponse defines model for ZipcodeDecodeResponse.
type ZipcodeDecodeResponse struct {
	CityName   string `json:"cityName"`
	CountyName string `json:"countyName"`
	StateCode  string `json:"stateCode"`
	StateName  string `json:"stateName"`
}

// IsRiskScoreAvailable defines model for isRiskScoreAvailable.
type IsRiskScoreAvailable = bool

// ApplicationID defines model for ApplicationID.
type ApplicationID = string

// ContactID defines model for ContactID.
type ContactID = openapi_types.UUID

// Deductibles defines model for Deductibles.
type Deductibles map[string]interface{}

// FetchBindableSubmission defines model for FetchBindableSubmission.
type FetchBindableSubmission = bool

// HandleID defines model for HandleID.
type HandleID = openapi_types.UUID

// ZipCode defines model for ZipCode.
type ZipCode = string

// AsDuplicateApplicationDetails returns the union data inside the PreApplicationChecksRuleResponse_Details as a DuplicateApplicationDetails
func (t PreApplicationChecksRuleResponse_Details) AsDuplicateApplicationDetails() (DuplicateApplicationDetails, error) {
	var body DuplicateApplicationDetails
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromDuplicateApplicationDetails overwrites any union data inside the PreApplicationChecksRuleResponse_Details as the provided DuplicateApplicationDetails
func (t *PreApplicationChecksRuleResponse_Details) FromDuplicateApplicationDetails(v DuplicateApplicationDetails) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeDuplicateApplicationDetails performs a merge with any union data inside the PreApplicationChecksRuleResponse_Details, using the provided DuplicateApplicationDetails
func (t *PreApplicationChecksRuleResponse_Details) MergeDuplicateApplicationDetails(v DuplicateApplicationDetails) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

// AsPotentialClearanceDetails returns the union data inside the PreApplicationChecksRuleResponse_Details as a PotentialClearanceDetails
func (t PreApplicationChecksRuleResponse_Details) AsPotentialClearanceDetails() (PotentialClearanceDetails, error) {
	var body PotentialClearanceDetails
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromPotentialClearanceDetails overwrites any union data inside the PreApplicationChecksRuleResponse_Details as the provided PotentialClearanceDetails
func (t *PreApplicationChecksRuleResponse_Details) FromPotentialClearanceDetails(v PotentialClearanceDetails) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergePotentialClearanceDetails performs a merge with any union data inside the PreApplicationChecksRuleResponse_Details, using the provided PotentialClearanceDetails
func (t *PreApplicationChecksRuleResponse_Details) MergePotentialClearanceDetails(v PotentialClearanceDetails) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

// AsPotentialRenewalDetails returns the union data inside the PreApplicationChecksRuleResponse_Details as a PotentialRenewalDetails
func (t PreApplicationChecksRuleResponse_Details) AsPotentialRenewalDetails() (PotentialRenewalDetails, error) {
	var body PotentialRenewalDetails
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromPotentialRenewalDetails overwrites any union data inside the PreApplicationChecksRuleResponse_Details as the provided PotentialRenewalDetails
func (t *PreApplicationChecksRuleResponse_Details) FromPotentialRenewalDetails(v PotentialRenewalDetails) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergePotentialRenewalDetails performs a merge with any union data inside the PreApplicationChecksRuleResponse_Details, using the provided PotentialRenewalDetails
func (t *PreApplicationChecksRuleResponse_Details) MergePotentialRenewalDetails(v PotentialRenewalDetails) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

func (t PreApplicationChecksRuleResponse_Details) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *PreApplicationChecksRuleResponse_Details) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}
