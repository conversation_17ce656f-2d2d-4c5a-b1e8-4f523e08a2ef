components:
  parameters:
    ApplicationID:
      in: path
      name: applicationID
      required: true
      schema:
        type: string
        example: a81bc81b-dead-4e5d-abff-90865d1e13b1
    HandleID:
      in: path
      name: handleID
      required: true
      schema:
        type: string
        format: uuid
        example: 76ae9906-d0ea-4c95-8385-593cadd44127
    ContactID:
      in: path
      name: contactID
      required: true
      schema:
        type: string
        format: uuid
        example: 76ae9906-d0ea-4c95-8385-593cadd44127
    Deductibles:
      in: query
      name: deductibles
      schema:
        type: object
        additionalProperties: {}
        example: '?deductibles=CoverageAutoPhysicalDamage,10000,CoverageAutoLiability,2500'
      explode: false
    ZipCode:
      in: path
      name: zipCode
      required: true
      schema:
        type: string
        example: 56020
    FetchBindableSubmission:
      name: fetchBindableSubmission
      in: query
      required: false
      schema:
        type: boolean
        default: false
  schemas:
    CreatedApplicationResponse:
      type: object
      required:
        - applicationID
      properties:
        applicationID:
          type: string
          example: a81bc81b-dead-4e5d-abff-90865d1e13b1
        flags:
          $ref: '#/components/schemas/Flags'
    Flags:
      type: object
      required:
        - clearanceConflict
      properties:
        clearanceConflict:
          type: boolean
    RejectedApplicationResponse:
      type: object
      required:
        - declinedRules
      properties:
        declinedRules:
          type: array
          items:
            $ref: '#/components/schemas/AppetiteCheckRuleResponse'

    PreApplicationChecksRuleRequest:
      type: object
      required:
        - dotNumber
        - producerId
        - effectiveDate
      properties:
        dotNumber:
          type: integer
        producerId:
          type: string
          format: uuid
        effectiveDate:
          type: string
          format: date
    PreApplicationChecksRuleResponse:
      type: object
      required:
        - rule
        - details
      properties:
        rule:
          $ref: '#/components/schemas/PreApplicationCheckRule'
        details:
          type: object
          oneOf:
            - $ref: '#/components/schemas/DuplicateApplicationDetails'
            - $ref: '#/components/schemas/PotentialClearanceDetails'
            - $ref: '#/components/schemas/PotentialRenewalDetails'

    DuplicateApplicationDetails:
      type: object
      required:
        - existingApplicationId
        - existingApplicationStatus
        - isRenewal
      properties:
        existingApplicationId:
          type: string
        existingApplicationStatus:
          type: string
        isRenewal:
          type: boolean

    PotentialClearanceDetails:
      type: object
      required:
        - status
        - isSameAgency
      properties:
        status:
          $ref : '#/components/schemas/PotentialClearanceStatus'
        isSameAgency:
            type: boolean

    PotentialRenewalDetails:
      type: object
      required:
        - applicationExpiryDate
      properties:
        applicationExpiryDate:
            type: string
            format: date


    PotentialClearanceStatus:
      type: string
      enum:
        [
          ClearedApplicationExists,
          ClearedApplicationExistsSameAgency,
          DeclinedClearedApplicationExists
        ]

    AppetiteCheckRuleResponse:
      type: object
      required:
        - rule
        - details
      properties:
        rule:
          $ref: '#/components/schemas/AppetiteCheckRule'
        details:
          type: object
          additionalProperties: { }
    #       The following list covers the possible details for all the
    #       available enums:
    #       AppetiteCheckRuleIsValidOrActive: {"isValid":"false", "isActive":"false"}
    #       AppetiteCheckRuleUnitMeasure: {"unitMeasure":"<number>"}
    #       AppetiteCheckRuleDotRating: {"dotRatingMinYears":"<number>"}
    #       AppetiteCheckRuleCompanyAddress: {"companyAddressState":"<state>"}
    #       AppetiteCheckRuleYearsInOperation: {"yearsInOperation":"<number>"}
    #       AppetiteCheckRuleLapsesInOperation: {"lapsesInOperation":"<lapses>"}
    #       AppetiteCheckRuleCarrierLoyalty: {
    #         "numCarriers":"<number>",
    #         "eligibleYears":"<number>",
    #         "authorityType":"<number>"
    #       }
    #       AppetiteCheckRuleHasUndesiredOperations: {}
    IndicationOptions:
      type: object
      required:
        - options
        ## DEPRECATED
        - currentDeductibles
        ## DEPRECATED
        - deductiblesOptions
        - coverageVariablesOptions
        - coveragesWithCombinedDeductibles
      properties:
        options:
          type: array
          items:
            $ref: '#/components/schemas/IndicationOption'
        currentDeductibles:
          type: array
          items:
            $ref: '#/components/schemas/CurrentDeductibleRecord'
        deductiblesOptions:
          type: array
          items:
            $ref: '#/components/schemas/DeductibleRecord'
        coverageVariablesOptions:
          type: array
          items:
            $ref: '#/components/schemas/CoverageVariablesOptions'
        coveragesWithCombinedDeductibles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/CombinedCoverages'
        limitsOptions:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/CoverageVariablesOptionNumeric'
    IndicationOption:
      type: object
      required:
        - id
        - totalPremium
        - coverages
        - optionTag
        - isRecommended
        - totalPowerUnits
        - TIV
        - totalMiles
      properties:
        id:
          type: string
          example: e53sd43s-gmsk-4l0a-sfss-43652de2f54v3
        totalPremium:
          type: integer
          format: int32
          example: 10000
        coverages:
          type: array
          items:
            $ref: '#/components/schemas/CoverageRecord'
        optionTag:
          $ref: '#/components/schemas/IndicationOptionTag'
        isRecommended:
          type: boolean
          example: false
        ## Total number of power units
        totalPowerUnits:
          type: integer
          format: int32
          example: 28
        ## Total insured value
        TIV:
          type: integer
          format: int32
          example: 1,175,000
        ## Total projected miles
        totalMiles:
          type: integer
          format: int32
          example: 2,500,000
        createdAt:
          type: string
          format: date
          example: 2021-06-01
    SelectedIndication:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          example: e53sd43s-gmsk-4l0a-sfss-43652de2f54v3
    StatusTracker:
      type: object
      required:
        ## Even though we follow a similar naming convention, these names are
        ## not considered ApplicationStates. They are intermediate representations
        ## used only for the tracker.
        - statusSubmitted
        - statusWaitingELDTelematicsConsent
        - statusWaitingELDTelematicsData
        - statusUnderReviewForQuote
      properties:
        statusSubmitted:
          $ref: '#/components/schemas/StatusTrackerRecord'
        statusWaitingELDTelematicsConsent:
          $ref: '#/components/schemas/StatusTrackerRecord'
        statusWaitingELDTelematicsData:
          $ref: '#/components/schemas/StatusTrackerRecord'
        statusUnderReviewForQuote:
          $ref: '#/components/schemas/StatusTrackerRecord'
    StatusTrackerRecord:
      type: object
      properties:
        finalizedDate:
          type: string
          format: date
          example: 2021-06-01
        minEstimatedDays:
          type: integer
          example: 5
        maxEstimatedDays:
          type: integer
          example: 7
    ApplicationListResponse:
      type: object
      required:
        - totalCount
        - apps
        - appListItems
      properties:
        totalCount:
          type: integer
        apps:
          type: array
          items:
            $ref: '#/components/schemas/ApplicationSummary'
        appListItems:
          type: array
          items:
              $ref: '#/components/schemas/ApplicationListResponseItem'
        cursor:
          type: string
    ApplicationListResponseItem:
      type: object
      required:
        - applicationID
        - shortID
        - companyName
        - state
        - producerName
        - effectiveDate
      properties:
        applicationID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        shortID:
          type: string
          minLength: 7
          maxLength: 7
          example: ABC1234
        companyName:
          type: string
          example: All Trucking Inc
        state:
          $ref: '#/components/schemas/ApplicationState'
        producerName:
          type: string
          example: John Doe
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        renewalMetadata:
          $ref: '#/components/schemas/RenewalMetadata'
    ApplicationSummary:
      type: object
      required:
        - applicationID
        - shortID
        - dotNumber
        - companyName
        - state
        - declineReason
        - producerName
        - effectiveDate
        - createdAt
        - updatedAt
        - agencyID
        - createdBy
        - underwriterName
        - underwriterEmail
      properties:
        applicationID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        shortID:
          type: string
          minLength: 7
          maxLength: 7
          example: ABC1234
        dotNumber:
          type: integer
          format: int64
          example: 1002125
        companyName:
          type: string
          example: All Trucking Inc
        state:
          $ref: '#/components/schemas/ApplicationState'
        underwriterName:
          type: string
          example: Taylor G.
        underwriterEmail:
          type: string
          format: email
          example: <EMAIL>
        bdName:
          type: string
          example: Taylor G.
        bdEmail:
          type: string
          format: email
          example: <EMAIL>
        producerName:
          type: string
          example: John Doe
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        createdAt:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
        updatedAt:
          type: string
          format: date-time
          example: 2021-03-02T12:32:11.312321-05:00
        agencyID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        producerID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        createdBy:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        renewalMetadata:
          $ref: '#/components/schemas/RenewalMetadata'
        packageType:
          $ref: '#/components/schemas/IndicationOptionTag'
        telematicsDataStatus:
          $ref: '#/components/schemas/ApplicationSummaryTelematicsDataStatus'
        clearanceStatus:
            $ref: '#/components/schemas/PotentialClearanceStatus'
        hidePrice:
          type: boolean
          example: false
        mtcVersion:
          $ref: '../common/spec.yaml#/components/schemas/MTCVersion'
        bindableSubmissionInfo:
          $ref: '#/components/schemas/BindableSubmissionInfo'
    TelematicsConsentInfo:
      type: object
      required:
        - dotNumber
        - companyName
      properties:
        insuredName:
          type: string
          example: Timothy
        insuredEmail:
          type: string
          format: email
          example: <EMAIL>
        dotNumber:
          type: integer
          format: int64
          example: 43567878
        companyName:
          type: string
          example: Tmat Agency
        agencyId:
          type: string
          format: uuid
        agencyName:
          type: string
          example: Abric trasnports pvt. Ltd.
        creatorId:
          type: string
          format: uuid
        producerName:
          type: string
          example: John Doe
        producerEmail:
          type: string
          format: email
          example: <EMAIL>

    RenewalMetadata:
      type: object
      required:
        - originalApplicationId
      properties:
        originalApplicationId:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        SectionCompletionMap:
          type: object
          $ref: '#/components/schemas/SectionCompletionMap'
        iftaFiles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/FileMetadata'
        belongsToRedList:
          type: boolean

    PatchRenewalApplicationForm:
      type: object
      properties:
        renewalCoverageForm:
          $ref: '#/components/schemas/RenewalCoverageForm'
        renewalOperationsForm:
          $ref: '#/components/schemas/RenewalOperationsForm'
        renewalClassesAndCommoditiesForm:
          $ref: '#/components/schemas/RenewalClassesAndCommoditiesForm'
        renewalLossForm:
          $ref: '#/components/schemas/RenewalLossForm'
        renewalAdditionalForm:
          $ref: '#/components/schemas/RenewalAdditionalForm'
        sectionCompletionMap:
          $ref: '#/components/schemas/SectionCompletionMap'
        renewalFilesForm:
          $ref: '#/components/schemas/RenewalFilesForm'

    RenewalCoverageForm:
      type: object
      required:
        - coveragesRequired
        - packageType
        - producerId
      properties:
        coveragesRequired:
          type: array
          items:
            $ref: '#/components/schemas/CoverageRecord'
        coveragesWithCombinedDeductibles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/CombinedCoverages'
        producerId:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        packageType:
          $ref: '#/components/schemas/IndicationOptionTag'
        ancillaryCoveragesRequired:
          type: array
          items:
            $ref: '#/components/schemas/CoverageRecord'

    RenewalOperationsForm:
      type: object
      required:
        - numberOfPowerUnits
        - projectedMileage
        - equipmentList
      properties:
        numberOfPowerUnits:
          type: integer
          format: int
          example: 17
        projectedMileage:
          type: integer
          example: 1000000
        equipmentList:
          $ref: '#/components/schemas/EquipmentList'
        terminalLocations:
          type: array
          items:
            $ref: '#/components/schemas/TerminalLocation'
    RenewalClassesAndCommoditiesForm:
      type: object
      required:
        - operatingClassDistribution
        - primaryOperatingClass
      properties:
        operatingClassDistribution:
          $ref: '#/components/schemas/OperatingClassDistribution'
        primaryOperatingClass:
          $ref: '../common/spec.yaml#/components/schemas/OperatingClass'
        primaryCommodity:
          $ref: '../common/spec.yaml#/components/schemas/PrimaryCommodityHauled'
        primaryCategory:
          $ref: '../common/spec.yaml#/components/schemas/CommodityCategory'
        commodityDistribution:
          $ref: '#/components/schemas/CommodityDistribution'
        additionalCommodities:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalInformationCommodity'
        commoditiesComment:
          type: string

    RenewalLossForm:
      type: object
      required:
        - lossRunSummaryForm
      properties:
        lossRunSummaryForm:
          $ref: '#/components/schemas/LossRunSummaryForm'

    RenewalAdditionalForm:
      type: object
      required:
        - driverList
        - numOwnerOperatorUnits
        - lossRunFiles
        - largeLossComment
        - overallComment
        - iftaFiles
      properties:
        driverList:
          $ref: '#/components/schemas/DriverList'
        numOwnerOperatorUnits:
          type: integer
          example: 10
        lossRunFiles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/FileMetadata'
        largeLossComment:
          type: string
        overallComment:
          type: string
        iftaFiles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/FileMetadata'

    RenewalFilesForm:
      type: object
      required:
        - driverList
        - equipmentList
        - lossRunFiles
        - iftaFiles
      properties:
        lossRunFiles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/FileMetadata'
        equipmentList:
          $ref: '#/components/schemas/EquipmentList'
        driverList:
          $ref: '#/components/schemas/DriverList'
        iftaFiles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/FileMetadata'

    SectionCompletionMap:
      type: object
      required:
        - coverages
        - operations
        - classesAndCommodities
        - lossHistory
        - additionalInformation
      properties:
        coverages:
          type: boolean
        operations:
          type: boolean
        classesAndCommodities:
          type: boolean
        lossHistory:
          type: boolean
        additionalInformation:
          type: boolean

    UpdateApplicationRedListForm:
      type: object
      required:
        - belongsToRedList
      properties:
        belongsToRedList:
          type: boolean

    QuoteSummary:
      type: object
      required:
        - optionTag
        - effectiveDate
        - totalPremium
        - updatedAt
      properties:
        optionTag:
          $ref: '#/components/schemas/IndicationOptionTag'
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        totalPremium:
          type: integer
          format: int32
          example: 170434
        updatedAt:
          type: string
          format: date
          example: 2021-03-02
    ApplicationPublicInfo:
      type: object
      required:
        - dotNumber
        - companyName
        - agencyName
        - producerName
        - producerEmail
      properties:
        insuredName:
          type: string
          example: Timothy
        insuredEmail:
          type: string
          format: email
          example: <EMAIL>
        dotNumber:
          type: integer
          format: int64
          example: 43567878
        companyName:
          type: string
          example: Tmat Agency
        agencyName:
          type: string
          example: Abric trasnports pvt. Ltd.
        producerName:
          type: string
          example: John Doe
        producerEmail:
          type: string
          format: email
          example: <EMAIL>
    AppetiteForm:
      type: object
      required:
        - dotNumber
        - companyName
        - hasUndesiredOperations
        - effectiveDate
        - numPowerUnits
      properties:
        dotNumber:
          type: integer
          format: int64
          example: 1002125
        companyName:
          type: string
          example: AllTrucking Inc
        hasUndesiredOperations:
          type: boolean
          example: false
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        producerId:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        numPowerUnits:
          type: integer
          example: 17
        marketerId:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
    TelematicsInfo:
      type: object
      required:
        - name
        - link
      properties:
        name:
          type: string
        email:
          type: string
          format: email
          example: <EMAIL>
        linkEmailedAt:
          type: string
          format: date-time
        link:
          type: string
          format: uri
          example: https://agents.nirvanatech.com/telematics/connect?token=<app_id>
    BindableSubmissionInfo:
      type: object
      required:
        - submissionId
      properties:
        submissionId:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        coveragesRequired:
          type: array
          items:
            $ref: '#/components/schemas/CoverageRecord'
        ancillaryCoveragesRequired:
          type: array
          items:
            $ref: '#/components/schemas/CoverageRecord'
    isRiskScoreAvailable:
      type: boolean
    ApplicationDetail:
      type: object
      required:
        - summary
      properties:
        summary:
          $ref: '#/components/schemas/ApplicationSummary'
        indicationForm:
          $ref: '#/components/schemas/IndicationForm'
        additionalInfoForm:
          $ref: '#/components/schemas/AdditionalInformationForm'
        selectedIndication:
          $ref: '#/components/schemas/IndicationOption'
        telematicsInfo:
          $ref: '#/components/schemas/TelematicsInfo'
    AdditionalInformationForm:
      type: object
      properties:
        numOwnerOperatorUnits:
          type: integer
          example: 10
        percentageOfSubhaul:
          type: number
          format: float
          example: 0.32
        commodities:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalInformationCommodity'
        commoditiesComment:
          type: string
        driverList:
          $ref: '#/components/schemas/DriverList'
        targetPrices:
          type: array
          items:
            $ref: '#/components/schemas/TargetPrice'
        lossRunFiles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/FileMetadata'
        additionalAgentFiles:
            type: array
            items:
              $ref: '../common/spec.yaml#/components/schemas/FileMetadata'
        largeLossComment:
          type: string
        overallComment:
          type: string
        coverageMetadata:
          $ref: '#/components/schemas/AdditionalInfoCoverageMetadata'
    AdditionalInfoCoverageMetadata:
      type: object
      required:
        - additionalIncumbentInfo
      properties:
        additionalIncumbentInfo:
          $ref: '#/components/schemas/AdditionalIncumbentInfo'
    AdditionalIncumbentInfo:
      type: object
      required:
        - isALIncumbent
      properties:
        isALIncumbent:
          type: boolean
    TargetPrice:
      type: object
      required:
        - coverageType
      properties:
        coverageType:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        totalPremium:
          type: number
          format: float
          example: 47045.19
    ApplicationBasicInfoForm:
      type: object
      properties:
        CompanyName:
          type: string
        EffectiveDateOfCoverage:
          type: string
          format: date
          example: 2021-06-01
        ProducerID:
          type: string
        CreatedBy:
          type: string
        AgencyID:
          type: string
        InsuredName:
          type: string
        InsuredEmail:
          type: string
          format: email
    ApplicationBasicInfo:
      type: object
      required:
        - CompanyName
        - EffectiveDateOfCoverage
        - CreatedBy
        - AgencyID
        - InsuredName
        - InsuredEmail
      properties:
        CompanyName:
          type: string
        EffectiveDateOfCoverage:
          type: string
          format: date
          example: 2021-06-01
        ProducerID:
          type: string
        CreatedBy:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        AgencyID:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        InsuredName:
          type: string
        InsuredEmail:
          type: string
    QuoteData:
      type: object
      required:
        - userAppDetails
        - finalAppDetails
        - fieldsChangedByUW
      properties:
        userAppDetails:
          $ref: '#/components/schemas/ApplicationDetail'
        finalAppDetails:
          $ref: '#/components/schemas/ApplicationDetail'
        ## TODO: change this solution to a more flexible/maintainable one
        fieldsChangedByUW:
          $ref: '#/components/schemas/UWChangesToApp'
    UWChangesToApp:
      type: array
      items:
        type: string
        example: ['radiusOfOperation', 'driverList']
    QuoteDetails:
      type: object
      required:
        - appSummary
        - quoteSummary
        - premiumDetails
        - coverageDetails
        - formsDetails
        - paymentOptions
        - documentSummary
      properties:
        appSummary:
          $ref: '#/components/schemas/ApplicationSummary'
        quoteSummary:
          $ref: '#/components/schemas/QuoteSummary'
        premiumDetails:
          $ref: '#/components/schemas/PremiumDetails'
        coverageDetails:
          $ref: '#/components/schemas/CoverageDetails'
        formsDetails:
          $ref: '../forms/spec.yaml#/components/schemas/FormsDetails'
        paymentOptions:
          $ref: '#/components/schemas/PaymentOptions'
        documentSummary:
          $ref: '#/components/schemas/DocumentSummary'
    DeclineAppForm:
      type: object
      required:
        - description
      properties:
        description:
          type: string
          example: Drivers are out of our appetite
    PremiumDetails:
      type: object
      required:
        - subtotalPremium
        - preDiscountTotalPremium
        - flatCharges
        - safetyDiscountPremium
        - safetyDiscountPercentage
        - totalPremium
        - totalPowerUnits
        - tiv
        - totalMiles
      properties:
        totalPowerUnits:
          type: integer
          format: int32
          example: 17
        tiv:
          type: integer
          format: int32
          example: 170000
        totalMiles:
          type: integer
          format: int32
          example: 170000
        ## Coverages total premium
        subtotalPremium:
          type: integer
          format: int32
          example: 220,011
        ## Total premium before discount
        preDiscountTotalPremium:
          type: integer
          format: int32
          example: 220,011
        ## State surcharges & taxes
        flatCharges:
          type: integer
          format: int32
          example: 500
        ## Based on Nirvana Safety Score
        safetyDiscountPremium:
          type: integer
          format: int32
          example: 50,077
        ## Based on Nirvana Safety Score
        safetyDiscountPercentage:
          type: integer
          format: int32
          example: 15
        ## Includes, flat charges & discounts
        totalPremium:
          type: integer
          format: int32
          example: 170,434
        totalSurchargePremium:
          type: integer
          format: int32
          example: 1000
        totalSurplusLinesTax:
          type: integer
          format: int32
          example: 1000
        totalStampingFee:
          type: integer
          format: int32
          example: 1000
    CoverageDetails:
      type: object
      properties:
        approvedCoverages:
          type: array
          items:
            $ref: '#/components/schemas/CoverageRecord'
        rejectedCoverages:
          type: array
          items:
            $ref: '#/components/schemas/RejectedCoverage'
        coveragesWithCombinedDeductibles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/CombinedCoverages'
    PaymentOptions:
      type: array
      items:
        $ref: '#/components/schemas/PaymentOptionRecord'
    DocumentSummary:
      type: object
      required:
        - signaturePacketHandleId
      properties:
        signaturePacketHandleId:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        signaturePacketZipHandleId:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
    PaymentOptionRecord:
      type: object
      required:
        - name
        - description
        - toBind
        - type
        - paymentDueDate
      properties:
        name:
          $ref: '#/components/schemas/PaymentOption'
        description:
          type: string
          example: Audited annually based on actual miles driven
        ## Amount to pay upfront
        toBind:
          type: integer
          format: int32
          example: 100,000
        type:
          type: string
          $ref: '#/components/schemas/PaymentOptionType'
        estimatedMonthlyPayment:
          type: integer
          format: int32
          example: 100,000
        paymentDueDate:
          type: string
          format: date
          example: 2021-06-01
        collateral:
          type: integer
          format: int
          example: 100,000
    PaymentOptionType:
      type: string
      enum: [ monthly, annual ]
    IndicationForm:
      type: object
      properties:
        operationsForm:
          $ref: '#/components/schemas/OperationsForm'
        classesAndCommoditiesForm:
          $ref: '#/components/schemas/ClassesAndCommoditiesForm'
        lossRunSummaryForm:
          $ref: '#/components/schemas/LossRunSummaryForm'
    OperationsForm:
      type: object
      required:
        - numberOfPowerUnits
        - projectedMileage
        - radiusOfOperation
        - equipmentList
        - producerId
      properties:
        numberOfPowerUnits:
          type: integer
          example: 53
        projectedMileage:
          type: integer
          example: 1000000
        radiusOfOperation:
          type: array
          items:
            $ref: '#/components/schemas/MileageRadiusRecord'
        equipmentList:
          $ref: '#/components/schemas/EquipmentList'
        coveragesRequired:
          type: array
          items:
            $ref: '#/components/schemas/CoverageRecord'
        ancillaryCoveragesRequired:
          type: array
          description: List of ancillary coverages required (This is combined with the ones that already attach during indication, if both have the same coverage type, the one in this list will be the one that will be used)
          items:
            $ref: '#/components/schemas/CoverageRecord'
        producerId:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        coveragesWithCombinedDeductibles:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/CombinedCoverages'
        terminalLocations:
          type: array
          items:
            $ref: '#/components/schemas/TerminalLocation'
        retailerInfo:
          $ref: '#/components/schemas/RetailerInfo'
          description: Optional retailer information for wholesaler agents
    TerminalLocation:
      type: object
      required:
        - addressLineOne
        - zipCode
        - typeOfTerminal
        - usState
        - isGuarded
        - isGated
      properties:
        addressLineOne:
          type: string
          example: House 123, XYZ street
        addressLineTwo:
          type: string
          example: Area A, City B
        zipCode:
          type: string
          example: 02635
        usState:
          $ref: '../common/spec.yaml#/components/schemas/USState'
        typeOfTerminal:
          $ref: '#/components/schemas/TypeOfTerminal'
        isGuarded:
          type: boolean
        isGated:
          type: boolean
        cargoTerminalSchedule:
          $ref: '../common/spec.yaml#/components/schemas/CargoTerminalSchedule'
    TypeOfTerminal:
      type: string
      enum: [ Terminal, Dock, Office, DropLot ]
    ClassesAndCommoditiesForm:
      type: object
      required:
        - operatingClassDistribution
        - primaryOperatingClass
      properties:
        operatingClassDistribution:
          $ref: '#/components/schemas/OperatingClassDistribution'
        primaryOperatingClass:
          $ref: '../common/spec.yaml#/components/schemas/OperatingClass'
        primaryCommodity:
          $ref: '../common/spec.yaml#/components/schemas/PrimaryCommodityHauled'
        primaryCategory:
          $ref: '../common/spec.yaml#/components/schemas/CommodityCategory'
        commodityDistribution:
          $ref: '#/components/schemas/CommodityDistribution'
    CommodityDistribution:
      type: object
      required:
        - commodities
      properties:
        commodities:
          type: array
          items:
            $ref: '#/components/schemas/WeightedCommodityRecord'
        additionalCommodities:
          $ref: '#/components/schemas/AdditionalCommoditiyRecord'
    WeightedCommodityRecord:
      type: object
      required:
        - category
        - commodity
        - avgDollarValueHauled
        - maxDollarValueHauled
        - percentageOfHauls
      properties:
        commodity:
          $ref: '../common/spec.yaml#/components/schemas/CommodityHauled'
        category:
          $ref: '../common/spec.yaml#/components/schemas/CommodityCategory'
        avgDollarValueHauled:
          type: integer
          format: int64
        maxDollarValueHauled:
          type: integer
          format: int64
        percentageOfHauls:
          type: integer
          format: int32
    AdditionalCommoditiyRecord:
      type: object
      required:
        - commodities
        - percentageOfHauls
      properties:
        commodities:
          type: string
        percentageOfHauls:
          type: integer
          format: int32
    LossRunSummaryForm:
      type: array
      items:
        $ref: '#/components/schemas/LossRunSummaryPerCoverage'
    MileageRadiusRecord:
      type: object
      required:
        - percentageOfFleet
        - mileageRadiusBucket
      properties:
        percentageOfFleet:
          type: integer
          format: int32
          example: 30
        mileageRadiusBucket:
          $ref: '#/components/schemas/MileageRadiusBucket'
    OperatingClassDistribution:
      type: array
      items:
        $ref: '#/components/schemas/OperatingClassDistributionRecord'
    OperatingClassDistributionRecord:
      type: object
      required:
        - operatingClass
        - percentageOfFleet
      properties:
        operatingClass:
          $ref: '../common/spec.yaml#/components/schemas/OperatingClass'
        percentageOfFleet:
          type: integer
          format: int32
          example: 30
    LossRunSummaryPerCoverage:
      type: object
      required:
        - coverageType
        - lossRunSummary
      properties:
        coverageType:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        lossRunSummary:
          type: array
          items:
            $ref: '#/components/schemas/LossRunSummaryRecord'
    LossRunSummaryRecord:
      type: object
      required:
        - policyPeriodStartDate
        - policyPeriodEndDate
        - numberOfPowerUnits
        - lossIncurred
        - numberOfClaims
      properties:
        policyPeriodStartDate:
          type: string
          format: date
          example: 2021-06-01
        policyPeriodEndDate:
          type: string
          format: date
          example: 2021-06-01
        numberOfPowerUnits:
          type: integer
          format: int32
          example: 27
        lossIncurred:
          type: integer
          format: int32
          example: 154000
        numberOfClaims:
          type: integer
          format: int32
          example: 2
        isNirvanaPeriod:
          type: boolean
    RejectedCoverage:
      type: object
      properties:
        coverageType:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        reason:
          type: string
      required:
        - coverageType
        - reason
    CoverageRecord:
      type: object
      required:
        - coverageType
      properties:
        coverageType:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        ## Total Premium for coverage
        premium:
          type: integer
          format: int32
          example: 218,400
        ## Premium per power unit
        premiumPerUnit:
          type: integer
          format: int32
          example: 7,800

        ## Premium per 100 miles
        premiumPerHundredMiles:
          type: number
          format: float
          example: 8,736
        ## Percentage insured of total insured value
        TIVPercentage:
          description: What percentage of the total value of the equipment is insured - mostly relates to Physical Damage. For example if a fleet is valuated at 100k and the total premium is 30k, TIV% = 30%.
          type: number
          format: float
          example: 0.32
        deductible:
          type: integer
          format: int32
          example: 1000
        limit:
          type: integer
          format: int32
          example: 1000

        ## Comercial auto list of symbols and definitions
        symbolsAndDefinitions:
          type: array
          items:
            $ref: '#/components/schemas/SymbolAndDefinition'
    SymbolAndDefinition:
      type: object
      required:
        - symbol
        - definition
      properties:
        symbol:
          type: string
          example: 72
        definition:
          type: string
          example: Any 'auto' other than Private passenger type 'autos'
    DriverList:
      type: object
      required:
        - drivers
      properties:
        flatfileMetadata:
          $ref: '../common/spec.yaml#/components/schemas/FlatfileMetadata'
        implerMetadata:
          $ref: '../common/spec.yaml#/components/schemas/ImplerMetadata'
        drivers:
          type: array
          items:
            $ref: '#/components/schemas/DriverListRecord'
    EquipmentList:
      type: object
      required:
        - info
      properties:
        flatfileMetadata:
          $ref: '../common/spec.yaml#/components/schemas/FlatfileMetadata'
        implerMetadata:
          $ref: '../common/spec.yaml#/components/schemas/ImplerMetadata'
        fileHandle:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        info:
          type: array
          items:
            $ref: '#/components/schemas/EquipmentListRecord'
    AvailableProducersResponse:
      type: array
      items:
        $ref: '#/components/schemas/Producer'
    Producer:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          example: 80f675b3-d5ad-473e-8ee7-585ddf2c5109
        name:
          type: string
          example: John Doe
    # TODO: firstName and lastName should be required
    DriverListRecord:
      type: object
      required:
        - dlNumber
        - usState
        - dateHired
      properties:
        dlNumber:
          type: string
          example: 'F255-921-50-094-0'
        usState:
          $ref: '../common/spec.yaml#/components/schemas/USState'
        dateHired:
          type: string
          format: date
          example: 2021-06-01
        # The following fields may be required depending on the state
        dateOfBirth:
          type: string
          format: date
          example: 1990-01-01
        firstName:
          type: string
        lastName:
          type: string
        yearsOfExperience:
          type: number
          example: 10
    EquipmentListRecord:
      type: object
      required:
        - vin
      properties:
        vin:
          type: string
          example: '1DAKSJALDMA22S090'
        statedValue:
          type: integer
          format: int32
          example: 150000
    ## DEPRECATED
    DeductibleRecord:
      type: object
      required:
        - coverage
        - amounts
      properties:
        coverage:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        amounts:
          type: array
          items:
            type: integer
            format: int32
            example: 0, 1000, 2500
    ## DEPRECATED
    CurrentDeductibleRecord:
      type: object
      required:
        - coverage
        - amount
      properties:
        coverage:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        amount:
          type: integer
          format: int32
          example: 1000
    CoverageVariablesOptions:
      type: object
      required:
        - coverage
      properties:
        coverage:
          $ref: '../common/spec.yaml#/components/schemas/CoverageType'
        deductibles:
          $ref: '../common/spec.yaml#/components/schemas/CoverageVariablesOptionNumeric'
        limits:
          $ref: '../common/spec.yaml#/components/schemas/CoverageVariablesOptionNumeric'
    SupportedOperations:
      $ref: '../common/spec.yaml#/components/schemas/SupportedOperationsRecordV2'
    ApplicationState:
      type: string
      enum:
        [
          ApplicationStateCreated,
          ApplicationStateInProgress,
          ApplicationStateUnderReviewForIndication,
          ApplicationStatePanic,
          ApplicationStateIndicationDelay,
          ApplicationStateIndicationGenerated,
          ApplicationStateIndicationSelected,
          ApplicationStatePendingELDTelematics,
          ApplicationStateProcessingELDTelematics,
          ApplicationStateUnderReviewForQuote,
          ApplicationStateQuoteGenerated,
          ApplicationStateDeclined,
          ApplicationStateBound,
          ApplicationStateApproved,
          ApplicationStateClosed,
        ]
    ApplicationTab:
      type: string
      enum:
        [
          ApplicationTabAll,
          ApplicationTabInProgress,
          ApplicationTabPendingELDTelematics,
          ApplicationTabUnderUWReview,
          ApplicationTabReadyToQuote,
          ApplicationTabReadyToBind,
          ApplicationTabDeclined,
          ApplicationTabBound,
          ApplicationTabClosed,
        ]
    MileageRadiusBucket:
      type: string
      enum:
        [
          MileageRadiusBucketZeroToFifty,
          MileageRadiusBucketFiftyToTwoHundred,
          MileageRadiusBucketTwoHundredToFiveHundred,
          MileageRadiusBucketFiveHundredPlus,
        ]
    IndicationOptionTag:
      type: string
      enum:
        [
          IndicationOptionTagBasic,
          IndicationOptionTagStandard,
          IndicationOptionTagComplete,
          IndicationOptionTagCustom,
        ]
    AdditionalInformationCommodity:
      type: string
      enum:
        [
          AddlInfoHazardousMaterialsInclClass9,
          AddlInfoLiftGateOrWhiteGloveService,
          AddlInfoResidentialDelivery,
          AddlInfoDoubleOrTripleTrailers,
          AddlInfoMeatOnHook,
        ]
    AppetiteCheckRule:
      type: string
      enum:
        [
          AppetiteCheckRuleYearsInOperation,
          AppetiteCheckRuleLapsesInOperation,
          AppetiteCheckRuleCarrierLoyalty,
          AppetiteCheckRuleIsValidOrActive,
          AppetiteCheckRuleUnitMeasure,
          AppetiteCheckRuleCompanyAddress,
          AppetiteCheckRuleDotRating,
          AppetiteCheckRuleHasUndesiredOperations,
        ]
    PreApplicationCheckRule:
      type: string
      enum:
        [
          PreApplicationRuleDuplicateApplication,
          PreApplicationRulePotentialClearance,
          PreApplicationRulePossibleRenewal,
          PreApplicationRulePossibleMidTermMove,
          PreApplicationRuleSameAgencyRenewal,
        ]
    TSP:
      type: string
      enum:
        [
          TSPSamsara,
          TSPKeepTruckin,
          TSPGeotab,
          TSPAdvantageAssetTracking,
          TSPAssuredTelematics,
          TSPEagleWireless,
          TSPGoFleet,
          TSPGridline,
          TSPOnTrakSolutions,
          TSPRushEnterpises,
          TSPTraxxisGPS,
          TSPOmnitracsXRS,
          TSPRandMcNally,
          TSPAzuga,
          TSPJJKeller,
          TSPBigRoad,
          TSPTeletracNavman,
          TSPTrimble,
          TSPVerizonConnect,
          TSPVerizonConnectReveal,
          TspMountainEld,
          TSPVerizonConnectFleet,
          TSPWebfleet,
          TSPEROAD,
          TSPGPSInsight,
          TSPZonar,
          TSPAgilisLinxup,
          TSPOther,
          TSPTransflo,
          TSPActsoftEncore,
          TSPBlueInkTechnology,
          TSPGeoforce,
          TSPSimpleTruckELD,
          TSPGorillaSafety,
          TSPInTouchGPS,
          TSPEZFleet,
          TSPOrbcomm,
          TSPNextraq,
          TSPArgosConnectedSolutions,
          TSPARIFleet,
          TSPATAndTFleet,
          TSPATAndTFleetComplete,
          TSPAttriX,
          TSPAwareGPS,
          TSPBadgerFleetSolutions,
          TSPBlueArrow,
          TSPCarrierHQ,
          TSPClutchELD,
          TSPCommandGPS,
          TSPCyntrXELDPlus,
          TSPDriveEDR,
          TSPELDFleet,
          TSPELDMandatePlus,
          TSPELDMandatePro,
          TSPELDOne,
          TSPELDRider,
          TSPEnVueTelematics,
          TSPFleetComplete,
          TSPFleetNavSystems,
          TSPFleetProfitCenter,
          TSPFleetBossGPS,
          TSPFleetistics,
          TSPFleetLocate21,
          TSPFleetLocateAdvancedAndCompliance,
          TSPFleetLocateELD,
          TSPFleetmaster,
          TSPFleetSharp,
          TSPFlexport,
          TSPForceByMojio,
          TSPGlobalELD,
          TSPGlostone,
          TSPGoGPS,
          TSPGPSCommander,
          TSPGPSFleetFinder,
          TSPGPSSolutions,
          TSPGPSTab,
          TSPGPSTrackingCanada,
          TSPGPSTrackit,
          TSPGrayboxSolutions,
          TSPHighPointGPS,
          TSPInsightMobileData,
          TSPStreetEagle,
          TSPIntellishift,
          TSPVTS,
          TSPIntouchELD,
          TSPIoTab,
          TSPLynx,
          TSPMasterELD,
          TSPMonarchGPS,
          TSPOmnitracs,
          TSPOneStepGPS,
          TSPOrion,
          TSPPositrace,
          TSPPowerFleet,
          TSPPrePassELD,
          TSPQualityGPS,
          TSPRealELD,
          TSPRightTruckingELD,
          TSPRMJTechnologies,
          TSPRoadStarELD,
          TSPSafetyVision,
          TSPSimpleELOG,
          TSPSmartDrive,
          TSPSmartWitness,
          TSPTMobile,
          TSPTangerine,
          TSPTFMELD,
          TSPTrackOnHOS,
          TSPTruckerPathELDPro,
          TSPTruPathSystems,
          TSPVertrax,
          TSPZELD,
          TSPZenduit,
          TSP3MD,
          TSP888ELD,
          TSPApolloELD,
          TSPBlueStarELD,
          TSPCNELD,
          TSPCTELogELD,
          TSPDreamELD,
          TSPELDTab,
          TSPExpressWayELD,
          TSPEZELDSolutions,
          TSPEZLogz,
          TSPFleetmatics,
          TSPForwardThinkingELD,
          TSPGoodDealGPS,
          TSPHorizonPathELD,
          TSPKSKELD,
          TSPLogPlusELD,
          TSPLookTruckELD,
          TSPLytXDriveCam,
          TSPMy20ELD,
          TSPNetradyneInc,
          TSPOaneELD,
          TSPOnePlusELD,
          TSPOptimaELD,
          TSPPhoenixELD,
          TspProRideEld,
          TSPReliableELD,
          TSPSwiftELD,
          TSPTMELD,
          TSPTTELD,
          TSPTrackEnsureInc,
          TSPUnityELD,
          TSPVistaELD,
          TSPVLogELD,
          TSPWorldTruckingELD,
          TSPXELD,
          TspContiGO,
          TSPRigbot,
          TSPColumbusELD,
          TSPBlackBearELD,
          TSPELOG42,
          TSPPeopleNet,
          TSPTrendyELD,
          TSPCoretex,
          TSPeasiTrack,
          TSPDigitalELD,
          TSPAlfaELD,
          TSPDailyELD,
          TSPRoadReadySolutions,
          TSPMTELD,
          TSPSFELD,
          TSPPlatformScience,
          TSPSRELD,
          TSPProLogs,
          TSPBELLFAMELD,
          TSPDSGELOGS,
          TSPTruckXELD,
          TSPGarmin,
          TSPFACTORELD,
          TSPSMARTCHOICELOGSELD,
          TSPSTATEELOGS,
          TSPISAACInstruments,
          TSPKonexial,
          TSPTRUSTELD,
          TSPLBTechnology,
          TSPPowerELD,
          TSPLEGACYELD,
          TSPMondo,
          TSPMOONLIGHTELD,
          TSPZippyELD,
          TSPEVOELD,
          TSPIDELD
        ]
    TelematicsConsentKind:
      type: string
      enum:
        [
          TelematicsConsentKindOAuth,
          TelematicsConsentKindSpeedgauge,
          TelematicsConsentKindSpeedgaugeGeotab,
          TelematicsConsentKindBasicAuth,
          TelematicsConsentKindApiKey,
          TelematicsConsentKindBasicAuthWithLoginId,
          TelematicsConsentKindBasicAuthWithDatabase,
        ]
    TelematicsConnectionStatus:
      type: string
      enum:
        [
          TelematicsConnectionStatusUnknown,
          TelematicsConnectionStatusInitiated,
          TelematicsConnectionStatusAuthorized,
          TelematicsConnectionStatusRejected,
          TelematicsConnectionStatusConnected,
          TelematicsConnectionStatusDisconnected,
          TelematicsConnectionStatusInternallyDisabled,
          TelematicsConnectionStatusPermanentlyLost,
          TelematicsConnectionStatusPermanentlyDeleted,
        ]
    PaymentOption:
      type: string
      enum: [ PaymentOptionPaidInFull, PaymentOptionMonthlyReporter ]
    DotPrefillResponse:
      type: object
      required:
        - address
        - usState
        - name
        - last_reported_power_unit_count
        - effectiveDate
      properties:
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        usState:
          $ref: '../common/spec.yaml#/components/schemas/USState'
        address:
          type: string
          example: 15427 E FREMONT DRIVE CENTENNIAL, CO 80112
        name:
          type: string
          example: TIC - THE INDUSTRIAL COMPANY
        # Not required - may be missing if the fleet hasn't reported to DOT yet
        last_reported_mileage:
          type: integer
          format: int64
          example: 120000
        last_reported_power_unit_count:
          type: integer
          format: int64
          example: 30
        isStateActive:
          type: boolean
          example: true
          description: Whether nirvana is operating in the state or not (based on the state of the address associated to the DOT) - used for appetite check
    SupportedTSPs:
      type: array
      items:
        $ref: '#/components/schemas/TSPRecord'
    TSPRecord:
      type: object
      required:
        - name
        - tsp
        - consentKind
      properties:
        name:
          type: string
          example: Samsara
        tsp:
          $ref: '#/components/schemas/TSP'
        consentKind:
          $ref: '#/components/schemas/TelematicsConsentKind'
        logoUrl:
          type: string
          example: https://<storage-url>/samsara.png
    TSPConnection:
      type: object
      required:
        - tsp
        - consentKind
      properties:
        programType:
          $ref: '../common/spec.yaml#/components/schemas/ProgramType'
        tsp:
          $ref: '#/components/schemas/TSP'
        consentKind:
          $ref: '#/components/schemas/TelematicsConsentKind'
        speedgauge:
          $ref: '#/components/schemas/SpeedgaugeBaseConnData'
        speedgaugeGeotab:
          $ref: '#/components/schemas/GeoTabConnData'
        basicAuth:
          $ref: '#/components/schemas/BasicAuthConnData'
        apiKeyAuth:
          $ref: '#/components/schemas/ApiKeyAuthConnData'
        basicAuthWithLoginId:
          $ref: '#/components/schemas/BasicAuthWithLoginIdConnData'
        basicAuthWithDatabase:
          $ref: '#/components/schemas/BasicAuthWithDatabaseConnData'
        additionalInfo:
          $ref: '#/components/schemas/TSPConnectionAdditionalInfo'
    OAuthConnection:
      type: boolean
    TSPConnectionAdditionalInfo:
      type: object
      required:
        - freeTextTSPName
      properties:
        freeTextTSPName:
          type: string
          example: My TSP
    SpeedgaugeBaseConnData:
      type: object
      required:
        - name
        - email
      properties:
        name:
          type: string
        email:
          type: string
          format: email
    GeoTabConnData:
      type: object
      required:
        - name
        - email
        - dbName
      properties:
        name:
          type: string
        email:
          type: string
          format: email
        dbName:
          type: string
    BasicAuthConnData:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
        password:
          type: string
    ApiKeyAuthConnData:
      type: object
      required:
        - apiKey
      properties:
        apiKey:
          type: string
    BasicAuthWithLoginIdConnData:
      type: object
      required:
        - username
        - password
        - loginId
      properties:
        username:
          type: string
        password:
          type: string
        loginId:
          type: string
    BasicAuthWithDatabaseConnData:
      type: object
      required:
        - username
        - password
        - database
      properties:
        username:
          type: string
        password:
          type: string
        database:
          type: string
    Email:
      type: object
      required:
        - email
      properties:
        name:
          type: string
        email:
          type: string
          format: email
    TelematicsConsentRequestEmail:
      type: object
      required:
        - to
      properties:
        to:
          type: array
          minItems: 1
          description: List of email addresses to send the consent link email to.
          items:
            $ref: '#/components/schemas/Email'
        cc:
          type: array
          minItems: 0
          description: List of email addresses to CC the consent link email to.
          items:
            $ref: '#/components/schemas/Email'
    TelematicsConsentInfoResponse:
      type: object
      required:
        - isExpired
        - isRevoked
      properties:
        isExpired:
          type: boolean
          default: false
          example: false
        isRevoked:
          type: boolean
          default: false
          example: true
        telematicsConsentInfo:
          $ref: '#/components/schemas/TelematicsConsentInfo'
    TelematicsConsentRequestEmailData:
      type: object
      required:
        - completed
      description: Mark the consent request email task as completed, and optionally send a request email to the insured.
      properties:
        completed:
          type: boolean
          default: false
          example: false
        email:
          $ref: '#/components/schemas/TelematicsConsentRequestEmail'
        programType:
          $ref: '../common/spec.yaml#/components/schemas/ProgramType'

    PhoneNumber:
      type: object
      required:
        - phoneNumber
      properties:
        name:
          type: string
        phoneNumber:
          type: string
          pattern: '^\+[1-9]\d{1,14}$'
          example: '+14155552671'

    TelematicsConsentRequestSMS:
      type: object
      required:
        - to
      properties:
        to:
          type: array
          minItems: 1
          description: List of phone numbers to send the consent link SMS to.
          items:
            $ref: '#/components/schemas/PhoneNumber'

    TelematicsConsentRequestSMSData:
      type: object
      required:
        - completed
      description: Mark the consent request SMS task as completed, and optionally send a request SMS to the insured.
      properties:
        completed:
          type: boolean
          default: false
          example: false
        sms:
          $ref: '#/components/schemas/TelematicsConsentRequestSMS'
        programType:
          $ref: '../common/spec.yaml#/components/schemas/ProgramType'

    TelematicsConsentRequestSMSResponse:
      type: object
      required:
        - completed
      properties:
        completed:
          type: boolean
          example: true
        completedAt:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00

    GenerateSafetyReportURIResponse:
      type: object
      required:
        - url
      properties:
        url:
          type: string
          example: https://safety.nirvanatech.com/c5ec4b73-8dcd-49e8-b25f-d26952b6a2fa/overview
    TSPConnectionResponse:
      type: object
      properties:
        url:
          type: string
          example: https://api.samsara.com/oauth2/authorize?client_id=<client_id>&state=<state>&response_type=code
        handleID:
          type: string
          example: 721a6899-38c8-4d96-90af-11e1c65a7a83
    TSPLinkGenerationResponse:
      type: object
      properties:
        url:
          type: string
          example: https://agents.nirvanatech.com/telematics/connect?token=<app_id>
    TelematicsConsentRequestEmailResponse:
      type: object
      required:
        - completed
      properties:
        completed:
          type: boolean
          example: true
        completedAt:
          type: string
          format: date-time
          example: 2021-03-01T23:51:04.290898-05:00
    TSPLinkOpenedResponse:
      type: object
      properties:
        opened:
          type: boolean
          example: true
    FleetInfoVehicle:
      type: object
      properties:
        vin:
          type: string
          example: 4V4NC9TG27N429281
        name:
          type: string
          example: F23
    FleetInfo:
      type: object
      properties:
        dotNumber:
          type: integer
          format: int64
          example: 123456
        fleetName:
          type: string
          example: Trucks R Us
        vehicles:
          type: array
          items:
            $ref: '#/components/schemas/FleetInfoVehicle'
    TelematicsFleetApplicationInfo:
      allOf:
        - $ref: '#/components/schemas/FleetInfo'
        - type: object
          required:
            - numPowerUnits
          properties:
            numPowerUnits:
              type: integer
              example: 5
    TelematicsFleetTelematicsInfo:
      allOf:
        - $ref: '#/components/schemas/FleetInfo'
        - type: object
          required:
            - matchingPowerUnits
          properties:
            matchingPowerUnits:
              type: integer
              example: 4
    TelematicsConnectionInfo:
      type: object
      required:
        - handleID
        - tsp
        - status
      properties:
        handleID:
          type: string
          format: uuid
          example: 76ae9906-d0ea-4c95-8385-593cadd44127
        tsp:
          $ref: '#/components/schemas/TSP'
        status:
          $ref: '#/components/schemas/TelematicsConnectionStatus'
        error:
          type: string
        validationErrors:
          type: array
          deprecated: true
          items:
            type: string
    TelematicsFleetInfoResponse:
      type: object
      required:
        - applicationInfo
        - telematicsInfo
        - status
        - handleID
        - tsp
      properties:
        applicationInfo:
          $ref: '#/components/schemas/TelematicsFleetApplicationInfo'
        telematicsInfo:
          $ref: '#/components/schemas/TelematicsFleetTelematicsInfo'
        handleID:
          type: string
          format: uuid
          example: 76ae9906-d0ea-4c95-8385-593cadd44127
        tsp:
          $ref: '#/components/schemas/TSP'
        validationErrors:
          type: array
          items:
            type: string
            enum:
              [ MissingTelematicsVehicles, InvalidDOTNumber, InvalidFleetName ]
    PolicyDetails:
      type: object
      required:
        - policyId
        - policyNumber
      properties:
        policyNumber:
          type: string
          example: NITSK0012345-22
        policyId:
          type: string
          example: a81bc81b-dead-4e5d-abff-90865d1e13b1
    GetPoliciesResponse:
      type: array
      description: List of policies and its information
      items:
        $ref: '#/components/schemas/PolicyRecord'
    PolicyCommissionRecordUpdate:
      type: object
      required:
        - policyIdentifier
        - commissionRate
      properties:
        policyIdentifier:
          type: string
          example: 1327564
        commissionRate:
          type: number
          format: float
          example: 0.12
    PolicyChangeResponse:
      type: object
      required:
        - message
        - success
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: 'Policy removed successfully'
    PolicyRecord:
      type: object
      required:
        - policyNumber
        - policyID
        - insuredName
        - producerName
        - dotNumber
        - policyIssuanceDate
        - policyExpirationDate
        - agencyName
        - applicationID
        - policyIdentifier
        - status
      properties:
        policyNumber:
          type: string
          example: NITSK00123456-22
        policyIdentifier:
          type: string
          example: 110008
        policyID:
          type: string
          example: e2f3961c-7bd5-474d-bb5f-8dd998a946ac
        applicationID:
          type: string
          example: e2f3961c-7bd5-474d-bb5f-8dd998a946ac
        insuredName:
          type: string
          example: Big Woods Trucking
        producerName:
          type: string
          example: John Doe
        agencyName:
          type: string
          example: John Doe
        dotNumber:
          type: integer
          format: int64
          example: 1002125
        policyIssuanceDate:
          type: string
          format: date
          example: 2021-06-01
        policyExpirationDate:
          type: string
          format: date
          example: 2021-06-01
        status:
          $ref: '../common/spec.yaml#/components/schemas/PolicyStatus'

    ContactType:
      type: string
      enum: [ ContactTypeSafety, ContactTypeBilling ]

    SafetyContactRole:
      type: string
      enum: [ Owner, SafetyManager, FleetManager, Other ]

    BillingContactRole:
      type: string
      enum: [ Owner, BillingManager, Agent, Producer, Other ]

    Contact:
      type: object
      required:
        - firstName
        - lastName
        - email
        - pocType
      properties:
        firstName:
          type: string
          minLength: 1
          maxLength: 50
          example: john
        lastName:
          type: string
          minLength: 1
          maxLength: 50
          example: Doe
        email:
          type: string
          #          format: email
          example: <EMAIL>
        phoneNumber:
          type: string
          minLength: 1
          maxLength: 50
          example: *************
        pocType:
          $ref: '#/components/schemas/POCType'
        originalSignatory:
          type: boolean
          example: true
        safetyContactRole:
          $ref: '#/components/schemas/SafetyContactRole'
        billingContactRole:
          $ref: '#/components/schemas/BillingContactRole'

    POCType:
      type: string
      enum: [ POCTypePrimary, POCTypeRegular ]

    GetContactsResponse:
      type: object
      required:
        - safety
        - billing
      properties:
        safety:
          type: array
          description: List of Contacts and their details
          items:
            $ref: '#/components/schemas/Contact'
        billing:
          type: array
          description: List of Contacts and their details
          items:
            $ref: '#/components/schemas/Contact'

    ContactRecordsUpdate:
      type: object
      required:
        - type
        - contacts
      properties:
        type:
          $ref: '#/components/schemas/ContactType'
        contacts:
          type: array
          description: List of Contacts and their details
          items:
            $ref: '#/components/schemas/Contact'

    ContactRecordsUpdateResponse:
      type: object
      required:
        - success
        - message
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: 'Contacts Updated Successfully'

    PatchModelVersionInLatestPendingAppReviewForm:
      type: object
      required:
        - version
      properties:
        version:
          type: string
          example: 'v6.0.3'
    PatchModelVersionInLatestPendingAppReviewResponse:
      type: object
      required:
        - updated
        - message
      properties:
        updated:
          type: boolean
          example: true
        message:
          type: string
          example: 'successfully updated version to v0.6.3'

    ApplicationSummaryTelematicsDataStatus:
        type: string
        enum:
            [
              TelematicsDataStatusSuccess,
              TelematicsDataStatusFailed,
            ]

    ZipcodeDecodeResponse:
        type: object
        required:
            - cityName
            - stateCode
            - stateName
            - countyName
        properties:
          cityName:
            type: string
            example: Aguada
          stateCode:
            type: string
            example: PR
          stateName:
            type: string
            example: Puerto Rico
          countyName:
            type: string
            example: Aguada

    AgencyBDMappingsResponse:
      type: object
      required:
        - agencyBDMappings
        - bdList
      properties:
        agencyBDMappings:
          type: array
          items:
            $ref: '#/components/schemas/AgencyBDMapping'
    AgencyBDMapping:
      type: object
      required:
        - id
        - user
        - agency
        - programType
        - userOptions
      properties:
        id:
          type: string
          format: uuid
          example: 0EC2372B-D6E7-4AA4-9E3E-8EFD378FB83F
        user:
          $ref: '../common/spec.yaml#/components/schemas/User'
        agency:
          $ref: '../common/spec.yaml#/components/schemas/Agency'
        programType:
          $ref: '../common/spec.yaml#/components/schemas/ProgramType'
        userOptions:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/User'

    AgencyBDMappingRequest:
      type: object
      required:
        - agencyId
        - userId
        - programType
      properties:
        agencyId:
          type: string
          format: uuid
          example: 0EC2372B-D6E7-4AA4-9E3E-8EFD378FB83F
        userId:
          type: string
          format: uuid
          example: 0EC2372B-D6E7-4AA4-9E3E-8EFD378FB83F
        programType:
          $ref: '../common/spec.yaml#/components/schemas/ProgramType'

    TelematicsApplicationConsentLinkRequest:
      type: object
      required:
        - applicationId
        - programType
      properties:
        applicationId:
          type: string
          format: uuid
          example: 0EC2372B-D6E7-4AA4-9E3E-8EFD378FB83F
        programType:
          $ref: '../common/spec.yaml#/components/schemas/ProgramType'
        insuredInformation:
          type: object
          required:
            - name
            - email
          properties:
            name:
              type: string
              minLength: 1
            email:
              type: string
              format: email

    TelematicsApplicationConsentLinkResponse:
      type: object
      required:
        - link
      properties:
        link:
          type: string
          description: link that redirects you to the consent flow

    PostClearanceProtectedApplicationsRequestBody:
      type: object
      required:
        - applications
      properties:
        applications:
          type: array
          items:
            $ref: '#/components/schemas/PostClearanceProtectedApplicationsRequestBodyItem'

    PostClearanceProtectedApplicationsRequestBodyItem:
      type: object
      required:
        - dotNumber
        - hasUndesiredOperations
        - effectiveDate
        - numberOfPowerUnits
        - producerEmail
      properties:
        dotNumber:
          type: integer
          format: int64
          example: 123456
        hasUndesiredOperations:
          type: boolean
          example: true
        effectiveDate:
          type: string
          format: date
          example: 2021-06-01
        numberOfPowerUnits:
          type: integer
          format: int64
          example: 5
        producerEmail:
          type: string
          format: email
          example: <EMAIL>

    SSNPostRequestBody:
      type: object
      required:
        - ssn
      properties:
        ssn:
          type: string
          example: ***********

    GetCreditReportStatusResponse:
      type: object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/CreditReportStatus'
    CreditReportStatus:
      type: string
      enum: [ NoHit, ThinFile, Success, Failed, InProgress ]

    RetailerInfo:
      type: object
      properties:
        firstName:
          type: string
          example: John
          description: Retailer's first name
        lastName:
          type: string
          example: Doe
          description: Retailer's last name
        email:
          type: string
          format: email
          example: <EMAIL>
          description: Retailer's email address
        agency:
          type: string
          example: ABC Insurance Agency
          description: Retailer's agency name


