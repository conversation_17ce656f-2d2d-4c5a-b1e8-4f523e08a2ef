// Package common provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package common

import (
	"time"

	openapi_types "github.com/oapi-codegen/runtime/types"
)

// Defines values for APIDescMethods.
const (
	DELETE APIDescMethods = "DELETE"
	GET    APIDescMethods = "GET"
	PATCH  APIDescMethods = "PATCH"
	POST   APIDescMethods = "POST"
	PUT    APIDescMethods = "PUT"
)

// Defines values for ApplicationClearanceStatus.
const (
	ApplicationClearanceIncomplete ApplicationClearanceStatus = "ApplicationClearanceIncomplete"
	ApplicationCleared             ApplicationClearanceStatus = "ApplicationCleared"
	ApplicationRequiresClearance   ApplicationClearanceStatus = "ApplicationRequiresClearance"
)

// Defines values for ApplicationReviewWidgetEnum.
const (
	ApplicationReviewWidgetEnumBasicScoreTrend      ApplicationReviewWidgetEnum = "BasicScoreTrend"
	ApplicationReviewWidgetEnumBasicThreshold       ApplicationReviewWidgetEnum = "BasicThreshold"
	ApplicationReviewWidgetEnumCommodities          ApplicationReviewWidgetEnum = "Commodities"
	ApplicationReviewWidgetEnumCrashRecords         ApplicationReviewWidgetEnum = "CrashRecords"
	ApplicationReviewWidgetEnumDotRating            ApplicationReviewWidgetEnum = "DotRating"
	ApplicationReviewWidgetEnumDriversList          ApplicationReviewWidgetEnum = "DriversList"
	ApplicationReviewWidgetEnumEquipmentList        ApplicationReviewWidgetEnum = "EquipmentList"
	ApplicationReviewWidgetEnumFinancials           ApplicationReviewWidgetEnum = "Financials"
	ApplicationReviewWidgetEnumFleetHistory         ApplicationReviewWidgetEnum = "FleetHistory"
	ApplicationReviewWidgetEnumHazardZones          ApplicationReviewWidgetEnum = "HazardZones"
	ApplicationReviewWidgetEnumIssScoreTrend        ApplicationReviewWidgetEnum = "IssScoreTrend"
	ApplicationReviewWidgetEnumLargeLosses          ApplicationReviewWidgetEnum = "LargeLosses"
	ApplicationReviewWidgetEnumLossAverages         ApplicationReviewWidgetEnum = "LossAverages"
	ApplicationReviewWidgetEnumLossSummary          ApplicationReviewWidgetEnum = "LossSummary"
	ApplicationReviewWidgetEnumOOSSummary           ApplicationReviewWidgetEnum = "OOSSummary"
	ApplicationReviewWidgetEnumOperatingClasses     ApplicationReviewWidgetEnum = "OperatingClasses"
	ApplicationReviewWidgetEnumProjectedInformation ApplicationReviewWidgetEnum = "ProjectedInformation"
	ApplicationReviewWidgetEnumRadiusOfOperation    ApplicationReviewWidgetEnum = "RadiusOfOperation"
	ApplicationReviewWidgetEnumSafetyScoreTrend     ApplicationReviewWidgetEnum = "SafetyScoreTrend"
	ApplicationReviewWidgetEnumSevereViolations     ApplicationReviewWidgetEnum = "SevereViolations"
	ApplicationReviewWidgetEnumTerminalLocations    ApplicationReviewWidgetEnum = "TerminalLocations"
	ApplicationReviewWidgetEnumYearsInBusiness      ApplicationReviewWidgetEnum = "YearsInBusiness"
)

// Defines values for ApplicationReviewWidgetStatusEnum.
const (
	Completed        ApplicationReviewWidgetStatusEnum = "Completed"
	DataNotAvailable ApplicationReviewWidgetStatusEnum = "DataNotAvailable"
	Failed           ApplicationReviewWidgetStatusEnum = "Failed"
	Processing       ApplicationReviewWidgetStatusEnum = "Processing"
)

// Defines values for ApplicationReviewWidgetVersion.
const (
	ApplicationReviewWidgetVersionV1 ApplicationReviewWidgetVersion = "V1"
	ApplicationReviewWidgetVersionV2 ApplicationReviewWidgetVersion = "V2"
)

// Defines values for BillingFrequency.
const (
	BillingFrequencyMonthly    BillingFrequency = "Monthly"
	BillingFrequencyPaidInFull BillingFrequency = "PaidInFull"
)

// Defines values for CargoTerminalScheduleConstructionClass.
const (
	FireResistive         CargoTerminalScheduleConstructionClass = "FireResistive"
	Frame                 CargoTerminalScheduleConstructionClass = "Frame"
	JoistedMasonry        CargoTerminalScheduleConstructionClass = "JoistedMasonry"
	MasonryNonCombustible CargoTerminalScheduleConstructionClass = "MasonryNonCombustible"
	ModifiedFireResistive CargoTerminalScheduleConstructionClass = "ModifiedFireResistive"
	NonCombustible        CargoTerminalScheduleConstructionClass = "NonCombustible"
	NoneOfAboveOrUnknown  CargoTerminalScheduleConstructionClass = "NoneOfAboveOrUnknown"
)

// Defines values for CargoTerminalSchedulePrivateFireProtection.
const (
	AutomaticSuppressionAndAdditionalProtections CargoTerminalSchedulePrivateFireProtection = "AutomaticSuppressionAndAdditionalProtections"
	CompleteSuppression                          CargoTerminalSchedulePrivateFireProtection = "CompleteSuppression"
	IncompleteSuppression                        CargoTerminalSchedulePrivateFireProtection = "IncompleteSuppression"
	NoAutomaticFireSuppression                   CargoTerminalSchedulePrivateFireProtection = "NoAutomaticFireSuppression"
)

// Defines values for CargoTerminalSchedulePrivateTheftProtection.
const (
	CentralStationAlarm                              CargoTerminalSchedulePrivateTheftProtection = "CentralStationAlarm"
	CentralStationAlarmAndAdditionalTheftProtections CargoTerminalSchedulePrivateTheftProtection = "CentralStationAlarmAndAdditionalTheftProtections"
	LocalAlarm                                       CargoTerminalSchedulePrivateTheftProtection = "LocalAlarm"
	NoAlarmSystem                                    CargoTerminalSchedulePrivateTheftProtection = "NoAlarmSystem"
)

// Defines values for CommodityCategoryEnum.
const (
	CommodityCategoryAutoPartsAndAccessories        CommodityCategoryEnum = "CommodityCategoryAutoPartsAndAccessories"
	CommodityCategoryBeverageNonAlcoholic           CommodityCategoryEnum = "CommodityCategoryBeverageNonAlcoholic"
	CommodityCategoryBeveragesAlcoholic             CommodityCategoryEnum = "CommodityCategoryBeveragesAlcoholic"
	CommodityCategoryBuildingMaterialsAndLumber     CommodityCategoryEnum = "CommodityCategoryBuildingMaterialsAndLumber"
	CommodityCategoryElectronicParts                CommodityCategoryEnum = "CommodityCategoryElectronicParts"
	CommodityCategoryElectronicsFinished            CommodityCategoryEnum = "CommodityCategoryElectronicsFinished"
	CommodityCategoryFoodsBakedDryPackagedCanned    CommodityCategoryEnum = "CommodityCategoryFoodsBakedDryPackagedCanned"
	CommodityCategoryFoodsFreshProduceNondry        CommodityCategoryEnum = "CommodityCategoryFoodsFreshProduceNondry"
	CommodityCategoryGeneralFreight                 CommodityCategoryEnum = "CommodityCategoryGeneralFreight"
	CommodityCategoryHouseholdGoods                 CommodityCategoryEnum = "CommodityCategoryHouseholdGoods"
	CommodityCategoryHouseholdHardware              CommodityCategoryEnum = "CommodityCategoryHouseholdHardware"
	CommodityCategoryIntermodalContainerizedFreight CommodityCategoryEnum = "CommodityCategoryIntermodalContainerizedFreight"
	CommodityCategoryMailAndParcel                  CommodityCategoryEnum = "CommodityCategoryMailAndParcel"
	CommodityCategoryMetalsNonPrecious              CommodityCategoryEnum = "CommodityCategoryMetalsNonPrecious"
	CommodityCategoryMetalsPrecious                 CommodityCategoryEnum = "CommodityCategoryMetalsPrecious"
	CommodityCategoryOther                          CommodityCategoryEnum = "CommodityCategoryOther"
	CommodityCategoryPaperAndPlasticProducts        CommodityCategoryEnum = "CommodityCategoryPaperAndPlasticProducts"
	CommodityCategoryReeferMeatAndSeafood           CommodityCategoryEnum = "CommodityCategoryReeferMeatAndSeafood"
	CommodityCategoryReeferNonMeat                  CommodityCategoryEnum = "CommodityCategoryReeferNonMeat"
	CommodityCategoryTextiles                       CommodityCategoryEnum = "CommodityCategoryTextiles"
)

// Defines values for CommodityHauledEnum.
const (
	CommodityHauledEnumCommodityHauledAdipicPellets                                                       CommodityHauledEnum = "CommodityHauledAdipicPellets"
	CommodityHauledEnumCommodityHauledAggregatesRockOrSandOrGravelOrStoneOrDirt                           CommodityHauledEnum = "CommodityHauledAggregatesRockOrSandOrGravelOrStoneOrDirt"
	CommodityHauledEnumCommodityHauledAgriculturalEquipment                                               CommodityHauledEnum = "CommodityHauledAgriculturalEquipment"
	CommodityHauledEnumCommodityHauledAircraftOrJetEngines                                                CommodityHauledEnum = "CommodityHauledAircraftOrJetEngines"
	CommodityHauledEnumCommodityHauledAllTerrainVehicles                                                  CommodityHauledEnum = "CommodityHauledAllTerrainVehicles"
	CommodityHauledEnumCommodityHauledAluminumChloride                                                    CommodityHauledEnum = "CommodityHauledAluminumChloride"
	CommodityHauledEnumCommodityHauledAmmoniumNitrateOrFertilizer                                         CommodityHauledEnum = "CommodityHauledAmmoniumNitrateOrFertilizer"
	CommodityHauledEnumCommodityHauledAmusementDevices                                                    CommodityHauledEnum = "CommodityHauledAmusementDevices"
	CommodityHauledEnumCommodityHauledAnhydrousAmmoniaUN1005Class203                                      CommodityHauledEnum = "CommodityHauledAnhydrousAmmonia_UN1005_Class203"
	CommodityHauledEnumCommodityHauledAppliances                                                          CommodityHauledEnum = "CommodityHauledAppliances"
	CommodityHauledEnumCommodityHauledAppliancesOrHardware                                                CommodityHauledEnum = "CommodityHauledAppliancesOrHardware"
	CommodityHauledEnumCommodityHauledAsbestos                                                            CommodityHauledEnum = "CommodityHauledAsbestos"
	CommodityHauledEnumCommodityHauledAsh                                                                 CommodityHauledEnum = "CommodityHauledAsh"
	CommodityHauledEnumCommodityHauledAsphaltAndBlacktop                                                  CommodityHauledEnum = "CommodityHauledAsphaltAndBlacktop"
	CommodityHauledEnumCommodityHauledAsphaltLiquid                                                       CommodityHauledEnum = "CommodityHauledAsphaltLiquid"
	CommodityHauledEnumCommodityHauledAutoPartsAccessories                                                CommodityHauledEnum = "CommodityHauledAutoPartsAccessories"
	CommodityHauledEnumCommodityHauledAutomobilePartsAndAccessories                                       CommodityHauledEnum = "CommodityHauledAutomobilePartsAndAccessories"
	CommodityHauledEnumCommodityHauledAutomobilesCrushedOrJunk                                            CommodityHauledEnum = "CommodityHauledAutomobiles_CrushedOrJunk"
	CommodityHauledEnumCommodityHauledAutomobilesNew                                                      CommodityHauledEnum = "CommodityHauledAutomobiles_New"
	CommodityHauledEnumCommodityHauledAutomobilesUsedOrClassicOrAntiqueOrAuctionandSnowbirdTypeOperations CommodityHauledEnum = "CommodityHauledAutomobiles_UsedOrClassicOrAntiqueOrAuctionandSnowbirdTypeOperations"
	CommodityHauledEnumCommodityHauledAutosPersonalOrPrivatePassengeOrMotorHomes                          CommodityHauledEnum = "CommodityHauledAutos_PersonalOrPrivatePassengeOrMotorHomes"
	CommodityHauledEnumCommodityHauledAutosRaceCars                                                       CommodityHauledEnum = "CommodityHauledAutos_RaceCars"
	CommodityHauledEnumCommodityHauledBakeryGoodsBreadOrPieOrPastriesOrCookieOrCakes                      CommodityHauledEnum = "CommodityHauledBakeryGoods_BreadOrPieOrPastriesOrCookieOrCakes"
	CommodityHauledEnumCommodityHauledBananas                                                             CommodityHauledEnum = "CommodityHauledBananas"
	CommodityHauledEnumCommodityHauledBatteriesDryCarOrMarineOrCampersOretc                               CommodityHauledEnum = "CommodityHauledBatteries_Dry_CarOrMarineOrCampersOretc"
	CommodityHauledEnumCommodityHauledBatteriesHousehold                                                  CommodityHauledEnum = "CommodityHauledBatteries_Household"
	CommodityHauledEnumCommodityHauledBatteriesWetCarOrMarineOrCamperOretc                                CommodityHauledEnum = "CommodityHauledBatteries_Wet_CarOrMarineOrCamperOretc"
	CommodityHauledEnumCommodityHauledBeansSoybeansOrKidneyOrPeasOrLentilsOrChickpeas                     CommodityHauledEnum = "CommodityHauledBeans_SoybeansOrKidneyOrPeasOrLentilsOrChickpeas"
	CommodityHauledEnumCommodityHauledBeerOrWineOrBrandy                                                  CommodityHauledEnum = "CommodityHauledBeerOrWineOrBrandy"
	CommodityHauledEnumCommodityHauledBees                                                                CommodityHauledEnum = "CommodityHauledBees"
	CommodityHauledEnumCommodityHauledBeveragesAlcoholic                                                  CommodityHauledEnum = "CommodityHauledBeveragesAlcoholic"
	CommodityHauledEnumCommodityHauledBeveragesNonAlcoholic                                               CommodityHauledEnum = "CommodityHauledBeveragesNonAlcoholic"
	CommodityHauledEnumCommodityHauledBoats                                                               CommodityHauledEnum = "CommodityHauledBoats"
	CommodityHauledEnumCommodityHauledBoats25feetandOverInLengthNewOrUsed                                 CommodityHauledEnum = "CommodityHauledBoats25feetandOverInLength_NewOrUsed"
	CommodityHauledEnumCommodityHauledBoatsUnder25feetLengthNewOrUsed                                     CommodityHauledEnum = "CommodityHauledBoatsUnder25feetLength_NewOrUsed"
	CommodityHauledEnumCommodityHauledBottledAndCannedSoftdrinks                                          CommodityHauledEnum = "CommodityHauledBottledAndCannedSoftdrinks"
	CommodityHauledEnumCommodityHauledBottledWater                                                        CommodityHauledEnum = "CommodityHauledBottledWater"
	CommodityHauledEnumCommodityHauledBricks                                                              CommodityHauledEnum = "CommodityHauledBricks"
	CommodityHauledEnumCommodityHauledBuildingMaterialsFinished                                           CommodityHauledEnum = "CommodityHauledBuildingMaterialsFinished"
	CommodityHauledEnumCommodityHauledBuildingMaterialsRaw                                                CommodityHauledEnum = "CommodityHauledBuildingMaterialsRaw"
	CommodityHauledEnumCommodityHauledBulldozer                                                           CommodityHauledEnum = "CommodityHauledBulldozer"
	CommodityHauledEnumCommodityHauledCadmium                                                             CommodityHauledEnum = "CommodityHauledCadmium"
	CommodityHauledEnumCommodityHauledCalciumCarbide                                                      CommodityHauledEnum = "CommodityHauledCalciumCarbide"
	CommodityHauledEnumCommodityHauledCalciumChlorideSolution                                             CommodityHauledEnum = "CommodityHauledCalciumChlorideSolution"
	CommodityHauledEnumCommodityHauledCamerasAndPhotography                                               CommodityHauledEnum = "CommodityHauledCamerasAndPhotography"
	CommodityHauledEnumCommodityHauledCampersandRecreationalVehicles                                      CommodityHauledEnum = "CommodityHauledCampersandRecreationalVehicles"
	CommodityHauledEnumCommodityHauledCannedGoods                                                         CommodityHauledEnum = "CommodityHauledCannedGoods"
	CommodityHauledEnumCommodityHauledCarbonBlack                                                         CommodityHauledEnum = "CommodityHauledCarbonBlack"
	CommodityHauledEnumCommodityHauledCarnivalOrCircusEquipmentOrRides                                    CommodityHauledEnum = "CommodityHauledCarnivalOrCircusEquipmentOrRides"
	CommodityHauledEnumCommodityHauledCarpeting                                                           CommodityHauledEnum = "CommodityHauledCarpeting"
	CommodityHauledEnumCommodityHauledCeilingOrFlooringAndWallCovering                                    CommodityHauledEnum = "CommodityHauledCeilingOrFlooringAndWallCovering"
	CommodityHauledEnumCommodityHauledCementAndConcreteBulk                                               CommodityHauledEnum = "CommodityHauledCementAndConcrete_Bulk"
	CommodityHauledEnumCommodityHauledCeramicTileOrQuarryTileOrPaversOrProsaic                            CommodityHauledEnum = "CommodityHauledCeramicTileOrQuarryTileOrPaversOrProsaic"
	CommodityHauledEnumCommodityHauledCereals                                                             CommodityHauledEnum = "CommodityHauledCereals"
	CommodityHauledEnumCommodityHauledCharcoal                                                            CommodityHauledEnum = "CommodityHauledCharcoal"
	CommodityHauledEnumCommodityHauledChemicalsBulkLiquid                                                 CommodityHauledEnum = "CommodityHauledChemicalsBulkLiquid"
	CommodityHauledEnumCommodityHauledChickenSludgeOrGutsHotOffalOrChickenFat                             CommodityHauledEnum = "CommodityHauledChickenSludgeOrGuts_HotOffalOrChickenFat"
	CommodityHauledEnumCommodityHauledChinaAndCeramics                                                    CommodityHauledEnum = "CommodityHauledChinaAndCeramics"
	CommodityHauledEnumCommodityHauledChipsOrCandyAndOtherSnackFoods                                      CommodityHauledEnum = "CommodityHauledChipsOrCandyAndOtherSnackFoods"
	CommodityHauledEnumCommodityHauledChlorine                                                            CommodityHauledEnum = "CommodityHauledChlorine"
	CommodityHauledEnumCommodityHauledClass1Explosives                                                    CommodityHauledEnum = "CommodityHauledClass1Explosives"
	CommodityHauledEnumCommodityHauledClass21FlammableGas                                                 CommodityHauledEnum = "CommodityHauledClass2_1_FlammableGas"
	CommodityHauledEnumCommodityHauledClass22NonFlammableCompressedGas                                    CommodityHauledEnum = "CommodityHauledClass2_2_NonFlammableCompressedGas"
	CommodityHauledEnumCommodityHauledClass23PoisonousGas                                                 CommodityHauledEnum = "CommodityHauledClass2_3_PoisonousGas"
	CommodityHauledEnumCommodityHauledClass2Gases                                                         CommodityHauledEnum = "CommodityHauledClass2Gases"
	CommodityHauledEnumCommodityHauledClass3FlammableOrCombustibleLiquids                                 CommodityHauledEnum = "CommodityHauledClass3FlammableOrCombustibleLiquids"
	CommodityHauledEnumCommodityHauledClass3FlammablePackagingGroupI                                      CommodityHauledEnum = "CommodityHauledClass3_Flammable_PackagingGroupI"
	CommodityHauledEnumCommodityHauledClass3FlammablePackagingGroupII                                     CommodityHauledEnum = "CommodityHauledClass3_Flammable_PackagingGroupII"
	CommodityHauledEnumCommodityHauledClass3FlammablePackagingGroupIII                                    CommodityHauledEnum = "CommodityHauledClass3_Flammable_PackagingGroupIII"
	CommodityHauledEnumCommodityHauledClass41FlammableSolidPackagingGroupI                                CommodityHauledEnum = "CommodityHauledClass4_1_FlammableSolid_PackagingGroupI"
	CommodityHauledEnumCommodityHauledClass41FlammableSolidPackagingGroupII                               CommodityHauledEnum = "CommodityHauledClass4_1_FlammableSolid_PackagingGroupII"
	CommodityHauledEnumCommodityHauledClass41FlammableSolidPackagingGroupIII                              CommodityHauledEnum = "CommodityHauledClass4_1_FlammableSolid_PackagingGroupIII"
	CommodityHauledEnumCommodityHauledClass42SpontaneouslyCombustibleMaterial                             CommodityHauledEnum = "CommodityHauledClass4_2_SpontaneouslyCombustibleMaterial"
	CommodityHauledEnumCommodityHauledClass43DangerousWhenWetMaterial                                     CommodityHauledEnum = "CommodityHauledClass4_3_DangerousWhenWetMaterial"
	CommodityHauledEnumCommodityHauledClass4FlammableSolids                                               CommodityHauledEnum = "CommodityHauledClass4FlammableSolids"
	CommodityHauledEnumCommodityHauledClass51OxidizerPackagingGroupI                                      CommodityHauledEnum = "CommodityHauledClass5_1_Oxidizer_PackagingGroupI"
	CommodityHauledEnumCommodityHauledClass51OxidizerPackagingGroupII                                     CommodityHauledEnum = "CommodityHauledClass5_1_Oxidizer_PackagingGroupII"
	CommodityHauledEnumCommodityHauledClass51OxidizerPackagingGroupIII                                    CommodityHauledEnum = "CommodityHauledClass5_1_Oxidizer_PackagingGroupIII"
	CommodityHauledEnumCommodityHauledClass52OrganicPeroxide                                              CommodityHauledEnum = "CommodityHauledClass5_2_OrganicPeroxide"
	CommodityHauledEnumCommodityHauledClass5OxidizingSubstancesOrOrganicPeroxides                         CommodityHauledEnum = "CommodityHauledClass5OxidizingSubstancesOrOrganicPeroxides"
	CommodityHauledEnumCommodityHauledClass61PoisonousMaterialsInhalationHazard                           CommodityHauledEnum = "CommodityHauledClass6_1_PoisonousMaterials_InhalationHazard"
	CommodityHauledEnumCommodityHauledClass61PoisonousMaterialsOtherthanInhalationHazard                  CommodityHauledEnum = "CommodityHauledClass6_1_PoisonousMaterials_OtherthanInhalationHazard"
	CommodityHauledEnumCommodityHauledClass62InfectiousSubstancesPackagingGroupI                          CommodityHauledEnum = "CommodityHauledClass6_2_InfectiousSubstances_PackagingGroupI"
	CommodityHauledEnumCommodityHauledClass62InfectiousSubstancesPackagingGroupII                         CommodityHauledEnum = "CommodityHauledClass6_2_InfectiousSubstances_PackagingGroupII"
	CommodityHauledEnumCommodityHauledClass62InfectiousSubstancesPackagingGroupIII                        CommodityHauledEnum = "CommodityHauledClass6_2_InfectiousSubstances_PackagingGroupIII"
	CommodityHauledEnumCommodityHauledClass6ToxinsOrInfectiousSubstances                                  CommodityHauledEnum = "CommodityHauledClass6ToxinsOrInfectiousSubstances"
	CommodityHauledEnumCommodityHauledClass7Radioactive                                                   CommodityHauledEnum = "CommodityHauledClass7_Radioactive"
	CommodityHauledEnumCommodityHauledClass7RadioactiveMaterials                                          CommodityHauledEnum = "CommodityHauledClass7RadioactiveMaterials"
	CommodityHauledEnumCommodityHauledClass8Corrosives                                                    CommodityHauledEnum = "CommodityHauledClass8Corrosives"
	CommodityHauledEnumCommodityHauledClass8CorrosivesPackagingGroupI                                     CommodityHauledEnum = "CommodityHauledClass8_Corrosives_PackagingGroupI"
	CommodityHauledEnumCommodityHauledClass8CorrosivesPackagingGroupII                                    CommodityHauledEnum = "CommodityHauledClass8_Corrosives_PackagingGroupII"
	CommodityHauledEnumCommodityHauledClass8CorrosivesPackagingGroupIII                                   CommodityHauledEnum = "CommodityHauledClass8_Corrosives_PackagingGroupIII"
	CommodityHauledEnumCommodityHauledClass9MiscellaneousHazardousMaterials                               CommodityHauledEnum = "CommodityHauledClass9_MiscellaneousHazardousMaterials"
	CommodityHauledEnumCommodityHauledClass9MiscellaneousOrNotOtherwiseSpecified                          CommodityHauledEnum = "CommodityHauledClass9MiscellaneousOrNotOtherwiseSpecified"
	CommodityHauledEnumCommodityHauledClay                                                                CommodityHauledEnum = "CommodityHauledClay"
	CommodityHauledEnumCommodityHauledClothing                                                            CommodityHauledEnum = "CommodityHauledClothing"
	CommodityHauledEnumCommodityHauledCoal                                                                CommodityHauledEnum = "CommodityHauledCoal"
	CommodityHauledEnumCommodityHauledCoalDustPowderTitaniumDioxide                                       CommodityHauledEnum = "CommodityHauledCoalDust_Powder_TitaniumDioxide"
	CommodityHauledEnumCommodityHauledCommunicationEquipment                                              CommodityHauledEnum = "CommodityHauledCommunicationEquipment"
	CommodityHauledEnumCommodityHauledCommunicationsCellPhones                                            CommodityHauledEnum = "CommodityHauledCommunications_CellPhones"
	CommodityHauledEnumCommodityHauledCompressedGasesandHeatingOil                                        CommodityHauledEnum = "CommodityHauledCompressedGasesandHeatingOil"
	CommodityHauledEnumCommodityHauledComputerAccessories                                                 CommodityHauledEnum = "CommodityHauledComputerAccessories"
	CommodityHauledEnumCommodityHauledComputerComponents                                                  CommodityHauledEnum = "CommodityHauledComputerComponents"
	CommodityHauledEnumCommodityHauledComputerHardware                                                    CommodityHauledEnum = "CommodityHauledComputerHardware"
	CommodityHauledEnumCommodityHauledConcreteProducts                                                    CommodityHauledEnum = "CommodityHauledConcreteProducts"
	CommodityHauledEnumCommodityHauledConfectionaryProducts                                               CommodityHauledEnum = "CommodityHauledConfectionaryProducts"
	CommodityHauledEnumCommodityHauledConstructionDebrisIncludesDemolitionDebris                          CommodityHauledEnum = "CommodityHauledConstructionDebris_IncludesDemolitionDebris"
	CommodityHauledEnumCommodityHauledConstructionEquipment                                               CommodityHauledEnum = "CommodityHauledConstructionEquipment"
	CommodityHauledEnumCommodityHauledContaminatedDirtorSoil                                              CommodityHauledEnum = "CommodityHauledContaminatedDirtorSoil"
	CommodityHauledEnumCommodityHauledCopperAndCopperProducts                                             CommodityHauledEnum = "CommodityHauledCopperAndCopperProducts"
	CommodityHauledEnumCommodityHauledCorrosiveSolidsSalt                                                 CommodityHauledEnum = "CommodityHauledCorrosiveSolids_Salt"
	CommodityHauledEnumCommodityHauledCosmetics                                                           CommodityHauledEnum = "CommodityHauledCosmetics"
	CommodityHauledEnumCommodityHauledCottonGinned                                                        CommodityHauledEnum = "CommodityHauledCotton_Ginned"
	CommodityHauledEnumCommodityHauledCowhidesRawHides                                                    CommodityHauledEnum = "CommodityHauledCowhides_RawHides"
	CommodityHauledEnumCommodityHauledCranesBooms                                                         CommodityHauledEnum = "CommodityHauledCranes_Booms"
	CommodityHauledEnumCommodityHauledDairyProducts                                                       CommodityHauledEnum = "CommodityHauledDairyProducts"
	CommodityHauledEnumCommodityHauledDietFoods                                                           CommodityHauledEnum = "CommodityHauledDietFoods"
	CommodityHauledEnumCommodityHauledDimensionStoneStoneSlabsGraniteMarbleLimestoneSlate                 CommodityHauledEnum = "CommodityHauledDimensionStone_StoneSlabs_Granite_Marble_Limestone_Slate"
	CommodityHauledEnumCommodityHauledDriveTowAwayTrailersTractors                                        CommodityHauledEnum = "CommodityHauledDrive_TowAwayTrailers_Tractors"
	CommodityHauledEnumCommodityHauledDrugsOverTheCounterDrugsMedications                                 CommodityHauledEnum = "CommodityHauledDrugs_Over_the_CounterDrugs_Medications"
	CommodityHauledEnumCommodityHauledDrugsPrescriptionsAndPharmaceuticals                                CommodityHauledEnum = "CommodityHauledDrugs_PrescriptionsAndPharmaceuticals"
	CommodityHauledEnumCommodityHauledDryChemicals                                                        CommodityHauledEnum = "CommodityHauledDryChemicals"
	CommodityHauledEnumCommodityHauledDryFoods                                                            CommodityHauledEnum = "CommodityHauledDryFoods"
	CommodityHauledEnumCommodityHauledDryIce                                                              CommodityHauledEnum = "CommodityHauledDryIce"
	CommodityHauledEnumCommodityHauledEdibleOils                                                          CommodityHauledEnum = "CommodityHauledEdibleOils"
	CommodityHauledEnumCommodityHauledEggs                                                                CommodityHauledEnum = "CommodityHauledEggs"
	CommodityHauledEnumCommodityHauledElectricalEquipment                                                 CommodityHauledEnum = "CommodityHauledElectricalEquipment"
	CommodityHauledEnumCommodityHauledElectricalPartsSuppliesorFixturesSupplies                           CommodityHauledEnum = "CommodityHauledElectricalParts_SuppliesorFixtures_Supplies"
	CommodityHauledEnumCommodityHauledElectricalSystemsAndEquipment                                       CommodityHauledEnum = "CommodityHauledElectricalSystemsAndEquipment"
	CommodityHauledEnumCommodityHauledElectronics                                                         CommodityHauledEnum = "CommodityHauledElectronics"
	CommodityHauledEnumCommodityHauledElectronicsAndComputersnototherwiseclassified                       CommodityHauledEnum = "CommodityHauledElectronicsAndComputersnototherwiseclassified"
	CommodityHauledEnumCommodityHauledEmptyContainers                                                     CommodityHauledEnum = "CommodityHauledEmptyContainers"
	CommodityHauledEnumCommodityHauledEnginesOrMachinery                                                  CommodityHauledEnum = "CommodityHauledEnginesOrMachinery"
	CommodityHauledEnumCommodityHauledExoticCircusZooorWildAnimalsLive                                    CommodityHauledEnum = "CommodityHauledExotic_Circus_ZooorWildAnimals_Live"
	CommodityHauledEnumCommodityHauledExplosivesDetonators                                                CommodityHauledEnum = "CommodityHauledExplosives_Detonators"
	CommodityHauledEnumCommodityHauledFeedSeeds                                                           CommodityHauledEnum = "CommodityHauledFeed_Seeds"
	CommodityHauledEnumCommodityHauledFertilizerDryBagged                                                 CommodityHauledEnum = "CommodityHauledFertilizer_Dry_Bagged"
	CommodityHauledEnumCommodityHauledFertilizerLiquid                                                    CommodityHauledEnum = "CommodityHauledFertilizer_Liquid"
	CommodityHauledEnumCommodityHauledFertilizers                                                         CommodityHauledEnum = "CommodityHauledFertilizers"
	CommodityHauledEnumCommodityHauledFilmCelluloidScrap                                                  CommodityHauledEnum = "CommodityHauledFilm_CelluloidScrap"
	CommodityHauledEnumCommodityHauledFirearmsAndSuppliesAmmunitionBlackPowderEtc                         CommodityHauledEnum = "CommodityHauledFirearmsAndSupplies_Ammunition_BlackPowder_etc"
	CommodityHauledEnumCommodityHauledFirewood                                                            CommodityHauledEnum = "CommodityHauledFirewood"
	CommodityHauledEnumCommodityHauledFireworks                                                           CommodityHauledEnum = "CommodityHauledFireworks"
	CommodityHauledEnumCommodityHauledFishAndSeafoodFreshorFrozen                                         CommodityHauledEnum = "CommodityHauledFishAndSeafoodFreshorFrozen"
	CommodityHauledEnumCommodityHauledFishLive                                                            CommodityHauledEnum = "CommodityHauledFish_Live"
	CommodityHauledEnumCommodityHauledFlammables                                                          CommodityHauledEnum = "CommodityHauledFlammables"
	CommodityHauledEnumCommodityHauledFlour                                                               CommodityHauledEnum = "CommodityHauledFlour"
	CommodityHauledEnumCommodityHauledFlowersOrPlants                                                     CommodityHauledEnum = "CommodityHauledFlowersOrPlants"
	CommodityHauledEnumCommodityHauledFlyAsh                                                              CommodityHauledEnum = "CommodityHauledFlyAsh"
	CommodityHauledEnumCommodityHauledFreshFoodsProduceVegetablesFruit                                    CommodityHauledEnum = "CommodityHauledFreshFoods_Produce_Vegetables_Fruit"
	CommodityHauledEnumCommodityHauledFrozenFoodsVegetablesFruitExcludingFishAndSeafood                   CommodityHauledEnum = "CommodityHauledFrozenFoods_Vegetables_Fruit_ExcludingFishAndSeafood"
	CommodityHauledEnumCommodityHauledFrozenReadytoBakeCookFoods                                          CommodityHauledEnum = "CommodityHauledFrozen_ReadytoBake_CookFoods"
	CommodityHauledEnumCommodityHauledFurFurSkinPelts                                                     CommodityHauledEnum = "CommodityHauledFur_FurSkin_Pelts"
	CommodityHauledEnumCommodityHauledFurniture                                                           CommodityHauledEnum = "CommodityHauledFurniture"
	CommodityHauledEnumCommodityHauledGameorWildBirdsLivePheasantsQuailDuckGeese                          CommodityHauledEnum = "CommodityHauledGameorWildBirds_LivePheasants_Quail_Duck_Geese"
	CommodityHauledEnumCommodityHauledGasoline                                                            CommodityHauledEnum = "CommodityHauledGasoline"
	CommodityHauledEnumCommodityHauledGeneralDryFreight                                                   CommodityHauledEnum = "CommodityHauledGeneralDryFreight"
	CommodityHauledEnumCommodityHauledGlassDustPowder                                                     CommodityHauledEnum = "CommodityHauledGlassDust_Powder"
	CommodityHauledEnumCommodityHauledGlassFlat                                                           CommodityHauledEnum = "CommodityHauledGlass_Flat"
	CommodityHauledEnumCommodityHauledGlassProducts                                                       CommodityHauledEnum = "CommodityHauledGlassProducts"
	CommodityHauledEnumCommodityHauledGolfCarts                                                           CommodityHauledEnum = "CommodityHauledGolfCarts"
	CommodityHauledEnumCommodityHauledGrainOrSeedOrFeed                                                   CommodityHauledEnum = "CommodityHauledGrainOrSeedOrFeed"
	CommodityHauledEnumCommodityHauledGrainsCornWheatBarleyRiceOatsRyePeanutsSorghum                      CommodityHauledEnum = "CommodityHauledGrains_Corn_Wheat_Barley_Rice_Oats_Rye_Peanuts_Sorghum"
	CommodityHauledEnumCommodityHauledGypsum                                                              CommodityHauledEnum = "CommodityHauledGypsum"
	CommodityHauledEnumCommodityHauledHandToolsPowerTools                                                 CommodityHauledEnum = "CommodityHauledHandTools_PowerTools"
	CommodityHauledEnumCommodityHauledHandheldDevicesGPSPDAsIPODsHeadsetsHeadphonesMP3                    CommodityHauledEnum = "CommodityHauledHandheldDevices_GPS_PDAs_I_PODs_Headsets_Headphones_MP3"
	CommodityHauledEnumCommodityHauledHay                                                                 CommodityHauledEnum = "CommodityHauledHay"
	CommodityHauledEnumCommodityHauledHazardousMaterialsOrBatteries                                       CommodityHauledEnum = "CommodityHauledHazardousMaterialsOrBatteries"
	CommodityHauledEnumCommodityHauledHazardouswaste                                                      CommodityHauledEnum = "CommodityHauledHazardouswaste"
	CommodityHauledEnumCommodityHauledHealthAids                                                          CommodityHauledEnum = "CommodityHauledHealthAids"
	CommodityHauledEnumCommodityHauledHeatingVentilationAndAirConditioning                                CommodityHauledEnum = "CommodityHauledHeating_VentilationAndAirConditioning"
	CommodityHauledEnumCommodityHauledHomeAudio                                                           CommodityHauledEnum = "CommodityHauledHomeAudio"
	CommodityHauledEnumCommodityHauledHomeFurnishingsAndAccessoriesDrapes                                 CommodityHauledEnum = "CommodityHauledHomeFurnishingsAndAccessories_Drapes"
	CommodityHauledEnumCommodityHauledHouseAndBuildingMovers                                              CommodityHauledEnum = "CommodityHauledHouseAndBuildingMovers"
	CommodityHauledEnumCommodityHauledHouseholdCleaningProducts                                           CommodityHauledEnum = "CommodityHauledHouseholdCleaningProducts"
	CommodityHauledEnumCommodityHauledHouseholdHardwareFastenersKeysLocksHingesChains                     CommodityHauledEnum = "CommodityHauledHouseholdHardware_Fasteners_Keys_Locks_Hinges_Chains"
	CommodityHauledEnumCommodityHauledHouseholdHardwarePlumbingSuppliesPaintCabinetBath                   CommodityHauledEnum = "CommodityHauledHouseholdHardware_PlumbingSupplies_Paint_Cabinet_Bath"
	CommodityHauledEnumCommodityHauledHousewaresCookingUtensilsDishesEtc                                  CommodityHauledEnum = "CommodityHauledHousewares_CookingUtensils_Dishes_etc_"
	CommodityHauledEnumCommodityHauledIceCreamIce                                                         CommodityHauledEnum = "CommodityHauledIceCream_Ice"
	CommodityHauledEnumCommodityHauledIndustrialEquipment                                                 CommodityHauledEnum = "CommodityHauledIndustrialEquipment"
	CommodityHauledEnumCommodityHauledInkWaterSoluble                                                     CommodityHauledEnum = "CommodityHauledInk_WaterSoluble"
	CommodityHauledEnumCommodityHauledInputDevices                                                        CommodityHauledEnum = "CommodityHauledInputDevices"
	CommodityHauledEnumCommodityHauledIntegratedCircuitComputerChipMicroprocessorsSemiconductorHardDrive  CommodityHauledEnum = "CommodityHauledIntegratedCircuit_ComputerChip_Microprocessors_Semiconductor_HardDrive"
	CommodityHauledEnumCommodityHauledIntermodalContainerContainerizedFreight                             CommodityHauledEnum = "CommodityHauledIntermodalContainer_ContainerizedFreight"
	CommodityHauledEnumCommodityHauledJewelry                                                             CommodityHauledEnum = "CommodityHauledJewelry"
	CommodityHauledEnumCommodityHauledJunk                                                                CommodityHauledEnum = "CommodityHauledJunk"
	CommodityHauledEnumCommodityHauledLargeMachinery                                                      CommodityHauledEnum = "CommodityHauledLargeMachinery"
	CommodityHauledEnumCommodityHauledLawnandGarden                                                       CommodityHauledEnum = "CommodityHauledLawnandGarden"
	CommodityHauledEnumCommodityHauledLeadPowderDust                                                      CommodityHauledEnum = "CommodityHauledLead_Powder_Dust"
	CommodityHauledEnumCommodityHauledLeather                                                             CommodityHauledEnum = "CommodityHauledLeather"
	CommodityHauledEnumCommodityHauledLimeSlackedAndUnslacked                                             CommodityHauledEnum = "CommodityHauledLime_SlackedAndUnslacked"
	CommodityHauledEnumCommodityHauledLimestone                                                           CommodityHauledEnum = "CommodityHauledLimestone"
	CommodityHauledEnumCommodityHauledLiquidLatex                                                         CommodityHauledEnum = "CommodityHauledLiquidLatex"
	CommodityHauledEnumCommodityHauledLiquidsFuel                                                         CommodityHauledEnum = "CommodityHauledLiquidsFuel"
	CommodityHauledEnumCommodityHauledLiquidsMilk                                                         CommodityHauledEnum = "CommodityHauledLiquidsMilk"
	CommodityHauledEnumCommodityHauledLiquidsOtherNonFlammable                                            CommodityHauledEnum = "CommodityHauledLiquidsOtherNonFlammable"
	CommodityHauledEnumCommodityHauledLiquorExcludingBeerAndWine                                          CommodityHauledEnum = "CommodityHauledLiquor_ExcludingBeerAndWine"
	CommodityHauledEnumCommodityHauledLivestockLivePigsCattleSheepHorses                                  CommodityHauledEnum = "CommodityHauledLivestock_LivePigs_Cattle_Sheep_Horses"
	CommodityHauledEnumCommodityHauledLogsLoggersWoodHarvestingPulp                                       CommodityHauledEnum = "CommodityHauledLogs_Loggers_WoodHarvesting_Pulp"
	CommodityHauledEnumCommodityHauledLumber                                                              CommodityHauledEnum = "CommodityHauledLumber"
	CommodityHauledEnumCommodityHauledLumberOrLogs                                                        CommodityHauledEnum = "CommodityHauledLumberOrLogs"
	CommodityHauledEnumCommodityHauledMachineryAndHeavyEquipment                                          CommodityHauledEnum = "CommodityHauledMachineryAndHeavyEquipment"
	CommodityHauledEnumCommodityHauledMail                                                                CommodityHauledEnum = "CommodityHauledMail"
	CommodityHauledEnumCommodityHauledMailParcelsOrAmazon                                                 CommodityHauledEnum = "CommodityHauledMailParcelsOrAmazon"
	CommodityHauledEnumCommodityHauledMailUSPS                                                            CommodityHauledEnum = "CommodityHauledMailUSPS"
	CommodityHauledEnumCommodityHauledManureAndFertilizerBulk                                             CommodityHauledEnum = "CommodityHauledManureAndFertilizer_Bulk"
	CommodityHauledEnumCommodityHauledMedicalInstruments                                                  CommodityHauledEnum = "CommodityHauledMedicalInstruments"
	CommodityHauledEnumCommodityHauledMetalProducts                                                       CommodityHauledEnum = "CommodityHauledMetalProducts"
	CommodityHauledEnumCommodityHauledMetalSheetsCoilsRolls                                               CommodityHauledEnum = "CommodityHauledMetalSheets_Coils_Rolls"
	CommodityHauledEnumCommodityHauledMetalsBarsOrBeams                                                   CommodityHauledEnum = "CommodityHauledMetalsBarsOrBeams"
	CommodityHauledEnumCommodityHauledMetalsCopper                                                        CommodityHauledEnum = "CommodityHauledMetalsCopper"
	CommodityHauledEnumCommodityHauledMetalsRolledOrCoiledSteels                                          CommodityHauledEnum = "CommodityHauledMetalsRolledOrCoiledSteels"
	CommodityHauledEnumCommodityHauledMethylBromide                                                       CommodityHauledEnum = "CommodityHauledMethylBromide"
	CommodityHauledEnumCommodityHauledMethylMethanolAlcohol                                               CommodityHauledEnum = "CommodityHauledMethyl_MethanolAlcohol"
	CommodityHauledEnumCommodityHauledMilk                                                                CommodityHauledEnum = "CommodityHauledMilk"
	CommodityHauledEnumCommodityHauledMobileHomes                                                         CommodityHauledEnum = "CommodityHauledMobileHomes"
	CommodityHauledEnumCommodityHauledMobileModularHomes                                                  CommodityHauledEnum = "CommodityHauledMobile_ModularHomes"
	CommodityHauledEnumCommodityHauledMolasses                                                            CommodityHauledEnum = "CommodityHauledMolasses"
	CommodityHauledEnumCommodityHauledMotorcycles                                                         CommodityHauledEnum = "CommodityHauledMotorcycles"
	CommodityHauledEnumCommodityHauledMulchTopSoilorFill                                                  CommodityHauledEnum = "CommodityHauledMulch_TopSoilorFill"
	CommodityHauledEnumCommodityHauledMultimediaProjectors                                                CommodityHauledEnum = "CommodityHauledMultimediaProjectors"
	CommodityHauledEnumCommodityHauledMusicalInstruments                                                  CommodityHauledEnum = "CommodityHauledMusicalInstruments"
	CommodityHauledEnumCommodityHauledMuslin                                                              CommodityHauledEnum = "CommodityHauledMuslin"
	CommodityHauledEnumCommodityHauledNetworking                                                          CommodityHauledEnum = "CommodityHauledNetworking"
	CommodityHauledEnumCommodityHauledNutrition                                                           CommodityHauledEnum = "CommodityHauledNutrition"
	CommodityHauledEnumCommodityHauledNutsAndSeeds                                                        CommodityHauledEnum = "CommodityHauledNutsAndSeeds"
	CommodityHauledEnumCommodityHauledOfficeEquipmentMachinesAndSupplies                                  CommodityHauledEnum = "CommodityHauledOfficeEquipment_MachinesAndSupplies"
	CommodityHauledEnumCommodityHauledOilFieldEquipment                                                   CommodityHauledEnum = "CommodityHauledOilFieldEquipment"
	CommodityHauledEnumCommodityHauledOilLubricatingInTankers                                             CommodityHauledEnum = "CommodityHauledOil_Lubricating_InTankers"
	CommodityHauledEnumCommodityHauledOilinBarrelsorSmallCans                                             CommodityHauledEnum = "CommodityHauledOilinBarrelsorSmallCans"
	CommodityHauledEnumCommodityHauledOres                                                                CommodityHauledEnum = "CommodityHauledOres"
	CommodityHauledEnumCommodityHauledOtherBuildingMaterialsNotOtherwiseClassified                        CommodityHauledEnum = "CommodityHauledOtherBuildingMaterialsNotOtherwiseClassified"
	CommodityHauledEnumCommodityHauledOtherPaperPlasticGlass                                              CommodityHauledEnum = "CommodityHauledOtherPaper_Plastic_Glass"
	CommodityHauledEnumCommodityHauledOversizedOrOverweight                                               CommodityHauledEnum = "CommodityHauledOversizedOrOverweight"
	CommodityHauledEnumCommodityHauledOxidizers                                                           CommodityHauledEnum = "CommodityHauledOxidizers"
	CommodityHauledEnumCommodityHauledPackingMaterialAndSupplies                                          CommodityHauledEnum = "CommodityHauledPackingMaterialAndSupplies"
	CommodityHauledEnumCommodityHauledPaintAndPaintThinnersCannedorBulk                                   CommodityHauledEnum = "CommodityHauledPaintAndPaintThinners_CannedorBulk"
	CommodityHauledEnumCommodityHauledPaperAndPaperProductsIncludingPackagingMaterials                    CommodityHauledEnum = "CommodityHauledPaperAndPaperProducts_IncludingPackagingMaterials"
	CommodityHauledEnumCommodityHauledPaperOrPlasticProducts                                              CommodityHauledEnum = "CommodityHauledPaperOrPlasticProducts"
	CommodityHauledEnumCommodityHauledPaperShredded                                                       CommodityHauledEnum = "CommodityHauledPaperShredded"
	CommodityHauledEnumCommodityHauledPerfumesAndColognes                                                 CommodityHauledEnum = "CommodityHauledPerfumesAndColognes"
	CommodityHauledEnumCommodityHauledPersonalCare                                                        CommodityHauledEnum = "CommodityHauledPersonalCare"
	CommodityHauledEnumCommodityHauledPetSuppliesFoodEtc                                                  CommodityHauledEnum = "CommodityHauledPetSupplies_Food_etc_"
	CommodityHauledEnumCommodityHauledPetroleumProductsBulkorinTankers                                    CommodityHauledEnum = "CommodityHauledPetroleumProducts_BulkorinTankers"
	CommodityHauledEnumCommodityHauledPharmaceuticals                                                     CommodityHauledEnum = "CommodityHauledPharmaceuticals"
	CommodityHauledEnumCommodityHauledPipeExceptOilFieldPipe                                              CommodityHauledEnum = "CommodityHauledPipe_ExceptOilFieldPipe"
	CommodityHauledEnumCommodityHauledPlantsAndNurseryStock                                               CommodityHauledEnum = "CommodityHauledPlantsAndNurseryStock"
	CommodityHauledEnumCommodityHauledPlasterDrywallGypsumboard                                           CommodityHauledEnum = "CommodityHauledPlaster_Drywall_Gypsumboard"
	CommodityHauledEnumCommodityHauledPlasticPellets                                                      CommodityHauledEnum = "CommodityHauledPlasticPellets"
	CommodityHauledEnumCommodityHauledPlasticProducts                                                     CommodityHauledEnum = "CommodityHauledPlasticProducts"
	CommodityHauledEnumCommodityHauledPlumbingFixturesAndEquipment                                        CommodityHauledEnum = "CommodityHauledPlumbingFixturesAndEquipment"
	CommodityHauledEnumCommodityHauledPotash                                                              CommodityHauledEnum = "CommodityHauledPotash"
	CommodityHauledEnumCommodityHauledPotassiumChloride                                                   CommodityHauledEnum = "CommodityHauledPotassiumChloride"
	CommodityHauledEnumCommodityHauledPoultryFrozenFreshPackagedBoxedProcessed                            CommodityHauledEnum = "CommodityHauledPoultry_Frozen_Fresh_Packaged_Boxed_Processed"
	CommodityHauledEnumCommodityHauledPoultryLiveDucksChickensGeese                                       CommodityHauledEnum = "CommodityHauledPoultry_LiveDucks_Chickens_Geese"
	CommodityHauledEnumCommodityHauledPrepackagedFoods                                                    CommodityHauledEnum = "CommodityHauledPrepackagedFoods"
	CommodityHauledEnumCommodityHauledPrintedMaterial                                                     CommodityHauledEnum = "CommodityHauledPrintedMaterial"
	CommodityHauledEnumCommodityHauledPrinters                                                            CommodityHauledEnum = "CommodityHauledPrinters"
	CommodityHauledEnumCommodityHauledRadioactiveMaterial                                                 CommodityHauledEnum = "CommodityHauledRadioactiveMaterial"
	CommodityHauledEnumCommodityHauledRawSilk                                                             CommodityHauledEnum = "CommodityHauledRawSilk"
	CommodityHauledEnumCommodityHauledRefrigeratedFoodsNonmeat                                            CommodityHauledEnum = "CommodityHauledRefrigeratedFoodsNonmeat"
	CommodityHauledEnumCommodityHauledRefrigeratedFoodsmeat                                               CommodityHauledEnum = "CommodityHauledRefrigeratedFoodsmeat"
	CommodityHauledEnumCommodityHauledRefrigeratedGoods                                                   CommodityHauledEnum = "CommodityHauledRefrigeratedGoods"
	CommodityHauledEnumCommodityHauledRefrigerationMeat                                                   CommodityHauledEnum = "CommodityHauledRefrigerationMeat"
	CommodityHauledEnumCommodityHauledRefrigerationNonMeat                                                CommodityHauledEnum = "CommodityHauledRefrigerationNonMeat"
	CommodityHauledEnumCommodityHauledRendering                                                           CommodityHauledEnum = "CommodityHauledRendering"
	CommodityHauledEnumCommodityHauledRetailProducts                                                      CommodityHauledEnum = "CommodityHauledRetailProducts"
	CommodityHauledEnumCommodityHauledRiggingCranes                                                       CommodityHauledEnum = "CommodityHauledRigging_Cranes"
	CommodityHauledEnumCommodityHauledRock                                                                CommodityHauledEnum = "CommodityHauledRock"
	CommodityHauledEnumCommodityHauledRubberProducts                                                      CommodityHauledEnum = "CommodityHauledRubberProducts"
	CommodityHauledEnumCommodityHauledSalineSolution                                                      CommodityHauledEnum = "CommodityHauledSalineSolution"
	CommodityHauledEnumCommodityHauledSalt                                                                CommodityHauledEnum = "CommodityHauledSalt"
	CommodityHauledEnumCommodityHauledSandAndGravelCrushedStoneSlagAggregateSilica                        CommodityHauledEnum = "CommodityHauledSandAndGravel_CrushedStone_Slag_Aggregate_Silica"
	CommodityHauledEnumCommodityHauledSandOrSilicate                                                      CommodityHauledEnum = "CommodityHauledSandOrSilicate"
	CommodityHauledEnumCommodityHauledSatin                                                               CommodityHauledEnum = "CommodityHauledSatin"
	CommodityHauledEnumCommodityHauledSawdust                                                             CommodityHauledEnum = "CommodityHauledSawdust"
	CommodityHauledEnumCommodityHauledScientificInstrumentsAndEquipment                                   CommodityHauledEnum = "CommodityHauledScientificInstrumentsAndEquipment"
	CommodityHauledEnumCommodityHauledScrapMetal                                                          CommodityHauledEnum = "CommodityHauledScrapMetal"
	CommodityHauledEnumCommodityHauledScrapMetalIronorSalvageOperations                                   CommodityHauledEnum = "CommodityHauledScrapMetal_IronorSalvageOperations"
	CommodityHauledEnumCommodityHauledShavers                                                             CommodityHauledEnum = "CommodityHauledShavers"
	CommodityHauledEnumCommodityHauledShellfishMollusksCrustaceansFreshorFrozen                           CommodityHauledEnum = "CommodityHauledShellfish_Mollusks_Crustaceans_FreshorFrozen"
	CommodityHauledEnumCommodityHauledShingles                                                            CommodityHauledEnum = "CommodityHauledShingles"
	CommodityHauledEnumCommodityHauledSilk                                                                CommodityHauledEnum = "CommodityHauledSilk"
	CommodityHauledEnumCommodityHauledSludgeClassAorClassB                                                CommodityHauledEnum = "CommodityHauledSludge_ClassAorClassB"
	CommodityHauledEnumCommodityHauledSoapLiquid                                                          CommodityHauledEnum = "CommodityHauledSoap_Liquid"
	CommodityHauledEnumCommodityHauledSod                                                                 CommodityHauledEnum = "CommodityHauledSod"
	CommodityHauledEnumCommodityHauledSodiumChloride                                                      CommodityHauledEnum = "CommodityHauledSodiumChloride"
	CommodityHauledEnumCommodityHauledSodiumSulfate                                                       CommodityHauledEnum = "CommodityHauledSodiumSulfate"
	CommodityHauledEnumCommodityHauledSoftware                                                            CommodityHauledEnum = "CommodityHauledSoftware"
	CommodityHauledEnumCommodityHauledSolarPanelsCells                                                    CommodityHauledEnum = "CommodityHauledSolarPanels_Cells"
	CommodityHauledEnumCommodityHauledSportingGoods                                                       CommodityHauledEnum = "CommodityHauledSportingGoods"
	CommodityHauledEnumCommodityHauledSteelAndConstructionMetals                                          CommodityHauledEnum = "CommodityHauledSteelAndConstructionMetals"
	CommodityHauledEnumCommodityHauledStorageAndMedia                                                     CommodityHauledEnum = "CommodityHauledStorageAndMedia"
	CommodityHauledEnumCommodityHauledSuede                                                               CommodityHauledEnum = "CommodityHauledSuede"
	CommodityHauledEnumCommodityHauledSugar                                                               CommodityHauledEnum = "CommodityHauledSugar"
	CommodityHauledEnumCommodityHauledSugarLiquid                                                         CommodityHauledEnum = "CommodityHauledSugar_Liquid"
	CommodityHauledEnumCommodityHauledSwingingmeat                                                        CommodityHauledEnum = "CommodityHauledSwingingmeat"
	CommodityHauledEnumCommodityHauledTVAndVideo                                                          CommodityHauledEnum = "CommodityHauledTVAndVideo"
	CommodityHauledEnumCommodityHauledTelephoneandOrUtilityPoles                                          CommodityHauledEnum = "CommodityHauledTelephoneand_orUtilityPoles"
	CommodityHauledEnumCommodityHauledTextiles                                                            CommodityHauledEnum = "CommodityHauledTextiles"
	CommodityHauledEnumCommodityHauledTiresNewOnly                                                        CommodityHauledEnum = "CommodityHauledTires_NewOnly"
	CommodityHauledEnumCommodityHauledTiresScrap                                                          CommodityHauledEnum = "CommodityHauledTires_Scrap"
	CommodityHauledEnumCommodityHauledTobaccoFinishedProducts                                             CommodityHauledEnum = "CommodityHauledTobacco_FinishedProducts"
	CommodityHauledEnumCommodityHauledTobaccoRaw                                                          CommodityHauledEnum = "CommodityHauledTobacco_Raw"
	CommodityHauledEnumCommodityHauledTrailersasCargoPulledbyPowerUnit                                    CommodityHauledEnum = "CommodityHauledTrailersasCargoPulledbyPowerUnit"
	CommodityHauledEnumCommodityHauledTrailersasCargoonFlatbed                                            CommodityHauledEnum = "CommodityHauledTrailersasCargoonFlatbed"
	CommodityHauledEnumCommodityHauledUnknownCommodityA                                                   CommodityHauledEnum = "CommodityHauledUnknownCommodityA"
	CommodityHauledEnumCommodityHauledUnknownCommodityB                                                   CommodityHauledEnum = "CommodityHauledUnknownCommodityB"
	CommodityHauledEnumCommodityHauledUnknownCommodityC                                                   CommodityHauledEnum = "CommodityHauledUnknownCommodityC"
	CommodityHauledEnumCommodityHauledUnknownCommodityD                                                   CommodityHauledEnum = "CommodityHauledUnknownCommodityD"
	CommodityHauledEnumCommodityHauledUnknownCommodityE                                                   CommodityHauledEnum = "CommodityHauledUnknownCommodityE"
	CommodityHauledEnumCommodityHauledUnknownCommodityF                                                   CommodityHauledEnum = "CommodityHauledUnknownCommodityF"
	CommodityHauledEnumCommodityHauledUraniumOre                                                          CommodityHauledEnum = "CommodityHauledUraniumOre"
	CommodityHauledEnumCommodityHauledUraniumOxide                                                        CommodityHauledEnum = "CommodityHauledUraniumOxide"
	CommodityHauledEnumCommodityHauledVansCustomized                                                      CommodityHauledEnum = "CommodityHauledVans_Customized"
	CommodityHauledEnumCommodityHauledVegetableOil                                                        CommodityHauledEnum = "CommodityHauledVegetableOil"
	CommodityHauledEnumCommodityHauledVelvet                                                              CommodityHauledEnum = "CommodityHauledVelvet"
	CommodityHauledEnumCommodityHauledVideoGames                                                          CommodityHauledEnum = "CommodityHauledVideoGames"
	CommodityHauledEnumCommodityHauledWasteWaterLandfill                                                  CommodityHauledEnum = "CommodityHauledWasteWater_Landfill"
	CommodityHauledEnumCommodityHauledWaterWell                                                           CommodityHauledEnum = "CommodityHauledWaterWell"
	CommodityHauledEnumCommodityHauledWoodChips                                                           CommodityHauledEnum = "CommodityHauledWoodChips"
	CommodityHauledEnumCommodityHauledWool                                                                CommodityHauledEnum = "CommodityHauledWool"
	CommodityHauledEnumCommodityHauledgarbageRefuseTrash                                                  CommodityHauledEnum = "CommodityHauledgarbage_Refuse_Trash"
	CommodityHauledEnumCommodityHauledmeatFrozenFreshPackagedBoxedExcludingSwingingmeat                   CommodityHauledEnum = "CommodityHauledmeat_Frozen_Fresh_Packaged_BoxedExcludingSwingingmeat"
)

// Defines values for CoverageType.
const (
	CoverageAPDNonOwnedTrailer                 CoverageType = "CoverageAPDNonOwnedTrailer"
	CoverageAPDTrailerInterchange              CoverageType = "CoverageAPDTrailerInterchange"
	CoverageAPDUIIA                            CoverageType = "CoverageAPDUIIA"
	CoverageAccidentalDeathBenefits            CoverageType = "CoverageAccidentalDeathBenefits"
	CoverageAutoLiability                      CoverageType = "CoverageAutoLiability"
	CoverageAutoPhysicalDamage                 CoverageType = "CoverageAutoPhysicalDamage"
	CoverageBlanketAdditional                  CoverageType = "CoverageBlanketAdditional"
	CoverageBlanketAdditionalPNC               CoverageType = "CoverageBlanketAdditionalPNC"
	CoverageBlanketWaiverOfSubrogation         CoverageType = "CoverageBlanketWaiverOfSubrogation"
	CoverageBroadenedPollution                 CoverageType = "CoverageBroadenedPollution"
	CoverageCargoAtScheduledTerminals          CoverageType = "CoverageCargoAtScheduledTerminals"
	CoverageCargoTrailerInterchange            CoverageType = "CoverageCargoTrailerInterchange"
	CoverageDebrisRemoval                      CoverageType = "CoverageDebrisRemoval"
	CoverageEarnedFreight                      CoverageType = "CoverageEarnedFreight"
	CoverageEnhancedPackageTowingLimit         CoverageType = "CoverageEnhancedPackageTowingLimit"
	CoverageExtraordinaryMedicalBenefits       CoverageType = "CoverageExtraordinaryMedicalBenefits"
	CoverageFuneralExpenseBenefits             CoverageType = "CoverageFuneralExpenseBenefits"
	CoverageGLBlanketAdditional                CoverageType = "CoverageGLBlanketAdditional"
	CoverageGLBlanketWaiverOfSubrogation       CoverageType = "CoverageGLBlanketWaiverOfSubrogation"
	CoverageGeneralLiability                   CoverageType = "CoverageGeneralLiability"
	CoverageGuestPersonalInjuryProtection      CoverageType = "CoverageGuestPersonalInjuryProtection"
	CoverageHiredAuto                          CoverageType = "CoverageHiredAuto"
	CoverageHiredAutoLiab                      CoverageType = "CoverageHiredAutoLiab"
	CoverageHiredAutoPD                        CoverageType = "CoverageHiredAutoPD"
	CoverageLossMitigationExpenses             CoverageType = "CoverageLossMitigationExpenses"
	CoverageMTCBlanketAdditional               CoverageType = "CoverageMTCBlanketAdditional"
	CoverageMTCBlanketWaiverOfSubrogation      CoverageType = "CoverageMTCBlanketWaiverOfSubrogation"
	CoverageMTCNonOwnedTrailer                 CoverageType = "CoverageMTCNonOwnedTrailer"
	CoverageMTCTrailerInterchange              CoverageType = "CoverageMTCTrailerInterchange"
	CoverageMTCUIIA                            CoverageType = "CoverageMTCUIIA"
	CoverageMedicalExpenseBenefits             CoverageType = "CoverageMedicalExpenseBenefits"
	CoverageMedicalPayments                    CoverageType = "CoverageMedicalPayments"
	CoverageMiscellaneousEquipment             CoverageType = "CoverageMiscellaneousEquipment"
	CoverageMotorTruckCargo                    CoverageType = "CoverageMotorTruckCargo"
	CoverageNonOwnedAuto                       CoverageType = "CoverageNonOwnedAuto"
	CoverageNonOwnedTrailer                    CoverageType = "CoverageNonOwnedTrailer"
	CoveragePIPExcessAttendantCare             CoverageType = "CoveragePIPExcessAttendantCare"
	CoveragePersonalInjuryProtection           CoverageType = "CoveragePersonalInjuryProtection"
	CoveragePersonalInjuryProtectionBasic      CoverageType = "CoveragePersonalInjuryProtectionBasic"
	CoveragePersonalInjuryProtectionIncreased  CoverageType = "CoveragePersonalInjuryProtectionIncreased"
	CoveragePollutantCleanupAndRemoval         CoverageType = "CoveragePollutantCleanupAndRemoval"
	CoveragePropertyProtectionInsurance        CoverageType = "CoveragePropertyProtectionInsurance"
	CoverageReefer                             CoverageType = "CoverageReefer"
	CoverageReeferWithHumanError               CoverageType = "CoverageReeferWithHumanError"
	CoverageReeferWithoutHumanError            CoverageType = "CoverageReeferWithoutHumanError"
	CoverageRentalReimbursement                CoverageType = "CoverageRentalReimbursement"
	CoverageStopGap                            CoverageType = "CoverageStopGap"
	CoverageTerrorism                          CoverageType = "CoverageTerrorism"
	CoverageTowingLaborAndStorage              CoverageType = "CoverageTowingLaborAndStorage"
	CoverageTrailerInterchange                 CoverageType = "CoverageTrailerInterchange"
	CoverageUIIA                               CoverageType = "CoverageUIIA"
	CoverageUIM                                CoverageType = "CoverageUIM"
	CoverageUM                                 CoverageType = "CoverageUM"
	CoverageUMBIUIMBI                          CoverageType = "CoverageUMBIUIMBI"
	CoverageUMUIM                              CoverageType = "CoverageUMUIM"
	CoverageUMUIMBodilyInjury                  CoverageType = "CoverageUMUIMBodilyInjury"
	CoverageUMUIMPropertyDamage                CoverageType = "CoverageUMUIMPropertyDamage"
	CoverageUnattendedTruck                    CoverageType = "CoverageUnattendedTruck"
	CoverageUnderinsuredMotoristBodilyInjury   CoverageType = "CoverageUnderinsuredMotoristBodilyInjury"
	CoverageUnderinsuredMotoristPropertyDamage CoverageType = "CoverageUnderinsuredMotoristPropertyDamage"
	CoverageUninsuredMotoristBodilyInjury      CoverageType = "CoverageUninsuredMotoristBodilyInjury"
	CoverageUninsuredMotoristPropertyDamage    CoverageType = "CoverageUninsuredMotoristPropertyDamage"
	CoverageWorkLossBenefits                   CoverageType = "CoverageWorkLossBenefits"
)

// Defines values for EquipmentRecordEquipmentType.
const (
	EquipmentTypePowerUnits   EquipmentRecordEquipmentType = "EquipmentTypePowerUnits"
	EquipmentTypeTrailerUnits EquipmentRecordEquipmentType = "EquipmentTypeTrailerUnits"
)

// Defines values for FileDestinationGroup.
const (
	FileDestinationGroupClaims       FileDestinationGroup = "FileDestinationGroupClaims"
	FileDestinationGroupForms        FileDestinationGroup = "FileDestinationGroupForms"
	FileDestinationGroupQuoting      FileDestinationGroup = "FileDestinationGroupQuoting"
	FileDestinationGroupUnderwriting FileDestinationGroup = "FileDestinationGroupUnderwriting"
)

// Defines values for FileType.
const (
	FileTypeAgentAdditionalFiles FileType = "FileTypeAgentAdditionalFiles"
	FileTypeClaimDocument        FileType = "FileTypeClaimDocument"
	FileTypeDriversList          FileType = "FileTypeDriversList"
	FileTypeEquipmentList        FileType = "FileTypeEquipmentList"
	FileTypeIFTAFile             FileType = "FileTypeIFTAFile"
	FileTypeLossRun              FileType = "FileTypeLossRun"
	FileTypePDFForms             FileType = "FileTypePDFForms"
	FileTypeUwDocument           FileType = "FileTypeUwDocument"
)

// Defines values for KeyStatus.
const (
	KeyStatusActive  KeyStatus = "active"
	KeyStatusBlocked KeyStatus = "blocked"
	KeyStatusExpired KeyStatus = "expired"
)

// Defines values for LoginRequestSource.
const (
	LoginRequestSourceAgent       LoginRequestSource = "Agent"
	LoginRequestSourceSafety      LoginRequestSource = "Safety"
	LoginRequestSourceUnderwriter LoginRequestSource = "Underwriter"
	LoginRequestSourceUnknown     LoginRequestSource = "Unknown"
)

// Defines values for MTCVersion.
const (
	MTCVersionNone MTCVersion = "MTCVersionNone"
	MTCVersionV1   MTCVersion = "MTCVersionV1"
	MTCVersionV2   MTCVersion = "MTCVersionV2"
)

// Defines values for MVRPullStatus.
const (
	MVRPullStatusError        MVRPullStatus = "Error"
	MVRPullStatusNotRequested MVRPullStatus = "NotRequested"
	MVRPullStatusSuccess      MVRPullStatus = "Success"
)

// Defines values for MileageEstimateFeatureType.
const (
	MileageEstimateFeatureTypeHalfYearly MileageEstimateFeatureType = "MileageEstimateFeatureTypeHalfYearly"
	MileageEstimateFeatureTypeMileage    MileageEstimateFeatureType = "MileageEstimateFeatureTypeMileage"
	MileageEstimateFeatureTypeYearly     MileageEstimateFeatureType = "MileageEstimateFeatureTypeYearly"
)

// Defines values for NHTSAVehicleType.
const (
	VehicleTypeIncompleteVehicle NHTSAVehicleType = "VehicleTypeIncompleteVehicle"
	VehicleTypeMotorcycle        NHTSAVehicleType = "VehicleTypeMotorcycle"
	VehicleTypeNil               NHTSAVehicleType = "VehicleTypeNil"
	VehicleTypeTrailer           NHTSAVehicleType = "VehicleTypeTrailer"
	VehicleTypeTruck             NHTSAVehicleType = "VehicleTypeTruck"
)

// Defines values for NHTSAVehicleWeightClass.
const (
	WeightClass3   NHTSAVehicleWeightClass = "WeightClass3"
	WeightClass4   NHTSAVehicleWeightClass = "WeightClass4"
	WeightClass5   NHTSAVehicleWeightClass = "WeightClass5"
	WeightClass6   NHTSAVehicleWeightClass = "WeightClass6"
	WeightClass7   NHTSAVehicleWeightClass = "WeightClass7"
	WeightClass8   NHTSAVehicleWeightClass = "WeightClass8"
	WeightClassA   NHTSAVehicleWeightClass = "WeightClassA"
	WeightClassB   NHTSAVehicleWeightClass = "WeightClassB"
	WeightClassC   NHTSAVehicleWeightClass = "WeightClassC"
	WeightClassD   NHTSAVehicleWeightClass = "WeightClassD"
	WeightClassE   NHTSAVehicleWeightClass = "WeightClassE"
	WeightClassF   NHTSAVehicleWeightClass = "WeightClassF"
	WeightClassG   NHTSAVehicleWeightClass = "WeightClassG"
	WeightClassH   NHTSAVehicleWeightClass = "WeightClassH"
	WeightClassNil NHTSAVehicleWeightClass = "WeightClassNil"
)

// Defines values for NegotiatedRateRulesEnum.
const (
	ConsentToRate NegotiatedRateRulesEnum = "ConsentToRate"
	LargeFleet    NegotiatedRateRulesEnum = "LargeFleet"
	RuleFifteen   NegotiatedRateRulesEnum = "RuleFifteen"
)

// Defines values for OOSViolationsDataItemCategory.
const (
	Driver  OOSViolationsDataItemCategory = "driver"
	Hazmat  OOSViolationsDataItemCategory = "hazmat"
	Overall OOSViolationsDataItemCategory = "overall"
	Vehicle OOSViolationsDataItemCategory = "vehicle"
)

// Defines values for OperatingClass.
const (
	OperatingClassDryVan       OperatingClass = "OperatingClassDryVan"
	OperatingClassDump         OperatingClass = "OperatingClassDump"
	OperatingClassFlatbed      OperatingClass = "OperatingClassFlatbed"
	OperatingClassHazmat       OperatingClass = "OperatingClassHazmat"
	OperatingClassHeavyHaul    OperatingClass = "OperatingClassHeavyHaul"
	OperatingClassIntermodal   OperatingClass = "OperatingClassIntermodal"
	OperatingClassRefrigerated OperatingClass = "OperatingClassRefrigerated"
	OperatingClassTanker       OperatingClass = "OperatingClassTanker"
)

// Defines values for PaymentMethod.
const (
	PaymentMethodMonthlyReporter PaymentMethod = "MonthlyReporter"
	PaymentMethodPaidInFull      PaymentMethod = "PaidInFull"
)

// Defines values for PolicyStatus.
const (
	PolicyStatusActive              PolicyStatus = "Active"
	PolicyStatusCancellationPending PolicyStatus = "Cancellation Pending"
	PolicyStatusCancelled           PolicyStatus = "Cancelled"
	PolicyStatusExpired             PolicyStatus = "Expired"
	PolicyStatusExpiringSoon        PolicyStatus = "Expiring Soon"
	PolicyStatusUpcoming            PolicyStatus = "Upcoming"
)

// Defines values for PrimaryCommodityHauledEnum.
const (
	PrimaryCommodityHauledEnumCommodityHauledAggregatesRockOrSandOrGravelOrStoneOrDirt   PrimaryCommodityHauledEnum = "CommodityHauledAggregatesRockOrSandOrGravelOrStoneOrDirt"
	PrimaryCommodityHauledEnumCommodityHauledAppliancesOrHardware                        PrimaryCommodityHauledEnum = "CommodityHauledAppliancesOrHardware"
	PrimaryCommodityHauledEnumCommodityHauledAsh                                         PrimaryCommodityHauledEnum = "CommodityHauledAsh"
	PrimaryCommodityHauledEnumCommodityHauledAsphaltLiquid                               PrimaryCommodityHauledEnum = "CommodityHauledAsphaltLiquid"
	PrimaryCommodityHauledEnumCommodityHauledAutoPartsAccessories                        PrimaryCommodityHauledEnum = "CommodityHauledAutoPartsAccessories"
	PrimaryCommodityHauledEnumCommodityHauledBeveragesAlcoholic                          PrimaryCommodityHauledEnum = "CommodityHauledBeveragesAlcoholic"
	PrimaryCommodityHauledEnumCommodityHauledBeveragesNonAlcoholic                       PrimaryCommodityHauledEnum = "CommodityHauledBeveragesNonAlcoholic"
	PrimaryCommodityHauledEnumCommodityHauledBoats                                       PrimaryCommodityHauledEnum = "CommodityHauledBoats"
	PrimaryCommodityHauledEnumCommodityHauledBuildingMaterialsFinished                   PrimaryCommodityHauledEnum = "CommodityHauledBuildingMaterialsFinished"
	PrimaryCommodityHauledEnumCommodityHauledBuildingMaterialsRaw                        PrimaryCommodityHauledEnum = "CommodityHauledBuildingMaterialsRaw"
	PrimaryCommodityHauledEnumCommodityHauledChemicalsBulkLiquid                         PrimaryCommodityHauledEnum = "CommodityHauledChemicalsBulkLiquid"
	PrimaryCommodityHauledEnumCommodityHauledClass1Explosives                            PrimaryCommodityHauledEnum = "CommodityHauledClass1Explosives"
	PrimaryCommodityHauledEnumCommodityHauledClass2Gases                                 PrimaryCommodityHauledEnum = "CommodityHauledClass2Gases"
	PrimaryCommodityHauledEnumCommodityHauledClass3FlammableOrCombustibleLiquids         PrimaryCommodityHauledEnum = "CommodityHauledClass3FlammableOrCombustibleLiquids"
	PrimaryCommodityHauledEnumCommodityHauledClass4FlammableSolids                       PrimaryCommodityHauledEnum = "CommodityHauledClass4FlammableSolids"
	PrimaryCommodityHauledEnumCommodityHauledClass5OxidizingSubstancesOrOrganicPeroxides PrimaryCommodityHauledEnum = "CommodityHauledClass5OxidizingSubstancesOrOrganicPeroxides"
	PrimaryCommodityHauledEnumCommodityHauledClass6ToxinsOrInfectiousSubstances          PrimaryCommodityHauledEnum = "CommodityHauledClass6ToxinsOrInfectiousSubstances"
	PrimaryCommodityHauledEnumCommodityHauledClass7RadioactiveMaterials                  PrimaryCommodityHauledEnum = "CommodityHauledClass7RadioactiveMaterials"
	PrimaryCommodityHauledEnumCommodityHauledClass8Corrosives                            PrimaryCommodityHauledEnum = "CommodityHauledClass8Corrosives"
	PrimaryCommodityHauledEnumCommodityHauledClass9MiscellaneousOrNotOtherwiseSpecified  PrimaryCommodityHauledEnum = "CommodityHauledClass9MiscellaneousOrNotOtherwiseSpecified"
	PrimaryCommodityHauledEnumCommodityHauledConstructionEquipment                       PrimaryCommodityHauledEnum = "CommodityHauledConstructionEquipment"
	PrimaryCommodityHauledEnumCommodityHauledCosmetics                                   PrimaryCommodityHauledEnum = "CommodityHauledCosmetics"
	PrimaryCommodityHauledEnumCommodityHauledDryFoods                                    PrimaryCommodityHauledEnum = "CommodityHauledDryFoods"
	PrimaryCommodityHauledEnumCommodityHauledElectronics                                 PrimaryCommodityHauledEnum = "CommodityHauledElectronics"
	PrimaryCommodityHauledEnumCommodityHauledEmptyContainers                             PrimaryCommodityHauledEnum = "CommodityHauledEmptyContainers"
	PrimaryCommodityHauledEnumCommodityHauledEnginesOrMachinery                          PrimaryCommodityHauledEnum = "CommodityHauledEnginesOrMachinery"
	PrimaryCommodityHauledEnumCommodityHauledFertilizers                                 PrimaryCommodityHauledEnum = "CommodityHauledFertilizers"
	PrimaryCommodityHauledEnumCommodityHauledFlowersOrPlants                             PrimaryCommodityHauledEnum = "CommodityHauledFlowersOrPlants"
	PrimaryCommodityHauledEnumCommodityHauledGeneralDryFreight                           PrimaryCommodityHauledEnum = "CommodityHauledGeneralDryFreight"
	PrimaryCommodityHauledEnumCommodityHauledGrainOrSeedOrFeed                           PrimaryCommodityHauledEnum = "CommodityHauledGrainOrSeedOrFeed"
	PrimaryCommodityHauledEnumCommodityHauledHazardousMaterialsOrBatteries               PrimaryCommodityHauledEnum = "CommodityHauledHazardousMaterialsOrBatteries"
	PrimaryCommodityHauledEnumCommodityHauledLargeMachinery                              PrimaryCommodityHauledEnum = "CommodityHauledLargeMachinery"
	PrimaryCommodityHauledEnumCommodityHauledLiquidsFuel                                 PrimaryCommodityHauledEnum = "CommodityHauledLiquidsFuel"
	PrimaryCommodityHauledEnumCommodityHauledLiquidsMilk                                 PrimaryCommodityHauledEnum = "CommodityHauledLiquidsMilk"
	PrimaryCommodityHauledEnumCommodityHauledLiquidsOtherNonFlammable                    PrimaryCommodityHauledEnum = "CommodityHauledLiquidsOtherNonFlammable"
	PrimaryCommodityHauledEnumCommodityHauledLumberOrLogs                                PrimaryCommodityHauledEnum = "CommodityHauledLumberOrLogs"
	PrimaryCommodityHauledEnumCommodityHauledMailParcelsOrAmazon                         PrimaryCommodityHauledEnum = "CommodityHauledMailParcelsOrAmazon"
	PrimaryCommodityHauledEnumCommodityHauledMailUSPS                                    PrimaryCommodityHauledEnum = "CommodityHauledMailUSPS"
	PrimaryCommodityHauledEnumCommodityHauledMetalsBarsOrBeams                           PrimaryCommodityHauledEnum = "CommodityHauledMetalsBarsOrBeams"
	PrimaryCommodityHauledEnumCommodityHauledMetalsCopper                                PrimaryCommodityHauledEnum = "CommodityHauledMetalsCopper"
	PrimaryCommodityHauledEnumCommodityHauledMetalsRolledOrCoiledSteels                  PrimaryCommodityHauledEnum = "CommodityHauledMetalsRolledOrCoiledSteels"
	PrimaryCommodityHauledEnumCommodityHauledOversizedOrOverweight                       PrimaryCommodityHauledEnum = "CommodityHauledOversizedOrOverweight"
	PrimaryCommodityHauledEnumCommodityHauledPaperOrPlasticProducts                      PrimaryCommodityHauledEnum = "CommodityHauledPaperOrPlasticProducts"
	PrimaryCommodityHauledEnumCommodityHauledPharmaceuticals                             PrimaryCommodityHauledEnum = "CommodityHauledPharmaceuticals"
	PrimaryCommodityHauledEnumCommodityHauledRefrigeratedFoodsNonmeat                    PrimaryCommodityHauledEnum = "CommodityHauledRefrigeratedFoodsNonmeat"
	PrimaryCommodityHauledEnumCommodityHauledRefrigeratedFoodsmeat                       PrimaryCommodityHauledEnum = "CommodityHauledRefrigeratedFoodsmeat"
	PrimaryCommodityHauledEnumCommodityHauledRefrigeratedGoods                           PrimaryCommodityHauledEnum = "CommodityHauledRefrigeratedGoods"
	PrimaryCommodityHauledEnumCommodityHauledRetailProducts                              PrimaryCommodityHauledEnum = "CommodityHauledRetailProducts"
	PrimaryCommodityHauledEnumCommodityHauledSandOrSilicate                              PrimaryCommodityHauledEnum = "CommodityHauledSandOrSilicate"
	PrimaryCommodityHauledEnumCommodityHauledScrapMetal                                  PrimaryCommodityHauledEnum = "CommodityHauledScrapMetal"
	PrimaryCommodityHauledEnumCommodityHauledWoodChips                                   PrimaryCommodityHauledEnum = "CommodityHauledWoodChips"
)

// Defines values for ProgramType.
const (
	ProgramTypeBusinessAuto        ProgramType = "ProgramTypeBusinessAuto"
	ProgramTypeFleet               ProgramType = "ProgramTypeFleet"
	ProgramTypeInvalid             ProgramType = "ProgramTypeInvalid"
	ProgramTypeNonFleetAdmitted    ProgramType = "ProgramTypeNonFleetAdmitted"
	ProgramTypeNonFleetCanopiusNRB ProgramType = "ProgramTypeNonFleetCanopiusNRB"
)

// Defines values for ScoreType.
const (
	GForceAdjustedTRS ScoreType = "gForce_Adjusted_TRS"
	GPSOnly10SecTRS   ScoreType = "GPS_Only_10_sec_TRS"
	GPSOnly1MinTRS    ScoreType = "GPS_Only_1_min_TRS"
	ProxyMilliman     ScoreType = "Proxy_Milliman"
	TRS               ScoreType = "TRS"
)

// Defines values for SurchargeType.
const (
	FEECHARGESTAMPINGFEE SurchargeType = "FEE_CHARGE_STAMPING_FEE"
	FEECHARGESURPLUSTAX  SurchargeType = "FEE_CHARGE_SURPLUS_TAX"
	MCCA                 SurchargeType = "MCCA"
	NCRF                 SurchargeType = "NCRF"
	STAMPINGFEE          SurchargeType = "STAMPING_FEE"
	SURPLUSTAX           SurchargeType = "SURPLUS_TAX"
)

// Defines values for TelematicsDataStatus.
const (
	ConnectionFailed                 TelematicsDataStatus = "Connection Failed"
	ConsentNotGrantedAfter3Followups TelematicsDataStatus = "Consent Not Granted After 3 Followups"
	DataAvailable                    TelematicsDataStatus = "Data Available"
	NoHistoricalDataException        TelematicsDataStatus = "No Historical Data(Exception)"
	NotEnoughData                    TelematicsDataStatus = "Not Enough Data"
	Panic                            TelematicsDataStatus = "Panic"
	ProcessingData                   TelematicsDataStatus = "Processing Data"
	ReConnectionRequired             TelematicsDataStatus = "Re-Connection Required"
	TelematicsConnectionNotGranted   TelematicsDataStatus = "Telematics Connection Not Granted"
	WaitingForTSPConsent             TelematicsDataStatus = "Waiting for TSP Consent"
)

// Defines values for TelematicsIdentifierType.
const (
	ConnectionHandleId TelematicsIdentifierType = "ConnectionHandleId"
	DOT                TelematicsIdentifierType = "DOT"
	VIN                TelematicsIdentifierType = "VIN"
)

// Defines values for TelematicsVehicleStatKind.
const (
	EngineStateLog TelematicsVehicleStatKind = "EngineStateLog"
	GPSLog         TelematicsVehicleStatKind = "GPSLog"
	OdometerLog    TelematicsVehicleStatKind = "OdometerLog"
)

// Defines values for USState.
const (
	AK USState = "AK"
	AL USState = "AL"
	AR USState = "AR"
	AZ USState = "AZ"
	CA USState = "CA"
	CO USState = "CO"
	CT USState = "CT"
	DC USState = "DC"
	DE USState = "DE"
	FL USState = "FL"
	GA USState = "GA"
	HI USState = "HI"
	IA USState = "IA"
	ID USState = "ID"
	IL USState = "IL"
	IN USState = "IN"
	KS USState = "KS"
	KY USState = "KY"
	LA USState = "LA"
	MA USState = "MA"
	MD USState = "MD"
	ME USState = "ME"
	MI USState = "MI"
	MN USState = "MN"
	MO USState = "MO"
	MS USState = "MS"
	MT USState = "MT"
	NC USState = "NC"
	ND USState = "ND"
	NE USState = "NE"
	NH USState = "NH"
	NJ USState = "NJ"
	NM USState = "NM"
	NV USState = "NV"
	NY USState = "NY"
	OH USState = "OH"
	OK USState = "OK"
	OR USState = "OR"
	PA USState = "PA"
	RI USState = "RI"
	SC USState = "SC"
	SD USState = "SD"
	TN USState = "TN"
	TX USState = "TX"
	UT USState = "UT"
	VA USState = "VA"
	VT USState = "VT"
	WA USState = "WA"
	WI USState = "WI"
	WV USState = "WV"
	WY USState = "WY"
)

// Defines values for UTMParamMedium.
const (
	QuotingApp UTMParamMedium = "quoting_app"
)

// Defines values for UTMParamSource.
const (
	TelematicsConsent UTMParamSource = "telematics_consent"
)

// Defines values for UserProfileResponseUserType.
const (
	UserProfileResponseUserTypeAgent        UserProfileResponseUserType = "agent"
	UserProfileResponseUserTypeFleet        UserProfileResponseUserType = "fleet"
	UserProfileResponseUserTypeNirvana      UserProfileResponseUserType = "nirvana"
	UserProfileResponseUserTypeSharedLink   UserProfileResponseUserType = "shared_link"
	UserProfileResponseUserTypeUnprivileged UserProfileResponseUserType = "unprivileged"
)

// Defines values for Version.
const (
	VersionUndefined Version = "Undefined"
	VersionV1        Version = "V1"
	VersionV2        Version = "V2"
	VersionV3        Version = "V3"
	VersionV4        Version = "V4"
	VersionV5        Version = "V5"
)

// Defines values for WindowType.
const (
	N12M WindowType = "12M"
	N3M  WindowType = "3M"
	N6M  WindowType = "6M"
)

// Defines values for ApplicationType.
const (
	ApplicationTypeBusinessAuto     ApplicationType = "ApplicationTypeBusinessAuto"
	ApplicationTypeFleet            ApplicationType = "ApplicationTypeFleet"
	ApplicationTypeNonFleet         ApplicationType = "ApplicationTypeNonFleet"
	ApplicationTypeNonFleetAdmitted ApplicationType = "ApplicationTypeNonFleetAdmitted"
)

// Defines values for ProviderType.
const (
	ProviderTypeKeepTruckin ProviderType = "ProviderTypeKeepTruckin"
	ProviderTypeSamsara     ProviderType = "ProviderTypeSamsara"
)

// APIDesc defines model for APIDesc.
type APIDesc struct {
	Api       string           `json:"api"`
	Methods   []APIDescMethods `json:"methods"`
	RateLimit *[]APIRateLimit  `json:"rate_limit,omitempty"`
}

// APIDescMethods defines model for APIDesc.Methods.
type APIDescMethods string

// APIKeyScope defines model for APIKeyScope.
type APIKeyScope struct {
	Apis            []APIDesc      `json:"apis"`
	GlobalRateLimit []APIRateLimit `json:"global_rate_limit"`
}

// APIKeysWithAgency defines model for APIKeysWithAgency.
type APIKeysWithAgency struct {
	AgencyName string             `json:"agencyName"`
	CreatedAt  time.Time          `json:"createdAt"`
	CreatedBy  openapi_types.UUID `json:"createdBy"`
	ExpiresAt  time.Time          `json:"expiresAt"`
	KeyId      openapi_types.UUID `json:"keyId"`
	Scope      APIKeyScope        `json:"scope"`
	Status     KeyStatus          `json:"status"`
	UpdatedAt  time.Time          `json:"updatedAt"`
	UserId     openapi_types.UUID `json:"userId"`
}

// APIRateLimit defines model for APIRateLimit.
type APIRateLimit struct {
	Duration string `json:"duration"`
	Limit    int    `json:"limit"`
}

// AboutResponse defines model for AboutResponse.
type AboutResponse struct {
	Time    time.Time `json:"time"`
	Version string    `json:"version"`
}

// AcademyCertification Completed Workramp certification
type AcademyCertification struct {
	AwardedByName  *string            `json:"awardedByName,omitempty"`
	AwardedByTitle *string            `json:"awardedByTitle,omitempty"`
	CreatedAt      time.Time          `json:"createdAt"`
	Description    *string            `json:"description,omitempty"`
	Id             openapi_types.UUID `json:"id"`
	Name           *string            `json:"name,omitempty"`
}

// AcademyCertificationEnrollment Workramp certification enrollment response from https://developers.workramp.com/reference/get-all-certifications-for-a-contact
type AcademyCertificationEnrollment struct {
	Awarded      bool       `json:"awarded"`
	AwardedAt    *time.Time `json:"awardedAt,omitempty"`
	CanRecertify bool       `json:"canRecertify"`

	// Certification Completed Workramp certification
	Certification                         *AcademyCertification `json:"certification,omitempty"`
	Completed                             bool                  `json:"completed"`
	CompletedAt                           *time.Time            `json:"completedAt,omitempty"`
	CreatedAt                             time.Time             `json:"createdAt"`
	DueAt                                 *time.Time            `json:"dueAt,omitempty"`
	Expired                               bool                  `json:"expired"`
	ExpiresAt                             *time.Time            `json:"expiresAt,omitempty"`
	HasLearnerSeenAward                   bool                  `json:"hasLearnerSeenAward"`
	HasLearnerSeenAwardAndRequiresGrading bool                  `json:"hasLearnerSeenAwardAndRequiresGrading"`
	Id                                    openapi_types.UUID    `json:"id"`
	IsCompletedAndRequiresGrading         bool                  `json:"isCompletedAndRequiresGrading"`
	IsStarted                             bool                  `json:"isStarted"`
	LastAccessedAt                        *time.Time            `json:"lastAccessedAt,omitempty"`

	// ProgressPercentage Decimal percent the certification is completed.
	ProgressPercentage float32   `json:"progressPercentage"`
	PublicURL          *string   `json:"publicURL,omitempty"`
	ShortID            *string   `json:"shortID,omitempty"`
	TimeSpentCache     *int      `json:"timeSpentCache,omitempty"`
	UpdatedAt          time.Time `json:"updatedAt"`
}

// AcademyUserCertificationsResponse defines model for AcademyUserCertificationsResponse.
type AcademyUserCertificationsResponse struct {
	// Certifications List of workramp academy certifications for the user.
	Certifications []AcademyCertificationEnrollment `json:"certifications"`
}

// Agency defines model for Agency.
type Agency struct {
	Id   openapi_types.UUID `json:"id"`
	Name string             `json:"name"`
}

// AgencyRole defines model for AgencyRole.
type AgencyRole struct {
	Agency Agency `json:"agency"`
	Role   string `json:"role"`
}

// AgentDetails defines model for AgentDetails.
type AgentDetails struct {
	IsDetailsComplete bool `json:"isDetailsComplete"`
}

// AncillaryCoverage defines model for AncillaryCoverage.
type AncillaryCoverage struct {
	CoverageName          CoverageType `json:"CoverageName"`
	Deductible            int32        `json:"Deductible"`
	DeductibleOptions     []int32      `json:"DeductibleOptions"`
	Description           string       `json:"Description"`
	IsAppliedAtIndication bool         `json:"IsAppliedAtIndication"`
	IsEditable            bool         `json:"IsEditable"`
	IsEnabled             bool         `json:"IsEnabled"`
	IsMandatory           bool         `json:"IsMandatory"`
	Limit                 *int32       `json:"Limit,omitempty"`
	LimitOptions          []int32      `json:"LimitOptions"`
}

// AncillaryCoverages defines model for AncillaryCoverages.
type AncillaryCoverages = []AncillaryCoverage

// ApplicationClearanceStatus defines model for ApplicationClearanceStatus.
type ApplicationClearanceStatus string

// ApplicationReviewWidgetEnum defines model for ApplicationReviewWidgetEnum.
type ApplicationReviewWidgetEnum string

// ApplicationReviewWidgetStatus defines model for ApplicationReviewWidgetStatus.
type ApplicationReviewWidgetStatus struct {
	Metadata     ApplicationReviewWidgetStatusMetadata `json:"metadata"`
	WidgetStatus ApplicationReviewWidgetStatusEnum     `json:"widgetStatus"`
}

// ApplicationReviewWidgetStatusEnum defines model for ApplicationReviewWidgetStatusEnum.
type ApplicationReviewWidgetStatusEnum string

// ApplicationReviewWidgetStatusMetadata defines model for ApplicationReviewWidgetStatusMetadata.
type ApplicationReviewWidgetStatusMetadata struct {
	RefreshedAt time.Time `json:"refreshed_at"`
}

// ApplicationReviewWidgetVersion defines model for ApplicationReviewWidgetVersion.
type ApplicationReviewWidgetVersion string

// BasicScoreThresholdItem defines model for BasicScoreThresholdItem.
type BasicScoreThresholdItem struct {
	Category  string   `json:"category"`
	Score     *float32 `json:"score,omitempty"`
	Threshold float32  `json:"threshold"`
}

// BasicScoreThresholds defines model for BasicScoreThresholds.
type BasicScoreThresholds = []BasicScoreThresholdItem

// BillingFrequency defines model for BillingFrequency.
type BillingFrequency string

// CargoTerminalSchedule defines model for CargoTerminalSchedule.
type CargoTerminalSchedule struct {
	// ConstructionClass Default to `Frame` if not provided by the agent.
	ConstructionClass *CargoTerminalScheduleConstructionClass `json:"constructionClass,omitempty"`

	// Limit Limit for the terminal
	Limit *int32 `json:"limit,omitempty"`

	// PrivateFireProtection Private fire protection of the terminal
	PrivateFireProtection CargoTerminalSchedulePrivateFireProtection `json:"privateFireProtection"`

	// PrivateTheftProtection Private theft protection of the terminal
	PrivateTheftProtection CargoTerminalSchedulePrivateTheftProtection `json:"privateTheftProtection"`
}

// CargoTerminalScheduleConstructionClass Default to `Frame` if not provided by the agent.
type CargoTerminalScheduleConstructionClass string

// CargoTerminalSchedulePrivateFireProtection Private fire protection of the terminal
type CargoTerminalSchedulePrivateFireProtection string

// CargoTerminalSchedulePrivateTheftProtection Private theft protection of the terminal
type CargoTerminalSchedulePrivateTheftProtection string

// CombinedCoverages defines model for CombinedCoverages.
type CombinedCoverages = []CoverageType

// CommodityCategory defines model for CommodityCategory.
type CommodityCategory struct {
	Label *string               `json:"label,omitempty"`
	Type  CommodityCategoryEnum `json:"type"`
}

// CommodityCategoryEnum defines model for CommodityCategoryEnum.
type CommodityCategoryEnum string

// CommodityHauled Only label is set as required field, because we accept free form text from agent
type CommodityHauled struct {
	Label string               `json:"label"`
	Type  *CommodityHauledEnum `json:"type,omitempty"`
}

// CommodityHauledEnum defines model for CommodityHauledEnum.
type CommodityHauledEnum string

// Coverage defines model for Coverage.
type Coverage struct {
	Deductible  string   `json:"deductible"`
	Description *string  `json:"description,omitempty"`
	Limits      []string `json:"limits"`
	Name        string   `json:"name"`
}

// CoverageAndLimits defines model for CoverageAndLimits.
type CoverageAndLimits struct {
	Coverage CoverageType `json:"coverage"`
	Limit    int          `json:"limit"`
}

// CoverageGroup defines model for CoverageGroup.
type CoverageGroup struct {
	Children []SubCoverage `json:"children"`
	Parent   Coverage      `json:"parent"`
}

// CoverageType defines model for CoverageType.
type CoverageType string

// CoverageVariablesOptionNumeric defines model for CoverageVariablesOptionNumeric.
type CoverageVariablesOptionNumeric struct {
	Current int32   `json:"current"`
	Options []int32 `json:"options"`
}

// CrashRecordHistory defines model for CrashRecordHistory.
type CrashRecordHistory = []CrashRecordHistoryItem

// CrashRecordHistoryItem defines model for CrashRecordHistoryItem.
type CrashRecordHistoryItem struct {
	AccessControl        *string            `json:"accessControl,omitempty"`
	CargoBodyType        *string            `json:"cargoBodyType,omitempty"`
	City                 *string            `json:"city,omitempty"`
	CityCode             *string            `json:"cityCode,omitempty"`
	CountyCode           *string            `json:"countyCode,omitempty"`
	Date                 openapi_types.Date `json:"date"`
	Fatalities           int32              `json:"fatalities"`
	FederalRecordable    *bool              `json:"federalRecordable,omitempty"`
	Injuries             int32              `json:"injuries"`
	InvestigatingAgency  *string            `json:"investigatingAgency,omitempty"`
	LightCondition       *string            `json:"lightCondition,omitempty"`
	Location             string             `json:"location"`
	NotPreventable       *bool              `json:"notPreventable,omitempty"`
	OfficerBadge         *string            `json:"officerBadge,omitempty"`
	RoadSurfaceCondition *string            `json:"roadSurfaceCondition,omitempty"`
	StateRecordable      *bool              `json:"stateRecordable,omitempty"`
	TowAway              bool               `json:"towAway"`
	TrafficWay           *string            `json:"trafficWay,omitempty"`
	UsState              string             `json:"usState"`
	VehicleHazmatPlacard *bool              `json:"vehicleHazmatPlacard,omitempty"`
	VehicleLicenseNumber *string            `json:"vehicleLicenseNumber,omitempty"`
	VehicleType          *string            `json:"vehicleType,omitempty"`
	Vin                  *string            `json:"vin,omitempty"`
	WeatherCondition     *string            `json:"weatherCondition,omitempty"`
}

// CrashRecordSummary defines model for CrashRecordSummary.
type CrashRecordSummary struct {
	AccidentToPowerUnitRatio float32 `json:"accidentToPowerUnitRatio"`
	Fatal                    int32   `json:"fatal"`
	Injury                   int32   `json:"injury"`
	TotalReportable          int32   `json:"totalReportable"`
	Tow                      int32   `json:"tow"`
}

// CreateAPIKeyRequest defines model for CreateAPIKeyRequest.
type CreateAPIKeyRequest struct {
	// AgencyID Agency ID of the API user
	AgencyID  string      `json:"agencyID"`
	ExpiresAt *time.Time  `json:"expiresAt,omitempty"`
	Scope     APIKeyScope `json:"scope"`
}

// CreateAPIKeyResponse defines model for CreateAPIKeyResponse.
type CreateAPIKeyResponse struct {
	Id     openapi_types.UUID `json:"id"`
	Secret string             `json:"secret"`
}

// DownloadFileLinkResponse defines model for DownloadFileLinkResponse.
type DownloadFileLinkResponse struct {
	Link string `json:"link"`
}

// DownloadFileResponse defines model for DownloadFileResponse.
type DownloadFileResponse struct {
	File openapi_types.File `json:"file"`
}

// DriverViolation defines model for DriverViolation.
type DriverViolation struct {
	AtFaultViolation                     *bool               `json:"atFaultViolation,omitempty"`
	Code                                 *string             `json:"code,omitempty"`
	Date                                 *openapi_types.Date `json:"date,omitempty"`
	Description                          *string             `json:"description,omitempty"`
	IsExpired                            *bool               `json:"isExpired,omitempty"`
	IsManuallyAdded                      *bool               `json:"isManuallyAdded,omitempty"`
	MovingViolation                      *bool               `json:"movingViolation,omitempty"`
	OriginalPoints                       *int                `json:"originalPoints,omitempty"`
	Points                               *int                `json:"points,omitempty"`
	RecklessDrivingMobileDeviceViolation *bool               `json:"recklessDrivingMobileDeviceViolation,omitempty"`
	SevereViolation                      *bool               `json:"severeViolation,omitempty"`
	Type                                 *string             `json:"type,omitempty"`
}

// EquipmentRecord defines model for EquipmentRecord.
type EquipmentRecord struct {
	Count         int32                        `json:"count"`
	EquipmentType EquipmentRecordEquipmentType `json:"equipmentType"`
}

// EquipmentRecordEquipmentType defines model for EquipmentRecord.EquipmentType.
type EquipmentRecordEquipmentType string

// Error Generic error response from backend. Safe to render to end-user, and print to browser console.
type Error struct {
	// Code Status code repeated again the response body for convenience.
	Code int `json:"code"`

	// Message Message for the end user
	Message string `json:"message"`

	// RequestId RequestId of the associated request (for investigation)
	RequestId string `json:"requestId"`
}

// ErrorMessage defines model for ErrorMessage.
type ErrorMessage struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// FetchTelematicsVehicleStatsResponse defines model for FetchTelematicsVehicleStatsResponse.
type FetchTelematicsVehicleStatsResponse struct {
	Interval TimeInterval                 `json:"interval"`
	Vehicles []TelematicsVehicleStatsItem `json:"vehicles"`
}

// FileDestinationGroup defines model for FileDestinationGroup.
type FileDestinationGroup string

// FileHandle defines model for FileHandle.
type FileHandle struct {
	Handle string `json:"handle"`
}

// FileInfo defines model for FileInfo.
type FileInfo struct {
	DestinationGroup FileDestinationGroup `json:"destinationGroup"`
	Type             FileType             `json:"type"`
}

// FileMetadata defines model for FileMetadata.
type FileMetadata struct {
	FileDestinationGroup *FileDestinationGroup `json:"FileDestinationGroup,omitempty"`
	FileType             *FileType             `json:"FileType,omitempty"`
	Handle               *string               `json:"handle,omitempty"`
	Name                 string                `json:"name"`
}

// FileType defines model for FileType.
type FileType string

// FileUploadRequest defines model for FileUploadRequest.
type FileUploadRequest struct {
	FileDestinationGroup FileDestinationGroup `json:"fileDestinationGroup"`
	FileType             FileType             `json:"fileType"`
	FlatfileMetadata     *FlatfileMetadata    `json:"flatfileMetadata,omitempty"`
	ImplerMetadata       *ImplerMetadata      `json:"implerMetadata,omitempty"`
}

// FlatfileMetadata defines model for FlatfileMetadata.
type FlatfileMetadata struct {
	FileMetadata   FileMetadata `json:"fileMetadata"`
	FlatfileHandle string       `json:"flatfileHandle"`
}

// Fleet defines model for Fleet.
type Fleet struct {
	DotNumber int64              `json:"dotNumber"`
	Id        openapi_types.UUID `json:"id"`
	Name      string             `json:"name"`
}

// FleetRole defines model for FleetRole.
type FleetRole struct {
	Fleet Fleet  `json:"fleet"`
	Role  string `json:"role"`
}

// ForgotPasswordRequest defines model for ForgotPasswordRequest.
type ForgotPasswordRequest struct {
	Email openapi_types.Email `json:"email"`
}

// GetAPIKeyResponse defines model for GetAPIKeyResponse.
type GetAPIKeyResponse struct {
	CreatedAt time.Time          `json:"createdAt"`
	CreatedBy openapi_types.UUID `json:"createdBy"`
	ExpiresAt time.Time          `json:"expiresAt"`
	KeyId     openapi_types.UUID `json:"keyId"`
	Scope     APIKeyScope        `json:"scope"`
	Status    KeyStatus          `json:"status"`
	UpdatedAt time.Time          `json:"updatedAt"`
	UserId    openapi_types.UUID `json:"userId"`
}

// GoogleAuthResponse defines model for GoogleAuthResponse.
type GoogleAuthResponse struct {
	HandleID *string `json:"handleID,omitempty"`
	Url      *string `json:"url,omitempty"`
}

// HubspotLoginRequest defines model for HubspotLoginRequest.
type HubspotLoginRequest struct {
	// RedirectURL The redirect_url query parameter that is provided by hubspot. We echo this query param back to hubspot in our client redirect URI.
	RedirectURL *string `json:"redirectURL,omitempty"`
}

// HubspotLoginResponse defines model for HubspotLoginResponse.
type HubspotLoginResponse struct {
	// AbsoluteLoginUrl The URL that will authenticate this user with hubspot upon redirecting to it.
	AbsoluteLoginUrl string `json:"absoluteLoginUrl"`
}

// ImplerMetadata defines model for ImplerMetadata.
type ImplerMetadata struct {
	FileMetadata FileMetadata `json:"fileMetadata"`
	ImplerHandle string       `json:"implerHandle"`
}

// InsuredData defines model for InsuredData.
type InsuredData struct {
	City          string `json:"city"`
	InsuredName   string `json:"insuredName"`
	State         string `json:"state"`
	StreetAddress string `json:"streetAddress"`
	ZipCode       string `json:"zipCode"`
}

// KeyStatus defines model for KeyStatus.
type KeyStatus string

// LoginRequest defines model for LoginRequest.
type LoginRequest struct {
	Email    openapi_types.Email `json:"email"`
	Password string              `json:"password"`

	// Source The source of the login request.
	Source  *LoginRequestSource `json:"source,omitempty"`
	UtmTags *UTMTags            `json:"utmTags,omitempty"`
}

// LoginRequestSource The source of the login request.
type LoginRequestSource string

// LoginResponse defines model for LoginResponse.
type LoginResponse struct {
	Expiration time.Time `json:"expiration"`
	SessionId  string    `json:"sessionId"`
}

// MTCVersion MTC Rating Model Version - None (Not Applicable), V1 (Sentry Model) or V2 (GWCC Model)
type MTCVersion string

// MVRFlag defines model for MVRFlag.
type MVRFlag struct {
	PullMvr bool `json:"PullMvr"`
}

// MVRPullStatus defines model for MVRPullStatus.
type MVRPullStatus string

// MileageEstimateFeature defines model for MileageEstimateFeature.
type MileageEstimateFeature = []MileageEstimateFeatureData

// MileageEstimateFeatureData defines model for MileageEstimateFeatureData.
type MileageEstimateFeatureData struct {
	Timestamp *time.Time                     `json:"timestamp,omitempty"`
	Values    []MileageEstimateFeatureValues `json:"values"`
	VinCount  int32                          `json:"vinCount"`
}

// MileageEstimateFeatureType defines model for MileageEstimateFeatureType.
type MileageEstimateFeatureType string

// MileageEstimateFeatureValues defines model for MileageEstimateFeatureValues.
type MileageEstimateFeatureValues struct {
	Type  MileageEstimateFeatureType `json:"type"`
	Value float32                    `json:"value"`
}

// NHTSAVINDecodeError defines model for NHTSAVINDecodeError.
type NHTSAVINDecodeError struct {
	Codes []string `json:"codes"`
	Text  string   `json:"text"`
}

// NHTSAVehicleType defines model for NHTSAVehicleType.
type NHTSAVehicleType string

// NHTSAVehicleWeightClass defines model for NHTSAVehicleWeightClass.
type NHTSAVehicleWeightClass string

// NegotiatedRateRulesEnum defines model for NegotiatedRateRulesEnum.
type NegotiatedRateRulesEnum string

// NirvanaRole defines model for NirvanaRole.
type NirvanaRole struct {
	Role string `json:"role"`
}

// OOSViolations defines model for OOSViolations.
type OOSViolations = []OOSViolationsDataItem

// OOSViolationsDataItem defines model for OOSViolationsDataItem.
type OOSViolationsDataItem struct {
	Category                  OOSViolationsDataItemCategory `json:"category"`
	InspectionCount           int32                         `json:"inspectionCount"`
	NationalAveragePercentage float32                       `json:"nationalAveragePercentage"`
	OosCount                  int32                         `json:"oosCount"`
	OosPercentage             float32                       `json:"oosPercentage"`
}

// OOSViolationsDataItemCategory defines model for OOSViolationsDataItem.Category.
type OOSViolationsDataItemCategory string

// OperatingClass defines model for OperatingClass.
type OperatingClass string

// PatchAPIKeyRequest defines model for PatchAPIKeyRequest.
type PatchAPIKeyRequest struct {
	KeyId  openapi_types.UUID `json:"keyId"`
	Scope  *APIKeyScope       `json:"scope,omitempty"`
	Status *KeyStatus         `json:"status,omitempty"`
}

// PaymentMethod defines model for PaymentMethod.
type PaymentMethod string

// PolicyStatus defines model for PolicyStatus.
type PolicyStatus string

// PrimaryCommodityHauled defines model for PrimaryCommodityHauled.
type PrimaryCommodityHauled struct {
	Label *string                    `json:"label,omitempty"`
	Type  PrimaryCommodityHauledEnum `json:"type"`
}

// PrimaryCommodityHauledEnum defines model for PrimaryCommodityHauledEnum.
type PrimaryCommodityHauledEnum string

// ProgramType defines model for ProgramType.
type ProgramType string

// ProgramTypeRequest defines model for ProgramTypeRequest.
type ProgramTypeRequest struct {
	ProgramType *ProgramType `json:"programType,omitempty"`
}

// RedirectionURLResponse defines model for RedirectionURLResponse.
type RedirectionURLResponse struct {
	Url string `json:"url"`
}

// RiskScore defines model for RiskScore.
type RiskScore struct {
	HandleId       string               `json:"handleId"`
	RiskScoreTrend []RiskScoreTrendItem `json:"riskScoreTrend"`
	ScoreType      ScoreType            `json:"scoreType"`
	ScoreVersion   Version              `json:"scoreVersion"`
	UwRubric       struct {
		Items   []UwRubricItem `json:"items"`
		Version Version        `json:"version"`
	} `json:"uwRubric"`
}

// RiskScoreTrendItem defines model for RiskScoreTrendItem.
type RiskScoreTrendItem struct {
	IsSelected     *bool      `json:"isSelected,omitempty"`
	IsShortHaul    *bool      `json:"isShortHaul,omitempty"`
	MarketCategory *string    `json:"marketCategory,omitempty"`
	Score          *float32   `json:"score,omitempty"`
	Timestamp      time.Time  `json:"timestamp"`
	VinCount       *float32   `json:"vinCount,omitempty"`
	WindowEnd      time.Time  `json:"windowEnd"`
	WindowStart    time.Time  `json:"windowStart"`
	WindowType     WindowType `json:"windowType"`
}

// Roles defines model for Roles.
type Roles struct {
	// AgencyRoles List of authz `Agency` roles for this user. If a user has an `agency` role, then they are typically either an `Agent` or an internal `Nirvana` user.
	AgencyRoles *[]AgencyRole `json:"agencyRoles,omitempty"`

	// FleetRoles List of authz `Fleet` roles for this user. If a user has `Fleet` roles but no `Agency` or `Nirvana` roles, then this user can only view their own DOT(s) in the Safety App.
	FleetRoles *[]FleetRole `json:"fleetRoles,omitempty"`

	// NirvanaRoles List of authz internal `Nirvana` roles for this user.
	NirvanaRoles *[]NirvanaRole `json:"nirvanaRoles,omitempty"`
}

// ScoreType defines model for ScoreType.
type ScoreType string

// SevereViolations defines model for SevereViolations.
type SevereViolations = []SevereViolationsItem

// SevereViolationsItem defines model for SevereViolationsItem.
type SevereViolationsItem struct {
	IsControlledSubstancesAndAlcohol *bool               `json:"IsControlledSubstancesAndAlcohol,omitempty"`
	Description                      string              `json:"description"`
	Frequency                        int32               `json:"frequency"`
	Impact                           float32             `json:"impact"`
	InspectionDate                   *openapi_types.Date `json:"inspectionDate,omitempty"`
	PublishedDate                    *openapi_types.Date `json:"publishedDate,omitempty"`
	Violation                        string              `json:"violation"`
}

// SubCoverage defines model for SubCoverage.
type SubCoverage struct {
	Deductible  string  `json:"deductible"`
	Description *string `json:"description,omitempty"`

	// Id id is the enum string value
	Id     string   `json:"id"`
	Limits []string `json:"limits"`
	Name   string   `json:"name"`
}

// SupportedOperationsRecord defines model for SupportedOperationsRecord.
type SupportedOperationsRecord struct {
	OperatingClass              OperatingClass           `json:"operatingClass"`
	PrimaryOperatingCommodities []PrimaryCommodityHauled `json:"primaryOperatingCommodities"`
}

// SupportedOperationsRecordV2 defines model for SupportedOperationsRecordV2.
type SupportedOperationsRecordV2 struct {
	Categories  []CommodityCategory         `json:"categories"`
	Classes     []OperatingClass            `json:"classes"`
	Commodities []CommodityHauled           `json:"commodities"`
	Data        []SupportedOperationsRecord `json:"data"`
}

// SurchargeType Type of surcharge
type SurchargeType string

// TelematicsDataStatus defines model for TelematicsDataStatus.
type TelematicsDataStatus string

// TelematicsIdentifierType defines model for TelematicsIdentifierType.
type TelematicsIdentifierType string

// TelematicsVehicleStatKind defines model for TelematicsVehicleStatKind.
type TelematicsVehicleStatKind string

// TelematicsVehicleStatsItem defines model for TelematicsVehicleStatsItem.
type TelematicsVehicleStatsItem struct {
	Paths []string `json:"paths"`
	Vin   string   `json:"vin"`
}

// TimeInterval defines model for TimeInterval.
type TimeInterval struct {
	End   time.Time `json:"end"`
	Start time.Time `json:"start"`
}

// TokenPagination Response object for token-based pagination. Suitable for infinite-scrolling based pagination schemes. Notably, this scheme does not support jumping to a far-off page number.
type TokenPagination struct {
	PageSize int `json:"pageSize"`

	// PaginationToken Pagination token to get next page of the results. Null token indicates EOF.
	PaginationToken *string `json:"paginationToken"`

	// TotalCount Total number of items in the set
	TotalCount *int `json:"totalCount,omitempty"`
}

// USState Two character short code for the US state the driver is licensed in.
type USState string

// UTMParamMedium defines model for UTMParamMedium.
type UTMParamMedium string

// UTMParamSource defines model for UTMParamSource.
type UTMParamSource string

// UTMTags defines model for UTMTags.
type UTMTags struct {
	UtmAdgroup  *string `json:"utm_adgroup,omitempty"`
	UtmCampaign *string `json:"utm_campaign,omitempty"`
	UtmKeyword  *string `json:"utm_keyword,omitempty"`
	UtmMedium   *string `json:"utm_medium,omitempty"`
	UtmSource   *string `json:"utm_source,omitempty"`
}

// UploadFileRequest defines model for UploadFileRequest.
type UploadFileRequest struct {
	File                 openapi_types.File `json:"file"`
	FileDestinationGroup string             `json:"fileDestinationGroup"`
	FileType             string             `json:"fileType"`
}

// UploadFilesRequest defines model for UploadFilesRequest.
type UploadFilesRequest struct {
	Files     []openapi_types.File `json:"files"`
	FilesInfo []FileInfo           `json:"filesInfo"`
}

// UploadFilesResponse defines model for UploadFilesResponse.
type UploadFilesResponse = []FileHandle

// UploadFlatFileRequest defines model for UploadFlatFileRequest.
type UploadFlatFileRequest struct {
	FileDestinationGroup FileDestinationGroup `json:"fileDestinationGroup"`
	FileType             FileType             `json:"fileType"`
	FlatfileMetadata     FlatfileMetadata     `json:"flatfileMetadata"`
}

// User defines model for User.
type User struct {
	Id   openapi_types.UUID `json:"id"`
	Name string             `json:"name"`
}

// UserProfileResponse defines model for UserProfileResponse.
type UserProfileResponse struct {
	DefaultAgencyId *string             `json:"defaultAgencyId,omitempty"`
	Email           openapi_types.Email `json:"email"`
	Id              string              `json:"id"`
	Name            string              `json:"name"`
	ReportId        string              `json:"reportId"`
	Roles           *Roles              `json:"roles,omitempty"`

	// UserType Type of the user's roles:
	//  * `nirvana` - Internal Nirvana user, such as `superuser` or `support` users.
	//  * `agent` - User belongs to one or more Agency roles.
	//  * `fleet` - User belongs to one or more Fleet roles, and no Agency or Nirvana roles. This user should only have access to the safety app.
	//  * `shared_link` - Shared link user that is anonymously viewing a Safety App shared URL.
	//  * `unprivileged` - User has login credentials but does not belong to any agencies or fleets.
	UserType UserProfileResponseUserType `json:"userType"`
}

// UserProfileResponseUserType Type of the user's roles:
//   - `nirvana` - Internal Nirvana user, such as `superuser` or `support` users.
//   - `agent` - User belongs to one or more Agency roles.
//   - `fleet` - User belongs to one or more Fleet roles, and no Agency or Nirvana roles. This user should only have access to the safety app.
//   - `shared_link` - Shared link user that is anonymously viewing a Safety App shared URL.
//   - `unprivileged` - User has login credentials but does not belong to any agencies or fleets.
type UserProfileResponseUserType string

// UwRubricItem defines model for UwRubricItem.
type UwRubricItem struct {
	Decile         float32  `json:"decile"`
	Discount       *float32 `json:"discount,omitempty"`
	Market         bool     `json:"market"`
	MarketCategory string   `json:"marketCategory"`
	ScoreEnd       int64    `json:"scoreEnd"`
	ScoreStart     int64    `json:"scoreStart"`
}

// VINDetails defines model for VINDetails.
type VINDetails struct {
	BodyClass    string                  `json:"bodyClass"`
	DecodeError  *NHTSAVINDecodeError    `json:"decodeError,omitempty"`
	Make         string                  `json:"make"`
	Manufacturer string                  `json:"manufacturer"`
	Model        string                  `json:"model"`
	ModelYear    string                  `json:"modelYear"`
	ParsedType   NHTSAVehicleType        `json:"parsedType"`
	RawType      string                  `json:"rawType"`
	Trim         string                  `json:"trim"`
	WeightClass  NHTSAVehicleWeightClass `json:"weightClass"`
}

// VerifyTokenRequest defines model for VerifyTokenRequest.
type VerifyTokenRequest struct {
	Email       openapi_types.Email `json:"email"`
	NewPassword string              `json:"newPassword"`
	Token       string              `json:"token"`
}

// Version defines model for Version.
type Version string

// WindowType defines model for WindowType.
type WindowType string

// WorkrampLoginRequest defines model for WorkrampLoginRequest.
type WorkrampLoginRequest struct {
	// DestPath The relative path where the user should be redirected to upon authenticating with absoluteLoginUrl.
	DestPath *string `json:"destPath,omitempty"`
}

// WorkrampLoginResponse defines model for WorkrampLoginResponse.
type WorkrampLoginResponse struct {
	AbsoluteLoginUrl string `json:"absoluteLoginUrl"`
}

// ApplicationId defines model for ApplicationId.
type ApplicationId = string

// ApplicationType defines model for ApplicationType.
type ApplicationType string

// DotNumber defines model for DotNumber.
type DotNumber = int64

// FileHandleParam defines model for FileHandleParam.
type FileHandleParam = string

// FileHandleParamUUID defines model for FileHandleParamUUID.
type FileHandleParamUUID = openapi_types.UUID

// KeyID defines model for KeyID.
type KeyID = openapi_types.UUID

// PaginationToken defines model for PaginationToken.
type PaginationToken = string

// PolicyId defines model for PolicyId.
type PolicyId = string

// PolicyIdentifier defines model for PolicyIdentifier.
type PolicyIdentifier = string

// PolicyIssuanceYear defines model for PolicyIssuanceYear.
type PolicyIssuanceYear = int16

// PolicyNumber defines model for PolicyNumber.
type PolicyNumber = string

// ProviderType defines model for ProviderType.
type ProviderType string

// TelematicsIdentifierValue defines model for TelematicsIdentifierValue.
type TelematicsIdentifierValue = string

// UTMMedium defines model for UTMMedium.
type UTMMedium = UTMParamMedium

// UTMSource defines model for UTMSource.
type UTMSource = UTMParamSource

// UserID defines model for UserID.
type UserID = string
