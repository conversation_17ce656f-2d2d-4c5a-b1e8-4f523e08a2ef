
openapi: 3.0.0
info:
  version: 1.0.0
  title: Nirvana Non Fleet Underwriting API
  description: Nirvana Non Fleet Underwriting APIs

servers:
  - url: https://api.prod.nirvanatech.com/underwriting

tags:
  - name: 'underwriting'
    description: 'Non Fleet Underwriting related APIs'

security:
  - sessionIdAuth: []

paths:
  /nonfleet/underwriting/application_reviews:
    get:
      operationId: GetApplicationReviews
      tags:
        - 'underwriting'
      description: Get all reviews
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewDetails'
        '422':
          description: Unable to load Application Reviews
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_review/list:
    get:
      operationId: GetApplicationReviewsWithPagination
      tags:
        - "underwriting"
      description: Get all application reviews with pagination
      parameters:
        - in: query
          name: size
          schema:
            type: integer
        - in: query
          name: cursor
          schema:
            type: string
        - in: query
          name: q
          schema:
            type: string
        - in: query
          name: effectiveDateBefore
          schema:
            type: string
            format: date
        - in: query
          name: effectiveDateAfter
          schema:
            type: string
            format: date
        - in: query
          name: underWriterID
          schema:
            type: string
        - in: query
          name: tab
          schema:
            $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewTab'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewsWithPaginationResponse'
        '422':
          description: Unable to load Applications Reviews
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /nonfleet/underwriting/application_reviews/{applicationReviewID}/flags:
    get:
      operationId: GetApplicationReviewFlags
      tags:
        - 'underwriting'
      description: Get all flags info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewFlag'
        '422':
          description: Unable to load Application Review Operations
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/operations:
    get:
      operationId: GetApplicationReviewOperations
      tags:
        - 'underwriting'
      description: Get all operations tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewOperation'
        '422':
          description: Unable to load Application Review Operations
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperations
      tags:
        - 'underwriting'
      description: Update operations tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Operations tab panel info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewOperationForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/equipments:
    get:
      operationId: GetApplicationReviewEquipments
      tags:
        - 'underwriting'
      description: Get all equipments tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewEquipment'
        '422':
          description: Unable to load Application Review Equipments
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewEquipments
      tags:
        - 'underwriting'
      description: Update equipments tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Equipments tab panel info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewEquipmentForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/set_mvr_pull:
    post:
      operationId: SetApplicationReviewMVRPull
      tags:
        - 'underwriting'
      description: Set MVR Pull flag as true
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '201':
          description: MVR Pull Flag was set to true
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/MVRFlag'
        '422':
          description: Unable to toggle MVR Pull
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/approve:
    post:
      operationId: ApproveApplicationReview
      tags:
        - 'underwriting'
      description: Approve the application
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Approve the application
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApproveApplicationReviewForm'
      responses:
        '201':
          description: Approved the application
        '422':
          description: Unable to approve the application
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/decline:
    post:
      operationId: DeclineApplicationReview
      tags:
        - 'underwriting'
      description: Decline the application
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Decline the application
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/DeclineApplicationReviewForm'
      responses:
        '201':
          description: Declined the application
        '422':
          description: Unable to decline the application
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/close:
    post:
      operationId: CloseApplicationReview
      tags:
        - 'underwriting'
      description: Close the application
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Close the application
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/CloseApplicationReviewForm'
      responses:
        '201':
          description: Close the application
        '422':
          description: Unable to close the application
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/refer:
    post:
      operationId: ReferApplicationReview
      tags:
        - 'underwriting'
      description: Refer the application
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Refer the application
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ReferApplicationReviewForm'
      responses:
        '201':
          description: Referred the application
        '422':
          description: Unable to refer the application
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/drivers:
    get:
      operationId: GetApplicationReviewDrivers
      tags:
        - 'underwriting'
      description: Get all drivers tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewDriver'
        '422':
          description: Unable to load Application Review Drivers
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewDrivers
      tags:
        - 'underwriting'
      description: Update drivers tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Drivers tab panel info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewDriversForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/driver/{dlNumber}:
    patch:
      operationId: UpdateApplicationReviewDriver
      tags:
        - 'underwriting'
      description: Update driver tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
        - name: dlNumber
          in: path
          description: Driver License Number
          required: true
          schema:
            type: string
      requestBody:
        description: Driver tab panel info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewDriverRecordForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/safety:
    get:
      operationId: GetApplicationReviewSafety
      tags:
        - 'underwriting'
      description: Get all safety tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewSafety'
        '422':
          description: Unable to load Application Review Safety
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafety
      tags:
        - 'underwriting'
      description: Update safety tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Safety tab panel info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewSafetyForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/losses:
    get:
      operationId: GetApplicationReviewLosses
      tags:
        - 'underwriting'
      description: Get all losses tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewLosses'
        '422':
          description: Unable to load Application Review Losses
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewLosses
      tags:
        - 'underwriting'
      description: Update losses tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Losses tab panel info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewLossesForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/packages:
    get:
      operationId: GetApplicationReviewsPackages
      tags:
        - 'underwriting'
      description: Get all packages tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewPackages'
        '422':
          description: Unable to load Application Review Losses
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewPackages
      tags:
        - 'underwriting'
      description: Update packages tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Packages tab panel info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewPackagesForm'
      responses:
        '201':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/summary:
    get:
      operationId: GetApplicationReviewsSummary
      tags:
        - 'underwriting'
      description: Get all packages tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewSummary'
        '422':
          description: Unable to load Application Review Losses
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/quote/submit:
    post:
      operationId: PostApplicationReviewQuoteSubmit
      tags:
        - 'underwriting'
      description: Trigger RateML Job to generate Quote
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
        - in: query
          name: bindable
          schema:
            type: boolean
      responses:
        '201':
          description: Submission successful
        '422':
          description: Failed Application Creation
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/timeline:
    get:
      operationId: GetApplicationReviewTimeline
      tags:
        - 'underwriting'
      description: Get all timeline tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewTimeline'
        '422':
          description: Unable to load Application Review Timeline
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/notes:
    get:
      operationId: GetApplicationReviewNotes
      tags:
        - 'underwriting'
      description: Return application review's notes
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewNotes'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewNotes
      tags:
        - 'underwriting'
      description: Update application review's notes
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Note info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewNotes'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /nonfleet/underwriting/application_reviews/{applicationReviewID}/documents:
    get:
      operationId: GetApplicationReviewDocuments
      tags:
        - 'underwriting'
      description: Return application review's documents
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewDocuments'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    post:
      operationId: UploadApplicationReviewDocuments
      tags:
        - 'underwriting'
      description: Upload documents for an application_review
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '../../components/common/spec.yaml#/components/schemas/UploadFileRequest'
      responses:
        '201':
          description: Upload successfully
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/FileHandle'
        default:
          description: Unprocessable Document
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /nonfleet/underwriting/application_reviews/{applicationReviewID}/safety/safety_score:
    get:
      operationId: GetApplicationReviewSafetyScore
      tags:
        - 'underwriting'
      description: Return application review's safety score (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyScoreV2'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyScoreV2
      tags:
        - 'underwriting'
      description: Update application review's safety score
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyScoreFormV2'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /nonfleet/underwriting/application_reviews/{applicationReviewID}/rollback:
    post:
      operationId: RollbackApplicationReview
      tags:
        - 'underwriting'
      description: Rollbacks an approved application
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Rollback Successful
        '422':
          description: Unable to rollback application review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/{applicationReviewID}/scrape:
    post:
      operationId: TriggerScrape
      tags:
        - 'underwriting'
      description: Triggers a scrape request for an application review
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
        - name: scrapeType
          in: query
          description: The type of scrape to trigger
          required: true
          schema:
            $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ScrapeType'
      responses:
        '201':
          description: Scrape Triggered
        '422':
          description: Unable to trigger scrape
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    get:
      operationId: GetScrapeInfo
      tags:
        - 'underwriting'
      description: Fetches the scrape info for an application review
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
        - name: scrapeType
          in: query
          description: The type of scrape to trigger
          required: true
          schema:
            $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ScrapeType'
      responses:
        '200':
          description: Fetched Scrape Info
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ScrapeResponse'
        '422':
          description: Unable to trigger scrape
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/application_reviews/{applicationReviewID}/assignee:
    patch:
      operationId: UpdateApplicationReviewAssignee
      tags:
        - "underwriting"
      description: Update the application review's assignee
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewAssigneesForm"
      responses:
        "200":
          description: Assigned successfully
        "422":
          description: Unprocessable data
          content:
            application/json:
              schema:
                $ref: "../../components/common/spec.yaml#/components/schemas/ErrorMessage"
  /nonfleet/underwriting/driver-violations:
    get:
      operationId: GetDriverViolations
      tags:
        - 'underwriting'
      description: Get all PGR driver violations
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '../../components/common/spec.yaml#/components/schemas/DriverViolation'
        '422':
          description: Unable to get PGR driver violations
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/v2/application-reviews/{applicationReviewID}/driver/{dlNumber}:
    patch:
      operationId: UpdateApplicationReviewDriverV2
      tags:
        - 'underwriting'
      description: Update driver tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
        - name: dlNumber
          in: path
          description: Driver License Number
          required: true
          schema:
            type: string
      requestBody:
        description: Driver tab panel info
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewDriverRecordForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /nonfleet/underwriting/v2/application-reviews/{applicationReviewID}/drivers:
    get:
      operationId: GetApplicationReviewDriversV2
      tags:
        - 'underwriting'
      description: Get all drivers tab panel info
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewDriver'
        '422':
          description: Unable to load Application Review Drivers
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /nonfleet/underwriting/application_reviews/{applicationReviewID}/actions:
    get:
      operationId: GetNonFleetApplicationReviewActions
      tags:
        - 'underwriting'
        - 'action'
      description: Returns a list of actions visible to the user
      parameters:
        - $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ApplicationReviewGetActionsResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /nonfleet/underwriting/application_reviews/{applicationReviewID}/express_lane_feedback/submit:
    get:
      operationId: GetExpressLaneFeedback
      tags:
        - 'underwriting'
        - 'express_lane'
      description: Returns a list of actions visible to the user
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ExpressLaneFeedbackGetResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      description: Patch express lane feedback
      operationId: PatchExpressLaneFeedback
      parameters:
        - name: applicationReviewID
          in: path
          description: Application Review ID
          required: true
          schema:
            type: string
      requestBody:
        description: Feedback for express lane
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - panel_wise_decision
              properties:
                overall_decision:
                  $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/DecisionType'
                overall_reason:
                  type: string
                  example: "The decision aligns with our safety standards."
                panel_wise_decision:
                  type: array
                  items:
                    $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ExpressLanePanelWiseDecision'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/nonfleet_underwriting/spec.yaml#/components/schemas/ExpressLaneFeedbackPatchResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
