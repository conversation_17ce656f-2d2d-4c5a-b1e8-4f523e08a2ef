# Appetite Guidelines – package overview

Appetite guidelines determine whether a risk falls inside or outside the company's underwriting appetite.  The code in `underwriting/appetite_guidelines` builds the data ("facts") required for the evaluation, runs the corresponding rule set, and stores the resulting guideline decisions.

Key sub-packages
----------------

1. `models/` – Go structs shared across the package (`InputFact`, `OutputFact`, `Decisions`, etc.).
2. `guidelines/` – Core logic:
   * `fact_generators/` – small pluggable components that build an `InputFact`.
   * `FactService` – orchestrates generators.
   * `RuleService` – loads rule sets from DB & executes them with `hyperjumptech/grule-rule-engine`.
   * `Manager` – end-to-end façade used by callers (`GenerateGuidelines`, `GetGuidelines`).
3. `db/` – Thin wrapper around `sqlc`-generated queries for rule-sets & decisions.

Entry points
------------

* **Generate guidelines** – `Manager.GenerateGuidelines(ctx, category, program, appReviewID)`
  * Builds facts, executes rules, writes a `Decisions` row (if not duplicate).
* **Fetch latest guidelines** – `Manager.GetGuidelines(ctx, category, program, appReviewID)`

Extending the system
--------------------

1. Add new *fact generator* ➜ see `guidelines/fact_generators/README.md`.
2. Add / update *rules* ➜ insert new row into `rule_sets` table with incremented `version`.
3. Persist extra data ➜ extend `models.InputFact` & `OutputFact`, update generators and rules accordingly.
