# Appetite Guidelines – `guidelines` package

This package is responsible for **building the *input fact*** that will later be fed into appetite-guideline rules.

It does so by composing a **bundle of small, independent *generators***.  Each generator knows how to fetch one specific piece of information (burn-rates, DOT rating, …) from upstream services and attach it to an `models.InputFact` instance.

The diagram below shows the high-level flow:

```
                          +-----------------------+
                          |  FactService          |
                          +-----------------------+
                                      |
                                      | calls
                                      v
                          +-----------------------+
                          | Composite generator   |
                          +-----------------------+
                             /           \
                   +---------+             +---------+
                   |  Sub-gen.|             | Sub-gen.|
                   +----------+             +---------+
```

> **TL;DR** – The core abstractions are:
>
> * `Generator` interface – contract that every generator must implement
> * `BundleVersion` – a *named set* of generators to run together
> * **Composite generator** – loops through the generators in a bundle, runs them and applies the results back onto the fact

---

## 1. `Generator` interface

```go
// interfaces.go

type Generator interface {
    // Unique name + version, e.g. {Name: "losses_burn_rate", Version: 1}
    Name() types.GeneratorVersion

    // Generate retrieves the raw data from an upstream system.
    //   • ctx  – request context
    //   • args – arbitrary arguments (usually *types.GeneratorArgs)
    // It returns a generator-specific result object.
    Generate(ctx context.Context, args any) (any, error)

    // Apply takes the result produced by Generate and mutates the provided
    // *models.InputFact in-place.
    Apply(f *models.InputFact, result any) error
}
```

### Notes
* **Versioning** – `Name()` returns a `types.GeneratorVersion` (`{Name, Version}`) so that multiple versions of the **same logical generator** can coexist side-by-side.
* **Decoupling** – `Generate` and `Apply` are deliberately separate.  This lets us run heavy I/O (e.g. DB calls) before touching the `InputFact`, simplifying error handling.

---

## 2. Generator registry

All concrete generators are registered at process start-up via

```go
var _ = fxregistry.Register(fx.Invoke(RegisterGenerators))
```

`Registry` is just an in-memory map guarded by an `RWMutex`.  It supports:

* `Register(g Generator)` – add new generator implementation.
* `Get(version types.GeneratorVersion)` – fetch by name+version.

This indirection makes testing easier and keeps the **Composite** generator generic – it only speaks to the registry.

---

## 3. Bundles

A **bundle** is a curated *list* of generator versions that should be executed together.

```go
// versions.go

type BundleVersion int

const (
    BundleVersionV1 BundleVersion = iota + 1
)

var BundleToGenerators = map[BundleVersion][]types.GeneratorVersion{
    BundleVersionV1: {
        types.GV(loss_burn_rate.LossesBurnRateName, loss_burn_rate.LossesBurnRateV1),
        // … add more generator versions here …
    },
}
```

Why bundles?

1. **Stability** – changing the list of generators counts as a *breaking* change.  We keep the old bundle around (V1, V2, …) so historical decisions are reproducible.
2. **Feature Flags** – the caller (`FactService`) decides *which* bundle to run, giving us an easy way to roll-out new data sources.

---

## 4. Composite generator

`Composite` itself **implements the `Generator` interface** so it can be treated like any other generator – but internally it orchestrates the sub-generators defined in a bundle.

Important bits (see `composite_generator.go`):

1. **Generate**
    * Verifies the bundle exists and arguments are non-nil.
    * Iterates over every `GeneratorVersion` in the bundle:
        1. Fetch generator from registry.
        2. Call `Generate`.
        3. Collect result in a `CompositeResult` map.
    * Aggregates any errors via `go-multierror`.
2. **Apply**
    * For every `(version → value)` pair in the `CompositeResult` map
        1. Fetch generator from registry.
        2. Delegate to generator’s `Apply` to mutate the `InputFact`.

Because `Composite` delegates all domain logic to sub-generators it remains slim and testable.

---

## 5. Concrete sub-generators

A sub-generator lives under `fact_generators/<topic>/`.

Example: **Loss burn-rate v1** (`loss_burn_rate/v1.go`)

```go
type V1 struct { /* dependencies */ }

func (v *V1) Name() types.GeneratorVersion {
    return types.GV(LossesBurnRateName, LossesBurnRateV1)
}

func (v *V1) Generate(ctx context.Context, args any) (any, error) {
    concreteArgs := args.(*types.GeneratorArgs)
    // 1. Fetch data from app-review service
    // 2. Transform into V1Result map
    return V1Result{/* AL, APD, MTC */}, nil
}

func (v *V1) Apply(f *models.InputFact, result any) error {
    res := result.(V1Result)
    f.AlLossesBurnRate  = null.Float32FromPtr(res[KeyAL])
    f.ApdLossesBurnRate = null.Float32FromPtr(res[KeyAPD])
    f.MtcLossesBurnRate = null.Float32FromPtr(res[KeyMTC])
    return nil
}
```

### Naming convention
* Directory: `fact_generators/<topic>/`
* Constants:
  * `<Topic>Name` – canonical generator name
  * `<Topic>V<N>` – version number

---

## 6. Adding a new generator (check-list)

1. Create `fact_generators/<topic>/v<N>.go` implementing the `Generator` interface.
2. Add constants `<Topic>Name`, `<Topic>V<N>`.
3. Wire it into DI:
   * Add constructor to `fact_generators/fx.go` `fx.Provide()` list.
   * Register it inside `RegisterGenerators()`.
4. Decide if it belongs to an existing bundle or a brand-new bundle.
   * Update `BundleToGenerators` accordingly.
5. Write unit-tests.

---

## 7. FAQ

**Q: Why do `Generate` and `Apply` take `any` instead of a concrete type?**

A: Each generator has its own input/output data-shapes.  Using `any` avoids polluting shared packages with many tiny structs.  The trade-off is that each generator must perform type-assertions internally.

**Q: How is duplication avoided when persisting decisions?**

A: `models.Decisions.Equal` performs a deep equality check on the generated output.  `Manager.isDuplicateDecision` uses this to skip inserting identical successive decisions.