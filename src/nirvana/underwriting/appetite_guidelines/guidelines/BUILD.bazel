load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "guidelines",
    srcs = [
        "fact_service.go",
        "fx.go",
        "manager.go",
        "rule_service.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/rule-engine",
        "//nirvana/common-go/log",
        "//nirvana/common-go/rule_engine",
        "//nirvana/common-go/str_utils",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/underwriting/app_review",
        "//nirvana/underwriting/appetite_guidelines/db",
        "//nirvana/underwriting/appetite_guidelines/guidelines/fact_generators",
        "//nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types",
        "//nirvana/underwriting/appetite_guidelines/models",
        "@com_github_benb<PERSON><PERSON><PERSON>_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_hyperjumptech_grule_rule_engine//ast",
        "@com_github_hyperjumptech_grule_rule_engine//engine",
        "@com_github_hyperjumptech_grule_rule_engine//pkg",
        "@com_github_labstack_gommon//log",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "guidelines_test",
    srcs = ["manager_test.go"],
    embed = [":guidelines"],
    deps = [
        "//nirvana/infra/fx/testfixtures/appetite_guidelines_fixture",
        "//nirvana/infra/fx/testfixtures/application_review_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/underwriting/appetite_guidelines/models",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
