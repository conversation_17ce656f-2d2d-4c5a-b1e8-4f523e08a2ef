package guidelines

import (
	"context"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators"

	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

type FactService struct {
	manager  uw_app_review.ReviewManager
	registry *fact_generators.Registry
}

func NewFactService(m uw_app_review.ReviewManager, r *fact_generators.Registry) *FactService {
	return &FactService{
		manager:  m,
		registry: r,
	}
}

func (f *FactService) GenerateFact(ctx context.Context, appReviewID string) (*models.InputFact, error) {
	fact := &models.InputFact{}

	// default generator bundle (V1) for now
	args := &fact_generators.CompositeArgs{
		Bundle: fact_generators.BundleVersionV1,
		SubArgs: &types.GeneratorArgs{
			AppReviewId: appReviewID,
		},
	}

	compositeGenerator, err := f.registry.Get(fact_generators.DefaultCompositeGeneratorVersion())
	if err != nil {
		return nil, errors.Wrap(err, "failed to get composite generator")
	}

	result, err := compositeGenerator.Generate(ctx, args)
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate composite fact")
	}

	compositeResult, ok := result.(fact_generators.CompositeResult)
	if !ok {
		return nil, errors.Newf("unexpected result type: %T; expected CompositeResult", result)
	}

	err = compositeGenerator.Apply(fact, compositeResult)
	if err != nil {
		return nil, errors.Wrap(err, "failed to apply composite result to fact")
	}

	return fact, nil
}
