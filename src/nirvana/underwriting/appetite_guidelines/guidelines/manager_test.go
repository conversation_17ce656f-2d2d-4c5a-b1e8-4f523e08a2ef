package guidelines

import (
	"testing"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/appetite_guidelines_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"

	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

func TestGenerateGuidelines(t *testing.T) {
	t.Parallel()
	var env struct {
		fx.In

		*appetite_guidelines_fixture.Fixture
		AppReviewFixture *application_review_fixture.ApplicationReviewsFixture
		Manager          *Manager
	}

	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := t.Context()

	err := env.Manager.GenerateGuidelines(ctx,
		models.CategoryInternal,
		models.ProgramFleetAdmitted,
		uuid.MustParse(env.AppReviewFixture.ApplicationReview.Id))
	require.NoError(t, err)

	decision, err := env.Manager.GetGuidelines(ctx, models.CategoryInternal,
		models.ProgramFleetAdmitted,
		uuid.MustParse(env.AppReviewFixture.ApplicationReview.Id))
	require.NoError(t, err)
	require.Equal(t, models.DecisionNone, decision.Decision)
}
