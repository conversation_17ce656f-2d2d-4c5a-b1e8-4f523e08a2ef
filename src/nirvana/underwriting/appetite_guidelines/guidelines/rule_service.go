package guidelines

import (
	"context"
	"fmt"
	"strconv"

	"github.com/cockroachdb/errors"
	"github.com/hyperjumptech/grule-rule-engine/ast"
	"github.com/hyperjumptech/grule-rule-engine/engine"
	"github.com/hyperjumptech/grule-rule-engine/pkg"
	ruleengine "nirvanatech.com/nirvana/api-server/rule-engine"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/rule_engine"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/db"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

type RuleService struct {
	ruleEngine *ruleengine.RuleEngine
	dbWrapper  *db.AppetiteGuidelinesDbWrapper
}

func NewRuleService(r *ruleengine.RuleEngine, d *db.AppetiteGuidelinesDbWrapper) *RuleService {
	return &RuleService{
		ruleEngine: r,
		dbWrapper:  d,
	}
}

func (r *RuleService) LoadAndExecute(
	ctx context.Context,
	category models.Category,
	program models.Program,
	inputFact *models.InputFact,
	version int,
) (*models.OutputFact, error) {
	log.Info(ctx, "Loading and executing rule set",
		log.Stringer("program", program), log.Int("version", version))

	ruleName := fmt.Sprintf("Program_%s_Version_%d", str_utils.ToSnakeCase(program.String()), version)
	versionStr := strconv.Itoa(version)

	if err := r.ensureRuleSetLoaded(ctx, category, program, ruleName, versionStr, version); err != nil {
		return nil, err
	}

	dataCtx := ast.NewDataContext()
	if err := dataCtx.Add("input", inputFact); err != nil {
		return nil, errors.Wrapf(err, "failed to add fact to data context")
	}

	outputFact := &models.OutputFact{}
	if err := dataCtx.Add("output", outputFact); err != nil {
		return nil, errors.Wrapf(err, "failed to add output fact to data context")
	}

	workableKnowledgeBase := r.ruleEngine.KnowledgeLibrary.NewKnowledgeBaseInstance(ruleName, versionStr)
	if err := engine.NewGruleEngine().ExecuteWithContext(ctx, dataCtx, workableKnowledgeBase); err != nil {
		return nil, errors.Wrapf(err, "failed to execute rule engine")
	}

	return outputFact, nil
}

func (r *RuleService) GetRuleSetByVersion(
	ctx context.Context,
	category models.Category,
	program models.Program,
	version int,
) (*models.RuleSet, error) {
	ruleSet, err := r.dbWrapper.GetRuleSetByVersion(ctx, category.String(), program.String(), version)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get rule set for program %s and version %d", program, version)
	}
	return ruleSet, nil
}

func (r *RuleService) ensureRuleSetLoaded(
	ctx context.Context,
	category models.Category,
	program models.Program,
	ruleName, versionStr string,
	version int,
) error {
	knowledgeBase := r.ruleEngine.KnowledgeLibrary.GetKnowledgeBase(ruleName, versionStr)
	if knowledgeBase.ContainsRuleEntry(ruleName) {
		return nil
	}

	// Lazy load from DB and build rules
	ruleSet, err := r.GetRuleSetByVersion(ctx, category, program, version)
	if err != nil {
		return errors.Wrapf(err, "failed to get rule set for program %s and version %d", program, version)
	}

	underlying := pkg.NewBytesResource(ruleSet.Rules)
	resource := pkg.NewJSONResourceFromResource(underlying)

	err = rule_engine.BuildRuleFromResourceWithErrCheck(ctx, r.ruleEngine.RuleBuilder, ruleName, versionStr, resource)
	if err != nil {
		return errors.Wrapf(err, "failed to build rule from resource for program %s and version %d", program, version)
	}
	return nil
}
