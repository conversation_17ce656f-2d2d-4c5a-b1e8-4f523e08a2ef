package loss_burn_rate

import (
	"context"

	"github.com/volatiletech/null/v8"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types"

	"github.com/cockroachdb/errors"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
)

type V1 struct {
	manager uw_app_review.ReviewManager
}

func NewV1(manager uw_app_review.ReviewManager) *V1 {
	return &V1{
		manager: manager,
	}
}

func (v *V1) Name() types.GeneratorVersion {
	return types.GeneratorVersion{
		Name:    LossesBurnRateName,
		Version: LossesBurnRateV1,
	}
}

const (
	KeyAL  = "AL"
	KeyAPD = "APD"
	KeyMTC = "MTC"
)

type V1Result map[string]*float32

func (v *V1) Generate(ctx context.Context, args any) (any, error) {
	concreteArgs, ok := args.(*types.GeneratorArgs)
	if !ok {
		return nil, errors.Newf("invalid args: %+v; expected GeneratorArgs type", args)
	}
	lossAvg, err := v.manager.Losses.LossAverages.Get(ctx, concreteArgs.AppReviewId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get losses average")
	}

	for _, loss := range lossAvg.Data {
		if loss.AverageType != "Burn Rate" {
			continue
		}
		return V1Result{
			KeyAL:  loss.AutoLiability,
			KeyAPD: loss.AutoPhysicalDamage,
			KeyMTC: loss.MotorTruckCargo,
		}, nil
	}

	return V1Result{}, nil
}

func (v *V1) Apply(f *models.InputFact, result any) error {
	concreteResult, ok := result.(V1Result)
	if !ok {
		return errors.Newf("unexpected result type: %T; expected V1Result", result)
	}

	if len(concreteResult) == 0 {
		return nil
	}

	al, alOk := concreteResult[KeyAL]
	if !alOk {
		return errors.New("missing required burn rate key: AL")
	}
	f.AlLossesBurnRate = null.Float32FromPtr(al)

	if apd, apdOk := concreteResult[KeyAPD]; apdOk {
		f.ApdLossesBurnRate = null.Float32FromPtr(apd)
	}

	if mtc, mtcOk := concreteResult[KeyMTC]; mtcOk {
		f.MtcLossesBurnRate = null.Float32FromPtr(mtc)
	}

	return nil
}
