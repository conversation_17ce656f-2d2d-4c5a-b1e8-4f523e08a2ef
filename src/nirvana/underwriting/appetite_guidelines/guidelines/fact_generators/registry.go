package fact_generators

import (
	"sync"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types"
)

type Registry struct {
	mu         sync.RWMutex
	generators map[types.GeneratorVersion]Generator
}

func NewRegistry() *Registry {
	return &Registry{
		generators: make(map[types.GeneratorVersion]Generator),
	}
}

func (r *Registry) Register(g Generator) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.generators[g.Name()] = g
}

func (r *Registry) Get(name types.GeneratorVersion) (Generator, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	g, ok := r.generators[name]
	if !ok {
		return nil, errors.Newf("generator not found: %s", name)
	}
	return g, nil
}
