load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fact_generators",
    srcs = [
        "composite_generator.go",
        "fx.go",
        "interfaces.go",
        "registry.go",
        "versions.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/loss_burn_rate",
        "//nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types",
        "//nirvana/underwriting/appetite_guidelines/models",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_hashicorp_go_multierror//:go-multierror",
        "@org_uber_go_fx//:fx",
    ],
)
