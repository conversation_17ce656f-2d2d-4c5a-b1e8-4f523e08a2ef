package fact_generators

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/hashicorp/go-multierror"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"
)

type Composite struct {
	registry *Registry
}

func NewComposite(registry *Registry) *Composite {
	return &Composite{
		registry: registry,
	}
}

func DefaultCompositeGeneratorVersion() types.GeneratorVersion {
	return types.GV(GeneratorName, DefaultGeneratorVersion)
}

func (c *Composite) Name() types.GeneratorVersion {
	return DefaultCompositeGeneratorVersion()
}

type CompositeResult map[types.GeneratorVersion]any

type CompositeArgs struct {
	Bundle  BundleVersion
	SubArgs *types.GeneratorArgs // TODO: move to models pkg instead
}

func (c *Composite) Generate(ctx context.Context, args any) (any, error) {
	concreteArgs, ok := args.(*CompositeArgs)
	if !ok {
		return nil, errors.Newf("invalid args: %+v; expected CompositeArgs type", args)
	}

	subVersions, ok := BundleToGenerators[concreteArgs.Bundle]
	if !ok {
		return nil, errors.Newf("unknown bundle version: %d", concreteArgs.Bundle)
	}

	if len(subVersions) == 0 {
		return nil, errors.Newf("no sub-generators found for bundle version: %d", concreteArgs.Bundle)
	}

	if concreteArgs.SubArgs == nil {
		return nil, errors.Newf("sub-args cannot be nil for bundle version: %d", concreteArgs.Bundle)
	}

	results := make(CompositeResult)
	var errs error

	for _, version := range subVersions {
		gen, err := c.registry.Get(version)
		if err != nil {
			errs = multierror.Append(errs, err)
			continue
		}

		res, err := gen.Generate(ctx, concreteArgs.SubArgs)
		if err != nil {
			errs = multierror.Append(errs, err)
			continue
		}

		results[version] = res
	}

	return results, errs
}

func (c *Composite) Apply(f *models.InputFact, compositeResult any) error {
	concreteResult, ok := compositeResult.(CompositeResult)
	if !ok {
		return errors.Newf("unexpected result type: %T; expected CompositeResult", compositeResult)
	}

	for name, value := range concreteResult {
		generator, err := c.registry.Get(name)
		if err != nil {
			return errors.Wrapf(err, "failed to get generator: %s", name)
		}

		err = generator.Apply(f, value)
		if err != nil {
			return errors.Wrapf(err, "failed to apply generator: %s", name)
		}
	}
	return nil
}
