package fact_generators

import (
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/loss_burn_rate"
)

var _ = fxregistry.Register(
	fx.Provide(NewRegistry,
		loss_burn_rate.NewV1,
		NewComposite,
	),
)

var _ = fxregistry.Register(
	fx.Invoke(RegisterGenerators))

func RegisterGenerators(
	r *Registry,
	lossBurnRateV1 *loss_burn_rate.V1,
	composite *Composite,
) {
	r.Register(lossBurnRateV1)
	r.Register(composite)
}
