package types

// GeneratorArgs, GeneratorVersion, and GV are defined here to avoid cyclic imports
// when these types are shared across multiple packages.

type GeneratorArgs struct {
	AppReviewId string
}

type GeneratorVersion struct {
	Name    string
	Version int
}

// GV returns a new instance of GeneratorVersion with the provided name and version.
func GV(name string, version int) GeneratorVersion {
	return GeneratorVersion{Name: name, Version: version}
}
