package fact_generators

import (
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/loss_burn_rate"
	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/guidelines/fact_generators/types"
)

const (
	GeneratorName           = "composite_generator"
	DefaultGeneratorVersion = 1
)

type BundleVersion int

const (
	BundleVersionV1 BundleVersion = iota + 1
)

// BundleToGenerators says “Bundle X consists of these sub-generators”
var BundleToGenerators = map[BundleVersion][]types.GeneratorVersion{
	BundleVersionV1: {
		types.GV(loss_burn_rate.LossesBurnRateName, loss_burn_rate.LossesBurnRateV1),
	},
}
