load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "db",
    srcs = [
        "fx.go",
        "serde.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/appetite_guidelines/db",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/appetite_guidelines",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/underwriting/appetite_guidelines/models",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "db_test",
    srcs = [
        "serde_test.go",
        "wrapper_test.go",
    ],
    embed = [":db"],
    deps = [
        "//nirvana/db-api/db_models/appetite_guidelines",
        "//nirvana/infra/fx/testfixtures/appetite_guidelines_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/underwriting/appetite_guidelines/models",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_sqlboiler_v4//types",
        "@org_uber_go_fx//:fx",
    ],
)
