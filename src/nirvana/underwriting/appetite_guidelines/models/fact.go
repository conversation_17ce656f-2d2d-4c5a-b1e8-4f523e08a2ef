package models

import (
	"github.com/cockroachdb/errors"
	"github.com/volatiletech/null/v8"
)

const (
	RuleLossesBurnRate  = "LossesBurnRate"
	RuleYearsInBusiness = "YearsInBusiness"
	RuleDotRating       = "DotRating"
)

// InputFact represents the input data for the appetite guidelines rule engine.
type InputFact struct {
	AlLossesBurnRate  null.Float32
	ApdLossesBurnRate null.Float32
	MtcLossesBurnRate null.Float32
	TotalMonths       null.Int
	DotRating         null.String
}

// OutputFact represents the output data after applying the appetite guidelines rules. Grule rule engine
// uses this to store decisions made based on the input fact.
type OutputFact struct {
	Decisions []DecisionEntity
}

func (f *OutputFact) addDecision(rule, decisionStr string, variant *string) {
	decision, _ := DecisionString(decisionStr)

	f.Decisions = append(f.Decisions, DecisionEntity{
		RuleName: rule,
		Variant:  variant,
		Decision: decision,
	})
}

func (f *OutputFact) SetLossesBurnRateDecision(d, variant string) {
	f.addDecision(RuleLossesBurnRate, d, &variant)
}

func (f *OutputFact) SetYearsInBusinessDecision(d string) {
	f.addDecision(RuleYearsInBusiness, d, nil)
}

func (f *OutputFact) SetDotRatingDecision(d string) {
	f.addDecision(RuleDotRating, d, nil)
}

func (f *OutputFact) GenerateOverallDecision() (Decision, error) {
	if len(f.Decisions) == 0 {
		return DecisionInvalid, errors.New("no decisions available to generate overall decision")
	}

	for _, d := range f.Decisions {
		if d.Decision == DecisionDecline {
			return DecisionDecline, nil
		}
		// continue if the decision is not Decline
	}

	for _, d := range f.Decisions {
		if d.Decision == DecisionFurtherReview {
			return DecisionFurtherReview, nil
		}
	}

	return DecisionNone, nil
}
