package models

import (
	"time"

	"github.com/google/uuid"
)

type Decisions struct {
	Id                  uuid.UUID
	RuleSetId           uuid.UUID // RuleSetId is the ID of the rule set used to generate this decision
	ApplicationReviewID uuid.UUID
	Decision            Decision // Decision represents the overall decision made based on the rules applied
	CreatedAt           time.Time
	InputContext        *InputFact  // InputFact contains the input data used to generate the decision
	OutputContext       *OutputFact // OutputFact contains the decisions made based on the input data
}

// DecisionEntity represents a single decision made from a rule.
type DecisionEntity struct {
	RuleName string
	Variant  *string
	Decision Decision
}

//go:generate go run github.com/dmarkham/enumer -type=Decision -json -trimprefix=Decision
type Decision int

const (
	DecisionInvalid Decision = iota
	DecisionPending
	DecisionNone
	DecisionDecline
	DecisionFurtherReview
)
