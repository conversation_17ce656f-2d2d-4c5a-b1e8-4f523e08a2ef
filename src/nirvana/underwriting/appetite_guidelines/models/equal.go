package models

func (d *Decisions) Equal(other *Decisions) bool {
	// skips check on Id
	if d == other {
		return true
	}
	if d == nil || other == nil {
		return false
	}
	if d.RuleSetId != other.RuleSetId ||
		d.ApplicationReviewID != other.ApplicationReviewID ||
		d.Decision != other.Decision {
		return false
	}
	return d.OutputContext.Equal(other.OutputContext) && d.InputContext.Equal(other.InputContext)
}

// Equal compares two DecisionEntity objects for equality.
func (d *DecisionEntity) Equal(other *DecisionEntity) bool {
	if d == other {
		return true
	}
	if d == nil || other == nil {
		return false
	}

	// Compare Variant pointers safely
	if (d.Variant == nil) != (other.Variant == nil) {
		return false
	}
	if d.Variant != nil && other.Variant != nil && *d.Variant != *other.Variant {
		return false
	}

	return d.RuleName == other.RuleName && d.Decision == other.Decision
}

// Equal compares two InputFact objects for equality.
func (f *InputFact) Equal(other *InputFact) bool {
	if f == other {
		return true
	}
	if f == nil || other == nil {
		return false
	}

	return f.AlLossesBurnRate == other.AlLossesBurnRate &&
		f.ApdLossesBurnRate == other.ApdLossesBurnRate &&
		f.MtcLossesBurnRate == other.MtcLossesBurnRate &&
		f.TotalMonths == other.TotalMonths &&
		f.DotRating == other.DotRating
}

// Equal compares two OutputFact objects for equality.
func (f *OutputFact) Equal(other *OutputFact) bool {
	if f == other {
		return true
	}
	if f == nil || other == nil {
		return false
	}

	if len(f.Decisions) != len(other.Decisions) {
		return false
	}

	// Track used decisions to avoid duplicates
	used := make([]bool, len(other.Decisions))

	for _, d1 := range f.Decisions {
		found := false
		for j, d2 := range other.Decisions {
			if !used[j] && d1.Equal(&d2) {
				used[j] = true
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}
