load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "models",
    srcs = [
        "category_enumer.go",
        "decision_enumer.go",
        "decisions.go",
        "equal.go",
        "fact.go",
        "program_enumer.go",
        "rule_set.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/appetite_guidelines/models",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
    ],
)
