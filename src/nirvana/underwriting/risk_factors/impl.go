package risk_factors

import (
	"context"
	"database/sql"
	"sort"
	"strings"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cockroachdb/errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/google/uuid"
	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"google.golang.org/protobuf/types/known/timestamppb"
	coverage_enum "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/risk_factors"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/authz"

	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/emptypb"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
)

type Deps struct {
	fx.In
	Clk                            clock.Clock
	RiskFactorDataWrapper          risk_factors.RiskFactorWrapper
	RiskFactorWorksheetWrapper     risk_factors.RiskWorksheetWrapper
	RiskWorksheetRiskFactorWrapper risk_factors.WorksheetRiskFactorsWrapper
	RiskFactorPricingManager       PricingManager
	FeatureFlag                    feature_flag_lib.Client
	AuthWrapper                    auth.DataWrapper
	ApplicationReviewWrapper       uw.ApplicationReviewWrapper
	RiskFactorPricingWrapper       risk_factors.RiskPricingWrapper
}

type Impl struct {
	UnimplementedRiskFactorsServiceServer
	deps *Deps
}

func newImpl(deps Deps) *Impl {
	return &Impl{
		deps: &deps,
	}
}

func (impl *Impl) UpdateWorksheet(
	ctx context.Context,
	req *WorksheetUpdateRequest,
) (*emptypb.Empty, error) {
	log.Info(ctx, "UpdateWorksheetState", log.Any("request", req))
	worksheetID, err := uuid.Parse(req.GetWorksheetId())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid worksheet ID")
	}
	if err := impl.deps.RiskFactorWorksheetWrapper.UpdateRiskWorksheet(
		ctx, worksheetID,
		func(worksheet *risk_factors.RiskWorksheet) (*risk_factors.RiskWorksheet, error) {
			worksheet.Notes = pointer_utils.String(req.GetNotes())
			return worksheet, nil
		},
	); err != nil {
		log.Error(ctx, "Failed to update worksheet state", log.Err(err))
		return nil, status.Error(codes.Internal, "update failed")
	}
	return &emptypb.Empty{}, nil
}

func (impl *Impl) GetLatestWorksheetForReview(ctx context.Context,
	in *GetLatestWorksheetForReviewRequest,
) (*Worksheet, error) {
	log.Info(ctx, "GetLatestWorksheetForReview")
	// Get the latest worksheet for the given review id
	if in.GetReviewId() == "" {
		return nil, status.Error(codes.InvalidArgument, "review ID is required")
	}

	worksheet, err := impl.deps.RiskFactorWorksheetWrapper.GetLatestRiskWorksheetForAppReview(ctx, in.GetReviewId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &Worksheet{}, nil
		}
		log.Error(ctx, "Failed to get latest worksheet", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch latest worksheet")
	}
	return impl.riskWorksheetDetailsFromDb(ctx, worksheet)
}

func (impl *Impl) ListRiskFactors(ctx context.Context, _ *emptypb.Empty) (*ListRiskFactorsResponse, error) {
	log.Info(ctx, "GetRiskFactors")
	riskFactors, err := impl.deps.RiskFactorDataWrapper.GetAllRiskFactors(ctx)
	if err != nil {
		log.Error(ctx, "Failed to get risk factors", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch risk factors")
	}
	retval := make([]*RiskFactor, 0, len(riskFactors))
	for _, rf := range riskFactors {
		retval = append(retval, riskFactorFromDb(&rf))
	}
	return &ListRiskFactorsResponse{RiskFactors: retval}, nil
}

const (
	isSystemGeneratedTag = "system-generated"
)

func riskFactorFromDb(rf *risk_factors.RiskFactor) *RiskFactor {
	if rf == nil {
		return nil
	}

	// Check if "system-generated" tag is present in tags (case-insensitive)
	isSystemGenerated := false
	for _, tag := range rf.Tags {
		if strings.EqualFold(tag, isSystemGeneratedTag) {
			isSystemGenerated = true
			break
		}
	}

	retval := &RiskFactor{
		Id:                rf.Id.String(),
		Name:              rf.Name,
		Description:       rf.Description,
		Category:          categoryEnumFromDbToProto(rf.Category),
		PricingType:       pricingTypeEnumFromDbToProto(rf.PricingType),
		Version:           int32(rf.Version),
		State:             stateEnumFromDbToProto(rf.State),
		CreatedAt:         timestamppb.New(rf.CreatedAt),
		UpdatedAt:         timestamppb.New(rf.UpdatedAt),
		IsSystemGenerated: isSystemGenerated,
		Tags:              rf.Tags,
		CompositeName:     rf.CompositeName,
	}
	return retval
}

// getAssignedUnderwriter fetches the assigned underwriter for an application review
func (impl *Impl) getAssignedUnderwriter(ctx context.Context, reviewId string) (*authz.User, error) {
	// Get the application review
	reviewObj, err := impl.deps.ApplicationReviewWrapper.GetReview(ctx, reviewId)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "application review not found")
		}
		log.Error(ctx, "Failed to fetch application review", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch application review")
	}
	// Get the underwriter user
	underwriter, err := impl.deps.AuthWrapper.FetchAuthzUser(ctx, reviewObj.UnderwriterID)
	if err != nil {
		log.Error(ctx, "Failed to fetch underwriter user",
			log.String("reviewId", reviewId),
			log.String("underwriterId", reviewObj.UnderwriterID.String()),
			log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch underwriter user")
	}
	return underwriter, nil
}

// determineWorksheetVersion determines the worksheet version based on the feature flag for the underwriter.
func (impl *Impl) determineWorksheetVersion(ctx context.Context, reviewID string) (int, error) {
	worksheetVersion := 1 // Default to version 1

	// Get the assigned underwriter to check feature flag
	underwriter, err := impl.getAssignedUnderwriter(ctx, reviewID)
	if err != nil {
		log.Warn(ctx, "Unable to fetch underwriter, defaulting to V1 worksheet",
			log.String("reviewId", reviewID),
			log.Err(err))
		return worksheetVersion, nil
	}

	if underwriter == nil {
		return worksheetVersion, nil
	}

	// Check if this underwriter should get v2 pricing
	isV2Enabled, err := impl.deps.FeatureFlag.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*underwriter),
		feature_flag_lib.FeatureRiskFactorPricingV2,
		false,
	)
	if err != nil {
		log.Warn(ctx, "Failed to check feature flag, defaulting to V1 worksheet",
			log.String("underwriterId", underwriter.ID.String()),
			log.Err(err))
		return worksheetVersion, nil
	}

	if !isV2Enabled {
		return worksheetVersion, nil
	}

	worksheetVersion = 2
	log.Info(ctx, "Using V2 worksheet for underwriter",
		log.String("underwriterId", underwriter.ID.String()))
	return worksheetVersion, nil
}

// markExistingWorksheetAsStale marks an existing worksheet as stale if one exists for the given review ID.
func (impl *Impl) markExistingWorksheetAsStale(ctx context.Context, reviewID string) error {
	// Check for existing active worksheets for this application review
	existingWorksheet, err := impl.deps.RiskFactorWorksheetWrapper.GetLatestRiskWorksheetForAppReview(ctx, reviewID)
	if err != nil {
		if !errors.Is(err, sql.ErrNoRows) {
			log.Error(ctx, "Failed to check for existing worksheets", log.Err(err))
			return status.Error(codes.Internal, "failed to check for existing worksheets")
		}
		return nil
	}

	// If no existing worksheet is found, nothing to do
	if existingWorksheet == nil {
		return nil
	}

	log.Info(ctx, "Found existing worksheet, marking as stale",
		log.String("existing_worksheet_id", existingWorksheet.ID.String()))

	err = impl.deps.RiskFactorWorksheetWrapper.UpdateRiskWorksheet(
		ctx, existingWorksheet.ID,
		func(worksheet *risk_factors.RiskWorksheet) (*risk_factors.RiskWorksheet, error) {
			worksheet.State = risk_factors.RiskWorksheetStateStale
			worksheet.UpdatedAt = impl.deps.Clk.Now()
			return worksheet, nil
		},
	)
	if err != nil {
		log.Error(ctx, "Failed to mark existing worksheet as stale", log.Err(err))
		return status.Error(codes.Internal, "failed to mark existing worksheet as stale")
	}

	return nil
}

// TODO: The method should be called setup risk worksheet and pricing or something
// All of this should be inserted in a single transaction
func (impl *Impl) CreateWorksheet(ctx context.Context, in *CreateWorksheetRequest) (*CreateWorksheetResponse, error) {
	log.Info(ctx, "CreateWorksheet")

	if in.GetReviewId() == "" {
		return nil, status.Error(codes.InvalidArgument, "review ID is required")
	}

	// create a new worksheet with the given review id
	reviewId, err := uuid.Parse(in.GetReviewId())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid review ID format")
	}

	// Mark existing worksheet as stale if one exists
	err = impl.markExistingWorksheetAsStale(ctx, in.GetReviewId())
	if err != nil {
		return nil, err
	}

	// Determine worksheet version based on feature flag for the underwriter
	worksheetVersion, err := impl.determineWorksheetVersion(ctx, in.GetReviewId())
	if err != nil {
		log.Error(ctx, "Failed to determine worksheet version", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to determine worksheet version")
	}

	worksheetId := uuid.New()
	worksheet := risk_factors.RiskWorksheet{
		ID:                  worksheetId,
		ApplicationReviewID: reviewId,
		State:               risk_factors.RiskWorksheetStateActive,
		CreatedAt:           impl.deps.Clk.Now(),
		UpdatedAt:           impl.deps.Clk.Now(),
		Version:             worksheetVersion,
	}

	if err := impl.deps.RiskFactorWorksheetWrapper.InsertRiskWorksheet(ctx, worksheet); err != nil {
		log.Error(ctx, "Failed to create worksheet", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to create worksheet")
	}

	log.Info(ctx, "Created new worksheet",
		log.String("worksheet_id", worksheetId.String()),
		log.Int("version", worksheetVersion))

	// For v1 worksheets, create a pricing sheet during worksheet creation
	// For v2 worksheets, pricing details will be created per risk factor
	if worksheetVersion == 1 {
		if err := impl.deps.RiskFactorPricingManager.CreatePricingWorksheet(ctx, worksheetId.String(), worksheetVersion); err != nil {
			log.Error(ctx, "Failed to create pricing sheet", log.Err(err))
			return nil, err
		}
		log.Info(ctx, "Created pricing sheet for V1 worksheet", log.String("worksheet_id", worksheetId.String()))
	}

	return &CreateWorksheetResponse{WorksheetId: worksheetId.String()}, nil
}

func (impl *Impl) worksheetRiskFactorsFromDb(
	ctx context.Context,
	worksheet *risk_factors.RiskWorksheet,
	worksheetRiskFactors []risk_factors.RiskWorksheetRiskFactor,
	pricingData *PricingData,
) ([]*WorksheetRiskFactorResponse, error) {
	if worksheetRiskFactors == nil {
		return nil, nil
	}
	retval := make([]*WorksheetRiskFactorResponse, 0, len(worksheetRiskFactors))

	// For V2 pricing, create a map of factor ID to pricing data for efficient lookup
	factorPricingMap := make(map[string][]*CoveragePricingDetail)
	if worksheet != nil && worksheet.Version == PricingVersionV2 && pricingData != nil {
		// Extract risk factor pricing details from the pricing data
		for id, factorPricing := range pricingData.RiskFactorPricing {
			coveragePricingDetails := make([]*CoveragePricingDetail, 0, len(factorPricing))
			for coverage, credit := range factorPricing {
				coveragePricingDetails = append(coveragePricingDetails, &CoveragePricingDetail{
					Coverage: CoverageEnumFromDbToProto(coverage),
					Credit:   credit,
				})
			}
			factorPricingMap[id] = coveragePricingDetails
		}
	}

	for _, rf := range worksheetRiskFactors {
		val := &WorksheetRiskFactorResponse{
			WorksheetId:           rf.RiskWorksheetID.String(),
			WorksheetRiskFactorId: rf.ID.String(),
			RiskFactor:            nil,
			CreatedAt:             timestamppb.New(rf.CreatedAt),
			UpdatedAt:             timestamppb.New(rf.UpdatedAt),
			CreatedBy:             rf.CreatedBy.String(),
			LastUpdatedBy:         rf.LastUpdatedBy.String(),
			Sentiment:             sentimentTypeFromDbToProto(rf.Sentiment),
		}

		// For V2 worksheets, include pricing details for this risk factor from the map
		if worksheet != nil && worksheet.Version == PricingVersionV2 {
			if pricingDetails, ok := factorPricingMap[rf.ID.String()]; ok && len(pricingDetails) > 0 {
				val.CoveragePricingDetail = pricingDetails
			}
		}

		if rf.Notes != nil {
			val.Notes = *rf.Notes
		}
		if rf.Value != nil {
			val.Value = *rf.Value
		}
		// fetch the risk factor details
		riskFactor, err := impl.deps.RiskFactorDataWrapper.GetRiskFactor(ctx, rf.RiskFactorID.String())
		if err != nil {
			return nil, err
		}
		val.RiskFactor = riskFactorFromDb(riskFactor)
		// check if overrides are present
		if rf.Overrides != nil {
			val.RiskFactor.Category = categoryEnumFromDbToProto(rf.Overrides.Category)
			val.RiskFactor.Name = rf.Overrides.Label
		}
		retval = append(retval, val)
	}
	// sort risk factors alphabetically by category and name
	sort.Slice(retval, func(i, j int) bool {
		// First compare categories
		if retval[i].RiskFactor.Category != retval[j].RiskFactor.Category {
			return retval[i].RiskFactor.Category < retval[j].RiskFactor.Category
		}
		// If categories are the same, compare names
		return retval[i].RiskFactor.Name < retval[j].RiskFactor.Name
	})
	return retval, nil
}

// Update worksheetPricingDetailsFromDb to handle both v1 and v2 pricing data
func worksheetPricingDetailsFromDb(
	ctx context.Context,
	pricingData *PricingData,
) *WorksheetPricingDetails {
	if pricingData == nil {
		return nil
	}

	var categoryPricingDetails []*CategoryPricingDetails

	categoryPricingDetails = make([]*CategoryPricingDetails, 0, len(pricingData.CategoryPricing.CategoryAdjustments))
	for category, coverage := range pricingData.CategoryPricing.CategoryAdjustments {
		coveragePricingDetails := make([]*CoveragePricingDetail, 0, len(coverage))
		for cov, credit := range coverage {
			coveragePricingDetails = append(coveragePricingDetails, &CoveragePricingDetail{
				Coverage: CoverageEnumFromDbToProto(cov),
				Credit:   credit,
			})
		}
		categoryPricingDetails = append(categoryPricingDetails, &CategoryPricingDetails{
			Category:              categoryEnumFromDbToProto(category),
			CoveragePricingDetail: coveragePricingDetails,
		})
	}

	// We can now use the timestamp and last updated by fields from PricingData
	return &WorksheetPricingDetails{
		WorksheetId:            pricingData.WorksheetID,
		WorksheetPricingId:     pricingData.WorksheetID,
		CreatedAt:              timestamppb.New(pricingData.CreatedAt),
		UpdatedAt:              timestamppb.New(pricingData.UpdatedAt),
		LastUpdatedBy:          pricingData.LastUpdatedBy,
		CategoryPricingDetails: categoryPricingDetails,
	}
}

func (impl *Impl) worksheetFromDb(
	ctx context.Context,
	dbWorksheet *risk_factors.RiskWorksheet,
	worksheetRiskFactors []risk_factors.RiskWorksheetRiskFactor,
	pricingData *PricingData,
) (*Worksheet, error) {
	factors, err := impl.worksheetRiskFactorsFromDb(ctx, dbWorksheet, worksheetRiskFactors, pricingData)
	if err != nil {
		return nil, err
	}
	retval := Worksheet{
		Id:                   dbWorksheet.ID.String(),
		ReviewId:             dbWorksheet.ApplicationReviewID.String(),
		WorksheetRiskFactors: factors,
		CreatedAt:            timestamppb.New(dbWorksheet.CreatedAt),
		UpdatedAt:            timestamppb.New(dbWorksheet.UpdatedAt),
		LastUpdatedBy:        dbWorksheet.LastUpdatedBy.String(),
		State:                worksheetStateEnumFromDbToProto(dbWorksheet.State),
		Version:              int32(dbWorksheet.Version),
	}

	// Only include pricing details if we have pricing data
	if pricingData != nil {
		retval.PricingDetails = worksheetPricingDetailsFromDb(ctx, pricingData)
	}

	if dbWorksheet.Notes != nil {
		retval.Notes = *dbWorksheet.Notes
	}
	return &retval, nil
}

func (impl *Impl) GetWorksheet(ctx context.Context, in *GetWorksheetRequest) (*Worksheet, error) {
	log.Info(ctx, "GetWorksheet", log.Any("request", in))

	if in.GetWorksheetId() == "" {
		return nil, status.Error(codes.InvalidArgument, "worksheet ID is required")
	}

	worksheet, err := impl.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(ctx, in.GetWorksheetId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "worksheet not found")
		}
		log.Error(ctx, "Failed to get worksheet", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch worksheet")
	}

	log.Info(ctx, "Got worksheet", log.Any("worksheet", worksheet))
	return impl.riskWorksheetDetailsFromDb(ctx, worksheet)
}

func (impl *Impl) riskWorksheetDetailsFromDb(ctx context.Context, worksheet *risk_factors.RiskWorksheet) (*Worksheet, error) {
	// Get pricing data using the pricing manager
	pricingData, err := impl.deps.RiskFactorPricingManager.GetPricingWorksheet(ctx, worksheet.ID.String())
	if err != nil {
		if errors.Is(err, ErrWorksheetPricingNotFound) {
			log.Info(ctx, "No pricing data found for worksheet", log.String("worksheetId", worksheet.ID.String()))
			pricingData = nil
		} else {
			log.Error(ctx, "Failed to get pricing data", log.Err(err))
			return nil, status.Error(codes.Internal, "failed to fetch pricing data")
		}
	}
	log.Info(ctx, "pricing data", log.Any("pricing data", pricingData))
	// Get all risk factors for this worksheet
	worksheetRiskFactors, err := impl.deps.RiskWorksheetRiskFactorWrapper.GetWorksheetRiskFactors(ctx, worksheet.ID.String())
	if err != nil {
		log.Error(ctx, "Failed to get worksheet risk factors", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch worksheet risk factors")
	}

	return impl.worksheetFromDb(ctx, worksheet, worksheetRiskFactors, pricingData)
}

// addRiskFactorToWorksheet is a helper function to add a risk factor to a worksheet, handling both standard and suggested risk factors.
// It encapsulates common logic for validation, insertion, and V2 pricing creation.
func (impl *Impl) addRiskFactorToWorksheet(ctx context.Context, worksheetID, riskFactorID, value, notes string, sentiment Sentiment, state risk_factors.WorksheetRiskFactorState, overrides *risk_factors.Overrides) (*risk_factors.RiskWorksheetRiskFactor, error) {
	wID, err := uuid.Parse(worksheetID)
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid worksheet ID")
	}

	rfID, err := uuid.Parse(riskFactorID)
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid risk factor ID")
	}

	// Check worksheet version before adding the risk factor
	worksheet, err := impl.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(ctx, worksheetID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "worksheet not found")
		}
		log.Error(ctx, "Failed to get worksheet", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch worksheet")
	}
	id := uuid.New()
	newRiskFactor := risk_factors.RiskWorksheetRiskFactor{
		ID:              id,
		RiskWorksheetID: wID,
		RiskFactorID:    rfID,
		State:           state,
		CreatedAt:       impl.deps.Clk.Now(),
		UpdatedAt:       impl.deps.Clk.Now(),
		Value:           pointer_utils.String(value),
		Sentiment:       sentimentFromProtoToDb(sentiment),
	}

	if notes != "" {
		newRiskFactor.Notes = pointer_utils.String(notes)
	}

	if overrides != nil {
		newRiskFactor.Overrides = overrides
	}

	if err := impl.deps.RiskWorksheetRiskFactorWrapper.InsertRiskWorksheetRiskFactor(ctx, newRiskFactor); err != nil {
		log.Error(ctx, "Failed to add worksheet risk factor", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to add risk factor")
	}

	// For V2 worksheets, create individual pricing details for this risk factor
	if worksheet.Version == 2 {
		// TODO: call pricing manager method for this
		pricingSheet := risk_factors.NewRiskWorksheetPricing(wID, 2)
		pricingSheet.WorksheetFactorID = &id
		pricingSheet.PricingAdjustmentsV2 = &risk_factors.PricingAdjustmentsV2{
			CoverageAdjustments: make(map[coverage_enum.Coverage]int32),
		}

		if err := impl.deps.RiskFactorPricingWrapper.InsertRiskPricing(ctx, *pricingSheet); err != nil {
			log.Error(ctx, "Failed to create initial pricing for risk factor in V2 worksheet",
				log.String("worksheetId", worksheetID),
				log.String("riskFactorId", riskFactorID),
				log.Err(err))
			return nil, status.Error(codes.Internal, "failed to create initial pricing for risk factor")
		}
	}

	// Fetch created entity to return full response
	created, err := impl.deps.RiskWorksheetRiskFactorWrapper.GetRiskWorksheetRiskFactor(ctx, newRiskFactor.ID.String())
	if err != nil {
		return nil, status.Error(codes.NotFound, "failed to fetch created risk factor")
	}

	return created, nil
}

func (impl *Impl) AddWorksheetRiskFactor(ctx context.Context, req *AddWorksheetRiskFactorRequest) (*WorksheetRiskFactorResponse, error) {
	log.Info(ctx, "AddWorksheetRiskFactor", log.Any("request", req))

	// Validate required fields
	if req.GetValue() == "" {
		if req.GetSentiment() == Sentiment_SENTIMENT_UNSPECIFIED {
			return nil, status.Error(codes.InvalidArgument, "value and sentiment are required")
		}
		return nil, status.Error(codes.InvalidArgument, "value is required")
	}

	if req.GetSentiment() != Sentiment_POSITIVE && req.GetSentiment() != Sentiment_NEGATIVE && req.GetSentiment() != Sentiment_NEUTRAL {
		return nil, status.Error(codes.InvalidArgument, "sentiment must be either POSITIVE, NEGATIVE, or NEUTRAL")
	}

	created, err := impl.addRiskFactorToWorksheet(
		ctx,
		req.GetWorksheetId(),
		req.GetRiskFactorId(),
		req.GetValue(),
		req.GetNotes(),
		req.GetSentiment(),
		risk_factors.WorksheetRiskFactorStateActive,
		nil,
	)
	if err != nil {
		return nil, err
	}

	return impl.worksheetRiskFactorToProto(ctx, created)
}

func (impl *Impl) UpdateWorksheetRiskFactor(
	ctx context.Context,
	req *UpdateWorksheetRiskFactorRequest,
) (*WorksheetRiskFactorResponse, error) {
	log.Info(ctx, "UpdateWorksheetRiskFactor", log.Any("request", req))

	riskFactorID, err := uuid.Parse(req.GetWorksheetRiskFactorId())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid risk factor ID")
	}

	var updated *risk_factors.RiskWorksheetRiskFactor
	err = impl.deps.RiskWorksheetRiskFactorWrapper.UpdateRiskWorksheetRiskFactor(
		ctx, riskFactorID,
		func(rf *risk_factors.RiskWorksheetRiskFactor) (*risk_factors.RiskWorksheetRiskFactor, error) {
			if req.GetValue() != "" {
				rf.Value = pointer_utils.String(req.GetValue())
			}
			if req.GetNotes() != "" {
				rf.Notes = pointer_utils.String(req.GetNotes())
			}
			if req.GetSentiment() != Sentiment_SENTIMENT_UNSPECIFIED {
				rf.Sentiment = sentimentFromProtoToDb(req.GetSentiment())
			}
			updated = rf
			return rf, nil
		},
	)
	if err != nil {
		log.Error(ctx, "Failed to update worksheet risk factor", log.Err(err))
		return nil, status.Error(codes.Internal, "update failed")
	}

	return impl.worksheetRiskFactorToProto(ctx, updated)
}

func (impl *Impl) DeleteWorksheetRiskFactor(
	ctx context.Context,
	req *DeleteRiskFactorRequest,
) (*emptypb.Empty, error) {
	log.Info(ctx, "DeleteWorksheetRiskFactor", log.Any("request", req))

	riskFactorID, err := uuid.Parse(req.GetWorksheetRiskFactorId())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid risk factor ID")
	}

	if err := impl.deps.RiskWorksheetRiskFactorWrapper.UpdateRiskWorksheetRiskFactor(
		ctx, riskFactorID, func(worksheetRiskFactor *risk_factors.RiskWorksheetRiskFactor,
		) (*risk_factors.RiskWorksheetRiskFactor, error) {
			worksheetRiskFactor.State = risk_factors.WorksheetRiskFactorStateArchived
			return worksheetRiskFactor, nil
		}); err != nil {
		log.Error(ctx, "Failed to delete worksheet risk factor", log.Err(err))
		return nil, status.Error(codes.Internal, "deletion failed")
	}

	return &emptypb.Empty{}, nil
}

func (impl *Impl) DeleteRiskFactorByLabel(
	ctx context.Context,
	req *DeleteRiskFactorByLabelRequest,
) (*emptypb.Empty, error) {
	log.Info(ctx, "DeleteRiskFactorByLabel", log.Any("request", req))

	// Validate input
	if req.GetReviewId() == "" {
		return nil, status.Error(codes.InvalidArgument, "review ID is required")
	}
	if req.GetRiskFactorLabel() == RiskFactorLabel_UNSPECIFIED_RISK_FACTOR_LABEL {
		return nil, status.Error(codes.InvalidArgument, "risk factor label is required")
	}

	// Get the latest worksheet for the review
	worksheet, err := impl.deps.RiskFactorWorksheetWrapper.GetLatestRiskWorksheetForAppReview(ctx, req.GetReviewId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "no worksheet found for the given review")
		}
		log.Error(ctx, "Failed to get latest worksheet", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch latest worksheet")
	}

	// Convert RiskFactorLabel to label string
	label, ok := riskFactorLabelProtoToDb[req.GetRiskFactorLabel()]
	if !ok {
		return nil, status.Error(codes.InvalidArgument, "invalid risk factor label")
	}

	// Get the risk factor by label
	riskFactor, err := impl.deps.RiskFactorDataWrapper.GetRiskFactorByLabel(ctx, label)
	if err != nil {
		if errors.Is(err, risk_factors.ErrRiskFactorNotFound) {
			return nil, status.Error(codes.NotFound, "risk factor not found for the given label")
		}
		log.Error(ctx, "Failed to get risk factor by label", log.String("label", label), log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch risk factor")
	}

	// Find the worksheet risk factor
	worksheetRiskFactor, err := impl.deps.RiskWorksheetRiskFactorWrapper.GetWorksheetFactorFromRiskFactorId(
		ctx, worksheet.ID.String(), riskFactor.Id.String())
	if err != nil {
		if errors.Is(err, risk_factors.ErrWorksheetRiskFactorNotFound) {
			return nil, status.Error(codes.NotFound, "risk factor not found in worksheet")
		}
		log.Error(ctx, "Failed to get worksheet risk factor", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch worksheet risk factor")
	}

	_, err = impl.DeleteWorksheetRiskFactor(ctx, &DeleteRiskFactorRequest{
		WorksheetRiskFactorId: worksheetRiskFactor.ID.String(),
	})
	if err != nil {
		log.Error(ctx, "Failed to delete worksheet risk factor",
			log.String("worksheet_risk_factor_id", worksheetRiskFactor.ID.String()),
			log.Err(err))
		return nil, status.Error(codes.Internal, "failed to delete risk factor")
	}

	log.Info(ctx, "Successfully deleted risk factor",
		log.String("review_id", req.GetReviewId()),
		log.String("risk_factor_label", label),
		log.String("worksheet_risk_factor_id", worksheetRiskFactor.ID.String()))

	return &emptypb.Empty{}, nil
}

func (impl *Impl) UpdateWorksheetPricing(
	ctx context.Context,
	req *UpdateWorksheetPricingRequest,
) (*emptypb.Empty, error) {
	log.Info(ctx, "UpdateWorksheetPricing", log.Any("request", req))
	_, err := uuid.Parse(req.GetWorksheetId())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, "invalid worksheet ID")
	}
	// Use the PricingManager to update pricing
	_, err = impl.deps.RiskFactorPricingManager.UpdatePricing(ctx, req)
	if err != nil {
		log.Error(ctx, "Failed to update worksheet pricing", log.Err(err))
		return nil, err // The pricing manager already wraps the error in the appropriate gRPC status
	}

	return &emptypb.Empty{}, nil
}

var SuggestedFactorId = uuid.MustParse("d4f35a40-74ec-4e42-a67e-2c2519dee8c0")

func (impl *Impl) SuggestRiskFactor(ctx context.Context, req *SuggestRiskFactorRequest) (*emptypb.Empty, error) {
	log.Info(ctx, "SuggestRiskFactor", log.Any("request", req))

	// Validate required fields
	if req.GetValue() == "" {
		if req.GetSentiment() == Sentiment_SENTIMENT_UNSPECIFIED {
			return nil, status.Error(codes.InvalidArgument, "value and sentiment are required")
		}
		return nil, status.Error(codes.InvalidArgument, "value is required")
	}

	if req.GetSentiment() != Sentiment_POSITIVE && req.GetSentiment() != Sentiment_NEGATIVE && req.GetSentiment() != Sentiment_NEUTRAL {
		return nil, status.Error(codes.InvalidArgument, "sentiment must be either POSITIVE, NEGATIVE, or NEUTRAL")
	}

	overrides := &risk_factors.Overrides{
		Label:    req.GetName(),
		Category: CategoryEnumFromProtoToDb(req.GetCategory()),
	}

	_, err := impl.addRiskFactorToWorksheet(
		ctx,
		req.GetWorksheetId(),
		SuggestedFactorId.String(),
		req.GetValue(),
		req.GetNotes(),
		req.GetSentiment(),
		risk_factors.WorksheetRiskFactorStateDraft,
		overrides,
	)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// Helper to convert DB model to protobuf response
func (impl *Impl) worksheetRiskFactorToProto(
	ctx context.Context,
	dbModel *risk_factors.RiskWorksheetRiskFactor,
) (*WorksheetRiskFactorResponse, error) {
	riskFactor, err := impl.deps.RiskFactorDataWrapper.GetRiskFactor(
		ctx, dbModel.RiskFactorID.String(),
	)
	if err != nil {
		log.Error(ctx, "Failed to fetch risk factor details",
			log.String("risk_factor_id", dbModel.RiskFactorID.String()))
		return nil, status.Error(codes.Internal, "failed to fetch risk factor details")
	}

	resp := &WorksheetRiskFactorResponse{
		WorksheetId:           dbModel.RiskWorksheetID.String(),
		WorksheetRiskFactorId: dbModel.ID.String(),
		RiskFactor:            riskFactorFromDb(riskFactor),
		CreatedAt:             timestamppb.New(dbModel.CreatedAt),
		UpdatedAt:             timestamppb.New(dbModel.UpdatedAt),
		CreatedBy:             dbModel.CreatedBy.String(),
		LastUpdatedBy:         dbModel.LastUpdatedBy.String(),
		Sentiment:             sentimentTypeFromDbToProto(dbModel.Sentiment),
	}
	if dbModel.Notes != nil {
		resp.Notes = *dbModel.Notes
	}
	if dbModel.Value != nil {
		resp.Value = *dbModel.Value
	}
	// Get the worksheet first to determine if it's using V2 pricing
	worksheet, err := impl.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(ctx, dbModel.RiskWorksheetID.String())
	if err != nil {
		log.Error(ctx, "Failed to fetch worksheet details",
			log.String("worksheet_id", dbModel.RiskWorksheetID.String()),
			log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch worksheet details")
	}
	if worksheet.Version != PricingVersionV2 {
		return resp, nil
	}
	// In V2 pricing, fetch pricing data for this risk factor ID
	pricing, err := impl.deps.RiskFactorPricingWrapper.GetPricingWorksheetForRiskFactorId(ctx, dbModel.ID.String())
	if err != nil {
		log.Error(ctx, "Failed to fetch pricing details",
			log.String("risk_factor_id", dbModel.ID.String()),
			log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch pricing details")
	}

	// Convert pricing data to protobuf format
	resp.CoveragePricingDetail = make([]*CoveragePricingDetail, 0, len(pricing.PricingAdjustmentsV2.CoverageAdjustments))
	for coverage, credit := range pricing.PricingAdjustmentsV2.CoverageAdjustments {
		resp.CoveragePricingDetail = append(resp.CoveragePricingDetail, &CoveragePricingDetail{
			Coverage: CoverageEnumFromDbToProto(coverage),
			Credit:   credit,
		})
	}
	return resp, nil
}

var riskFactorLabelProtoToDb = map[RiskFactorLabel]string{
	RiskFactorLabel_UNSPECIFIED_RISK_FACTOR_LABEL:              "unspecified",
	RiskFactorLabel_Telematics_Risk_Score:                      "Telematics Risk Score",
	RiskFactorLabel_M1_Score:                                   "M1 Score",
	RiskFactorLabel_Utilization_Adjustment:                     "Utilization Adjustment",
	RiskFactorLabel_Vin_Visibility_Adjustment:                  "Vin Visibility Adjustment",
	RiskFactorLabel_Fleet_Size_Adjustment:                      "Fleet Size Adjustment",
	RiskFactorLabel_Primary_Operation_Class_Adjustment:         "Primary Operation Class Adjustment",
	RiskFactorLabel_Hazard_Zones_Distance_Adjustment:           "Hazard Zones Distance Adjustment",
	RiskFactorLabel_BASIC_Alert_Count_Adjustment:               "BASIC Alert Count Adjustment",
	RiskFactorLabel_Driver_OOS_Adjustment:                      "Driver OOS Adjustment",
	RiskFactorLabel_Overall_OOS_Adjustment:                     "Overall OOS Adjustment",
	RiskFactorLabel_BASIC_Unsafe_Driving_Score_Adjustment:      "Unsafe Driving BASIC Score Adjustment",
	RiskFactorLabel_Driver_Turnover_Adjustment:                 "Driver Turnover Adjustment",
	RiskFactorLabel_BASIC_Vehicle_Maintenance_Score_Adjustment: "Vehicle Maintenance BASIC Score Adjustment",
	RiskFactorLabel_BASIC_HOS_Compliance_Score_Adjustment:      "HOS Compliance BASIC Score Adjustment",
}

// InsertOrUpdateRiskFactor provides a general method to handle inserting or updating risk factors in a worksheet.
// This method will check if the risk factor already exists, and either create a new one or update the existing one.
// It also handles pricing adjustments for the risk factor.
//
// Parameters:
// - ctx: The context for the operation
// - req: The InsertOrUpdateRiskFactorRequest containing all details needed for the operation
//
// Returns:
// - Empty response on success
// - Any error that occurred during the operation
func (impl *Impl) InsertOrUpdateRiskFactor(
	ctx context.Context,
	req *InsertOrUpdateRiskFactorRequest,
) (*emptypb.Empty, error) {
	// Validate input parameters
	if err := validateRiskFactorRequest(req); err != nil {
		return nil, err
	}

	// Get latest worksheet and risk factor information
	worksheetResp, riskFactor, err := impl.getWorksheetAndRiskFactor(ctx, req)
	if err != nil {
		return nil, err
	}

	worksheetID := worksheetResp.ID.String()
	category := categoryEnumFromDbToProto(riskFactor.Category)

	// Check if risk factor already exists in worksheet
	existingFactorID, err := impl.findExistingRiskFactor(ctx, worksheetID, riskFactor.Id.String())
	if err != nil {
		return nil, err
	}

	// Process the risk factor (add new or update existing)
	if existingFactorID == "" {
		return impl.addNewRiskFactorWithPricing(ctx, worksheetResp, worksheetID, riskFactor, req, category)
	}

	return impl.updateExistingRiskFactorWithPricing(ctx, worksheetResp, worksheetID, existingFactorID, req, category)
}

// validateRiskFactorRequest validates the input parameters for InsertOrUpdateRiskFactor
func validateRiskFactorRequest(req *InsertOrUpdateRiskFactorRequest) error {
	if req.ReviewId == "" {
		return status.Error(codes.InvalidArgument, "review ID is required")
	}
	if req.RiskFactorLabel == RiskFactorLabel_UNSPECIFIED_RISK_FACTOR_LABEL {
		return status.Error(codes.InvalidArgument, "risk factor label is required")
	}
	return nil
}

// getWorksheetAndRiskFactor fetches the worksheet and risk factor based on the request
func (impl *Impl) getWorksheetAndRiskFactor(
	ctx context.Context,
	req *InsertOrUpdateRiskFactorRequest,
) (*risk_factors.RiskWorksheet, *risk_factors.RiskFactor, error) {
	// Get the worksheet
	worksheetResp, err := impl.deps.RiskFactorWorksheetWrapper.GetLatestRiskWorksheetForAppReview(ctx, req.ReviewId)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil, status.Error(codes.NotFound, "worksheet not found")
		}
		log.Error(ctx, "Failed to get worksheet", log.Err(err))
		return nil, nil, status.Error(codes.Internal, "failed to fetch worksheet")
	}

	// Fetch risk factor for the given label
	label := riskFactorLabelProtoToDb[req.RiskFactorLabel]
	riskFactor, err := impl.deps.RiskFactorDataWrapper.GetRiskFactorByLabel(ctx, label)
	if err != nil {
		log.Error(ctx, "Failed to get risk factor by label",
			log.String("label", label),
			log.Err(err))
		return nil, nil, status.Error(codes.Internal, "failed to fetch risk factor by label")
	}

	return worksheetResp, riskFactor, nil
}

// findExistingRiskFactor checks if risk factor already exists in worksheet
func (impl *Impl) findExistingRiskFactor(
	ctx context.Context,
	worksheetID string,
	riskFactorID string,
) (string, error) {
	existingFactor, err := impl.deps.RiskWorksheetRiskFactorWrapper.GetWorksheetFactorFromRiskFactorId(
		ctx, worksheetID, riskFactorID)
	if err != nil {
		if errors.Is(err, risk_factors.ErrWorksheetRiskFactorNotFound) {
			log.Info(ctx, "No existing risk factor found",
				log.String("worksheet_id", worksheetID),
				log.String("risk_factor_id", riskFactorID))
			return "", nil
		}
		log.Error(ctx, "Failed to get existing risk factor", log.Err(err))
		return "", status.Error(codes.Internal, "failed to fetch existing risk factor")
	}

	return existingFactor.ID.String(), nil
}

// addNewRiskFactorWithPricing adds a new risk factor and applies pricing
func (impl *Impl) addNewRiskFactorWithPricing(
	ctx context.Context,
	worksheetResp *risk_factors.RiskWorksheet,
	worksheetID string,
	riskFactor *risk_factors.RiskFactor,
	req *InsertOrUpdateRiskFactorRequest,
	category Category,
) (*emptypb.Empty, error) {
	// Create new risk factor
	addRequest := &AddWorksheetRiskFactorRequest{
		WorksheetId:  worksheetID,
		RiskFactorId: riskFactor.Id.String(),
		Value:        req.Value,
		Notes:        req.Notes,
		Sentiment:    req.Sentiment,
	}

	result, err := impl.AddWorksheetRiskFactor(ctx, addRequest)
	if err != nil {
		log.Error(ctx, "Failed to add risk factor", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to add risk factor")
	}

	// Skip pricing if no coverage pricing details provided
	if len(req.CoveragePricingDetail) == 0 {
		return &emptypb.Empty{}, nil
	}

	// Apply appropriate pricing based on worksheet version
	isV2Worksheet := worksheetResp.Version == PricingVersionV2
	if isV2Worksheet {
		return impl.applyV2Pricing(ctx, worksheetID, result.WorksheetRiskFactorId, req.CoveragePricingDetail)
	}

	return impl.applyV1Pricing(ctx, worksheetID, category, req.CoveragePricingDetail)
}

// updateExistingRiskFactorWithPricing updates existing risk factor and applies pricing
func (impl *Impl) updateExistingRiskFactorWithPricing(
	ctx context.Context,
	worksheetResp *risk_factors.RiskWorksheet,
	worksheetID string,
	existingFactorID string,
	req *InsertOrUpdateRiskFactorRequest,
	category Category,
) (*emptypb.Empty, error) {
	// Build update request with only non-empty fields
	updateRequest := &UpdateWorksheetRiskFactorRequest{
		WorksheetRiskFactorId: existingFactorID,
		Sentiment:             req.Sentiment,
	}

	if req.Value != "" {
		updateRequest.Value = req.Value
	}
	if req.Notes != "" {
		updateRequest.Notes = req.Notes
	}

	_, err := impl.UpdateWorksheetRiskFactor(ctx, updateRequest)
	if err != nil {
		log.Error(ctx, "Failed to update risk factor", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to update risk factor")
	}

	log.Info(ctx, "Risk factor updated successfully",
		log.String("worksheet_id", worksheetID),
		log.String("factor_id", existingFactorID))

	// Skip pricing if no coverage pricing details provided
	if len(req.CoveragePricingDetail) == 0 {
		return &emptypb.Empty{}, nil
	}

	// Apply appropriate pricing based on worksheet version
	isV2Worksheet := worksheetResp.Version == PricingVersionV2
	if isV2Worksheet {
		return impl.applyV2Pricing(ctx, worksheetID, existingFactorID, req.CoveragePricingDetail)
	}

	return impl.applyV1Pricing(ctx, worksheetID, category, req.CoveragePricingDetail)
}

// applyV2Pricing applies V2 pricing model (per-factor pricing)
func (impl *Impl) applyV2Pricing(
	ctx context.Context,
	worksheetID string,
	factorID string,
	coveragePricingDetail []*CoveragePricingDetail,
) (*emptypb.Empty, error) {
	pricingRequest := &UpdateWorksheetPricingRequest{
		WorksheetId:           worksheetID,
		WorksheetFactorId:     factorID,
		CoveragePricingDetail: coveragePricingDetail,
	}

	_, err := impl.deps.RiskFactorPricingManager.UpdatePricing(ctx, pricingRequest)
	if err != nil {
		log.Error(ctx, "Failed to update V2 pricing",
			log.String("worksheet_id", worksheetID),
			log.String("factor_id", factorID),
			log.Err(err))
		return nil, status.Error(codes.Internal, "failed to update pricing details")
	}

	return &emptypb.Empty{}, nil
}

// applyV1Pricing applies V1 pricing model (category-level pricing)
func (impl *Impl) applyV1Pricing(
	ctx context.Context,
	worksheetID string,
	category Category,
	coveragePricingDetail []*CoveragePricingDetail,
) (*emptypb.Empty, error) {
	pricingRequest := &UpdateWorksheetPricingRequest{
		WorksheetId: worksheetID,
		CategoryPricingDetails: &CategoryPricingDetails{
			Category:              category,
			CoveragePricingDetail: coveragePricingDetail,
		},
	}

	_, err := impl.deps.RiskFactorPricingManager.UpdatePricing(ctx, pricingRequest)
	if err != nil {
		log.Error(ctx, "Failed to update V1 pricing",
			log.String("worksheet_id", worksheetID),
			log.String("category", category.String()),
			log.Err(err))
		return nil, status.Error(codes.Internal, "failed to update pricing details")
	}

	return &emptypb.Empty{}, nil
}

func (impl *Impl) GetAllWorksheetFactorWithTag(
	ctx context.Context,
	req *GetAllWorksheetFactorWithTagRequest,
) (*GetAllWorksheetFactorWithTagResponse, error) {
	log.Info(ctx, "GetAllWorksheetFactorWithTag", log.Any("request", req))

	if req == nil {
		return nil, status.Error(codes.InvalidArgument, "request cannot be nil")
	}
	if req.WorksheetId == "" {
		return nil, status.Error(codes.InvalidArgument, "worksheet ID is required")
	}

	// Verify worksheet exists
	if _, err := impl.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(ctx, req.WorksheetId); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.Info(ctx, "Worksheet not found", log.String("worksheet_id", req.WorksheetId))
			return nil, status.Error(codes.NotFound, "worksheet not found")
		}
		log.Error(ctx, "Failed to get worksheet", log.String("worksheet_id", req.WorksheetId), log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch worksheet")
	}

	// Fetch worksheet risk factors filtered by tags (DB wrapper handles tag lookup)
	worksheetRiskFactors, err := impl.deps.RiskWorksheetRiskFactorWrapper.GetWorksheetRiskFactorsByTags(ctx, req.WorksheetId, req.Tags)
	if err != nil {
		log.Error(ctx, "Failed to get worksheet risk factors by tags", log.Err(err))
		return nil, status.Error(codes.Internal, "failed to fetch worksheet risk factors")
	}

	// Convert to proto format
	filteredFactors := make([]*WorksheetRiskFactorResponse, 0, len(worksheetRiskFactors))
	for _, factor := range worksheetRiskFactors {
		protoFactor, err := impl.worksheetRiskFactorToProto(ctx, &factor)
		if err != nil {
			log.Error(ctx, "Failed to convert factor to proto", log.String("factor_id", factor.ID.String()), log.Err(err))
			return nil, status.Error(codes.Internal, "failed to convert risk factors")
		}
		filteredFactors = append(filteredFactors, protoFactor)
	}

	log.Info(ctx, "GetAllWorksheetFactorWithTag completed",
		log.String("worksheet_id", req.WorksheetId),
		log.Int("filtered_factors", len(filteredFactors)),
		log.Any("tags", req.Tags))

	return &GetAllWorksheetFactorWithTagResponse{
		WorksheetRiskFactors: filteredFactors,
	}, nil
}
