package risk_factors

import (
	"context"
	"testing"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/google/uuid"
	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/protobuf/types/known/emptypb"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/risk_factors"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin/testing/testfixtures"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/basic_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/emailer_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/feature_store_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/lni_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func TestRiskFactorSuite(t *testing.T) {
	suite.Run(t, new(riskFactorTestSuite))
}

type deps struct {
	fx.In
	Client                     RiskFactorsServiceClient
	RiskFactorWrapper          risk_factors.RiskFactorWrapper
	RiskFactorWorksheetWrapper risk_factors.RiskWorksheetWrapper
	FeatureFlag                *feature_flag_lib.MockClient
}

type riskFactorTestSuite struct {
	suite.Suite
	suiteCtx          context.Context
	testApp           *fxtest.App
	appReviewId       string
	deps              deps
	riskFactorIds     []string
	suggestedFactorId string
}

func (s *riskFactorTestSuite) SetupSuite() {
	s.suiteCtx = context.Background()
	var env struct {
		fx.In
		*emailer_fixture.NoopEmailerFixture
		*fmcsa_fixture.FmcsaFixture
		*feature_store_fixture.FeatureStoreFixture
		*basic_fixture.BasicFixture
		*testfixtures.NhtsaToIsoMappingV1Fixture
		*lni_fixture.VersionFixture
		*lni_fixture.ActiveOrPendingInsuranceFixture
		*application_review_fixture.ApplicationReviewsFixture
		FeatureFlagClient *feature_flag_lib.MockClient
		Deps              deps
	}
	s.testApp = testloader.RequireStart(s.T(), &env)
	s.appReviewId = env.ApplicationReview.Id
	s.deps = env.Deps

	riskFactorIds, err := s.setupRiskFactors()
	s.Require().NoError(err)
	s.Require().NotEmpty(riskFactorIds)
	s.riskFactorIds = riskFactorIds
	suggestedFactorID, err := s.setupSuggestedRiskFactor()
	s.Require().NoError(err)
	s.Require().NotNil(suggestedFactorID)
	s.suggestedFactorId = *suggestedFactorID
}

func (s *riskFactorTestSuite) TearDownSuite() {
	s.testApp.RequireStop()
}

// setupRiskWorksheet creates a risk worksheet and returns the ID of the worksheet
// It is responsible for also creating the pricing sheet
func (s *riskFactorTestSuite) setupRiskWorksheet() (*string, error) {
	appReview := s.appReviewId
	// create risk worksheet and pricing sheet
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: appReview,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp)
	return &resp.WorksheetId, nil
}

// setup risk factors and return their IDs
func (s *riskFactorTestSuite) setupRiskFactors() ([]string, error) {
	riskFactors := []risk_factors.RiskFactor{
		{
			Name:          "Driver Age",
			Label:         "Driver Age",
			Category:      risk_factors.RiskFactorsCategoryDrivers,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"driver", "age", "demographics"},
		},
		{
			Name:          "Equipment Age",
			Label:         "Equipment Age",
			Category:      risk_factors.RiskFactorsCategoryEquipments,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"equipment", "age", "maintenance"},
		},
		{
			Name:          "Company Financials",
			Label:         "Company Financials",
			Category:      risk_factors.RiskFactorsCategoryFinancials,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"financial", "company", "credit"},
		},
		{
			Name:          "Large Losses",
			Label:         "Large Loses",
			Category:      risk_factors.RiskFactorsCategoryLosses,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"losses", "claims", "large"},
		},
		{
			Name:          "Years in Business",
			Label:         "Years in Business",
			Category:      risk_factors.RiskFactorsCategoryOperations,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"operations", "experience", "business"},
		},
		{
			Name:          "Driver Turnover",
			Label:         "Driver Turnover",
			Category:      risk_factors.RiskFactorsCategorySafety,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"safety", "driver", "turnover"},
		},
		{
			Name:          "Telematics Risk Score",
			Label:         "Telematics Risk Score",
			Category:      risk_factors.RiskFactorsCategorySafety,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"safety", "telematics", "score", "system-generated"},
		},
		{
			Name:          "M1 Score",
			Label:         "M1 Score",
			Category:      risk_factors.RiskFactorsCategorySafety,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"safety", "score", "m1", "system-generated"},
		},
		{
			Name:          "Fleet Size Adjustment",
			Label:         "Fleet Size Adjustment",
			Category:      risk_factors.RiskFactorsCategoryOperations,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"operations", "fleet", "size"},
		},
		{
			Name:          "Driver Turnover Adjustment",
			Label:         "Driver Turnover Adjustment",
			Category:      risk_factors.RiskFactorsCategoryDrivers,
			Description:   "risk factor description",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          []string{"drivers", "turnover", "retention"},
		},
	}
	var riskFactorIds []string
	for _, rf := range riskFactors {
		rf.Id = uuid.New()
		riskFactorIds = append(riskFactorIds, rf.Id.String())
		err := s.deps.RiskFactorWrapper.InsertRiskFactor(s.suiteCtx, rf)
		s.Require().NoError(err)
	}
	return riskFactorIds, nil
}

func (s *riskFactorTestSuite) setupSuggestedRiskFactor() (*string, error) {
	// Add the suggested risk factor to the worksheet
	suggestedFactor := risk_factors.RiskFactor{
		Id:            SuggestedFactorId,
		Name:          "Suggested Risk Factor",
		Label:         "Suggested Risk Factor",
		Category:      risk_factors.RiskFactorsCategoryUnspecified,
		Description:   "suggest risk factor description",
		PricingType:   risk_factors.RiskFactorsPricingTypeUnspecified,
		State:         risk_factors.RiskFactorsStateUnspecified,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
		Version:       1,
		IsLatest:      true,
		CreatedBy:     uuid.New(),
		LastUpdatedBy: uuid.New(),
		Tags:          []string{"suggested", "custom"},
	}
	err := s.deps.RiskFactorWrapper.InsertRiskFactor(s.suiteCtx, suggestedFactor)
	s.Require().NoError(err)
	return pointer_utils.String(SuggestedFactorId.String()), nil
}

// setupWorksheetWithRiskFactor is a helper to create a worksheet and add a risk factor with the given label
func (s *riskFactorTestSuite) setupWorksheetWithRiskFactor(label RiskFactorLabel, value string, sentiment Sentiment) (string, string, error) {
	// Create worksheet
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	if err != nil {
		return "", "", err
	}
	worksheetId := resp.WorksheetId

	// Add risk factor
	_, err = s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
		ReviewId:        s.appReviewId,
		RiskFactorLabel: label,
		Value:           value,
		Sentiment:       sentiment,
	})
	if err != nil {
		return worksheetId, "", err
	}

	// Get the risk factor ID
	worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: worksheetId,
	})
	if err != nil {
		return worksheetId, "", err
	}
	if len(worksheet.WorksheetRiskFactors) == 0 {
		return worksheetId, "", nil
	}

	return worksheetId, worksheet.WorksheetRiskFactors[0].WorksheetRiskFactorId, nil
}

func (s *riskFactorTestSuite) TestCreateRiskFactor() {
	riskFactorIds := s.riskFactorIds
	// Fetch the risk factors
	for _, id := range riskFactorIds {
		resp, err := s.deps.RiskFactorWrapper.GetRiskFactor(s.suiteCtx, id)
		s.Require().NoError(err)
		s.Require().NotNil(resp)
	}
}

func (s *riskFactorTestSuite) TestCreateWorksheet() {
	tests := []struct {
		name          string
		request       *CreateWorksheetRequest
		expectedError error
	}{
		{
			name: "success",
			request: &CreateWorksheetRequest{
				ReviewId: s.appReviewId,
			},
			expectedError: nil,
		},
		{
			name: "empty_review_id",
			request: &CreateWorksheetRequest{
				ReviewId: "",
			},
			expectedError: status.Error(codes.InvalidArgument, "review ID is required"),
		},
		{
			name: "invalid_review_id",
			request: &CreateWorksheetRequest{
				ReviewId: "invalid-uuid",
			},
			expectedError: status.Error(codes.InvalidArgument, "invalid review ID format"),
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, tt.request)

			if tt.expectedError != nil {
				s.Require().Error(err)
				s.Equal(tt.expectedError.Error(), err.Error())
				s.Nil(resp)
			} else {
				s.Require().NoError(err)
				s.NotNil(resp)
				s.NotEmpty(resp.WorksheetId)

				// Verify the created worksheet exists
				worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
					WorksheetId: resp.WorksheetId,
				})
				s.Require().NoError(err)
				s.NotNil(worksheet)
				s.Equal(resp.WorksheetId, worksheet.Id)
				s.Equal(tt.request.ReviewId, worksheet.ReviewId)
			}
		})
	}
}

func (s *riskFactorTestSuite) TestWorksheetRiskFactorCreate() {
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotNil(worksheetId)
	riskFactorIds := s.riskFactorIds

	// Verify that the worksheet starts with no risk factors
	initialResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: *worksheetId,
	})
	s.Require().NoError(err)
	s.Require().Empty(initialResp.WorksheetRiskFactors)

	tests := []struct {
		name          string
		request       *AddWorksheetRiskFactorRequest
		expectedError error
		validate      func(response *WorksheetRiskFactorResponse)
	}{
		{
			name: "missing_value_and_sentiment",
			request: &AddWorksheetRiskFactorRequest{
				WorksheetId:  *worksheetId,
				RiskFactorId: riskFactorIds[0],
				Notes:        "some notes",
			},
			expectedError: status.Error(codes.InvalidArgument, "value and sentiment are required"),
		},
		{
			name: "missing_value",
			request: &AddWorksheetRiskFactorRequest{
				WorksheetId:  *worksheetId,
				RiskFactorId: riskFactorIds[0],
				Sentiment:    Sentiment_POSITIVE,
			},
			expectedError: status.Error(codes.InvalidArgument, "value is required"),
		},
		{
			name: "missing_sentiment",
			request: &AddWorksheetRiskFactorRequest{
				WorksheetId:  *worksheetId,
				RiskFactorId: riskFactorIds[0],
				Value:        "test value",
			},
			expectedError: status.Error(codes.InvalidArgument, "sentiment must be either POSITIVE, NEGATIVE, or NEUTRAL"),
		},
		{
			name: "creation_with_required_fields",
			request: &AddWorksheetRiskFactorRequest{
				WorksheetId:  *worksheetId,
				RiskFactorId: riskFactorIds[1],
				Value:        "test value",
				Sentiment:    Sentiment_POSITIVE,
			},
			validate: func(response *WorksheetRiskFactorResponse) {
				s.Equal("test value", response.Value)
				s.Empty(response.Notes)
				s.Equal(Sentiment_POSITIVE, response.Sentiment)
			},
		},
		{
			name: "creation_with_all_fields",
			request: &AddWorksheetRiskFactorRequest{
				WorksheetId:  *worksheetId,
				RiskFactorId: riskFactorIds[2],
				Value:        "complete value",
				Notes:        "complete notes",
				Sentiment:    Sentiment_NEGATIVE,
			},
			validate: func(response *WorksheetRiskFactorResponse) {
				s.Equal("complete value", response.Value)
				s.Equal("complete notes", response.Notes)
				s.Equal(Sentiment_NEGATIVE, response.Sentiment)
			},
		},
		{
			name: "sentiment_unspecified_not_allowed",
			request: &AddWorksheetRiskFactorRequest{
				WorksheetId:  *worksheetId,
				RiskFactorId: riskFactorIds[3],
				Value:        "test value",
				Sentiment:    Sentiment_SENTIMENT_UNSPECIFIED,
			},
			expectedError: status.Error(codes.InvalidArgument, "sentiment must be either POSITIVE, NEGATIVE, or NEUTRAL"),
		},
		{
			name: "creation_with_neutral_sentiment",
			request: &AddWorksheetRiskFactorRequest{
				WorksheetId:  *worksheetId,
				RiskFactorId: riskFactorIds[4],
				Value:        "neutral value",
				Notes:        "neutral notes",
				Sentiment:    Sentiment_NEUTRAL,
			},
			validate: func(response *WorksheetRiskFactorResponse) {
				s.Equal("neutral value", response.Value)
				s.Equal("neutral notes", response.Notes)
				s.Equal(Sentiment_NEUTRAL, response.Sentiment)
			},
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			resp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, tt.request)

			if tt.expectedError != nil {
				s.Require().Error(err)
				s.Equal(tt.expectedError.Error(), err.Error())
				return
			}

			s.Require().NoError(err)
			s.Require().NotNil(resp)

			tt.validate(resp)

			worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
				WorksheetId: *worksheetId,
			})
			s.Require().NoError(err)

			var found bool
			for _, rf := range worksheetResp.WorksheetRiskFactors {
				if rf.WorksheetRiskFactorId == resp.WorksheetRiskFactorId {
					tt.validate(rf)
					found = true
					break
				}
			}
			s.True(found, "Added risk factor not found in worksheet response")
		})
	}
}

// TestUpdateRiskFactor tests the update of a worksheet risk factor, by updating values, notes
func (s *riskFactorTestSuite) TestUpdateRiskFactor() {
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotNil(worksheetId)
	riskFactorIds := s.riskFactorIds
	// Create worksheet risk factors
	for _, rfId := range riskFactorIds {
		resp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
			WorksheetId:  *worksheetId,
			RiskFactorId: rfId,
			Value:        "dummy value",
			Sentiment:    Sentiment_POSITIVE,
		})
		s.Require().NoError(err)
		s.Require().NotNil(resp)
	}
	// Fetch the worksheet risk factors
	resp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: *worksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetRiskFactors)
	// Update the worksheet risk factors
	for _, rf := range resp.WorksheetRiskFactors {
		_, err = s.deps.Client.UpdateWorksheetRiskFactor(s.suiteCtx, &UpdateWorksheetRiskFactorRequest{
			WorksheetRiskFactorId: rf.WorksheetRiskFactorId,
			Value:                 "values",
			Notes:                 "notes",
		})
		s.Require().NoError(err)
	}
	// Fetch the worksheet risk factors
	resp, err = s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: *worksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetRiskFactors)
	// Compare the risk factors and verify that they have the same IDs
	for _, rf := range resp.WorksheetRiskFactors {
		s.Require().Equal("values", rf.Value)
		s.Require().Equal("notes", rf.Notes)
	}
}

func (s *riskFactorTestSuite) TestDeleteRiskFactor() {
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotNil(worksheetId)
	riskFactorIds := s.riskFactorIds
	// Create worksheet risk factors
	for _, rfId := range riskFactorIds {
		resp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
			WorksheetId:  *worksheetId,
			RiskFactorId: rfId,
			Value:        "dummy value",
			Sentiment:    Sentiment_POSITIVE,
		})
		s.Require().NoError(err)
		s.Require().NotNil(resp)
	}
	// Fetch the worksheet risk factors
	resp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: *worksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetRiskFactors)
	// Delete the worksheet risk factors
	for _, rf := range resp.WorksheetRiskFactors {
		_, err = s.deps.Client.DeleteWorksheetRiskFactor(s.suiteCtx, &DeleteRiskFactorRequest{
			WorksheetRiskFactorId: rf.WorksheetRiskFactorId,
		})
		s.Require().NoError(err)
	}
	// Fetch the worksheet risk factors
	resp, err = s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: *worksheetId,
	})
	s.Require().NoError(err)
	s.Require().Empty(resp.WorksheetRiskFactors)
}

// TestSuggestRiskFactor tests the suggestion of risk factors method
func (s *riskFactorTestSuite) TestSuggestRiskFactor() {
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotNil(worksheetId)
	// Add a suggested risk factor
	_, err = s.deps.Client.SuggestRiskFactor(s.suiteCtx, &SuggestRiskFactorRequest{
		Name:        "Driver Age",
		Notes:       "notes",
		Category:    Category_DRIVERS,
		Value:       "values",
		Sentiment:   Sentiment_POSITIVE,
		WorksheetId: *worksheetId,
	})
	s.Require().NoError(err)
	// Fetch the worksheet risk factors
	resp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: *worksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	for _, rf := range resp.WorksheetRiskFactors {
		s.Require().Equal("values", rf.Value)
		s.Require().Equal("notes", rf.Notes)
		s.Require().Equal(Category_DRIVERS, rf.RiskFactor.Category)
		s.Require().Equal(Sentiment_POSITIVE, rf.Sentiment)
		s.Require().Equal("Driver Age", rf.RiskFactor.Name)
	}
}

// TestUpdateWorksheetPricing tests the update of worksheet pricing
func (s *riskFactorTestSuite) TestUpdateWorksheetPricing() {
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
	// Create a worksheet first
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotEmpty(worksheetId)

	tests := []struct {
		name           string
		request        *UpdateWorksheetPricingRequest
		expectedError  error
		validateResult func(s *riskFactorTestSuite, worksheet *Worksheet)
	}{
		{
			name: "update_new_category",
			request: &UpdateWorksheetPricingRequest{
				WorksheetId: *worksheetId,
				CategoryPricingDetails: &CategoryPricingDetails{
					Category: Category_DRIVERS,
					CoveragePricingDetail: []*CoveragePricingDetail{
						{
							Coverage: Coverage_AUTO_LIABILITY,
							Credit:   10,
						},
					},
				},
			},
			validateResult: func(s *riskFactorTestSuite, worksheet *Worksheet) {
				s.Require().NotNil(worksheet.GetPricingDetails())
				s.Require().Len(worksheet.GetPricingDetails().GetCategoryPricingDetails(), 1)
				details := worksheet.GetPricingDetails().GetCategoryPricingDetails()[0]
				s.Equal(Category_DRIVERS, details.GetCategory())
				s.Require().Len(details.GetCoveragePricingDetail(), 1)
				s.Equal(Coverage_AUTO_LIABILITY, details.GetCoveragePricingDetail()[0].GetCoverage())
				s.Equal(int32(10), details.GetCoveragePricingDetail()[0].GetCredit())
			},
		},
		{
			name: "update_existing_category_multiple_coverages",
			request: &UpdateWorksheetPricingRequest{
				WorksheetId: *worksheetId,
				CategoryPricingDetails: &CategoryPricingDetails{
					Category: Category_DRIVERS,
					CoveragePricingDetail: []*CoveragePricingDetail{
						{
							Coverage: Coverage_AUTO_LIABILITY,
							Credit:   15,
						},
						{
							Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
							Credit:   20,
						},
					},
				},
			},
			validateResult: func(s *riskFactorTestSuite, worksheet *Worksheet) {
				s.Require().NotNil(worksheet.GetPricingDetails())
				details := worksheet.GetPricingDetails().GetCategoryPricingDetails()[0]
				s.Equal(Category_DRIVERS, details.GetCategory())
				s.Require().Len(details.GetCoveragePricingDetail(), 2)

				// Map to store found coverages
				foundCoverages := make(map[Coverage]bool)

				for _, coverage := range details.GetCoveragePricingDetail() {
					switch coverage.GetCoverage() {
					case Coverage_AUTO_LIABILITY:
						s.Equal(int32(15), coverage.GetCredit())
						foundCoverages[Coverage_AUTO_LIABILITY] = true
					case Coverage_AUTO_PHYSICAL_DAMAGE:
						s.Equal(int32(20), coverage.GetCredit())
						foundCoverages[Coverage_AUTO_PHYSICAL_DAMAGE] = true
					default:
						s.Fail("Unexpected coverage type")
					}
				}

				s.Len(foundCoverages, 2, "Not all expected coverages were found")
			},
		},
		{
			name: "add_new_category_preserve_existing",
			request: &UpdateWorksheetPricingRequest{
				WorksheetId: *worksheetId,
				CategoryPricingDetails: &CategoryPricingDetails{
					Category: Category_OPERATIONS,
					CoveragePricingDetail: []*CoveragePricingDetail{
						{
							Coverage: Coverage_AUTO_LIABILITY,
							Credit:   5,
						},
					},
				},
			},
			validateResult: func(s *riskFactorTestSuite, worksheet *Worksheet) {
				s.Require().NotNil(worksheet.GetPricingDetails())
				categoryDetails := worksheet.GetPricingDetails().GetCategoryPricingDetails()
				s.Require().Len(categoryDetails, 2)

				// Map to store category validations
				foundCategories := make(map[Category]bool)

				for _, category := range categoryDetails {
					switch category.GetCategory() {
					case Category_DRIVERS:
						s.Require().Len(category.GetCoveragePricingDetail(), 2)
						for _, coverage := range category.GetCoveragePricingDetail() {
							switch coverage.GetCoverage() {
							case Coverage_AUTO_LIABILITY:
								s.Equal(int32(15), coverage.GetCredit())
							case Coverage_AUTO_PHYSICAL_DAMAGE:
								s.Equal(int32(20), coverage.GetCredit())
							}
						}
						foundCategories[Category_DRIVERS] = true
					case Category_OPERATIONS:
						s.Require().Len(category.GetCoveragePricingDetail(), 1)
						s.Equal(Coverage_AUTO_LIABILITY, category.GetCoveragePricingDetail()[0].GetCoverage())
						s.Equal(int32(5), category.GetCoveragePricingDetail()[0].GetCredit())
						foundCategories[Category_OPERATIONS] = true
					}
				}

				s.Len(foundCategories, 2, "Not all expected categories were found")
			},
		},
		{
			name: "invalid_worksheet_id",
			request: &UpdateWorksheetPricingRequest{
				WorksheetId: "invalid-uuid",
				CategoryPricingDetails: &CategoryPricingDetails{
					Category: Category_DRIVERS,
					CoveragePricingDetail: []*CoveragePricingDetail{
						{
							Coverage: Coverage_AUTO_LIABILITY,
							Credit:   10,
						},
					},
				},
			},
			expectedError: status.Error(codes.InvalidArgument, "invalid worksheet ID"),
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			_, err := s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, tt.request)

			if tt.expectedError != nil {
				s.Require().Error(err)
				s.Equal(tt.expectedError.Error(), err.Error())
				return
			}

			s.Require().NoError(err)

			// If we expect success, validate the result
			if tt.validateResult != nil {
				worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
					WorksheetId: tt.request.WorksheetId,
				})
				s.Require().NoError(err)
				tt.validateResult(s, worksheet)
			}
		})
	}
}

func (s *riskFactorTestSuite) TestGetWorksheet() {
	tests := []struct {
		name          string
		setupFunc     func() string
		request       *GetWorksheetRequest
		expectedError error
	}{
		{
			name: "success",
			setupFunc: func() string {
				worksheetId, err := s.setupRiskWorksheet()
				s.Require().NoError(err)
				s.Require().NotNil(worksheetId)
				return *worksheetId
			},
			request: &GetWorksheetRequest{
				WorksheetId: "", // Will be filled by setupFunc
			},
			expectedError: nil,
		},
		{
			name: "empty_worksheet_id",
			request: &GetWorksheetRequest{
				WorksheetId: "",
			},
			expectedError: status.Error(codes.InvalidArgument, "worksheet ID is required"),
		},
		{
			name: "invalid_uuid",
			request: &GetWorksheetRequest{
				WorksheetId: "invalid-uuid",
			},
			expectedError: status.Error(codes.Internal, "failed to fetch worksheet"),
		},
		{
			name: "non_existent_worksheet",
			request: &GetWorksheetRequest{
				WorksheetId: uuid.New().String(),
			},
			expectedError: status.Error(codes.NotFound, "worksheet not found"),
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			if tt.setupFunc != nil {
				tt.request.WorksheetId = tt.setupFunc()
			}

			resp, err := s.deps.Client.GetWorksheet(s.suiteCtx, tt.request)

			if tt.expectedError != nil {
				s.Require().Error(err)
				s.Equal(tt.expectedError.Error(), err.Error())
				s.Nil(resp)
			} else {
				s.Require().NoError(err)
				s.NotNil(resp)
				s.Equal(tt.request.WorksheetId, resp.Id)
			}
		})
	}
}

func (s *riskFactorTestSuite) TestUpdateWorksheet() {
	// Setup a worksheet
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotNil(worksheetId)

	tests := []struct {
		name          string
		request       *WorksheetUpdateRequest
		expectedError error
		validate      func(*Worksheet)
	}{
		{
			name: "success",
			request: &WorksheetUpdateRequest{
				WorksheetId: *worksheetId,
				Notes:       "Updated notes",
			},
			validate: func(worksheet *Worksheet) {
				s.Equal("Updated notes", worksheet.Notes)
			},
		},
		{
			name: "empty_worksheet_id",
			request: &WorksheetUpdateRequest{
				WorksheetId: "",
				Notes:       "Test notes",
			},
			expectedError: status.Error(codes.InvalidArgument, "invalid worksheet ID"),
		},
		{
			name: "invalid_uuid",
			request: &WorksheetUpdateRequest{
				WorksheetId: "invalid-uuid",
				Notes:       "Test notes",
			},
			expectedError: status.Error(codes.InvalidArgument, "invalid worksheet ID"),
		},
		{
			name: "non_existent_worksheet",
			request: &WorksheetUpdateRequest{
				WorksheetId: uuid.New().String(),
				Notes:       "Test notes",
			},
			expectedError: status.Error(codes.Internal, "update failed"),
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			_, err := s.deps.Client.UpdateWorksheet(s.suiteCtx, tt.request)

			if tt.expectedError != nil {
				s.Require().Error(err)
				s.Equal(tt.expectedError.Error(), err.Error())
				return
			}

			s.Require().NoError(err)

			// If we expect success, validate the worksheet state
			if tt.validate != nil {
				worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
					WorksheetId: tt.request.WorksheetId,
				})
				s.Require().NoError(err)
				tt.validate(worksheet)
			}
		})
	}
}

func (s *riskFactorTestSuite) TestListRiskFactors() {
	tests := []struct {
		name          string
		setupFunc     func()
		expectedError error
		validate      func(*ListRiskFactorsResponse)
	}{
		{
			name: "success",
			validate: func(resp *ListRiskFactorsResponse) {
				s.NotNil(resp)
				s.NotEmpty(resp.RiskFactors)
				// Verify at least the expected risk factors are present
				s.GreaterOrEqual(len(resp.RiskFactors), len(s.riskFactorIds), "Should have at least the initial risk factors")
				// Verify each of our created risk factor IDs is found in the response
				foundIds := make(map[string]bool)
				for _, rf := range resp.RiskFactors {
					foundIds[rf.Id] = true
				}
				for _, id := range s.riskFactorIds {
					s.True(foundIds[id], "Risk factor %s not found in response", id)
				}
			},
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			if tt.setupFunc != nil {
				tt.setupFunc()
			}

			resp, err := s.deps.Client.ListRiskFactors(s.suiteCtx, &emptypb.Empty{})

			if tt.expectedError != nil {
				s.Require().Error(err)
				s.Equal(tt.expectedError.Error(), err.Error())
				s.Nil(resp)
			} else {
				s.Require().NoError(err)
				if tt.validate != nil {
					tt.validate(resp)
				}
			}
		})
	}
}

func (s *riskFactorTestSuite) TestUpdateWorksheetRiskFactor() {
	// Setup a worksheet with a risk factor
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotNil(worksheetId)

	// Add a risk factor to update
	addResp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
		WorksheetId:  *worksheetId,
		RiskFactorId: s.riskFactorIds[0],
		Value:        "dummy value",
		Sentiment:    Sentiment_POSITIVE,
	})
	s.Require().NoError(err)
	s.Require().NotNil(addResp)

	tests := []struct {
		name          string
		request       *UpdateWorksheetRiskFactorRequest
		expectedError error
		validate      func(*WorksheetRiskFactorResponse)
	}{
		{
			name: "success",
			request: &UpdateWorksheetRiskFactorRequest{
				WorksheetRiskFactorId: addResp.WorksheetRiskFactorId,
				Value:                 "test value",
				Notes:                 "test notes",
				Sentiment:             Sentiment_POSITIVE,
			},
			validate: func(resp *WorksheetRiskFactorResponse) {
				s.Equal("test value", resp.Value)
				s.Equal("test notes", resp.Notes)
				s.Equal(Sentiment_POSITIVE, resp.Sentiment)
			},
		},
		{
			name: "update_without_sentiment",
			request: &UpdateWorksheetRiskFactorRequest{
				WorksheetRiskFactorId: addResp.WorksheetRiskFactorId,
				Value:                 "updated value",
				Notes:                 "updated notes",
			},
			validate: func(resp *WorksheetRiskFactorResponse) {
				s.Equal("updated value", resp.Value)
				s.Equal("updated notes", resp.Notes)
				s.Equal(Sentiment_POSITIVE, resp.Sentiment)
			},
		},
		{
			name: "update_with_neutral_sentiment",
			request: &UpdateWorksheetRiskFactorRequest{
				WorksheetRiskFactorId: addResp.WorksheetRiskFactorId,
				Value:                 "neutral value",
				Notes:                 "neutral notes",
				Sentiment:             Sentiment_NEUTRAL,
			},
			validate: func(resp *WorksheetRiskFactorResponse) {
				s.Equal("neutral value", resp.Value)
				s.Equal("neutral notes", resp.Notes)
				s.Equal(Sentiment_NEUTRAL, resp.Sentiment)
			},
		},
		{
			name: "empty_worksheet_risk_factor_id",
			request: &UpdateWorksheetRiskFactorRequest{
				WorksheetRiskFactorId: "",
				Value:                 "test value",
			},
			expectedError: status.Error(codes.InvalidArgument, "invalid risk factor ID"),
		},
		{
			name: "invalid_uuid",
			request: &UpdateWorksheetRiskFactorRequest{
				WorksheetRiskFactorId: "invalid-uuid",
				Value:                 "test value",
			},
			expectedError: status.Error(codes.InvalidArgument, "invalid risk factor ID"),
		},
		{
			name: "non_existent_risk_factor",
			request: &UpdateWorksheetRiskFactorRequest{
				WorksheetRiskFactorId: uuid.New().String(),
				Value:                 "test value",
			},
			expectedError: status.Error(codes.Internal, "update failed"),
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			resp, err := s.deps.Client.UpdateWorksheetRiskFactor(s.suiteCtx, tt.request)

			if tt.expectedError != nil {
				s.Require().Error(err)
				s.Equal(tt.expectedError.Error(), err.Error())
				s.Nil(resp)
			} else {
				s.Require().NoError(err)
				s.NotNil(resp)
				if tt.validate != nil {
					tt.validate(resp)
				}
			}
		})
	}
}

func (s *riskFactorTestSuite) TestCreateWorksheetMakesExistingStale() {
	// Create a first worksheet
	resp1, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp1)

	// Verify it was created properly
	worksheet1, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp1.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().Equal(WorksheetState_WORKSHEET_STATE_ACTIVE, worksheet1.State)

	// Create a second worksheet for the same review
	resp2, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp2)
	s.Require().NotEqual(resp1.WorksheetId, resp2.WorksheetId)

	// Verify the second one is active
	worksheet2, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp2.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().Equal(WorksheetState_WORKSHEET_STATE_ACTIVE, worksheet2.State)

	// Verify the first one is now stale
	worksheet1Updated, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp1.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().Equal(WorksheetState_WORKSHEET_STATE_STALE, worksheet1Updated.State)

	// Test GetLatestWorksheetForReview returns only the active worksheet
	latestWorksheet, err := s.deps.Client.GetLatestWorksheetForReview(s.suiteCtx, &GetLatestWorksheetForReviewRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().Equal(resp2.WorksheetId, latestWorksheet.Id)
	s.Require().Equal(WorksheetState_WORKSHEET_STATE_ACTIVE, latestWorksheet.State)
}

func (s *riskFactorTestSuite) TestCreateV2Worksheet() {
	// Get the feature flag client from deps and update it
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Create a worksheet
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Get the worksheet and verify it's version 2
	worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(worksheet)

	// Check that the worksheet has version 2
	dbWorksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, resp.WorksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV2, dbWorksheet.Version)

	// Reset the feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

func (s *riskFactorTestSuite) TestAddRiskFactorToV2Worksheet() {
	// Get the feature flag client from deps and update it
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Create a worksheet (will be v2)
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Verify it's a v2 worksheet
	worksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, resp.WorksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV2, worksheet.Version)

	// Add a risk factor to the worksheet
	riskFactorResp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
		WorksheetId:  resp.WorksheetId,
		RiskFactorId: s.riskFactorIds[0],
		Value:        "test value",
		Sentiment:    Sentiment_POSITIVE,
	})
	s.Require().NoError(err)
	s.Require().NotNil(riskFactorResp)

	// Get the worksheet with risk factors
	worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(worksheetResp.WorksheetRiskFactors)
	s.Require().Equal(1, len(worksheetResp.WorksheetRiskFactors))

	// Reset the feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

func (s *riskFactorTestSuite) TestSuggestRiskFactorToV2Worksheet() {
	// Get the feature flag client from deps and update it
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Create a worksheet (will be v2)
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Verify it's a v2 worksheet
	worksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, resp.WorksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV2, worksheet.Version)

	// Suggest a risk factor
	_, err = s.deps.Client.SuggestRiskFactor(s.suiteCtx, &SuggestRiskFactorRequest{
		Name:        "Suggested Factor for V2",
		Notes:       "Test notes for v2",
		Category:    Category_SAFETY,
		Value:       "Test value for v2",
		Sentiment:   Sentiment_POSITIVE,
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)

	// Get the worksheet with risk factors
	worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(worksheetResp.WorksheetRiskFactors)
	s.Require().Equal(1, len(worksheetResp.WorksheetRiskFactors))

	// Verify the suggested risk factor
	suggestedRiskFactor := worksheetResp.WorksheetRiskFactors[0]
	s.Require().Equal("Suggested Factor for V2", suggestedRiskFactor.RiskFactor.Name)
	s.Require().Equal("Test notes for v2", suggestedRiskFactor.Notes)
	s.Require().Equal("Test value for v2", suggestedRiskFactor.Value)
	s.Require().Equal(Category_SAFETY, suggestedRiskFactor.RiskFactor.Category)
	s.Require().Equal(Sentiment_POSITIVE, suggestedRiskFactor.Sentiment)

	// Reset the feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

// TestV2WorksheetHasNoPricingAtCreation verifies that V2 worksheets don't have an initial pricing sheet
func (s *riskFactorTestSuite) TestV2WorksheetHasNoPricingAtCreation() {
	// Get the feature flag client from deps and update it
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Create a worksheet (will be v2)
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Verify it's a v2 worksheet
	worksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, resp.WorksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV2, worksheet.Version)

	// Get the worksheet response - it should have no pricing details yet
	worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)

	// Verify the response has the Version field set correctly
	s.Require().Equal(int32(PricingVersionV2), worksheetResp.Version)

	// For V2 worksheets with no risk factors, there should be no pricing details yet
	s.Require().Nil(worksheetResp.PricingDetails)

	// Reset the feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

// TestV2WorksheetRiskFactorPricing verifies that V2 worksheets create individual pricing entries per risk factor
func (s *riskFactorTestSuite) TestV2WorksheetRiskFactorPricing() {
	// Get the feature flag client from deps and update it
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Create a worksheet (will be v2)
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Verify it's a v2 worksheet
	worksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, resp.WorksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV2, worksheet.Version)

	worksheetMap := []string{}
	// Add multiple risk factors to the worksheet
	for i := 0; i < 5; i++ {
		worksheetResp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
			WorksheetId:  resp.WorksheetId,
			RiskFactorId: s.riskFactorIds[i],
			Value:        "test value " + string(rune(i+48)),
			Sentiment:    Sentiment_POSITIVE,
		})
		s.Require().NoError(err)
		s.Require().NotNil(worksheetResp)
		worksheetMap = append(worksheetMap, worksheetResp.WorksheetRiskFactorId)
	}

	// Get the worksheet with risk factors
	worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)

	// Validate the worksheet
	s.Require().NotNil(worksheetResp)
	s.Require().Equal(int32(PricingVersionV2), worksheetResp.Version)
	s.Require().NotEmpty(worksheetResp.WorksheetRiskFactors)
	s.Require().Equal(5, len(worksheetResp.WorksheetRiskFactors))

	// Now add pricing for one of the risk factors with all valid coverages
	updateReq := &UpdateWorksheetPricingRequest{
		WorksheetId:       resp.WorksheetId,
		WorksheetFactorId: worksheetMap[0],
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   15,
			},
			{
				Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
				Credit:   10,
			},
			{
				Coverage: Coverage_GENERAL_LIABILITY,
				Credit:   8,
			},
		},
	}
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, updateReq)
	s.Require().NoError(err)

	// Add pricing for another risk factor with different coverages
	updateReq2 := &UpdateWorksheetPricingRequest{
		WorksheetId:       resp.WorksheetId,
		WorksheetFactorId: worksheetMap[1],
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   5,
			},
			{
				Coverage: Coverage_MOTOR_TRUCK_CARGO,
				Credit:   7,
			},
		},
	}
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, updateReq2)
	s.Require().NoError(err)

	// Get the worksheet again to check the pricing
	worksheetResp, err = s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)

	// Now there should be pricing details
	s.Require().NotNil(worksheetResp.PricingDetails)

	// Reset the feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

// TestUpdatePricingForV2Worksheet verifies that updating pricing for V2 worksheets works correctly
func (s *riskFactorTestSuite) TestUpdatePricingForV2Worksheet() {
	// Get the feature flag client from deps and update it
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Create a worksheet (will be v2)
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Add three risk factors to the worksheet
	var riskFactorResponses []*WorksheetRiskFactorResponse
	for i := 0; i < 3; i++ {
		rfResp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
			WorksheetId:  resp.WorksheetId,
			RiskFactorId: s.riskFactorIds[i],
			Value:        "test value " + string(rune(i+48)),
			Sentiment:    Sentiment_POSITIVE,
		})
		s.Require().NoError(err)
		s.Require().NotNil(rfResp)
		riskFactorResponses = append(riskFactorResponses, rfResp)
	}

	// Update pricing for the first risk factor with multiple coverages
	updateReq := &UpdateWorksheetPricingRequest{
		WorksheetId:       resp.WorksheetId,
		WorksheetFactorId: riskFactorResponses[0].WorksheetRiskFactorId,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   10,
			},
			{
				Coverage: Coverage_GENERAL_LIABILITY,
				Credit:   5,
			},
			{
				Coverage: Coverage_MOTOR_TRUCK_CARGO,
				Credit:   3,
			},
		},
	}
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, updateReq)
	s.Require().NoError(err)

	// Get the worksheet to verify pricing was updated
	worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(worksheetResp.PricingDetails)

	// Update pricing for the second risk factor with different coverage types
	updateReq2 := &UpdateWorksheetPricingRequest{
		WorksheetId:       resp.WorksheetId,
		WorksheetFactorId: riskFactorResponses[1].WorksheetRiskFactorId,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
				Credit:   20,
			},
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   8,
			},
		},
	}
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, updateReq2)
	s.Require().NoError(err)

	// Update pricing for the third risk factor with overlapping coverages
	updateReq3 := &UpdateWorksheetPricingRequest{
		WorksheetId:       resp.WorksheetId,
		WorksheetFactorId: riskFactorResponses[2].WorksheetRiskFactorId,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY, // Overlaps with the first risk factor
				Credit:   15,
			},
			{
				Coverage: Coverage_GENERAL_LIABILITY, // Overlaps with the first risk factor
				Credit:   12,
			},
		},
	}
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, updateReq3)
	s.Require().NoError(err)

	// Get the worksheet again to check all pricing entries
	worksheetResp, err = s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(worksheetResp.PricingDetails)

	// Count the number of different coverage types across all categories
	coverageTypes := make(map[Coverage]bool)
	for _, category := range worksheetResp.PricingDetails.CategoryPricingDetails {
		for _, coverage := range category.CoveragePricingDetail {
			coverageTypes[coverage.Coverage] = true
		}
	}
	s.Require().GreaterOrEqual(len(coverageTypes), 4, "Expected at least 4 different coverage types")

	// Reset the feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

// TestMultipleRiskFactorsForV2Worksheet verifies multiple risk factors with different categories are handled correctly
func (s *riskFactorTestSuite) TestMultipleRiskFactorsForV2Worksheet() {
	// Get the feature flag client from deps and update it
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Create a worksheet (will be v2)
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Add three risk factors with different categories:
	// Index 0: DRIVERS
	// Index 1: EQUIPMENTS
	// Index 2: FINANCIALS
	worksheetRespSlice := []string{}
	for i := 0; i < 3; i++ {
		worksheetFactorResp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
			WorksheetId:  resp.WorksheetId,
			RiskFactorId: s.riskFactorIds[i],
			Value:        "test value " + string(rune(i+48)),
			Sentiment:    Sentiment_POSITIVE,
		})
		s.Require().NoError(err)
		worksheetRespSlice = append(worksheetRespSlice, worksheetFactorResp.WorksheetRiskFactorId)
	}

	// Update pricing for all three risk factors (each in a different category)
	updateReqs := []*UpdateWorksheetPricingRequest{
		{
			// DRIVERS category (index 0)
			WorksheetId:       resp.WorksheetId,
			WorksheetFactorId: worksheetRespSlice[0],
			CoveragePricingDetail: []*CoveragePricingDetail{
				{
					Coverage: Coverage_AUTO_LIABILITY,
					Credit:   10,
				},
				{
					Coverage: Coverage_GENERAL_LIABILITY,
					Credit:   5,
				},
			},
		},
		{
			// EQUIPMENTS category (index 1)
			WorksheetId:       resp.WorksheetId,
			WorksheetFactorId: worksheetRespSlice[1],
			CoveragePricingDetail: []*CoveragePricingDetail{
				{
					Coverage: Coverage_AUTO_LIABILITY,
					Credit:   15,
				},
				{
					Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
					Credit:   12,
				},
			},
		},
		{
			// FINANCIALS category (index 2)
			WorksheetId:       resp.WorksheetId,
			WorksheetFactorId: worksheetRespSlice[2],
			CoveragePricingDetail: []*CoveragePricingDetail{
				{
					Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
					Credit:   8,
				},
				{
					Coverage: Coverage_MOTOR_TRUCK_CARGO,
					Credit:   20,
				},
				{
					Coverage: Coverage_GENERAL_LIABILITY,
					Credit:   7,
				},
			},
		},
	}

	for _, req := range updateReqs {
		_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, req)
		s.Require().NoError(err)
	}

	// Get the worksheet to verify pricing was updated
	worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(worksheetResp.PricingDetails)

	// Ensure we have pricing details for each category
	s.Require().NotEmpty(worksheetResp.PricingDetails.CategoryPricingDetails)
	s.Require().Equal(3, len(worksheetResp.PricingDetails.CategoryPricingDetails),
		"Should have pricing details for all 3 categories")

	// Create maps to store and validate each category's coverage details
	categoryMapping := map[Category]map[Coverage]int32{
		Category_DRIVERS:    {},
		Category_EQUIPMENTS: {},
		Category_FINANCIALS: {},
	}

	// Extract coverages for each category
	for _, category := range worksheetResp.PricingDetails.CategoryPricingDetails {
		s.Require().Contains(categoryMapping, category.Category,
			"Category %v should be one of the expected categories", category.Category)

		for _, coverage := range category.CoveragePricingDetail {
			categoryMapping[category.Category][coverage.Coverage] = coverage.Credit
		}
	}

	// Verify DRIVERS category
	driversMap := categoryMapping[Category_DRIVERS]
	s.Require().Equal(2, len(driversMap), "DRIVERS category should have 2 coverage types")
	s.Require().Equal(int32(10), driversMap[Coverage_AUTO_LIABILITY],
		"AUTO_LIABILITY credit should be 10 for DRIVERS")
	s.Require().Equal(int32(5), driversMap[Coverage_GENERAL_LIABILITY],
		"GENERAL_LIABILITY credit should be 5 for DRIVERS")

	// Verify EQUIPMENTS category
	equipmentsMap := categoryMapping[Category_EQUIPMENTS]
	s.Require().Equal(2, len(equipmentsMap), "EQUIPMENTS category should have 2 coverage types")
	s.Require().Equal(int32(15), equipmentsMap[Coverage_AUTO_LIABILITY],
		"AUTO_LIABILITY credit should be 15 for EQUIPMENTS")
	s.Require().Equal(int32(12), equipmentsMap[Coverage_AUTO_PHYSICAL_DAMAGE],
		"AUTO_PHYSICAL_DAMAGE credit should be 12 for EQUIPMENTS")

	// Verify FINANCIALS category
	financialsMap := categoryMapping[Category_FINANCIALS]
	s.Require().Equal(3, len(financialsMap), "FINANCIALS category should have 3 coverage types")
	s.Require().Equal(int32(8), financialsMap[Coverage_AUTO_PHYSICAL_DAMAGE],
		"AUTO_PHYSICAL_DAMAGE credit should be 8 for FINANCIALS")
	s.Require().Equal(int32(20), financialsMap[Coverage_MOTOR_TRUCK_CARGO],
		"MOTOR_TRUCK_CARGO credit should be 20 for FINANCIALS")
	s.Require().Equal(int32(7), financialsMap[Coverage_GENERAL_LIABILITY],
		"GENERAL_LIABILITY credit should be 7 for FINANCIALS")

	// Reset the feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

// TestV2WorksheetRiskFactorsPricingDetails verifies that V2 worksheet risk factors include pricing details
func (s *riskFactorTestSuite) TestV2WorksheetRiskFactorsPricingDetails() {
	// Enable V2 pricing with feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))
	defer s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))

	// Create a V2 worksheet
	worksheetResp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(worksheetResp.WorksheetId)

	// Verify it's a V2 worksheet
	worksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, worksheetResp.WorksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV2, worksheet.Version)

	// Add multiple risk factors to the worksheet
	riskFactorIDs := []string{
		s.riskFactorIds[0], // Driver Age
		s.riskFactorIds[1], // Equipment Age
	}

	// Map to store worksheet factor IDs
	worksheetFactorIDs := make(map[string]string)

	// Add the risk factors to the worksheet
	for _, rfID := range riskFactorIDs {
		resp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
			WorksheetId:  worksheetResp.WorksheetId,
			RiskFactorId: rfID,
			Value:        "Test Value",
			Notes:        "Test Notes",
			Sentiment:    Sentiment_POSITIVE,
		})
		s.Require().NoError(err)
		s.Require().NotNil(resp)

		// Store the worksheet factor ID for later use
		worksheetFactorIDs[rfID] = resp.WorksheetRiskFactorId
	}

	// Set up multiple coverage pricing for the first risk factor
	factor1ID := worksheetFactorIDs[riskFactorIDs[0]]
	factor1InitialPricingRequest := &UpdateWorksheetPricingRequest{
		WorksheetId:       worksheetResp.WorksheetId,
		WorksheetFactorId: factor1ID,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   -15, // Discount
			},
			{
				Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
				Credit:   -10, // Discount
			},
			{
				Coverage: Coverage_GENERAL_LIABILITY,
				Credit:   -5, // Small discount
			},
		},
	}
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, factor1InitialPricingRequest)
	s.Require().NoError(err)

	// Verify all three coverages were set correctly
	worksheetAfterInitialUpdate, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: worksheetResp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(worksheetAfterInitialUpdate)
	s.Require().Len(worksheetAfterInitialUpdate.WorksheetRiskFactors, 2)

	// Find the first risk factor
	var factor1 *WorksheetRiskFactorResponse
	for _, rf := range worksheetAfterInitialUpdate.WorksheetRiskFactors {
		if rf.WorksheetRiskFactorId == factor1ID {
			factor1 = rf
			break
		}
	}
	s.Require().NotNil(factor1, "Couldn't find the first risk factor")
	s.Require().NotEmpty(factor1.CoveragePricingDetail)
	s.Require().Len(factor1.CoveragePricingDetail, 3, "Should have 3 coverages initially")

	// Create a map of initial coverages
	initialCoverages := make(map[Coverage]int32)
	for _, pd := range factor1.CoveragePricingDetail {
		initialCoverages[pd.Coverage] = pd.Credit
	}
	s.Require().Len(initialCoverages, 3)
	s.Require().Equal(int32(-15), initialCoverages[Coverage_AUTO_LIABILITY])
	s.Require().Equal(int32(-10), initialCoverages[Coverage_AUTO_PHYSICAL_DAMAGE])
	s.Require().Equal(int32(-5), initialCoverages[Coverage_GENERAL_LIABILITY])

	// Now update only ONE coverage - just AUTO_LIABILITY
	factor1PartialUpdateRequest := &UpdateWorksheetPricingRequest{
		WorksheetId:       worksheetResp.WorksheetId,
		WorksheetFactorId: factor1ID,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   -25, // Changed discount
			},
		},
	}
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, factor1PartialUpdateRequest)
	s.Require().NoError(err)

	// Set up pricing for the second risk factor
	factor2ID := worksheetFactorIDs[riskFactorIDs[1]]
	factor2PricingRequest := &UpdateWorksheetPricingRequest{
		WorksheetId:       worksheetResp.WorksheetId,
		WorksheetFactorId: factor2ID,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_MOTOR_TRUCK_CARGO,
				Credit:   8, // Surcharge
			},
		},
	}
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, factor2PricingRequest)
	s.Require().NoError(err)

	// Now get the worksheet and verify the risk factors have pricing details
	getResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: worksheetResp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(getResp)
	s.Require().Len(getResp.WorksheetRiskFactors, 2)

	// Create maps for the risk factors and their pricing
	riskFactors := make(map[string]*WorksheetRiskFactorResponse)
	for _, rf := range getResp.WorksheetRiskFactors {
		riskFactors[rf.WorksheetRiskFactorId] = rf
	}

	// Verify the first risk factor has the correct pricing details after complete overwrite
	rf1 := riskFactors[factor1ID]
	s.Require().NotNil(rf1)
	s.Require().NotEmpty(rf1.CoveragePricingDetail)
	s.Require().Len(rf1.CoveragePricingDetail, 1, "Should have only 1 coverage after partial update (complete overwrite behavior)")

	// Track which coverages we found for factor 1 after partial update
	finalCoverages := make(map[Coverage]int32)
	for _, pd := range rf1.CoveragePricingDetail {
		finalCoverages[pd.Coverage] = pd.Credit
	}

	// Verify only AUTO_LIABILITY is present with the updated value (complete overwrite behavior)
	s.Require().Equal(int32(-25), finalCoverages[Coverage_AUTO_LIABILITY], "AUTO_LIABILITY should be updated to -25")
	s.Require().NotContains(finalCoverages, Coverage_AUTO_PHYSICAL_DAMAGE, "AUTO_PHYSICAL_DAMAGE should be removed (complete overwrite)")
	s.Require().NotContains(finalCoverages, Coverage_GENERAL_LIABILITY, "GENERAL_LIABILITY should be removed (complete overwrite)")

	// Verify the second risk factor has the correct pricing details
	rf2 := riskFactors[factor2ID]
	s.Require().NotNil(rf2)
	s.Require().NotEmpty(rf2.CoveragePricingDetail)
	s.Require().Len(rf2.CoveragePricingDetail, 1)

	// Track which coverages we found for factor 2
	foundCoverages2 := make(map[Coverage]bool)
	for _, pd := range rf2.CoveragePricingDetail {
		foundCoverages2[pd.Coverage] = true
		switch pd.Coverage {
		case Coverage_MOTOR_TRUCK_CARGO:
			s.Equal(int32(8), pd.Credit)
		}
	}
	s.Require().True(foundCoverages2[Coverage_MOTOR_TRUCK_CARGO], "MOTOR_TRUCK_CARGO pricing not found")
}

// TestDeleteAndVerifyWorksheetRiskFactors tests deleting risk factors and verifying GetWorksheet reflects the changes
func (s *riskFactorTestSuite) TestDeleteAndVerifyWorksheetRiskFactors() {
	// Test with V2 pricing enabled
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Setup a worksheet with multiple risk factors
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	worksheetId := resp.WorksheetId

	// Verify it's a V2 worksheet
	worksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, worksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV2, worksheet.Version)

	riskFactorIds := s.riskFactorIds
	var worksheetRiskFactorIds []string

	// Add several risk factors to the worksheet
	for i := 0; i < 3; i++ {
		factorResp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
			WorksheetId:  worksheetId,
			RiskFactorId: riskFactorIds[i],
			Value:        "test value " + string(rune(i+48)),
			Sentiment:    Sentiment_POSITIVE,
		})
		s.Require().NoError(err)
		s.Require().NotNil(factorResp)
		worksheetRiskFactorIds = append(worksheetRiskFactorIds, factorResp.WorksheetRiskFactorId)
	}

	// Add pricing to the first risk factor
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, &UpdateWorksheetPricingRequest{
		WorksheetId:       worksheetId,
		WorksheetFactorId: worksheetRiskFactorIds[0],
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   -10, // Discount
			},
		},
	})
	s.Require().NoError(err)

	// Add pricing to the second risk factor
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, &UpdateWorksheetPricingRequest{
		WorksheetId:       worksheetId,
		WorksheetFactorId: worksheetRiskFactorIds[1],
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
				Credit:   5, // Surcharge
			},
		},
	})
	s.Require().NoError(err)

	// Add pricing to the third risk factor
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, &UpdateWorksheetPricingRequest{
		WorksheetId:       worksheetId,
		WorksheetFactorId: worksheetRiskFactorIds[2],
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_GENERAL_LIABILITY,
				Credit:   15, // Surcharge
			},
		},
	})
	s.Require().NoError(err)

	// Verify initial state - should have 3 risk factors with pricing
	initialWorksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: worksheetId,
	})
	s.Require().NoError(err)
	s.Require().Len(initialWorksheet.WorksheetRiskFactors, 3)

	// Verify each risk factor has the correct pricing
	for _, factor := range initialWorksheet.WorksheetRiskFactors {
		s.Require().NotEmpty(factor.CoveragePricingDetail, "Risk factor missing pricing: %s", factor.WorksheetRiskFactorId)

		// Check specific pricing values
		if factor.WorksheetRiskFactorId == worksheetRiskFactorIds[0] {
			s.Require().Len(factor.CoveragePricingDetail, 1)
			s.Require().Equal(Coverage_AUTO_LIABILITY, factor.CoveragePricingDetail[0].Coverage)
			s.Require().Equal(int32(-10), factor.CoveragePricingDetail[0].Credit)
		} else if factor.WorksheetRiskFactorId == worksheetRiskFactorIds[1] {
			s.Require().Len(factor.CoveragePricingDetail, 1)
			s.Require().Equal(Coverage_AUTO_PHYSICAL_DAMAGE, factor.CoveragePricingDetail[0].Coverage)
			s.Require().Equal(int32(5), factor.CoveragePricingDetail[0].Credit)
		} else if factor.WorksheetRiskFactorId == worksheetRiskFactorIds[2] {
			s.Require().Len(factor.CoveragePricingDetail, 1)
			s.Require().Equal(Coverage_GENERAL_LIABILITY, factor.CoveragePricingDetail[0].Coverage)
			s.Require().Equal(int32(15), factor.CoveragePricingDetail[0].Credit)
		}
	}

	// Delete the second risk factor
	_, err = s.deps.Client.DeleteWorksheetRiskFactor(s.suiteCtx, &DeleteRiskFactorRequest{
		WorksheetRiskFactorId: worksheetRiskFactorIds[1],
	})
	s.Require().NoError(err)

	// Verify updated state - should have 2 risk factors
	updatedWorksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: worksheetId,
	})
	s.Require().NoError(err)
	s.Require().Len(updatedWorksheet.WorksheetRiskFactors, 2)

	// Verify remaining risk factors and their pricing data
	var foundIds []string
	for _, rf := range updatedWorksheet.WorksheetRiskFactors {
		foundIds = append(foundIds, rf.WorksheetRiskFactorId)

		if rf.WorksheetRiskFactorId == worksheetRiskFactorIds[0] {
			// First risk factor should still have its pricing data
			s.Require().Len(rf.CoveragePricingDetail, 1)
			s.Require().Equal(Coverage_AUTO_LIABILITY, rf.CoveragePricingDetail[0].Coverage)
			s.Require().Equal(int32(-10), rf.CoveragePricingDetail[0].Credit)
		} else if rf.WorksheetRiskFactorId == worksheetRiskFactorIds[2] {
			// Third risk factor should still have its pricing data
			s.Require().Len(rf.CoveragePricingDetail, 1)
			s.Require().Equal(Coverage_GENERAL_LIABILITY, rf.CoveragePricingDetail[0].Coverage)
			s.Require().Equal(int32(15), rf.CoveragePricingDetail[0].Credit)
		}
	}

	s.Require().Contains(foundIds, worksheetRiskFactorIds[0])
	s.Require().Contains(foundIds, worksheetRiskFactorIds[2])
	s.Require().NotContains(foundIds, worksheetRiskFactorIds[1])

	// Now update pricing for the remaining risk factors
	_, err = s.deps.Client.UpdateWorksheetPricing(s.suiteCtx, &UpdateWorksheetPricingRequest{
		WorksheetId:       worksheetId,
		WorksheetFactorId: worksheetRiskFactorIds[0],
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   -20, // Increased discount
			},
		},
	})
	s.Require().NoError(err)

	// Delete all remaining risk factors
	for _, id := range []string{worksheetRiskFactorIds[0], worksheetRiskFactorIds[2]} {
		_, err = s.deps.Client.DeleteWorksheetRiskFactor(s.suiteCtx, &DeleteRiskFactorRequest{
			WorksheetRiskFactorId: id,
		})
		s.Require().NoError(err)
	}

	// Verify final state - should have 0 risk factors
	finalWorksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: worksheetId,
	})
	s.Require().NoError(err)
	s.Require().Empty(finalWorksheet.WorksheetRiskFactors)

	// Reset the feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

// TestInsertOrUpdateRiskFactorV1 verifies that the InsertOrUpdateRiskFactor method
// works correctly for V1 worksheets, creating or updating risk factors and pricing
func (s *riskFactorTestSuite) TestInsertOrUpdateRiskFactorV1() {
	// Ensure feature flag is set to V1
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))

	// Create a new worksheet
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Verify it's a V1 worksheet
	worksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, resp.WorksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV1, worksheet.Version)

	// Use InsertOrUpdateRiskFactor to create a new risk factor
	createResp, err := s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
		ReviewId:        s.appReviewId,
		RiskFactorLabel: RiskFactorLabel_Telematics_Risk_Score,
		Value:           "Test Value",
		Notes:           "Test Notes",
		Sentiment:       Sentiment_POSITIVE,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   15,
			},
			{
				Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
				Credit:   10,
			},
		},
	})
	s.Require().NoError(err)
	s.Require().NotNil(createResp)

	// Verify the risk factor was created by getting the worksheet
	worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(worksheetResp)
	// Should have one risk factor
	s.Require().Equal(1, len(worksheetResp.WorksheetRiskFactors))
	s.Require().Equal("Test Value", worksheetResp.WorksheetRiskFactors[0].Value)
	s.Require().Equal("Test Notes", worksheetResp.WorksheetRiskFactors[0].Notes)
	s.Require().Equal(Sentiment_POSITIVE, worksheetResp.WorksheetRiskFactors[0].Sentiment)

	// Pricing should have been created
	s.Require().NotNil(worksheetResp.PricingDetails)

	// Now update the existing risk factor using InsertOrUpdateRiskFactor
	updateResp, err := s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
		ReviewId:        s.appReviewId,
		Value:           "Updated Value",
		Notes:           "Updated Notes",
		Sentiment:       Sentiment_NEGATIVE,
		RiskFactorLabel: RiskFactorLabel_Telematics_Risk_Score,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   -10, // Changed to negative to match sentiment
			},
			{
				Coverage: Coverage_AUTO_PHYSICAL_DAMAGE,
				Credit:   -5, // Changed to negative to match sentiment
			},
		},
	})
	s.Require().NoError(err)
	s.Require().NotNil(updateResp)

	// Verify the risk factor was updated
	updatedWorksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(updatedWorksheetResp)

	// Should still have one risk factor
	s.Require().Equal(1, len(updatedWorksheetResp.WorksheetRiskFactors))
	s.Require().Equal("Updated Value", updatedWorksheetResp.WorksheetRiskFactors[0].Value)
	s.Require().Equal("Updated Notes", updatedWorksheetResp.WorksheetRiskFactors[0].Notes)
	s.Require().Equal(Sentiment_NEGATIVE, updatedWorksheetResp.WorksheetRiskFactors[0].Sentiment)

	// Pricing should have been updated
	s.Require().NotNil(updatedWorksheetResp.PricingDetails)

	// Check for correct category pricing in the worksheet
	var foundCategory bool
	for _, category := range updatedWorksheetResp.PricingDetails.CategoryPricingDetails {
		if category.Category == Category_SAFETY {
			foundCategory = true
			// Check the pricing adjustments
			s.Require().NotEmpty(category.CoveragePricingDetail)

			var foundAL, foundAPD bool
			for _, coverage := range category.CoveragePricingDetail {
				if coverage.Coverage == Coverage_AUTO_LIABILITY {
					foundAL = true
					s.Require().Equal(int32(-10), coverage.Credit)
				} else if coverage.Coverage == Coverage_AUTO_PHYSICAL_DAMAGE {
					foundAPD = true
					s.Require().Equal(int32(-5), coverage.Credit)
				}
			}
			s.Require().True(foundAL, "AUTO_LIABILITY pricing not found")
			s.Require().True(foundAPD, "AUTO_PHYSICAL_DAMAGE pricing not found")
		}
	}
	s.Require().True(foundCategory, "Category DRIVERS pricing not found")
}

// TestInsertOrUpdateRiskFactorV2 verifies that the InsertOrUpdateRiskFactor method
// works correctly for V2 worksheets, creating or updating risk factors and individual pricing
func (s *riskFactorTestSuite) TestInsertOrUpdateRiskFactorV2() {
	// Enable V2 pricing
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))

	// Create a new V2 worksheet
	resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
		ReviewId: s.appReviewId,
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.WorksheetId)

	// Verify it's a V2 worksheet
	worksheet, err := s.deps.RiskFactorWorksheetWrapper.GetRiskWorksheet(s.suiteCtx, resp.WorksheetId)
	s.Require().NoError(err)
	s.Require().Equal(PricingVersionV2, worksheet.Version)

	// Use InsertOrUpdateRiskFactor to create a new risk factor with pricing
	createResp, err := s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
		ReviewId:        s.appReviewId,
		RiskFactorLabel: RiskFactorLabel_Telematics_Risk_Score,
		Value:           "V2 Test Value",
		Notes:           "V2 Test Notes",
		Sentiment:       Sentiment_POSITIVE,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_MOTOR_TRUCK_CARGO,
				Credit:   20,
			},
			{
				Coverage: Coverage_GENERAL_LIABILITY,
				Credit:   15,
			},
		},
	})
	s.Require().NoError(err)
	s.Require().NotNil(createResp)

	// Verify the risk factor was created by getting the worksheet
	worksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(worksheetResp)
	s.Require().Equal(int32(PricingVersionV2), worksheetResp.Version)

	// Should have one risk factor
	s.Require().Equal(1, len(worksheetResp.WorksheetRiskFactors))
	s.Require().Equal("V2 Test Value", worksheetResp.WorksheetRiskFactors[0].Value)
	s.Require().Equal("V2 Test Notes", worksheetResp.WorksheetRiskFactors[0].Notes)
	s.Require().Equal(Sentiment_POSITIVE, worksheetResp.WorksheetRiskFactors[0].Sentiment)

	// In V2, the risk factor should have its own pricing details
	s.Require().NotEmpty(worksheetResp.WorksheetRiskFactors[0].CoveragePricingDetail)

	// Find the ID of the created risk factor for update
	createdFactorId := worksheetResp.WorksheetRiskFactors[0].WorksheetRiskFactorId

	// Create a second risk factor to verify multiple factors work
	secondFactorResp, err := s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
		ReviewId:        s.appReviewId,
		RiskFactorLabel: RiskFactorLabel_M1_Score,
		Value:           "Second V2 Factor",
		Notes:           "Second V2 Notes",
		Sentiment:       Sentiment_NEGATIVE,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   -25,
			},
		},
	})
	s.Require().NoError(err)
	s.Require().NotNil(secondFactorResp)

	// Now update the first risk factor
	updateResp, err := s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
		ReviewId:        s.appReviewId,
		RiskFactorLabel: RiskFactorLabel_Telematics_Risk_Score,
		Value:           "Updated V2 Value",
		Notes:           "Updated V2 Notes",
		Sentiment:       Sentiment_POSITIVE,
		CoveragePricingDetail: []*CoveragePricingDetail{
			{
				Coverage: Coverage_MOTOR_TRUCK_CARGO,
				Credit:   25, // Increased
			},
			{
				Coverage: Coverage_GENERAL_LIABILITY,
				Credit:   18, // Increased
			},
			{
				Coverage: Coverage_AUTO_LIABILITY,
				Credit:   10, // Added new coverage
			},
		},
	})
	s.Require().NoError(err)
	s.Require().NotNil(updateResp)

	// Get the worksheet again to verify updates
	updatedWorksheetResp, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
		WorksheetId: resp.WorksheetId,
	})
	s.Require().NoError(err)
	s.Require().NotNil(updatedWorksheetResp)

	// Should now have two risk factors
	s.Require().Equal(2, len(updatedWorksheetResp.WorksheetRiskFactors))

	// Verify first risk factor was updated
	var firstFactorUpdated, secondFactorFound bool
	for _, factor := range updatedWorksheetResp.WorksheetRiskFactors {
		if factor.WorksheetRiskFactorId == createdFactorId {
			firstFactorUpdated = true
			s.Require().Equal("Updated V2 Value", factor.Value)
			s.Require().Equal("Updated V2 Notes", factor.Notes)
			s.Require().Equal(Sentiment_POSITIVE, factor.Sentiment)

			// Check that pricing details were updated correctly
			s.Require().NotEmpty(factor.CoveragePricingDetail)

			var foundMTC, foundGL, foundAL bool
			for _, coverage := range factor.CoveragePricingDetail {
				if coverage.Coverage == Coverage_MOTOR_TRUCK_CARGO {
					foundMTC = true
					s.Require().Equal(int32(25), coverage.Credit)
				} else if coverage.Coverage == Coverage_GENERAL_LIABILITY {
					foundGL = true
					s.Require().Equal(int32(18), coverage.Credit)
				} else if coverage.Coverage == Coverage_AUTO_LIABILITY {
					foundAL = true
					s.Require().Equal(int32(10), coverage.Credit)
				}
			}
			s.Require().True(foundMTC, "MOTOR_TRUCK_CARGO pricing not found")
			s.Require().True(foundGL, "GENERAL_LIABILITY pricing not found")
			s.Require().True(foundAL, "AUTO_LIABILITY pricing not found")
		} else {
			secondFactorFound = true
			s.Require().Equal("Second V2 Factor", factor.Value)
			s.Require().Equal("Second V2 Notes", factor.Notes)
			s.Require().Equal(Sentiment_NEGATIVE, factor.Sentiment)

			// Verify second factor's pricing
			s.Require().NotEmpty(factor.CoveragePricingDetail)
			s.Require().Equal(Coverage_AUTO_LIABILITY, factor.CoveragePricingDetail[0].Coverage)
			s.Require().Equal(int32(-25), factor.CoveragePricingDetail[0].Credit)
		}
	}
	s.Require().True(firstFactorUpdated, "First risk factor was not updated correctly")
	s.Require().True(secondFactorFound, "Second risk factor was not found")

	// Reset feature flag
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))
}

// TestGetAllWorksheetFactorWithTag tests the GetAllWorksheetFactorWithTag method
func (s *riskFactorTestSuite) TestGetAllWorksheetFactorWithTag() {
	// Setup a worksheet with multiple risk factors that have different tags
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotNil(worksheetId)

	// Create risk factors with different tag combinations for testing
	testRiskFactors := []struct {
		name     string
		label    string
		category risk_factors.RiskFactorsCategory
		tags     []string
	}{
		{
			name:     "Safety Telemetry Factor",
			label:    "Safety Telemetry Factor",
			category: risk_factors.RiskFactorsCategorySafety,
			tags:     []string{"safety", "telemetry"},
		},
		{
			name:     "Financial Analytics Factor",
			label:    "Financial Analytics Factor",
			category: risk_factors.RiskFactorsCategoryFinancials,
			tags:     []string{"finance", "analytics"},
		},
		{
			name:     "Driver Safety Factor",
			label:    "Driver Safety Factor",
			category: risk_factors.RiskFactorsCategoryDrivers,
			tags:     []string{"safety", "driver"},
		},
		{
			name:     "Equipment Maintenance Factor",
			label:    "Equipment Maintenance Factor",
			category: risk_factors.RiskFactorsCategoryEquipments,
			tags:     []string{"equipment", "maintenance"},
		},
		{
			name:     "Operations Analytics Factor",
			label:    "Operations Analytics Factor",
			category: risk_factors.RiskFactorsCategoryOperations,
			tags:     []string{"operations", "analytics"},
		},
		{
			name:     "No Tags Factor",
			label:    "No Tags Factor",
			category: risk_factors.RiskFactorsCategoryLosses,
			tags:     []string{}, // Empty tags
		},
	}

	// Create the risk factors and add them to worksheet
	var createdFactorIds []string
	for i, tf := range testRiskFactors {
		// Create the risk factor with tags
		riskFactor := risk_factors.RiskFactor{
			Id:            uuid.New(),
			Name:          tf.name,
			Label:         tf.label,
			Category:      tf.category,
			Description:   "Test risk factor for tag filtering",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          tf.tags,
		}

		err := s.deps.RiskFactorWrapper.InsertRiskFactor(s.suiteCtx, riskFactor)
		s.Require().NoError(err)
		createdFactorIds = append(createdFactorIds, riskFactor.Id.String())

		// Add to worksheet
		_, err = s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
			WorksheetId:  *worksheetId,
			RiskFactorId: riskFactor.Id.String(),
			Value:        "test value " + string(rune(i+48)),
			Sentiment:    Sentiment_POSITIVE,
		})
		s.Require().NoError(err)
	}

	// Test various tag filter scenarios
	tests := []struct {
		name           string
		request        *GetAllWorksheetFactorWithTagRequest
		expectedCount  int
		expectedLabels []string
		expectedError  error
	}{
		{
			name: "filter_by_safety_tag",
			request: &GetAllWorksheetFactorWithTagRequest{
				WorksheetId: *worksheetId,
				Tags:        []string{"safety"},
			},
			expectedCount:  2,
			expectedLabels: []string{"Safety Telemetry Factor", "Driver Safety Factor"},
		},
		{
			name: "filter_by_analytics_tag",
			request: &GetAllWorksheetFactorWithTagRequest{
				WorksheetId: *worksheetId,
				Tags:        []string{"analytics"},
			},
			expectedCount:  2,
			expectedLabels: []string{"Financial Analytics Factor", "Operations Analytics Factor"},
		},
		{
			name: "filter_by_telemetry_tag",
			request: &GetAllWorksheetFactorWithTagRequest{
				WorksheetId: *worksheetId,
				Tags:        []string{"telemetry"},
			},
			expectedCount:  1,
			expectedLabels: []string{"Safety Telemetry Factor"},
		},
		{
			name: "filter_by_multiple_tags_or_logic",
			request: &GetAllWorksheetFactorWithTagRequest{
				WorksheetId: *worksheetId,
				Tags:        []string{"finance", "equipment"},
			},
			expectedCount:  2,
			expectedLabels: []string{"Financial Analytics Factor", "Equipment Maintenance Factor"},
		},
		{
			name: "filter_by_non_existent_tag",
			request: &GetAllWorksheetFactorWithTagRequest{
				WorksheetId: *worksheetId,
				Tags:        []string{"nonexistent"},
			},
			expectedCount:  0,
			expectedLabels: []string{},
		},
		{
			name: "filter_by_empty_tags_returns_all",
			request: &GetAllWorksheetFactorWithTagRequest{
				WorksheetId: *worksheetId,
				Tags:        []string{},
			},
			expectedCount: 6, // All factors should be returned
		},
		{
			name: "case_insensitive_matching",
			request: &GetAllWorksheetFactorWithTagRequest{
				WorksheetId: *worksheetId,
				Tags:        []string{"SAFETY", "Finance"},
			},
			expectedCount:  3,
			expectedLabels: []string{"Safety Telemetry Factor", "Driver Safety Factor", "Financial Analytics Factor"},
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			resp, err := s.deps.Client.GetAllWorksheetFactorWithTag(s.suiteCtx, tt.request)

			if tt.expectedError != nil {
				s.Require().Error(err)
				s.Equal(tt.expectedError.Error(), err.Error())
				s.Nil(resp)
				return
			}

			s.Require().NoError(err)
			s.Require().NotNil(resp)
			s.Require().Len(resp.WorksheetRiskFactors, tt.expectedCount)

			if len(tt.expectedLabels) > 0 {
				// Extract the labels from results and verify they match expected
				var foundLabels []string
				for _, result := range resp.WorksheetRiskFactors {
					foundLabels = append(foundLabels, result.RiskFactor.Name)
				}

				// Check that all expected labels are found
				for _, expectedLabel := range tt.expectedLabels {
					s.Require().Contains(foundLabels, expectedLabel, "Expected label %s not found in results", expectedLabel)
				}
			}

			// Verify each returned risk factor has the expected structure
			for _, rf := range resp.WorksheetRiskFactors {
				s.Require().NotEmpty(rf.WorksheetRiskFactorId)
				s.Require().NotNil(rf.RiskFactor)
				s.Require().NotEmpty(rf.RiskFactor.Id)
				s.Require().NotEmpty(rf.RiskFactor.Name)
				s.Require().NotEmpty(rf.Value)
				s.Require().Equal(Sentiment_POSITIVE, rf.Sentiment)

				// If this is a test with specific tags, verify the risk factor has the expected tags
				if len(tt.request.Tags) > 0 && tt.expectedCount > 0 {
					// Find the corresponding test risk factor to verify tags match
					for _, testFactor := range testRiskFactors {
						if testFactor.name == rf.RiskFactor.Name {
							s.Require().Equal(testFactor.tags, rf.RiskFactor.Tags,
								"Tags should match for risk factor %s", rf.RiskFactor.Name)
							break
						}
					}
				}
			}
		})
	}
}

// TestGetAllWorksheetFactorWithTagArchived tests that archived risk factors are not returned
func (s *riskFactorTestSuite) TestGetAllWorksheetFactorWithTagArchived() {
	// Setup a worksheet
	worksheetId, err := s.setupRiskWorksheet()
	s.Require().NoError(err)
	s.Require().NotNil(worksheetId)

	// Create a risk factor with tags
	riskFactor := risk_factors.RiskFactor{
		Id:            uuid.New(),
		Name:          "Test Archived Factor",
		Label:         "Test Archived Factor",
		Category:      risk_factors.RiskFactorsCategorySafety,
		Description:   "Test risk factor to be archived",
		PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
		State:         risk_factors.RiskFactorsStateActive,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
		Version:       1,
		IsLatest:      true,
		CreatedBy:     uuid.New(),
		LastUpdatedBy: uuid.New(),
		Tags:          []string{"test", "archive"},
	}

	err = s.deps.RiskFactorWrapper.InsertRiskFactor(s.suiteCtx, riskFactor)
	s.Require().NoError(err)

	// Add to worksheet
	worksheetFactorResp, err := s.deps.Client.AddWorksheetRiskFactor(s.suiteCtx, &AddWorksheetRiskFactorRequest{
		WorksheetId:  *worksheetId,
		RiskFactorId: riskFactor.Id.String(),
		Value:        "test value",
		Sentiment:    Sentiment_POSITIVE,
	})
	s.Require().NoError(err)

	// Verify it's initially found by tag
	resp, err := s.deps.Client.GetAllWorksheetFactorWithTag(s.suiteCtx, &GetAllWorksheetFactorWithTagRequest{
		WorksheetId: *worksheetId,
		Tags:        []string{"test"},
	})
	s.Require().NoError(err)
	s.Require().Len(resp.WorksheetRiskFactors, 1)

	// Delete/archive the worksheet risk factor
	_, err = s.deps.Client.DeleteWorksheetRiskFactor(s.suiteCtx, &DeleteRiskFactorRequest{
		WorksheetRiskFactorId: worksheetFactorResp.WorksheetRiskFactorId,
	})
	s.Require().NoError(err)

	// Now it should not be found when filtering by tags (only active factors)
	resp, err = s.deps.Client.GetAllWorksheetFactorWithTag(s.suiteCtx, &GetAllWorksheetFactorWithTagRequest{
		WorksheetId: *worksheetId,
		Tags:        []string{"test"},
	})
	s.Require().NoError(err)
	s.Require().Empty(resp.WorksheetRiskFactors, "Archived worksheet factors should not be returned")
}

// TestSystemGeneratedTagsField tests that the is_system_generated field is set correctly based on tags
func (s *riskFactorTestSuite) TestSystemGeneratedTagsField() {
	// Create risk factors with and without system-generated tags
	testRiskFactors := []struct {
		name              string
		label             string
		tags              []string
		expectedSystemGen bool
	}{
		{
			name:              "Manual Risk Factor",
			label:             "Manual Risk Factor",
			tags:              []string{"user-defined", "manual"},
			expectedSystemGen: false,
		},
		{
			name:              "System Generated Factor",
			label:             "System Generated Factor",
			tags:              []string{"automated", "system-generated"},
			expectedSystemGen: true,
		},
		{
			name:              "Mixed Tags Factor",
			label:             "Mixed Tags Factor",
			tags:              []string{"user-defined", "system-generated", "analytics"},
			expectedSystemGen: true,
		},
		{
			name:              "No Tags Factor",
			label:             "No Tags Factor",
			tags:              []string{},
			expectedSystemGen: false,
		},
	}

	// Create the risk factors
	var createdFactorIds []string
	for _, tf := range testRiskFactors {
		riskFactor := risk_factors.RiskFactor{
			Id:            uuid.New(),
			Name:          tf.name,
			Label:         tf.label,
			Category:      risk_factors.RiskFactorsCategorySafety,
			Description:   "Test risk factor for system-generated testing",
			PricingType:   risk_factors.RiskFactorsPricingTypeManuallyPriced,
			State:         risk_factors.RiskFactorsStateActive,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			Version:       1,
			IsLatest:      true,
			CreatedBy:     uuid.New(),
			LastUpdatedBy: uuid.New(),
			Tags:          tf.tags,
		}

		err := s.deps.RiskFactorWrapper.InsertRiskFactor(s.suiteCtx, riskFactor)
		s.Require().NoError(err)
		createdFactorIds = append(createdFactorIds, riskFactor.Id.String())
	}

	// Test via ListRiskFactors
	resp, err := s.deps.Client.ListRiskFactors(s.suiteCtx, &emptypb.Empty{})
	s.Require().NoError(err)
	s.Require().NotNil(resp)

	// Find our test factors and verify is_system_generated is set correctly
	foundFactors := make(map[string]*RiskFactor)
	for _, rf := range resp.RiskFactors {
		for _, createdId := range createdFactorIds {
			if rf.Id == createdId {
				foundFactors[rf.Name] = rf
				break
			}
		}
	}

	// Verify each test factor has correct is_system_generated value
	for _, tf := range testRiskFactors {
		rf, found := foundFactors[tf.name]
		s.Require().True(found, "Risk factor %s should be found in response", tf.name)
		s.Require().Equal(tf.expectedSystemGen, rf.IsSystemGenerated,
			"Risk factor %s should have is_system_generated=%v", tf.name, tf.expectedSystemGen)
	}
}

// TestDeleteRiskFactorByLabel tests the DeleteRiskFactorByLabel method with V2 worksheets
func (s *riskFactorTestSuite) TestDeleteRiskFactorByLabel() {
	// Ensure V2 is enabled for all tests
	s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(true))
	defer s.deps.FeatureFlag.SetValue(feature_flag_lib.FeatureRiskFactorPricingV2, ldvalue.Bool(false))

	testCases := []struct {
		name         string
		setupFunc    func() (*string, *string) // returns worksheetId, riskFactorId
		label        RiskFactorLabel
		reviewId     string
		expectError  bool
		expectedCode codes.Code
		expectedMsg  string
		verifyFunc   func(worksheetId, riskFactorId string)
	}{
		{
			name: "Successfully delete risk factor by label in V2 worksheet",
			setupFunc: func() (*string, *string) {
				// Create worksheet
				resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
					ReviewId: s.appReviewId,
				})
				s.Require().NoError(err)
				worksheetId := resp.WorksheetId

				// Add risk factor with known label and V2 pricing
				_, err = s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
					ReviewId:        s.appReviewId,
					RiskFactorLabel: RiskFactorLabel_Telematics_Risk_Score,
					Value:           "Test Value",
					Notes:           "Test Notes",
					Sentiment:       Sentiment_POSITIVE,
					CoveragePricingDetail: []*CoveragePricingDetail{
						{Coverage: Coverage_AUTO_LIABILITY, Credit: 10},
						{Coverage: Coverage_GENERAL_LIABILITY, Credit: 5},
					},
				})
				s.Require().NoError(err)

				// Get the risk factor ID for verification
				worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
					WorksheetId: worksheetId,
				})
				s.Require().NoError(err)
				s.Require().Len(worksheet.WorksheetRiskFactors, 1)
				s.Require().Equal(int32(2), worksheet.Version, "Should be V2 worksheet")
				s.Require().NotEmpty(worksheet.WorksheetRiskFactors[0].CoveragePricingDetail, "V2 factor should have pricing")

				riskFactorId := worksheet.WorksheetRiskFactors[0].WorksheetRiskFactorId
				return &worksheetId, &riskFactorId
			},
			label:       RiskFactorLabel_Telematics_Risk_Score,
			reviewId:    "", // will be set to s.appReviewId
			expectError: false,
			verifyFunc: func(worksheetId, riskFactorId string) {
				// Verify risk factor is archived (not in active list)
				worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
					WorksheetId: worksheetId,
				})
				s.Require().NoError(err)
				s.Require().Len(worksheet.WorksheetRiskFactors, 0, "Risk factor should be archived")
			},
		},
		{
			name: "Empty review ID returns error",
			setupFunc: func() (*string, *string) {
				return nil, nil
			},
			label:        RiskFactorLabel_Telematics_Risk_Score,
			reviewId:     "",
			expectError:  true,
			expectedCode: codes.InvalidArgument,
			expectedMsg:  "review ID is required",
		},
		{
			name: "Unspecified risk factor label returns error",
			setupFunc: func() (*string, *string) {
				return nil, nil
			},
			label:        RiskFactorLabel_UNSPECIFIED_RISK_FACTOR_LABEL,
			reviewId:     "", // will be set to s.appReviewId
			expectError:  true,
			expectedCode: codes.InvalidArgument,
			expectedMsg:  "risk factor label is required",
		},
		{
			name: "Non-existent review ID returns not found",
			setupFunc: func() (*string, *string) {
				return nil, nil
			},
			label:        RiskFactorLabel_Telematics_Risk_Score,
			reviewId:     uuid.New().String(),
			expectError:  true,
			expectedCode: codes.NotFound,
			expectedMsg:  "no worksheet found for the given review",
		},
		{
			name: "Risk factor not in worksheet returns not found",
			setupFunc: func() (*string, *string) {
				// Create worksheet without any risk factors
				resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
					ReviewId: s.appReviewId,
				})
				s.Require().NoError(err)
				return &resp.WorksheetId, nil
			},
			label:        RiskFactorLabel_M1_Score, // Different label that wasn't added
			reviewId:     "",                       // will be set to s.appReviewId
			expectError:  true,
			expectedCode: codes.NotFound,
			expectedMsg:  "risk factor not found in worksheet",
		},
		{
			name: "Delete already archived risk factor still succeeds",
			setupFunc: func() (*string, *string) {
				// Create worksheet
				resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
					ReviewId: s.appReviewId,
				})
				s.Require().NoError(err)
				worksheetId := resp.WorksheetId

				// Add risk factor with V2 pricing
				_, err = s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
					ReviewId:        s.appReviewId,
					RiskFactorLabel: RiskFactorLabel_Fleet_Size_Adjustment,
					Value:           "Test Value",
					Sentiment:       Sentiment_NEUTRAL,
					CoveragePricingDetail: []*CoveragePricingDetail{
						{Coverage: Coverage_AUTO_LIABILITY, Credit: 0},
					},
				})
				s.Require().NoError(err)

				// Get the risk factor to delete it traditionally first
				worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
					WorksheetId: worksheetId,
				})
				s.Require().NoError(err)
				s.Require().Len(worksheet.WorksheetRiskFactors, 1)

				// Delete it using the regular method first
				_, err = s.deps.Client.DeleteWorksheetRiskFactor(s.suiteCtx, &DeleteRiskFactorRequest{
					WorksheetRiskFactorId: worksheet.WorksheetRiskFactors[0].WorksheetRiskFactorId,
				})
				s.Require().NoError(err)

				return &worksheetId, &worksheet.WorksheetRiskFactors[0].WorksheetRiskFactorId
			},
			label:        RiskFactorLabel_Fleet_Size_Adjustment,
			reviewId:     "", // will be set to s.appReviewId
			expectError:  true,
			expectedCode: codes.NotFound,
			expectedMsg:  "risk factor not found in worksheet",
		},
		{
			name: "Delete risk factor with multiple coverage pricing in V2",
			setupFunc: func() (*string, *string) {
				// Create V2 worksheet
				resp, err := s.deps.Client.CreateWorksheet(s.suiteCtx, &CreateWorksheetRequest{
					ReviewId: s.appReviewId,
				})
				s.Require().NoError(err)
				worksheetId := resp.WorksheetId

				_, err = s.deps.Client.InsertOrUpdateRiskFactor(s.suiteCtx, &InsertOrUpdateRiskFactorRequest{
					ReviewId:        s.appReviewId,
					RiskFactorLabel: RiskFactorLabel_Driver_Turnover_Adjustment,
					Value:           "V2 Test",
					Sentiment:       Sentiment_NEGATIVE,
					CoveragePricingDetail: []*CoveragePricingDetail{
						{Coverage: Coverage_AUTO_LIABILITY, Credit: -15},
						{Coverage: Coverage_MOTOR_TRUCK_CARGO, Credit: -10},
						{Coverage: Coverage_AUTO_PHYSICAL_DAMAGE, Credit: -5},
					},
				})
				s.Require().NoError(err)

				worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
					WorksheetId: worksheetId,
				})
				s.Require().NoError(err)
				s.Require().Equal(int32(2), worksheet.Version, "Should be V2 worksheet")
				riskFactorId := worksheet.WorksheetRiskFactors[0].WorksheetRiskFactorId

				return &worksheetId, &riskFactorId
			},
			label:       RiskFactorLabel_Driver_Turnover_Adjustment,
			reviewId:    "", // will be set to s.appReviewId
			expectError: false,
			verifyFunc: func(worksheetId, riskFactorId string) {
				worksheet, err := s.deps.Client.GetWorksheet(s.suiteCtx, &GetWorksheetRequest{
					WorksheetId: worksheetId,
				})
				s.Require().NoError(err)
				s.Require().Len(worksheet.WorksheetRiskFactors, 0, "V2 risk factor should be archived")
			},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			var worksheetId, riskFactorId *string
			if tc.setupFunc != nil {
				worksheetId, riskFactorId = tc.setupFunc()
			}

			// Set review ID if not explicitly set
			reviewId := tc.reviewId
			if reviewId == "" && tc.expectedMsg != "review ID is required" {
				reviewId = s.appReviewId
			}

			// Execute the delete operation
			_, err := s.deps.Client.DeleteRiskFactorByLabel(s.suiteCtx, &DeleteRiskFactorByLabelRequest{
				ReviewId:        reviewId,
				RiskFactorLabel: tc.label,
			})

			if tc.expectError {
				s.Require().Error(err)
				st, ok := status.FromError(err)
				s.Require().True(ok, "Expected gRPC status error")
				s.Require().Equal(tc.expectedCode, st.Code(), "Expected error code %v but got %v", tc.expectedCode, st.Code())
				s.Require().Contains(st.Message(), tc.expectedMsg, "Error message should contain expected text")
			} else {
				s.Require().NoError(err)
				if tc.verifyFunc != nil && worksheetId != nil {
					tc.verifyFunc(*worksheetId, *riskFactorId)
				}
			}
		})
	}
}
