// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: risk_factors/risk_factors.proto

package risk_factors

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Sentiment int32

const (
	Sentiment_SENTIMENT_UNSPECIFIED Sentiment = 0
	Sentiment_POSITIVE              Sentiment = 1
	Sentiment_NEGATIVE              Sentiment = 2
	Sentiment_NEUTRAL               Sentiment = 3
)

// Enum value maps for Sentiment.
var (
	Sentiment_name = map[int32]string{
		0: "SENTIMENT_UNSPECIFIED",
		1: "POSITIVE",
		2: "NEGATIVE",
		3: "NEUTRAL",
	}
	Sentiment_value = map[string]int32{
		"SENTIMENT_UNSPECIFIED": 0,
		"POSITIVE":              1,
		"NEGATIVE":              2,
		"NEUTRAL":               3,
	}
)

func (x Sentiment) Enum() *Sentiment {
	p := new(Sentiment)
	*p = x
	return p
}

func (x Sentiment) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Sentiment) Descriptor() protoreflect.EnumDescriptor {
	return file_risk_factors_risk_factors_proto_enumTypes[0].Descriptor()
}

func (Sentiment) Type() protoreflect.EnumType {
	return &file_risk_factors_risk_factors_proto_enumTypes[0]
}

func (x Sentiment) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Sentiment.Descriptor instead.
func (Sentiment) EnumDescriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{0}
}

type Coverage int32

const (
	Coverage_UNSPECIFIED_COVERAGE Coverage = 0
	Coverage_AUTO_LIABILITY       Coverage = 1
	Coverage_GENERAL_LIABILITY    Coverage = 2
	Coverage_AUTO_PHYSICAL_DAMAGE Coverage = 3
	Coverage_MOTOR_TRUCK_CARGO    Coverage = 4
)

// Enum value maps for Coverage.
var (
	Coverage_name = map[int32]string{
		0: "UNSPECIFIED_COVERAGE",
		1: "AUTO_LIABILITY",
		2: "GENERAL_LIABILITY",
		3: "AUTO_PHYSICAL_DAMAGE",
		4: "MOTOR_TRUCK_CARGO",
	}
	Coverage_value = map[string]int32{
		"UNSPECIFIED_COVERAGE": 0,
		"AUTO_LIABILITY":       1,
		"GENERAL_LIABILITY":    2,
		"AUTO_PHYSICAL_DAMAGE": 3,
		"MOTOR_TRUCK_CARGO":    4,
	}
)

func (x Coverage) Enum() *Coverage {
	p := new(Coverage)
	*p = x
	return p
}

func (x Coverage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Coverage) Descriptor() protoreflect.EnumDescriptor {
	return file_risk_factors_risk_factors_proto_enumTypes[1].Descriptor()
}

func (Coverage) Type() protoreflect.EnumType {
	return &file_risk_factors_risk_factors_proto_enumTypes[1]
}

func (x Coverage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Coverage.Descriptor instead.
func (Coverage) EnumDescriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{1}
}

type WorksheetState int32

const (
	WorksheetState_UNSPECIFIED_WORKSHEET_STATE WorksheetState = 0
	WorksheetState_WORKSHEET_STATE_APPROVED    WorksheetState = 1
	WorksheetState_WORKSHEET_STATE_ACTIVE      WorksheetState = 2
	WorksheetState_WORKSHEET_STATE_STALE       WorksheetState = 3
)

// Enum value maps for WorksheetState.
var (
	WorksheetState_name = map[int32]string{
		0: "UNSPECIFIED_WORKSHEET_STATE",
		1: "WORKSHEET_STATE_APPROVED",
		2: "WORKSHEET_STATE_ACTIVE",
		3: "WORKSHEET_STATE_STALE",
	}
	WorksheetState_value = map[string]int32{
		"UNSPECIFIED_WORKSHEET_STATE": 0,
		"WORKSHEET_STATE_APPROVED":    1,
		"WORKSHEET_STATE_ACTIVE":      2,
		"WORKSHEET_STATE_STALE":       3,
	}
)

func (x WorksheetState) Enum() *WorksheetState {
	p := new(WorksheetState)
	*p = x
	return p
}

func (x WorksheetState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorksheetState) Descriptor() protoreflect.EnumDescriptor {
	return file_risk_factors_risk_factors_proto_enumTypes[2].Descriptor()
}

func (WorksheetState) Type() protoreflect.EnumType {
	return &file_risk_factors_risk_factors_proto_enumTypes[2]
}

func (x WorksheetState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorksheetState.Descriptor instead.
func (WorksheetState) EnumDescriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{2}
}

type RiskFactorState int32

const (
	RiskFactorState_UNSPECIFIED_RISK_FACTOR_STATE RiskFactorState = 0
	RiskFactorState_ACTIVE                        RiskFactorState = 1
	RiskFactorState_INACTIVE                      RiskFactorState = 2
	RiskFactorState_DRAFT                         RiskFactorState = 3
)

// Enum value maps for RiskFactorState.
var (
	RiskFactorState_name = map[int32]string{
		0: "UNSPECIFIED_RISK_FACTOR_STATE",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DRAFT",
	}
	RiskFactorState_value = map[string]int32{
		"UNSPECIFIED_RISK_FACTOR_STATE": 0,
		"ACTIVE":                        1,
		"INACTIVE":                      2,
		"DRAFT":                         3,
	}
)

func (x RiskFactorState) Enum() *RiskFactorState {
	p := new(RiskFactorState)
	*p = x
	return p
}

func (x RiskFactorState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskFactorState) Descriptor() protoreflect.EnumDescriptor {
	return file_risk_factors_risk_factors_proto_enumTypes[3].Descriptor()
}

func (RiskFactorState) Type() protoreflect.EnumType {
	return &file_risk_factors_risk_factors_proto_enumTypes[3]
}

func (x RiskFactorState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskFactorState.Descriptor instead.
func (RiskFactorState) EnumDescriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{3}
}

type PricingType int32

const (
	PricingType_UNSPECIFIED_PRICING_TYPE PricingType = 0
	PricingType_ALREADY_PRICED           PricingType = 1
	PricingType_MANUALLY_PRICED          PricingType = 2
)

// Enum value maps for PricingType.
var (
	PricingType_name = map[int32]string{
		0: "UNSPECIFIED_PRICING_TYPE",
		1: "ALREADY_PRICED",
		2: "MANUALLY_PRICED",
	}
	PricingType_value = map[string]int32{
		"UNSPECIFIED_PRICING_TYPE": 0,
		"ALREADY_PRICED":           1,
		"MANUALLY_PRICED":          2,
	}
)

func (x PricingType) Enum() *PricingType {
	p := new(PricingType)
	*p = x
	return p
}

func (x PricingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PricingType) Descriptor() protoreflect.EnumDescriptor {
	return file_risk_factors_risk_factors_proto_enumTypes[4].Descriptor()
}

func (PricingType) Type() protoreflect.EnumType {
	return &file_risk_factors_risk_factors_proto_enumTypes[4]
}

func (x PricingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PricingType.Descriptor instead.
func (PricingType) EnumDescriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{4}
}

type Category int32

const (
	Category_UNSPECIFIED_CATEGORY Category = 0
	Category_OPERATIONS           Category = 1
	Category_EQUIPMENTS           Category = 2
	Category_DRIVERS              Category = 3
	Category_SAFETY               Category = 4
	Category_FINANCIALS           Category = 5
	Category_LOSSES               Category = 6
)

// Enum value maps for Category.
var (
	Category_name = map[int32]string{
		0: "UNSPECIFIED_CATEGORY",
		1: "OPERATIONS",
		2: "EQUIPMENTS",
		3: "DRIVERS",
		4: "SAFETY",
		5: "FINANCIALS",
		6: "LOSSES",
	}
	Category_value = map[string]int32{
		"UNSPECIFIED_CATEGORY": 0,
		"OPERATIONS":           1,
		"EQUIPMENTS":           2,
		"DRIVERS":              3,
		"SAFETY":               4,
		"FINANCIALS":           5,
		"LOSSES":               6,
	}
)

func (x Category) Enum() *Category {
	p := new(Category)
	*p = x
	return p
}

func (x Category) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Category) Descriptor() protoreflect.EnumDescriptor {
	return file_risk_factors_risk_factors_proto_enumTypes[5].Descriptor()
}

func (Category) Type() protoreflect.EnumType {
	return &file_risk_factors_risk_factors_proto_enumTypes[5]
}

func (x Category) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Category.Descriptor instead.
func (Category) EnumDescriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{5}
}

type RiskFactorLabel int32

const (
	RiskFactorLabel_UNSPECIFIED_RISK_FACTOR_LABEL              RiskFactorLabel = 0
	RiskFactorLabel_Telematics_Risk_Score                      RiskFactorLabel = 1
	RiskFactorLabel_M1_Score                                   RiskFactorLabel = 2
	RiskFactorLabel_Utilization_Adjustment                     RiskFactorLabel = 3
	RiskFactorLabel_Vin_Visibility_Adjustment                  RiskFactorLabel = 4
	RiskFactorLabel_Fleet_Size_Adjustment                      RiskFactorLabel = 5
	RiskFactorLabel_Primary_Operation_Class_Adjustment         RiskFactorLabel = 6
	RiskFactorLabel_Hazard_Zones_Distance_Adjustment           RiskFactorLabel = 7
	RiskFactorLabel_BASIC_Alert_Count_Adjustment               RiskFactorLabel = 8
	RiskFactorLabel_Driver_OOS_Adjustment                      RiskFactorLabel = 9
	RiskFactorLabel_Overall_OOS_Adjustment                     RiskFactorLabel = 10
	RiskFactorLabel_BASIC_Unsafe_Driving_Score_Adjustment      RiskFactorLabel = 11
	RiskFactorLabel_Driver_Turnover_Adjustment                 RiskFactorLabel = 12
	RiskFactorLabel_BASIC_Vehicle_Maintenance_Score_Adjustment RiskFactorLabel = 13
	RiskFactorLabel_BASIC_HOS_Compliance_Score_Adjustment      RiskFactorLabel = 14
)

// Enum value maps for RiskFactorLabel.
var (
	RiskFactorLabel_name = map[int32]string{
		0:  "UNSPECIFIED_RISK_FACTOR_LABEL",
		1:  "Telematics_Risk_Score",
		2:  "M1_Score",
		3:  "Utilization_Adjustment",
		4:  "Vin_Visibility_Adjustment",
		5:  "Fleet_Size_Adjustment",
		6:  "Primary_Operation_Class_Adjustment",
		7:  "Hazard_Zones_Distance_Adjustment",
		8:  "BASIC_Alert_Count_Adjustment",
		9:  "Driver_OOS_Adjustment",
		10: "Overall_OOS_Adjustment",
		11: "BASIC_Unsafe_Driving_Score_Adjustment",
		12: "Driver_Turnover_Adjustment",
		13: "BASIC_Vehicle_Maintenance_Score_Adjustment",
		14: "BASIC_HOS_Compliance_Score_Adjustment",
	}
	RiskFactorLabel_value = map[string]int32{
		"UNSPECIFIED_RISK_FACTOR_LABEL":              0,
		"Telematics_Risk_Score":                      1,
		"M1_Score":                                   2,
		"Utilization_Adjustment":                     3,
		"Vin_Visibility_Adjustment":                  4,
		"Fleet_Size_Adjustment":                      5,
		"Primary_Operation_Class_Adjustment":         6,
		"Hazard_Zones_Distance_Adjustment":           7,
		"BASIC_Alert_Count_Adjustment":               8,
		"Driver_OOS_Adjustment":                      9,
		"Overall_OOS_Adjustment":                     10,
		"BASIC_Unsafe_Driving_Score_Adjustment":      11,
		"Driver_Turnover_Adjustment":                 12,
		"BASIC_Vehicle_Maintenance_Score_Adjustment": 13,
		"BASIC_HOS_Compliance_Score_Adjustment":      14,
	}
)

func (x RiskFactorLabel) Enum() *RiskFactorLabel {
	p := new(RiskFactorLabel)
	*p = x
	return p
}

func (x RiskFactorLabel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskFactorLabel) Descriptor() protoreflect.EnumDescriptor {
	return file_risk_factors_risk_factors_proto_enumTypes[6].Descriptor()
}

func (RiskFactorLabel) Type() protoreflect.EnumType {
	return &file_risk_factors_risk_factors_proto_enumTypes[6]
}

func (x RiskFactorLabel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskFactorLabel.Descriptor instead.
func (RiskFactorLabel) EnumDescriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{6}
}

type InsertOrUpdateRiskFactorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReviewId              string                   `protobuf:"bytes,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	RiskFactorLabel       RiskFactorLabel          `protobuf:"varint,2,opt,name=risk_factor_label,json=riskFactorLabel,proto3,enum=risk_factors.RiskFactorLabel" json:"risk_factor_label,omitempty"`
	Value                 string                   `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	Notes                 string                   `protobuf:"bytes,4,opt,name=notes,proto3" json:"notes,omitempty"`
	Sentiment             Sentiment                `protobuf:"varint,5,opt,name=sentiment,proto3,enum=risk_factors.Sentiment" json:"sentiment,omitempty"`
	CoveragePricingDetail []*CoveragePricingDetail `protobuf:"bytes,6,rep,name=coverage_pricing_detail,json=coveragePricingDetail,proto3" json:"coverage_pricing_detail,omitempty"`
}

func (x *InsertOrUpdateRiskFactorRequest) Reset() {
	*x = InsertOrUpdateRiskFactorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsertOrUpdateRiskFactorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsertOrUpdateRiskFactorRequest) ProtoMessage() {}

func (x *InsertOrUpdateRiskFactorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsertOrUpdateRiskFactorRequest.ProtoReflect.Descriptor instead.
func (*InsertOrUpdateRiskFactorRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{0}
}

func (x *InsertOrUpdateRiskFactorRequest) GetReviewId() string {
	if x != nil {
		return x.ReviewId
	}
	return ""
}

func (x *InsertOrUpdateRiskFactorRequest) GetRiskFactorLabel() RiskFactorLabel {
	if x != nil {
		return x.RiskFactorLabel
	}
	return RiskFactorLabel_UNSPECIFIED_RISK_FACTOR_LABEL
}

func (x *InsertOrUpdateRiskFactorRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *InsertOrUpdateRiskFactorRequest) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *InsertOrUpdateRiskFactorRequest) GetSentiment() Sentiment {
	if x != nil {
		return x.Sentiment
	}
	return Sentiment_SENTIMENT_UNSPECIFIED
}

func (x *InsertOrUpdateRiskFactorRequest) GetCoveragePricingDetail() []*CoveragePricingDetail {
	if x != nil {
		return x.CoveragePricingDetail
	}
	return nil
}

type GetAllWorksheetFactorWithTagRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId string   `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
	Tags        []string `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *GetAllWorksheetFactorWithTagRequest) Reset() {
	*x = GetAllWorksheetFactorWithTagRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllWorksheetFactorWithTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllWorksheetFactorWithTagRequest) ProtoMessage() {}

func (x *GetAllWorksheetFactorWithTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllWorksheetFactorWithTagRequest.ProtoReflect.Descriptor instead.
func (*GetAllWorksheetFactorWithTagRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{1}
}

func (x *GetAllWorksheetFactorWithTagRequest) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

func (x *GetAllWorksheetFactorWithTagRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type GetAllWorksheetFactorWithTagResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetRiskFactors []*WorksheetRiskFactorResponse `protobuf:"bytes,1,rep,name=worksheet_risk_factors,json=worksheetRiskFactors,proto3" json:"worksheet_risk_factors,omitempty"`
}

func (x *GetAllWorksheetFactorWithTagResponse) Reset() {
	*x = GetAllWorksheetFactorWithTagResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllWorksheetFactorWithTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllWorksheetFactorWithTagResponse) ProtoMessage() {}

func (x *GetAllWorksheetFactorWithTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllWorksheetFactorWithTagResponse.ProtoReflect.Descriptor instead.
func (*GetAllWorksheetFactorWithTagResponse) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{2}
}

func (x *GetAllWorksheetFactorWithTagResponse) GetWorksheetRiskFactors() []*WorksheetRiskFactorResponse {
	if x != nil {
		return x.WorksheetRiskFactors
	}
	return nil
}

type AddWorksheetRiskFactorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId  string    `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
	RiskFactorId string    `protobuf:"bytes,2,opt,name=risk_factor_id,json=riskFactorId,proto3" json:"risk_factor_id,omitempty"`
	Value        string    `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	Sentiment    Sentiment `protobuf:"varint,4,opt,name=sentiment,proto3,enum=risk_factors.Sentiment" json:"sentiment,omitempty"`
	Notes        string    `protobuf:"bytes,5,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (x *AddWorksheetRiskFactorRequest) Reset() {
	*x = AddWorksheetRiskFactorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddWorksheetRiskFactorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddWorksheetRiskFactorRequest) ProtoMessage() {}

func (x *AddWorksheetRiskFactorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddWorksheetRiskFactorRequest.ProtoReflect.Descriptor instead.
func (*AddWorksheetRiskFactorRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{3}
}

func (x *AddWorksheetRiskFactorRequest) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

func (x *AddWorksheetRiskFactorRequest) GetRiskFactorId() string {
	if x != nil {
		return x.RiskFactorId
	}
	return ""
}

func (x *AddWorksheetRiskFactorRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *AddWorksheetRiskFactorRequest) GetSentiment() Sentiment {
	if x != nil {
		return x.Sentiment
	}
	return Sentiment_SENTIMENT_UNSPECIFIED
}

func (x *AddWorksheetRiskFactorRequest) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type UpdateWorksheetRiskFactorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetRiskFactorId string    `protobuf:"bytes,1,opt,name=worksheet_risk_factor_id,json=worksheetRiskFactorId,proto3" json:"worksheet_risk_factor_id,omitempty"`
	Value                 string    `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Sentiment             Sentiment `protobuf:"varint,3,opt,name=sentiment,proto3,enum=risk_factors.Sentiment" json:"sentiment,omitempty"`
	Notes                 string    `protobuf:"bytes,4,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (x *UpdateWorksheetRiskFactorRequest) Reset() {
	*x = UpdateWorksheetRiskFactorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorksheetRiskFactorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorksheetRiskFactorRequest) ProtoMessage() {}

func (x *UpdateWorksheetRiskFactorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorksheetRiskFactorRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorksheetRiskFactorRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateWorksheetRiskFactorRequest) GetWorksheetRiskFactorId() string {
	if x != nil {
		return x.WorksheetRiskFactorId
	}
	return ""
}

func (x *UpdateWorksheetRiskFactorRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *UpdateWorksheetRiskFactorRequest) GetSentiment() Sentiment {
	if x != nil {
		return x.Sentiment
	}
	return Sentiment_SENTIMENT_UNSPECIFIED
}

func (x *UpdateWorksheetRiskFactorRequest) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type UpdateWorksheetPricingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId            string                   `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
	CategoryPricingDetails *CategoryPricingDetails  `protobuf:"bytes,2,opt,name=category_pricing_details,json=categoryPricingDetails,proto3" json:"category_pricing_details,omitempty"`
	WorksheetFactorId      string                   `protobuf:"bytes,3,opt,name=worksheet_factor_id,json=worksheetFactorId,proto3" json:"worksheet_factor_id,omitempty"`
	CoveragePricingDetail  []*CoveragePricingDetail `protobuf:"bytes,4,rep,name=coverage_pricing_detail,json=coveragePricingDetail,proto3" json:"coverage_pricing_detail,omitempty"`
}

func (x *UpdateWorksheetPricingRequest) Reset() {
	*x = UpdateWorksheetPricingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorksheetPricingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorksheetPricingRequest) ProtoMessage() {}

func (x *UpdateWorksheetPricingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorksheetPricingRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorksheetPricingRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateWorksheetPricingRequest) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

func (x *UpdateWorksheetPricingRequest) GetCategoryPricingDetails() *CategoryPricingDetails {
	if x != nil {
		return x.CategoryPricingDetails
	}
	return nil
}

func (x *UpdateWorksheetPricingRequest) GetWorksheetFactorId() string {
	if x != nil {
		return x.WorksheetFactorId
	}
	return ""
}

func (x *UpdateWorksheetPricingRequest) GetCoveragePricingDetail() []*CoveragePricingDetail {
	if x != nil {
		return x.CoveragePricingDetail
	}
	return nil
}

type SuggestRiskFactorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Notes       string    `protobuf:"bytes,2,opt,name=notes,proto3" json:"notes,omitempty"`
	Category    Category  `protobuf:"varint,3,opt,name=category,proto3,enum=risk_factors.Category" json:"category,omitempty"`
	Value       string    `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	Sentiment   Sentiment `protobuf:"varint,5,opt,name=sentiment,proto3,enum=risk_factors.Sentiment" json:"sentiment,omitempty"`
	WorksheetId string    `protobuf:"bytes,6,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
}

func (x *SuggestRiskFactorRequest) Reset() {
	*x = SuggestRiskFactorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuggestRiskFactorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuggestRiskFactorRequest) ProtoMessage() {}

func (x *SuggestRiskFactorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuggestRiskFactorRequest.ProtoReflect.Descriptor instead.
func (*SuggestRiskFactorRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{6}
}

func (x *SuggestRiskFactorRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SuggestRiskFactorRequest) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *SuggestRiskFactorRequest) GetCategory() Category {
	if x != nil {
		return x.Category
	}
	return Category_UNSPECIFIED_CATEGORY
}

func (x *SuggestRiskFactorRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *SuggestRiskFactorRequest) GetSentiment() Sentiment {
	if x != nil {
		return x.Sentiment
	}
	return Sentiment_SENTIMENT_UNSPECIFIED
}

func (x *SuggestRiskFactorRequest) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

type GetLatestWorksheetForReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReviewId string `protobuf:"bytes,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
}

func (x *GetLatestWorksheetForReviewRequest) Reset() {
	*x = GetLatestWorksheetForReviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestWorksheetForReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestWorksheetForReviewRequest) ProtoMessage() {}

func (x *GetLatestWorksheetForReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestWorksheetForReviewRequest.ProtoReflect.Descriptor instead.
func (*GetLatestWorksheetForReviewRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{7}
}

func (x *GetLatestWorksheetForReviewRequest) GetReviewId() string {
	if x != nil {
		return x.ReviewId
	}
	return ""
}

type WorksheetUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId string `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
	Notes       string `protobuf:"bytes,2,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (x *WorksheetUpdateRequest) Reset() {
	*x = WorksheetUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorksheetUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorksheetUpdateRequest) ProtoMessage() {}

func (x *WorksheetUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorksheetUpdateRequest.ProtoReflect.Descriptor instead.
func (*WorksheetUpdateRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{8}
}

func (x *WorksheetUpdateRequest) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

func (x *WorksheetUpdateRequest) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type UpdateWorksheetRatingDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryPricingDetails []*CategoryPricingDetails `protobuf:"bytes,1,rep,name=category_pricing_details,json=categoryPricingDetails,proto3" json:"category_pricing_details,omitempty"`
}

func (x *UpdateWorksheetRatingDetailsRequest) Reset() {
	*x = UpdateWorksheetRatingDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorksheetRatingDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorksheetRatingDetailsRequest) ProtoMessage() {}

func (x *UpdateWorksheetRatingDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorksheetRatingDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorksheetRatingDetailsRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateWorksheetRatingDetailsRequest) GetCategoryPricingDetails() []*CategoryPricingDetails {
	if x != nil {
		return x.CategoryPricingDetails
	}
	return nil
}

type CategoryPricingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category              Category                 `protobuf:"varint,1,opt,name=category,proto3,enum=risk_factors.Category" json:"category,omitempty"`
	CoveragePricingDetail []*CoveragePricingDetail `protobuf:"bytes,2,rep,name=coverage_pricing_detail,json=coveragePricingDetail,proto3" json:"coverage_pricing_detail,omitempty"`
}

func (x *CategoryPricingDetails) Reset() {
	*x = CategoryPricingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryPricingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryPricingDetails) ProtoMessage() {}

func (x *CategoryPricingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryPricingDetails.ProtoReflect.Descriptor instead.
func (*CategoryPricingDetails) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{10}
}

func (x *CategoryPricingDetails) GetCategory() Category {
	if x != nil {
		return x.Category
	}
	return Category_UNSPECIFIED_CATEGORY
}

func (x *CategoryPricingDetails) GetCoveragePricingDetail() []*CoveragePricingDetail {
	if x != nil {
		return x.CoveragePricingDetail
	}
	return nil
}

type CoveragePricingDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Coverage Coverage `protobuf:"varint,1,opt,name=coverage,proto3,enum=risk_factors.Coverage" json:"coverage,omitempty"`
	Credit   int32    `protobuf:"varint,2,opt,name=credit,proto3" json:"credit,omitempty"`
}

func (x *CoveragePricingDetail) Reset() {
	*x = CoveragePricingDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoveragePricingDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoveragePricingDetail) ProtoMessage() {}

func (x *CoveragePricingDetail) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoveragePricingDetail.ProtoReflect.Descriptor instead.
func (*CoveragePricingDetail) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{11}
}

func (x *CoveragePricingDetail) GetCoverage() Coverage {
	if x != nil {
		return x.Coverage
	}
	return Coverage_UNSPECIFIED_COVERAGE
}

func (x *CoveragePricingDetail) GetCredit() int32 {
	if x != nil {
		return x.Credit
	}
	return 0
}

type DeleteRiskFactorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetRiskFactorId string `protobuf:"bytes,1,opt,name=worksheet_risk_factor_id,json=worksheetRiskFactorId,proto3" json:"worksheet_risk_factor_id,omitempty"`
}

func (x *DeleteRiskFactorRequest) Reset() {
	*x = DeleteRiskFactorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRiskFactorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRiskFactorRequest) ProtoMessage() {}

func (x *DeleteRiskFactorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRiskFactorRequest.ProtoReflect.Descriptor instead.
func (*DeleteRiskFactorRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteRiskFactorRequest) GetWorksheetRiskFactorId() string {
	if x != nil {
		return x.WorksheetRiskFactorId
	}
	return ""
}

type ListWorksheetRiskFactorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId string `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
}

func (x *ListWorksheetRiskFactorsRequest) Reset() {
	*x = ListWorksheetRiskFactorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorksheetRiskFactorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorksheetRiskFactorsRequest) ProtoMessage() {}

func (x *ListWorksheetRiskFactorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorksheetRiskFactorsRequest.ProtoReflect.Descriptor instead.
func (*ListWorksheetRiskFactorsRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{13}
}

func (x *ListWorksheetRiskFactorsRequest) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

type ListWorksheetRiskFactorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetRiskFactors []*WorksheetRiskFactorResponse `protobuf:"bytes,1,rep,name=worksheet_risk_factors,json=worksheetRiskFactors,proto3" json:"worksheet_risk_factors,omitempty"`
}

func (x *ListWorksheetRiskFactorsResponse) Reset() {
	*x = ListWorksheetRiskFactorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorksheetRiskFactorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorksheetRiskFactorsResponse) ProtoMessage() {}

func (x *ListWorksheetRiskFactorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorksheetRiskFactorsResponse.ProtoReflect.Descriptor instead.
func (*ListWorksheetRiskFactorsResponse) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{14}
}

func (x *ListWorksheetRiskFactorsResponse) GetWorksheetRiskFactors() []*WorksheetRiskFactorResponse {
	if x != nil {
		return x.WorksheetRiskFactors
	}
	return nil
}

type GetWorksheetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId string `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
}

func (x *GetWorksheetRequest) Reset() {
	*x = GetWorksheetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorksheetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorksheetRequest) ProtoMessage() {}

func (x *GetWorksheetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorksheetRequest.ProtoReflect.Descriptor instead.
func (*GetWorksheetRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{15}
}

func (x *GetWorksheetRequest) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

type Worksheet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   string                         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ReviewId             string                         `protobuf:"bytes,2,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	WorksheetRiskFactors []*WorksheetRiskFactorResponse `protobuf:"bytes,3,rep,name=worksheet_risk_factors,json=worksheetRiskFactors,proto3" json:"worksheet_risk_factors,omitempty"`
	CreatedAt            *timestamppb.Timestamp         `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            *timestamppb.Timestamp         `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	LastUpdatedBy        string                         `protobuf:"bytes,6,opt,name=last_updated_by,json=lastUpdatedBy,proto3" json:"last_updated_by,omitempty"`
	State                WorksheetState                 `protobuf:"varint,7,opt,name=state,proto3,enum=risk_factors.WorksheetState" json:"state,omitempty"`
	PricingDetails       *WorksheetPricingDetails       `protobuf:"bytes,8,opt,name=pricing_details,json=pricingDetails,proto3" json:"pricing_details,omitempty"`
	Notes                string                         `protobuf:"bytes,9,opt,name=notes,proto3" json:"notes,omitempty"`
	Version              int32                          `protobuf:"varint,10,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *Worksheet) Reset() {
	*x = Worksheet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Worksheet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Worksheet) ProtoMessage() {}

func (x *Worksheet) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Worksheet.ProtoReflect.Descriptor instead.
func (*Worksheet) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{16}
}

func (x *Worksheet) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Worksheet) GetReviewId() string {
	if x != nil {
		return x.ReviewId
	}
	return ""
}

func (x *Worksheet) GetWorksheetRiskFactors() []*WorksheetRiskFactorResponse {
	if x != nil {
		return x.WorksheetRiskFactors
	}
	return nil
}

func (x *Worksheet) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Worksheet) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Worksheet) GetLastUpdatedBy() string {
	if x != nil {
		return x.LastUpdatedBy
	}
	return ""
}

func (x *Worksheet) GetState() WorksheetState {
	if x != nil {
		return x.State
	}
	return WorksheetState_UNSPECIFIED_WORKSHEET_STATE
}

func (x *Worksheet) GetPricingDetails() *WorksheetPricingDetails {
	if x != nil {
		return x.PricingDetails
	}
	return nil
}

func (x *Worksheet) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *Worksheet) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type WorksheetPricingDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId            string                    `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
	WorksheetPricingId     string                    `protobuf:"bytes,2,opt,name=worksheet_pricing_id,json=worksheetPricingId,proto3" json:"worksheet_pricing_id,omitempty"`
	CreatedAt              *timestamppb.Timestamp    `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt              *timestamppb.Timestamp    `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	LastUpdatedBy          string                    `protobuf:"bytes,5,opt,name=last_updated_by,json=lastUpdatedBy,proto3" json:"last_updated_by,omitempty"`
	CategoryPricingDetails []*CategoryPricingDetails `protobuf:"bytes,6,rep,name=category_pricing_details,json=categoryPricingDetails,proto3" json:"category_pricing_details,omitempty"`
}

func (x *WorksheetPricingDetails) Reset() {
	*x = WorksheetPricingDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorksheetPricingDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorksheetPricingDetails) ProtoMessage() {}

func (x *WorksheetPricingDetails) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorksheetPricingDetails.ProtoReflect.Descriptor instead.
func (*WorksheetPricingDetails) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{17}
}

func (x *WorksheetPricingDetails) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

func (x *WorksheetPricingDetails) GetWorksheetPricingId() string {
	if x != nil {
		return x.WorksheetPricingId
	}
	return ""
}

func (x *WorksheetPricingDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *WorksheetPricingDetails) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *WorksheetPricingDetails) GetLastUpdatedBy() string {
	if x != nil {
		return x.LastUpdatedBy
	}
	return ""
}

func (x *WorksheetPricingDetails) GetCategoryPricingDetails() []*CategoryPricingDetails {
	if x != nil {
		return x.CategoryPricingDetails
	}
	return nil
}

type WorksheetRiskFactorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId           string                   `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
	WorksheetRiskFactorId string                   `protobuf:"bytes,2,opt,name=worksheet_risk_factor_id,json=worksheetRiskFactorId,proto3" json:"worksheet_risk_factor_id,omitempty"`
	RiskFactor            *RiskFactor              `protobuf:"bytes,3,opt,name=risk_factor,json=riskFactor,proto3" json:"risk_factor,omitempty"`
	CreatedAt             *timestamppb.Timestamp   `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt             *timestamppb.Timestamp   `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedBy             string                   `protobuf:"bytes,6,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	LastUpdatedBy         string                   `protobuf:"bytes,7,opt,name=last_updated_by,json=lastUpdatedBy,proto3" json:"last_updated_by,omitempty"`
	Notes                 string                   `protobuf:"bytes,8,opt,name=notes,proto3" json:"notes,omitempty"`
	Value                 string                   `protobuf:"bytes,9,opt,name=value,proto3" json:"value,omitempty"`
	Sentiment             Sentiment                `protobuf:"varint,10,opt,name=sentiment,proto3,enum=risk_factors.Sentiment" json:"sentiment,omitempty"`
	CoveragePricingDetail []*CoveragePricingDetail `protobuf:"bytes,11,rep,name=coverage_pricing_detail,json=coveragePricingDetail,proto3" json:"coverage_pricing_detail,omitempty"`
}

func (x *WorksheetRiskFactorResponse) Reset() {
	*x = WorksheetRiskFactorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorksheetRiskFactorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorksheetRiskFactorResponse) ProtoMessage() {}

func (x *WorksheetRiskFactorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorksheetRiskFactorResponse.ProtoReflect.Descriptor instead.
func (*WorksheetRiskFactorResponse) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{18}
}

func (x *WorksheetRiskFactorResponse) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

func (x *WorksheetRiskFactorResponse) GetWorksheetRiskFactorId() string {
	if x != nil {
		return x.WorksheetRiskFactorId
	}
	return ""
}

func (x *WorksheetRiskFactorResponse) GetRiskFactor() *RiskFactor {
	if x != nil {
		return x.RiskFactor
	}
	return nil
}

func (x *WorksheetRiskFactorResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *WorksheetRiskFactorResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *WorksheetRiskFactorResponse) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *WorksheetRiskFactorResponse) GetLastUpdatedBy() string {
	if x != nil {
		return x.LastUpdatedBy
	}
	return ""
}

func (x *WorksheetRiskFactorResponse) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *WorksheetRiskFactorResponse) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *WorksheetRiskFactorResponse) GetSentiment() Sentiment {
	if x != nil {
		return x.Sentiment
	}
	return Sentiment_SENTIMENT_UNSPECIFIED
}

func (x *WorksheetRiskFactorResponse) GetCoveragePricingDetail() []*CoveragePricingDetail {
	if x != nil {
		return x.CoveragePricingDetail
	}
	return nil
}

type CreateWorksheetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReviewId string `protobuf:"bytes,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
}

func (x *CreateWorksheetRequest) Reset() {
	*x = CreateWorksheetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorksheetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorksheetRequest) ProtoMessage() {}

func (x *CreateWorksheetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorksheetRequest.ProtoReflect.Descriptor instead.
func (*CreateWorksheetRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{19}
}

func (x *CreateWorksheetRequest) GetReviewId() string {
	if x != nil {
		return x.ReviewId
	}
	return ""
}

type CreateWorksheetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorksheetId string `protobuf:"bytes,1,opt,name=worksheet_id,json=worksheetId,proto3" json:"worksheet_id,omitempty"`
}

func (x *CreateWorksheetResponse) Reset() {
	*x = CreateWorksheetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorksheetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorksheetResponse) ProtoMessage() {}

func (x *CreateWorksheetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorksheetResponse.ProtoReflect.Descriptor instead.
func (*CreateWorksheetResponse) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{20}
}

func (x *CreateWorksheetResponse) GetWorksheetId() string {
	if x != nil {
		return x.WorksheetId
	}
	return ""
}

type ListRiskFactorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RiskFactors []*RiskFactor `protobuf:"bytes,1,rep,name=risk_factors,json=riskFactors,proto3" json:"risk_factors,omitempty"`
}

func (x *ListRiskFactorsResponse) Reset() {
	*x = ListRiskFactorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRiskFactorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRiskFactorsResponse) ProtoMessage() {}

func (x *ListRiskFactorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRiskFactorsResponse.ProtoReflect.Descriptor instead.
func (*ListRiskFactorsResponse) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{21}
}

func (x *ListRiskFactorsResponse) GetRiskFactors() []*RiskFactor {
	if x != nil {
		return x.RiskFactors
	}
	return nil
}

type RiskFactor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name              string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description       string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Category          Category               `protobuf:"varint,4,opt,name=category,proto3,enum=risk_factors.Category" json:"category,omitempty"`
	PricingType       PricingType            `protobuf:"varint,5,opt,name=pricing_type,json=pricingType,proto3,enum=risk_factors.PricingType" json:"pricing_type,omitempty"`
	Version           int32                  `protobuf:"varint,6,opt,name=version,proto3" json:"version,omitempty"`
	State             RiskFactorState        `protobuf:"varint,7,opt,name=state,proto3,enum=risk_factors.RiskFactorState" json:"state,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	LastUpdatedBy     string                 `protobuf:"bytes,10,opt,name=last_updated_by,json=lastUpdatedBy,proto3" json:"last_updated_by,omitempty"`
	CreatedBy         string                 `protobuf:"bytes,11,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	IsSystemGenerated bool                   `protobuf:"varint,12,opt,name=is_system_generated,json=isSystemGenerated,proto3" json:"is_system_generated,omitempty"`
	Tags              []string               `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty"`
	CompositeName     *string                `protobuf:"bytes,14,opt,name=composite_name,json=compositeName,proto3,oneof" json:"composite_name,omitempty"`
}

func (x *RiskFactor) Reset() {
	*x = RiskFactor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskFactor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskFactor) ProtoMessage() {}

func (x *RiskFactor) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskFactor.ProtoReflect.Descriptor instead.
func (*RiskFactor) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{22}
}

func (x *RiskFactor) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RiskFactor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RiskFactor) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RiskFactor) GetCategory() Category {
	if x != nil {
		return x.Category
	}
	return Category_UNSPECIFIED_CATEGORY
}

func (x *RiskFactor) GetPricingType() PricingType {
	if x != nil {
		return x.PricingType
	}
	return PricingType_UNSPECIFIED_PRICING_TYPE
}

func (x *RiskFactor) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *RiskFactor) GetState() RiskFactorState {
	if x != nil {
		return x.State
	}
	return RiskFactorState_UNSPECIFIED_RISK_FACTOR_STATE
}

func (x *RiskFactor) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RiskFactor) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RiskFactor) GetLastUpdatedBy() string {
	if x != nil {
		return x.LastUpdatedBy
	}
	return ""
}

func (x *RiskFactor) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *RiskFactor) GetIsSystemGenerated() bool {
	if x != nil {
		return x.IsSystemGenerated
	}
	return false
}

func (x *RiskFactor) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *RiskFactor) GetCompositeName() string {
	if x != nil && x.CompositeName != nil {
		return *x.CompositeName
	}
	return ""
}

type DeleteRiskFactorByLabelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReviewId        string          `protobuf:"bytes,1,opt,name=review_id,json=reviewId,proto3" json:"review_id,omitempty"`
	RiskFactorLabel RiskFactorLabel `protobuf:"varint,2,opt,name=risk_factor_label,json=riskFactorLabel,proto3,enum=risk_factors.RiskFactorLabel" json:"risk_factor_label,omitempty"`
}

func (x *DeleteRiskFactorByLabelRequest) Reset() {
	*x = DeleteRiskFactorByLabelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_risk_factors_risk_factors_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRiskFactorByLabelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRiskFactorByLabelRequest) ProtoMessage() {}

func (x *DeleteRiskFactorByLabelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_risk_factors_risk_factors_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRiskFactorByLabelRequest.ProtoReflect.Descriptor instead.
func (*DeleteRiskFactorByLabelRequest) Descriptor() ([]byte, []int) {
	return file_risk_factors_risk_factors_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteRiskFactorByLabelRequest) GetReviewId() string {
	if x != nil {
		return x.ReviewId
	}
	return ""
}

func (x *DeleteRiskFactorByLabelRequest) GetRiskFactorLabel() RiskFactorLabel {
	if x != nil {
		return x.RiskFactorLabel
	}
	return RiskFactorLabel_UNSPECIFIED_RISK_FACTOR_LABEL
}

var File_risk_factors_risk_factors_proto protoreflect.FileDescriptor

var file_risk_factors_risk_factors_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc9, 0x02,
	0x0a, 0x1f, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x49,
	0x0a, 0x11, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x0f, 0x72, 0x69, 0x73, 0x6b, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x5b, 0x0a, 0x17,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x15, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x5c, 0x0a, 0x23, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5f, 0x0a, 0x16, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x14, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x22, 0xcb, 0x01, 0x0a, 0x1d, 0x41, 0x64, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65,
	0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09,
	0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x22,
	0xbe, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f,
	0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73,
	0x22, 0xaf, 0x02, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x18, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65,
	0x65, 0x74, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x17, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x15, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x22, 0xe8, 0x01, 0x0a, 0x18, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x52, 0x69,
	0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x49, 0x64, 0x22, 0x41, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64,
	0x22, 0x51, 0x0a, 0x16, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f,
	0x74, 0x65, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5e, 0x0a, 0x18, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x16, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xa9, 0x01, 0x0a, 0x16,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x5b, 0x0a, 0x17, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x15, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x63, 0x0a, 0x15, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x32, 0x0a, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x2e, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x22, 0x52, 0x0a, 0x17,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x22, 0x44, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x49, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x16, 0x77,
	0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x14, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x22, 0x38, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x49, 0x64, 0x22, 0xeb, 0x03, 0x0a, 0x09, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49,
	0x64, 0x12, 0x5f, 0x0a, 0x16, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73,
	0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x14, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x32, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0xec, 0x02, 0x0a, 0x17, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x5e, 0x0a, 0x18, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0xb1, 0x04, 0x0a, 0x1b, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x39, 0x0a, 0x0b, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0a,
	0x72, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x09, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x5b, 0x0a, 0x17, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x15, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x35, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x22, 0x3c,
	0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x17,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x72, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x22, 0xd3, 0x04, 0x0a, 0x0a, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x3c, 0x0a,
	0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x0e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x1e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42,
	0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x11, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x52, 0x0f, 0x72, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x2a, 0x4f, 0x0a, 0x09, 0x53, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x45, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4e,
	0x45, 0x47, 0x41, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x45, 0x55,
	0x54, 0x52, 0x41, 0x4c, 0x10, 0x03, 0x2a, 0x80, 0x01, 0x0a, 0x08, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x5f, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x47, 0x45, 0x10, 0x00, 0x12, 0x12, 0x0a,
	0x0e, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10,
	0x01, 0x12, 0x15, 0x0a, 0x11, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x41,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x55, 0x54, 0x4f,
	0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45,
	0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x4f, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x55, 0x43,
	0x4b, 0x5f, 0x43, 0x41, 0x52, 0x47, 0x4f, 0x10, 0x04, 0x2a, 0x86, 0x01, 0x0a, 0x0e, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x1b,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4b,
	0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x00, 0x12, 0x1c, 0x0a,
	0x18, 0x57, 0x4f, 0x52, 0x4b, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x57,
	0x4f, 0x52, 0x4b, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x57, 0x4f, 0x52, 0x4b, 0x53,
	0x48, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x4c, 0x45,
	0x10, 0x03, 0x2a, 0x59, 0x0a, 0x0f, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x52, 0x41, 0x46, 0x54, 0x10, 0x03, 0x2a, 0x54, 0x0a,
	0x0b, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x43,
	0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x44, 0x10, 0x01, 0x12, 0x13,
	0x0a, 0x0f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45,
	0x44, 0x10, 0x02, 0x2a, 0x79, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x18, 0x0a, 0x14, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x50, 0x45,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x51, 0x55,
	0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x52, 0x49,
	0x56, 0x45, 0x52, 0x53, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x41, 0x46, 0x45, 0x54, 0x59,
	0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41, 0x4c, 0x53,
	0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x4f, 0x53, 0x53, 0x45, 0x53, 0x10, 0x06, 0x2a, 0x80,
	0x04, 0x0a, 0x0f, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x41,
	0x42, 0x45, 0x4c, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x5f, 0x52, 0x69, 0x73, 0x6b, 0x5f, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10, 0x01,
	0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x31, 0x5f, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10, 0x02, 0x12, 0x1a,
	0x0a, 0x16, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x41, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x69,
	0x6e, 0x5f, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x41, 0x64, 0x6a,
	0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x5f, 0x53, 0x69, 0x7a, 0x65, 0x5f, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x10, 0x05, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x5f,
	0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x06, 0x12, 0x24, 0x0a, 0x20,
	0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x5f, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x5f, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x5f, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x10, 0x08, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x4f,
	0x4f, 0x53, 0x5f, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x09, 0x12,
	0x1a, 0x0a, 0x16, 0x4f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x4f, 0x4f, 0x53, 0x5f, 0x41,
	0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x0a, 0x12, 0x29, 0x0a, 0x25, 0x42,
	0x41, 0x53, 0x49, 0x43, 0x5f, 0x55, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x5f, 0x44, 0x72, 0x69, 0x76,
	0x69, 0x6e, 0x67, 0x5f, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x10, 0x0b, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x5f, 0x54, 0x75, 0x72, 0x6e, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x10, 0x0c, 0x12, 0x2e, 0x0a, 0x2a, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x10, 0x0d, 0x12, 0x29, 0x0a, 0x25, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f,
	0x48, 0x4f, 0x53, 0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x5f, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x10,
	0x0e, 0x32, 0x93, 0x0a, 0x0a, 0x12, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x50, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x0f, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x12, 0x24, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65,
	0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x47, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x12, 0x21, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x12, 0x68, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x30, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x12, 0x4f, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x12, 0x24, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x70, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2b, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x12, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52,
	0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x19, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x69,
	0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69,
	0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5d, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x12, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x53, 0x0a, 0x11, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x26, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x53, 0x75, 0x67, 0x67, 0x65,
	0x73, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x61, 0x0a, 0x18, 0x49,
	0x6e, 0x73, 0x65, 0x72, 0x74, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73,
	0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x4f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x85,
	0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65,
	0x65, 0x74, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x12,
	0x31, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65, 0x74, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x32, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x2c, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x2f, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_risk_factors_risk_factors_proto_rawDescOnce sync.Once
	file_risk_factors_risk_factors_proto_rawDescData = file_risk_factors_risk_factors_proto_rawDesc
)

func file_risk_factors_risk_factors_proto_rawDescGZIP() []byte {
	file_risk_factors_risk_factors_proto_rawDescOnce.Do(func() {
		file_risk_factors_risk_factors_proto_rawDescData = protoimpl.X.CompressGZIP(file_risk_factors_risk_factors_proto_rawDescData)
	})
	return file_risk_factors_risk_factors_proto_rawDescData
}

var file_risk_factors_risk_factors_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_risk_factors_risk_factors_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_risk_factors_risk_factors_proto_goTypes = []interface{}{
	(Sentiment)(0),                               // 0: risk_factors.Sentiment
	(Coverage)(0),                                // 1: risk_factors.Coverage
	(WorksheetState)(0),                          // 2: risk_factors.WorksheetState
	(RiskFactorState)(0),                         // 3: risk_factors.RiskFactorState
	(PricingType)(0),                             // 4: risk_factors.PricingType
	(Category)(0),                                // 5: risk_factors.Category
	(RiskFactorLabel)(0),                         // 6: risk_factors.RiskFactorLabel
	(*InsertOrUpdateRiskFactorRequest)(nil),      // 7: risk_factors.InsertOrUpdateRiskFactorRequest
	(*GetAllWorksheetFactorWithTagRequest)(nil),  // 8: risk_factors.GetAllWorksheetFactorWithTagRequest
	(*GetAllWorksheetFactorWithTagResponse)(nil), // 9: risk_factors.GetAllWorksheetFactorWithTagResponse
	(*AddWorksheetRiskFactorRequest)(nil),        // 10: risk_factors.AddWorksheetRiskFactorRequest
	(*UpdateWorksheetRiskFactorRequest)(nil),     // 11: risk_factors.UpdateWorksheetRiskFactorRequest
	(*UpdateWorksheetPricingRequest)(nil),        // 12: risk_factors.UpdateWorksheetPricingRequest
	(*SuggestRiskFactorRequest)(nil),             // 13: risk_factors.SuggestRiskFactorRequest
	(*GetLatestWorksheetForReviewRequest)(nil),   // 14: risk_factors.GetLatestWorksheetForReviewRequest
	(*WorksheetUpdateRequest)(nil),               // 15: risk_factors.WorksheetUpdateRequest
	(*UpdateWorksheetRatingDetailsRequest)(nil),  // 16: risk_factors.UpdateWorksheetRatingDetailsRequest
	(*CategoryPricingDetails)(nil),               // 17: risk_factors.CategoryPricingDetails
	(*CoveragePricingDetail)(nil),                // 18: risk_factors.CoveragePricingDetail
	(*DeleteRiskFactorRequest)(nil),              // 19: risk_factors.DeleteRiskFactorRequest
	(*ListWorksheetRiskFactorsRequest)(nil),      // 20: risk_factors.ListWorksheetRiskFactorsRequest
	(*ListWorksheetRiskFactorsResponse)(nil),     // 21: risk_factors.ListWorksheetRiskFactorsResponse
	(*GetWorksheetRequest)(nil),                  // 22: risk_factors.GetWorksheetRequest
	(*Worksheet)(nil),                            // 23: risk_factors.Worksheet
	(*WorksheetPricingDetails)(nil),              // 24: risk_factors.WorksheetPricingDetails
	(*WorksheetRiskFactorResponse)(nil),          // 25: risk_factors.WorksheetRiskFactorResponse
	(*CreateWorksheetRequest)(nil),               // 26: risk_factors.CreateWorksheetRequest
	(*CreateWorksheetResponse)(nil),              // 27: risk_factors.CreateWorksheetResponse
	(*ListRiskFactorsResponse)(nil),              // 28: risk_factors.ListRiskFactorsResponse
	(*RiskFactor)(nil),                           // 29: risk_factors.RiskFactor
	(*DeleteRiskFactorByLabelRequest)(nil),       // 30: risk_factors.DeleteRiskFactorByLabelRequest
	(*timestamppb.Timestamp)(nil),                // 31: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                        // 32: google.protobuf.Empty
}
var file_risk_factors_risk_factors_proto_depIdxs = []int32{
	6,  // 0: risk_factors.InsertOrUpdateRiskFactorRequest.risk_factor_label:type_name -> risk_factors.RiskFactorLabel
	0,  // 1: risk_factors.InsertOrUpdateRiskFactorRequest.sentiment:type_name -> risk_factors.Sentiment
	18, // 2: risk_factors.InsertOrUpdateRiskFactorRequest.coverage_pricing_detail:type_name -> risk_factors.CoveragePricingDetail
	25, // 3: risk_factors.GetAllWorksheetFactorWithTagResponse.worksheet_risk_factors:type_name -> risk_factors.WorksheetRiskFactorResponse
	0,  // 4: risk_factors.AddWorksheetRiskFactorRequest.sentiment:type_name -> risk_factors.Sentiment
	0,  // 5: risk_factors.UpdateWorksheetRiskFactorRequest.sentiment:type_name -> risk_factors.Sentiment
	17, // 6: risk_factors.UpdateWorksheetPricingRequest.category_pricing_details:type_name -> risk_factors.CategoryPricingDetails
	18, // 7: risk_factors.UpdateWorksheetPricingRequest.coverage_pricing_detail:type_name -> risk_factors.CoveragePricingDetail
	5,  // 8: risk_factors.SuggestRiskFactorRequest.category:type_name -> risk_factors.Category
	0,  // 9: risk_factors.SuggestRiskFactorRequest.sentiment:type_name -> risk_factors.Sentiment
	17, // 10: risk_factors.UpdateWorksheetRatingDetailsRequest.category_pricing_details:type_name -> risk_factors.CategoryPricingDetails
	5,  // 11: risk_factors.CategoryPricingDetails.category:type_name -> risk_factors.Category
	18, // 12: risk_factors.CategoryPricingDetails.coverage_pricing_detail:type_name -> risk_factors.CoveragePricingDetail
	1,  // 13: risk_factors.CoveragePricingDetail.coverage:type_name -> risk_factors.Coverage
	25, // 14: risk_factors.ListWorksheetRiskFactorsResponse.worksheet_risk_factors:type_name -> risk_factors.WorksheetRiskFactorResponse
	25, // 15: risk_factors.Worksheet.worksheet_risk_factors:type_name -> risk_factors.WorksheetRiskFactorResponse
	31, // 16: risk_factors.Worksheet.created_at:type_name -> google.protobuf.Timestamp
	31, // 17: risk_factors.Worksheet.updated_at:type_name -> google.protobuf.Timestamp
	2,  // 18: risk_factors.Worksheet.state:type_name -> risk_factors.WorksheetState
	24, // 19: risk_factors.Worksheet.pricing_details:type_name -> risk_factors.WorksheetPricingDetails
	31, // 20: risk_factors.WorksheetPricingDetails.created_at:type_name -> google.protobuf.Timestamp
	31, // 21: risk_factors.WorksheetPricingDetails.updated_at:type_name -> google.protobuf.Timestamp
	17, // 22: risk_factors.WorksheetPricingDetails.category_pricing_details:type_name -> risk_factors.CategoryPricingDetails
	29, // 23: risk_factors.WorksheetRiskFactorResponse.risk_factor:type_name -> risk_factors.RiskFactor
	31, // 24: risk_factors.WorksheetRiskFactorResponse.created_at:type_name -> google.protobuf.Timestamp
	31, // 25: risk_factors.WorksheetRiskFactorResponse.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 26: risk_factors.WorksheetRiskFactorResponse.sentiment:type_name -> risk_factors.Sentiment
	18, // 27: risk_factors.WorksheetRiskFactorResponse.coverage_pricing_detail:type_name -> risk_factors.CoveragePricingDetail
	29, // 28: risk_factors.ListRiskFactorsResponse.risk_factors:type_name -> risk_factors.RiskFactor
	5,  // 29: risk_factors.RiskFactor.category:type_name -> risk_factors.Category
	4,  // 30: risk_factors.RiskFactor.pricing_type:type_name -> risk_factors.PricingType
	3,  // 31: risk_factors.RiskFactor.state:type_name -> risk_factors.RiskFactorState
	31, // 32: risk_factors.RiskFactor.created_at:type_name -> google.protobuf.Timestamp
	31, // 33: risk_factors.RiskFactor.updated_at:type_name -> google.protobuf.Timestamp
	6,  // 34: risk_factors.DeleteRiskFactorByLabelRequest.risk_factor_label:type_name -> risk_factors.RiskFactorLabel
	32, // 35: risk_factors.RiskFactorsService.ListRiskFactors:input_type -> google.protobuf.Empty
	26, // 36: risk_factors.RiskFactorsService.CreateWorksheet:input_type -> risk_factors.CreateWorksheetRequest
	22, // 37: risk_factors.RiskFactorsService.GetWorksheet:input_type -> risk_factors.GetWorksheetRequest
	14, // 38: risk_factors.RiskFactorsService.GetLatestWorksheetForReview:input_type -> risk_factors.GetLatestWorksheetForReviewRequest
	15, // 39: risk_factors.RiskFactorsService.UpdateWorksheet:input_type -> risk_factors.WorksheetUpdateRequest
	10, // 40: risk_factors.RiskFactorsService.AddWorksheetRiskFactor:input_type -> risk_factors.AddWorksheetRiskFactorRequest
	11, // 41: risk_factors.RiskFactorsService.UpdateWorksheetRiskFactor:input_type -> risk_factors.UpdateWorksheetRiskFactorRequest
	19, // 42: risk_factors.RiskFactorsService.DeleteWorksheetRiskFactor:input_type -> risk_factors.DeleteRiskFactorRequest
	12, // 43: risk_factors.RiskFactorsService.UpdateWorksheetPricing:input_type -> risk_factors.UpdateWorksheetPricingRequest
	13, // 44: risk_factors.RiskFactorsService.SuggestRiskFactor:input_type -> risk_factors.SuggestRiskFactorRequest
	7,  // 45: risk_factors.RiskFactorsService.InsertOrUpdateRiskFactor:input_type -> risk_factors.InsertOrUpdateRiskFactorRequest
	8,  // 46: risk_factors.RiskFactorsService.GetAllWorksheetFactorWithTag:input_type -> risk_factors.GetAllWorksheetFactorWithTagRequest
	30, // 47: risk_factors.RiskFactorsService.DeleteRiskFactorByLabel:input_type -> risk_factors.DeleteRiskFactorByLabelRequest
	28, // 48: risk_factors.RiskFactorsService.ListRiskFactors:output_type -> risk_factors.ListRiskFactorsResponse
	27, // 49: risk_factors.RiskFactorsService.CreateWorksheet:output_type -> risk_factors.CreateWorksheetResponse
	23, // 50: risk_factors.RiskFactorsService.GetWorksheet:output_type -> risk_factors.Worksheet
	23, // 51: risk_factors.RiskFactorsService.GetLatestWorksheetForReview:output_type -> risk_factors.Worksheet
	32, // 52: risk_factors.RiskFactorsService.UpdateWorksheet:output_type -> google.protobuf.Empty
	25, // 53: risk_factors.RiskFactorsService.AddWorksheetRiskFactor:output_type -> risk_factors.WorksheetRiskFactorResponse
	25, // 54: risk_factors.RiskFactorsService.UpdateWorksheetRiskFactor:output_type -> risk_factors.WorksheetRiskFactorResponse
	32, // 55: risk_factors.RiskFactorsService.DeleteWorksheetRiskFactor:output_type -> google.protobuf.Empty
	32, // 56: risk_factors.RiskFactorsService.UpdateWorksheetPricing:output_type -> google.protobuf.Empty
	32, // 57: risk_factors.RiskFactorsService.SuggestRiskFactor:output_type -> google.protobuf.Empty
	32, // 58: risk_factors.RiskFactorsService.InsertOrUpdateRiskFactor:output_type -> google.protobuf.Empty
	9,  // 59: risk_factors.RiskFactorsService.GetAllWorksheetFactorWithTag:output_type -> risk_factors.GetAllWorksheetFactorWithTagResponse
	32, // 60: risk_factors.RiskFactorsService.DeleteRiskFactorByLabel:output_type -> google.protobuf.Empty
	48, // [48:61] is the sub-list for method output_type
	35, // [35:48] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_risk_factors_risk_factors_proto_init() }
func file_risk_factors_risk_factors_proto_init() {
	if File_risk_factors_risk_factors_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_risk_factors_risk_factors_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsertOrUpdateRiskFactorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllWorksheetFactorWithTagRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllWorksheetFactorWithTagResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddWorksheetRiskFactorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorksheetRiskFactorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorksheetPricingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuggestRiskFactorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestWorksheetForReviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorksheetUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorksheetRatingDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryPricingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoveragePricingDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRiskFactorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorksheetRiskFactorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorksheetRiskFactorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorksheetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Worksheet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorksheetPricingDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorksheetRiskFactorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorksheetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorksheetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRiskFactorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskFactor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_risk_factors_risk_factors_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRiskFactorByLabelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_risk_factors_risk_factors_proto_msgTypes[22].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_risk_factors_risk_factors_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_risk_factors_risk_factors_proto_goTypes,
		DependencyIndexes: file_risk_factors_risk_factors_proto_depIdxs,
		EnumInfos:         file_risk_factors_risk_factors_proto_enumTypes,
		MessageInfos:      file_risk_factors_risk_factors_proto_msgTypes,
	}.Build()
	File_risk_factors_risk_factors_proto = out.File
	file_risk_factors_risk_factors_proto_rawDesc = nil
	file_risk_factors_risk_factors_proto_goTypes = nil
	file_risk_factors_risk_factors_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// RiskFactorsServiceClient is the client API for RiskFactorsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RiskFactorsServiceClient interface {
	ListRiskFactors(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListRiskFactorsResponse, error)
	CreateWorksheet(ctx context.Context, in *CreateWorksheetRequest, opts ...grpc.CallOption) (*CreateWorksheetResponse, error)
	GetWorksheet(ctx context.Context, in *GetWorksheetRequest, opts ...grpc.CallOption) (*Worksheet, error)
	GetLatestWorksheetForReview(ctx context.Context, in *GetLatestWorksheetForReviewRequest, opts ...grpc.CallOption) (*Worksheet, error)
	UpdateWorksheet(ctx context.Context, in *WorksheetUpdateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	AddWorksheetRiskFactor(ctx context.Context, in *AddWorksheetRiskFactorRequest, opts ...grpc.CallOption) (*WorksheetRiskFactorResponse, error)
	UpdateWorksheetRiskFactor(ctx context.Context, in *UpdateWorksheetRiskFactorRequest, opts ...grpc.CallOption) (*WorksheetRiskFactorResponse, error)
	DeleteWorksheetRiskFactor(ctx context.Context, in *DeleteRiskFactorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	UpdateWorksheetPricing(ctx context.Context, in *UpdateWorksheetPricingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SuggestRiskFactor(ctx context.Context, in *SuggestRiskFactorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	InsertOrUpdateRiskFactor(ctx context.Context, in *InsertOrUpdateRiskFactorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetAllWorksheetFactorWithTag(ctx context.Context, in *GetAllWorksheetFactorWithTagRequest, opts ...grpc.CallOption) (*GetAllWorksheetFactorWithTagResponse, error)
	DeleteRiskFactorByLabel(ctx context.Context, in *DeleteRiskFactorByLabelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type riskFactorsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRiskFactorsServiceClient(cc grpc.ClientConnInterface) RiskFactorsServiceClient {
	return &riskFactorsServiceClient{cc}
}

func (c *riskFactorsServiceClient) ListRiskFactors(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListRiskFactorsResponse, error) {
	out := new(ListRiskFactorsResponse)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/ListRiskFactors", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) CreateWorksheet(ctx context.Context, in *CreateWorksheetRequest, opts ...grpc.CallOption) (*CreateWorksheetResponse, error) {
	out := new(CreateWorksheetResponse)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/CreateWorksheet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) GetWorksheet(ctx context.Context, in *GetWorksheetRequest, opts ...grpc.CallOption) (*Worksheet, error) {
	out := new(Worksheet)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/GetWorksheet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) GetLatestWorksheetForReview(ctx context.Context, in *GetLatestWorksheetForReviewRequest, opts ...grpc.CallOption) (*Worksheet, error) {
	out := new(Worksheet)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/GetLatestWorksheetForReview", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) UpdateWorksheet(ctx context.Context, in *WorksheetUpdateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/UpdateWorksheet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) AddWorksheetRiskFactor(ctx context.Context, in *AddWorksheetRiskFactorRequest, opts ...grpc.CallOption) (*WorksheetRiskFactorResponse, error) {
	out := new(WorksheetRiskFactorResponse)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/AddWorksheetRiskFactor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) UpdateWorksheetRiskFactor(ctx context.Context, in *UpdateWorksheetRiskFactorRequest, opts ...grpc.CallOption) (*WorksheetRiskFactorResponse, error) {
	out := new(WorksheetRiskFactorResponse)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/UpdateWorksheetRiskFactor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) DeleteWorksheetRiskFactor(ctx context.Context, in *DeleteRiskFactorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/DeleteWorksheetRiskFactor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) UpdateWorksheetPricing(ctx context.Context, in *UpdateWorksheetPricingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/UpdateWorksheetPricing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) SuggestRiskFactor(ctx context.Context, in *SuggestRiskFactorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/SuggestRiskFactor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) InsertOrUpdateRiskFactor(ctx context.Context, in *InsertOrUpdateRiskFactorRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/InsertOrUpdateRiskFactor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) GetAllWorksheetFactorWithTag(ctx context.Context, in *GetAllWorksheetFactorWithTagRequest, opts ...grpc.CallOption) (*GetAllWorksheetFactorWithTagResponse, error) {
	out := new(GetAllWorksheetFactorWithTagResponse)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/GetAllWorksheetFactorWithTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskFactorsServiceClient) DeleteRiskFactorByLabel(ctx context.Context, in *DeleteRiskFactorByLabelRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/risk_factors.RiskFactorsService/DeleteRiskFactorByLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RiskFactorsServiceServer is the server API for RiskFactorsService service.
type RiskFactorsServiceServer interface {
	ListRiskFactors(context.Context, *emptypb.Empty) (*ListRiskFactorsResponse, error)
	CreateWorksheet(context.Context, *CreateWorksheetRequest) (*CreateWorksheetResponse, error)
	GetWorksheet(context.Context, *GetWorksheetRequest) (*Worksheet, error)
	GetLatestWorksheetForReview(context.Context, *GetLatestWorksheetForReviewRequest) (*Worksheet, error)
	UpdateWorksheet(context.Context, *WorksheetUpdateRequest) (*emptypb.Empty, error)
	AddWorksheetRiskFactor(context.Context, *AddWorksheetRiskFactorRequest) (*WorksheetRiskFactorResponse, error)
	UpdateWorksheetRiskFactor(context.Context, *UpdateWorksheetRiskFactorRequest) (*WorksheetRiskFactorResponse, error)
	DeleteWorksheetRiskFactor(context.Context, *DeleteRiskFactorRequest) (*emptypb.Empty, error)
	UpdateWorksheetPricing(context.Context, *UpdateWorksheetPricingRequest) (*emptypb.Empty, error)
	SuggestRiskFactor(context.Context, *SuggestRiskFactorRequest) (*emptypb.Empty, error)
	InsertOrUpdateRiskFactor(context.Context, *InsertOrUpdateRiskFactorRequest) (*emptypb.Empty, error)
	GetAllWorksheetFactorWithTag(context.Context, *GetAllWorksheetFactorWithTagRequest) (*GetAllWorksheetFactorWithTagResponse, error)
	DeleteRiskFactorByLabel(context.Context, *DeleteRiskFactorByLabelRequest) (*emptypb.Empty, error)
}

// UnimplementedRiskFactorsServiceServer can be embedded to have forward compatible implementations.
type UnimplementedRiskFactorsServiceServer struct {
}

func (*UnimplementedRiskFactorsServiceServer) ListRiskFactors(context.Context, *emptypb.Empty) (*ListRiskFactorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRiskFactors not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) CreateWorksheet(context.Context, *CreateWorksheetRequest) (*CreateWorksheetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorksheet not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) GetWorksheet(context.Context, *GetWorksheetRequest) (*Worksheet, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorksheet not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) GetLatestWorksheetForReview(context.Context, *GetLatestWorksheetForReviewRequest) (*Worksheet, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestWorksheetForReview not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) UpdateWorksheet(context.Context, *WorksheetUpdateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorksheet not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) AddWorksheetRiskFactor(context.Context, *AddWorksheetRiskFactorRequest) (*WorksheetRiskFactorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddWorksheetRiskFactor not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) UpdateWorksheetRiskFactor(context.Context, *UpdateWorksheetRiskFactorRequest) (*WorksheetRiskFactorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorksheetRiskFactor not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) DeleteWorksheetRiskFactor(context.Context, *DeleteRiskFactorRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorksheetRiskFactor not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) UpdateWorksheetPricing(context.Context, *UpdateWorksheetPricingRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorksheetPricing not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) SuggestRiskFactor(context.Context, *SuggestRiskFactorRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuggestRiskFactor not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) InsertOrUpdateRiskFactor(context.Context, *InsertOrUpdateRiskFactorRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertOrUpdateRiskFactor not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) GetAllWorksheetFactorWithTag(context.Context, *GetAllWorksheetFactorWithTagRequest) (*GetAllWorksheetFactorWithTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllWorksheetFactorWithTag not implemented")
}
func (*UnimplementedRiskFactorsServiceServer) DeleteRiskFactorByLabel(context.Context, *DeleteRiskFactorByLabelRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRiskFactorByLabel not implemented")
}

func RegisterRiskFactorsServiceServer(s *grpc.Server, srv RiskFactorsServiceServer) {
	s.RegisterService(&_RiskFactorsService_serviceDesc, srv)
}

func _RiskFactorsService_ListRiskFactors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).ListRiskFactors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/ListRiskFactors",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).ListRiskFactors(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_CreateWorksheet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorksheetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).CreateWorksheet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/CreateWorksheet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).CreateWorksheet(ctx, req.(*CreateWorksheetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_GetWorksheet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorksheetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).GetWorksheet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/GetWorksheet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).GetWorksheet(ctx, req.(*GetWorksheetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_GetLatestWorksheetForReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestWorksheetForReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).GetLatestWorksheetForReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/GetLatestWorksheetForReview",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).GetLatestWorksheetForReview(ctx, req.(*GetLatestWorksheetForReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_UpdateWorksheet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WorksheetUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).UpdateWorksheet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/UpdateWorksheet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).UpdateWorksheet(ctx, req.(*WorksheetUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_AddWorksheetRiskFactor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWorksheetRiskFactorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).AddWorksheetRiskFactor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/AddWorksheetRiskFactor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).AddWorksheetRiskFactor(ctx, req.(*AddWorksheetRiskFactorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_UpdateWorksheetRiskFactor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorksheetRiskFactorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).UpdateWorksheetRiskFactor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/UpdateWorksheetRiskFactor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).UpdateWorksheetRiskFactor(ctx, req.(*UpdateWorksheetRiskFactorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_DeleteWorksheetRiskFactor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRiskFactorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).DeleteWorksheetRiskFactor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/DeleteWorksheetRiskFactor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).DeleteWorksheetRiskFactor(ctx, req.(*DeleteRiskFactorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_UpdateWorksheetPricing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorksheetPricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).UpdateWorksheetPricing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/UpdateWorksheetPricing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).UpdateWorksheetPricing(ctx, req.(*UpdateWorksheetPricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_SuggestRiskFactor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SuggestRiskFactorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).SuggestRiskFactor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/SuggestRiskFactor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).SuggestRiskFactor(ctx, req.(*SuggestRiskFactorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_InsertOrUpdateRiskFactor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertOrUpdateRiskFactorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).InsertOrUpdateRiskFactor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/InsertOrUpdateRiskFactor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).InsertOrUpdateRiskFactor(ctx, req.(*InsertOrUpdateRiskFactorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_GetAllWorksheetFactorWithTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWorksheetFactorWithTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).GetAllWorksheetFactorWithTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/GetAllWorksheetFactorWithTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).GetAllWorksheetFactorWithTag(ctx, req.(*GetAllWorksheetFactorWithTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskFactorsService_DeleteRiskFactorByLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRiskFactorByLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskFactorsServiceServer).DeleteRiskFactorByLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_factors.RiskFactorsService/DeleteRiskFactorByLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskFactorsServiceServer).DeleteRiskFactorByLabel(ctx, req.(*DeleteRiskFactorByLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _RiskFactorsService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "risk_factors.RiskFactorsService",
	HandlerType: (*RiskFactorsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListRiskFactors",
			Handler:    _RiskFactorsService_ListRiskFactors_Handler,
		},
		{
			MethodName: "CreateWorksheet",
			Handler:    _RiskFactorsService_CreateWorksheet_Handler,
		},
		{
			MethodName: "GetWorksheet",
			Handler:    _RiskFactorsService_GetWorksheet_Handler,
		},
		{
			MethodName: "GetLatestWorksheetForReview",
			Handler:    _RiskFactorsService_GetLatestWorksheetForReview_Handler,
		},
		{
			MethodName: "UpdateWorksheet",
			Handler:    _RiskFactorsService_UpdateWorksheet_Handler,
		},
		{
			MethodName: "AddWorksheetRiskFactor",
			Handler:    _RiskFactorsService_AddWorksheetRiskFactor_Handler,
		},
		{
			MethodName: "UpdateWorksheetRiskFactor",
			Handler:    _RiskFactorsService_UpdateWorksheetRiskFactor_Handler,
		},
		{
			MethodName: "DeleteWorksheetRiskFactor",
			Handler:    _RiskFactorsService_DeleteWorksheetRiskFactor_Handler,
		},
		{
			MethodName: "UpdateWorksheetPricing",
			Handler:    _RiskFactorsService_UpdateWorksheetPricing_Handler,
		},
		{
			MethodName: "SuggestRiskFactor",
			Handler:    _RiskFactorsService_SuggestRiskFactor_Handler,
		},
		{
			MethodName: "InsertOrUpdateRiskFactor",
			Handler:    _RiskFactorsService_InsertOrUpdateRiskFactor_Handler,
		},
		{
			MethodName: "GetAllWorksheetFactorWithTag",
			Handler:    _RiskFactorsService_GetAllWorksheetFactorWithTag_Handler,
		},
		{
			MethodName: "DeleteRiskFactorByLabel",
			Handler:    _RiskFactorsService_DeleteRiskFactorByLabel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "risk_factors/risk_factors.proto",
}
