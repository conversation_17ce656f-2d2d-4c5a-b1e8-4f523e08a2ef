package appetite_guidelines_fixture

import (
	"context"
	_ "embed"

	"go.uber.org/fx"
	"nirvanatech.com/nirvana/common-go/log"

	db_api "nirvanatech.com/nirvana/db-api"
)

//go:embed rule_set.sql
var ruleSetData string

type Fixture struct {
	RuleSetData string

	Db db_api.NirvanaRW
}

func newAppetiteGuidelinesFixture(lc fx.Lifecycle, db db_api.NirvanaRW) (*Fixture, error) {
	log.Plain.Info("Starting newAppetiteGuidelinesFixture")
	fixture := &Fixture{
		RuleSetData: ruleSetData,
		Db:          db,
	}
	lc.Append(fx.Hook{
		OnStart: fixture.apply,
	})

	return fixture, nil
}

func (f *Fixture) apply(ctx context.Context) error {
	log.Info(ctx, "Running sql data for Appetite Guidelines Fixture")
	if _, err := f.Db.Exec(f.RuleSetData); err != nil {
		return err
	}
	return nil
}
