INSERT INTO appetite_guidelines.rule_sets (id,
                                           category,
                                           program,
                                           version,
                                           description,
                                           rules,
                                           created_at,
                                           created_by)
VALUES ('5f3c6c2e-8e4f-4c7a-9359-1b2d9f5a0abc',
        'Internal',
        'FleetAdmitted',
        1,
        'Initial rule using GRL JSON DSL',
        '[
          {
            "name": "LossesBurnRate_None",
            "desc": "",
            "salience": 0,
            "when": "input.AlLossesBurnRate.Valid && input.AlLossesBurnRate.Float32 <= 20000",
            "then": [
              "output.SetLossesBurnRateDecision(\"None\", \"AutoLiability\");",
              "Retract(\"LossesBurnRate_None\");"
            ]
          },
          {
            "name": "LossesBurnRate_Decline",
            "desc": "",
            "salience": 0,
            "when": "input.AlLossesBurnRate.Valid && input.AlLossesBurnRate.Float32 > 20000",
            "then": [
              "output.SetLossesBurnRateDecision(\"Decline\", \"AutoLiability\");",
              "Retract(\"LossesBurnRate_Decline\");"
            ]
          },
          {
            "name": "YearsInBusiness_None",
            "desc": "",
            "salience": 0,
            "when": "input.TotalMonths.Valid && input.TotalMonths.Int > 12",
            "then": [
              "output.SetYearsInBusinessDecision(\"None\");",
              "Retract(\"YearsInBusiness_None\");"
            ]
          },
          {
            "name": "YearsInBusiness_Decline",
            "desc": "",
            "salience": 0,
            "when": "input.TotalMonths.Valid && input.TotalMonths.Int < 12",
            "then": [
              "output.SetYearsInBusinessDecision(\"Decline\");",
              "Retract(\"YearsInBusiness_Decline\");"
            ]
          },
          {
            "name": "DotRating_None",
            "desc": "",
            "salience": 0,
            "when": "input.DotRating.Valid && input.DotRating.String != \"ConditionalRating\"",
            "then": [
              "output.SetDotRatingDecision(\"None\");",
              "Retract(\"DotRating_None\");"
            ]
          },
          {
            "name": "DotRating_ConditionalRating",
            "desc": "",
            "salience": 0,
            "when": "input.DotRating.Valid && input.DotRating.String == \"ConditionalRating\"",
            "then": [
              "output.SetDotRatingDecision(\"Decline\");",
              "Retract(\"DotRating_ConditionalRating\");"
            ]
          }
        ]'::jsonb,
        now(),
        '<EMAIL>');