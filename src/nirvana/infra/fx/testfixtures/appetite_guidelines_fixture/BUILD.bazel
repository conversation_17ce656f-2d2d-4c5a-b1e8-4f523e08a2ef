load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "appetite_guidelines_fixture",
    srcs = [
        "fixture.go",
        "fx.go",
    ],
    embedsrcs = ["rule_set.sql"],
    importpath = "nirvanatech.com/nirvana/infra/fx/testfixtures/appetite_guidelines_fixture",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/db-api",
        "//nirvana/infra/fx/fxregistry",
        "@org_uber_go_fx//:fx",
    ],
)
