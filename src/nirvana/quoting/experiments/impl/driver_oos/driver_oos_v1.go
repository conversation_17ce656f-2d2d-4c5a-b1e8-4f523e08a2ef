package driver_oos

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/safety"
)

var V1ExperimentId = uuid.MustParse("e3c2b1a4-d5f6-4e7a-9b8c-1d2e3f4a5b6c")

const driverOOSThresholdPercent = 10

type v1Deps struct {
	fx.In

	ApplicationWrapper application.DataWrapper
	SafetyFetcher      safety.SafetyFetcher
}

type DriverOOSV1 struct {
	deps v1Deps
}

func newDriverOOSV1(deps v1Deps) *DriverOOSV1 {
	return &DriverOOSV1{
		deps: deps,
	}
}

func (d *DriverOOSV1) Id() uuid.UUID {
	return V1ExperimentId
}

func (d *DriverOOSV1) IsApplicable(_ context.Context, _ uuid.UUID) (bool, error) {
	// All applications are applicable for this experiment
	return true, nil
}

func (d *DriverOOSV1) Coverages() []enums.Coverage {
	return []enums.Coverage{
		enums.CoverageAutoLiability,
	}
}

func (d *DriverOOSV1) Apply(
	ctx context.Context,
	applicationId uuid.UUID,
	submissionId uuid.UUID,
	applicationReviewId *uuid.UUID,
) (*experiments.Applicability, *common.ExperimentMetadata, error) {
	// Get active coverages that this experiment applies to
	submission, err := d.deps.ApplicationWrapper.GetSubmissionById(ctx, submissionId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get submission %s", submissionId)
	}

	activeCoverages := common.GetActiveCoveragesForExperiment(submission, d.Coverages())
	if len(activeCoverages) == 0 {
		applicability, metadata := d.buildApplicability(
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	driverOOSPercentage, dataAvailable, err := d.getDriverOOSPercentage(ctx, submission.CompanyInfo.DOTNumber, &submission.CoverageInfo.EffectiveDate)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get driver OOS percentage for DOT %d", submission.CompanyInfo.DOTNumber)
	}

	// If no data is available, return non-applied applicability without inserting risk factor
	if !dataAvailable {
		applicability, metadata := d.buildApplicability(
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			activeCoverages,
		)
		return applicability, metadata, nil
	}

	credit := calculateCredit(driverOOSPercentage)

	valueUsed := fmt.Sprintf("%.1f%%", driverOOSPercentage)

	applicability, metadata := d.buildApplicability(
		applicationId.String(),
		submissionId,
		credit,
		true,
		valueUsed,
		activeCoverages,
	)

	return applicability, metadata, nil
}

// getDriverOOSPercentage fetches the Driver OOS percentage from the SafetyFetcher.
// This method uses graceful error handling only for data not found errors (sql.ErrNoRows).
// Other errors are propagated to the caller.
//
// It returns the Driver OOS percentage, a boolean indicating if data is available,
// and an error if any occurred during the fetch.
func (d *DriverOOSV1) getDriverOOSPercentage(ctx context.Context, dotNumber int64, effectiveDate *time.Time) (float32, bool, error) {
	oosData, _, _, err := d.deps.SafetyFetcher.OOSViolations(ctx, dotNumber, effectiveDate)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return 0, false, nil
		}
		return 0, false, errors.Wrapf(err, "failed to fetch driver OOS percentage for dot %d", dotNumber)
	}

	// Find the Driver OOS data from the returned slice
	for _, item := range oosData {
		if item.Category == oapi_common.Driver {
			return item.OosPercentage, true, nil
		}
	}

	return 0, false, nil
}

// getExplanation returns the explanation for the driver OOS experiment
func (d *DriverOOSV1) getExplanation(credit int32, valueUsed string) string {
	return fmt.Sprintf("Driver OOS Adjustment applied: %d%%. Driver OOS Percentage: %s", credit, valueUsed)
}

func (d *DriverOOSV1) buildApplicability(
	applicationId string,
	submissionId uuid.UUID,
	credit int32,
	applied bool,
	valueUsed string,
	activeCoverages []enums.Coverage,
) (*experiments.Applicability, *common.ExperimentMetadata) {
	applicabilityCreditsByCoverage := make(map[string]int)
	creditsByCoverage := make(map[enums.Coverage]int32)
	for _, coverage := range activeCoverages {
		applicabilityCreditsByCoverage[coverage.String()] = int(credit)
		creditsByCoverage[coverage] = credit
	}

	applicability := common.BuildExperimentsApplicability(
		d.Id(),
		applicationId,
		submissionId,
		applicabilityCreditsByCoverage,
		applied,
	)

	var explanation string
	if applied {
		explanation = d.getExplanation(credit, valueUsed)
	}

	metadata := common.NewExperimentMetadata(credit, valueUsed, creditsByCoverage, explanation)

	return applicability, metadata
}

// calculateCredit determines the pricing adjustment based on Driver OOS percentage
func calculateCredit(driverOOSPercentage float32) int32 {
	if driverOOSPercentage > driverOOSThresholdPercent {
		return -5
	}
	return 0
}
