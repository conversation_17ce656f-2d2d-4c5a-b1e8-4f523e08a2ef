package driver_oos

import (
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/submission_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/safety"
)

type driverOOSV1ExperimentTestSuiteEnv struct {
	fx.In

	AppReviewFixture   *application_review_fixture.ApplicationReviewsFixture
	SubmissionFixture  *submission_fixture.SubmissionsFixture
	AppReviewWrapper   uw.ApplicationReviewWrapper
	ApplicationWrapper application.DataWrapper
	Experiment         *DriverOOSV1
	ExperimentsWrapper *db.ExperimentsWrapper
}

type driverOOSV1ExperimentTestSuite struct {
	suite.Suite
	env   driverOOSV1ExperimentTestSuiteEnv
	fxApp *fxtest.App
	ctx   context.Context
	ctrl  *gomock.Controller

	mockSafetyFetcher *safety.MockSafetyFetcher

	appId        uuid.UUID
	experimentId uuid.UUID
	submissionId uuid.UUID
	appReviewId  uuid.UUID

	originalSubmissionCoverageDetails []application.CoverageDetails
}

func TestDriverOOSV1Experiment(t *testing.T) {
	suite.Run(t, new(driverOOSV1ExperimentTestSuite))
}

func (s *driverOOSV1ExperimentTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())
	s.experimentId = V1ExperimentId

	s.mockSafetyFetcher = safety.NewMockSafetyFetcher(s.ctrl)
	newMockSafetyFetcher := func() safety.SafetyFetcher {
		return s.mockSafetyFetcher
	}

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockSafetyFetcher)),
	)

	s.appId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.ApplicationID)
	s.submissionId = uuid.MustParse(s.env.SubmissionFixture.UWSubmission.ID)
	s.appReviewId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.Id)

	// Store original coverage details for cleanup
	s.originalSubmissionCoverageDetails = s.env.SubmissionFixture.UWSubmission.CoverageInfo.Coverages

	// Ensure Auto Liability coverage is active for the experiment
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = []application.CoverageDetails{
			{CoverageType: enums.CoverageAutoLiability},
		}
		return sub, nil
	})
	s.Require().NoError(err)

	experiment := experiments.NewExperimentBuilder().
		WithMockData_TestOnly().
		WithId(s.experimentId).WithEnabled(true).
		Build()

	err = s.env.ExperimentsWrapper.InsertExperiment(s.ctx, experiment)
	s.Require().NoError(err)
}

func (s *driverOOSV1ExperimentTestSuite) TearDownSuite() {
	s.ctrl.Finish()
	s.fxApp.RequireStop()
}

func (s *driverOOSV1ExperimentTestSuite) TearDownTest() {
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = s.originalSubmissionCoverageDetails
		return sub, nil
	})
	s.Require().NoError(err)

	// Insert invalid applicability to reset experiment state
	applicability := experiments.NewApplicabilityBuilder().
		WithMockData_TestOnly().
		WithExperimentId(s.experimentId).
		WithSubjectId(s.env.AppReviewFixture.ApplicationReview.ApplicationID).
		WithApplied(false).
		Build()
	err = s.env.ExperimentsWrapper.InsertApplicability(s.ctx, applicability)
	if err != nil {
		// Don't fail test teardown if database operations fail
		s.T().Logf("Warning: Failed to insert reset applicability: %v", err)
	}
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_ID() {
	id := s.env.Experiment.Id()
	s.Equal(V1ExperimentId, id)
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_IsApplicable() {
	id := uuid.New()
	// This experiment is applicable to all applications, so we can use any ID
	applicable, err := s.env.Experiment.IsApplicable(s.ctx, id)
	s.NoError(err)
	s.True(applicable)
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Coverages() {
	coverages := s.env.Experiment.Coverages()
	s.Equal([]enums.Coverage{enums.CoverageAutoLiability}, coverages)
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Apply_WithLowPercentageSuccess() {
	// Test scenario: 5% Driver OOS percentage (<=10%)
	// Expected outcome: 0% adjustment
	oosData := oapi_common.OOSViolations{
		{
			Category:      oapi_common.Driver,
			OosPercentage: float32(5.0),
		},
	}
	s.mockSafetyFetcher.EXPECT().
		OOSViolations(gomock.Any(), s.env.SubmissionFixture.UWSubmission.CompanyInfo.DOTNumber, gomock.Any()).
		Return(oosData, time.Now(), time.Now(), nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("5.0%", metadata.ValueUsed)
	s.Equal("Driver OOS Adjustment applied: 0%. Driver OOS Percentage: 5.0%", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.env.Experiment.Id(), applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Apply_WithHighPercentageSuccess() {
	// Test scenario: 15% Driver OOS percentage (>10%)
	// Expected outcome: -10 credit applied to Auto Liability coverage (penalty for high OOS rate)
	oosData := oapi_common.OOSViolations{
		{
			Category:      oapi_common.Driver,
			OosPercentage: float32(15.0),
		},
	}
	s.mockSafetyFetcher.EXPECT().
		OOSViolations(gomock.Any(), s.env.SubmissionFixture.UWSubmission.CompanyInfo.DOTNumber, gomock.Any()).
		Return(oosData, time.Now(), time.Now(), nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(-5), metadata.CreditApplied)
	s.Equal("15.0%", metadata.ValueUsed)
	s.Equal("Driver OOS Adjustment applied: -5%. Driver OOS Percentage: 15.0%", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.env.Experiment.Id(), applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(-5, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Apply_WithExactThresholdPercentageSuccess() {
	// Test scenario: exactly 10% Driver OOS percentage (boundary case)
	// Expected outcome: 0% adjustment (<=10% rule)
	oosData := oapi_common.OOSViolations{
		{
			Category:      oapi_common.Driver,
			OosPercentage: float32(10.0),
		},
	}
	s.mockSafetyFetcher.EXPECT().
		OOSViolations(gomock.Any(), s.env.SubmissionFixture.UWSubmission.CompanyInfo.DOTNumber, gomock.Any()).
		Return(oosData, time.Now(), time.Now(), nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("10.0%", metadata.ValueUsed)
	s.Equal("Driver OOS Adjustment applied: 0%. Driver OOS Percentage: 10.0%", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.env.Experiment.Id(), applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Apply_WithMissingOOSData() {
	// Test scenario: OOS data is unavailable due to sql.ErrNoRows (graceful handling)
	// Expected outcome: 0% adjustment
	s.mockSafetyFetcher.EXPECT().
		OOSViolations(gomock.Any(), s.env.SubmissionFixture.UWSubmission.CompanyInfo.DOTNumber, gomock.Any()).
		Return(nil, time.Time{}, time.Time{}, sql.ErrNoRows).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.env.Experiment.Id(), applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.False(applicability.Applied)
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Apply_WithSafetyFetcherError() {
	// Test scenario: SafetyFetcher returns a non-sql.ErrNoRows error (should propagate)
	// Expected outcome: Error should be returned, no applicability
	networkError := errors.New("network connection failed")
	s.mockSafetyFetcher.EXPECT().
		OOSViolations(gomock.Any(), s.env.SubmissionFixture.UWSubmission.CompanyInfo.DOTNumber, gomock.Any()).
		Return(nil, time.Time{}, time.Time{}, networkError).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().Error(err)
	s.Require().Nil(metadata)
	s.Require().Nil(applicability)
	s.Contains(err.Error(), "failed to get driver OOS percentage for DOT")
	s.Contains(err.Error(), "network connection failed")
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Apply_WithNoDriverOOSData() {
	// Test scenario: OOS data exists but no Driver category data (graceful handling)
	// Expected outcome: 0% adjustment
	oosData := oapi_common.OOSViolations{
		{
			Category:      oapi_common.Vehicle, // Only vehicle data, no driver data
			OosPercentage: 20.0,
		},
	}
	s.mockSafetyFetcher.EXPECT().
		OOSViolations(gomock.Any(), s.env.SubmissionFixture.UWSubmission.CompanyInfo.DOTNumber, gomock.Any()).
		Return(oosData, time.Now(), time.Now(), nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.env.Experiment.Id(), applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.False(applicability.Applied)
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Apply_WithZeroPercentDriverOOSData() {
	// Test scenario: Driver OOS data exists with 0% (data available, but 0% rate)
	// Expected outcome: 0% adjustment
	oosData := oapi_common.OOSViolations{
		{
			Category:      oapi_common.Driver,
			OosPercentage: float32(0.0),
		},
	}
	s.mockSafetyFetcher.EXPECT().
		OOSViolations(gomock.Any(), s.env.SubmissionFixture.UWSubmission.CompanyInfo.DOTNumber, gomock.Any()).
		Return(oosData, time.Now(), time.Now(), nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("0.0%", metadata.ValueUsed)
	s.Equal("Driver OOS Adjustment applied: 0%. Driver OOS Percentage: 0.0%", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.env.Experiment.Id(), applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
}

func (s *driverOOSV1ExperimentTestSuite) TestDriverOOSV1_Apply_WithNoCoverageIntersection() {
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(),
		func(sub application.SubmissionObject) (application.SubmissionObject, error) {
			sub.CoverageInfo.Coverages = []application.CoverageDetails{
				{CoverageType: enums.CoverageGeneralLiability}, // Not applicable to driver OOS experiment
			}
			return sub, nil
		})
	s.Require().NoError(err)

	// No mocks needed - when there are no active coverages, the implementation returns early
	// before calling SafetyFetcher.OOSViolations or any risk factor operations

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.env.Experiment.Id(), applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.False(applicability.Applied)
}

func TestCalculateCredit(t *testing.T) {
	tests := []struct {
		name       string
		percentage float32
		want       int32
	}{
		{
			name:       "Zero percentage should return 0% adjustment",
			percentage: 0,
			want:       0,
		},
		{
			name:       "Low percentage (5%) should return 0% adjustment",
			percentage: 5.0,
			want:       0,
		},
		{
			name:       "Exactly 10% should return 0% adjustment",
			percentage: 10.0,
			want:       0,
		},
		{
			name:       "Just above threshold (10.1%) should return +10% debit",
			percentage: 10.1,
			want:       -5,
		},
		{
			name:       "High percentage (25%) should return +10% debit",
			percentage: 25.0,
			want:       -5,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := calculateCredit(tt.percentage)
			if got != tt.want {
				t.Errorf("calculateCredit(%v) = %v, want %v", tt.percentage, got, tt.want)
			}
		})
	}
}
