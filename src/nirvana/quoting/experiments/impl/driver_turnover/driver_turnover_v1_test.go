package driver_turnover

import (
	"context"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/submission_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

type driverTurnoverV1ExperimentTestSuiteEnv struct {
	fx.In

	AppReviewFixture   *application_review_fixture.ApplicationReviewsFixture
	SubmissionFixture  *submission_fixture.SubmissionsFixture
	AppReviewWrapper   uw.ApplicationReviewWrapper
	ApplicationWrapper application.DataWrapper
	Experiment         *DriverTurnoverV1
	ExperimentsWrapper *db.ExperimentsWrapper
}

type driverTurnoverV1ExperimentTestSuite struct {
	suite.Suite
	env   driverTurnoverV1ExperimentTestSuiteEnv
	fxApp *fxtest.App
	ctx   context.Context
	ctrl  *gomock.Controller

	appId        uuid.UUID
	experimentId uuid.UUID
	submissionId uuid.UUID
	appReviewId  uuid.UUID

	originalSubmissionCoverageDetails []application.CoverageDetails
}

func TestDriverTurnoverV1Experiment(t *testing.T) {
	suite.Run(t, new(driverTurnoverV1ExperimentTestSuite))
}

func (s *driverTurnoverV1ExperimentTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())
	s.experimentId = V1ExperimentId

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
	)

	s.appId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.ApplicationID)
	s.submissionId = uuid.MustParse(s.env.SubmissionFixture.UWSubmission.ID)
	s.appReviewId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.Id)

	// Store original coverage details for cleanup
	s.originalSubmissionCoverageDetails = s.env.SubmissionFixture.UWSubmission.CoverageInfo.Coverages

	// Ensure Auto Liability coverage is active for the experiment
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = []application.CoverageDetails{
			{CoverageType: enums.CoverageAutoLiability},
		}
		return sub, nil
	})
	s.Require().NoError(err)

	experiment := experiments.NewExperimentBuilder().
		WithMockData_TestOnly().
		WithId(s.experimentId).WithEnabled(true).
		Build()

	err = s.env.ExperimentsWrapper.InsertExperiment(s.ctx, experiment)
	s.Require().NoError(err)
}

func (s *driverTurnoverV1ExperimentTestSuite) TearDownSuite() {
	s.ctrl.Finish()
	s.fxApp.RequireStop()
}

func (s *driverTurnoverV1ExperimentTestSuite) TearDownTest() {
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = s.originalSubmissionCoverageDetails
		return sub, nil
	})
	s.NoError(err)

	// Insert invalid applicability to reset experiment state
	applicability := experiments.NewApplicabilityBuilder().
		WithMockData_TestOnly().
		WithExperimentId(s.experimentId).
		WithSubjectId(s.env.AppReviewFixture.ApplicationReview.ApplicationID).
		WithApplied(false).
		Build()
	err = s.env.ExperimentsWrapper.InsertApplicability(s.ctx, applicability)
	s.Require().NoError(err)
}

func (s *driverTurnoverV1ExperimentTestSuite) TestDriverTurnoverV1_ID() {
	id := s.env.Experiment.Id()
	s.Equal(V1ExperimentId, id)
}

func (s *driverTurnoverV1ExperimentTestSuite) TestDriverTurnoverV1_IsApplicable() {
	id := uuid.New()
	// This experiment is applicable to all applications, so we can use any ID
	applicable, err := s.env.Experiment.IsApplicable(s.ctx, id)
	s.NoError(err)
	s.True(applicable)
}

func (s *driverTurnoverV1ExperimentTestSuite) TestDriverTurnoverV1_Coverages() {
	coverages := s.env.Experiment.Coverages()
	s.Equal([]enums.Coverage{enums.CoverageAutoLiability}, coverages)
}

func (s *driverTurnoverV1ExperimentTestSuite) TestDriverTurnoverV1_Apply_WithMock() {
	// Create mock
	mockAppReviewManager := NewMockApplicationReviewManagerInterface(s.ctrl)

	// Create instance with mock (including all required dependencies)
	driverTurnover := &DriverTurnoverV1{
		deps: v1Deps{
			ApplicationWrapper:       s.env.ApplicationWrapper,
			ApplicationReviewManager: mockAppReviewManager,
		},
	}

	appReview := s.env.AppReviewFixture.ApplicationReview

	// Test case 1: High driver turnover (≥75) should get -7% credit and applied=true
	s.Run("apply experiment with high driver turnover", func() {
		expectedDriversList := &oapi_uw.ApplicationReviewDriversList{
			Summary: oapi_uw.ApplicationReviewDriversListSummary{
				TenureTurnover: 80, // High turnover
			},
		}

		mockAppReviewManager.EXPECT().
			GetDriversList(gomock.Any(), appReview.Id).
			Return(expectedDriversList, nil).
			Times(1)

		applicability, metadata, err := driverTurnover.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
		s.Require().NoError(err)
		s.Require().NotNil(metadata)
		s.Equal(int32(-7), metadata.CreditApplied)
		s.Equal("80.0", metadata.ValueUsed)
		s.Equal("Driver Turnover Adjustment applied: -7%. Driver Turnover: 80.0", metadata.Explanation)

		s.Require().NotNil(applicability)
		s.Equal(s.experimentId, applicability.ExperimentId)
		s.Equal(s.appId.String(), applicability.SubjectId)
		s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
		s.True(applicability.Applied)
		s.Len(applicability.Metadata.CreditsByCoverage, 1)
		s.Equal(-7, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	})

	// Test case 2: Low driver turnover (0-25) should get 0% credit
	s.Run("apply experiment with low driver turnover", func() {
		expectedDriversList := &oapi_uw.ApplicationReviewDriversList{
			Summary: oapi_uw.ApplicationReviewDriversListSummary{
				TenureTurnover: 15, // Low turnover
			},
		}

		mockAppReviewManager.EXPECT().
			GetDriversList(gomock.Any(), appReview.Id).
			Return(expectedDriversList, nil).
			Times(1)

		applicability, metadata, err := driverTurnover.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
		s.Require().NoError(err)
		s.Require().NotNil(metadata)
		s.Equal(int32(0), metadata.CreditApplied)
		s.Equal("15.0", metadata.ValueUsed)
		s.Equal("Driver Turnover Adjustment applied: 0%. Driver Turnover: 15.0", metadata.Explanation)

		s.Require().NotNil(applicability)
		s.Equal(s.experimentId, applicability.ExperimentId)
		s.Equal(s.appId.String(), applicability.SubjectId)
		s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
		s.True(applicability.Applied)
		s.Len(applicability.Metadata.CreditsByCoverage, 1)
		s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	})

	// Test case 3: Zero driver turnover should get -25% credit and applied=true
	s.Run("apply experiment with zero driver turnover", func() {
		expectedDriversList := &oapi_uw.ApplicationReviewDriversList{
			Summary: oapi_uw.ApplicationReviewDriversListSummary{
				TenureTurnover: 0, // Zero turnover
			},
		}

		mockAppReviewManager.EXPECT().
			GetDriversList(gomock.Any(), appReview.Id).
			Return(expectedDriversList, nil).
			Times(1)

		applicability, metadata, err := driverTurnover.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
		s.Require().NoError(err)
		s.Require().NotNil(metadata)
		s.Equal(int32(-25), metadata.CreditApplied)
		s.Equal("0.0", metadata.ValueUsed)
		s.Equal("Driver Turnover Adjustment applied: -25%. Driver Turnover: 0.0", metadata.Explanation)

		s.Require().NotNil(applicability)
		s.Equal(s.experimentId, applicability.ExperimentId)
		s.Equal(s.appId.String(), applicability.SubjectId)
		s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
		s.True(applicability.Applied)
		s.Len(applicability.Metadata.CreditsByCoverage, 1)
		s.Equal(-25, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	})

	// Test case 4: No drivers data - should return non-applicable
	s.Run("apply experiment when no drivers data available", func() {
		mockAppReviewManager.EXPECT().
			GetDriversList(gomock.Any(), appReview.Id).
			Return(nil, nil).
			Times(1)

		applicability, metadata, err := driverTurnover.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
		s.Require().NoError(err)
		s.Require().NotNil(metadata)
		s.Equal(int32(0), metadata.CreditApplied)
		s.Equal("", metadata.ValueUsed)
		s.Equal("", metadata.Explanation)

		s.Require().NotNil(applicability)
		s.Equal(s.experimentId, applicability.ExperimentId)
		s.Equal(s.appId.String(), applicability.SubjectId)
		s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
		s.False(applicability.Applied)
		s.Len(applicability.Metadata.CreditsByCoverage, 1)
		s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	})

	// Test case 5: Error fetching drivers data - should return error
	s.Run("apply experiment when drivers data fetch fails", func() {
		expectedError := errors.New("failed to fetch drivers data")

		mockAppReviewManager.EXPECT().
			GetDriversList(gomock.Any(), appReview.Id).
			Return(nil, expectedError).
			Times(1)

		applicability, metadata, err := driverTurnover.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
		s.Require().Error(err)
		s.Require().Nil(applicability)
		s.Require().Nil(metadata)
		s.Contains(err.Error(), "failed to get driver turnover")
	})
}

func (s *driverTurnoverV1ExperimentTestSuite) Test_CalculateCredit() {
	tests := []struct {
		name           string
		driverTurnover float32
		wantCredit     int32
	}{
		{
			name:           "driver turnover of 0 should return 25% credit",
			driverTurnover: 0,
			wantCredit:     -25,
		},
		{
			name:           "driver turnover lower than 25 should return 0% credit",
			driverTurnover: 20,
			wantCredit:     0,
		},
		{
			name:           "driver turnover higher or equal to 25 but lower than 50 should return 0% credit",
			driverTurnover: 30,
			wantCredit:     0,
		},
		{
			name:           "driver turnover higher or equal to 50 but lower than 75 should return 10% debit",
			driverTurnover: 60,
			wantCredit:     -5,
		},
		{
			name:           "driver turnover higher or equal to 75 should return 10% debit",
			driverTurnover: 80,
			wantCredit:     -7,
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			got, err := calculateCredit(tt.driverTurnover)
			s.Require().NoError(err)
			s.Require().Equal(tt.wantCredit, got)
		})
	}
}
