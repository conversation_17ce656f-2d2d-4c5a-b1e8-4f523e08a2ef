package driver_turnover

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
)

var V1ExperimentId = uuid.MustParse("f7e8d9c6-b5a4-3210-9876-543210fedcba")

type v1Deps struct {
	fx.In

	ApplicationWrapper       application.DataWrapper
	ApplicationReviewManager ApplicationReviewManagerInterface
}

type DriverTurnoverV1 struct {
	deps v1Deps
}

func newDriverTurnoverV1(deps v1Deps) *DriverTurnoverV1 {
	return &DriverTurnoverV1{deps: deps}
}

func (b *DriverTurnoverV1) Id() uuid.UUID {
	return V1ExperimentId
}

func (b *DriverTurnoverV1) IsApplicable(_ context.Context, _ uuid.UUID) (bool, error) {
	return true, nil
}

func (b *DriverTurnoverV1) Coverages() []enums.Coverage {
	return []enums.Coverage{
		enums.CoverageAutoLiability,
	}
}

func (b *DriverTurnoverV1) Apply(
	ctx context.Context,
	applicationId uuid.UUID,
	submissionId uuid.UUID,
	applicationReviewId *uuid.UUID,
) (*experiments.Applicability, *common.ExperimentMetadata, error) {
	if applicationReviewId == nil {
		applicability, metadata := b.buildApplicability(
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	sub, err := b.deps.ApplicationWrapper.GetSubmissionById(ctx, submissionId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get submission %s", submissionId)
	}

	activeCoverages := common.GetActiveCoveragesForExperiment(sub, b.Coverages())
	if len(activeCoverages) == 0 {
		// If there are no active coverages, we return an applicability with zero credit
		applicability, metadata := b.buildApplicability(
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	driverTurnover, isApplicable, err := b.getDriverTurnover(ctx, applicationReviewId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get driver turnover for submission %s", submissionId)
	}

	if !isApplicable {
		applicability, metadata := b.buildApplicability(
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			activeCoverages,
		)
		return applicability, metadata, nil
	}

	credit, err := calculateCredit(driverTurnover)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to calculate credit for driver turnover")
	}

	valueUsed := fmt.Sprintf("%.1f", driverTurnover)

	applicability, metadata := b.buildApplicability(
		applicationId.String(),
		submissionId,
		credit,
		true,
		valueUsed,
		activeCoverages,
	)

	return applicability, metadata, nil
}

func (b *DriverTurnoverV1) getDriverTurnover(
	ctx context.Context,
	appReviewID string,
) (float32, bool, error) {
	driversList, err := b.deps.ApplicationReviewManager.GetDriversList(ctx, appReviewID)
	if err != nil {
		return 0, false, errors.Wrapf(err, "failed to get driver list for appReview %s", appReviewID)
	}

	if driversList == nil {
		return 0, false, nil
	}

	return driversList.Summary.TenureTurnover, true, nil
}

func calculateCredit(driverTurnover float32) (int32, error) {
	switch {
	case driverTurnover == 0:
		return -25, nil
	case 0 < driverTurnover && driverTurnover < 50:
		return 0, nil
	case 50 <= driverTurnover && driverTurnover < 75:
		return -5, nil
	case 75 <= driverTurnover && driverTurnover <= 100:
		return -7, nil
	default:
		return 0, errors.New("invalid driver turnover")
	}
}

// getExplanation returns the explanation for the driver turnover experiment
func (b *DriverTurnoverV1) getExplanation(credit int32, valueUsed string) string {
	return fmt.Sprintf("Driver Turnover Adjustment applied: %d%%. Driver Turnover: %s", credit, valueUsed)
}

func (b *DriverTurnoverV1) buildApplicability(
	applicationId string,
	submissionId uuid.UUID,
	credit int32,
	applied bool,
	valueUsed string,
	activeCoverages []enums.Coverage,
) (*experiments.Applicability, *common.ExperimentMetadata) {
	applicabilityCreditsByCoverage := make(map[string]int)
	creditsByCoverage := make(map[enums.Coverage]int32)
	for _, coverage := range activeCoverages {
		applicabilityCreditsByCoverage[coverage.String()] = int(credit)
		creditsByCoverage[coverage] = credit
	}

	applicability := common.BuildExperimentsApplicability(
		b.Id(),
		applicationId,
		submissionId,
		applicabilityCreditsByCoverage,
		applied,
	)

	var explanation string
	if applied {
		explanation = b.getExplanation(credit, valueUsed)
	}

	metadata := common.NewExperimentMetadata(credit, valueUsed, creditsByCoverage, explanation)

	return applicability, metadata
}
