package primary_operating_class

import (
	"context"
	"fmt"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
)

var V1ExperimentId = uuid.MustParse("a2c8b0a2-4d1e-4b5c-9f3d-7a2e6f8b1c5e")

type v1Deps struct {
	fx.In

	ApplicationWrapper application.DataWrapper
	AppReviewWrapper   uw.ApplicationReviewWrapper
}

type PrimaryOperatingClassV1 struct {
	deps v1Deps
}

func newPrimaryOperatingClassV1(deps v1Deps) *PrimaryOperatingClassV1 {
	return &PrimaryOperatingClassV1{
		deps: deps,
	}
}

func (p *PrimaryOperatingClassV1) Id() uuid.UUID {
	return V1ExperimentId
}

func (p *PrimaryOperatingClassV1) IsApplicable(_ context.Context, _ uuid.UUID) (bool, error) {
	return true, nil
}

func (p *PrimaryOperatingClassV1) Coverages() []enums.Coverage {
	return []enums.Coverage{
		enums.CoverageAutoLiability,
	}
}

func (p *PrimaryOperatingClassV1) Apply(
	ctx context.Context,
	applicationId uuid.UUID,
	submissionId uuid.UUID,
	_ *uuid.UUID,
) (*experiments.Applicability, *common.ExperimentMetadata, error) {
	submission, err := p.deps.ApplicationWrapper.GetSubmissionById(ctx, submissionId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get submission %s", submissionId)
	}

	activeCoverages := common.GetActiveCoveragesForExperiment(submission, p.Coverages())
	if len(activeCoverages) == 0 {
		// If there are no active coverages, we return an applicability with zero credit
		applicability, metadata := p.BuildApplicability(
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	primaryOperatingClass, err := p.getPrimaryOperatingClass(submission)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get primary operating class for submission %s", submissionId)
	}

	creditApplied := calculateCredit(*primaryOperatingClass)

	valueUsed := strings.TrimPrefix(primaryOperatingClass.String(), "OperatingClass")

	applicability, metadata := p.BuildApplicability(
		applicationId.String(),
		submissionId,
		creditApplied,
		true,
		valueUsed,
		activeCoverages,
	)
	return applicability, metadata, nil
}

func (p *PrimaryOperatingClassV1) getPrimaryOperatingClass(
	submission *application.SubmissionObject,
) (*enums.OperatingClass, error) {
	if submission.EquipmentInfo == nil {
		return nil, errors.New("equipment info not found in submission")
	}

	if submission.EquipmentInfo.PrimaryOperatingClass == nil {
		return nil, errors.New("primary operating class not found in submission")
	}

	return submission.EquipmentInfo.PrimaryOperatingClass, nil
}

func calculateCredit(primaryOperatingClass enums.OperatingClass) int32 {
	switch primaryOperatingClass {
	case enums.OperatingClassDryVan:
		return -3
	case enums.OperatingClassRefrigerated, enums.OperatingClassFlatbed:
		return 5
	default:
		return 0
	}
}

func (p *PrimaryOperatingClassV1) getExplanation(credit int32, valueUsed string) string {
	return fmt.Sprintf("Primary Operation Adjustment applied: %d%%. Primary Operation Category: %s", credit, valueUsed)
}

func (p *PrimaryOperatingClassV1) BuildApplicability(
	applicationId string,
	submissionId uuid.UUID,
	credit int32,
	applied bool,
	valueUsed string,
	activeCoverages []enums.Coverage,
) (*experiments.Applicability, *common.ExperimentMetadata) {
	applicabilityCreditsByCoverage := make(map[string]int)
	creditsByCoverage := make(map[enums.Coverage]int32)
	for _, coverage := range activeCoverages {
		applicabilityCreditsByCoverage[coverage.String()] = int(credit)
		creditsByCoverage[coverage] = credit
	}

	applicability := common.BuildExperimentsApplicability(
		p.Id(),
		applicationId,
		submissionId,
		applicabilityCreditsByCoverage,
		applied,
	)

	var explanation string
	if applied {
		explanation = p.getExplanation(credit, valueUsed)
	}

	metadata := common.NewExperimentMetadata(credit, valueUsed, creditsByCoverage, explanation)

	return applicability, metadata
}
