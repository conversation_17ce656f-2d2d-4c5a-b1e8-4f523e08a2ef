package primary_operating_class

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/submission_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type primaryOperatingClassV1ExperimentTestSuiteEnv struct {
	fx.In

	AppReviewFixture   *application_review_fixture.ApplicationReviewsFixture
	SubmissionFixture  *submission_fixture.SubmissionsFixture
	AppReviewWrapper   uw.ApplicationReviewWrapper
	ApplicationWrapper application.DataWrapper
	Experiment         *PrimaryOperatingClassV1
	ExperimentsWrapper *db.ExperimentsWrapper
}

type primaryOperatingClassV1ExperimentTestSuite struct {
	suite.Suite
	env   primaryOperatingClassV1ExperimentTestSuiteEnv
	fxApp *fxtest.App
	ctx   context.Context
	ctrl  *gomock.Controller

	appId        uuid.UUID
	experimentId uuid.UUID
	submissionId uuid.UUID
	appReviewId  uuid.UUID

	originalSubmissionCoverageDetails []application.CoverageDetails
}

func TestPrimaryOperatingClassV1Experiment(t *testing.T) {
	suite.Run(t, new(primaryOperatingClassV1ExperimentTestSuite))
}

func (s *primaryOperatingClassV1ExperimentTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())
	s.experimentId = V1ExperimentId

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
	)

	s.appId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.ApplicationID)
	s.submissionId = uuid.MustParse(s.env.SubmissionFixture.UWSubmission.ID)
	s.appReviewId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.Id)

	// Store original coverage details for cleanup
	s.originalSubmissionCoverageDetails = s.env.SubmissionFixture.UWSubmission.CoverageInfo.Coverages

	// Ensure Auto Liability coverage is active for the experiment
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = []application.CoverageDetails{
			{CoverageType: enums.CoverageAutoLiability},
		}
		dryVan := enums.OperatingClassDryVan
		sub.EquipmentInfo.PrimaryOperatingClass = &dryVan
		return sub, nil
	})
	s.Require().NoError(err)

	experiment := experiments.NewExperimentBuilder().
		WithMockData_TestOnly().
		WithId(s.experimentId).WithEnabled(true).
		Build()

	err = s.env.ExperimentsWrapper.InsertExperiment(s.ctx, experiment)
	s.Require().NoError(err)
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TearDownSuite() {
	s.ctrl.Finish()
	s.fxApp.RequireStop()
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TearDownTest() {
	// Restore original coverage details
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = s.originalSubmissionCoverageDetails
		dryVan := enums.OperatingClassDryVan
		sub.EquipmentInfo.PrimaryOperatingClass = &dryVan
		return sub, nil
	})
	s.NoError(err)

	// Insert invalid applicability to reset experiment state
	applicability := experiments.NewApplicabilityBuilder().
		WithMockData_TestOnly().
		WithExperimentId(s.experimentId).
		WithSubjectId(s.env.AppReviewFixture.ApplicationReview.ApplicationID).
		WithApplied(false).
		Build()
	err = s.env.ExperimentsWrapper.InsertApplicability(s.ctx, applicability)
	s.Require().NoError(err)
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TestPrimaryOperatingClassV1_ID() {
	id := s.env.Experiment.Id()
	s.Equal(V1ExperimentId, id)
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TestPrimaryOperatingClassV1_IsApplicable() {
	id := uuid.New()
	// This experiment is applicable to all applications, so we can use any ID
	applicable, err := s.env.Experiment.IsApplicable(s.ctx, id)
	s.NoError(err)
	s.True(applicable)
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TestPrimaryOperatingClassV1_Coverages() {
	coverages := s.env.Experiment.Coverages()
	s.Equal([]enums.Coverage{enums.CoverageAutoLiability}, coverages)
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TestPrimaryOperatingClassV1_Apply_WithDryVanSuccess() {
	// Test scenario: Application with DryVan operating class
	// Expected outcome: 3% credit applied to Auto Liability coverage
	s.setOperatingClass(enums.OperatingClassDryVan)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(-3), metadata.CreditApplied)
	s.Equal("DryVan", metadata.ValueUsed)
	s.Equal("Primary Operation Adjustment applied: -3%. Primary Operation Category: DryVan", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(-3, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TestPrimaryOperatingClassV1_Apply_WithRefrigeratedSuccess() {
	// Test scenario: Application with Refrigerated operating class
	// Expected outcome: 5% credit applied to Auto Liability coverage
	s.setOperatingClass(enums.OperatingClassRefrigerated)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(5), metadata.CreditApplied)
	s.Equal("Refrigerated", metadata.ValueUsed)
	s.Equal("Primary Operation Adjustment applied: 5%. Primary Operation Category: Refrigerated", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(5, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TestPrimaryOperatingClassV1_Apply_WithFlatbedSuccess() {
	// Test scenario: Application with Flatbed operating class
	// Expected outcome: -5% credit applied to Auto Liability coverage
	s.setOperatingClass(enums.OperatingClassFlatbed)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(5), metadata.CreditApplied)
	s.Equal("Flatbed", metadata.ValueUsed)
	s.Equal("Primary Operation Adjustment applied: 5%. Primary Operation Category: Flatbed", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(5, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TestPrimaryOperatingClassV1_Apply_WithOtherOperatingClassSuccess() {
	// Test scenario: Application with other operating class (e.g., Tanker)
	// Expected outcome: 0% credit applied to Auto Liability coverage
	s.setOperatingClass(enums.OperatingClassTanker)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("Tanker", metadata.ValueUsed)
	s.Equal("Primary Operation Adjustment applied: 0%. Primary Operation Category: Tanker", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
}

func (s *primaryOperatingClassV1ExperimentTestSuite) TestPrimaryOperatingClassV1_Apply_WithNoCoverageIntersection() {
	// Modify submission to have no coverages that the experiment applies to
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(),
		func(sub application.SubmissionObject) (application.SubmissionObject, error) {
			sub.CoverageInfo.Coverages = []application.CoverageDetails{
				{CoverageType: enums.CoverageGeneralLiability}, // Not applicable to primary operating class experiment
			}
			return sub, nil
		})
	s.Require().NoError(err)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.Require().NoError(err)
	s.Require().NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.Require().NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(0, len(applicability.Metadata.CreditsByCoverage))
	s.False(applicability.Applied)
}

// Helper method to set the operating class in the submission
func (s *primaryOperatingClassV1ExperimentTestSuite) setOperatingClass(operatingClass enums.OperatingClass) {
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(submission application.SubmissionObject) (application.SubmissionObject, error) {
		submission.EquipmentInfo.PrimaryOperatingClass = &operatingClass
		return submission, nil
	})
	s.Require().NoError(err)

	// Refresh the app review to pick up the submission changes
	appReview, err := s.env.AppReviewWrapper.GetReview(s.ctx, s.env.AppReviewFixture.ApplicationReview.Id)
	s.Require().NoError(err)

	// Update the fixture with the refreshed app review
	s.env.AppReviewFixture.ApplicationReview = appReview
}

func TestCalculateCredit(t *testing.T) {
	tests := []struct {
		name           string
		operatingClass enums.OperatingClass
		want           int32
	}{
		{
			name:           "DryVan should return +3% credit",
			operatingClass: enums.OperatingClassDryVan,
			want:           -3,
		},
		{
			name:           "Refrigerated should return -5% credit",
			operatingClass: enums.OperatingClassRefrigerated,
			want:           5,
		},
		{
			name:           "Flatbed should return -5% credit",
			operatingClass: enums.OperatingClassFlatbed,
			want:           5,
		},
		{
			name:           "Tanker should return 0% credit",
			operatingClass: enums.OperatingClassTanker,
			want:           0,
		},
		{
			name:           "Intermodal should return 0% credit",
			operatingClass: enums.OperatingClassIntermodal,
			want:           0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := calculateCredit(tt.operatingClass)
			if got != tt.want {
				t.Errorf("calculateCredit(%v) = %v, want %v", tt.operatingClass, got, tt.want)
			}
		})
	}
}
