package basic_unsafe_driving_score

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/safety"
)

const (
	unsafeDrivingScoreThreshold = 65.0
)

// getUnsafeDrivingScore retrieves the unsafe driving score from safety data
func getUnsafeDrivingScore(
	ctx context.Context,
	safetyFetcher safety.SafetyFetcher,
	dotNumber int64,
) (float32, bool, error) {
	basicScoreThresholds, err := safetyFetcher.GetBasicScoresFromComputedMeasures(ctx, dotNumber)
	if err != nil {
		return 0, false, errors.Wrap(err, "failed to get basic scores")
	}

	unsafeDrivingScore := float32(0)
	isApplicable := false
	for _, score := range basicScoreThresholds {
		if score.Category == basic.UnsafeDriving.FMCSAName() {
			if score.Score != nil {
				unsafeDrivingScore = *score.Score
				isApplicable = true
			}
		}
	}

	return unsafeDrivingScore, isApplicable, nil
}

// buildUnsafeDrivingScoreApplicability creates an applicability for unsafe driving score experiments
func buildUnsafeDrivingScoreApplicability(
	experimentId uuid.UUID,
	applicationId string,
	submissionId uuid.UUID,
	credit int32,
	applied bool,
	valueUsed string,
	activeCoverages []enums.Coverage,
) (*experiments.Applicability, *common.ExperimentMetadata) {
	applicabilityCreditsByCoverage := make(map[string]int)
	creditsByCoverage := make(map[enums.Coverage]int32)
	for _, coverage := range activeCoverages {
		applicabilityCreditsByCoverage[coverage.String()] = int(credit)
		creditsByCoverage[coverage] = credit
	}

	applicability := common.BuildExperimentsApplicability(
		experimentId,
		applicationId,
		submissionId,
		applicabilityCreditsByCoverage,
		applied,
	)

	var explanation string
	if applied {
		explanation = getUnsafeDrivingScoreExplanation(credit, valueUsed)
	}

	metadata := common.NewExperimentMetadata(credit, valueUsed, creditsByCoverage, explanation)

	return applicability, metadata
}

// getUnsafeDrivingScoreExplanation returns the explanation for the basic unsafe driving score experiment
func getUnsafeDrivingScoreExplanation(credit int32, valueUsed string) string {
	return fmt.Sprintf("Unsafe Driving BASIC Score Adjustment applied: %d%%. Unsafe Driving Score: %s", credit, valueUsed)
}
