package basic_unsafe_driving_score

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/safety"
)

var V2ExperimentId = uuid.MustParse("b2c3d4e5-f6a7-8901-bcde-f23456789012")

type v2Deps struct {
	fx.In

	ApplicationWrapper application.DataWrapper
	UWSafetyFetcher    safety.SafetyFetcher
}

type BasicUnsafeDrivingScoreV2 struct {
	deps v2Deps
}

func newBasicUnsafeDrivingScoreV2(deps v2Deps) *BasicUnsafeDrivingScoreV2 {
	return &BasicUnsafeDrivingScoreV2{deps: deps}
}

func (b *BasicUnsafeDrivingScoreV2) Id() uuid.UUID {
	return V2ExperimentId
}

func (b *BasicUnsafeDrivingScoreV2) IsApplicable(_ context.Context, _ uuid.UUID) (bool, error) {
	return true, nil
}

func (b *BasicUnsafeDrivingScoreV2) Coverages() []enums.Coverage {
	return []enums.Coverage{
		enums.CoverageAutoLiability,
	}
}

func (b *BasicUnsafeDrivingScoreV2) Apply(
	ctx context.Context,
	applicationId uuid.UUID,
	submissionId uuid.UUID,
	_ *uuid.UUID,
) (*experiments.Applicability, *common.ExperimentMetadata, error) {
	sub, err := b.deps.ApplicationWrapper.GetSubmissionById(ctx, submissionId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get submission %s", submissionId)
	}

	activeCoverages := common.GetActiveCoveragesForExperiment(sub, b.Coverages())
	if len(activeCoverages) == 0 {
		// If there are no active coverages, we return an applicability with zero credit
		applicability, metadata := buildUnsafeDrivingScoreApplicability(
			b.Id(),
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	unsafeDrivingScore, isApplicable, err := getUnsafeDrivingScore(ctx, b.deps.UWSafetyFetcher, sub.CompanyInfo.DOTNumber)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get unsafe driving score for submission %s", submissionId)
	}

	if !isApplicable {
		applicability, metadata := buildUnsafeDrivingScoreApplicability(
			b.Id(),
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			activeCoverages,
		)
		return applicability, metadata, nil
	}

	credit := b.calculateCredit(unsafeDrivingScore)

	valueUsed := fmt.Sprintf("%.1f", unsafeDrivingScore)

	applicability, metadata := buildUnsafeDrivingScoreApplicability(
		b.Id(),
		applicationId.String(),
		submissionId,
		credit,
		true,
		valueUsed,
		activeCoverages,
	)

	return applicability, metadata, nil
}

// calculateCredit implements the V2 credit calculation logic with -10 credit for over threshold
func (b *BasicUnsafeDrivingScoreV2) calculateCredit(unsafeDrivingScore float32) int32 {
	if unsafeDrivingScore > unsafeDrivingScoreThreshold {
		return -10
	}

	return 0
}
