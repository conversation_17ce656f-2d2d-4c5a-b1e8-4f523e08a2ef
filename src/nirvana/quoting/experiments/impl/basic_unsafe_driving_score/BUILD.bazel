load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "basic_unsafe_driving_score",
    srcs = [
        "basic_unsafe_driving_score_v1.go",
        "basic_unsafe_driving_score_v2.go",
        "common.go",
        "fx.go",
    ],
    importpath = "nirvanatech.com/nirvana/quoting/experiments/impl/basic_unsafe_driving_score",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/experiments",
        "//nirvana/fmcsa/basic",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/quoting/experiments/impl/common",
        "//nirvana/quoting/experiments/impl/safety",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "basic_unsafe_driving_score_test",
    srcs = [
        "basic_unsafe_driving_score_v1_test.go",
        "basic_unsafe_driving_score_v2_test.go",
    ],
    embed = [":basic_unsafe_driving_score"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/experiments",
        "//nirvana/experiments/db",
        "//nirvana/fmcsa/basic",
        "//nirvana/infra/fx/testfixtures/application_review_fixture",
        "//nirvana/infra/fx/testfixtures/submission_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/quoting/experiments/impl/safety",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
