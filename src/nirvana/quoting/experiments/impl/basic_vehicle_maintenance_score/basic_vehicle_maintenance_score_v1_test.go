package basic_vehicle_maintenance_score

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/submission_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/safety"
)

type basicVehicleMaintenanceScoreV1ExperimentTestSuiteEnv struct {
	fx.In

	AppReviewFixture   *application_review_fixture.ApplicationReviewsFixture
	SubmissionFixture  *submission_fixture.SubmissionsFixture
	AppReviewWrapper   uw.ApplicationReviewWrapper
	ApplicationWrapper application.DataWrapper
	Experiment         *BasicVehicleMaintenanceScoreV1
	ExperimentsWrapper *db.ExperimentsWrapper
}

type basicVehicleMaintenanceScoreV1ExperimentTestSuite struct {
	suite.Suite
	env   basicVehicleMaintenanceScoreV1ExperimentTestSuiteEnv
	fxApp *fxtest.App
	ctx   context.Context
	ctrl  *gomock.Controller

	mockSafetyFetcher *safety.MockSafetyFetcher

	appId        uuid.UUID
	experimentId uuid.UUID
	submissionId uuid.UUID
	appReviewId  uuid.UUID

	originalSubmissionCoverageDetails []application.CoverageDetails
}

func TestBasicVehicleMaintenanceScoreV1Experiment(t *testing.T) {
	suite.Run(t, new(basicVehicleMaintenanceScoreV1ExperimentTestSuite))
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())
	s.experimentId = V1ExperimentId

	s.mockSafetyFetcher = safety.NewMockSafetyFetcher(s.ctrl)
	newMockSafetyFetcher := func() safety.SafetyFetcher {
		return s.mockSafetyFetcher
	}

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockSafetyFetcher)),
	)

	s.appId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.ApplicationID)
	s.submissionId = uuid.MustParse(s.env.SubmissionFixture.UWSubmission.ID)
	s.appReviewId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.Id)

	s.originalSubmissionCoverageDetails = s.env.SubmissionFixture.UWSubmission.CoverageInfo.Coverages

	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = []application.CoverageDetails{
			{CoverageType: enums.CoverageAutoLiability},
		}
		return sub, nil
	})
	s.Require().NoError(err)

	experiment := experiments.NewExperimentBuilder().
		WithMockData_TestOnly().
		WithId(s.experimentId).WithEnabled(true).
		Build()

	err = s.env.ExperimentsWrapper.InsertExperiment(s.ctx, experiment)
	s.Require().NoError(err)
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TearDownSuite() {
	s.ctrl.Finish()
	s.fxApp.RequireStop()
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TearDownTest() {
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = s.originalSubmissionCoverageDetails
		return sub, nil
	})
	s.NoError(err)

	// Insert invalid applicability to reset experiment state
	applicability := experiments.NewApplicabilityBuilder().
		WithMockData_TestOnly().
		WithExperimentId(s.experimentId).
		WithSubjectId(s.env.AppReviewFixture.ApplicationReview.ApplicationID).
		WithApplied(false).
		Build()
	err = s.env.ExperimentsWrapper.InsertApplicability(s.ctx, applicability)
	s.Require().NoError(err)
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV1_ID() {
	id := s.env.Experiment.Id()
	s.Equal(V1ExperimentId, id)
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV1_IsApplicable() {
	id := uuid.New()
	// This experiment is applicable to all applications, so we can use any ID
	applicable, err := s.env.Experiment.IsApplicable(s.ctx, id)
	s.NoError(err)
	s.True(applicable)
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV1_Coverages() {
	coverages := s.env.Experiment.Coverages()
	s.Equal([]enums.Coverage{enums.CoverageAutoLiability}, coverages)
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV1_Apply_WithMissingVehicleMaintenanceScore() {
	s.mockSafetyFetcher.EXPECT().
		GetBasicScoresFromComputedMeasures(gomock.Any(), gomock.Any()).
		Return(oapi_common.BasicScoreThresholds{}, nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.False(applicability.Applied)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV1_Apply_WithScoreAboveThreshold() {
	basicScoreThresholds := oapi_common.BasicScoreThresholds{
		{
			Category:  basic.VehicleMaintenance.FMCSAName(),
			Score:     pointer_utils.ToPointer(float32(85)),
			Threshold: 80,
		},
	}

	s.mockSafetyFetcher.EXPECT().
		GetBasicScoresFromComputedMeasures(gomock.Any(), gomock.Any()).
		Return(basicScoreThresholds, nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(-15), metadata.CreditApplied)
	s.Equal("85.0", metadata.ValueUsed)
	s.Equal("Vehicle Maintenance BASIC Score Adjustment applied: -15%. Vehicle Maintenance Score: 85.0", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(-15, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV1_Apply_WithScoreBelowThreshold() {
	basicScoreThresholds := oapi_common.BasicScoreThresholds{
		{
			Category:  basic.VehicleMaintenance.FMCSAName(),
			Score:     pointer_utils.ToPointer(float32(75)),
			Threshold: 80,
		},
	}

	s.mockSafetyFetcher.EXPECT().
		GetBasicScoresFromComputedMeasures(gomock.Any(), gomock.Any()).
		Return(basicScoreThresholds, nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("75.0", metadata.ValueUsed)
	s.Equal("Vehicle Maintenance BASIC Score Adjustment applied: 0%. Vehicle Maintenance Score: 75.0", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV1_Apply_WithScoreEqualToThreshold() {
	basicScoreThresholds := oapi_common.BasicScoreThresholds{
		{
			Category:  basic.VehicleMaintenance.FMCSAName(),
			Score:     pointer_utils.ToPointer(float32(80)),
			Threshold: 80,
		},
	}

	s.mockSafetyFetcher.EXPECT().
		GetBasicScoresFromComputedMeasures(gomock.Any(), gomock.Any()).
		Return(basicScoreThresholds, nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("80.0", metadata.ValueUsed)
	s.Equal("Vehicle Maintenance BASIC Score Adjustment applied: 0%. Vehicle Maintenance Score: 80.0", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV1_Apply_WithNoCoverageIntersection() {
	// Modify submission to have no coverages that the experiment applies to
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(),
		func(sub application.SubmissionObject) (application.SubmissionObject, error) {
			sub.CoverageInfo.Coverages = []application.CoverageDetails{
				{CoverageType: enums.CoverageGeneralLiability}, // Not applicable to vehicle maintenance experiment
			}
			return sub, nil
		})
	s.Require().NoError(err)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.False(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
}

func (s *basicVehicleMaintenanceScoreV1ExperimentTestSuite) TestCalculateCredit() {
	experiment := &BasicVehicleMaintenanceScoreV1{}

	tests := []struct {
		name       string
		score      float32
		wantCredit int32
	}{
		{
			name:       "score lower than 80 should return 0% credit",
			score:      70,
			wantCredit: 0,
		},
		{
			name:       "score equal to 80 should return 0% credit",
			score:      80,
			wantCredit: 0,
		},
		{
			name:       "score higher than 80 should return 15% debit",
			score:      85,
			wantCredit: -15,
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			got := experiment.calculateCredit(tt.score)
			s.Require().Equal(tt.wantCredit, got)
		})
	}
}
