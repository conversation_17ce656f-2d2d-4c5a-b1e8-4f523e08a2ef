package basic_vehicle_maintenance_score

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/safety"
)

const (
	vehicleMaintenanceScoreThreshold = 80.0
)

// getVehicleMaintenanceScore retrieves the vehicle maintenance score from safety data
func getVehicleMaintenanceScore(
	ctx context.Context,
	safetyFetcher safety.SafetyFetcher,
	dotNumber int64,
) (float32, bool, error) {
	basicScores, err := safetyFetcher.GetBasicScoresFromComputedMeasures(ctx, dotNumber)
	if err != nil {
		return 0, false, errors.Wrap(err, "failed to get basic scores")
	}

	vehicleMaintenanceScore := float32(0)
	isApplicable := false
	for _, score := range basicScores {
		if score.Category == basic.VehicleMaintenance.FMCSAName() {
			if score.Score != nil {
				vehicleMaintenanceScore = *score.Score
				isApplicable = true
			}
		}
	}

	return vehicleMaintenanceScore, isApplicable, nil
}

// buildVehicleMaintenanceScoreApplicability creates an applicability for vehicle maintenance score experiments
func buildVehicleMaintenanceScoreApplicability(
	experimentId uuid.UUID,
	applicationId string,
	submissionId uuid.UUID,
	credit int32,
	applied bool,
	valueUsed string,
	activeCoverages []enums.Coverage,
) (*experiments.Applicability, *common.ExperimentMetadata) {
	applicabilityCreditsByCoverage := make(map[string]int)
	creditsByCoverage := make(map[enums.Coverage]int32)
	for _, coverage := range activeCoverages {
		applicabilityCreditsByCoverage[coverage.String()] = int(credit)
		creditsByCoverage[coverage] = credit
	}

	applicability := common.BuildExperimentsApplicability(
		experimentId,
		applicationId,
		submissionId,
		applicabilityCreditsByCoverage,
		applied,
	)

	var explanation string
	if applied {
		explanation = getVehicleMaintenanceScoreExplanation(credit, valueUsed)
	}

	metadata := common.NewExperimentMetadata(credit, valueUsed, creditsByCoverage, explanation)

	return applicability, metadata
}

// getVehicleMaintenanceScoreExplanation returns the explanation for the basic vehicle maintenance score experiment
func getVehicleMaintenanceScoreExplanation(credit int32, valueUsed string) string {
	return fmt.Sprintf("Vehicle Maintenance BASIC Score Adjustment applied: %d%%. Vehicle Maintenance Score: %s", credit, valueUsed)
}
