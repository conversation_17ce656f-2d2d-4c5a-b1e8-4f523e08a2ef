package basic_vehicle_maintenance_score

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/safety"
)

var V1ExperimentId = uuid.MustParse("b1a2c3d4-e5f6-7890-abcd-ef1234567890")

type v1Deps struct {
	fx.In

	ApplicationWrapper application.DataWrapper
	UWSafetyFetcher    safety.SafetyFetcher
}

type BasicVehicleMaintenanceScoreV1 struct {
	deps v1Deps
}

func newBasicVehicleMaintenanceScoreV1(deps v1Deps) *BasicVehicleMaintenanceScoreV1 {
	return &BasicVehicleMaintenanceScoreV1{deps: deps}
}

func (b *BasicVehicleMaintenanceScoreV1) Id() uuid.UUID {
	return V1ExperimentId
}

func (b *BasicVehicleMaintenanceScoreV1) IsApplicable(_ context.Context, _ uuid.UUID) (bool, error) {
	return true, nil
}

func (b *BasicVehicleMaintenanceScoreV1) Coverages() []enums.Coverage {
	return []enums.Coverage{
		enums.CoverageAutoLiability,
	}
}

func (b *BasicVehicleMaintenanceScoreV1) Apply(
	ctx context.Context,
	applicationId uuid.UUID,
	submissionId uuid.UUID,
	applicationReviewId *uuid.UUID,
) (*experiments.Applicability, *common.ExperimentMetadata, error) {
	sub, err := b.deps.ApplicationWrapper.GetSubmissionById(ctx, submissionId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get submission %s", submissionId)
	}

	activeCoverages := common.GetActiveCoveragesForExperiment(sub, b.Coverages())
	if len(activeCoverages) == 0 {
		// If there are no active coverages, we return an applicability with zero credit
		applicability, metadata := buildVehicleMaintenanceScoreApplicability(
			b.Id(),
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	vehicleMaintenanceScore, isApplicable, err := getVehicleMaintenanceScore(ctx, b.deps.UWSafetyFetcher, sub.CompanyInfo.DOTNumber)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get vehicle maintenance score for submission %s", submissionId)
	}

	if !isApplicable {
		applicability, metadata := buildVehicleMaintenanceScoreApplicability(
			b.Id(),
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			activeCoverages,
		)
		return applicability, metadata, nil
	}

	credit := b.calculateCredit(vehicleMaintenanceScore)

	valueUsed := fmt.Sprintf("%.1f", vehicleMaintenanceScore)

	applicability, metadata := buildVehicleMaintenanceScoreApplicability(
		b.Id(),
		applicationId.String(),
		submissionId,
		credit,
		true,
		valueUsed,
		activeCoverages,
	)

	return applicability, metadata, nil
}

// calculateCredit implements the V1 credit calculation logic with -15 credit for over threshold
func (b *BasicVehicleMaintenanceScoreV1) calculateCredit(vehicleMaintenanceScore float32) int32 {
	if vehicleMaintenanceScore > vehicleMaintenanceScoreThreshold {
		return -15
	}

	return 0
}
