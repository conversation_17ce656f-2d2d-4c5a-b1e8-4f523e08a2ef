package basic_vehicle_maintenance_score

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/fmcsa/basic"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/submission_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/safety"
)

type basicVehicleMaintenanceScoreV2ExperimentTestSuiteEnv struct {
	fx.In

	AppReviewFixture   *application_review_fixture.ApplicationReviewsFixture
	SubmissionFixture  *submission_fixture.SubmissionsFixture
	AppReviewWrapper   uw.ApplicationReviewWrapper
	ApplicationWrapper application.DataWrapper
	Experiment         *BasicVehicleMaintenanceScoreV2
	ExperimentsWrapper *db.ExperimentsWrapper
}

type basicVehicleMaintenanceScoreV2ExperimentTestSuite struct {
	suite.Suite
	env   basicVehicleMaintenanceScoreV2ExperimentTestSuiteEnv
	fxApp *fxtest.App
	ctx   context.Context
	ctrl  *gomock.Controller

	mockSafetyFetcher *safety.MockSafetyFetcher

	appId        uuid.UUID
	experimentId uuid.UUID
	submissionId uuid.UUID

	originalSubmissionCoverageDetails []application.CoverageDetails
}

func TestBasicVehicleMaintenanceScoreV2Experiment(t *testing.T) {
	suite.Run(t, new(basicVehicleMaintenanceScoreV2ExperimentTestSuite))
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())
	s.experimentId = V2ExperimentId

	s.mockSafetyFetcher = safety.NewMockSafetyFetcher(s.ctrl)

	newMockSafetyFetcher := func() safety.SafetyFetcher {
		return s.mockSafetyFetcher
	}

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockSafetyFetcher)),
	)

	// Shortcut handles to frequently used IDs.
	s.appId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.ApplicationID)
	s.submissionId = uuid.MustParse(s.env.SubmissionFixture.UWSubmission.ID)

	// Preserve original coverages so we can restore in TearDownTest.
	s.originalSubmissionCoverageDetails = s.env.SubmissionFixture.UWSubmission.CoverageInfo.Coverages

	// Ensure Auto Liability coverage active for experiment applicability.
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = []application.CoverageDetails{{CoverageType: enums.CoverageAutoLiability}}
		return sub, nil
	})
	s.Require().NoError(err)

	// Persist experiment definition so ExperimentWrapper queries succeed.
	experiment := experiments.NewExperimentBuilder().
		WithMockData_TestOnly().
		WithId(s.experimentId).WithEnabled(true).
		Build()
	err = s.env.ExperimentsWrapper.InsertExperiment(s.ctx, experiment)
	s.Require().NoError(err)
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) TearDownSuite() {
	s.ctrl.Finish()
	s.fxApp.RequireStop()
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) TearDownTest() {
	// Restore original coverages on the submission.
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = s.originalSubmissionCoverageDetails
		return sub, nil
	})
	s.NoError(err)

	// Insert invalid applicability to reset experiment state
	applicability := experiments.NewApplicabilityBuilder().
		WithMockData_TestOnly().
		WithExperimentId(s.experimentId).
		WithSubjectId(s.env.AppReviewFixture.ApplicationReview.ApplicationID).
		WithApplied(false).
		Build()
	err = s.env.ExperimentsWrapper.InsertApplicability(s.ctx, applicability)
	s.Require().NoError(err)
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) Test_ID() {
	s.Equal(V2ExperimentId, s.env.Experiment.Id())
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) Test_IsApplicable() {
	id := uuid.New()
	// This experiment is applicable to all applications, so we can use any ID
	applicable, err := s.env.Experiment.IsApplicable(s.ctx, id)
	s.NoError(err)
	s.True(applicable)
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV2_Coverages() {
	coverages := s.env.Experiment.Coverages()
	s.Equal([]enums.Coverage{enums.CoverageAutoLiability}, coverages)
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) TestBasicVehicleMaintenanceScoreV2_Apply_WithMissingVehicleMaintenanceScore() {
	s.mockSafetyFetcher.EXPECT().
		GetBasicScoresFromComputedMeasures(gomock.Any(), gomock.Any()).
		Return(oapi_common.BasicScoreThresholds{}, nil).
		Times(1)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, nil)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.False(applicability.Applied)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) Test_Apply_ScoreAboveThreshold() {
	score := float32(85)
	thresholds := oapi_common.BasicScoreThresholds{{Category: basic.VehicleMaintenance.FMCSAName(), Score: pointer_utils.ToPointer(score), Threshold: 80}}

	s.mockSafetyFetcher.EXPECT().
		GetBasicScoresFromComputedMeasures(gomock.Any(), gomock.Any()).
		Return(thresholds, nil)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, nil)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(-8), metadata.CreditApplied)
	s.Equal("85.0", metadata.ValueUsed)
	s.Equal("Vehicle Maintenance BASIC Score Adjustment applied: -8%. Vehicle Maintenance Score: 85.0", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(-8, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) Test_Apply_ScoreBelowThreshold() {
	score := float32(75)
	thresholds := oapi_common.BasicScoreThresholds{{Category: basic.VehicleMaintenance.FMCSAName(), Score: pointer_utils.ToPointer(score), Threshold: 80}}

	s.mockSafetyFetcher.EXPECT().
		GetBasicScoresFromComputedMeasures(gomock.Any(), gomock.Any()).
		Return(thresholds, nil)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, nil)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("75.0", metadata.ValueUsed)
	s.Equal("Vehicle Maintenance BASIC Score Adjustment applied: 0%. Vehicle Maintenance Score: 75.0", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) Test_Apply_NoCoverageIntersection() {
	// Switch submission coverages to one the experiment doesn't act on.
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = []application.CoverageDetails{{CoverageType: enums.CoverageGeneralLiability}}
		return sub, nil
	})
	s.Require().NoError(err)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, nil)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(0, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.False(applicability.Applied)
}

func (s *basicVehicleMaintenanceScoreV2ExperimentTestSuite) Test_calculateCredit() {
	experiment := &BasicVehicleMaintenanceScoreV2{}

	tests := []struct {
		name       string
		score      float32
		wantCredit int32
	}{
		{
			name:       "score lower than 80 should return 0% credit",
			score:      60,
			wantCredit: 0,
		},
		{
			name:       "score equal to 80 should return 0% credit",
			score:      80,
			wantCredit: 0,
		},
		{
			name:       "score higher than 80 should return 8% debit (v2 change)",
			score:      90,
			wantCredit: -8,
		},
		{
			name:       "very high score should return 8% debit (v2 change)",
			score:      95,
			wantCredit: -8,
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			got := experiment.calculateCredit(tt.score)
			s.Equal(tt.wantCredit, got)
		})
	}
}
