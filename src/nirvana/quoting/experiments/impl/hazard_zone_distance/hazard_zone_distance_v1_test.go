package hazard_zone_distance

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/feature_store"
	"nirvanatech.com/nirvana/features/hazard_zones"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/feature_store_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/submission_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/telematics"
)

type hazardZoneDistanceV1ExperimentTestSuiteEnv struct {
	fx.In

	AppReviewFixture    *application_review_fixture.ApplicationReviewsFixture
	SubmissionFixture   *submission_fixture.SubmissionsFixture
	FeatureStoreFixture *feature_store_fixture.FeatureStoreFixture
	FeatureStore        feature_store.FeatureStore
	AppReviewWrapper    uw.ApplicationReviewWrapper
	ApplicationWrapper  application.DataWrapper
	Experiment          *HazardZoneDistanceV1
	ExperimentsWrapper  *db.ExperimentsWrapper
}

type hazardZoneDistanceV1ExperimentTestSuite struct {
	suite.Suite
	env   hazardZoneDistanceV1ExperimentTestSuiteEnv
	fxApp *fxtest.App
	ctx   context.Context
	ctrl  *gomock.Controller

	appId        uuid.UUID
	experimentId uuid.UUID
	submissionId uuid.UUID
	appReviewId  uuid.UUID

	originalSubmissionCoverageDetails []application.CoverageDetails
}

func TestHazardZoneDistanceV1Experiment(t *testing.T) {
	suite.Run(t, new(hazardZoneDistanceV1ExperimentTestSuite))
}

// setHazardZoneFixtures sets up the feature store fixture data for hazard zone distance tests
var setHazardZoneFixtures = fx.Invoke(
	func(
		fsf *feature_store_fixture.FeatureStoreFixture,
		af *application_fixture.ApplicationsFixture,
		arf *application_review_fixture.ApplicationReviewsFixture,
	) {
		// Create a consistent TSP handle ID for testing
		tspConnHandleId := "12345678-1234-1234-1234-123456789abc"

		// Set up the application with TSP information
		af.Application.TSPConnHandleId = pointer_utils.String(tspConnHandleId)
		af.Application.TSPEnum = pointer_utils.ToPointer(telematics.TSPSamsara)

		// Also update the application review to reflect this change
		arf.ApplicationReview.Application.TSPConnHandleId = pointer_utils.String(tspConnHandleId)
		arf.ApplicationReview.Application.TSPEnum = pointer_utils.ToPointer(telematics.TSPSamsara)

		// Initialize HazardZoneInput in feature store fixture with this TSP handle ID
		if fsf.HazardZoneInput == nil {
			fsf.HazardZoneInput = make(map[string]*feature_store_fixture.HazardZoneInputs)
		}
		fsf.HazardZoneInput[tspConnHandleId] = &feature_store_fixture.HazardZoneInputs{
			HazardZoneDistance: hazard_zones.HazardZoneDistanceFeature{
				Percentage: 0.0, // Default to 0%, will be overridden in individual tests
			},
			HazardZoneDuration: hazard_zones.HazardZoneDurationFeature{
				Percentage: 0.0,
			},
			HazardZoneStates: hazard_zones.HazardZoneStatesFeature{
				HazardStateTable: []hazard_zones.HazardStateTableRow{},
			},
		}
	},
)

func (s *hazardZoneDistanceV1ExperimentTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.experimentId = V1ExperimentId

	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(setHazardZoneFixtures),
	)

	s.appId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.ApplicationID)
	s.submissionId = uuid.MustParse(s.env.SubmissionFixture.UWSubmission.ID)
	s.appReviewId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.Id)
	s.originalSubmissionCoverageDetails = s.env.SubmissionFixture.UWSubmission.CoverageInfo.Coverages

	experiment := experiments.NewExperimentBuilder().
		WithMockData_TestOnly().
		WithId(s.experimentId).WithEnabled(true).
		Build()

	err := s.env.ExperimentsWrapper.InsertExperiment(s.ctx, experiment)
	s.Require().NoError(err)
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TearDownSuite() {
	s.ctrl.Finish()
	s.fxApp.RequireStop()
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TearDownTest() {
	// Restore original coverage details
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = s.originalSubmissionCoverageDetails
		return sub, nil
	})
	s.NoError(err)

	// Insert invalid applicability to reset experiment state
	applicability := experiments.NewApplicabilityBuilder().
		WithMockData_TestOnly().
		WithExperimentId(s.experimentId).
		WithSubjectId(s.env.AppReviewFixture.ApplicationReview.ApplicationID).
		WithApplied(false).
		Build()
	err = s.env.ExperimentsWrapper.InsertApplicability(s.ctx, applicability)
	s.Require().NoError(err)
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TestHazardZoneDistanceV1_ID() {
	id := s.env.Experiment.Id()
	s.Equal(V1ExperimentId, id)
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TestHazardZoneDistanceV1_IsApplicable() {
	id := uuid.New()
	// This experiment is applicable to all applications, so we can use any ID
	applicable, err := s.env.Experiment.IsApplicable(s.ctx, id)
	s.NoError(err)
	s.True(applicable)
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TestHazardZoneDistanceV1_Coverages() {
	coverages := s.env.Experiment.Coverages()
	s.Equal([]enums.Coverage{enums.CoverageAutoLiability}, coverages)
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TestHazardZoneDistanceV1_Apply_WithZeroPercentageSuccess() {
	// Test scenario: 0% hazard zone distance
	// Expected outcome: 0% adjustment

	// Set hazard zone percentage to 0%
	s.setHazardZoneDistancePercentage(0)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("0.00%", metadata.ValueUsed)
	s.Equal("Hazard Zone Distance Adjustment applied: 0%. Hazard Zone Distance: 0.00%", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Len(applicability.Metadata.CreditsByCoverage, 1)
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TestHazardZoneDistanceV1_Apply_WithLowPercentageSuccess() {
	// Test scenario: 1.5% hazard zone distance (between 0 and 2%)
	// Expected outcome: +10% debit applied to Auto Liability coverage (low hazard exposure penalty)

	// Set hazard zone percentage to 1.5%
	s.setHazardZoneDistancePercentage(1.5)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(10), metadata.CreditApplied)
	s.Equal("1.50%", metadata.ValueUsed)
	s.Equal("Hazard Zone Distance Adjustment applied: 10%. Hazard Zone Distance: 1.50%", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Len(applicability.Metadata.CreditsByCoverage, 1)
	s.Equal(10, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()]) // Updated expectation
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TestHazardZoneDistanceV1_Apply_WithHighPercentageSuccess() {
	// Test scenario: 3.2% hazard zone distance (2% and above)
	// Expected outcome: -10% credit applied to Auto Liability coverage (high hazard exposure reward)

	// Set hazard zone percentage to 3.2%
	s.setHazardZoneDistancePercentage(3.2)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(-10), metadata.CreditApplied)
	s.Equal("3.20%", metadata.ValueUsed)
	s.Equal("Hazard Zone Distance Adjustment applied: -10%. Hazard Zone Distance: 3.20%", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.True(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Len(applicability.Metadata.CreditsByCoverage, 1)
	s.Equal(-10, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()]) // Updated expectation
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TestHazardZoneDistanceV1_Apply_WithNoCoverageIntersection() {
	// Modify submission to have no coverages that the experiment applies to
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(),
		func(sub application.SubmissionObject) (application.SubmissionObject, error) {
			sub.CoverageInfo.Coverages = []application.CoverageDetails{
				{CoverageType: enums.CoverageGeneralLiability}, // Not applicable to hazard zone distance experiment
			}
			return sub, nil
		})
	s.Require().NoError(err)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.False(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) TestHazardZoneDistanceV1_Apply_WithNoTSPConnection() {
	// Test scenario: Application review has no TSP connection handle ID
	// Expected outcome: 0% adjustment, applied = false (cannot fetch hazard zone data)
	tspConnHandleId := *s.env.AppReviewFixture.ApplicationReview.Application.TSPConnHandleId

	err := s.env.ApplicationWrapper.UpdateApp(s.ctx, s.appId.String(), func(app application.Application) (application.Application, error) {
		app.TSPConnHandleId = nil
		return app, nil
	})
	s.Require().NoError(err)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.False(applicability.Applied)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Len(applicability.Metadata.CreditsByCoverage, 0)

	// restore the TSP connection handle ID
	s.env.ApplicationWrapper.UpdateApp(s.ctx, s.appId.String(), func(app application.Application) (application.Application, error) {
		app.TSPConnHandleId = pointer_utils.String(tspConnHandleId)
		return app, nil
	})
}

func (s *hazardZoneDistanceV1ExperimentTestSuite) setHazardZoneDistancePercentage(percentage float32) {
	tspConnHandleId := *s.env.AppReviewFixture.ApplicationReview.Application.TSPConnHandleId

	// Create the hazard zone distance feature with the desired percentage
	hazardZoneDistanceFeature := hazard_zones.HazardZoneDistanceFeature{
		Percentage: percentage,
	}

	// Store it directly in the feature store
	err := s.env.FeatureStore.Store(s.ctx, tspConnHandleId, nil, &hazardZoneDistanceFeature)
	s.Require().NoError(err)
}

func TestCalculateCredit(t *testing.T) {
	experiment := &HazardZoneDistanceV1{}

	tests := []struct {
		name       string
		percentage float32
		want       int32
	}{
		{
			name:       "Zero percentage should return 0% adjustment",
			percentage: 0,
			want:       0,
		},
		{
			name:       "Low percentage (0.5%) should return +10% debit",
			percentage: 0.5,
			want:       10,
		},
		{
			name:       "Low percentage (1.9%) should return +10% debit",
			percentage: 1.9,
			want:       10,
		},
		{
			name:       "Exactly 2% should return -10% credit",
			percentage: 2.0,
			want:       -10,
		},
		{
			name:       "High percentage (5.5%) should return -10% credit",
			percentage: 5.5,
			want:       -10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := experiment.calculateCredit(tt.percentage)
			if got != tt.want {
				t.Errorf("calculateCredit(%v) = %v, want %v", tt.percentage, got, tt.want)
			}
		})
	}
}
