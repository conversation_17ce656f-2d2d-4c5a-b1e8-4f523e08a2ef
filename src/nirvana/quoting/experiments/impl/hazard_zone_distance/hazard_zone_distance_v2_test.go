package hazard_zone_distance

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/feature_store"
	"nirvanatech.com/nirvana/features/hazard_zones"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/feature_store_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/submission_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/telematics"
)

type hazardZoneDistanceV2ExperimentTestSuiteEnv struct {
	fx.In

	ApplicationsFixture *application_fixture.ApplicationsFixture
	AppReviewFixture    *application_review_fixture.ApplicationReviewsFixture
	SubmissionFixture   *submission_fixture.SubmissionsFixture
	FeatureStoreFixture *feature_store_fixture.FeatureStoreFixture
	FeatureStore        feature_store.FeatureStore
	AppReviewWrapper    uw.ApplicationReviewWrapper
	ApplicationWrapper  application.DataWrapper
	Experiment          *HazardZoneDistanceV2
	ExperimentsWrapper  *db.ExperimentsWrapper
}

// setHazardZoneFixturesV2 sets up the feature store fixture data for hazard zone distance v2 tests
var setHazardZoneFixturesV2 = fx.Invoke(
	func(
		fsf *feature_store_fixture.FeatureStoreFixture,
		af *application_fixture.ApplicationsFixture,
		arf *application_review_fixture.ApplicationReviewsFixture,
	) {
		// Create a consistent TSP handle ID for testing
		tspConnHandleId := "12345678-1234-1234-1234-123456789abc"

		// Set up the application with TSP information
		af.Application.TSPConnHandleId = pointer_utils.String(tspConnHandleId)
		af.Application.TSPEnum = pointer_utils.ToPointer(telematics.TSPSamsara)

		// Also update the application review to reflect this change
		arf.ApplicationReview.Application.TSPConnHandleId = pointer_utils.String(tspConnHandleId)
		arf.ApplicationReview.Application.TSPEnum = pointer_utils.ToPointer(telematics.TSPSamsara)

		// Initialize HazardZoneInput in feature store fixture with this TSP handle ID
		if fsf.HazardZoneInput == nil {
			fsf.HazardZoneInput = make(map[string]*feature_store_fixture.HazardZoneInputs)
		}
		fsf.HazardZoneInput[tspConnHandleId] = &feature_store_fixture.HazardZoneInputs{
			HazardZoneDistance: hazard_zones.HazardZoneDistanceFeature{
				Percentage: 0.0, // Default to 0%, will be overridden in individual tests
			},
			HazardZoneDuration: hazard_zones.HazardZoneDurationFeature{
				Percentage: 0.0,
			},
			HazardZoneStates: hazard_zones.HazardZoneStatesFeature{
				HazardStateTable: []hazard_zones.HazardStateTableRow{},
			},
		}
	},
)

type hazardZoneDistanceV2ExperimentTestSuite struct {
	suite.Suite
	env   hazardZoneDistanceV2ExperimentTestSuiteEnv
	fxApp *fxtest.App
	ctx   context.Context
	ctrl  *gomock.Controller

	appId        uuid.UUID
	experimentId uuid.UUID
	submissionId uuid.UUID
	appReviewId  uuid.UUID

	originalSubmissionCoverageDetails []application.CoverageDetails
}

func TestHazardZoneDistanceV2Experiment(t *testing.T) {
	suite.Run(t, new(hazardZoneDistanceV2ExperimentTestSuite))
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())
	s.experimentId = V2ExperimentId

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(setHazardZoneFixturesV2),
	)

	// Shortcut handles to frequently used IDs.
	s.appId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.ApplicationID)
	s.submissionId = uuid.MustParse(s.env.SubmissionFixture.UWSubmission.ID)
	s.appReviewId = uuid.MustParse(s.env.AppReviewFixture.ApplicationReview.Id)

	// Preserve original coverages so we can restore in TearDownTest.
	s.originalSubmissionCoverageDetails = s.env.SubmissionFixture.UWSubmission.CoverageInfo.Coverages

	// Ensure Auto Liability coverage active for experiment applicability.
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = []application.CoverageDetails{{CoverageType: enums.CoverageAutoLiability}}
		return sub, nil
	})
	s.Require().NoError(err)

	// Persist experiment definition so ExperimentWrapper queries succeed.
	experiment := experiments.NewExperimentBuilder().
		WithMockData_TestOnly().
		WithId(s.experimentId).WithEnabled(true).
		Build()
	err = s.env.ExperimentsWrapper.InsertExperiment(s.ctx, experiment)
	s.Require().NoError(err)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) TearDownSuite() {
	s.ctrl.Finish()
	s.fxApp.RequireStop()
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) TearDownTest() {
	// Restore original coverages on the submission.
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = s.originalSubmissionCoverageDetails
		return sub, nil
	})
	s.NoError(err)

	// Insert invalid applicability to reset experiment state
	applicability := experiments.NewApplicabilityBuilder().
		WithMockData_TestOnly().
		WithExperimentId(s.experimentId).
		WithSubjectId(s.env.AppReviewFixture.ApplicationReview.ApplicationID).
		WithApplied(false).
		Build()
	err = s.env.ExperimentsWrapper.InsertApplicability(s.ctx, applicability)
	s.Require().NoError(err)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_ID() {
	s.Equal(V2ExperimentId, s.env.Experiment.Id())
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_IsApplicable() {
	id := uuid.New()
	// This experiment is applicable to all applications, so we can use any ID
	applicable, err := s.env.Experiment.IsApplicable(s.ctx, id)
	s.NoError(err)
	s.True(applicable)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_Coverages() {
	coverages := s.env.Experiment.Coverages()
	s.Equal([]enums.Coverage{enums.CoverageAutoLiability}, coverages)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_Apply_NoApplicationReviewId() {
	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, nil)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.submissionId, *applicability.Metadata.SubmissionId)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.False(applicability.Applied)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_Apply_ZeroPercentage() {
	// Set hazard zone percentage to 0%
	s.setHazardZoneDistancePercentage(0)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(5), metadata.CreditApplied)
	s.Equal("0.00%", metadata.ValueUsed)
	s.Equal("Hazard Zone Distance Adjustment applied: 5%. Hazard Zone Distance: 0.00%", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(5, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_Apply_BetweenZeroAndTwo() {
	// Set hazard zone percentage to 1.5%
	s.setHazardZoneDistancePercentage(1.5)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(5), metadata.CreditApplied)
	s.Equal("1.50%", metadata.ValueUsed)
	s.Equal("Hazard Zone Distance Adjustment applied: 5%. Hazard Zone Distance: 1.50%", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(5, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_Apply_TwoPercentOrMore() {
	// Set hazard zone percentage to 5.5%
	s.setHazardZoneDistancePercentage(5.5)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(-5), metadata.CreditApplied)
	s.Equal("5.50%", metadata.ValueUsed)
	s.Equal("Hazard Zone Distance Adjustment applied: -5%. Hazard Zone Distance: 5.50%", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(-5, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_Apply_ExactlyTwo() {
	// Set hazard zone percentage to 2.0%
	s.setHazardZoneDistancePercentage(2.0)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(-5), metadata.CreditApplied)
	s.Equal("2.00%", metadata.ValueUsed)
	s.Equal("Hazard Zone Distance Adjustment applied: -5%. Hazard Zone Distance: 2.00%", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(1, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(-5, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.True(applicability.Applied)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_Apply_NoCoverageIntersection() {
	// Switch submission coverages to one the experiment doesn't act on.
	err := s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submissionId.String(), func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.CoverageInfo.Coverages = []application.CoverageDetails{{CoverageType: enums.CoverageGeneralLiability}}
		return sub, nil
	})
	s.Require().NoError(err)

	applicability, metadata, err := s.env.Experiment.Apply(s.ctx, s.appId, s.submissionId, &s.appReviewId)
	s.NoError(err)
	s.NotNil(metadata)
	s.Equal(int32(0), metadata.CreditApplied)
	s.Equal("", metadata.ValueUsed)
	s.Equal("", metadata.Explanation)

	s.NotNil(applicability)
	s.Equal(s.experimentId, applicability.ExperimentId)
	s.Equal(s.appId.String(), applicability.SubjectId)
	s.Equal(0, len(applicability.Metadata.CreditsByCoverage))
	s.Equal(0, applicability.Metadata.CreditsByCoverage[enums.CoverageAutoLiability.String()])
	s.False(applicability.Applied)
}

// setHazardZoneDistancePercentage is a helper to set the hazard zone percentage in the feature store
func (s *hazardZoneDistanceV2ExperimentTestSuite) setHazardZoneDistancePercentage(percentage float32) {
	tspConnHandleId := *s.env.AppReviewFixture.ApplicationReview.Application.TSPConnHandleId

	hazardZoneDistanceFeature := hazard_zones.HazardZoneDistanceFeature{
		Percentage: percentage,
	}

	// Store it directly in the feature store
	err := s.env.FeatureStore.Store(s.ctx, tspConnHandleId, nil, &hazardZoneDistanceFeature)
	s.Require().NoError(err)
}

func (s *hazardZoneDistanceV2ExperimentTestSuite) Test_calculateCredit() {
	experiment := &HazardZoneDistanceV2{}

	tests := []struct {
		name       string
		percentage float32
		wantCredit int32
	}{
		{
			name:       "exactly 0% should return +5 credit",
			percentage: 0,
			wantCredit: 5,
		},
		{
			name:       "0.5% (between 0 and 2) should return +5 credit",
			percentage: 0.5,
			wantCredit: 5,
		},
		{
			name:       "exactly 2% should return -5 credit",
			percentage: 2.0,
			wantCredit: -5,
		},
		{
			name:       "5% (above 2) should return -5 credit",
			percentage: 5.0,
			wantCredit: -5,
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			got := experiment.calculateCredit(tt.percentage)
			s.Equal(tt.wantCredit, got)
		})
	}
}
