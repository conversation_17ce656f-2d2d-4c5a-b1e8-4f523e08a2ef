package hazard_zone_distance

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
)

var V1ExperimentId = uuid.MustParse("b3c8b0a2-4d1e-4b5c-9f3d-7a2e6f8b1c5e")

type v1Deps struct {
	fx.In

	ApplicationWrapper application.DataWrapper
	AppReviewManager   uw_app_review.ReviewManager
	AppReviewWrapper   uw.ApplicationReviewWrapper
}

type HazardZoneDistanceV1 struct {
	deps v1Deps
}

func newHazardZoneDistanceV1(deps v1Deps) *HazardZoneDistanceV1 {
	return &HazardZoneDistanceV1{
		deps: deps,
	}
}

func (h *HazardZoneDistanceV1) Id() uuid.UUID {
	return V1ExperimentId
}

func (h *HazardZoneDistanceV1) IsApplicable(_ context.Context, _ uuid.UUID) (bool, error) {
	return true, nil
}

func (h *HazardZoneDistanceV1) Coverages() []enums.Coverage {
	return []enums.Coverage{
		enums.CoverageAutoLiability,
	}
}

func (h *HazardZoneDistanceV1) Apply(
	ctx context.Context,
	applicationId uuid.UUID,
	submissionId uuid.UUID,
	applicationReviewId *uuid.UUID,
) (*experiments.Applicability, *common.ExperimentMetadata, error) {
	if applicationReviewId == nil {
		applicability, metadata := buildHazardZoneDistanceApplicability(
			h.Id(),
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	appReview, err := h.deps.AppReviewWrapper.GetReview(ctx, applicationReviewId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get application review %s", applicationReviewId.String())
	}

	if appReview.Application.TSPConnHandleId == nil {
		applicability, metadata := buildHazardZoneDistanceApplicability(
			h.Id(),
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	submission, err := h.deps.ApplicationWrapper.GetSubmissionById(ctx, submissionId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get submission %s", submissionId)
	}

	activeCoverages := common.GetActiveCoveragesForExperiment(submission, h.Coverages())
	if len(activeCoverages) == 0 {
		// If there are no active coverages, we return an applicability with zero credit
		applicability, metadata := buildHazardZoneDistanceApplicability(
			h.Id(),
			appReview.ApplicationID,
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	hazardZonePercentage, valid := getHazardZoneDistancePercentage(ctx, h.deps.AppReviewManager, appReview.Id)
	if !valid {
		// If hazard zone data is not available, we return an applicability with zero credit
		applicability, metadata := buildHazardZoneDistanceApplicability(
			h.Id(),
			appReview.ApplicationID,
			submissionId,
			0,
			false,
			"",
			activeCoverages,
		)
		return applicability, metadata, nil
	}

	credit := h.calculateCredit(hazardZonePercentage)

	valueUsed := fmt.Sprintf("%.2f%%", hazardZonePercentage)

	applicability, metadata := buildHazardZoneDistanceApplicability(
		h.Id(),
		appReview.ApplicationID,
		submissionId,
		credit,
		true,
		valueUsed,
		activeCoverages,
	)

	return applicability, metadata, nil
}

// calculateCredit implements the V1 credit calculation logic
func (h *HazardZoneDistanceV1) calculateCredit(hazardZonePercentage float32) int32 {
	switch {
	case hazardZonePercentage == 0:
		return 0
	case hazardZonePercentage > 0 && hazardZonePercentage < 2:
		return 10
	case hazardZonePercentage >= 2:
		return -10
	default:
		return 0
	}
}
