load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "hazard_zone_distance",
    srcs = [
        "common.go",
        "fx.go",
        "hazard_zone_distance_v1.go",
        "hazard_zone_distance_v2.go",
    ],
    importpath = "nirvanatech.com/nirvana/quoting/experiments/impl/hazard_zone_distance",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/experiments",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/quoting/experiments/impl/common",
        "//nirvana/underwriting/app_review",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "hazard_zone_distance_test",
    srcs = [
        "hazard_zone_distance_v1_test.go",
        "hazard_zone_distance_v2_test.go",
    ],
    embed = [":hazard_zone_distance"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/experiments",
        "//nirvana/experiments/db",
        "//nirvana/feature_store",
        "//nirvana/features/hazard_zones",
        "//nirvana/infra/fx/testfixtures/application_fixture",
        "//nirvana/infra/fx/testfixtures/application_review_fixture",
        "//nirvana/infra/fx/testfixtures/feature_store_fixture",
        "//nirvana/infra/fx/testfixtures/submission_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/telematics",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
