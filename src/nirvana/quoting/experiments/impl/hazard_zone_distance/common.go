package hazard_zone_distance

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
)

// getHazardZoneDistancePercentage fetches the hazard zone distance percentage from the application review's hazard zones data.
// This method uses graceful error handling, if hazard zone data is unavailable or the percentage field is nil,
// we return 0% rather than failing the experiment.
// Returns:
//   - The hazard zone distance percentage (0.0-100.0) if available
//   - 0.0 if data is missing/nil (graceful fallback)
//   - bool indicating if the data retrieval was valid
func getHazardZoneDistancePercentage(
	ctx context.Context,
	appReviewManager uw_app_review.ReviewManager,
	reviewId string,
) (float32, bool) {
	hazardZonesData, err := appReviewManager.Operations.HazardZones.Get(ctx, reviewId)
	if err != nil {
		log.Error(ctx, "failed to get hazard zones data for review", log.Err(err))
		return 0, false
	}

	if hazardZonesData == nil {
		return 0, true
	}

	if hazardZonesData.HazardZoneDistance.Percentage == nil {
		return 0, true
	}

	return *hazardZonesData.HazardZoneDistance.Percentage, true
}

// buildHazardZoneDistanceApplicability creates an applicability for hazard zone distance experiments
func buildHazardZoneDistanceApplicability(
	experimentId uuid.UUID,
	applicationId string,
	submissionId uuid.UUID,
	credit int32,
	applied bool,
	valueUsed string,
	activeCoverages []enums.Coverage,
) (*experiments.Applicability, *common.ExperimentMetadata) {
	applicabilityCreditsByCoverage := make(map[string]int)
	creditsByCoverage := make(map[enums.Coverage]int32)
	for _, coverage := range activeCoverages {
		applicabilityCreditsByCoverage[coverage.String()] = int(credit)
		creditsByCoverage[coverage] = credit
	}

	applicability := common.BuildExperimentsApplicability(
		experimentId,
		applicationId,
		submissionId,
		applicabilityCreditsByCoverage,
		applied,
	)

	var explanation string
	if applied {
		explanation = getHazardZoneDistanceExplanation(credit, valueUsed)
	}

	metadata := common.NewExperimentMetadata(credit, valueUsed, creditsByCoverage, explanation)

	return applicability, metadata
}

// getHazardZoneDistanceExplanation returns the explanation for the hazard zone distance experiment
func getHazardZoneDistanceExplanation(credit int32, valueUsed string) string {
	return fmt.Sprintf("Hazard Zone Distance Adjustment applied: %d%%. Hazard Zone Distance: %s", credit, valueUsed)
}
