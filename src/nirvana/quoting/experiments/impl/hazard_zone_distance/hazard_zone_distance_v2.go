package hazard_zone_distance

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/quoting/experiments/impl/common"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
)

var V2ExperimentId = uuid.MustParse("d4e5f6a7-b8c9-0123-defa-b456789cdef0")

type v2Deps struct {
	fx.In

	ApplicationWrapper application.DataWrapper
	AppReviewManager   uw_app_review.ReviewManager
	AppReviewWrapper   uw.ApplicationReviewWrapper
}

type HazardZoneDistanceV2 struct {
	deps v2Deps
}

func newHazardZoneDistanceV2(deps v2Deps) *HazardZoneDistanceV2 {
	return &HazardZoneDistanceV2{
		deps: deps,
	}
}

func (h *HazardZoneDistanceV2) Id() uuid.UUID {
	return V2ExperimentId
}

func (h *HazardZoneDistanceV2) IsApplicable(_ context.Context, _ uuid.UUID) (bool, error) {
	return true, nil
}

func (h *HazardZoneDistanceV2) Coverages() []enums.Coverage {
	return []enums.Coverage{
		enums.CoverageAutoLiability,
	}
}

func (h *HazardZoneDistanceV2) Apply(
	ctx context.Context,
	applicationId uuid.UUID,
	submissionId uuid.UUID,
	applicationReviewId *uuid.UUID,
) (*experiments.Applicability, *common.ExperimentMetadata, error) {
	if applicationReviewId == nil {
		applicability, metadata := buildHazardZoneDistanceApplicability(
			h.Id(),
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	appReview, err := h.deps.AppReviewWrapper.GetReview(ctx, applicationReviewId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get application review %s", applicationReviewId.String())
	}

	if appReview.Application.TSPConnHandleId == nil {
		applicability, metadata := buildHazardZoneDistanceApplicability(
			h.Id(),
			applicationId.String(),
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	submission, err := h.deps.ApplicationWrapper.GetSubmissionById(ctx, submissionId.String())
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get submission %s", submissionId)
	}

	activeCoverages := common.GetActiveCoveragesForExperiment(submission, h.Coverages())
	if len(activeCoverages) == 0 {
		// If there are no active coverages, we return an applicability with zero credit
		applicability, metadata := buildHazardZoneDistanceApplicability(
			h.Id(),
			appReview.ApplicationID,
			submissionId,
			0,
			false,
			"",
			nil,
		)
		return applicability, metadata, nil
	}

	hazardZonePercentage, valid := getHazardZoneDistancePercentage(ctx, h.deps.AppReviewManager, appReview.Id)
	if !valid {
		// If hazard zone data is not available, we return an applicability with zero credit
		applicability, metadata := buildHazardZoneDistanceApplicability(
			h.Id(),
			appReview.ApplicationID,
			submissionId,
			0,
			false,
			"",
			activeCoverages,
		)
		return applicability, metadata, nil
	}

	credit := h.calculateCredit(hazardZonePercentage)

	valueUsed := fmt.Sprintf("%.2f%%", hazardZonePercentage)

	applicability, metadata := buildHazardZoneDistanceApplicability(
		h.Id(),
		appReview.ApplicationID,
		submissionId,
		credit,
		true,
		valueUsed,
		activeCoverages,
	)

	return applicability, metadata, nil
}

func (h *HazardZoneDistanceV2) calculateCredit(hazardZonePercentage float32) int32 {
	switch {
	case hazardZonePercentage == 0:
		return 5
	case hazardZonePercentage > 0 && hazardZonePercentage < 2:
		return 5
	case hazardZonePercentage >= 2:
		return -5
	default:
		return 0
	}
}
