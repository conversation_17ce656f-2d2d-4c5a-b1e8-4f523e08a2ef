package experiments

import (
	"context"
	"sync"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/quoting/experiments/impl/basic_alert_count"
	"nirvanatech.com/nirvana/quoting/experiments/impl/basic_hos_compliance_score"
	"nirvanatech.com/nirvana/quoting/experiments/impl/basic_unsafe_driving_score"
	"nirvanatech.com/nirvana/quoting/experiments/impl/basic_vehicle_maintenance_score"
	"nirvanatech.com/nirvana/quoting/experiments/impl/driver_oos"
	"nirvanatech.com/nirvana/quoting/experiments/impl/driver_turnover"
	"nirvanatech.com/nirvana/quoting/experiments/impl/fleet_size"
	"nirvanatech.com/nirvana/quoting/experiments/impl/hazard_zone_distance"
	"nirvanatech.com/nirvana/quoting/experiments/impl/overall_oos"
	"nirvanatech.com/nirvana/quoting/experiments/impl/primary_operating_class"
	"nirvanatech.com/nirvana/quoting/experiments/impl/utilization"
	"nirvanatech.com/nirvana/quoting/experiments/impl/vin_visibility"
)

var ExperimentNotExistsError = errors.New("experiment does not exist")

type Registry struct {
	mu             sync.Mutex
	experimentsMap map[uuid.UUID]Experiment
}

func (r *Registry) Register(id uuid.UUID, experiment Experiment) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.experimentsMap[id] = experiment
}

func (r *Registry) GetExperiment(id uuid.UUID) (Experiment, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	experiment, exists := r.experimentsMap[id]
	if !exists {
		return nil, ExperimentNotExistsError
	}
	return experiment, nil
}

func (r *Registry) GetRegisteredExperimentsIds() []uuid.UUID {
	r.mu.Lock()
	defer r.mu.Unlock()
	ids := make([]uuid.UUID, 0, len(r.experimentsMap))
	for id := range r.experimentsMap {
		ids = append(ids, id)
	}
	return ids
}

func (r *Registry) Reset_TestOnly() {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.experimentsMap = make(map[uuid.UUID]Experiment)
}

func newRegistry(
	lc fx.Lifecycle,
	utilV1 *utilization.UtilizationV1,
	utilV2 *utilization.UtilizationV2,
	vvV1 *vin_visibility.VinVisibilityV1,
	fsV1 *fleet_size.FleetSizeV1,
	fsV2 *fleet_size.FleetSizeV2,
	pocV1 *primary_operating_class.PrimaryOperatingClassV1,
	hzdV1 *hazard_zone_distance.HazardZoneDistanceV1,
	hzdV2 *hazard_zone_distance.HazardZoneDistanceV2,
	bacV1 *basic_alert_count.BasicAlertCountV1,
	dOosV1 *driver_oos.DriverOOSV1,
	oOosV1 *overall_oos.OverallOOSV1,
	budsV1 *basic_unsafe_driving_score.BasicUnsafeDrivingScoreV1,
	budsV2 *basic_unsafe_driving_score.BasicUnsafeDrivingScoreV2,
	bvmsV1 *basic_vehicle_maintenance_score.BasicVehicleMaintenanceScoreV1,
	bvmsV2 *basic_vehicle_maintenance_score.BasicVehicleMaintenanceScoreV2,
	hossV1 *basic_hos_compliance_score.BasicHosComplianceScoreV1,
	dtV1 *driver_turnover.DriverTurnoverV1,
) *Registry {
	registry := &Registry{
		experimentsMap: make(map[uuid.UUID]Experiment),
	}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			registry.Register(utilV1.Id(), utilV1)
			registry.Register(utilV2.Id(), utilV2)
			registry.Register(vvV1.Id(), vvV1)
			registry.Register(fsV1.Id(), fsV1)
			registry.Register(fsV2.Id(), fsV2)
			registry.Register(pocV1.Id(), pocV1)
			registry.Register(hzdV1.Id(), hzdV1)
			registry.Register(bacV1.Id(), bacV1)
			registry.Register(dOosV1.Id(), dOosV1)
			registry.Register(oOosV1.Id(), oOosV1)
			registry.Register(budsV1.Id(), budsV1)
			registry.Register(bvmsV1.Id(), bvmsV1)
			registry.Register(hossV1.Id(), hossV1)
			registry.Register(budsV2.Id(), budsV2)
			registry.Register(bvmsV2.Id(), bvmsV2)
			registry.Register(hzdV2.Id(), hzdV2)
			registry.Register(dtV1.Id(), dtV1)
			return nil
		},
	})
	return registry
}
