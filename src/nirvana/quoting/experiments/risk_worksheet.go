package experiments

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/log"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/quoting/experiments/impl/basic_alert_count"
	"nirvanatech.com/nirvana/quoting/experiments/impl/basic_hos_compliance_score"
	"nirvanatech.com/nirvana/quoting/experiments/impl/basic_unsafe_driving_score"
	"nirvanatech.com/nirvana/quoting/experiments/impl/basic_vehicle_maintenance_score"
	common "nirvanatech.com/nirvana/quoting/experiments/impl/common"
	"nirvanatech.com/nirvana/quoting/experiments/impl/driver_oos"
	"nirvanatech.com/nirvana/quoting/experiments/impl/driver_turnover"
	"nirvanatech.com/nirvana/quoting/experiments/impl/fleet_size"
	"nirvanatech.com/nirvana/quoting/experiments/impl/hazard_zone_distance"
	"nirvanatech.com/nirvana/quoting/experiments/impl/overall_oos"
	"nirvanatech.com/nirvana/quoting/experiments/impl/primary_operating_class"
	"nirvanatech.com/nirvana/quoting/experiments/impl/risk_factors"
	"nirvanatech.com/nirvana/quoting/experiments/impl/utilization"
	"nirvanatech.com/nirvana/quoting/experiments/impl/vin_visibility"
	risk_factors_client "nirvanatech.com/nirvana/underwriting/risk_factors"
)

var experimentToRiskWorksheetVersion = map[uuid.UUID]int{
	// Experiments that are not yet migrated to V2.
	utilization.V1ExperimentId:    risk_factors_client.PricingVersionV1,
	vin_visibility.V1ExperimentId: risk_factors_client.PricingVersionV1,
	fleet_size.V1ExperimentId:     risk_factors_client.PricingVersionV1,

	// Experiments that are migrated to V2.
	basic_alert_count.V1ExperimentId:               risk_factors_client.PricingVersionV2,
	basic_vehicle_maintenance_score.V1ExperimentId: risk_factors_client.PricingVersionV2,
	basic_vehicle_maintenance_score.V2ExperimentId: risk_factors_client.PricingVersionV2,
	basic_unsafe_driving_score.V1ExperimentId:      risk_factors_client.PricingVersionV2,
	basic_unsafe_driving_score.V2ExperimentId:      risk_factors_client.PricingVersionV2,
	basic_hos_compliance_score.V1ExperimentId:      risk_factors_client.PricingVersionV2,
	driver_oos.V1ExperimentId:                      risk_factors_client.PricingVersionV2,
	driver_turnover.V1ExperimentId:                 risk_factors_client.PricingVersionV2,
	hazard_zone_distance.V1ExperimentId:            risk_factors_client.PricingVersionV2,
	hazard_zone_distance.V2ExperimentId:            risk_factors_client.PricingVersionV2,
	overall_oos.V1ExperimentId:                     risk_factors_client.PricingVersionV2,
	primary_operating_class.V1ExperimentId:         risk_factors_client.PricingVersionV2,
	utilization.V2ExperimentId:                     risk_factors_client.PricingVersionV2,
	fleet_size.V2ExperimentId:                      risk_factors_client.PricingVersionV2,
}

var experimentToRiskFactorLabel = map[uuid.UUID]risk_factors_client.RiskFactorLabel{
	// Experiments that are not yet migrated to V2.
	utilization.V1ExperimentId:    risk_factors_client.RiskFactorLabel_Utilization_Adjustment,
	vin_visibility.V1ExperimentId: risk_factors_client.RiskFactorLabel_Vin_Visibility_Adjustment,
	fleet_size.V1ExperimentId:     risk_factors_client.RiskFactorLabel_Fleet_Size_Adjustment,

	// Experiments that are migrated to V2.
	basic_alert_count.V1ExperimentId:               risk_factors_client.RiskFactorLabel_BASIC_Alert_Count_Adjustment,
	basic_vehicle_maintenance_score.V1ExperimentId: risk_factors_client.RiskFactorLabel_BASIC_Vehicle_Maintenance_Score_Adjustment,
	basic_vehicle_maintenance_score.V2ExperimentId: risk_factors_client.RiskFactorLabel_BASIC_Vehicle_Maintenance_Score_Adjustment,
	basic_unsafe_driving_score.V1ExperimentId:      risk_factors_client.RiskFactorLabel_BASIC_Unsafe_Driving_Score_Adjustment,
	basic_unsafe_driving_score.V2ExperimentId:      risk_factors_client.RiskFactorLabel_BASIC_Unsafe_Driving_Score_Adjustment,
	basic_hos_compliance_score.V1ExperimentId:      risk_factors_client.RiskFactorLabel_BASIC_HOS_Compliance_Score_Adjustment,
	driver_oos.V1ExperimentId:                      risk_factors_client.RiskFactorLabel_Driver_OOS_Adjustment,
	driver_turnover.V1ExperimentId:                 risk_factors_client.RiskFactorLabel_Driver_Turnover_Adjustment,
	hazard_zone_distance.V1ExperimentId:            risk_factors_client.RiskFactorLabel_Hazard_Zones_Distance_Adjustment,
	hazard_zone_distance.V2ExperimentId:            risk_factors_client.RiskFactorLabel_Hazard_Zones_Distance_Adjustment,
	overall_oos.V1ExperimentId:                     risk_factors_client.RiskFactorLabel_Overall_OOS_Adjustment,
	primary_operating_class.V1ExperimentId:         risk_factors_client.RiskFactorLabel_Primary_Operation_Class_Adjustment,
	utilization.V2ExperimentId:                     risk_factors_client.RiskFactorLabel_Utilization_Adjustment,
	fleet_size.V2ExperimentId:                      risk_factors_client.RiskFactorLabel_Fleet_Size_Adjustment,
}

var experimentToRiskFactorCategory = map[uuid.UUID]risk_factors_client.Category{
	utilization.V1ExperimentId:    risk_factors_client.Category_OPERATIONS,
	vin_visibility.V1ExperimentId: risk_factors_client.Category_EQUIPMENTS,
	fleet_size.V1ExperimentId:     risk_factors_client.Category_OPERATIONS,
}

func insertOrUpdateRiskFactorIntoWorksheet(
	ctx context.Context,
	riskFactorsServiceClient risk_factors.RiskFactorsServiceClient,
	applicationReviewId uuid.UUID,
	experimentId uuid.UUID,
	metadata *common.ExperimentMetadata,
) error {
	if metadata == nil {
		return errors.New("missing metadata")
	}

	riskWorksheetVersion, found := experimentToRiskWorksheetVersion[experimentId]
	if !found {
		return errors.Newf("risk worksheet version not found for experiment %s", experimentId)
	}

	riskWorksheetLabel, found := experimentToRiskFactorLabel[experimentId]
	if !found {
		return errors.Newf("risk factor label not found for experiment %s", experimentId)
	}

	var err error
	switch riskWorksheetVersion {
	case 1:
		if metadata.PreviousCreditsByCoverage == nil {
			return errors.New("missing previous credits by coverage")
		}

		riskFactorCategory, found := experimentToRiskFactorCategory[experimentId]
		if !found {
			return errors.Newf("risk factor category not found for experiment %s", experimentId)
		}

		err = insertOrUpdateRiskFactorV1(
			ctx,
			riskFactorsServiceClient,
			applicationReviewId.String(),
			parseCreditByCoverageMap(metadata.CreditsByCoverage),
			parseCreditByCoverageMap(metadata.PreviousCreditsByCoverage),
			riskWorksheetLabel,
			riskFactorCategory,
			metadata.Explanation,
			metadata.ValueUsed,
		)
	case 2:
		err = insertOrUpdateRiskFactorV2(
			ctx,
			riskFactorsServiceClient,
			riskWorksheetLabel,
			applicationReviewId.String(),
			parseCreditByCoverageMap(metadata.CreditsByCoverage),
			metadata.Explanation,
			metadata.ValueUsed,
		)
	}

	if err != nil {
		if errors.Is(err, risk_factors.ErrRiskWorksheetNotFound) || errors.Is(err, risk_factors.ErrInvalidRiskWorksheetVersion) {
			log.Warn(
				ctx,
				"risk worksheet not found or invalid version for experiment %s",
				log.String("error", err.Error()),
				log.String("experimentId", experimentId.String()),
			)
			return nil
		}
		return errors.Wrapf(err, "failed to insert or update risk factor for experiment %s", experimentId)
	}

	return nil
}

func parseCreditByCoverageMap(creditsPerCoverageMap map[app_enums.Coverage]int32) map[risk_factors_client.Coverage]int32 {
	parsedMap := make(map[risk_factors_client.Coverage]int32)
	for coverage, credit := range creditsPerCoverageMap {
		parsedMap[risk_factors_client.Coverage(coverage)] = credit
	}

	return parsedMap
}

// InsertOrUpdateRiskFactor handles the common flow for updating risk factors
func insertOrUpdateRiskFactorV1(
	ctx context.Context,
	riskFactorsServiceClient risk_factors.RiskFactorsServiceClient,
	appReviewId string,
	creditAppliedPerCoverage map[risk_factors_client.Coverage]int32,
	previousCreditsByCoverage map[risk_factors_client.Coverage]int32,
	riskFactorLabel risk_factors_client.RiskFactorLabel,
	category risk_factors_client.Category,
	notes string,
	value string,
) error {
	// Get worksheet
	worksheet, err := riskFactorsServiceClient.GetLatestWorksheetForReview(
		ctx,
		&risk_factors_client.GetLatestWorksheetForReviewRequest{
			ReviewId: appReviewId,
		},
	)
	if err != nil {
		return errors.Wrap(err, "failed to get latest risk worksheet")
	}
	if worksheet == nil || worksheet.Id == "" {
		log.Error(ctx, "no risk worksheet found for review %s", log.String("appReviewId", appReviewId))
		return risk_factors.ErrRiskWorksheetNotFound
	}

	var coveragePricingDetails []*risk_factors_client.CoveragePricingDetail
	if worksheet.Version == int32(risk_factors_client.PricingVersionV1) {
		coveragePricingDetails, err = createCoveragePricingDetailsV1(
			worksheet.GetPricingDetails(),
			creditAppliedPerCoverage,
			previousCreditsByCoverage,
			category,
		)
	} else {
		coveragePricingDetails, err = createCoveragePricingDetailsV2(creditAppliedPerCoverage)
	}
	if err != nil {
		return errors.Wrapf(err, "failed to create coverage pricing details")
	}

	var averageCreditApplied int32
	if n := len(creditAppliedPerCoverage); n > 0 {
		var total int32
		for _, credit := range creditAppliedPerCoverage {
			total += credit
		}
		averageCreditApplied = total / int32(n)
	}

	req := &risk_factors_client.InsertOrUpdateRiskFactorRequest{
		ReviewId:              appReviewId,
		RiskFactorLabel:       riskFactorLabel,
		Value:                 value,
		Notes:                 notes,
		Sentiment:             sentimentFromCredit(averageCreditApplied),
		CoveragePricingDetail: coveragePricingDetails,
	}

	_, err = riskFactorsServiceClient.InsertOrUpdateRiskFactor(ctx, req)
	if err != nil {
		return errors.Wrapf(
			err,
			"failed to insert or update risk factor %s for review %s",
			riskFactorLabel,
			appReviewId,
		)
	}

	return nil
}

func createCoveragePricingDetailsV2(
	creditAppliedPerCoverage map[risk_factors_client.Coverage]int32,
) ([]*risk_factors_client.CoveragePricingDetail, error) {
	details := make([]*risk_factors_client.CoveragePricingDetail, 0, len(creditAppliedPerCoverage))
	for cov, credit := range creditAppliedPerCoverage {
		details = append(details, &risk_factors_client.CoveragePricingDetail{
			Coverage: cov,
			Credit:   int32(credit),
		})
	}
	return details, nil
}

// TODO: Remove this function once we fully migrate to v2 pricing
func createCoveragePricingDetailsV1(
	pricingDetails *risk_factors_client.WorksheetPricingDetails,
	creditAppliedPerCoverage map[risk_factors_client.Coverage]int32,
	previousCreditsByCoverage map[risk_factors_client.Coverage]int32,
	category risk_factors_client.Category,
) ([]*risk_factors_client.CoveragePricingDetail, error) {
	// Create map to store the adjusted credit values for each coverage
	// Initialize with the base experiment credit for all applicable coverages
	adjustedCredits := make(map[risk_factors_client.Coverage]int32)
	for cov, credit := range creditAppliedPerCoverage {
		adjustedCredits[cov] = credit
	}

	// If we have current pricing, adjust the credits based on existing worksheet values
	if pricingDetails != nil {
		for _, catDetails := range pricingDetails.GetCategoryPricingDetails() {
			if catDetails.GetCategory() != category {
				continue
			}

			for _, coverageDetail := range catDetails.GetCoveragePricingDetail() {
				coverage := coverageDetail.GetCoverage()
				credit, ok := adjustedCredits[coverage]
				if !ok {
					continue
				}

				currentCredit := coverageDetail.GetCredit()
				newCredit := currentCredit + credit

				// If we have previous credits by coverage, subtract the previous credit for this specific coverage
				if previousCreditsByCoverage != nil {
					if previousCredit, exists := previousCreditsByCoverage[coverage]; exists {
						newCredit = newCredit - previousCredit
					}
				}

				adjustedCredits[coverage] = newCredit
			}

		}
	}

	// Convert map to slice
	var coveragePricingDetailsList []*risk_factors_client.CoveragePricingDetail
	for coverage, credit := range adjustedCredits {
		coveragePricingDetailsList = append(coveragePricingDetailsList, &risk_factors_client.CoveragePricingDetail{
			Coverage: coverage,
			Credit:   credit,
		})
	}

	return coveragePricingDetailsList, nil
}

func sentimentFromCredit(credit int32) risk_factors_client.Sentiment {
	switch {
	case credit > 0:
		return risk_factors_client.Sentiment_POSITIVE
	case credit < 0:
		return risk_factors_client.Sentiment_NEGATIVE
	default:
		return risk_factors_client.Sentiment_NEUTRAL
	}
}

// GetRiskFactorLabelForExperiment returns the risk factor label for a given experiment ID.
// Returns an error if the experiment ID is not found in the mapping.
func GetRiskFactorLabelForExperiment(experimentID uuid.UUID) (risk_factors_client.RiskFactorLabel, error) {
	label, found := experimentToRiskFactorLabel[experimentID]
	if !found {
		return 0, errors.Newf("risk factor label not found for experiment %s", experimentID)
	}
	return label, nil
}

func insertOrUpdateRiskFactorV2(
	ctx context.Context,
	riskFactorsServiceClient risk_factors.RiskFactorsServiceClient,
	riskFactorLabel risk_factors_client.RiskFactorLabel,
	appReviewId string,
	creditAppliedPerCoverage map[risk_factors_client.Coverage]int32,
	notes string,
	value string,
) error {
	// Get worksheet
	worksheet, err := riskFactorsServiceClient.GetLatestWorksheetForReview(
		ctx,
		&risk_factors_client.GetLatestWorksheetForReviewRequest{
			ReviewId: appReviewId,
		},
	)
	if err != nil {
		return errors.Wrap(err, "failed to get latest risk worksheet")
	}
	if worksheet == nil || worksheet.Id == "" {
		log.Error(ctx, "no risk worksheet found for review %s", log.String("appReviewId", appReviewId))
		return risk_factors.ErrRiskWorksheetNotFound
	}

	var coveragePricingDetails []*risk_factors_client.CoveragePricingDetail
	if worksheet.Version != int32(risk_factors_client.PricingVersionV2) {
		return risk_factors.ErrInvalidRiskWorksheetVersion
	}

	coveragePricingDetails, err = createCoveragePricingDetailsV2(creditAppliedPerCoverage)
	if err != nil {
		return errors.Wrapf(err, "failed to create coverage pricing details")
	}

	var averageCreditApplied int32
	if n := len(creditAppliedPerCoverage); n > 0 {
		var total int32
		for _, credit := range creditAppliedPerCoverage {
			total += credit
		}
		averageCreditApplied = total / int32(n)
	}

	req := &risk_factors_client.InsertOrUpdateRiskFactorRequest{
		ReviewId:              appReviewId,
		RiskFactorLabel:       riskFactorLabel,
		Value:                 value,
		Notes:                 notes,
		Sentiment:             sentimentFromCredit(averageCreditApplied),
		CoveragePricingDetail: coveragePricingDetails,
	}

	_, err = riskFactorsServiceClient.InsertOrUpdateRiskFactor(ctx, req)
	if err != nil {
		return errors.Wrapf(
			err,
			"failed to insert or update risk factor %s for review %s",
			riskFactorLabel,
			appReviewId,
		)
	}

	return nil
}
