package assign_experiments

import (
	"context"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/underwriting/risk_factors"
	"testing"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/experiments/builders"
	"nirvanatech.com/nirvana/experiments/client"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/experiments/enums"
	"nirvanatech.com/nirvana/experiments/models"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

type assignExperimentsTestSuiteEnv struct {
	fx.In

	ExperimentsClient  client.ExperimentsClient
	ExperimentsWrapper *db.ExperimentsWrapper
	Clock              *clock.Mock
	AppReviewWrapper   uw.ApplicationReviewWrapper
	RiskFactorsClient  risk_factors.RiskFactorsServiceClient
}

type assignExperimentsTestSuite struct {
	suite.Suite

	ctx   context.Context
	fxApp *fxtest.App
	env   assignExperimentsTestSuiteEnv

	appID         string
	experimentID1 uuid.UUID
	experimentID2 uuid.UUID
	experimentID3 uuid.UUID // for disabled experiments
	variantID1    uuid.UUID
	variantID2    uuid.UUID
	variantID3    uuid.UUID

	assignEnv assignEnv
}

func (s *assignExperimentsTestSuite) buildTestAssignEnv() assignEnv {
	var nilDB db_api.NirvanaRW

	return assignEnv{
		DB:                       nilDB, // Not used in current tests
		Client:                   s.env.ExperimentsClient,
		Wrapper:                  s.env.ExperimentsWrapper,
		Clock:                    s.env.Clock,
		ApplicationReviewWrapper: s.env.AppReviewWrapper,
	}
}

func TestAssignExperiments(t *testing.T) {
	suite.Run(t, new(assignExperimentsTestSuite))
}

func (s *assignExperimentsTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxApp = testloader.RequireStart(s.T(), &s.env)

	s.assignEnv = s.buildTestAssignEnv()
}

func (s *assignExperimentsTestSuite) TearDownSuite() {
	s.fxApp.RequireStop()
}

func (s *assignExperimentsTestSuite) SetupTest() {
	// Generate fresh IDs for each test to avoid primary key conflicts
	s.appID = uuid.New().String()
	s.experimentID1 = uuid.New()
	s.experimentID2 = uuid.New()
	s.experimentID3 = uuid.New()
	s.variantID1 = uuid.New()
	s.variantID2 = uuid.New()
	s.variantID3 = uuid.New()

	// Create test experiments and variants
	s.createTestExperiment(s.experimentID1, s.variantID1, true)  // enabled
	s.createTestExperiment(s.experimentID2, s.variantID2, true)  // enabled
	s.createTestExperiment(s.experimentID3, s.variantID3, false) // disabled
}

func (s *assignExperimentsTestSuite) createTestExperiment(experimentID, variantID uuid.UUID, enabled bool) {
	// Create experiment
	experiment := builders.NewExperimentBuilder().
		WithId(experimentID).
		WithEnabled(enabled).
		WithDomain(enums.DomainPricing).
		WithSubjectType(enums.SubjectTypeApplication).
		WithAllocationMethod(enums.AllocationMethodSingleVariant).
		WithName("Test Experiment").
		WithDescription("Test experiment for unit tests").
		WithCreatedBy(uuid.New()).
		Build()

	err := s.env.ExperimentsWrapper.InsertExperiment(s.ctx, experiment)
	s.Require().NoError(err)

	// Create variant
	variant := builders.NewExperimentVariantBuilder().
		WithId(variantID).
		WithExperimentId(experimentID).
		WithName("Test Variant").
		WithDescription("Test variant for unit tests").
		Build()

	err = s.env.ExperimentsWrapper.InsertVariant(s.ctx, variant)
	s.Require().NoError(err)
}

func (s *assignExperimentsTestSuite) TestGetExperimentToEnable_WithNewExperiment() {
	activateIDs := []uuid.UUID{s.experimentID1}

	result, err := getExperimentToEnable(s.ctx, s.env.ExperimentsClient, s.appID, activateIDs)

	s.Require().NoError(err)
	s.Require().Len(result, 1)
	s.Require().Equal(s.experimentID1, result[0].Id)
	s.Require().True(result[0].Enabled)
}

func (s *assignExperimentsTestSuite) TestGetExperimentToEnable_WithExistingAssignment() {
	activateIDs := []uuid.UUID{s.experimentID1}

	// Create an existing assignment for this experiment using the same clock
	assignment := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID1).
		WithVariantId(s.variantID1).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(false).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{&assignment})
	s.Require().NoError(err)

	result, err := getExperimentToEnable(s.ctx, s.env.ExperimentsClient, s.appID, activateIDs)

	// Should return 1 experiment since the existing assignment is disabled and can be re-enabled
	s.Require().NoError(err)
	s.Require().Len(result, 1)
	s.Require().Equal(s.experimentID1, result[0].Id)
}

func (s *assignExperimentsTestSuite) TestGetExperimentToEnable_WithExistingEnabledAssignment() {
	activateIDs := []uuid.UUID{s.experimentID1}

	// Create an existing ENABLED assignment for this experiment using the same clock
	assignment := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID1).
		WithVariantId(s.variantID1).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(true). // Enabled assignment
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{&assignment})
	s.Require().NoError(err)

	result, err := getExperimentToEnable(s.ctx, s.env.ExperimentsClient, s.appID, activateIDs)

	// Should return 0 experiments because existing assignment is already enabled
	s.Require().NoError(err)
	s.Require().Len(result, 0)
}

func (s *assignExperimentsTestSuite) TestGetExperimentToEnable_WithMultipleExperiments() {
	activateIDs := []uuid.UUID{s.experimentID1, s.experimentID2}

	// Create assignment for experiment1 but not experiment2 using the same clock
	assignment1 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID1).
		WithVariantId(s.variantID1).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(false).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{&assignment1})
	s.Require().NoError(err)

	result, err := getExperimentToEnable(s.ctx, s.env.ExperimentsClient, s.appID, activateIDs)

	// Should return both experiments: experiment1 (disabled assignment can be re-enabled) and experiment2 (no assignment)
	s.Require().NoError(err)
	s.Require().Len(result, 2)

	// Extract IDs from results for comparison
	resultIDs := make([]uuid.UUID, len(result))
	for i, exp := range result {
		resultIDs[i] = exp.Id
	}

	// Check that both experiments are in the results
	s.Require().Contains(resultIDs, s.experimentID1)
	s.Require().Contains(resultIDs, s.experimentID2)
}

func (s *assignExperimentsTestSuite) TestGetExperimentToDisable_WithExistingEnabledAssignment() {
	deactivateIDs := []uuid.UUID{s.experimentID2}

	// Create an existing enabled assignment using the same clock
	assignment := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID2).
		WithVariantId(s.variantID2).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(true).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{&assignment})
	s.Require().NoError(err)

	result, err := getExperimentToDisable(s.ctx, s.env.ExperimentsClient, s.appID, deactivateIDs)

	s.Require().NoError(err)
	s.Require().Len(result, 1)
	s.Require().Equal(s.experimentID2, result[0].Id)
}

func (s *assignExperimentsTestSuite) TestGetExperimentToDisable_WithNoExistingAssignment() {
	deactivateIDs := []uuid.UUID{s.experimentID2}

	result, err := getExperimentToDisable(s.ctx, s.env.ExperimentsClient, s.appID, deactivateIDs)

	// Should return empty since there's no existing assignment to disable
	s.Require().NoError(err)
	s.Require().Len(result, 0)
}

func (s *assignExperimentsTestSuite) TestGetExperimentToDisable_WithExistingDisabledAssignment() {
	deactivateIDs := []uuid.UUID{s.experimentID2}

	// Create an existing disabled assignment using the same clock
	assignment := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID2).
		WithVariantId(s.variantID2).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(false).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{&assignment})
	s.Require().NoError(err)

	result, err := getExperimentToDisable(s.ctx, s.env.ExperimentsClient, s.appID, deactivateIDs)

	// Should return empty since the existing assignment is already disabled
	s.Require().NoError(err)
	s.Require().Len(result, 0)
}

func (s *assignExperimentsTestSuite) TestProcessApplicationAssignments_EnableNewExperiment() {
	activateIDs := []uuid.UUID{s.experimentID1}
	var deactivateIDs []uuid.UUID

	// Verify no assignments exist initially
	initialAssignments, err := s.env.ExperimentsClient.GetLatestExperimentsAssignations(
		s.ctx, enums.DomainPricing, s.appID)
	s.Require().NoError(err)
	s.Require().Len(initialAssignments, 0)

	// Process assignments
	err = processApplicationAssignments(s.ctx, s.assignEnv, s.appID, activateIDs, deactivateIDs)
	s.Require().NoError(err)

	// Verify assignment was created
	finalAssignments, err := s.env.ExperimentsClient.GetLatestExperimentsAssignations(
		s.ctx, enums.DomainPricing, s.appID)
	s.Require().NoError(err)
	s.Require().Len(finalAssignments, 1)
	s.Require().Equal(s.experimentID1, finalAssignments[0].ExperimentId)
	s.Require().True(finalAssignments[0].Enabled)
}

func (s *assignExperimentsTestSuite) TestProcessApplicationAssignments_DisableExistingExperiment() {
	var activateIDs []uuid.UUID
	deactivateIDs := []uuid.UUID{s.experimentID2}

	// Create an existing enabled assignment using the same clock
	assignment := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID2).
		WithVariantId(s.variantID2).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(true).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{&assignment})
	s.Require().NoError(err)

	// Advance the clock so the new assignment has a later timestamp
	s.env.Clock.Add(time.Minute)

	// Process assignments
	err = processApplicationAssignments(s.ctx, s.assignEnv, s.appID, activateIDs, deactivateIDs)
	s.Require().NoError(err)

	// Verify a new disabled assignment was created
	finalAssignments, err := s.env.ExperimentsClient.GetLatestExperimentsAssignations(
		s.ctx,
		enums.DomainPricing,
		s.appID,
	)
	s.Require().NoError(err)

	// Also check all assignments in the database for this experiment
	allAssignments, err := s.env.ExperimentsWrapper.GetAssignments(s.ctx)
	s.Require().NoError(err)
	s.T().Logf("Total assignments in database: %d", len(allAssignments))
	for i, assignment := range allAssignments {
		if assignment.SubjectId == s.appID && assignment.ExperimentId == s.experimentID2 {
			s.T().Logf("DB Assignment %d for our experiment: ID=%s, ExperimentID=%s, Enabled=%v, CreatedAt=%v",
				i, assignment.Id, assignment.ExperimentId, assignment.Enabled, assignment.CreatedAt)
		}
	}

	s.Require().Len(finalAssignments, 1)
	s.Require().Equal(s.experimentID2, finalAssignments[0].ExperimentId)
	s.Require().False(finalAssignments[0].Enabled)
}

func (s *assignExperimentsTestSuite) TestProcessApplicationAssignments_EnableAndDisable() {
	activateIDs := []uuid.UUID{s.experimentID1}
	deactivateIDs := []uuid.UUID{s.experimentID2}

	// Create an existing enabled assignment for experiment2 using the same clock
	assignment2 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID2).
		WithVariantId(s.variantID2).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(true).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{&assignment2})
	s.Require().NoError(err)

	// Advance the clock so the new assignments have later timestamps
	s.env.Clock.Add(time.Minute)

	// Process assignments
	err = processApplicationAssignments(s.ctx, s.assignEnv, s.appID, activateIDs, deactivateIDs)
	s.Require().NoError(err)

	// Verify both assignments were created
	finalAssignments, err := s.env.ExperimentsClient.GetLatestExperimentsAssignations(
		s.ctx, enums.DomainPricing, s.appID)
	s.Require().NoError(err)
	s.Require().Len(finalAssignments, 2)

	// Check that we have one enabled and one disabled assignment
	enabledCount := 0
	disabledCount := 0
	var experiment1Found, experiment2Found bool

	for _, assignment := range finalAssignments {
		if assignment.ExperimentId == s.experimentID1 {
			experiment1Found = true
			s.Require().True(assignment.Enabled, "Experiment1 should be enabled")
			enabledCount++
		} else if assignment.ExperimentId == s.experimentID2 {
			experiment2Found = true
			s.Require().False(assignment.Enabled, "Experiment2 should be disabled")
			disabledCount++
		}
	}

	s.Require().True(experiment1Found, "Experiment1 assignment should exist")
	s.Require().True(experiment2Found, "Experiment2 assignment should exist")
	s.Require().Equal(1, enabledCount, "Should have one enabled assignment")
	s.Require().Equal(1, disabledCount, "Should have one disabled assignment")
}

func (s *assignExperimentsTestSuite) TestProcessApplicationAssignments_NoExperimentsToProcess() {
	activateIDs := []uuid.UUID{s.experimentID1}
	deactivateIDs := []uuid.UUID{s.experimentID2}

	// Create existing assignments for both experiments using the same clock
	assignment1 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID1).
		WithVariantId(s.variantID1).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(false).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	assignment2 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(s.experimentID2).
		WithVariantId(s.variantID2).
		WithSubjectId(s.appID).
		WithDomain(enums.DomainPricing).
		WithEnabled(false).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{&assignment1, &assignment2})
	s.Require().NoError(err)

	// Process assignments
	err = processApplicationAssignments(s.ctx, s.assignEnv, s.appID, activateIDs, deactivateIDs)
	s.Require().NoError(err)

	// Verify no new assignments were created (should still have the original 2)
	finalAssignments, err := s.env.ExperimentsClient.GetLatestExperimentsAssignations(
		s.ctx, enums.DomainPricing, s.appID)
	s.Require().NoError(err)
	s.Require().Len(finalAssignments, 2)

	// Both should still be disabled
	for _, assignment := range finalAssignments {
		s.Require().False(assignment.Enabled, "All assignments should remain disabled")
	}
}

// TestFullWorkflowIntegration tests the complete end-to-end flow of experiment activation/deactivation
// and assignment processing using the refactored workflow
func (s *assignExperimentsTestSuite) TestFullWorkflowIntegration() {
	// Create additional test experiments for a comprehensive integration test
	experimentToActivate1 := uuid.New()
	experimentToActivate2 := uuid.New()
	experimentToDeactivate1 := uuid.New()
	experimentToDeactivate2 := uuid.New()

	variantToActivate1 := uuid.New()
	variantToActivate2 := uuid.New()
	variantToDeactivate1 := uuid.New()
	variantToDeactivate2 := uuid.New()

	// Create experiments with different initial states
	s.createTestExperiment(experimentToActivate1, variantToActivate1, false)    // disabled -> will be activated
	s.createTestExperiment(experimentToActivate2, variantToActivate2, false)    // disabled -> will be activated
	s.createTestExperiment(experimentToDeactivate1, variantToDeactivate1, true) // enabled -> will be deactivated
	s.createTestExperiment(experimentToDeactivate2, variantToDeactivate2, true) // enabled -> will be deactivated

	// Create test applications with different assignment states
	app1 := uuid.New().String()
	app2 := uuid.New().String()
	app3 := uuid.New().String()

	// App1: Has enabled assignments for experiments to be deactivated
	assignment1 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(experimentToDeactivate1).
		WithVariantId(variantToDeactivate1).
		WithSubjectId(app1).
		WithDomain(enums.DomainPricing).
		WithEnabled(true).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	assignment2 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(experimentToDeactivate2).
		WithVariantId(variantToDeactivate2).
		WithSubjectId(app1).
		WithDomain(enums.DomainPricing).
		WithEnabled(true).
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	// App2: Has mixed assignments (one disabled for activation, one enabled for deactivation)
	assignment3 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(experimentToActivate1).
		WithVariantId(variantToActivate1).
		WithSubjectId(app2).
		WithDomain(enums.DomainPricing).
		WithEnabled(false). // disabled assignment for experiment to be activated
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	assignment4 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(experimentToDeactivate1).
		WithVariantId(variantToDeactivate1).
		WithSubjectId(app2).
		WithDomain(enums.DomainPricing).
		WithEnabled(true). // enabled assignment for experiment to be deactivated
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	// App3: Has a disabled assignment for experiment to be activated
	assignment5 := builders.NewExperimentAssignmentBuilder().
		WithId(uuid.New()).
		WithExperimentId(experimentToActivate2).
		WithVariantId(variantToActivate2).
		WithSubjectId(app3).
		WithDomain(enums.DomainPricing).
		WithEnabled(false). // disabled assignment for experiment to be activated
		WithChangeReason("Test setup").
		WithCreatedAt(s.env.Clock.Now()).
		WithUpdatedAt(s.env.Clock.Now()).
		Build()

	// Insert all pre-assignments
	err := s.env.ExperimentsWrapper.InsertAssignments(s.ctx, []*models.ExperimentAssignment{
		&assignment1, &assignment2, &assignment3, &assignment4, &assignment5,
	})
	s.Require().NoError(err)

	// Advance clock to ensure new assignments have different timestamps
	s.env.Clock.Add(time.Minute)

	// Test the current workflow structure
	activateIDs := []uuid.UUID{experimentToActivate1, experimentToActivate2}
	deactivateIDs := []uuid.UUID{experimentToDeactivate1, experimentToDeactivate2}

	// Step 1: Deactivate experiments at the experiment level first
	err = s.env.ExperimentsWrapper.UpdateExperimentDetail(s.ctx, experimentToDeactivate1, func(e *models.Experiment) error {
		e.Enabled = false
		return nil
	})
	s.Require().NoError(err)

	err = s.env.ExperimentsWrapper.UpdateExperimentDetail(s.ctx, experimentToDeactivate2, func(e *models.Experiment) error {
		e.Enabled = false
		return nil
	})
	s.Require().NoError(err)

	// Step 2: Activate experiments at the experiment level
	err = s.env.ExperimentsWrapper.UpdateExperimentDetail(s.ctx, experimentToActivate1, func(e *models.Experiment) error {
		e.Enabled = true
		return nil
	})
	s.Require().NoError(err)

	err = s.env.ExperimentsWrapper.UpdateExperimentDetail(s.ctx, experimentToActivate2, func(e *models.Experiment) error {
		e.Enabled = true
		return nil
	})
	s.Require().NoError(err)

	// Step 3: Process all application assignments together (both activations and deactivations)
	err = processApplicationAssignments(s.ctx, s.assignEnv, app1, activateIDs, deactivateIDs)
	s.Require().NoError(err)

	err = processApplicationAssignments(s.ctx, s.assignEnv, app2, activateIDs, deactivateIDs)
	s.Require().NoError(err)

	err = processApplicationAssignments(s.ctx, s.assignEnv, app3, activateIDs, deactivateIDs)
	s.Require().NoError(err)

	// Verify results for each application
	testApps := []struct {
		appID               string
		expectedAssignments map[uuid.UUID]bool // experimentID -> expectedEnabled
	}{
		{
			appID: app1,
			expectedAssignments: map[uuid.UUID]bool{
				experimentToDeactivate1: false, // was enabled, should now be disabled
				experimentToDeactivate2: false, // was enabled, should now be disabled
				experimentToActivate1:   true,  // new enabled assignment
				experimentToActivate2:   true,  // new enabled assignment
			},
		},
		{
			appID: app2,
			expectedAssignments: map[uuid.UUID]bool{
				experimentToActivate1:   true,  // was disabled, should now be enabled
				experimentToDeactivate1: false, // was enabled, should now be disabled
				experimentToActivate2:   true,  // new enabled assignment
			},
		},
		{
			appID: app3,
			expectedAssignments: map[uuid.UUID]bool{
				experimentToActivate2: true, // was disabled, should now be enabled
				experimentToActivate1: true, // new enabled assignment
			},
		},
	}

	for _, testApp := range testApps {
		assignments, err := s.env.ExperimentsClient.GetLatestExperimentsAssignations(
			s.ctx, enums.DomainPricing, testApp.appID)
		s.Require().NoError(err)

		assignmentMap := make(map[uuid.UUID]bool)
		for _, assignment := range assignments {
			assignmentMap[assignment.ExperimentId] = assignment.Enabled
		}

		for expID, expectedEnabled := range testApp.expectedAssignments {
			actualEnabled, exists := assignmentMap[expID]
			s.Require().True(exists, "Expected assignment for experiment %s in app %s", expID, testApp.appID)
			s.Require().Equal(expectedEnabled, actualEnabled,
				"Expected assignment for experiment %s in app %s to be enabled=%v, got enabled=%v",
				expID, testApp.appID, expectedEnabled, actualEnabled)
		}
	}

	s.T().Logf("Integration test completed successfully with %d experiments and %d applications",
		len(activateIDs)+len(deactivateIDs), len(testApps))
}
