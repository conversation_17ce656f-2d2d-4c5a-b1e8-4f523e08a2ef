load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "assign_experiments",
    srcs = ["assign_experiments.go"],
    importpath = "nirvanatech.com/nirvana/quoting/experiments/cmd/assign_experiments",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/db-api",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/experiments",
        "//nirvana/experiments/client",
        "//nirvana/experiments/db",
        "//nirvana/experiments/enums",
        "//nirvana/experiments/models",
        "//nirvana/infra/fx/appfx/cobrafx",
        "//nirvana/quoting/experiments",
        "//nirvana/underwriting/risk_factors",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_spf13_cobra//:cobra",
        "@com_github_volatiletech_sqlboiler_v4//queries",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "assign_experiments_test",
    srcs = ["assign_experiments_test.go"],
    embed = [":assign_experiments"],
    deps = [
        "//nirvana/db-api",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/experiments/builders",
        "//nirvana/experiments/client",
        "//nirvana/experiments/db",
        "//nirvana/experiments/enums",
        "//nirvana/experiments/models",
        "//nirvana/infra/fx/testloader",
        "//nirvana/underwriting/risk_factors",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
