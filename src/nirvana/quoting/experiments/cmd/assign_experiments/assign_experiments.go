// Package assign_experiments provides CLI tools to assign and manage quoting experiments.
//
// # assign-experiments
//
// The `assign-experiments` command activates and deactivates experiments for applications that meet specific quoting review criteria.
// It queries applications in quoting review that haven't been quoted yet, then processes experiment assignments for each application.
//
// **The command will:**
//  1. Activate specified experiments (set enabled=true in experiments table)
//  2. Deactivate specified experiments (set enabled=false in experiments table)
//  3. Query applications in quoting review that haven't been quoted yet
//  4. For each application, create assignment records based on experiment enabled status
//  5. Create new assignment records (append-only) for all changes
//
// **Important considerations:**
//   - The assignments table is append-only, so deactivation creates new records with enabled=false
//   - Experiment activation/deactivation happens at the experiment level first
//   - Assignment records are then created based on the current experiment enabled status
//   - At least one of --activate-experiments or --deactivate-experiments must be specified
//   - Both parameters accept comma-separated experiment IDs
//   - Only applications in 'AppStateUnderUWReview' state are processed
//   - Test agencies are excluded from processing
//   - Applications that have already been quoted are excluded
//
// **Application selection criteria:**
//   - Application state must be 'AppStateUnderUWReview'
//   - Agency must not be a test agency
//   - Application review state must not be 'ApplicationReviewStateStale'
//   - Application must not have been quoted (no 'AppStateQuoteGenerated' state transitions)
//   - Application review current status must be 'InReview', 'BORRequested', or 'ReviewNotStarted'
//
// Example usage:
//
//	bazel run //nirvana/quoting/experiments/cmd assign-experiments --activate-experiments f3c8b0a2-4d1e-4b5c-9f3d-7a2e6f8b1c5e,a1b2c3d4-5e6f-7a8b-9c0d-1e2f3a4b5c6d
//
//	bazel run //nirvana/quoting/experiments/cmd assign-experiments --deactivate-experiments f3c8b0a2-4d1e-4b5c-9f3d-7a2e6f8b1c5e
//
//	bazel run //nirvana/quoting/experiments/cmd assign-experiments --activate-experiments f3c8b0a2-4d1e-4b5c-9f3d-7a2e6f8b1c5e --deactivate-experiments a1b2c3d4-5e6f-7a8b-9c0d-1e2f3a4b5c6d
package assign_experiments

import (
	"context"
	"strings"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/spf13/cobra"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/log"
	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/client"
	"nirvanatech.com/nirvana/experiments/db"
	"nirvanatech.com/nirvana/experiments/enums"
	"nirvanatech.com/nirvana/experiments/models"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	quoting_experiments "nirvanatech.com/nirvana/quoting/experiments"
	"nirvanatech.com/nirvana/underwriting/risk_factors"
)

type assignEnv struct {
	fx.In

	DB                       db_api.NirvanaRW
	Client                   client.ExperimentsClient
	Wrapper                  *db.ExperimentsWrapper
	ExperimentsClient        experiments.Client
	Clock                    clock.Clock
	ApplicationReviewWrapper uw.ApplicationReviewWrapper
	RiskFactorsClient        risk_factors.RiskFactorsServiceClient
}

var (
	activateExperimentIds   string
	deactivateExperimentIds string
)

var AssignExperimentsCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:   "assign-experiments",
		Short: "Assign and manage experiments for applications based on quoting review criteria",
		Long: `This command manages experiments for applications that meet specific quoting review criteria.

The command will:
1. Activate specified experiments (set enabled=true in experiments table)
2. Deactivate specified experiments (set enabled=false in experiments table)
3. Query applications in quoting review that haven't been quoted yet
4. For each application, create assignment records based on current experiment enabled status
5. Create new assignment records (append-only) for all changes

The --activate-experiments parameter accepts comma-separated experiment IDs.
The --deactivate-experiments parameter accepts comma-separated experiment IDs.`,
	},
	runAssignExperiments,
)

// ApplicationResult represents the result of the SQL query
type ApplicationResult struct {
	AppID string `boil:"app_id"`
}

func runAssignExperiments(cmd *cobra.Command, _ []string, e assignEnv) error {
	ctx := cmd.Context()

	activateIDs, err := parseExperimentIds(activateExperimentIds)
	if err != nil {
		return errors.Wrap(err, "failed to parse activate experiment IDs")
	}

	deactivateIDs, err := parseExperimentIds(deactivateExperimentIds)
	if err != nil {
		return errors.Wrap(err, "failed to parse deactivate experiment IDs")
	}

	if len(activateIDs) == 0 && len(deactivateIDs) == 0 {
		return errors.New("at least one of --activate-experiments or --deactivate-experiments must be specified")
	}

	log.Info(ctx, "Starting experiment management and assignment process",
		log.Int("activate_count", len(activateIDs)),
		log.Int("deactivate_count", len(deactivateIDs)))

	// Step 1: Deactivate experiments at the experiment level
	if err := deactivateExperiments(ctx, e.ExperimentsClient, deactivateIDs); err != nil {
		return errors.Wrap(err, "failed to deactivate experiments")
	}

	// Step 2: Activate experiments at the experiment level
	if err := activateExperiments(ctx, e.ExperimentsClient, activateIDs); err != nil {
		return errors.Wrap(err, "failed to activate experiments")
	}

	// Step 3: Handle application assignments
	if err := processAllApplicationAssignments(ctx, e, activateIDs, deactivateIDs); err != nil {
		return errors.Wrap(err, "failed to process application assignments")
	}

	return nil
}

// deactivateExperiments deactivates the specified experiments at the experiment level
func deactivateExperiments(ctx context.Context, client experiments.Client, experimentIDs []uuid.UUID) error {
	log.Info(ctx, "Deactivating experiments at experiment level", log.Int("count", len(experimentIDs)))

	for _, experimentID := range experimentIDs {
		err := client.DeactivateExperiment(ctx, experimentID)
		if err != nil {
			log.Error(ctx, "Failed to deactivate experiment",
				log.String("experiment_id", experimentID.String()),
				log.Err(err))
			// Continue with other experiments even if one fails
		} else {
			log.Info(ctx, "Successfully deactivated experiment",
				log.String("experiment_id", experimentID.String()))
		}
	}

	return nil
}

// activateExperiments activates the specified experiments at the experiment level
func activateExperiments(ctx context.Context, client experiments.Client, experimentIDs []uuid.UUID) error {
	log.Info(ctx, "Activating experiments at experiment level", log.Int("count", len(experimentIDs)))

	for _, experimentID := range experimentIDs {
		err := client.ActivateExperiment(ctx, experimentID)
		if err != nil {
			log.Error(ctx, "Failed to activate experiment",
				log.String("experiment_id", experimentID.String()),
				log.Err(err))
			// Continue with other experiments even if one fails
		} else {
			log.Info(ctx, "Successfully activated experiment",
				log.String("experiment_id", experimentID.String()))
		}
	}

	return nil
}

// processAllApplicationAssignments handles the assignment processing for all applications
func processAllApplicationAssignments(
	ctx context.Context,
	env assignEnv,
	activateIDs, deactivateIDs []uuid.UUID,
) error {
	log.Info(ctx, "Starting application assignment processing")

	// Get applications matching the criteria
	applications, err := getApplicationsForExperiments(ctx, env.DB)
	if err != nil {
		return errors.Wrap(err, "failed to get applications for experiments")
	}

	log.Info(ctx, "Found applications for assignment processing", log.Int("count", len(applications)))

	// Process each application for assignments
	successCount := 0
	errorCount := 0

	for _, app := range applications {
		log.Info(ctx, "Processing application", log.String("app_id", app.AppID))
		err := processApplicationAssignments(ctx, env, app.AppID, activateIDs, deactivateIDs)
		if err != nil {
			log.Error(ctx, "Failed to process assignments for application",
				log.String("app_id", app.AppID),
				log.Err(err))
			errorCount++
		} else {
			successCount++
		}
	}

	log.Info(ctx, "Experiment assignment processing completed",
		log.Int("success_count", successCount),
		log.Int("error_count", errorCount))

	return nil
}

func parseExperimentIds(idsString string) ([]uuid.UUID, error) {
	if strings.TrimSpace(idsString) == "" {
		return []uuid.UUID{}, nil
	}

	idStrings := strings.Split(idsString, ",")
	ids := make([]uuid.UUID, 0, len(idStrings))

	for _, idStr := range idStrings {
		idStr = strings.TrimSpace(idStr)
		if idStr == "" {
			continue
		}

		id, err := uuid.Parse(idStr)
		if err != nil {
			return nil, errors.Wrapf(err, "invalid experiment ID: %s", idStr)
		}
		ids = append(ids, id)
	}

	return ids, nil
}

func getApplicationsForExperiments(ctx context.Context, db db_api.NirvanaRW) ([]ApplicationResult, error) {
	query := `
		SELECT
			app.id as app_id
		FROM 
			application app
			LEFT JOIN agency ag ON app.agency_id = ag.id
			LEFT JOIN application_review ar ON ar.application_id = app.id
		WHERE
			app.state = 'AppStateUnderUWReview'
			AND ag.is_test_agency = FALSE
			AND ar.state != 'ApplicationReviewStateStale'
			AND app.id NOT IN (
				SELECT
					app_id
				FROM
					application_state_transitions
				WHERE
					to_state = 'AppStateQuoteGenerated'
			) 
			AND ar.current_status in ('InReview', 'BORRequested', 'ReviewNotStarted')
	`

	var results []ApplicationResult
	err := queries.Raw(query).Bind(ctx, db, &results)
	if err != nil {
		return nil, errors.Wrap(err, "failed to execute applications query")
	}

	return results, nil
}

func processApplicationAssignments(ctx context.Context, env assignEnv, appID string, activateIDs, deactivateIDs []uuid.UUID) error {
	// Get assignments that need to be enabled
	experimentsToEnable, err := getExperimentToEnable(ctx, env.Client, appID, activateIDs)
	if err != nil {
		return errors.Wrapf(err, "failed to get enablement assignments for app %s", appID)
	}
	log.Info(ctx, "Amount of experiments to enable for application",
		log.String("app_id", appID),
		log.Int("count", len(experimentsToEnable)))

	// Get assignments that need to be disabled
	experimentsToDisable, err := getExperimentToDisable(ctx, env.Client, appID, deactivateIDs)
	if err != nil {
		return errors.Wrapf(err, "failed to get disablement assignments for app %s", appID)
	}
	log.Info(ctx, "Amount of experiments to disable for application",
		log.String("app_id", appID),
		log.Int("count", len(experimentsToDisable)))

	if len(experimentsToEnable) != 0 {
		err = env.Client.AssignExperiments(ctx, appID, true, experimentsToEnable)
		if err != nil {
			return errors.Wrapf(err, "failed to assign experiments to enable for app %s", appID)
		}
	}

	if len(experimentsToDisable) != 0 {
		err = env.Client.AssignExperiments(ctx, appID, false, experimentsToDisable)
		if err != nil {
			return errors.Wrapf(err, "failed to assign experiments to disable for app %s", appID)
		}

		if err := deleteRiskFactorsForDisabledExperiments(ctx, env, appID, experimentsToDisable); err != nil {
			log.Error(ctx, "Failed to delete risk factors for disabled experiments",
				log.String("app_id", appID),
				log.Err(err))
			// Continue even if deletion fails
		}
	}

	return nil
}

// getEnablementAssignments returns assignment objects that should be enabled for the given application
func getExperimentToEnable(ctx context.Context, experimentsClient client.ExperimentsClient, appID string, activateExperimentIDs []uuid.UUID) ([]*models.Experiment, error) {
	experiments, err := experimentsClient.GetExperimentsByIDs(ctx, activateExperimentIDs)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get active experiments for app %s", appID)
	}

	experimentMap := make(map[uuid.UUID]*models.Experiment)
	for _, experiment := range experiments {
		experimentMap[experiment.Id] = experiment
	}

	// Get current assignments for this application
	currentAssignments, err := experimentsClient.GetLatestExperimentsAssignations(
		ctx,
		enums.DomainPricing,
		appID,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get current assignments for app %s", appID)
	}

	currentAssignmentMap := make(map[uuid.UUID]*models.ExperimentAssignment)
	for _, assignment := range currentAssignments {
		currentAssignmentMap[assignment.ExperimentId] = assignment
	}

	var experimentsToEnable []*models.Experiment
	for _, experimentID := range activateExperimentIDs {
		if currentAssignment, ok := currentAssignmentMap[experimentID]; !ok || (!currentAssignment.Enabled) {
			experiment, ok := experimentMap[experimentID]
			if !ok {
				return nil, errors.Wrapf(err, "experiment %s not found", experimentID)
			}

			experimentsToEnable = append(experimentsToEnable, experiment)
		}
	}

	return experimentsToEnable, nil
}

// getExperimentToDisable returns assignment objects that should be disabled for the given application
func getExperimentToDisable(ctx context.Context, experimentsClient client.ExperimentsClient, appID string, deactivateExperimentIDs []uuid.UUID) ([]*models.Experiment, error) {
	experiments, err := experimentsClient.GetExperimentsByIDs(ctx, deactivateExperimentIDs)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get active experiments for app %s", appID)
	}

	experimentMap := make(map[uuid.UUID]*models.Experiment)
	for _, experiment := range experiments {
		experimentMap[experiment.Id] = experiment
	}

	// Get current assignments for this application
	currentAssignments, err := experimentsClient.GetLatestExperimentsAssignations(
		ctx,
		enums.DomainPricing,
		appID,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get current assignments for app %s", appID)
	}

	currentAssignmentMap := make(map[uuid.UUID]*models.ExperimentAssignment)
	for _, assignment := range currentAssignments {
		currentAssignmentMap[assignment.ExperimentId] = assignment
	}

	var experimentsToDisable []*models.Experiment
	for _, experimentID := range deactivateExperimentIDs {
		if assignment, ok := currentAssignmentMap[experimentID]; ok && assignment.Enabled {
			experiment, ok := experimentMap[experimentID]
			if !ok {
				return nil, errors.Wrapf(err, "experiment %s not found", experimentID)
			}

			experimentsToDisable = append(experimentsToDisable, experiment)
		}
	}

	return experimentsToDisable, nil
}

// deleteRiskFactorsForDisabledExperiments deletes risk factors for experiments being disabled for a specific application
func deleteRiskFactorsForDisabledExperiments(ctx context.Context, env assignEnv, appID string, experimentsToDisable []*models.Experiment) error {
	// Get the latest pending review for this application
	review, err := env.ApplicationReviewWrapper.GetLatestPendingReview(ctx, appID)
	if err != nil {
		if errors.Is(err, uw.ErrAppReviewNotFound) {
			log.Info(ctx, "No pending review found for application, skipping risk factor deletion",
				log.String("app_id", appID))
			return nil
		}
		return errors.Wrapf(err, "failed to get latest pending review for app %s", appID)
	}

	// Delete risk factors for each experiment being disabled
	for _, experiment := range experimentsToDisable {
		// Get the risk factor label for this experiment
		riskFactorLabel, err := quoting_experiments.GetRiskFactorLabelForExperiment(experiment.Id)
		if err != nil {
			// If no mapping exists, this experiment doesn't have an associated risk factor
			log.Info(ctx, "No risk factor label found for experiment, skipping",
				log.String("experiment_id", experiment.Id.String()))
			continue
		}

		// Delete the risk factor from the review
		_, err = env.RiskFactorsClient.DeleteRiskFactorByLabel(ctx, &risk_factors.DeleteRiskFactorByLabelRequest{
			ReviewId:        review.Id,
			RiskFactorLabel: riskFactorLabel,
		})
		if err != nil {
			// Log error but continue processing other risk factors
			log.Error(ctx, "Failed to delete risk factor from review",
				log.String("app_id", appID),
				log.String("review_id", review.Id),
				log.String("experiment_id", experiment.Id.String()),
				log.String("risk_factor_label", riskFactorLabel.String()),
				log.Err(err))
		} else {
			log.Info(ctx, "Successfully deleted risk factor from review",
				log.String("app_id", appID),
				log.String("review_id", review.Id),
				log.String("experiment_id", experiment.Id.String()),
				log.String("risk_factor_label", riskFactorLabel.String()))
		}
	}

	return nil
}

func init() {
	AssignExperimentsCmd.Flags().StringVar(&activateExperimentIds, "activate-experiments", "", "Comma-separated list of experiment IDs to activate")
	AssignExperimentsCmd.Flags().StringVar(&deactivateExperimentIds, "deactivate-experiments", "", "Comma-separated list of experiment IDs to deactivate")
}
