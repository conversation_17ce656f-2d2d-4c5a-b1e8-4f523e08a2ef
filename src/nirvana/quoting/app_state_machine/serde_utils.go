package app_state_machine

import (
	"context"
	"database/sql"
	"strconv"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
	"github.com/volatiletech/null/v8"
	"go.opentelemetry.io/otel/attribute"

	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/math_utils"
	"nirvanatech.com/nirvana/common-go/metrics"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/common-go/tracing"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency_bd_mapping"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums/old_enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	db_sharing "nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/infra/constants"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_forms "nirvanatech.com/nirvana/openapi-specs/components/forms"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	"nirvanatech.com/nirvana/quoting/clearance"
	"nirvanatech.com/nirvana/quoting/clearance/enums"
	"nirvanatech.com/nirvana/quoting/quote_generator"
	"nirvanatech.com/nirvana/rating/models/models_release"
	"nirvanatech.com/nirvana/sharing"
	"nirvanatech.com/nirvana/telematics"
)

const invalidHandleId = "00000000-0000-0000-0000-000000000000"

func (a *appStateMachineHelper) bindAppDetailsFromDbToRest(
	ctx context.Context, appObj *application.Application,
) (*oapi_app.ApplicationDetail, error) {
	// Fill summary
	applicationSummary, err := a.bindApplicationSummaryFromDbToRest(ctx, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind app summary from db to REST")
	}
	// Fill indication form
	indicationForm := a.bindIndicationFormFromDb(appObj)
	// Fill additional information
	additionalInfoForm := a.bindAddlInfoFormFromDbToRest(appObj)
	// Selected option
	selInd, err := a.bindSelectedOptionFromDbToRest(ctx, *appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind selected option from db to REST")
	}

	var oapiTelematicsInfo *oapi_app.TelematicsInfo
	if appObj.AdditionalInsuredInfo != nil && appObj.AdditionalInsuredInfo.Name != "" {
		oapiTelematicsInfo, err = bindTelematicsInfoFromDbtoRest(ctx, a.deps.SharingWrapper, appObj)
		if err != nil {
			return nil, errors.Wrap(err, "failed to bind telematics info from db to REST")
		}
	}

	appDetail := oapi_app.ApplicationDetail{
		Summary:            *applicationSummary,
		IndicationForm:     indicationForm,
		AdditionalInfoForm: additionalInfoForm,
		SelectedIndication: selInd,
		TelematicsInfo:     oapiTelematicsInfo,
	}
	return &appDetail, nil
}

func bindTelematicsInfoFromDbtoRest(
	ctx context.Context,
	sharingWrapper db_sharing.DataWrapper,
	appObj *application.Application,
) (*oapi_app.TelematicsInfo, error) {
	var telematicsInfo oapi_app.TelematicsInfo
	if appObj.TelematicsInfo != nil && appObj.TelematicsInfo.TelematicsConsentRequestEmail != nil {
		telematicsInfo.LinkEmailedAt = appObj.TelematicsInfo.TelematicsConsentRequestEmail.CompletedAt.Ptr()
	}
	// if the name is an empty string then the telematics info struct
	// should be nil since we have not generated tsp conn link yet
	// hence other fields would be naturally empty
	if appObj.AdditionalInsuredInfo.Name != "" {
		telematicsInfo.Name = appObj.AdditionalInsuredInfo.Name

		appId, err := uuid.Parse(appObj.ID)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse applicationId: %s", appObj.ID)
		}

		consentURL, err := sharing.FetchApplicationConsentURL(
			ctx,
			sharingWrapper,
			appId,
			policy_enums.ProgramTypeFleet,
		)
		if err != nil {
			log.Error(
				ctx,
				"failed to fetch quoting consent url",
				log.Stringer("ProgramType", policy_enums.ProgramTypeFleet),
				log.Stringer("ApplicationId", appId),
			)
			return nil, errors.Wrap(err, "failed to fetch quoting consent url")
		}

		// we'll add the referer in the FE
		telematicsInfo.Link = consentURL
	}
	// email is an optional field, but we should guarantee that it's
	// a valid email
	if err := str_utils.IsEmailValid(appObj.AdditionalInsuredInfo.Email.String); err == nil {
		email := types.Email(appObj.AdditionalInsuredInfo.Email.String)
		telematicsInfo.Email = &email
	}

	return &telematicsInfo, nil
}

// This function binds the Application Summary information from the DB to
// the REST structs.
func (a *appStateMachineHelper) bindApplicationSummaryFromDbToRest(
	ctx context.Context,
	appObj *application.Application,
) (*oapi_app.ApplicationSummary, error) {
	ctx, span := tracing.Start(ctx, "bindApplicationSummaryFromDbToRest")
	defer span.End()
	span.SetAttributes(attribute.String("appId", appObj.ID))

	var producerName string
	if appObj.ProducerID != nil {
		producerId, err := uuid.Parse(*appObj.ProducerID)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to parse producer id %s", *appObj.ProducerID)
		}
		_, fetchProducerSpan := tracing.Start(ctx, "bindApplicationSummaryFromDbToRest.fetchProducerInfo")
		producer, err := a.deps.AuthWrapper.FetchUserInfo(ctx, producerId)
		fetchProducerSpan.End()
		if err != nil {
			return nil, errors.Newf("unable to fetch producer name for producer id %s", *appObj.ProducerID)
		}
		producerName = producer.FullName()
	}
	var renewalMetadata *oapi_app.RenewalMetadata
	if appObj.IsRenewal() {
		renewalMetadata = &oapi_app.RenewalMetadata{
			OriginalApplicationId: appObj.RenewalMetadata.OriginalApplicationId,
		}
		if appObj.IsRenewalV2() {
			completionMap := oapi_app.SectionCompletionMap{
				Coverages:             (*appObj.RenewalMetadata.SectionCompletionMap)[application.Coverages],
				Operations:            (*appObj.RenewalMetadata.SectionCompletionMap)[application.Operations],
				ClassesAndCommodities: (*appObj.RenewalMetadata.SectionCompletionMap)[application.ClassesAndCommodities],
				LossHistory:           (*appObj.RenewalMetadata.SectionCompletionMap)[application.LossHistory],
				AdditionalInformation: (*appObj.RenewalMetadata.SectionCompletionMap)[application.AdditionalInformation],
			}
			renewalMetadata.SectionCompletionMap = &completionMap
			renewalMetadata.IftaFiles = a.bindIftaFilesFromDBtoRest(appObj)
			renewalMetadata.BelongsToRedList = appObj.RenewalMetadata.BelongsToRedList
		}
	}
	var packageType *oapi_app.IndicationOptionTag
	if appObj.PackageType != nil {
		switch *appObj.PackageType {
		case app_enums.IndicationOptionTagBasic:
			basicIndicationTag := oapi_app.IndicationOptionTagBasic
			packageType = &basicIndicationTag
		case app_enums.IndicationOptionTagStandard:
			basicIndicationTag := oapi_app.IndicationOptionTagStandard
			packageType = &basicIndicationTag
		case app_enums.IndicationOptionTagComplete:
			basicIndicationTag := oapi_app.IndicationOptionTagComplete
			packageType = &basicIndicationTag
		}
	}

	_, fetchUnderwriterSpan := tracing.Start(ctx, "bindApplicationSummaryFromDbToRest.fetchUnderwriterInfo")
	underwriter, err := a.deps.AuthWrapper.FetchAuthzUser(ctx, appObj.UnderwriterID)
	fetchUnderwriterSpan.End()
	if err != nil {
		return nil, errors.Wrapf(err, "unable to fetch underwriter %s", appObj.UnderwriterID)
	}

	var agencyBDMapping *agency_bd_mapping.AgencyBDMapping
	if _, isTestAgency := constants.TestAgencies()[appObj.AgencyID]; !isTestAgency {
		_, fetchBDMappingSpan := tracing.Start(ctx, "bindApplicationSummaryFromDbToRest.fetchBDMapping")
		agencyBDMapping, err = a.deps.AgencyBDMapping.GetBDForAgency(ctx, appObj.AgencyID, policy_enums.ProgramTypeFleet)
		fetchBDMappingSpan.End()
		if err != nil {
			err = a.deps.MetricsClient.Inc(
				metrics.Join("asm.bd_mapping_not_found"), 1, 1,
			)
			if err != nil {
				log.Error(ctx, "unable to upload metrics for BD mapping", log.Err(err))
			}
			log.Warn(ctx, "failed to get BD for agency", log.AppID(appObj.ID), log.Err(err))
		}
	}

	var (
		bdName  *string
		bdEmail *types.Email
	)
	if agencyBDMapping != nil {
		_, fetchBDInfoSpan := tracing.Start(ctx, "bindApplicationSummaryFromDbToRest.fetchBDInfo")
		bdInfo, err := a.deps.AuthWrapper.FetchUserInfo(ctx, agencyBDMapping.UserID)
		fetchBDInfoSpan.End()
		if err != nil {
			return nil, errors.Wrapf(err, "unable to fetch BD %s", agencyBDMapping.UserID)
		}
		name := bdInfo.FullName()
		email := types.Email(bdInfo.Email)
		bdName = &name
		bdEmail = &email
	}

	telematicsDataStatus := oapi_app.TelematicsDataStatusFailed

	if appObj.TSPConnHandleId != nil {
		_, handleTelematicsInfoSpan := tracing.Start(ctx, "bindApplicationSummaryFromDbToRest.processTelematicsInfo")
		tspConnHandleID, err := uuid.Parse(*appObj.TSPConnHandleId)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to parse tsp conn handle id %s", *appObj.TSPConnHandleId)
		}
		if *appObj.TSPConnHandleId != invalidHandleId {
			connProps, err := a.deps.TspConnManager.GetConnectionProperties(ctx, tspConnHandleID)

			isPremierTsp := false
			if err != nil {
				err = a.deps.MetricsClient.Inc(
					metrics.Join("asm.connection_properties_not_found"), 1, 1,
				)
				if err != nil {
					log.Error(ctx, "unable to upload metrics for connection properties", log.Err(err))
				}
			} else {
				isPremierTsp = telematics.IsTelematicsIntegrationPremier(connProps, appObj.CreatedAt)
			}

			if isPremierTsp && appObj.TelematicsInfo != nil {
				switch appObj.TelematicsInfo.TelematicsDataStatus {
				case app_enums.TelemtaticsDataStatusWaitingForTSPConsent,
					app_enums.TelematicsDataStatusReconnectionRequired,
					app_enums.TelematicsDataStatusPanic,
					app_enums.TelematicsDataStatusConnectionFailed:
					telematicsDataStatus = oapi_app.TelematicsDataStatusFailed

				case app_enums.TelematicsDataStatusProcessingData,
					app_enums.TelematicsDataStatusDataAvailable,
					app_enums.TelematicsDataStatusNotEnoughData,
					app_enums.TelematicsDataNoHistoricalData:
					telematicsDataStatus = oapi_app.TelematicsDataStatusSuccess
				}
			} else {
				telematicsDataStatus = oapi_app.TelematicsDataStatusSuccess
			}
		}
		handleTelematicsInfoSpan.End()
	}

	_, checkClearanceStatusSpan := tracing.Start(ctx, "bindApplicationSummaryFromDbToRest.checkClearanceStatus")
	canClear, clearedApplication, err := clearance.CanClearApplication(
		ctx, a.deps.AppWrapper, appObj.CompanyInfo.DOTNumber, appObj.CoverageInfo.EffectiveDate, appObj,
	)
	checkClearanceStatusSpan.End()
	if err != nil {
		return nil, errors.Wrap(err, "failed to check if application can be cleared")
	}

	var clearanceStatus *oapi_app.PotentialClearanceStatus

	if !canClear {
		if clearedApplication == nil {
			log.Error(ctx, "failed to check if application can be cleared, cleared application is nil")
			err := errors.New("Failed to check if application can be cleared, cleared application is nil")
			return nil, err
		}

		applicationClearanceStatus := clearance.GenerateClearanceOapiStatus(clearedApplication, appObj.AgencyID)
		clearanceStatus = &applicationClearanceStatus
	} else {
		if appObj.ClearanceStatus != nil && *appObj.ClearanceStatus == enums.ApplicationRequiresClearance {
			// Renewal use case, we don't have any cleared applications
			clearanceStatus = pointer_utils.ToPointer(oapi_app.ClearedApplicationExists)
		}
	}

	hidePrice := !constants.TestAgencies()[appObj.AgencyID] &&
		(appObj.ClearanceStatus != nil && *appObj.ClearanceStatus == enums.ApplicationCleared)

	return &oapi_app.ApplicationSummary{
		ApplicationID:        appObj.ID,
		DotNumber:            appObj.CompanyInfo.DOTNumber,
		ShortID:              string(appObj.ShortID),
		CompanyName:          appObj.CompanyInfo.Name,
		State:                oapi_app.ApplicationState(appObj.State.String()),
		UnderwriterName:      underwriter.FullName(),
		UnderwriterEmail:     types.Email(underwriter.Email),
		BdName:               bdName,
		BdEmail:              bdEmail,
		ProducerName:         producerName,
		EffectiveDate:        types.Date{Time: appObj.CoverageInfo.EffectiveDate},
		CreatedAt:            appObj.CreatedAt,
		UpdatedAt:            appObj.UpdatedAt,
		ProducerID:           appObj.ProducerID,
		AgencyID:             appObj.AgencyID.String(),
		CreatedBy:            appObj.CreatedBy,
		RenewalMetadata:      renewalMetadata,
		PackageType:          packageType,
		TelematicsDataStatus: &telematicsDataStatus,
		ClearanceStatus:      clearanceStatus,
		HidePrice:            pointer_utils.ToPointer(hidePrice),
		MtcVersion:           a.determineMTCVersionFromApplication(appObj),
	}, nil
}

func (a *appStateMachineHelper) bindFileMetadataFromDbToRest(
	metadata application.FileMetadata,
) oapi_common.FileMetadata {
	return oapi_common.FileMetadata{
		Name:   metadata.Name,
		Handle: pointer_utils.UUIDStringOrNil(metadata.Handle),
	}
}

func (a *appStateMachineHelper) bindFlatfileMetadataFromDbToRest(
	metadata *application.FlatfileMetadata,
) *oapi_common.FlatfileMetadata {
	if metadata == nil {
		return nil
	}

	return &oapi_common.FlatfileMetadata{
		FlatfileHandle: metadata.FlatfileHandle,
		FileMetadata: oapi_common.FileMetadata{
			Handle: pointer_utils.UUIDStringOrNil(metadata.FileMetadata.Handle),
			Name:   metadata.FileMetadata.Name,
		},
	}
}

func (a *appStateMachineHelper) bindImplerMetadataFromDbToRest(
	metadata *application.ImplerMetadata,
) *oapi_common.ImplerMetadata {
	if metadata == nil {
		return nil
	}

	return &oapi_common.ImplerMetadata{
		ImplerHandle: metadata.ImplerHandle,
		FileMetadata: oapi_common.FileMetadata{
			Handle: pointer_utils.UUIDStringOrNil(metadata.FileMetadata.Handle),
			Name:   metadata.FileMetadata.Name,
		},
	}
}

// This function binds the Operations data from the DB to the REST structs.
// RadiusOfOperation and CoveragesRequired have to be manually parsed to
// map enums.
func (a *appStateMachineHelper) bindOperationsFromDBToRest(
	appObj *application.Application,
) *oapi_app.OperationsForm {
	operationsForm := &oapi_app.OperationsForm{}
	operationsForm.ProducerId = *appObj.ProducerID
	operationsForm.NumberOfPowerUnits = appObj.CompanyInfo.NumberOfPowerUnits
	operationsForm.ProjectedMileage = appObj.CompanyInfo.ProjectedMileage
	operationsForm.RadiusOfOperation = make(
		[]oapi_app.MileageRadiusRecord, 0)
	operationsForm.TerminalLocations = nil
	if appObj.CompanyInfo.TerminalLocations != nil {
		var terminalLocations []oapi_app.TerminalLocation

		for _, v := range *(appObj.CompanyInfo.TerminalLocations) {
			tLoc := oapi_app.TerminalLocation{
				AddressLineOne: v.AddressLineOne,
				AddressLineTwo: v.AddressLineTwo,
				IsGated:        v.IsGated,
				IsGuarded:      v.IsGuarded,
				TypeOfTerminal: oapi_app.TypeOfTerminal(v.TypeOfTerminal.String()),
				UsState:        oapi_common.USState(v.UsState.ToCode()),
				ZipCode:        v.ZipCodeString,
			}

			if v.CargoTerminalSchedule != nil {
				tLoc.CargoTerminalSchedule = &oapi_common.CargoTerminalSchedule{
					ConstructionClass:      pointer_utils.ToPointer(oapi_common.CargoTerminalScheduleConstructionClass(v.CargoTerminalSchedule.ConstructionClass.String())),
					PrivateFireProtection:  oapi_common.CargoTerminalSchedulePrivateFireProtection(v.CargoTerminalSchedule.PrivateFireProtection.String()),
					PrivateTheftProtection: oapi_common.CargoTerminalSchedulePrivateTheftProtection(v.CargoTerminalSchedule.PrivateTheftProtection.String()),
				}
			}

			terminalLocations = append(terminalLocations, tLoc)
		}
		operationsForm.TerminalLocations = &terminalLocations
	}
	for _, v := range appObj.CompanyInfo.RadiusOfOperation {
		milRadRec := oapi_app.MileageRadiusRecord{
			PercentageOfFleet: v.PercentageOfFleet,
			MileageRadiusBucket: oapi_app.MileageRadiusBucket(
				v.RadiusBucket.String()),
		}
		operationsForm.RadiusOfOperation = append(
			operationsForm.RadiusOfOperation, milRadRec)
	}
	// EquipmentList
	info := make([]oapi_app.EquipmentListRecord, 0)
	for _, vehicle := range appObj.EquipmentInfo.EquipmentList.Info {
		info = append(info, oapi_app.EquipmentListRecord{
			StatedValue: vehicle.StatedValue,
			Vin:         vehicle.VIN,
		})
	}
	operationsForm.EquipmentList = oapi_app.EquipmentList{
		Info: info,
		FlatfileMetadata: a.bindFlatfileMetadataFromDbToRest(
			appObj.EquipmentInfo.EquipmentList.FlatfileMetadata),
		ImplerMetadata: a.bindImplerMetadataFromDbToRest(
			appObj.EquipmentInfo.EquipmentList.ImplerMetadata),
	}
	// CoveragesRequired & AncillaryCoveragesRequired
	if appObj.CoverageInfo != nil && len(appObj.CoverageInfo.Coverages) != 0 {
		covRequired := make([]oapi_app.CoverageRecord, 0)
		ancillaryCovsRequired := make([]oapi_app.CoverageRecord, 0)
		for _, cov := range appObj.CoverageInfo.Coverages {
			if cov.CoverageType.IsPrimaryCoverage() {
				covRequired = append(
					covRequired,
					oapi_app.CoverageRecord{
						CoverageType: oapi_common.CoverageType(cov.CoverageType.String()),
						Deductible:   cov.Deductible,
						Limit:        cov.Limit,
					})
			} else {
				ancillaryCovsRequired = append(ancillaryCovsRequired,
					oapi_app.CoverageRecord{
						CoverageType: oapi_common.CoverageType(cov.CoverageType.String()),
						Deductible:   cov.Deductible,
						Limit:        cov.Limit,
					})
			}
		}
		operationsForm.CoveragesRequired = &covRequired
		operationsForm.AncillaryCoveragesRequired = &ancillaryCovsRequired
	}

	operationsForm.CoveragesWithCombinedDeductibles = a.bindCombinedCoveragesFromDBToRest(
		appObj.CoverageInfo.CoveragesWithCombinedDeductibles,
	)

	// Bind retailer info from DB to REST
	if appObj.RetailerInfo != nil {
		operationsForm.RetailerInfo = &oapi_app.RetailerInfo{
			FirstName: appObj.RetailerInfo.FirstName,
			LastName:  appObj.RetailerInfo.LastName,
			Agency:    appObj.RetailerInfo.Agency,
		}

		// Handle email conversion from database string to REST types.Email
		if appObj.RetailerInfo.Email != nil {
			email := types.Email(*appObj.RetailerInfo.Email)
			operationsForm.RetailerInfo.Email = &email
		}
	}

	return operationsForm
}

// This function binds the Classes and Commodities data from the DB to the REST
// structs. OperatingClassDistribution, PrimaryOperatingClass, and
// PrimaryCommodity have to be manually parsed to map enums.
func (a *appStateMachineHelper) bindClsAndComFromDBToRest(
	appObj *application.Application,
) *oapi_app.ClassesAndCommoditiesForm {
	clsAndComForm := &oapi_app.ClassesAndCommoditiesForm{}

	// Operating Class Distribution
	for _, opClsDis := range appObj.EquipmentInfo.OperatingClassDistribution {
		clsAndComForm.OperatingClassDistribution = append(
			clsAndComForm.OperatingClassDistribution,
			oapi_app.OperatingClassDistributionRecord{
				OperatingClass:    oapi_common.OperatingClass(opClsDis.Class.String()),
				PercentageOfFleet: opClsDis.PercentageOfFleet,
			})
	}

	// Primary Operating Class
	clsAndComForm.PrimaryOperatingClass = oapi_common.OperatingClass(
		appObj.EquipmentInfo.PrimaryOperatingClass.String())

	// MTC Commodities
	if slice_utils.Contains[app_enums.Coverage](
		appObj.CoverageInfo.GetCoverageTypes(), app_enums.CoverageMotorTruckCargo,
	) {
		comDistribution := a.bindCommodityDistributionFromDBToRest(appObj.EquipmentInfo.CommodityDistribution)
		if comDistribution == nil {
			return clsAndComForm
		}

		clsAndComForm.CommodityDistribution = comDistribution
	}

	// If Pinned to Primary Category based Rating
	if appObj.ModelPinConfig.Application.Flags.UsePrimaryCategory {
		if appObj.EquipmentInfo.PrimaryCategory != nil {
			primCat := a.bindPrimaryCategoryFromDBToRest(*(appObj.EquipmentInfo.PrimaryCategory))
			clsAndComForm.PrimaryCategory = primCat
			clsAndComForm.PrimaryCommodity = nil
		}
	} else {
		if appObj.EquipmentInfo.PrimaryCommodity != nil {
			primCom := a.bindPrimaryCommodityFromDBToRest(*(appObj.EquipmentInfo.PrimaryCommodity))
			clsAndComForm.PrimaryCommodity = primCom
			clsAndComForm.PrimaryCategory = nil
		}
	}

	return clsAndComForm
}

func (a *appStateMachineHelper) bindCommodityDistributionFromDBToRest(
	comDistribution *application.CommodityDistribution,
) *oapi_app.CommodityDistribution {
	if comDistribution == nil ||
		comDistribution.Commodities == nil {
		return nil
	}

	var commodities []oapi_app.WeightedCommodityRecord
	additionalCommodities := comDistribution.AdditionalCommodities

	for _, weightedCommodityRecord := range comDistribution.Commodities {
		categoryLabel := app_logic.CategoryLabelMap[weightedCommodityRecord.Category]
		catType := oapi_common.CommodityCategoryEnum(weightedCommodityRecord.Category.String())
		commodities = append(commodities, oapi_app.WeightedCommodityRecord{
			Category: oapi_common.CommodityCategory{
				Type:  catType,
				Label: &categoryLabel,
			},
			Commodity: oapi_common.CommodityHauled{
				Type:  nil,
				Label: weightedCommodityRecord.Commodity.Label,
			},
			AvgDollarValueHauled: weightedCommodityRecord.AvgDollarValueHauled,
			MaxDollarValueHauled: weightedCommodityRecord.MaxDollarValueHauled,
			PercentageOfHauls:    weightedCommodityRecord.PercentageOfHauls,
		})
	}
	return &oapi_app.CommodityDistribution{
		AdditionalCommodities: &oapi_app.AdditionalCommoditiyRecord{
			Commodities:       additionalCommodities.Commodities,
			PercentageOfHauls: additionalCommodities.PercentageOfHauls,
		},
		Commodities: commodities,
	}
}

func (a *appStateMachineHelper) bindPrimaryCommodityFromDBToRest(
	primCom old_enums.PrimaryCommodityHauled,
) *oapi_common.PrimaryCommodityHauled {
	comType := oapi_common.PrimaryCommodityHauledEnum(primCom.String())
	label := app_logic.CommodityLabelMapV1[primCom]
	return &oapi_common.PrimaryCommodityHauled{
		Type:  comType,
		Label: &label,
	}
}

func (a *appStateMachineHelper) bindPrimaryCategoryFromDBToRest(
	primCat app_enums.CommodityCategory,
) *oapi_common.CommodityCategory {
	catType := oapi_common.CommodityCategoryEnum(primCat.String())
	label := app_logic.CategoryLabelMap[primCat]
	return &oapi_common.CommodityCategory{
		Type:  catType,
		Label: &label,
	}
}

// This function binds the Loss Run Summary data from the DB to the REST
// structs. LossRunSummaryForm itself has to be manually parsed to map enums.
func (a *appStateMachineHelper) bindLossRunSummaryFromDBToRest(
	appObj *application.Application,
) *oapi_app.LossRunSummaryForm {
	lossRunSummaryForm := oapi_app.LossRunSummaryForm{}
	for _, summaryPerCovDB := range appObj.LossInfo.LossRunSummary {
		summaryPerCovRest := make([]oapi_app.LossRunSummaryRecord, 0)
		for _, recordDB := range summaryPerCovDB.Summary {
			summaryPerCovRest = append(summaryPerCovRest, oapi_app.LossRunSummaryRecord{
				LossIncurred:          recordDB.LossIncurred,
				NumberOfClaims:        recordDB.NumberOfClaims,
				NumberOfPowerUnits:    recordDB.NumberOfPowerUnits,
				PolicyPeriodEndDate:   types.Date{Time: recordDB.PolicyPeriodEndDate},
				PolicyPeriodStartDate: types.Date{Time: recordDB.PolicyPeriodStartDate},
				IsNirvanaPeriod:       recordDB.IsNirvanaPeriod,
			})
		}
		lossRunSummaryForm = append(lossRunSummaryForm,
			oapi_app.LossRunSummaryPerCoverage{
				CoverageType:   oapi_common.CoverageType(summaryPerCovDB.CoverageType.String()),
				LossRunSummary: summaryPerCovRest,
			},
		)
	}
	return &lossRunSummaryForm
}

// This function binds all the indication form data for an application from the
// DB to the REST structs, by first binding Operations, then Classes and
// Commodities, and then Loss Run Summary. Each group of data if first checked
// within the DB and then bound, returning empty pointers when data is not
// available.
func (a *appStateMachineHelper) bindIndicationFormFromDb(appObj *application.Application) *oapi_app.IndicationForm {
	indicationForm := &oapi_app.IndicationForm{}
	// First check / bind the operations data
	if !a.isOperationsAvailableInDB(appObj) {
		return nil
	}
	indicationForm.OperationsForm = a.bindOperationsFromDBToRest(appObj)
	// Second check / bind the Classes and Commodities data
	if !a.isClsAndComAvailableInDB(appObj) {
		return indicationForm
	}
	indicationForm.ClassesAndCommoditiesForm = a.bindClsAndComFromDBToRest(appObj)
	// Third check / bind the Loss Run Summary data
	if !a.isLossRunSummaryAvailableInDB(appObj) {
		return indicationForm
	}
	indicationForm.LossRunSummaryForm = a.bindLossRunSummaryFromDBToRest(appObj)
	return indicationForm
}

// bindSelectedOptionFromDbToRest gets the indication options from the db and
// binds them to REST format.
func (a *appStateMachineHelper) bindSelectedOptionFromDbToRest(ctx context.Context,
	appObj application.Application,
) (*oapi_app.IndicationOption, error) {
	if appObj.IndicationSubmissionID == nil {
		return nil, nil
	}
	subObj, err := a.deps.AppWrapper.GetSubmissionById(ctx, *appObj.IndicationSubmissionID)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		return nil, nil
	case err != nil:
		return nil, errors.Wrap(err, "unable to load last submission")
	case subObj.SelectedIndicationID == "" ||
		subObj.SelectedIndicationID == uuid.Nil.String():
		return nil, nil
	}
	opt, err := a.deps.AppWrapper.GetIndOptionById(ctx,
		subObj.SelectedIndicationID)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load indication option")
	}
	optRest := a.bindIndOptionFromDbToRest(*opt)
	return &optRest, nil
}

// bindIndOptionFromDbToRest is a helper function used to bind one
// indication option from the db to a REST format.
func (a *appStateMachineHelper) bindIndOptionFromDbToRest(
	optionDb application.IndicationOption,
) oapi_app.IndicationOption {
	covRest := make([]oapi_app.CoverageRecord, 0)
	for _, cov := range optionDb.Coverages {
		covRest = append(covRest, a.bindCoverageFromDbToRest(cov))
	}

	return oapi_app.IndicationOption{
		Coverages:       a.bindCoveragesFromDbToRest(optionDb.Coverages),
		Id:              optionDb.ID,
		IsRecommended:   optionDb.IsRecommended,
		OptionTag:       oapi_app.IndicationOptionTag(optionDb.OptionTag.String()),
		TotalPremium:    optionDb.TotalPremium,
		TotalMiles:      optionDb.TotalMiles,
		TotalPowerUnits: optionDb.TotalPowerUnits,
		TIV:             optionDb.TIV,
		CreatedAt:       pointer_utils.ToPointer(types.Date{Time: optionDb.CreatedAt}),
	}
}

// bindSymbolsAndDefinitionsFromDbToRest binds symbols and definition from db
// to REST format.
func (a *appStateMachineHelper) bindSymbolsAndDefinitionsFromDbToRest(
	symbsAndDefs *[]application.SymbolAndDefinition,
) *[]oapi_app.SymbolAndDefinition {
	if symbsAndDefs == nil {
		return nil
	}
	var retval []oapi_app.SymbolAndDefinition
	for _, s := range *symbsAndDefs {
		retval = append(retval, oapi_app.SymbolAndDefinition{
			Definition: s.Definition,
			Symbol:     s.Symbol,
		})
	}
	return &retval
}

// bindCoverageFromDbToRest is a helper func to bind coverages from Db to REST.
func (a *appStateMachineHelper) bindCoveragesFromDbToRest(covs []application.CoverageDetails) []oapi_app.CoverageRecord {
	var retval []oapi_app.CoverageRecord
	for _, cov := range covs {
		retval = append(retval, a.bindCoverageFromDbToRest(cov))
	}
	return retval
}

// bindCoverageFromDbToRest is a helper func to bind a single coverage from Db
// to REST.
func (a *appStateMachineHelper) bindCoverageFromDbToRest(cov application.CoverageDetails) oapi_app.CoverageRecord {
	return oapi_app.CoverageRecord{
		TIVPercentage:          math_utils.RoundFloatPtr(cov.TIVPercentage, 3),
		CoverageType:           oapi_common.CoverageType(cov.CoverageType.String()),
		Premium:                cov.Premium,
		PremiumPerUnit:         cov.PremiumPerUnit,
		Deductible:             cov.Deductible,
		Limit:                  cov.Limit,
		SymbolsAndDefinitions:  a.bindSymbolsAndDefinitionsFromDbToRest(cov.SymbolsAndDefinitions),
		PremiumPerHundredMiles: math_utils.RoundFloatPtr(cov.PremiumPerHundredMiles, 3),
	}
}

// This function binds the Operations data from the REST structs to the DB.
// RadiusOfOperation and CoveragesRequired have to be manually parsed to
// map enums. Additionally, we first check that the OperationsForm pointer
// contains information; if it doesn't, we wipe the Operations data from the
// the DB to preserve the PUT logic.
func (a *appStateMachineHelper) bindOperationsFromRestToDB(
	operationsForm *oapi_app.OperationsForm, appObj *application.Application,
) (*application.Application, error) {
	appID := appObj.ID
	// Check if operationForm contains data, wipe the DB if it doesn't
	if operationsForm == nil {
		appObj.ProducerID = nil
		appObj.CompanyInfo.NumberOfPowerUnits = 0
		appObj.CompanyInfo.ProjectedMileage = 0
		appObj.CompanyInfo.TerminalLocations = nil
		appObj.CompanyInfo.RadiusOfOperation = make([]*application.MileageRadiusRecord, 0)
		appObj.EquipmentInfo.EquipmentList = application.EquipmentList{}
		appObj.CoverageInfo.Coverages = make([]application.CoverageDetails, 0)
		appObj.CoverageInfo.CoveragesWithCombinedDeductibles = nil
		return appObj, nil
	}
	// Bind the data
	appObj.ProducerID = &operationsForm.ProducerId
	appObj.CompanyInfo.NumberOfPowerUnits = operationsForm.NumberOfPowerUnits
	appObj.CompanyInfo.ProjectedMileage = operationsForm.ProjectedMileage
	appObj.CompanyInfo.TerminalLocations = nil

	// Bind coverages
	appObj, err := a.bindCoveragesFromRestToDB(operationsForm.CoveragesRequired, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind coverages from rest to db")
	}

	// bind terminal locations
	appObj, err = a.bindTerminalLocationsFromRestToDB(operationsForm.TerminalLocations, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind terminal locations for appId %s", appID)
	}

	// RadiusOfOperation
	var radiusOfOperation []*application.MileageRadiusRecord
	for _, v := range operationsForm.RadiusOfOperation {
		radBuckEnum, err := app_enums.MileageRadiusBucketString(string(v.MileageRadiusBucket))
		if err != nil {
			return nil, errors.Wrapf(err, "unsupported radius bucket %s", v.MileageRadiusBucket)
		}
		milRadRec := application.MileageRadiusRecord{
			PercentageOfFleet: v.PercentageOfFleet,
			RadiusBucket:      radBuckEnum,
		}
		radiusOfOperation = append(radiusOfOperation, &milRadRec)
	}
	appObj.CompanyInfo.RadiusOfOperation = radiusOfOperation

	// EquipmentList
	appObj, err = a.bindEquipmentListFromRestToDB(&operationsForm.EquipmentList, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind equipment list from rest to db")
	}
	// Combined Coverages
	// NOTE: Combined coverages should mimic each other's deductibles
	// So if APD & MTC are combined, if we change deductible of MTC, APD should also change
	// and vice versa
	// For now, adding a check inside `bindCombinedCoveragesFromRestToDB` to verify that this rule is
	// followed or not. i.e. Making it the client's responsibility to send the correct deductibles
	if operationsForm.CoveragesWithCombinedDeductibles != nil {
		coveragesWithCombinedDeductibles, err := a.bindCombinedCoveragesFromRestToDB(
			operationsForm.CoveragesWithCombinedDeductibles,
			appObj.CoverageInfo.Coverages,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to bind combined coverages from rest to db")
		}
		appObj.CoverageInfo.CoveragesWithCombinedDeductibles = coveragesWithCombinedDeductibles
	} else {
		appObj.CoverageInfo.CoveragesWithCombinedDeductibles = nil
	}

	if operationsForm.AncillaryCoveragesRequired != nil {
		// Bind Ancillary Coverages
		appObj, err = a.bindAncillaryCoveragesRequiredFromRestToDB(*operationsForm.AncillaryCoveragesRequired, appObj)
		if err != nil {
			return nil, errors.Wrap(err, "unable to bind coverages from rest to db")
		}
	}

	// Bind retailer info from REST to DB
	if operationsForm.RetailerInfo != nil {
		appObj.RetailerInfo = &application.RetailerInfo{
			FirstName: operationsForm.RetailerInfo.FirstName,
			LastName:  operationsForm.RetailerInfo.LastName,
			Agency:    operationsForm.RetailerInfo.Agency,
		}

		// Handle email conversion from REST types.Email to database string
		if operationsForm.RetailerInfo.Email != nil {
			emailStr := string(*operationsForm.RetailerInfo.Email)
			appObj.RetailerInfo.Email = &emailStr
		}
	}

	return appObj, nil
}

// bindAncillaryCoveragesRequiredFromRestToDB processes ancillary coverages from the REST request
// and updates the application's coverage information. For each ancillary coverage:
// - If the coverage type already exists in the application, it updates the existing coverage
// - If the coverage type doesn't exist, it adds it as a new coverage
func (a *appStateMachineHelper) bindAncillaryCoveragesRequiredFromRestToDB(
	formAncillaryCoverages []oapi_app.CoverageRecord, appObj *application.Application,
) (*application.Application, error) {
	// Convert existing coverages to a map for easier lookup
	covMap := make(map[app_enums.Coverage]int)
	for i, cov := range appObj.CoverageInfo.Coverages {
		covMap[cov.CoverageType] = i
	}

	// Process each ancillary coverage
	for _, ancCov := range formAncillaryCoverages {
		ancCovEnum, err := app_enums.CoverageString(string(ancCov.CoverageType))
		if err != nil {
			return nil, errors.Wrapf(err, "unable to parse coverage type %s", ancCov.CoverageType)
		}

		newCov := application.CoverageDetails{
			CoverageType: ancCovEnum,
			Deductible:   ancCov.Deductible,
			Limit:        ancCov.Limit,
		}

		if idx, exists := covMap[ancCovEnum]; exists {
			// Update existing coverage
			appObj.CoverageInfo.Coverages[idx] = newCov
		} else {
			// Add new coverage
			appObj.CoverageInfo.Coverages = append(appObj.CoverageInfo.Coverages, newCov)
		}
	}

	// Apply all coverage rules (towing, defaults etc)
	appObj.CoverageInfo.Coverages = a.applyCoverageRules(appObj.CoverageInfo)

	return appObj, nil
}

// This function binds the Classes and Commodities data from the REST structs
// to the DB. OperatingClassDistribution has to be manually parsed to
// map enums, and we also verify if the operation held by PrimaryOperation
// and PrimaryCommodity is supported. Additionally, we first check that the
// clsAndComForm pointer contains information; if it doesn't, we wipe the
// Classes and Commodities data from the the DB to preserve the PUT logic.
func (a *appStateMachineHelper) bindClsAndComFromRestToDB(
	clsAndComForm *oapi_app.ClassesAndCommoditiesForm, appObj *application.Application,
) (*application.Application, error) {
	appId := appObj.ID
	// Check if clsAndComForm contains data, wipe the DB if it doesn't
	if clsAndComForm == nil {
		appObj.EquipmentInfo.OperatingClassDistribution = make(
			[]application.OperatingClassDistributionRecord, 0)
		appObj.EquipmentInfo.PrimaryOperatingClass = nil
		appObj.EquipmentInfo.PrimaryCommodity = nil
		appObj.EquipmentInfo.PrimaryCategory = nil
		appObj.EquipmentInfo.CommodityDistribution = nil
		return appObj, nil
	}
	// Bind the data
	// OperatingClass
	appObj, err := a.bindOperatingClassDistributionFromRestToDB(clsAndComForm.OperatingClassDistribution, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind operating class distribution for appId %s", appId)
	}
	// Primary Operating Class
	appObj, err = a.bindPrimaryOperationgClassFromRestToDB(clsAndComForm.PrimaryOperatingClass, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind primary operating class for appId %s", appId)
	}
	var primCategoryPtr *app_enums.CommodityCategory = nil
	var primCommodityPtr *old_enums.PrimaryCommodityHauled = nil
	var commoditiesPtr *application.CommodityDistribution = nil
	// This is applicable only when MTC coverage is selected
	if appObj.CoverageInfo.ContainsCoverage(app_enums.CoverageMotorTruckCargo) {
		commoditiesPtr, primCategoryPtr, err = a.bindCommodityDistributionFromRestToDB(
			clsAndComForm.CommodityDistribution,
		)
		if err != nil {
			return nil, errors.Wrap(err, "couldn't bind commodity distribution")
		}
	} else {
		// If it is pinned to Primary Category based rating
		if appObj.ModelPinConfig.Application.Flags.UsePrimaryCategory {
			// If non-MTC application & pinned to Primary Category based rating,
			// they should pass primary category in the request for it to be valid

			if clsAndComForm.PrimaryCategory != nil {
				primCategoryPtr, err = a.bindPrimaryCategoryFromRestToDB(clsAndComForm.PrimaryCategory.Type)
				if err != nil {
					return nil, errors.Wrap(err, "couldn't bind primary category")
				}
			}
		} else {
			primCommodityPtr, err = a.bindPrimaryCommodityFromRestToDB(
				clsAndComForm.PrimaryOperatingClass,
				clsAndComForm.PrimaryCommodity.Type,
			)
			if err != nil {
				return nil, errors.Wrap(err, "couldn't bind primary commodity")
			}
		}
	}

	appObj.EquipmentInfo.CommodityDistribution = commoditiesPtr
	appObj.EquipmentInfo.PrimaryCommodity = primCommodityPtr
	appObj.EquipmentInfo.PrimaryCategory = primCategoryPtr

	return appObj, nil
}

func (a *appStateMachineHelper) bindCommodityDistributionFromRestToDB(
	comDistribution *oapi_app.CommodityDistribution,
) (*application.CommodityDistribution, *app_enums.CommodityCategory, error) {
	if comDistribution == nil ||
		comDistribution.Commodities == nil {
		return nil, nil, errors.New("can't have commodity distribution be null in case of MTC")
	}
	var weightedCommoditiesForDB application.CommodityDistribution
	commodities := make([]application.WeightedCommodityRecord, 0)
	for _, comDis := range comDistribution.Commodities {
		category, err := app_enums.CommodityCategoryString(string(comDis.Category.Type))
		if err != nil {
			return nil, nil, errors.Wrapf(err, "unsupported category %s", string(comDis.Category.Type))
		}

		commodities = append(commodities, application.WeightedCommodityRecord{
			Category: category,
			Commodity: application.Commodity{
				Type:  nil,
				Label: comDis.Commodity.Label,
			},
			AvgDollarValueHauled: comDis.AvgDollarValueHauled,
			MaxDollarValueHauled: comDis.MaxDollarValueHauled,
			PercentageOfHauls:    comDis.PercentageOfHauls,
		})
	}
	weightedCommoditiesForDB.Commodities = commodities
	weightedCommoditiesForDB.AdditionalCommodities = &application.AdditionalCommoditiyRecord{
		Commodities:       comDistribution.AdditionalCommodities.Commodities,
		PercentageOfHauls: comDistribution.AdditionalCommodities.PercentageOfHauls,
	}

	var primaryCategory *app_enums.CommodityCategory
	// Case when commodities array is empty is also valid
	// While allowing the agent to go back and select MTC, FE
	// will send commodities as empty array
	if len(commodities) != 0 {
		var err error
		primaryCategory, err = fetchPrimaryCategoryFromDistribution(commodities)
		if err != nil {
			return nil, nil, errors.Wrap(err, "couldn't fetch primary category from the distribution")
		}
	}

	return &weightedCommoditiesForDB, primaryCategory, nil
}

func (a *appStateMachineHelper) bindPrimaryCategoryFromRestToDB(
	oapiCat oapi_common.CommodityCategoryEnum,
) (*app_enums.CommodityCategory, error) {
	primCat, err := app_enums.CommodityCategoryString(string(oapiCat))
	if err != nil {
		return nil, errors.Wrapf(err, "unsupported primary category %s",
			oapiCat)
	}
	return &primCat, nil
}

func (a *appStateMachineHelper) bindPrimaryCommodityFromRestToDB(
	primOpClassOapi oapi_common.OperatingClass,
	primComOapi oapi_common.PrimaryCommodityHauledEnum,
) (*old_enums.PrimaryCommodityHauled, error) {
	primOp, err := app_enums.OperatingClassString(string(primOpClassOapi))
	if err != nil {
		return nil, errors.Wrapf(err, "unexpected operation class %s", primOpClassOapi)
	}
	primCom, err := old_enums.PrimaryCommodityHauledString(string(primComOapi))
	if err != nil {
		return nil, errors.Wrapf(err, "unexpected primary commodity %s", primComOapi)
	}
	if err = a.isOperationSupported(primOp, primCom); err != nil {
		return nil, errors.Wrapf(err,
			"this combination of primary class %v & primary commodity %s is not supported",
			primOp.String(), primCom.String())
	}
	return &primCom, nil
}

// This function binds the LossRunSummary data from the REST structs to the DB.
// LossRunSummaryForm itself has to be manually parsed to map enums.
// Additionally, we first check that the LossRunSummaryForm pointer contains
// information; if it doesn't, we wipe the LossRunSummaryForm data from the
// DB to preserve the PUT logic.
func (a *appStateMachineHelper) bindLossRunSummaryFromRestToDB(
	lossRunSummaryForm *oapi_app.LossRunSummaryForm,
	appObj *application.Application,
) (*application.Application, error) {
	// Check if lossRunSummaryForm contains data, wipe the DB if it doesn't
	if lossRunSummaryForm == nil {
		appObj.LossInfo.LossRunSummary = make(
			[]application.LossRunSummaryPerCoverage, 0)
		return appObj, nil
	}
	// Bind the data
	// Loss Runs Summary
	summaryPerCovDB := make([]application.LossRunSummaryPerCoverage, 0)
	for _, summaryPerCovRest := range *lossRunSummaryForm {
		summaryDB := make([]application.LossRunSummaryRecord, 0)
		for _, sum := range summaryPerCovRest.LossRunSummary {
			startDate, err := time.Parse("2006-01-02", sum.PolicyPeriodStartDate.Format("2006-01-02"))
			if err != nil {
				return nil, errors.Wrapf(err, "wrong start date %s on Loss Summary", sum.PolicyPeriodStartDate)
			}
			endDate, err := time.Parse("2006-01-02", sum.PolicyPeriodEndDate.Format("2006-01-02"))
			if err != nil {
				return nil, errors.Wrapf(err, "wrong end date %s on Loss Summary %s", sum.PolicyPeriodEndDate)
			}
			summaryDB = append(summaryDB, application.LossRunSummaryRecord{
				PolicyPeriodStartDate: startDate,
				PolicyPeriodEndDate:   endDate,
				NumberOfPowerUnits:    sum.NumberOfPowerUnits,
				LossIncurred:          sum.LossIncurred,
				NumberOfClaims:        sum.NumberOfClaims,
				IsNirvanaPeriod:       sum.IsNirvanaPeriod,
			})
		}
		covType, err := app_enums.CoverageString(string(summaryPerCovRest.CoverageType))
		if err != nil {
			return nil, errors.Wrapf(err, "wrong coverage type %s on Loss Summary",
				string(summaryPerCovRest.CoverageType))
		}
		summaryPerCovDB = append(summaryPerCovDB, application.LossRunSummaryPerCoverage{
			CoverageType: covType,
			Summary:      summaryDB,
		})
	}
	appObj.LossInfo.LossRunSummary = summaryPerCovDB
	return appObj, nil
}

// This function binds all the indication form data for an application from the
// REST structs to the DB, by first binding Operations, then Classes and
// Commodities, and then Loss Run Summary. Each group of data if first checked
// within the DB and then bound, returning empty pointers when data is not
// available.
func (a *appStateMachineHelper) bindIndicationFormFromRestToDB(
	indicationForm *oapi_app.IndicationForm, appObj *application.Application,
) error {
	// First bind the operations data
	appObj, err := a.bindOperationsFromRestToDB(
		indicationForm.OperationsForm, appObj)
	if err != nil {
		return errors.Wrap(err, "unable to bind operations from REST to db")
	}
	// Second bind the class and commodities data
	appObj, err = a.bindClsAndComFromRestToDB(indicationForm.ClassesAndCommoditiesForm, appObj)
	if err != nil {
		return errors.Wrap(err, "unable to bind classes & commodities from REST to db")
	}
	// Third bind the loss run summary data
	appObj, err = a.bindLossRunSummaryFromRestToDB(
		indicationForm.LossRunSummaryForm, appObj)
	if err != nil {
		return errors.Wrap(err, "unable to bind loss run summary from REST to db")
	}
	return nil
}

// bindDeductiblesFromDbToRest is a helper function that helps binding
// the current deductibles from our db to a REST format.
// DEPRECATED
func (a *appStateMachineHelper) bindDeductiblesFromDbToRest(
	coveragesDb []application.CoverageDetails,
) []oapi_app.CurrentDeductibleRecord {
	var result []oapi_app.CurrentDeductibleRecord
	for _, cov := range coveragesDb {
		// TODO fix this
		if quote_generator.PrimaryCoverageTypes[cov.CoverageType] {
			result = append(result, oapi_app.CurrentDeductibleRecord{
				Amount:   *cov.Deductible,
				Coverage: oapi_common.CoverageType(cov.CoverageType.String()),
			})
		}
	}
	return result
}

// bindDeductibleOptionsFromDbToRest is a helper function that helps binding
// the deductible options to a REST format.
// DEPRECATED
func (a *appStateMachineHelper) bindDeductibleOptionsFromDbToRest(
	coveragesDb []application.CoverageDetails,
	defaultDeductibles map[app_enums.Coverage][]int32,
) []oapi_app.DeductibleRecord {
	var result []oapi_app.DeductibleRecord
	for _, cov := range coveragesDb {
		// TODO fix this
		if quote_generator.PrimaryCoverageTypes[cov.CoverageType] {
			result = append(result, oapi_app.DeductibleRecord{
				Amounts:  defaultDeductibles[cov.CoverageType],
				Coverage: oapi_common.CoverageType(cov.CoverageType.String()),
			})
		}
	}
	return result
}

// bindLimitsFromDbToRest is a helper function that helps binding
// the current limits from our db to a REST format.
// DEPRECATED
func (a *appStateMachineHelper) bindLimitsFromDbToRest(
	coveragesDb []application.CoverageDetails,
	state us_states.USState,
) ([]oapi_common.CoverageVariablesOptionNumeric, error) {
	// TODO: Fix this to remove Current as -1
	var result []oapi_common.CoverageVariablesOptionNumeric
	for _, cov := range coveragesDb {
		if app_logic.AllowedCoveragesForLimitUpdates[state][cov.CoverageType] {
			if len(app_logic.LimitOptionsAmounts[state][cov.CoverageType]) == 0 {
				return nil, errors.Newf("couldn't find limits for coverage %s", cov.CoverageType.String())
			}
			var currentVal int32 = -1
			if cov.Limit != nil {
				currentVal = *cov.Limit
			}

			result = append(result, oapi_common.CoverageVariablesOptionNumeric{
				Options: app_logic.LimitOptionsAmounts[state][cov.CoverageType],
				Current: currentVal,
			})
		}
	}
	return result, nil
}

// bindCovVarsFromDbToRest is a helper function that helps binding
// the coverage variables from db to REST format.
// For now, we expose deductibles and limits.
func (a *appStateMachineHelper) bindCovVarsFromDbToRest(
	coverageInfo *application.CoverageInfo,
	companyInfo *application.CompanyInfo,
) ([]oapi_app.CoverageVariablesOptions, error) {
	var retval []oapi_app.CoverageVariablesOptions
	coveragesDb := coverageInfo.Coverages
	state := companyInfo.USState
	for _, cov := range coveragesDb {
		if !app_logic.AllowedCoveragesForDeductibleUpdate[cov.CoverageType] &&
			!app_logic.AllowedCoveragesForLimitUpdates[state][cov.CoverageType] {
			continue
		}
		opt := oapi_app.CoverageVariablesOptions{
			Coverage: oapi_common.CoverageType(cov.CoverageType.String()),
		}
		// Bind deductibles
		if app_logic.AllowedCoveragesForDeductibleUpdate[cov.CoverageType] {
			if cov.Deductible == nil || len(app_logic.GetDeductibleOptionsFromCoverageAndState(cov.CoverageType, a.appState)) == 0 {
				return nil, errors.Newf("couldn't find deductibles for coverage %s", cov.CoverageType.String())
			}
			opt.Deductibles = &oapi_common.CoverageVariablesOptionNumeric{
				Current: *cov.Deductible,
				Options: app_logic.GetDeductibleOptionsFromCoverageAndState(cov.CoverageType, a.appState),
			}
		}
		// Bind Limits
		if app_logic.AllowedCoveragesForLimitUpdates[state][cov.CoverageType] {
			if cov.Limit == nil || len(app_logic.LimitOptionsAmounts[state][cov.CoverageType]) == 0 {
				return nil, errors.Newf("couldn't find limits for coverage %s", cov.CoverageType.String())
			}
			opt.Limits = &oapi_common.CoverageVariablesOptionNumeric{
				Current: *cov.Limit,
				Options: app_logic.LimitOptionsAmounts[state][cov.CoverageType],
			}
		}
		retval = append(retval, opt)
	}
	return retval, nil
}

func (a *appStateMachineHelper) bindRenewalCovVarsFromDbToRest(
	coverages []application.CoverageDetails,
	companyInfo *application.CompanyInfo,
) ([]oapi_app.CoverageVariablesOptions, error) {
	var retval []oapi_app.CoverageVariablesOptions
	state := companyInfo.USState
	for _, cov := range coverages {
		if !app_logic.AllowedCoveragesForDeductibleUpdate[cov.CoverageType] &&
			!app_logic.AllowedCoveragesForLimitUpdates[state][cov.CoverageType] {
			continue
		}
		opt := oapi_app.CoverageVariablesOptions{
			Coverage: oapi_common.CoverageType(cov.CoverageType.String()),
		}
		// Bind deductibles
		if app_logic.AllowedCoveragesForDeductibleUpdate[cov.CoverageType] {
			if len(app_logic.GetDeductibleOptionsFromCoverageAndState(cov.CoverageType, a.appState)) == 0 {
				return nil, errors.Newf("couldn't find deductibles for coverage %s", cov.CoverageType.String())
			}
			opt.Deductibles = &oapi_common.CoverageVariablesOptionNumeric{
				Options: app_logic.GetDeductibleOptionsFromCoverageAndState(cov.CoverageType, a.appState),
			}
			if cov.Deductible != nil {
				opt.Deductibles.Current = *cov.Deductible
			}
		}
		// Bind Limits
		if app_logic.AllowedCoveragesForLimitUpdates[state][cov.CoverageType] {
			if len(app_logic.LimitOptionsAmounts[state][cov.CoverageType]) == 0 {
				return nil, errors.Newf("couldn't find limits for coverage %s", cov.CoverageType.String())
			}
			opt.Limits = &oapi_common.CoverageVariablesOptionNumeric{
				Options: app_logic.LimitOptionsAmounts[state][cov.CoverageType],
			}
			if cov.Limit != nil {
				opt.Limits.Current = *cov.Limit
			}
		}
		retval = append(retval, opt)
	}
	return retval, nil
}

// bindIndOptionsFromDbToRest is a helper function used to bind indication
// options from the db to a REST format.
func (a *appStateMachineHelper) bindIndOptionsFromDbToRest(
	optionsDb []application.IndicationOption,
) []oapi_app.IndicationOption {
	var result []oapi_app.IndicationOption
	for _, opt := range optionsDb {
		result = append(result, a.bindIndOptionFromDbToRest(opt))
	}
	return result
}

// bindPremiumDetailsFromIndOptionDbToRest binds premium details from an
// indication option from db to REST.
func (a *appStateMachineHelper) bindPremiumDetailsFromIndOptionDbToRest(
	option application.IndicationOption,
) oapi_app.PremiumDetails {
	return oapi_app.PremiumDetails{
		FlatCharges:              option.FlatCharges,
		SafetyDiscountPercentage: option.RoundedSafetyDiscountPercentage,
		SafetyDiscountPremium:    option.RoundedSafetyDiscountPremium,
		SubtotalPremium:          option.SubtotalPremium,
		PreDiscountTotalPremium:  option.RoundedPreDiscountTotalPremium,
		TotalPremium:             option.TotalPremium,
		Tiv:                      option.TIV,
		TotalPowerUnits:          option.TotalPowerUnits,
		TotalMiles:               option.TotalMiles,
		TotalSurchargePremium:    option.TotalSurchargePremium,
	}
}

// bindQuoteSummaryFromDbToRest binds quote summary from an
// indication option and submission from db to REST.
func (a *appStateMachineHelper) bindQuoteSummaryFromDbToRest(
	option application.IndicationOption, sub application.SubmissionObject,
) oapi_app.QuoteSummary {
	return oapi_app.QuoteSummary{
		EffectiveDate: types.Date{Time: sub.CoverageInfo.EffectiveDate},
		OptionTag:     oapi_app.IndicationOptionTag(option.OptionTag.String()),
		TotalPremium:  option.TotalPremium,
		UpdatedAt:     types.Date{Time: sub.CreatedAt},
	}
}

func (a *appStateMachineHelper) bindFormsFromDbToRest(forms []forms.Form) ([]oapi_forms.FormRecord, error) {
	var retval []oapi_forms.FormRecord
	for _, f := range forms {
		cov, err := helpers.BindPolicyCoverageType(f.Coverage)
		if err != nil {
			return nil, errors.Wrap(err, "failed to bind policy coverage type")
		}
		retval = append(retval, oapi_forms.FormRecord{
			Code:     f.Code,
			Name:     f.Name,
			Url:      f.Link,
			Coverage: *cov,
		})
	}
	return retval, nil
}

func (a *appStateMachineHelper) bindBasicInfoFieldsFromRestToDB(
	ctx context.Context,
	form oapi_app.ApplicationBasicInfoForm,
	appObj *application.Application,
) (*application.Application, error) {
	if form.ProducerID != nil && *form.ProducerID != "" {
		appObj.ProducerID = form.ProducerID
	}
	if form.AgencyID != nil && *form.AgencyID != "" {
		agencyID, err := uuid.Parse(*form.AgencyID)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse agencyID as uuid")
		}
		appObj.AgencyID = agencyID
	}
	if form.CompanyName != nil && *form.CompanyName != "" {
		appObj.CompanyInfo.Name = *form.CompanyName
	}
	if form.InsuredEmail != nil && *form.InsuredEmail != "" {
		if appObj.AdditionalInsuredInfo == nil {
			appObj.AdditionalInsuredInfo = &application.AdditionalInsuredInfo{
				Email: null.StringFrom(string(*form.InsuredEmail)),
			}
		} else {
			appObj.AdditionalInsuredInfo.Email = null.StringFrom(string(*form.InsuredEmail))
		}
	}
	if form.CreatedBy != nil && *form.CreatedBy != "" {
		appObj.CreatedBy = *form.CreatedBy
	}
	if form.EffectiveDateOfCoverage != nil {
		appObj.CoverageInfo.EffectiveDate = form.EffectiveDateOfCoverage.Time
	}
	if form.InsuredName != nil && *form.InsuredName != "" {
		if appObj.AdditionalInsuredInfo == nil {
			appObj.AdditionalInsuredInfo = &application.AdditionalInsuredInfo{
				Name: *form.InsuredName,
			}
		} else {
			appObj.AdditionalInsuredInfo.Name = *form.InsuredName
		}
	}
	return appObj, nil
}

func (a *appStateMachineHelper) bindCombinedCoveragesFromRestToDB(
	combinedCoverages *[]oapi_common.CombinedCoverages,
	coverages []application.CoverageDetails,
) (*application.CombinedDeductibleCoverages, error) {
	var retval []application.CombinedCoverages

	// Convert *[]oapi_app.CombinedCoverages to application.CombinedDeductibleCoverages
	// i.e. REST Format to DB Format
	for _, combinedCov := range *combinedCoverages {
		combinedCoveragesMap := make(map[app_enums.Coverage]bool)
		for _, cov := range combinedCov {
			covEnum, err := app_enums.CoverageString(string(cov))
			if err != nil {
				return nil, errors.Wrap(err, "invalid coverage passed")
			}
			combinedCoveragesMap[covEnum] = true
		}

		// Check to see if the deductibles for all combined coverages
		// are same or not. If not, throw err
		var deductible *int32
		for k := range combinedCoveragesMap {
			for _, cov := range coverages {
				if cov.CoverageType == k {
					if deductible == nil {
						deductible = cov.Deductible
					}

					if *(cov.Deductible) != *deductible {
						return nil, errors.New("invalid combined coverages, " +
							"all coverages which are combined should have the same deductible")
					}
				}
			}
		}

		// Check if the coverages can be combined or not
		if !app_logic.AreCombinedCoveragesValid(combinedCoveragesMap) {
			return nil, errors.New("can't combine deductibles for the given set of coverages")
		}

		retval = append(retval, combinedCoveragesMap)
	}

	return &application.CombinedDeductibleCoverages{CombinedCoveragesList: retval}, nil
}

func (a *appStateMachineHelper) bindCombinedCoveragesFromDBToRest(
	combinedCoverages *application.CombinedDeductibleCoverages,
) *[]oapi_common.CombinedCoverages {
	if combinedCoverages == nil {
		return nil
	}

	var retval []oapi_common.CombinedCoverages

	for _, coverages := range combinedCoverages.CombinedCoveragesList {
		var c []oapi_common.CoverageType
		for k := range coverages {
			c = append(c, oapi_common.CoverageType(k.String()))
		}
		retval = append(retval, c)
	}

	return &retval
}

// bindRenewalFormFromRestToDB follows the logic of a PATCH endpoint for the forms, where we only want to
// update the renewal application if the form is present
func (a *appStateMachineHelper) bindRenewalFormFromRestToDB(
	patchRenewalApplicationForm *oapi_app.PatchRenewalApplicationForm, appObj *application.Application,
) (*application.Application, error) {
	if !appObj.IsRenewalV2() {
		return appObj, errors.New("Unable to patch non-renewal v2 application")
	}
	if patchRenewalApplicationForm.RenewalFilesForm != nil {
		appObj, err := a.bindRenewalFilesFormToDB(
			patchRenewalApplicationForm.RenewalFilesForm, appObj)
		if err != nil {
			return appObj, errors.Wrap(err, "unable to bind renewal files from REST to db")
		}
	}
	// First bind the coverages data
	if patchRenewalApplicationForm.RenewalCoverageForm != nil {
		appObj, err := a.bindRenewalCoverageFormToDB(
			patchRenewalApplicationForm.RenewalCoverageForm, appObj)
		if err != nil {
			return appObj, errors.Wrap(err, "unable to bind renewal coverage from REST to db")
		}
	}
	// Second bind the operations data
	if patchRenewalApplicationForm.RenewalOperationsForm != nil {
		appObj, err := a.bindRenewalOperationsFormToDB(
			patchRenewalApplicationForm.RenewalOperationsForm, appObj)
		if err != nil {
			return appObj, errors.Wrap(err, "unable to bind renewal operations from REST to db")
		}
	}
	// Third bind the classes and commodities data
	if patchRenewalApplicationForm.RenewalClassesAndCommoditiesForm != nil {
		appObj, err := a.bindRenewalClsAndComFormToDB(
			patchRenewalApplicationForm.RenewalClassesAndCommoditiesForm, appObj)
		if err != nil {
			return appObj, errors.Wrap(err, "unable to bind renewal classes and commodities from REST to db")
		}
	}
	// Forth bind the loss history data
	if patchRenewalApplicationForm.RenewalLossForm != nil {
		appObj, err := a.bindRenewalLossFormToDB(
			patchRenewalApplicationForm.RenewalLossForm, appObj)
		if err != nil {
			return appObj, errors.Wrap(err, "unable to bind renewal loss history from REST to db")
		}
	}
	// Then we bind the additional info data
	if patchRenewalApplicationForm.RenewalAdditionalForm != nil {
		appObj, err := a.bindRenewalAdditionalFormToDB(
			patchRenewalApplicationForm.RenewalAdditionalForm, appObj)
		if err != nil {
			return appObj, errors.Wrap(err, "unable to bind renewal additional information from REST to db")
		}
	}
	// Finally we bind the section completion map
	if patchRenewalApplicationForm.SectionCompletionMap != nil {
		appObj, err := a.bindRenewalSectionCompletionMapToDB(
			patchRenewalApplicationForm.SectionCompletionMap, appObj)
		if err != nil {
			return appObj, errors.Wrap(err, "unable to bind renewal additional information from REST to db")
		}
	}
	return appObj, nil
}

func (a *appStateMachineHelper) bindRenewalCoverageFormToDB(
	renewalCoverageForm *oapi_app.RenewalCoverageForm, appObj *application.Application,
) (*application.Application, error) {
	// check if updated can be performed
	if (*appObj.RenewalMetadata.SectionCompletionMap)[application.Coverages] {
		return appObj, errors.New("Coverages section marked as complete")
	}

	// Bind coverages
	appObj, err := a.bindCoveragesFromRestToDB(&renewalCoverageForm.CoveragesRequired, appObj)
	if err != nil {
		return appObj, errors.Wrap(err, "unable to bind renewal additional information from REST to db")
	}

	// Bind combined coverages
	if renewalCoverageForm.CoveragesWithCombinedDeductibles != nil {
		coveragesWithCombinedDeductibles, err := a.bindCombinedCoveragesFromRestToDB(
			renewalCoverageForm.CoveragesWithCombinedDeductibles,
			appObj.CoverageInfo.Coverages,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to bind combined coverages from rest to db")
		}
		appObj.CoverageInfo.CoveragesWithCombinedDeductibles = coveragesWithCombinedDeductibles
	}
	// Bind ancilliary coverages
	if renewalCoverageForm.AncillaryCoveragesRequired != nil {
		// Bind Ancillary Coverages
		appObj, err = a.bindAncillaryCoveragesRequiredFromRestToDB(*renewalCoverageForm.AncillaryCoveragesRequired, appObj)
		if err != nil {
			return nil, errors.Wrap(err, "unable to bind coverages from rest to db")
		}
	}
	// Bind producer
	appObj.ProducerID = &renewalCoverageForm.ProducerId
	// Bind packageType
	appObj, err = a.bindPackageTypeFromRestToDb(&renewalCoverageForm.PackageType, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind package type from rest to db")
	}

	// Remove commodities and loss history for MTC and APD if MTC and APD were removed on patch.
	lossRunHistory := appObj.LossInfo.LossRunSummary
	if !appObj.CoverageInfo.ContainsCoverage(app_enums.CoverageMotorTruckCargo) {
		appObj.EquipmentInfo.CommodityDistribution = nil
		filteredLossRunSummary := make([]app.LossRunSummaryPerCoverage, 0)

		for _, item := range appObj.LossInfo.LossRunSummary {
			if item.CoverageType != app_enums.CoverageMotorTruckCargo {
				filteredLossRunSummary = append(filteredLossRunSummary, item)
			}
		}
		lossRunHistory = filteredLossRunSummary
	}
	if !appObj.CoverageInfo.ContainsCoverage(app_enums.CoverageAutoPhysicalDamage) {
		filteredLossRunSummary := make([]app.LossRunSummaryPerCoverage, 0)
		for _, item := range appObj.LossInfo.LossRunSummary {
			if item.CoverageType != app_enums.CoverageAutoPhysicalDamage {
				filteredLossRunSummary = append(filteredLossRunSummary, item)
			}
		}
		lossRunHistory = filteredLossRunSummary
	}
	appObj.LossInfo.LossRunSummary = lossRunHistory
	if !appObj.CoverageInfo.ContainsCoverage(app_enums.CoverageGeneralLiability) {
		appObj.CompanyInfo.TerminalLocations = nil
	}

	// We reset the completion map when coverages are updated
	appObj.RenewalMetadata.SectionCompletionMap = application.NewSectionCompletionMap()

	return appObj, nil
}

func (a *appStateMachineHelper) bindRenewalOperationsFormToDB(
	renewalOperationsForm *oapi_app.RenewalOperationsForm, appObj *application.Application,
) (*application.Application, error) {
	appID := appObj.ID
	// check if updated can be performed
	if (*appObj.RenewalMetadata.SectionCompletionMap)[application.Operations] {
		return appObj, errors.New("Coverages section marked as complete")
	}
	// bind totalPowerUnits
	appObj.CompanyInfo.NumberOfPowerUnits = renewalOperationsForm.NumberOfPowerUnits
	// bind projected Mileage
	appObj.CompanyInfo.ProjectedMileage = renewalOperationsForm.ProjectedMileage
	// bind equipmentList
	appObj, err := a.bindEquipmentListFromRestToDB(&renewalOperationsForm.EquipmentList, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind package type for appId %s", appID)
	}
	// bind terminal locations
	appObj, err = a.bindTerminalLocationsFromRestToDB(renewalOperationsForm.TerminalLocations, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind package type for appId %s", appID)
	}
	return appObj, nil
}

func (a *appStateMachineHelper) bindRenewalClsAndComFormToDB(
	renewalClsAndComForm *oapi_app.RenewalClassesAndCommoditiesForm, appObj *application.Application,
) (*application.Application, error) {
	appID := appObj.ID
	// check if updated can be performed
	if (*appObj.RenewalMetadata.SectionCompletionMap)[application.ClassesAndCommodities] {
		return appObj, errors.New("Coverages section marked as complete")
	}
	// bind operatingClassDistribution
	appObj, err := a.bindOperatingClassDistributionFromRestToDB(renewalClsAndComForm.OperatingClassDistribution, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind operating class distribution for appId %s", appID)
	}
	// bind primaryOperatingClass
	appObj, err = a.bindPrimaryOperationgClassFromRestToDB(renewalClsAndComForm.PrimaryOperatingClass, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind primary operating class for appId %s", appID)
	}
	// bind commodityDistribution
	// bind primaryCommodity
	// bind primaryCategory
	var (
		primCategoryPtr  *app_enums.CommodityCategory
		primCommodityPtr *old_enums.PrimaryCommodityHauled
		commoditiesPtr   *application.CommodityDistribution
	)
	// This is applicable only when MTC coverage is selected
	if appObj.CoverageInfo.ContainsCoverage(app_enums.CoverageMotorTruckCargo) {
		commoditiesPtr, primCategoryPtr, err = a.bindCommodityDistributionFromRestToDB(
			renewalClsAndComForm.CommodityDistribution,
		)
		if err != nil {
			return nil, errors.Wrap(err, "couldn't bind commodity distribution")
		}
	} else {
		// If it is pinned to Primary Category based rating
		if appObj.ModelPinConfig.Application.Flags.UsePrimaryCategory {
			// If non-MTC application & pinned to Primary Category based rating,
			// they should pass primary category in the request for it to be valid
			if renewalClsAndComForm.PrimaryCategory != nil {
				primCategoryPtr, err = a.bindPrimaryCategoryFromRestToDB(renewalClsAndComForm.PrimaryCategory.Type)
				if err != nil {
					return nil, errors.Wrap(err, "couldn't bind primary category")
				}
			}
		} else {
			primCommodityPtr, err = a.bindPrimaryCommodityFromRestToDB(
				renewalClsAndComForm.PrimaryOperatingClass,
				renewalClsAndComForm.PrimaryCommodity.Type,
			)
			if err != nil {
				return nil, errors.Wrap(err, "couldn't bind primary commodity")
			}
		}
	}
	appObj.EquipmentInfo.CommodityDistribution = commoditiesPtr
	appObj.EquipmentInfo.PrimaryCommodity = primCommodityPtr
	appObj.EquipmentInfo.PrimaryCategory = primCategoryPtr

	// bind additionalCommodities
	// bind commoditiesComments
	appObj, err = a.bindAdditionalCommodityInfoFromRestToDB(renewalClsAndComForm.AdditionalCommodities, renewalClsAndComForm.CommoditiesComment, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't bind additional commodities information")
	}

	return appObj, nil
}

func (a *appStateMachineHelper) bindRenewalLossFormToDB(
	renewalLossForm *oapi_app.RenewalLossForm, appObj *application.Application,
) (*application.Application, error) {
	// check if updated can be performed
	if (*appObj.RenewalMetadata.SectionCompletionMap)[application.LossHistory] {
		return appObj, errors.New("Loss history section marked as complete")
	}
	appObj, err := a.bindLossRunSummaryFromRestToDB(&renewalLossForm.LossRunSummaryForm, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind loss run summary from rest to db")
	}
	return appObj, nil
}

func (a *appStateMachineHelper) bindRenewalAdditionalFormToDB(
	renewalAdditionalForm *oapi_app.RenewalAdditionalForm, appObj *application.Application,
) (*application.Application, error) {
	// check if updated can be performed
	if (*appObj.RenewalMetadata.SectionCompletionMap)[application.AdditionalInformation] {
		return appObj, errors.New("Additional information section marked as complete")
	}

	// bind driverList
	appObj, err := a.bindDriverListFromRestToDB(&renewalAdditionalForm.DriverList, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind driver list from REST to db")
	}

	// bind additional loss run info
	appObj, err = a.bindAdditionalLossInfoFromRestToDB(&renewalAdditionalForm.LossRunFiles, &renewalAdditionalForm.LargeLossComment, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind additional loss run info from REST to db")
	}

	// bind numOwnerOperationUnits
	// bind overallComment
	appObj.AdditionalInfoExtraMetadata = &app.AdditionalInfoExtraMetadata{
		NumOwnerOperatorUnits: null.IntFromPtr(&renewalAdditionalForm.NumOwnerOperatorUnits),
		OverallComment:        null.StringFromPtr(&renewalAdditionalForm.OverallComment),
	}

	// bind ifta files
	appObj, err = a.bindIftaFilesFromRestToDB(&renewalAdditionalForm.IftaFiles, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind IFTA files")
	}

	return appObj, nil
}

func (a *appStateMachineHelper) bindRenewalFilesFormToDB(
	renewalFilesForm *oapi_app.RenewalFilesForm, appObj *application.Application,
) (*application.Application, error) {
	appID := appObj.ID
	// bind driverList
	appObj, err := a.bindDriverListFromRestToDB(&renewalFilesForm.DriverList, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind driver list from REST to db")
	}
	// bind equipmentList
	appObj, err = a.bindEquipmentListFromRestToDB(&renewalFilesForm.EquipmentList, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind package type for appId %s", appID)
	}
	// bind lossRunFiles
	appObj, err = a.bindLossRunFilesFromRestToDB(&renewalFilesForm.LossRunFiles, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind loss run files")
	}
	// bind iftaFiles
	appObj, err = a.bindIftaFilesFromRestToDB(&renewalFilesForm.IftaFiles, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to bind IFTA files")
	}

	return appObj, nil
}

func (a *appStateMachineHelper) bindRenewalSectionCompletionMapToDB(
	sectionCompletionMap *oapi_app.SectionCompletionMap, appObj *application.Application,
) (*application.Application, error) {
	// Check for sectionCompletionMap, if we are not patching it, continue.
	if sectionCompletionMap == nil {
		return appObj, nil
	}

	completionMap := application.SectionCompletionMap{}
	completionMap[application.Coverages] = sectionCompletionMap.Coverages
	completionMap[application.Operations] = sectionCompletionMap.Operations
	completionMap[application.ClassesAndCommodities] = sectionCompletionMap.ClassesAndCommodities
	completionMap[application.LossHistory] = sectionCompletionMap.LossHistory
	completionMap[application.AdditionalInformation] = sectionCompletionMap.AdditionalInformation

	appObj.RenewalMetadata.SectionCompletionMap = &completionMap

	return appObj, nil
}

// bindCoveragesFromRestToDB binds coverage records from REST to the DB.
// includes GL's special behavior where we follow AL deductibles. Every time
// there is a change in AL deductible, GL should reflect that as well.
func (a *appStateMachineHelper) bindCoveragesFromRestToDB(
	formCoverages *[]oapi_app.CoverageRecord, appObj *application.Application,
) (*application.Application, error) {
	// CoveragesRequired
	coverages := make([]application.CoverageDetails, 0)
	// Flag to check if GL is present, as it has a special behavior that
	// follows AL deductibles.
	hasGl := false
	var alDeductible int32
	coverageRequired := formCoverages
	if coverageRequired == nil {
		return nil, errors.New("coverages required is nil")
	}

	for _, cov := range *coverageRequired {
		covEnum, err := app_enums.CoverageString(string(cov.CoverageType))
		if err != nil {
			return nil, errors.Wrapf(err, "unable to parse coverage type %s", cov.CoverageType)
		}
		ded := app_logic.DefaultDeductibleAmounts[covEnum]
		if cov.Deductible != nil {
			ded = *cov.Deductible
		}
		lim, err := app_logic.GetCoverageLimit(covEnum, appObj.CompanyInfo.USState)
		if err != nil {
			return nil, errors.Wrapf(err,
				"unable to get default limit for coverage %s in state", covEnum.String(), appObj.CompanyInfo.USState,
			)
		}
		if cov.Limit != nil {
			lim = cov.Limit
		}
		coverages = append(coverages, application.CoverageDetails{
			CoverageType: covEnum,
			Deductible:   pointer_utils.Int32(ded),
			Limit:        lim,
		})
		// Store AL deductible for GL special case
		switch covEnum {
		case app_enums.CoverageAutoLiability:
			alDeductible = ded
		case app_enums.CoverageGeneralLiability:
			hasGl = true
		}
	}
	appObj.CoverageInfo.Coverages = coverages
	// GL has a special behavior where we follow AL deductibles. Every time
	// there is a change in AL deductible, GL should reflect that as well.
	if hasGl {
		// Update GL deductible
		for i := range appObj.CoverageInfo.Coverages {
			if appObj.CoverageInfo.Coverages[i].CoverageType == app_enums.CoverageGeneralLiability {
				appObj.CoverageInfo.Coverages[i].Deductible = pointer_utils.Int32(alDeductible)
			}
		}
	}

	// Apply all coverage rules (towing, defaults etc.)
	appObj.CoverageInfo.Coverages = a.applyCoverageRules(appObj.CoverageInfo)

	return appObj, nil
}

func (a *appStateMachineHelper) bindPackageTypeFromRestToDb(
	formPackageType *oapi_app.IndicationOptionTag, appObj *application.Application,
) (*application.Application, error) {
	switch *formPackageType {
	case oapi_app.IndicationOptionTagBasic:
		basicIndicationTag := app_enums.IndicationOptionTagBasic
		appObj.PackageType = &basicIndicationTag
	case oapi_app.IndicationOptionTagStandard:
		standardIndicationTag := app_enums.IndicationOptionTagStandard
		appObj.PackageType = &standardIndicationTag
	case oapi_app.IndicationOptionTagComplete:
		completeIndicationTag := app_enums.IndicationOptionTagComplete
		appObj.PackageType = &completeIndicationTag
	}

	return appObj, nil
}

func (a *appStateMachineHelper) bindTerminalLocationsFromRestToDB(
	formTerminalLocations *[]oapi_app.TerminalLocation, appObj *application.Application,
) (*application.Application, error) {
	mtcCoverage := appObj.CoverageInfo.GetCoverage(app_enums.CoverageMotorTruckCargo)
	if formTerminalLocations != nil {
		// TerminalLocations
		var terminalLocations []application.TerminalLocation
		var state us_states.USState
		var err error
		var terminalType app_enums.TypeOfTerminal
		for _, v := range *formTerminalLocations {
			state, err = us_states.StrToUSState(string(v.UsState))
			if err != nil {
				return nil, errors.Wrapf(err, "can't get US State from state %s", v.UsState)
			}
			terminalType, err = app_enums.TypeOfTerminalString(string(v.TypeOfTerminal))
			if err != nil {
				return nil, errors.Wrapf(err, "can't get type of terminal string from type of terminal %s",
					v.UsState)
			}
			zipCodeInt, err := strconv.Atoi(v.ZipCode)
			if err != nil {
				return nil, errors.Wrapf(err, "can't get zip code integer from zip code %s", v.ZipCode)
			}

			var cargoTerminalSchedule *application.CargoTerminalSchedule
			if v.CargoTerminalSchedule != nil {
				privateTheftProtection, err := app_enums.PrivateTheftProtectionString(string(v.CargoTerminalSchedule.PrivateTheftProtection))
				if err != nil {
					return nil, errors.Wrapf(
						err, "can't get private theft protection string from private theft protection %s",
						v.CargoTerminalSchedule.PrivateTheftProtection,
					)
				}
				privateFireProtection, err := app_enums.PrivateFireProtectionString(string(v.CargoTerminalSchedule.PrivateFireProtection))
				if err != nil {
					return nil, errors.Wrapf(
						err, "can't get private fire protection string from private fire protection %s",
						v.CargoTerminalSchedule.PrivateFireProtection,
					)
				}

				cargoTerminalSchedule = &application.CargoTerminalSchedule{
					ID:                     uuid.New(),
					PublicProtectionClass:  pointer_utils.ToPointer(app_enums.PublicProtectionClass1),
					PrivateTheftProtection: privateTheftProtection,
					PrivateFireProtection:  privateFireProtection,
					ConstructionClass:      pointer_utils.ToPointer(app_enums.ConstructionClassFrame),
				}

				// terminal schedule limit should be the same as the MTC coverage limit
				if mtcCoverage != nil {
					cargoTerminalSchedule.Limit = mtcCoverage.Limit
				}

				// Override the default construction class if the agent provided a construction class
				if v.CargoTerminalSchedule.ConstructionClass != nil {
					constructionClass, err := app_enums.ConstructionClassString(string(*v.CargoTerminalSchedule.ConstructionClass))
					if err != nil {
						return nil, errors.Wrapf(
							err, "can't get construction class string from construction class %s",
							*v.CargoTerminalSchedule.ConstructionClass,
						)
					}
					cargoTerminalSchedule.ConstructionClass = &constructionClass
				}
			}

			tLoc := application.TerminalLocation{
				AddressLineOne:        v.AddressLineOne,
				AddressLineTwo:        v.AddressLineTwo,
				IsGated:               v.IsGated,
				IsGuarded:             v.IsGuarded,
				TypeOfTerminal:        terminalType,
				UsState:               state,
				ZipCode:               zipCodeInt,
				ZipCodeString:         v.ZipCode,
				CargoTerminalSchedule: cargoTerminalSchedule,
			}
			terminalLocations = append(terminalLocations, tLoc)
		}

		appObj.CompanyInfo.TerminalLocations = &terminalLocations
	}
	return appObj, nil
}

func (a *appStateMachineHelper) bindEquipmentListFromRestToDB(
	formEquipmentList *oapi_app.EquipmentList, appObj *application.Application,
) (*application.Application, error) {
	info := make([]application.EquipmentListRecord, 0)
	for _, vehicle := range formEquipmentList.Info {
		info = append(info, application.EquipmentListRecord{
			VIN:         strings.ToUpper(vehicle.Vin),
			StatedValue: vehicle.StatedValue,
		})
	}
	flatfileMetadata, err := a.bindFlatfileMetadataFromRestToDB(formEquipmentList.FlatfileMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "failed to bind flatfile metadata from rest to db")
	}
	implerMetadata, err := a.bindImplerMetadataFromRestToDB(formEquipmentList.ImplerMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "failed to bind impler metadata from rest to db")
	}
	appObj.EquipmentInfo.EquipmentList = application.EquipmentList{
		Info:             info,
		FlatfileMetadata: flatfileMetadata,
		ImplerMetadata:   implerMetadata,
	}
	return appObj, nil
}

func (a *appStateMachineHelper) bindFlatfileMetadataFromRestToDB(
	metadata *oapi_common.FlatfileMetadata,
) (*application.FlatfileMetadata, error) {
	if metadata == nil {
		return nil, nil // nolint:nilnil
	}

	handle, err := pointer_utils.UUIDParseOrNil(metadata.FileMetadata.Handle)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse equipment file handle %s",
			metadata.FileMetadata.Handle)
	}

	return &application.FlatfileMetadata{
		FlatfileHandle: metadata.FlatfileHandle,
		FileMetadata: application.FileMetadata{
			Name:   metadata.FileMetadata.Name,
			Handle: handle,
		},
	}, nil
}

func (a *appStateMachineHelper) bindImplerMetadataFromRestToDB(
	metadata *oapi_common.ImplerMetadata,
) (*application.ImplerMetadata, error) {
	if metadata == nil {
		return nil, nil // nolint:nilnil
	}

	handle, err := pointer_utils.UUIDParseOrNil(metadata.FileMetadata.Handle)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse equipment file handle %s",
			metadata.FileMetadata.Handle)
	}

	return &application.ImplerMetadata{
		ImplerHandle: metadata.ImplerHandle,
		FileMetadata: application.FileMetadata{
			Name:   metadata.FileMetadata.Name,
			Handle: handle,
		},
	}, nil
}

func (a *appStateMachineHelper) bindOperatingClassDistributionFromRestToDB(
	formOperatingClassDistribution oapi_app.OperatingClassDistribution, appObj *application.Application,
) (*application.Application, error) {
	opClsAndComForDB := make(
		[]application.OperatingClassDistributionRecord, 0)
	for _, opClsDis := range formOperatingClassDistribution {
		cls, err := app_enums.OperatingClassString(string(opClsDis.OperatingClass))
		if err != nil {
			return nil, errors.Wrapf(err, "unsupported operating class %s", string(opClsDis.OperatingClass))
		}
		opClsAndComForDB = append(opClsAndComForDB, application.OperatingClassDistributionRecord{
			Class:             cls,
			PercentageOfFleet: opClsDis.PercentageOfFleet,
		})
	}
	appObj.EquipmentInfo.OperatingClassDistribution = opClsAndComForDB

	return appObj, nil
}

func (a *appStateMachineHelper) bindPrimaryOperationgClassFromRestToDB(
	formPrimaryOperationgClass oapi_common.OperatingClass, appObj *application.Application,
) (*application.Application, error) {
	primOp, err := app_enums.OperatingClassString(string(formPrimaryOperationgClass))
	if err != nil {
		return nil, errors.Wrapf(err, "unsupported primary operating class %s",
			string(formPrimaryOperationgClass))
	}
	appObj.EquipmentInfo.PrimaryOperatingClass = &primOp

	return appObj, nil
}

func (a *appStateMachineHelper) bindIftaFilesFromRestToDB(
	formIftaRunFiles *[]oapi_common.FileMetadata, appObj *app.Application,
) (*app.Application, error) {
	var iftaFiles []app.FileMetadata
	if formIftaRunFiles != nil {
		for _, metadata := range *formIftaRunFiles {
			handle, err := pointer_utils.UUIDParseOrNil(metadata.Handle)
			if err != nil {
				return nil, errors.Wrapf(err, "unable to parse ifta file handle")
			}
			iftaFiles = append(iftaFiles, app.FileMetadata{
				Name:   metadata.Name,
				Handle: handle,
			})
		}
	}
	appObj.RenewalMetadata.IftaFiles = &iftaFiles

	return appObj, nil
}

func (a *appStateMachineHelper) bindIftaFilesFromDBtoRest(
	appObj *app.Application,
) *[]oapi_common.FileMetadata {
	var formIftaRunFiles []oapi_common.FileMetadata

	if appObj.RenewalMetadata.IftaFiles != nil {
		for _, metadata := range *appObj.RenewalMetadata.IftaFiles {
			handle := pointer_utils.UUIDStringOrNil(metadata.Handle)
			formIftaRunFiles = append(formIftaRunFiles, oapi_common.FileMetadata{
				Name:   metadata.Name,
				Handle: handle,
			})
		}
	}

	return &formIftaRunFiles
}

// determineMTCVersionFromApplication determines the MTC version for an application
// based on its model pin configuration. Returns None as default when no config is available.
func (a *appStateMachineHelper) determineMTCVersionFromApplication(
	appObj *application.Application,
) *oapi_common.MTCVersion {
	if appObj != nil && appObj.ModelPinConfig != nil {
		// Extract state and version from model pin config
		state := appObj.ModelPinConfig.RateML.USState
		version := appObj.ModelPinConfig.RateML.Version
		provider := appObj.ModelPinConfig.RateML.Provider
		mtcVersionEnum := models_release.GetMTCVersion(state, version, provider)

		return ConvertMTCVersionToOAPI(mtcVersionEnum)
	}

	return pointer_utils.ToPointer(oapi_common.MTCVersionNone)
}

// ConvertMTCVersionToOAPI converts models_release.MTCVersion to oapi_app.MTCVersion
func ConvertMTCVersionToOAPI(mtcVersion models_release.MTCVersion) *oapi_common.MTCVersion {
	switch mtcVersion {
	case models_release.MTCVersionNone:
		return pointer_utils.ToPointer(oapi_common.MTCVersionNone)
	case models_release.MTCVersionV1:
		return pointer_utils.ToPointer(oapi_common.MTCVersionV1)
	case models_release.MTCVersionV2:
		return pointer_utils.ToPointer(oapi_common.MTCVersionV2)
	default:
		return pointer_utils.ToPointer(oapi_common.MTCVersionNone)
	}
}

// applyCoverageRules applies all coverage management rules including:
// - Towing coverage management based on APD coverage presence
// - CargoTrailerInterchange default values
// - CargoAtScheduledTerminalCoverage deductible always follows MTC deductible
func (a *appStateMachineHelper) applyCoverageRules(coverageInfo *application.CoverageInfo) []application.CoverageDetails {
	coverages := coverageInfo.Coverages

	// 1. Manage towing coverage based on APD coverage presence
	hasAPD := coverageInfo.ContainsCoverage(app_enums.CoverageAutoPhysicalDamage)
	hasTowing := coverageInfo.ContainsCoverage(app_enums.CoverageEnhancedPackageTowingLimit)

	switch {
	case hasAPD && !hasTowing:
		// Add towing coverage when APD is present but towing is missing
		coverages = append(coverages, application.CoverageDetails{
			CoverageType: app_enums.CoverageEnhancedPackageTowingLimit,
			Limit:        pointer_utils.ToPointer(int32(app_logic.DefaultTowingLimit)),
		})
	case !hasAPD && hasTowing:
		// Remove towing coverage when APD is absent but towing is present
		coverages = a.removeCoverageType(coverages, app_enums.CoverageEnhancedPackageTowingLimit)
	case hasAPD && hasTowing:
		// Remove existing towing coverage first, then add with correct limit
		coverages = a.removeCoverageType(coverages, app_enums.CoverageEnhancedPackageTowingLimit)
		coverages = append(coverages, application.CoverageDetails{
			CoverageType: app_enums.CoverageEnhancedPackageTowingLimit,
			Limit:        pointer_utils.ToPointer(int32(app_logic.DefaultTowingLimit)),
		})
	}

	// 2. Apply default values and deductible rules
	mtcCov := coverageInfo.GetCoverage(app_enums.CoverageMotorTruckCargo)
	for i := range coverages {
		cov := &coverages[i]

		// Set default limit & deductible for cargo trailer interchange
		if cov.CoverageType == app_enums.CoverageCargoTrailerInterchange {
			cov.Limit = pointer_utils.Int32(app_logic.DefaultTrailerInterchangeLimit)
			cov.Deductible = pointer_utils.Int32(app_logic.DefaultDeductibleCoverageTrailerInterchange)
		}

		// CoverageCargoAtScheduledTerminals deductible always follows MTC deductible
		if cov.CoverageType == app_enums.CoverageCargoAtScheduledTerminals && mtcCov != nil && mtcCov.Deductible != nil {
			cov.Deductible = mtcCov.Deductible
		}
	}

	return coverages
}

// removeCoverageType efficiently removes a specific coverage type from a slice of coverages.
func (a *appStateMachineHelper) removeCoverageType(
	coverages []application.CoverageDetails,
	coverageType app_enums.Coverage,
) []application.CoverageDetails {
	return slice_utils.Filter(coverages, func(cov application.CoverageDetails) bool {
		return cov.CoverageType != coverageType
	})
}
