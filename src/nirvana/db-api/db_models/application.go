// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package db_models

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/sqlboiler/v4/types"
	"github.com/volatiletech/strmangle"
)

// Application is an object representing the database table.
type Application struct {
	ID                       string      `boil:"id" json:"id" toml:"id" yaml:"id"`
	State                    string      `boil:"state" json:"state" toml:"state" yaml:"state"`
	CompanyInfo              types.JSON  `boil:"company_info" json:"company_info" toml:"company_info" yaml:"company_info"`
	EquipmentInfo            types.JSON  `boil:"equipment_info" json:"equipment_info" toml:"equipment_info" yaml:"equipment_info"`
	DriversInfo              types.JSON  `boil:"drivers_info" json:"drivers_info" toml:"drivers_info" yaml:"drivers_info"`
	LossInfo                 types.JSON  `boil:"loss_info" json:"loss_info" toml:"loss_info" yaml:"loss_info"`
	CoverageInfo             types.JSON  `boil:"coverage_info" json:"coverage_info" toml:"coverage_info" yaml:"coverage_info"`
	UwSubmissionID           null.String `boil:"uw_submission_id" json:"uw_submission_id,omitempty" toml:"uw_submission_id" yaml:"uw_submission_id,omitempty"`
	CreatedAt                time.Time   `boil:"created_at" json:"created_at" toml:"created_at" yaml:"created_at"`
	UpdatedAt                time.Time   `boil:"updated_at" json:"updated_at" toml:"updated_at" yaml:"updated_at"`
	ShortID                  string      `boil:"short_id" json:"short_id" toml:"short_id" yaml:"short_id"`
	ProducerID               null.String `boil:"producer_id" json:"producer_id,omitempty" toml:"producer_id" yaml:"producer_id,omitempty"`
	AddlLossInfo             null.JSON   `boil:"addl_loss_info" json:"addl_loss_info,omitempty" toml:"addl_loss_info" yaml:"addl_loss_info,omitempty"`
	AddlCommodityInfo        null.JSON   `boil:"addl_commodity_info" json:"addl_commodity_info,omitempty" toml:"addl_commodity_info" yaml:"addl_commodity_info,omitempty"`
	AddlInfoExtra            null.JSON   `boil:"addl_info_extra" json:"addl_info_extra,omitempty" toml:"addl_info_extra" yaml:"addl_info_extra,omitempty"`
	StateMetadata            null.JSON   `boil:"state_metadata" json:"state_metadata,omitempty" toml:"state_metadata" yaml:"state_metadata,omitempty"`
	CreatedBy                string      `boil:"created_by" json:"created_by" toml:"created_by" yaml:"created_by"`
	TSPConnHandleID          null.String `boil:"tsp_conn_handle_id" json:"tsp_conn_handle_id,omitempty" toml:"tsp_conn_handle_id" yaml:"tsp_conn_handle_id,omitempty"`
	TSPEnum                  null.String `boil:"tsp_enum" json:"tsp_enum,omitempty" toml:"tsp_enum" yaml:"tsp_enum,omitempty"`
	PackageType              null.String `boil:"package_type" json:"package_type,omitempty" toml:"package_type" yaml:"package_type,omitempty"`
	AddlInsuredInfo          null.JSON   `boil:"addl_insured_info" json:"addl_insured_info,omitempty" toml:"addl_insured_info" yaml:"addl_insured_info,omitempty"`
	BindableSubmissionID     null.String `boil:"bindable_submission_id" json:"bindable_submission_id,omitempty" toml:"bindable_submission_id" yaml:"bindable_submission_id,omitempty"`
	Problems                 null.JSON   `boil:"problems" json:"problems,omitempty" toml:"problems" yaml:"problems,omitempty"`
	AgencyID                 null.String `boil:"agency_id" json:"agency_id,omitempty" toml:"agency_id" yaml:"agency_id,omitempty"`
	UnderwriterID            null.String `boil:"underwriter_id" json:"underwriter_id,omitempty" toml:"underwriter_id" yaml:"underwriter_id,omitempty"`
	ArchivedAt               null.Time   `boil:"archived_at" json:"archived_at,omitempty" toml:"archived_at" yaml:"archived_at,omitempty"`
	IndicationSubmissionID   null.String `boil:"indication_submission_id" json:"indication_submission_id,omitempty" toml:"indication_submission_id" yaml:"indication_submission_id,omitempty"`
	ModelPinConfig           null.JSON   `boil:"model_pin_config" json:"model_pin_config,omitempty" toml:"model_pin_config" yaml:"model_pin_config,omitempty"`
	AddlInfoCoverageMetadata null.JSON   `boil:"addl_info_coverage_metadata" json:"addl_info_coverage_metadata,omitempty" toml:"addl_info_coverage_metadata" yaml:"addl_info_coverage_metadata,omitempty"`
	RenewalMetadata          null.JSON   `boil:"renewal_metadata" json:"renewal_metadata,omitempty" toml:"renewal_metadata" yaml:"renewal_metadata,omitempty"`
	AddlEmailInfo            null.JSON   `boil:"addl_email_info" json:"addl_email_info,omitempty" toml:"addl_email_info" yaml:"addl_email_info,omitempty"`
	TelematicsInfo           null.JSON   `boil:"telematics_info" json:"telematics_info,omitempty" toml:"telematics_info" yaml:"telematics_info,omitempty"`
	PricingMetadata          null.JSON   `boil:"pricing_metadata" json:"pricing_metadata,omitempty" toml:"pricing_metadata" yaml:"pricing_metadata,omitempty"`
	Tags                     null.JSON   `boil:"tags" json:"tags,omitempty" toml:"tags" yaml:"tags,omitempty"`
	DataContextID            string      `boil:"data_context_id" json:"data_context_id" toml:"data_context_id" yaml:"data_context_id"`
	ClearanceStatus          null.String `boil:"clearance_status" json:"clearance_status,omitempty" toml:"clearance_status" yaml:"clearance_status,omitempty"`
	AssignedBD               null.String `boil:"assigned_bd" json:"assigned_bd,omitempty" toml:"assigned_bd" yaml:"assigned_bd,omitempty"`
	AdditionalAgentFiles     null.JSON   `boil:"additional_agent_files" json:"additional_agent_files,omitempty" toml:"additional_agent_files" yaml:"additional_agent_files,omitempty"`
	RetailerInfo             null.JSON   `boil:"retailer_info" json:"retailer_info,omitempty" toml:"retailer_info" yaml:"retailer_info,omitempty"`

	R *applicationR `boil:"" json:"" toml:"" yaml:""`
	L applicationL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var ApplicationColumns = struct {
	ID                       string
	State                    string
	CompanyInfo              string
	EquipmentInfo            string
	DriversInfo              string
	LossInfo                 string
	CoverageInfo             string
	UwSubmissionID           string
	CreatedAt                string
	UpdatedAt                string
	ShortID                  string
	ProducerID               string
	AddlLossInfo             string
	AddlCommodityInfo        string
	AddlInfoExtra            string
	StateMetadata            string
	CreatedBy                string
	TSPConnHandleID          string
	TSPEnum                  string
	PackageType              string
	AddlInsuredInfo          string
	BindableSubmissionID     string
	Problems                 string
	AgencyID                 string
	UnderwriterID            string
	ArchivedAt               string
	IndicationSubmissionID   string
	ModelPinConfig           string
	AddlInfoCoverageMetadata string
	RenewalMetadata          string
	AddlEmailInfo            string
	TelematicsInfo           string
	PricingMetadata          string
	Tags                     string
	DataContextID            string
	ClearanceStatus          string
	AssignedBD               string
	AdditionalAgentFiles     string
	RetailerInfo             string
}{
	ID:                       "id",
	State:                    "state",
	CompanyInfo:              "company_info",
	EquipmentInfo:            "equipment_info",
	DriversInfo:              "drivers_info",
	LossInfo:                 "loss_info",
	CoverageInfo:             "coverage_info",
	UwSubmissionID:           "uw_submission_id",
	CreatedAt:                "created_at",
	UpdatedAt:                "updated_at",
	ShortID:                  "short_id",
	ProducerID:               "producer_id",
	AddlLossInfo:             "addl_loss_info",
	AddlCommodityInfo:        "addl_commodity_info",
	AddlInfoExtra:            "addl_info_extra",
	StateMetadata:            "state_metadata",
	CreatedBy:                "created_by",
	TSPConnHandleID:          "tsp_conn_handle_id",
	TSPEnum:                  "tsp_enum",
	PackageType:              "package_type",
	AddlInsuredInfo:          "addl_insured_info",
	BindableSubmissionID:     "bindable_submission_id",
	Problems:                 "problems",
	AgencyID:                 "agency_id",
	UnderwriterID:            "underwriter_id",
	ArchivedAt:               "archived_at",
	IndicationSubmissionID:   "indication_submission_id",
	ModelPinConfig:           "model_pin_config",
	AddlInfoCoverageMetadata: "addl_info_coverage_metadata",
	RenewalMetadata:          "renewal_metadata",
	AddlEmailInfo:            "addl_email_info",
	TelematicsInfo:           "telematics_info",
	PricingMetadata:          "pricing_metadata",
	Tags:                     "tags",
	DataContextID:            "data_context_id",
	ClearanceStatus:          "clearance_status",
	AssignedBD:               "assigned_bd",
	AdditionalAgentFiles:     "additional_agent_files",
	RetailerInfo:             "retailer_info",
}

var ApplicationTableColumns = struct {
	ID                       string
	State                    string
	CompanyInfo              string
	EquipmentInfo            string
	DriversInfo              string
	LossInfo                 string
	CoverageInfo             string
	UwSubmissionID           string
	CreatedAt                string
	UpdatedAt                string
	ShortID                  string
	ProducerID               string
	AddlLossInfo             string
	AddlCommodityInfo        string
	AddlInfoExtra            string
	StateMetadata            string
	CreatedBy                string
	TSPConnHandleID          string
	TSPEnum                  string
	PackageType              string
	AddlInsuredInfo          string
	BindableSubmissionID     string
	Problems                 string
	AgencyID                 string
	UnderwriterID            string
	ArchivedAt               string
	IndicationSubmissionID   string
	ModelPinConfig           string
	AddlInfoCoverageMetadata string
	RenewalMetadata          string
	AddlEmailInfo            string
	TelematicsInfo           string
	PricingMetadata          string
	Tags                     string
	DataContextID            string
	ClearanceStatus          string
	AssignedBD               string
	AdditionalAgentFiles     string
	RetailerInfo             string
}{
	ID:                       "application.id",
	State:                    "application.state",
	CompanyInfo:              "application.company_info",
	EquipmentInfo:            "application.equipment_info",
	DriversInfo:              "application.drivers_info",
	LossInfo:                 "application.loss_info",
	CoverageInfo:             "application.coverage_info",
	UwSubmissionID:           "application.uw_submission_id",
	CreatedAt:                "application.created_at",
	UpdatedAt:                "application.updated_at",
	ShortID:                  "application.short_id",
	ProducerID:               "application.producer_id",
	AddlLossInfo:             "application.addl_loss_info",
	AddlCommodityInfo:        "application.addl_commodity_info",
	AddlInfoExtra:            "application.addl_info_extra",
	StateMetadata:            "application.state_metadata",
	CreatedBy:                "application.created_by",
	TSPConnHandleID:          "application.tsp_conn_handle_id",
	TSPEnum:                  "application.tsp_enum",
	PackageType:              "application.package_type",
	AddlInsuredInfo:          "application.addl_insured_info",
	BindableSubmissionID:     "application.bindable_submission_id",
	Problems:                 "application.problems",
	AgencyID:                 "application.agency_id",
	UnderwriterID:            "application.underwriter_id",
	ArchivedAt:               "application.archived_at",
	IndicationSubmissionID:   "application.indication_submission_id",
	ModelPinConfig:           "application.model_pin_config",
	AddlInfoCoverageMetadata: "application.addl_info_coverage_metadata",
	RenewalMetadata:          "application.renewal_metadata",
	AddlEmailInfo:            "application.addl_email_info",
	TelematicsInfo:           "application.telematics_info",
	PricingMetadata:          "application.pricing_metadata",
	Tags:                     "application.tags",
	DataContextID:            "application.data_context_id",
	ClearanceStatus:          "application.clearance_status",
	AssignedBD:               "application.assigned_bd",
	AdditionalAgentFiles:     "application.additional_agent_files",
	RetailerInfo:             "application.retailer_info",
}

// Generated where

type whereHelpertypes_JSON struct{ field string }

func (w whereHelpertypes_JSON) EQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.EQ, x)
}
func (w whereHelpertypes_JSON) NEQ(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.NEQ, x)
}
func (w whereHelpertypes_JSON) LT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpertypes_JSON) LTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpertypes_JSON) GT(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpertypes_JSON) GTE(x types.JSON) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

type whereHelpernull_String struct{ field string }

func (w whereHelpernull_String) EQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, false, x)
}
func (w whereHelpernull_String) NEQ(x null.String) qm.QueryMod {
	return qmhelper.WhereNullEQ(w.field, true, x)
}
func (w whereHelpernull_String) LT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LT, x)
}
func (w whereHelpernull_String) LTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.LTE, x)
}
func (w whereHelpernull_String) GT(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GT, x)
}
func (w whereHelpernull_String) GTE(x null.String) qm.QueryMod {
	return qmhelper.Where(w.field, qmhelper.GTE, x)
}

func (w whereHelpernull_String) IsNull() qm.QueryMod    { return qmhelper.WhereIsNull(w.field) }
func (w whereHelpernull_String) IsNotNull() qm.QueryMod { return qmhelper.WhereIsNotNull(w.field) }

var ApplicationWhere = struct {
	ID                       whereHelperstring
	State                    whereHelperstring
	CompanyInfo              whereHelpertypes_JSON
	EquipmentInfo            whereHelpertypes_JSON
	DriversInfo              whereHelpertypes_JSON
	LossInfo                 whereHelpertypes_JSON
	CoverageInfo             whereHelpertypes_JSON
	UwSubmissionID           whereHelpernull_String
	CreatedAt                whereHelpertime_Time
	UpdatedAt                whereHelpertime_Time
	ShortID                  whereHelperstring
	ProducerID               whereHelpernull_String
	AddlLossInfo             whereHelpernull_JSON
	AddlCommodityInfo        whereHelpernull_JSON
	AddlInfoExtra            whereHelpernull_JSON
	StateMetadata            whereHelpernull_JSON
	CreatedBy                whereHelperstring
	TSPConnHandleID          whereHelpernull_String
	TSPEnum                  whereHelpernull_String
	PackageType              whereHelpernull_String
	AddlInsuredInfo          whereHelpernull_JSON
	BindableSubmissionID     whereHelpernull_String
	Problems                 whereHelpernull_JSON
	AgencyID                 whereHelpernull_String
	UnderwriterID            whereHelpernull_String
	ArchivedAt               whereHelpernull_Time
	IndicationSubmissionID   whereHelpernull_String
	ModelPinConfig           whereHelpernull_JSON
	AddlInfoCoverageMetadata whereHelpernull_JSON
	RenewalMetadata          whereHelpernull_JSON
	AddlEmailInfo            whereHelpernull_JSON
	TelematicsInfo           whereHelpernull_JSON
	PricingMetadata          whereHelpernull_JSON
	Tags                     whereHelpernull_JSON
	DataContextID            whereHelperstring
	ClearanceStatus          whereHelpernull_String
	AssignedBD               whereHelpernull_String
	AdditionalAgentFiles     whereHelpernull_JSON
	RetailerInfo             whereHelpernull_JSON
}{
	ID:                       whereHelperstring{field: "\"application\".\"id\""},
	State:                    whereHelperstring{field: "\"application\".\"state\""},
	CompanyInfo:              whereHelpertypes_JSON{field: "\"application\".\"company_info\""},
	EquipmentInfo:            whereHelpertypes_JSON{field: "\"application\".\"equipment_info\""},
	DriversInfo:              whereHelpertypes_JSON{field: "\"application\".\"drivers_info\""},
	LossInfo:                 whereHelpertypes_JSON{field: "\"application\".\"loss_info\""},
	CoverageInfo:             whereHelpertypes_JSON{field: "\"application\".\"coverage_info\""},
	UwSubmissionID:           whereHelpernull_String{field: "\"application\".\"uw_submission_id\""},
	CreatedAt:                whereHelpertime_Time{field: "\"application\".\"created_at\""},
	UpdatedAt:                whereHelpertime_Time{field: "\"application\".\"updated_at\""},
	ShortID:                  whereHelperstring{field: "\"application\".\"short_id\""},
	ProducerID:               whereHelpernull_String{field: "\"application\".\"producer_id\""},
	AddlLossInfo:             whereHelpernull_JSON{field: "\"application\".\"addl_loss_info\""},
	AddlCommodityInfo:        whereHelpernull_JSON{field: "\"application\".\"addl_commodity_info\""},
	AddlInfoExtra:            whereHelpernull_JSON{field: "\"application\".\"addl_info_extra\""},
	StateMetadata:            whereHelpernull_JSON{field: "\"application\".\"state_metadata\""},
	CreatedBy:                whereHelperstring{field: "\"application\".\"created_by\""},
	TSPConnHandleID:          whereHelpernull_String{field: "\"application\".\"tsp_conn_handle_id\""},
	TSPEnum:                  whereHelpernull_String{field: "\"application\".\"tsp_enum\""},
	PackageType:              whereHelpernull_String{field: "\"application\".\"package_type\""},
	AddlInsuredInfo:          whereHelpernull_JSON{field: "\"application\".\"addl_insured_info\""},
	BindableSubmissionID:     whereHelpernull_String{field: "\"application\".\"bindable_submission_id\""},
	Problems:                 whereHelpernull_JSON{field: "\"application\".\"problems\""},
	AgencyID:                 whereHelpernull_String{field: "\"application\".\"agency_id\""},
	UnderwriterID:            whereHelpernull_String{field: "\"application\".\"underwriter_id\""},
	ArchivedAt:               whereHelpernull_Time{field: "\"application\".\"archived_at\""},
	IndicationSubmissionID:   whereHelpernull_String{field: "\"application\".\"indication_submission_id\""},
	ModelPinConfig:           whereHelpernull_JSON{field: "\"application\".\"model_pin_config\""},
	AddlInfoCoverageMetadata: whereHelpernull_JSON{field: "\"application\".\"addl_info_coverage_metadata\""},
	RenewalMetadata:          whereHelpernull_JSON{field: "\"application\".\"renewal_metadata\""},
	AddlEmailInfo:            whereHelpernull_JSON{field: "\"application\".\"addl_email_info\""},
	TelematicsInfo:           whereHelpernull_JSON{field: "\"application\".\"telematics_info\""},
	PricingMetadata:          whereHelpernull_JSON{field: "\"application\".\"pricing_metadata\""},
	Tags:                     whereHelpernull_JSON{field: "\"application\".\"tags\""},
	DataContextID:            whereHelperstring{field: "\"application\".\"data_context_id\""},
	ClearanceStatus:          whereHelpernull_String{field: "\"application\".\"clearance_status\""},
	AssignedBD:               whereHelpernull_String{field: "\"application\".\"assigned_bd\""},
	AdditionalAgentFiles:     whereHelpernull_JSON{field: "\"application\".\"additional_agent_files\""},
	RetailerInfo:             whereHelpernull_JSON{field: "\"application\".\"retailer_info\""},
}

// ApplicationRels is where relationship names are stored.
var ApplicationRels = struct {
	BindableSubmission   string
	IndicationSubmission string
	UwSubmission         string
	ApplicationReviews   string
	Submissions          string
}{
	BindableSubmission:   "BindableSubmission",
	IndicationSubmission: "IndicationSubmission",
	UwSubmission:         "UwSubmission",
	ApplicationReviews:   "ApplicationReviews",
	Submissions:          "Submissions",
}

// applicationR is where relationships are stored.
type applicationR struct {
	BindableSubmission   *Submission            `boil:"BindableSubmission" json:"BindableSubmission" toml:"BindableSubmission" yaml:"BindableSubmission"`
	IndicationSubmission *Submission            `boil:"IndicationSubmission" json:"IndicationSubmission" toml:"IndicationSubmission" yaml:"IndicationSubmission"`
	UwSubmission         *Submission            `boil:"UwSubmission" json:"UwSubmission" toml:"UwSubmission" yaml:"UwSubmission"`
	ApplicationReviews   ApplicationReviewSlice `boil:"ApplicationReviews" json:"ApplicationReviews" toml:"ApplicationReviews" yaml:"ApplicationReviews"`
	Submissions          SubmissionSlice        `boil:"Submissions" json:"Submissions" toml:"Submissions" yaml:"Submissions"`
}

// NewStruct creates a new relationship struct
func (*applicationR) NewStruct() *applicationR {
	return &applicationR{}
}

// applicationL is where Load methods for each relationship are stored.
type applicationL struct{}

var (
	applicationAllColumns            = []string{"id", "state", "company_info", "equipment_info", "drivers_info", "loss_info", "coverage_info", "uw_submission_id", "created_at", "updated_at", "short_id", "producer_id", "addl_loss_info", "addl_commodity_info", "addl_info_extra", "state_metadata", "created_by", "tsp_conn_handle_id", "tsp_enum", "package_type", "addl_insured_info", "bindable_submission_id", "problems", "agency_id", "underwriter_id", "archived_at", "indication_submission_id", "model_pin_config", "addl_info_coverage_metadata", "renewal_metadata", "addl_email_info", "telematics_info", "pricing_metadata", "tags", "data_context_id", "clearance_status", "assigned_bd", "additional_agent_files", "retailer_info"}
	applicationColumnsWithoutDefault = []string{"id", "state", "company_info", "equipment_info", "drivers_info", "loss_info", "coverage_info", "short_id", "created_by", "data_context_id"}
	applicationColumnsWithDefault    = []string{"uw_submission_id", "created_at", "updated_at", "producer_id", "addl_loss_info", "addl_commodity_info", "addl_info_extra", "state_metadata", "tsp_conn_handle_id", "tsp_enum", "package_type", "addl_insured_info", "bindable_submission_id", "problems", "agency_id", "underwriter_id", "archived_at", "indication_submission_id", "model_pin_config", "addl_info_coverage_metadata", "renewal_metadata", "addl_email_info", "telematics_info", "pricing_metadata", "tags", "clearance_status", "assigned_bd", "additional_agent_files", "retailer_info"}
	applicationPrimaryKeyColumns     = []string{"id"}
	applicationGeneratedColumns      = []string{}
)

type (
	// ApplicationSlice is an alias for a slice of pointers to Application.
	// This should almost always be used instead of []Application.
	ApplicationSlice []*Application
	// ApplicationHook is the signature for custom Application hook methods
	ApplicationHook func(context.Context, boil.ContextExecutor, *Application) error

	applicationQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	applicationType                 = reflect.TypeOf(&Application{})
	applicationMapping              = queries.MakeStructMapping(applicationType)
	applicationPrimaryKeyMapping, _ = queries.BindMapping(applicationType, applicationMapping, applicationPrimaryKeyColumns)
	applicationInsertCacheMut       sync.RWMutex
	applicationInsertCache          = make(map[string]insertCache)
	applicationUpdateCacheMut       sync.RWMutex
	applicationUpdateCache          = make(map[string]updateCache)
	applicationUpsertCacheMut       sync.RWMutex
	applicationUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var applicationAfterSelectHooks []ApplicationHook

var applicationBeforeInsertHooks []ApplicationHook
var applicationAfterInsertHooks []ApplicationHook

var applicationBeforeUpdateHooks []ApplicationHook
var applicationAfterUpdateHooks []ApplicationHook

var applicationBeforeDeleteHooks []ApplicationHook
var applicationAfterDeleteHooks []ApplicationHook

var applicationBeforeUpsertHooks []ApplicationHook
var applicationAfterUpsertHooks []ApplicationHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *Application) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *Application) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *Application) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *Application) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *Application) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *Application) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *Application) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *Application) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *Application) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range applicationAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddApplicationHook registers your hook function for all future operations.
func AddApplicationHook(hookPoint boil.HookPoint, applicationHook ApplicationHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		applicationAfterSelectHooks = append(applicationAfterSelectHooks, applicationHook)
	case boil.BeforeInsertHook:
		applicationBeforeInsertHooks = append(applicationBeforeInsertHooks, applicationHook)
	case boil.AfterInsertHook:
		applicationAfterInsertHooks = append(applicationAfterInsertHooks, applicationHook)
	case boil.BeforeUpdateHook:
		applicationBeforeUpdateHooks = append(applicationBeforeUpdateHooks, applicationHook)
	case boil.AfterUpdateHook:
		applicationAfterUpdateHooks = append(applicationAfterUpdateHooks, applicationHook)
	case boil.BeforeDeleteHook:
		applicationBeforeDeleteHooks = append(applicationBeforeDeleteHooks, applicationHook)
	case boil.AfterDeleteHook:
		applicationAfterDeleteHooks = append(applicationAfterDeleteHooks, applicationHook)
	case boil.BeforeUpsertHook:
		applicationBeforeUpsertHooks = append(applicationBeforeUpsertHooks, applicationHook)
	case boil.AfterUpsertHook:
		applicationAfterUpsertHooks = append(applicationAfterUpsertHooks, applicationHook)
	}
}

// One returns a single application record from the query.
func (q applicationQuery) One(ctx context.Context, exec boil.ContextExecutor) (*Application, error) {
	o := &Application{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "db_models: failed to execute a one query for application")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all Application records from the query.
func (q applicationQuery) All(ctx context.Context, exec boil.ContextExecutor) (ApplicationSlice, error) {
	var o []*Application

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "db_models: failed to assign all query results to Application slice")
	}

	if len(applicationAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all Application records in the query.
func (q applicationQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to count application rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q applicationQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "db_models: failed to check if application exists")
	}

	return count > 0, nil
}

// BindableSubmission pointed to by the foreign key.
func (o *Application) BindableSubmission(mods ...qm.QueryMod) submissionQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.BindableSubmissionID),
	}

	queryMods = append(queryMods, mods...)

	return Submissions(queryMods...)
}

// IndicationSubmission pointed to by the foreign key.
func (o *Application) IndicationSubmission(mods ...qm.QueryMod) submissionQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.IndicationSubmissionID),
	}

	queryMods = append(queryMods, mods...)

	return Submissions(queryMods...)
}

// UwSubmission pointed to by the foreign key.
func (o *Application) UwSubmission(mods ...qm.QueryMod) submissionQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.UwSubmissionID),
	}

	queryMods = append(queryMods, mods...)

	return Submissions(queryMods...)
}

// ApplicationReviews retrieves all the application_review's ApplicationReviews with an executor.
func (o *Application) ApplicationReviews(mods ...qm.QueryMod) applicationReviewQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"application_review\".\"application_id\"=?", o.ID),
	)

	return ApplicationReviews(queryMods...)
}

// Submissions retrieves all the submission's Submissions with an executor.
func (o *Application) Submissions(mods ...qm.QueryMod) submissionQuery {
	var queryMods []qm.QueryMod
	if len(mods) != 0 {
		queryMods = append(queryMods, mods...)
	}

	queryMods = append(queryMods,
		qm.Where("\"submission\".\"application_id\"=?", o.ID),
	)

	return Submissions(queryMods...)
}

// LoadBindableSubmission allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (applicationL) LoadBindableSubmission(ctx context.Context, e boil.ContextExecutor, singular bool, maybeApplication interface{}, mods queries.Applicator) error {
	var slice []*Application
	var object *Application

	if singular {
		object = maybeApplication.(*Application)
	} else {
		slice = *maybeApplication.(*[]*Application)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &applicationR{}
		}
		if !queries.IsNil(object.BindableSubmissionID) {
			args = append(args, object.BindableSubmissionID)
		}

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &applicationR{}
			}

			for _, a := range args {
				if queries.Equal(a, obj.BindableSubmissionID) {
					continue Outer
				}
			}

			if !queries.IsNil(obj.BindableSubmissionID) {
				args = append(args, obj.BindableSubmissionID)
			}

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`submission`),
		qm.WhereIn(`submission.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Submission")
	}

	var resultSlice []*Submission
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Submission")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for submission")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for submission")
	}

	if len(applicationAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.BindableSubmission = foreign
		if foreign.R == nil {
			foreign.R = &submissionR{}
		}
		foreign.R.BindableSubmissionApplications = append(foreign.R.BindableSubmissionApplications, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if queries.Equal(local.BindableSubmissionID, foreign.ID) {
				local.R.BindableSubmission = foreign
				if foreign.R == nil {
					foreign.R = &submissionR{}
				}
				foreign.R.BindableSubmissionApplications = append(foreign.R.BindableSubmissionApplications, local)
				break
			}
		}
	}

	return nil
}

// LoadIndicationSubmission allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (applicationL) LoadIndicationSubmission(ctx context.Context, e boil.ContextExecutor, singular bool, maybeApplication interface{}, mods queries.Applicator) error {
	var slice []*Application
	var object *Application

	if singular {
		object = maybeApplication.(*Application)
	} else {
		slice = *maybeApplication.(*[]*Application)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &applicationR{}
		}
		if !queries.IsNil(object.IndicationSubmissionID) {
			args = append(args, object.IndicationSubmissionID)
		}

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &applicationR{}
			}

			for _, a := range args {
				if queries.Equal(a, obj.IndicationSubmissionID) {
					continue Outer
				}
			}

			if !queries.IsNil(obj.IndicationSubmissionID) {
				args = append(args, obj.IndicationSubmissionID)
			}

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`submission`),
		qm.WhereIn(`submission.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Submission")
	}

	var resultSlice []*Submission
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Submission")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for submission")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for submission")
	}

	if len(applicationAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.IndicationSubmission = foreign
		if foreign.R == nil {
			foreign.R = &submissionR{}
		}
		foreign.R.IndicationSubmissionApplications = append(foreign.R.IndicationSubmissionApplications, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if queries.Equal(local.IndicationSubmissionID, foreign.ID) {
				local.R.IndicationSubmission = foreign
				if foreign.R == nil {
					foreign.R = &submissionR{}
				}
				foreign.R.IndicationSubmissionApplications = append(foreign.R.IndicationSubmissionApplications, local)
				break
			}
		}
	}

	return nil
}

// LoadUwSubmission allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (applicationL) LoadUwSubmission(ctx context.Context, e boil.ContextExecutor, singular bool, maybeApplication interface{}, mods queries.Applicator) error {
	var slice []*Application
	var object *Application

	if singular {
		object = maybeApplication.(*Application)
	} else {
		slice = *maybeApplication.(*[]*Application)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &applicationR{}
		}
		if !queries.IsNil(object.UwSubmissionID) {
			args = append(args, object.UwSubmissionID)
		}

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &applicationR{}
			}

			for _, a := range args {
				if queries.Equal(a, obj.UwSubmissionID) {
					continue Outer
				}
			}

			if !queries.IsNil(obj.UwSubmissionID) {
				args = append(args, obj.UwSubmissionID)
			}

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`submission`),
		qm.WhereIn(`submission.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Submission")
	}

	var resultSlice []*Submission
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Submission")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for submission")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for submission")
	}

	if len(applicationAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.UwSubmission = foreign
		if foreign.R == nil {
			foreign.R = &submissionR{}
		}
		foreign.R.UwSubmissionApplications = append(foreign.R.UwSubmissionApplications, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if queries.Equal(local.UwSubmissionID, foreign.ID) {
				local.R.UwSubmission = foreign
				if foreign.R == nil {
					foreign.R = &submissionR{}
				}
				foreign.R.UwSubmissionApplications = append(foreign.R.UwSubmissionApplications, local)
				break
			}
		}
	}

	return nil
}

// LoadApplicationReviews allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (applicationL) LoadApplicationReviews(ctx context.Context, e boil.ContextExecutor, singular bool, maybeApplication interface{}, mods queries.Applicator) error {
	var slice []*Application
	var object *Application

	if singular {
		object = maybeApplication.(*Application)
	} else {
		slice = *maybeApplication.(*[]*Application)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &applicationR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &applicationR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`application_review`),
		qm.WhereIn(`application_review.application_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load application_review")
	}

	var resultSlice []*ApplicationReview
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice application_review")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on application_review")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for application_review")
	}

	if len(applicationReviewAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.ApplicationReviews = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &applicationReviewR{}
			}
			foreign.R.Application = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ApplicationID {
				local.R.ApplicationReviews = append(local.R.ApplicationReviews, foreign)
				if foreign.R == nil {
					foreign.R = &applicationReviewR{}
				}
				foreign.R.Application = local
				break
			}
		}
	}

	return nil
}

// LoadSubmissions allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for a 1-M or N-M relationship.
func (applicationL) LoadSubmissions(ctx context.Context, e boil.ContextExecutor, singular bool, maybeApplication interface{}, mods queries.Applicator) error {
	var slice []*Application
	var object *Application

	if singular {
		object = maybeApplication.(*Application)
	} else {
		slice = *maybeApplication.(*[]*Application)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &applicationR{}
		}
		args = append(args, object.ID)
	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &applicationR{}
			}

			for _, a := range args {
				if a == obj.ID {
					continue Outer
				}
			}

			args = append(args, obj.ID)
		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`submission`),
		qm.WhereIn(`submission.application_id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load submission")
	}

	var resultSlice []*Submission
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice submission")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results in eager load on submission")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for submission")
	}

	if len(submissionAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}
	if singular {
		object.R.Submissions = resultSlice
		for _, foreign := range resultSlice {
			if foreign.R == nil {
				foreign.R = &submissionR{}
			}
			foreign.R.Application = object
		}
		return nil
	}

	for _, foreign := range resultSlice {
		for _, local := range slice {
			if local.ID == foreign.ApplicationID {
				local.R.Submissions = append(local.R.Submissions, foreign)
				if foreign.R == nil {
					foreign.R = &submissionR{}
				}
				foreign.R.Application = local
				break
			}
		}
	}

	return nil
}

// SetBindableSubmission of the application to the related item.
// Sets o.R.BindableSubmission to related.
// Adds o to related.R.BindableSubmissionApplications.
func (o *Application) SetBindableSubmission(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Submission) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"application\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"bindable_submission_id"}),
		strmangle.WhereClause("\"", "\"", 2, applicationPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	queries.Assign(&o.BindableSubmissionID, related.ID)
	if o.R == nil {
		o.R = &applicationR{
			BindableSubmission: related,
		}
	} else {
		o.R.BindableSubmission = related
	}

	if related.R == nil {
		related.R = &submissionR{
			BindableSubmissionApplications: ApplicationSlice{o},
		}
	} else {
		related.R.BindableSubmissionApplications = append(related.R.BindableSubmissionApplications, o)
	}

	return nil
}

// RemoveBindableSubmission relationship.
// Sets o.R.BindableSubmission to nil.
// Removes o from all passed in related items' relationships struct.
func (o *Application) RemoveBindableSubmission(ctx context.Context, exec boil.ContextExecutor, related *Submission) error {
	var err error

	queries.SetScanner(&o.BindableSubmissionID, nil)
	if _, err = o.Update(ctx, exec, boil.Whitelist("bindable_submission_id")); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	if o.R != nil {
		o.R.BindableSubmission = nil
	}
	if related == nil || related.R == nil {
		return nil
	}

	for i, ri := range related.R.BindableSubmissionApplications {
		if queries.Equal(o.BindableSubmissionID, ri.BindableSubmissionID) {
			continue
		}

		ln := len(related.R.BindableSubmissionApplications)
		if ln > 1 && i < ln-1 {
			related.R.BindableSubmissionApplications[i] = related.R.BindableSubmissionApplications[ln-1]
		}
		related.R.BindableSubmissionApplications = related.R.BindableSubmissionApplications[:ln-1]
		break
	}
	return nil
}

// SetIndicationSubmission of the application to the related item.
// Sets o.R.IndicationSubmission to related.
// Adds o to related.R.IndicationSubmissionApplications.
func (o *Application) SetIndicationSubmission(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Submission) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"application\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"indication_submission_id"}),
		strmangle.WhereClause("\"", "\"", 2, applicationPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	queries.Assign(&o.IndicationSubmissionID, related.ID)
	if o.R == nil {
		o.R = &applicationR{
			IndicationSubmission: related,
		}
	} else {
		o.R.IndicationSubmission = related
	}

	if related.R == nil {
		related.R = &submissionR{
			IndicationSubmissionApplications: ApplicationSlice{o},
		}
	} else {
		related.R.IndicationSubmissionApplications = append(related.R.IndicationSubmissionApplications, o)
	}

	return nil
}

// RemoveIndicationSubmission relationship.
// Sets o.R.IndicationSubmission to nil.
// Removes o from all passed in related items' relationships struct.
func (o *Application) RemoveIndicationSubmission(ctx context.Context, exec boil.ContextExecutor, related *Submission) error {
	var err error

	queries.SetScanner(&o.IndicationSubmissionID, nil)
	if _, err = o.Update(ctx, exec, boil.Whitelist("indication_submission_id")); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	if o.R != nil {
		o.R.IndicationSubmission = nil
	}
	if related == nil || related.R == nil {
		return nil
	}

	for i, ri := range related.R.IndicationSubmissionApplications {
		if queries.Equal(o.IndicationSubmissionID, ri.IndicationSubmissionID) {
			continue
		}

		ln := len(related.R.IndicationSubmissionApplications)
		if ln > 1 && i < ln-1 {
			related.R.IndicationSubmissionApplications[i] = related.R.IndicationSubmissionApplications[ln-1]
		}
		related.R.IndicationSubmissionApplications = related.R.IndicationSubmissionApplications[:ln-1]
		break
	}
	return nil
}

// SetUwSubmission of the application to the related item.
// Sets o.R.UwSubmission to related.
// Adds o to related.R.UwSubmissionApplications.
func (o *Application) SetUwSubmission(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Submission) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"application\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"uw_submission_id"}),
		strmangle.WhereClause("\"", "\"", 2, applicationPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.ID}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	queries.Assign(&o.UwSubmissionID, related.ID)
	if o.R == nil {
		o.R = &applicationR{
			UwSubmission: related,
		}
	} else {
		o.R.UwSubmission = related
	}

	if related.R == nil {
		related.R = &submissionR{
			UwSubmissionApplications: ApplicationSlice{o},
		}
	} else {
		related.R.UwSubmissionApplications = append(related.R.UwSubmissionApplications, o)
	}

	return nil
}

// RemoveUwSubmission relationship.
// Sets o.R.UwSubmission to nil.
// Removes o from all passed in related items' relationships struct.
func (o *Application) RemoveUwSubmission(ctx context.Context, exec boil.ContextExecutor, related *Submission) error {
	var err error

	queries.SetScanner(&o.UwSubmissionID, nil)
	if _, err = o.Update(ctx, exec, boil.Whitelist("uw_submission_id")); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	if o.R != nil {
		o.R.UwSubmission = nil
	}
	if related == nil || related.R == nil {
		return nil
	}

	for i, ri := range related.R.UwSubmissionApplications {
		if queries.Equal(o.UwSubmissionID, ri.UwSubmissionID) {
			continue
		}

		ln := len(related.R.UwSubmissionApplications)
		if ln > 1 && i < ln-1 {
			related.R.UwSubmissionApplications[i] = related.R.UwSubmissionApplications[ln-1]
		}
		related.R.UwSubmissionApplications = related.R.UwSubmissionApplications[:ln-1]
		break
	}
	return nil
}

// AddApplicationReviews adds the given related objects to the existing relationships
// of the application, optionally inserting them as new records.
// Appends related to o.R.ApplicationReviews.
// Sets related.R.Application appropriately.
func (o *Application) AddApplicationReviews(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*ApplicationReview) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ApplicationID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"application_review\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"application_id"}),
				strmangle.WhereClause("\"", "\"", 2, applicationReviewPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ApplicationID = o.ID
		}
	}

	if o.R == nil {
		o.R = &applicationR{
			ApplicationReviews: related,
		}
	} else {
		o.R.ApplicationReviews = append(o.R.ApplicationReviews, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &applicationReviewR{
				Application: o,
			}
		} else {
			rel.R.Application = o
		}
	}
	return nil
}

// AddSubmissions adds the given related objects to the existing relationships
// of the application, optionally inserting them as new records.
// Appends related to o.R.Submissions.
// Sets related.R.Application appropriately.
func (o *Application) AddSubmissions(ctx context.Context, exec boil.ContextExecutor, insert bool, related ...*Submission) error {
	var err error
	for _, rel := range related {
		if insert {
			rel.ApplicationID = o.ID
			if err = rel.Insert(ctx, exec, boil.Infer()); err != nil {
				return errors.Wrap(err, "failed to insert into foreign table")
			}
		} else {
			updateQuery := fmt.Sprintf(
				"UPDATE \"submission\" SET %s WHERE %s",
				strmangle.SetParamNames("\"", "\"", 1, []string{"application_id"}),
				strmangle.WhereClause("\"", "\"", 2, submissionPrimaryKeyColumns),
			)
			values := []interface{}{o.ID, rel.ID}

			if boil.IsDebug(ctx) {
				writer := boil.DebugWriterFrom(ctx)
				fmt.Fprintln(writer, updateQuery)
				fmt.Fprintln(writer, values)
			}
			if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
				return errors.Wrap(err, "failed to update foreign table")
			}

			rel.ApplicationID = o.ID
		}
	}

	if o.R == nil {
		o.R = &applicationR{
			Submissions: related,
		}
	} else {
		o.R.Submissions = append(o.R.Submissions, related...)
	}

	for _, rel := range related {
		if rel.R == nil {
			rel.R = &submissionR{
				Application: o,
			}
		} else {
			rel.R.Application = o
		}
	}
	return nil
}

// Applications retrieves all the records using an executor.
func Applications(mods ...qm.QueryMod) applicationQuery {
	mods = append(mods, qm.From("\"application\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"application\".*"})
	}

	return applicationQuery{q}
}

// FindApplication retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindApplication(ctx context.Context, exec boil.ContextExecutor, iD string, selectCols ...string) (*Application, error) {
	applicationObj := &Application{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"application\" where \"id\"=$1", sel,
	)

	q := queries.Raw(query, iD)

	err := q.Bind(ctx, exec, applicationObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "db_models: unable to select from application")
	}

	if err = applicationObj.doAfterSelectHooks(ctx, exec); err != nil {
		return applicationObj, err
	}

	return applicationObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *Application) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("db_models: no application provided for insertion")
	}

	var err error
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		if o.UpdatedAt.IsZero() {
			o.UpdatedAt = currTime
		}
	}

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(applicationColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	applicationInsertCacheMut.RLock()
	cache, cached := applicationInsertCache[key]
	applicationInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			applicationAllColumns,
			applicationColumnsWithDefault,
			applicationColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(applicationType, applicationMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(applicationType, applicationMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"application\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"application\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "db_models: unable to insert into application")
	}

	if !cached {
		applicationInsertCacheMut.Lock()
		applicationInsertCache[key] = cache
		applicationInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the Application.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *Application) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		o.UpdatedAt = currTime
	}

	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	applicationUpdateCacheMut.RLock()
	cache, cached := applicationUpdateCache[key]
	applicationUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			applicationAllColumns,
			applicationPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("db_models: unable to update application, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"application\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, applicationPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(applicationType, applicationMapping, append(wl, applicationPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update application row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by update for application")
	}

	if !cached {
		applicationUpdateCacheMut.Lock()
		applicationUpdateCache[key] = cache
		applicationUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q applicationQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update all for application")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to retrieve rows affected for application")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o ApplicationSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("db_models: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"application\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, applicationPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to update all in application slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to retrieve rows affected all in update all application")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *Application) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("db_models: no application provided for upsert")
	}
	if !boil.TimestampsAreSkipped(ctx) {
		currTime := time.Now().In(boil.GetLocation())

		if o.CreatedAt.IsZero() {
			o.CreatedAt = currTime
		}
		o.UpdatedAt = currTime
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(applicationColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	applicationUpsertCacheMut.RLock()
	cache, cached := applicationUpsertCache[key]
	applicationUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			applicationAllColumns,
			applicationColumnsWithDefault,
			applicationColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			applicationAllColumns,
			applicationPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("db_models: unable to upsert application, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(applicationPrimaryKeyColumns))
			copy(conflict, applicationPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"application\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(applicationType, applicationMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(applicationType, applicationMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "db_models: unable to upsert application")
	}

	if !cached {
		applicationUpsertCacheMut.Lock()
		applicationUpsertCache[key] = cache
		applicationUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single Application record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *Application) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("db_models: no Application provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), applicationPrimaryKeyMapping)
	sql := "DELETE FROM \"application\" WHERE \"id\"=$1"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete from application")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by delete for application")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q applicationQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("db_models: no applicationQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete all from application")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by deleteall for application")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o ApplicationSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(applicationBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"application\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, applicationPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "db_models: unable to delete all from application slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "db_models: failed to get rows affected by deleteall for application")
	}

	if len(applicationAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *Application) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindApplication(ctx, exec, o.ID)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *ApplicationSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := ApplicationSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), applicationPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"application\".* FROM \"application\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, applicationPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "db_models: unable to reload all in ApplicationSlice")
	}

	*o = slice

	return nil
}

// ApplicationExists checks if the Application row exists.
func ApplicationExists(ctx context.Context, exec boil.ContextExecutor, iD string) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"application\" where \"id\"=$1 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, iD)
	}
	row := exec.QueryRowContext(ctx, sql, iD)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "db_models: unable to check if application exists")
	}

	return exists, nil
}
