// Code generated by SQLBoiler (https://github.com/volatiletech/sqlboiler). DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package authorities

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/friendsofgo/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"github.com/volatiletech/sqlboiler/v4/queries/qmhelper"
	"github.com/volatiletech/strmangle"
)

// RequestsStateTransition is an object representing the database table.
type RequestsStateTransition struct {
	RequestID string    `boil:"request_id" json:"request_id" toml:"request_id" yaml:"request_id"`
	Timestamp time.Time `boil:"timestamp" json:"timestamp" toml:"timestamp" yaml:"timestamp"`
	FromState string    `boil:"from_state" json:"from_state" toml:"from_state" yaml:"from_state"`
	ToState   string    `boil:"to_state" json:"to_state" toml:"to_state" yaml:"to_state"`
	Metadata  null.JSON `boil:"metadata" json:"metadata,omitempty" toml:"metadata" yaml:"metadata,omitempty"`

	R *requestsStateTransitionR `boil:"" json:"" toml:"" yaml:""`
	L requestsStateTransitionL  `boil:"-" json:"-" toml:"-" yaml:"-"`
}

var RequestsStateTransitionColumns = struct {
	RequestID string
	Timestamp string
	FromState string
	ToState   string
	Metadata  string
}{
	RequestID: "request_id",
	Timestamp: "timestamp",
	FromState: "from_state",
	ToState:   "to_state",
	Metadata:  "metadata",
}

var RequestsStateTransitionTableColumns = struct {
	RequestID string
	Timestamp string
	FromState string
	ToState   string
	Metadata  string
}{
	RequestID: "requests_state_transitions.request_id",
	Timestamp: "requests_state_transitions.timestamp",
	FromState: "requests_state_transitions.from_state",
	ToState:   "requests_state_transitions.to_state",
	Metadata:  "requests_state_transitions.metadata",
}

// Generated where

var RequestsStateTransitionWhere = struct {
	RequestID whereHelperstring
	Timestamp whereHelpertime_Time
	FromState whereHelperstring
	ToState   whereHelperstring
	Metadata  whereHelpernull_JSON
}{
	RequestID: whereHelperstring{field: "\"authorities\".\"requests_state_transitions\".\"request_id\""},
	Timestamp: whereHelpertime_Time{field: "\"authorities\".\"requests_state_transitions\".\"timestamp\""},
	FromState: whereHelperstring{field: "\"authorities\".\"requests_state_transitions\".\"from_state\""},
	ToState:   whereHelperstring{field: "\"authorities\".\"requests_state_transitions\".\"to_state\""},
	Metadata:  whereHelpernull_JSON{field: "\"authorities\".\"requests_state_transitions\".\"metadata\""},
}

// RequestsStateTransitionRels is where relationship names are stored.
var RequestsStateTransitionRels = struct {
	Request string
}{
	Request: "Request",
}

// requestsStateTransitionR is where relationships are stored.
type requestsStateTransitionR struct {
	Request *Request `boil:"Request" json:"Request" toml:"Request" yaml:"Request"`
}

// NewStruct creates a new relationship struct
func (*requestsStateTransitionR) NewStruct() *requestsStateTransitionR {
	return &requestsStateTransitionR{}
}

// requestsStateTransitionL is where Load methods for each relationship are stored.
type requestsStateTransitionL struct{}

var (
	requestsStateTransitionAllColumns            = []string{"request_id", "timestamp", "from_state", "to_state", "metadata"}
	requestsStateTransitionColumnsWithoutDefault = []string{"request_id", "from_state", "to_state"}
	requestsStateTransitionColumnsWithDefault    = []string{"timestamp", "metadata"}
	requestsStateTransitionPrimaryKeyColumns     = []string{"request_id", "timestamp"}
	requestsStateTransitionGeneratedColumns      = []string{}
)

type (
	// RequestsStateTransitionSlice is an alias for a slice of pointers to RequestsStateTransition.
	// This should almost always be used instead of []RequestsStateTransition.
	RequestsStateTransitionSlice []*RequestsStateTransition
	// RequestsStateTransitionHook is the signature for custom RequestsStateTransition hook methods
	RequestsStateTransitionHook func(context.Context, boil.ContextExecutor, *RequestsStateTransition) error

	requestsStateTransitionQuery struct {
		*queries.Query
	}
)

// Cache for insert, update and upsert
var (
	requestsStateTransitionType                 = reflect.TypeOf(&RequestsStateTransition{})
	requestsStateTransitionMapping              = queries.MakeStructMapping(requestsStateTransitionType)
	requestsStateTransitionPrimaryKeyMapping, _ = queries.BindMapping(requestsStateTransitionType, requestsStateTransitionMapping, requestsStateTransitionPrimaryKeyColumns)
	requestsStateTransitionInsertCacheMut       sync.RWMutex
	requestsStateTransitionInsertCache          = make(map[string]insertCache)
	requestsStateTransitionUpdateCacheMut       sync.RWMutex
	requestsStateTransitionUpdateCache          = make(map[string]updateCache)
	requestsStateTransitionUpsertCacheMut       sync.RWMutex
	requestsStateTransitionUpsertCache          = make(map[string]insertCache)
)

var (
	// Force time package dependency for automated UpdatedAt/CreatedAt.
	_ = time.Second
	// Force qmhelper dependency for where clause generation (which doesn't
	// always happen)
	_ = qmhelper.Where
)

var requestsStateTransitionAfterSelectHooks []RequestsStateTransitionHook

var requestsStateTransitionBeforeInsertHooks []RequestsStateTransitionHook
var requestsStateTransitionAfterInsertHooks []RequestsStateTransitionHook

var requestsStateTransitionBeforeUpdateHooks []RequestsStateTransitionHook
var requestsStateTransitionAfterUpdateHooks []RequestsStateTransitionHook

var requestsStateTransitionBeforeDeleteHooks []RequestsStateTransitionHook
var requestsStateTransitionAfterDeleteHooks []RequestsStateTransitionHook

var requestsStateTransitionBeforeUpsertHooks []RequestsStateTransitionHook
var requestsStateTransitionAfterUpsertHooks []RequestsStateTransitionHook

// doAfterSelectHooks executes all "after Select" hooks.
func (o *RequestsStateTransition) doAfterSelectHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionAfterSelectHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeInsertHooks executes all "before insert" hooks.
func (o *RequestsStateTransition) doBeforeInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionBeforeInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterInsertHooks executes all "after Insert" hooks.
func (o *RequestsStateTransition) doAfterInsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionAfterInsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpdateHooks executes all "before Update" hooks.
func (o *RequestsStateTransition) doBeforeUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionBeforeUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpdateHooks executes all "after Update" hooks.
func (o *RequestsStateTransition) doAfterUpdateHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionAfterUpdateHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeDeleteHooks executes all "before Delete" hooks.
func (o *RequestsStateTransition) doBeforeDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionBeforeDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterDeleteHooks executes all "after Delete" hooks.
func (o *RequestsStateTransition) doAfterDeleteHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionAfterDeleteHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doBeforeUpsertHooks executes all "before Upsert" hooks.
func (o *RequestsStateTransition) doBeforeUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionBeforeUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// doAfterUpsertHooks executes all "after Upsert" hooks.
func (o *RequestsStateTransition) doAfterUpsertHooks(ctx context.Context, exec boil.ContextExecutor) (err error) {
	if boil.HooksAreSkipped(ctx) {
		return nil
	}

	for _, hook := range requestsStateTransitionAfterUpsertHooks {
		if err := hook(ctx, exec, o); err != nil {
			return err
		}
	}

	return nil
}

// AddRequestsStateTransitionHook registers your hook function for all future operations.
func AddRequestsStateTransitionHook(hookPoint boil.HookPoint, requestsStateTransitionHook RequestsStateTransitionHook) {
	switch hookPoint {
	case boil.AfterSelectHook:
		requestsStateTransitionAfterSelectHooks = append(requestsStateTransitionAfterSelectHooks, requestsStateTransitionHook)
	case boil.BeforeInsertHook:
		requestsStateTransitionBeforeInsertHooks = append(requestsStateTransitionBeforeInsertHooks, requestsStateTransitionHook)
	case boil.AfterInsertHook:
		requestsStateTransitionAfterInsertHooks = append(requestsStateTransitionAfterInsertHooks, requestsStateTransitionHook)
	case boil.BeforeUpdateHook:
		requestsStateTransitionBeforeUpdateHooks = append(requestsStateTransitionBeforeUpdateHooks, requestsStateTransitionHook)
	case boil.AfterUpdateHook:
		requestsStateTransitionAfterUpdateHooks = append(requestsStateTransitionAfterUpdateHooks, requestsStateTransitionHook)
	case boil.BeforeDeleteHook:
		requestsStateTransitionBeforeDeleteHooks = append(requestsStateTransitionBeforeDeleteHooks, requestsStateTransitionHook)
	case boil.AfterDeleteHook:
		requestsStateTransitionAfterDeleteHooks = append(requestsStateTransitionAfterDeleteHooks, requestsStateTransitionHook)
	case boil.BeforeUpsertHook:
		requestsStateTransitionBeforeUpsertHooks = append(requestsStateTransitionBeforeUpsertHooks, requestsStateTransitionHook)
	case boil.AfterUpsertHook:
		requestsStateTransitionAfterUpsertHooks = append(requestsStateTransitionAfterUpsertHooks, requestsStateTransitionHook)
	}
}

// One returns a single requestsStateTransition record from the query.
func (q requestsStateTransitionQuery) One(ctx context.Context, exec boil.ContextExecutor) (*RequestsStateTransition, error) {
	o := &RequestsStateTransition{}

	queries.SetLimit(q.Query, 1)

	err := q.Bind(ctx, exec, o)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "authorities: failed to execute a one query for requests_state_transitions")
	}

	if err := o.doAfterSelectHooks(ctx, exec); err != nil {
		return o, err
	}

	return o, nil
}

// All returns all RequestsStateTransition records from the query.
func (q requestsStateTransitionQuery) All(ctx context.Context, exec boil.ContextExecutor) (RequestsStateTransitionSlice, error) {
	var o []*RequestsStateTransition

	err := q.Bind(ctx, exec, &o)
	if err != nil {
		return nil, errors.Wrap(err, "authorities: failed to assign all query results to RequestsStateTransition slice")
	}

	if len(requestsStateTransitionAfterSelectHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterSelectHooks(ctx, exec); err != nil {
				return o, err
			}
		}
	}

	return o, nil
}

// Count returns the count of all RequestsStateTransition records in the query.
func (q requestsStateTransitionQuery) Count(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return 0, errors.Wrap(err, "authorities: failed to count requests_state_transitions rows")
	}

	return count, nil
}

// Exists checks if the row exists in the table.
func (q requestsStateTransitionQuery) Exists(ctx context.Context, exec boil.ContextExecutor) (bool, error) {
	var count int64

	queries.SetSelect(q.Query, nil)
	queries.SetCount(q.Query)
	queries.SetLimit(q.Query, 1)

	err := q.Query.QueryRowContext(ctx, exec).Scan(&count)
	if err != nil {
		return false, errors.Wrap(err, "authorities: failed to check if requests_state_transitions exists")
	}

	return count > 0, nil
}

// Request pointed to by the foreign key.
func (o *RequestsStateTransition) Request(mods ...qm.QueryMod) requestQuery {
	queryMods := []qm.QueryMod{
		qm.Where("\"id\" = ?", o.RequestID),
	}

	queryMods = append(queryMods, mods...)

	return Requests(queryMods...)
}

// LoadRequest allows an eager lookup of values, cached into the
// loaded structs of the objects. This is for an N-1 relationship.
func (requestsStateTransitionL) LoadRequest(ctx context.Context, e boil.ContextExecutor, singular bool, maybeRequestsStateTransition interface{}, mods queries.Applicator) error {
	var slice []*RequestsStateTransition
	var object *RequestsStateTransition

	if singular {
		object = maybeRequestsStateTransition.(*RequestsStateTransition)
	} else {
		slice = *maybeRequestsStateTransition.(*[]*RequestsStateTransition)
	}

	args := make([]interface{}, 0, 1)
	if singular {
		if object.R == nil {
			object.R = &requestsStateTransitionR{}
		}
		args = append(args, object.RequestID)

	} else {
	Outer:
		for _, obj := range slice {
			if obj.R == nil {
				obj.R = &requestsStateTransitionR{}
			}

			for _, a := range args {
				if a == obj.RequestID {
					continue Outer
				}
			}

			args = append(args, obj.RequestID)

		}
	}

	if len(args) == 0 {
		return nil
	}

	query := NewQuery(
		qm.From(`authorities.requests`),
		qm.WhereIn(`authorities.requests.id in ?`, args...),
	)
	if mods != nil {
		mods.Apply(query)
	}

	results, err := query.QueryContext(ctx, e)
	if err != nil {
		return errors.Wrap(err, "failed to eager load Request")
	}

	var resultSlice []*Request
	if err = queries.Bind(results, &resultSlice); err != nil {
		return errors.Wrap(err, "failed to bind eager loaded slice Request")
	}

	if err = results.Close(); err != nil {
		return errors.Wrap(err, "failed to close results of eager load for requests")
	}
	if err = results.Err(); err != nil {
		return errors.Wrap(err, "error occurred during iteration of eager loaded relations for requests")
	}

	if len(requestsStateTransitionAfterSelectHooks) != 0 {
		for _, obj := range resultSlice {
			if err := obj.doAfterSelectHooks(ctx, e); err != nil {
				return err
			}
		}
	}

	if len(resultSlice) == 0 {
		return nil
	}

	if singular {
		foreign := resultSlice[0]
		object.R.Request = foreign
		if foreign.R == nil {
			foreign.R = &requestR{}
		}
		foreign.R.RequestsStateTransitions = append(foreign.R.RequestsStateTransitions, object)
		return nil
	}

	for _, local := range slice {
		for _, foreign := range resultSlice {
			if local.RequestID == foreign.ID {
				local.R.Request = foreign
				if foreign.R == nil {
					foreign.R = &requestR{}
				}
				foreign.R.RequestsStateTransitions = append(foreign.R.RequestsStateTransitions, local)
				break
			}
		}
	}

	return nil
}

// SetRequest of the requestsStateTransition to the related item.
// Sets o.R.Request to related.
// Adds o to related.R.RequestsStateTransitions.
func (o *RequestsStateTransition) SetRequest(ctx context.Context, exec boil.ContextExecutor, insert bool, related *Request) error {
	var err error
	if insert {
		if err = related.Insert(ctx, exec, boil.Infer()); err != nil {
			return errors.Wrap(err, "failed to insert into foreign table")
		}
	}

	updateQuery := fmt.Sprintf(
		"UPDATE \"authorities\".\"requests_state_transitions\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, []string{"request_id"}),
		strmangle.WhereClause("\"", "\"", 2, requestsStateTransitionPrimaryKeyColumns),
	)
	values := []interface{}{related.ID, o.RequestID, o.Timestamp}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, updateQuery)
		fmt.Fprintln(writer, values)
	}
	if _, err = exec.ExecContext(ctx, updateQuery, values...); err != nil {
		return errors.Wrap(err, "failed to update local table")
	}

	o.RequestID = related.ID
	if o.R == nil {
		o.R = &requestsStateTransitionR{
			Request: related,
		}
	} else {
		o.R.Request = related
	}

	if related.R == nil {
		related.R = &requestR{
			RequestsStateTransitions: RequestsStateTransitionSlice{o},
		}
	} else {
		related.R.RequestsStateTransitions = append(related.R.RequestsStateTransitions, o)
	}

	return nil
}

// RequestsStateTransitions retrieves all the records using an executor.
func RequestsStateTransitions(mods ...qm.QueryMod) requestsStateTransitionQuery {
	mods = append(mods, qm.From("\"authorities\".\"requests_state_transitions\""))
	q := NewQuery(mods...)
	if len(queries.GetSelect(q)) == 0 {
		queries.SetSelect(q, []string{"\"authorities\".\"requests_state_transitions\".*"})
	}

	return requestsStateTransitionQuery{q}
}

// FindRequestsStateTransition retrieves a single record by ID with an executor.
// If selectCols is empty Find will return all columns.
func FindRequestsStateTransition(ctx context.Context, exec boil.ContextExecutor, requestID string, timestamp time.Time, selectCols ...string) (*RequestsStateTransition, error) {
	requestsStateTransitionObj := &RequestsStateTransition{}

	sel := "*"
	if len(selectCols) > 0 {
		sel = strings.Join(strmangle.IdentQuoteSlice(dialect.LQ, dialect.RQ, selectCols), ",")
	}
	query := fmt.Sprintf(
		"select %s from \"authorities\".\"requests_state_transitions\" where \"request_id\"=$1 AND \"timestamp\"=$2", sel,
	)

	q := queries.Raw(query, requestID, timestamp)

	err := q.Bind(ctx, exec, requestsStateTransitionObj)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, sql.ErrNoRows
		}
		return nil, errors.Wrap(err, "authorities: unable to select from requests_state_transitions")
	}

	if err = requestsStateTransitionObj.doAfterSelectHooks(ctx, exec); err != nil {
		return requestsStateTransitionObj, err
	}

	return requestsStateTransitionObj, nil
}

// Insert a single record using an executor.
// See boil.Columns.InsertColumnSet documentation to understand column list inference for inserts.
func (o *RequestsStateTransition) Insert(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) error {
	if o == nil {
		return errors.New("authorities: no requests_state_transitions provided for insertion")
	}

	var err error

	if err := o.doBeforeInsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(requestsStateTransitionColumnsWithDefault, o)

	key := makeCacheKey(columns, nzDefaults)
	requestsStateTransitionInsertCacheMut.RLock()
	cache, cached := requestsStateTransitionInsertCache[key]
	requestsStateTransitionInsertCacheMut.RUnlock()

	if !cached {
		wl, returnColumns := columns.InsertColumnSet(
			requestsStateTransitionAllColumns,
			requestsStateTransitionColumnsWithDefault,
			requestsStateTransitionColumnsWithoutDefault,
			nzDefaults,
		)

		cache.valueMapping, err = queries.BindMapping(requestsStateTransitionType, requestsStateTransitionMapping, wl)
		if err != nil {
			return err
		}
		cache.retMapping, err = queries.BindMapping(requestsStateTransitionType, requestsStateTransitionMapping, returnColumns)
		if err != nil {
			return err
		}
		if len(wl) != 0 {
			cache.query = fmt.Sprintf("INSERT INTO \"authorities\".\"requests_state_transitions\" (\"%s\") %%sVALUES (%s)%%s", strings.Join(wl, "\",\""), strmangle.Placeholders(dialect.UseIndexPlaceholders, len(wl), 1, 1))
		} else {
			cache.query = "INSERT INTO \"authorities\".\"requests_state_transitions\" %sDEFAULT VALUES%s"
		}

		var queryOutput, queryReturning string

		if len(cache.retMapping) != 0 {
			queryReturning = fmt.Sprintf(" RETURNING \"%s\"", strings.Join(returnColumns, "\",\""))
		}

		cache.query = fmt.Sprintf(cache.query, queryOutput, queryReturning)
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}

	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(queries.PtrsFromMapping(value, cache.retMapping)...)
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}

	if err != nil {
		return errors.Wrap(err, "authorities: unable to insert into requests_state_transitions")
	}

	if !cached {
		requestsStateTransitionInsertCacheMut.Lock()
		requestsStateTransitionInsertCache[key] = cache
		requestsStateTransitionInsertCacheMut.Unlock()
	}

	return o.doAfterInsertHooks(ctx, exec)
}

// Update uses an executor to update the RequestsStateTransition.
// See boil.Columns.UpdateColumnSet documentation to understand column list inference for updates.
// Update does not automatically update the record in case of default values. Use .Reload() to refresh the records.
func (o *RequestsStateTransition) Update(ctx context.Context, exec boil.ContextExecutor, columns boil.Columns) (int64, error) {
	var err error
	if err = o.doBeforeUpdateHooks(ctx, exec); err != nil {
		return 0, err
	}
	key := makeCacheKey(columns, nil)
	requestsStateTransitionUpdateCacheMut.RLock()
	cache, cached := requestsStateTransitionUpdateCache[key]
	requestsStateTransitionUpdateCacheMut.RUnlock()

	if !cached {
		wl := columns.UpdateColumnSet(
			requestsStateTransitionAllColumns,
			requestsStateTransitionPrimaryKeyColumns,
		)

		if !columns.IsWhitelist() {
			wl = strmangle.SetComplement(wl, []string{"created_at"})
		}
		if len(wl) == 0 {
			return 0, errors.New("authorities: unable to update requests_state_transitions, could not build whitelist")
		}

		cache.query = fmt.Sprintf("UPDATE \"authorities\".\"requests_state_transitions\" SET %s WHERE %s",
			strmangle.SetParamNames("\"", "\"", 1, wl),
			strmangle.WhereClause("\"", "\"", len(wl)+1, requestsStateTransitionPrimaryKeyColumns),
		)
		cache.valueMapping, err = queries.BindMapping(requestsStateTransitionType, requestsStateTransitionMapping, append(wl, requestsStateTransitionPrimaryKeyColumns...))
		if err != nil {
			return 0, err
		}
	}

	values := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), cache.valueMapping)

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, values)
	}
	var result sql.Result
	result, err = exec.ExecContext(ctx, cache.query, values...)
	if err != nil {
		return 0, errors.Wrap(err, "authorities: unable to update requests_state_transitions row")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "authorities: failed to get rows affected by update for requests_state_transitions")
	}

	if !cached {
		requestsStateTransitionUpdateCacheMut.Lock()
		requestsStateTransitionUpdateCache[key] = cache
		requestsStateTransitionUpdateCacheMut.Unlock()
	}

	return rowsAff, o.doAfterUpdateHooks(ctx, exec)
}

// UpdateAll updates all rows with the specified column values.
func (q requestsStateTransitionQuery) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	queries.SetUpdate(q.Query, cols)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "authorities: unable to update all for requests_state_transitions")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "authorities: unable to retrieve rows affected for requests_state_transitions")
	}

	return rowsAff, nil
}

// UpdateAll updates all rows with the specified column values, using an executor.
func (o RequestsStateTransitionSlice) UpdateAll(ctx context.Context, exec boil.ContextExecutor, cols M) (int64, error) {
	ln := int64(len(o))
	if ln == 0 {
		return 0, nil
	}

	if len(cols) == 0 {
		return 0, errors.New("authorities: update all requires at least one column argument")
	}

	colNames := make([]string, len(cols))
	args := make([]interface{}, len(cols))

	i := 0
	for name, value := range cols {
		colNames[i] = name
		args[i] = value
		i++
	}

	// Append all of the primary key values for each column
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), requestsStateTransitionPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := fmt.Sprintf("UPDATE \"authorities\".\"requests_state_transitions\" SET %s WHERE %s",
		strmangle.SetParamNames("\"", "\"", 1, colNames),
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), len(colNames)+1, requestsStateTransitionPrimaryKeyColumns, len(o)))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "authorities: unable to update all in requestsStateTransition slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "authorities: unable to retrieve rows affected all in update all requestsStateTransition")
	}
	return rowsAff, nil
}

// Upsert attempts an insert using an executor, and does an update or ignore on conflict.
// See boil.Columns documentation for how to properly use updateColumns and insertColumns.
func (o *RequestsStateTransition) Upsert(ctx context.Context, exec boil.ContextExecutor, updateOnConflict bool, conflictColumns []string, updateColumns, insertColumns boil.Columns) error {
	if o == nil {
		return errors.New("authorities: no requests_state_transitions provided for upsert")
	}

	if err := o.doBeforeUpsertHooks(ctx, exec); err != nil {
		return err
	}

	nzDefaults := queries.NonZeroDefaultSet(requestsStateTransitionColumnsWithDefault, o)

	// Build cache key in-line uglily - mysql vs psql problems
	buf := strmangle.GetBuffer()
	if updateOnConflict {
		buf.WriteByte('t')
	} else {
		buf.WriteByte('f')
	}
	buf.WriteByte('.')
	for _, c := range conflictColumns {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(updateColumns.Kind))
	for _, c := range updateColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	buf.WriteString(strconv.Itoa(insertColumns.Kind))
	for _, c := range insertColumns.Cols {
		buf.WriteString(c)
	}
	buf.WriteByte('.')
	for _, c := range nzDefaults {
		buf.WriteString(c)
	}
	key := buf.String()
	strmangle.PutBuffer(buf)

	requestsStateTransitionUpsertCacheMut.RLock()
	cache, cached := requestsStateTransitionUpsertCache[key]
	requestsStateTransitionUpsertCacheMut.RUnlock()

	var err error

	if !cached {
		insert, ret := insertColumns.InsertColumnSet(
			requestsStateTransitionAllColumns,
			requestsStateTransitionColumnsWithDefault,
			requestsStateTransitionColumnsWithoutDefault,
			nzDefaults,
		)

		update := updateColumns.UpdateColumnSet(
			requestsStateTransitionAllColumns,
			requestsStateTransitionPrimaryKeyColumns,
		)

		if updateOnConflict && len(update) == 0 {
			return errors.New("authorities: unable to upsert requests_state_transitions, could not build update column list")
		}

		conflict := conflictColumns
		if len(conflict) == 0 {
			conflict = make([]string, len(requestsStateTransitionPrimaryKeyColumns))
			copy(conflict, requestsStateTransitionPrimaryKeyColumns)
		}
		cache.query = buildUpsertQueryPostgres(dialect, "\"authorities\".\"requests_state_transitions\"", updateOnConflict, ret, update, conflict, insert)

		cache.valueMapping, err = queries.BindMapping(requestsStateTransitionType, requestsStateTransitionMapping, insert)
		if err != nil {
			return err
		}
		if len(ret) != 0 {
			cache.retMapping, err = queries.BindMapping(requestsStateTransitionType, requestsStateTransitionMapping, ret)
			if err != nil {
				return err
			}
		}
	}

	value := reflect.Indirect(reflect.ValueOf(o))
	vals := queries.ValuesFromMapping(value, cache.valueMapping)
	var returns []interface{}
	if len(cache.retMapping) != 0 {
		returns = queries.PtrsFromMapping(value, cache.retMapping)
	}

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, cache.query)
		fmt.Fprintln(writer, vals)
	}
	if len(cache.retMapping) != 0 {
		err = exec.QueryRowContext(ctx, cache.query, vals...).Scan(returns...)
		if errors.Is(err, sql.ErrNoRows) {
			err = nil // Postgres doesn't return anything when there's no update
		}
	} else {
		_, err = exec.ExecContext(ctx, cache.query, vals...)
	}
	if err != nil {
		return errors.Wrap(err, "authorities: unable to upsert requests_state_transitions")
	}

	if !cached {
		requestsStateTransitionUpsertCacheMut.Lock()
		requestsStateTransitionUpsertCache[key] = cache
		requestsStateTransitionUpsertCacheMut.Unlock()
	}

	return o.doAfterUpsertHooks(ctx, exec)
}

// Delete deletes a single RequestsStateTransition record with an executor.
// Delete will match against the primary key column to find the record to delete.
func (o *RequestsStateTransition) Delete(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if o == nil {
		return 0, errors.New("authorities: no RequestsStateTransition provided for delete")
	}

	if err := o.doBeforeDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	args := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(o)), requestsStateTransitionPrimaryKeyMapping)
	sql := "DELETE FROM \"authorities\".\"requests_state_transitions\" WHERE \"request_id\"=$1 AND \"timestamp\"=$2"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args...)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "authorities: unable to delete from requests_state_transitions")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "authorities: failed to get rows affected by delete for requests_state_transitions")
	}

	if err := o.doAfterDeleteHooks(ctx, exec); err != nil {
		return 0, err
	}

	return rowsAff, nil
}

// DeleteAll deletes all matching rows.
func (q requestsStateTransitionQuery) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if q.Query == nil {
		return 0, errors.New("authorities: no requestsStateTransitionQuery provided for delete all")
	}

	queries.SetDelete(q.Query)

	result, err := q.Query.ExecContext(ctx, exec)
	if err != nil {
		return 0, errors.Wrap(err, "authorities: unable to delete all from requests_state_transitions")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "authorities: failed to get rows affected by deleteall for requests_state_transitions")
	}

	return rowsAff, nil
}

// DeleteAll deletes all rows in the slice, using an executor.
func (o RequestsStateTransitionSlice) DeleteAll(ctx context.Context, exec boil.ContextExecutor) (int64, error) {
	if len(o) == 0 {
		return 0, nil
	}

	if len(requestsStateTransitionBeforeDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doBeforeDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	var args []interface{}
	for _, obj := range o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), requestsStateTransitionPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "DELETE FROM \"authorities\".\"requests_state_transitions\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, requestsStateTransitionPrimaryKeyColumns, len(o))

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, args)
	}
	result, err := exec.ExecContext(ctx, sql, args...)
	if err != nil {
		return 0, errors.Wrap(err, "authorities: unable to delete all from requestsStateTransition slice")
	}

	rowsAff, err := result.RowsAffected()
	if err != nil {
		return 0, errors.Wrap(err, "authorities: failed to get rows affected by deleteall for requests_state_transitions")
	}

	if len(requestsStateTransitionAfterDeleteHooks) != 0 {
		for _, obj := range o {
			if err := obj.doAfterDeleteHooks(ctx, exec); err != nil {
				return 0, err
			}
		}
	}

	return rowsAff, nil
}

// Reload refetches the object from the database
// using the primary keys with an executor.
func (o *RequestsStateTransition) Reload(ctx context.Context, exec boil.ContextExecutor) error {
	ret, err := FindRequestsStateTransition(ctx, exec, o.RequestID, o.Timestamp)
	if err != nil {
		return err
	}

	*o = *ret
	return nil
}

// ReloadAll refetches every row with matching primary key column values
// and overwrites the original object slice with the newly updated slice.
func (o *RequestsStateTransitionSlice) ReloadAll(ctx context.Context, exec boil.ContextExecutor) error {
	if o == nil || len(*o) == 0 {
		return nil
	}

	slice := RequestsStateTransitionSlice{}
	var args []interface{}
	for _, obj := range *o {
		pkeyArgs := queries.ValuesFromMapping(reflect.Indirect(reflect.ValueOf(obj)), requestsStateTransitionPrimaryKeyMapping)
		args = append(args, pkeyArgs...)
	}

	sql := "SELECT \"authorities\".\"requests_state_transitions\".* FROM \"authorities\".\"requests_state_transitions\" WHERE " +
		strmangle.WhereClauseRepeated(string(dialect.LQ), string(dialect.RQ), 1, requestsStateTransitionPrimaryKeyColumns, len(*o))

	q := queries.Raw(sql, args...)

	err := q.Bind(ctx, exec, &slice)
	if err != nil {
		return errors.Wrap(err, "authorities: unable to reload all in RequestsStateTransitionSlice")
	}

	*o = slice

	return nil
}

// RequestsStateTransitionExists checks if the RequestsStateTransition row exists.
func RequestsStateTransitionExists(ctx context.Context, exec boil.ContextExecutor, requestID string, timestamp time.Time) (bool, error) {
	var exists bool
	sql := "select exists(select 1 from \"authorities\".\"requests_state_transitions\" where \"request_id\"=$1 AND \"timestamp\"=$2 limit 1)"

	if boil.IsDebug(ctx) {
		writer := boil.DebugWriterFrom(ctx)
		fmt.Fprintln(writer, sql)
		fmt.Fprintln(writer, requestID, timestamp)
	}
	row := exec.QueryRowContext(ctx, sql, requestID, timestamp)

	err := row.Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "authorities: unable to check if requests_state_transitions exists")
	}

	return exists, nil
}
