package application

import (
	"encoding/json"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/telematics"
)

type StateMetadata struct {
	Description   string
	Time          time.Time
	PreviousState string
}

type Submission[T AppInfo] struct {
	ID            uuid.UUID
	ApplicationID uuid.UUID

	Bindable bool

	Info                 T
	UnderwriterID        uuid.UUID
	SelectedIndicationID uuid.UUID
	IndicationIDs        []uuid.UUID

	SelectedQuotingPricingContextID uuid.UUID
	QuotingPricingContextIDs        []uuid.UUID

	JobRunInfo *jtypes.JobRunId

	AppPDFHandleID        *uuid.UUID
	QuotePDFHandleID      *uuid.UUID
	SignaturePacketFormID *uuid.UUID
	PolicyFormID          *uuid.UUID

	CreatedAt     time.Time
	UpdatedAt     time.Time
	DataContextId *uuid.UUID
}

type CoverageSelection struct {
	IsRequired bool
	Deductible *int
	Limit      *int
}

type EffectiveDates struct {
	Start time.Time
	End   time.Time
}

// UnderwriterInput captures the additional overrides which UWs can provide
type UnderwriterInput struct {
	ScheduleModAPD float64
	ScheduleModMTC float64
}

type Address struct {
	Street  string
	City    string
	State   string
	ZipCode string
}

type CoverageDetails struct {
	CoverageType          appEnums.Coverage
	Label                 string
	IsRequired            bool
	Limit                 *int
	Deductible            *int
	Premium               *int
	PremiumPerUnit        *int
	SymbolsAndDefinitions *[]application.SymbolAndDefinition
}

type LossInfo struct {
	HasLoss        *bool
	HasLossOver20k *bool
	Files          []application.FileMetadata `copier:"-"`
}

type ClassInfo struct {
	OwnsRestrictedClasses *bool
	PrimaryOperatingClass *appEnums.OperatingClass
}

type CommodityInfo struct {
	HaulsRestrictedCommodities *bool
	CommodityDistribution      []CommodityRecord
}

func (c *CommodityInfo) PrimaryCommodity() *CommodityRecord {
	// Get the commodityRecord with the maximum percentage.
	var maxPercentage int
	var maxPercentageCommodity *CommodityRecord
	for i := range c.CommodityDistribution {
		if c.CommodityDistribution[i].Percentage > maxPercentage {
			maxPercentage = c.CommodityDistribution[i].Percentage
			maxPercentageCommodity = &c.CommodityDistribution[i]
		}
	}
	return maxPercentageCommodity
}

type CommodityRecord struct {
	Category      enums.Category
	CategoryLabel string
	Name          string
	Percentage    int
}

type DriverBasicDetails struct {
	FirstName     string
	LastName      string
	LicenseState  string
	LicenseNumber string
	DateOfBirth   time.Time
	DateOfHire    time.Time
}

type DriverDetails struct {
	DriverBasicDetails
	LicenseIssueYear int
	OwnerOperator    bool
	IsIncluded       bool
	Violations       Violations
	IsOutOfState     bool
	YearsOfExp       int
}

type Violations struct {
	Total                    *int
	RecklessnessOrPhoneUsage *int
	Severe                   *int
	AtFaultAccidents         *int
}

type TSPInfo struct {
	TSPEnum         *telematics.TSP
	TSPConnHandleId uuid.UUID
	TelematicsInfo  *application.TelematicsInfo
	InsuredInfo     InsuredInfo
	// SelectedPremierProvider and SelectedProviderType
	// are used for the pre-quote telematics experiment
	SelectedPremierProvider *telematics.TSP
	SelectedProviderType    *telematics.SelectedProviderType
}

type ClosureInfo struct {
	PrimaryReason   *string
	SecondaryReason *string
	Comments        string
	WinCarrier      *string
}

type InsuredInfo struct {
	Name  string
	Email string
}

type RMLVehicle struct {
	ComputedFields map[string]interface{}
}

type RMLCompany struct {
	ComputedFields map[string]interface{}
}

func (r *RMLVehicle) GetFieldValue(field RMLVehicleField) interface{} {
	if val, ok := r.ComputedFields[string(field)]; ok {
		return val
	}
	return nil
}

type Options struct {
	PageSize                *int
	Query                   *string
	Tab                     *nonfleet.ApplicationTab
	EffectiveDateOnOrAfter  *time.Time
	EffectiveDateOnOrBefore *time.Time
	AgencyID                *uuid.UUID
}

type ModelPinConfigInfo struct {
	RateML RateMLConfig
}

type RateMLConfig struct {
	Provider rtypes.RatemlModelProvider
	USState  us_states.USState
	Version  rtypes.RatemlModelVersion
	Flags    RateMLFlags
}

type RateMLFlags struct {
	// IsSpareTrailerEnabledForAllCoverages is a flag to enable spare trailer logic in rateML
	// adaptor for all coverages and not just APD. This will remain false by default for all the previous apps
	// and submissions & for newer apps we set this to true. This will ensure backwards compatibility
	IsSpareTrailerEnabledForAllCoverages bool
}

func NewModelPinConfigInfo(
	provider rtypes.RatemlModelProvider,
	usState us_states.USState,
	version rtypes.RatemlModelVersion,
) ModelPinConfigInfo {
	return ModelPinConfigInfo{
		RateML: RateMLConfig{
			Provider: provider,
			USState:  usState,
			Version:  version,
			Flags: RateMLFlags{
				IsSpareTrailerEnabledForAllCoverages: true,
			},
		},
	}
}

func (c *RateMLConfig) UnmarshalJSON(bytes []byte) error {
	type alias RateMLConfig
	aux := &struct {
		USState *string
		*alias
	}{
		alias: (*alias)(c),
	}
	if err := json.Unmarshal(bytes, &aux); err != nil {
		return errors.Wrap(err, "unable to unmarshal into aux rate ml info")
	}

	if aux.USState != nil {
		if *aux.USState == "" {
			c.USState = us_states.InvalidStateCode
		} else {
			usState, err := us_states.StrToUSState(*aux.USState)
			if err != nil {
				return errors.Wrapf(err, "unable to parse %s us state for rate ml info", *aux.USState)
			}
			c.USState = usState
		}
	}
	return nil
}

type RMLVehicleField string

const (
	CollVehPremium       RMLVehicleField = "collVehPremium"
	CompVehPremium       RMLVehicleField = "compVehPremium"
	LiabVehPremium       RMLVehicleField = "liabVehPremium"
	StatedValueTIV       RMLVehicleField = "statedValueTIV"
	TrlintCompVehPremium RMLVehicleField = "trlintCompVehPremium"
	TrlintCollVehPremium RMLVehicleField = "trlintCollVehPremium"
	TlsVehPremium        RMLVehicleField = "tlsVehPremium"
	RentalVehPremium     RMLVehicleField = "rentalVehPremium"
)

type SurplusTaxInfo struct {
	// LiabAPDPlusFlatSurplusLinesTax represents the surplus lines tax for AL, APD, and flat fee combined.
	LiabAPDPlusFlatSurplusLinesTax *float64
	// GLSurplusLinesTax represents the surplus lines tax for GL.
	GLSurplusLinesTax *float64
	// MTCSurplusLinesTax represents the surplus lines tax for MTC.
	MTCSurplusLinesTax *float64
	// LiabAPDPlusFlatSurplusStampingFee represents the stamping fee for AL, APD, and flat fee combined.
	LiabAPDPlusFlatSurplusStampingFee *float64
	// GLSurplusStampingFee represents the stamping fee for GL.
	GLSurplusStampingFee *float64
	// MTCSurplusStampingFee represents the stamping fee for MTC.
	MTCSurplusStampingFee *float64
}
