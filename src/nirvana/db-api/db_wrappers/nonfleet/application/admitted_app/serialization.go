package admitted_app

import (
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/type_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	nfApp "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admittedEnums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
)

var OAPIPrimaryCoverages = []common.CoverageType{
	common.CoverageAutoLiability,
	common.CoverageAutoPhysicalDamage,
	common.CoverageMotorTruckCargo,
	common.CoverageGeneralLiability,
}

func bindOperationsFormRestToDB(
	operationsForm *nonfleet.AdmittedAppOperationsForm,
	a *AdmittedApp,
) error {
	if operationsForm == nil {
		return nil
	}

	// Effective Date
	if operationsForm.EffectiveDate != nil {
		effectiveDate, err := time.Parse("2006-01-02", (*operationsForm.EffectiveDate).String())
		if err != nil {
			return errors.Wrap(err, "couldn't parse effective date")
		}
		a.CoverageInfo.EffectiveDate = effectiveDate
		a.CoverageInfo.EffectiveDateTo = time_utils.AddYears(effectiveDate, 1)
	}

	// AtFaultAccidents
	a.LossInfo.AtFaultLosses = type_utils.GetValueOrDefault(operationsForm.AtFaultAccidents, a.LossInfo.AtFaultLosses)

	if operationsForm.LossRunFiles != nil {
		files, err := bindFileMetadataFromRestToDB(*operationsForm.LossRunFiles)
		if err != nil {
			return errors.Wrap(err, "couldn't bind loss file metadata from rest to db")
		}
		a.LossInfo.Files = files
	}

	// BusinessOwner
	if operationsForm.BusinessOwner != nil {
		businessOwner := *operationsForm.BusinessOwner
		dob, err := time.Parse("2006-01-02", businessOwner.DateOfBirth)
		if err != nil {
			return errors.Wrap(err, "couldn't parse businessOwner's DateOfBirth")
		}
		a.CompanyInfo.BusinessOwner = BusinessOwner{
			Firstname:   businessOwner.FirstName,
			Lastname:    businessOwner.LastName,
			DateOfBirth: dob,
			Address: nfApp.Address{
				Street:  businessOwner.Address.Street,
				City:    businessOwner.Address.City,
				State:   businessOwner.Address.State,
				ZipCode: businessOwner.Address.Zip,
			},
			DriverOnPolicy: businessOwner.DriverOnPolicy,
		}
	}

	// PrimaryCommodity
	if operationsForm.PrimaryCommodity != nil && *operationsForm.PrimaryCommodity != "" {
		category, err := admittedEnums.AdmittedCommodityString(string(*operationsForm.PrimaryCommodity))
		if err != nil {
			return errors.Wrapf(err, "couldn't parse the category: %s", string(*operationsForm.PrimaryCommodity))
		}
		a.CommodityInfo.PrimaryCommodity = pointer_utils.ToPointer(category)
	}

	// CommodityDistribution
	if operationsForm.CommodityDistribution != nil && len(*operationsForm.CommodityDistribution) != 0 {
		oapiCommDistribution := *operationsForm.CommodityDistribution
		commodityDistribution := make([]CommodityRecord, 0)

		primaryCommodityPercent := 0
		primaryCommodityIndex := 0
		seenCategories := make(map[admittedEnums.AdmittedCommodity]struct{})
		for idx, c := range oapiCommDistribution {
			commodity, err := admittedEnums.AdmittedCommodityString(string(c.Category))
			if err != nil {
				return errors.Wrapf(err, "couldn't parse commodity: %s", c.Category)
			}
			if _, ok := seenCategories[commodity]; ok {
				return errors.Newf("duplicate commodity %s in commodity distribution", commodity)
			}
			seenCategories[commodity] = struct{}{}

			commodityDistribution = append(commodityDistribution, CommodityRecord{
				Category:      commodity,
				CategoryLabel: c.CategoryLabel,
				Name:          c.Name,
				Percentage:    c.Percentage,
			})

			// Find the highest percentage commodity
			if primaryCommodityPercent < c.Percentage {
				primaryCommodityIndex = idx
				primaryCommodityPercent = c.Percentage
			}
		}

		a.CommodityInfo.CommodityDistribution = commodityDistribution
		a.CommodityInfo.PrimaryCommodity = pointer_utils.ToPointer(commodityDistribution[primaryCommodityIndex].Category)
	}

	// Coverages
	if operationsForm.Coverages != nil {
		primaryCovs := make([]nfApp.CoverageDetails, 0)
		for _, c := range *operationsForm.Coverages {
			cov, err := bindCoverageFromRestToDB(c)
			if err != nil {
				return errors.Wrap(err, "couldn't bind primary coverage")
			}
			if slice_utils.Contains(OAPIPrimaryCoverages, c.CoverageType) {
				primaryCovs = append(primaryCovs, *cov)
			} else {
				return errors.Newf("Coverage %s is not a primary coverage", cov.CoverageType)
			}
		}

		a.CoverageInfo.PrimaryCovs = primaryCovs
	}

	// MaxRadiusOfOperation
	if operationsForm.MaxRadiusOfOperation != nil {
		maxRadius, err := admittedEnums.MaxRadiusOfOperationString(string(*operationsForm.MaxRadiusOfOperation))
		if err != nil {
			return errors.Wrap(err, "couldn't parse the max radius of operation")
		}
		a.ClassInfo.MaxRadiusOfOperation = maxRadius
	}

	// PrimaryCategory
	a.CommodityInfo.PrimaryCategory = type_utils.GetValueOrDefault(operationsForm.PrimaryCategory, "")

	// TerminalLocation
	if operationsForm.TerminalLocation != nil {
		address := nfApp.Address{
			Street:  operationsForm.TerminalLocation.Address.Street,
			City:    operationsForm.TerminalLocation.Address.City,
			State:   operationsForm.TerminalLocation.Address.State,
			ZipCode: operationsForm.TerminalLocation.Address.Zip,
		}
		if operationsForm.TerminalLocation.SameAsPhysicalAddress {
			address = a.CompanyInfo.BusinessOwner.Address
		}
		terminalLocation := TerminalLocation{
			SameAsPhysicalAddress: operationsForm.TerminalLocation.SameAsPhysicalAddress,
			Address:               address,
		}
		a.CompanyInfo.TerminalLocation = &terminalLocation
		state, err := us_states.StrToUSState(address.State)
		if err != nil {
			return errors.Wrap(err, "couldn't parse terminal location state")
		}
		// Update company state to terminal location state
		// This replaces the previous use of FMCSA mailing address state
		// for improved accuracy in the risk representation
		a.CompanyInfo.USState = state
	}

	a.CoverageInfo.HasHiredAuto = ContainsHiredAuto(*operationsForm)

	return nil
}

func bindEquipmentsFormRestToDB(
	equipmentsForm *nonfleet.AdmittedAppEquipmentsForm,
	a *AdmittedApp,
) error {
	if equipmentsForm == nil {
		return nil
	}

	if equipmentsForm.Vehicles != nil {
		vehicles := make([]VehicleDetails, 0)
		for _, v := range *equipmentsForm.Vehicles {
			var convertedVehicle VehicleDetails
			err := convertedVehicle.FromRest(&v)
			if err != nil {
				return errors.Wrap(err, "couldn't bind vehicle")
			}
			vehicles = append(vehicles, convertedVehicle)
		}

		a.EquipmentInfo.Vehicles = vehicles
	}
	return nil
}

func bindDriversFormRestToDB(
	driversForm *nonfleet.AdmittedAppDriversForm,
	a *AdmittedApp,
) error {
	if driversForm == nil {
		return nil
	}

	if driversForm.Drivers != nil {
		drivers := make([]DriverDetails, 0)
		for _, d := range *driversForm.Drivers {
			var convertedDriver DriverDetails
			err := convertedDriver.FromRest(&d, a.CompanyInfo.USState)
			if err != nil {
				return errors.Wrap(err, "couldn't bind driver")
			}

			drivers = append(drivers, convertedDriver)
		}

		a.DriverInfo.Drivers = drivers
	}
	return nil
}

func bindCoveragesFormRestToDB(
	coveragesForm *nonfleet.AdmittedAppCoverageForm,
	a *AdmittedApp,
) error {
	if coveragesForm == nil {
		return nil
	}

	var (
		alCov  = coveragesForm.CoverageAutoLiability
		apdCov = coveragesForm.CoverageAutoPhysicalDamage
		glCov  = coveragesForm.CoverageGeneralLiability
		mtcCov = coveragesForm.CoverageMotorTruckCargo
	)
	covs := make([]nfApp.CoverageDetails, 0)
	for _, c := range a.CoverageInfo.PrimaryCovs {
		if c.CoverageType == appEnums.CoverageAutoLiability && alCov != nil {
			if alCov.Limit != nil {
				c.Limit = alCov.Limit
			}
			if alCov.Deductible != nil {
				c.Deductible = alCov.Deductible
			}
		}

		if c.CoverageType == appEnums.CoverageAutoPhysicalDamage && apdCov != nil {
			if apdCov.Limit != nil {
				c.Limit = apdCov.Limit
			}
			if apdCov.Deductible != nil {
				c.Deductible = apdCov.Deductible
			}
		}

		if c.CoverageType == appEnums.CoverageGeneralLiability && glCov != nil {
			if glCov.Limit != nil {
				c.Limit = glCov.Limit
			}
			if glCov.Deductible != nil {
				c.Deductible = glCov.Deductible
			}
		}

		if c.CoverageType == appEnums.CoverageMotorTruckCargo && mtcCov != nil {
			if mtcCov.Limit != nil {
				c.Limit = mtcCov.Limit
			}
			if mtcCov.Deductible != nil {
				c.Deductible = mtcCov.Deductible
			}
		}
		covs = append(covs, c)
	}

	a.CoverageInfo.PrimaryCovs = covs

	if coveragesForm.IsAPDMTCDeductibleCombined != nil {
		apdRequired := a.CoverageInfo.ContainsCoverageInPrimaryCoveragesAndIsRequired(appEnums.CoverageAutoPhysicalDamage)
		mtcRequired := a.CoverageInfo.ContainsCoverageInPrimaryCoveragesAndIsRequired(appEnums.CoverageMotorTruckCargo)

		if apdRequired && mtcRequired {
			a.CoverageInfo.IsAPDMTCDeductibleCombined = *coveragesForm.IsAPDMTCDeductibleCombined
		} else {
			a.CoverageInfo.IsAPDMTCDeductibleCombined = false
		}
	}

	return nil
}

func bindCoverageFromRestToDB(cov nonfleet.CoverageDetails) (*nfApp.CoverageDetails, error) {
	covType, err := appEnums.CoverageString(string(cov.CoverageType))
	if err != nil {
		return nil, errors.Wrapf(err, "couldn't parse coverage: %s", cov.CoverageType)
	}
	covForDB := nfApp.CoverageDetails{
		CoverageType: covType,
		Label:        str_utils.PrettyEnumString(covType.String(), "Coverage"),
		IsRequired:   cov.IsRequired,
		Limit:        cov.Limit,
		Deductible:   cov.Deductible,
	}

	return &covForDB, nil
}

func bindFileMetadataFromRestToDB(
	files []common.FileMetadata,
) ([]application.FileMetadata, error) {
	retval := make([]application.FileMetadata, 0)

	for _, f := range files {
		handleID := uuid.Nil
		var err error
		if f.Handle != nil {
			handleID, err = uuid.Parse(*f.Handle)
			if err != nil {
				return retval, errors.Wrap(err, "couldn't parse handleID")
			}
		}
		retval = append(retval, application.FileMetadata{
			Name:   f.Name,
			Handle: pointer_utils.UUID(handleID),
		})
	}

	return retval, nil
}
