load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "admitted_app",
    srcs = [
        "admitted_wrapper_mock.go",
        "app.go",
        "commodity_utils.go",
        "constants.go",
        "converters.go",
        "deserialization.go",
        "doc.go",
        "equipment_info.go",
        "fx.go",
        "handler_utils.go",
        "indication_option.go",
        "mock_utils.go",
        "object_defs.go",
        "serialization.go",
        "transformer.go",
        "utils.go",
        "validator.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/email_utils",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/random_utils",
        "//nirvana/common-go/retry",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/type_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/common-go/zip_code_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_models/non_fleet",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/common",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/common",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/rule_runs",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/sharing",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/jobber/jtypes",
        "//nirvana/nirvanaapp/enums",
        "//nirvana/nirvanaapp/models/application",
        "//nirvana/nonfleet/coverages/admitted",
        "//nirvana/nonfleet/model",
        "//nirvana/openapi-specs/components/application",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/policy_common/constants",
        "//nirvana/rating/models/models_release",
        "//nirvana/rating/rtypes",
        "//nirvana/sharing",
        "//nirvana/telematics",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "admitted_app_test",
    srcs = [
        "app_test.go",
        "converters_test.go",
        "serde_test.go",
        "utils_test.go",
        "validator_test.go",
        "wrapper_test.go",
    ],
    embed = [":admitted_app"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/test_utils/builders/admitted_app",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/infra/fx/testloader",
        "//nirvana/nirvanaapp/enums",
        "//nirvana/nirvanaapp/models/application",
        "//nirvana/openapi-specs/components/nonfleet",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
