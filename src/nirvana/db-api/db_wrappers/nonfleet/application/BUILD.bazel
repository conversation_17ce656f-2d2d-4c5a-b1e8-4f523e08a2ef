load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "application",
    srcs = [
        "app_def.go",
        "authz_resource.go",
        "company_info.go",
        "coverage_info.go",
        "db_operations.go",
        "equipment_info.go",
        "interfaces.go",
        "object_defs.go",
        "serde_utils.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/retry",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_models/non_fleet",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/common",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/common",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/sharing",
        "//nirvana/infra/authz",
        "//nirvana/jobber/jtypes",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/openapi-specs/components/nonfleet_underwriting",
        "//nirvana/policy_common/constants",
        "//nirvana/rating/rtypes",
        "//nirvana/telematics",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
    ],
)
