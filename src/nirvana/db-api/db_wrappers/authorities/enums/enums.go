package enums

//go:generate go run github.com/dmarkham/enumer -type=RequestType -json -text -trimprefix=RequestType
type RequestType int

const (
	RequestTypeInvalid RequestType = iota
	RequestTypeDecline
	RequestTypeApprove
	RequestTypeClose
)

//go:generate go run github.com/dmarkham/enumer -type=RequestState -json -text -trimprefix=RequestState
type RequestState int

const (
	RequestStateInvalid RequestState = iota
	RequestStateDraft
	RequestStatePending
	RequestStateApproved
	RequestStateRejected
	RequestStateExecuted
	RequestStateCancelled
)
