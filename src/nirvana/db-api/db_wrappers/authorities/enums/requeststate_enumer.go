// Code generated by "enumer -type=RequestState -json -text -trimprefix=RequestState"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _RequestStateName = "InvalidDraftPendingApprovedRejectedExecutedCancelled"

var _RequestStateIndex = [...]uint8{0, 7, 12, 19, 27, 35, 43, 52}

const _RequestStateLowerName = "invaliddraftpendingapprovedrejectedexecutedcancelled"

func (i RequestState) String() string {
	if i < 0 || i >= RequestState(len(_RequestStateIndex)-1) {
		return fmt.Sprintf("RequestState(%d)", i)
	}
	return _RequestStateName[_RequestStateIndex[i]:_RequestStateIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _RequestStateNoOp() {
	var x [1]struct{}
	_ = x[RequestStateInvalid-(0)]
	_ = x[RequestStateDraft-(1)]
	_ = x[RequestStatePending-(2)]
	_ = x[RequestStateApproved-(3)]
	_ = x[RequestStateRejected-(4)]
	_ = x[RequestStateExecuted-(5)]
	_ = x[RequestStateCancelled-(6)]
}

var _RequestStateValues = []RequestState{RequestStateInvalid, RequestStateDraft, RequestStatePending, RequestStateApproved, RequestStateRejected, RequestStateExecuted, RequestStateCancelled}

var _RequestStateNameToValueMap = map[string]RequestState{
	_RequestStateName[0:7]:        RequestStateInvalid,
	_RequestStateLowerName[0:7]:   RequestStateInvalid,
	_RequestStateName[7:12]:       RequestStateDraft,
	_RequestStateLowerName[7:12]:  RequestStateDraft,
	_RequestStateName[12:19]:      RequestStatePending,
	_RequestStateLowerName[12:19]: RequestStatePending,
	_RequestStateName[19:27]:      RequestStateApproved,
	_RequestStateLowerName[19:27]: RequestStateApproved,
	_RequestStateName[27:35]:      RequestStateRejected,
	_RequestStateLowerName[27:35]: RequestStateRejected,
	_RequestStateName[35:43]:      RequestStateExecuted,
	_RequestStateLowerName[35:43]: RequestStateExecuted,
	_RequestStateName[43:52]:      RequestStateCancelled,
	_RequestStateLowerName[43:52]: RequestStateCancelled,
}

var _RequestStateNames = []string{
	_RequestStateName[0:7],
	_RequestStateName[7:12],
	_RequestStateName[12:19],
	_RequestStateName[19:27],
	_RequestStateName[27:35],
	_RequestStateName[35:43],
	_RequestStateName[43:52],
}

// RequestStateString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func RequestStateString(s string) (RequestState, error) {
	if val, ok := _RequestStateNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _RequestStateNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to RequestState values", s)
}

// RequestStateValues returns all values of the enum
func RequestStateValues() []RequestState {
	return _RequestStateValues
}

// RequestStateStrings returns a slice of all String values of the enum
func RequestStateStrings() []string {
	strs := make([]string, len(_RequestStateNames))
	copy(strs, _RequestStateNames)
	return strs
}

// IsARequestState returns "true" if the value is listed in the enum definition. "false" otherwise
func (i RequestState) IsARequestState() bool {
	for _, v := range _RequestStateValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for RequestState
func (i RequestState) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for RequestState
func (i *RequestState) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("RequestState should be a string, got %s", data)
	}

	var err error
	*i, err = RequestStateString(s)
	return err
}

// MarshalText implements the encoding.TextMarshaler interface for RequestState
func (i RequestState) MarshalText() ([]byte, error) {
	return []byte(i.String()), nil
}

// UnmarshalText implements the encoding.TextUnmarshaler interface for RequestState
func (i *RequestState) UnmarshalText(text []byte) error {
	var err error
	*i, err = RequestStateString(string(text))
	return err
}
