// Code generated by "enumer -type=RequestType -json -text -trimprefix=RequestType"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _RequestTypeName = "InvalidDeclineApproveClose"

var _RequestTypeIndex = [...]uint8{0, 7, 14, 21, 26}

const _RequestTypeLowerName = "invaliddeclineapproveclose"

func (i RequestType) String() string {
	if i < 0 || i >= RequestType(len(_RequestTypeIndex)-1) {
		return fmt.Sprintf("RequestType(%d)", i)
	}
	return _RequestTypeName[_RequestTypeIndex[i]:_RequestTypeIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _RequestTypeNoOp() {
	var x [1]struct{}
	_ = x[RequestTypeInvalid-(0)]
	_ = x[RequestTypeDecline-(1)]
	_ = x[RequestTypeApprove-(2)]
	_ = x[RequestTypeClose-(3)]
}

var _RequestTypeValues = []RequestType{RequestTypeInvalid, RequestTypeDecline, RequestTypeApprove, RequestTypeClose}

var _RequestTypeNameToValueMap = map[string]RequestType{
	_RequestTypeName[0:7]:        RequestTypeInvalid,
	_RequestTypeLowerName[0:7]:   RequestTypeInvalid,
	_RequestTypeName[7:14]:       RequestTypeDecline,
	_RequestTypeLowerName[7:14]:  RequestTypeDecline,
	_RequestTypeName[14:21]:      RequestTypeApprove,
	_RequestTypeLowerName[14:21]: RequestTypeApprove,
	_RequestTypeName[21:26]:      RequestTypeClose,
	_RequestTypeLowerName[21:26]: RequestTypeClose,
}

var _RequestTypeNames = []string{
	_RequestTypeName[0:7],
	_RequestTypeName[7:14],
	_RequestTypeName[14:21],
	_RequestTypeName[21:26],
}

// RequestTypeString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func RequestTypeString(s string) (RequestType, error) {
	if val, ok := _RequestTypeNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _RequestTypeNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to RequestType values", s)
}

// RequestTypeValues returns all values of the enum
func RequestTypeValues() []RequestType {
	return _RequestTypeValues
}

// RequestTypeStrings returns a slice of all String values of the enum
func RequestTypeStrings() []string {
	strs := make([]string, len(_RequestTypeNames))
	copy(strs, _RequestTypeNames)
	return strs
}

// IsARequestType returns "true" if the value is listed in the enum definition. "false" otherwise
func (i RequestType) IsARequestType() bool {
	for _, v := range _RequestTypeValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for RequestType
func (i RequestType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for RequestType
func (i *RequestType) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("RequestType should be a string, got %s", data)
	}

	var err error
	*i, err = RequestTypeString(s)
	return err
}

// MarshalText implements the encoding.TextMarshaler interface for RequestType
func (i RequestType) MarshalText() ([]byte, error) {
	return []byte(i.String()), nil
}

// UnmarshalText implements the encoding.TextUnmarshaler interface for RequestType
func (i *RequestType) UnmarshalText(text []byte) error {
	var err error
	*i, err = RequestTypeString(string(text))
	return err
}
