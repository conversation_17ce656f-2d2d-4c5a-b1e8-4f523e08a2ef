package authorities

import (
	"context"
	"database/sql"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"nirvanatech.com/nirvana/common-go/postgres_utils"
	db_api "nirvanatech.com/nirvana/db-api"
	authorities_db_models "nirvanatech.com/nirvana/db-api/db_models/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
)

// DataWrapperImpl implements the AuthorityRequestWrapper interface
type DataWrapperImpl struct {
	db db_api.NirvanaRW
}

// NewDataWrapperImpl creates a new DataWrapperImpl
func NewDataWrapperImpl(db db_api.NirvanaRW) *DataWrapperImpl {
	return &DataWrapperImpl{db: db}
}

// CreateRequest creates a new authority request and records the initial state transition
func (w *DataWrapperImpl) CreateRequest(ctx context.Context, request AuthorityRequest) (*AuthorityRequest, error) {
	// Set timestamps
	now := time.Now()
	request.CreatedAt = now
	request.UpdatedAt = now

	// Ensure we have a UUID
	if request.ID == uuid.Nil {
		request.ID = uuid.New()
	}

	// Default to draft state if not set
	if request.State == enums.RequestStateInvalid {
		request.State = enums.RequestStateDraft
	}

	err := postgres_utils.WrapInTxContext(ctx, w.db.DB, func(txCtx context.Context, tx *sql.Tx) error {
		// Insert the request
		requestDB, err := AuthorityRequestToDB(request)
		if err != nil {
			return errors.Wrap(err, "failed to convert request to DB model")
		}

		err = requestDB.Insert(txCtx, tx, boil.Infer())
		if err != nil {
			return errors.Wrapf(err, "failed to insert authority request %s", request.ID)
		}

		// Record initial state transition
		transition := AuthorityRequestStateTransition{
			RequestID: request.ID,
			Timestamp: now,
			FromState: enums.RequestStateInvalid, // No previous state for new requests
			ToState:   request.State,
		}

		transitionDB, err := AuthorityRequestStateTransitionToDB(transition)
		if err != nil {
			return errors.Wrap(err, "failed to convert state transition to DB model")
		}

		err = transitionDB.Insert(txCtx, tx, boil.Infer())
		if err != nil {
			return errors.Wrapf(err, "failed to insert initial state transition for request %s", request.ID)
		}

		return nil
	}, postgres_utils.IsolationSerializable, postgres_utils.MaxSerializationRetries(5))
	if err != nil {
		return nil, errors.Wrap(err, "failed to create authority request")
	}

	return &request, nil
}

// GetRequest retrieves an authority request by ID
func (w *DataWrapperImpl) GetRequest(ctx context.Context, id uuid.UUID) (*AuthorityRequest, error) {
	requestDB, err := authorities_db_models.FindRequest(ctx, w.db.DB, id.String())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors.Wrapf(err, "authority request not found: %s", id)
		}
		return nil, errors.Wrapf(err, "failed to fetch authority request: %s", id)
	}

	request, err := AuthorityRequestFromDB(*requestDB)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to convert authority request from DB: %s", id)
	}

	return request, nil
}

// GetRequests retrieves requests with flexible filtering using qm queries
func (w *DataWrapperImpl) GetRequests(ctx context.Context, mods ...qm.QueryMod) ([]*AuthorityRequest, error) {
	requestsDB, err := authorities_db_models.Requests(mods...).All(ctx, w.db.DB)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch requests")
	}

	requests := make([]*AuthorityRequest, len(requestsDB))
	for i, requestDB := range requestsDB {
		request, err := AuthorityRequestFromDB(*requestDB)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to convert request from DB: %s", requestDB.ID)
		}
		requests[i] = request
	}

	return requests, nil
}

// UpdateRequest updates an authority request using an update function
// Automatically handles state transitions when state changes
func (w *DataWrapperImpl) UpdateRequest(ctx context.Context, id uuid.UUID, updateFn AuthorityRequestUpdateFn) (*AuthorityRequest, error) {
	var updatedRequest *AuthorityRequest
	now := time.Now()

	err := postgres_utils.WrapInTxContext(ctx, w.db.DB, func(txCtx context.Context, tx *sql.Tx) error {
		// Fetch current request
		requestDB, err := authorities_db_models.FindRequest(txCtx, tx, id.String())
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return errors.Wrapf(err, "authority request not found: %s", id)
			}
			return errors.Wrapf(err, "failed to fetch authority request: %s", id)
		}

		currentRequest, err := AuthorityRequestFromDB(*requestDB)
		if err != nil {
			return errors.Wrapf(err, "failed to convert request from DB: %s", id)
		}

		oldState := currentRequest.State

		// Apply update function
		var transitionInfo *StateTransitionInfo
		updatedRequest, transitionInfo, err = updateFn(currentRequest)
		if err != nil {
			return errors.Wrap(err, "update function failed")
		}

		// Update timestamp
		updatedRequest.UpdatedAt = now

		// Handle state-specific logic if state changed
		if oldState != updatedRequest.State {
			w.applyStateSpecificLogic(updatedRequest, now, transitionInfo)
		}

		// Convert back to DB model and update
		updatedRequestDB, err := AuthorityRequestToDB(*updatedRequest)
		if err != nil {
			return errors.Wrap(err, "failed to convert updated request to DB model")
		}

		_, err = updatedRequestDB.Update(txCtx, tx, boil.Infer())
		if err != nil {
			return errors.Wrapf(err, "failed to update authority request: %s", id)
		}

		// Record state transition if state changed
		if oldState != updatedRequest.State && transitionInfo != nil {
			err = w.recordStateTransitionInTx(txCtx, tx, id, oldState, updatedRequest.State, now, transitionInfo)
			if err != nil {
				return errors.Wrap(err, "failed to record state transition")
			}
		}

		return nil
	}, postgres_utils.IsolationSerializable, postgres_utils.MaxSerializationRetries(5))
	if err != nil {
		return nil, errors.Wrap(err, "failed to update authority request")
	}

	return updatedRequest, nil
}

// applyStateSpecificLogic applies state-specific logic like setting timestamps and reviewer info
func (w *DataWrapperImpl) applyStateSpecificLogic(request *AuthorityRequest, now time.Time, transitionInfo *StateTransitionInfo) {
	// Set reviewer and review timestamp if provided
	if transitionInfo != nil && transitionInfo.ReviewerID != nil {
		request.LastReviewedBy = null.StringFrom(transitionInfo.ReviewerID.String())
		request.LastReviewedAt = null.TimeFrom(now)
	}

	// Set specific timestamps based on state
	switch request.State {
	case enums.RequestStatePending:
		if !request.SubmittedAt.Valid {
			request.SubmittedAt = null.TimeFrom(now)
		}
	case enums.RequestStateExecuted:
		request.ExecutedAt = null.TimeFrom(now)
	case enums.RequestStateInvalid, enums.RequestStateDraft, enums.RequestStateApproved,
		enums.RequestStateRejected, enums.RequestStateCancelled:
		// No specific timestamp handling needed for these states
	}
}

// recordStateTransitionInTx records a state transition within a transaction
func (w *DataWrapperImpl) recordStateTransitionInTx(ctx context.Context, tx *sql.Tx, requestID uuid.UUID,
	fromState, toState enums.RequestState, timestamp time.Time, transitionInfo *StateTransitionInfo,
) error {
	var metadata null.JSON
	var err error

	if transitionInfo != nil && transitionInfo.Metadata != nil {
		metadata, err = MetadataToJSON(transitionInfo.Metadata)
		if err != nil {
			return errors.Wrap(err, "failed to convert metadata to JSON")
		}
	}

	transition := AuthorityRequestStateTransition{
		RequestID: requestID,
		Timestamp: timestamp,
		FromState: fromState,
		ToState:   toState,
		Metadata:  metadata,
	}

	transitionDB, err := AuthorityRequestStateTransitionToDB(transition)
	if err != nil {
		return errors.Wrap(err, "failed to convert state transition to DB model")
	}

	err = transitionDB.Insert(ctx, tx, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "failed to insert state transition for request %s", requestID)
	}

	return nil
}

// GetStateTransitions retrieves all state transitions for a request
func (w *DataWrapperImpl) GetStateTransitions(ctx context.Context, requestID uuid.UUID,
) ([]*AuthorityRequestStateTransition, error) {
	transitionsDB, err := authorities_db_models.RequestsStateTransitions(
		authorities_db_models.RequestsStateTransitionWhere.RequestID.EQ(requestID.String()),
		qm.OrderBy(authorities_db_models.RequestsStateTransitionColumns.Timestamp+" ASC"),
	).All(ctx, w.db.DB)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch state transitions for request: %s", requestID)
	}

	transitions := make([]*AuthorityRequestStateTransition, len(transitionsDB))
	for i, transitionDB := range transitionsDB {
		transition, err := AuthorityRequestStateTransitionFromDB(*transitionDB)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to convert state transition from DB: %s", requestID)
		}
		transitions[i] = transition
	}

	return transitions, nil
}

// Ensure DataWrapperImpl implements AuthorityRequestWrapper
var _ AuthorityRequestWrapper = &DataWrapperImpl{}
