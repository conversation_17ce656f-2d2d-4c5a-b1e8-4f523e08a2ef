package authorities

import (
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	authorities_db_models "nirvanatech.com/nirvana/db-api/db_models/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
)

// AuthorityRequestToDB converts domain model to DB model
func AuthorityRequestToDB(request AuthorityRequest) (*authorities_db_models.Request, error) {
	return &authorities_db_models.Request{
		ID:                  request.ID.String(),
		ApplicationReviewID: request.ApplicationReviewID.String(),
		RequestType:         request.RequestType.String(),
		State:               request.State.String(),
		RequestData:         request.RequestData,
		RequesterID:         request.RequesterID.String(),
		LastReviewedBy:      request.LastReviewedBy,
		ReviewData:          request.ReviewData,
		CreatedAt:           request.CreatedAt,
		UpdatedAt:           request.UpdatedAt,
		SubmittedAt:         request.SubmittedAt,
		LastReviewedAt:      request.LastReviewedAt,
		ExecutedAt:          request.ExecutedAt,
	}, nil
}

// AuthorityRequestFromDB converts DB model to domain model
func AuthorityRequestFromDB(requestDB authorities_db_models.Request) (*AuthorityRequest, error) {
	id, err := uuid.Parse(requestDB.ID)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid request ID: %s", requestDB.ID)
	}

	applicationReviewID, err := uuid.Parse(requestDB.ApplicationReviewID)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid application review ID: %s", requestDB.ApplicationReviewID)
	}

	requesterID, err := uuid.Parse(requestDB.RequesterID)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid requester ID: %s", requestDB.RequesterID)
	}

	requestType, err := enums.RequestTypeString(requestDB.RequestType)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid request type: %s", requestDB.RequestType)
	}

	state, err := enums.RequestStateString(requestDB.State)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid request state: %s", requestDB.State)
	}

	return &AuthorityRequest{
		ID:                  id,
		ApplicationReviewID: applicationReviewID,
		RequestType:         requestType,
		State:               state,
		RequestData:         requestDB.RequestData,
		RequesterID:         requesterID,
		LastReviewedBy:      requestDB.LastReviewedBy,
		ReviewData:          requestDB.ReviewData,
		CreatedAt:           requestDB.CreatedAt,
		UpdatedAt:           requestDB.UpdatedAt,
		SubmittedAt:         requestDB.SubmittedAt,
		LastReviewedAt:      requestDB.LastReviewedAt,
		ExecutedAt:          requestDB.ExecutedAt,
	}, nil
}

// AuthorityRequestStateTransitionToDB converts domain model to DB model
func AuthorityRequestStateTransitionToDB(transition AuthorityRequestStateTransition) (*authorities_db_models.RequestsStateTransition, error) {
	return &authorities_db_models.RequestsStateTransition{
		RequestID: transition.RequestID.String(),
		Timestamp: transition.Timestamp,
		FromState: transition.FromState.String(),
		ToState:   transition.ToState.String(),
		Metadata:  transition.Metadata,
	}, nil
}

// AuthorityRequestStateTransitionFromDB converts DB model to domain model
func AuthorityRequestStateTransitionFromDB(transitionDB authorities_db_models.RequestsStateTransition) (*AuthorityRequestStateTransition, error) {
	requestID, err := uuid.Parse(transitionDB.RequestID)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid request ID: %s", transitionDB.RequestID)
	}

	fromState, err := enums.RequestStateString(transitionDB.FromState)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid from state: %s", transitionDB.FromState)
	}

	toState, err := enums.RequestStateString(transitionDB.ToState)
	if err != nil {
		return nil, errors.Wrapf(err, "invalid to state: %s", transitionDB.ToState)
	}

	return &AuthorityRequestStateTransition{
		RequestID: requestID,
		Timestamp: transitionDB.Timestamp,
		FromState: fromState,
		ToState:   toState,
		Metadata:  transitionDB.Metadata,
	}, nil
}

// MetadataToJSON converts a map to JSON for storage
func MetadataToJSON(metadata map[string]interface{}) (null.JSON, error) {
	if metadata == nil {
		return null.JSON{}, nil
	}

	jsonBytes, err := json.Marshal(metadata)
	if err != nil {
		return null.JSON{}, errors.Wrap(err, "failed to marshal metadata to JSON")
	}

	return null.JSONFrom(jsonBytes), nil
}
