package authorities

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
)

// AuthorityRequestWrapper defines the interface for authority request operations
type AuthorityRequestWrapper interface {
	// CreateRequest creates a new authority request and records the initial state transition
	CreateRequest(ctx context.Context, request AuthorityRequest) (*AuthorityRequest, error)

	// GetRequest retrieves an authority request by ID
	GetRequest(ctx context.Context, id uuid.UUID) (*AuthorityRequest, error)

	// GetRequests retrieves requests with flexible filtering using qm queries
	GetRequests(ctx context.Context, mods ...qm.QueryMod) ([]*AuthorityRequest, error)

	// UpdateRequest updates an authority request using an update function
	// Automatically handles state transitions when state changes
	UpdateRequest(ctx context.Context, id uuid.UUID, updateFn AuthorityRequestUpdateFn) (*AuthorityRequest, error)

	// GetStateTransitions retrieves all state transitions for a request
	GetStateTransitions(ctx context.Context, requestID uuid.UUID) ([]*AuthorityRequestStateTransition, error)
}

// StateTransitionInfo contains metadata for state transitions
type StateTransitionInfo struct {
	Metadata   map[string]interface{}
	ReviewerID *uuid.UUID
}

// AuthorityRequestUpdateFn is a function type for updating authority requests
// Returns the updated request, optional state transition info, and error
type AuthorityRequestUpdateFn func(request *AuthorityRequest) (*AuthorityRequest, *StateTransitionInfo, error)

// AuthorityRequest represents an authority request domain model
type AuthorityRequest struct {
	ID                  uuid.UUID
	ApplicationReviewID uuid.UUID
	RequestType         enums.RequestType
	State               enums.RequestState
	RequestData         null.JSON
	RequesterID         uuid.UUID
	LastReviewedBy      null.String // UUID as string
	ReviewData          null.JSON
	CreatedAt           time.Time
	UpdatedAt           time.Time
	SubmittedAt         null.Time
	LastReviewedAt      null.Time
	ExecutedAt          null.Time
}

// AuthorityRequestStateTransition represents a state transition domain model
type AuthorityRequestStateTransition struct {
	RequestID uuid.UUID
	Timestamp time.Time
	FromState enums.RequestState
	ToState   enums.RequestState
	Metadata  null.JSON
}
