package application

import (
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/problem"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/zip_code_utils"
	"nirvanatech.com/nirvana/db-api/db_models"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	"nirvanatech.com/nirvana/quoting/clearance/enums"
	"nirvanatech.com/nirvana/telematics"
)

func applicationToDb(a Application) (*db_models.Application, error) {
	stateMetadata, err := json.Marshal(a.StateMetadata)
	if err != nil {
		return nil, err
	}
	companyInfo, err := json.Marshal(a.CompanyInfo)
	if err != nil {
		return nil, err
	}
	equipmentInfo, err := json.Marshal(a.EquipmentInfo)
	if err != nil {
		return nil, err
	}
	driversInfo, err := json.Marshal(a.DriversInfo)
	if err != nil {
		return nil, err
	}
	lossInfo, err := json.Marshal(a.LossInfo)
	if err != nil {
		return nil, err
	}
	coverageInfo, err := json.Marshal(a.CoverageInfo)
	if err != nil {
		return nil, err
	}
	addlLossInfo, err := json.Marshal(a.AdditionalLossInfo)
	if err != nil {
		return nil, err
	}
	addlCommodityInfo, err := json.Marshal(a.AdditionalCommodityInfo)
	if err != nil {
		return nil, err
	}
	addlExtraInfo, err := json.Marshal(a.AdditionalInfoExtraMetadata)
	if err != nil {
		return nil, err
	}
	addlInsuredInfo, err := json.Marshal(a.AdditionalInsuredInfo)
	if err != nil {
		return nil, err
	}
	addlCoverageMetadata, err := json.Marshal(a.AdditionalInfoCoverageMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshall coverage metadata")
	}
	problems, err := json.Marshal(a.Problems)
	if err != nil {
		return nil, err
	}
	modelPinConfig, err := json.Marshal(a.ModelPinConfig)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshall modelPinConfig")
	}
	addlEmailInfo, err := json.Marshal(a.AdditionalEmailInfo)
	if err != nil {
		return nil, err
	}
	telematicsInfo, err := json.Marshal(a.TelematicsInfo)
	if err != nil {
		return nil, err
	}
	pricingMetadata, err := json.Marshal(a.PricingMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshall pricingMetadata")
	}
	additionalAgentFiles, err := json.Marshal(a.AdditionalAgentFiles)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshall AdditionalAgentFiles")
	}
	retailerInfo, err := json.Marshal(a.RetailerInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshall RetailerInfo")
	}
	// TODO: refactor and remove this
	tspConnHandleId := uuid.Nil.String()
	if a.TSPConnHandleId != nil {
		tspConnHandleId = *a.TSPConnHandleId
	}
	var tspEnum string
	if a.TSPEnum != nil {
		tspEnum = a.TSPEnum.String()
	}
	var packageType string
	if a.PackageType != nil {
		packageType = a.PackageType.String()
	}
	var clearanceStatus string
	if a.ClearanceStatus != nil {
		clearanceStatus = a.ClearanceStatus.String()
	}

	var assignedBD *string
	if a.AssignedBD != nil {
		assignedBD = pointer_utils.String(a.AssignedBD.String())
	}

	appDb := db_models.Application{
		ID:                       a.ID,
		State:                    a.State.String(),
		ShortID:                  string(a.ShortID),
		StateMetadata:            null.JSONFrom(stateMetadata),
		CreatedAt:                a.CreatedAt,
		UpdatedAt:                a.UpdatedAt,
		CreatedBy:                a.CreatedBy,
		CompanyInfo:              companyInfo,
		EquipmentInfo:            equipmentInfo,
		DriversInfo:              driversInfo,
		LossInfo:                 lossInfo,
		CoverageInfo:             coverageInfo,
		DataContextID:            a.DataContextID.String(),
		AddlLossInfo:             null.JSONFrom(addlLossInfo),
		AddlCommodityInfo:        null.JSONFrom(addlCommodityInfo),
		AddlInfoExtra:            null.JSONFrom(addlExtraInfo),
		AddlInsuredInfo:          null.JSONFrom(addlInsuredInfo),
		AddlInfoCoverageMetadata: null.JSONFrom(addlCoverageMetadata),
		AgencyID:                 null.StringFrom(a.AgencyID.String()),
		ProducerID:               null.StringFromPtr(a.ProducerID),
		TSPConnHandleID:          null.StringFrom(tspConnHandleId),
		TSPEnum:                  null.StringFrom(tspEnum),
		IndicationSubmissionID:   null.StringFromPtr(a.IndicationSubmissionID),
		UwSubmissionID:           null.StringFromPtr(a.UwSubmissionID),
		BindableSubmissionID:     null.StringFromPtr(a.BindableSubmissionID),
		PackageType:              null.StringFrom(packageType),
		Problems:                 null.JSONFrom(problems),
		UnderwriterID:            null.StringFrom(a.UnderwriterID.String()),
		ArchivedAt:               null.TimeFromPtr(a.archivedAt),
		ModelPinConfig:           null.JSONFrom(modelPinConfig),
		AddlEmailInfo:            null.JSONFrom(addlEmailInfo),
		TelematicsInfo:           null.JSONFrom(telematicsInfo),
		PricingMetadata:          null.JSONFrom(pricingMetadata),
		ClearanceStatus:          null.StringFrom(clearanceStatus),
		AssignedBD:               null.StringFromPtr(assignedBD),
		AdditionalAgentFiles:     null.JSONFrom(additionalAgentFiles),
		RetailerInfo:             null.JSONFrom(retailerInfo),
	}
	if a.IsRenewal() {
		renewalMetadata, err := json.Marshal(a.RenewalMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to marshall renewalMetadata")
		}
		appDb.RenewalMetadata = null.JSONFrom(renewalMetadata)
	}

	if a.Tags != nil {
		tags, err := json.Marshal(a.Tags)
		if err != nil {
			return nil, errors.Wrap(err, "unable to marshall tags")
		}
		appDb.Tags = null.JSONFrom(tags)
	}

	return &appDb, nil
}

func applicationsFromDb(
	applicationsFromDB []*db_models.Application,
) ([]Application, error) {
	return slice_utils.MapErr(applicationsFromDB,
		func(applicationFromDB *db_models.Application) (app Application, err error) {
			appPtr, err := applicationFromDb(*applicationFromDB)
			if err != nil {
				return app, errors.Wrapf(err, "failed to deserialize application %s", applicationFromDB.ID)
			}
			app = *appPtr
			return app, nil
		},
	)
}

func applicationFromDb(
	applicationFromDB db_models.Application,
) (*Application, error) {
	var applicationObject Application
	applicationObject.ID = applicationFromDB.ID
	agencyId, err := uuid.Parse(applicationFromDB.AgencyID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse agencyId")
	}
	applicationObject.AgencyID = agencyId
	underwriterId, err := uuid.Parse(applicationFromDB.UnderwriterID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse underwriterId")
	}
	applicationObject.UnderwriterID = underwriterId
	applicationObject.ProducerID = applicationFromDB.ProducerID.Ptr()
	applicationObject.CreatedAt = applicationFromDB.CreatedAt
	applicationObject.UpdatedAt = applicationFromDB.UpdatedAt
	shortID := short_id.ShortID(applicationFromDB.ShortID)
	applicationObject.ShortID = shortID
	applicationObject.CreatedBy = applicationFromDB.CreatedBy
	if applicationFromDB.TSPConnHandleID.String != uuid.Nil.String() {
		applicationObject.TSPConnHandleId = applicationFromDB.TSPConnHandleID.Ptr()
	}
	if applicationFromDB.ClearanceStatus.String != "" {
		clearanceStatusEnum, err := enums.ClearanceStateString(applicationFromDB.ClearanceStatus.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse clearance status")
		}
		applicationObject.ClearanceStatus = &clearanceStatusEnum
	}
	if applicationFromDB.TSPEnum.String != "" {
		tspEnum, err := telematics.TSPString(applicationFromDB.TSPEnum.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse tsp enum")
		}
		applicationObject.TSPEnum = &tspEnum
	}
	if applicationFromDB.PackageType.String != "" {
		packageType, err := app_enums.IndicationOptionTagString(
			applicationFromDB.PackageType.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse ind opt tag")
		}
		applicationObject.PackageType = &packageType
	}
	applicationObject.IndicationSubmissionID = applicationFromDB.IndicationSubmissionID.Ptr()
	applicationObject.UwSubmissionID = applicationFromDB.UwSubmissionID.Ptr()
	applicationObject.BindableSubmissionID = applicationFromDB.BindableSubmissionID.Ptr()
	state, err := state_enums.AppStateString(applicationFromDB.State)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse app state")
	}
	applicationObject.State = state
	err = json.Unmarshal(applicationFromDB.StateMetadata.JSON,
		&applicationObject.StateMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall state metadata")
	}
	err = json.Unmarshal(applicationFromDB.CompanyInfo, &applicationObject.CompanyInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall company info")
	}
	if applicationObject.CompanyInfo != nil && applicationObject.CompanyInfo.TerminalLocations != nil {
		for i, terminalLocation := range *applicationObject.CompanyInfo.TerminalLocations {
			if terminalLocation.ZipCodeString == "" && terminalLocation.ZipCode != 0 {
				zipCodeString, err := zip_code_utils.ConvertToStringFromInt(terminalLocation.ZipCode)
				if err != nil {
					return nil, errors.Wrap(err, "unable to convert zip code to string")
				}
				(*applicationObject.CompanyInfo.TerminalLocations)[i].ZipCodeString = zipCodeString
			}
		}
	}
	err = json.Unmarshal(applicationFromDB.EquipmentInfo, &applicationObject.EquipmentInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall equipment info")
	}
	err = json.Unmarshal(applicationFromDB.DriversInfo, &applicationObject.DriversInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall drivers info")
	}
	err = json.Unmarshal(applicationFromDB.LossInfo, &applicationObject.LossInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall loss info")
	}
	err = json.Unmarshal(applicationFromDB.CoverageInfo, &applicationObject.CoverageInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall coverage info")
	}

	if !applicationFromDB.AddlLossInfo.Valid {
		applicationObject.AdditionalLossInfo = nil
	} else {
		err = json.Unmarshal(applicationFromDB.AddlLossInfo.JSON, &applicationObject.AdditionalLossInfo)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall addl loss info")
		}
	}

	if !applicationFromDB.AddlCommodityInfo.Valid {
		applicationObject.AdditionalCommodityInfo = nil
	} else {
		err = json.Unmarshal(applicationFromDB.AddlCommodityInfo.JSON, &applicationObject.AdditionalCommodityInfo)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall addl commodities info")
		}
	}

	if !applicationFromDB.AddlInfoExtra.Valid {
		applicationObject.AdditionalInfoExtraMetadata = nil
	} else {
		err = json.Unmarshal(applicationFromDB.AddlInfoExtra.JSON, &applicationObject.AdditionalInfoExtraMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall addl extra metadata info")
		}
	}
	if !applicationFromDB.AddlInsuredInfo.Valid {
		applicationObject.AdditionalInsuredInfo = nil
	} else {
		err = json.Unmarshal(applicationFromDB.AddlInsuredInfo.JSON, &applicationObject.AdditionalInsuredInfo)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall add insured info")
		}
	}
	if !applicationFromDB.AddlInfoCoverageMetadata.Valid {
		applicationObject.AdditionalInfoCoverageMetadata = nil
	} else {
		err = json.Unmarshal(applicationFromDB.AddlInfoCoverageMetadata.JSON, &applicationObject.AdditionalInfoCoverageMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall add coverage metadata")
		}
	}

	if !applicationFromDB.ModelPinConfig.Valid {
		applicationObject.ModelPinConfig = nil
	} else {
		err = json.Unmarshal(applicationFromDB.ModelPinConfig.JSON, &applicationObject.ModelPinConfig)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall modelPinConfig info")
		}
	}

	applicationObject.Problems = problem.New()
	if applicationFromDB.Problems.Valid {
		err = json.Unmarshal(applicationFromDB.Problems.JSON, applicationObject.Problems)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall problems")
		}
	}
	applicationObject.archivedAt = applicationFromDB.ArchivedAt.Ptr()
	if !applicationFromDB.RenewalMetadata.Valid {
		applicationObject.RenewalMetadata = nil
	} else {
		err = json.Unmarshal(applicationFromDB.RenewalMetadata.JSON, &applicationObject.RenewalMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall renewal metadata")
		}
	}
	if !applicationFromDB.AddlEmailInfo.Valid {
		applicationObject.AdditionalEmailInfo = nil
	} else {
		err = json.Unmarshal(applicationFromDB.AddlEmailInfo.JSON, &applicationObject.AdditionalEmailInfo)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall AddlEmailInfo")
		}
	}
	if !applicationFromDB.TelematicsInfo.Valid {
		applicationObject.TelematicsInfo = nil
	} else {
		err = json.Unmarshal(applicationFromDB.TelematicsInfo.JSON, &applicationObject.TelematicsInfo)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall TelematicsInfo")
		}
	}
	if !applicationFromDB.PricingMetadata.Valid {
		applicationObject.PricingMetadata = nil
	} else {
		err = json.Unmarshal(applicationFromDB.PricingMetadata.JSON, &applicationObject.PricingMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall PricingMetadata")
		}
	}
	if applicationFromDB.Tags.Valid {
		err = json.Unmarshal(applicationFromDB.Tags.JSON, &applicationObject.Tags)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall Tags")
		}
	}

	if applicationFromDB.AssignedBD.Valid {
		assignedBD, err := uuid.Parse(applicationFromDB.AssignedBD.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse assignedBD")
		}
		applicationObject.AssignedBD = &assignedBD
	}

	if !applicationFromDB.AdditionalAgentFiles.Valid {
		applicationObject.AdditionalAgentFiles = nil
	} else {
		err = json.Unmarshal(applicationFromDB.AdditionalAgentFiles.JSON, &applicationObject.AdditionalAgentFiles)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall AdditionalAgentFiles")
		}
	}

	if !applicationFromDB.RetailerInfo.Valid {
		applicationObject.RetailerInfo = nil
	} else {
		err = json.Unmarshal(applicationFromDB.RetailerInfo.JSON, &applicationObject.RetailerInfo)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall RetailerInfo")
		}
	}

	rawDataContextID := applicationFromDB.DataContextID
	dataContextID, err := uuid.Parse(rawDataContextID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse dataContextID %s", rawDataContextID)
	}
	applicationObject.DataContextID = dataContextID

	return &applicationObject, nil
}

func submissionToDb(submission SubmissionObject) (*db_models.Submission, error) {
	companyInfo, err := json.Marshal(submission.CompanyInfo)
	if err != nil {
		return nil, err
	}
	equipmentInfo, err := json.Marshal(submission.EquipmentInfo)
	if err != nil {
		return nil, err
	}
	driversInfo, err := json.Marshal(submission.DriversInfo)
	if err != nil {
		return nil, err
	}
	lossInfo, err := json.Marshal(submission.LossInfo)
	if err != nil {
		return nil, err
	}
	coverageInfo, err := json.Marshal(submission.CoverageInfo)
	if err != nil {
		return nil, err
	}
	addlLossInfo, err := json.Marshal(submission.AdditionalLossInfo)
	if err != nil {
		return nil, err
	}
	addlCommodityInfo, err := json.Marshal(submission.AdditionalCommodityInfo)
	if err != nil {
		return nil, err
	}
	addlExtraInfo, err := json.Marshal(submission.AdditionalInfoExtraMetadata)
	if err != nil {
		return nil, err
	}
	addlInsuredInfo, err := json.Marshal(submission.AdditionalInsuredInfo)
	if err != nil {
		return nil, err
	}
	addlCoverageMetadata, err := json.Marshal(submission.AdditionalInfoCoverageMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshall coverage metadata")
	}
	jobRunId, err := json.Marshal(submission.JobRunId)
	if err != nil {
		return nil, err
	}
	underwriterInput, err := json.Marshal(submission.UnderwriterInput)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshal underwriter input as json")
	}
	modelPinConfig, err := json.Marshal(submission.ModelPinConfig)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshal modelPinConfig as json")
	}
	pricingMetadata, err := json.Marshal(submission.PricingMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshal target prices as json")
	}

	var packageType string
	if submission.PackageType != nil {
		packageType = submission.PackageType.String()
	}
	// Form Compilation
	var signaturePacketID *string
	var policyFormID *string
	if submission.SignaturePacketFormID != nil {
		signaturePacketID = pointer_utils.String(submission.SignaturePacketFormID.String())
	}
	if submission.PolicyFormID != nil {
		policyFormID = pointer_utils.String(submission.PolicyFormID.String())
	}

	submissionDB := db_models.Submission{
		ID:                       submission.ID,
		ApplicationID:            submission.ApplicationID,
		Bindable:                 null.BoolFrom(submission.Bindable),
		CreatedAt:                submission.CreatedAt,
		CompanyInfo:              companyInfo,
		EquipmentInfo:            equipmentInfo,
		DriversInfo:              driversInfo,
		LossInfo:                 lossInfo,
		CoverageInfo:             coverageInfo,
		DataContextID:            submission.DataContextID.String(),
		AddlLossInfo:             null.JSONFrom(addlLossInfo),
		AddlCommodityInfo:        null.JSONFrom(addlCommodityInfo),
		AddlInfoExtra:            null.JSONFrom(addlExtraInfo),
		AddlInsuredInfo:          null.JSONFrom(addlInsuredInfo),
		AddlInfoCoverageMetadata: null.JSONFrom(addlCoverageMetadata),
		IndicationOptionsIds:     submission.IndicationOptionsIDs,
		SelectedIndicationID:     null.StringFrom(submission.SelectedIndicationID),
		JobRunID:                 null.JSONFrom(jobRunId),
		UnderwriterID:            null.StringFrom(submission.UnderwriterID.String()),
		UnderwriterInput:         null.JSONFrom(underwriterInput),
		QuotePDFHandleID:         null.StringFromPtr(pointer_utils.UUIDStringOrNil(submission.QuotePDFHandleId)),
		ApplicationPDFHandleID:   null.StringFromPtr(pointer_utils.UUIDStringOrNil(submission.ApplicationPDFHandleId)),
		EffectiveDateTo:          null.TimeFromPtr(submission.EffectiveDateTo),
		PackageType:              null.StringFrom(packageType),
		SignaturePackedFormID:    null.StringFromPtr(signaturePacketID),
		PolicyFormID:             null.StringFromPtr(policyFormID),
		ModelPinConfig:           null.JSONFrom(modelPinConfig),
		PricingMetadata:          null.JSONFrom(pricingMetadata),
	}

	return &submissionDB, nil
}

func SubmissionFromDb(
	submissionDb db_models.Submission,
) (*SubmissionObject, error) {
	submissionObject := SubmissionObject{
		ID:                   submissionDb.ID,
		ApplicationID:        submissionDb.ApplicationID,
		Bindable:             submissionDb.Bindable.Bool,
		CreatedAt:            submissionDb.CreatedAt,
		IndicationOptionsIDs: submissionDb.IndicationOptionsIds,
		SelectedIndicationID: submissionDb.SelectedIndicationID.String,
	}

	rawDataContextID := submissionDb.DataContextID
	dataContextID, err := uuid.Parse(rawDataContextID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to parse data context id: %s", rawDataContextID)
	}
	submissionObject.DataContextID = dataContextID

	if submissionDb.UnderwriterID.Valid {
		uID, err := uuid.Parse(submissionDb.UnderwriterID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse underwriter id as uuid")
		}
		submissionObject.UnderwriterID = uID
	}
	if submissionDb.QuotePDFHandleID.Valid {
		uID, err := uuid.Parse(submissionDb.QuotePDFHandleID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse quote PDF handle id as uuid")
		}
		submissionObject.QuotePDFHandleId = pointer_utils.UUID(uID)
	}
	if submissionDb.ApplicationPDFHandleID.Valid {
		uID, err := uuid.Parse(submissionDb.ApplicationPDFHandleID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse app PDF handle id as uuid")
		}
		submissionObject.ApplicationPDFHandleId = pointer_utils.UUID(uID)
	}
	if submissionDb.EffectiveDateTo.Valid {
		submissionObject.EffectiveDateTo = &submissionDb.EffectiveDateTo.Time
	}
	if submissionDb.PackageType.String != "" {
		packageType, err := app_enums.IndicationOptionTagString(
			submissionDb.PackageType.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse ind opt tag")
		}
		submissionObject.PackageType = &packageType
	}
	err = json.Unmarshal(
		submissionDb.CompanyInfo,
		&submissionObject.CompanyInfo)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(
		submissionDb.EquipmentInfo,
		&submissionObject.EquipmentInfo)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(
		submissionDb.DriversInfo,
		&submissionObject.DriversInfo)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(
		submissionDb.LossInfo,
		&submissionObject.LossInfo)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(
		submissionDb.CoverageInfo,
		&submissionObject.CoverageInfo)
	if err != nil {
		return nil, err
	}
	if !submissionDb.PricingMetadata.Valid {
		submissionObject.PricingMetadata = nil
	} else {
		err = json.Unmarshal(
			submissionDb.PricingMetadata.JSON,
			&submissionObject.PricingMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshal pricing metadata")
		}
	}
	if !submissionDb.AddlLossInfo.Valid {
		submissionObject.AdditionalLossInfo = nil
	} else {
		err = json.Unmarshal(
			submissionDb.AddlLossInfo.JSON,
			&submissionObject.AdditionalLossInfo)
		if err != nil {
			return nil, err
		}
	}
	if !submissionDb.AddlCommodityInfo.Valid {
		submissionObject.AdditionalCommodityInfo = nil
	} else {
		err = json.Unmarshal(
			submissionDb.AddlCommodityInfo.JSON,
			&submissionObject.AdditionalCommodityInfo)
		if err != nil {
			return nil, err
		}
	}
	if !submissionDb.AddlInfoExtra.Valid {
		submissionObject.AdditionalInfoExtraMetadata = nil
	} else {
		err = json.Unmarshal(
			submissionDb.AddlInfoExtra.JSON,
			&submissionObject.AdditionalInfoExtraMetadata)
		if err != nil {
			return nil, err
		}
	}
	if !submissionDb.AddlInsuredInfo.Valid {
		submissionObject.AdditionalInsuredInfo = nil
	} else {
		err = json.Unmarshal(
			submissionDb.AddlInsuredInfo.JSON,
			&submissionObject.AdditionalInsuredInfo)
		if err != nil {
			return nil, err
		}
	}
	if !submissionDb.AddlInfoCoverageMetadata.Valid {
		submissionObject.AdditionalInfoCoverageMetadata = nil
	} else {
		err = json.Unmarshal(submissionDb.AddlInfoCoverageMetadata.JSON, &submissionObject.AdditionalInfoCoverageMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall add coverage metadata")
		}
	}
	if !submissionDb.JobRunID.Valid {
		submissionObject.JobRunId = nil
	} else {
		err = json.Unmarshal(
			submissionDb.JobRunID.JSON,
			&submissionObject.JobRunId)
		if err != nil {
			return nil, err
		}
	}
	if !submissionDb.UnderwriterInput.Valid {
		submissionObject.UnderwriterInput = nil
	} else {
		err = json.Unmarshal(
			submissionDb.UnderwriterInput.JSON,
			&submissionObject.UnderwriterInput)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse underwriter input as json")
		}
	}
	if !submissionDb.ModelPinConfig.Valid {
		submissionObject.ModelPinConfig = nil
	} else {
		err = json.Unmarshal(
			submissionDb.ModelPinConfig.JSON,
			&submissionObject.ModelPinConfig)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse modelPinConfig as json")
		}
	}
	if submissionDb.SignaturePackedFormID.Ptr() != nil {
		id, err := uuid.Parse(submissionDb.SignaturePackedFormID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse signature form id")
		}
		submissionObject.SignaturePacketFormID = &id
	}
	if submissionDb.PolicyFormID.Ptr() != nil {
		id, err := uuid.Parse(submissionDb.PolicyFormID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse policy form id")
		}
		submissionObject.PolicyFormID = &id
	}
	return &submissionObject, nil
}

// IndicationOptionFromDb exported to use in postup generate_billing_info_from_policy.go
func IndicationOptionFromDb(
	indicationOptionDb db_models.IndicationOption,
) (*IndicationOption, error) {
	optionTag, err := app_enums.IndicationOptionTagString(
		indicationOptionDb.OptionTag)
	if err != nil {
		return nil, err
	}

	indicationOptionObject := IndicationOption{
		ID:                              indicationOptionDb.ID,
		TotalPremium:                    int32(indicationOptionDb.TotalPremium),
		TotalPowerUnits:                 int32(indicationOptionDb.TotalPowerUnits),
		TotalTrailerUnits:               int32(indicationOptionDb.TotalTrailerUnits),
		TotalMiles:                      int32(indicationOptionDb.TotalMiles),
		SubtotalPremium:                 int32(indicationOptionDb.SubtotalPremium),
		RoundedSafetyDiscountPercentage: int32(indicationOptionDb.RoundedSafetyDiscountPercentage),
		RoundedSafetyDiscountPremium:    int32(indicationOptionDb.RoundedSafetyDiscountPremium),
		RoundedPreDiscountTotalPremium:  int32(indicationOptionDb.RoundedPreDiscountTotalPremium),
		FlatCharges:                     int32(indicationOptionDb.FlatCharges),
		SafetyDiscountPremium:           int32(indicationOptionDb.SafetyDiscountPremium),
		SafetyDiscountPercentage:        int32(indicationOptionDb.SafetyDiscountPercentage),
		TIV:                             int32(indicationOptionDb.Tiv),
		PremiumPerUnit:                  int32(indicationOptionDb.PremiumPerUnit),
		IsRecommended:                   indicationOptionDb.IsRecommended,
		OptionTag:                       optionTag,
		Coverages:                       []CoverageDetails{},
		SubmissionID:                    indicationOptionDb.SubmissionID,
		CreatedAt:                       indicationOptionDb.CreatedAt,
	}
	err = json.Unmarshal(indicationOptionDb.Coverages,
		&indicationOptionObject.Coverages)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal coverages")
	}
	if indicationOptionDb.TotalSurchargePremium.Valid {
		indicationOptionObject.TotalSurchargePremium = pointer_utils.Int32(int32(indicationOptionDb.TotalSurchargePremium.Int))
	}
	if !indicationOptionDb.SurchargeInfo.Valid {
		indicationOptionObject.SurchargeInfo = nil
	} else {
		err = json.Unmarshal(
			indicationOptionDb.SurchargeInfo.JSON,
			&indicationOptionObject.SurchargeInfo)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshal surcharge info")
		}
	}
	if indicationOptionDb.TotalPolicyPremiumUnmodified.Valid {
		indicationOptionObject.TotalPolicyPremiumUnmodified = pointer_utils.Int32(int32(indicationOptionDb.TotalPolicyPremiumUnmodified.Int))
	}
	if indicationOptionDb.NegotiatedRates.Valid {
		err = json.Unmarshal(indicationOptionDb.NegotiatedRates.JSON, &indicationOptionObject.NegotiatedRates)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshal negotiated rates")
		}
	}

	return &indicationOptionObject, nil
}

func indicationOptionToDb(
	i IndicationOption,
) (*db_models.IndicationOption, error) {
	covInfo, err := json.Marshal(i.Coverages)
	if err != nil {
		return nil, err
	}
	surchargeInfo, err := json.Marshal(i.SurchargeInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshal surcharge info as json")
	}
	indDb := db_models.IndicationOption{
		ID:                              i.ID,
		SubmissionID:                    i.SubmissionID,
		TotalPremium:                    int(i.TotalPremium),
		TotalPowerUnits:                 int(i.TotalPowerUnits),
		TotalTrailerUnits:               int(i.TotalTrailerUnits),
		TotalMiles:                      int(i.TotalMiles),
		SubtotalPremium:                 int(i.SubtotalPremium),
		FlatCharges:                     int(i.FlatCharges),
		SafetyDiscountPremium:           int(i.SafetyDiscountPremium),
		SafetyDiscountPercentage:        int(i.SafetyDiscountPercentage),
		RoundedPreDiscountTotalPremium:  int(i.RoundedPreDiscountTotalPremium),
		RoundedSafetyDiscountPremium:    int(i.RoundedSafetyDiscountPremium),
		RoundedSafetyDiscountPercentage: int(i.RoundedSafetyDiscountPercentage),
		Tiv:                             int(i.TIV),
		PremiumPerUnit:                  int(i.PremiumPerUnit),
		Coverages:                       covInfo,
		IsRecommended:                   i.IsRecommended,
		OptionTag:                       i.OptionTag.String(),
		CreatedAt:                       i.CreatedAt,
		SurchargeInfo:                   null.JSONFrom(surchargeInfo),
	}
	// TODO: this is a hack to get around the fact that we don't have have totalSurchargePremium
	// for all states yet. Hence we are defaulting to 0. Once we have totalSurchargePremium for all
	// states, we can remove this hack.
	if i.TotalSurchargePremium != nil && *i.TotalSurchargePremium != 0 {
		indDb.TotalSurchargePremium = null.IntFrom(int(*i.TotalSurchargePremium))
	}
	if i.TotalPolicyPremiumUnmodified != nil && *i.TotalPolicyPremiumUnmodified != 0 {
		indDb.TotalPolicyPremiumUnmodified = null.IntFrom(int(*i.TotalPolicyPremiumUnmodified))
	}
	if i.NegotiatedRates != nil {
		negotiatedRates, err := json.Marshal(i.NegotiatedRates)
		if err != nil {
			return nil, errors.Wrap(err, "unable to marshal negotiated rates")
		}
		indDb.NegotiatedRates = null.JSONFrom(negotiatedRates)
	}
	return &indDb, nil
}

func transitionToDb(transition *StateTransition) (*db_models.ApplicationStateTransition, error) {
	var metadataJson null.JSON
	if transition.Metadata == nil {
		metadataJson = null.NewJSON(nil, false)
	} else {
		json, err := json.Marshal(transition.Metadata)
		if err != nil {
			return nil, errors.Wrapf(err, "Failed to marshal metadata to json for app_id=%s", transition.AppId)
		}
		metadataJson = null.JSONFrom(json)
	}
	return &db_models.ApplicationStateTransition{
		AppID:     transition.AppId,
		Timestamp: transition.Timestamp,
		FromState: transition.FromState.String(),
		ToState:   transition.ToState.String(),
		Metadata:  metadataJson,
	}, nil
}

func transitionFromDb(transition *db_models.ApplicationStateTransition) (*StateTransition, error) {
	st := StateTransition{
		AppId:     transition.AppID,
		Timestamp: transition.Timestamp,
	}
	if enum, err := state_enums.AppStateString(transition.FromState); err == nil {
		st.FromState = enum
	} else {
		return nil, errors.Wrapf(err, "Unrecognized FromState (possibly corrupted): %s", transition.FromState)
	}
	if enum, err := state_enums.AppStateString(transition.ToState); err == nil {
		st.ToState = enum
	} else {
		return nil, errors.Wrapf(err, "Unrecognized ToState (possibly corrupted): %s", transition.ToState)
	}
	if transition.Metadata.Valid {
		var metadata StateMetadata
		if err := json.Unmarshal(transition.Metadata.JSON, &metadata); err != nil {
			return nil, errors.Wrap(err, "Failed to parse metadata JSON (format mismatch?)")
		}
		st.Metadata = &metadata
	}
	return &st, nil
}
