package application

import (
	"context"
	"encoding/json"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/problem"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/common-go/vin_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums/old_enums"
	"nirvanatech.com/nirvana/fleet/model"
	"nirvanatech.com/nirvana/jobber/jtypes"
	nirvanaapp "nirvanatech.com/nirvana/nirvanaapp/models/application"
	"nirvanatech.com/nirvana/policy_common/constants"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	"nirvanatech.com/nirvana/quoting/clearance/enums"
	"nirvanatech.com/nirvana/rating/data_processing/vin_processing/iso_utils"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/telematics"
)

type SectionType int

const (
	Coverages SectionType = iota
	Operations
	ClassesAndCommodities
	LossHistory
	AdditionalInformation
)

var ErrCoverageNotFound = errors.New("coverage not found")

func NewApplication(ID string, shortID short_id.ShortID, dataContextID uuid.UUID, assignedBD *uuid.UUID) *Application {
	applicationObject := Application{
		ID:            ID,
		ShortID:       shortID,
		DataContextID: dataContextID,
		AssignedBD:    assignedBD,
	}
	return &applicationObject
}

func NewCompanyInfo() *CompanyInfo {
	return &CompanyInfo{
		DOTNumber:          0,
		Name:               "",
		NumberOfPowerUnits: 0,
		ProjectedMileage:   0,
		RadiusOfOperation:  []*MileageRadiusRecord{},
		USState:            nil,
	}
}

func NewEquipmentInfo() *EquipmentInfo {
	return &EquipmentInfo{
		OperatingClassDistribution: []OperatingClassDistributionRecord{},
		EquipmentList:              EquipmentList{},
	}
}

func NewLossInfo() *LossInfo {
	return &LossInfo{
		LossRunSummary: []LossRunSummaryPerCoverage{},
	}
}

func NewCoverageInfo() *CoverageInfo {
	return &CoverageInfo{
		Coverages:     []CoverageDetails{},
		EffectiveDate: time.Time{},
	}
}

// AllSectionTypes returns a slice of all the SectionType enum values.
func AllSectionTypes() []SectionType {
	return []SectionType{
		Coverages,
		Operations,
		ClassesAndCommodities,
		LossHistory,
		AdditionalInformation,
	}
}

func NewSectionCompletionMap() *SectionCompletionMap {
	sectionCompletionMap := make(SectionCompletionMap)

	for _, sectionType := range AllSectionTypes() {
		sectionCompletionMap[sectionType] = false
	}
	return &sectionCompletionMap
}

func NewRenewalMetadata(originalApplicationId string, previousApplicationsIds []string) *RenewalMetadata {
	version := 2
	return &RenewalMetadata{
		OriginalApplicationId:   originalApplicationId,
		SectionCompletionMap:    NewSectionCompletionMap(),
		Version:                 &version,
		PreviousApplicationsIds: &previousApplicationsIds,
	}
}

type Application struct {
	// Copier is being used to populate renewal application with the
	// information that should be carried over from the original application.
	ID            string               `copier:"-"`
	ShortID       short_id.ShortID     `copier:"-"`
	State         state_enums.AppState `copier:"-"`
	StateMetadata StateMetadata        `copier:"-"`
	CreatedAt     time.Time            `copier:"-"`
	UpdatedAt     time.Time            `copier:"-"`
	CreatedBy     string               `copier:"-"`
	UnderwriterID uuid.UUID

	ModelInput
	AdditionalInsuredInfo *AdditionalInsuredInfo
	AdditionalEmailInfo   *AdditionalEmailInfo  `copier:"-"`
	AdditionalAgentFiles  *AdditionalAgentFiles `copier:"-"`
	RetailerInfo          *RetailerInfo

	AgencyID               uuid.UUID
	AssignedBD             *uuid.UUID
	ProducerID             *string
	TSPEnum                *telematics.TSP
	TSPConnHandleId        *string
	IndicationSubmissionID *string                        `copier:"-"`
	UwSubmissionID         *string                        `copier:"-"`
	BindableSubmissionID   *string                        `copier:"-"`
	PackageType            *app_enums.IndicationOptionTag `copier:"-"`
	ModelPinConfig         *ModelPinConfig                `copier:"-"`
	RenewalMetadata        *RenewalMetadata               `copier:"-"`
	Problems               *problem.Problems              `copier:"-"`
	archivedAt             *time.Time                     `copier:"-"`
	TelematicsInfo         *TelematicsInfo
	Tags                   Tags
	ClearanceStatus        *enums.ClearanceState `copier:"-"`

	// DataContextID is used to identify a scope/context when using the store interceptor
	// with either the data_fetching.FetcherClient or data_processing.ProcessorClient. Data
	// that exists in a certain scope can be reused to avoid getting the data "from source".
	// For now, each application has a unique scope, that is shared by all of its submissions
	// but this might change for edge cases like endorsements and renewals.
	DataContextID uuid.UUID `copier:"-"`
}

type AdditionalAgentFiles struct {
	AgentFiles []FileMetadata
}

func NewAdditionalAgentInfo() *AdditionalAgentFiles {
	return &AdditionalAgentFiles{
		AgentFiles: []FileMetadata{},
	}
}

type (
	SectionCompletionMap map[SectionType]bool
	Tags                 map[string]interface{}
)

type RenewalMetadata struct {
	// OriginalApplicationId corresponds to the first application ever for a given lineage of renewals. In other words,
	// if application A1 resulted in policy P1, which got renewed into policy P2 via application A2, which in turn got
	// renewed into policy P3 via application A3, then for both A2 and A3 this value will hold A1's ID.
	OriginalApplicationId string

	// PreviousApplicationsIds holds the lineage of every application that came before the current application in terms
	// of renewals, and in ascending order according to their creation dates. In other words, if application A1 resulted
	// in policy P1, which got renewed into policy P2 via application A2, which in turn got renewed into policy P3 via
	// application A3, then A3's slice will hold [A1.ID, A2.ID].
	PreviousApplicationsIds *[]string

	SectionCompletionMap *SectionCompletionMap
	Version              *int
	IftaFiles            *[]FileMetadata
	BelongsToRedList     *bool
}

// IsRenewal returns whether the application is a renewal one or not, irrespective of the renewal algorithm version.
func (a *Application) IsRenewal() bool {
	return a.RenewalMetadata != nil
}

// IsRenewalV2 returns true if the application is a renewal (v2) application, and false otherwise.
func (a *Application) IsRenewalV2() bool {
	if a.RenewalMetadata != nil &&
		a.RenewalMetadata.SectionCompletionMap != nil &&
		a.RenewalMetadata.Version != nil &&
		*a.RenewalMetadata.Version == 2 {
		return true
	}
	return false
}

func BelongsToRedList(app Application) bool {
	if app.IsRenewal() && app.RenewalMetadata.BelongsToRedList != nil {
		return *app.RenewalMetadata.BelongsToRedList
	}
	return false
}

// IsArchived checks if the application is archived
func (a *Application) IsArchived() bool {
	return a.archivedAt != nil
}

// Archive archives an application using a given timestamp
func (a *Application) Archive(t *time.Time) error {
	if t == nil || t.IsZero() {
		return errors.New("unable to archive app with null time")
	}
	if a.IsArchived() {
		return errors.New("app already archived")
	}
	a.archivedAt = t
	return nil
}

// PreviousApplicationId returns the ID of the application that resulted in a policy that got renewed into the current
// application (the receiver one). It returns nil if it is a non-renewal application.
func (a *Application) PreviousApplicationId() *string {
	if !a.IsRenewal() {
		return nil
	}

	if a.IsRenewalV2() {
		ids := *a.RenewalMetadata.PreviousApplicationsIds
		return pointer_utils.ToPointer(ids[len(ids)-1])
	}

	return &a.RenewalMetadata.OriginalApplicationId
}

// HasClearance returns whether the application has been cleared or not.
func (a *Application) HasClearance() bool {
	return a.ClearanceStatus != nil && *a.ClearanceStatus == enums.ApplicationCleared
}

// ModelInput contains fields that are sent as input to rateml_models.
type ModelInput struct {
	// Indication data
	CompanyInfo     *CompanyInfo
	EquipmentInfo   *EquipmentInfo
	LossInfo        *LossInfo
	CoverageInfo    *CoverageInfo
	PricingMetadata *PricingMetadata
	// Addl info data
	DriversInfo                    *DriversInfo `copier:"-"`
	AdditionalLossInfo             *AdditionalLossInfo
	AdditionalCommodityInfo        *AdditionalCommodityInfo
	AdditionalInfoExtraMetadata    *AdditionalInfoExtraMetadata
	AdditionalInfoCoverageMetadata *AdditionalInfoCoverageMetadata
	// UW Input
	UnderwriterInput *UnderwriterInput `copier:"-"`

	// RateMLUnderwritingEntityInputs is a temporary field that
	// is used to pass discount/credit related data to adaptors.
	// These value comes from TransientSafetyCredit field
	// and the AggregateCreditByCoverage field. Check the
	// function populateSafetyDiscountFields for more details.
	//
	// This field is not persisted in DB. It's only populated
	// in memory before calling RateML adaptor/executor.
	RateMLUnderwritingEntityInputs RateMLUnderwritingEntityInputs

	// RateMLLargeLossesInputs is a temporary field that
	// is used to pass the final large losses to RateML
	// (or more concretely, adaptors).
	//
	// These losses can come either from the UW/Agents
	// platform (i.e. the UnderwriterInput.LargeLosses field)
	// or from proxied data (indicated by the LargeLossesProxied
	// field in this struct).
	RateMLLargeLossesInputs RateMLLargeLossesInputs

	// ExtraPricingInfo is a temporary field, that is needed
	// until we stop using ModelInput in entity creator functions.
	// We will be able to remove it when we replace it by the
	// final Pricing request object.
	ExtraPricingInfo *ExtraPricingInfo
}

// The SubmissionObject handles the submission information, without
// any db handling methods because it shouldn't be modified.
type SubmissionObject struct {
	ID            string
	ApplicationID string
	// Bindable is true if result of this submission will be enforceable.
	Bindable  bool
	CreatedAt time.Time

	ModelInput
	AdditionalInsuredInfo  *AdditionalInsuredInfo
	UnderwriterID          uuid.UUID
	IndicationOptionsIDs   []string
	SelectedIndicationID   string
	JobRunId               *jtypes.JobRunId
	QuotePDFHandleId       *uuid.UUID
	ApplicationPDFHandleId *uuid.UUID
	EffectiveDateTo        *time.Time
	PackageType            *app_enums.IndicationOptionTag
	SignaturePacketFormID  *uuid.UUID
	// Deprecated
	PolicyFormID   *uuid.UUID
	ModelPinConfig *ModelPinConfig

	// The DataContextID field is populated from the field of the same name in
	// the associated Application object (refer to the documentation on that field
	// for more details).
	//
	// We have two columns because eventually we can have submissions that have a
	// unique scope or context, different from the one set in the associated application.
	DataContextID uuid.UUID
}

// IndicationOption handles the options generated by our rating algorithm.
// They are linked to a submission, storing the exact coverages and deductible
// amounts required, as well as the calculated total premium and any optional
// relevant information (premium per truck, TIV% etc...)
// NOTE: TotalPremium is USD
// TODO: refactor name as this is being used for indication & quote
type IndicationOption struct {
	ID            string
	SubmissionID  string
	Coverages     []CoverageDetails
	CreatedAt     time.Time
	OptionTag     app_enums.IndicationOptionTag
	IsRecommended bool
	// TotalPremium is the total premium of the policy, including all charges
	// and discounts
	TotalPremium int32
	// SubtotalPremium is the sum of premiums for all coverages
	SubtotalPremium int32
	// FlatCharges include state surcharges & taxes
	FlatCharges int32
	// SafetyDiscountPremium premium savings based on safety discount
	SafetyDiscountPremium int32
	// SafetyDiscountPercentage premium
	SafetyDiscountPercentage int32
	// RoundedSafetyDiscountPremium rounded premium savings based on rounded
	// safety discount percentage
	RoundedSafetyDiscountPremium int32
	// RoundedSafetyDiscountPercentage rounded safety discount percentage
	RoundedSafetyDiscountPercentage int32
	// RoundedPreDiscountTotalPremium displays total premium before discount
	// considering rounded safety discount percentage/premium.
	RoundedPreDiscountTotalPremium int32
	// TotalPowerUnits represents the total number of power units in the fleet
	TotalPowerUnits int32
	// TotalTrailerUnits represents the total number of trailer units in the fleet
	TotalTrailerUnits int32
	// TIV represents the total insured value
	TIV int32
	// TotalMiles represents the total projected driven miles
	TotalMiles int32
	// PremiumPerUnit represents the total premium per unit
	PremiumPerUnit int32
	// TotalSurchargePremium represents the total surcharge premium
	TotalSurchargePremium *int32
	// NegotiatedNonFlatPremium represents the negotiated rate non-flat premium
	NegotiatedNonFlatPremium *int32
	// TraditionalNonFlatPremium represents the traditional non-flat premium
	TraditionalNonFlatPremium *int32
	// TotalPolicyPremiumUnmodified represents the total policy premium unmodified
	TotalPolicyPremiumUnmodified *int32
	SurchargeInfo                *SurchargeInfo

	NegotiatedRates *NegotiatedRates
}

func (i *IndicationOption) GetCoverage(typ app_enums.Coverage) (*CoverageDetails, error) {
	for j := range i.Coverages {
		if i.Coverages[j].CoverageType == typ {
			return &i.Coverages[j], nil
		}
	}
	return nil, ErrCoverageNotFound
}

type IndicationOptionInputs struct {
	ID                           string
	SubmissionID                 string
	TotalPowerUnits              int32
	TotalTrailerUnits            int32
	Tiv                          int32
	TotalMiles                   int32
	TotalPremium                 int32
	PremiumPerUnit               int32
	FlatCharges                  int32
	Coverages                    []CoverageDetails
	CreatedAt                    time.Time
	OptionTag                    app_enums.IndicationOptionTag
	IsRecommended                bool
	TotalSurchargePremium        *int32
	TotalPolicyPremiumUnmodified *int32
	SurchargeInfo                *SurchargeInfo
}

func NewIndicationOption(inputs IndicationOptionInputs) *IndicationOption {
	return &IndicationOption{
		ID:                           inputs.ID,
		SubmissionID:                 inputs.SubmissionID,
		TotalPremium:                 inputs.TotalPremium,
		Coverages:                    inputs.Coverages,
		CreatedAt:                    inputs.CreatedAt,
		OptionTag:                    inputs.OptionTag,
		IsRecommended:                inputs.IsRecommended,
		TotalPowerUnits:              inputs.TotalPowerUnits,
		TIV:                          inputs.Tiv,
		TotalMiles:                   inputs.TotalMiles,
		FlatCharges:                  inputs.FlatCharges,
		PremiumPerUnit:               inputs.PremiumPerUnit,
		TotalSurchargePremium:        inputs.TotalSurchargePremium,
		TotalPolicyPremiumUnmodified: inputs.TotalPolicyPremiumUnmodified,
		SurchargeInfo:                inputs.SurchargeInfo,
	}
}

type QuoteOptionInputs struct {
	IndicationOptionInputs
	SubtotalPremium                 int32
	SafetyDiscountPremium           int32
	RoundedSafetyDiscountPremium    int32
	SafetyDiscountPercentage        int32
	RoundedSafetyDiscountPercentage int32
	RoundedPreDiscountTotalPremium  int32
	TotalSurchargePremium           *int32
	NegotiatedRates                 *NegotiatedRates
}

func NewQuoteOption(inputs QuoteOptionInputs) *IndicationOption {
	return &IndicationOption{
		ID:                              inputs.ID,
		SubmissionID:                    inputs.SubmissionID,
		TotalPremium:                    inputs.TotalPremium,
		SubtotalPremium:                 inputs.SubtotalPremium,
		FlatCharges:                     inputs.FlatCharges,
		SafetyDiscountPremium:           inputs.SafetyDiscountPremium,
		RoundedSafetyDiscountPremium:    inputs.RoundedSafetyDiscountPremium,
		RoundedSafetyDiscountPercentage: inputs.RoundedSafetyDiscountPercentage,
		RoundedPreDiscountTotalPremium:  inputs.RoundedPreDiscountTotalPremium,
		SafetyDiscountPercentage:        inputs.SafetyDiscountPercentage,
		Coverages:                       inputs.Coverages,
		CreatedAt:                       inputs.CreatedAt,
		OptionTag:                       inputs.OptionTag,
		IsRecommended:                   inputs.IsRecommended,
		TotalPowerUnits:                 inputs.TotalPowerUnits,
		TotalTrailerUnits:               inputs.TotalTrailerUnits,
		TIV:                             inputs.Tiv,
		TotalMiles:                      inputs.TotalMiles,
		PremiumPerUnit:                  inputs.PremiumPerUnit,
		TotalSurchargePremium:           inputs.TotalSurchargePremium,
		TotalPolicyPremiumUnmodified:    inputs.TotalPolicyPremiumUnmodified,
		SurchargeInfo:                   inputs.SurchargeInfo,
		NegotiatedRates:                 inputs.NegotiatedRates,
	}
}

// Until we finalize the required inputs, these structs will be marshaled into
// JSON and stored into the DB as json columns to facilitate faster iteration.
// Be careful about renaming fields here, and if you add a new member, you
// may need to add a default during Unmarshal.

// TODO: support flexible descriptions for declined reasons
type StateMetadata struct {
	Description     string
	Time            time.Time
	PreviousState   state_enums.AppState
	IsRatingFailure bool
}

type CompanyInfo struct {
	// Copier is being used to populate renewal application with the
	// information that should be carried over from the original application.
	DOTNumber          int64
	Name               string
	RadiusOfOperation  []*MileageRadiusRecord
	NumberOfPowerUnits int
	ProjectedMileage   int
	USState            us_states.USState
	TerminalLocations  *[]TerminalLocation
	PremiumTaxRecords  []PremiumTaxRecord `copier:"-"`
}

type PremiumTaxRecord struct {
	TaxCode               string
	JurisdictionName      string
	JurisdictionType      string
	LineOfBusinessDetails []LineOfBusinessDetail
}

type LineOfBusinessDetail struct {
	CoverageType app_enums.Coverage
	TaxValue     string
}

func (c *CompanyInfo) UnmarshalJSON(bytes []byte) error {
	type alias CompanyInfo
	aux := &struct {
		USState *string
		*alias
	}{
		alias: (*alias)(c),
	}
	if err := json.Unmarshal(bytes, &aux); err != nil {
		return errors.Wrap(err, "unable to unmarshal into aux company info")
	}

	if aux.USState != nil {
		usState, err := us_states.StrToUSState(*aux.USState)
		if err != nil {
			return errors.Wrapf(err, "unable to parse %s us state for company info", *aux.USState)
		}
		c.USState = usState
	}
	return nil
}

type FlatfileMetadata struct {
	FlatfileHandle string
	FileMetadata   FileMetadata
}

type ImplerMetadata struct {
	ImplerHandle string
	FileMetadata FileMetadata
}

type FileMetadata struct {
	Name   string
	Handle *uuid.UUID
}

// Indication form defs
// TerminalLocation defines model for TerminalLocation.
type TerminalLocation struct {
	AddressLineOne string
	AddressLineTwo *string
	IsGated        bool
	IsGuarded      bool
	TypeOfTerminal app_enums.TypeOfTerminal

	// Two character short code for the US state the driver is licensed in.
	UsState               us_states.USState
	ZipCode               int
	ZipCodeString         string
	IsDeletedByUW         bool
	IsCreatedByUW         bool                   // true means that location was added by underwriter in UW panel
	CargoTerminalSchedule *CargoTerminalSchedule // terminal schedule info for Cargo at Terminals coverage
}

// TerminalSchedule represents a scheduled terminal for Cargo at Terminals coverage
type CargoTerminalSchedule struct {
	ID                     uuid.UUID
	Limit                  *int32
	ConstructionClass      *app_enums.ConstructionClass     // Optional, defaults to "Frame"
	PublicProtectionClass  *app_enums.PublicProtectionClass // default to 1-8, will be consumed from Verisk API later
	PrivateTheftProtection app_enums.PrivateTheftProtection
	PrivateFireProtection  app_enums.PrivateFireProtection
}

// GetAddress returns the concatenated address of the terminal location
func (c *TerminalLocation) GetAddress() string {
	return c.AddressLineOne +
		" " +
		pointer_utils.StringValOr(c.AddressLineTwo, "") +
		", " +
		c.UsState.String() +
		" " +
		c.ZipCodeString
}

type CargoTerminalScheduleLocationProtoEnums struct {
	ConstructionClass      model.TerminalConstructionClass
	PublicProtectionClass  model.TerminalPublicProtectionClass
	PrivateTheftProtection model.TerminalPrivateTheftProtectionSystem
	PrivateFireProtection  model.TerminalPrivateFireProtectionSystem
}

func (c *CargoTerminalSchedule) TransformEnumsToProto() (*CargoTerminalScheduleLocationProtoEnums, error) {
	if c == nil {
		return nil, errors.New("cargo terminal schedule is nil")
	}

	constructionClass, err := app_enums.TransformApplicationConstructionClassToProto(
		c.ConstructionClass,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to transform construction class %s", *c.ConstructionClass)
	}
	publicProtectionClass, err := app_enums.TransformApplicationPublicProtectionClassToProto(
		c.PublicProtectionClass,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to transform public protection class %s", c.PublicProtectionClass)
	}
	privateTheftProtection, err := app_enums.TransformApplicationPrivateTheftProtectionToProto(
		c.PrivateTheftProtection,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to transform private theft protection %s", c.PrivateTheftProtection)
	}
	privateFireProtection, err := app_enums.TransformApplicationPrivateFireProtectionToProto(
		c.PrivateFireProtection,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to transform private fire protection %s", c.PrivateFireProtection)
	}

	return &CargoTerminalScheduleLocationProtoEnums{
		ConstructionClass:      constructionClass,
		PublicProtectionClass:  publicProtectionClass,
		PrivateTheftProtection: privateTheftProtection,
		PrivateFireProtection:  privateFireProtection,
	}, nil
}

// IsIdentical compares the current TerminalLocation instance with another instance
// to determine if they are identical based on specific attributes.
func (c *TerminalLocation) IsIdentical(locationToCompare *TerminalLocation) bool {
	if locationToCompare == nil && c == nil {
		return true
	} else if locationToCompare == nil || c == nil {
		return false
	}
	return c.IsGated == locationToCompare.IsGated && c.IsGuarded == locationToCompare.IsGuarded && c.AddressLineOne == locationToCompare.AddressLineOne && c.AddressLineTwo == locationToCompare.AddressLineTwo && c.TypeOfTerminal == locationToCompare.TypeOfTerminal && c.UsState == locationToCompare.UsState && c.ZipCodeString == locationToCompare.ZipCodeString
}

func (c *TerminalLocation) UnmarshalJSON(bytes []byte) error {
	type alias TerminalLocation
	aux := &struct {
		// Replace as string for manual unmarshall
		UsState string

		*alias
	}{
		alias: (*alias)(c),
	}
	if err := json.Unmarshal(bytes, &aux); err != nil {
		return errors.Wrap(err, "unable to unmarshal into aux terminal location")
	}

	usState, err := us_states.StrToUSState(aux.UsState)
	if err != nil {
		return errors.Wrapf(err, "unable to parse %s us state for company info", aux.UsState)
	}
	c.UsState = usState
	return nil
}

type MileageRadiusRecord struct {
	PercentageOfFleet int32
	RadiusBucket      app_enums.MileageRadiusBucket
}

type EquipmentInfo struct {
	OperatingClassDistribution []OperatingClassDistributionRecord
	PrimaryOperatingClass      *app_enums.OperatingClass

	PrimaryCommodity *old_enums.PrimaryCommodityHauled
	PrimaryCategory  *app_enums.CommodityCategory

	EquipmentList         EquipmentList `copier:"-"`
	CommodityDistribution *CommodityDistribution
}

func (e *EquipmentInfo) VINsList() []string {
	if e == nil {
		return nil
	}

	var retval []string
	for _, vehicle := range e.EquipmentList.Info {
		vin := vin_utils.TransformVIN(vehicle.VIN)
		if vin != "" {
			retval = append(retval, vin)
		}
	}
	return retval
}

type CommodityDistribution struct {
	Commodities []WeightedCommodityRecord

	// We accept max 5 commodities from agents with metadata
	// like (category - $ avg - $ max - % hauls)
	// AdditionalCommodities will store the excess commodities
	// which the agent enters without the metadata
	AdditionalCommodities *AdditionalCommoditiyRecord
}

type WeightedCommodityRecord struct {
	Category             app_enums.CommodityCategory
	Commodity            Commodity
	AvgDollarValueHauled int64
	MaxDollarValueHauled int64
	PercentageOfHauls    int32
}

type Commodity struct {
	// Type is the actual commodity we map
	// to the free form text
	Type *app_enums.CommodityHauled

	// Label is the free form text which
	// the agent enters
	Label string
}

type AdditionalCommoditiyRecord struct {
	Commodities       string
	PercentageOfHauls int32
}

type EquipmentList struct {
	Info             []EquipmentListRecord
	FlatfileMetadata *FlatfileMetadata
	ImplerMetadata   *ImplerMetadata
}

func (e *EquipmentList) GetFileHandle() *uuid.UUID {
	if e != nil && e.ImplerMetadata != nil {
		return e.ImplerMetadata.FileMetadata.Handle
	}

	if e != nil && e.FlatfileMetadata != nil {
		return e.FlatfileMetadata.FileMetadata.Handle
	}

	return nil
}

func (e *EquipmentList) GetFileName() string {
	if e == nil {
		return ""
	}

	if e.ImplerMetadata != nil {
		return e.ImplerMetadata.FileMetadata.Name
	}

	if e.FlatfileMetadata != nil {
		return e.FlatfileMetadata.FileMetadata.Name
	}

	return ""
}

type EquipmentListRecord struct {
	VIN         string
	StatedValue *int32
}

type OperatingClassDistributionRecord struct {
	Class             app_enums.OperatingClass
	PercentageOfFleet int32
}

type LossInfo struct {
	LossRunSummary []LossRunSummaryPerCoverage
}

type LossRunSummaryPerCoverage struct {
	CoverageType app_enums.Coverage
	Summary      []LossRunSummaryRecord
}

type LossRunSummaryRecord struct {
	PolicyPeriodStartDate time.Time
	PolicyPeriodEndDate   time.Time
	NumberOfPowerUnits    int32
	LossIncurred          int32
	NumberOfClaims        int32
	IsNirvanaPeriod       *bool
	PeriodTag             app_enums.LossRunSummaryPeriod
}

type ByStartDateDesc []LossRunSummaryRecord

func (a ByStartDateDesc) Len() int      { return len(a) }
func (a ByStartDateDesc) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a ByStartDateDesc) Less(i, j int) bool {
	return a[i].PolicyPeriodStartDate.After(a[j].PolicyPeriodStartDate)
}

type CoverageInfo struct {
	Coverages                        []CoverageDetails
	CoveragesWithCombinedDeductibles *CombinedDeductibleCoverages
	EffectiveDate                    time.Time `copier:"-"`
}

// CombinedDeductibleCoverages stores information regarding which
// coverages have a combined deductible. The actual deductible amount
// is still stored in the `CoverageDetails` struct.
type CombinedDeductibleCoverages struct {
	CombinedCoveragesList []CombinedCoverages
}

type CombinedCoverages map[app_enums.Coverage]bool

func (c *CoverageInfo) GetCoverage(typ app_enums.Coverage) *CoverageDetails {
	for i := range c.Coverages {
		if c.Coverages[i].CoverageType == typ {
			return &c.Coverages[i]
		}
	}
	return nil
}

func (c *CoverageInfo) ContainsCoverage(coverageToFind app_enums.Coverage) bool {
	for i := range c.Coverages {
		if c.Coverages[i].CoverageType == coverageToFind {
			return true
		}
	}
	return false
}

func (c *CoverageInfo) GetCoverageTypes() []app_enums.Coverage {
	var retval []app_enums.Coverage
	for _, cov := range c.Coverages {
		retval = append(retval, cov.CoverageType)
	}
	return retval
}

func (c *CoverageInfo) GetPrimaryCoverages() []app_enums.Coverage {
	var retval []app_enums.Coverage
	for i := range c.Coverages {
		if c.Coverages[i].CoverageType.IsPrimaryCoverage() {
			retval = append(retval, c.Coverages[i].CoverageType)
		}
	}
	return retval
}

func (c *CoverageInfo) GetPrimaryCoveragesDetails() []CoverageDetails {
	var retVal []CoverageDetails
	for i := range c.Coverages {
		if c.Coverages[i].CoverageType.IsPrimaryCoverage() {
			retVal = append(retVal, c.Coverages[i])
		}
	}
	return retVal
}

func (c *CoverageInfo) GetAncillaryCoveragesDetails() []CoverageDetails {
	return FilterAncillaryCoverages(c.Coverages)
}

// FindCoverageIndex returns the index of the coverage in the coverages slice.
// If the coverage is not found, it returns -1.
func (c *CoverageInfo) FindCoverageIndex(covType app_enums.Coverage) int {
	for i := range c.Coverages {
		if c.Coverages[i].CoverageType == covType {
			return i
		}
	}
	return -1
}

func FilterAncillaryCoverages(coverages []CoverageDetails) []CoverageDetails {
	var ancillaryCoverages []CoverageDetails
	for i := range coverages {
		if !coverages[i].CoverageType.IsPrimaryCoverage() {
			ancillaryCoverages = append(ancillaryCoverages, coverages[i])
		}
	}
	return ancillaryCoverages
}

// NOTE: Deductible, Premium, PremiumPerUnit are USD
type CoverageDetails struct {
	CoverageType app_enums.Coverage
	// TODO: deductible should be an enum
	// Deductibles can be combined across multiple coverages
	// The data regarding which coverages are combined is stored
	// within CombinedDeductibleCoverages on CoverageInfo struct
	Deductible     *int32
	Premium        *int32
	PremiumPerUnit *int32
	TIVPercentage  *float32
	// Limit of coverage
	Limit *int32
	// PremiumPerHundredMiles total premium for every 100 miles driven
	PremiumPerHundredMiles *float32
	// SymbolsAndDefinitions stores all symbols and definitions for the coverage
	SymbolsAndDefinitions *[]SymbolAndDefinition

	TraditionalPremium   *int32
	NegotiatedPremium    *int32
	CollisionPremium     *float64
	ComprehensivePremium *float64
	UnmodifiedPremium    *int32
}

func IsCoveragePresent(coverageDetailsArr []CoverageDetails, cov app_enums.Coverage) bool {
	for _, coverage := range coverageDetailsArr {
		if coverage.CoverageType == cov {
			return true
		}
	}
	return false
}

// SymbolAndDefinition stores symbols and definitions for coverages
type SymbolAndDefinition struct {
	// Symbol represents the commercial auto symbol
	Symbol string
	// Definition gives a verbal explanation of the symbol
	Definition string
}

// Addl info defs

type AdditionalLossInfo struct {
	Files   []FileMetadata `copier:"-"`
	Comment null.String
}

type DriverListRecord struct {
	DriverLicenseNumber string
	// TODO: Change this to common-go/us_states.USState
	UsState   string
	DateHired time.Time
	// The next 3 are optional
	DateOfBirth       null.Time
	FirstName         null.String
	LastName          null.String
	YearsOfExperience *float32
}

func NewDriverListRecord(
	driverLicenseNumber, usState string, dateHired time.Time,
	dateOfBirth null.Time, yearsOfExperience *float32, firstName, lastName null.String,
) *DriverListRecord {
	return &DriverListRecord{
		DriverLicenseNumber: driverLicenseNumber,
		UsState:             usState, DateHired: dateHired, DateOfBirth: dateOfBirth,
		FirstName: firstName, LastName: lastName, YearsOfExperience: yearsOfExperience,
	}
}

type DriversInfo struct {
	FlatfileMetadata *FlatfileMetadata
	ImplerMetadata   *ImplerMetadata
	Drivers          []DriverListRecord
}

func (f *DriversInfo) GetFileHandle() *uuid.UUID {
	if f != nil && f.ImplerMetadata != nil {
		return f.ImplerMetadata.FileMetadata.Handle
	}

	if f != nil && f.FlatfileMetadata != nil {
		return f.FlatfileMetadata.FileMetadata.Handle
	}

	return nil
}

func (f *DriversInfo) GetFileName() string {
	if f == nil {
		return ""
	}

	if f.ImplerMetadata != nil {
		return f.ImplerMetadata.FileMetadata.Name
	}

	if f.FlatfileMetadata != nil {
		return f.FlatfileMetadata.FileMetadata.Name
	}

	return ""
}

type AdditionalCommodityInfo struct {
	Commodities []app_enums.AdditionalInformationCommodity
	Comment     null.String
}

// AdditionalInfoExtraMetadata captures additional metadata for the addl info
// step
type AdditionalInfoExtraMetadata struct {
	NumOwnerOperatorUnits null.Int
	PercentageOfSubhaul   *float32
	OverallComment        null.String
}

type AdditionalInsuredInfo struct {
	Name  string
	Email null.String
}

type RetailerInfo struct {
	FirstName *string `json:"firstName"`
	LastName  *string `json:"lastName"`
	Email     *string `json:"email"`
	Agency    *string `json:"agency"`
}

type AdditionalInfoCoverageMetadata struct {
	AdditionalIncumbentInfo *AdditionalIncumbentInfo
}

type AdditionalIncumbentInfo struct {
	IsALIncumbent bool
}

type LargeLoss struct {
	Date         time.Time
	CoverageType app_enums.Coverage
	LossIncurred int32
	Description  string
}

// UnderwriterInput captures the explicit input provided by the Underwriter
// for a submission apart from the overrides
type UnderwriterInput struct {
	LargeLosses               []LargeLoss
	AggregateCreditByCoverage AggregateCreditByCoverage
	VehicleZoneDistribution   *[]VehicleZoneRecord
	NegotiatedRates           *NegotiatedRates
	// TransientSafetyCredit is a temp field used to pipe in UW safety credit
	// as a proxy for Nirvana Safety score.
	TransientSafetyCredit   float32
	RatingAddress           *TerminalLocation
	FetchAttractScore       *bool
	DepositAmount           *DepositAmount
	MinimumMileageGuarantee *MinimumMileageGuarantee
}

type RateMLLargeLossesInputs struct {
	LargeLossesProxied bool
	LargeLosses        []LargeLoss
}

type RateMLUnderwritingEntityInputs struct {
	SafetyModAllCov float64
	AlCredit        float64
	ApdCredit       float64
	MtcCredit       float64
}

type ExtraPricingInfo struct {
	CompanyExtraPricingInfo *CompanyExtraPricingInfo

	DecodedVehicles []*DecodedVehicle

	RateableDrivers []*RateableDriver

	// This field is used for backwards compatibility with
	// old RateML models that calculate outputs for all
	// packages at the same time. In such context, this field
	// is used to pick the relevant fields.
	//
	// In the future, RateML models should return package-agnostic
	// fields. And the differences in packages should be
	// represented as a difference in the inputs passed on to
	// the model.
	PackageType app_enums.IndicationOptionTag
}

type CompanyExtraPricingInfo struct {
	USState string
	ZipCode string

	YearsInBusiness int64

	AverageMiles               float64
	AverageCombinedGrossWeight float64
	CrashFrequency             float64
	VehicleInspectionRatio     float64
	MaintenanceViolationsRatio float64
	UnsafeViolationRatio       float64
	InspectionIndicator        string
	LargeMachineryIndicator    bool
	PowerUnits                 int64
	DriversHiredLastYear       int64
	TotalDriversLastYear       int64
	ObjectiveGrade             *string
	NirvanaYearsRetained       *int64
	PriorCarrierYearsRetained  *int64
	UnsafeDrivingScore         *float64
}

type DecodedVehicle struct {
	VIN            string
	StatedValue    float64
	YearMade       int64
	IsoVehicleType iso_utils.VehicleType
	IsoWeightGroup iso_utils.WeightGroup
	StartZone      *string
	EndZone        *string
}

type RateableDriver struct {
	Id                   string
	MovingViolationCount float64
	AttractScore         *float64
	TenureInYears        *float64
	ModifiedMvrScore     *int64
}

type MinimumMileageGuarantee struct {
	IsMinimumMileageGuaranteed bool
}

type DepositAmount struct {
	MonthlyPaymentDeposit *float32
}

type NegotiatedRates struct {
	IsNegotiatedRatesApplicable bool
	IsNegotiatedRatesApplied    bool
	BaseLimitPremium            *int64
	ThresholdPremium            *int64
	Coverages                   []app_enums.Coverage
	Rules                       []NegotiatedRatesRule
	Details                     *NegotiatedRatesDetails
}

type NegotiatedRatesRule struct {
	RuleType     app_enums.NegotiatedRatesExemption
	IsApplicable bool
}

type NegotiatedRatesDetails struct {
	AlNegotiatedRate   int64
	AlTraditionalRate  *int64
	ApdNegotiatedRate  *int64
	ApdTraditionalRate *int64
	Exemption          app_enums.NegotiatedRatesExemption
	CaseDescription    string
}

type VehicleZone struct {
	StartZone int32
	EndZone   int32
}

type VehicleZoneRecord struct {
	StartZone            int32
	EndZone              int32
	PercentageOfVehicles int32
}

type AggregateCreditByCoverage struct {
	AutoLiability      *float32
	AutoPhysicalDamage *float32
	GeneralLiability   *float32
	MotorTruckCargo    *float32
}

// StateTransition represents a state transition in the application
type StateTransition struct {
	AppId     string
	Timestamp time.Time
	FromState state_enums.AppState
	ToState   state_enums.AppState
	Metadata  *StateMetadata
}

type ModelPinConfig struct {
	RateML      RateMLConfig
	Application ApplicationConfig
	Data        DataConfig
}

type ApplicationConfig struct {
	Flags            ApplicationFlags
	InsuranceCarrier constants.InsuranceCarrier
	IsNonAdmitted    bool
}

type ApplicationFlags struct {
	// UsePrimaryCategory is a flag to enable Primary Category
	// based rating logic in RateML.
	// Released on 08/01/2022.
	// Not used anymore in adaptors.
	UsePrimaryCategory bool
	// UseVehiclesService is a flag to enable the use of Vehicles
	// Service for VIN Decoder information.
	UseVehiclesServiceForVINProblems bool
	// PrefillAndAutoReviewVinProblems is a flag to enable pre-filling
	// and auto resolution (TRAILERS) wrt VIN problems
	PrefillAndAutoReviewVinProblems bool
	// GetDriverYoeFromAgentInput is a flag that denotes whether we should pick YOE from agent input instead of MVR
	GetDriverYoeFromAgentInput bool
}

func NewModelPinConfig(
	provider rtypes.RatemlModelProvider,
	usState us_states.USState,
	version rtypes.RatemlModelVersion,
	enableVehiclesService bool,
	sentryInputsDate time.Time,
	sentryInputsDumpDate time.Time,
	insuranceCarrier constants.InsuranceCarrier,
	isNonAdmitted bool,
	prefillAndAutoReviewVinProblems bool,
	getDriverYoeFromAgentInput bool,
) *ModelPinConfig {
	return &ModelPinConfig{
		RateML: RateMLConfig{
			Provider:                provider,
			USState:                 usState,
			Version:                 version,
			SmartIndicationDiscount: nil,
			Flags: RateMLFlags{
				IsSpareTrailerEnabled:           true,
				IsLargeLossProxyEnabled:         true,
				UseCorrectFmcsaYears:            true,
				IndicationPricingExperiment:     true,
				IsNonOwnedTrailerEnabled:        true,
				IsDefaultSafetyDiscountDisabled: true,
				IsUncountedMVCEnabled:           true,
			},
			Bugs: RateMLBugs{
				IsDriverCountTotalLastYearFixed: true,
				IsGLMisdeliveryLiquidsFixed:     true,
				IsMedPayLimitForWIFixed:         true,
				IsGhostTrailerCountFixed:        true,
				IsYearlyLossSummaryLabelFixed:   true,
			},
		},
		Application: ApplicationConfig{
			Flags: ApplicationFlags{
				UsePrimaryCategory:               true,
				UseVehiclesServiceForVINProblems: enableVehiclesService,
				PrefillAndAutoReviewVinProblems:  prefillAndAutoReviewVinProblems,
				GetDriverYoeFromAgentInput:       getDriverYoeFromAgentInput,
			},
			InsuranceCarrier: insuranceCarrier,
			IsNonAdmitted:    isNonAdmitted,
		},
		Data: DataConfig{
			SentryInputsDate:     pointer_utils.ToPointer(sentryInputsDate.Format("01/02/2006")),
			SentryInputsDumpDate: pointer_utils.ToPointer(sentryInputsDumpDate.Format("01/02/2006")),
		},
	}
}

type RateMLConfig struct {
	Provider                rtypes.RatemlModelProvider
	USState                 us_states.USState
	Version                 rtypes.RatemlModelVersion
	SmartIndicationDiscount *float64
	Bugs                    RateMLBugs
	Flags                   RateMLFlags
}

type DataConfig struct {
	SentryInputsDumpDate *string `json:",omitempty"`
	SentryInputsDate     *string `json:",omitempty"`
}

// RateMLFlags stores all the flags that we use in our rateml adaptors, such as
// new functionality releases or namespaces.
type RateMLFlags struct {
	// IsSpareTrailerEnabled is a flag to enable spare trailer logic in rateml
	// adaptor.
	// Released on 2022-06-17
	IsSpareTrailerEnabled bool
	// IsLargeLossProxyEnabled is a flag to enable large loss proxy in rateml
	// adaptor.
	// Released on 2022-07-10.
	IsLargeLossProxyEnabled bool
	// UseCorrectFmcsaYears is a flag to enable the correct calculation of
	// fmscaYears in the rateml adaptor
	// Released on 2023-03-01
	UseCorrectFmcsaYears bool
	// IndicationPricingExperiment is a flag to enable discount based on
	// expected price provided by the agent
	// Released on 2023-03-22
	IndicationPricingExperiment bool
	// IsNonOwnedTrailerEnabled is a flag to enable non-owned trailer logic in
	// rateml adaptor.
	// Released on 2023-04-05
	IsNonOwnedTrailerEnabled bool
	// IsNonOwnedTrailerDisabled is a flag to disable default safety
	// discount logic in rateml adaptor.
	// Released on 2023-10-20
	IsDefaultSafetyDiscountDisabled bool
	// IsUncountedMVCEnabled is a flag to enable excluded MVC logic in rateml
	// adaptor. Released on 2023-11-06
	IsUncountedMVCEnabled bool
}

// RateMLBugs stores all the bugs we fix in rateml code and adaptors.
type RateMLBugs struct {
	// IsDriverCountTotalLastYearFixed should be counting total drivers for
	// last year. Bug was that we were counting total number of drivers that
	// were hired before the last year.
	// Fixed in 06/22/2022.
	IsDriverCountTotalLastYearFixed bool
	// IsGLMisdeliveryLiquidsFixed was causing all applications to include
	// Misdelivery of Liquids flag, causing prices to increase undesirably.
	// This was caused because frontend was sending an array of all operating
	// classes with 0 percentages for the non-selected ones, and then rateml
	// was failing to check for that.
	// Fixed in 06/30/2022.
	IsGLMisdeliveryLiquidsFixed bool
	// IsMedPayLimitForWIFixed was causing all the WI apps to have a $500
	// instead of $1,000 limit for MedPay coverage. No Premium changes, but
	// fixing for accuracy.
	// Fixed on 07/15/2022.
	IsMedPayLimitForWIFixed bool
	// IsGhostTrailerCountFixed was causing all application to count trucks in the
	// trailer vs non-trailer logic which results in pricing differences based on the amount of ghost trailers
	// Fixed on 06/16/2023
	IsGhostTrailerCountFixed bool

	// IsYearlyLossSummaryLabelFixed was causing all application incorrectly label the yearly loss summary
	// Issue came from the time-centric approach to tag generation
	// Fixed on 09/05/2023
	IsYearlyLossSummaryLabelFixed bool
}

func (r *RateMLConfig) UnmarshalJSON(bytes []byte) error {
	type alias RateMLConfig
	aux := &struct {
		USState *string
		*alias
	}{
		alias: (*alias)(r),
	}
	if err := json.Unmarshal(bytes, &aux); err != nil {
		return errors.Wrap(err, "unable to unmarshal into aux rateml config")
	}

	if aux.USState != nil {
		usState, err := us_states.StrToUSState(*aux.USState)
		if err != nil {
			return errors.Wrapf(err, "unable to parse %s us state for rateml config", *aux.USState)
		}
		r.USState = usState
	}

	return nil
}

func GetOptionByTag(tag app_enums.IndicationOptionTag,
	opts []IndicationOption,
) *IndicationOption {
	for _, o := range opts {
		if o.OptionTag == tag {
			return &o
		}
	}
	return nil
}

// AdditionalEmailInfo holds information about emails which are coupled with an application.
type AdditionalEmailInfo struct {
	// TelematicsConsentReminder holds info about the Telematics Consent Reminder emails.
	TelematicsConsentReminder EmailInfo
}

type EmailInfo struct {
	Preference EmailPreference
	JobStatus  []EmailJobStatus
}

type EmailPreference struct {
	Preference app_enums.EmailPreference
	UpdatedAt  time.Time
}

type EmailJobStatus struct {
	JobRunID     string
	JobRunNumber int32
	Status       app_enums.EmailJobStatus
	UpdatedAt    time.Time
}

type TelematicsConsentRequestEmail struct {
	CompletedAt   null.Time        // CompletedAt is set when the agent marks the email as sent OR sends the email through Nirvana.
	EmailJobRunID *jtypes.JobRunId // EmailJobRunID is set when the agent sends the consent request email through Nirvana.
}

type TelematicsConsentRequestSMS struct {
	CompletedAt null.Time        // CompletedAt is set when the agent marks the SMS as sent OR sends the SMS through Nirvana.
	SMSJobRunID *jtypes.JobRunId // SMSJobRunID is set when the agent sends the consent request SMS through Nirvana.
}

// TelematicsInfo holds information regarding the telematics data for an application.
type TelematicsInfo struct {
	TelematicsDataStatus          app_enums.TelematicsDataStatus
	TelematicsConnHandleId        uuid.UUID
	TelematicsPipelineStartedAt   *time.Time
	TelematicsConsentRequestEmail *TelematicsConsentRequestEmail
	TelematicsConsentRequestSMS   *TelematicsConsentRequestSMS
	// FirstPipelineSuccessAt denotes the timestamp at which
	// the first successful pipeline run finished
	FirstPipelineSuccessAt *time.Time
	// FirstPipelineSuccessWithRaceCondition denotes whether a race condition was encountered while storing it
	FirstPipelineSuccessWithRaceCondition *bool
}

type PricingMetadata struct {
	TargetPrices map[app_enums.Coverage]TargetPrice
}

type TargetPrice struct {
	TotalPremium *float32
}

type SurchargeInfo struct {
	TotalInsuranceSurchargePremium *float64
	AutoInsuranceSurchargePremium  *float64
	GLInsuranceSurchargePremium    *float64
	MTCInsuranceSurchargePremium   *float64
	AutoLGPTSurchargePremium       *float64
	GLLGPTSurchargePremium         *float64
	MTCLGPTSurchargePremium        *float64
	ALPolicySurplusLinesTax        *float64
	ALPolicyStampingFee            *float64
	APDPolicySurplusLinesTax       *float64
	APDPolicyStampingFee           *float64
	GLPolicySurplusLinesTax        *float64
	GLPolicyStampingFee            *float64
	MTCPolicySurplusLinesTax       *float64
	MTCPolicyStampingFee           *float64
}

func (a *Application) Validate(app nirvanaapp.App) error {
	panic("implement me")
}

func (a *Application) Construct(ctx context.Context, deps nirvanaapp.Deps, app nirvanaapp.App) error {
	panic("implement me")
}

var _ nirvanaapp.NirvanaImpl = (*Application)(nil)
