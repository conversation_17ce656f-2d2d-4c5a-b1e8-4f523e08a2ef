package forms

import (
	"context"
	"sort"
	"strconv"

	"nirvanatech.com/nirvana/common-go/time_utils"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	oapiforms "nirvanatech.com/nirvana/openapi-specs/components/forms"
	support_defs "nirvanatech.com/nirvana/support"
	support "nirvanatech.com/nirvana/support/agency"
)

func GetPrefillPreBindInfoAuthz(
	ctx context.Context,
) common.HandlerAuthzResponse {
	return common.HandlerAuthzResponse{IsAuthorized: true}
}

func HandleGetPrefillPreBindInfo(
	ctx context.Context,
	deps deps.Deps,
	req GetPrefillPreBindInfoRequest,
) GetPrefillPreBindInfoResponse {
	application, err := deps.ApplicationWrapper.GetAppById(ctx, req.ApplicationID)
	if err != nil {
		log.Error(ctx, "Error fetching application in Prefill Pre bind info", log.Err(err))
		err = common.NewNirvanaBadRequestErrorf(err, common.EntityApplication, req.ApplicationID)
		return GetPrefillPreBindInfoResponse{Error: err}
	}

	if application == nil {
		log.Error(ctx, "Application not found in Prefill Pre bind info", log.Err(err))
		appNotFoundError := errors.New("Application not found in Prefill Pre bind info")
		err = common.NewNirvanaNotFoundErrorf(appNotFoundError, common.EntityApplication, req.ApplicationID)
		return GetPrefillPreBindInfoResponse{Error: err}
	}

	if application.IsRenewal() {
		closestCommisionRenewalRate, err := findCommissionRate(application.AgencyID.String(), application.CoverageInfo.EffectiveDate.Format(time_utils.LayoutDDMMYYYYWitSlash), support.AgencyCommisionRenewalRates)
		if err != nil {
			log.Error(ctx, "Error finding commission rate", log.Err(err))
			err = common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, req.ApplicationID)
			return GetPrefillPreBindInfoResponse{
				Error: err,
			}
		}

		if closestCommisionRenewalRate == nil {
			return GetPrefillPreBindInfoResponse{Success: oapiforms.PrefillPreBindInfo{
				CommissionRate: nil,
			}}
		}

		commisionRateRenewal, err := strconv.Atoi(*closestCommisionRenewalRate)
		if err != nil {
			error := errors.New("Error converting commission rate to int")
			err = common.NewNirvanaInternalServerErrorf(error, common.EntityApplication, req.ApplicationID)
			return GetPrefillPreBindInfoResponse{Error: err}
		}

		return GetPrefillPreBindInfoResponse{Success: oapiforms.PrefillPreBindInfo{
			CommissionRate: pointer_utils.ToPointer(commisionRateRenewal),
		}}

	} else {
		closestCommissionNBRate, err := findCommissionRate(application.AgencyID.String(), application.CoverageInfo.EffectiveDate.Format(time_utils.LayoutDDMMYYYYWitSlash), support.AgencyCommisionNewBusinessRates)
		if err != nil {
			log.Error(ctx, "Error finding commission rate", log.Err(err))
			err = common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, req.ApplicationID)
			return GetPrefillPreBindInfoResponse{Error: err}
		}

		if closestCommissionNBRate == nil {
			return GetPrefillPreBindInfoResponse{Success: oapiforms.PrefillPreBindInfo{
				CommissionRate: nil,
			}}
		}

		commisionRateNB, err := strconv.Atoi(*closestCommissionNBRate)
		if err != nil {
			error := errors.New("Error converting commission rate to int")
			err = common.NewNirvanaInternalServerErrorf(error, common.EntityApplication, req.ApplicationID)
			return GetPrefillPreBindInfoResponse{Error: err}
		}

		return GetPrefillPreBindInfoResponse{Success: oapiforms.PrefillPreBindInfo{
			CommissionRate: pointer_utils.ToPointer(commisionRateNB),
		}}
	}
}

type GetPrefillPreBindInfoRequest struct {
	ApplicationID string
}

type GetPrefillPreBindInfoResponse struct {
	Success oapiforms.PrefillPreBindInfo
	Error   error
}

func findCommissionRate(agencyID, effectiveDate string, rates []support_defs.AgencyCommisionRateStruct) (*string, error) {
	// Parse the input effective date
	inputDate, err := time_utils.ParseEffectiveDate(effectiveDate)
	if err != nil {
		return nil, err
	}

	if inputDate == nil {
		return nil, errors.New("Invalid effective date")
	}

	var mostEligibleRate *support_defs.AgencyCommisionRateStruct

	var eligibleAgencyRates []support_defs.AgencyCommisionRateStruct

	for _, rate := range rates {
		if rate.AgencyId == agencyID {
			eligibleAgencyRates = append(eligibleAgencyRates, rate)
		}
	}

	// sort the eligibleAgencyRates by effective date
	sort.Slice(eligibleAgencyRates, func(i, j int) bool {
		iDate, err := time_utils.ParseEffectiveDate(eligibleAgencyRates[i].EffectiveDate)
		if err != nil || iDate == nil {
			return false
		}
		jDate, err := time_utils.ParseEffectiveDate(eligibleAgencyRates[j].EffectiveDate)
		if err != nil || jDate == nil {
			return false
		}

		iDateVal := *iDate
		jDateVal := *jDate

		return iDateVal.Before(jDateVal)
	})

	for _, rate := range eligibleAgencyRates {
		rateDate, err := time_utils.ParseEffectiveDate(rate.EffectiveDate)
		if err != nil {
			continue
		}

		if rateDate == nil {
			continue
		}

		if !inputDate.Before(*rateDate) {
			mostEligibleRate = &rate
		}

	}

	if mostEligibleRate == nil {
		// nolint: nilnil
		return nil, nil
	}

	return pointer_utils.String(mostEligibleRate.CommisionRate), nil
}
