package builders

import (
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/claims/fnol_intake_emails/db"
)

type FnolIntakeEmailBuilder struct {
	object *db.FnolIntakeEmail
}

func NewFnolIntakeEmail() *FnolIntakeEmailBuilder {
	return &FnolIntakeEmailBuilder{object: &db.FnolIntakeEmail{}}
}

func (f *FnolIntakeEmailBuilder) Build() *db.FnolIntakeEmail {
	return f.object
}

func (f *FnolIntakeEmailBuilder) WithDefaultMockData() *FnolIntakeEmailBuilder {
	return &FnolIntakeEmailBuilder{
		&db.FnolIntakeEmail{
			Id:        uuid.New(),
			MessageId: "123456",
			FnolId:    uuid.New(),
			SentAt:    time.Now(),
			CreatedAt: time.Now(),
		},
	}
}

func (f *FnolIntakeEmailBuilder) WithFnolId(fnolId uuid.UUID) *FnolIntakeEmailBuilder {
	f.object.FnolId = fnolId
	return f
}

func (f *FnolIntakeEmailBuilder) WithMessageId(messageId string) *FnolIntakeEmailBuilder {
	f.object.MessageId = messageId
	return f
}
