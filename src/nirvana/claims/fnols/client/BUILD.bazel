load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "archive_fnols.go",
        "client.go",
        "deps.go",
        "fx.go",
        "generate_claim_number.go",
        "get_fnol_by_message_id.go",
        "get_submittable_provider_options.go",
        "ingest_attachments.go",
        "ingest_contacts.go",
        "ingest_vehicles.go",
        "send_fnol.go",
        "upsert_fnol.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/fnols/client",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/application/normalizer",
        "//nirvana/claims/client",
        "//nirvana/claims/enums",
        "//nirvana/claims/fnol_intake_emails/db",
        "//nirvana/claims/fnols/db",
        "//nirvana/claims/fnols/enums",
        "//nirvana/claims/metrics",
        "//nirvana/claims/nars",
        "//nirvana/claims/snapsheet/client",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/retry",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/tracing",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/emailer",
        "//nirvana/emailer/models",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/policy",
        "//nirvana/policy_common/constants",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "client_test",
    srcs = [
        "archive_fnols_test.go",
        "client_suite_test.go",
        "client_test.go",
        "generate_claim_number_test.go",
        "get_submittable_provider_options_test.go",
        "send_fnol_test.go",
    ],
    deps = [
        ":client",
        "//nirvana/claims/enums",
        "//nirvana/claims/fnols/db",
        "//nirvana/claims/fnols/enums",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/test_utils/builders/admitted_app",
        "//nirvana/common-go/test_utils/builders/application",
        "//nirvana/common-go/test_utils/builders/policy",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/uuid_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/file_upload",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted",
        "//nirvana/emailer",
        "//nirvana/emailer/models",
        "//nirvana/graphql-server/queryclient",
        "//nirvana/infra/authz",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/testfixtures/agency_fixture",
        "//nirvana/infra/fx/testfixtures/emailer_fixture",
        "//nirvana/infra/fx/testfixtures/fleet_fixture",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/policy",
        "//nirvana/policy/constants",
        "//nirvana/policy/enums",
        "//nirvana/quoting/app_state_machine/enums",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
