package client

import (
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/application/normalizer"
	fnol_intake_emails_db "nirvanatech.com/nirvana/claims/fnol_intake_emails/db"
	fnols_db "nirvanatech.com/nirvana/claims/fnols/db"
	"nirvanatech.com/nirvana/claims/metrics"
	claims_nars "nirvanatech.com/nirvana/claims/nars"
	snapsheet_client "nirvanatech.com/nirvana/claims/snapsheet/client"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/emailer"
	"nirvanatech.com/nirvana/policy"
)

type deps struct {
	fx.In

	ApplicationClient *normalizer.Client
	PolicyClient      policy.Client
	MetricsClient     metrics.Client
	NarsClient        *claims_nars.Client
	SnapsheetClient   *snapsheet_client.Client

	FnolWrapper            *fnols_db.DataWrapper
	FnolIntakeEmailWrapper *fnol_intake_emails_db.DataWrapper

	Emailer           emailer.Emailer
	FileUploadManager file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	FileUploadKeygen  file_upload_lib.DefaultS3Keygen
}
