load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "db",
    srcs = [
        "auto_submit.go",
        "deletes.go",
        "filters.go",
        "fnol_message_id.go",
        "fx.go",
        "object_defs.go",
        "serde_utils.go",
        "upsert_fnol.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/fnols/db",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/enums",
        "//nirvana/claims/fnols/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/claims",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_benb<PERSON><PERSON><PERSON>_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_multierr//:multierr",
    ],
)

go_test(
    name = "db_test",
    srcs = [
        "auto_submit_test.go",
        "object_defs_test.go",
        "serde_utils_test.go",
        "upsert_fnol_test.go",
        "wrapper_test.go",
    ],
    embed = [":db"],
    deps = [
        "//nirvana/claims/enums",
        "//nirvana/claims/fnols/enums",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/file_upload",
        "//nirvana/infra/fx/testloader",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
