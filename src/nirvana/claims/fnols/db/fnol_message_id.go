package db

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"nirvanatech.com/nirvana/db-api/db_models/claims"
)

func (w *DataWrapper) GetFnolByMessageId(ctx context.Context, messageId string) (*ClaimFnol, error) {
	queryMods := []qm.QueryMod{
		qm.LeftOuterJoin(
			fmt.Sprintf(
				"claims.%s on %s.%s = %s.%s",
				claims.TableNames.FnolIntakeEmails,
				claims.TableNames.FnolIntakeEmails,
				claims.FnolIntakeEmailColumns.FnolID,
				claims.TableNames.Fnols,
				claims.FnolColumns.ID),
		),
		qm.<PERSON>ad(claims.FnolRels.FnolAttachments),
		qm.Load(claims.FnolRels.FnolContacts),
		qm.Load(claims.FnolRels.FnolVehicles),
		claims.FnolIntakeEmailWhere.MessageID.EQ(messageId),
	}

	fnol, err := claims.Fnols(queryMods...).One(ctx, w.db)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get fnol by message ID")
	}

	f, err := claimFnolFromDb(fnol)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to convert FNOL with message ID %s from db", messageId)
	}
	return f, nil
}
