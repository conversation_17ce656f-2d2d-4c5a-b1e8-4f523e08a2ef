package client_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/finola/client"
	fnol_intake_email_client "nirvanatech.com/nirvana/claims/fnol_intake_emails/client"
	fnol_intake_email_db "nirvanatech.com/nirvana/claims/fnol_intake_emails/db"
	"nirvanatech.com/nirvana/claims/fnols/db"
	fnol_enums "nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/common-go/test_utils"
	policy_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func TestClient_CreateOrUpdateFnol(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		Client                 *client.Client
		FnolWrapper            *db.DataWrapper
		FnolIntakeEmailClient  *fnol_intake_email_client.Client
		FnolIntakeEmailWrapper *fnol_intake_email_db.DataWrapper
		PolicyWrapper          policy.DataWrapper
	}

	defer testloader.RequireStart(t, &env).RequireStop()

	superuser := test_utils.Superuser()
	ctx = authz.WithUser(ctx, superuser)
	testDate := "01/02/2023, 15:04:05"
	lossDateTime := "2023-01-02T15:04:05.000Z"

	policy := policy_builder.
		New().
		WithDefaultMockData().
		WithPolicyNumber(
			"NISTK",
			"1000001",
			2025,
		).
		Build()
	require.NoError(t, env.PolicyWrapper.InsertPolicy(ctx, policy))

	completeParsedFnolContent := client.ParsedFnolContent{
		IncidentDetails: client.IncidentDetails{
			PolicyNumber:             "NISTK1000001-25",
			IncidentDescription:      "Car accident on highway",
			DateOfLoss:               lossDateTime,
			LocationDetails:          "Highway 101",
			State:                    "CA",
			PoliceAgencyName:         "LAPD",
			PoliceReportNumber:       "PR123",
			InjuriesInvolved:         true,
			OwnVehicleInvolved:       true,
			OwnVehicleInvolvedVin:    "VIN123456789",
			OtherVehiclesInvolved:    true,
			OtherVehiclesInvolvedVin: "LICENSE123",
		},
		ReporterInformation: client.ReporterInformation{
			NoticeType:        "Claim",
			InsuredName:       "John Doe",
			ReporterFirstName: "Jane",
			ReporterLastName:  "Smith",
			ReporterEmail:     "<EMAIL>",
			ReporterPhone:     "555-1234",
		},
	}

	completeArgs := client.CreateOrUpdateFnolArgs{
		Date:              testDate,
		MessageId:         "msg123",
		ParsedFnolContent: &completeParsedFnolContent,
		References:        []string{},
	}

	validDraftFnol, err := db.NewClaimFnolBuilder(enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithStatus(fnol_enums.FnolStatusDraft).
		Build()
	require.NoError(t, err)

	tests := []struct {
		name                string
		argsFactory         func() client.CreateOrUpdateFnolArgs
		existingFnolFactory func() *db.ClaimFnol
		existingFnol        *db.ClaimFnol
		expectedError       bool
	}{
		{
			name: "create new FNOL with complete data",
			argsFactory: func() client.CreateOrUpdateFnolArgs {
				return completeArgs
			},
			existingFnolFactory: func() *db.ClaimFnol {
				return nil // No existing FNOL
			},
		},
		{
			name: "create new FNOL with missing reporter data fills in with N/A",
			argsFactory: func() client.CreateOrUpdateFnolArgs {
				args := completeArgs
				args.ParsedFnolContent.ReporterInformation.ReporterFirstName = ""
				args.ParsedFnolContent.ReporterInformation.ReporterLastName = ""
				args.ParsedFnolContent.ReporterInformation.ReporterPhone = ""
				args.MessageId = "msg124"
				return args
			},
			existingFnolFactory: func() *db.ClaimFnol {
				return nil // No existing FNOL
			},
		},
		{
			name: "error when existing FNOL is not draft",
			argsFactory: func() client.CreateOrUpdateFnolArgs {
				args := completeArgs
				args.MessageId = "msg456"
				args.References = []string{"original_msg123"}
				return args
			},
			// Create an existing FNOL that is not in draft status
			existingFnolFactory: func() *db.ClaimFnol {
				fnol := *validDraftFnol
				fnol.Id = uuid.New()
				fnol.Status = fnol_enums.FnolStatusSent
				return &fnol
			},
			expectedError: true,
		},
		{
			name: "update FNOL with email thread references",
			argsFactory: func() client.CreateOrUpdateFnolArgs {
				args := completeArgs
				args.MessageId = "msg789"
				args.References = []string{"original_msg1235", "reply_msg124"}
				return args
			},
			existingFnolFactory: func() *db.ClaimFnol {
				fnol := *validDraftFnol
				fnol.Id = uuid.New()
				return &fnol
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			args := tt.argsFactory()

			existingFnol := tt.existingFnolFactory()
			if existingFnol != nil {
				env.FnolWrapper.UpsertFnol(ctx, *existingFnol)

				fnolIntakeEmailArgs := fnol_intake_email_client.CreateFnolIntakeEmailArgs{
					MessageId: args.References[0],
					FnolId:    existingFnol.Id,
					SentAt:    time.Now(),
				}

				_, err := env.FnolIntakeEmailClient.CreateFnolIntakeEmail(ctx, fnolIntakeEmailArgs)
				require.NoError(t, err)

			}

			result, err := env.Client.CreateOrUpdateFnol(ctx, args)

			// Verify error expectations
			if tt.expectedError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)

			// Verify we can retrieve the persisted FNOL
			retrievedFnol, err := env.FnolWrapper.GetFnol(ctx, result.Id)
			require.NoError(t, err)
			assert.Equal(t, fnol_enums.FnolStatusDraft, retrievedFnol.Status)
			assert.Equal(t, "NISTK1000001-25", *retrievedFnol.PolicyNumber)
			assert.Equal(t, "Car accident on highway", *retrievedFnol.IncidentDescription)
			assert.Equal(t, "Highway 101", *retrievedFnol.LossLocation)
			assert.Equal(t, "CA", *retrievedFnol.LossState)
			assert.Equal(t, "LAPD", *retrievedFnol.PoliceAgencyName)
			assert.Equal(t, "PR123", *retrievedFnol.PoliceReportNumber)
			assert.Equal(t, 2, len(retrievedFnol.Vehicles))
			assert.Equal(t, 1, len(retrievedFnol.Contacts))

			reporter := retrievedFnol.GetReporter()

			assertWithDefaultIfEmpty(t, args.ParsedFnolContent.ReporterInformation.ReporterFirstName, reporter.FirstName)
			assertWithDefaultIfEmpty(t, args.ParsedFnolContent.ReporterInformation.ReporterLastName, reporter.LastName)
			assertWithDefaultIfEmpty(t, args.ParsedFnolContent.ReporterInformation.ReporterPhone, reporter.Phone)

			// Verify the FNOL intake email exists for FNOL if it was created
			if existingFnol == nil {
				fnolIntakeEmails, err := env.FnolIntakeEmailWrapper.GetFnolIntakeEmails(ctx, fnol_intake_email_db.FnolIntakeEmailFnolIdIs(result.Id))
				require.NoError(t, err)
				assert.Len(t, fnolIntakeEmails, 1)
				fnolIntakeEmail := fnolIntakeEmails[0]
				assert.Equal(t, fnolIntakeEmail.MessageId, fnolIntakeEmails[0].MessageId)
				assert.Equal(t, fnolIntakeEmail.FnolId, fnolIntakeEmails[0].FnolId)
			}
		})
	}
}

func assertWithDefaultIfEmpty(t *testing.T, expected, actual string) {
	if expected == "" {
		assert.Equal(t, "N/A", actual)
	} else {
		assert.Equal(t, expected, actual)
	}
}
