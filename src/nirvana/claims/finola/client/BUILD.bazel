load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "auto_submit.go",
        "client.go",
        "create_or_update_fnol.go",
        "deps.go",
        "email_response.go",
        "fx.go",
        "parse_fnol.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/finola/client",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/client",
        "//nirvana/claims/enums",
        "//nirvana/claims/fnol_intake_emails/client",
        "//nirvana/claims/fnols/client",
        "//nirvana/claims/fnols/db",
        "//nirvana/claims/fnols/enums",
        "//nirvana/claims/metrics",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/tracing",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/file_upload",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/llmops/src/go-client",
        "//nirvana/llmops/src/go-client/generated:llmops",
        "//nirvana/openapi-specs/components/claims",
        "//nirvana/policy",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "client_test",
    srcs = [
        "auto_submit_test.go",
        "create_or_update_fnol_test.go",
        "email_response_test.go",
    ],
    deps = [
        ":client",
        "//nirvana/claims/enums",
        "//nirvana/claims/fnol_intake_emails/client",
        "//nirvana/claims/fnol_intake_emails/db",
        "//nirvana/claims/fnols/db",
        "//nirvana/claims/fnols/enums",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/test_utils",
        "//nirvana/common-go/test_utils/builders/policy",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/uuid_utils",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/file_upload",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/emailer",
        "//nirvana/emailer/models",
        "//nirvana/infra/authz",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/policy/constants",
        "//nirvana/policy_common/constants",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_google_uuid//:uuid",
        "@com_github_launchdarkly_go_sdk_common_v3//ldvalue",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
