package client

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/claims/db"
	"nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
)

func (c *Client) newSnapsheetReservesSummary(lossRunsRow snowflake.LossRunsRow) (*ReservesSummary, error) {
	return &ReservesSummary{
		Claimant:       pointer_utils.StringValOr(lossRunsRow.CLAIMANT_NAME, ""),
		LineOfBusiness: lossRunsRow.LINE_CODE,
		Coverage:       lossRunsRow.TYPE,
		Status:         lossRunsRow.STATUS,
		Amounts: Amounts{
			LossPaid:               decimal.NewFromFloat(lossRunsRow.LOSS_PAID),
			MedicalPaid:            decimal.NewFromFloat(lossRunsRow.MEDICAL_PAID),
			ExpensesPaid:           decimal.NewFromFloat(lossRunsRow.EXPENSES_PAID),
			CurrentLossReserve:     decimal.NewFromFloat(lossRunsRow.LOSS_RESERVES),
			CurrentExpensesReserve: decimal.NewFromFloat(lossRunsRow.EXPENSES_RESERVES),
			CurrentMedicalReserve:  decimal.NewFromFloat(lossRunsRow.MEDICAL_RESERVES),
			GrossIncurred:          decimal.NewFromFloat(lossRunsRow.GROSS_INCURRED),
			OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromFloat(lossRunsRow.OTHER_RECOVERIES)),
			DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromFloat(lossRunsRow.DEDUCTIBLE_RECOVERIES)),
			SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromFloat(lossRunsRow.SALVAGE_RECOVERIES)),
			SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromFloat(lossRunsRow.SUBROGATION_RECOVERIES)),
		},
	}, nil
}

// newSnapsheetClaim creates a Claim from a db.Claim with provider Snapsheet
func (c *Client) newSnapsheetClaim(
	ctx context.Context, dbClaim db.Claim, lossRunsRows []snowflake.LossRunsRow,
) (*Claim, error) {
	if dbClaim.Source != enums.ClaimsProviderSnapsheet {
		return nil, errors.Newf(
			"claim with external id %s is not from Snapsheet", dbClaim.ExternalId,
		)
	}

	claim := Claim{
		ClaimNumber:     dbClaim.ClaimNumber,
		DateOpened:      dbClaim.CreatedAt,
		LossDate:        dbClaim.LossDatetime,
		LossState:       dbClaim.LossState,
		LossDescription: dbClaim.LossDescription,
		Status:          dbClaim.Status.String(),
	}

	latestClosedStatuses, err := c.deps.ClaimsClient.GetClaimStatusesBySource(
		ctx,
		enums.ClaimsProviderSnapsheet,
		dbClaim.ExternalId,
		db.StatusClaimOrderByCreatedAtDesc(),
		db.StatusIs(enums.ClaimStatusClosed),
	)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to get latest closed statuses for claim with claim number %s",
			dbClaim.ClaimNumber,
		)
	}
	if len(latestClosedStatuses) > 0 {
		claim.DateClosed = &latestClosedStatuses[0].CreatedAt
	}

	// we explicitly set the reserve summaries as a slice, to avoid it being null in template
	claim.ReservesSummaries = make([]ReservesSummary, 0, len(lossRunsRows))
	for _, lrr := range lossRunsRows {
		if lrr.DRIVER != nil {
			driver := Driver{
				FirstName: *lrr.DRIVER,
			}
			if !slice_utils.Contains(claim.Drivers, driver) {
				claim.Drivers = append(claim.Drivers, driver)
			}
		}

		summary, err := c.newSnapsheetReservesSummary(lrr)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to create reserves summary for claim with claim number %s",
				dbClaim.ClaimNumber,
			)
		}

		claim.ReservesSummaries = append(claim.ReservesSummaries, *summary)

		claim.TotalAmounts.Add(summary.Amounts)
	}

	return &claim, nil
}
