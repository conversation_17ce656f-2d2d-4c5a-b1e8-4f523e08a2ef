package client_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/benb<PERSON><PERSON>son/clock"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	cp_builders "nirvanatech.com/nirvana/claims/claim_parties/builders"
	nars_cp "nirvanatech.com/nirvana/claims/claim_parties/db/nars"
	nars_postings "nirvanatech.com/nirvana/claims/claim_postings/db/nars"
	cr_builders "nirvanatech.com/nirvana/claims/claim_reserves/builders"
	nars_cr "nirvanatech.com/nirvana/claims/claim_reserves/db/nars"
	cr_enums "nirvanatech.com/nirvana/claims/claim_reserves/enums"
	claims_client "nirvanatech.com/nirvana/claims/client"
	claim_builder "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/client"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/db"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	policy_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/policy"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/agency_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	policy_enums "nirvanatech.com/nirvana/policy/enums"
	"nirvanatech.com/nirvana/policy_common/constants"
)

func TestLossRunsRenewalsClientTestSuite(t *testing.T) {
	suite.Run(t, new(lossRunsRenewalsClientTestSuite))
}

type lossRunsRenewalsClientTestSuite struct {
	suite.Suite

	fxapp *fxtest.App
	clock *clock.Mock

	claimsClient *claims_client.Client

	claimPartiesWrapper  *nars_cp.DataWrapper
	claimPostingsWrapper *nars_postings.DataWrapper
	claimReservesWrapper *nars_cr.DataWrapper
	client               *client.Client
	insuredWrapper       db.Wrapper
	policyWrapper        policy.DataWrapper
	snowflakeWrapper     *snowflake.MockDataWrapper

	// NARS only seeded policies
	expiredPolicyNumberWithNarsClaimsOnly string
	expiredPolicyWithNarsClaimsOnly       *policy.Policy
	activePolicyNumberWithNoClaims        string
	activePolicyWithNoClaims              *policy.Policy
	activePolicyNumberWithNarsClaimsOnly  string
	activePolicyWithNarsClaimsOnly        *policy.Policy
	// Snapsheet only seeded policies
	expiredPolicyNumberWithSnapsheetClaimsOnly string
	expiredPolicyWithSnapsheetClaimsOnly       *policy.Policy
	// NARS and Snapsheet seeded policies
	activePolicyNumberWithSnapsheetAndNarsClaims string
	activePolicyWithSnapsheetAndNarsClaims       *policy.Policy

	// policyNumberToRenewalsPolicyMap maps the seeded policy numbers to their respective Policy
	// representation for the lossruns/renewals package
	policyNumberToRenewalsPolicyMap map[string]client.Policy
	// policyNumberToRenewalsClaimsMap maps the seeded policy numbers to their respective
	// Claims representation for the renewals_loss_runs package
	policyNumberToRenewalsClaimsMap map[string][]client.Claim
	dotNumberWithPolicies           int64

	usersFixture *users_fixture.UsersFixture
	testAgencyId uuid.UUID
}

func (s *lossRunsRenewalsClientTestSuite) SetupTest() {
	var env struct {
		fx.In

		Clk *clock.Mock

		ClaimsClient *claims_client.Client

		ClaimReservesWrapper *nars_cr.DataWrapper
		ClaimPartiesWrapper  *nars_cp.DataWrapper
		ClaimPostingsWrapper *nars_postings.DataWrapper
		Client               *client.Client
		InsuredWrapper       db.Wrapper
		PolicyWrapper        policy.DataWrapper
		SnowflakeWrapper     *snowflake.MockDataWrapper

		UsersFixture  *users_fixture.UsersFixture
		AgencyFixture *agency_fixture.AgencyFixture
	}

	s.fxapp = testloader.RequireStart(s.T(), &env)

	env.Clk.Set(time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC))
	s.clock = env.Clk

	s.claimsClient = env.ClaimsClient

	s.claimPartiesWrapper = env.ClaimPartiesWrapper
	s.claimPostingsWrapper = env.ClaimPostingsWrapper
	s.claimReservesWrapper = env.ClaimReservesWrapper
	s.client = env.Client
	s.insuredWrapper = env.InsuredWrapper
	s.policyWrapper = env.PolicyWrapper
	s.snowflakeWrapper = env.SnowflakeWrapper

	s.usersFixture = env.UsersFixture

	s.policyNumberToRenewalsPolicyMap = make(map[string]client.Policy)
	s.policyNumberToRenewalsClaimsMap = make(map[string][]client.Claim)

	s.dotNumberWithPolicies = 9483902
	s.testAgencyId = env.AgencyFixture.Agency.ID

	ctx := context.Background()
	s.seedPolicies(ctx)
	s.seedNarsClaims(ctx)
	s.seedSnapsheetClaims(ctx)
}

func (s *lossRunsRenewalsClientTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

func (s *lossRunsRenewalsClientTestSuite) seedPolicies(ctx context.Context) {
	s.expiredPolicyNumberWithNarsClaimsOnly = "NISTK1000001-21"
	p := policy_builder.
		New().
		WithDefaultMockData().
		WithCompanyName("BOLA'S TRUCKING LLC").
		WithDOTNumber(s.dotNumberWithPolicies).
		WithState(policy_enums.PolicyStateExpired).
		WithInsuranceCarrier(constants.InsuranceCarrierFalseLake).
		WithPolicyNumber("NISTK", "1000001", 2021).
		WithEffectiveDates(
			time_utils.NewDate(2021, 1, 1).ToTime(),
			time_utils.NewDate(2021, 12, 31).ToTime()).
		WithAgencyId(s.testAgencyId).
		Build()
	s.expiredPolicyWithNarsClaimsOnly = p
	s.Require().NoError(s.policyWrapper.InsertPolicy(ctx, p))

	s.policyNumberToRenewalsPolicyMap[s.expiredPolicyNumberWithNarsClaimsOnly] = client.Policy{
		PolicyNumber:     s.expiredPolicyNumberWithNarsClaimsOnly,
		InsuredName:      s.expiredPolicyWithNarsClaimsOnly.InsuredName,
		InsuranceCarrier: s.expiredPolicyWithNarsClaimsOnly.InsuranceCarrier.String(),
		EffectiveDate:    time_utils.DateFromTime(s.expiredPolicyWithNarsClaimsOnly.EffectiveDate),
		ExpirationDate:   time_utils.DateFromTime(s.expiredPolicyWithNarsClaimsOnly.EffectiveDateTo),
	}

	issuanceYear := s.clock.Now().Year()
	s.activePolicyNumberWithNoClaims = "NNFTK1000002-" + fmt.Sprintf("%02d", issuanceYear%100)
	p = policy_builder.
		New().
		WithDefaultMockData().
		WithPolicyNumber("NNFTK", "1000002", issuanceYear).
		WithDOTNumber(s.dotNumberWithPolicies).
		WithState(policy_enums.PolicyStateActive).
		WithEffectiveDates(
			time_utils.NewDate(issuanceYear, 1, 1).ToTime(),
			time_utils.NewDate(issuanceYear, 12, 31).ToTime()).
		WithAgencyId(s.testAgencyId).
		Build()
	s.activePolicyWithNoClaims = p
	s.Require().NoError(s.policyWrapper.InsertPolicy(ctx, p))

	s.policyNumberToRenewalsPolicyMap[s.activePolicyNumberWithNoClaims] = client.Policy{
		PolicyNumber:     s.activePolicyNumberWithNoClaims,
		InsuredName:      s.activePolicyWithNoClaims.InsuredName,
		InsuranceCarrier: s.activePolicyWithNoClaims.InsuranceCarrier.String(),
		EffectiveDate:    time_utils.DateFromTime(s.activePolicyWithNoClaims.EffectiveDate),
		ExpirationDate:   time_utils.DateFromTime(s.activePolicyWithNoClaims.EffectiveDateTo),
	}

	s.activePolicyNumberWithNarsClaimsOnly = "NISTK1000003-" + fmt.Sprintf("%02d", issuanceYear%100)
	p = policy_builder.
		New().
		WithDefaultMockData().
		WithPolicyNumber("NISTK", "1000003", issuanceYear).
		WithDOTNumber(s.dotNumberWithPolicies).
		WithState(policy_enums.PolicyStateActive).
		WithInsuranceCarrier(constants.InsuranceCarrierFalseLake).
		WithEffectiveDates(
			time_utils.NewDate(issuanceYear, 1, 1).ToTime(),
			time_utils.NewDate(issuanceYear, 12, 31).ToTime()).
		WithAgencyId(s.testAgencyId).
		Build()
	s.activePolicyWithNarsClaimsOnly = p
	s.Require().NoError(s.policyWrapper.InsertPolicy(ctx, p))

	s.policyNumberToRenewalsPolicyMap[s.activePolicyNumberWithNarsClaimsOnly] = client.Policy{
		PolicyNumber:     s.activePolicyNumberWithNarsClaimsOnly,
		InsuredName:      s.activePolicyWithNarsClaimsOnly.InsuredName,
		InsuranceCarrier: s.activePolicyWithNarsClaimsOnly.InsuranceCarrier.String(),
		EffectiveDate:    time_utils.DateFromTime(s.activePolicyWithNarsClaimsOnly.EffectiveDate),
		ExpirationDate:   time_utils.DateFromTime(s.activePolicyWithNarsClaimsOnly.EffectiveDateTo),
	}

	s.expiredPolicyNumberWithSnapsheetClaimsOnly = "TINCA1866550-22"
	p = policy_builder.
		New().
		WithDefaultMockData().
		WithPolicyNumber("TINCA", "1866550", 2022).
		WithDOTNumber(s.dotNumberWithPolicies).
		WithState(policy_enums.PolicyStateExpired).
		WithEffectiveDates(
			time_utils.NewDate(2022, 1, 1).ToTime(),
			time_utils.NewDate(2022, 12, 31).ToTime()).
		WithAgencyId(s.testAgencyId).
		Build()
	s.expiredPolicyWithSnapsheetClaimsOnly = p
	s.Require().NoError(s.policyWrapper.InsertPolicy(ctx, p))

	s.policyNumberToRenewalsPolicyMap[s.expiredPolicyNumberWithSnapsheetClaimsOnly] = client.Policy{
		PolicyNumber:     s.expiredPolicyNumberWithSnapsheetClaimsOnly,
		InsuredName:      s.expiredPolicyWithSnapsheetClaimsOnly.InsuredName,
		InsuranceCarrier: s.expiredPolicyWithSnapsheetClaimsOnly.InsuranceCarrier.String(),
		EffectiveDate:    time_utils.DateFromTime(s.expiredPolicyWithSnapsheetClaimsOnly.EffectiveDate),
		ExpirationDate:   time_utils.DateFromTime(s.expiredPolicyWithSnapsheetClaimsOnly.EffectiveDateTo),
	}

	s.activePolicyNumberWithSnapsheetAndNarsClaims = "TINCA0100010-" + fmt.Sprintf("%02d", issuanceYear%100)
	p = policy_builder.
		New().
		WithDefaultMockData().
		WithPolicyNumber("TINCA", "0100010", issuanceYear).
		WithDOTNumber(s.dotNumberWithPolicies).
		WithState(policy_enums.PolicyStateActive).
		WithEffectiveDates(
			time_utils.NewDate(issuanceYear, 1, 1).ToTime(),
			time_utils.NewDate(issuanceYear, 12, 31).ToTime()).
		WithAgencyId(s.testAgencyId).
		Build()
	s.activePolicyWithSnapsheetAndNarsClaims = p
	s.Require().NoError(s.policyWrapper.InsertPolicy(ctx, p))

	s.policyNumberToRenewalsPolicyMap[s.activePolicyNumberWithSnapsheetAndNarsClaims] = client.Policy{
		PolicyNumber:     s.activePolicyNumberWithSnapsheetAndNarsClaims,
		InsuredName:      s.activePolicyWithSnapsheetAndNarsClaims.InsuredName,
		InsuranceCarrier: s.activePolicyWithSnapsheetAndNarsClaims.InsuranceCarrier.String(),
		EffectiveDate:    time_utils.DateFromTime(s.activePolicyWithSnapsheetAndNarsClaims.EffectiveDate),
		ExpirationDate:   time_utils.DateFromTime(s.activePolicyWithSnapsheetAndNarsClaims.EffectiveDateTo),
	}
}

// seedNarsClaims seeds the database with claims for some of the policies created in seedPolicies,
// and the claims' respective claim statuses, claim parties & reserve summaries. All these claims
// will have NARS as a provider.
func (s *lossRunsRenewalsClientTestSuite) seedNarsClaims(ctx context.Context) {
	// Claim
	openClaim, err := claim_builder.
		New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithExternalId("CLAIM10000001").
		WithPolicyNumber(s.expiredPolicyNumberWithNarsClaimsOnly).
		WithLineOfBusiness("Auto Liability - Property Damage").
		WithStatus(claim_enums.ClaimStatusOpen).
		WithCreatedAt(time_utils.NewDate(2024, 5, 28).ToTime()).
		WithLossDatetime(pointer_utils.Time(time_utils.NewDate(2024, 5, 25).ToTime())).
		WithLossState(pointer_utils.String("NY")).
		WithLossDescription(pointer_utils.String("Loss description")).
		WithReportedAt(time_utils.NewDate(2024, 5, 28).ToTime()).
		Build()
	s.Require().NoError(err)
	s.Require().NoError(s.claimsClient.InsertClaim(ctx, openClaim))

	// Claim Parties
	openClaimPartyClaimant := cp_builders.
		NewClaimParty().
		WithDefaultMockData().
		WithClaimId(openClaim.Id).
		WithExternalId("***********").
		WithEntity("Owner").
		WithCompanyName(pointer_utils.String("Bola's Trucking LLC")).
		Build()
	s.Require().NoError(s.claimPartiesWrapper.UpsertClaimParty(ctx, *openClaimPartyClaimant))

	openClaimPartyDriver := cp_builders.
		NewClaimParty().
		WithDefaultMockData().
		WithClaimId(openClaim.Id).
		WithExternalId("1234567890").
		WithEntity("Driver").
		WithCompanyName(pointer_utils.String("Bola's Trucking LLC")).
		WithFirstName(pointer_utils.String("John")).
		WithLastName(pointer_utils.String("Doe")).
		Build()
	s.Require().NoError(s.claimPartiesWrapper.UpsertClaimParty(ctx, *openClaimPartyDriver))

	// Reserve Summaries
	openClaimReserveSummaryOpened := cr_builders.
		NewReserveSummary().
		WithDefaultMockData().
		WithClaimExternalId(openClaim.ExternalId).
		WithLOBCode("COMMERCIAL AUTO PHYSICAL DAMAGE").
		WithLOBCoverage("AUTOMOBILE OTHER THAN ASSIGNED RISK").
		WithIndemnityAvailable(decimal.NewFromInt(4000)).
		WithMedicalAvailable(decimal.NewFromInt(5000)).
		WithExpenseAvailable(decimal.NewFromInt(5000)).
		WithIndemnityPaid(decimal.NewFromInt(1000)).
		WithMedicalPaid(decimal.NewFromInt(0)).
		WithExpensePaid(decimal.NewFromInt(200)).
		WithStatus(cr_enums.ReserveStatusOpen).
		WithClaimPartyExternalId(&openClaimPartyClaimant.ExternalId).
		Build()
	s.Require().NoError(s.claimReservesWrapper.InsertReserveSummary(ctx, *openClaimReserveSummaryOpened))

	openClaimReserveSummaryClosed := cr_builders.
		NewReserveSummary().
		WithDefaultMockData().
		WithClaimExternalId(openClaim.ExternalId).
		WithLOBCode("OTHER COMMERCIAL AUTO LIABILITY").
		WithLOBCoverage("LIABILITY OTHER THAN UM, UIM AND MEDICAL PAYMENTS").
		WithIndemnityAvailable(decimal.NewFromInt(0)).
		WithMedicalAvailable(decimal.NewFromInt(0)).
		WithExpenseAvailable(decimal.NewFromInt(0)).
		WithIndemnityPaid(decimal.NewFromInt(0)).
		WithMedicalPaid(decimal.NewFromInt(0)).
		WithExpensePaid(decimal.NewFromInt(5000)).
		WithStatus(cr_enums.ReserveStatusClosed).
		Build()
	s.Require().NoError(s.claimReservesWrapper.InsertReserveSummary(ctx, *openClaimReserveSummaryClosed))

	// Recoveries
	openClaimRecoverySalvage1 := nars_postings.
		NewPosting("108845160591705", openClaimReserveSummaryClosed.Id).
		WithTransactionType("Recovery payment").
		WithType("Rcvy-Salvage").
		WithDatetime(time_utils.NewDate(2024, 5, 30).ToTime()).
		WithAmount(decimal.NewFromInt(2400)).
		WithCreatedAt(time_utils.NewDate(2024, 5, 30).ToTime()).
		WithModifiedAt(time_utils.NewDate(2024, 5, 30).ToTime()).
		WithUpdatedAt(time_utils.NewDate(2024, 5, 30).ToTime())
	s.Require().NoError(s.claimPostingsWrapper.UpsertPosting(ctx, *openClaimRecoverySalvage1))

	openClaimRecoverySalvage2 := nars_postings.
		NewPosting("108845160591706", openClaimReserveSummaryClosed.Id).
		WithTransactionType("Recovery payment").
		WithType("Rcvy-Salvage").
		WithDatetime(time_utils.NewDate(2024, 5, 31).ToTime()).
		WithAmount(decimal.NewFromInt(130)).
		WithCreatedAt(time_utils.NewDate(2024, 5, 31).ToTime()).
		WithModifiedAt(time_utils.NewDate(2024, 5, 31).ToTime()).
		WithUpdatedAt(time_utils.NewDate(2024, 5, 31).ToTime())
	s.Require().NoError(s.claimPostingsWrapper.UpsertPosting(ctx, *openClaimRecoverySalvage2))

	openClaimRecoveryDeductible := nars_postings.
		NewPosting("108845160591707", openClaimReserveSummaryClosed.Id).
		WithTransactionType("Recovery payment").
		WithType("Rcvy-Deductible").
		WithDatetime(time_utils.NewDate(2024, 5, 29).ToTime()).
		WithAmount(decimal.NewFromInt(1000)).
		WithCreatedAt(time_utils.NewDate(2024, 5, 29).ToTime()).
		WithModifiedAt(time_utils.NewDate(2024, 5, 29).ToTime()).
		WithUpdatedAt(time_utils.NewDate(2024, 5, 29).ToTime())
	s.Require().NoError(s.claimPostingsWrapper.UpsertPosting(ctx, *openClaimRecoveryDeductible))

	// Postings Fetches
	numDays := int(s.clock.Now().Sub(openClaim.ReportedAt).Hours()/24) + 1
	for i := range numDays {
		d := openClaim.ReportedAt.AddDate(0, 0, i)
		postingsFetch := nars_postings.NewPostingsFetch(openClaim.Id, d)
		s.Require().NoError(s.claimPostingsWrapper.InsertPostingsFetch(ctx, postingsFetch))
	}

	s.policyNumberToRenewalsClaimsMap[s.expiredPolicyNumberWithNarsClaimsOnly] = append(
		s.policyNumberToRenewalsClaimsMap[s.expiredPolicyNumberWithNarsClaimsOnly],
		client.Claim{
			ClaimNumber:     openClaim.ExternalId,
			DateOpened:      openClaim.CreatedAt,
			LossDate:        openClaim.LossDatetime,
			LossState:       openClaim.LossState,
			LossDescription: openClaim.LossDescription,
			Status:          claim_enums.ClaimStatusOpen.String(),
			Drivers: []client.Driver{
				{
					FirstName: "John",
					LastName:  "Doe",
				},
			},
			ReservesSummaries: []client.ReservesSummary{
				{
					Claimant:       "Bola's Trucking LLC",
					LineOfBusiness: "COMMERCIAL AUTO PHYSICAL DAMAGE",
					Coverage:       "AUTOMOBILE OTHER THAN ASSIGNED RISK",
					Status:         cr_enums.ReserveStatusOpen.String(),
					Amounts: client.Amounts{
						LossPaid:               decimal.NewFromInt(1000),
						MedicalPaid:            decimal.NewFromInt(0),
						ExpensesPaid:           decimal.NewFromInt(200),
						CurrentLossReserve:     decimal.NewFromInt(4000),
						CurrentExpensesReserve: decimal.NewFromInt(5000),
						CurrentMedicalReserve:  decimal.NewFromInt(5000),
						GrossIncurred:          decimal.NewFromInt(15200),
						OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromInt(0)),
						DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromInt(0)),
						SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromInt(0)),
						SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromInt(0)),
					},
				},
				{
					Claimant:       "",
					LineOfBusiness: "OTHER COMMERCIAL AUTO LIABILITY",
					Coverage:       "LIABILITY OTHER THAN UM, UIM AND MEDICAL PAYMENTS",
					Status:         cr_enums.ReserveStatusClosed.String(),
					Amounts: client.Amounts{
						LossPaid:               decimal.NewFromInt(0),
						MedicalPaid:            decimal.NewFromInt(0),
						ExpensesPaid:           decimal.NewFromInt(5000),
						CurrentLossReserve:     decimal.NewFromInt(0),
						CurrentExpensesReserve: decimal.NewFromInt(0),
						CurrentMedicalReserve:  decimal.NewFromInt(0),
						GrossIncurred:          decimal.NewFromInt(5000),
						OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromInt(0)),
						DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromInt(1000)),
						SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromInt(2530)),
						SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromInt(0)),
					},
				},
			},
			TotalAmounts: client.Amounts{
				LossPaid:               decimal.NewFromInt(1000),
				MedicalPaid:            decimal.NewFromInt(0),
				ExpensesPaid:           decimal.NewFromInt(5200),
				CurrentLossReserve:     decimal.NewFromInt(4000),
				CurrentExpensesReserve: decimal.NewFromInt(5000),
				CurrentMedicalReserve:  decimal.NewFromInt(5000),
				GrossIncurred:          decimal.NewFromInt(20200),
				OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromInt(0)),
				DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromInt(1000)),
				SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromInt(2530)),
				SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromInt(0)),
			},
		})

	// Claim
	closedClaim, err := claim_builder.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithExternalId("CLAIM10000002").
		WithPolicyNumber(s.expiredPolicyNumberWithNarsClaimsOnly).
		WithLineOfBusiness("Collision").
		WithStatus(claim_enums.ClaimStatusClosed).
		WithCreatedAt(time_utils.NewDate(2021, 2, 15).ToTime()).
		WithReportedAt(time_utils.NewDate(2021, 2, 15).ToTime()).
		WithLossDatetime(pointer_utils.Time(time_utils.NewDate(2021, 2, 14).ToTime())).
		WithLossState(pointer_utils.String("NY")).
		WithLossDescription(pointer_utils.String("Loss description")).
		Build()
	s.Require().NoError(err)
	s.Require().NoError(s.claimsClient.InsertClaim(ctx, closedClaim))

	// Claim Statuses
	closedClaimStatuses := []claim_builder.ClaimStatus{
		claim_builder.NewClaimStatus(
			closedClaim.Id,
			closedClaim.ExternalId,
			claim_enums.ClaimStatusOpen,
			time_utils.NewDate(2021, 2, 15).ToTime(),
		),
		claim_builder.NewClaimStatus(
			closedClaim.Id,
			closedClaim.ExternalId,
			claim_enums.ClaimStatusClosed,
			time_utils.NewDate(2021, 2, 20).ToTime(),
		),
		claim_builder.NewClaimStatus(
			closedClaim.Id,
			closedClaim.ExternalId,
			claim_enums.ClaimStatusReopen,
			time_utils.NewDate(2021, 2, 25).ToTime(),
		),
		claim_builder.NewClaimStatus(
			closedClaim.Id,
			closedClaim.ExternalId,
			claim_enums.ClaimStatusClosed,
			time_utils.NewDate(2021, 3, 1).ToTime(),
		),
	}
	for _, status := range closedClaimStatuses {
		s.Require().NoError(
			s.claimsClient.InsertClaimStatus(ctx, claim_enums.ClaimsProviderNars, status),
		)
	}

	// Claim Parties
	closedClaimPartyAgent := cp_builders.
		NewClaimParty().
		WithDefaultMockData().
		WithClaimId(closedClaim.Id).
		WithExternalId("467538473829").
		WithEntity("Agent").
		WithFirstName(pointer_utils.String("UNK")).
		WithLastName(pointer_utils.String("UNK")).
		Build()
	s.Require().NoError(s.claimPartiesWrapper.UpsertClaimParty(ctx, *closedClaimPartyAgent))

	// Postings Fetches
	numDays = int(s.clock.Now().Sub(closedClaim.ReportedAt).Hours()/24) + 1
	for i := range numDays {
		d := closedClaim.ReportedAt.AddDate(0, 0, i)
		postingsFetch := nars_postings.NewPostingsFetch(closedClaim.Id, d)
		s.Require().NoError(s.claimPostingsWrapper.InsertPostingsFetch(ctx, postingsFetch))
	}

	s.policyNumberToRenewalsClaimsMap[s.expiredPolicyNumberWithNarsClaimsOnly] = append(
		s.policyNumberToRenewalsClaimsMap[s.expiredPolicyNumberWithNarsClaimsOnly],
		client.Claim{
			ClaimNumber:       closedClaim.ExternalId,
			DateOpened:        closedClaim.CreatedAt,
			DateClosed:        &closedClaimStatuses[3].CreatedAt,
			LossDate:          closedClaim.LossDatetime,
			LossState:         closedClaim.LossState,
			LossDescription:   closedClaim.LossDescription,
			Status:            claim_enums.ClaimStatusClosed.String(),
			Drivers:           []client.Driver{},
			ReservesSummaries: []client.ReservesSummary{},
			TotalAmounts:      client.Amounts{},
		})

	// Claim
	reopenedClaim, err := claim_builder.
		New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithExternalId("NISNI10000003").
		WithPolicyNumber(s.activePolicyNumberWithNarsClaimsOnly).
		WithLineOfBusiness("Auto Liability - Property Damage").
		WithStatus(claim_enums.ClaimStatusReopen).
		WithReportedAt(time_utils.NewDate(2024, 5, 20).ToTime()).
		WithCreatedAt(time_utils.NewDate(2024, 5, 20).ToTime()).
		WithLossDatetime(pointer_utils.Time(time_utils.NewDate(2024, 5, 18).ToTime())).
		WithLossState(pointer_utils.String("CA")).
		WithLossDescription(pointer_utils.String("Reopened claim description")).
		Build()
	s.Require().NoError(err)
	s.Require().NoError(s.claimsClient.InsertClaim(ctx, reopenedClaim))

	// Claim Statuses
	reopenedClaimStatuses := []claim_builder.ClaimStatus{
		claim_builder.NewClaimStatus(
			reopenedClaim.Id,
			reopenedClaim.ExternalId,
			claim_enums.ClaimStatusOpen,
			time_utils.NewDate(2024, 5, 20).ToTime(),
		),
		claim_builder.NewClaimStatus(
			reopenedClaim.Id,
			reopenedClaim.ExternalId,
			claim_enums.ClaimStatusClosed,
			time_utils.NewDate(2024, 5, 31).ToTime(),
		),
		claim_builder.NewClaimStatus(
			reopenedClaim.Id,
			reopenedClaim.ExternalId,
			claim_enums.ClaimStatusReopen,
			time_utils.NewDate(2024, 6, 1).ToTime(),
		),
	}
	for _, status := range reopenedClaimStatuses {
		s.Require().NoError(
			s.claimsClient.InsertClaimStatus(ctx, claim_enums.ClaimsProviderNars, status),
		)
	}

	// Claim Parties
	reopenedClaimPartyClaimant := cp_builders.
		NewClaimParty().
		WithDefaultMockData().
		WithClaimId(reopenedClaim.Id).
		WithExternalId("**********").
		WithEntity("Insured").
		WithCompanyName(pointer_utils.String("TAS INC")).
		WithFirstName(pointer_utils.String("Taylor")).
		Build()
	s.Require().NoError(s.claimPartiesWrapper.UpsertClaimParty(ctx, *reopenedClaimPartyClaimant))

	// Reserve Summaries
	reopenedClaimReserveSummary := cr_builders.
		NewReserveSummary().
		WithDefaultMockData().
		WithClaimExternalId(reopenedClaim.ExternalId).
		WithLOBCode("COMMERCIAL AUTO PHYSICAL DAMAGE").
		WithLOBCoverage("AUTOMOBILE OTHER THAN ASSIGNED RISK").
		WithIndemnityAvailable(decimal.NewFromInt(10000)).
		WithMedicalAvailable(decimal.NewFromInt(5000)).
		WithExpenseAvailable(decimal.NewFromInt(0)).
		WithIndemnityPaid(decimal.NewFromInt(0)).
		WithMedicalPaid(decimal.NewFromInt(0)).
		WithExpensePaid(decimal.NewFromInt(800)).
		WithStatus(cr_enums.ReserveStatusOpen).
		WithClaimPartyExternalId(&reopenedClaimPartyClaimant.ExternalId).
		Build()
	s.Require().NoError(
		s.claimReservesWrapper.InsertReserveSummary(ctx, *reopenedClaimReserveSummary),
	)

	// Recoveries
	reopenedClaimRecoveryDeductible := nars_postings.
		NewPosting("108845160591708", reopenedClaimReserveSummary.Id).
		WithTransactionType("Recovery payment").
		WithType("Rcvy-Deductible").
		WithDatetime(time_utils.NewDate(2024, 5, 31).ToTime()).
		WithAmount(decimal.NewFromInt(500)).
		WithCreatedAt(time_utils.NewDate(2024, 5, 31).ToTime()).
		WithModifiedAt(time_utils.NewDate(2024, 5, 31).ToTime()).
		WithUpdatedAt(time_utils.NewDate(2024, 5, 31).ToTime())
	s.Require().NoError(s.claimPostingsWrapper.UpsertPosting(ctx, *reopenedClaimRecoveryDeductible))

	// Postings Fetches - only 1, so data will be "incomplete"
	postingsFetch := nars_postings.NewPostingsFetch(
		reopenedClaim.Id, time_utils.NewDate(2024, 5, 31).ToTime(),
	)
	s.Require().NoError(s.claimPostingsWrapper.InsertPostingsFetch(ctx, postingsFetch))

	s.policyNumberToRenewalsClaimsMap[s.activePolicyNumberWithNarsClaimsOnly] = append(
		s.policyNumberToRenewalsClaimsMap[s.activePolicyNumberWithNarsClaimsOnly],
		client.Claim{
			ClaimNumber:     reopenedClaim.ExternalId,
			DateOpened:      reopenedClaim.CreatedAt,
			DateClosed:      &reopenedClaimStatuses[1].CreatedAt,
			LossDate:        reopenedClaim.LossDatetime,
			LossState:       reopenedClaim.LossState,
			LossDescription: reopenedClaim.LossDescription,
			Status:          "Reopened",
			Drivers:         []client.Driver{},
			ReservesSummaries: []client.ReservesSummary{
				{
					Claimant:       "TAS INC",
					LineOfBusiness: "COMMERCIAL AUTO PHYSICAL DAMAGE",
					Coverage:       "AUTOMOBILE OTHER THAN ASSIGNED RISK",
					Status:         cr_enums.ReserveStatusOpen.String(),
					Amounts: client.Amounts{
						LossPaid:               decimal.NewFromInt(0),
						MedicalPaid:            decimal.NewFromInt(0),
						ExpensesPaid:           decimal.NewFromInt(800),
						CurrentLossReserve:     decimal.NewFromInt(10000),
						CurrentExpensesReserve: decimal.NewFromInt(0),
						CurrentMedicalReserve:  decimal.NewFromInt(5000),
						GrossIncurred:          decimal.NewFromInt(15800),
						// All recoveries should be nil for this claim, since data is incomplete
					},
				},
			},
			TotalAmounts: client.Amounts{
				LossPaid:               decimal.NewFromInt(0),
				MedicalPaid:            decimal.NewFromInt(0),
				ExpensesPaid:           decimal.NewFromInt(800),
				CurrentLossReserve:     decimal.NewFromInt(10000),
				CurrentExpensesReserve: decimal.NewFromInt(0),
				CurrentMedicalReserve:  decimal.NewFromInt(5000),
				GrossIncurred:          decimal.NewFromInt(15800),
			},
		},
	)

	// Claim
	year := s.clock.Now().Year()
	anotherOpenClaim, err := claim_builder.
		New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithExternalId("NISNI10000004").
		WithPolicyNumber(s.activePolicyNumberWithSnapsheetAndNarsClaims).
		WithLineOfBusiness("Auto Liability - Property Damage").
		WithStatus(claim_enums.ClaimStatusClosed).
		WithCreatedAt(time_utils.NewDate(year, 5, 29).ToTime()).
		WithReportedAt(time_utils.NewDate(year, 5, 29).ToTime()).
		WithLossDatetime(pointer_utils.Time(time_utils.NewDate(year, 5, 28).ToTime())).
		Build()
	s.Require().NoError(err)
	s.Require().NoError(s.claimsClient.InsertClaim(ctx, anotherOpenClaim))

	// Claim Parties
	anotherOpenClaimPartyClaimant := cp_builders.
		NewClaimParty().
		WithDefaultMockData().
		WithClaimId(anotherOpenClaim.Id).
		WithExternalId("**********").
		WithEntity("Insured").
		WithCompanyName(pointer_utils.String("Johnny's Trucking LLC")).
		WithFirstName(pointer_utils.String("Johnny")).
		WithLastName(pointer_utils.String("Smith")).
		Build()
	s.Require().NoError(s.claimPartiesWrapper.UpsertClaimParty(ctx, *anotherOpenClaimPartyClaimant))

	// Reserve Summaries
	anotherOpenClaimReserveSummary := cr_builders.
		NewReserveSummary().
		WithDefaultMockData().
		WithClaimExternalId(anotherOpenClaim.ExternalId).
		WithLOBCode("COMMERCIAL AUTO PHYSICAL DAMAGE").
		WithLOBCoverage("AUTOMOBILE OTHER THAN ASSIGNED RISK").
		WithIndemnityAvailable(decimal.NewFromInt(1000)).
		WithMedicalAvailable(decimal.NewFromInt(0)).
		WithExpenseAvailable(decimal.NewFromInt(200)).
		WithIndemnityPaid(decimal.NewFromInt(1000)).
		WithMedicalPaid(decimal.NewFromInt(0)).
		WithExpensePaid(decimal.NewFromInt(1800)).
		WithStatus(cr_enums.ReserveStatusOpen).
		WithClaimPartyExternalId(&anotherOpenClaimPartyClaimant.ExternalId).
		Build()
	s.Require().NoError(
		s.claimReservesWrapper.InsertReserveSummary(ctx, *anotherOpenClaimReserveSummary),
	)

	// Recoveries
	anotherOpenClaimRecoverySalvage := nars_postings.
		NewPosting("108845160591709", anotherOpenClaimReserveSummary.Id).
		WithTransactionType("Recovery payment").
		WithType("Rcvy-Salvage").
		WithDatetime(time_utils.NewDate(year, 5, 30).ToTime()).
		WithAmount(decimal.NewFromInt(500)).
		WithCreatedAt(time_utils.NewDate(year, 5, 30).ToTime()).
		WithModifiedAt(time_utils.NewDate(year, 5, 30).ToTime()).
		WithUpdatedAt(time_utils.NewDate(year, 5, 30).ToTime())
	s.Require().NoError(s.claimPostingsWrapper.UpsertPosting(ctx, *anotherOpenClaimRecoverySalvage))

	// Postings Fetches
	numDays = int(s.clock.Now().Sub(anotherOpenClaim.ReportedAt).Hours()/24) + 1
	for i := range numDays {
		d := anotherOpenClaim.CreatedAt.AddDate(0, 0, i)
		postingsFetch := nars_postings.NewPostingsFetch(anotherOpenClaim.Id, d)
		s.Require().NoError(s.claimPostingsWrapper.InsertPostingsFetch(ctx, postingsFetch))
	}

	s.policyNumberToRenewalsClaimsMap[s.activePolicyNumberWithSnapsheetAndNarsClaims] = append(
		s.policyNumberToRenewalsClaimsMap[s.activePolicyNumberWithSnapsheetAndNarsClaims],
		client.Claim{
			ClaimNumber:     anotherOpenClaim.ExternalId,
			DateOpened:      anotherOpenClaim.CreatedAt,
			LossDate:        anotherOpenClaim.LossDatetime,
			LossState:       anotherOpenClaim.LossState,
			LossDescription: anotherOpenClaim.LossDescription,
			Status:          claim_enums.ClaimStatusClosed.String(),
			Drivers:         []client.Driver{},
			ReservesSummaries: []client.ReservesSummary{
				{
					Claimant:       "Johnny's Trucking LLC",
					LineOfBusiness: "COMMERCIAL AUTO PHYSICAL DAMAGE",
					Coverage:       "AUTOMOBILE OTHER THAN ASSIGNED RISK",
					Status:         cr_enums.ReserveStatusOpen.String(),
					Amounts: client.Amounts{
						LossPaid:               decimal.NewFromInt(1000),
						MedicalPaid:            decimal.NewFromInt(0),
						ExpensesPaid:           decimal.NewFromInt(1800),
						CurrentLossReserve:     decimal.NewFromInt(1000),
						CurrentExpensesReserve: decimal.NewFromInt(200),
						CurrentMedicalReserve:  decimal.NewFromInt(0),
						GrossIncurred:          decimal.NewFromInt(4000),
						OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromInt(0)),
						DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromInt(0)),
						SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromInt(500)),
						SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromInt(0)),
					},
				},
			},
			TotalAmounts: client.Amounts{
				LossPaid:               decimal.NewFromInt(1000),
				MedicalPaid:            decimal.NewFromInt(0),
				ExpensesPaid:           decimal.NewFromInt(1800),
				CurrentLossReserve:     decimal.NewFromInt(1000),
				CurrentExpensesReserve: decimal.NewFromInt(200),
				CurrentMedicalReserve:  decimal.NewFromInt(0),
				GrossIncurred:          decimal.NewFromInt(4000),
				OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromInt(0)),
				DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromInt(0)),
				SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromInt(500)),
				SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromInt(0)),
			},
		},
	)
}

// seedSnapsheetClaims seeds the database with claims for some of the policies created in
// seedPolicies. All these claims will have Snapsheet as a provider.
func (s *lossRunsRenewalsClientTestSuite) seedSnapsheetClaims(ctx context.Context) {
	claim, err := claim_builder.
		New(claim_enums.ClaimsProviderSnapsheet).
		WithDefaultMockData().
		WithExternalId("123457").
		WithClaimNumber("SS-CLAIM10000004").
		WithPolicyNumber(s.expiredPolicyNumberWithSnapsheetClaimsOnly).
		WithCreatedAt(time_utils.NewDate(2022, 5, 30).ToTime()).
		WithLossDatetime(pointer_utils.Time(time_utils.NewDate(2022, 5, 28).ToTime())).
		WithStatus(claim_enums.ClaimStatusClosed).
		Build()
	s.Require().NoError(err)
	s.Require().NoError(s.claimsClient.InsertClaim(ctx, claim))

	claimStatuses := []claim_builder.ClaimStatus{
		claim_builder.NewClaimStatus(
			claim.Id,
			claim.ExternalId,
			claim_enums.ClaimStatusOpen,
			time_utils.NewDate(2022, 5, 30).ToTime(),
		),
		claim_builder.NewClaimStatus(
			claim.Id,
			claim.ExternalId,
			claim_enums.ClaimStatusClosed,
			time_utils.NewDate(2022, 6, 5).ToTime(),
		),
	}
	for _, status := range claimStatuses {
		s.Require().NoError(
			s.claimsClient.InsertClaimStatus(ctx, claim_enums.ClaimsProviderSnapsheet, status),
		)
	}

	s.policyNumberToRenewalsClaimsMap[s.expiredPolicyNumberWithSnapsheetClaimsOnly] = append(
		s.policyNumberToRenewalsClaimsMap[s.expiredPolicyNumberWithSnapsheetClaimsOnly],
		client.Claim{
			ClaimNumber:     claim.ClaimNumber,
			DateOpened:      claim.CreatedAt,
			DateClosed:      &claimStatuses[1].CreatedAt,
			LossDate:        claim.LossDatetime,
			LossState:       claim.LossState,
			LossDescription: claim.LossDescription,
			Status:          claim_enums.ClaimStatusClosed.String(),
			Drivers: []client.Driver{
				{
					FirstName: "UNK UNK",
				},
			},
			ReservesSummaries: []client.ReservesSummary{
				{
					Claimant:       "UNK UNK",
					LineOfBusiness: "19.4",
					Coverage:       "VEHICLE",
					Status:         "CLOSED",
					Amounts: client.Amounts{
						LossPaid:               decimal.NewFromFloat(1000),
						MedicalPaid:            decimal.NewFromFloat(0),
						ExpensesPaid:           decimal.NewFromFloat(200),
						CurrentLossReserve:     decimal.NewFromFloat(0),
						CurrentExpensesReserve: decimal.NewFromFloat(0),
						CurrentMedicalReserve:  decimal.NewFromFloat(0),
						GrossIncurred:          decimal.NewFromFloat(1200),
						OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromFloat(0)),
						DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromFloat(0)),
						SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromFloat(500)),
						SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromFloat(0)),
					},
				},
				{
					Claimant:       "Juanita's Trucking LLC",
					LineOfBusiness: "19.4",
					Coverage:       "VEHICLE",
					Status:         "CLOSED",
					Amounts: client.Amounts{
						LossPaid:               decimal.NewFromFloat(0),
						MedicalPaid:            decimal.NewFromFloat(0),
						ExpensesPaid:           decimal.NewFromFloat(0),
						CurrentLossReserve:     decimal.NewFromFloat(0),
						CurrentExpensesReserve: decimal.NewFromFloat(0),
						CurrentMedicalReserve:  decimal.NewFromFloat(0),
						GrossIncurred:          decimal.NewFromFloat(0),
						OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromFloat(0)),
						DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromFloat(0)),
						SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromFloat(0)),
						SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromFloat(0)),
					},
				},
			},
			TotalAmounts: client.Amounts{
				LossPaid:               decimal.NewFromFloat(1000),
				MedicalPaid:            decimal.NewFromFloat(0),
				ExpensesPaid:           decimal.NewFromFloat(200),
				CurrentLossReserve:     decimal.NewFromFloat(0),
				CurrentExpensesReserve: decimal.NewFromFloat(0),
				CurrentMedicalReserve:  decimal.NewFromFloat(0),
				GrossIncurred:          decimal.NewFromFloat(1200),
				OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromFloat(0)),
				DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromFloat(0)),
				SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromFloat(500)),
				SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromFloat(0)),
			},
		},
	)

	year := s.clock.Now().Year()
	claim, err = claim_builder.
		New(claim_enums.ClaimsProviderSnapsheet).
		WithDefaultMockData().
		WithExternalId("123459").
		WithClaimNumber("SS-CLAIM10040405").
		WithPolicyNumber(s.activePolicyNumberWithSnapsheetAndNarsClaims).
		WithCreatedAt(time_utils.NewDate(year, 5, 30).ToTime()).
		WithLossDatetime(pointer_utils.Time(time_utils.NewDate(year, 5, 28).ToTime())).
		WithStatus(claim_enums.ClaimStatusClosed).
		Build()
	s.Require().NoError(err)
	s.Require().NoError(s.claimsClient.InsertClaim(ctx, claim))

	s.policyNumberToRenewalsClaimsMap[s.activePolicyNumberWithSnapsheetAndNarsClaims] = append(
		s.policyNumberToRenewalsClaimsMap[s.activePolicyNumberWithSnapsheetAndNarsClaims],
		client.Claim{
			ClaimNumber:     claim.ClaimNumber,
			DateOpened:      claim.CreatedAt,
			LossDate:        claim.LossDatetime,
			LossState:       claim.LossState,
			LossDescription: claim.LossDescription,
			Status:          claim_enums.ClaimStatusClosed.String(),
			Drivers: []client.Driver{
				{
					FirstName: "James Smith",
				},
			},
			ReservesSummaries: []client.ReservesSummary{
				{
					Claimant:       "Jimmy's Trucking LLC",
					LineOfBusiness: "19.4",
					Coverage:       "VEHICLE",
					Status:         "CLOSED",
					Amounts: client.Amounts{
						LossPaid:               decimal.NewFromFloat(1000.25),
						MedicalPaid:            decimal.NewFromFloat(0),
						ExpensesPaid:           decimal.NewFromFloat(200.75),
						CurrentLossReserve:     decimal.NewFromFloat(0),
						CurrentExpensesReserve: decimal.NewFromFloat(0),
						CurrentMedicalReserve:  decimal.NewFromFloat(0),
						GrossIncurred:          decimal.NewFromFloat(1300),
						OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromFloat(0)),
						DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromFloat(130)),
						SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromFloat(70)),
						SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromFloat(1000)),
					},
				},
			},
			TotalAmounts: client.Amounts{
				LossPaid:               decimal.NewFromFloat(1000.25),
				MedicalPaid:            decimal.NewFromFloat(0),
				ExpensesPaid:           decimal.NewFromFloat(200.75),
				CurrentLossReserve:     decimal.NewFromFloat(0),
				CurrentExpensesReserve: decimal.NewFromFloat(0),
				CurrentMedicalReserve:  decimal.NewFromFloat(0),
				GrossIncurred:          decimal.NewFromFloat(1300),
				OtherRecoveries:        pointer_utils.ToPointer(decimal.NewFromFloat(0)),
				DeductibleRecoveries:   pointer_utils.ToPointer(decimal.NewFromFloat(130)),
				SalvageRecoveries:      pointer_utils.ToPointer(decimal.NewFromFloat(70)),
				SubrogationRecoveries:  pointer_utils.ToPointer(decimal.NewFromFloat(1000)),
			},
		},
	)
}
