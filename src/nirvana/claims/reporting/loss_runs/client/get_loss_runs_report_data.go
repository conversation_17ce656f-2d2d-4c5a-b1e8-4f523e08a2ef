package client

import (
	"context"
	"time"

	claims_db "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	"nirvanatech.com/nirvana/common-go/log"
)

// GetLossRunsReportData will return the data needed to generate a loss runs report, given a policy
// number
func (c *Client) GetLossRunsReportData(
	ctx context.Context, policyNumber string,
) (*LossRunsReportData, error) {
	lrrd := LossRunsReportData{
		GenerationDate: time.Now().Round(time.Millisecond).UTC(),
		IsDataComplete: true, // Assume data is complete until proven otherwise
	}

	p, err := c.GetPolicy(ctx, policyNumber)
	if err != nil {
		return nil, err
	}
	lrrd.Policy = *p

	agency, err := c.deps.AgencyWrapper.FetchAgency(ctx, p.AgencyId)
	if err != nil {
		return nil, err
	}
	lrrd.AgencyName = agency.Name

	if err := c.populateSnapsheetLossRunsReportData(ctx, &lrrd); err != nil {
		return nil, err
	}

	if err := c.populateNarsLossRunsReportData(ctx, &lrrd); err != nil {
		return nil, err
	}

	return &lrrd, nil
}

func (c *Client) populateNarsLossRunsReportData(ctx context.Context, lrrd *LossRunsReportData) error {
	dbClaims, err := c.deps.ClaimsClient.GetClaimsBySource(
		ctx,
		claim_enums.ClaimsProviderNars,
		claims_db.PolicyNumberIn([]string{lrrd.Policy.PolicyNumber}),
		claims_db.ClaimStatusIsNot(claim_enums.ClaimStatusCreatedInError),
	)
	if err != nil {
		return err
	}
	if len(dbClaims) == 0 {
		return nil
	}

	for _, dbc := range dbClaims {
		claim, err := c.newNarsClaim(ctx, dbc)
		if err != nil {
			return err
		}

		lrrd.Claims = append(lrrd.Claims, *claim)

		// Update the grand total amounts
		lrrd.GrandTotalAmounts.Add(claim.TotalAmounts)
	}

	isReportDataComplete, err := c.isNarsReportDataComplete(ctx, dbClaims)
	if err != nil {
		lrrd.IsDataComplete = false
		log.Error(ctx, "Failed to check if loss runs report data is complete", log.Err(err))
	} else {
		lrrd.IsDataComplete = isReportDataComplete
	}

	return nil
}

func (c *Client) populateSnapsheetLossRunsReportData(ctx context.Context, lrrd *LossRunsReportData) error {
	dbClaims, err := c.deps.ClaimsClient.GetClaimsBySource(
		ctx,
		claim_enums.ClaimsProviderSnapsheet,
		claims_db.PolicyNumberIn([]string{lrrd.Policy.PolicyNumber}),
		claims_db.ClaimStatusIsNot(claim_enums.ClaimStatusCancelled),
	)
	if err != nil {
		return err
	}
	if len(dbClaims) == 0 {
		return nil
	}

	lossRunsRows, err := c.deps.SnowflakeWrapper.GetLossRunsRows(ctx, lrrd.Policy.PolicyNumber)
	if err != nil {
		return err
	}

	claimNumberToLossRunsRows := make(map[string][]snowflake.LossRunsRow)
	for _, exposure := range lossRunsRows {
		claimNumberToLossRunsRows[exposure.CLAIM_ID] = append(
			claimNumberToLossRunsRows[exposure.CLAIM_ID], exposure,
		)
	}

	for _, dbc := range dbClaims {
		claim, err := c.newSnapsheetClaim(ctx, dbc, claimNumberToLossRunsRows[dbc.ExternalId])
		if err != nil {
			return err
		}

		lrrd.Claims = append(lrrd.Claims, *claim)

		// Update the grand total amounts
		lrrd.GrandTotalAmounts.Add(claim.TotalAmounts)
	}

	return nil
}
