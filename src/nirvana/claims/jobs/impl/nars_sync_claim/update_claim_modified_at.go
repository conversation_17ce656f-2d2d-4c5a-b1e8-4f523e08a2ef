package nars_sync_claim

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"

	claim_notes_db "nirvanatech.com/nirvana/claims/claim_notes/db"
	nars_cr "nirvanatech.com/nirvana/claims/claim_reserves/db/nars"
	"nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/claims/nars"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type updateClaimModifiedAtTask struct {
	job_utils.NoopUndoTask[*messages.NARSSyncClaimMessage]

	deps impl.Deps
}

func (t *updateClaimModifiedAtTask) ID() string {
	return "updateClaimModifiedAtTask"
}

func (t *updateClaimModifiedAtTask) Run(
	jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage,
) error {
	return t.updateClaimModifiedAt(jCtx, msg.ClaimExternalId)
}

func (t *updateClaimModifiedAtTask) ShouldRetry(
	_ jtypes.Context,
	_ *messages.NARSSyncClaimMessage,
	failureError error,
	retryAttempt int,
) (retryable bool, waitBeforeRetry time.Duration) {
	if errors.Is(failureError, nars.ErrFailedRequest) && retryAttempt <= maxTaskRetryAttempts {
		return true, time.Second * 10
	}
	return false, 0
}

func (t *updateClaimModifiedAtTask) Retry(
	jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage,
) error {
	return t.Run(jCtx, msg)
}

var _ jtypes.Task[*messages.NARSSyncClaimMessage] = &updateClaimModifiedAtTask{}

func (t *updateClaimModifiedAtTask) updateClaimModifiedAt(
	ctx context.Context, claimExternalId string,
) error {
	log.ContextWithFields(ctx, log.String("ClaimExternalId", claimExternalId))

	claim, err := t.deps.ClaimsClient.GetClaimByExternalId(ctx, claimExternalId)
	if err != nil {
		return err
	}

	if claim.Status == claim_enums.ClaimStatusCreatedInError {
		log.Info(ctx, "skipping update due to claim being created in error")
		return nil
	}

	reserveSummaries, err := t.deps.ClaimReservesWrapper.GetReserveSummaries(
		ctx,
		nars_cr.ReserveSummaryClaimExternalIdIs(claimExternalId),
		nars_cr.ReserveSummaryOrderByModifiedAtDesc(),
		nars_cr.Limit(1),
	)
	if err != nil {
		return err
	}

	newModifiedAt := claim.ModifiedAt
	// We might not have reserve summaries for that claim, therefore if we find any, we check if
	// their modifiedAt is after the claim's modifiedAt
	if len(reserveSummaries) != 0 && reserveSummaries[0].ModifiedAt.After(newModifiedAt) {
		newModifiedAt = reserveSummaries[0].ModifiedAt
	}

	notes, err := t.deps.ClaimNotesClient.GetNotes(
		ctx,
		claim_notes_db.ClaimExternalIdIs(claimExternalId),
		claim_notes_db.OrderByModifiedAtDesc(),
		claim_notes_db.Limit(1),
	)
	if err != nil {
		return errors.Wrap(err, "failed to retrieve latest note based on modifiedAt")
	}

	// We might not have notes for that claim, therefore if we find any we check if their modifiedAt
	// is after the claim's modifiedAt
	if len(notes) != 0 && notes[0].ModifiedAt.After(newModifiedAt) {
		newModifiedAt = notes[0].ModifiedAt
	}

	if newModifiedAt == claim.ModifiedAt {
		log.Info(ctx, "skipping update claim modified at due to claim having same modified at")
		return nil
	}

	updateFn := func(c db.Claim) (db.Claim, error) {
		c.ModifiedAt = newModifiedAt
		return c, nil
	}
	if err := t.deps.ClaimsClient.UpdateClaim(ctx, claim.Id, updateFn); err != nil {
		return errors.Wrapf(err, "failed to update Claim: %s", claim.Id)
	}
	return nil
}
