load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "nars_sync_claim",
    srcs = [
        "const.go",
        "nars_sync_claim.go",
        "persist_claim_task.go",
        "persist_notes_task.go",
        "persist_parties_task.go",
        "persist_postings_task.go",
        "persist_reserve_summaries_task.go",
        "persist_reserve_transactions_task.go",
        "update_claim_modified_at.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/jobs/impl/nars_sync_claim",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/claim_notes/db",
        "//nirvana/claims/claim_parties/db/nars",
        "//nirvana/claims/claim_postings/db/nars",
        "//nirvana/claims/claim_reserves/db/nars",
        "//nirvana/claims/claim_reserves/enums",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/claims/jobs",
        "//nirvana/claims/jobs/impl",
        "//nirvana/claims/jobs/messages",
        "//nirvana/claims/nars",
        "//nirvana/claims/nars/api/components",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
    ],
)

go_test(
    name = "nars_sync_claim_test",
    srcs = ["update_claim_modified_at_test.go"],
    embed = [":nars_sync_claim"],
    deps = [
        "//nirvana/claims/claim_notes/client",
        "//nirvana/claims/claim_notes/db",
        "//nirvana/claims/claim_reserves/builders",
        "//nirvana/claims/claim_reserves/db/nars",
        "//nirvana/claims/client",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/claims/jobs/impl",
        "//nirvana/infra/fx/testloader",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
