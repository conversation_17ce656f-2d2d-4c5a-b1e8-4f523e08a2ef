package nars_sync_claim

import (
	"context"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	claim_db "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/claims/nars"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type persistClaimTask struct {
	job_utils.NoopUndoTask[*messages.NARSSyncClaimMessage]

	deps impl.Deps
}

func (t *persistClaimTask) ID() string {
	return "persistClaimTask"
}

func (t *persistClaimTask) Run(jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage) error {
	return t.persistClaim(jCtx, msg.ClaimExternalId)
}

func (t *persistClaimTask) ShouldRetry(
	_ jtypes.Context,
	_ *messages.NARSSyncClaimMessage,
	failureError error,
	retryAttempt int,
) (retryable bool, waitBeforeRetry time.Duration) {
	if errors.Is(failureError, nars.ErrFailedRequest) && retryAttempt <= maxTaskRetryAttempts {
		return true, time.Second * 10
	}
	return false, 0
}

func (t *persistClaimTask) Retry(jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage) error {
	return t.Run(jCtx, msg)
}

var _ jtypes.Task[*messages.NARSSyncClaimMessage] = &persistClaimTask{}

func (t *persistClaimTask) persistClaim(ctx context.Context, claimExternalId string) error {
	details, err := t.deps.NARSClient.GetClaimByExternalId(ctx, claimExternalId)
	if err != nil {
		return err
	}
	status, err := claim_enums.ClaimStatusString(string(details.ClaimStatusDesc))
	if err != nil {
		return err
	}

	if details.ClosureCodeDesc != nil &&
		strings.ToLower(*details.ClosureCodeDesc) == "created in error" {
		status = claim_enums.ClaimStatusCreatedInError
	}

	createdAt, err := time.Parse(NarsTimestampFormatWithSeconds, details.DateCreated)
	if err != nil {
		return err
	}

	modifiedAt, err := time.Parse(NarsTimestampFormatWithSeconds, details.DateModified)
	if err != nil {
		return err
	}

	reportedAt, err := time.Parse(NarsTimestampFormatWithSeconds, details.ReportedDate)
	if err != nil {
		return err
	}

	var lossDate time.Time
	if details.LossDate != nil {
		lossDate, err = time.Parse(NarsTimestampFormatWithSeconds, *details.LossDate)
		if err != nil {
			return err
		}
	}

	reportedBy := pointer_utils.FromPointerOr(details.ReportedBy, "")
	claimNumber := pointer_utils.FromPointerOr(details.ClientClaimNumber, "")
	claim, err := claim_db.
		New(claim_enums.ClaimsProviderNars).
		WithId(uuid.New()).
		WithExternalId(details.ClaimNumber).
		WithPolicyNumber(details.PolicyNbr).
		WithLineOfBusiness(details.ClaimTypeDesc).
		WithCreatedAt(createdAt).
		WithModifiedAt(modifiedAt).
		WithReportedAt(reportedAt).
		WithReportedBy(&reportedBy).
		WithStatus(status).
		WithAdjusterName(details.AdjusterFullName).
		WithAdjusterEmail(details.AdjusterEmail).
		WithClaimNumber(claimNumber).
		WithLossDatetime(&lossDate).
		WithLossState(details.LossState).
		WithLossDescription(details.LossDesc).
		Build()
	if err != nil {
		return errors.Wrapf(err, "failed to build claim from NARS details: %s", claimExternalId)
	}

	if err := t.deps.ClaimsClient.UpsertClaim(ctx, *claim); err != nil {
		return err
	}

	latestStatuses, err := t.deps.ClaimsClient.GetClaimStatusesBySource(
		ctx,
		claim_enums.ClaimsProviderNars,
		claim.ExternalId,
		claim_db.StatusClaimOrderByCreatedAtDesc(),
	)
	if err != nil {
		return err
	}

	currentStatus := claim_db.NewClaimStatus(
		claim.Id,
		claim.ExternalId,
		status,
		modifiedAt,
	)

	// We perform a 3 part check; the first being if there's any status in the DB, the second is if
	// the status changed and the third is that if latest status is closed and the updatedAt has
	// been updated.
	didStatusChange := len(latestStatuses) == 0 ||
		latestStatuses[0].Value != currentStatus.Value ||
		(latestStatuses[0].Value == claim_enums.ClaimStatusClosed &&
			latestStatuses[0].CreatedAt != currentStatus.CreatedAt)
	if didStatusChange {
		if err := t.deps.ClaimsClient.InsertClaimStatus(
			ctx,
			claim_enums.ClaimsProviderNars,
			currentStatus,
		); err != nil {
			return err
		}
	}
	return nil
}
