package nars_sync_claim

import (
	"context"
	"strconv"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	db_nars "nirvanatech.com/nirvana/claims/claim_reserves/db/nars"
	"nirvanatech.com/nirvana/claims/claim_reserves/enums"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/claims/nars"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type persistReserveSummariesTask struct {
	job_utils.NoopUndoTask[*messages.NARSSyncClaimMessage]

	deps impl.Deps
}

func (t *persistReserveSummariesTask) ID() string {
	return "persistReserveSummariesTask"
}

func (t *persistReserveSummariesTask) Run(
	jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage,
) error {
	return t.persistReserveSummaries(jCtx, msg.ClaimExternalId)
}

func (t *persistReserveSummariesTask) ShouldRetry(
	_ jtypes.Context,
	_ *messages.NARSSyncClaimMessage,
	failureError error,
	retryAttempt int,
) (retryable bool, waitBeforeRetry time.Duration) {
	if errors.Is(failureError, nars.ErrFailedRequest) && retryAttempt <= maxTaskRetryAttempts {
		return true, time.Second * 10
	}
	return false, 0
}

func (t *persistReserveSummariesTask) Retry(
	jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage,
) error {
	return t.Run(jCtx, msg)
}

var _ jtypes.Task[*messages.NARSSyncClaimMessage] = &persistReserveSummariesTask{}

func (t *persistReserveSummariesTask) persistReserveSummaries(
	ctx context.Context, claimExternalId string,
) error {
	narsReserveSummaries, err := t.deps.NARSClient.GetReserveSummariesByClaimExternalId(
		ctx, claimExternalId)
	if err != nil {
		return err
	}
	for _, narsReserveSummary := range narsReserveSummaries {
		status, err := enums.ReserveStatusString(narsReserveSummary.ReserveStatus)
		if err != nil {
			return err
		}
		createdAt, err := time.Parse(NarsTimestampFormatWithSeconds, narsReserveSummary.DateCreated)
		if err != nil {
			return err
		}
		modifiedAt, err := time.Parse(
			NarsTimestampFormatWithSeconds, narsReserveSummary.DateModified)
		if err != nil {
			return err
		}
		indemnityPaid, err := decimal.NewFromString(narsReserveSummary.IndemnityPaidAmt)
		if err != nil {
			return err
		}
		medicalPaid, err := decimal.NewFromString(narsReserveSummary.MedicalPaidAmt)
		if err != nil {
			return err
		}
		expensePaid, err := decimal.NewFromString(narsReserveSummary.ExpensePaidAmt)
		if err != nil {
			return err
		}
		var indemnityAvailable, medicalAvailable, expenseAvailable decimal.Decimal
		if status != enums.ReserveStatusClosed {
			indemnityReserve, err := decimal.NewFromString(narsReserveSummary.IndemnityReserveAmt)
			if err != nil {
				return err
			}
			medicalReserve, err := decimal.NewFromString(narsReserveSummary.MedicalReserveAmt)
			if err != nil {
				return err
			}
			expenseReserve, err := decimal.NewFromString(narsReserveSummary.ExpenseReserveAmt)
			if err != nil {
				return err
			}
			indemnityAvailable = indemnityReserve.Sub(indemnityPaid)
			medicalAvailable = medicalReserve.Sub(medicalPaid)
			expenseAvailable = expenseReserve.Sub(expensePaid)
		}
		reserveSummary := db_nars.NewReserveSummary(
			claimExternalId,
			strconv.FormatInt(narsReserveSummary.ReserveId, 10),
			narsReserveSummary.LineCode,
			narsReserveSummary.LineCoverageTypeDesc,
			status,
			indemnityAvailable,
			medicalAvailable,
			expenseAvailable,
			indemnityPaid,
			medicalPaid,
			expensePaid,
			createdAt,
			modifiedAt,
		)
		claimPartyId := strconv.FormatInt(narsReserveSummary.ClaimPartyId, 10)
		reserveSummary = reserveSummary.WithClaimPartyExternalId(&claimPartyId)

		if err := t.deps.ClaimReservesWrapper.UpsertReserveSummary(ctx, *reserveSummary); err != nil {
			return err
		}
	}
	return nil
}
