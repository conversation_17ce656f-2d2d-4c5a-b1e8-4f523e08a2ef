package nars_sync_claim

import (
	"strconv"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	nars_postings "nirvanatech.com/nirvana/claims/claim_postings/db/nars"
	nars_reserves "nirvanatech.com/nirvana/claims/claim_reserves/db/nars"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/claims/nars"
	"nirvanatech.com/nirvana/claims/nars/api/components"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type persistPostingsTask struct {
	job_utils.NoopUndoTask[*messages.NARSSyncClaimMessage]

	deps impl.Deps
}

func (t *persistPostingsTask) ID() string {
	return "persistPostingsTask"
}

func (t *persistPostingsTask) Run(jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage) error {
	return t.persistPostings(jCtx, msg.ClaimExternalId)
}

func (t *persistPostingsTask) ShouldRetry(
	_ jtypes.Context,
	_ *messages.NARSSyncClaimMessage,
	failureError error,
	retryAttempt int,
) (retryable bool, waitBeforeRetry time.Duration) {
	if errors.Is(failureError, nars.ErrFailedRequest) && retryAttempt <= maxTaskRetryAttempts {
		return true, time.Second * 10
	}
	return false, 0
}

func (t *persistPostingsTask) Retry(jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage) error {
	return t.Run(jCtx, msg)
}

var _ jtypes.Task[*messages.NARSSyncClaimMessage] = &persistPostingsTask{}

func (t *persistPostingsTask) persistPostings(ctx jtypes.Context, claimExternalId string) error {
	// For this task, we want to retrieve postings modified in the last 24 hours.
	yesterday := t.deps.Clk.Now().Add(-24 * time.Hour)
	modifiedDate := yesterday.Format("20060102")
	narsPostings, err := t.deps.NARSClient.GetPostingsByClaimExternalIdAndModifiedDate(
		ctx, claimExternalId, modifiedDate)
	if err != nil {
		return err
	}

	// At the moment, we only care about recoveries. If we need to handle other types of postings in
	// the future, we can modify/remove this filter. We'll also immediately filter out postings
	// without a PostingId, ReserveId or PostingDesc, as having a posting without these fields
	// doesn't make sense in our context.
	narsRecoveries := slice_utils.Filter(
		narsPostings,
		func(p components.Posting) bool {
			return (p.TransactionDesc != nil && *p.TransactionDesc == "Recovery payment") &&
				p.PostingId != nil &&
				p.ReserveId != nil &&
				(p.PostingDesc != nil && *p.PostingDesc != "")
		},
	)

	reserveSummaries, err := t.deps.ClaimReservesWrapper.GetReserveSummaries(
		ctx, nars_reserves.ReserveSummaryClaimExternalIdIs(claimExternalId))
	if err != nil {
		return errors.Wrapf(err, "failed to get reserve summaries for claim %s", claimExternalId)
	}

	// externalIdToReserveSummary maps a reserve's external ID to its ReserveSummary object.
	externalIdToReserveSummary := slice_utils.ToMap(
		reserveSummaries,
		func(s nars_reserves.ReserveSummary) string {
			return s.ExternalId
		},
		func(s nars_reserves.ReserveSummary) nars_reserves.ReserveSummary {
			return s
		},
	)

	for _, narsPosting := range narsRecoveries {
		externalId := strconv.FormatInt(*narsPosting.PostingId, 10)

		reserveId := strconv.FormatInt(*narsPosting.ReserveId, 10)
		rs, ok := externalIdToReserveSummary[reserveId]
		if !ok {
			log.Warn(
				ctx,
				"Posting retrieved from NARS has a ReserveId that does not match any existing reserve summaries. Skipping.",
				log.String("postingId", externalId),
				log.String("claimExternalId", claimExternalId),
				log.String("reserveId", reserveId),
				log.String("modifiedDate", modifiedDate),
			)
			continue
		}

		posting := nars_postings.
			NewPosting(externalId, rs.Id).
			WithTransactionType(*narsPosting.TransactionDesc).
			WithType(*narsPosting.PostingDesc).
			WithUpdatedAt(t.deps.Clk.Now().Round(time.Millisecond).UTC())

		if narsPosting.PostingDate != nil {
			dt, err := time.Parse(NarsTimestampFormatWithSeconds, *narsPosting.PostingDate)
			if err != nil {
				log.Warn(
					ctx,
					"Failed to parse posting date from NARS. Skipping posting.",
					log.String("postingId", externalId),
					log.String("claimExternalId", claimExternalId),
					log.String("postingDate", *narsPosting.PostingDate),
					log.String("modifiedDate", modifiedDate),
				)
				continue
			}
			posting = posting.WithDatetime(dt.Round(time.Millisecond).UTC())
		}

		if narsPosting.PostingAmt != nil {
			amt, err := decimal.NewFromString(*narsPosting.PostingAmt)
			if err != nil {
				log.Warn(
					ctx,
					"Failed to parse posting amount from NARS. Skipping posting.",
					log.String("postingId", externalId),
					log.String("claimExternalId", claimExternalId),
					log.String("postingAmt", *narsPosting.PostingAmt),
					log.String("modifiedDate", modifiedDate),
				)
				continue
			}
			posting = posting.WithAmount(amt)
		}

		if narsPosting.DateCreated != nil {
			createdAt, err := time.Parse(NarsTimestampFormatWithSeconds, *narsPosting.DateCreated)
			if err != nil {
				log.Warn(
					ctx,
					"Failed to parse created at date from NARS. Skipping posting.",
					log.String("postingId", externalId),
					log.String("claimExternalId", claimExternalId),
					log.String("createdAt", *narsPosting.DateCreated),
					log.String("modifiedDate", modifiedDate),
				)
				continue
			}
			posting = posting.WithCreatedAt(createdAt.Round(time.Millisecond).UTC())
		}

		if narsPosting.DateModified != nil {
			modifiedAt, err := time.Parse(NarsTimestampFormatWithSeconds, *narsPosting.DateModified)
			if err != nil {
				log.Warn(
					ctx,
					"Failed to parse modified at date from NARS. Skipping posting.",
					log.String("postingId", externalId),
					log.String("claimExternalId", claimExternalId),
					log.String("modifiedAt", *narsPosting.DateModified),
					log.String("modifiedDate", modifiedDate),
				)
				continue
			}
			posting = posting.WithModifiedAt(modifiedAt.Round(time.Millisecond).UTC())
		}

		if err := t.deps.ClaimPostingsWrapper.UpsertPosting(ctx, *posting); err != nil {
			return errors.Wrapf(
				err,
				"failed to upsert posting %s for claim %s and modifiedDate %s",
				externalId,
				claimExternalId,
				modifiedDate,
			)
		}
	}

	// After processing all postings, we insert a PostingsFetch record to indicate that we have
	// successfully fetched postings for this claim and modified date.
	claim, err := t.deps.ClaimsClient.GetClaimByExternalId(ctx, claimExternalId)
	if err != nil {
		return errors.Wrapf(
			err,
			"failed to get claim by external ID %s",
			claimExternalId,
		)
	}
	postingsFetch := nars_postings.NewPostingsFetch(claim.Id, yesterday)
	if err := t.deps.ClaimPostingsWrapper.InsertPostingsFetch(ctx, postingsFetch); err != nil {
		return errors.Wrapf(
			err,
			"failed to insert postings fetch for claim %s and dateRequested %s",
			claimExternalId,
			yesterday,
		)
	}

	return nil
}
