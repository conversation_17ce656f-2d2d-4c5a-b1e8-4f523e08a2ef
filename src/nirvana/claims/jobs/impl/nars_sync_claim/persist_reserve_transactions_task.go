package nars_sync_claim

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	nars_cr "nirvanatech.com/nirvana/claims/claim_reserves/db/nars"
	"nirvanatech.com/nirvana/claims/claim_reserves/enums"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/claims/nars"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type persistReserveTransactionsTask struct {
	job_utils.NoopUndoTask[*messages.NARSSyncClaimMessage]

	deps impl.Deps
}

func (t *persistReserveTransactionsTask) ID() string {
	return "persistReserveTransactionsTask"
}

func (t *persistReserveTransactionsTask) Run(
	jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage,
) error {
	return t.persistReserveTransaction(jCtx, msg.ClaimExternalId)
}

func (t *persistReserveTransactionsTask) ShouldRetry(
	_ jtypes.Context,
	_ *messages.NARSSyncClaimMessage,
	failureError error,
	retryAttempt int,
) (retryable bool, waitBeforeRetry time.Duration) {
	if errors.Is(failureError, nars.ErrFailedRequest) && retryAttempt <= maxTaskRetryAttempts {
		return true, time.Second * 10
	}
	return false, 0
}

func (t *persistReserveTransactionsTask) Retry(
	jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage,
) error {
	return t.Run(jCtx, msg)
}

var _ jtypes.Task[*messages.NARSSyncClaimMessage] = &persistReserveTransactionsTask{}

func (t *persistReserveTransactionsTask) persistReserveTransaction(
	ctx context.Context,
	claimExternalId string,
) error {
	narsTransactions, err := t.deps.NARSClient.GetReserveDetailsByClaimExternalId(
		ctx, claimExternalId)
	if err != nil {
		return err
	}
	for _, narsTransaction := range narsTransactions {
		// We will skip all non-value transaction
		if strings.ToLower(narsTransaction.ReserveStatusChgInd) != "no" {
			continue
		}

		amount, err := decimal.NewFromString(narsTransaction.ReserveChangeAmt)
		if err != nil {
			return err
		}

		category, err := enums.ReserveTransactionCategoryString(
			narsTransaction.ReserveCategoryTypeDesc)
		if err != nil {
			return err
		}

		subcategory, err := enums.ReserveTransactionSubcategoryString(
			narsTransaction.ReserveCategoryDesc)
		if err != nil {
			return err
		}

		createdAt, err := time.Parse(NarsTimestampFormatWithSeconds, narsTransaction.DateCreated)
		if err != nil {
			return err
		}
		updatedAt, err := time.Parse(NarsTimestampFormatWithSeconds, narsTransaction.DateModified)
		if err != nil {
			return err
		}

		transaction := nars_cr.NewReserveTransaction(
			strconv.FormatInt(narsTransaction.ReserveId, 10),
			strconv.FormatInt(narsTransaction.ReserveTransactionId, 10),
			category,
			subcategory,
			amount,
			createdAt,
			updatedAt,
		)
		if err := t.deps.ClaimReservesWrapper.UpsertReserveTransaction(ctx, transaction); err != nil {
			return err
		}
	}
	return nil
}
