package nars_sync_claim

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	notes_client "nirvanatech.com/nirvana/claims/claim_notes/client"
	claim_notes_db "nirvanatech.com/nirvana/claims/claim_notes/db"
	cr_builders "nirvanatech.com/nirvana/claims/claim_reserves/builders"
	nars_cr "nirvanatech.com/nirvana/claims/claim_reserves/db/nars"
	claims_client "nirvanatech.com/nirvana/claims/client"
	claim_builder "nirvanatech.com/nirvana/claims/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_UpdateClaimModifiedAt(t *testing.T) {
	var env struct {
		fx.In

		ClaimsClient *claims_client.Client

		ClaimNotesClient     *notes_client.Client
		ClaimReservesWrapper *nars_cr.DataWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()
	now := time.Now()

	task := &updateClaimModifiedAtTask{
		deps: impl.Deps{
			ClaimReservesWrapper: env.ClaimReservesWrapper,
			ClaimNotesClient:     env.ClaimNotesClient,
			ClaimsClient:         env.ClaimsClient,
		},
	}

	// Insert the claims data
	openClaim, err := claim_builder.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithCreatedAt(now.AddDate(0, -1, 0)).
		WithModifiedAt(now.AddDate(0, 0, -5)). // placing last modification 5 days before
		Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, openClaim))

	closedClaim, err := claim_builder.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithCreatedAt(now.AddDate(0, -1, 0)).
		WithModifiedAt(now.AddDate(0, 0, -4)).
		WithStatus(claim_enums.ClaimStatusClosed).
		Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, closedClaim))

	createdInErrorClaim, err := claim_builder.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithStatus(claim_enums.ClaimStatusCreatedInError).
		Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, createdInErrorClaim))

	// Insert the reserve summaries data
	openClaimReserveSummary := cr_builders.NewReserveSummary().
		WithDefaultMockData().
		WithClaimExternalId(openClaim.ExternalId).
		WithCreatedAt(now.AddDate(0, -1, 0)). // created the same day as the claim
		WithModifiedAt(now).                  // modified today
		Build()
	require.NoError(t, env.ClaimReservesWrapper.InsertReserveSummary(ctx, *openClaimReserveSummary))

	closedClaimReserveSummary := cr_builders.NewReserveSummary().
		WithDefaultMockData().
		WithClaimExternalId(closedClaim.ExternalId).
		WithCreatedAt(closedClaim.CreatedAt).   // created the same day as the claim
		WithModifiedAt(closedClaim.ModifiedAt). // modified today
		Build()
	require.NoError(t, env.ClaimReservesWrapper.InsertReserveSummary(ctx, *closedClaimReserveSummary))

	createdInErrorClaimReserveSummary := cr_builders.NewReserveSummary().
		WithDefaultMockData().
		WithClaimExternalId(createdInErrorClaim.ExternalId).
		Build()
	require.NoError(t, env.ClaimReservesWrapper.InsertReserveSummary(ctx, *createdInErrorClaimReserveSummary))

	notedClaim, err := claim_builder.New(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithCreatedAt(now.AddDate(0, -1, 0)).
		WithModifiedAt(now.AddDate(0, 0, -5)). // placing last modification 5 days before
		Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimsClient.InsertClaim(ctx, notedClaim))

	notedClaimReserveSummary := cr_builders.NewReserveSummary().
		WithDefaultMockData().
		WithClaimExternalId(openClaim.ExternalId).
		WithCreatedAt(now.AddDate(0, -1, 0)).
		WithModifiedAt(now.AddDate(0, 0, -4)).
		Build()
	require.NoError(t, env.ClaimReservesWrapper.InsertReserveSummary(ctx, *notedClaimReserveSummary))

	// defaults to modifiedAt: now()
	notedClaimNote, err := claim_notes_db.NewNoteBuilder(claim_enums.ClaimsProviderNars).
		WithDefaultMockData().
		WithClaimExternalId(notedClaim.ExternalId).
		Build()
	require.NoError(t, err)
	require.NoError(t, env.ClaimNotesClient.InsertNote(ctx, *notedClaimNote))

	testCases := []struct {
		name            string
		claimExternalId string
		wantModifiedAt  time.Time
		wantUpdate      bool
		wantErr         bool
	}{
		{
			name:            "open claim should have reserve summary modified at",
			claimExternalId: openClaim.ExternalId,
			wantModifiedAt:  openClaimReserveSummary.ModifiedAt,
			wantUpdate:      true,
		},
		{
			name:            "closed claim should not be updated due to being having the latest modified_at",
			claimExternalId: closedClaim.ExternalId,
			wantModifiedAt:  closedClaim.ModifiedAt,
		},
		{
			name:            "noted claim should be updated to latest note",
			claimExternalId: notedClaim.ExternalId,
			wantModifiedAt:  notedClaimNote.ModifiedAt,
		},
		{
			name:            "created in error should not be updated",
			claimExternalId: createdInErrorClaim.ExternalId,
			wantModifiedAt:  createdInErrorClaim.UpdatedAt,
		},
		{
			name:            "claim doesn't exist",
			claimExternalId: "fakeclaim1234567",
			wantErr:         true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := task.updateClaimModifiedAt(ctx, tc.claimExternalId)
			if tc.wantErr {
				assert.Error(t, err)
				return
			}

			claim, err := env.ClaimsClient.GetClaimByExternalId(ctx, tc.claimExternalId)
			require.NoError(t, err)
			assert.Equal(t, tc.wantModifiedAt.Unix(), claim.ModifiedAt.Unix())
			if tc.wantUpdate {
				assert.True(t, claim.UpdatedAt.After(now))
			}
		})
	}
}
