package nars_sync_claim

import (
	"context"
	"strconv"
	"time"

	"github.com/cockroachdb/errors"

	nars_cp "nirvanatech.com/nirvana/claims/claim_parties/db/nars"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/claims/nars"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type persistPartiesTask struct {
	job_utils.NoopUndoTask[*messages.NARSSyncClaimMessage]

	deps impl.Deps
}

func (t *persistPartiesTask) ID() string {
	return "persistePartiesTask"
}

func (t *persistPartiesTask) Run(jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage) error {
	return t.persistParties(jCtx, msg.ClaimExternalId)
}

func (t *persistPartiesTask) ShouldRetry(
	_ jtypes.Context,
	_ *messages.NARSSyncClaimMessage,
	failureError error,
	retryAttempt int,
) (retryable bool, waitBeforeRetry time.Duration) {
	if errors.Is(failureError, nars.ErrFailedRequest) && retryAttempt <= maxTaskRetryAttempts {
		return true, time.Second * 10
	}
	return false, 0
}

func (t *persistPartiesTask) Retry(jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage) error {
	return t.Run(jCtx, msg)
}

var _ jtypes.Task[*messages.NARSSyncClaimMessage] = &persistPartiesTask{}

func (t *persistPartiesTask) persistParties(ctx context.Context, claimExternalId string) error {
	narsParties, err := t.deps.NARSClient.GetPartiesByClaimExternalId(ctx, claimExternalId)
	if err != nil {
		return err
	}

	claim, err := t.deps.ClaimsClient.GetClaimByExternalId(ctx, claimExternalId)
	if err != nil {
		return err
	}

	for _, narsParty := range narsParties {
		createdAt, err := time.Parse(NarsTimestampFormatWithSeconds, narsParty.DateCreated)
		if err != nil {
			return err
		}

		modifiedAt, err := time.Parse(NarsTimestampFormatWithSeconds, narsParty.DateModified)
		if err != nil {
			return err
		}

		party := nars_cp.
			NewClaimParty(claim.Id, strconv.FormatInt(narsParty.ClaimPartyId, 10)).
			WithEntity(narsParty.ClaimPartyDesc).
			WithFirstName(narsParty.FirstName).
			WithMiddleName(narsParty.MiddleName).
			WithLastName(narsParty.LastName).
			WithCompanyName(narsParty.CompanyName).
			WithCreatedAt(createdAt).
			WithModifiedAt(modifiedAt)

		if err := t.deps.ClaimPartiesWrapper.UpsertClaimParty(ctx, *party); err != nil {
			return errors.Wrap(err, "failed to upsert claim party")
		}
	}

	return nil
}
