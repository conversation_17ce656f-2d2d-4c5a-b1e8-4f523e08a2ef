package nars_sync_claim

import (
	"context"
	"strconv"
	"time"

	"github.com/cockroachdb/errors"

	claim_notes_db "nirvanatech.com/nirvana/claims/claim_notes/db"
	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/nars"

	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

type persistNotesTask struct {
	job_utils.NoopUndoTask[*messages.NARSSyncClaimMessage]

	deps impl.Deps
}

func (t *persistNotesTask) ID() string {
	return "persistNotesTask"
}

func (t *persistNotesTask) Run(jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage) error {
	return t.persistNotes(jCtx, msg.ClaimExternalId)
}

func (t *persistNotesTask) ShouldRetry(
	_ jtypes.Context,
	_ *messages.NARSSyncClaimMessage,
	failureError error,
	retryAttempt int,
) (retryable bool, waitBeforeRetry time.Duration) {
	if errors.Is(failureError, nars.ErrFailedRequest) && retryAttempt <= maxTaskRetryAttempts {
		return true, time.Second * 10
	}
	return false, 0
}

func (t *persistNotesTask) Retry(jCtx jtypes.Context, msg *messages.NARSSyncClaimMessage) error {
	return t.Run(jCtx, msg)
}

var _ jtypes.Task[*messages.NARSSyncClaimMessage] = &persistNotesTask{}

func (t *persistNotesTask) persistNotes(ctx context.Context, claimExternalId string) error {
	narsNotes, err := t.deps.NARSClient.GetNotesByClaimExternalId(ctx, claimExternalId)
	if err != nil {
		return errors.Wrap(err, "failed to get notes by Claim External Id")
	}
	for _, narsNote := range narsNotes {
		createdAt, err := time.Parse(NarsTimestampFormatWithSeconds, narsNote.DateCreated)
		if err != nil {
			return err
		}
		modifiedAt, err := time.Parse(NarsTimestampFormatWithSeconds, narsNote.DateModified)
		if err != nil {
			return err
		}

		note, err := claim_notes_db.NewNoteBuilder(claim_enums.ClaimsProviderNars).
			WithExternalId(strconv.FormatInt(narsNote.NoteId, 10)).
			WithClaimExternalId(narsNote.ClaimNumber).
			WithValue(narsNote.NoteText).
			WithCategory(narsNote.NoteCategoryDesc).
			WithCreatedAt(createdAt).
			WithModifiedAt(modifiedAt).
			WithUpdatedAt(modifiedAt).
			Build()
		if err != nil {
			return err
		}
		//nolint:staticcheck // UpsertNote deprecated for general use; intentionally used here within NARS sync job.
		if err := t.deps.ClaimNotesClient.UpsertNote(ctx, *note); err != nil {
			return errors.Wrap(err, "failed to upsert note")
		}
	}
	return nil
}
