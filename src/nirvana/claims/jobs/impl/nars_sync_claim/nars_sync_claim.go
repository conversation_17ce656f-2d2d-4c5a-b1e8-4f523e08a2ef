package nars_sync_claim

import (
	"nirvanatech.com/nirvana/claims/jobs"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

func NewNARSSyncClaimJob(deps impl.Deps) (*jtypes.Job[*messages.NARSSyncClaimMessage], error) {
	return jtypes.NewJob(
		jobs.NARSSyncClaim,
		[]jtypes.TaskCreator[*messages.NARSSyncClaimMessage]{
			func() jtypes.Task[*messages.NARSSyncClaimMessage] {
				return &persistClaimTask{deps: deps}
			},
			func() jtypes.Task[*messages.NARSSyncClaimMessage] {
				return &persistReserveSummariesTask{deps: deps}
			},
			func() jtypes.Task[*messages.NARSSyncClaimMessage] {
				return &persistPostingsTask{deps: deps}
			},
			func() jtypes.Task[*messages.NARSSyncClaimMessage] {
				return &persistReserveTransactionsTask{deps: deps}
			},
			func() jtypes.Task[*messages.NARSSyncClaimMessage] {
				return &persistNotesTask{deps: deps}
			},
			func() jtypes.Task[*messages.NARSSyncClaimMessage] {
				return &persistPartiesTask{deps: deps}
			},
			func() jtypes.Task[*messages.NARSSyncClaimMessage] {
				return &updateClaimModifiedAtTask{deps: deps}
			},
		},
		messages.NARSSyncClaimMessageUnmarshalFn,
	)
}
