package proactive_status_summaries

import (
	"nirvanatech.com/nirvana/claims/jobs"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

func NewProactiveStatusSummariesJob(deps *impl.Deps) (
	*jtypes.Job[*messages.ProactiveStatusSummariesMessage],
	error,
) {
	return jtypes.NewJob(
		jobs.ProactiveStatusSummaries,
		[]jtypes.TaskCreator[*messages.ProactiveStatusSummariesMessage]{
			func() jtypes.Task[*messages.ProactiveStatusSummariesMessage] {
				return &proactiveStatusSummaryTask{deps: deps}
			},
		},
		messages.ProactiveStatusSummariesMessageMessageUnmarshalFn,
	)
}
