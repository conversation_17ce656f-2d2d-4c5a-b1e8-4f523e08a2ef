load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "proactive_status_summaries",
    srcs = [
        "proactive_status_summaries.go",
        "proactive_status_summary_task.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/jobs/impl/proactive_status_summaries",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/jobs",
        "//nirvana/claims/jobs/impl",
        "//nirvana/claims/jobs/messages",
        "//nirvana/common-go/log",
        "//nirvana/infra/authz",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "@com_github_cockroachdb_errors//:errors",
    ],
)
