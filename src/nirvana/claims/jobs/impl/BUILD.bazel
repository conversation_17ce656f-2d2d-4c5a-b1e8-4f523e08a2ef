load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "impl",
    srcs = ["deps.go"],
    importpath = "nirvanatech.com/nirvana/claims/jobs/impl",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/claim_feedbacks/client",
        "//nirvana/claims/claim_notes/client",
        "//nirvana/claims/claim_parties/db/nars",
        "//nirvana/claims/claim_postings/db/nars",
        "//nirvana/claims/claim_reserves/db/nars",
        "//nirvana/claims/claim_summaries/client",
        "//nirvana/claims/client",
        "//nirvana/claims/metrics",
        "//nirvana/claims/nars",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/events",
        "@com_github_benbjo<PERSON>son_clock//:clock",
        "@org_uber_go_fx//:fx",
    ],
)
