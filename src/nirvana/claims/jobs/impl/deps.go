package impl

import (
	"github.com/benbjohnson/clock"
	"go.uber.org/fx"

	cf_client "nirvanatech.com/nirvana/claims/claim_feedbacks/client"
	notes_client "nirvanatech.com/nirvana/claims/claim_notes/client"
	nars_cp "nirvanatech.com/nirvana/claims/claim_parties/db/nars"
	nars_postings "nirvanatech.com/nirvana/claims/claim_postings/db/nars"
	nars_cr "nirvanatech.com/nirvana/claims/claim_reserves/db/nars"
	cs_client "nirvanatech.com/nirvana/claims/claim_summaries/client"
	claims_client "nirvanatech.com/nirvana/claims/client"
	"nirvanatech.com/nirvana/claims/metrics"
	"nirvanatech.com/nirvana/claims/nars"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/events"
)

type Deps struct {
	fx.In

	Clk clock.Clock

	CfClient           *cf_client.Client
	ClaimsClient       *claims_client.Client
	ClaimSummaryClient cs_client.Client
	EventsHandler      events.EventsHandler
	MetricsClient      metrics.Client
	NARSClient         *nars.Client

	AuthWrapper          auth.DataWrapper
	ClaimNotesClient     *notes_client.Client
	ClaimPartiesWrapper  *nars_cp.DataWrapper
	ClaimPostingsWrapper *nars_postings.DataWrapper
	ClaimReservesWrapper *nars_cr.DataWrapper
}
