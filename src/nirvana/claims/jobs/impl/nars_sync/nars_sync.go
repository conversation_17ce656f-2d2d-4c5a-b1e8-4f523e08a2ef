package nars_sync

import (
	"nirvanatech.com/nirvana/claims/jobs"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

func NewNARSSyncJob(deps impl.Deps) (
	*jtypes.Job[job_utils.EmptyMessageT],
	error,
) {
	return jtypes.NewJob(
		jobs.NARSSync,
		[]jtypes.TaskCreator[job_utils.EmptyMessageT]{
			narsSyncClaimSpawn(deps),
			narsSyncClaimAwait(),
			func() jtypes.Task[job_utils.EmptyMessageT] {
				return &EmitMetricsTask{deps: deps}
			},
		},
		job_utils.EmptyUnmarshalMessageFn,
	)
}
