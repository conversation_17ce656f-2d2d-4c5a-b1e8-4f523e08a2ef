load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "nars_sync",
    srcs = [
        "emit_metrics.go",
        "nars_sync.go",
        "nars_sync_claim_await.go",
        "nars_sync_claim_spawn.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/jobs/impl/nars_sync",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/client",
        "//nirvana/claims/jobs",
        "//nirvana/claims/jobs/impl",
        "//nirvana/claims/jobs/messages",
        "//nirvana/claims/metrics",
        "//nirvana/claims/nars/api/components",
        "//nirvana/common-go/log",
        "//nirvana/common-go/retry",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/telematics/data_platform/workflows/common",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "nars_sync_test",
    srcs = ["nars_sync_test.go"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/claims/claim_notes/client",
        "//nirvana/claims/claim_parties/db/nars",
        "//nirvana/claims/claim_reserves/db/nars",
        "//nirvana/claims/claim_reserves/enums",
        "//nirvana/claims/client",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/claims/jobs",
        "//nirvana/claims/jobs/registry",
        "//nirvana/common-go/metrics",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/nars",
        "//nirvana/infra/fx/testloader",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
