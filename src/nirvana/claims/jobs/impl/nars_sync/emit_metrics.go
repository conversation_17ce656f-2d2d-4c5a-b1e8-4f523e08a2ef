package nars_sync

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/claims/jobs"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/metrics"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/common"
)

type EmitMetricsTask struct {
	job_utils.DefaultRetryable[job_utils.EmptyMessageT]
	job_utils.NoopUndoTask[job_utils.EmptyMessageT]

	deps impl.Deps
}

func (t *EmitMetricsTask) Retry(
	jCtx jtypes.Context,
	msg job_utils.EmptyMessageT,
) error {
	return t.Run(jCtx, msg)
}

func (t *EmitMetricsTask) ID() string {
	return "EmitMetricsTask"
}

func (t *EmitMetricsTask) Run(jCtx jtypes.Context, _ job_utils.EmptyMessageT) error {
	jobRuns, err := common.GetSpawnedJobRuns(jCtx, jobs.NARSSyncClaim)
	if err != nil {
		return err
	}

	getErr := func(jr *jtypes.JobRun) error {
		isFailedAndRetried := jr.Status == jtypes.JobRunStatusFailed && jr.RetryAttempt.Ptr() != nil
		if !isFailedAndRetried {
			return nil
		}
		return errors.New(jr.Error)
	}
	for _, jr := range jobRuns {
		t.deps.MetricsClient.IncOperation(jCtx, metrics.OperationNARSSyncClaim, getErr(jr))
	}

	return nil
}

var _ jtypes.Task[job_utils.EmptyMessageT] = &EmitMetricsTask{}
