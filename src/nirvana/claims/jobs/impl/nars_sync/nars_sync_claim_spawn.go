package nars_sync

import (
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/claims/client"
	"nirvanatech.com/nirvana/claims/jobs"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/messages"
	"nirvanatech.com/nirvana/claims/metrics"
	"nirvanatech.com/nirvana/claims/nars/api/components"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/retry"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/common"
)

const (
	narsSyncClaimSpawnTaskId                  = "NARSSyncClaimSpawnTask"
	narsSyncClaimSpawnFanOut                  = 5
	narsSyncClaimMaxRetryAttempts             = 5
	narsSyncClaimExponentialBackOffStart      = 1 * time.Second
	narsSyncClaimExponentialBackOffMultiplier = 2
)

func narsSyncClaimSpawn(deps impl.Deps) jtypes.TaskCreator[job_utils.EmptyMessageT] {
	return common.NewSpawnJobsTaskCreator(
		narsSyncClaimSpawnTaskId,
		func(jCtx jtypes.Context, _ job_utils.EmptyMessageT) ([]common.JobRunPayloadArgs, error) {
			var claims components.ClaimsResponse
			var err error

			retryableGetClaims := func() error {
				claims, err = deps.NARSClient.GetClaims(jCtx)
				deps.MetricsClient.IncOperation(jCtx, metrics.OperationNARSGetClaims, err)
				return err
			}

			err = retry.Do(
				jCtx,
				retryableGetClaims,
				retry.WithMaxAttempts(narsSyncClaimMaxRetryAttempts),
				retry.WithBackOff(
					retry.ExponentialBackOff(
						narsSyncClaimExponentialBackOffStart,
						narsSyncClaimExponentialBackOffMultiplier,
					),
				),
			)
			if err != nil {
				return nil, errors.Wrap(err, "failed to get claims")
			}

			if len(claims) == 0 {
				return nil, errors.New("no claims to sync")
			}

			var payloadArgs []common.JobRunPayloadArgs
			for _, claimOverview := range claims {
				policyAllowed, err := client.IsPolicyAllowed(claimOverview.PolicyNbr)
				if err != nil {
					log.Error(
						jCtx,
						"failed to check if policy is allowed",
						log.Err(err),
						log.String("claimNumber", claimOverview.ClaimNumber),
						log.String("policyNumber", claimOverview.PolicyNbr),
					)
					continue
				}
				if !policyAllowed {
					log.Info(
						jCtx,
						"policy not allowed, omitting claim",
						log.String("claimNumber", claimOverview.ClaimNumber),
						log.String("policyNumber", claimOverview.PolicyNbr),
					)
					continue
				}
				msgClaimOverview := claimOverview
				payload := common.JobRunPayloadArgs{
					Id:          claimOverview.ClaimNumber,
					RegistryKey: jobs.NARSSyncClaim,
					Message: &messages.NARSSyncClaimMessage{
						ClaimExternalId: msgClaimOverview.ClaimNumber,
					},
					Metadata: jtypes.NewMetadata(jtypes.OneOff),
				}
				payloadArgs = append(
					payloadArgs,
					payload,
				)
			}
			return payloadArgs, nil
		},
		common.JobSpawnTaskNoOpOnSuccess[job_utils.EmptyMessageT],
		narsSyncClaimSpawnFanOut,
	)
}
