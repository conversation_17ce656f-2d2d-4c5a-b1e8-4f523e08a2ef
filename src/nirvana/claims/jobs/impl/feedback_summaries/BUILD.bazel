load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "feedback_summaries",
    srcs = ["feedback_summaries.go"],
    importpath = "nirvanatech.com/nirvana/claims/jobs/impl/feedback_summaries",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/claim_feedbacks/client",
        "//nirvana/claims/claim_feedbacks/notifications",
        "//nirvana/claims/jobs",
        "//nirvana/claims/jobs/impl",
        "//nirvana/events",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
    ],
)

go_test(
    name = "feedback_summaries_test",
    srcs = ["feedback_summaries_test.go"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/claims/claim_feedbacks/client",
        "//nirvana/claims/claim_feedbacks/db",
        "//nirvana/claims/client",
        "//nirvana/claims/db",
        "//nirvana/claims/enums",
        "//nirvana/claims/jobs",
        "//nirvana/claims/jobs/registry",
        "//nirvana/infra/fx/testloader",
        "//nirvana/jobber",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
