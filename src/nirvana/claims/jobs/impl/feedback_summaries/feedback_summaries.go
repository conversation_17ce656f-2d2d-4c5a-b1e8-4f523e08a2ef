package feedback_summaries

import (
	"context"
	"time"

	cf_client "nirvanatech.com/nirvana/claims/claim_feedbacks/client"
	"nirvanatech.com/nirvana/claims/claim_feedbacks/notifications"
	"nirvanatech.com/nirvana/claims/jobs"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
)

func NewFeedbackSummariesJob(deps *impl.Deps) (
	*jtypes.Job[job_utils.EmptyMessageT],
	error,
) {
	return jtypes.NewJob(
		jobs.FeedbackSummaries,
		[]jtypes.TaskCreator[job_utils.EmptyMessageT]{
			func() jtypes.Task[job_utils.EmptyMessageT] {
				return &FeedbackSummaryTask{deps: deps}
			},
		},
		job_utils.EmptyUnmarshalMessageFn,
	)
}

type FeedbackSummaryTask struct {
	job_utils.NonRetryableTask[job_utils.EmptyMessageT]
	job_utils.NoopUndoTask[job_utils.EmptyMessageT]

	deps *impl.Deps
}

func (t *FeedbackSummaryTask) ID() string {
	return "ClaimFeedbackSummaryTask"
}

func (t *FeedbackSummaryTask) Run(ctx jtypes.Context, _ job_utils.EmptyMessageT) error {
	return uploadClaimFeedbackEvent(ctx, t.deps.CfClient, t.deps.EventsHandler)
}

// uploadClaimFeedbackEvent uploads claim feedback stats to the events system.
func uploadClaimFeedbackEvent(ctx context.Context, cfClient *cf_client.Client, eventsHandler events.EventsHandler) error {
	summary, err := cfClient.GetStats(ctx)
	if err != nil {
		return err
	}

	now := time.Now()
	event := notifications.NewClaimFeedbackSummary(
		summary.AvgRating,
		summary.Avg30DaysRating,
		summary.OneToFourStarRatings,
		summary.FiveStarRatings,
		now.AddDate(0, 0, -7),
		now,
	)

	if err := eventsHandler.UploadEvent(ctx, event); err != nil {
		return err
	}
	return nil
}
