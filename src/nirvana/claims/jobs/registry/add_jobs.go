package registry

import (
	"go.uber.org/multierr"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/claims/jobs/impl"
	"nirvanatech.com/nirvana/claims/jobs/impl/feedback_summaries"
	"nirvanatech.com/nirvana/claims/jobs/impl/nars_sync"
	"nirvanatech.com/nirvana/claims/jobs/impl/nars_sync_claim"
	"nirvanatech.com/nirvana/claims/jobs/impl/proactive_status_summaries"
	"nirvanatech.com/nirvana/jobber/registry"
)

func AddJobsToRegistry(r *quoting_jobber.Registry, deps impl.Deps) (combinedErr error) {
	claimFeedbackSummariesJob, err := feedback_summaries.NewFeedbackSummariesJob(&deps)
	combinedErr = multierr.Append(combinedErr, err)

	NARSSyncJob, err := nars_sync.NewNARSSyncJob(deps)
	combinedErr = multierr.Append(combinedErr, err)

	NARSSyncClaimJob, err := nars_sync_claim.NewNARSSyncClaimJob(deps)
	combinedErr = multierr.Append(combinedErr, err)

	proactiveStatusSummariesJob, err := proactive_status_summaries.NewProactiveStatusSummariesJob(&deps)
	combinedErr = multierr.Append(combinedErr, err)

	return multierr.Combine(combinedErr,
		registry.AddJob(r, claimFeedbackSummariesJob),
		registry.AddJob(r, NARSSyncJob),
		registry.AddJob(r, NARSSyncClaimJob),
		registry.AddJob(r, proactiveStatusSummariesJob),
	)
}
