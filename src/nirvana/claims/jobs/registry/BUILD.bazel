load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "registry",
    srcs = [
        "add_jobs.go",
        "fx.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/jobs/registry",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/claims/jobs/impl",
        "//nirvana/claims/jobs/impl/feedback_summaries",
        "//nirvana/claims/jobs/impl/nars_sync",
        "//nirvana/claims/jobs/impl/nars_sync_claim",
        "//nirvana/claims/jobs/impl/proactive_status_summaries",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/jobber/registry",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_multierr//:multierr",
    ],
)
