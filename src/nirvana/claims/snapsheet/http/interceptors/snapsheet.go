package interceptors

import (
	"database/sql"
	"net/http"

	"github.com/cockroachdb/errors"

	"github.com/labstack/echo/v4"

	"nirvanatech.com/nirvana/claims/metrics"
	snapsheet_client "nirvanatech.com/nirvana/claims/snapsheet/client"
	"nirvanatech.com/nirvana/claims/snapsheet/client/policy_type"
	"nirvanatech.com/nirvana/claims/snapsheet/http/openapi"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/tracing"
)

func NewEchoInterceptor(d deps) *EchoInterceptor {
	return &EchoInterceptor{d}
}

type EchoInterceptor struct {
	deps deps
}

func (e *EchoInterceptor) GetSnapsheetPolicy(echoCtx echo.Context, params openapi.GetSnapsheetPolicyParams) error {
	ctx := echoCtx.Request().Context()
	ctx, span := tracing.Start(ctx, "GetSnapsheetPolicy")
	defer span.End()

	policyNumber := pointer_utils.StringValOr(params.PolicyNumber, "PolicyNumberExample-25")

	log.Info(ctx, "fetching policy details for Snapsheet", log.String("policyNumber", policyNumber))

	pd, err := e.deps.SnapsheetClient.GetPolicyDetails(ctx, policyNumber)
	e.deps.ClaimsMetricsClient.IncOperation(ctx, metrics.OperationSnapsheetGetPolicyDetails, err)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return echoCtx.JSON(http.StatusNotFound, "Policy not found")
		}
		return err
	}

	var agencyAddress *openapi.Address
	if pd.Agency.Address != nil {
		agencyAddress = &openapi.Address{
			Region:     &pd.Agency.Address.State,
			City:       &pd.Agency.Address.City,
			PostalCode: &pd.Agency.Address.Zip,
			Address1:   &pd.Agency.Address.Street1,
			Address2:   pd.Agency.Address.Street2,
		}
	}

	coverages := make([]openapi.Coverage, 0, len(pd.Coverages))
	for _, c := range pd.Coverages {
		coverages = append(coverages, openapi.Coverage{
			CoverageCode: c.Code,
			Limits: &[]openapi.Limit{
				{
					Amount:     pointer_utils.ToPointer(int32(c.Limit.Amount)),
					Deductible: pointer_utils.ToPointer(int32(c.Limit.Deductible)),
				},
			},
			Name: c.Name,
		})
	}

	vehiclesAsRisks := parseVehiclesIntoRisks(pd.Vehicles, coverages)
	locationsAsRisks := parseLocationsIntoRisks(pd.TerminalLocations, coverages)
	risks := append(vehiclesAsRisks, locationsAsRisks...)

	parsedEndorsements := slice_utils.Map(
		pd.Endorsements,
		func(e snapsheet_client.Endorsement) openapi.Endorsement {
			return openapi.Endorsement{
				Name:           &e.Filename,
				EffectiveAt:    &e.DateEffective,
				ExpirationDate: &pd.ExpirationAt,
				ClauseType:     &e.ChangeType,
			}
		},
	)

	businesses := []openapi.Business{
		{
			Role: pointer_utils.ToPointer(openapi.Policyholder),
			Name: &pd.PolicyHolder,
		},
	}

	policyType, err := parsePolicyType(pd.PolicyType)
	if err != nil {
		return err
	}

	response := &openapi.GetPolicyResponse{
		EffectiveAt:  pd.EffectiveAt,
		ExpirationAt: pd.ExpirationAt,
		InceptionAt:  pd.InceptionAt,
		PolicyNumber: policyNumber,
		PolicyType:   &policyType,
		Status:       pointer_utils.ToPointer(pd.Status.String()),
		Version:      pointer_utils.ToPointer(pd.Version),
		AgentInformation: &openapi.AgentInformation{
			AgencyName:    pd.Agency.Name,
			AgencyType:    openapi.Broker,
			AgencyAddress: agencyAddress,
		},
		Underwriting: &openapi.Underwriting{
			Contact: &pd.Underwriting.Contact,
			Team:    pointer_utils.ToPointer(pd.Underwriting.Team.String()),
		},
		Risks:           risks,
		CancelledAt:     pd.CancelledTime,
		CancelledReason: pd.CancelledReason,
		Coverages:       coverages,
		Endorsements:    &parsedEndorsements,
		Businesses:      &businesses,
	}

	log.Info(ctx, "policy details response for Snapsheet", log.Any("response", response))
	return echoCtx.JSON(http.StatusOK, response)
}

func parseLocationsIntoRisks(
	locs []snapsheet_client.Location,
	covs []openapi.Coverage,
) []openapi.Risk {
	coveragesForLocations := slice_utils.Filter(covs, func(c openapi.Coverage) bool {
		return c.CoverageCode == "GL"
	})

	risks := make([]openapi.Risk, 0, len(locs))
	for _, l := range locs {
		r := openapi.Risk{
			ExternalLocationIdentifier: l.AddressLineOne,
			ExternalRiskIdentifier:     l.AddressLineOne,
			Address: &openapi.Address{
				Address1:   &l.AddressLineOne,
				Address2:   l.AddressLineTwo,
				Country:    pointer_utils.ToPointer("US"),
				PostalCode: pointer_utils.ToPointer(l.ZipCode),
				Region:     pointer_utils.ToPointer(l.State.String()),
			},
			Coverages: coveragesForLocations,
		}
		risks = append(risks, r)
	}

	return risks
}

func parseVehiclesIntoRisks(
	vehs []snapsheet_client.Vehicle,
	covs []openapi.Coverage,
) []openapi.Risk {
	coveragesForVehicles := slice_utils.Filter(covs, func(c openapi.Coverage) bool {
		return c.CoverageCode != "GL"
	})

	risks := make([]openapi.Risk, 0, len(vehs))
	for _, v := range vehs {
		var val *int32
		if v.Value != nil {
			val = pointer_utils.ToPointer(int32(*v.Value))
		}
		r := openapi.Risk{
			ExternalLocationIdentifier: "N/A",
			ExternalRiskIdentifier:     v.Vin,
			Vehicle: &openapi.Vehicle{
				Make:      v.Make,
				Model:     v.Model,
				Value:     val,
				VinNumber: &v.Vin,
				Year:      v.Year,
			},
			Coverages: coveragesForVehicles,
		}
		risks = append(risks, r)
	}

	return risks
}

func parsePolicyType(p policy_type.PolicyType) (openapi.GetPolicyResponsePolicyType, error) {
	switch p {
	case policy_type.PolicyTypeCommercialAuto:
		return openapi.COMMERCIALAUTO, nil
	case policy_type.PolicyTypeGeneralLiability:
		return openapi.GENERALLIABILITY, nil
	default:
		return "", errors.Newf("unsupported policy type: %s", p)
	}
}

var _ = openapi.ServerInterface(&EchoInterceptor{})
