// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package openapi

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	SessionIdAuthScopes = "sessionIdAuth.Scopes"
)

// Defines values for AgentInformationAgencyType.
const (
	Broker AgentInformationAgencyType = "broker"
	Direct AgentInformationAgencyType = "direct"
)

// Defines values for BusinessRole.
const (
	AdditionalInsured BusinessRole = "additional_insured"
	Lienholder        BusinessRole = "lienholder"
	LossPayee         BusinessRole = "loss_payee"
	Mortgagee         BusinessRole = "mortgagee"
	NamedInsured      BusinessRole = "named_insured"
	Other             BusinessRole = "other"
	Policyholder      BusinessRole = "policyholder"
	Trustee           BusinessRole = "trustee"
)

// Defines values for GetPolicyResponsePolicyType.
const (
	AUTO                  GetPolicyResponsePolicyType = "AUTO"
	AUTOPROPERTY          GetPolicyResponsePolicyType = "AUTO_PROPERTY"
	BUSINESSOWNERSPOLICY  GetPolicyResponsePolicyType = "BUSINESS_OWNERS_POLICY"
	COMMERCIALAUTO        GetPolicyResponsePolicyType = "COMMERCIAL_AUTO"
	COMMERCIALPACKAGE     GetPolicyResponsePolicyType = "COMMERCIAL_PACKAGE"
	COMMERCIALPACKAGEAUTO GetPolicyResponsePolicyType = "COMMERCIAL_PACKAGE_AUTO"
	COMMERCIALPROPERTY    GetPolicyResponsePolicyType = "COMMERCIAL_PROPERTY"
	FARMOWNERS            GetPolicyResponsePolicyType = "FARM_OWNERS"
	GENERALLIABILITY      GetPolicyResponsePolicyType = "GENERAL_LIABILITY"
	HOME                  GetPolicyResponsePolicyType = "HOME"
	INLANDMARINE          GetPolicyResponsePolicyType = "INLAND_MARINE"
	PROFESSIONALLIABILITY GetPolicyResponsePolicyType = "PROFESSIONAL_LIABILITY"
	PROPERTY              GetPolicyResponsePolicyType = "PROPERTY"
	WORKERSCOMPENSATION   GetPolicyResponsePolicyType = "WORKERS_COMPENSATION"
)

// Address defines model for Address.
type Address struct {
	Address1   *string `json:"address1,omitempty"`
	Address2   *string `json:"address2,omitempty"`
	City       *string `json:"city,omitempty"`
	Country    *string `json:"country,omitempty"`
	PostalCode *string `json:"postalCode,omitempty"`
	Region     *string `json:"region,omitempty"`
}

// AgentInformation defines model for AgentInformation.
type AgentInformation struct {
	AgencyAddress        *Address                   `json:"agencyAddress,omitempty"`
	AgencyCode           *string                    `json:"agencyCode,omitempty"`
	AgencyContactMethods *[]ContactMethod           `json:"agencyContactMethods,omitempty"`
	AgencyName           string                     `json:"agencyName"`
	AgencyType           AgentInformationAgencyType `json:"agencyType"`
	BranchCode           *string                    `json:"branchCode,omitempty"`
	BrokerSegmentation   *string                    `json:"brokerSegmentation,omitempty"`
	ParentAgencyCode     *string                    `json:"parentAgencyCode,omitempty"`
	ParentBranchCode     *string                    `json:"parentBranchCode,omitempty"`
	ParentName           *string                    `json:"parentName,omitempty"`
}

// AgentInformationAgencyType defines model for AgentInformation.AgencyType.
type AgentInformationAgencyType string

// BuildingDetail defines model for BuildingDetail.
type BuildingDetail struct {
	BldgCodeGrade            *string `json:"bldgCodeGrade,omitempty"`
	BuildingType             *string `json:"buildingType,omitempty"`
	ConstructionType         *string `json:"constructionType,omitempty"`
	DistanceToTidalWater     *int32  `json:"distanceToTidalWater,omitempty"`
	DistanceToTidalWaterUnit *string `json:"distanceToTidalWaterUnit,omitempty"`
	FireProtectionClass      *string `json:"fireProtectionClass,omitempty"`
	HoFormNumber             *string `json:"hoFormNumber,omitempty"`
	NumberOfFloors           *int32  `json:"numberOfFloors,omitempty"`
	NumberOfMonthsOccupied   *int32  `json:"numberOfMonthsOccupied,omitempty"`
	NumberRooms              *int32  `json:"numberRooms,omitempty"`
	RoofMaterial             *string `json:"roofMaterial,omitempty"`
	Siding                   *string `json:"siding,omitempty"`
	Size                     *int32  `json:"size,omitempty"`
	Sprinkler                *string `json:"sprinkler,omitempty"`
	SwimmingPool             *string `json:"swimmingPool,omitempty"`
	UsageType                *string `json:"usageType,omitempty"`
	Wiring                   *string `json:"wiring,omitempty"`
	YearBuilt                *int32  `json:"yearBuilt,omitempty"`
}

// Business defines model for Business.
type Business struct {
	Address             *Address         `json:"address,omitempty"`
	ContactMethods      *[]ContactMethod `json:"contactMethods,omitempty"`
	Interests           *[]Interest      `json:"interests,omitempty"`
	Name                *string          `json:"name,omitempty"`
	Occupation          *string          `json:"occupation,omitempty"`
	ProviderSubjectId   *string          `json:"providerSubjectId,omitempty"`
	ReferenceLoanNumber *string          `json:"referenceLoanNumber,omitempty"`
	Role                *BusinessRole    `json:"role,omitempty"`
	TaxId               *string          `json:"taxId,omitempty"`
}

// BusinessRole defines model for Business.Role.
type BusinessRole string

// ContactMethod defines model for ContactMethod.
type ContactMethod struct {
	Country         string  `json:"country"`
	CountryCode     *string `json:"countryCode,omitempty"`
	PreferredMethod *bool   `json:"preferredMethod,omitempty"`
	Type            *string `json:"type,omitempty"`
	Value           *string `json:"value,omitempty"`
}

// Coverage defines model for Coverage.
type Coverage struct {
	CoverageCode            string   `json:"coverageCode"`
	ExternalCoverageDetails *string  `json:"externalCoverageDetails,omitempty"`
	Limits                  *[]Limit `json:"limits,omitempty"`
	Name                    string   `json:"name"`
	Optional                *bool    `json:"optional,omitempty"`
}

// Deductible defines model for Deductible.
type Deductible struct {
	Amount         *int32  `json:"amount,omitempty"`
	DeductibleType *string `json:"deductibleType,omitempty"`
	Percent        *int32  `json:"percent,omitempty"`
}

// Driver defines model for Driver.
type Driver struct {
	CoverType             *string             `json:"coverType,omitempty"`
	DateOfBirth           *openapi_types.Date `json:"dateOfBirth,omitempty"`
	DriverType            *string             `json:"driverType,omitempty"`
	FirstName             *string             `json:"firstName,omitempty"`
	Gender                *string             `json:"gender,omitempty"`
	LastName              *string             `json:"lastName,omitempty"`
	LicenseIssuingCountry *string             `json:"licenseIssuingCountry,omitempty"`
	LicenseNumber         *string             `json:"licenseNumber,omitempty"`
	LicenseType           *string             `json:"licenseType,omitempty"`
	MainDriver            *bool               `json:"mainDriver,omitempty"`
	MiddleName            *string             `json:"middleName,omitempty"`
	Occupation            *string             `json:"occupation,omitempty"`
	PenaltyPoints         *int32              `json:"penaltyPoints,omitempty"`
	Prefix                *string             `json:"prefix,omitempty"`
	Suffix                *string             `json:"suffix,omitempty"`
}

// Endorsement defines model for Endorsement.
type Endorsement struct {
	Clause            *string    `json:"clause,omitempty"`
	ClauseType        *string    `json:"clauseType,omitempty"`
	Description       *string    `json:"description,omitempty"`
	EffectiveAt       *time.Time `json:"effectiveAt,omitempty"`
	EndorsementNumber *string    `json:"endorsementNumber,omitempty"`
	ExpirationDate    *time.Time `json:"expirationDate,omitempty"`
	Name              *string    `json:"name,omitempty"`
	TerminationAt     *time.Time `json:"terminationAt,omitempty"`
}

// GetPolicyResponse defines model for GetPolicyResponse.
type GetPolicyResponse struct {
	AgentInformation *AgentInformation            `json:"agentInformation,omitempty"`
	Businesses       *[]Business                  `json:"businesses,omitempty"`
	CancelledAt      *time.Time                   `json:"cancelledAt,omitempty"`
	CancelledReason  *string                      `json:"cancelledReason,omitempty"`
	Coverages        []Coverage                   `json:"coverages"`
	Deductibles      *[]Deductible                `json:"deductibles,omitempty"`
	EffectiveAt      time.Time                    `json:"effectiveAt"`
	Endorsements     *[]Endorsement               `json:"endorsements,omitempty"`
	ExpirationAt     time.Time                    `json:"expirationAt"`
	InceptionAt      time.Time                    `json:"inceptionAt"`
	Notes            *[]Note                      `json:"notes,omitempty"`
	People           *[]Person                    `json:"people,omitempty"`
	PolicyNumber     string                       `json:"policyNumber"`
	PolicyType       *GetPolicyResponsePolicyType `json:"policyType,omitempty"`
	Product          *Product                     `json:"product,omitempty"`
	Reservation      *Reservation                 `json:"reservation,omitempty"`
	Risks            []Risk                       `json:"risks"`
	Status           *string                      `json:"status,omitempty"`
	Underwriting     *Underwriting                `json:"underwriting,omitempty"`
	Version          *string                      `json:"version,omitempty"`
	Versions         *[]Version                   `json:"versions,omitempty"`
}

// GetPolicyResponsePolicyType defines model for GetPolicyResponse.PolicyType.
type GetPolicyResponsePolicyType string

// Interest defines model for Interest.
type Interest struct {
	InterestId   *string `json:"interestId,omitempty"`
	InterestType *string `json:"interestType,omitempty"`
}

// Limit defines model for Limit.
type Limit struct {
	Amount            *int32  `json:"amount,omitempty"`
	CoverageLimitCode *string `json:"coverageLimitCode,omitempty"`
	CoverageLimitType *string `json:"coverageLimitType,omitempty"`
	Deductible        *int32  `json:"deductible,omitempty"`
	Excess            *int32  `json:"excess,omitempty"`
	Name              *string `json:"name,omitempty"`
}

// Note defines model for Note.
type Note struct {
	CreatedAt time.Time `json:"createdAt"`
	Details   string    `json:"details"`
	UserName  string    `json:"userName"`
}

// Person defines model for Person.
type Person struct {
	Address             *Address            `json:"address,omitempty"`
	ContactMethods      *[]ContactMethod    `json:"contactMethods,omitempty"`
	DateOfBirth         *openapi_types.Date `json:"dateOfBirth,omitempty"`
	FirstName           *string             `json:"firstName,omitempty"`
	Gender              *string             `json:"gender,omitempty"`
	Interests           *[]Interest         `json:"interests,omitempty"`
	LastName            *string             `json:"lastName,omitempty"`
	MiddleName          *string             `json:"middleName,omitempty"`
	Occupation          *string             `json:"occupation,omitempty"`
	Prefix              *string             `json:"prefix,omitempty"`
	ProviderSubjectId   *string             `json:"providerSubjectId,omitempty"`
	ReferenceLoanNumber *string             `json:"referenceLoanNumber,omitempty"`
	Role                *string             `json:"role,omitempty"`
	Ssn                 *string             `json:"ssn,omitempty"`
	Suffix              *string             `json:"suffix,omitempty"`
}

// Product defines model for Product.
type Product struct {
	Code *string `json:"code,omitempty"`
	Name *string `json:"name,omitempty"`
}

// Property defines model for Property.
type Property struct {
	BuildingDetail   *BuildingDetail `json:"buildingDetail,omitempty"`
	Model            *string         `json:"model,omitempty"`
	Name             *string         `json:"name,omitempty"`
	PropertyLocation *string         `json:"propertyLocation,omitempty"`
	PropertyType     *string         `json:"propertyType,omitempty"`
	PurchaseYear     *int32          `json:"purchaseYear,omitempty"`
	ReplacementCost  *float32        `json:"replacementCost,omitempty"`
	SerialNumber     *string         `json:"serialNumber,omitempty"`
}

// Reservation defines model for Reservation.
type Reservation struct {
	ReservationEndDatetime   *time.Time `json:"reservationEndDatetime,omitempty"`
	ReservationNumber        *string    `json:"reservationNumber,omitempty"`
	ReservationStartDatetime *time.Time `json:"reservationStartDatetime,omitempty"`
}

// Risk defines model for Risk.
type Risk struct {
	Address                    *Address           `json:"address,omitempty"`
	Code                       *string            `json:"code,omitempty"`
	CodeDescription            *string            `json:"codeDescription,omitempty"`
	Coverages                  []Coverage         `json:"coverages"`
	Drivers                    *[]Driver          `json:"drivers,omitempty"`
	Endorsements               *[]Endorsement     `json:"endorsements,omitempty"`
	ExternalLocationIdentifier string             `json:"externalLocationIdentifier"`
	ExternalRiskIdentifier     string             `json:"externalRiskIdentifier"`
	Id                         *string            `json:"id,omitempty"`
	Property                   *Property          `json:"property,omitempty"`
	Type                       *string            `json:"type,omitempty"`
	Vehicle                    *Vehicle           `json:"vehicle,omitempty"`
	VehicleLocations           *[]VehicleLocation `json:"vehicleLocations,omitempty"`
}

// Underwriting defines model for Underwriting.
type Underwriting struct {
	Account *string `json:"account,omitempty"`
	Contact *string `json:"contact,omitempty"`
	Team    *string `json:"team,omitempty"`
}

// Vehicle defines model for Vehicle.
type Vehicle struct {
	Affiliation      *string `json:"affiliation,omitempty"`
	CertDriving      *string `json:"certDriving,omitempty"`
	CertUse          *string `json:"certUse,omitempty"`
	ClassOfUse       *string `json:"classOfUse,omitempty"`
	Make             *string `json:"make,omitempty"`
	Model            *string `json:"model,omitempty"`
	Registration     *string `json:"registration,omitempty"`
	Temporary        *bool   `json:"temporary,omitempty"`
	TemporaryPremium *int32  `json:"temporaryPremium,omitempty"`
	Value            *int32  `json:"value,omitempty"`
	VinNumber        *string `json:"vinNumber,omitempty"`
	Year             *int32  `json:"year,omitempty"`
}

// VehicleLocation defines model for VehicleLocation.
type VehicleLocation struct {
	Address      *Address `json:"address,omitempty"`
	LocationType *string  `json:"locationType,omitempty"`
}

// Version defines model for Version.
type Version struct {
	EffectiveAt             *time.Time `json:"effectiveAt,omitempty"`
	ExpirationAt            *time.Time `json:"expirationAt,omitempty"`
	ProviderTypeDescription *string    `json:"providerTypeDescription,omitempty"`
	ProviderVersionNumber   *string    `json:"providerVersionNumber,omitempty"`
}

// GetSnapsheetPolicyParams defines parameters for GetSnapsheetPolicy.
type GetSnapsheetPolicyParams struct {
	PolicyNumber   *string    `form:"policyNumber,omitempty" json:"policyNumber,omitempty"`
	DateTimeOfLoss *time.Time `form:"dateTimeOfLoss,omitempty" json:"dateTimeOfLoss,omitempty"`
}

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// GetSnapsheetPolicy request
	GetSnapsheetPolicy(ctx context.Context, params *GetSnapsheetPolicyParams, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) GetSnapsheetPolicy(ctx context.Context, params *GetSnapsheetPolicyParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetSnapsheetPolicyRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetSnapsheetPolicyRequest generates requests for GetSnapsheetPolicy
func NewGetSnapsheetPolicyRequest(server string, params *GetSnapsheetPolicyParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/snapsheet/policy")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.PolicyNumber != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "policyNumber", runtime.ParamLocationQuery, *params.PolicyNumber); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.DateTimeOfLoss != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "dateTimeOfLoss", runtime.ParamLocationQuery, *params.DateTimeOfLoss); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetSnapsheetPolicyWithResponse request
	GetSnapsheetPolicyWithResponse(ctx context.Context, params *GetSnapsheetPolicyParams, reqEditors ...RequestEditorFn) (*GetSnapsheetPolicyResponse, error)
}

type GetSnapsheetPolicyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetPolicyResponse
}

// Status returns HTTPResponse.Status
func (r GetSnapsheetPolicyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetSnapsheetPolicyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetSnapsheetPolicyWithResponse request returning *GetSnapsheetPolicyResponse
func (c *ClientWithResponses) GetSnapsheetPolicyWithResponse(ctx context.Context, params *GetSnapsheetPolicyParams, reqEditors ...RequestEditorFn) (*GetSnapsheetPolicyResponse, error) {
	rsp, err := c.GetSnapsheetPolicy(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetSnapsheetPolicyResponse(rsp)
}

// ParseGetSnapsheetPolicyResponse parses an HTTP response from a GetSnapsheetPolicyWithResponse call
func ParseGetSnapsheetPolicyResponse(rsp *http.Response) (*GetSnapsheetPolicyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetSnapsheetPolicyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetPolicyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (GET /snapsheet/policy)
	GetSnapsheetPolicy(ctx echo.Context, params GetSnapsheetPolicyParams) error
}

// ServerInterfaceWrapper converts echo contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler ServerInterface
}

// GetSnapsheetPolicy converts echo context to params.
func (w *ServerInterfaceWrapper) GetSnapsheetPolicy(ctx echo.Context) error {
	var err error

	ctx.Set(SessionIdAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetSnapsheetPolicyParams
	// ------------- Optional query parameter "policyNumber" -------------

	err = runtime.BindQueryParameter("form", true, false, "policyNumber", ctx.QueryParams(), &params.PolicyNumber)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter policyNumber: %s", err))
	}

	// ------------- Optional query parameter "dateTimeOfLoss" -------------

	err = runtime.BindQueryParameter("form", true, false, "dateTimeOfLoss", ctx.QueryParams(), &params.DateTimeOfLoss)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Invalid format for parameter dateTimeOfLoss: %s", err))
	}

	// Invoke the callback with all the unmarshaled arguments
	err = w.Handler.GetSnapsheetPolicy(ctx, params)
	return err
}

// This is a simple interface which specifies echo.Route addition functions which
// are present on both echo.Echo and echo.Group, since we want to allow using
// either of them for path registration
type EchoRouter interface {
	CONNECT(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	DELETE(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	GET(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	HEAD(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	OPTIONS(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	PATCH(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	POST(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	PUT(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
	TRACE(path string, h echo.HandlerFunc, m ...echo.MiddlewareFunc) *echo.Route
}

// RegisterHandlers adds each server route to the EchoRouter.
func RegisterHandlers(router EchoRouter, si ServerInterface) {
	RegisterHandlersWithBaseURL(router, si, "")
}

// Registers handlers, and prepends BaseURL to the paths, so that the paths
// can be served under a prefix.
func RegisterHandlersWithBaseURL(router EchoRouter, si ServerInterface, baseURL string) {

	wrapper := ServerInterfaceWrapper{
		Handler: si,
	}

	router.GET(baseURL+"/snapsheet/policy", wrapper.GetSnapsheetPolicy)

}
