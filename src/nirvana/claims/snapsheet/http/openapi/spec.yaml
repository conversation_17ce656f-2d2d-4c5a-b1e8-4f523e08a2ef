openapi: 3.0.0
info:
  version: 1.0.0
  title: Nirvana Application API
  description: Nirvana Application APIs

servers:
  - url: https://api.prod.nirvanatech.com/

tags:
  - name: "external"
    description: "Snapsheet CMS integration"

security:
  - sessionIdAuth: [ ]


paths:

  ###############################################################################
  ############################### Policy Details ################################
  ###############################################################################

  /snapsheet/policy:
    get:
      tags:
        - "Policy Details"
      description: Get policy details given a policy number or datetime of loss
      parameters:
        - in: query
          name: policyNumber
          schema:
            type: string
        - in: query
          name: dateTimeOfLoss
          schema:
            type: string
            format: date-time
      responses:
        200:
          description: Succesfully fetched policy details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPolicyResponse'

components:
  securitySchemes:
    sessionIdAuth:
      type: apiKey
      in: header
      name: JSESSIONID

  schemas:
    GetPolicyResponse:
      type: object
      required:
        - effectiveAt
        - expirationAt
        - inceptionAt
        - policyNumber
        - risks
        - coverages
      properties:
        cancelledAt:
          type: string
          format: date-time
        cancelledReason:
          type: string
        effectiveAt:
          type: string
          format: date-time
        expirationAt:
          type: string
          format: date-time
        inceptionAt:
          type: string
          format: date-time
        policyNumber:
          type: string
        policyType:
          type: string
          enum:
            - AUTO
            - AUTO_PROPERTY
            - BUSINESS_OWNERS_POLICY
            - COMMERCIAL_AUTO
            - COMMERCIAL_PACKAGE
            - COMMERCIAL_PACKAGE_AUTO
            - COMMERCIAL_PROPERTY
            - FARM_OWNERS
            - GENERAL_LIABILITY
            - HOME
            - INLAND_MARINE
            - WORKERS_COMPENSATION
            - PROPERTY
            - PROFESSIONAL_LIABILITY
        status:
          type: string
        version:
          type: string
        agentInformation:
          $ref: '#/components/schemas/AgentInformation'
        product:
          $ref: '#/components/schemas/Product'
        reservation:
          $ref: '#/components/schemas/Reservation'
        underwriting:
          $ref: '#/components/schemas/Underwriting'
        coverages:
          type: array
          items:
            $ref: '#/components/schemas/Coverage'
        endorsements:
          type: array
          items:
            $ref: '#/components/schemas/Endorsement'
        notes:
          type: array
          items:
            $ref: '#/components/schemas/Note'
        businesses:
          type: array
          items:
            $ref: '#/components/schemas/Business'
        people:
          type: array
          items:
            $ref: '#/components/schemas/Person'
        risks:
          type: array
          items:
            $ref: '#/components/schemas/Risk'
        versions:
          type: array
          items:
            $ref: '#/components/schemas/Version'
        deductibles:
          type: array
          items:
            $ref: '#/components/schemas/Deductible'
    AgentInformation:
      type: object
      required:
        - agencyName
        - agencyType
      properties:
        agencyCode:
          type: string
        agencyName:
          type: string
        agencyAddress:
          $ref: '#/components/schemas/Address'
        agencyContactMethods:
          type: array
          items:
            $ref: '#/components/schemas/ContactMethod'
        agencyType:
          type: string
          enum:
          - broker
          - direct
          title: Agency Type
        branchCode:
          type: string
        brokerSegmentation:
          type: string
        parentAgencyCode:
          type: string
        parentBranchCode:
          type: string
        parentName:
          type: string
    Product:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
    Reservation:
      type: object
      properties:
        reservationNumber:
          type: string
        reservationStartDatetime:
          type: string
          format: date-time
        reservationEndDatetime:
          type: string
          format: date-time
    Underwriting:
      type: object
      properties:
        account:
          type: string
        contact:
          type: string
        team:
          type: string
    Coverage:
      type: object
      required:
        - name
        - coverageCode
      properties:
        name:
          type: string
        optional:
          type: boolean
        coverageCode:
          type: string
        externalCoverageDetails:
          type: string
        limits:
          type: array
          items:
            $ref: '#/components/schemas/Limit'
    Endorsement:
      type: object
      properties:
        clause:
          type: string
        clauseType:
          type: string
        description:
          type: string
        effectiveAt:
          type: string
          format: date-time
        endorsementNumber:
          type: string
        expirationDate:
          type: string
          format: date-time
        name:
          type: string
        terminationAt:
          type: string
          format: date-time
    Note:
      type: object
      required:
        - createdAt
        - userName
        - details
      properties:
        createdAt:
          type: string
          format: date-time
        userName:
          type: string
        details:
          type: string
    Business:
      type: object
      properties:
        role:
          type: string
          enum:
            - policyholder
            - named_insured
            - additional_insured
            - mortgagee
            - lienholder
            - loss_payee
            - trustee
            - other
        referenceLoanNumber:
          type: string
        name:
          type: string
        occupation:
          type: string
        providerSubjectId:
          type: string
        taxId:
          type: string
        address:
          $ref: '#/components/schemas/Address'
        contactMethods:
          type: array
          items:
            $ref: '#/components/schemas/ContactMethod'
        interests:
          type: array
          items:
            $ref: '#/components/schemas/Interest'
    Person:
      type: object
      properties:
        prefix:
          type: string
        firstName:
          type: string
        middleName:
          type: string
        lastName:
          type: string
        suffix:
          type: string
        gender:
          type: string
        ssn:
          type: string
        occupation:
          type: string
        dateOfBirth:
          type: string
          format: date
        role:
          type: string
        referenceLoanNumber:
          type: string
        providerSubjectId:
          type: string
        address:
          $ref: '#/components/schemas/Address'
        contactMethods:
          type: array
          items:
            $ref: '#/components/schemas/ContactMethod'
        interests:
          type: array
          items:
            $ref: '#/components/schemas/Interest'
    Risk:
      type: object
      required:
        - externalLocationIdentifier
        - externalRiskIdentifier
        - coverages
      properties:
        id:
          type: string
        code:
          type: string
        codeDescription:
          type: string
        externalLocationIdentifier:
          type: string
        externalRiskIdentifier:
          type: string
        type:
          type: string
        address:
          $ref: '#/components/schemas/Address'
        vehicle:
          $ref: '#/components/schemas/Vehicle'
        property:
          $ref: '#/components/schemas/Property'
        coverages:
          type: array
          items:
            $ref: '#/components/schemas/Coverage'
        endorsements:
          type: array
          items:
            $ref: '#/components/schemas/Endorsement'
        vehicleLocations:
          type: array
          items:
            $ref: '#/components/schemas/VehicleLocation'
        drivers:
          type: array
          items:
            $ref: '#/components/schemas/Driver'
    Version:
      type: object
      properties:
        effectiveAt:
          type: string
          format: date-time
        expirationAt:
          type: string
          format: date-time
        providerTypeDescription:
          type: string
        providerVersionNumber:
          type: string
    Deductible:
      type: object
      properties:
        deductibleType:
          type: string
        amount:
          type: integer
          format: int32
        percent:
          type: integer
          format: int32
    Address:
      type: object
      properties:
        address1:
          type: string
        address2:
          type: string
        city:
          type: string
        postalCode:
          type: string
        region:
          type: string
        country:
          type: string
    ContactMethod:
      type: object
      required:
        - country
      properties:
        country:
          type: string
        countryCode:
          type: string
        preferredMethod:
          type: boolean
        type:
          type: string
        value:
          type: string
    Limit:
      type: object
      properties:
        amount:
          type: integer
          format: int32
        coverageLimitCode:
          type: string
        coverageLimitType:
          type: string
        deductible:
          type: integer
          format: int32
        excess:
          type: integer
          format: int32
        name:
          type: string
    Interest:
      type: object
      properties:
        interestType:
          type: string
        interestId:
          type: string
    Vehicle:
      type: object
      properties:
        affiliation:
          type: string
        certDriving:
          type: string
        certUse:
          type: string
        classOfUse:
          type: string
        make:
          type: string
        model:
          type: string
        registration:
          type: string
        vinNumber:
          type: string
        temporary:
          type: boolean
        temporaryPremium:
          type: integer
          format: int32
        value:
          type: integer
          format: int32
        year:
          type: integer
          format: int32
    Property:
      type: object
      properties:
        model:
          type: string
        name:
          type: string
        propertyType:
          type: string
        propertyLocation:
          type: string
        purchaseYear:
          type: integer
          format: int32
        replacementCost:
          type: number
          format: float
        serialNumber:
          type: string
        buildingDetail:
          $ref: '#/components/schemas/BuildingDetail'
    VehicleLocation:
      type: object
      properties:
        locationType:
          type: string
        address:
          $ref: '#/components/schemas/Address'
    Driver:
      type: object
      properties:
        prefix:
          type: string
        firstName:
          type: string
        middleName:
          type: string
        lastName:
          type: string
        suffix:
          type: string
        gender:
          type: string
        occupation:
          type: string
        dateOfBirth:
          type: string
          format: date
        coverType:
          type: string
        driverType:
          type: string
        licenseIssuingCountry:
          type: string
        licenseNumber:
          type: string
        licenseType:
          type: string
        mainDriver:
          type: boolean
        penaltyPoints:
          type: integer
          format: int32
    BuildingDetail:
      type: object
      properties:
        buildingType:
          type: string
        constructionType:
          type: string
        numberOfFloors:
          type: integer
          format: int32
        size:
          type: integer
          format: int32
        yearBuilt:
          type: integer
          format: int32
        hoFormNumber:
          type: string
        usageType:
          type: string
        numberOfMonthsOccupied:
          type: integer
          format: int32
        numberRooms:
          type: integer
          format: int32
        bldgCodeGrade:
          type: string
        fireProtectionClass:
          type: string
        distanceToTidalWater:
          type: integer
          format: int32
        distanceToTidalWaterUnit:
          type: string
        siding:
          type: string
        roofMaterial:
          type: string
        wiring:
          type: string
        sprinkler:
          type: string
        swimmingPool:
          type: string
