package client

import (
	"context"
	"fmt"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/claims/snapsheet/client/policy_type"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	policy_client "nirvanatech.com/nirvana/policy"
	"nirvanatech.com/nirvana/policy/enums"
)

// the data that Snapsheet requires is documented here: https://docs.snapsheetclaims.com/development/reference/get_api-v1-policies
type PolicyDetails struct {
	// Date Time that represents the effective date for the policy term based on the date of loss.
	EffectiveAt time.Time
	// Date Time representing the expiration date of the policy term based on the date of loss.
	ExpirationAt time.Time
	// Date Time representing the inception of the policy (first day the policy was effective in the
	// event the policy number has been renewed).
	InceptionAt  time.Time
	PolicyNumber string
	PolicyHolder string
	PolicyType   policy_type.PolicyType
	Status       enums.PolicyState
	// Version Identifier in Date value format YYYY-MM-DD for the policy information represented.
	Version           string
	CancelledTime     *time.Time
	CancelledReason   *string
	Agency            Agency
	Underwriting      Underwriting
	TerminalLocations []Location
	Vehicles          []Vehicle
	Coverages         []Coverage
	Endorsements      []Endorsement
}

type Agency struct {
	Name    string
	Address *agency.Address
}

type Underwriting struct {
	// The name of the Underwriter for the policy
	Contact string
	// Whether the Underwriting was Fleet or Non Fleet
	Team UnderwritingTeam
}

type Location struct {
	AddressLineOne string
	AddressLineTwo *string
	ZipCode        string
	State          us_states.USState
}

type Vehicle struct {
	Make  *string
	Model *string
	Value *float32
	Vin   string
	Year  *int32
}

type CancellationDetails struct {
	FilingDate time.Time
	Reason     string
}

type Limit struct {
	Amount     int
	Deductible int
}

type Coverage struct {
	Name  string
	Code  string
	Limit Limit
}

type Endorsement struct {
	Filename      string
	FileHandleId  *uuid.UUID
	DateEffective time.Time
	ChangeType    string
}

// ProgramResolver serves all the program-specific behavior
type ProgramResolver interface {
	GetUnderwritingDetails(ctx context.Context, subId uuid.UUID) (*Underwriting, error)
	GetVehicles(ctx context.Context, p policy.Policy) ([]Vehicle, error)
	GetCancellationDetails(ctx context.Context, p policy.Policy) (bool, *CancellationDetails, error)
	GetCoverages(ctx context.Context, subId uuid.UUID) ([]Coverage, error)
	GetEndorsements(ctx context.Context, p policy.Policy) ([]Endorsement, error)
}

// GetPolicyDetails returns all the details that Snapsheet requires for a policy.
func (c *Client) GetPolicyDetails(ctx context.Context, policyNumber string) (*PolicyDetails, error) {
	p, err := c.deps.PolicyClient.GetLatestPolicy(ctx, policyNumber, false)
	if err != nil {
		return nil, err
	}

	policyType, err := policy_type.New(*p.PolicyNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get policy type from policy number %s", policyNumber)
	}

	// we need the date at which this version of the policy came live. For the first version, it's
	// the effective date. For subsequent versions, it's the date the specific policy entity was created.
	policyVersionDate := p.EffectiveDate
	if p.Version > 0 {
		policyVersionDate = p.CreatedAt
	}

	ag, err := c.deps.AgencyWrapper.FetchAgency(ctx, p.AgencyID)
	if err != nil {
		return nil, err
	}

	inceptionAt, err := c.getInceptionAt(ctx, *p)
	if err != nil {
		return nil, err
	}

	pd := PolicyDetails{
		EffectiveAt:  p.EffectiveDate,
		ExpirationAt: p.EffectiveDateTo,
		InceptionAt:  *inceptionAt,
		PolicyNumber: policyNumber,
		PolicyType:   *policyType,
		PolicyHolder: fmt.Sprintf("%s - %d", p.CompanyInfo.Name, p.CompanyInfo.DOTNumber),
		Status:       p.State,
		Version:      policyVersionDate.Format(time.DateOnly),
		Agency: Agency{
			Name:    ag.Name,
			Address: ag.Address,
		},
		TerminalLocations: getLocations(p.CompanyInfo),
	}

	resolver, err := c.getProgramResolver(p.ProgramType)
	if err != nil {
		return nil, err
	}

	uwDetails, err := resolver.GetUnderwritingDetails(ctx, p.SubmissionId)
	if err != nil {
		return nil, err
	}
	pd.Underwriting = *uwDetails

	vehicles, err := resolver.GetVehicles(ctx, *p)
	if err != nil {
		return nil, err
	}
	pd.Vehicles = vehicles

	cancelled, cancellation, err := resolver.GetCancellationDetails(ctx, *p)
	if err != nil {
		return nil, err
	}
	if cancelled {
		if cancellation == nil {
			return nil, errors.New("cancellation details are missing for a cancelled policy")
		}
		pd.CancelledTime = &cancellation.FilingDate
		pd.CancelledReason = &cancellation.Reason
	}

	coverages, err := resolver.GetCoverages(ctx, p.SubmissionId)
	if err != nil {
		return nil, err
	}
	pd.Coverages = coverages

	endorsements, err := resolver.GetEndorsements(ctx, *p)
	if err != nil {
		return nil, err
	}

	pd.Endorsements = endorsements

	return &pd, nil
}

func (c *Client) getProgramResolver(
	programType policy_enums.ProgramType,
) (ProgramResolver, error) {
	switch programType {
	case policy_enums.ProgramTypeFleet:
		return &FleetResolver{deps: c.deps}, nil
	case policy_enums.ProgramTypeNonFleetAdmitted:
		return &NonFleetResolver{deps: c.deps}, nil
	default:
		return nil, errors.Newf("programType %s not supported", programType)
	}
}

func getLocations(companyInfo *application.CompanyInfo) []Location {
	if companyInfo == nil || companyInfo.TerminalLocations == nil {
		return nil
	}

	return slice_utils.Map(
		*companyInfo.TerminalLocations,
		func(loc application.TerminalLocation) Location {
			return Location{
				AddressLineOne: loc.AddressLineOne,
				AddressLineTwo: loc.AddressLineTwo,
				ZipCode:        fmt.Sprintf("%d", loc.ZipCode),
				State:          loc.UsState,
			}
		},
	)
}

// getInceptionAt finds the effective date for the first policy for the account denoted by the policy.
func (c *Client) getInceptionAt(ctx context.Context, p policy.Policy) (*time.Time, error) {
	inceptionAt := p.EffectiveDate
	if p.CompanyInfo == nil {
		return &inceptionAt, nil
	}

	allPoliciesForDOT, err := c.deps.PolicyClient.GetPolicies(
		ctx,
		policy_client.GetPoliciesFilter{
			DotNumber:        &p.CompanyInfo.DOTNumber,
			SkipTestAgencies: true,
		},
	)
	if err != nil {
		return nil, err
	}

	for _, pol := range allPoliciesForDOT {
		if pol.EffectiveDate.Before(inceptionAt) {
			inceptionAt = pol.EffectiveDate
		}
	}

	return &inceptionAt, nil
}
