package client

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/claims/fnol_intake_emails/db"
)

type Client struct {
	deps deps
}

func newClient(d deps) *Client {
	return &Client{d}
}

type CreateFnolIntakeEmailArgs struct {
	MessageId string
	FnolId    uuid.UUID
	SentAt    time.Time
}

func (c *Client) CreateFnolIntakeEmail(
	ctx context.Context, args CreateFnolIntakeEmailArgs,
) (*db.FnolIntakeEmail, error) {
	fnolIntakeEmail := db.NewFnolIntakeEmail(args.MessageId, args.FnolId, args.SentAt)
	if err := c.deps.Wrapper.InsertFnolIntakeEmail(ctx, *fnolIntakeEmail); err != nil {
		return nil, errors.Wrap(err, "failed to insert FNOL intake email")
	}

	return fnolIntakeEmail, nil
}
