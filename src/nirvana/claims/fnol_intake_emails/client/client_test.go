package client_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/fnol_intake_emails/client"
	"nirvanatech.com/nirvana/claims/fnol_intake_emails/db"
	fnol_db "nirvanatech.com/nirvana/claims/fnols/db"
	"nirvanatech.com/nirvana/infra/fx/testloader"
)

func Test_CreateFnolIntakeEmail(t *testing.T) {
	var env struct {
		fx.In

		Client      *client.Client
		Wrapper     *db.DataWrapper
		FnolWrapper *fnol_db.DataWrapper
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	fnol, err := fnol_db.NewClaimFnolBuilder(enums.ClaimsProviderNars).
		WithDefaultMockData().
		Build()
	require.NoError(t, err)
	require.NoError(t, env.FnolWrapper.UpsertFnol(context.Background(), *fnol))

	ctx := context.Background()

	testCases := []struct {
		name    string
		args    client.CreateFnolIntakeEmailArgs
		wantErr bool
	}{
		{
			name: "FnolIntakeEmail created successfully",
			args: client.CreateFnolIntakeEmailArgs{
				MessageId: "message-id-1",
				FnolId:    fnol.Id,
				SentAt:    time.Date(2023, time.October, 12, 10, 0, 0, 0, time.UTC),
			},
			wantErr: false,
		},
		{
			name: "Empty message id",
			args: client.CreateFnolIntakeEmailArgs{
				FnolId: fnol.Id,
				SentAt: time.Date(2022, time.August, 2, 12, 20, 0, 0, time.UTC),
			},
			wantErr: true,
		},
		{
			name: "Empty sent at",
			args: client.CreateFnolIntakeEmailArgs{
				MessageId: "message-id-2",
				FnolId:    fnol.Id,
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			fnolIntakeEmail, err := env.Client.CreateFnolIntakeEmail(ctx, tc.args)
			if tc.wantErr {
				require.Error(t, err)
				require.Nil(t, fnolIntakeEmail)
				return
			}
			require.NoError(t, err)

			dbFnolIntakeEmails, err := env.Wrapper.GetFnolIntakeEmails(ctx, db.FnolIntakeEmailMessageIdIs(tc.args.MessageId))
			require.NoError(t, err)

			require.Len(t, dbFnolIntakeEmails, 1)
			dbFnolIntakeEmail := dbFnolIntakeEmails[0]

			// Check values were assigned correctly
			assert.NotNil(t, dbFnolIntakeEmail.Id)
			assert.Equal(t, tc.args.MessageId, dbFnolIntakeEmail.MessageId)
			assert.Equal(t, tc.args.FnolId, dbFnolIntakeEmail.FnolId)
			assert.Equal(t, tc.args.SentAt.UTC().Round(time.Millisecond), dbFnolIntakeEmail.SentAt.UTC().Round(time.Millisecond))
		})
	}
}
