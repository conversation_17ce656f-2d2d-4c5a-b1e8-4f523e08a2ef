load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "client.go",
        "deps.go",
        "fx.go",
    ],
    importpath = "nirvanatech.com/nirvana/claims/fnol_intake_emails/client",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/claims/fnol_intake_emails/db",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "client_test",
    srcs = ["client_test.go"],
    deps = [
        ":client",
        "//nirvana/claims/enums",
        "//nirvana/claims/fnol_intake_emails/db",
        "//nirvana/claims/fnols/db",
        "//nirvana/infra/fx/testloader",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
