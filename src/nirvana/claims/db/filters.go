package db

import (
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	claim_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_models/nars"
	"nirvanatech.com/nirvana/db-api/db_models/snapsheet"
)

type Filter interface {
	QueryMods(source claim_enums.ClaimsProvider) []qm.QueryMod
}

type qmFilters struct {
	snapsheetQueryMods []qm.QueryMod
	narsQueryMods      []qm.QueryMod
}

func (q *qmFilters) QueryMods(source claim_enums.ClaimsProvider) []qm.QueryMod {
	switch source {
	case claim_enums.ClaimsProviderSnapsheet:
		return q.snapsheetQueryMods
	case claim_enums.ClaimsProviderNars:
		return q.narsQueryMods
	default:
		return nil
	}
}

// OrderByCreatedAtDesc applies the "ORDER BY DESC" clause
func OrderByCreatedAtDesc() Filter {
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			qm.OrderBy(snapsheet.ClaimColumns.CreatedAt + " DESC"),
		},
		narsQueryMods: []qm.QueryMod{
			qm.OrderBy(nars.ClaimColumns.CreatedAt + " DESC"),
		},
	}
}

// OrderByReportedAt applies the "ORDER BY DESC" clause
func OrderByReportedAtDesc() Filter {
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			qm.OrderBy(snapsheet.ClaimColumns.ReportedAt + " DESC"),
		},
		narsQueryMods: []qm.QueryMod{
			qm.OrderBy(nars.ClaimColumns.ReportedAt + " DESC"),
		},
	}
}

// ClaimIdIs filters claims by their ID
func ClaimIdIs(claimId string) Filter {
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			snapsheet.ClaimWhere.ID.EQ(claimId),
		},
		narsQueryMods: []qm.QueryMod{
			nars.ClaimWhere.ID.EQ(claimId),
		},
	}
}

// ClaimExternalIdIs filters claims by their external ID
func ClaimExternalIdIs(externalId string) Filter {
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			snapsheet.ClaimWhere.SnapsheetID.EQ(externalId),
		},
		narsQueryMods: []qm.QueryMod{
			nars.ClaimWhere.ExternalID.EQ(externalId),
		},
	}
}

// ClaimStatusIsNot filters claims by excluding a specific status
func ClaimStatusIsNot(status claim_enums.ClaimStatus) Filter {
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			snapsheet.ClaimWhere.Status.NEQ(status.String()),
		},
		narsQueryMods: []qm.QueryMod{
			nars.ClaimWhere.Status.NEQ(status.String()),
		},
	}
}

// ClaimStatusIn filters claims by including multiple statuses
func ClaimStatusIn(statuses ...claim_enums.ClaimStatus) Filter {
	parsedStatuses := slice_utils.Map(statuses, func(s claim_enums.ClaimStatus) string {
		return s.String()
	})
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			snapsheet.ClaimWhere.Status.IN(parsedStatuses),
		},
		narsQueryMods: []qm.QueryMod{
			nars.ClaimWhere.Status.IN(parsedStatuses),
		},
	}
}

// PolicyNumberIn applies the "WHERE claims.policy_number in ()" clause
func PolicyNumberIn(policyNumbers []string) Filter {
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			snapsheet.ClaimWhere.PolicyNumber.IN(policyNumbers),
		},
		narsQueryMods: []qm.QueryMod{
			nars.ClaimWhere.PolicyNumber.IN(policyNumbers),
		},
	}
}

func StatusClaimOrderByCreatedAtDesc() Filter {
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			qm.OrderBy(snapsheet.ClaimStatusColumns.CreatedAt + " desc"),
		},
		narsQueryMods: []qm.QueryMod{
			qm.OrderBy(nars.StatusColumns.CreatedAt + " desc"),
		},
	}
}

func StatusIs(status claim_enums.ClaimStatus) Filter {
	return &qmFilters{
		snapsheetQueryMods: []qm.QueryMod{
			snapsheet.ClaimStatusWhere.Value.EQ(status.String()),
		},
		narsQueryMods: []qm.QueryMod{
			nars.StatusWhere.Value.EQ(status.String()),
		},
	}
}
