package IL_12_09_08_23

import (
	"encoding/json"

	"nirvanatech.com/nirvana/pdffill/fields"
)

const FormName = "IL_12_09_08_23.pdf"

type Request struct {
	PolicyNumber             fields.StringM            `json:"PolicyNumber"`
	PolicyChangeNumber       fields.StringM            `json:"PolicyChangeNumber"`
	Company                  fields.InsuranceCarrier   `json:"CompanyName"`
	FromMM                   fields.StringS            `json:"FromMM"`
	FromDD                   fields.StringS            `json:"FromDD"`
	FromYYYY                 fields.StringS            `json:"FromYYYY"`
	ToMM                     fields.StringS            `json:"ToMM"`
	ToDD                     fields.StringS            `json:"ToDD"`
	ToYYYY                   fields.StringS            `json:"ToYYYY"`
	NamedInsured             fields.StringXXL          `json:"NamedInsured"`
	AuthorizedRepresentative fields.StringL            `json:"AuthorizedRepresentative"`
	InsuredMailingAddress    fields.StringXXL          `json:"InsuredMailingAddress"`
	CoverageAffected         fields.OptionalStringXXL  `json:"CoverageAffected"`
	Changes                  fields.OptionalStringXXXL `json:"Changes"`
	AdditionalPremium        fields.OptionalStringS    `json:"AdditionalPremium"`
	ReturnPremium            fields.OptionalStringS    `json:"ReturnPremium"`
	Date                     fields.NonZeroDate        `json:"Date"`
}

func (r *Request) MarshalJSON() ([]byte, error) {
	return json.Marshal(*r)
}

func (r Request) InputPath() string {
	return FormName
}
