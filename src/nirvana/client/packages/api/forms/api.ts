/* tslint:disable */
/* eslint-disable */
/**
 * Nirvana Application API
 * Nirvana Application APIs
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { Configuration } from './configuration';
import globalAxios, {
  AxiosPromise,
  AxiosInstance,
  AxiosRequestConfig,
} from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  RequestArgs,
  BaseAPI,
  RequiredError,
} from './base';

/**
 *
 * @export
 * @interface Applicability
 */
export interface Applicability {
  /**
   *
   * @type {Array<ProgramType>}
   * @memberof Applicability
   */
  programType: Array<ProgramType>;
}
/**
 *
 * @export
 * @interface ApplicationIdBoundReasonObject
 */
export interface ApplicationIdBoundReasonObject {
  /**
   *
   * @type {string}
   * @memberof ApplicationIdBoundReasonObject
   */
  reason: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationIdBoundReasonObject
   */
  reasonId: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationIdBoundReasonObject
   */
  primaryWinNote: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationIdBoundReasonObject
   */
  secondaryWinNote: string;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationIdBoundReasonObject
   */
  isActive: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationIdBoundReasonObject
   */
  isRenewal: boolean;
}
/**
 *
 * @export
 * @interface ApplicationIdBoundReasons
 */
export interface ApplicationIdBoundReasons {
  /**
   *
   * @type {Array<ApplicationIdBoundReasonObject>}
   * @memberof ApplicationIdBoundReasons
   */
  reasons?: Array<ApplicationIdBoundReasonObject>;
}
/**
 *
 * @export
 * @interface ApplicationReviewBindReasonObject
 */
export interface ApplicationReviewBindReasonObject {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewBindReasonObject
   */
  primaryReason: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewBindReasonObject
   */
  primaryReasonId: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewBindReasonObject
   */
  secondaryReason: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewBindReasonObject
   */
  secondaryReasonId: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewBindReasonObject
   */
  externalNote: string;
}
/**
 *
 * @export
 * @interface ApplicationReviewPreBindInfo
 */
export interface ApplicationReviewPreBindInfo {
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewPreBindInfo
   */
  commissionRate: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewPreBindInfo
   */
  depositAmount: number;
  /**
   *
   * @type {PaymentMethod}
   * @memberof ApplicationReviewPreBindInfo
   */
  paymentMethod: PaymentMethod;
  /**
   *
   * @type {DepositPaymentMethod}
   * @memberof ApplicationReviewPreBindInfo
   */
  depositPaymentMethod?: DepositPaymentMethod;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewPreBindInfo
   */
  externalNote?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationType {
  ApplicationTypeFleet = 'ApplicationTypeFleet',
  ApplicationTypeNonFleet = 'ApplicationTypeNonFleet',
  ApplicationTypeNonFleetAdmitted = 'ApplicationTypeNonFleetAdmitted',
  ApplicationTypeBusinessAuto = 'ApplicationTypeBusinessAuto',
}

/**
 *
 * @export
 * @interface Config
 */
export interface Config {
  /**
   *
   * @type {string}
   * @memberof Config
   */
  formFieldVarName: string;
  /**
   *
   * @type {ConfigField}
   * @memberof Config
   */
  field: ConfigField;
}
/**
 *
 * @export
 * @interface ConfigField
 */
export interface ConfigField {
  /**
   *
   * @type {ConfigFieldSource}
   * @memberof ConfigField
   */
  source: ConfigFieldSource;
  /**
   *
   * @type {ConfigFieldValidation}
   * @memberof ConfigField
   */
  validation?: ConfigFieldValidation;
}
/**
 *
 * @export
 * @interface ConfigFieldSource
 */
export interface ConfigFieldSource {
  /**
   *
   * @type {string}
   * @memberof ConfigFieldSource
   */
  path: string;
}
/**
 *
 * @export
 * @interface ConfigFieldValidation
 */
export interface ConfigFieldValidation {
  /**
   *
   * @type {boolean}
   * @memberof ConfigFieldValidation
   */
  required: boolean;
}
/**
 *
 * @export
 * @interface CoverageIdsMap
 */
export interface CoverageIdsMap {
  /**
   *
   * @type {CoverageType}
   * @memberof CoverageIdsMap
   */
  coverage: CoverageType;
  /**
   *
   * @type {string}
   * @memberof CoverageIdsMap
   */
  id?: string;
}
/**
 *
 * @export
 * @interface CoveragePackagePair
 */
export interface CoveragePackagePair {
  /**
   *
   * @type {CoverageType}
   * @memberof CoveragePackagePair
   */
  coverageType: CoverageType;
  /**
   *
   * @type {PackageType}
   * @memberof CoveragePackagePair
   */
  packageType: PackageType;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum CoverageType {
  CoverageAutoLiability = 'CoverageAutoLiability',
  CoverageAutoPhysicalDamage = 'CoverageAutoPhysicalDamage',
  CoverageGeneralLiability = 'CoverageGeneralLiability',
  CoverageMotorTruckCargo = 'CoverageMotorTruckCargo',
  CoverageTrailerInterchange = 'CoverageTrailerInterchange',
  CoverageUninsuredMotoristBodilyInjury = 'CoverageUninsuredMotoristBodilyInjury',
  CoverageUnderinsuredMotoristBodilyInjury = 'CoverageUnderinsuredMotoristBodilyInjury',
  CoverageUninsuredMotoristPropertyDamage = 'CoverageUninsuredMotoristPropertyDamage',
  CoverageUnderinsuredMotoristPropertyDamage = 'CoverageUnderinsuredMotoristPropertyDamage',
  CoveragePersonalInjuryProtection = 'CoveragePersonalInjuryProtection',
  CoverageMedicalPayments = 'CoverageMedicalPayments',
  CoverageUmuimBodilyInjury = 'CoverageUMUIMBodilyInjury',
  CoverageUmuimPropertyDamage = 'CoverageUMUIMPropertyDamage',
  CoveragePropertyProtectionInsurance = 'CoveragePropertyProtectionInsurance',
  CoveragePersonalInjuryProtectionBasic = 'CoveragePersonalInjuryProtectionBasic',
  CoveragePersonalInjuryProtectionIncreased = 'CoveragePersonalInjuryProtectionIncreased',
  CoveragePipExcessAttendantCare = 'CoveragePIPExcessAttendantCare',
  CoverageReefer = 'CoverageReefer',
  CoverageReeferWithHumanError = 'CoverageReeferWithHumanError',
  CoverageEnhancedPackageTowingLimit = 'CoverageEnhancedPackageTowingLimit',
  CoverageGuestPersonalInjuryProtection = 'CoverageGuestPersonalInjuryProtection',
  CoverageReeferWithoutHumanError = 'CoverageReeferWithoutHumanError',
  CoverageStopGap = 'CoverageStopGap',
  CoverageBroadenedPollution = 'CoverageBroadenedPollution',
  CoverageBlanketAdditional = 'CoverageBlanketAdditional',
  CoverageUiia = 'CoverageUIIA',
  CoverageBlanketWaiverOfSubrogation = 'CoverageBlanketWaiverOfSubrogation',
  CoverageGlBlanketWaiverOfSubrogation = 'CoverageGLBlanketWaiverOfSubrogation',
  CoverageGlBlanketAdditional = 'CoverageGLBlanketAdditional',
  CoverageMtcBlanketWaiverOfSubrogation = 'CoverageMTCBlanketWaiverOfSubrogation',
  CoverageMtcBlanketAdditional = 'CoverageMTCBlanketAdditional',
  CoverageUmuim = 'CoverageUMUIM',
  CoverageUmbiuimbi = 'CoverageUMBIUIMBI',
  CoverageUm = 'CoverageUM',
  CoverageUim = 'CoverageUIM',
  CoverageTerrorism = 'CoverageTerrorism',
  CoverageDebrisRemoval = 'CoverageDebrisRemoval',
  CoverageNonOwnedTrailer = 'CoverageNonOwnedTrailer',
  CoverageApduiia = 'CoverageAPDUIIA',
  CoverageMtcuiia = 'CoverageMTCUIIA',
  CoverageApdTrailerInterchange = 'CoverageAPDTrailerInterchange',
  CoverageMtcTrailerInterchange = 'CoverageMTCTrailerInterchange',
  CoverageApdNonOwnedTrailer = 'CoverageAPDNonOwnedTrailer',
  CoverageMtcNonOwnedTrailer = 'CoverageMTCNonOwnedTrailer',
  CoverageUnattendedTruck = 'CoverageUnattendedTruck',
  CoverageEarnedFreight = 'CoverageEarnedFreight',
  CoverageRentalReimbursement = 'CoverageRentalReimbursement',
  CoverageTowingLaborAndStorage = 'CoverageTowingLaborAndStorage',
  CoverageHiredAuto = 'CoverageHiredAuto',
  CoverageCargoAtScheduledTerminals = 'CoverageCargoAtScheduledTerminals',
  CoverageCargoTrailerInterchange = 'CoverageCargoTrailerInterchange',
  CoverageLossMitigationExpenses = 'CoverageLossMitigationExpenses',
  CoverageMiscellaneousEquipment = 'CoverageMiscellaneousEquipment',
  CoveragePollutantCleanupAndRemoval = 'CoveragePollutantCleanupAndRemoval',
  CoverageBlanketAdditionalPnc = 'CoverageBlanketAdditionalPNC',
  CoverageNonOwnedAuto = 'CoverageNonOwnedAuto',
  CoverageWorkLossBenefits = 'CoverageWorkLossBenefits',
  CoverageFuneralExpenseBenefits = 'CoverageFuneralExpenseBenefits',
  CoverageAccidentalDeathBenefits = 'CoverageAccidentalDeathBenefits',
  CoverageExtraordinaryMedicalBenefits = 'CoverageExtraordinaryMedicalBenefits',
  CoverageMedicalExpenseBenefits = 'CoverageMedicalExpenseBenefits',
  CoverageHiredAutoLiab = 'CoverageHiredAutoLiab',
  CoverageHiredAutoPd = 'CoverageHiredAutoPD',
}

/**
 *
 * @export
 * @interface CoveragesToScheduleTypes
 */
export interface CoveragesToScheduleTypes {
  /**
   *
   * @type {CoverageType}
   * @memberof CoveragesToScheduleTypes
   */
  coverageType: CoverageType;
  /**
   *
   * @type {Array<ScheduleTypeToForms>}
   * @memberof CoveragesToScheduleTypes
   */
  scheduleTypes: Array<ScheduleTypeToForms>;
}
/**
 *
 * @export
 * @interface CreateFormScheduleRequest
 */
export interface CreateFormScheduleRequest {
  /**
   *
   * @type {string}
   * @memberof CreateFormScheduleRequest
   */
  fullFormCode: string;
  /**
   *
   * @type {string}
   * @memberof CreateFormScheduleRequest
   */
  formCode: string;
  /**
   *
   * @type {string}
   * @memberof CreateFormScheduleRequest
   */
  formName: string;
  /**
   *
   * @type {string}
   * @memberof CreateFormScheduleRequest
   */
  description?: string;
  /**
   *
   * @type {OrderCategory}
   * @memberof CreateFormScheduleRequest
   */
  orderCategory: OrderCategory;
  /**
   *
   * @type {Array<FormApplicabilityRule>}
   * @memberof CreateFormScheduleRequest
   */
  applicabilityRules: Array<FormApplicabilityRule>;
  /**
   *
   * @type {object}
   * @memberof CreateFormScheduleRequest
   */
  metadata?: object;
  /**
   *
   * @type {FormTemplateType}
   * @memberof CreateFormScheduleRequest
   */
  formTemplateType: FormTemplateType;
  /**
   *
   * @type {FormScheduleState}
   * @memberof CreateFormScheduleRequest
   */
  status?: FormScheduleState;
  /**
   *
   * @type {FillTemplate}
   * @memberof CreateFormScheduleRequest
   */
  fillTemplate?: FillTemplate;
}
/**
 *
 * @export
 * @interface CreateFormScheduleResponse
 */
export interface CreateFormScheduleResponse {
  /**
   *
   * @type {string}
   * @memberof CreateFormScheduleResponse
   */
  fullFormCode?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum DepositPaymentMethod {
  DepositPaymentInvoiceCash = 'DepositPaymentInvoiceCash',
  DepositPaymentLetterOfCredit = 'DepositPaymentLetterOfCredit',
}

/**
 *
 * @export
 * @interface DownloadFileLinkResponse
 */
export interface DownloadFileLinkResponse {
  /**
   *
   * @type {string}
   * @memberof DownloadFileLinkResponse
   */
  link: string;
}
/**
 *
 * @export
 * @interface ErrorMessage
 */
export interface ErrorMessage {
  /**
   *
   * @type {string}
   * @memberof ErrorMessage
   */
  message: string;
  /**
   *
   * @type {number}
   * @memberof ErrorMessage
   */
  code: number;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum FileDestinationGroup {
  FileDestinationGroupQuoting = 'FileDestinationGroupQuoting',
  FileDestinationGroupUnderwriting = 'FileDestinationGroupUnderwriting',
  FileDestinationGroupForms = 'FileDestinationGroupForms',
  FileDestinationGroupClaims = 'FileDestinationGroupClaims',
}

/**
 *
 * @export
 * @interface FileHandle
 */
export interface FileHandle {
  /**
   *
   * @type {string}
   * @memberof FileHandle
   */
  handle: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum FileType {
  FileTypeEquipmentList = 'FileTypeEquipmentList',
  FileTypeDriversList = 'FileTypeDriversList',
  FileTypeLossRun = 'FileTypeLossRun',
  FileTypeUwDocument = 'FileTypeUwDocument',
  FileTypePdfForms = 'FileTypePDFForms',
  FileTypeIftaFile = 'FileTypeIFTAFile',
  FileTypeClaimDocument = 'FileTypeClaimDocument',
  FileTypeAgentAdditionalFiles = 'FileTypeAgentAdditionalFiles',
}

/**
 *
 * @export
 * @interface FillTemplate
 */
export interface FillTemplate {
  /**
   *
   * @type {Array<Template>}
   * @memberof FillTemplate
   */
  templates?: Array<Template>;
}
/**
 *
 * @export
 * @interface FormApplicabilityAdditionalRule
 */
export interface FormApplicabilityAdditionalRule {
  /**
   * The when condition for the rule. This field can either be a plain string value or a condition object. More Details here - https://github.com/hyperjumptech/grule-rule-engine/blob/master/docs/en/GRL_JSON_en.md
   * @type {object}
   * @memberof FormApplicabilityAdditionalRule
   */
  when: object;
  /**
   * An array of actions for the rule. Each element can be a plain string or an action object. Only needed for special cases, default values are set internally
   * @type {Array<object>}
   * @memberof FormApplicabilityAdditionalRule
   */
  then?: Array<object>;
}
/**
 *
 * @export
 * @interface FormApplicabilityCoreRule
 */
export interface FormApplicabilityCoreRule {
  /**
   *
   * @type {Array<FormType>}
   * @memberof FormApplicabilityCoreRule
   */
  formCompilationTypes?: Array<FormType>;
  /**
   *
   * @type {Array<CoveragePackagePair>}
   * @memberof FormApplicabilityCoreRule
   */
  applicableCoveragePackages?: Array<CoveragePackagePair>;
  /**
   *
   * @type {Array<USState>}
   * @memberof FormApplicabilityCoreRule
   */
  includedStates?: Array<USState>;
  /**
   *
   * @type {Array<USState>}
   * @memberof FormApplicabilityCoreRule
   */
  excludedStates?: Array<USState>;
  /**
   *
   * @type {Array<string>}
   * @memberof FormApplicabilityCoreRule
   */
  applicableInsuranceCarriers?: Array<string>;
  /**
   *
   * @type {HierarchicalStateDateRule}
   * @memberof FormApplicabilityCoreRule
   */
  expirationDate?: HierarchicalStateDateRule;
  /**
   *
   * @type {HierarchicalStateDateRule}
   * @memberof FormApplicabilityCoreRule
   */
  effectiveDate?: HierarchicalStateDateRule;
  /**
   *
   * @type {Array<ProgramType>}
   * @memberof FormApplicabilityCoreRule
   */
  programTypes?: Array<ProgramType>;
}
/**
 *
 * @export
 * @interface FormApplicabilityRule
 */
export interface FormApplicabilityRule {
  /**
   *
   * @type {FormApplicabilityCoreRule}
   * @memberof FormApplicabilityRule
   */
  coreRule: FormApplicabilityCoreRule;
  /**
   *
   * @type {FormApplicabilityAdditionalRule}
   * @memberof FormApplicabilityRule
   */
  additionalRule?: FormApplicabilityAdditionalRule;
  /**
   * The unique incremental identifier for the rule within a form schedule
   * @type {string}
   * @memberof FormApplicabilityRule
   */
  ruleId?: string;
  /**
   *
   * @type {Array<ScheduleType>}
   * @memberof FormApplicabilityRule
   */
  scheduleType?: Array<ScheduleType>;
}
/**
 *
 * @export
 * @interface FormCompilation
 */
export interface FormCompilation {
  /**
   *
   * @type {string}
   * @memberof FormCompilation
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof FormCompilation
   */
  companyName?: string;
  /**
   *
   * @type {number}
   * @memberof FormCompilation
   */
  dotNumber?: number;
  /**
   *
   * @type {Array<CoveragesToScheduleTypes>}
   * @memberof FormCompilation
   */
  recommendedForms: Array<CoveragesToScheduleTypes>;
  /**
   *
   * @type {Array<CoveragesToScheduleTypes>}
   * @memberof FormCompilation
   */
  additionalForms: Array<CoveragesToScheduleTypes>;
  /**
   *
   * @type {string}
   * @memberof FormCompilation
   */
  createdAt: string;
  /**
   *
   * @type {string}
   * @memberof FormCompilation
   */
  modifiedAt: string;
  /**
   *
   * @type {FormType}
   * @memberof FormCompilation
   */
  type: FormType;
  /**
   *
   * @type {CoverageType}
   * @memberof FormCompilation
   */
  coverageType?: CoverageType;
  /**
   *
   * @type {FormCompilationState}
   * @memberof FormCompilation
   */
  state: FormCompilationState;
  /**
   *
   * @type {FormCompilationMetadata}
   * @memberof FormCompilation
   */
  metadata?: FormCompilationMetadata;
}
/**
 *
 * @export
 * @interface FormCompilationMetadata
 */
export interface FormCompilationMetadata {
  /**
   *
   * @type {string}
   * @memberof FormCompilationMetadata
   */
  ApplicationId?: string;
  /**
   *
   * @type {string}
   * @memberof FormCompilationMetadata
   */
  SubmissionId?: string;
  /**
   *
   * @type {CoverageType}
   * @memberof FormCompilationMetadata
   */
  CoverageType?: CoverageType;
  /**
   *
   * @type {Array<CoverageIdsMap>}
   * @memberof FormCompilationMetadata
   */
  CoverageIdsMap?: Array<CoverageIdsMap>;
  /**
   *
   * @type {string}
   * @memberof FormCompilationMetadata
   */
  ReleasedDate?: string;
  /**
   *
   * @type {string}
   * @memberof FormCompilationMetadata
   */
  ZipHandleId?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum FormCompilationState {
  FormCompilationStateInitialized = 'FormCompilationStateInitialized',
  FormCompilationStateFilled = 'FormCompilationStateFilled',
  FormCompilationStateFrozen = 'FormCompilationStateFrozen',
  FormCompilationStateStale = 'FormCompilationStateStale',
  FormCompilationStatePanic = 'FormCompilationStatePanic',
  FormCompilationStateReleased = 'FormCompilationStateReleased',
  FormCompilationStateBound = 'FormCompilationStateBound',
}

/**
 *
 * @export
 * @interface FormRecord
 */
export interface FormRecord {
  /**
   *
   * @type {string}
   * @memberof FormRecord
   */
  code: string;
  /**
   *
   * @type {string}
   * @memberof FormRecord
   */
  name: string;
  /**
   * template url
   * @type {string}
   * @memberof FormRecord
   */
  url: string;
  /**
   * filled PDF file handle
   * @type {string}
   * @memberof FormRecord
   */
  handle?: string;
  /**
   *
   * @type {OrderCategory}
   * @memberof FormRecord
   */
  orderCategory: OrderCategory;
  /**
   * frozen PDF file handle
   * @type {string}
   * @memberof FormRecord
   */
  frozenHandleId?: string;
  /**
   *
   * @type {boolean}
   * @memberof FormRecord
   */
  isDynamic: boolean;
  /**
   *
   * @type {CoverageType}
   * @memberof FormRecord
   */
  coverage: CoverageType;
  /**
   *
   * @type {string}
   * @memberof FormRecord
   */
  UserVisibleCode: string;
}
/**
 *
 * @export
 * @interface FormSchedule
 */
export interface FormSchedule {
  /**
   *
   * @type {string}
   * @memberof FormSchedule
   */
  fullFormCode: string;
  /**
   *
   * @type {string}
   * @memberof FormSchedule
   */
  formCode: string;
  /**
   *
   * @type {string}
   * @memberof FormSchedule
   */
  formName: string;
  /**
   *
   * @type {string}
   * @memberof FormSchedule
   */
  description?: string;
  /**
   *
   * @type {OrderCategory}
   * @memberof FormSchedule
   */
  orderCategory: OrderCategory;
  /**
   *
   * @type {Array<FormApplicabilityRule>}
   * @memberof FormSchedule
   */
  applicabilityRules: Array<FormApplicabilityRule>;
  /**
   *
   * @type {object}
   * @memberof FormSchedule
   */
  metadata?: object;
  /**
   *
   * @type {FormTemplateType}
   * @memberof FormSchedule
   */
  formTemplateType: FormTemplateType;
  /**
   *
   * @type {FormScheduleState}
   * @memberof FormSchedule
   */
  status: FormScheduleState;
  /**
   *
   * @type {string}
   * @memberof FormSchedule
   */
  createdAt: string;
  /**
   *
   * @type {string}
   * @memberof FormSchedule
   */
  updatedAt: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum FormScheduleState {
  Active = 'Active',
  Inactive = 'Inactive',
  Archived = 'Archived',
}

/**
 *
 * @export
 * @interface FormScheduleUpdateRequest
 */
export interface FormScheduleUpdateRequest {
  /**
   *
   * @type {string}
   * @memberof FormScheduleUpdateRequest
   */
  fullFormCode: string;
  /**
   *
   * @type {string}
   * @memberof FormScheduleUpdateRequest
   */
  formName?: string;
  /**
   *
   * @type {string}
   * @memberof FormScheduleUpdateRequest
   */
  description?: string;
  /**
   *
   * @type {OrderCategory}
   * @memberof FormScheduleUpdateRequest
   */
  orderCategory?: OrderCategory;
  /**
   *
   * @type {Array<FormApplicabilityRule>}
   * @memberof FormScheduleUpdateRequest
   */
  applicabilityRules?: Array<FormApplicabilityRule>;
  /**
   *
   * @type {object}
   * @memberof FormScheduleUpdateRequest
   */
  metadata?: object;
  /**
   *
   * @type {FormScheduleState}
   * @memberof FormScheduleUpdateRequest
   */
  status?: FormScheduleState;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum FormTemplateType {
  Pdf = 'PDF',
  ComposedPdf = 'ComposedPDF',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum FormType {
  FormTypePolicy = 'FormTypePolicy',
  FormTypeSignaturePacket = 'FormTypeSignaturePacket',
}

/**
 *
 * @export
 * @interface HierarchicalStateDateRule
 */
export interface HierarchicalStateDateRule {
  /**
   *
   * @type {string}
   * @memberof HierarchicalStateDateRule
   */
  default: string;
  /**
   *
   * @type {Array<StateDatePair>}
   * @memberof HierarchicalStateDateRule
   */
  stateSpecific?: Array<StateDatePair>;
}
/**
 *
 * @export
 * @interface InitFormsRequest
 */
export interface InitFormsRequest {
  /**
   *
   * @type {FormType}
   * @memberof InitFormsRequest
   */
  formType: FormType;
  /**
   *
   * @type {string}
   * @memberof InitFormsRequest
   */
  applicationId?: string;
  /**
   *
   * @type {CoverageType}
   * @memberof InitFormsRequest
   */
  coverage?: CoverageType;
  /**
   *
   * @type {ApplicationType}
   * @memberof InitFormsRequest
   */
  applicationType?: ApplicationType;
}
/**
 *
 * @export
 * @interface InitFormsResponse
 */
export interface InitFormsResponse {
  /**
   *
   * @type {string}
   * @memberof InitFormsResponse
   */
  formCompilationId: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum OrderCategory {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
  E = 'E',
  F = 'F',
  G = 'G',
  H = 'H',
  I = 'I',
  J = 'J',
  K = 'K',
  L = 'L',
  M = 'M',
  N = 'N',
  O = 'O',
  P = 'P',
  Q = 'Q',
  R = 'R',
  S = 'S',
  T = 'T',
  U = 'U',
  V = 'V',
  W = 'W',
  X = 'X',
  Y = 'Y',
  Z = 'Z',
  Za = 'ZA',
  Zb = 'ZB',
  Zc = 'ZC',
  Zd = 'ZD',
  Ze = 'ZE',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum PackageType {
  Basic = 'Basic',
  Standard = 'Standard',
  Complete = 'Complete',
  BasicAndStandard = 'BasicAndStandard',
  StandardAndComplete = 'StandardAndComplete',
  BasicAndComplete = 'BasicAndComplete',
  AllPackages = 'AllPackages',
  None = 'None',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum PaymentMethod {
  PaidInFull = 'PaidInFull',
  MonthlyReporter = 'MonthlyReporter',
}

/**
 *
 * @export
 * @interface PreBindInfo
 */
export interface PreBindInfo {
  /**
   *
   * @type {number}
   * @memberof PreBindInfo
   */
  collateral?: number;
  /**
   *
   * @type {number}
   * @memberof PreBindInfo
   */
  depositAmount?: number;
}
/**
 *
 * @export
 * @interface PrefillPreBindInfo
 */
export interface PrefillPreBindInfo {
  /**
   *
   * @type {number}
   * @memberof PrefillPreBindInfo
   */
  commissionRate?: number;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ProgramType {
  ProgramTypeFleet = 'ProgramTypeFleet',
  ProgramTypeNonFleetCanopiusNrb = 'ProgramTypeNonFleetCanopiusNRB',
  ProgramTypeNonFleetAdmitted = 'ProgramTypeNonFleetAdmitted',
  ProgramTypeBusinessAuto = 'ProgramTypeBusinessAuto',
  ProgramTypeInvalid = 'ProgramTypeInvalid',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum ScheduleType {
  ScheduleTypeCore = 'ScheduleTypeCore',
  ScheduleTypeStateDependent = 'ScheduleTypeStateDependent',
  ScheduleTypePackageDependent = 'ScheduleTypePackageDependent',
  ScheduleTypeSignedQuoteForms = 'ScheduleTypeSignedQuoteForms',
  ScheduleTypeManualForms = 'ScheduleTypeManualForms',
}

/**
 *
 * @export
 * @interface ScheduleTypeToForms
 */
export interface ScheduleTypeToForms {
  /**
   *
   * @type {ScheduleType}
   * @memberof ScheduleTypeToForms
   */
  scheduleType: ScheduleType;
  /**
   *
   * @type {Array<FormRecord>}
   * @memberof ScheduleTypeToForms
   */
  forms: Array<FormRecord>;
}
/**
 *
 * @export
 * @interface SignaturePacketAndCoveragePolicy
 */
export interface SignaturePacketAndCoveragePolicy {
  /**
   *
   * @type {string}
   * @memberof SignaturePacketAndCoveragePolicy
   */
  signaturePacket: string;
  /**
   *
   * @type {string}
   * @memberof SignaturePacketAndCoveragePolicy
   */
  coveragePolicy: string;
}
/**
 *
 * @export
 * @interface StateDatePair
 */
export interface StateDatePair {
  /**
   *
   * @type {USState}
   * @memberof StateDatePair
   */
  state: USState;
  /**
   *
   * @type {string}
   * @memberof StateDatePair
   */
  date: string;
}
/**
 *
 * @export
 * @interface Template
 */
export interface Template {
  /**
   *
   * @type {Array<Config>}
   * @memberof Template
   */
  config?: Array<Config>;
  /**
   *
   * @type {Applicability}
   * @memberof Template
   */
  applicability?: Applicability;
}
/**
 * Two character short code for the US state the driver is licensed in.
 * @export
 * @enum {string}
 */

export enum USState {
  Al = 'AL',
  Ak = 'AK',
  Az = 'AZ',
  Ar = 'AR',
  Ca = 'CA',
  Co = 'CO',
  Ct = 'CT',
  De = 'DE',
  Dc = 'DC',
  Fl = 'FL',
  Ga = 'GA',
  Hi = 'HI',
  Id = 'ID',
  Il = 'IL',
  In = 'IN',
  Ia = 'IA',
  Ks = 'KS',
  Ky = 'KY',
  La = 'LA',
  Me = 'ME',
  Md = 'MD',
  Ma = 'MA',
  Mi = 'MI',
  Mn = 'MN',
  Ms = 'MS',
  Mo = 'MO',
  Mt = 'MT',
  Ne = 'NE',
  Nv = 'NV',
  Nh = 'NH',
  Nj = 'NJ',
  Nm = 'NM',
  Ny = 'NY',
  Nc = 'NC',
  Nd = 'ND',
  Oh = 'OH',
  Ok = 'OK',
  Or = 'OR',
  Pa = 'PA',
  Ri = 'RI',
  Sc = 'SC',
  Sd = 'SD',
  Tn = 'TN',
  Tx = 'TX',
  Ut = 'UT',
  Vt = 'VT',
  Va = 'VA',
  Wa = 'WA',
  Wv = 'WV',
  Wi = 'WI',
  Wy = 'WY',
}

/**
 *
 * @export
 * @interface UploadFormRequest
 */
export interface UploadFormRequest {
  /**
   *
   * @type {any}
   * @memberof UploadFormRequest
   */
  file: any;
  /**
   *
   * @type {FileType}
   * @memberof UploadFormRequest
   */
  fileType: FileType;
  /**
   *
   * @type {FileDestinationGroup}
   * @memberof UploadFormRequest
   */
  fileDestinationGroup: FileDestinationGroup;
}

/**
 * ApplicationApi - axios parameter creator
 * @export
 */
export const ApplicationApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Zips S3 objects and returns the handle id of the zip file
     * @param {string} formCompilationId
     * @param {boolean} [isEditable]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsZipFormCompilationIdPost: async (
      formCompilationId: string,
      isEditable?: boolean,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsZipFormCompilationIdPost',
        'formCompilationId',
        formCompilationId,
      );
      const localVarPath = `/forms/zip/{formCompilationId}`.replace(
        `{${'formCompilationId'}}`,
        encodeURIComponent(String(formCompilationId)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      if (isEditable !== undefined) {
        localVarQueryParameter['isEditable'] = isEditable;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get bind reasons for an applicationId
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationBindReasons: async (
      applicationID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationID' is not null or undefined
      assertParamExists(
        'getApplicationBindReasons',
        'applicationID',
        applicationID,
      );
      const localVarPath =
        `/form/reasons/{applicationID}/get_application_bind_reasons`.replace(
          `{${'applicationID'}}`,
          encodeURIComponent(String(applicationID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Gets intermediate billing info
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationPreBindInfo: async (
      applicationID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationID' is not null or undefined
      assertParamExists(
        'getApplicationPreBindInfo',
        'applicationID',
        applicationID,
      );
      const localVarPath = `/form/{applicationID}/pre_bind_info`.replace(
        `{${'applicationID'}}`,
        encodeURIComponent(String(applicationID)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Gets billing info
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getPrefillPreBindInfo: async (
      applicationID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationID' is not null or undefined
      assertParamExists(
        'getPrefillPreBindInfo',
        'applicationID',
        applicationID,
      );
      const localVarPath =
        `/form/{applicationID}/prefill_pre_bind_info`.replace(
          `{${'applicationID'}}`,
          encodeURIComponent(String(applicationID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Stores intermediate billing info
     * @param {string} applicationID
     * @param {ApplicationReviewPreBindInfo} applicationReviewPreBindInfo
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postApplicationPreBindInfo: async (
      applicationID: string,
      applicationReviewPreBindInfo: ApplicationReviewPreBindInfo,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationID' is not null or undefined
      assertParamExists(
        'postApplicationPreBindInfo',
        'applicationID',
        applicationID,
      );
      // verify required parameter 'applicationReviewPreBindInfo' is not null or undefined
      assertParamExists(
        'postApplicationPreBindInfo',
        'applicationReviewPreBindInfo',
        applicationReviewPreBindInfo,
      );
      const localVarPath = `/form/{applicationID}/pre_bind_info`.replace(
        `{${'applicationID'}}`,
        encodeURIComponent(String(applicationID)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        applicationReviewPreBindInfo,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ApplicationApi - functional programming interface
 * @export
 */
export const ApplicationApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ApplicationApiAxiosParamCreator(configuration);
  return {
    /**
     * Zips S3 objects and returns the handle id of the zip file
     * @param {string} formCompilationId
     * @param {boolean} [isEditable]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsZipFormCompilationIdPost(
      formCompilationId: string,
      isEditable?: boolean,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<FileHandle>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsZipFormCompilationIdPost(
          formCompilationId,
          isEditable,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get bind reasons for an applicationId
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationBindReasons(
      applicationID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationIdBoundReasons>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationBindReasons(
          applicationID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Gets intermediate billing info
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationPreBindInfo(
      applicationID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<PreBindInfo>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationPreBindInfo(
          applicationID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Gets billing info
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getPrefillPreBindInfo(
      applicationID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<PrefillPreBindInfo>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getPrefillPreBindInfo(
          applicationID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Stores intermediate billing info
     * @param {string} applicationID
     * @param {ApplicationReviewPreBindInfo} applicationReviewPreBindInfo
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async postApplicationPreBindInfo(
      applicationID: string,
      applicationReviewPreBindInfo: ApplicationReviewPreBindInfo,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.postApplicationPreBindInfo(
          applicationID,
          applicationReviewPreBindInfo,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * ApplicationApi - factory interface
 * @export
 */
export const ApplicationApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = ApplicationApiFp(configuration);
  return {
    /**
     * Zips S3 objects and returns the handle id of the zip file
     * @param {string} formCompilationId
     * @param {boolean} [isEditable]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsZipFormCompilationIdPost(
      formCompilationId: string,
      isEditable?: boolean,
      options?: any,
    ): AxiosPromise<FileHandle> {
      return localVarFp
        .formsZipFormCompilationIdPost(formCompilationId, isEditable, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get bind reasons for an applicationId
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationBindReasons(
      applicationID: string,
      options?: any,
    ): AxiosPromise<ApplicationIdBoundReasons> {
      return localVarFp
        .getApplicationBindReasons(applicationID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Gets intermediate billing info
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationPreBindInfo(
      applicationID: string,
      options?: any,
    ): AxiosPromise<PreBindInfo> {
      return localVarFp
        .getApplicationPreBindInfo(applicationID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Gets billing info
     * @param {string} applicationID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getPrefillPreBindInfo(
      applicationID: string,
      options?: any,
    ): AxiosPromise<PrefillPreBindInfo> {
      return localVarFp
        .getPrefillPreBindInfo(applicationID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Stores intermediate billing info
     * @param {string} applicationID
     * @param {ApplicationReviewPreBindInfo} applicationReviewPreBindInfo
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postApplicationPreBindInfo(
      applicationID: string,
      applicationReviewPreBindInfo: ApplicationReviewPreBindInfo,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .postApplicationPreBindInfo(
          applicationID,
          applicationReviewPreBindInfo,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ApplicationApi - object-oriented interface
 * @export
 * @class ApplicationApi
 * @extends {BaseAPI}
 */
export class ApplicationApi extends BaseAPI {
  /**
   * Zips S3 objects and returns the handle id of the zip file
   * @param {string} formCompilationId
   * @param {boolean} [isEditable]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ApplicationApi
   */
  public formsZipFormCompilationIdPost(
    formCompilationId: string,
    isEditable?: boolean,
    options?: AxiosRequestConfig,
  ) {
    return ApplicationApiFp(this.configuration)
      .formsZipFormCompilationIdPost(formCompilationId, isEditable, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get bind reasons for an applicationId
   * @param {string} applicationID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ApplicationApi
   */
  public getApplicationBindReasons(
    applicationID: string,
    options?: AxiosRequestConfig,
  ) {
    return ApplicationApiFp(this.configuration)
      .getApplicationBindReasons(applicationID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Gets intermediate billing info
   * @param {string} applicationID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ApplicationApi
   */
  public getApplicationPreBindInfo(
    applicationID: string,
    options?: AxiosRequestConfig,
  ) {
    return ApplicationApiFp(this.configuration)
      .getApplicationPreBindInfo(applicationID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Gets billing info
   * @param {string} applicationID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ApplicationApi
   */
  public getPrefillPreBindInfo(
    applicationID: string,
    options?: AxiosRequestConfig,
  ) {
    return ApplicationApiFp(this.configuration)
      .getPrefillPreBindInfo(applicationID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Stores intermediate billing info
   * @param {string} applicationID
   * @param {ApplicationReviewPreBindInfo} applicationReviewPreBindInfo
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ApplicationApi
   */
  public postApplicationPreBindInfo(
    applicationID: string,
    applicationReviewPreBindInfo: ApplicationReviewPreBindInfo,
    options?: AxiosRequestConfig,
  ) {
    return ApplicationApiFp(this.configuration)
      .postApplicationPreBindInfo(
        applicationID,
        applicationReviewPreBindInfo,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DefaultApi - axios parameter creator
 * @export
 */
export const DefaultApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Retrieve a list of form schedules.
     * @param {USState} [state]
     * @param {FormScheduleState} [formScheduleStatus]
     * @param {string} [fullFormCode]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formSchedulesGet: async (
      state?: USState,
      formScheduleStatus?: FormScheduleState,
      fullFormCode?: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/form/schedules`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      if (state !== undefined) {
        localVarQueryParameter['state'] = state;
      }

      if (formScheduleStatus !== undefined) {
        localVarQueryParameter['formScheduleStatus'] = formScheduleStatus;
      }

      if (fullFormCode !== undefined) {
        localVarQueryParameter['fullFormCode'] = fullFormCode;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DefaultApiAxiosParamCreator(configuration);
  return {
    /**
     * Retrieve a list of form schedules.
     * @param {USState} [state]
     * @param {FormScheduleState} [formScheduleStatus]
     * @param {string} [fullFormCode]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formSchedulesGet(
      state?: USState,
      formScheduleStatus?: FormScheduleState,
      fullFormCode?: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<FormSchedule>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formSchedulesGet(
          state,
          formScheduleStatus,
          fullFormCode,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = DefaultApiFp(configuration);
  return {
    /**
     * Retrieve a list of form schedules.
     * @param {USState} [state]
     * @param {FormScheduleState} [formScheduleStatus]
     * @param {string} [fullFormCode]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formSchedulesGet(
      state?: USState,
      formScheduleStatus?: FormScheduleState,
      fullFormCode?: string,
      options?: any,
    ): AxiosPromise<Array<FormSchedule>> {
      return localVarFp
        .formSchedulesGet(state, formScheduleStatus, fullFormCode, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
  /**
   * Retrieve a list of form schedules.
   * @param {USState} [state]
   * @param {FormScheduleState} [formScheduleStatus]
   * @param {string} [fullFormCode]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public formSchedulesGet(
    state?: USState,
    formScheduleStatus?: FormScheduleState,
    fullFormCode?: string,
    options?: AxiosRequestConfig,
  ) {
    return DefaultApiFp(this.configuration)
      .formSchedulesGet(state, formScheduleStatus, fullFormCode, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * FormScheduleApi - axios parameter creator
 * @export
 */
export const FormScheduleApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Creates form schedule, which is typically a row on the form schedule matrix sheet
     * @param {CreateFormScheduleRequest} [createFormScheduleRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formSchedulesPost: async (
      createFormScheduleRequest?: CreateFormScheduleRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/form/schedules`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createFormScheduleRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Updates form schedule, which is typically a row on the form schedule matrix
     * @param {FormScheduleUpdateRequest} [formScheduleUpdateRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formSchedulesPut: async (
      formScheduleUpdateRequest?: FormScheduleUpdateRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/form/schedules`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        formScheduleUpdateRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * FormScheduleApi - functional programming interface
 * @export
 */
export const FormScheduleApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    FormScheduleApiAxiosParamCreator(configuration);
  return {
    /**
     * Creates form schedule, which is typically a row on the form schedule matrix sheet
     * @param {CreateFormScheduleRequest} [createFormScheduleRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formSchedulesPost(
      createFormScheduleRequest?: CreateFormScheduleRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<CreateFormScheduleResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formSchedulesPost(
          createFormScheduleRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Updates form schedule, which is typically a row on the form schedule matrix
     * @param {FormScheduleUpdateRequest} [formScheduleUpdateRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formSchedulesPut(
      formScheduleUpdateRequest?: FormScheduleUpdateRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formSchedulesPut(
          formScheduleUpdateRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * FormScheduleApi - factory interface
 * @export
 */
export const FormScheduleApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = FormScheduleApiFp(configuration);
  return {
    /**
     * Creates form schedule, which is typically a row on the form schedule matrix sheet
     * @param {CreateFormScheduleRequest} [createFormScheduleRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formSchedulesPost(
      createFormScheduleRequest?: CreateFormScheduleRequest,
      options?: any,
    ): AxiosPromise<CreateFormScheduleResponse> {
      return localVarFp
        .formSchedulesPost(createFormScheduleRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Updates form schedule, which is typically a row on the form schedule matrix
     * @param {FormScheduleUpdateRequest} [formScheduleUpdateRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formSchedulesPut(
      formScheduleUpdateRequest?: FormScheduleUpdateRequest,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .formSchedulesPut(formScheduleUpdateRequest, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * FormScheduleApi - object-oriented interface
 * @export
 * @class FormScheduleApi
 * @extends {BaseAPI}
 */
export class FormScheduleApi extends BaseAPI {
  /**
   * Creates form schedule, which is typically a row on the form schedule matrix sheet
   * @param {CreateFormScheduleRequest} [createFormScheduleRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormScheduleApi
   */
  public formSchedulesPost(
    createFormScheduleRequest?: CreateFormScheduleRequest,
    options?: AxiosRequestConfig,
  ) {
    return FormScheduleApiFp(this.configuration)
      .formSchedulesPost(createFormScheduleRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Updates form schedule, which is typically a row on the form schedule matrix
   * @param {FormScheduleUpdateRequest} [formScheduleUpdateRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormScheduleApi
   */
  public formSchedulesPut(
    formScheduleUpdateRequest?: FormScheduleUpdateRequest,
    options?: AxiosRequestConfig,
  ) {
    return FormScheduleApiFp(this.configuration)
      .formSchedulesPut(formScheduleUpdateRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * FormsApi - axios parameter creator
 * @export
 */
export const FormsApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Bind a form compilation
     * @param {string} formCompilationId
     * @param {ApplicationReviewBindReasonObject} [applicationReviewBindReasonObject]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationBindFormCompilationIdPost: async (
      formCompilationId: string,
      applicationReviewBindReasonObject?: ApplicationReviewBindReasonObject,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsCompilationBindFormCompilationIdPost',
        'formCompilationId',
        formCompilationId,
      );
      const localVarPath =
        `/forms/compilation/bind/{formCompilationId}`.replace(
          `{${'formCompilationId'}}`,
          encodeURIComponent(String(formCompilationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        applicationReviewBindReasonObject,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Create a temporary public URL to download an application file.
     * @param {string} formCompilationId
     * @param {string} fileHandle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdFileFileHandleDownloadGet: async (
      formCompilationId: string,
      fileHandle: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsCompilationFormCompilationIdFileFileHandleDownloadGet',
        'formCompilationId',
        formCompilationId,
      );
      // verify required parameter 'fileHandle' is not null or undefined
      assertParamExists(
        'formsCompilationFormCompilationIdFileFileHandleDownloadGet',
        'fileHandle',
        fileHandle,
      );
      const localVarPath =
        `/forms/compilation/{formCompilationId}/file/{fileHandle}/download`
          .replace(
            `{${'formCompilationId'}}`,
            encodeURIComponent(String(formCompilationId)),
          )
          .replace(`{${'fileHandle'}}`, encodeURIComponent(String(fileHandle)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Fills the forms PDFs
     * @param {string} formCompilationId
     * @param {string} [body]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdFillPost: async (
      formCompilationId: string,
      body?: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsCompilationFormCompilationIdFillPost',
        'formCompilationId',
        formCompilationId,
      );
      const localVarPath =
        `/forms/compilation/{formCompilationId}/fill`.replace(
          `{${'formCompilationId'}}`,
          encodeURIComponent(String(formCompilationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Freeze the forms compilation and fetch the download handle of the combined PDF file
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdFreezePost: async (
      formCompilationId: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsCompilationFormCompilationIdFreezePost',
        'formCompilationId',
        formCompilationId,
      );
      const localVarPath =
        `/forms/compilation/{formCompilationId}/freeze`.replace(
          `{${'formCompilationId'}}`,
          encodeURIComponent(String(formCompilationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get list of forms for a compilation
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdGet: async (
      formCompilationId: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsCompilationFormCompilationIdGet',
        'formCompilationId',
        formCompilationId,
      );
      const localVarPath = `/forms/compilation/{formCompilationId}`.replace(
        `{${'formCompilationId'}}`,
        encodeURIComponent(String(formCompilationId)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Finalise the list of required forms in a compilation
     * @param {string} formCompilationId
     * @param {Array<CoveragesToScheduleTypes>} [coveragesToScheduleTypes]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdPut: async (
      formCompilationId: string,
      coveragesToScheduleTypes?: Array<CoveragesToScheduleTypes>,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsCompilationFormCompilationIdPut',
        'formCompilationId',
        formCompilationId,
      );
      const localVarPath = `/forms/compilation/{formCompilationId}`.replace(
        `{${'formCompilationId'}}`,
        encodeURIComponent(String(formCompilationId)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        coveragesToScheduleTypes,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Replace a filled in form with the user\'s corrected version of the form.
     * @param {string} formCompilationId
     * @param {FormRecord} [formRecord]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdReplacePost: async (
      formCompilationId: string,
      formRecord?: FormRecord,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsCompilationFormCompilationIdReplacePost',
        'formCompilationId',
        formCompilationId,
      );
      const localVarPath =
        `/forms/compilation/{formCompilationId}/replace`.replace(
          `{${'formCompilationId'}}`,
          encodeURIComponent(String(formCompilationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        formRecord,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Inits a form compilation
     * @param {InitFormsRequest} [initFormsRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationPost: async (
      initFormsRequest?: InitFormsRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/forms/compilation`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        initFormsRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Release a form compilation
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationReleaseFormCompilationIdPost: async (
      formCompilationId: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsCompilationReleaseFormCompilationIdPost',
        'formCompilationId',
        formCompilationId,
      );
      const localVarPath =
        `/forms/compilation/release/{formCompilationId}`.replace(
          `{${'formCompilationId'}}`,
          encodeURIComponent(String(formCompilationId)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get list of form compilation associated with an application for a compilation
     * @param {string} applicationID
     * @param {'ApplicationTypeFleet' | 'ApplicationTypeNonFleet' | 'ApplicationTypeNonFleetAdmitted' | 'ApplicationTypeBusinessAuto'} [applicationType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationsApplicationIDGet: async (
      applicationID: string,
      applicationType?:
        | 'ApplicationTypeFleet'
        | 'ApplicationTypeNonFleet'
        | 'ApplicationTypeNonFleetAdmitted'
        | 'ApplicationTypeBusinessAuto',
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationID' is not null or undefined
      assertParamExists(
        'formsCompilationsApplicationIDGet',
        'applicationID',
        applicationID,
      );
      const localVarPath = `/forms/compilations/{applicationID}`.replace(
        `{${'applicationID'}}`,
        encodeURIComponent(String(applicationID)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      if (applicationType !== undefined) {
        localVarQueryParameter['applicationType'] = applicationType;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Confirm a coverage policy for a signature packet
     * @param {SignaturePacketAndCoveragePolicy} [signaturePacketAndCoveragePolicy]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationsConfirmFormsPut: async (
      signaturePacketAndCoveragePolicy?: SignaturePacketAndCoveragePolicy,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/forms/compilations/confirm_forms`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PUT',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        signaturePacketAndCoveragePolicy,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Upload a form and return it\'s handle id
     * @param {string} formCompilationId
     * @param {any} file
     * @param {FileType} fileType
     * @param {FileDestinationGroup} fileDestinationGroup
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsUploadFormCompilationIdPost: async (
      formCompilationId: string,
      file: any,
      fileType: FileType,
      fileDestinationGroup: FileDestinationGroup,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'formCompilationId' is not null or undefined
      assertParamExists(
        'formsUploadFormCompilationIdPost',
        'formCompilationId',
        formCompilationId,
      );
      // verify required parameter 'file' is not null or undefined
      assertParamExists('formsUploadFormCompilationIdPost', 'file', file);
      // verify required parameter 'fileType' is not null or undefined
      assertParamExists(
        'formsUploadFormCompilationIdPost',
        'fileType',
        fileType,
      );
      // verify required parameter 'fileDestinationGroup' is not null or undefined
      assertParamExists(
        'formsUploadFormCompilationIdPost',
        'fileDestinationGroup',
        fileDestinationGroup,
      );
      const localVarPath = `/forms/upload/{formCompilationId}`.replace(
        `{${'formCompilationId'}}`,
        encodeURIComponent(String(formCompilationId)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;
      const localVarFormParams = new ((configuration &&
        configuration.formDataCtor) ||
        FormData)();

      // authentication sessionIdAuth required
      await setApiKeyToObject(
        localVarHeaderParameter,
        'JSESSIONID',
        configuration,
      );

      if (file !== undefined) {
        localVarFormParams.append('file', file as any);
      }

      if (fileType !== undefined) {
        localVarFormParams.append(
          'fileType',
          new Blob([JSON.stringify(fileType)], { type: 'application/json' }),
        );
      }

      if (fileDestinationGroup !== undefined) {
        localVarFormParams.append(
          'fileDestinationGroup',
          new Blob([JSON.stringify(fileDestinationGroup)], {
            type: 'application/json',
          }),
        );
      }

      localVarHeaderParameter['Content-Type'] = 'multipart/form-data';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = localVarFormParams;

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * FormsApi - functional programming interface
 * @export
 */
export const FormsApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = FormsApiAxiosParamCreator(configuration);
  return {
    /**
     * Bind a form compilation
     * @param {string} formCompilationId
     * @param {ApplicationReviewBindReasonObject} [applicationReviewBindReasonObject]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationBindFormCompilationIdPost(
      formCompilationId: string,
      applicationReviewBindReasonObject?: ApplicationReviewBindReasonObject,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationBindFormCompilationIdPost(
          formCompilationId,
          applicationReviewBindReasonObject,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Create a temporary public URL to download an application file.
     * @param {string} formCompilationId
     * @param {string} fileHandle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationFormCompilationIdFileFileHandleDownloadGet(
      formCompilationId: string,
      fileHandle: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<DownloadFileLinkResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationFormCompilationIdFileFileHandleDownloadGet(
          formCompilationId,
          fileHandle,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Fills the forms PDFs
     * @param {string} formCompilationId
     * @param {string} [body]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationFormCompilationIdFillPost(
      formCompilationId: string,
      body?: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationFormCompilationIdFillPost(
          formCompilationId,
          body,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Freeze the forms compilation and fetch the download handle of the combined PDF file
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationFormCompilationIdFreezePost(
      formCompilationId: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationFormCompilationIdFreezePost(
          formCompilationId,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get list of forms for a compilation
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationFormCompilationIdGet(
      formCompilationId: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<FormCompilation>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationFormCompilationIdGet(
          formCompilationId,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Finalise the list of required forms in a compilation
     * @param {string} formCompilationId
     * @param {Array<CoveragesToScheduleTypes>} [coveragesToScheduleTypes]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationFormCompilationIdPut(
      formCompilationId: string,
      coveragesToScheduleTypes?: Array<CoveragesToScheduleTypes>,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationFormCompilationIdPut(
          formCompilationId,
          coveragesToScheduleTypes,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Replace a filled in form with the user\'s corrected version of the form.
     * @param {string} formCompilationId
     * @param {FormRecord} [formRecord]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationFormCompilationIdReplacePost(
      formCompilationId: string,
      formRecord?: FormRecord,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationFormCompilationIdReplacePost(
          formCompilationId,
          formRecord,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Inits a form compilation
     * @param {InitFormsRequest} [initFormsRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationPost(
      initFormsRequest?: InitFormsRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<InitFormsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationPost(
          initFormsRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Release a form compilation
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationReleaseFormCompilationIdPost(
      formCompilationId: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationReleaseFormCompilationIdPost(
          formCompilationId,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get list of form compilation associated with an application for a compilation
     * @param {string} applicationID
     * @param {'ApplicationTypeFleet' | 'ApplicationTypeNonFleet' | 'ApplicationTypeNonFleetAdmitted' | 'ApplicationTypeBusinessAuto'} [applicationType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationsApplicationIDGet(
      applicationID: string,
      applicationType?:
        | 'ApplicationTypeFleet'
        | 'ApplicationTypeNonFleet'
        | 'ApplicationTypeNonFleetAdmitted'
        | 'ApplicationTypeBusinessAuto',
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<FormCompilation>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationsApplicationIDGet(
          applicationID,
          applicationType,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Confirm a coverage policy for a signature packet
     * @param {SignaturePacketAndCoveragePolicy} [signaturePacketAndCoveragePolicy]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsCompilationsConfirmFormsPut(
      signaturePacketAndCoveragePolicy?: SignaturePacketAndCoveragePolicy,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsCompilationsConfirmFormsPut(
          signaturePacketAndCoveragePolicy,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Upload a form and return it\'s handle id
     * @param {string} formCompilationId
     * @param {any} file
     * @param {FileType} fileType
     * @param {FileDestinationGroup} fileDestinationGroup
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async formsUploadFormCompilationIdPost(
      formCompilationId: string,
      file: any,
      fileType: FileType,
      fileDestinationGroup: FileDestinationGroup,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.formsUploadFormCompilationIdPost(
          formCompilationId,
          file,
          fileType,
          fileDestinationGroup,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * FormsApi - factory interface
 * @export
 */
export const FormsApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = FormsApiFp(configuration);
  return {
    /**
     * Bind a form compilation
     * @param {string} formCompilationId
     * @param {ApplicationReviewBindReasonObject} [applicationReviewBindReasonObject]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationBindFormCompilationIdPost(
      formCompilationId: string,
      applicationReviewBindReasonObject?: ApplicationReviewBindReasonObject,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .formsCompilationBindFormCompilationIdPost(
          formCompilationId,
          applicationReviewBindReasonObject,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Create a temporary public URL to download an application file.
     * @param {string} formCompilationId
     * @param {string} fileHandle
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdFileFileHandleDownloadGet(
      formCompilationId: string,
      fileHandle: string,
      options?: any,
    ): AxiosPromise<DownloadFileLinkResponse> {
      return localVarFp
        .formsCompilationFormCompilationIdFileFileHandleDownloadGet(
          formCompilationId,
          fileHandle,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Fills the forms PDFs
     * @param {string} formCompilationId
     * @param {string} [body]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdFillPost(
      formCompilationId: string,
      body?: string,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .formsCompilationFormCompilationIdFillPost(
          formCompilationId,
          body,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Freeze the forms compilation and fetch the download handle of the combined PDF file
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdFreezePost(
      formCompilationId: string,
      options?: any,
    ): AxiosPromise<string> {
      return localVarFp
        .formsCompilationFormCompilationIdFreezePost(formCompilationId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get list of forms for a compilation
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdGet(
      formCompilationId: string,
      options?: any,
    ): AxiosPromise<FormCompilation> {
      return localVarFp
        .formsCompilationFormCompilationIdGet(formCompilationId, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Finalise the list of required forms in a compilation
     * @param {string} formCompilationId
     * @param {Array<CoveragesToScheduleTypes>} [coveragesToScheduleTypes]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdPut(
      formCompilationId: string,
      coveragesToScheduleTypes?: Array<CoveragesToScheduleTypes>,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .formsCompilationFormCompilationIdPut(
          formCompilationId,
          coveragesToScheduleTypes,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Replace a filled in form with the user\'s corrected version of the form.
     * @param {string} formCompilationId
     * @param {FormRecord} [formRecord]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationFormCompilationIdReplacePost(
      formCompilationId: string,
      formRecord?: FormRecord,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .formsCompilationFormCompilationIdReplacePost(
          formCompilationId,
          formRecord,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Inits a form compilation
     * @param {InitFormsRequest} [initFormsRequest]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationPost(
      initFormsRequest?: InitFormsRequest,
      options?: any,
    ): AxiosPromise<InitFormsResponse> {
      return localVarFp
        .formsCompilationPost(initFormsRequest, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Release a form compilation
     * @param {string} formCompilationId
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationReleaseFormCompilationIdPost(
      formCompilationId: string,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .formsCompilationReleaseFormCompilationIdPost(
          formCompilationId,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get list of form compilation associated with an application for a compilation
     * @param {string} applicationID
     * @param {'ApplicationTypeFleet' | 'ApplicationTypeNonFleet' | 'ApplicationTypeNonFleetAdmitted' | 'ApplicationTypeBusinessAuto'} [applicationType]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationsApplicationIDGet(
      applicationID: string,
      applicationType?:
        | 'ApplicationTypeFleet'
        | 'ApplicationTypeNonFleet'
        | 'ApplicationTypeNonFleetAdmitted'
        | 'ApplicationTypeBusinessAuto',
      options?: any,
    ): AxiosPromise<Array<FormCompilation>> {
      return localVarFp
        .formsCompilationsApplicationIDGet(
          applicationID,
          applicationType,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Confirm a coverage policy for a signature packet
     * @param {SignaturePacketAndCoveragePolicy} [signaturePacketAndCoveragePolicy]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsCompilationsConfirmFormsPut(
      signaturePacketAndCoveragePolicy?: SignaturePacketAndCoveragePolicy,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .formsCompilationsConfirmFormsPut(
          signaturePacketAndCoveragePolicy,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Upload a form and return it\'s handle id
     * @param {string} formCompilationId
     * @param {any} file
     * @param {FileType} fileType
     * @param {FileDestinationGroup} fileDestinationGroup
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    formsUploadFormCompilationIdPost(
      formCompilationId: string,
      file: any,
      fileType: FileType,
      fileDestinationGroup: FileDestinationGroup,
      options?: any,
    ): AxiosPromise<string> {
      return localVarFp
        .formsUploadFormCompilationIdPost(
          formCompilationId,
          file,
          fileType,
          fileDestinationGroup,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * FormsApi - object-oriented interface
 * @export
 * @class FormsApi
 * @extends {BaseAPI}
 */
export class FormsApi extends BaseAPI {
  /**
   * Bind a form compilation
   * @param {string} formCompilationId
   * @param {ApplicationReviewBindReasonObject} [applicationReviewBindReasonObject]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationBindFormCompilationIdPost(
    formCompilationId: string,
    applicationReviewBindReasonObject?: ApplicationReviewBindReasonObject,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationBindFormCompilationIdPost(
        formCompilationId,
        applicationReviewBindReasonObject,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Create a temporary public URL to download an application file.
   * @param {string} formCompilationId
   * @param {string} fileHandle
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationFormCompilationIdFileFileHandleDownloadGet(
    formCompilationId: string,
    fileHandle: string,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationFormCompilationIdFileFileHandleDownloadGet(
        formCompilationId,
        fileHandle,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Fills the forms PDFs
   * @param {string} formCompilationId
   * @param {string} [body]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationFormCompilationIdFillPost(
    formCompilationId: string,
    body?: string,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationFormCompilationIdFillPost(
        formCompilationId,
        body,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Freeze the forms compilation and fetch the download handle of the combined PDF file
   * @param {string} formCompilationId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationFormCompilationIdFreezePost(
    formCompilationId: string,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationFormCompilationIdFreezePost(formCompilationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get list of forms for a compilation
   * @param {string} formCompilationId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationFormCompilationIdGet(
    formCompilationId: string,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationFormCompilationIdGet(formCompilationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Finalise the list of required forms in a compilation
   * @param {string} formCompilationId
   * @param {Array<CoveragesToScheduleTypes>} [coveragesToScheduleTypes]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationFormCompilationIdPut(
    formCompilationId: string,
    coveragesToScheduleTypes?: Array<CoveragesToScheduleTypes>,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationFormCompilationIdPut(
        formCompilationId,
        coveragesToScheduleTypes,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Replace a filled in form with the user\'s corrected version of the form.
   * @param {string} formCompilationId
   * @param {FormRecord} [formRecord]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationFormCompilationIdReplacePost(
    formCompilationId: string,
    formRecord?: FormRecord,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationFormCompilationIdReplacePost(
        formCompilationId,
        formRecord,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Inits a form compilation
   * @param {InitFormsRequest} [initFormsRequest]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationPost(
    initFormsRequest?: InitFormsRequest,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationPost(initFormsRequest, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Release a form compilation
   * @param {string} formCompilationId
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationReleaseFormCompilationIdPost(
    formCompilationId: string,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationReleaseFormCompilationIdPost(formCompilationId, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get list of form compilation associated with an application for a compilation
   * @param {string} applicationID
   * @param {'ApplicationTypeFleet' | 'ApplicationTypeNonFleet' | 'ApplicationTypeNonFleetAdmitted' | 'ApplicationTypeBusinessAuto'} [applicationType]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationsApplicationIDGet(
    applicationID: string,
    applicationType?:
      | 'ApplicationTypeFleet'
      | 'ApplicationTypeNonFleet'
      | 'ApplicationTypeNonFleetAdmitted'
      | 'ApplicationTypeBusinessAuto',
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationsApplicationIDGet(
        applicationID,
        applicationType,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Confirm a coverage policy for a signature packet
   * @param {SignaturePacketAndCoveragePolicy} [signaturePacketAndCoveragePolicy]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsCompilationsConfirmFormsPut(
    signaturePacketAndCoveragePolicy?: SignaturePacketAndCoveragePolicy,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsCompilationsConfirmFormsPut(
        signaturePacketAndCoveragePolicy,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Upload a form and return it\'s handle id
   * @param {string} formCompilationId
   * @param {any} file
   * @param {FileType} fileType
   * @param {FileDestinationGroup} fileDestinationGroup
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof FormsApi
   */
  public formsUploadFormCompilationIdPost(
    formCompilationId: string,
    file: any,
    fileType: FileType,
    fileDestinationGroup: FileDestinationGroup,
    options?: AxiosRequestConfig,
  ) {
    return FormsApiFp(this.configuration)
      .formsUploadFormCompilationIdPost(
        formCompilationId,
        file,
        fileType,
        fileDestinationGroup,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}
