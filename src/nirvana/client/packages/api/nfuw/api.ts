/* tslint:disable */
/* eslint-disable */
/**
 * Nirvana Non Fleet Underwriting API
 * Nirvana Non Fleet Underwriting APIs
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { Configuration } from './configuration';
import globalAxios, {
  AxiosPromise,
  AxiosInstance,
  AxiosRequestConfig,
} from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  RequestArgs,
  BaseAPI,
  RequiredError,
} from './base';

/**
 *
 * @export
 * @interface Address
 */
export interface Address {
  /**
   *
   * @type {string}
   * @memberof Address
   */
  street: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  city: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  state: string;
  /**
   *
   * @type {string}
   * @memberof Address
   */
  zip: string;
}
/**
 *
 * @export
 * @interface AdmittedAppBusinessOwner
 */
export interface AdmittedAppBusinessOwner {
  /**
   *
   * @type {string}
   * @memberof AdmittedAppBusinessOwner
   */
  firstName: string;
  /**
   *
   * @type {string}
   * @memberof AdmittedAppBusinessOwner
   */
  lastName: string;
  /**
   *
   * @type {string}
   * @memberof AdmittedAppBusinessOwner
   */
  dateOfBirth: string;
  /**
   *
   * @type {Address}
   * @memberof AdmittedAppBusinessOwner
   */
  address: Address;
  /**
   *
   * @type {boolean}
   * @memberof AdmittedAppBusinessOwner
   */
  driverOnPolicy?: boolean;
  /**
   *
   * @type {string}
   * @memberof AdmittedAppBusinessOwner
   */
  email?: string;
}
/**
 *
 * @export
 * @interface AdmittedAppTerminalLocation
 */
export interface AdmittedAppTerminalLocation {
  /**
   *
   * @type {boolean}
   * @memberof AdmittedAppTerminalLocation
   */
  sameAsPhysicalAddress: boolean;
  /**
   *
   * @type {Address}
   * @memberof AdmittedAppTerminalLocation
   */
  address: Address;
}
/**
 *
 * @export
 * @interface AdmittedOperationsAdditionalInformation
 */
export interface AdmittedOperationsAdditionalInformation {
  /**
   *
   * @type {OperationsMaxRadiusOfOperations}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  maxRadiusOfOperation: OperationsMaxRadiusOfOperations;
  /**
   *
   * @type {BusinessOwnerCreditScore}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  bizOwnerCreditScore: BusinessOwnerCreditScore;
  /**
   *
   * @type {USDotScore}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  usDotScore: USDotScore;
  /**
   *
   * @type {BusinessOwnerCreditScore}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  recommendedBizOwnerCreditScore?: BusinessOwnerCreditScore;
  /**
   *
   * @type {USDotScore}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  recommendedUSDotScore?: USDotScore;
  /**
   *
   * @type {Array<RecommendationFactor>}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  recommendedBizOwnerCreditScoreFactor?: Array<RecommendationFactor>;
  /**
   *
   * @type {number}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  recommendedUSDotScoreFactor?: number;
  /**
   *
   * @type {boolean}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  enableUSDOTRefresh: boolean;
  /**
   *
   * @type {boolean}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  isExistingPgrCustomer: boolean;
  /**
   *
   * @type {BusinessOwnerCreditScore}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  lnCreditScore?: BusinessOwnerCreditScore;
  /**
   *
   * @type {boolean}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  lnNoHit?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  isNewCreditScoreApp: boolean;
  /**
   *
   * @type {boolean}
   * @memberof AdmittedOperationsAdditionalInformation
   */
  hasNewCreditError?: boolean;
}
/**
 *
 * @export
 * @interface AncillaryCoverage
 */
export interface AncillaryCoverage {
  /**
   *
   * @type {CoverageType}
   * @memberof AncillaryCoverage
   */
  CoverageName: CoverageType;
  /**
   *
   * @type {number}
   * @memberof AncillaryCoverage
   */
  Limit?: number;
  /**
   *
   * @type {boolean}
   * @memberof AncillaryCoverage
   */
  IsEnabled: boolean;
  /**
   *
   * @type {Array<number>}
   * @memberof AncillaryCoverage
   */
  LimitOptions: Array<number>;
  /**
   *
   * @type {number}
   * @memberof AncillaryCoverage
   */
  Deductible: number;
  /**
   *
   * @type {Array<number>}
   * @memberof AncillaryCoverage
   */
  DeductibleOptions: Array<number>;
  /**
   *
   * @type {boolean}
   * @memberof AncillaryCoverage
   */
  IsEditable: boolean;
  /**
   *
   * @type {boolean}
   * @memberof AncillaryCoverage
   */
  IsMandatory: boolean;
  /**
   *
   * @type {boolean}
   * @memberof AncillaryCoverage
   */
  IsAppliedAtIndication: boolean;
  /**
   *
   * @type {string}
   * @memberof AncillaryCoverage
   */
  Description: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum AppState {
  AppStateIncomplete = 'AppStateIncomplete',
  AppStateComplete = 'AppStateComplete',
  AppStateQuoteGenerating = 'AppStateQuoteGenerating',
  AppStateQuoteGenerated = 'AppStateQuoteGenerated',
  AppStateUnderUwReview = 'AppStateUnderUWReview',
  AppStateUnderReferralReview = 'AppStateUnderReferralReview',
  AppStateApproved = 'AppStateApproved',
  AppStateDeclined = 'AppStateDeclined',
  AppStatePolicyCreated = 'AppStatePolicyCreated',
  AppStateClosed = 'AppStateClosed',
  AppStatePanic = 'AppStatePanic',
  AppStateBindableQuoteGenerated = 'AppStateBindableQuoteGenerated',
}

/**
 *
 * @export
 * @interface ApplicationReviewAction
 */
export interface ApplicationReviewAction {
  /**
   *
   * @type {ApplicationUserActionType}
   * @memberof ApplicationReviewAction
   */
  actionType: ApplicationUserActionType;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewAction
   */
  isEnabled: boolean;
  /**
   *
   * @type {Array<ApplicationReviewActionDisableReason>}
   * @memberof ApplicationReviewAction
   */
  disableReasons?: Array<ApplicationReviewActionDisableReason>;
}
/**
 *
 * @export
 * @interface ApplicationReviewActionDisableReason
 */
export interface ApplicationReviewActionDisableReason {
  /**
   *
   * @type {ApplicationReviewActionDisableReasonCategory}
   * @memberof ApplicationReviewActionDisableReason
   */
  category: ApplicationReviewActionDisableReasonCategory;
  /**
   *
   * @type {ApplicationReviewActionDisableReasonDataType}
   * @memberof ApplicationReviewActionDisableReason
   */
  dataType: ApplicationReviewActionDisableReasonDataType;
  /**
   *
   * @type {ApplicationReviewActionDisableReasonData}
   * @memberof ApplicationReviewActionDisableReason
   */
  data: ApplicationReviewActionDisableReasonData;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationReviewActionDisableReasonCategory {
  Authority = 'Authority',
  Miscellaneous = 'Miscellaneous',
}

/**
 *
 * @export
 * @interface ApplicationReviewActionDisableReasonData
 */
export interface ApplicationReviewActionDisableReasonData {
  /**
   *
   * @type {ApplicationReviewActionDisableReasonMarkdownData}
   * @memberof ApplicationReviewActionDisableReasonData
   */
  markdownData?: ApplicationReviewActionDisableReasonMarkdownData;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationReviewActionDisableReasonDataType {
  ApplicationReviewActionDisableReasonDataTypeMarkdown = 'ApplicationReviewActionDisableReasonDataTypeMarkdown',
}

/**
 *
 * @export
 * @interface ApplicationReviewActionDisableReasonMarkdownData
 */
export interface ApplicationReviewActionDisableReasonMarkdownData {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewActionDisableReasonMarkdownData
   */
  markdown: string;
}
/**
 *
 * @export
 * @interface ApplicationReviewAggregateCreditLimitsByCoverage
 */
export interface ApplicationReviewAggregateCreditLimitsByCoverage {
  /**
   *
   * @type {ApplicationReviewAggregateCreditLimitsByCoverageData}
   * @memberof ApplicationReviewAggregateCreditLimitsByCoverage
   */
  autoLiability?: ApplicationReviewAggregateCreditLimitsByCoverageData;
  /**
   *
   * @type {ApplicationReviewAggregateCreditLimitsByCoverageData}
   * @memberof ApplicationReviewAggregateCreditLimitsByCoverage
   */
  autoPhysicalDamage?: ApplicationReviewAggregateCreditLimitsByCoverageData;
  /**
   *
   * @type {ApplicationReviewAggregateCreditLimitsByCoverageData}
   * @memberof ApplicationReviewAggregateCreditLimitsByCoverage
   */
  motorTruckCargo?: ApplicationReviewAggregateCreditLimitsByCoverageData;
}
/**
 *
 * @export
 * @interface ApplicationReviewAggregateCreditLimitsByCoverageData
 */
export interface ApplicationReviewAggregateCreditLimitsByCoverageData {
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewAggregateCreditLimitsByCoverageData
   */
  maximum?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewAggregateCreditLimitsByCoverageData
   */
  minimum?: number;
}
/**
 *
 * @export
 * @interface ApplicationReviewAssignee
 */
export interface ApplicationReviewAssignee {
  /**
   *
   * @type {ApplicationReviewUser}
   * @memberof ApplicationReviewAssignee
   */
  current?: ApplicationReviewUser;
  /**
   *
   * @type {Array<ApplicationReviewUser>}
   * @memberof ApplicationReviewAssignee
   */
  options?: Array<ApplicationReviewUser>;
}
/**
 *
 * @export
 * @interface ApplicationReviewAssignees
 */
export interface ApplicationReviewAssignees {
  /**
   *
   * @type {ApplicationReviewAssignee}
   * @memberof ApplicationReviewAssignees
   */
  underwriter?: ApplicationReviewAssignee;
}
/**
 *
 * @export
 * @interface ApplicationReviewAssigneesForm
 */
export interface ApplicationReviewAssigneesForm {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewAssigneesForm
   */
  underwriterID?: string;
}
/**
 *
 * @export
 * @interface ApplicationReviewDetails
 */
export interface ApplicationReviewDetails {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  appReviewID: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  shortID: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  dotNo: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  companyName: string;
  /**
   *
   * @type {ApplicationReviewStatus}
   * @memberof ApplicationReviewDetails
   */
  status: ApplicationReviewStatus;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  effectiveDate: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  createdAt: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  updatedAt: string;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewDetails
   */
  alerts: number;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewDetails
   */
  isInternal: boolean;
  /**
   *
   * @type {ProgramType}
   * @memberof ApplicationReviewDetails
   */
  programType: ProgramType;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  underwriterName?: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  underwriterID?: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDetails
   */
  tsp?: string;
}
/**
 *
 * @export
 * @interface ApplicationReviewDocuments
 */
export interface ApplicationReviewDocuments {
  /**
   *
   * @type {Array<FileMetadata>}
   * @memberof ApplicationReviewDocuments
   */
  files: Array<FileMetadata>;
}
/**
 *
 * @export
 * @interface ApplicationReviewDriver
 */
export interface ApplicationReviewDriver {
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewDriver
   */
  isReviewed: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewDriver
   */
  isMVRPulled: boolean;
  /**
   *
   * @type {Array<ApplicationReviewDriverRecord>}
   * @memberof ApplicationReviewDriver
   */
  records: Array<ApplicationReviewDriverRecord>;
}
/**
 *
 * @export
 * @interface ApplicationReviewDriverRecord
 */
export interface ApplicationReviewDriverRecord {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  dlNumber: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  state: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  dateOfBirth: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  dateOfHire: string;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewDriverRecord
   */
  yearIssued?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewDriverRecord
   */
  yearsOfExp?: number;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  cdlNumber: string;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewDriverRecord
   */
  violationCount?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewDriverRecord
   */
  violationPoints?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewDriverRecord
   */
  severeViolations?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewDriverRecord
   */
  recklessDrivingMobileViolations?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewDriverRecord
   */
  atFaultViolations?: number;
  /**
   *
   * @type {MVRPullStatus}
   * @memberof ApplicationReviewDriverRecord
   */
  mvrPullStatus?: MVRPullStatus;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  mvrPullError?: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  issueDate?: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  expirationDate?: string;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewDriverRecord
   */
  isExcluded: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewDriverRecord
   */
  isOutOfState?: boolean;
  /**
   *
   * @type {Array<DriverViolation>}
   * @memberof ApplicationReviewDriverRecord
   */
  violations?: Array<DriverViolation>;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  cdlClass?: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  cdlStatus?: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  addressLine1: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewDriverRecord
   */
  addressLine2: string;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewDriverRecord
   */
  isOwner: boolean;
}
/**
 *
 * @export
 * @interface ApplicationReviewEquipment
 */
export interface ApplicationReviewEquipment {
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewEquipment
   */
  isReviewed: boolean;
  /**
   *
   * @type {Array<EquipmentUnit>}
   * @memberof ApplicationReviewEquipment
   */
  units: Array<EquipmentUnit>;
  /**
   *
   * @type {Array<EquipmentSummary>}
   * @memberof ApplicationReviewEquipment
   */
  summary: Array<EquipmentSummary>;
}
/**
 *
 * @export
 * @interface ApplicationReviewEvent
 */
export interface ApplicationReviewEvent {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewEvent
   */
  note: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewEvent
   */
  time: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewEvent
   */
  initiator: string;
  /**
   *
   * @type {ApplicationReviewEventType}
   * @memberof ApplicationReviewEvent
   */
  eventType: ApplicationReviewEventType;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationReviewEventType {
  Referred = 'Referred',
  ReferredBackToNirvana = 'ReferredBackToNirvana',
  ReferrerApproved = 'ReferrerApproved',
  ReferrerDeclined = 'ReferrerDeclined',
  NirvanaApproved = 'NirvanaApproved',
  NirvanaDeclined = 'NirvanaDeclined',
}

/**
 *
 * @export
 * @interface ApplicationReviewFlag
 */
export interface ApplicationReviewFlag {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewFlag
   */
  name: string;
  /**
   *
   * @type {ApplicationReviewFlagType}
   * @memberof ApplicationReviewFlag
   */
  type: ApplicationReviewFlagType;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewFlag
   */
  reason: string;
  /**
   *
   * @type {ApplicationReviewFlagPanel}
   * @memberof ApplicationReviewFlag
   */
  panel: ApplicationReviewFlagPanel;
  /**
   *
   * @type {Array<ApplicationReviewFlagMetadata>}
   * @memberof ApplicationReviewFlag
   */
  metadata?: Array<ApplicationReviewFlagMetadata>;
}
/**
 *
 * @export
 * @interface ApplicationReviewFlagMetadata
 */
export interface ApplicationReviewFlagMetadata {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewFlagMetadata
   */
  key: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewFlagMetadata
   */
  value: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationReviewFlagPanel {
  Coverages = 'Coverages',
  Operations = 'Operations',
  Drivers = 'Drivers',
  Equipments = 'Equipments',
  Safety = 'Safety',
  Losses = 'Losses',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationReviewFlagType {
  Referral = 'Referral',
  Error = 'Error',
  Flag = 'Flag',
}

/**
 *
 * @export
 * @interface ApplicationReviewGetActionsResponse
 */
export interface ApplicationReviewGetActionsResponse {
  /**
   *
   * @type {Array<ApplicationReviewAction>}
   * @memberof ApplicationReviewGetActionsResponse
   */
  visibleActions: Array<ApplicationReviewAction>;
}
/**
 *
 * @export
 * @interface ApplicationReviewLosses
 */
export interface ApplicationReviewLosses {
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewLosses
   */
  isReviewed: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewLosses
   */
  hasLossesOver20k?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewLosses
   */
  hasLosses?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewLosses
   */
  seriousDriverViolations: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewLosses
   */
  recentBankruptcies: boolean;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewLosses
   */
  atFaultAccidents?: number;
  /**
   *
   * @type {Array<FileMetadata>}
   * @memberof ApplicationReviewLosses
   */
  lossRunFiles?: Array<FileMetadata>;
}
/**
 *
 * @export
 * @interface ApplicationReviewNotes
 */
export interface ApplicationReviewNotes {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewNotes
   */
  notes: string;
}
/**
 *
 * @export
 * @interface ApplicationReviewOperation
 */
export interface ApplicationReviewOperation {
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewOperation
   */
  isReviewed: boolean;
  /**
   *
   * @type {OperationsYearsInBusiness}
   * @memberof ApplicationReviewOperation
   */
  yearsInBusiness: OperationsYearsInBusiness;
  /**
   *
   * @type {OperationsAdditionalInformation}
   * @memberof ApplicationReviewOperation
   */
  additionalInformation: OperationsAdditionalInformation;
  /**
   *
   * @type {Array<OperationsCommodity>}
   * @memberof ApplicationReviewOperation
   */
  commodities: Array<OperationsCommodity>;
  /**
   *
   * @type {Array<OperationsFleetHistoryInsuranceHistoryItem>}
   * @memberof ApplicationReviewOperation
   */
  fleetHistoryInsuranceHistory: Array<OperationsFleetHistoryInsuranceHistoryItem>;
  /**
   *
   * @type {AdmittedAppBusinessOwner}
   * @memberof ApplicationReviewOperation
   */
  businessOwner?: AdmittedAppBusinessOwner;
  /**
   *
   * @type {AdmittedAppTerminalLocation}
   * @memberof ApplicationReviewOperation
   */
  terminalLocation?: AdmittedAppTerminalLocation;
  /**
   *
   * @type {LNIData}
   * @memberof ApplicationReviewOperation
   */
  lniData?: LNIData;
}
/**
 *
 * @export
 * @interface ApplicationReviewPackages
 */
export interface ApplicationReviewPackages {
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewPackages
   */
  isReviewed: boolean;
  /**
   *
   * @type {JobRunStatus}
   * @memberof ApplicationReviewPackages
   */
  status?: JobRunStatus;
  /**
   *
   * @type {PackageDetails}
   * @memberof ApplicationReviewPackages
   */
  originalPackage?: PackageDetails;
  /**
   *
   * @type {PackageDetails}
   * @memberof ApplicationReviewPackages
   */
  updatedPackage?: PackageDetails;
  /**
   *
   * @type {Array<CoverageOption>}
   * @memberof ApplicationReviewPackages
   */
  options?: Array<CoverageOption>;
  /**
   *
   * @type {CoverageOptionMetadata}
   * @memberof ApplicationReviewPackages
   */
  metadata?: CoverageOptionMetadata;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewPackages
   */
  apdPercentage?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewPackages
   */
  mtcPercentage?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewPackages
   */
  alPercentage?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewPackages
   */
  glPercentage?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewPackages
   */
  safetyCredits?: number;
  /**
   *
   * @type {Array<AncillaryCoverage>}
   * @memberof ApplicationReviewPackages
   */
  ancillaryCoverages?: Array<AncillaryCoverage>;
  /**
   *
   * @type {PaymentPlan}
   * @memberof ApplicationReviewPackages
   */
  paymentPlan?: PaymentPlan;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewPackages
   */
  isAPDMTCDeductibleCombined?: boolean;
  /**
   *
   * @type {ApplicationReviewAggregateCreditLimitsByCoverage}
   * @memberof ApplicationReviewPackages
   */
  aggregateCreditLimitsByCoverage?: ApplicationReviewAggregateCreditLimitsByCoverage;
}
/**
 *
 * @export
 * @interface ApplicationReviewSafety
 */
export interface ApplicationReviewSafety {
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewSafety
   */
  isReviewed: boolean;
  /**
   *
   * @type {SafetyRating}
   * @memberof ApplicationReviewSafety
   */
  safetyRating: SafetyRating;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSafety
   */
  effectiveDate?: string;
  /**
   *
   * @type {Array<BasicScoreThresholdItem>}
   * @memberof ApplicationReviewSafety
   */
  basicScores: Array<BasicScoreThresholdItem>;
  /**
   *
   * @type {Array<SevereViolationsItem>}
   * @memberof ApplicationReviewSafety
   */
  severeViolations: Array<SevereViolationsItem>;
  /**
   *
   * @type {Array<OOSViolationsDataItem>}
   * @memberof ApplicationReviewSafety
   */
  oOSSummary: Array<OOSViolationsDataItem>;
  /**
   *
   * @type {CrashRecordSummary}
   * @memberof ApplicationReviewSafety
   */
  crashRecordsSummary: CrashRecordSummary;
  /**
   *
   * @type {Array<CrashRecordHistoryItem>}
   * @memberof ApplicationReviewSafety
   */
  crashRecords: Array<CrashRecordHistoryItem>;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewSafety
   */
  safetyCredit?: number;
}
/**
 *
 * @export
 * @interface ApplicationReviewSafetyScoreFormV2
 */
export interface ApplicationReviewSafetyScoreFormV2 {
  /**
   *
   * @type {ApplicationReviewWidgetMeta}
   * @memberof ApplicationReviewSafetyScoreFormV2
   */
  meta?: ApplicationReviewWidgetMeta;
  /**
   *
   * @type {UpdateSafetyScoreRequest}
   * @memberof ApplicationReviewSafetyScoreFormV2
   */
  form?: UpdateSafetyScoreRequest;
}
/**
 *
 * @export
 * @interface ApplicationReviewSafetyScoreFormV2AllOf
 */
export interface ApplicationReviewSafetyScoreFormV2AllOf {
  /**
   *
   * @type {UpdateSafetyScoreRequest}
   * @memberof ApplicationReviewSafetyScoreFormV2AllOf
   */
  form?: UpdateSafetyScoreRequest;
}
/**
 *
 * @export
 * @interface ApplicationReviewSafetyScoreV2
 */
export interface ApplicationReviewSafetyScoreV2 {
  /**
   *
   * @type {ApplicationReviewWidgetMeta}
   * @memberof ApplicationReviewSafetyScoreV2
   */
  meta?: ApplicationReviewWidgetMeta;
  /**
   *
   * @type {FeatureStatus}
   * @memberof ApplicationReviewSafetyScoreV2
   */
  status: FeatureStatus;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewSafetyScoreV2
   */
  totalVinCount: number;
  /**
   *
   * @type {RiskScore}
   * @memberof ApplicationReviewSafetyScoreV2
   */
  riskScore: RiskScore;
  /**
   *
   * @type {ApplicationReviewWidgetVersion}
   * @memberof ApplicationReviewSafetyScoreV2
   */
  version: ApplicationReviewWidgetVersion;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewSafetyScoreV2
   */
  isShortHaul?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewSafetyScoreV2
   */
  canEdit: boolean;
}
/**
 *
 * @export
 * @interface ApplicationReviewSafetyScoreV2AllOf
 */
export interface ApplicationReviewSafetyScoreV2AllOf {
  /**
   *
   * @type {FeatureStatus}
   * @memberof ApplicationReviewSafetyScoreV2AllOf
   */
  status: FeatureStatus;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewSafetyScoreV2AllOf
   */
  totalVinCount: number;
  /**
   *
   * @type {RiskScore}
   * @memberof ApplicationReviewSafetyScoreV2AllOf
   */
  riskScore: RiskScore;
  /**
   *
   * @type {ApplicationReviewWidgetVersion}
   * @memberof ApplicationReviewSafetyScoreV2AllOf
   */
  version: ApplicationReviewWidgetVersion;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewSafetyScoreV2AllOf
   */
  isShortHaul?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewSafetyScoreV2AllOf
   */
  canEdit: boolean;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationReviewStatus {
  UnderUwReview = 'UnderUWReview',
  UnderReferralReview = 'UnderReferralReview',
  Approved = 'Approved',
  Declined = 'Declined',
  Closed = 'Closed',
  Unhandled = 'Unhandled',
  Stale = 'Stale',
}

/**
 *
 * @export
 * @interface ApplicationReviewSummary
 */
export interface ApplicationReviewSummary {
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewSummary
   */
  dotNumber: number;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  companyName: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  effectiveDate: string;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewSummary
   */
  numberOfPUs: number;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  agencyName: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  producerName: string;
  /**
   *
   * @type {Address}
   * @memberof ApplicationReviewSummary
   */
  address: Address;
  /**
   *
   * @type {Address}
   * @memberof ApplicationReviewSummary
   */
  fmcsaAddress?: Address;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  agentPhoneNumber: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  agentEmail: string;
  /**
   *
   * @type {ApplicationReviewStatus}
   * @memberof ApplicationReviewSummary
   */
  state: ApplicationReviewStatus;
  /**
   *
   * @type {AppState}
   * @memberof ApplicationReviewSummary
   */
  appState?: AppState;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  applicationID: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  applicationShortID: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  tspConnectionHandleId?: string;
  /**
   *
   * @type {ProgramType}
   * @memberof ApplicationReviewSummary
   */
  programType: ProgramType;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  tspName?: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  underwriterName: string;
  /**
   *
   * @type {ApplicationReviewAssignees}
   * @memberof ApplicationReviewSummary
   */
  assignees: ApplicationReviewAssignees;
  /**
   *
   * @type {Array<PanelReviewStatus>}
   * @memberof ApplicationReviewSummary
   */
  panelReviewStatuses: Array<PanelReviewStatus>;
  /**
   *
   * @type {PreTelematicsQuoteState}
   * @memberof ApplicationReviewSummary
   */
  preTelematicsQuoteState?: PreTelematicsQuoteState;
  /**
   *
   * @type {Array<DuplicateApplications>}
   * @memberof ApplicationReviewSummary
   */
  duplicateApplications: Array<DuplicateApplications>;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewSummary
   */
  createdAt?: string;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewSummary
   */
  allowSafetyCreditOverrideDueToPreTelematics: boolean;
  /**
   *
   * @type {boolean}
   * @memberof ApplicationReviewSummary
   */
  isExpressLaneApplication: boolean;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationReviewTab {
  ApplicationReviewTabAll = 'ApplicationReviewTabAll',
  ApplicationReviewTabIncomplete = 'ApplicationReviewTabIncomplete',
  ApplicationReviewTabReadyForReview = 'ApplicationReviewTabReadyForReview',
  ApplicationReviewTabApproved = 'ApplicationReviewTabApproved',
  ApplicationReviewTabReadyToBind = 'ApplicationReviewTabReadyToBind',
  ApplicationReviewTabDeclined = 'ApplicationReviewTabDeclined',
  ApplicationReviewTabStale = 'ApplicationReviewTabStale',
  ApplicationReviewTabClosed = 'ApplicationReviewTabClosed',
  ApplicationReviewTabInternal = 'ApplicationReviewTabInternal',
  ApplicationReviewTabReferral = 'ApplicationReviewTabReferral',
  ApplicationReviewTabPending = 'ApplicationReviewTabPending',
  ApplicationReviewTabPreTelematicsExperiment = 'ApplicationReviewTabPreTelematicsExperiment',
}

/**
 *
 * @export
 * @interface ApplicationReviewUser
 */
export interface ApplicationReviewUser {
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewUser
   */
  id: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewUser
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewUser
   */
  email: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewUser
   */
  iconUrl?: string;
}
/**
 *
 * @export
 * @interface ApplicationReviewWidgetBase
 */
export interface ApplicationReviewWidgetBase {
  /**
   *
   * @type {ApplicationReviewWidgetMeta}
   * @memberof ApplicationReviewWidgetBase
   */
  meta?: ApplicationReviewWidgetMeta;
}
/**
 *
 * @export
 * @interface ApplicationReviewWidgetCoverageMeta
 */
export interface ApplicationReviewWidgetCoverageMeta {
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewWidgetCoverageMeta
   */
  merit?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewWidgetCoverageMeta
   */
  credit?: number;
}
/**
 *
 * @export
 * @interface ApplicationReviewWidgetMeta
 */
export interface ApplicationReviewWidgetMeta {
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewWidgetMeta
   */
  merit?: number;
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewWidgetMeta
   */
  credit?: number;
  /**
   *
   * @type {ApplicationReviewWidgetCoverageMeta}
   * @memberof ApplicationReviewWidgetMeta
   */
  autoLiability?: ApplicationReviewWidgetCoverageMeta;
  /**
   *
   * @type {ApplicationReviewWidgetCoverageMeta}
   * @memberof ApplicationReviewWidgetMeta
   */
  autoPhysicalDamage?: ApplicationReviewWidgetCoverageMeta;
  /**
   *
   * @type {ApplicationReviewWidgetCoverageMeta}
   * @memberof ApplicationReviewWidgetMeta
   */
  generalLiability?: ApplicationReviewWidgetCoverageMeta;
  /**
   *
   * @type {ApplicationReviewWidgetCoverageMeta}
   * @memberof ApplicationReviewWidgetMeta
   */
  motorTruckCargo?: ApplicationReviewWidgetCoverageMeta;
  /**
   *
   * @type {Array<ApplicationReviewWidgetMetaFlag>}
   * @memberof ApplicationReviewWidgetMeta
   */
  flags?: Array<ApplicationReviewWidgetMetaFlag>;
}
/**
 *
 * @export
 * @interface ApplicationReviewWidgetMetaFlag
 */
export interface ApplicationReviewWidgetMetaFlag {
  /**
   *
   * @type {number}
   * @memberof ApplicationReviewWidgetMetaFlag
   */
  weight: number;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewWidgetMetaFlag
   */
  title: string;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewWidgetMetaFlag
   */
  description?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationReviewWidgetVersion {
  V1 = 'V1',
  V2 = 'V2',
}

/**
 *
 * @export
 * @interface ApplicationReviewsWithPaginationResponse
 */
export interface ApplicationReviewsWithPaginationResponse {
  /**
   *
   * @type {Array<ApplicationReviewDetails>}
   * @memberof ApplicationReviewsWithPaginationResponse
   */
  appReviews: Array<ApplicationReviewDetails>;
  /**
   *
   * @type {string}
   * @memberof ApplicationReviewsWithPaginationResponse
   */
  cursor?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ApplicationUserActionType {
  UserActionApproval = 'UserActionApproval',
  UserActionDecline = 'UserActionDecline',
  UserActionClose = 'UserActionClose',
  UserActionViewReleaseDocuments = 'UserActionViewReleaseDocuments',
  UserActionReopenReview = 'UserActionReopenReview',
}

/**
 *
 * @export
 * @interface ApproveApplicationReviewForm
 */
export interface ApproveApplicationReviewForm {
  /**
   *
   * @type {string}
   * @memberof ApproveApplicationReviewForm
   */
  note: string;
}
/**
 *
 * @export
 * @interface BasicScoreThresholdItem
 */
export interface BasicScoreThresholdItem {
  /**
   *
   * @type {string}
   * @memberof BasicScoreThresholdItem
   */
  category: string;
  /**
   *
   * @type {number}
   * @memberof BasicScoreThresholdItem
   */
  score?: number;
  /**
   *
   * @type {number}
   * @memberof BasicScoreThresholdItem
   */
  threshold: number;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum BusinessOwnerCreditScore {
  CreditScoreB0 = 'CreditScoreB0',
  CreditScoreA1 = 'CreditScoreA1',
  CreditScoreA2 = 'CreditScoreA2',
  CreditScoreA3 = 'CreditScoreA3',
  CreditScoreB1 = 'CreditScoreB1',
  CreditScoreB2 = 'CreditScoreB2',
  CreditScoreB3 = 'CreditScoreB3',
  CreditScoreC0 = 'CreditScoreC0',
  CreditScoreC1 = 'CreditScoreC1',
  CreditScoreC2 = 'CreditScoreC2',
  CreditScoreC3 = 'CreditScoreC3',
  CreditScoreD0 = 'CreditScoreD0',
  CreditScoreD1 = 'CreditScoreD1',
  CreditScoreD2 = 'CreditScoreD2',
  CreditScoreE0 = 'CreditScoreE0',
  CreditScoreE1 = 'CreditScoreE1',
  CreditScoreE2 = 'CreditScoreE2',
  CreditScoreI1 = 'CreditScoreI1',
  CreditScoreNa = 'CreditScoreNA',
  CreditScoreO1 = 'CreditScoreO1',
  CreditScoreP1 = 'CreditScoreP1',
  CreditScoreQ1 = 'CreditScoreQ1',
  CreditScoreT1 = 'CreditScoreT1',
  CreditScoreT3 = 'CreditScoreT3',
  CreditScoreT4 = 'CreditScoreT4',
  CreditScoreT5 = 'CreditScoreT5',
  CreditScoreX1 = 'CreditScoreX1',
  CreditScoreX3 = 'CreditScoreX3',
  CreditScoreX4 = 'CreditScoreX4',
  CreditScoreX5 = 'CreditScoreX5',
  CreditScoreXx = 'CreditScoreXX',
}

/**
 *
 * @export
 * @interface CloseApplicationReviewForm
 */
export interface CloseApplicationReviewForm {
  /**
   *
   * @type {string}
   * @memberof CloseApplicationReviewForm
   */
  note: string;
  /**
   *
   * @type {string}
   * @memberof CloseApplicationReviewForm
   */
  primaryReason?: string;
  /**
   *
   * @type {string}
   * @memberof CloseApplicationReviewForm
   */
  secondaryReason?: string;
  /**
   *
   * @type {string}
   * @memberof CloseApplicationReviewForm
   */
  winCarrier?: string;
}
/**
 *
 * @export
 * @interface CoverageDetails
 */
export interface CoverageDetails {
  /**
   *
   * @type {CoverageType}
   * @memberof CoverageDetails
   */
  coverageType: CoverageType;
  /**
   *
   * @type {string}
   * @memberof CoverageDetails
   */
  label: string;
  /**
   *
   * @type {number}
   * @memberof CoverageDetails
   */
  limit?: number;
  /**
   *
   * @type {number}
   * @memberof CoverageDetails
   */
  deductible?: number;
  /**
   *
   * @type {number}
   * @memberof CoverageDetails
   */
  premium?: number;
  /**
   *
   * @type {number}
   * @memberof CoverageDetails
   */
  premiumPerUnit?: number;
  /**
   *
   * @type {boolean}
   * @memberof CoverageDetails
   */
  isRequired: boolean;
  /**
   *
   * @type {number}
   * @memberof CoverageDetails
   */
  premiumNew?: number;
}
/**
 *
 * @export
 * @interface CoverageOption
 */
export interface CoverageOption {
  /**
   *
   * @type {CoverageType}
   * @memberof CoverageOption
   */
  coverage: CoverageType;
  /**
   *
   * @type {boolean}
   * @memberof CoverageOption
   */
  isRequired: boolean;
  /**
   *
   * @type {Array<number>}
   * @memberof CoverageOption
   */
  limitOptions?: Array<number>;
  /**
   *
   * @type {Array<number>}
   * @memberof CoverageOption
   */
  deductibleOptions?: Array<number>;
  /**
   *
   * @type {number}
   * @memberof CoverageOption
   */
  defaultLimit?: number;
  /**
   *
   * @type {number}
   * @memberof CoverageOption
   */
  limit?: number;
  /**
   *
   * @type {number}
   * @memberof CoverageOption
   */
  defaultDeductible?: number;
  /**
   *
   * @type {number}
   * @memberof CoverageOption
   */
  deductible?: number;
}
/**
 *
 * @export
 * @interface CoverageOptionMetadata
 */
export interface CoverageOptionMetadata {
  /**
   *
   * @type {Array<CoverageType>}
   * @memberof CoverageOptionMetadata
   */
  mandatoryCoverages: Array<CoverageType>;
  /**
   *
   * @type {boolean}
   * @memberof CoverageOptionMetadata
   */
  hasReefer: boolean;
  /**
   *
   * @type {Array<string>}
   * @memberof CoverageOptionMetadata
   */
  targetCommodities?: Array<string>;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum CoverageType {
  CoverageAutoLiability = 'CoverageAutoLiability',
  CoverageAutoPhysicalDamage = 'CoverageAutoPhysicalDamage',
  CoverageGeneralLiability = 'CoverageGeneralLiability',
  CoverageMotorTruckCargo = 'CoverageMotorTruckCargo',
  CoverageTrailerInterchange = 'CoverageTrailerInterchange',
  CoverageUninsuredMotoristBodilyInjury = 'CoverageUninsuredMotoristBodilyInjury',
  CoverageUnderinsuredMotoristBodilyInjury = 'CoverageUnderinsuredMotoristBodilyInjury',
  CoverageUninsuredMotoristPropertyDamage = 'CoverageUninsuredMotoristPropertyDamage',
  CoverageUnderinsuredMotoristPropertyDamage = 'CoverageUnderinsuredMotoristPropertyDamage',
  CoveragePersonalInjuryProtection = 'CoveragePersonalInjuryProtection',
  CoverageMedicalPayments = 'CoverageMedicalPayments',
  CoverageUmuimBodilyInjury = 'CoverageUMUIMBodilyInjury',
  CoverageUmuimPropertyDamage = 'CoverageUMUIMPropertyDamage',
  CoveragePropertyProtectionInsurance = 'CoveragePropertyProtectionInsurance',
  CoveragePersonalInjuryProtectionBasic = 'CoveragePersonalInjuryProtectionBasic',
  CoveragePersonalInjuryProtectionIncreased = 'CoveragePersonalInjuryProtectionIncreased',
  CoveragePipExcessAttendantCare = 'CoveragePIPExcessAttendantCare',
  CoverageReefer = 'CoverageReefer',
  CoverageReeferWithHumanError = 'CoverageReeferWithHumanError',
  CoverageEnhancedPackageTowingLimit = 'CoverageEnhancedPackageTowingLimit',
  CoverageGuestPersonalInjuryProtection = 'CoverageGuestPersonalInjuryProtection',
  CoverageReeferWithoutHumanError = 'CoverageReeferWithoutHumanError',
  CoverageStopGap = 'CoverageStopGap',
  CoverageBroadenedPollution = 'CoverageBroadenedPollution',
  CoverageBlanketAdditional = 'CoverageBlanketAdditional',
  CoverageUiia = 'CoverageUIIA',
  CoverageBlanketWaiverOfSubrogation = 'CoverageBlanketWaiverOfSubrogation',
  CoverageGlBlanketWaiverOfSubrogation = 'CoverageGLBlanketWaiverOfSubrogation',
  CoverageGlBlanketAdditional = 'CoverageGLBlanketAdditional',
  CoverageMtcBlanketWaiverOfSubrogation = 'CoverageMTCBlanketWaiverOfSubrogation',
  CoverageMtcBlanketAdditional = 'CoverageMTCBlanketAdditional',
  CoverageUmuim = 'CoverageUMUIM',
  CoverageUmbiuimbi = 'CoverageUMBIUIMBI',
  CoverageUm = 'CoverageUM',
  CoverageUim = 'CoverageUIM',
  CoverageTerrorism = 'CoverageTerrorism',
  CoverageDebrisRemoval = 'CoverageDebrisRemoval',
  CoverageNonOwnedTrailer = 'CoverageNonOwnedTrailer',
  CoverageApduiia = 'CoverageAPDUIIA',
  CoverageMtcuiia = 'CoverageMTCUIIA',
  CoverageApdTrailerInterchange = 'CoverageAPDTrailerInterchange',
  CoverageMtcTrailerInterchange = 'CoverageMTCTrailerInterchange',
  CoverageApdNonOwnedTrailer = 'CoverageAPDNonOwnedTrailer',
  CoverageMtcNonOwnedTrailer = 'CoverageMTCNonOwnedTrailer',
  CoverageUnattendedTruck = 'CoverageUnattendedTruck',
  CoverageEarnedFreight = 'CoverageEarnedFreight',
  CoverageRentalReimbursement = 'CoverageRentalReimbursement',
  CoverageTowingLaborAndStorage = 'CoverageTowingLaborAndStorage',
  CoverageHiredAuto = 'CoverageHiredAuto',
  CoverageCargoAtScheduledTerminals = 'CoverageCargoAtScheduledTerminals',
  CoverageCargoTrailerInterchange = 'CoverageCargoTrailerInterchange',
  CoverageLossMitigationExpenses = 'CoverageLossMitigationExpenses',
  CoverageMiscellaneousEquipment = 'CoverageMiscellaneousEquipment',
  CoveragePollutantCleanupAndRemoval = 'CoveragePollutantCleanupAndRemoval',
  CoverageBlanketAdditionalPnc = 'CoverageBlanketAdditionalPNC',
  CoverageNonOwnedAuto = 'CoverageNonOwnedAuto',
  CoverageWorkLossBenefits = 'CoverageWorkLossBenefits',
  CoverageFuneralExpenseBenefits = 'CoverageFuneralExpenseBenefits',
  CoverageAccidentalDeathBenefits = 'CoverageAccidentalDeathBenefits',
  CoverageExtraordinaryMedicalBenefits = 'CoverageExtraordinaryMedicalBenefits',
  CoverageMedicalExpenseBenefits = 'CoverageMedicalExpenseBenefits',
  CoverageHiredAutoLiab = 'CoverageHiredAutoLiab',
  CoverageHiredAutoPd = 'CoverageHiredAutoPD',
}

/**
 *
 * @export
 * @interface CrashRecordHistoryItem
 */
export interface CrashRecordHistoryItem {
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  date: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  location: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  usState: string;
  /**
   *
   * @type {number}
   * @memberof CrashRecordHistoryItem
   */
  fatalities: number;
  /**
   *
   * @type {number}
   * @memberof CrashRecordHistoryItem
   */
  injuries: number;
  /**
   *
   * @type {boolean}
   * @memberof CrashRecordHistoryItem
   */
  towAway: boolean;
  /**
   *
   * @type {boolean}
   * @memberof CrashRecordHistoryItem
   */
  notPreventable?: boolean;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  city?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  cityCode?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  countyCode?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  vin?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  vehicleLicenseNumber?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  cargoBodyType?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  vehicleType?: string;
  /**
   *
   * @type {boolean}
   * @memberof CrashRecordHistoryItem
   */
  vehicleHazmatPlacard?: boolean;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  lightCondition?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  weatherCondition?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  roadSurfaceCondition?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  trafficWay?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  accessControl?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  investigatingAgency?: string;
  /**
   *
   * @type {string}
   * @memberof CrashRecordHistoryItem
   */
  officerBadge?: string;
  /**
   *
   * @type {boolean}
   * @memberof CrashRecordHistoryItem
   */
  stateRecordable?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof CrashRecordHistoryItem
   */
  federalRecordable?: boolean;
}
/**
 *
 * @export
 * @interface CrashRecordSummary
 */
export interface CrashRecordSummary {
  /**
   *
   * @type {number}
   * @memberof CrashRecordSummary
   */
  fatal: number;
  /**
   *
   * @type {number}
   * @memberof CrashRecordSummary
   */
  injury: number;
  /**
   *
   * @type {number}
   * @memberof CrashRecordSummary
   */
  tow: number;
  /**
   *
   * @type {number}
   * @memberof CrashRecordSummary
   */
  totalReportable: number;
  /**
   *
   * @type {number}
   * @memberof CrashRecordSummary
   */
  accidentToPowerUnitRatio: number;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum DecisionType {
  Agree = 'Agree',
  Disagree = 'Disagree',
}

/**
 *
 * @export
 * @interface DeclineApplicationReviewForm
 */
export interface DeclineApplicationReviewForm {
  /**
   *
   * @type {string}
   * @memberof DeclineApplicationReviewForm
   */
  note: string;
  /**
   *
   * @type {string}
   * @memberof DeclineApplicationReviewForm
   */
  primaryReason?: string;
  /**
   *
   * @type {string}
   * @memberof DeclineApplicationReviewForm
   */
  secondaryReason?: string;
}
/**
 *
 * @export
 * @interface DriverViolation
 */
export interface DriverViolation {
  /**
   *
   * @type {string}
   * @memberof DriverViolation
   */
  code?: string;
  /**
   *
   * @type {string}
   * @memberof DriverViolation
   */
  description?: string;
  /**
   *
   * @type {string}
   * @memberof DriverViolation
   */
  type?: string;
  /**
   *
   * @type {string}
   * @memberof DriverViolation
   */
  date?: string;
  /**
   *
   * @type {number}
   * @memberof DriverViolation
   */
  points?: number;
  /**
   *
   * @type {boolean}
   * @memberof DriverViolation
   */
  movingViolation?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof DriverViolation
   */
  severeViolation?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof DriverViolation
   */
  recklessDrivingMobileDeviceViolation?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof DriverViolation
   */
  atFaultViolation?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof DriverViolation
   */
  isExpired?: boolean;
  /**
   *
   * @type {number}
   * @memberof DriverViolation
   */
  originalPoints?: number;
  /**
   *
   * @type {boolean}
   * @memberof DriverViolation
   */
  isManuallyAdded?: boolean;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum DuplicateApplicationTag {
  FlexQuote = 'FlexQuote',
}

/**
 *
 * @export
 * @interface DuplicateApplications
 */
export interface DuplicateApplications {
  /**
   *
   * @type {string}
   * @memberof DuplicateApplications
   */
  shortId: string;
  /**
   *
   * @type {Array<DuplicateApplicationTag>}
   * @memberof DuplicateApplications
   */
  tags: Array<DuplicateApplicationTag>;
}
/**
 *
 * @export
 * @interface EquipmentSummary
 */
export interface EquipmentSummary {
  /**
   *
   * @type {string}
   * @memberof EquipmentSummary
   */
  unitType: string;
  /**
   *
   * @type {number}
   * @memberof EquipmentSummary
   */
  unitCount: number;
  /**
   *
   * @type {number}
   * @memberof EquipmentSummary
   */
  tiv?: number;
  /**
   *
   * @type {number}
   * @memberof EquipmentSummary
   */
  averageYear: number;
}
/**
 *
 * @export
 * @interface EquipmentUnit
 */
export interface EquipmentUnit {
  /**
   *
   * @type {string}
   * @memberof EquipmentUnit
   */
  vin: string;
  /**
   *
   * @type {string}
   * @memberof EquipmentUnit
   */
  make: string;
  /**
   *
   * @type {string}
   * @memberof EquipmentUnit
   */
  model: string;
  /**
   *
   * @type {number}
   * @memberof EquipmentUnit
   */
  year: number;
  /**
   *
   * @type {string}
   * @memberof EquipmentUnit
   */
  weightClass: string;
  /**
   *
   * @type {string}
   * @memberof EquipmentUnit
   */
  vehicleType: string;
  /**
   *
   * @type {string}
   * @memberof EquipmentUnit
   */
  vehicleClass?: string;
  /**
   *
   * @type {number}
   * @memberof EquipmentUnit
   */
  value?: number;
}
/**
 *
 * @export
 * @interface ErrorMessage
 */
export interface ErrorMessage {
  /**
   *
   * @type {string}
   * @memberof ErrorMessage
   */
  message: string;
  /**
   *
   * @type {number}
   * @memberof ErrorMessage
   */
  code: number;
}
/**
 *
 * @export
 * @interface ExpressLaneFeedbackGetResponse
 */
export interface ExpressLaneFeedbackGetResponse {
  /**
   *
   * @type {DecisionType}
   * @memberof ExpressLaneFeedbackGetResponse
   */
  overall_decision?: DecisionType;
  /**
   *
   * @type {string}
   * @memberof ExpressLaneFeedbackGetResponse
   */
  overall_reason?: string;
  /**
   *
   * @type {Array<ExpressLanePanelWiseDecision>}
   * @memberof ExpressLaneFeedbackGetResponse
   */
  panel_wise_decision: Array<ExpressLanePanelWiseDecision>;
}
/**
 *
 * @export
 * @interface ExpressLaneFeedbackPatchResponse
 */
export interface ExpressLaneFeedbackPatchResponse {
  /**
   *
   * @type {boolean}
   * @memberof ExpressLaneFeedbackPatchResponse
   */
  success: boolean;
}
/**
 *
 * @export
 * @interface ExpressLanePanelWiseDecision
 */
export interface ExpressLanePanelWiseDecision {
  /**
   *
   * @type {string}
   * @memberof ExpressLanePanelWiseDecision
   */
  panelName?: string;
  /**
   *
   * @type {DecisionType}
   * @memberof ExpressLanePanelWiseDecision
   */
  decision?: DecisionType;
  /**
   *
   * @type {string}
   * @memberof ExpressLanePanelWiseDecision
   */
  reason?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum FeatureStatus {
  Success = 'Success',
  Processing = 'Processing',
  NoDataAvailable = 'NoDataAvailable',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum FileDestinationGroup {
  FileDestinationGroupQuoting = 'FileDestinationGroupQuoting',
  FileDestinationGroupUnderwriting = 'FileDestinationGroupUnderwriting',
  FileDestinationGroupForms = 'FileDestinationGroupForms',
  FileDestinationGroupClaims = 'FileDestinationGroupClaims',
}

/**
 *
 * @export
 * @interface FileHandle
 */
export interface FileHandle {
  /**
   *
   * @type {string}
   * @memberof FileHandle
   */
  handle: string;
}
/**
 *
 * @export
 * @interface FileMetadata
 */
export interface FileMetadata {
  /**
   *
   * @type {string}
   * @memberof FileMetadata
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof FileMetadata
   */
  handle?: string;
  /**
   *
   * @type {FileType}
   * @memberof FileMetadata
   */
  FileType?: FileType;
  /**
   *
   * @type {FileDestinationGroup}
   * @memberof FileMetadata
   */
  FileDestinationGroup?: FileDestinationGroup;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum FileType {
  FileTypeEquipmentList = 'FileTypeEquipmentList',
  FileTypeDriversList = 'FileTypeDriversList',
  FileTypeLossRun = 'FileTypeLossRun',
  FileTypeUwDocument = 'FileTypeUwDocument',
  FileTypePdfForms = 'FileTypePDFForms',
  FileTypeIftaFile = 'FileTypeIFTAFile',
  FileTypeClaimDocument = 'FileTypeClaimDocument',
  FileTypeAgentAdditionalFiles = 'FileTypeAgentAdditionalFiles',
}

/**
 *
 * @export
 * @interface InlineObject
 */
export interface InlineObject {
  /**
   *
   * @type {DecisionType}
   * @memberof InlineObject
   */
  overall_decision?: DecisionType;
  /**
   *
   * @type {string}
   * @memberof InlineObject
   */
  overall_reason?: string;
  /**
   *
   * @type {Array<ExpressLanePanelWiseDecision>}
   * @memberof InlineObject
   */
  panel_wise_decision: Array<ExpressLanePanelWiseDecision>;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum JobRunStatus {
  Running = 'running',
  Failure = 'failure',
  Success = 'success',
  Unknown = 'unknown',
}

/**
 *
 * @export
 * @interface LNIData
 */
export interface LNIData {
  /**
   *
   * @type {number}
   * @memberof LNIData
   */
  monthsInBusiness?: number;
  /**
   *
   * @type {number}
   * @memberof LNIData
   */
  yearsInBusiness?: number;
  /**
   *
   * @type {number}
   * @memberof LNIData
   */
  currentCarrierYears?: number;
  /**
   *
   * @type {number}
   * @memberof LNIData
   */
  continuousCoverageYears?: number;
}
/**
 *
 * @export
 * @interface MVRFlag
 */
export interface MVRFlag {
  /**
   *
   * @type {boolean}
   * @memberof MVRFlag
   */
  PullMvr: boolean;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum MVRPullStatus {
  Success = 'Success',
  Error = 'Error',
  NotRequested = 'NotRequested',
}

/**
 *
 * @export
 * @interface OOSViolationsDataItem
 */
export interface OOSViolationsDataItem {
  /**
   *
   * @type {string}
   * @memberof OOSViolationsDataItem
   */
  category: OOSViolationsDataItemCategoryEnum;
  /**
   *
   * @type {number}
   * @memberof OOSViolationsDataItem
   */
  inspectionCount: number;
  /**
   *
   * @type {number}
   * @memberof OOSViolationsDataItem
   */
  oosCount: number;
  /**
   *
   * @type {number}
   * @memberof OOSViolationsDataItem
   */
  nationalAveragePercentage: number;
  /**
   *
   * @type {number}
   * @memberof OOSViolationsDataItem
   */
  oosPercentage: number;
}

/**
 * @export
 * @enum {string}
 */
export enum OOSViolationsDataItemCategoryEnum {
  Overall = 'overall',
  Driver = 'driver',
  Vehicle = 'vehicle',
  Hazmat = 'hazmat',
}

/**
 *
 * @export
 * @interface OperationsAdditionalInformation
 */
export interface OperationsAdditionalInformation {
  /**
   *
   * @type {OperationsOperatingClasses}
   * @memberof OperationsAdditionalInformation
   */
  nrbAdditionalInformation?: OperationsOperatingClasses;
  /**
   *
   * @type {AdmittedOperationsAdditionalInformation}
   * @memberof OperationsAdditionalInformation
   */
  admittedAdditionalInformation?: AdmittedOperationsAdditionalInformation;
}
/**
 *
 * @export
 * @interface OperationsCommodity
 */
export interface OperationsCommodity {
  /**
   *
   * @type {string}
   * @memberof OperationsCommodity
   */
  name: string;
  /**
   *
   * @type {string}
   * @memberof OperationsCommodity
   */
  category: string;
  /**
   *
   * @type {number}
   * @memberof OperationsCommodity
   */
  percentage: number;
}
/**
 *
 * @export
 * @interface OperationsFleetHistoryInsuranceHistoryItem
 */
export interface OperationsFleetHistoryInsuranceHistoryItem {
  /**
   *
   * @type {string}
   * @memberof OperationsFleetHistoryInsuranceHistoryItem
   */
  insuranceType?: string;
  /**
   *
   * @type {string}
   * @memberof OperationsFleetHistoryInsuranceHistoryItem
   */
  carrier: string;
  /**
   *
   * @type {string}
   * @memberof OperationsFleetHistoryInsuranceHistoryItem
   */
  startDate: string;
  /**
   *
   * @type {string}
   * @memberof OperationsFleetHistoryInsuranceHistoryItem
   */
  endDate?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum OperationsMaxRadiusOfOperations {
  MaxRadiusOfOperationInvalid = 'MaxRadiusOfOperationInvalid',
  MaxRadiusOfOperation50 = 'MaxRadiusOfOperation50',
  MaxRadiusOfOperation100 = 'MaxRadiusOfOperation100',
  MaxRadiusOfOperation200 = 'MaxRadiusOfOperation200',
  MaxRadiusOfOperation300 = 'MaxRadiusOfOperation300',
  MaxRadiusOfOperation500 = 'MaxRadiusOfOperation500',
  MaxRadiusOfOperation999plus = 'MaxRadiusOfOperation999plus',
}

/**
 *
 * @export
 * @interface OperationsOperatingClasses
 */
export interface OperationsOperatingClasses {
  /**
   *
   * @type {string}
   * @memberof OperationsOperatingClasses
   */
  primaryOperatingClass: string;
  /**
   *
   * @type {boolean}
   * @memberof OperationsOperatingClasses
   */
  hasRestrictedCommodities?: boolean;
}
/**
 *
 * @export
 * @interface OperationsYearsInBusiness
 */
export interface OperationsYearsInBusiness {
  /**
   *
   * @type {number}
   * @memberof OperationsYearsInBusiness
   */
  value: number;
  /**
   *
   * @type {number}
   * @memberof OperationsYearsInBusiness
   */
  years: number;
  /**
   *
   * @type {number}
   * @memberof OperationsYearsInBusiness
   */
  months: number;
}
/**
 *
 * @export
 * @interface PackageDetails
 */
export interface PackageDetails {
  /**
   *
   * @type {number}
   * @memberof PackageDetails
   */
  totalPremium: number;
  /**
   *
   * @type {Array<CoverageDetails>}
   * @memberof PackageDetails
   */
  primaryCovs: Array<CoverageDetails>;
  /**
   *
   * @type {Array<CoverageDetails>}
   * @memberof PackageDetails
   */
  ancillaryCovs: Array<CoverageDetails>;
  /**
   *
   * @type {number}
   * @memberof PackageDetails
   */
  TIV: number;
  /**
   *
   * @type {number}
   * @memberof PackageDetails
   */
  TIVPercentage: number;
  /**
   *
   * @type {number}
   * @memberof PackageDetails
   */
  subTotalPremium: number;
  /**
   *
   * @type {number}
   * @memberof PackageDetails
   */
  flatCharges: number;
  /**
   *
   * @type {number}
   * @memberof PackageDetails
   */
  stateSurcharge: number;
}
/**
 *
 * @export
 * @interface PanelReviewStatus
 */
export interface PanelReviewStatus {
  /**
   *
   * @type {ApplicationReviewFlagPanel}
   * @memberof PanelReviewStatus
   */
  panel: ApplicationReviewFlagPanel;
  /**
   *
   * @type {boolean}
   * @memberof PanelReviewStatus
   */
  isReviewed: boolean;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum PaymentPlan {
  PaymentPlanInstallmentWithEft = 'PaymentPlanInstallmentWithEFT',
  PaymentPlanPaidInFull = 'PaymentPlanPaidInFull',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum PreTelematicsQuoteState {
  NotReadyBecauseDataPipelinePending = 'NotReadyBecauseDataPipelinePending',
  NotReadyBecauseDataPipelineIssues = 'NotReadyBecauseDataPipelineIssues',
  ReadyBecauseDataPipelineSuccess = 'ReadyBecauseDataPipelineSuccess',
  ReadyBecauseTimeElapsedAndDataPipelineSuccess = 'ReadyBecauseTimeElapsedAndDataPipelineSuccess',
  ReadyBecauseTimeElapsedAndDataPipelinePending = 'ReadyBecauseTimeElapsedAndDataPipelinePending',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum ProgramType {
  ProgramTypeFleet = 'ProgramTypeFleet',
  ProgramTypeNonFleetCanopiusNrb = 'ProgramTypeNonFleetCanopiusNRB',
  ProgramTypeNonFleetAdmitted = 'ProgramTypeNonFleetAdmitted',
  ProgramTypeInvalid = 'ProgramTypeInvalid',
}

/**
 *
 * @export
 * @interface RecommendationFactor
 */
export interface RecommendationFactor {
  /**
   *
   * @type {CoverageType}
   * @memberof RecommendationFactor
   */
  coverageType: CoverageType;
  /**
   *
   * @type {number}
   * @memberof RecommendationFactor
   */
  value?: number;
}
/**
 *
 * @export
 * @interface ReferApplicationReviewForm
 */
export interface ReferApplicationReviewForm {
  /**
   *
   * @type {string}
   * @memberof ReferApplicationReviewForm
   */
  note: string;
}
/**
 *
 * @export
 * @interface RiskScore
 */
export interface RiskScore {
  /**
   *
   * @type {string}
   * @memberof RiskScore
   */
  handleId: string;
  /**
   *
   * @type {ScoreType}
   * @memberof RiskScore
   */
  scoreType: ScoreType;
  /**
   *
   * @type {Version}
   * @memberof RiskScore
   */
  scoreVersion: Version;
  /**
   *
   * @type {Array<RiskScoreTrendItem>}
   * @memberof RiskScore
   */
  riskScoreTrend: Array<RiskScoreTrendItem>;
  /**
   *
   * @type {RiskScoreUwRubric}
   * @memberof RiskScore
   */
  uwRubric: RiskScoreUwRubric;
}
/**
 *
 * @export
 * @interface RiskScoreTrendItem
 */
export interface RiskScoreTrendItem {
  /**
   *
   * @type {number}
   * @memberof RiskScoreTrendItem
   */
  score?: number;
  /**
   *
   * @type {string}
   * @memberof RiskScoreTrendItem
   */
  windowStart: string;
  /**
   *
   * @type {string}
   * @memberof RiskScoreTrendItem
   */
  windowEnd: string;
  /**
   *
   * @type {WindowType}
   * @memberof RiskScoreTrendItem
   */
  windowType: WindowType;
  /**
   *
   * @type {number}
   * @memberof RiskScoreTrendItem
   */
  vinCount?: number;
  /**
   *
   * @type {string}
   * @memberof RiskScoreTrendItem
   */
  timestamp: string;
  /**
   *
   * @type {boolean}
   * @memberof RiskScoreTrendItem
   */
  isShortHaul?: boolean;
  /**
   *
   * @type {boolean}
   * @memberof RiskScoreTrendItem
   */
  isSelected?: boolean;
  /**
   *
   * @type {string}
   * @memberof RiskScoreTrendItem
   */
  marketCategory?: string;
}
/**
 *
 * @export
 * @interface RiskScoreUwRubric
 */
export interface RiskScoreUwRubric {
  /**
   *
   * @type {Array<UwRubricItem>}
   * @memberof RiskScoreUwRubric
   */
  items: Array<UwRubricItem>;
  /**
   *
   * @type {Version}
   * @memberof RiskScoreUwRubric
   */
  version: Version;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum SafetyRating {
  Satisfactory = 'Satisfactory',
  Unsatisfactory = 'Unsatisfactory',
  UnRated = 'UnRated',
  Conditional = 'Conditional',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum ScoreType {
  Trs = 'TRS',
  ProxyMilliman = 'Proxy_Milliman',
  GForceAdjustedTrs = 'gForce_Adjusted_TRS',
  GpsOnly1MinTrs = 'GPS_Only_1_min_TRS',
  GpsOnly10SecTrs = 'GPS_Only_10_sec_TRS',
}

/**
 *
 * @export
 * @interface ScrapeResponse
 */
export interface ScrapeResponse {
  /**
   *
   * @type {ScrapeType}
   * @memberof ScrapeResponse
   */
  type: ScrapeType;
  /**
   *
   * @type {ScrapeStatus}
   * @memberof ScrapeResponse
   */
  status: ScrapeStatus;
  /**
   *
   * @type {string}
   * @memberof ScrapeResponse
   */
  error?: string;
  /**
   *
   * @type {BusinessOwnerCreditScore}
   * @memberof ScrapeResponse
   */
  creditScore?: BusinessOwnerCreditScore;
  /**
   *
   * @type {USDotScore}
   * @memberof ScrapeResponse
   */
  usdotScore?: USDotScore;
  /**
   *
   * @type {number}
   * @memberof ScrapeResponse
   */
  creditVariance?: number;
  /**
   *
   * @type {number}
   * @memberof ScrapeResponse
   */
  usdotVariance?: number;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum ScrapeStatus {
  NotStarted = 'NOT_STARTED',
  Running = 'RUNNING',
  Failed = 'FAILED',
  Success = 'SUCCESS',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum ScrapeType {
  CreditScore = 'CreditScore',
  UsdotScore = 'USDOTScore',
}

/**
 *
 * @export
 * @interface SevereViolationsItem
 */
export interface SevereViolationsItem {
  /**
   *
   * @type {string}
   * @memberof SevereViolationsItem
   */
  violation: string;
  /**
   *
   * @type {string}
   * @memberof SevereViolationsItem
   */
  description: string;
  /**
   *
   * @type {number}
   * @memberof SevereViolationsItem
   */
  frequency: number;
  /**
   *
   * @type {number}
   * @memberof SevereViolationsItem
   */
  impact: number;
  /**
   *
   * @type {boolean}
   * @memberof SevereViolationsItem
   */
  IsControlledSubstancesAndAlcohol?: boolean;
  /**
   *
   * @type {string}
   * @memberof SevereViolationsItem
   */
  publishedDate?: string;
  /**
   *
   * @type {string}
   * @memberof SevereViolationsItem
   */
  inspectionDate?: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum USDotScore {
  UsDotScoreX = 'USDotScoreX',
  UsDotScoreA01 = 'USDotScoreA01',
  UsDotScoreA02 = 'USDotScoreA02',
  UsDotScoreA03 = 'USDotScoreA03',
  UsDotScoreA04 = 'USDotScoreA04',
  UsDotScoreA05 = 'USDotScoreA05',
  UsDotScoreA06 = 'USDotScoreA06',
  UsDotScoreA07 = 'USDotScoreA07',
  UsDotScoreA08 = 'USDotScoreA08',
  UsDotScoreA09 = 'USDotScoreA09',
  UsDotScoreA10 = 'USDotScoreA10',
  UsDotScoreA11 = 'USDotScoreA11',
  UsDotScoreA12 = 'USDotScoreA12',
  UsDotScoreA13 = 'USDotScoreA13',
  UsDotScoreB01 = 'USDotScoreB01',
  UsDotScoreB02 = 'USDotScoreB02',
  UsDotScoreB03 = 'USDotScoreB03',
  UsDotScoreB04 = 'USDotScoreB04',
  UsDotScoreB05 = 'USDotScoreB05',
  UsDotScoreB06 = 'USDotScoreB06',
  UsDotScoreB07 = 'USDotScoreB07',
  UsDotScoreB08 = 'USDotScoreB08',
  UsDotScoreB09 = 'USDotScoreB09',
  UsDotScoreB10 = 'USDotScoreB10',
  UsDotScoreB11 = 'USDotScoreB11',
  UsDotScoreB12 = 'USDotScoreB12',
  UsDotScoreB13 = 'USDotScoreB13',
  UsDotScoreZ92 = 'USDotScoreZ92',
  UsDotScoreZ93 = 'USDotScoreZ93',
  UsDotScoreZ94 = 'USDotScoreZ94',
  UsDotScoreZ95 = 'USDotScoreZ95',
  UsDotScoreZ97 = 'USDotScoreZ97',
  UsDotScoreZ98 = 'USDotScoreZ98',
  UsDotScoreZ99 = 'USDotScoreZ99',
}

/**
 *
 * @export
 * @interface UpdateApplicationReviewDriverRecordForm
 */
export interface UpdateApplicationReviewDriverRecordForm {
  /**
   *
   * @type {boolean}
   * @memberof UpdateApplicationReviewDriverRecordForm
   */
  isExcluded: boolean;
  /**
   *
   * @type {boolean}
   * @memberof UpdateApplicationReviewDriverRecordForm
   */
  isOutOfState: boolean;
  /**
   *
   * @type {Array<DriverViolation>}
   * @memberof UpdateApplicationReviewDriverRecordForm
   */
  violations?: Array<DriverViolation>;
}
/**
 *
 * @export
 * @interface UpdateApplicationReviewDriversForm
 */
export interface UpdateApplicationReviewDriversForm {
  /**
   *
   * @type {boolean}
   * @memberof UpdateApplicationReviewDriversForm
   */
  isReviewed: boolean;
}
/**
 *
 * @export
 * @interface UpdateApplicationReviewEquipmentForm
 */
export interface UpdateApplicationReviewEquipmentForm {
  /**
   *
   * @type {boolean}
   * @memberof UpdateApplicationReviewEquipmentForm
   */
  isReviewed: boolean;
}
/**
 *
 * @export
 * @interface UpdateApplicationReviewLossesForm
 */
export interface UpdateApplicationReviewLossesForm {
  /**
   *
   * @type {boolean}
   * @memberof UpdateApplicationReviewLossesForm
   */
  isReviewed: boolean;
}
/**
 *
 * @export
 * @interface UpdateApplicationReviewOperationForm
 */
export interface UpdateApplicationReviewOperationForm {
  /**
   *
   * @type {boolean}
   * @memberof UpdateApplicationReviewOperationForm
   */
  isReviewed: boolean;
  /**
   *
   * @type {BusinessOwnerCreditScore}
   * @memberof UpdateApplicationReviewOperationForm
   */
  bizOwnerCreditScore?: BusinessOwnerCreditScore;
  /**
   *
   * @type {USDotScore}
   * @memberof UpdateApplicationReviewOperationForm
   */
  usDotScore?: USDotScore;
  /**
   *
   * @type {string}
   * @memberof UpdateApplicationReviewOperationForm
   */
  effectiveDate?: string;
}
/**
 *
 * @export
 * @interface UpdateApplicationReviewPackagesForm
 */
export interface UpdateApplicationReviewPackagesForm {
  /**
   *
   * @type {boolean}
   * @memberof UpdateApplicationReviewPackagesForm
   */
  isReviewed?: boolean;
  /**
   *
   * @type {number}
   * @memberof UpdateApplicationReviewPackagesForm
   */
  apdPercent?: number;
  /**
   *
   * @type {number}
   * @memberof UpdateApplicationReviewPackagesForm
   */
  mtcPercent?: number;
  /**
   *
   * @type {number}
   * @memberof UpdateApplicationReviewPackagesForm
   */
  alPercent?: number;
  /**
   *
   * @type {number}
   * @memberof UpdateApplicationReviewPackagesForm
   */
  glPercent?: number;
  /**
   *
   * @type {Array<AncillaryCoverage>}
   * @memberof UpdateApplicationReviewPackagesForm
   */
  ancillaryCoverages?: Array<AncillaryCoverage>;
  /**
   *
   * @type {PaymentPlan}
   * @memberof UpdateApplicationReviewPackagesForm
   */
  paymentPlan?: PaymentPlan;
}
/**
 *
 * @export
 * @interface UpdateApplicationReviewSafetyForm
 */
export interface UpdateApplicationReviewSafetyForm {
  /**
   *
   * @type {number}
   * @memberof UpdateApplicationReviewSafetyForm
   */
  safetyCredits?: number;
  /**
   *
   * @type {boolean}
   * @memberof UpdateApplicationReviewSafetyForm
   */
  isReviewed: boolean;
}
/**
 *
 * @export
 * @interface UpdateSafetyScoreRequest
 */
export interface UpdateSafetyScoreRequest {
  /**
   *
   * @type {string}
   * @memberof UpdateSafetyScoreRequest
   */
  newScoreTimestamp: string;
  /**
   *
   * @type {string}
   * @memberof UpdateSafetyScoreRequest
   */
  changeReason: string;
}
/**
 *
 * @export
 * @interface UploadFileRequest
 */
export interface UploadFileRequest {
  /**
   *
   * @type {any}
   * @memberof UploadFileRequest
   */
  file: any;
  /**
   *
   * @type {string}
   * @memberof UploadFileRequest
   */
  fileType: string;
  /**
   *
   * @type {string}
   * @memberof UploadFileRequest
   */
  fileDestinationGroup: string;
}
/**
 *
 * @export
 * @interface UwRubricItem
 */
export interface UwRubricItem {
  /**
   *
   * @type {number}
   * @memberof UwRubricItem
   */
  scoreStart: number;
  /**
   *
   * @type {number}
   * @memberof UwRubricItem
   */
  scoreEnd: number;
  /**
   *
   * @type {number}
   * @memberof UwRubricItem
   */
  decile: number;
  /**
   *
   * @type {number}
   * @memberof UwRubricItem
   */
  discount?: number;
  /**
   *
   * @type {boolean}
   * @memberof UwRubricItem
   */
  market: boolean;
  /**
   *
   * @type {string}
   * @memberof UwRubricItem
   */
  marketCategory: string;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum Version {
  Undefined = 'Undefined',
  V1 = 'V1',
  V2 = 'V2',
  V3 = 'V3',
  V4 = 'V4',
  V5 = 'V5',
}

/**
 *
 * @export
 * @enum {string}
 */

export enum WindowType {
  _3M = '3M',
  _6M = '6M',
  _12M = '12M',
}

/**
 * ActionApi - axios parameter creator
 * @export
 */
export const ActionApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getNonFleetApplicationReviewActions: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getNonFleetApplicationReviewActions',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/actions`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ActionApi - functional programming interface
 * @export
 */
export const ActionApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = ActionApiAxiosParamCreator(configuration);
  return {
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getNonFleetApplicationReviewActions(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewGetActionsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getNonFleetApplicationReviewActions(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * ActionApi - factory interface
 * @export
 */
export const ActionApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = ActionApiFp(configuration);
  return {
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getNonFleetApplicationReviewActions(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewGetActionsResponse> {
      return localVarFp
        .getNonFleetApplicationReviewActions(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ActionApi - object-oriented interface
 * @export
 * @class ActionApi
 * @extends {BaseAPI}
 */
export class ActionApi extends BaseAPI {
  /**
   * Returns a list of actions visible to the user
   * @param {string} applicationReviewID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ActionApi
   */
  public getNonFleetApplicationReviewActions(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return ActionApiFp(this.configuration)
      .getNonFleetApplicationReviewActions(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * DefaultApi - axios parameter creator
 * @export
 */
export const DefaultApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Patch express lane feedback
     * @param {string} applicationReviewID Application Review ID
     * @param {InlineObject} inlineObject
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    patchExpressLaneFeedback: async (
      applicationReviewID: string,
      inlineObject: InlineObject,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'patchExpressLaneFeedback',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'inlineObject' is not null or undefined
      assertParamExists(
        'patchExpressLaneFeedback',
        'inlineObject',
        inlineObject,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/express_lane_feedback/submit`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        inlineObject,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = DefaultApiAxiosParamCreator(configuration);
  return {
    /**
     * Patch express lane feedback
     * @param {string} applicationReviewID Application Review ID
     * @param {InlineObject} inlineObject
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async patchExpressLaneFeedback(
      applicationReviewID: string,
      inlineObject: InlineObject,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ExpressLaneFeedbackPatchResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.patchExpressLaneFeedback(
          applicationReviewID,
          inlineObject,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = DefaultApiFp(configuration);
  return {
    /**
     * Patch express lane feedback
     * @param {string} applicationReviewID Application Review ID
     * @param {InlineObject} inlineObject
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    patchExpressLaneFeedback(
      applicationReviewID: string,
      inlineObject: InlineObject,
      options?: any,
    ): AxiosPromise<ExpressLaneFeedbackPatchResponse> {
      return localVarFp
        .patchExpressLaneFeedback(applicationReviewID, inlineObject, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
  /**
   * Patch express lane feedback
   * @param {string} applicationReviewID Application Review ID
   * @param {InlineObject} inlineObject
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApi
   */
  public patchExpressLaneFeedback(
    applicationReviewID: string,
    inlineObject: InlineObject,
    options?: AxiosRequestConfig,
  ) {
    return DefaultApiFp(this.configuration)
      .patchExpressLaneFeedback(applicationReviewID, inlineObject, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * ExpressLaneApi - axios parameter creator
 * @export
 */
export const ExpressLaneApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getExpressLaneFeedback: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getExpressLaneFeedback',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/express_lane_feedback/submit`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ExpressLaneApi - functional programming interface
 * @export
 */
export const ExpressLaneApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    ExpressLaneApiAxiosParamCreator(configuration);
  return {
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getExpressLaneFeedback(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ExpressLaneFeedbackGetResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getExpressLaneFeedback(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * ExpressLaneApi - factory interface
 * @export
 */
export const ExpressLaneApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = ExpressLaneApiFp(configuration);
  return {
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getExpressLaneFeedback(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ExpressLaneFeedbackGetResponse> {
      return localVarFp
        .getExpressLaneFeedback(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * ExpressLaneApi - object-oriented interface
 * @export
 * @class ExpressLaneApi
 * @extends {BaseAPI}
 */
export class ExpressLaneApi extends BaseAPI {
  /**
   * Returns a list of actions visible to the user
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof ExpressLaneApi
   */
  public getExpressLaneFeedback(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return ExpressLaneApiFp(this.configuration)
      .getExpressLaneFeedback(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * UnderwritingApi - axios parameter creator
 * @export
 */
export const UnderwritingApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Approve the application
     * @param {string} applicationReviewID Application Review ID
     * @param {ApproveApplicationReviewForm} approveApplicationReviewForm Approve the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    approveApplicationReview: async (
      applicationReviewID: string,
      approveApplicationReviewForm: ApproveApplicationReviewForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'approveApplicationReview',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'approveApplicationReviewForm' is not null or undefined
      assertParamExists(
        'approveApplicationReview',
        'approveApplicationReviewForm',
        approveApplicationReviewForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/approve`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        approveApplicationReviewForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Close the application
     * @param {string} applicationReviewID Application Review ID
     * @param {CloseApplicationReviewForm} closeApplicationReviewForm Close the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    closeApplicationReview: async (
      applicationReviewID: string,
      closeApplicationReviewForm: CloseApplicationReviewForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'closeApplicationReview',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'closeApplicationReviewForm' is not null or undefined
      assertParamExists(
        'closeApplicationReview',
        'closeApplicationReviewForm',
        closeApplicationReviewForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/close`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        closeApplicationReviewForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Decline the application
     * @param {string} applicationReviewID Application Review ID
     * @param {DeclineApplicationReviewForm} declineApplicationReviewForm Decline the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    declineApplicationReview: async (
      applicationReviewID: string,
      declineApplicationReviewForm: DeclineApplicationReviewForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'declineApplicationReview',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'declineApplicationReviewForm' is not null or undefined
      assertParamExists(
        'declineApplicationReview',
        'declineApplicationReviewForm',
        declineApplicationReviewForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/decline`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        declineApplicationReviewForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Return application review\'s documents
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewDocuments: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewDocuments',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/documents`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewDrivers: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewDrivers',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/drivers`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewDriversV2: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewDriversV2',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/v2/application-reviews/{applicationReviewID}/drivers`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all equipments tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewEquipments: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewEquipments',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/equipments`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all flags info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewFlags: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewFlags',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/flags`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all losses tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewLosses: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewLosses',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/losses`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Return application review\'s notes
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewNotes: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewNotes',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/notes`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all operations tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewOperations: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewOperations',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/operations`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all safety tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewSafety: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewSafety',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/safety`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Return application review\'s safety score (safety) widget data
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewSafetyScore: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewSafetyScore',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/safety/safety_score`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all timeline tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewTimeline: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewTimeline',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/timeline`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all reviews
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviews: async (
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/nonfleet/underwriting/application_reviews`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewsPackages: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewsPackages',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/packages`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewsSummary: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getApplicationReviewsSummary',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/summary`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all application reviews with pagination
     * @param {number} [size]
     * @param {string} [cursor]
     * @param {string} [q]
     * @param {string} [effectiveDateBefore]
     * @param {string} [effectiveDateAfter]
     * @param {string} [underWriterID]
     * @param {ApplicationReviewTab} [tab]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewsWithPagination: async (
      size?: number,
      cursor?: string,
      q?: string,
      effectiveDateBefore?: string,
      effectiveDateAfter?: string,
      underWriterID?: string,
      tab?: ApplicationReviewTab,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/nonfleet/underwriting/application_review/list`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (size !== undefined) {
        localVarQueryParameter['size'] = size;
      }

      if (cursor !== undefined) {
        localVarQueryParameter['cursor'] = cursor;
      }

      if (q !== undefined) {
        localVarQueryParameter['q'] = q;
      }

      if (effectiveDateBefore !== undefined) {
        localVarQueryParameter['effectiveDateBefore'] =
          (effectiveDateBefore as any) instanceof Date
            ? (effectiveDateBefore as any).toISOString().substr(0, 10)
            : effectiveDateBefore;
      }

      if (effectiveDateAfter !== undefined) {
        localVarQueryParameter['effectiveDateAfter'] =
          (effectiveDateAfter as any) instanceof Date
            ? (effectiveDateAfter as any).toISOString().substr(0, 10)
            : effectiveDateAfter;
      }

      if (underWriterID !== undefined) {
        localVarQueryParameter['underWriterID'] = underWriterID;
      }

      if (tab !== undefined) {
        localVarQueryParameter['tab'] = tab;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all PGR driver violations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDriverViolations: async (
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/nonfleet/underwriting/driver-violations`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getExpressLaneFeedback: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getExpressLaneFeedback',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/express_lane_feedback/submit`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getNonFleetApplicationReviewActions: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getNonFleetApplicationReviewActions',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/actions`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Fetches the scrape info for an application review
     * @param {string} applicationReviewID Application Review ID
     * @param {ScrapeType} scrapeType The type of scrape to trigger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getScrapeInfo: async (
      applicationReviewID: string,
      scrapeType: ScrapeType,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'getScrapeInfo',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'scrapeType' is not null or undefined
      assertParamExists('getScrapeInfo', 'scrapeType', scrapeType);
      const localVarPath =
        `/nonfleet/underwriting/{applicationReviewID}/scrape`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (scrapeType !== undefined) {
        localVarQueryParameter['scrapeType'] = scrapeType;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Trigger RateML Job to generate Quote
     * @param {string} applicationReviewID Application Review ID
     * @param {boolean} [bindable]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postApplicationReviewQuoteSubmit: async (
      applicationReviewID: string,
      bindable?: boolean,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'postApplicationReviewQuoteSubmit',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/quote/submit`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (bindable !== undefined) {
        localVarQueryParameter['bindable'] = bindable;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Refer the application
     * @param {string} applicationReviewID Application Review ID
     * @param {ReferApplicationReviewForm} referApplicationReviewForm Refer the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    referApplicationReview: async (
      applicationReviewID: string,
      referApplicationReviewForm: ReferApplicationReviewForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'referApplicationReview',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'referApplicationReviewForm' is not null or undefined
      assertParamExists(
        'referApplicationReview',
        'referApplicationReviewForm',
        referApplicationReviewForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/refer`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        referApplicationReviewForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Rollbacks an approved application
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rollbackApplicationReview: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'rollbackApplicationReview',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/rollback`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Set MVR Pull flag as true
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setApplicationReviewMVRPull: async (
      applicationReviewID: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'setApplicationReviewMVRPull',
        'applicationReviewID',
        applicationReviewID,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/set_mvr_pull`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Triggers a scrape request for an application review
     * @param {string} applicationReviewID Application Review ID
     * @param {ScrapeType} scrapeType The type of scrape to trigger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    triggerScrape: async (
      applicationReviewID: string,
      scrapeType: ScrapeType,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'triggerScrape',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'scrapeType' is not null or undefined
      assertParamExists('triggerScrape', 'scrapeType', scrapeType);
      const localVarPath =
        `/nonfleet/underwriting/{applicationReviewID}/scrape`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      if (scrapeType !== undefined) {
        localVarQueryParameter['scrapeType'] = scrapeType;
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update the application review\'s assignee
     * @param {string} applicationReviewID Application Review ID
     * @param {ApplicationReviewAssigneesForm} applicationReviewAssigneesForm
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewAssignee: async (
      applicationReviewID: string,
      applicationReviewAssigneesForm: ApplicationReviewAssigneesForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewAssignee',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'applicationReviewAssigneesForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewAssignee',
        'applicationReviewAssigneesForm',
        applicationReviewAssigneesForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/assignee`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        applicationReviewAssigneesForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update driver tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {string} dlNumber Driver License Number
     * @param {UpdateApplicationReviewDriverRecordForm} updateApplicationReviewDriverRecordForm Driver tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewDriver: async (
      applicationReviewID: string,
      dlNumber: string,
      updateApplicationReviewDriverRecordForm: UpdateApplicationReviewDriverRecordForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewDriver',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'dlNumber' is not null or undefined
      assertParamExists('updateApplicationReviewDriver', 'dlNumber', dlNumber);
      // verify required parameter 'updateApplicationReviewDriverRecordForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewDriver',
        'updateApplicationReviewDriverRecordForm',
        updateApplicationReviewDriverRecordForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/driver/{dlNumber}`
          .replace(
            `{${'applicationReviewID'}}`,
            encodeURIComponent(String(applicationReviewID)),
          )
          .replace(`{${'dlNumber'}}`, encodeURIComponent(String(dlNumber)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateApplicationReviewDriverRecordForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update driver tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {string} dlNumber Driver License Number
     * @param {UpdateApplicationReviewDriverRecordForm} updateApplicationReviewDriverRecordForm Driver tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewDriverV2: async (
      applicationReviewID: string,
      dlNumber: string,
      updateApplicationReviewDriverRecordForm: UpdateApplicationReviewDriverRecordForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewDriverV2',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'dlNumber' is not null or undefined
      assertParamExists(
        'updateApplicationReviewDriverV2',
        'dlNumber',
        dlNumber,
      );
      // verify required parameter 'updateApplicationReviewDriverRecordForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewDriverV2',
        'updateApplicationReviewDriverRecordForm',
        updateApplicationReviewDriverRecordForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/v2/application-reviews/{applicationReviewID}/driver/{dlNumber}`
          .replace(
            `{${'applicationReviewID'}}`,
            encodeURIComponent(String(applicationReviewID)),
          )
          .replace(`{${'dlNumber'}}`, encodeURIComponent(String(dlNumber)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateApplicationReviewDriverRecordForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewDriversForm} updateApplicationReviewDriversForm Drivers tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewDrivers: async (
      applicationReviewID: string,
      updateApplicationReviewDriversForm: UpdateApplicationReviewDriversForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewDrivers',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'updateApplicationReviewDriversForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewDrivers',
        'updateApplicationReviewDriversForm',
        updateApplicationReviewDriversForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/drivers`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateApplicationReviewDriversForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update equipments tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewEquipmentForm} updateApplicationReviewEquipmentForm Equipments tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewEquipments: async (
      applicationReviewID: string,
      updateApplicationReviewEquipmentForm: UpdateApplicationReviewEquipmentForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewEquipments',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'updateApplicationReviewEquipmentForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewEquipments',
        'updateApplicationReviewEquipmentForm',
        updateApplicationReviewEquipmentForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/equipments`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateApplicationReviewEquipmentForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update losses tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewLossesForm} updateApplicationReviewLossesForm Losses tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewLosses: async (
      applicationReviewID: string,
      updateApplicationReviewLossesForm: UpdateApplicationReviewLossesForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewLosses',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'updateApplicationReviewLossesForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewLosses',
        'updateApplicationReviewLossesForm',
        updateApplicationReviewLossesForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/losses`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateApplicationReviewLossesForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update application review\'s notes
     * @param {string} applicationReviewID Application Review ID
     * @param {ApplicationReviewNotes} applicationReviewNotes Note info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewNotes: async (
      applicationReviewID: string,
      applicationReviewNotes: ApplicationReviewNotes,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewNotes',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'applicationReviewNotes' is not null or undefined
      assertParamExists(
        'updateApplicationReviewNotes',
        'applicationReviewNotes',
        applicationReviewNotes,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/notes`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        applicationReviewNotes,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update operations tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewOperationForm} updateApplicationReviewOperationForm Operations tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewOperations: async (
      applicationReviewID: string,
      updateApplicationReviewOperationForm: UpdateApplicationReviewOperationForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewOperations',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'updateApplicationReviewOperationForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewOperations',
        'updateApplicationReviewOperationForm',
        updateApplicationReviewOperationForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/operations`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateApplicationReviewOperationForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewPackagesForm} updateApplicationReviewPackagesForm Packages tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewPackages: async (
      applicationReviewID: string,
      updateApplicationReviewPackagesForm: UpdateApplicationReviewPackagesForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewPackages',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'updateApplicationReviewPackagesForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewPackages',
        'updateApplicationReviewPackagesForm',
        updateApplicationReviewPackagesForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/packages`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateApplicationReviewPackagesForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update safety tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewSafetyForm} updateApplicationReviewSafetyForm Safety tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewSafety: async (
      applicationReviewID: string,
      updateApplicationReviewSafetyForm: UpdateApplicationReviewSafetyForm,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewSafety',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'updateApplicationReviewSafetyForm' is not null or undefined
      assertParamExists(
        'updateApplicationReviewSafety',
        'updateApplicationReviewSafetyForm',
        updateApplicationReviewSafetyForm,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/safety`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        updateApplicationReviewSafetyForm,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Update application review\'s safety score
     * @param {string} applicationReviewID
     * @param {ApplicationReviewSafetyScoreFormV2} applicationReviewSafetyScoreFormV2
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewSafetyScoreV2: async (
      applicationReviewID: string,
      applicationReviewSafetyScoreFormV2: ApplicationReviewSafetyScoreFormV2,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'updateApplicationReviewSafetyScoreV2',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'applicationReviewSafetyScoreFormV2' is not null or undefined
      assertParamExists(
        'updateApplicationReviewSafetyScoreV2',
        'applicationReviewSafetyScoreFormV2',
        applicationReviewSafetyScoreFormV2,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/safety/safety_score`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'PATCH',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        applicationReviewSafetyScoreFormV2,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Upload documents for an application_review
     * @param {string} applicationReviewID Application Review ID
     * @param {any} file
     * @param {string} fileType
     * @param {string} fileDestinationGroup
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    uploadApplicationReviewDocuments: async (
      applicationReviewID: string,
      file: any,
      fileType: string,
      fileDestinationGroup: string,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'applicationReviewID' is not null or undefined
      assertParamExists(
        'uploadApplicationReviewDocuments',
        'applicationReviewID',
        applicationReviewID,
      );
      // verify required parameter 'file' is not null or undefined
      assertParamExists('uploadApplicationReviewDocuments', 'file', file);
      // verify required parameter 'fileType' is not null or undefined
      assertParamExists(
        'uploadApplicationReviewDocuments',
        'fileType',
        fileType,
      );
      // verify required parameter 'fileDestinationGroup' is not null or undefined
      assertParamExists(
        'uploadApplicationReviewDocuments',
        'fileDestinationGroup',
        fileDestinationGroup,
      );
      const localVarPath =
        `/nonfleet/underwriting/application_reviews/{applicationReviewID}/documents`.replace(
          `{${'applicationReviewID'}}`,
          encodeURIComponent(String(applicationReviewID)),
        );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;
      const localVarFormParams = new ((configuration &&
        configuration.formDataCtor) ||
        FormData)();

      if (file !== undefined) {
        localVarFormParams.append('file', file as any);
      }

      if (fileType !== undefined) {
        localVarFormParams.append('fileType', fileType as any);
      }

      if (fileDestinationGroup !== undefined) {
        localVarFormParams.append(
          'fileDestinationGroup',
          fileDestinationGroup as any,
        );
      }

      localVarHeaderParameter['Content-Type'] = 'multipart/form-data';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = localVarFormParams;

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * UnderwritingApi - functional programming interface
 * @export
 */
export const UnderwritingApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator =
    UnderwritingApiAxiosParamCreator(configuration);
  return {
    /**
     * Approve the application
     * @param {string} applicationReviewID Application Review ID
     * @param {ApproveApplicationReviewForm} approveApplicationReviewForm Approve the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async approveApplicationReview(
      applicationReviewID: string,
      approveApplicationReviewForm: ApproveApplicationReviewForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.approveApplicationReview(
          applicationReviewID,
          approveApplicationReviewForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Close the application
     * @param {string} applicationReviewID Application Review ID
     * @param {CloseApplicationReviewForm} closeApplicationReviewForm Close the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async closeApplicationReview(
      applicationReviewID: string,
      closeApplicationReviewForm: CloseApplicationReviewForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.closeApplicationReview(
          applicationReviewID,
          closeApplicationReviewForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Decline the application
     * @param {string} applicationReviewID Application Review ID
     * @param {DeclineApplicationReviewForm} declineApplicationReviewForm Decline the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async declineApplicationReview(
      applicationReviewID: string,
      declineApplicationReviewForm: DeclineApplicationReviewForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.declineApplicationReview(
          applicationReviewID,
          declineApplicationReviewForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Return application review\'s documents
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewDocuments(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewDocuments>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewDocuments(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewDrivers(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewDriver>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewDrivers(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewDriversV2(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewDriver>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewDriversV2(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all equipments tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewEquipments(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewEquipment>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewEquipments(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all flags info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewFlags(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<ApplicationReviewFlag>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewFlags(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all losses tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewLosses(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewLosses>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewLosses(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Return application review\'s notes
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewNotes(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewNotes>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewNotes(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all operations tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewOperations(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewOperation>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewOperations(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all safety tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewSafety(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewSafety>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewSafety(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Return application review\'s safety score (safety) widget data
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewSafetyScore(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewSafetyScoreV2>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewSafetyScore(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all timeline tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewTimeline(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<ApplicationReviewEvent>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewTimeline(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all reviews
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviews(
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<ApplicationReviewDetails>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviews(options);
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewsPackages(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewPackages>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewsPackages(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewsSummary(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewSummary>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewsSummary(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all application reviews with pagination
     * @param {number} [size]
     * @param {string} [cursor]
     * @param {string} [q]
     * @param {string} [effectiveDateBefore]
     * @param {string} [effectiveDateAfter]
     * @param {string} [underWriterID]
     * @param {ApplicationReviewTab} [tab]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getApplicationReviewsWithPagination(
      size?: number,
      cursor?: string,
      q?: string,
      effectiveDateBefore?: string,
      effectiveDateAfter?: string,
      underWriterID?: string,
      tab?: ApplicationReviewTab,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewsWithPaginationResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getApplicationReviewsWithPagination(
          size,
          cursor,
          q,
          effectiveDateBefore,
          effectiveDateAfter,
          underWriterID,
          tab,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all PGR driver violations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getDriverViolations(
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<DriverViolation>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getDriverViolations(options);
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getExpressLaneFeedback(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ExpressLaneFeedbackGetResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getExpressLaneFeedback(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getNonFleetApplicationReviewActions(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<ApplicationReviewGetActionsResponse>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getNonFleetApplicationReviewActions(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Fetches the scrape info for an application review
     * @param {string} applicationReviewID Application Review ID
     * @param {ScrapeType} scrapeType The type of scrape to trigger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getScrapeInfo(
      applicationReviewID: string,
      scrapeType: ScrapeType,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<ScrapeResponse>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.getScrapeInfo(
        applicationReviewID,
        scrapeType,
        options,
      );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Trigger RateML Job to generate Quote
     * @param {string} applicationReviewID Application Review ID
     * @param {boolean} [bindable]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async postApplicationReviewQuoteSubmit(
      applicationReviewID: string,
      bindable?: boolean,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.postApplicationReviewQuoteSubmit(
          applicationReviewID,
          bindable,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Refer the application
     * @param {string} applicationReviewID Application Review ID
     * @param {ReferApplicationReviewForm} referApplicationReviewForm Refer the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async referApplicationReview(
      applicationReviewID: string,
      referApplicationReviewForm: ReferApplicationReviewForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.referApplicationReview(
          applicationReviewID,
          referApplicationReviewForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Rollbacks an approved application
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async rollbackApplicationReview(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.rollbackApplicationReview(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Set MVR Pull flag as true
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async setApplicationReviewMVRPull(
      applicationReviewID: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<MVRFlag>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.setApplicationReviewMVRPull(
          applicationReviewID,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Triggers a scrape request for an application review
     * @param {string} applicationReviewID Application Review ID
     * @param {ScrapeType} scrapeType The type of scrape to trigger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async triggerScrape(
      applicationReviewID: string,
      scrapeType: ScrapeType,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs = await localVarAxiosParamCreator.triggerScrape(
        applicationReviewID,
        scrapeType,
        options,
      );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update the application review\'s assignee
     * @param {string} applicationReviewID Application Review ID
     * @param {ApplicationReviewAssigneesForm} applicationReviewAssigneesForm
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewAssignee(
      applicationReviewID: string,
      applicationReviewAssigneesForm: ApplicationReviewAssigneesForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewAssignee(
          applicationReviewID,
          applicationReviewAssigneesForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update driver tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {string} dlNumber Driver License Number
     * @param {UpdateApplicationReviewDriverRecordForm} updateApplicationReviewDriverRecordForm Driver tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewDriver(
      applicationReviewID: string,
      dlNumber: string,
      updateApplicationReviewDriverRecordForm: UpdateApplicationReviewDriverRecordForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewDriver(
          applicationReviewID,
          dlNumber,
          updateApplicationReviewDriverRecordForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update driver tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {string} dlNumber Driver License Number
     * @param {UpdateApplicationReviewDriverRecordForm} updateApplicationReviewDriverRecordForm Driver tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewDriverV2(
      applicationReviewID: string,
      dlNumber: string,
      updateApplicationReviewDriverRecordForm: UpdateApplicationReviewDriverRecordForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewDriverV2(
          applicationReviewID,
          dlNumber,
          updateApplicationReviewDriverRecordForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewDriversForm} updateApplicationReviewDriversForm Drivers tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewDrivers(
      applicationReviewID: string,
      updateApplicationReviewDriversForm: UpdateApplicationReviewDriversForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewDrivers(
          applicationReviewID,
          updateApplicationReviewDriversForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update equipments tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewEquipmentForm} updateApplicationReviewEquipmentForm Equipments tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewEquipments(
      applicationReviewID: string,
      updateApplicationReviewEquipmentForm: UpdateApplicationReviewEquipmentForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewEquipments(
          applicationReviewID,
          updateApplicationReviewEquipmentForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update losses tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewLossesForm} updateApplicationReviewLossesForm Losses tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewLosses(
      applicationReviewID: string,
      updateApplicationReviewLossesForm: UpdateApplicationReviewLossesForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewLosses(
          applicationReviewID,
          updateApplicationReviewLossesForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update application review\'s notes
     * @param {string} applicationReviewID Application Review ID
     * @param {ApplicationReviewNotes} applicationReviewNotes Note info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewNotes(
      applicationReviewID: string,
      applicationReviewNotes: ApplicationReviewNotes,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewNotes(
          applicationReviewID,
          applicationReviewNotes,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update operations tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewOperationForm} updateApplicationReviewOperationForm Operations tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewOperations(
      applicationReviewID: string,
      updateApplicationReviewOperationForm: UpdateApplicationReviewOperationForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewOperations(
          applicationReviewID,
          updateApplicationReviewOperationForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewPackagesForm} updateApplicationReviewPackagesForm Packages tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewPackages(
      applicationReviewID: string,
      updateApplicationReviewPackagesForm: UpdateApplicationReviewPackagesForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewPackages(
          applicationReviewID,
          updateApplicationReviewPackagesForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update safety tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewSafetyForm} updateApplicationReviewSafetyForm Safety tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewSafety(
      applicationReviewID: string,
      updateApplicationReviewSafetyForm: UpdateApplicationReviewSafetyForm,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewSafety(
          applicationReviewID,
          updateApplicationReviewSafetyForm,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Update application review\'s safety score
     * @param {string} applicationReviewID
     * @param {ApplicationReviewSafetyScoreFormV2} applicationReviewSafetyScoreFormV2
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async updateApplicationReviewSafetyScoreV2(
      applicationReviewID: string,
      applicationReviewSafetyScoreFormV2: ApplicationReviewSafetyScoreFormV2,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.updateApplicationReviewSafetyScoreV2(
          applicationReviewID,
          applicationReviewSafetyScoreFormV2,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Upload documents for an application_review
     * @param {string} applicationReviewID Application Review ID
     * @param {any} file
     * @param {string} fileType
     * @param {string} fileDestinationGroup
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async uploadApplicationReviewDocuments(
      applicationReviewID: string,
      file: any,
      fileType: string,
      fileDestinationGroup: string,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<FileHandle>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.uploadApplicationReviewDocuments(
          applicationReviewID,
          file,
          fileType,
          fileDestinationGroup,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * UnderwritingApi - factory interface
 * @export
 */
export const UnderwritingApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = UnderwritingApiFp(configuration);
  return {
    /**
     * Approve the application
     * @param {string} applicationReviewID Application Review ID
     * @param {ApproveApplicationReviewForm} approveApplicationReviewForm Approve the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    approveApplicationReview(
      applicationReviewID: string,
      approveApplicationReviewForm: ApproveApplicationReviewForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .approveApplicationReview(
          applicationReviewID,
          approveApplicationReviewForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Close the application
     * @param {string} applicationReviewID Application Review ID
     * @param {CloseApplicationReviewForm} closeApplicationReviewForm Close the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    closeApplicationReview(
      applicationReviewID: string,
      closeApplicationReviewForm: CloseApplicationReviewForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .closeApplicationReview(
          applicationReviewID,
          closeApplicationReviewForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Decline the application
     * @param {string} applicationReviewID Application Review ID
     * @param {DeclineApplicationReviewForm} declineApplicationReviewForm Decline the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    declineApplicationReview(
      applicationReviewID: string,
      declineApplicationReviewForm: DeclineApplicationReviewForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .declineApplicationReview(
          applicationReviewID,
          declineApplicationReviewForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Return application review\'s documents
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewDocuments(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewDocuments> {
      return localVarFp
        .getApplicationReviewDocuments(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewDrivers(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewDriver> {
      return localVarFp
        .getApplicationReviewDrivers(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewDriversV2(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewDriver> {
      return localVarFp
        .getApplicationReviewDriversV2(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all equipments tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewEquipments(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewEquipment> {
      return localVarFp
        .getApplicationReviewEquipments(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all flags info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewFlags(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<Array<ApplicationReviewFlag>> {
      return localVarFp
        .getApplicationReviewFlags(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all losses tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewLosses(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewLosses> {
      return localVarFp
        .getApplicationReviewLosses(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Return application review\'s notes
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewNotes(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewNotes> {
      return localVarFp
        .getApplicationReviewNotes(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all operations tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewOperations(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewOperation> {
      return localVarFp
        .getApplicationReviewOperations(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all safety tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewSafety(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewSafety> {
      return localVarFp
        .getApplicationReviewSafety(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Return application review\'s safety score (safety) widget data
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewSafetyScore(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewSafetyScoreV2> {
      return localVarFp
        .getApplicationReviewSafetyScore(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all timeline tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewTimeline(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<Array<ApplicationReviewEvent>> {
      return localVarFp
        .getApplicationReviewTimeline(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all reviews
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviews(
      options?: any,
    ): AxiosPromise<Array<ApplicationReviewDetails>> {
      return localVarFp
        .getApplicationReviews(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewsPackages(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewPackages> {
      return localVarFp
        .getApplicationReviewsPackages(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewsSummary(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewSummary> {
      return localVarFp
        .getApplicationReviewsSummary(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all application reviews with pagination
     * @param {number} [size]
     * @param {string} [cursor]
     * @param {string} [q]
     * @param {string} [effectiveDateBefore]
     * @param {string} [effectiveDateAfter]
     * @param {string} [underWriterID]
     * @param {ApplicationReviewTab} [tab]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getApplicationReviewsWithPagination(
      size?: number,
      cursor?: string,
      q?: string,
      effectiveDateBefore?: string,
      effectiveDateAfter?: string,
      underWriterID?: string,
      tab?: ApplicationReviewTab,
      options?: any,
    ): AxiosPromise<ApplicationReviewsWithPaginationResponse> {
      return localVarFp
        .getApplicationReviewsWithPagination(
          size,
          cursor,
          q,
          effectiveDateBefore,
          effectiveDateAfter,
          underWriterID,
          tab,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all PGR driver violations
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDriverViolations(options?: any): AxiosPromise<Array<DriverViolation>> {
      return localVarFp
        .getDriverViolations(options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getExpressLaneFeedback(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ExpressLaneFeedbackGetResponse> {
      return localVarFp
        .getExpressLaneFeedback(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Returns a list of actions visible to the user
     * @param {string} applicationReviewID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getNonFleetApplicationReviewActions(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<ApplicationReviewGetActionsResponse> {
      return localVarFp
        .getNonFleetApplicationReviewActions(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Fetches the scrape info for an application review
     * @param {string} applicationReviewID Application Review ID
     * @param {ScrapeType} scrapeType The type of scrape to trigger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getScrapeInfo(
      applicationReviewID: string,
      scrapeType: ScrapeType,
      options?: any,
    ): AxiosPromise<ScrapeResponse> {
      return localVarFp
        .getScrapeInfo(applicationReviewID, scrapeType, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Trigger RateML Job to generate Quote
     * @param {string} applicationReviewID Application Review ID
     * @param {boolean} [bindable]
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    postApplicationReviewQuoteSubmit(
      applicationReviewID: string,
      bindable?: boolean,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .postApplicationReviewQuoteSubmit(
          applicationReviewID,
          bindable,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Refer the application
     * @param {string} applicationReviewID Application Review ID
     * @param {ReferApplicationReviewForm} referApplicationReviewForm Refer the application
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    referApplicationReview(
      applicationReviewID: string,
      referApplicationReviewForm: ReferApplicationReviewForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .referApplicationReview(
          applicationReviewID,
          referApplicationReviewForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Rollbacks an approved application
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    rollbackApplicationReview(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .rollbackApplicationReview(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Set MVR Pull flag as true
     * @param {string} applicationReviewID Application Review ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    setApplicationReviewMVRPull(
      applicationReviewID: string,
      options?: any,
    ): AxiosPromise<MVRFlag> {
      return localVarFp
        .setApplicationReviewMVRPull(applicationReviewID, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Triggers a scrape request for an application review
     * @param {string} applicationReviewID Application Review ID
     * @param {ScrapeType} scrapeType The type of scrape to trigger
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    triggerScrape(
      applicationReviewID: string,
      scrapeType: ScrapeType,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .triggerScrape(applicationReviewID, scrapeType, options)
        .then((request) => request(axios, basePath));
    },
    /**
     * Update the application review\'s assignee
     * @param {string} applicationReviewID Application Review ID
     * @param {ApplicationReviewAssigneesForm} applicationReviewAssigneesForm
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewAssignee(
      applicationReviewID: string,
      applicationReviewAssigneesForm: ApplicationReviewAssigneesForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewAssignee(
          applicationReviewID,
          applicationReviewAssigneesForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update driver tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {string} dlNumber Driver License Number
     * @param {UpdateApplicationReviewDriverRecordForm} updateApplicationReviewDriverRecordForm Driver tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewDriver(
      applicationReviewID: string,
      dlNumber: string,
      updateApplicationReviewDriverRecordForm: UpdateApplicationReviewDriverRecordForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewDriver(
          applicationReviewID,
          dlNumber,
          updateApplicationReviewDriverRecordForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update driver tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {string} dlNumber Driver License Number
     * @param {UpdateApplicationReviewDriverRecordForm} updateApplicationReviewDriverRecordForm Driver tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewDriverV2(
      applicationReviewID: string,
      dlNumber: string,
      updateApplicationReviewDriverRecordForm: UpdateApplicationReviewDriverRecordForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewDriverV2(
          applicationReviewID,
          dlNumber,
          updateApplicationReviewDriverRecordForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update drivers tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewDriversForm} updateApplicationReviewDriversForm Drivers tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewDrivers(
      applicationReviewID: string,
      updateApplicationReviewDriversForm: UpdateApplicationReviewDriversForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewDrivers(
          applicationReviewID,
          updateApplicationReviewDriversForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update equipments tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewEquipmentForm} updateApplicationReviewEquipmentForm Equipments tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewEquipments(
      applicationReviewID: string,
      updateApplicationReviewEquipmentForm: UpdateApplicationReviewEquipmentForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewEquipments(
          applicationReviewID,
          updateApplicationReviewEquipmentForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update losses tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewLossesForm} updateApplicationReviewLossesForm Losses tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewLosses(
      applicationReviewID: string,
      updateApplicationReviewLossesForm: UpdateApplicationReviewLossesForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewLosses(
          applicationReviewID,
          updateApplicationReviewLossesForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update application review\'s notes
     * @param {string} applicationReviewID Application Review ID
     * @param {ApplicationReviewNotes} applicationReviewNotes Note info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewNotes(
      applicationReviewID: string,
      applicationReviewNotes: ApplicationReviewNotes,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewNotes(
          applicationReviewID,
          applicationReviewNotes,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update operations tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewOperationForm} updateApplicationReviewOperationForm Operations tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewOperations(
      applicationReviewID: string,
      updateApplicationReviewOperationForm: UpdateApplicationReviewOperationForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewOperations(
          applicationReviewID,
          updateApplicationReviewOperationForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update packages tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewPackagesForm} updateApplicationReviewPackagesForm Packages tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewPackages(
      applicationReviewID: string,
      updateApplicationReviewPackagesForm: UpdateApplicationReviewPackagesForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewPackages(
          applicationReviewID,
          updateApplicationReviewPackagesForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update safety tab panel info
     * @param {string} applicationReviewID Application Review ID
     * @param {UpdateApplicationReviewSafetyForm} updateApplicationReviewSafetyForm Safety tab panel info
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewSafety(
      applicationReviewID: string,
      updateApplicationReviewSafetyForm: UpdateApplicationReviewSafetyForm,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewSafety(
          applicationReviewID,
          updateApplicationReviewSafetyForm,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Update application review\'s safety score
     * @param {string} applicationReviewID
     * @param {ApplicationReviewSafetyScoreFormV2} applicationReviewSafetyScoreFormV2
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateApplicationReviewSafetyScoreV2(
      applicationReviewID: string,
      applicationReviewSafetyScoreFormV2: ApplicationReviewSafetyScoreFormV2,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .updateApplicationReviewSafetyScoreV2(
          applicationReviewID,
          applicationReviewSafetyScoreFormV2,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Upload documents for an application_review
     * @param {string} applicationReviewID Application Review ID
     * @param {any} file
     * @param {string} fileType
     * @param {string} fileDestinationGroup
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    uploadApplicationReviewDocuments(
      applicationReviewID: string,
      file: any,
      fileType: string,
      fileDestinationGroup: string,
      options?: any,
    ): AxiosPromise<FileHandle> {
      return localVarFp
        .uploadApplicationReviewDocuments(
          applicationReviewID,
          file,
          fileType,
          fileDestinationGroup,
          options,
        )
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * UnderwritingApi - object-oriented interface
 * @export
 * @class UnderwritingApi
 * @extends {BaseAPI}
 */
export class UnderwritingApi extends BaseAPI {
  /**
   * Approve the application
   * @param {string} applicationReviewID Application Review ID
   * @param {ApproveApplicationReviewForm} approveApplicationReviewForm Approve the application
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public approveApplicationReview(
    applicationReviewID: string,
    approveApplicationReviewForm: ApproveApplicationReviewForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .approveApplicationReview(
        applicationReviewID,
        approveApplicationReviewForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Close the application
   * @param {string} applicationReviewID Application Review ID
   * @param {CloseApplicationReviewForm} closeApplicationReviewForm Close the application
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public closeApplicationReview(
    applicationReviewID: string,
    closeApplicationReviewForm: CloseApplicationReviewForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .closeApplicationReview(
        applicationReviewID,
        closeApplicationReviewForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Decline the application
   * @param {string} applicationReviewID Application Review ID
   * @param {DeclineApplicationReviewForm} declineApplicationReviewForm Decline the application
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public declineApplicationReview(
    applicationReviewID: string,
    declineApplicationReviewForm: DeclineApplicationReviewForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .declineApplicationReview(
        applicationReviewID,
        declineApplicationReviewForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Return application review\'s documents
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewDocuments(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewDocuments(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all drivers tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewDrivers(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewDrivers(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all drivers tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewDriversV2(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewDriversV2(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all equipments tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewEquipments(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewEquipments(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all flags info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewFlags(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewFlags(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all losses tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewLosses(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewLosses(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Return application review\'s notes
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewNotes(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewNotes(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all operations tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewOperations(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewOperations(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all safety tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewSafety(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewSafety(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Return application review\'s safety score (safety) widget data
   * @param {string} applicationReviewID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewSafetyScore(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewSafetyScore(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all timeline tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewTimeline(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewTimeline(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all reviews
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviews(options?: AxiosRequestConfig) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviews(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all packages tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewsPackages(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewsPackages(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all packages tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewsSummary(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewsSummary(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all application reviews with pagination
   * @param {number} [size]
   * @param {string} [cursor]
   * @param {string} [q]
   * @param {string} [effectiveDateBefore]
   * @param {string} [effectiveDateAfter]
   * @param {string} [underWriterID]
   * @param {ApplicationReviewTab} [tab]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getApplicationReviewsWithPagination(
    size?: number,
    cursor?: string,
    q?: string,
    effectiveDateBefore?: string,
    effectiveDateAfter?: string,
    underWriterID?: string,
    tab?: ApplicationReviewTab,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getApplicationReviewsWithPagination(
        size,
        cursor,
        q,
        effectiveDateBefore,
        effectiveDateAfter,
        underWriterID,
        tab,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all PGR driver violations
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getDriverViolations(options?: AxiosRequestConfig) {
    return UnderwritingApiFp(this.configuration)
      .getDriverViolations(options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns a list of actions visible to the user
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getExpressLaneFeedback(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getExpressLaneFeedback(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Returns a list of actions visible to the user
   * @param {string} applicationReviewID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getNonFleetApplicationReviewActions(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getNonFleetApplicationReviewActions(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Fetches the scrape info for an application review
   * @param {string} applicationReviewID Application Review ID
   * @param {ScrapeType} scrapeType The type of scrape to trigger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public getScrapeInfo(
    applicationReviewID: string,
    scrapeType: ScrapeType,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .getScrapeInfo(applicationReviewID, scrapeType, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Trigger RateML Job to generate Quote
   * @param {string} applicationReviewID Application Review ID
   * @param {boolean} [bindable]
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public postApplicationReviewQuoteSubmit(
    applicationReviewID: string,
    bindable?: boolean,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .postApplicationReviewQuoteSubmit(applicationReviewID, bindable, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Refer the application
   * @param {string} applicationReviewID Application Review ID
   * @param {ReferApplicationReviewForm} referApplicationReviewForm Refer the application
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public referApplicationReview(
    applicationReviewID: string,
    referApplicationReviewForm: ReferApplicationReviewForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .referApplicationReview(
        applicationReviewID,
        referApplicationReviewForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Rollbacks an approved application
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public rollbackApplicationReview(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .rollbackApplicationReview(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Set MVR Pull flag as true
   * @param {string} applicationReviewID Application Review ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public setApplicationReviewMVRPull(
    applicationReviewID: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .setApplicationReviewMVRPull(applicationReviewID, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Triggers a scrape request for an application review
   * @param {string} applicationReviewID Application Review ID
   * @param {ScrapeType} scrapeType The type of scrape to trigger
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public triggerScrape(
    applicationReviewID: string,
    scrapeType: ScrapeType,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .triggerScrape(applicationReviewID, scrapeType, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update the application review\'s assignee
   * @param {string} applicationReviewID Application Review ID
   * @param {ApplicationReviewAssigneesForm} applicationReviewAssigneesForm
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewAssignee(
    applicationReviewID: string,
    applicationReviewAssigneesForm: ApplicationReviewAssigneesForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewAssignee(
        applicationReviewID,
        applicationReviewAssigneesForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update driver tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {string} dlNumber Driver License Number
   * @param {UpdateApplicationReviewDriverRecordForm} updateApplicationReviewDriverRecordForm Driver tab panel info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewDriver(
    applicationReviewID: string,
    dlNumber: string,
    updateApplicationReviewDriverRecordForm: UpdateApplicationReviewDriverRecordForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewDriver(
        applicationReviewID,
        dlNumber,
        updateApplicationReviewDriverRecordForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update driver tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {string} dlNumber Driver License Number
   * @param {UpdateApplicationReviewDriverRecordForm} updateApplicationReviewDriverRecordForm Driver tab panel info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewDriverV2(
    applicationReviewID: string,
    dlNumber: string,
    updateApplicationReviewDriverRecordForm: UpdateApplicationReviewDriverRecordForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewDriverV2(
        applicationReviewID,
        dlNumber,
        updateApplicationReviewDriverRecordForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update drivers tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {UpdateApplicationReviewDriversForm} updateApplicationReviewDriversForm Drivers tab panel info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewDrivers(
    applicationReviewID: string,
    updateApplicationReviewDriversForm: UpdateApplicationReviewDriversForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewDrivers(
        applicationReviewID,
        updateApplicationReviewDriversForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update equipments tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {UpdateApplicationReviewEquipmentForm} updateApplicationReviewEquipmentForm Equipments tab panel info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewEquipments(
    applicationReviewID: string,
    updateApplicationReviewEquipmentForm: UpdateApplicationReviewEquipmentForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewEquipments(
        applicationReviewID,
        updateApplicationReviewEquipmentForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update losses tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {UpdateApplicationReviewLossesForm} updateApplicationReviewLossesForm Losses tab panel info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewLosses(
    applicationReviewID: string,
    updateApplicationReviewLossesForm: UpdateApplicationReviewLossesForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewLosses(
        applicationReviewID,
        updateApplicationReviewLossesForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update application review\'s notes
   * @param {string} applicationReviewID Application Review ID
   * @param {ApplicationReviewNotes} applicationReviewNotes Note info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewNotes(
    applicationReviewID: string,
    applicationReviewNotes: ApplicationReviewNotes,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewNotes(
        applicationReviewID,
        applicationReviewNotes,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update operations tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {UpdateApplicationReviewOperationForm} updateApplicationReviewOperationForm Operations tab panel info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewOperations(
    applicationReviewID: string,
    updateApplicationReviewOperationForm: UpdateApplicationReviewOperationForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewOperations(
        applicationReviewID,
        updateApplicationReviewOperationForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update packages tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {UpdateApplicationReviewPackagesForm} updateApplicationReviewPackagesForm Packages tab panel info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewPackages(
    applicationReviewID: string,
    updateApplicationReviewPackagesForm: UpdateApplicationReviewPackagesForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewPackages(
        applicationReviewID,
        updateApplicationReviewPackagesForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update safety tab panel info
   * @param {string} applicationReviewID Application Review ID
   * @param {UpdateApplicationReviewSafetyForm} updateApplicationReviewSafetyForm Safety tab panel info
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewSafety(
    applicationReviewID: string,
    updateApplicationReviewSafetyForm: UpdateApplicationReviewSafetyForm,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewSafety(
        applicationReviewID,
        updateApplicationReviewSafetyForm,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Update application review\'s safety score
   * @param {string} applicationReviewID
   * @param {ApplicationReviewSafetyScoreFormV2} applicationReviewSafetyScoreFormV2
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public updateApplicationReviewSafetyScoreV2(
    applicationReviewID: string,
    applicationReviewSafetyScoreFormV2: ApplicationReviewSafetyScoreFormV2,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .updateApplicationReviewSafetyScoreV2(
        applicationReviewID,
        applicationReviewSafetyScoreFormV2,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Upload documents for an application_review
   * @param {string} applicationReviewID Application Review ID
   * @param {any} file
   * @param {string} fileType
   * @param {string} fileDestinationGroup
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UnderwritingApi
   */
  public uploadApplicationReviewDocuments(
    applicationReviewID: string,
    file: any,
    fileType: string,
    fileDestinationGroup: string,
    options?: AxiosRequestConfig,
  ) {
    return UnderwritingApiFp(this.configuration)
      .uploadApplicationReviewDocuments(
        applicationReviewID,
        file,
        fileType,
        fileDestinationGroup,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }
}
