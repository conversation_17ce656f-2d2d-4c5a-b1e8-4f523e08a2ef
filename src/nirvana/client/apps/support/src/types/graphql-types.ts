import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  Time: string;
  bool: boolean;
  float64: number;
  int: number;
  int64: number;
  string: string;
};

export type Address = {
  __typename?: 'Address';
  city: Scalars['string'];
  state: Scalars['string'];
  street1: Scalars['string'];
  street2?: Maybe<Scalars['string']>;
  zip: Scalars['string'];
};

export type Address_InputObject = {
  city: Scalars['string'];
  state: Scalars['string'];
  street1: Scalars['string'];
  street2?: InputMaybe<Scalars['string']>;
  zip: Scalars['string'];
};

export enum AgencyRole {
  /** Accounting */
  Accounting = 'Accounting',
  /** Billing */
  Billing = 'Billing',
  /** BrandMarketer */
  BrandMarketer = 'BrandMarketer',
  /** Claims */
  Claims = 'Claims',
  /** ContentDistributor */
  ContentDistributor = 'ContentDistributor',
  /** CustomerService */
  CustomerService = 'CustomerService',
  /** DirectBilling */
  DirectBilling = 'DirectBilling',
  /** Leadership */
  Leadership = 'Leadership',
  /** LegalOrCompliance */
  LegalOrCompliance = 'LegalOrCompliance',
  /** Marketer */
  Marketer = 'Marketer',
  /** Producer */
  Producer = 'Producer',
  /** RiskServices */
  RiskServices = 'RiskServices',
  /** Safety */
  Safety = 'Safety',
}

export type BasicScoreDetail = {
  __typename?: 'BasicScoreDetail';
  measure?: Maybe<Scalars['float64']>;
  name: Scalars['string'];
  percentile?: Maybe<Scalars['int64']>;
  threshold: Scalars['float64'];
};

export type BasicUser = {
  __typename?: 'BasicUser';
  email: Scalars['string'];
  name: Scalars['string'];
};

export enum Carrier {
  /** MSTransverse */
  MsTransverse = 'MSTransverse',
}

export enum Category {
  /** ControlledSubstancesAlcohol */
  ControlledSubstancesAlcohol = 'ControlledSubstancesAlcohol',
  /** CrashIndicator */
  CrashIndicator = 'CrashIndicator',
  /** DriverFitness */
  DriverFitness = 'DriverFitness',
  /** HMCompliance */
  HmCompliance = 'HMCompliance',
  /** HOSCompliance */
  HosCompliance = 'HOSCompliance',
  /** InsuranceOther */
  InsuranceOther = 'InsuranceOther',
  /** UnsafeDriving */
  UnsafeDriving = 'UnsafeDriving',
  /** Unspecified */
  Unspecified = 'Unspecified',
  /** VehicleMaintenance */
  VehicleMaintenance = 'VehicleMaintenance',
}

export type Chart = {
  __typename?: 'Chart';
  geoJSON?: Maybe<GeoJson>;
  projection?: Maybe<LineChart>;
  table?: Maybe<Table>;
  title?: Maybe<Scalars['string']>;
  url?: Maybe<Scalars['string']>;
};

export type ClaimFeedback = {
  __typename?: 'ClaimFeedback';
  category: FeedbackCategory;
  claimId: Scalars['string'];
  createdAt: Scalars['Time'];
  createdBy: Scalars['string'];
  id: Scalars['string'];
  rating: Scalars['int'];
  value?: Maybe<Scalars['string']>;
};

export enum ClaimStatus {
  /** Cancelled */
  Cancelled = 'Cancelled',
  /** Closed */
  Closed = 'Closed',
  /** CreatedInError */
  CreatedInError = 'CreatedInError',
  /** Invalid */
  Invalid = 'Invalid',
  /** Open */
  Open = 'Open',
  /** Reopen */
  Reopen = 'Reopen',
}

export type ClaimStatusChange = {
  __typename?: 'ClaimStatusChange';
  claimExternalId: Scalars['string'];
  createdAt: Scalars['Time'];
  id: Scalars['string'];
  value: ClaimStatus;
};

export type ClaimSummary = {
  __typename?: 'ClaimSummary';
  claimId: Scalars['string'];
  createdAt: Scalars['Time'];
  feedback?: Maybe<Scalars['bool']>;
  id: Scalars['string'];
  intervalEnd: Scalars['Time'];
  intervalStart: Scalars['Time'];
  scheduled: Scalars['bool'];
  summary: Scalars['string'];
  title: Scalars['string'];
};

export type ClaimSummaryFeedback = {
  __typename?: 'ClaimSummaryFeedback';
  claimSummaryId: Scalars['string'];
  createdAt: Scalars['Time'];
  createdBy: Scalars['string'];
  id: Scalars['string'];
  rating: Scalars['bool'];
  updatedAt: Scalars['Time'];
};

export type ClaimVehicle_InputObject = {
  registrationNumber?: InputMaybe<Scalars['string']>;
  vin?: InputMaybe<Scalars['string']>;
};

export enum ClaimsProvider {
  /** Nars */
  Nars = 'Nars',
  /** Snapsheet */
  Snapsheet = 'Snapsheet',
  /** Undetermined */
  Undetermined = 'Undetermined',
}

export type ComparisonMarkdown = {
  __typename?: 'ComparisonMarkdown';
  comparisons: Array<Scalars['string']>;
};

export type CoverageWithSymbols = {
  __typename?: 'CoverageWithSymbols';
  coverage: Scalars['string'];
  symbols: Array<Scalars['string']>;
};

export type Data = {
  __typename?: 'Data';
  xVal?: Maybe<Scalars['string']>;
  yVals: Array<Scalars['float64']>;
};

export type DeleteSentInspectionsResponse = {
  __typename?: 'DeleteSentInspectionsResponse';
  deletedCount: Scalars['int64'];
};

export enum DotRating {
  /** ConditionalRating */
  ConditionalRating = 'ConditionalRating',
  /** SatisfactoryRating */
  SatisfactoryRating = 'SatisfactoryRating',
  /** Unrated */
  Unrated = 'Unrated',
  /** UnsatisfactoryRating */
  UnsatisfactoryRating = 'UnsatisfactoryRating',
}

export type DraftFnolAttachment = {
  __typename?: 'DraftFnolAttachment';
  handleId: Scalars['string'];
  key: Scalars['string'];
  url: Scalars['string'];
};

export type DraftFnolContact = {
  __typename?: 'DraftFnolContact';
  draftFnolId: Scalars['string'];
  email?: Maybe<Scalars['string']>;
  firstName?: Maybe<Scalars['string']>;
  id: Scalars['string'];
  lastName?: Maybe<Scalars['string']>;
  phone?: Maybe<Scalars['string']>;
};

export type DraftFnolVehicle = {
  __typename?: 'DraftFnolVehicle';
  draftFnolId: Scalars['string'];
  id: Scalars['string'];
  isInsuredVehicle?: Maybe<Scalars['bool']>;
  registrationNumber?: Maybe<Scalars['string']>;
  vin?: Maybe<Scalars['string']>;
};

export type DraftReporter_InputObject = {
  email?: InputMaybe<Scalars['string']>;
  firstName?: InputMaybe<Scalars['string']>;
  id?: InputMaybe<Scalars['string']>;
  lastName?: InputMaybe<Scalars['string']>;
  phone?: InputMaybe<Scalars['string']>;
};

export type DraftVehicle_InputObject = {
  id?: InputMaybe<Scalars['string']>;
  registrationNumber?: InputMaybe<Scalars['string']>;
  vin?: InputMaybe<Scalars['string']>;
};

export type Endorsement = {
  __typename?: 'Endorsement';
  approvedAt: Scalars['string'];
  changeTypes: Array<Scalars['string']>;
  documentID: Scalars['string'];
  effectiveInterval: EndorsementEffectiveInterval;
  id: Scalars['string'];
  signedLink: ExpirableLink;
  supportingDocsAndForms: Array<SupportingDocumentOrForm>;
  underwriter: BasicUser;
};

export type EndorsementEffectiveInterval = {
  __typename?: 'EndorsementEffectiveInterval';
  effectiveDate: Scalars['string'];
  expirationDate: Scalars['string'];
};

export type ExpirableLink = {
  __typename?: 'ExpirableLink';
  expiration: Scalars['string'];
  link: Scalars['string'];
};

export type ExplainabilityRecommendation = {
  __typename?: 'ExplainabilityRecommendation';
  category: Scalars['string'];
  percentile: Scalars['float64'];
  recommendedAmountComparison: Array<Scalars['string']>;
  recommendedAmountComparisonMarkdown?: Maybe<ComparisonMarkdown>;
};

export enum FeedbackCategory {
  /** FasterClaimResolution */
  FasterClaimResolution = 'FasterClaimResolution',
  /** FrequentAdjusterCommunication */
  FrequentAdjusterCommunication = 'FrequentAdjusterCommunication',
  /** Invalid */
  Invalid = 'Invalid',
  /** OfferFairerCompensation */
  OfferFairerCompensation = 'OfferFairerCompensation',
  /** Other */
  Other = 'Other',
  /** OutstandingService */
  OutstandingService = 'OutstandingService',
  /** ProvideBetterSupport */
  ProvideBetterSupport = 'ProvideBetterSupport',
  /** RequireFewerDocuments */
  RequireFewerDocuments = 'RequireFewerDocuments',
}

export enum FlagCategory {
  /** BASICS */
  Basics = 'BASICS',
  /** Crashes */
  Crashes = 'Crashes',
  /** DOTRating */
  DotRating = 'DOTRating',
  /** Insurance */
  Insurance = 'Insurance',
  /** RelatedEntities */
  RelatedEntities = 'RelatedEntities',
  /** Violations */
  Violations = 'Violations',
}

export type FlagConnection = {
  __typename?: 'FlagConnection';
  edges: Array<FlagEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['int64'];
};

export type FlagEdge = {
  __typename?: 'FlagEdge';
  cursor: Scalars['string'];
  node: Flag;
};

export enum FlagSeverity {
  /** Critical */
  Critical = 'Critical',
  /** Minor */
  Minor = 'Minor',
  /** Moderate */
  Moderate = 'Moderate',
}

export type FleetSafetyReportConnection = {
  __typename?: 'FleetSafetyReportConnection';
  edges: Array<FleetSafetyReportEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['int64'];
};

export type FleetSafetyReportEdge = {
  __typename?: 'FleetSafetyReportEdge';
  cursor: Scalars['string'];
  node: FleetSafetyReport;
};

export type FleetSearchResult = {
  __typename?: 'FleetSearchResult';
  dotNumber: Scalars['string'];
  lastViewedAt?: Maybe<Scalars['Time']>;
  name: Scalars['string'];
  state?: Maybe<Scalars['string']>;
};

export type Fnol = {
  __typename?: 'Fnol';
  attachments: Array<FnolAttachment>;
  clientClaimNumber: Scalars['string'];
  contacts: Array<FnolContact>;
  createdAt: Scalars['Time'];
  createdBy: Scalars['string'];
  id: Scalars['string'];
  incidentDescription: Scalars['string'];
  injuriesInvolved: Scalars['bool'];
  lossDatetime: Scalars['Time'];
  lossLocation: Scalars['string'];
  lossState: Scalars['string'];
  noticeType: FnolNoticeType;
  policeAgencyName?: Maybe<Scalars['string']>;
  policeReportNumber?: Maybe<Scalars['string']>;
  policy?: Maybe<Policy>;
  policyNumber: Scalars['string'];
  provider: ClaimsProvider;
  status: FnolStatus;
  submittedFrom: FnolSource;
  vehicles: Array<FnolVehicle>;
};

export type FnolAttachment = {
  __typename?: 'FnolAttachment';
  createdAt: Scalars['Time'];
  fnolId: Scalars['string'];
  handleId: Scalars['string'];
  id: Scalars['string'];
};

export type FnolContact = {
  __typename?: 'FnolContact';
  contactType: Scalars['int'];
  email?: Maybe<Scalars['string']>;
  firstName: Scalars['string'];
  fnolId: Scalars['string'];
  id: Scalars['string'];
  lastName: Scalars['string'];
  phone: Scalars['string'];
};

export enum FnolNoticeType {
  /** Claim */
  Claim = 'Claim',
  /** Invalid */
  Invalid = 'Invalid',
  /** Report */
  Report = 'Report',
}

export enum FnolSource {
  /** FinolaAutoSubmit */
  FinolaAutoSubmit = 'FinolaAutoSubmit',
  /** FinolaEmail */
  FinolaEmail = 'FinolaEmail',
  /** SafetyApp */
  SafetyApp = 'SafetyApp',
  /** SupportApp */
  SupportApp = 'SupportApp',
  /** Unknown */
  Unknown = 'Unknown',
}

export enum FnolStatus {
  /** Draft */
  Draft = 'Draft',
  /** Invalid */
  Invalid = 'Invalid',
  /** Sendable */
  Sendable = 'Sendable',
  /** Sent */
  Sent = 'Sent',
}

export type FnolVehicle = {
  __typename?: 'FnolVehicle';
  fnolId: Scalars['string'];
  id: Scalars['string'];
  isInsuredVehicle: Scalars['bool'];
  registrationNumber?: Maybe<Scalars['string']>;
  vIN?: Maybe<Scalars['string']>;
};

export type GenerateBordereauxReportResponse = {
  __typename?: 'GenerateBordereauxReportResponse';
  downloadURL: Scalars['string'];
  id: Scalars['string'];
};

export type GeoFeature = {
  __typename?: 'GeoFeature';
  geometry?: Maybe<Geometry>;
  properties?: Maybe<Properties>;
  type?: Maybe<Scalars['string']>;
};

export type GeoJson = {
  __typename?: 'GeoJSON';
  features: Array<GeoFeature>;
};

export type Geometry = {
  __typename?: 'Geometry';
  coordinates: Array<Array<Array<Scalars['float64']>>>;
  type: Scalars['string'];
};

export type IssScore = {
  __typename?: 'ISSScore';
  date: Scalars['Time'];
  discount?: Maybe<Scalars['float64']>;
  errorMessage?: Maybe<Scalars['string']>;
  hardBrakingCountPer1000Miles?: Maybe<Scalars['float64']>;
  hardBrakingPercentile?: Maybe<Scalars['float64']>;
  month: Scalars['int'];
  speedingCountPer1000Miles?: Maybe<Scalars['float64']>;
  speedingPercentile?: Maybe<Scalars['float64']>;
  value?: Maybe<Scalars['float64']>;
  year: Scalars['int'];
};

export type InitiateDriverViolationFetchResults = {
  __typename?: 'InitiateDriverViolationFetchResults';
  browserWSEndpoint: Scalars['string'];
  twoFactorUrl: Scalars['string'];
};

export enum InspectionLevel {
  /** DriverOnly */
  DriverOnly = 'DriverOnly',
  /** Full */
  Full = 'Full',
  /** Material */
  Material = 'Material',
  /** SpecialStudy */
  SpecialStudy = 'SpecialStudy',
  /** Terminal */
  Terminal = 'Terminal',
  /** Unknown */
  Unknown = 'Unknown',
  /** WalkAround */
  WalkAround = 'WalkAround',
}

export type InspectionRecordConnection = {
  __typename?: 'InspectionRecordConnection';
  edges: Array<InspectionRecordEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['int64'];
};

export type InspectionRecordEdge = {
  __typename?: 'InspectionRecordEdge';
  cursor: Scalars['string'];
  node: Inspection;
};

export type InsuranceRecord = {
  __typename?: 'InsuranceRecord';
  carrier: Scalars['string'];
  effectiveDateFrom: Scalars['Time'];
  effectiveDateTo?: Maybe<Scalars['Time']>;
  policyNumber?: Maybe<Scalars['string']>;
  status?: Maybe<Scalars['string']>;
  types: Scalars['string'];
};

export type LineChart = {
  __typename?: 'LineChart';
  data: Array<Data>;
  lineConfigs: Array<LineConfig>;
  threshold?: Maybe<Scalars['float64']>;
  xField?: Maybe<Scalars['string']>;
  xLabel?: Maybe<Scalars['string']>;
  yLabel?: Maybe<Scalars['string']>;
};

export type LineConfig = {
  __typename?: 'LineConfig';
  dashed?: Maybe<Scalars['bool']>;
  name?: Maybe<Scalars['string']>;
};

export enum LineOfBusiness {
  /** AutoLiability */
  AutoLiability = 'AutoLiability',
  /** GeneralLiability */
  GeneralLiability = 'GeneralLiability',
  /** Invalid */
  Invalid = 'Invalid',
  /** MotorTruckCargo */
  MotorTruckCargo = 'MotorTruckCargo',
}

export type Location = {
  __typename?: 'Location';
  countyCode?: Maybe<Scalars['string']>;
  countyName?: Maybe<Scalars['string']>;
};

export type MonthlyValues = {
  __typename?: 'MonthlyValues';
  mileage?: Maybe<Scalars['float64']>;
  month: Scalars['int'];
  pU?: Maybe<Scalars['float64']>;
  utilization?: Maybe<Scalars['float64']>;
  year: Scalars['int'];
};

export type Mutation = {
  __typename?: 'Mutation';
  activateUser?: Maybe<ActivateUserResponse>;
  archiveDraftFnols: Scalars['bool'];
  completeDriverViolationFetch: Scalars['bool'];
  createAgencyAndBDMapping?: Maybe<Agency>;
  createClaimFeedback?: Maybe<ClaimFeedback>;
  createFNOL?: Maybe<Fnol>;
  createFleetSafetyReport?: Maybe<FleetSafetyReport>;
  createRole?: Maybe<Role>;
  createUser?: Maybe<User>;
  deactivateUser: Scalars['bool'];
  deleteRole: Scalars['bool'];
  deleteSentInspections: DeleteSentInspectionsResponse;
  finalizeTelematics?: Maybe<FinalizeTelematicsResponse>;
  generateBordereauxReport?: Maybe<GenerateBordereauxReportResponse>;
  generateClaimSummary?: Maybe<ClaimSummary>;
  initiateTelematics?: Maybe<InitiateTelematicsResponse>;
  inviteAgencyUserFromAgents?: Maybe<AgencyUserInvite>;
  inviteAgencyUserFromSupport?: Maybe<AgencyUserInvite>;
  inviteFleetUser?: Maybe<FleetUserInvite>;
  patchAgency?: Maybe<Agency>;
  patchUser?: Maybe<User>;
  printFleetSafetyReport?: Maybe<PrintFleetSafetyReportLink>;
  setFleetStarredStatus: Scalars['bool'];
  shareSafetyReport?: Maybe<SafetyReportShare>;
  submitFnol?: Maybe<SubmitFnolResponse>;
  updateAgentDetail: Scalars['bool'];
  updateAgentLicense: Scalars['bool'];
  updateUserNotificationPreferences: Array<UserWorkflowPreference>;
  upsertClaimSummaryFeedback?: Maybe<ClaimSummaryFeedback>;
  upsertDraftFNOL?: Maybe<DraftFnols>;
};

export type MutationActivateUserArgs = {
  email: Scalars['string'];
  firstName: Scalars['string'];
  lastName: Scalars['string'];
  password: Scalars['string'];
  phoneNumber?: InputMaybe<Scalars['string']>;
  profilePicture?: InputMaybe<Scalars['string']>;
  title?: InputMaybe<Scalars['string']>;
};

export type MutationArchiveDraftFnolsArgs = {
  ids: Array<Scalars['string']>;
};

export type MutationCompleteDriverViolationFetchArgs = {
  browserWSEndpoint: Scalars['string'];
  reportId: Scalars['string'];
  twoFA: Scalars['string'];
  twoFactorUrl: Scalars['string'];
};

export type MutationCreateAgencyAndBdMappingArgs = {
  address: Address_InputObject;
  fleetBD?: InputMaybe<Scalars['string']>;
  name: Scalars['string'];
  nonFleetBD?: InputMaybe<Scalars['string']>;
};

export type MutationCreateClaimFeedbackArgs = {
  category: FeedbackCategory;
  claimId: Scalars['string'];
  rating: Scalars['int'];
  value?: InputMaybe<Scalars['string']>;
};

export type MutationCreateFnolArgs = {
  attachmentKeys?: InputMaybe<Array<Scalars['string']>>;
  description?: InputMaybe<Scalars['string']>;
  draftFnolId?: InputMaybe<Scalars['string']>;
  injuriesInvolved?: InputMaybe<Scalars['bool']>;
  insuredName?: InputMaybe<Scalars['string']>;
  insuredVehicles?: InputMaybe<Array<ClaimVehicle_InputObject>>;
  lineOfBusiness?: InputMaybe<LineOfBusiness>;
  lossDate: Scalars['Time'];
  lossLocation?: InputMaybe<Scalars['string']>;
  lossState: Scalars['string'];
  noticeType: FnolNoticeType;
  otherVehicles?: InputMaybe<Array<ClaimVehicle_InputObject>>;
  police?: InputMaybe<Police_InputObject>;
  policyNumber: Scalars['string'];
  provider?: InputMaybe<ClaimsProvider>;
  reporter: Reporter_InputObject;
  source?: InputMaybe<FnolSource>;
};

export type MutationCreateFleetSafetyReportArgs = {
  delegateUserID?: InputMaybe<Scalars['string']>;
  dotNumber: Scalars['string'];
  starred?: InputMaybe<Scalars['bool']>;
};

export type MutationCreateRoleArgs = {
  agencyID?: InputMaybe<Scalars['string']>;
  fleetID?: InputMaybe<Scalars['string']>;
  group: RoleGroupEnum;
  userID: Scalars['string'];
};

export type MutationCreateUserArgs = {
  email: Scalars['string'];
  firstName: Scalars['string'];
  lastName: Scalars['string'];
  password: Scalars['string'];
  phoneNumber?: InputMaybe<Scalars['string']>;
  profilePicture?: InputMaybe<Scalars['string']>;
  roles: Array<CreateUserRoleArgs_InputObject>;
  title?: InputMaybe<Scalars['string']>;
};

export type MutationDeactivateUserArgs = {
  userID: Scalars['string'];
};

export type MutationDeleteRoleArgs = {
  roleID: Scalars['string'];
  userID: Scalars['string'];
};

export type MutationDeleteSentInspectionsArgs = {
  inspectionIDs: Array<Scalars['int64']>;
  reportID: Scalars['string'];
};

export type MutationFinalizeTelematicsArgs = {
  authCode: Scalars['string'];
  error?: InputMaybe<Scalars['string']>;
  scope?: InputMaybe<Scalars['string']>;
  state: Scalars['string'];
  tsp: Tsp;
};

export type MutationGenerateBordereauxReportArgs = {
  carrier: Carrier;
};

export type MutationGenerateClaimSummaryArgs = {
  claimId: Scalars['string'];
};

export type MutationInitiateTelematicsArgs = {
  safetyReportID: Scalars['string'];
  tsp: Tsp;
};

export type MutationInviteAgencyUserFromAgentsArgs = {
  agencyID: Scalars['string'];
  email: Scalars['string'];
  role: RoleGroupEnum;
  sFDCAgencyRoles?: InputMaybe<Array<AgencyRole>>;
};

export type MutationInviteAgencyUserFromSupportArgs = {
  agencyID: Scalars['string'];
  email: Scalars['string'];
  role: RoleGroupEnum;
  sFDCAgencyRoles?: InputMaybe<Array<AgencyRole>>;
};

export type MutationInviteFleetUserArgs = {
  email: Scalars['string'];
  fleetSafetyReportID: Scalars['string'];
};

export type MutationPatchAgencyArgs = {
  address?: InputMaybe<Address_InputObject>;
  id: Scalars['string'];
  name?: InputMaybe<Scalars['string']>;
};

export type MutationPatchUserArgs = {
  email?: InputMaybe<Scalars['string']>;
  firstName?: InputMaybe<Scalars['string']>;
  id: Scalars['string'];
  lastName?: InputMaybe<Scalars['string']>;
  oldPassword?: InputMaybe<Scalars['string']>;
  password?: InputMaybe<Scalars['string']>;
  phoneNumber?: InputMaybe<Scalars['string']>;
  profilePicture?: InputMaybe<Scalars['string']>;
  title?: InputMaybe<Scalars['string']>;
};

export type MutationPrintFleetSafetyReportArgs = {
  endTime?: InputMaybe<Scalars['Time']>;
  reportID: Scalars['string'];
  startTime?: InputMaybe<Scalars['Time']>;
};

export type MutationSetFleetStarredStatusArgs = {
  markStarred: Scalars['bool'];
  reportId: Scalars['string'];
  userID?: InputMaybe<Scalars['string']>;
};

export type MutationShareSafetyReportArgs = {
  delegateUserID?: InputMaybe<Scalars['string']>;
  id: Scalars['string'];
};

export type MutationSubmitFnolArgs = {
  attachmentKeys?: InputMaybe<Array<Scalars['string']>>;
  description?: InputMaybe<Scalars['string']>;
  draftFnolId?: InputMaybe<Scalars['string']>;
  injuriesInvolved?: InputMaybe<Scalars['bool']>;
  insuredName?: InputMaybe<Scalars['string']>;
  insuredVehicles?: InputMaybe<Array<ClaimVehicle_InputObject>>;
  lineOfBusiness?: InputMaybe<LineOfBusiness>;
  lossDate: Scalars['Time'];
  lossLocation?: InputMaybe<Scalars['string']>;
  lossState: Scalars['string'];
  noticeType: FnolNoticeType;
  otherVehicles?: InputMaybe<Array<ClaimVehicle_InputObject>>;
  police?: InputMaybe<Police_InputObject>;
  policyNumber: Scalars['string'];
  provider?: InputMaybe<ClaimsProvider>;
  reporter: Reporter_InputObject;
  source?: InputMaybe<FnolSource>;
};

export type MutationUpdateAgentDetailArgs = {
  officeDepartmentRegion: Scalars['string'];
  phoneNumber: Scalars['string'];
  preferredStates: Array<Scalars['string']>;
  sFDCAgencyRoles?: InputMaybe<Array<AgencyRole>>;
  title: Scalars['string'];
  workLocation?: InputMaybe<Scalars['string']>;
};

export type MutationUpdateAgentLicenseArgs = {
  effectiveDate?: InputMaybe<Scalars['Time']>;
  expirationDate?: InputMaybe<Scalars['Time']>;
  id: Scalars['string'];
  licenseNumber?: InputMaybe<Scalars['string']>;
  licenseStatus?: InputMaybe<Scalars['string']>;
  licenseType?: InputMaybe<Scalars['string']>;
  state?: InputMaybe<Scalars['string']>;
};

export type MutationUpdateUserNotificationPreferencesArgs = {
  preferences: Array<UserWorkflowPreference_InputObject>;
};

export type MutationUpsertClaimSummaryFeedbackArgs = {
  claimSummaryId: Scalars['string'];
  rating: Scalars['int'];
};

export type MutationUpsertDraftFnolArgs = {
  attachmentKeys?: InputMaybe<Array<Scalars['string']>>;
  dotNumber?: InputMaybe<Scalars['int64']>;
  id?: InputMaybe<Scalars['string']>;
  incidentDescription?: InputMaybe<Scalars['string']>;
  injuriesInvolved?: InputMaybe<Scalars['bool']>;
  insuredName?: InputMaybe<Scalars['string']>;
  insuredVehicles?: InputMaybe<Array<DraftVehicle_InputObject>>;
  lossDatetime?: InputMaybe<Scalars['Time']>;
  lossLocation?: InputMaybe<Scalars['string']>;
  lossState?: InputMaybe<Scalars['string']>;
  noticeType?: InputMaybe<FnolNoticeType>;
  otherVehicles?: InputMaybe<Array<DraftVehicle_InputObject>>;
  police?: InputMaybe<Police_InputObject>;
  policyNumber?: InputMaybe<Scalars['string']>;
  reporter?: InputMaybe<DraftReporter_InputObject>;
  source?: InputMaybe<FnolSource>;
};

export type Note = {
  __typename?: 'Note';
  category?: Maybe<Scalars['string']>;
  claimExternalId: Scalars['string'];
  createdAt: Scalars['Time'];
  externalId: Scalars['string'];
  id: Scalars['string'];
  modifiedAt: Scalars['Time'];
  source: ClaimsProvider;
  updatedAt: Scalars['Time'];
  value: Scalars['string'];
};

export type OosSummary = {
  __typename?: 'OOSSummary';
  category: Scalars['string'];
  inspections: Scalars['int'];
  nationalAverage: Scalars['float64'];
  oOSPercent: Scalars['float64'];
  oOSViolations: Scalars['int'];
};

export type PageInfo = {
  __typename?: 'PageInfo';
  endCursor: Scalars['string'];
  hasNextPage: Scalars['bool'];
  hasPrevPage: Scalars['bool'];
  pages: Array<Scalars['string']>;
  startCursor: Scalars['string'];
};

export type Police_InputObject = {
  agencyName?: InputMaybe<Scalars['string']>;
  reportNumber?: InputMaybe<Scalars['string']>;
};

export enum PolicyCoverageEnums {
  /** CoverageAutoLiability */
  CoverageAutoLiability = 'CoverageAutoLiability',
  /** CoverageAutoPhysicalDamage */
  CoverageAutoPhysicalDamage = 'CoverageAutoPhysicalDamage',
  /** CoverageGeneralLiability */
  CoverageGeneralLiability = 'CoverageGeneralLiability',
  /** CoverageMotorTruckCargo */
  CoverageMotorTruckCargo = 'CoverageMotorTruckCargo',
}

export type PolicyDriver = {
  __typename?: 'PolicyDriver';
  dateOfBirth: Scalars['string'];
  dateOfHire: Scalars['string'];
  firstName: Scalars['string'];
  isIncludedInPolicy: Scalars['bool'];
  isOutOfState: Scalars['bool'];
  isOwner: Scalars['bool'];
  lastName: Scalars['string'];
  licenseNumber: Scalars['string'];
  licenseState: Scalars['string'];
  yearsOfExperience: Scalars['int'];
};

export enum PolicyState {
  /** Active */
  Active = 'Active',
  /** CancellationFiled */
  CancellationFiled = 'CancellationFiled',
  /** Cancelled */
  Cancelled = 'Cancelled',
  /** Created */
  Created = 'Created',
  /** Expired */
  Expired = 'Expired',
  /** Generated */
  Generated = 'Generated',
  /** Invalid */
  Invalid = 'Invalid',
  /** Stale */
  Stale = 'Stale',
}

export type PolicyVehicle = {
  __typename?: 'PolicyVehicle';
  make: Scalars['string'];
  model: Scalars['string'];
  statedValue?: Maybe<Scalars['int']>;
  vehicleClass: Scalars['string'];
  vehicleType: Scalars['string'];
  vin: Scalars['string'];
  weightClass: Scalars['string'];
  year: Scalars['int'];
};

export type PresignedUploadLink = {
  __typename?: 'PresignedUploadLink';
  key: Scalars['string'];
  url: Scalars['string'];
};

export type PrintFleetSafetyReportLink = {
  __typename?: 'PrintFleetSafetyReportLink';
  id: Scalars['string'];
  url: Scalars['string'];
};

export enum ProgramType {
  /** BusinessAuto */
  BusinessAuto = 'BusinessAuto',
  /** Fleet */
  Fleet = 'Fleet',
  /** NonFleet */
  NonFleet = 'NonFleet',
  /** Unkown */
  Unkown = 'Unkown',
}

export type Properties = {
  __typename?: 'Properties';
  countyCode?: Maybe<Scalars['string']>;
  inspectionRecords: Array<Inspection>;
  inspection_count?: Maybe<Scalars['int64']>;
  inspection_percentage?: Maybe<Scalars['float64']>;
  name?: Maybe<Scalars['string']>;
  violation_count?: Maybe<Scalars['int64']>;
  violation_percentage?: Maybe<Scalars['float64']>;
};

export type Query = {
  __typename?: 'Query';
  agencies: Array<Agency>;
  agency?: Maybe<Agency>;
  cameraEvents: Array<CameraEvents>;
  claimById?: Maybe<Claims>;
  claims: Array<Claims>;
  claimsByDOTNumber: Array<Claims>;
  claimsPresignedUploadLinks: Array<PresignedUploadLink>;
  draftFnolById?: Maybe<DraftFnols>;
  draftFnols: Array<DraftFnols>;
  fetchFleetSafetySearch: Array<FleetSearchResult>;
  fleet?: Maybe<Fleet>;
  fleetSafetyReport?: Maybe<FleetSafetyReport>;
  fleetSafetyReports: FleetSafetyReportConnection;
  fnols: Array<Fnol>;
  getBordereauxReports: Array<BordereauxReport>;
  getSubmittableProviderOptions: SubmittableOptions;
  initiateDriverViolationFetch?: Maybe<InitiateDriverViolationFetchResults>;
  policies: Array<Policy>;
  policy?: Maybe<Policy>;
  summariesForClaimId: Array<ClaimSummary>;
  user?: Maybe<User>;
  userNotificationPreferences: Array<UserWorkflowPreference>;
  users: Array<User>;
};

export type QueryAgencyArgs = {
  id: Scalars['string'];
};

export type QueryCameraEventsArgs = {
  endTime: Scalars['Time'];
  policyNumber: Scalars['string'];
  startTime: Scalars['Time'];
};

export type QueryClaimByIdArgs = {
  id: Scalars['string'];
};

export type QueryClaimsByDotNumberArgs = {
  dotNumber: Scalars['string'];
};

export type QueryClaimsPresignedUploadLinksArgs = {
  fileNames: Array<Scalars['string']>;
};

export type QueryDraftFnolByIdArgs = {
  id: Scalars['string'];
};

export type QueryDraftFnolsArgs = {
  hasFNOL?: InputMaybe<Scalars['bool']>;
};

export type QueryFetchFleetSafetySearchArgs = {
  searchText?: InputMaybe<Scalars['string']>;
};

export type QueryFleetArgs = {
  dotNumber?: InputMaybe<Scalars['string']>;
  id?: InputMaybe<Scalars['string']>;
};

export type QueryFleetSafetyReportArgs = {
  id: Scalars['string'];
};

export type QueryFleetSafetyReportsArgs = {
  after?: InputMaybe<Scalars['string']>;
  before?: InputMaybe<Scalars['string']>;
  filterText?: InputMaybe<Scalars['string']>;
  filterTextFields?: InputMaybe<Array<Scalars['string']>>;
  filterType?: InputMaybe<Scalars['string']>;
  first?: InputMaybe<Scalars['int64']>;
  last?: InputMaybe<Scalars['int64']>;
  sortBy?: InputMaybe<Scalars['string']>;
  sortOrder?: InputMaybe<SortOrder>;
};

export type QueryFnolsArgs = {
  dotNumber?: InputMaybe<Scalars['int64']>;
};

export type QueryGetSubmittableProviderOptionsArgs = {
  policyNumber: Scalars['string'];
};

export type QueryInitiateDriverViolationFetchArgs = {
  password: Scalars['string'];
  reportId: Scalars['string'];
  username: Scalars['string'];
};

export type QueryPoliciesArgs = {
  activeDateIn?: InputMaybe<Scalars['Time']>;
  dotNumber?: InputMaybe<Scalars['string']>;
  policyStates?: InputMaybe<Array<PolicyState>>;
};

export type QueryPolicyArgs = {
  claimId: Scalars['string'];
};

export type QuerySummariesForClaimIdArgs = {
  id: Scalars['string'];
};

export type QueryUserArgs = {
  id?: InputMaybe<Scalars['string']>;
};

export type Reason = {
  __typename?: 'Reason';
  basicCategory?: Maybe<Scalars['string']>;
  link?: Maybe<Scalars['string']>;
  vINs: Array<Scalars['string']>;
  violationCount?: Maybe<Scalars['int']>;
  violationGroup?: Maybe<Scalars['string']>;
  weightage?: Maybe<Scalars['float64']>;
};

export type RecommendationConnection = {
  __typename?: 'RecommendationConnection';
  edges: Array<RecommendationEdge>;
  pageInfo: PageInfo;
  totalCount: Scalars['int64'];
};

export type RecommendationEdge = {
  __typename?: 'RecommendationEdge';
  cursor: Scalars['string'];
  node: Recommendation;
};

export enum RecommendationImpact {
  /** High */
  High = 'High',
  /** Low */
  Low = 'Low',
  /** Medium */
  Medium = 'Medium',
}

export type RecommendationSection = {
  __typename?: 'RecommendationSection';
  chart?: Maybe<Chart>;
  markdown: Scalars['string'];
  reasons: Array<Reason>;
};

export type Reporter_InputObject = {
  email?: InputMaybe<Scalars['string']>;
  firstName?: InputMaybe<Scalars['string']>;
  lastName?: InputMaybe<Scalars['string']>;
  phone?: InputMaybe<Scalars['string']>;
};

export enum RoleGroupEnum {
  /** AgencyAccountManagerRole */
  AgencyAccountManagerRole = 'AgencyAccountManagerRole',
  /** AgencyAdminReaderRole */
  AgencyAdminReaderRole = 'AgencyAdminReaderRole',
  /** AgencyAdminRole */
  AgencyAdminRole = 'AgencyAdminRole',
  /** AgencyProducerRole */
  AgencyProducerRole = 'AgencyProducerRole',
  /** AgencyServiceMemberRole */
  AgencyServiceMemberRole = 'AgencyServiceMemberRole',
  /** BillingAdminRole */
  BillingAdminRole = 'BillingAdminRole',
  /** BusinessAutoContractorRole */
  BusinessAutoContractorRole = 'BusinessAutoContractorRole',
  /** CanopiusUnderwriterRole */
  CanopiusUnderwriterRole = 'CanopiusUnderwriterRole',
  /** ClaimsAdminRole */
  ClaimsAdminRole = 'ClaimsAdminRole',
  /** FleetAdminRole */
  FleetAdminRole = 'FleetAdminRole',
  /** FleetBDRole */
  FleetBdRole = 'FleetBDRole',
  /** FleetReaderRole */
  FleetReaderRole = 'FleetReaderRole',
  /** Level1UnderwriterRole */
  Level1UnderwriterRole = 'Level1UnderwriterRole',
  /** Level2UnderwriterRole */
  Level2UnderwriterRole = 'Level2UnderwriterRole',
  /** Level3UnderwriterRole */
  Level3UnderwriterRole = 'Level3UnderwriterRole',
  /** Level4UnderwriterRole */
  Level4UnderwriterRole = 'Level4UnderwriterRole',
  /** Level5UnderwriterRole */
  Level5UnderwriterRole = 'Level5UnderwriterRole',
  /** Level6UnderwriterRole */
  Level6UnderwriterRole = 'Level6UnderwriterRole',
  /** Level7UnderwriterRole */
  Level7UnderwriterRole = 'Level7UnderwriterRole',
  /** NirvanaAPIUserRole */
  NirvanaApiUserRole = 'NirvanaAPIUserRole',
  /** NonFleetBDRole */
  NonFleetBdRole = 'NonFleetBDRole',
  /** PibitAPIRole */
  PibitApiRole = 'PibitAPIRole',
  /** PolicyAdminReaderRole */
  PolicyAdminReaderRole = 'PolicyAdminReaderRole',
  /** PowerUserRole */
  PowerUserRole = 'PowerUserRole',
  /** QAUnderwriterRole */
  QaUnderwriterRole = 'QAUnderwriterRole',
  /** SafetyConsultantReader */
  SafetyConsultantReader = 'SafetyConsultantReader',
  /** SeniorSupportRole */
  SeniorSupportRole = 'SeniorSupportRole',
  /** SeniorUnderwriterRole */
  SeniorUnderwriterRole = 'SeniorUnderwriterRole',
  /** SharedFleetTelematicsRole */
  SharedFleetTelematicsRole = 'SharedFleetTelematicsRole',
  /** SharedSafetyReaderRole */
  SharedSafetyReaderRole = 'SharedSafetyReaderRole',
  /** SuperuserRole */
  SuperuserRole = 'SuperuserRole',
  /** UnderwriterManagerRole */
  UnderwriterManagerRole = 'UnderwriterManagerRole',
  /** UserOwnerRole */
  UserOwnerRole = 'UserOwnerRole',
}

export type SafetyReportShare = {
  __typename?: 'SafetyReportShare';
  expiresAt?: Maybe<Scalars['Time']>;
  id: Scalars['string'];
};

export type ScoreTrendItem = {
  __typename?: 'ScoreTrendItem';
  explainabilityRecommendations: Array<ExplainabilityRecommendation>;
  hasExplainabilityData: Scalars['bool'];
  isConfidentScore: Scalars['bool'];
  lowConfidenceReason?: Maybe<Scalars['int']>;
  score: Scalars['float64'];
  timestamp: Scalars['Time'];
};

export type ShipperData = {
  __typename?: 'ShipperData';
  inspectionsCount?: Maybe<Scalars['int']>;
  shipperName?: Maybe<Scalars['string']>;
};

export enum SortOrder {
  /** 0 */
  Asc = 'asc',
  /** 1 */
  Desc = 'desc',
}

export enum Status {
  /** Created */
  Created = 'Created',
  /** Invalid */
  Invalid = 'Invalid',
  /** Undefined */
  Undefined = 'Undefined',
  /** Valid */
  Valid = 'Valid',
  /** Validating */
  Validating = 'Validating',
}

export type SubmitFnolResponse = {
  __typename?: 'SubmitFnolResponse';
  externalId: Scalars['string'];
  id: Scalars['string'];
  url?: Maybe<Scalars['string']>;
};

export type SubmittableOptions = {
  __typename?: 'SubmittableOptions';
  nars: Scalars['bool'];
  snapsheet: Scalars['bool'];
};

export type SupportingDocumentOrForm = {
  __typename?: 'SupportingDocumentOrForm';
  documentID: Scalars['string'];
  filename: Scalars['string'];
  signedLink: ExpirableLink;
};

export enum Tsp {
  /** TSP2BROELD */
  Tsp2Broeld = 'TSP2BROELD',
  /** TSP3MD */
  Tsp3Md = 'TSP3MD',
  /** TSP888ELD */
  Tsp888Eld = 'TSP888ELD',
  /** TSPARIFleet */
  TspariFleet = 'TSPARIFleet',
  /** TSPATAndTFleet */
  TspatAndTFleet = 'TSPATAndTFleet',
  /** TSPATAndTFleetComplete */
  TspatAndTFleetComplete = 'TSPATAndTFleetComplete',
  /** TSPATELD */
  Tspateld = 'TSPATELD',
  /** TSPActionELD */
  TspActionEld = 'TSPActionELD',
  /** TSPActsoftEncore */
  TspActsoftEncore = 'TSPActsoftEncore',
  /** TSPAdvantageAssetTracking */
  TspAdvantageAssetTracking = 'TSPAdvantageAssetTracking',
  /** TSPAdvantageOne */
  TspAdvantageOne = 'TSPAdvantageOne',
  /** TSPAgilisLinxup */
  TspAgilisLinxup = 'TSPAgilisLinxup',
  /** TSPAirELD */
  TspAirEld = 'TSPAirELD',
  /** TSPAlfaELD */
  TspAlfaEld = 'TSPAlfaELD',
  /** TSPApexUltima */
  TspApexUltima = 'TSPApexUltima',
  /** TSPApolloELD */
  TspApolloEld = 'TSPApolloELD',
  /** TSPArgosConnectedSolutions */
  TspArgosConnectedSolutions = 'TSPArgosConnectedSolutions',
  /** TSPAssuredTelematics */
  TspAssuredTelematics = 'TSPAssuredTelematics',
  /** TSPAttriX */
  TspAttriX = 'TSPAttriX',
  /** TSPAwareGPS */
  TspAwareGps = 'TSPAwareGPS',
  /** TSPAzuga */
  TspAzuga = 'TSPAzuga',
  /** TSPBELLFAMELD */
  Tspbellfameld = 'TSPBELLFAMELD',
  /** TSPBadgerFleetSolutions */
  TspBadgerFleetSolutions = 'TSPBadgerFleetSolutions',
  /** TSPBigRoad */
  TspBigRoad = 'TSPBigRoad',
  /** TSPBlackBearELD */
  TspBlackBearEld = 'TSPBlackBearELD',
  /** TSPBlueArrow */
  TspBlueArrow = 'TSPBlueArrow',
  /** TSPBlueArrowTelematics */
  TspBlueArrowTelematics = 'TSPBlueArrowTelematics',
  /** TSPBlueHorseELD */
  TspBlueHorseEld = 'TSPBlueHorseELD',
  /** TSPBlueInkTechnology */
  TspBlueInkTechnology = 'TSPBlueInkTechnology',
  /** TSPBlueStarELD */
  TspBlueStarEld = 'TSPBlueStarELD',
  /** TSPCNELD */
  Tspcneld = 'TSPCNELD',
  /** TSPCTELogELD */
  TspcteLogEld = 'TSPCTELogELD',
  /** TSPCarrierHQ */
  TspCarrierHq = 'TSPCarrierHQ',
  /** TSPClearPathGPS */
  TspClearPathGps = 'TSPClearPathGPS',
  /** TSPClutchELD */
  TspClutchEld = 'TSPClutchELD',
  /** TSPColumbusELD */
  TspColumbusEld = 'TSPColumbusELD',
  /** TSPCommandGPS */
  TspCommandGps = 'TSPCommandGPS',
  /** TSPContiGO */
  TspContiGo = 'TSPContiGO',
  /** TSPCoretex */
  TspCoretex = 'TSPCoretex',
  /** TSPCyntrXELDPlus */
  TspCyntrXeldPlus = 'TSPCyntrXELDPlus',
  /** TSPDSGELOGS */
  Tspdsgelogs = 'TSPDSGELOGS',
  /** TSPDailyELD */
  TspDailyEld = 'TSPDailyELD',
  /** TSPDigitalELD */
  TspDigitalEld = 'TSPDigitalELD',
  /** TSPDreamELD */
  TspDreamEld = 'TSPDreamELD',
  /** TSPDriveEDR */
  TspDriveEdr = 'TSPDriveEDR',
  /** TSPELDBooks */
  TspeldBooks = 'TSPELDBooks',
  /** TSPELDFleet */
  TspeldFleet = 'TSPELDFleet',
  /** TSPELDMandate */
  TspeldMandate = 'TSPELDMandate',
  /** TSPELDMandatePlus */
  TspeldMandatePlus = 'TSPELDMandatePlus',
  /** TSPELDMandatePro */
  TspeldMandatePro = 'TSPELDMandatePro',
  /** TSPELDOne */
  TspeldOne = 'TSPELDOne',
  /** TSPELDRider */
  TspeldRider = 'TSPELDRider',
  /** TSPELDTab */
  TspeldTab = 'TSPELDTab',
  /** TSPELOG42 */
  Tspelog42 = 'TSPELOG42',
  /** TSPEROAD */
  Tsperoad = 'TSPEROAD',
  /** TSPEVAELD */
  Tspevaeld = 'TSPEVAELD',
  /** TSPEVOELD */
  Tspevoeld = 'TSPEVOELD',
  /** TSPEZELDSolutions */
  TspezeldSolutions = 'TSPEZELDSolutions',
  /** TSPEZFleet */
  TspezFleet = 'TSPEZFleet',
  /** TSPEZLogz */
  TspezLogz = 'TSPEZLogz',
  /** TSPEagleWireless */
  TspEagleWireless = 'TSPEagleWireless',
  /** TSPElevenELD */
  TspElevenEld = 'TSPElevenELD',
  /** TSPEnVueTelematics */
  TspEnVueTelematics = 'TSPEnVueTelematics',
  /** TSPExpressWayELD */
  TspExpressWayEld = 'TSPExpressWayELD',
  /** TSPFACTORELD */
  Tspfactoreld = 'TSPFACTORELD',
  /** TSPFMELD */
  Tspfmeld = 'TSPFMELD',
  /** TSPFirstELD */
  TspFirstEld = 'TSPFirstELD',
  /** TSPFleetBossGPS */
  TspFleetBossGps = 'TSPFleetBossGPS',
  /** TSPFleetComplete */
  TspFleetComplete = 'TSPFleetComplete',
  /** TSPFleetLocate21 */
  TspFleetLocate21 = 'TSPFleetLocate21',
  /** TSPFleetLocateAdvancedAndCompliance */
  TspFleetLocateAdvancedAndCompliance = 'TSPFleetLocateAdvancedAndCompliance',
  /** TSPFleetLocateELD */
  TspFleetLocateEld = 'TSPFleetLocateELD',
  /** TSPFleetNavSystems */
  TspFleetNavSystems = 'TSPFleetNavSystems',
  /** TSPFleetProfitCenter */
  TspFleetProfitCenter = 'TSPFleetProfitCenter',
  /** TSPFleetSharp */
  TspFleetSharp = 'TSPFleetSharp',
  /** TSPFleetistics */
  TspFleetistics = 'TSPFleetistics',
  /** TSPFleetmaster */
  TspFleetmaster = 'TSPFleetmaster',
  /** TSPFleetmatics */
  TspFleetmatics = 'TSPFleetmatics',
  /** TSPFlexport */
  TspFlexport = 'TSPFlexport',
  /** TSPForceByMojio */
  TspForceByMojio = 'TSPForceByMojio',
  /** TSPForwardThinkingELD */
  TspForwardThinkingEld = 'TSPForwardThinkingELD',
  /** TSPGPSCommander */
  TspgpsCommander = 'TSPGPSCommander',
  /** TSPGPSFleetFinder */
  TspgpsFleetFinder = 'TSPGPSFleetFinder',
  /** TSPGPSInsight */
  TspgpsInsight = 'TSPGPSInsight',
  /** TSPGPSSolutions */
  TspgpsSolutions = 'TSPGPSSolutions',
  /** TSPGPSTab */
  TspgpsTab = 'TSPGPSTab',
  /** TSPGPSTrackingCanada */
  TspgpsTrackingCanada = 'TSPGPSTrackingCanada',
  /** TSPGPSTrackit */
  TspgpsTrackit = 'TSPGPSTrackit',
  /** TSPGarmin */
  TspGarmin = 'TSPGarmin',
  /** TSPGeoforce */
  TspGeoforce = 'TSPGeoforce',
  /** TSPGeotab */
  TspGeotab = 'TSPGeotab',
  /** TSPGlobalELD */
  TspGlobalEld = 'TSPGlobalELD',
  /** TSPGlostone */
  TspGlostone = 'TSPGlostone',
  /** TSPGoFleet */
  TspGoFleet = 'TSPGoFleet',
  /** TSPGoGPS */
  TspGoGps = 'TSPGoGPS',
  /** TSPGoodDealGPS */
  TspGoodDealGps = 'TSPGoodDealGPS',
  /** TSPGorillaSafety */
  TspGorillaSafety = 'TSPGorillaSafety',
  /** TSPGrayboxSolutions */
  TspGrayboxSolutions = 'TSPGrayboxSolutions',
  /** TSPGridline */
  TspGridline = 'TSPGridline',
  /** TSPHOS247 */
  Tsphos247 = 'TSPHOS247',
  /** TSPHighPointGPS */
  TspHighPointGps = 'TSPHighPointGPS',
  /** TSPHorizonPathELD */
  TspHorizonPathEld = 'TSPHorizonPathELD',
  /** TSPHutchSystems */
  TspHutchSystems = 'TSPHutchSystems',
  /** TSPIDELD */
  Tspideld = 'TSPIDELD',
  /** TSPISAACInstruments */
  TspisaacInstruments = 'TSPISAACInstruments',
  /** TSPInTouchGPS */
  TspInTouchGps = 'TSPInTouchGPS',
  /** TSPInsightMobileData */
  TspInsightMobileData = 'TSPInsightMobileData',
  /** TSPIntellishift */
  TspIntellishift = 'TSPIntellishift',
  /** TSPIntouchELD */
  TspIntouchEld = 'TSPIntouchELD',
  /** TSPIoTab */
  TspIoTab = 'TSPIoTab',
  /** TSPJJKeller */
  TspjjKeller = 'TSPJJKeller',
  /** TSPKSKELD */
  Tspkskeld = 'TSPKSKELD',
  /** TSPKeepTruckin */
  TspKeepTruckin = 'TSPKeepTruckin',
  /** TSPKeepTruckinSG */
  TspKeepTruckinSg = 'TSPKeepTruckinSG',
  /** TSPKeepTruckinSafety */
  TspKeepTruckinSafety = 'TSPKeepTruckinSafety',
  /** TSPKonexial */
  TspKonexial = 'TSPKonexial',
  /** TSPLBTechnology */
  TsplbTechnology = 'TSPLBTechnology',
  /** TSPLEGACYELD */
  Tsplegacyeld = 'TSPLEGACYELD',
  /** TSPLightAndTravelELD */
  TspLightAndTravelEld = 'TSPLightAndTravelELD',
  /** TSPLogPlusELD */
  TspLogPlusEld = 'TSPLogPlusELD',
  /** TSPLookTruckELD */
  TspLookTruckEld = 'TSPLookTruckELD',
  /** TSPLynx */
  TspLynx = 'TSPLynx',
  /** TSPLytXDriveCam */
  TspLytXDriveCam = 'TSPLytXDriveCam',
  /** TSPMGKELD */
  Tspmgkeld = 'TSPMGKELD',
  /** TSPMOONLIGHTELD */
  Tspmoonlighteld = 'TSPMOONLIGHTELD',
  /** TSPMTELD */
  Tspmteld = 'TSPMTELD',
  /** TSPMasterELD */
  TspMasterEld = 'TSPMasterELD',
  /** TSPMatrack */
  TspMatrack = 'TSPMatrack',
  /** TSPMaxELD */
  TspMaxEld = 'TSPMaxELD',
  /** TSPMock */
  TspMock = 'TSPMock',
  /** TSPMonarchGPS */
  TspMonarchGps = 'TSPMonarchGPS',
  /** TSPMondo */
  TspMondo = 'TSPMondo',
  /** TSPMotionELD */
  TspMotionEld = 'TSPMotionELD',
  /** TSPMountainELD */
  TspMountainEld = 'TSPMountainELD',
  /** TSPMy20ELD */
  TspMy20Eld = 'TSPMy20ELD',
  /** TSPNetradyneInc */
  TspNetradyneInc = 'TSPNetradyneInc',
  /** TSPNewELDWorld */
  TspNewEldWorld = 'TSPNewELDWorld',
  /** TSPNextraq */
  TspNextraq = 'TSPNextraq',
  /** TSPNexusELD */
  TspNexusEld = 'TSPNexusELD',
  /** TSPNoorELD */
  TspNoorEld = 'TSPNoorELD',
  /** TSPNotConnected */
  TspNotConnected = 'TSPNotConnected',
  /** TSPOaneELD */
  TspOaneEld = 'TSPOaneELD',
  /** TSPOmnitracs */
  TspOmnitracs = 'TSPOmnitracs',
  /** TSPOmnitracsES */
  TspOmnitracsEs = 'TSPOmnitracsES',
  /** TSPOmnitracsXRS */
  TspOmnitracsXrs = 'TSPOmnitracsXRS',
  /** TSPOnTrakSolutions */
  TspOnTrakSolutions = 'TSPOnTrakSolutions',
  /** TSPOnePlusELD */
  TspOnePlusEld = 'TSPOnePlusELD',
  /** TSPOneStepGPS */
  TspOneStepGps = 'TSPOneStepGPS',
  /** TSPOntimeELD */
  TspOntimeEld = 'TSPOntimeELD',
  /** TSPOptimaELD */
  TspOptimaEld = 'TSPOptimaELD',
  /** TSPOrbcomm */
  TspOrbcomm = 'TSPOrbcomm',
  /** TSPOrientELD */
  TspOrientEld = 'TSPOrientELD',
  /** TSPOrion */
  TspOrion = 'TSPOrion',
  /** TSPOther */
  TspOther = 'TSPOther',
  /** TSPPeopleNet */
  TspPeopleNet = 'TSPPeopleNet',
  /** TSPPhoenixELD */
  TspPhoenixEld = 'TSPPhoenixELD',
  /** TSPPlatformScience */
  TspPlatformScience = 'TSPPlatformScience',
  /** TSPPositrace */
  TspPositrace = 'TSPPositrace',
  /** TSPPowerELD */
  TspPowerEld = 'TSPPowerELD',
  /** TSPPowerFleet */
  TspPowerFleet = 'TSPPowerFleet',
  /** TSPPrePassELD */
  TspPrePassEld = 'TSPPrePassELD',
  /** TSPProLogs */
  TspProLogs = 'TSPProLogs',
  /** TSPProRideELD */
  TspProRideEld = 'TSPProRideELD',
  /** TSPQualityGPS */
  TspQualityGps = 'TSPQualityGPS',
  /** TSPRMJTechnologies */
  TsprmjTechnologies = 'TSPRMJTechnologies',
  /** TSPRadicalELD */
  TspRadicalEld = 'TSPRadicalELD',
  /** TSPRandMcNally */
  TspRandMcNally = 'TSPRandMcNally',
  /** TSPRealELD */
  TspRealEld = 'TSPRealELD',
  /** TSPReliableELD */
  TspReliableEld = 'TSPReliableELD',
  /** TSPRenaissanceELD */
  TspRenaissanceEld = 'TSPRenaissanceELD',
  /** TSPRigbot */
  TspRigbot = 'TSPRigbot',
  /** TSPRightTruckingELD */
  TspRightTruckingEld = 'TSPRightTruckingELD',
  /** TSPRoadReadySolutions */
  TspRoadReadySolutions = 'TSPRoadReadySolutions',
  /** TSPRoadStarELD */
  TspRoadStarEld = 'TSPRoadStarELD',
  /** TSPRouteELD */
  TspRouteEld = 'TSPRouteELD',
  /** TSPRushEnterpises */
  TspRushEnterpises = 'TSPRushEnterpises',
  /** TSPSFELD */
  Tspsfeld = 'TSPSFELD',
  /** TSPSMARTCHOICELOGSELD */
  Tspsmartchoicelogseld = 'TSPSMARTCHOICELOGSELD',
  /** TSPSRELD */
  Tspsreld = 'TSPSRELD',
  /** TSPSTATEELOGS */
  Tspstateelogs = 'TSPSTATEELOGS',
  /** TSPSafetyComplianceSolutions */
  TspSafetyComplianceSolutions = 'TSPSafetyComplianceSolutions',
  /** TSPSafetyVision */
  TspSafetyVision = 'TSPSafetyVision',
  /** TSPSamsara */
  TspSamsara = 'TSPSamsara',
  /** TSPSamsaraSG */
  TspSamsaraSg = 'TSPSamsaraSG',
  /** TSPSamsaraSafety */
  TspSamsaraSafety = 'TSPSamsaraSafety',
  /** TSPSimpleELOG */
  TspSimpleElog = 'TSPSimpleELOG',
  /** TSPSimpleTruckELD */
  TspSimpleTruckEld = 'TSPSimpleTruckELD',
  /** TSPSmartDrive */
  TspSmartDrive = 'TSPSmartDrive',
  /** TSPSmartWitness */
  TspSmartWitness = 'TSPSmartWitness',
  /** TSPSmartelds */
  TspSmartelds = 'TSPSmartelds',
  /** TSPSpeedELD */
  TspSpeedEld = 'TSPSpeedELD',
  /** TSPSpireonFleetLocate */
  TspSpireonFleetLocate = 'TSPSpireonFleetLocate',
  /** TSPStreetEagle */
  TspStreetEagle = 'TSPStreetEagle',
  /** TSPSwiftELD */
  TspSwiftEld = 'TSPSwiftELD',
  /** TSPTFMELD */
  Tsptfmeld = 'TSPTFMELD',
  /** TSPTMELD */
  Tsptmeld = 'TSPTMELD',
  /** TSPTMobile */
  TsptMobile = 'TSPTMobile',
  /** TSPTRCeLOGS */
  TsptrCeLogs = 'TSPTRCeLOGS',
  /** TSPTRUSTELD */
  Tsptrusteld = 'TSPTRUSTELD',
  /** TSPTTELD */
  Tsptteld = 'TSPTTELD',
  /** TSPTangerine */
  TspTangerine = 'TSPTangerine',
  /** TSPTeletracNavman */
  TspTeletracNavman = 'TSPTeletracNavman',
  /** TSPTrackEnsureInc */
  TspTrackEnsureInc = 'TSPTrackEnsureInc',
  /** TSPTrackOnHOS */
  TspTrackOnHos = 'TSPTrackOnHOS',
  /** TSPTransflo */
  TspTransflo = 'TSPTransflo',
  /** TSPTraxxisGPS */
  TspTraxxisGps = 'TSPTraxxisGPS',
  /** TSPTrendyELD */
  TspTrendyEld = 'TSPTrendyELD',
  /** TSPTrimble */
  TspTrimble = 'TSPTrimble',
  /** TSPTruPathSystems */
  TspTruPathSystems = 'TSPTruPathSystems',
  /** TSPTruckXELD */
  TspTruckXeld = 'TSPTruckXELD',
  /** TSPTruckerPathELDPro */
  TspTruckerPathEldPro = 'TSPTruckerPathELDPro',
  /** TSPTrueRoadELD */
  TspTrueRoadEld = 'TSPTrueRoadELD',
  /** TSPUnityELD */
  TspUnityEld = 'TSPUnityELD',
  /** TSPVLogELD */
  TspvLogEld = 'TSPVLogELD',
  /** TSPVTS */
  Tspvts = 'TSPVTS',
  /** TSPVerizonConnect */
  TspVerizonConnect = 'TSPVerizonConnect',
  /** TSPVerizonConnectFleet */
  TspVerizonConnectFleet = 'TSPVerizonConnectFleet',
  /** TSPVerizonConnectReveal */
  TspVerizonConnectReveal = 'TSPVerizonConnectReveal',
  /** TSPVertrax */
  TspVertrax = 'TSPVertrax',
  /** TSPVistaELD */
  TspVistaEld = 'TSPVistaELD',
  /** TSPVulcansols */
  TspVulcansols = 'TSPVulcansols',
  /** TSPWebfleet */
  TspWebfleet = 'TSPWebfleet',
  /** TSPWorldTruckingELD */
  TspWorldTruckingEld = 'TSPWorldTruckingELD',
  /** TSPXELD */
  Tspxeld = 'TSPXELD',
  /** TSPZELD */
  Tspzeld = 'TSPZELD',
  /** TSPZenduit */
  TspZenduit = 'TSPZenduit',
  /** TSPZippyELD */
  TspZippyEld = 'TSPZippyELD',
  /** TSPZonar */
  TspZonar = 'TSPZonar',
  /** TSPeasiTrack */
  TsPeasiTrack = 'TSPeasiTrack',
}

export type Table = {
  __typename?: 'Table';
  records: Array<Array<Scalars['string']>>;
};

export type TelematicsVehicle = {
  __typename?: 'TelematicsVehicle';
  handleID: Scalars['string'];
  mileage?: Maybe<VehicleMileage>;
  riskScores: Array<VehicleScoreTrendItem>;
  tspID: Scalars['string'];
  vin: Scalars['string'];
};

export type UserWorkflowPreference = {
  __typename?: 'UserWorkflowPreference';
  email: Scalars['bool'];
  sms: Scalars['bool'];
  workflow: Scalars['string'];
};

export type UserWorkflowPreference_InputObject = {
  email: Scalars['bool'];
  sms: Scalars['bool'];
  workflow: Scalars['string'];
};

export type VehicleMileage = {
  __typename?: 'VehicleMileage';
  distanceMiles: Scalars['float64'];
};

export type VehicleStat = {
  __typename?: 'VehicleStat';
  keyBehaviour?: Maybe<Scalars['string']>;
  riskScore?: Maybe<Scalars['float64']>;
  riskScoreErrorMessages?: Maybe<Scalars['string']>;
  vIN?: Maybe<Scalars['string']>;
  vehicleName?: Maybe<Scalars['string']>;
  vehicleTSPName?: Maybe<Scalars['string']>;
  violationCount?: Maybe<Scalars['int']>;
  violationSeverityTimeWeighted?: Maybe<Scalars['float64']>;
  violationSeverityWeight?: Maybe<Scalars['float64']>;
};

export type VehiclesStatsList = {
  __typename?: 'VehiclesStatsList';
  vehicles: Array<VehicleStat>;
};

export type ViolationStat = {
  __typename?: 'ViolationStat';
  bASICCategory?: Maybe<Scalars['string']>;
  code?: Maybe<Scalars['string']>;
  description?: Maybe<Scalars['string']>;
  group?: Maybe<Scalars['string']>;
  inspectionDate?: Maybe<Scalars['Time']>;
  location?: Maybe<Location>;
  severityWeight?: Maybe<Scalars['int']>;
  vINs: Array<Scalars['string']>;
};

export type ViolationsStatsList = {
  __typename?: 'ViolationsStatsList';
  violations: Array<ViolationStat>;
};

export type ActivateUserResponse = {
  __typename?: 'activateUserResponse';
  expiration: Scalars['Time'];
  fleetSafetyReportId?: Maybe<Scalars['string']>;
  sessionId: Scalars['string'];
};

export type Agency = {
  __typename?: 'agency';
  address?: Maybe<Address>;
  createdAt: Scalars['Time'];
  id: Scalars['string'];
  isTestAgency: Scalars['bool'];
  name: Scalars['string'];
  updatedAt?: Maybe<Scalars['Time']>;
};

export type AgencyUserInvite = {
  __typename?: 'agencyUserInvite';
  shareID: Scalars['string'];
};

export type BasicScore = {
  __typename?: 'basicScore';
  discount?: Maybe<Scalars['float64']>;
  scoreDate: Scalars['string'];
  scores: Array<BasicScoreDetail>;
};

export type BordereauxReport = {
  __typename?: 'bordereauxReport';
  carrier: Carrier;
  downloadURL?: Maybe<Scalars['string']>;
  errorMessage?: Maybe<Scalars['string']>;
  generatedAt: Scalars['Time'];
  generatedBy?: Maybe<User>;
  generatedByUserId: Scalars['string'];
  id: Scalars['string'];
};

export type CameraEvents = {
  __typename?: 'cameraEvents';
  coachingState: Scalars['string'];
  forwardVideoURL?: Maybe<Scalars['string']>;
  id: Scalars['string'];
  inwardVideoURL?: Maybe<Scalars['string']>;
  labels: Array<Scalars['string']>;
  time: Scalars['Time'];
  trackedInwardVideoURL?: Maybe<Scalars['string']>;
  vin: Scalars['string'];
};

export type Claims = {
  __typename?: 'claims';
  adjusterEmail: Scalars['string'];
  adjusterName: Scalars['string'];
  canSubmitFeedback: Scalars['bool'];
  claimNumber: Scalars['string'];
  externalId: Scalars['string'];
  feedbacks: Array<ClaimFeedback>;
  hasNotesSinceLastScheduleSummary: Scalars['bool'];
  id: Scalars['string'];
  lineOfBusiness: Scalars['string'];
  lossDatetime?: Maybe<Scalars['Time']>;
  modifiedAt: Scalars['Time'];
  notes: Array<Note>;
  policy?: Maybe<Policy>;
  policyNumber: Scalars['string'];
  reportedAt: Scalars['Time'];
  reportedBy?: Maybe<Scalars['string']>;
  source: ClaimsProvider;
  status: ClaimStatus;
  statusChanges: Array<ClaimStatusChange>;
};

export type CreateUserRoleArgs_InputObject = {
  agencyID?: InputMaybe<Scalars['string']>;
  fleetID?: InputMaybe<Scalars['string']>;
  group: RoleGroupEnum;
};

export type DatagovInspection = {
  __typename?: 'datagovInspection';
  alcoholControlSub?: Maybe<Scalars['bool']>;
  changeDate: Scalars['Time'];
  countyCode: Scalars['string'];
  countyCodeState?: Maybe<Scalars['string']>;
  countyName: Scalars['string'];
  createdAt: Scalars['Time'];
  deletedAt?: Maybe<Scalars['Time']>;
  docketNumber?: Maybe<Scalars['int64']>;
  dotNumber: Scalars['int64'];
  driverOOSTotal: Scalars['int'];
  driverViolTotal: Scalars['int'];
  drugIntrdctnArrests: Scalars['int'];
  drugIntrdctnSearch?: Maybe<Scalars['bool']>;
  grossCompVehicleWeight?: Maybe<Scalars['int']>;
  hazmatOOSTotal: Scalars['int'];
  hazmatViolTotal: Scalars['int'];
  id: Scalars['int64'];
  inspEndTime: Scalars['string'];
  inspFacility: Scalars['string'];
  inspInterstate?: Maybe<Scalars['bool']>;
  inspLevelID: Scalars['int'];
  inspStartTime: Scalars['string'];
  inspectionDate: Scalars['Time'];
  localEnfJurisdiction?: Maybe<Scalars['bool']>;
  location?: Maybe<Scalars['string']>;
  locationDesc?: Maybe<Scalars['string']>;
  mcmisAddDate?: Maybe<Scalars['Time']>;
  oosTotal: Scalars['int'];
  postAccInd?: Maybe<Scalars['bool']>;
  region: Scalars['string'];
  reportNumber: Scalars['string'];
  reportState: Scalars['string'];
  shipperName?: Maybe<Scalars['string']>;
  shippingPaperNumber?: Maybe<Scalars['string']>;
  sizeWeightEnf?: Maybe<Scalars['bool']>;
  statusCode: Scalars['string'];
  telematicsAssignments: Array<TelematicsAssignment>;
  trafficEnf?: Maybe<Scalars['bool']>;
  vehicleOOSTotal: Scalars['int'];
  vehicleViolTotal: Scalars['int'];
  vehicles: Array<DatagovVehicle>;
  violTotal: Scalars['int'];
  violations: Array<DatagovViolation>;
};

export type DatagovVehicle = {
  __typename?: 'datagovVehicle';
  changeDate: Scalars['Time'];
  company?: Maybe<Scalars['string']>;
  createdAt: Scalars['Time'];
  decalIssued?: Maybe<Scalars['bool']>;
  decalNumber?: Maybe<Scalars['string']>;
  deletedAt?: Maybe<Scalars['Time']>;
  id: Scalars['int64'];
  inspectionID: Scalars['int64'];
  licensePlate?: Maybe<Scalars['string']>;
  licenseState?: Maybe<Scalars['string']>;
  make?: Maybe<Scalars['string']>;
  sequenceNumber: Scalars['int'];
  vehicleTypeID: Scalars['int'];
  vin?: Maybe<Scalars['string']>;
};

export type DatagovViolation = {
  __typename?: 'datagovViolation';
  changeDate: Scalars['Time'];
  citationNumber?: Maybe<Scalars['string']>;
  code?: Maybe<Scalars['string']>;
  createdAt: Scalars['Time'];
  defectVerificationID?: Maybe<Scalars['int']>;
  deletedAt?: Maybe<Scalars['Time']>;
  description?: Maybe<Scalars['string']>;
  group?: Maybe<ViolationGroup>;
  id: Scalars['int64'];
  inspViolUnit: Scalars['string'];
  inspViolationCategoryID: Scalars['int'];
  inspectionID: Scalars['int64'];
  oos?: Maybe<Scalars['bool']>;
  partNumber: Scalars['string'];
  partNumberSection: Scalars['string'];
  seqNumber: Scalars['int'];
  severity?: Maybe<Scalars['int']>;
  vehicle?: Maybe<DatagovVehicle>;
};

export type DraftFnols = {
  __typename?: 'draftFnols';
  archivedAt?: Maybe<Scalars['Time']>;
  attachments: Array<DraftFnolAttachment>;
  contacts: Array<DraftFnolContact>;
  createdAt: Scalars['Time'];
  createdBy: Scalars['string'];
  dotNumber?: Maybe<Scalars['int64']>;
  fnolId?: Maybe<Scalars['string']>;
  id: Scalars['string'];
  incidentDescription?: Maybe<Scalars['string']>;
  injuriesInvolved?: Maybe<Scalars['bool']>;
  insuredName?: Maybe<Scalars['string']>;
  lossDatetime?: Maybe<Scalars['Time']>;
  lossLocation?: Maybe<Scalars['string']>;
  lossState?: Maybe<Scalars['string']>;
  noticeType: FnolNoticeType;
  policeAgencyName?: Maybe<Scalars['string']>;
  policeReportNumber?: Maybe<Scalars['string']>;
  policyNumber?: Maybe<Scalars['string']>;
  submittedFrom: FnolSource;
  updatedAt: Scalars['Time'];
  vehicles: Array<DraftFnolVehicle>;
};

export type Driver = {
  __typename?: 'driver';
  id: Scalars['string'];
  licenseNumber: Scalars['string'];
  licenseState: Scalars['string'];
  name: Scalars['string'];
};

export type FinalizeTelematicsResponse = {
  __typename?: 'finalizeTelematicsResponse';
  error?: Maybe<Scalars['string']>;
  handleID: Scalars['string'];
  safetyReportID: Scalars['string'];
};

export type Flag = {
  __typename?: 'flag';
  category: FlagCategory;
  chart?: Maybe<Chart>;
  description: Scalars['string'];
  id: Scalars['string'];
  severity: FlagSeverity;
  title: Scalars['string'];
};

export type Fleet = {
  __typename?: 'fleet';
  createdAt: Scalars['Time'];
  dotNumber: Scalars['string'];
  id: Scalars['string'];
  name: Scalars['string'];
  updatedAt?: Maybe<Scalars['Time']>;
};

export type FleetSafetyReport = {
  __typename?: 'fleetSafetyReport';
  DotRating: DotRating;
  GeneralTrends: Array<MonthlyValues>;
  InsuranceSummary: Array<InsuranceRecord>;
  LocationStats?: Maybe<GeoJson>;
  OOSSummary: Array<OosSummary>;
  ShipperList: Array<ShipperData>;
  TspProvider: Tsp;
  VehiclesStatsList?: Maybe<VehiclesStatsList>;
  ViolationStats?: Maybe<ViolationsStatsList>;
  basicScores: Array<BasicScore>;
  basicScoresByCount: Array<BasicScore>;
  createdAt: Scalars['Time'];
  createdBy: Scalars['string'];
  datagovInspections: Array<DatagovInspection>;
  dotNumber: Scalars['string'];
  flags: FlagConnection;
  fleet?: Maybe<Fleet>;
  fmcsaAuthStatus: Status;
  hasTelematicsConnection: Scalars['bool'];
  id: Scalars['string'];
  inspections: Array<Inspection>;
  inspectionsConnection: InspectionRecordConnection;
  issScores: Array<IssScore>;
  issScoresByCount: Array<IssScore>;
  name: Scalars['string'];
  recommendations: RecommendationConnection;
  recommendations_subset: RecommendationConnection;
  starred: Scalars['bool'];
  state?: Maybe<Scalars['string']>;
  telematicsRiskFleetPercentiles: Array<ScoreTrendItem>;
  telematicsRiskVinPercentiles: Array<TelematicsRiskVinPercentile>;
  telematicsVehicles: Array<TelematicsVehicle>;
};

export type FleetSafetyReportLocationStatsArgs = {
  countType: Scalars['string'];
  endTime?: InputMaybe<Scalars['Time']>;
  startTime?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportVehiclesStatsListArgs = {
  endTime?: InputMaybe<Scalars['Time']>;
  startTime?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportViolationStatsArgs = {
  endTime?: InputMaybe<Scalars['Time']>;
  startTime?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportBasicScoresArgs = {
  fromTimestamp?: InputMaybe<Scalars['Time']>;
  toTimestamp?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportBasicScoresByCountArgs = {
  scoreCount: Scalars['int'];
};

export type FleetSafetyReportDatagovInspectionsArgs = {
  endDate?: InputMaybe<Scalars['Time']>;
  startDate?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportFlagsArgs = {
  after?: InputMaybe<Scalars['string']>;
  before?: InputMaybe<Scalars['string']>;
  category?: InputMaybe<FlagCategory>;
  filterText?: InputMaybe<Scalars['string']>;
  filterTextFields?: InputMaybe<Array<Scalars['string']>>;
  filterType?: InputMaybe<Scalars['string']>;
  first?: InputMaybe<Scalars['int64']>;
  last?: InputMaybe<Scalars['int64']>;
  severity?: InputMaybe<FlagSeverity>;
  sortBy?: InputMaybe<Scalars['string']>;
  sortOrder?: InputMaybe<SortOrder>;
};

export type FleetSafetyReportInspectionsArgs = {
  endDate?: InputMaybe<Scalars['Time']>;
  minViolations?: InputMaybe<Scalars['int64']>;
  startDate?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportInspectionsConnectionArgs = {
  after?: InputMaybe<Scalars['string']>;
  before?: InputMaybe<Scalars['string']>;
  endDate?: InputMaybe<Scalars['Time']>;
  filterText?: InputMaybe<Scalars['string']>;
  filterTextFields?: InputMaybe<Array<Scalars['string']>>;
  filterType?: InputMaybe<Scalars['string']>;
  first?: InputMaybe<Scalars['int64']>;
  last?: InputMaybe<Scalars['int64']>;
  minViolations?: InputMaybe<Scalars['int64']>;
  sortBy?: InputMaybe<Scalars['string']>;
  sortOrder?: InputMaybe<SortOrder>;
  startDate?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportIssScoresArgs = {
  fromTimestamp?: InputMaybe<Scalars['Time']>;
  toTimestamp?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportIssScoresByCountArgs = {
  scoreCount: Scalars['int'];
};

export type FleetSafetyReportRecommendationsArgs = {
  after?: InputMaybe<Scalars['string']>;
  before?: InputMaybe<Scalars['string']>;
  filterText?: InputMaybe<Scalars['string']>;
  filterTextFields?: InputMaybe<Array<Scalars['string']>>;
  filterType?: InputMaybe<Scalars['string']>;
  first?: InputMaybe<Scalars['int64']>;
  impact?: InputMaybe<RecommendationImpact>;
  last?: InputMaybe<Scalars['int64']>;
  sortBy?: InputMaybe<Scalars['string']>;
  sortOrder?: InputMaybe<SortOrder>;
};

export type FleetSafetyReportRecommendations_SubsetArgs = {
  after?: InputMaybe<Scalars['string']>;
  before?: InputMaybe<Scalars['string']>;
  filterText?: InputMaybe<Scalars['string']>;
  filterTextFields?: InputMaybe<Array<Scalars['string']>>;
  filterType?: InputMaybe<Scalars['string']>;
  first?: InputMaybe<Scalars['int64']>;
  impact?: InputMaybe<RecommendationImpact>;
  last?: InputMaybe<Scalars['int64']>;
  sortBy?: InputMaybe<Scalars['string']>;
  sortOrder?: InputMaybe<SortOrder>;
};

export type FleetSafetyReportTelematicsRiskFleetPercentilesArgs = {
  endTime?: InputMaybe<Scalars['Time']>;
  startTime?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportTelematicsRiskVinPercentilesArgs = {
  endTime?: InputMaybe<Scalars['Time']>;
  startTime?: InputMaybe<Scalars['Time']>;
};

export type FleetSafetyReportTelematicsVehiclesArgs = {
  endTime?: InputMaybe<Scalars['Time']>;
  startTime?: InputMaybe<Scalars['Time']>;
};

export type FleetUserInvite = {
  __typename?: 'fleetUserInvite';
  shareID: Scalars['string'];
};

export type InitiateTelematicsResponse = {
  __typename?: 'initiateTelematicsResponse';
  handleID: Scalars['string'];
  location?: Maybe<Scalars['string']>;
  state?: Maybe<Scalars['string']>;
  tsp: Tsp;
};

export type Inspection = {
  __typename?: 'inspection';
  combinationVehicleGrossWeight?: Maybe<Scalars['int']>;
  countyCode: Scalars['string'];
  countyName: Scalars['string'];
  dotNumber: Scalars['int64'];
  driver?: Maybe<Driver>;
  driverOOSTotal: Scalars['int'];
  hazmatOOSTotal: Scalars['int'];
  hazmatPlacardReq: Scalars['bool'];
  hazmatViolationsSent: Scalars['int'];
  inspectionDate: Scalars['Time'];
  inspectionID: Scalars['int64'];
  inspectionLevel: InspectionLevel;
  location?: Maybe<Scalars['string']>;
  publicVINs: Array<Scalars['string']>;
  publishedDate: Scalars['Time'];
  region?: Maybe<Scalars['string']>;
  reportNumber: Scalars['string'];
  reportState: Scalars['string'];
  rowID: Scalars['string'];
  shipperName?: Maybe<Scalars['string']>;
  telematicsAssignments: Array<TelematicsAssignment>;
  timeWeight: Scalars['int'];
  totalBASICViols: Scalars['int'];
  totalOOSViolations: Scalars['int'];
  vehicleOOSTotal: Scalars['int'];
  vehicles: Array<Vehicle>;
  violations: Array<Violation>;
  wasLocalEnforcement?: Maybe<Scalars['bool']>;
  wasPostAccident?: Maybe<Scalars['bool']>;
  wasSizeWeightEnforcement?: Maybe<Scalars['bool']>;
  wasTrafficEnforcement?: Maybe<Scalars['bool']>;
};

export type Policy = {
  __typename?: 'policy';
  coverages: Array<PolicyCoverageEnums>;
  documentID: Scalars['string'];
  drivers: Array<PolicyDriver>;
  endDate: Scalars['Time'];
  endorsements: Array<Endorsement>;
  id: Scalars['string'];
  insuredDOTNumber: Scalars['string'];
  insuredName: Scalars['string'];
  isTest: Scalars['bool'];
  policyNumber: Scalars['string'];
  programType: ProgramType;
  signedLink: ExpirableLink;
  startDate: Scalars['Time'];
  state: PolicyState;
  subCoverages: Array<CoverageWithSymbols>;
  underwriter: BasicUser;
  vehicles: Array<PolicyVehicle>;
};

export type Recommendation = {
  __typename?: 'recommendation';
  engine: Scalars['int'];
  id: Scalars['string'];
  impact: RecommendationImpact;
  sections: Array<RecommendationSection>;
  title: Scalars['string'];
};

export type Role = {
  __typename?: 'role';
  agency?: Maybe<Agency>;
  agencyID?: Maybe<Scalars['string']>;
  createdAt: Scalars['Time'];
  domain: Scalars['string'];
  fleetID?: Maybe<Scalars['string']>;
  group: RoleGroupEnum;
  id: Scalars['string'];
  updatedAt?: Maybe<Scalars['Time']>;
  userID: Scalars['string'];
};

export type TelematicsAssignment = {
  __typename?: 'telematicsAssignment';
  assignedDurationMs: Scalars['int64'];
  driver?: Maybe<TelematicsDriver>;
  vehicle?: Maybe<TelematicsVehicle>;
};

export type TelematicsDriver = {
  __typename?: 'telematicsDriver';
  id: Scalars['string'];
  licenseNumber: Scalars['string'];
  licenseState: Scalars['string'];
  name: Scalars['string'];
};

export type TelematicsRiskVinPercentile = {
  __typename?: 'telematicsRiskVinPercentile';
  scores: Array<VehicleScoreTrendItem>;
  vin: Scalars['string'];
};

export type User = {
  __typename?: 'user';
  agencies: Array<Agency>;
  createdAt: Scalars['Time'];
  deletedAt?: Maybe<Scalars['Time']>;
  email: Scalars['string'];
  firstName: Scalars['string'];
  id: Scalars['string'];
  lastLoginAt?: Maybe<Scalars['Time']>;
  lastName: Scalars['string'];
  phoneNumber?: Maybe<Scalars['string']>;
  profilePicture?: Maybe<Scalars['string']>;
  roles: Array<Role>;
  safetyReports: Array<FleetSafetyReport>;
  title?: Maybe<Scalars['string']>;
  updatedAt?: Maybe<Scalars['Time']>;
};

export type Vehicle = {
  __typename?: 'vehicle';
  company: Scalars['string'];
  make: Scalars['string'];
  model: Scalars['string'];
  vin: Scalars['string'];
};

export type VehicleScoreTrendItem = {
  __typename?: 'vehicleScoreTrendItem';
  fleetID: Scalars['string'];
  handleID: Scalars['string'];
  score: Scalars['float64'];
  telematicsAssignments: Array<TelematicsAssignment>;
  timestamp: Scalars['Time'];
  vin: Scalars['string'];
};

export type Violation = {
  __typename?: 'violation';
  category: Category;
  code: Scalars['string'];
  description: Scalars['string'];
  group: ViolationGroup;
  humanReadableCode: Scalars['string'];
  isDSMS: Scalars['bool'];
  oosIndicator: Scalars['bool'];
  oosWeight: Scalars['int'];
  publishedDate: Scalars['Time'];
  rowID: Scalars['string'];
  severityWeight: Scalars['int'];
  timeWeight: Scalars['int'];
  totalSeverityWeight: Scalars['int'];
  violationID?: Maybe<Scalars['int']>;
};

export type ViolationGroup = {
  __typename?: 'violationGroup';
  category: Category;
  humanReadable: Scalars['string'];
  name: Scalars['string'];
  severity: Scalars['int'];
};

export type AgencyFragment = {
  __typename?: 'agency';
  id: string;
  name: string;
  createdAt: string;
  updatedAt?: string | null;
};

export type CreateAgencyAndBdMappingMutationVariables = Exact<{
  name: Scalars['string'];
  address: Address_InputObject;
  fleetBD?: InputMaybe<Scalars['string']>;
  nonFleetBD?: InputMaybe<Scalars['string']>;
}>;

export type CreateAgencyAndBdMappingMutation = {
  __typename?: 'Mutation';
  createAgencyAndBDMapping?: {
    __typename?: 'agency';
    id: string;
    name: string;
    createdAt: string;
    updatedAt?: string | null;
    address?: {
      __typename?: 'Address';
      street1: string;
      city: string;
      state: string;
      zip: string;
    } | null;
  } | null;
};

export type AgenciesQueryVariables = Exact<{ [key: string]: never }>;

export type AgenciesQuery = {
  __typename?: 'Query';
  agencies: Array<{
    __typename?: 'agency';
    id: string;
    name: string;
    createdAt: string;
    updatedAt?: string | null;
  }>;
};

export type GetBordereauxReportsQueryVariables = Exact<{
  [key: string]: never;
}>;

export type GetBordereauxReportsQuery = {
  __typename?: 'Query';
  getBordereauxReports: Array<{
    __typename?: 'bordereauxReport';
    id: string;
    carrier: Carrier;
    downloadURL?: string | null;
    generatedByUserId: string;
    generatedAt: string;
    errorMessage?: string | null;
    generatedBy?: {
      __typename?: 'user';
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    } | null;
  }>;
};

export type GenerateBordereauxReportMutationVariables = Exact<{
  carrier: Carrier;
}>;

export type GenerateBordereauxReportMutation = {
  __typename?: 'Mutation';
  generateBordereauxReport?: {
    __typename?: 'GenerateBordereauxReportResponse';
    downloadURL: string;
  } | null;
};

export type CameraEventsQueryVariables = Exact<{
  policyNumber: Scalars['string'];
  endTime: Scalars['Time'];
  startTime: Scalars['Time'];
}>;

export type CameraEventsQuery = {
  __typename?: 'Query';
  cameraEvents: Array<{
    __typename?: 'cameraEvents';
    id: string;
    vin: string;
    labels: Array<string>;
    coachingState: string;
    forwardVideoURL?: string | null;
    inwardVideoURL?: string | null;
    trackedInwardVideoURL?: string | null;
    time: string;
  }>;
};

export type ClaimsQueryVariables = Exact<{ [key: string]: never }>;

export type ClaimsQuery = {
  __typename?: 'Query';
  claims: Array<{
    __typename?: 'claims';
    id: string;
    claimNumber: string;
    externalId: string;
    lineOfBusiness: string;
    policyNumber: string;
    adjusterName: string;
    adjusterEmail: string;
    status: ClaimStatus;
    reportedBy?: string | null;
    reportedAt: string;
    modifiedAt: string;
    source: ClaimsProvider;
    policy?: {
      __typename?: 'policy';
      id: string;
      insuredName: string;
      isTest: boolean;
    } | null;
  }>;
};

export type ClaimByIdQueryVariables = Exact<{
  id: Scalars['string'];
}>;

export type ClaimByIdQuery = {
  __typename?: 'Query';
  claimById?: {
    __typename?: 'claims';
    id: string;
    claimNumber: string;
    externalId: string;
    lineOfBusiness: string;
    modifiedAt: string;
    lossDatetime?: string | null;
    reportedBy?: string | null;
    status: ClaimStatus;
    policyNumber: string;
    canSubmitFeedback: boolean;
    reportedAt: string;
    adjusterEmail: string;
    adjusterName: string;
    source: ClaimsProvider;
    policy?: {
      __typename?: 'policy';
      id: string;
      insuredName: string;
      insuredDOTNumber: string;
    } | null;
    notes: Array<{
      __typename?: 'Note';
      createdAt: string;
      value: string;
      category?: string | null;
    }>;
    statusChanges: Array<{
      __typename?: 'ClaimStatusChange';
      id: string;
      value: ClaimStatus;
      createdAt: string;
    }>;
  } | null;
};

export type PolicyAndEndorsementsQueryVariables = Exact<{
  claimId: Scalars['string'];
}>;

export type PolicyAndEndorsementsQuery = {
  __typename?: 'Query';
  policy?: {
    __typename?: 'policy';
    id: string;
    documentID: string;
    policyNumber: string;
    startDate: string;
    endDate: string;
    coverages: Array<PolicyCoverageEnums>;
    subCoverages: Array<{
      __typename?: 'CoverageWithSymbols';
      coverage: string;
      symbols: Array<string>;
    }>;
    signedLink: {
      __typename?: 'ExpirableLink';
      expiration: string;
      link: string;
    };
    underwriter: { __typename?: 'BasicUser'; email: string; name: string };
    endorsements: Array<{
      __typename?: 'Endorsement';
      id: string;
      documentID: string;
      approvedAt: string;
      changeTypes: Array<string>;
      effectiveInterval: {
        __typename?: 'EndorsementEffectiveInterval';
        effectiveDate: string;
        expirationDate: string;
      };
      signedLink: {
        __typename?: 'ExpirableLink';
        expiration: string;
        link: string;
      };
      underwriter: { __typename?: 'BasicUser'; email: string; name: string };
      supportingDocsAndForms: Array<{
        __typename?: 'SupportingDocumentOrForm';
        documentID: string;
        filename: string;
        signedLink: {
          __typename?: 'ExpirableLink';
          expiration: string;
          link: string;
        };
      }>;
    }>;
    drivers: Array<{
      __typename?: 'PolicyDriver';
      dateOfHire: string;
      firstName: string;
      lastName: string;
      licenseNumber: string;
      licenseState: string;
      yearsOfExperience: number;
    }>;
    vehicles: Array<{
      __typename?: 'PolicyVehicle';
      make: string;
      model: string;
      statedValue?: number | null;
      vehicleClass: string;
      vehicleType: string;
      vin: string;
      weightClass: string;
      year: number;
    }>;
  } | null;
};

export type FleetSafetyReportQueryVariables = Exact<{
  dotNumber: Scalars['string'];
}>;

export type FleetSafetyReportQuery = {
  __typename?: 'Query';
  fleetSafetyReport?: {
    __typename?: 'fleetSafetyReport';
    TspProvider: Tsp;
  } | null;
};

export type ClaimsPresignedUploadLinksQueryVariables = Exact<{
  fileNames: Array<Scalars['string']> | Scalars['string'];
}>;

export type ClaimsPresignedUploadLinksQuery = {
  __typename?: 'Query';
  claimsPresignedUploadLinks: Array<{
    __typename?: 'PresignedUploadLink';
    url: string;
    key: string;
  }>;
};

export type UpsertDraftFnolMutationVariables = Exact<{
  id?: InputMaybe<Scalars['string']>;
  dotNumber?: InputMaybe<Scalars['int64']>;
  policyNumber?: InputMaybe<Scalars['string']>;
  insuredName?: InputMaybe<Scalars['string']>;
  description?: InputMaybe<Scalars['string']>;
  lossDate?: InputMaybe<Scalars['Time']>;
  lossLocation?: InputMaybe<Scalars['string']>;
  lossState?: InputMaybe<Scalars['string']>;
  noticeType?: InputMaybe<FnolNoticeType>;
  reporter?: InputMaybe<DraftReporter_InputObject>;
  police?: InputMaybe<Police_InputObject>;
  injuriesInvolved?: InputMaybe<Scalars['bool']>;
  source?: InputMaybe<FnolSource>;
  insuredVehicles?: InputMaybe<
    Array<DraftVehicle_InputObject> | DraftVehicle_InputObject
  >;
  otherVehicles?: InputMaybe<
    Array<DraftVehicle_InputObject> | DraftVehicle_InputObject
  >;
  attachmentKeys?: InputMaybe<Array<Scalars['string']> | Scalars['string']>;
}>;

export type UpsertDraftFnolMutation = {
  __typename?: 'Mutation';
  upsertDraftFNOL?: {
    __typename?: 'draftFnols';
    id: string;
    policyNumber?: string | null;
    createdAt: string;
  } | null;
};

export type CreateFnolMutationVariables = Exact<{
  policyNumber: Scalars['string'];
  description?: InputMaybe<Scalars['string']>;
  noticeType: FnolNoticeType;
  lossLocation?: InputMaybe<Scalars['string']>;
  lossState: Scalars['string'];
  lossDate: Scalars['Time'];
  injuriesInvolved?: InputMaybe<Scalars['bool']>;
  police?: InputMaybe<Police_InputObject>;
  reporter: Reporter_InputObject;
  insuredVehicles?: InputMaybe<
    Array<ClaimVehicle_InputObject> | ClaimVehicle_InputObject
  >;
  otherVehicles?: InputMaybe<
    Array<ClaimVehicle_InputObject> | ClaimVehicle_InputObject
  >;
  insuredName?: InputMaybe<Scalars['string']>;
  attachmentKeys?: InputMaybe<Array<Scalars['string']> | Scalars['string']>;
  draftFnolId?: InputMaybe<Scalars['string']>;
  source?: InputMaybe<FnolSource>;
}>;

export type CreateFnolMutation = {
  __typename?: 'Mutation';
  createFNOL?: {
    __typename?: 'Fnol';
    id: string;
    clientClaimNumber: string;
    policyNumber: string;
    status: FnolStatus;
    createdAt: string;
  } | null;
};

export type FleetDetailsQueryVariables = Exact<{
  dotNumber: Scalars['string'];
}>;

export type FleetDetailsQuery = {
  __typename?: 'Query';
  fleetSafetyReport?: { __typename?: 'fleetSafetyReport'; name: string } | null;
};

export type GetSubmittableProviderOptionsQueryVariables = Exact<{
  policyNumber: Scalars['string'];
}>;

export type GetSubmittableProviderOptionsQuery = {
  __typename?: 'Query';
  getSubmittableProviderOptions: {
    __typename?: 'SubmittableOptions';
    snapsheet: boolean;
    nars: boolean;
  };
};

export type PoliciesQueryVariables = Exact<{
  activeDateIn: Scalars['Time'];
  policyStates?: InputMaybe<Array<PolicyState> | PolicyState>;
}>;

export type PoliciesQuery = {
  __typename?: 'Query';
  policies: Array<{
    __typename?: 'policy';
    id: string;
    programType: ProgramType;
    policyNumber: string;
    coverages: Array<PolicyCoverageEnums>;
    state: PolicyState;
    isTest: boolean;
  }>;
};

export type SubmitFnolMutationVariables = Exact<{
  policyNumber: Scalars['string'];
  description?: InputMaybe<Scalars['string']>;
  noticeType: FnolNoticeType;
  lossLocation?: InputMaybe<Scalars['string']>;
  lossState: Scalars['string'];
  lossDate: Scalars['Time'];
  injuriesInvolved?: InputMaybe<Scalars['bool']>;
  police?: InputMaybe<Police_InputObject>;
  reporter: Reporter_InputObject;
  insuredVehicles?: InputMaybe<
    Array<ClaimVehicle_InputObject> | ClaimVehicle_InputObject
  >;
  otherVehicles?: InputMaybe<
    Array<ClaimVehicle_InputObject> | ClaimVehicle_InputObject
  >;
  insuredName?: InputMaybe<Scalars['string']>;
  attachmentKeys?: InputMaybe<Array<Scalars['string']> | Scalars['string']>;
  draftFnolId?: InputMaybe<Scalars['string']>;
  source?: InputMaybe<FnolSource>;
  provider?: InputMaybe<ClaimsProvider>;
}>;

export type SubmitFnolMutation = {
  __typename?: 'Mutation';
  submitFnol?: {
    __typename?: 'SubmitFnolResponse';
    id: string;
    externalId: string;
  } | null;
};

export type ArchiveDraftFnolsMutationVariables = Exact<{
  ids: Array<Scalars['string']> | Scalars['string'];
}>;

export type ArchiveDraftFnolsMutation = {
  __typename?: 'Mutation';
  archiveDraftFnols: boolean;
};

export type DraftFnolByIdQueryVariables = Exact<{
  id: Scalars['string'];
}>;

export type DraftFnolByIdQuery = {
  __typename?: 'Query';
  draftFnolById?: {
    __typename?: 'draftFnols';
    id: string;
    dotNumber?: number | null;
    policyNumber?: string | null;
    insuredName?: string | null;
    lossDatetime?: string | null;
    lossLocation?: string | null;
    lossState?: string | null;
    policeAgencyName?: string | null;
    policeReportNumber?: string | null;
    incidentDescription?: string | null;
    noticeType: FnolNoticeType;
    injuriesInvolved?: boolean | null;
    archivedAt?: string | null;
    submittedFrom: FnolSource;
    fnolId?: string | null;
    attachments: Array<{
      __typename?: 'DraftFnolAttachment';
      handleId: string;
      key: string;
      url: string;
    }>;
    contacts: Array<{
      __typename?: 'DraftFnolContact';
      firstName?: string | null;
      lastName?: string | null;
      phone?: string | null;
      email?: string | null;
    }>;
    vehicles: Array<{
      __typename?: 'DraftFnolVehicle';
      isInsuredVehicle?: boolean | null;
      vin?: string | null;
      registrationNumber?: string | null;
    }>;
  } | null;
};

export type OpenDraftFnolsQueryVariables = Exact<{ [key: string]: never }>;

export type OpenDraftFnolsQuery = {
  __typename?: 'Query';
  draftFnols: Array<{
    __typename?: 'draftFnols';
    id: string;
    createdBy: string;
    createdAt: string;
    policyNumber?: string | null;
    archivedAt?: string | null;
    submittedFrom: FnolSource;
    insuredName?: string | null;
  }>;
};

export type FnolsQueryVariables = Exact<{ [key: string]: never }>;

export type FnolsQuery = {
  __typename?: 'Query';
  fnols: Array<{
    __typename?: 'Fnol';
    clientClaimNumber: string;
    createdAt: string;
    id: string;
    incidentDescription: string;
    lossDatetime: string;
    lossLocation: string;
    lossState: string;
    noticeType: FnolNoticeType;
    policeAgencyName?: string | null;
    policeReportNumber?: string | null;
    createdBy: string;
    status: FnolStatus;
    submittedFrom: FnolSource;
    provider: ClaimsProvider;
    policy?: {
      __typename?: 'policy';
      policyNumber: string;
      insuredName: string;
    } | null;
  }>;
};

export type FleetSearchQueryVariables = Exact<{
  searchText: Scalars['string'];
}>;

export type FleetSearchQuery = {
  __typename?: 'Query';
  fetchFleetSafetySearch: Array<{
    __typename?: 'FleetSearchResult';
    dotNumber: string;
    name: string;
    state?: string | null;
  }>;
};

export type PoliciesByDotQueryVariables = Exact<{
  dotNumber: Scalars['string'];
}>;

export type PoliciesByDotQuery = {
  __typename?: 'Query';
  policies: Array<{
    __typename?: 'policy';
    id: string;
    policyNumber: string;
    coverages: Array<PolicyCoverageEnums>;
    startDate: string;
    endDate: string;
  }>;
};

export type CreateRoleMutationVariables = Exact<{
  group: RoleGroupEnum;
  id: Scalars['string'];
  agencyId?: InputMaybe<Scalars['string']>;
  fleetId?: InputMaybe<Scalars['string']>;
}>;

export type CreateRoleMutation = {
  __typename?: 'Mutation';
  createRole?: {
    __typename?: 'role';
    id: string;
    userID: string;
    agencyID?: string | null;
    fleetID?: string | null;
    group: RoleGroupEnum;
    domain: string;
    agency?: { __typename?: 'agency'; id: string; name: string } | null;
  } | null;
};

export type CreateUserMutationVariables = Exact<{
  email: Scalars['string'];
  firstName: Scalars['string'];
  lastName: Scalars['string'];
  password: Scalars['string'];
  phoneNumber?: InputMaybe<Scalars['string']>;
  title?: InputMaybe<Scalars['string']>;
  profilePicture?: InputMaybe<Scalars['string']>;
  roles: Array<CreateUserRoleArgs_InputObject> | CreateUserRoleArgs_InputObject;
}>;

export type CreateUserMutation = {
  __typename?: 'Mutation';
  createUser?: {
    __typename?: 'user';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string | null;
    title?: string | null;
    profilePicture?: string | null;
    deletedAt?: string | null;
    roles: Array<{
      __typename?: 'role';
      id: string;
      userID: string;
      agencyID?: string | null;
      fleetID?: string | null;
      group: RoleGroupEnum;
      domain: string;
      agency?: { __typename?: 'agency'; id: string; name: string } | null;
    }>;
  } | null;
};

export type DeactivateUserMutationVariables = Exact<{
  userID: Scalars['string'];
}>;

export type DeactivateUserMutation = {
  __typename?: 'Mutation';
  deactivateUser: boolean;
};

export type UsersQueryVariables = Exact<{ [key: string]: never }>;

export type UsersQuery = {
  __typename?: 'Query';
  users: Array<{
    __typename?: 'user';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string | null;
    title?: string | null;
    profilePicture?: string | null;
    deletedAt?: string | null;
    roles: Array<{
      __typename?: 'role';
      id: string;
      userID: string;
      agencyID?: string | null;
      fleetID?: string | null;
      group: RoleGroupEnum;
      domain: string;
      agency?: { __typename?: 'agency'; id: string; name: string } | null;
    }>;
  }>;
};

export type RoleFragment = {
  __typename?: 'role';
  id: string;
  userID: string;
  agencyID?: string | null;
  fleetID?: string | null;
  group: RoleGroupEnum;
  domain: string;
  agency?: { __typename?: 'agency'; id: string; name: string } | null;
};

export type UpdateAgentLicenseMutationVariables = Exact<{
  id: Scalars['string'];
  licenseNumber?: InputMaybe<Scalars['string']>;
  licenseType?: InputMaybe<Scalars['string']>;
  licenseStatus?: InputMaybe<Scalars['string']>;
  state?: InputMaybe<Scalars['string']>;
  effectiveDate?: InputMaybe<Scalars['Time']>;
  expirationDate?: InputMaybe<Scalars['Time']>;
}>;

export type UpdateAgentLicenseMutation = {
  __typename?: 'Mutation';
  updateAgentLicense: boolean;
};

export type UpdateUserMutationVariables = Exact<{
  id: Scalars['string'];
  firstName?: InputMaybe<Scalars['string']>;
  lastName?: InputMaybe<Scalars['string']>;
  email?: InputMaybe<Scalars['string']>;
  password?: InputMaybe<Scalars['string']>;
  phoneNumber?: InputMaybe<Scalars['string']>;
  title?: InputMaybe<Scalars['string']>;
  profilePicture?: InputMaybe<Scalars['string']>;
}>;

export type UpdateUserMutation = {
  __typename?: 'Mutation';
  patchUser?: {
    __typename?: 'user';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string | null;
    title?: string | null;
    profilePicture?: string | null;
    deletedAt?: string | null;
    roles: Array<{
      __typename?: 'role';
      id: string;
      userID: string;
      agencyID?: string | null;
      fleetID?: string | null;
      group: RoleGroupEnum;
      domain: string;
      agency?: { __typename?: 'agency'; id: string; name: string } | null;
    }>;
  } | null;
};

export type UserFragment = {
  __typename?: 'user';
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string | null;
  title?: string | null;
  profilePicture?: string | null;
  deletedAt?: string | null;
  roles: Array<{
    __typename?: 'role';
    id: string;
    userID: string;
    agencyID?: string | null;
    fleetID?: string | null;
    group: RoleGroupEnum;
    domain: string;
    agency?: { __typename?: 'agency'; id: string; name: string } | null;
  }>;
};

export type InviteUserMutationVariables = Exact<{
  email: Scalars['string'];
  role: RoleGroupEnum;
  agencyID: Scalars['string'];
}>;

export type InviteUserMutation = {
  __typename?: 'Mutation';
  inviteAgencyUserFromSupport?: {
    __typename?: 'agencyUserInvite';
    shareID: string;
  } | null;
};

export const AgencyFragmentDoc = gql`
  fragment Agency on agency {
    id
    name
    createdAt
    updatedAt
  }
`;
export const RoleFragmentDoc = gql`
  fragment Role on role {
    id
    userID
    agencyID
    fleetID
    group
    domain
    agency {
      id
      name
    }
  }
`;
export const UserFragmentDoc = gql`
  fragment User on user {
    id
    firstName
    lastName
    email
    phoneNumber
    title
    profilePicture
    deletedAt
    roles {
      ...Role
    }
  }
  ${RoleFragmentDoc}
`;
export const CreateAgencyAndBdMappingDocument = gql`
  mutation CreateAgencyAndBDMapping(
    $name: string!
    $address: Address_InputObject!
    $fleetBD: string
    $nonFleetBD: string
  ) {
    createAgencyAndBDMapping(
      name: $name
      address: $address
      fleetBD: $fleetBD
      nonFleetBD: $nonFleetBD
    ) {
      id
      name
      createdAt
      updatedAt
      address {
        street1
        city
        state
        zip
      }
    }
  }
`;
export type CreateAgencyAndBdMappingMutationFn = Apollo.MutationFunction<
  CreateAgencyAndBdMappingMutation,
  CreateAgencyAndBdMappingMutationVariables
>;

/**
 * __useCreateAgencyAndBdMappingMutation__
 *
 * To run a mutation, you first call `useCreateAgencyAndBdMappingMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateAgencyAndBdMappingMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createAgencyAndBdMappingMutation, { data, loading, error }] = useCreateAgencyAndBdMappingMutation({
 *   variables: {
 *      name: // value for 'name'
 *      address: // value for 'address'
 *      fleetBD: // value for 'fleetBD'
 *      nonFleetBD: // value for 'nonFleetBD'
 *   },
 * });
 */
export function useCreateAgencyAndBdMappingMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateAgencyAndBdMappingMutation,
    CreateAgencyAndBdMappingMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateAgencyAndBdMappingMutation,
    CreateAgencyAndBdMappingMutationVariables
  >(CreateAgencyAndBdMappingDocument, options);
}
export type CreateAgencyAndBdMappingMutationHookResult = ReturnType<
  typeof useCreateAgencyAndBdMappingMutation
>;
export type CreateAgencyAndBdMappingMutationResult =
  Apollo.MutationResult<CreateAgencyAndBdMappingMutation>;
export type CreateAgencyAndBdMappingMutationOptions =
  Apollo.BaseMutationOptions<
    CreateAgencyAndBdMappingMutation,
    CreateAgencyAndBdMappingMutationVariables
  >;
export const AgenciesDocument = gql`
  query Agencies {
    agencies {
      ...Agency
    }
  }
  ${AgencyFragmentDoc}
`;

/**
 * __useAgenciesQuery__
 *
 * To run a query within a React component, call `useAgenciesQuery` and pass it any options that fit your needs.
 * When your component renders, `useAgenciesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAgenciesQuery({
 *   variables: {
 *   },
 * });
 */
export function useAgenciesQuery(
  baseOptions?: Apollo.QueryHookOptions<AgenciesQuery, AgenciesQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AgenciesQuery, AgenciesQueryVariables>(
    AgenciesDocument,
    options,
  );
}
export function useAgenciesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AgenciesQuery,
    AgenciesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AgenciesQuery, AgenciesQueryVariables>(
    AgenciesDocument,
    options,
  );
}
export type AgenciesQueryHookResult = ReturnType<typeof useAgenciesQuery>;
export type AgenciesLazyQueryHookResult = ReturnType<
  typeof useAgenciesLazyQuery
>;
export type AgenciesQueryResult = Apollo.QueryResult<
  AgenciesQuery,
  AgenciesQueryVariables
>;
export const GetBordereauxReportsDocument = gql`
  query getBordereauxReports {
    getBordereauxReports {
      id
      carrier
      downloadURL
      generatedByUserId
      generatedBy {
        id
        firstName
        lastName
        email
      }
      generatedAt
      errorMessage
    }
  }
`;

/**
 * __useGetBordereauxReportsQuery__
 *
 * To run a query within a React component, call `useGetBordereauxReportsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetBordereauxReportsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetBordereauxReportsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetBordereauxReportsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetBordereauxReportsQuery,
    GetBordereauxReportsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetBordereauxReportsQuery,
    GetBordereauxReportsQueryVariables
  >(GetBordereauxReportsDocument, options);
}
export function useGetBordereauxReportsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetBordereauxReportsQuery,
    GetBordereauxReportsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetBordereauxReportsQuery,
    GetBordereauxReportsQueryVariables
  >(GetBordereauxReportsDocument, options);
}
export type GetBordereauxReportsQueryHookResult = ReturnType<
  typeof useGetBordereauxReportsQuery
>;
export type GetBordereauxReportsLazyQueryHookResult = ReturnType<
  typeof useGetBordereauxReportsLazyQuery
>;
export type GetBordereauxReportsQueryResult = Apollo.QueryResult<
  GetBordereauxReportsQuery,
  GetBordereauxReportsQueryVariables
>;
export const GenerateBordereauxReportDocument = gql`
  mutation generateBordereauxReport($carrier: Carrier!) {
    generateBordereauxReport(carrier: $carrier) {
      downloadURL
    }
  }
`;
export type GenerateBordereauxReportMutationFn = Apollo.MutationFunction<
  GenerateBordereauxReportMutation,
  GenerateBordereauxReportMutationVariables
>;

/**
 * __useGenerateBordereauxReportMutation__
 *
 * To run a mutation, you first call `useGenerateBordereauxReportMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGenerateBordereauxReportMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [generateBordereauxReportMutation, { data, loading, error }] = useGenerateBordereauxReportMutation({
 *   variables: {
 *      carrier: // value for 'carrier'
 *   },
 * });
 */
export function useGenerateBordereauxReportMutation(
  baseOptions?: Apollo.MutationHookOptions<
    GenerateBordereauxReportMutation,
    GenerateBordereauxReportMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    GenerateBordereauxReportMutation,
    GenerateBordereauxReportMutationVariables
  >(GenerateBordereauxReportDocument, options);
}
export type GenerateBordereauxReportMutationHookResult = ReturnType<
  typeof useGenerateBordereauxReportMutation
>;
export type GenerateBordereauxReportMutationResult =
  Apollo.MutationResult<GenerateBordereauxReportMutation>;
export type GenerateBordereauxReportMutationOptions =
  Apollo.BaseMutationOptions<
    GenerateBordereauxReportMutation,
    GenerateBordereauxReportMutationVariables
  >;
export const CameraEventsDocument = gql`
  query CameraEvents(
    $policyNumber: string!
    $endTime: Time!
    $startTime: Time!
  ) {
    cameraEvents(
      policyNumber: $policyNumber
      endTime: $endTime
      startTime: $startTime
    ) {
      id
      vin
      labels
      coachingState
      forwardVideoURL
      inwardVideoURL
      trackedInwardVideoURL
      time
    }
  }
`;

/**
 * __useCameraEventsQuery__
 *
 * To run a query within a React component, call `useCameraEventsQuery` and pass it any options that fit your needs.
 * When your component renders, `useCameraEventsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCameraEventsQuery({
 *   variables: {
 *      policyNumber: // value for 'policyNumber'
 *      endTime: // value for 'endTime'
 *      startTime: // value for 'startTime'
 *   },
 * });
 */
export function useCameraEventsQuery(
  baseOptions: Apollo.QueryHookOptions<
    CameraEventsQuery,
    CameraEventsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<CameraEventsQuery, CameraEventsQueryVariables>(
    CameraEventsDocument,
    options,
  );
}
export function useCameraEventsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    CameraEventsQuery,
    CameraEventsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<CameraEventsQuery, CameraEventsQueryVariables>(
    CameraEventsDocument,
    options,
  );
}
export type CameraEventsQueryHookResult = ReturnType<
  typeof useCameraEventsQuery
>;
export type CameraEventsLazyQueryHookResult = ReturnType<
  typeof useCameraEventsLazyQuery
>;
export type CameraEventsQueryResult = Apollo.QueryResult<
  CameraEventsQuery,
  CameraEventsQueryVariables
>;
export const ClaimsDocument = gql`
  query claims {
    claims {
      id
      claimNumber
      externalId
      lineOfBusiness
      policyNumber
      adjusterName
      adjusterEmail
      status
      reportedBy
      reportedAt
      modifiedAt
      source
      policy {
        id
        insuredName
        isTest
      }
    }
  }
`;

/**
 * __useClaimsQuery__
 *
 * To run a query within a React component, call `useClaimsQuery` and pass it any options that fit your needs.
 * When your component renders, `useClaimsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useClaimsQuery({
 *   variables: {
 *   },
 * });
 */
export function useClaimsQuery(
  baseOptions?: Apollo.QueryHookOptions<ClaimsQuery, ClaimsQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ClaimsQuery, ClaimsQueryVariables>(
    ClaimsDocument,
    options,
  );
}
export function useClaimsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<ClaimsQuery, ClaimsQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ClaimsQuery, ClaimsQueryVariables>(
    ClaimsDocument,
    options,
  );
}
export type ClaimsQueryHookResult = ReturnType<typeof useClaimsQuery>;
export type ClaimsLazyQueryHookResult = ReturnType<typeof useClaimsLazyQuery>;
export type ClaimsQueryResult = Apollo.QueryResult<
  ClaimsQuery,
  ClaimsQueryVariables
>;
export const ClaimByIdDocument = gql`
  query ClaimById($id: string!) {
    claimById(id: $id) {
      id
      claimNumber
      externalId
      lineOfBusiness
      modifiedAt
      lossDatetime
      reportedBy
      status
      policyNumber
      canSubmitFeedback
      reportedAt
      adjusterEmail
      adjusterName
      status
      source
      policy {
        id
        insuredName
        insuredDOTNumber
      }
      notes {
        createdAt
        value
        category
      }
      statusChanges {
        id
        value
        createdAt
      }
    }
  }
`;

/**
 * __useClaimByIdQuery__
 *
 * To run a query within a React component, call `useClaimByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useClaimByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useClaimByIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useClaimByIdQuery(
  baseOptions: Apollo.QueryHookOptions<ClaimByIdQuery, ClaimByIdQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ClaimByIdQuery, ClaimByIdQueryVariables>(
    ClaimByIdDocument,
    options,
  );
}
export function useClaimByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    ClaimByIdQuery,
    ClaimByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ClaimByIdQuery, ClaimByIdQueryVariables>(
    ClaimByIdDocument,
    options,
  );
}
export type ClaimByIdQueryHookResult = ReturnType<typeof useClaimByIdQuery>;
export type ClaimByIdLazyQueryHookResult = ReturnType<
  typeof useClaimByIdLazyQuery
>;
export type ClaimByIdQueryResult = Apollo.QueryResult<
  ClaimByIdQuery,
  ClaimByIdQueryVariables
>;
export const PolicyAndEndorsementsDocument = gql`
  query PolicyAndEndorsements($claimId: string!) {
    policy(claimId: $claimId) {
      id
      documentID
      policyNumber
      startDate
      endDate
      coverages
      subCoverages {
        coverage
        symbols
      }
      signedLink {
        expiration
        link
      }
      underwriter {
        email
        name
      }
      endorsements {
        id
        documentID
        approvedAt
        changeTypes
        effectiveInterval {
          effectiveDate
          expirationDate
        }
        signedLink {
          expiration
          link
        }
        underwriter {
          email
          name
        }
        supportingDocsAndForms {
          documentID
          filename
          signedLink {
            expiration
            link
          }
        }
      }
      drivers {
        dateOfHire
        firstName
        lastName
        licenseNumber
        licenseState
        yearsOfExperience
      }
      vehicles {
        make
        model
        statedValue
        vehicleClass
        vehicleType
        vin
        weightClass
        year
      }
    }
  }
`;

/**
 * __usePolicyAndEndorsementsQuery__
 *
 * To run a query within a React component, call `usePolicyAndEndorsementsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePolicyAndEndorsementsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePolicyAndEndorsementsQuery({
 *   variables: {
 *      claimId: // value for 'claimId'
 *   },
 * });
 */
export function usePolicyAndEndorsementsQuery(
  baseOptions: Apollo.QueryHookOptions<
    PolicyAndEndorsementsQuery,
    PolicyAndEndorsementsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    PolicyAndEndorsementsQuery,
    PolicyAndEndorsementsQueryVariables
  >(PolicyAndEndorsementsDocument, options);
}
export function usePolicyAndEndorsementsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    PolicyAndEndorsementsQuery,
    PolicyAndEndorsementsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    PolicyAndEndorsementsQuery,
    PolicyAndEndorsementsQueryVariables
  >(PolicyAndEndorsementsDocument, options);
}
export type PolicyAndEndorsementsQueryHookResult = ReturnType<
  typeof usePolicyAndEndorsementsQuery
>;
export type PolicyAndEndorsementsLazyQueryHookResult = ReturnType<
  typeof usePolicyAndEndorsementsLazyQuery
>;
export type PolicyAndEndorsementsQueryResult = Apollo.QueryResult<
  PolicyAndEndorsementsQuery,
  PolicyAndEndorsementsQueryVariables
>;
export const FleetSafetyReportDocument = gql`
  query fleetSafetyReport($dotNumber: string!) {
    fleetSafetyReport(id: $dotNumber) {
      TspProvider
    }
  }
`;

/**
 * __useFleetSafetyReportQuery__
 *
 * To run a query within a React component, call `useFleetSafetyReportQuery` and pass it any options that fit your needs.
 * When your component renders, `useFleetSafetyReportQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFleetSafetyReportQuery({
 *   variables: {
 *      dotNumber: // value for 'dotNumber'
 *   },
 * });
 */
export function useFleetSafetyReportQuery(
  baseOptions: Apollo.QueryHookOptions<
    FleetSafetyReportQuery,
    FleetSafetyReportQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FleetSafetyReportQuery,
    FleetSafetyReportQueryVariables
  >(FleetSafetyReportDocument, options);
}
export function useFleetSafetyReportLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FleetSafetyReportQuery,
    FleetSafetyReportQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FleetSafetyReportQuery,
    FleetSafetyReportQueryVariables
  >(FleetSafetyReportDocument, options);
}
export type FleetSafetyReportQueryHookResult = ReturnType<
  typeof useFleetSafetyReportQuery
>;
export type FleetSafetyReportLazyQueryHookResult = ReturnType<
  typeof useFleetSafetyReportLazyQuery
>;
export type FleetSafetyReportQueryResult = Apollo.QueryResult<
  FleetSafetyReportQuery,
  FleetSafetyReportQueryVariables
>;
export const ClaimsPresignedUploadLinksDocument = gql`
  query ClaimsPresignedUploadLinks($fileNames: [string!]!) {
    claimsPresignedUploadLinks(fileNames: $fileNames) {
      url
      key
    }
  }
`;

/**
 * __useClaimsPresignedUploadLinksQuery__
 *
 * To run a query within a React component, call `useClaimsPresignedUploadLinksQuery` and pass it any options that fit your needs.
 * When your component renders, `useClaimsPresignedUploadLinksQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useClaimsPresignedUploadLinksQuery({
 *   variables: {
 *      fileNames: // value for 'fileNames'
 *   },
 * });
 */
export function useClaimsPresignedUploadLinksQuery(
  baseOptions: Apollo.QueryHookOptions<
    ClaimsPresignedUploadLinksQuery,
    ClaimsPresignedUploadLinksQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    ClaimsPresignedUploadLinksQuery,
    ClaimsPresignedUploadLinksQueryVariables
  >(ClaimsPresignedUploadLinksDocument, options);
}
export function useClaimsPresignedUploadLinksLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    ClaimsPresignedUploadLinksQuery,
    ClaimsPresignedUploadLinksQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    ClaimsPresignedUploadLinksQuery,
    ClaimsPresignedUploadLinksQueryVariables
  >(ClaimsPresignedUploadLinksDocument, options);
}
export type ClaimsPresignedUploadLinksQueryHookResult = ReturnType<
  typeof useClaimsPresignedUploadLinksQuery
>;
export type ClaimsPresignedUploadLinksLazyQueryHookResult = ReturnType<
  typeof useClaimsPresignedUploadLinksLazyQuery
>;
export type ClaimsPresignedUploadLinksQueryResult = Apollo.QueryResult<
  ClaimsPresignedUploadLinksQuery,
  ClaimsPresignedUploadLinksQueryVariables
>;
export const UpsertDraftFnolDocument = gql`
  mutation upsertDraftFNOL(
    $id: string
    $dotNumber: int64
    $policyNumber: string
    $insuredName: string
    $description: string
    $lossDate: Time
    $lossLocation: string
    $lossState: string
    $noticeType: FnolNoticeType
    $reporter: DraftReporter_InputObject
    $police: Police_InputObject
    $injuriesInvolved: bool
    $source: FnolSource
    $insuredVehicles: [DraftVehicle_InputObject!]
    $otherVehicles: [DraftVehicle_InputObject!]
    $attachmentKeys: [string!]
  ) {
    upsertDraftFNOL(
      id: $id
      dotNumber: $dotNumber
      policyNumber: $policyNumber
      insuredName: $insuredName
      incidentDescription: $description
      lossDatetime: $lossDate
      lossLocation: $lossLocation
      lossState: $lossState
      noticeType: $noticeType
      reporter: $reporter
      police: $police
      injuriesInvolved: $injuriesInvolved
      source: $source
      insuredVehicles: $insuredVehicles
      otherVehicles: $otherVehicles
      attachmentKeys: $attachmentKeys
    ) {
      id
      policyNumber
      createdAt
    }
  }
`;
export type UpsertDraftFnolMutationFn = Apollo.MutationFunction<
  UpsertDraftFnolMutation,
  UpsertDraftFnolMutationVariables
>;

/**
 * __useUpsertDraftFnolMutation__
 *
 * To run a mutation, you first call `useUpsertDraftFnolMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpsertDraftFnolMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [upsertDraftFnolMutation, { data, loading, error }] = useUpsertDraftFnolMutation({
 *   variables: {
 *      id: // value for 'id'
 *      dotNumber: // value for 'dotNumber'
 *      policyNumber: // value for 'policyNumber'
 *      insuredName: // value for 'insuredName'
 *      description: // value for 'description'
 *      lossDate: // value for 'lossDate'
 *      lossLocation: // value for 'lossLocation'
 *      lossState: // value for 'lossState'
 *      noticeType: // value for 'noticeType'
 *      reporter: // value for 'reporter'
 *      police: // value for 'police'
 *      injuriesInvolved: // value for 'injuriesInvolved'
 *      source: // value for 'source'
 *      insuredVehicles: // value for 'insuredVehicles'
 *      otherVehicles: // value for 'otherVehicles'
 *      attachmentKeys: // value for 'attachmentKeys'
 *   },
 * });
 */
export function useUpsertDraftFnolMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpsertDraftFnolMutation,
    UpsertDraftFnolMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpsertDraftFnolMutation,
    UpsertDraftFnolMutationVariables
  >(UpsertDraftFnolDocument, options);
}
export type UpsertDraftFnolMutationHookResult = ReturnType<
  typeof useUpsertDraftFnolMutation
>;
export type UpsertDraftFnolMutationResult =
  Apollo.MutationResult<UpsertDraftFnolMutation>;
export type UpsertDraftFnolMutationOptions = Apollo.BaseMutationOptions<
  UpsertDraftFnolMutation,
  UpsertDraftFnolMutationVariables
>;
export const CreateFnolDocument = gql`
  mutation createFNOL(
    $policyNumber: string!
    $description: string
    $noticeType: FnolNoticeType!
    $lossLocation: string
    $lossState: string!
    $lossDate: Time!
    $injuriesInvolved: bool
    $police: Police_InputObject
    $reporter: Reporter_InputObject!
    $insuredVehicles: [ClaimVehicle_InputObject!]
    $otherVehicles: [ClaimVehicle_InputObject!]
    $insuredName: string
    $attachmentKeys: [string!]
    $draftFnolId: string
    $source: FnolSource
  ) {
    createFNOL(
      policyNumber: $policyNumber
      description: $description
      noticeType: $noticeType
      lossLocation: $lossLocation
      lossState: $lossState
      lossDate: $lossDate
      injuriesInvolved: $injuriesInvolved
      police: $police
      reporter: $reporter
      insuredVehicles: $insuredVehicles
      otherVehicles: $otherVehicles
      insuredName: $insuredName
      attachmentKeys: $attachmentKeys
      draftFnolId: $draftFnolId
      source: $source
    ) {
      id
      clientClaimNumber
      policyNumber
      status
      createdAt
    }
  }
`;
export type CreateFnolMutationFn = Apollo.MutationFunction<
  CreateFnolMutation,
  CreateFnolMutationVariables
>;

/**
 * __useCreateFnolMutation__
 *
 * To run a mutation, you first call `useCreateFnolMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateFnolMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createFnolMutation, { data, loading, error }] = useCreateFnolMutation({
 *   variables: {
 *      policyNumber: // value for 'policyNumber'
 *      description: // value for 'description'
 *      noticeType: // value for 'noticeType'
 *      lossLocation: // value for 'lossLocation'
 *      lossState: // value for 'lossState'
 *      lossDate: // value for 'lossDate'
 *      injuriesInvolved: // value for 'injuriesInvolved'
 *      police: // value for 'police'
 *      reporter: // value for 'reporter'
 *      insuredVehicles: // value for 'insuredVehicles'
 *      otherVehicles: // value for 'otherVehicles'
 *      insuredName: // value for 'insuredName'
 *      attachmentKeys: // value for 'attachmentKeys'
 *      draftFnolId: // value for 'draftFnolId'
 *      source: // value for 'source'
 *   },
 * });
 */
export function useCreateFnolMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateFnolMutation,
    CreateFnolMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CreateFnolMutation, CreateFnolMutationVariables>(
    CreateFnolDocument,
    options,
  );
}
export type CreateFnolMutationHookResult = ReturnType<
  typeof useCreateFnolMutation
>;
export type CreateFnolMutationResult =
  Apollo.MutationResult<CreateFnolMutation>;
export type CreateFnolMutationOptions = Apollo.BaseMutationOptions<
  CreateFnolMutation,
  CreateFnolMutationVariables
>;
export const FleetDetailsDocument = gql`
  query FleetDetails($dotNumber: string!) {
    fleetSafetyReport(id: $dotNumber) {
      name
    }
  }
`;

/**
 * __useFleetDetailsQuery__
 *
 * To run a query within a React component, call `useFleetDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useFleetDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFleetDetailsQuery({
 *   variables: {
 *      dotNumber: // value for 'dotNumber'
 *   },
 * });
 */
export function useFleetDetailsQuery(
  baseOptions: Apollo.QueryHookOptions<
    FleetDetailsQuery,
    FleetDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<FleetDetailsQuery, FleetDetailsQueryVariables>(
    FleetDetailsDocument,
    options,
  );
}
export function useFleetDetailsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FleetDetailsQuery,
    FleetDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<FleetDetailsQuery, FleetDetailsQueryVariables>(
    FleetDetailsDocument,
    options,
  );
}
export type FleetDetailsQueryHookResult = ReturnType<
  typeof useFleetDetailsQuery
>;
export type FleetDetailsLazyQueryHookResult = ReturnType<
  typeof useFleetDetailsLazyQuery
>;
export type FleetDetailsQueryResult = Apollo.QueryResult<
  FleetDetailsQuery,
  FleetDetailsQueryVariables
>;
export const GetSubmittableProviderOptionsDocument = gql`
  query getSubmittableProviderOptions($policyNumber: string!) {
    getSubmittableProviderOptions(policyNumber: $policyNumber) {
      snapsheet
      nars
    }
  }
`;

/**
 * __useGetSubmittableProviderOptionsQuery__
 *
 * To run a query within a React component, call `useGetSubmittableProviderOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSubmittableProviderOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSubmittableProviderOptionsQuery({
 *   variables: {
 *      policyNumber: // value for 'policyNumber'
 *   },
 * });
 */
export function useGetSubmittableProviderOptionsQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetSubmittableProviderOptionsQuery,
    GetSubmittableProviderOptionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSubmittableProviderOptionsQuery,
    GetSubmittableProviderOptionsQueryVariables
  >(GetSubmittableProviderOptionsDocument, options);
}
export function useGetSubmittableProviderOptionsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSubmittableProviderOptionsQuery,
    GetSubmittableProviderOptionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSubmittableProviderOptionsQuery,
    GetSubmittableProviderOptionsQueryVariables
  >(GetSubmittableProviderOptionsDocument, options);
}
export type GetSubmittableProviderOptionsQueryHookResult = ReturnType<
  typeof useGetSubmittableProviderOptionsQuery
>;
export type GetSubmittableProviderOptionsLazyQueryHookResult = ReturnType<
  typeof useGetSubmittableProviderOptionsLazyQuery
>;
export type GetSubmittableProviderOptionsQueryResult = Apollo.QueryResult<
  GetSubmittableProviderOptionsQuery,
  GetSubmittableProviderOptionsQueryVariables
>;
export const PoliciesDocument = gql`
  query Policies($activeDateIn: Time!, $policyStates: [PolicyState!]) {
    policies(activeDateIn: $activeDateIn, policyStates: $policyStates) {
      id
      programType
      policyNumber
      coverages
      state
      isTest
    }
  }
`;

/**
 * __usePoliciesQuery__
 *
 * To run a query within a React component, call `usePoliciesQuery` and pass it any options that fit your needs.
 * When your component renders, `usePoliciesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePoliciesQuery({
 *   variables: {
 *      activeDateIn: // value for 'activeDateIn'
 *      policyStates: // value for 'policyStates'
 *   },
 * });
 */
export function usePoliciesQuery(
  baseOptions: Apollo.QueryHookOptions<PoliciesQuery, PoliciesQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<PoliciesQuery, PoliciesQueryVariables>(
    PoliciesDocument,
    options,
  );
}
export function usePoliciesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    PoliciesQuery,
    PoliciesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<PoliciesQuery, PoliciesQueryVariables>(
    PoliciesDocument,
    options,
  );
}
export type PoliciesQueryHookResult = ReturnType<typeof usePoliciesQuery>;
export type PoliciesLazyQueryHookResult = ReturnType<
  typeof usePoliciesLazyQuery
>;
export type PoliciesQueryResult = Apollo.QueryResult<
  PoliciesQuery,
  PoliciesQueryVariables
>;
export const SubmitFnolDocument = gql`
  mutation submitFnol(
    $policyNumber: string!
    $description: string
    $noticeType: FnolNoticeType!
    $lossLocation: string
    $lossState: string!
    $lossDate: Time!
    $injuriesInvolved: bool
    $police: Police_InputObject
    $reporter: Reporter_InputObject!
    $insuredVehicles: [ClaimVehicle_InputObject!]
    $otherVehicles: [ClaimVehicle_InputObject!]
    $insuredName: string
    $attachmentKeys: [string!]
    $draftFnolId: string
    $source: FnolSource
    $provider: ClaimsProvider
  ) {
    submitFnol(
      policyNumber: $policyNumber
      description: $description
      noticeType: $noticeType
      lossLocation: $lossLocation
      lossState: $lossState
      lossDate: $lossDate
      injuriesInvolved: $injuriesInvolved
      police: $police
      reporter: $reporter
      insuredVehicles: $insuredVehicles
      otherVehicles: $otherVehicles
      insuredName: $insuredName
      attachmentKeys: $attachmentKeys
      draftFnolId: $draftFnolId
      source: $source
      provider: $provider
    ) {
      id
      externalId
    }
  }
`;
export type SubmitFnolMutationFn = Apollo.MutationFunction<
  SubmitFnolMutation,
  SubmitFnolMutationVariables
>;

/**
 * __useSubmitFnolMutation__
 *
 * To run a mutation, you first call `useSubmitFnolMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSubmitFnolMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [submitFnolMutation, { data, loading, error }] = useSubmitFnolMutation({
 *   variables: {
 *      policyNumber: // value for 'policyNumber'
 *      description: // value for 'description'
 *      noticeType: // value for 'noticeType'
 *      lossLocation: // value for 'lossLocation'
 *      lossState: // value for 'lossState'
 *      lossDate: // value for 'lossDate'
 *      injuriesInvolved: // value for 'injuriesInvolved'
 *      police: // value for 'police'
 *      reporter: // value for 'reporter'
 *      insuredVehicles: // value for 'insuredVehicles'
 *      otherVehicles: // value for 'otherVehicles'
 *      insuredName: // value for 'insuredName'
 *      attachmentKeys: // value for 'attachmentKeys'
 *      draftFnolId: // value for 'draftFnolId'
 *      source: // value for 'source'
 *      provider: // value for 'provider'
 *   },
 * });
 */
export function useSubmitFnolMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SubmitFnolMutation,
    SubmitFnolMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<SubmitFnolMutation, SubmitFnolMutationVariables>(
    SubmitFnolDocument,
    options,
  );
}
export type SubmitFnolMutationHookResult = ReturnType<
  typeof useSubmitFnolMutation
>;
export type SubmitFnolMutationResult =
  Apollo.MutationResult<SubmitFnolMutation>;
export type SubmitFnolMutationOptions = Apollo.BaseMutationOptions<
  SubmitFnolMutation,
  SubmitFnolMutationVariables
>;
export const ArchiveDraftFnolsDocument = gql`
  mutation archiveDraftFnols($ids: [string!]!) {
    archiveDraftFnols(ids: $ids)
  }
`;
export type ArchiveDraftFnolsMutationFn = Apollo.MutationFunction<
  ArchiveDraftFnolsMutation,
  ArchiveDraftFnolsMutationVariables
>;

/**
 * __useArchiveDraftFnolsMutation__
 *
 * To run a mutation, you first call `useArchiveDraftFnolsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useArchiveDraftFnolsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [archiveDraftFnolsMutation, { data, loading, error }] = useArchiveDraftFnolsMutation({
 *   variables: {
 *      ids: // value for 'ids'
 *   },
 * });
 */
export function useArchiveDraftFnolsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ArchiveDraftFnolsMutation,
    ArchiveDraftFnolsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    ArchiveDraftFnolsMutation,
    ArchiveDraftFnolsMutationVariables
  >(ArchiveDraftFnolsDocument, options);
}
export type ArchiveDraftFnolsMutationHookResult = ReturnType<
  typeof useArchiveDraftFnolsMutation
>;
export type ArchiveDraftFnolsMutationResult =
  Apollo.MutationResult<ArchiveDraftFnolsMutation>;
export type ArchiveDraftFnolsMutationOptions = Apollo.BaseMutationOptions<
  ArchiveDraftFnolsMutation,
  ArchiveDraftFnolsMutationVariables
>;
export const DraftFnolByIdDocument = gql`
  query draftFnolById($id: string!) {
    draftFnolById(id: $id) {
      id
      dotNumber
      policyNumber
      insuredName
      lossDatetime
      lossLocation
      lossState
      policeAgencyName
      policeReportNumber
      incidentDescription
      noticeType
      injuriesInvolved
      archivedAt
      submittedFrom
      attachments {
        handleId
        key
        url
      }
      contacts {
        firstName
        lastName
        phone
        email
      }
      vehicles {
        isInsuredVehicle
        vin
        registrationNumber
      }
      fnolId
    }
  }
`;

/**
 * __useDraftFnolByIdQuery__
 *
 * To run a query within a React component, call `useDraftFnolByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useDraftFnolByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDraftFnolByIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDraftFnolByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    DraftFnolByIdQuery,
    DraftFnolByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<DraftFnolByIdQuery, DraftFnolByIdQueryVariables>(
    DraftFnolByIdDocument,
    options,
  );
}
export function useDraftFnolByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    DraftFnolByIdQuery,
    DraftFnolByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<DraftFnolByIdQuery, DraftFnolByIdQueryVariables>(
    DraftFnolByIdDocument,
    options,
  );
}
export type DraftFnolByIdQueryHookResult = ReturnType<
  typeof useDraftFnolByIdQuery
>;
export type DraftFnolByIdLazyQueryHookResult = ReturnType<
  typeof useDraftFnolByIdLazyQuery
>;
export type DraftFnolByIdQueryResult = Apollo.QueryResult<
  DraftFnolByIdQuery,
  DraftFnolByIdQueryVariables
>;
export const OpenDraftFnolsDocument = gql`
  query openDraftFnols {
    draftFnols(hasFNOL: false) {
      id
      createdBy
      createdAt
      policyNumber
      archivedAt
      submittedFrom
      insuredName
    }
  }
`;

/**
 * __useOpenDraftFnolsQuery__
 *
 * To run a query within a React component, call `useOpenDraftFnolsQuery` and pass it any options that fit your needs.
 * When your component renders, `useOpenDraftFnolsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useOpenDraftFnolsQuery({
 *   variables: {
 *   },
 * });
 */
export function useOpenDraftFnolsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    OpenDraftFnolsQuery,
    OpenDraftFnolsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<OpenDraftFnolsQuery, OpenDraftFnolsQueryVariables>(
    OpenDraftFnolsDocument,
    options,
  );
}
export function useOpenDraftFnolsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    OpenDraftFnolsQuery,
    OpenDraftFnolsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<OpenDraftFnolsQuery, OpenDraftFnolsQueryVariables>(
    OpenDraftFnolsDocument,
    options,
  );
}
export type OpenDraftFnolsQueryHookResult = ReturnType<
  typeof useOpenDraftFnolsQuery
>;
export type OpenDraftFnolsLazyQueryHookResult = ReturnType<
  typeof useOpenDraftFnolsLazyQuery
>;
export type OpenDraftFnolsQueryResult = Apollo.QueryResult<
  OpenDraftFnolsQuery,
  OpenDraftFnolsQueryVariables
>;
export const FnolsDocument = gql`
  query fnols {
    fnols {
      clientClaimNumber
      createdAt
      id
      incidentDescription
      lossDatetime
      lossLocation
      lossState
      noticeType
      policeAgencyName
      policeReportNumber
      policy {
        policyNumber
        insuredName
      }
      createdBy
      status
      submittedFrom
      provider
    }
  }
`;

/**
 * __useFnolsQuery__
 *
 * To run a query within a React component, call `useFnolsQuery` and pass it any options that fit your needs.
 * When your component renders, `useFnolsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFnolsQuery({
 *   variables: {
 *   },
 * });
 */
export function useFnolsQuery(
  baseOptions?: Apollo.QueryHookOptions<FnolsQuery, FnolsQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<FnolsQuery, FnolsQueryVariables>(
    FnolsDocument,
    options,
  );
}
export function useFnolsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<FnolsQuery, FnolsQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<FnolsQuery, FnolsQueryVariables>(
    FnolsDocument,
    options,
  );
}
export type FnolsQueryHookResult = ReturnType<typeof useFnolsQuery>;
export type FnolsLazyQueryHookResult = ReturnType<typeof useFnolsLazyQuery>;
export type FnolsQueryResult = Apollo.QueryResult<
  FnolsQuery,
  FnolsQueryVariables
>;
export const FleetSearchDocument = gql`
  query fleetSearch($searchText: string!) {
    fetchFleetSafetySearch(searchText: $searchText) {
      dotNumber
      name
      state
    }
  }
`;

/**
 * __useFleetSearchQuery__
 *
 * To run a query within a React component, call `useFleetSearchQuery` and pass it any options that fit your needs.
 * When your component renders, `useFleetSearchQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFleetSearchQuery({
 *   variables: {
 *      searchText: // value for 'searchText'
 *   },
 * });
 */
export function useFleetSearchQuery(
  baseOptions: Apollo.QueryHookOptions<
    FleetSearchQuery,
    FleetSearchQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<FleetSearchQuery, FleetSearchQueryVariables>(
    FleetSearchDocument,
    options,
  );
}
export function useFleetSearchLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FleetSearchQuery,
    FleetSearchQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<FleetSearchQuery, FleetSearchQueryVariables>(
    FleetSearchDocument,
    options,
  );
}
export type FleetSearchQueryHookResult = ReturnType<typeof useFleetSearchQuery>;
export type FleetSearchLazyQueryHookResult = ReturnType<
  typeof useFleetSearchLazyQuery
>;
export type FleetSearchQueryResult = Apollo.QueryResult<
  FleetSearchQuery,
  FleetSearchQueryVariables
>;
export const PoliciesByDotDocument = gql`
  query PoliciesByDOT($dotNumber: string!) {
    policies(dotNumber: $dotNumber) {
      id
      policyNumber
      coverages
      startDate
      endDate
    }
  }
`;

/**
 * __usePoliciesByDotQuery__
 *
 * To run a query within a React component, call `usePoliciesByDotQuery` and pass it any options that fit your needs.
 * When your component renders, `usePoliciesByDotQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePoliciesByDotQuery({
 *   variables: {
 *      dotNumber: // value for 'dotNumber'
 *   },
 * });
 */
export function usePoliciesByDotQuery(
  baseOptions: Apollo.QueryHookOptions<
    PoliciesByDotQuery,
    PoliciesByDotQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<PoliciesByDotQuery, PoliciesByDotQueryVariables>(
    PoliciesByDotDocument,
    options,
  );
}
export function usePoliciesByDotLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    PoliciesByDotQuery,
    PoliciesByDotQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<PoliciesByDotQuery, PoliciesByDotQueryVariables>(
    PoliciesByDotDocument,
    options,
  );
}
export type PoliciesByDotQueryHookResult = ReturnType<
  typeof usePoliciesByDotQuery
>;
export type PoliciesByDotLazyQueryHookResult = ReturnType<
  typeof usePoliciesByDotLazyQuery
>;
export type PoliciesByDotQueryResult = Apollo.QueryResult<
  PoliciesByDotQuery,
  PoliciesByDotQueryVariables
>;
export const CreateRoleDocument = gql`
  mutation createRole(
    $group: RoleGroupEnum!
    $id: string!
    $agencyId: string
    $fleetId: string
  ) {
    createRole(
      group: $group
      userID: $id
      agencyID: $agencyId
      fleetID: $fleetId
    ) {
      ...Role
    }
  }
  ${RoleFragmentDoc}
`;
export type CreateRoleMutationFn = Apollo.MutationFunction<
  CreateRoleMutation,
  CreateRoleMutationVariables
>;

/**
 * __useCreateRoleMutation__
 *
 * To run a mutation, you first call `useCreateRoleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateRoleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createRoleMutation, { data, loading, error }] = useCreateRoleMutation({
 *   variables: {
 *      group: // value for 'group'
 *      id: // value for 'id'
 *      agencyId: // value for 'agencyId'
 *      fleetId: // value for 'fleetId'
 *   },
 * });
 */
export function useCreateRoleMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateRoleMutation,
    CreateRoleMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CreateRoleMutation, CreateRoleMutationVariables>(
    CreateRoleDocument,
    options,
  );
}
export type CreateRoleMutationHookResult = ReturnType<
  typeof useCreateRoleMutation
>;
export type CreateRoleMutationResult =
  Apollo.MutationResult<CreateRoleMutation>;
export type CreateRoleMutationOptions = Apollo.BaseMutationOptions<
  CreateRoleMutation,
  CreateRoleMutationVariables
>;
export const CreateUserDocument = gql`
  mutation CreateUser(
    $email: string!
    $firstName: string!
    $lastName: string!
    $password: string!
    $phoneNumber: string
    $title: string
    $profilePicture: string
    $roles: [createUserRoleArgs_InputObject!]!
  ) {
    createUser(
      email: $email
      firstName: $firstName
      lastName: $lastName
      password: $password
      phoneNumber: $phoneNumber
      title: $title
      profilePicture: $profilePicture
      roles: $roles
    ) {
      ...User
    }
  }
  ${UserFragmentDoc}
`;
export type CreateUserMutationFn = Apollo.MutationFunction<
  CreateUserMutation,
  CreateUserMutationVariables
>;

/**
 * __useCreateUserMutation__
 *
 * To run a mutation, you first call `useCreateUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createUserMutation, { data, loading, error }] = useCreateUserMutation({
 *   variables: {
 *      email: // value for 'email'
 *      firstName: // value for 'firstName'
 *      lastName: // value for 'lastName'
 *      password: // value for 'password'
 *      phoneNumber: // value for 'phoneNumber'
 *      title: // value for 'title'
 *      profilePicture: // value for 'profilePicture'
 *      roles: // value for 'roles'
 *   },
 * });
 */
export function useCreateUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateUserMutation,
    CreateUserMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CreateUserMutation, CreateUserMutationVariables>(
    CreateUserDocument,
    options,
  );
}
export type CreateUserMutationHookResult = ReturnType<
  typeof useCreateUserMutation
>;
export type CreateUserMutationResult =
  Apollo.MutationResult<CreateUserMutation>;
export type CreateUserMutationOptions = Apollo.BaseMutationOptions<
  CreateUserMutation,
  CreateUserMutationVariables
>;
export const DeactivateUserDocument = gql`
  mutation DeactivateUser($userID: string!) {
    deactivateUser(userID: $userID)
  }
`;
export type DeactivateUserMutationFn = Apollo.MutationFunction<
  DeactivateUserMutation,
  DeactivateUserMutationVariables
>;

/**
 * __useDeactivateUserMutation__
 *
 * To run a mutation, you first call `useDeactivateUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeactivateUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deactivateUserMutation, { data, loading, error }] = useDeactivateUserMutation({
 *   variables: {
 *      userID: // value for 'userID'
 *   },
 * });
 */
export function useDeactivateUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeactivateUserMutation,
    DeactivateUserMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeactivateUserMutation,
    DeactivateUserMutationVariables
  >(DeactivateUserDocument, options);
}
export type DeactivateUserMutationHookResult = ReturnType<
  typeof useDeactivateUserMutation
>;
export type DeactivateUserMutationResult =
  Apollo.MutationResult<DeactivateUserMutation>;
export type DeactivateUserMutationOptions = Apollo.BaseMutationOptions<
  DeactivateUserMutation,
  DeactivateUserMutationVariables
>;
export const UsersDocument = gql`
  query Users {
    users {
      ...User
    }
  }
  ${UserFragmentDoc}
`;

/**
 * __useUsersQuery__
 *
 * To run a query within a React component, call `useUsersQuery` and pass it any options that fit your needs.
 * When your component renders, `useUsersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUsersQuery({
 *   variables: {
 *   },
 * });
 */
export function useUsersQuery(
  baseOptions?: Apollo.QueryHookOptions<UsersQuery, UsersQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<UsersQuery, UsersQueryVariables>(
    UsersDocument,
    options,
  );
}
export function useUsersLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<UsersQuery, UsersQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<UsersQuery, UsersQueryVariables>(
    UsersDocument,
    options,
  );
}
export type UsersQueryHookResult = ReturnType<typeof useUsersQuery>;
export type UsersLazyQueryHookResult = ReturnType<typeof useUsersLazyQuery>;
export type UsersQueryResult = Apollo.QueryResult<
  UsersQuery,
  UsersQueryVariables
>;
export const UpdateAgentLicenseDocument = gql`
  mutation updateAgentLicense(
    $id: string!
    $licenseNumber: string
    $licenseType: string
    $licenseStatus: string
    $state: string
    $effectiveDate: Time
    $expirationDate: Time
  ) {
    updateAgentLicense(
      id: $id
      licenseNumber: $licenseNumber
      licenseType: $licenseType
      licenseStatus: $licenseStatus
      state: $state
      effectiveDate: $effectiveDate
      expirationDate: $expirationDate
    )
  }
`;
export type UpdateAgentLicenseMutationFn = Apollo.MutationFunction<
  UpdateAgentLicenseMutation,
  UpdateAgentLicenseMutationVariables
>;

/**
 * __useUpdateAgentLicenseMutation__
 *
 * To run a mutation, you first call `useUpdateAgentLicenseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateAgentLicenseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateAgentLicenseMutation, { data, loading, error }] = useUpdateAgentLicenseMutation({
 *   variables: {
 *      id: // value for 'id'
 *      licenseNumber: // value for 'licenseNumber'
 *      licenseType: // value for 'licenseType'
 *      licenseStatus: // value for 'licenseStatus'
 *      state: // value for 'state'
 *      effectiveDate: // value for 'effectiveDate'
 *      expirationDate: // value for 'expirationDate'
 *   },
 * });
 */
export function useUpdateAgentLicenseMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateAgentLicenseMutation,
    UpdateAgentLicenseMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateAgentLicenseMutation,
    UpdateAgentLicenseMutationVariables
  >(UpdateAgentLicenseDocument, options);
}
export type UpdateAgentLicenseMutationHookResult = ReturnType<
  typeof useUpdateAgentLicenseMutation
>;
export type UpdateAgentLicenseMutationResult =
  Apollo.MutationResult<UpdateAgentLicenseMutation>;
export type UpdateAgentLicenseMutationOptions = Apollo.BaseMutationOptions<
  UpdateAgentLicenseMutation,
  UpdateAgentLicenseMutationVariables
>;
export const UpdateUserDocument = gql`
  mutation updateUser(
    $id: string!
    $firstName: string
    $lastName: string
    $email: string
    $password: string
    $phoneNumber: string
    $title: string
    $profilePicture: string
  ) {
    patchUser(
      id: $id
      firstName: $firstName
      lastName: $lastName
      email: $email
      password: $password
      phoneNumber: $phoneNumber
      title: $title
      profilePicture: $profilePicture
    ) {
      ...User
    }
  }
  ${UserFragmentDoc}
`;
export type UpdateUserMutationFn = Apollo.MutationFunction<
  UpdateUserMutation,
  UpdateUserMutationVariables
>;

/**
 * __useUpdateUserMutation__
 *
 * To run a mutation, you first call `useUpdateUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserMutation, { data, loading, error }] = useUpdateUserMutation({
 *   variables: {
 *      id: // value for 'id'
 *      firstName: // value for 'firstName'
 *      lastName: // value for 'lastName'
 *      email: // value for 'email'
 *      password: // value for 'password'
 *      phoneNumber: // value for 'phoneNumber'
 *      title: // value for 'title'
 *      profilePicture: // value for 'profilePicture'
 *   },
 * });
 */
export function useUpdateUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateUserMutation,
    UpdateUserMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UpdateUserMutation, UpdateUserMutationVariables>(
    UpdateUserDocument,
    options,
  );
}
export type UpdateUserMutationHookResult = ReturnType<
  typeof useUpdateUserMutation
>;
export type UpdateUserMutationResult =
  Apollo.MutationResult<UpdateUserMutation>;
export type UpdateUserMutationOptions = Apollo.BaseMutationOptions<
  UpdateUserMutation,
  UpdateUserMutationVariables
>;
export const InviteUserDocument = gql`
  mutation InviteUser(
    $email: string!
    $role: RoleGroupEnum!
    $agencyID: string!
  ) {
    inviteAgencyUserFromSupport(
      email: $email
      role: $role
      agencyID: $agencyID
    ) {
      shareID
    }
  }
`;
export type InviteUserMutationFn = Apollo.MutationFunction<
  InviteUserMutation,
  InviteUserMutationVariables
>;

/**
 * __useInviteUserMutation__
 *
 * To run a mutation, you first call `useInviteUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useInviteUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [inviteUserMutation, { data, loading, error }] = useInviteUserMutation({
 *   variables: {
 *      email: // value for 'email'
 *      role: // value for 'role'
 *      agencyID: // value for 'agencyID'
 *   },
 * });
 */
export function useInviteUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    InviteUserMutation,
    InviteUserMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<InviteUserMutation, InviteUserMutationVariables>(
    InviteUserDocument,
    options,
  );
}
export type InviteUserMutationHookResult = ReturnType<
  typeof useInviteUserMutation
>;
export type InviteUserMutationResult =
  Apollo.MutationResult<InviteUserMutation>;
export type InviteUserMutationOptions = Apollo.BaseMutationOptions<
  InviteUserMutation,
  InviteUserMutationVariables
>;
