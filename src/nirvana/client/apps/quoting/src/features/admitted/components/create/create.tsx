import {
  AppBar,
  Box,
  CircularProgress,
  Container,
  Grid,
  Paper,
} from '@material-ui/core';
import {
  ArrowBackIosRounded as ArrowBackIosRoundedIcon,
  ArrowForwardIosRounded as ArrowForwardIosRoundedIcon,
} from '@material-ui/icons';
import {
  AdmittedApp,
  CoverageType,
  CreditReportStatus,
  VehicleType,
} from '@nirvana/api/non-fleet';
import { ApplicationHeader, debounce, Show } from '@nirvana/ui-kit';
import { ErrorBoundary } from '@sentry/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import _ from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import {
  useBeforeUnload,
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';
import Button from 'src/components/button';
import { ClearanceConflictDialog } from 'src/components/clearanceConflict';
import ContactUs from 'src/components/contactUs';
import ServerError from 'src/components/serverError';
import {
  NF_APPLICATION_START,
  NF_PROCEED_CLICK,
  NF_SSN_MODAL_CLOSE,
  NF_SSN_MODAL_OPEN,
  NF_SSN_SUBMIT,
} from 'src/constants/analytics';
import {
  decodeVehicleVINs,
  fetchCreditStatus,
  submitApplication,
  submitApplicationSSN,
  submitQuote,
  updateApplicationDetails,
} from 'src/features/admitted/queries/application';
import { useAnalytics } from 'src/helpers/analytics';
import { Feature, useFeatureFlag } from 'src/helpers/featureFlags';
import { useApplicationDetailsContext } from '../../hooks/useApplicationDetails';
import { Step, getSteps } from './constants/steps';
import VinConfirmationModal from './equipment/vinConfirmationModal';
import PageHeader from './pageHeader';
import DialogSSNPrompt from './ssn-prompt';
import { useStyles } from './styles';

const ApplicationCreate = () => {
  const classes = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const { applicationId = '' } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const { capture } = useAnalytics();
  const clearanceConflict = searchParams.get('clearanceConflict') || '0';
  const {
    applitionHeaderInfo,
    applicationDetails,
    isLoading: isLoadingApplicationDetails,
    setApplicationPanic,
    isApplicationPanic,
  } = useApplicationDetailsContext();

  const [isIndicationLoading, setIndicationLoading] = useState(false);
  const onIndicationLoadingChange = useCallback(setIndicationLoading, [
    setIndicationLoading,
  ]);
  const [activeStepIndex, setActiveStepIndex] = useState(0);
  const [invalidVins, setInvalidVins] = useState<string[]>([]);
  const [showVinConfirmationModal, setShowVinConfirmationModal] =
    useState(false);
  const [showSSNPromptModal, setShowSSNPromptModal] = useState(false);

  const { mutate: decodeVINMutate, isLoading: isDecodingVINs } =
    useMutation(decodeVehicleVINs);

  const getFeatureValue = useFeatureFlag();

  const isNoCreditHitEnabled = getFeatureValue(Feature.NO_CREDIT_HIT, false);
  const isPreTelematicsFlowEnabled = getFeatureValue(
    Feature.PRE_TELEMATICS_EXPERIMENT,
    false,
  );

  const { state } = location;
  const activeStepStateKey = state?.activeStep;

  useEffect(() => {
    capture(NF_APPLICATION_START, {});
    // Only fire once on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleStepEdit = useCallback((stepKey: string) => {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    gotoStep(stepKey);
  }, []);
  const Steps: Step[] = useMemo(
    () =>
      getSteps(applicationId, {
        onIndicationLoadingChange,
        onEdit: handleStepEdit,
      }),
    [applicationId, onIndicationLoadingChange, handleStepEdit],
  );
  const gotoStep = (stepKey: string) => {
    const stepIndex = Steps.findIndex((record) => stepKey === record.key);
    setActiveStepIndex(stepIndex);
  };

  const activeStep = Steps[activeStepIndex];
  const nextStep = Steps[Math.min(activeStepIndex + 1, Steps.length - 1)];
  const methods = useForm<AdmittedApp>();
  const { handleSubmit, reset, setError, getValues } = methods;

  const isQuoteTelematicsEnabled = getFeatureValue(
    Feature.QUOTE_TELEMATICS_ENABLED,
    false,
  );

  const handlePrevious = () => {
    setActiveStepIndex((prev) => (prev > 0 ? prev - 1 : prev));
  };

  const queryClient = useQueryClient();

  const gotoNextStep = () => {
    setActiveStepIndex((prev) => prev + 1);

    queryClient.invalidateQueries(['application', applicationId]);
  };

  const { mutate: submitIndicationMutation } = useMutation(submitQuote, {
    onSuccess: () => {
      setActiveStepIndex((prev) => prev + 1);

      queryClient.invalidateQueries(['application', applicationId]);
    },
    onError: (ex: AxiosError) => {
      if (ex.response?.status === 500) {
        setApplicationPanic(true);
      } else {
        navigate(`/non-fleet/application/${applicationId}/decline`);
      }
    },
  });

  const isPreQuoteTelematicsEnabled =
    applicationDetails?.admitted?.operationsForm?.companyInfo
      ?.selectedProviderType === 'Premier' && isPreTelematicsFlowEnabled;

  const {
    mutate: submitApplicationMutation,
    isLoading: isSubmittingApplication,
  } = useMutation(submitApplication, {
    onSuccess: () => {
      isPreQuoteTelematicsEnabled
        ? navigate(
            `/non-fleet/application/${applicationId}/preTelematicsSuccess`,
          )
        : navigate(`/non-fleet/application/${applicationId}/success`);
    },
    onError: (ex: AxiosError) => {
      if (ex.response?.status === 500) {
        setApplicationPanic(true);
      } else {
        navigate(`/non-fleet/application/${applicationId}/decline`);
      }
    },
  });

  const { mutate: submitSSN, isLoading: isSubmittingSSN } = useMutation(
    submitApplicationSSN,
    {
      onSettled: () => {
        setShowSSNPromptModal(false);
        gotoNextStep();
      },
    },
  );

  const { mutate: checkCreditStatus, isLoading: isCheckingCreditStatus } =
    useMutation(fetchCreditStatus, {
      onSuccess: (response) => {
        if (
          isNoCreditHitEnabled &&
          (response.status === CreditReportStatus.NoHit ||
            response.status === CreditReportStatus.Failed)
        ) {
          capture(NF_SSN_MODAL_OPEN, {
            applicationId,
          });
          setShowSSNPromptModal(true);
        } else if (
          isNoCreditHitEnabled &&
          response.status === CreditReportStatus.InProgress
        ) {
          setTimeout(() => {
            checkCreditStatus(applicationId);
          }, 2000);
        } else {
          gotoNextStep();
        }
      },
    });

  const { mutate, isLoading: isUpdatingApplication } = useMutation(
    updateApplicationDetails,
    {
      onSuccess: (data) => {
        localStorage.removeItem(`unsavedApplication|${applicationId}`);

        if (data.appStatus === 'AppStateDeclined') {
          navigate(`/non-fleet/application/${applicationId}/decline`);
        } else if (activeStep.key === 'drivers') {
          // Submit for indication
          submitIndicationMutation(applicationId);
        } else if (activeStepIndex <= 3) {
          if (activeStepIndex === 0) {
            // Check credit status
            checkCreditStatus(applicationId);
          } else {
            gotoNextStep();
          }
        }
      },
      onError: (ex: AxiosError) => {
        if (ex.response?.status === 500) {
          setApplicationPanic(true);
        } else {
          navigate(`/non-fleet/application/${applicationId}/error`);
        }
      },
    },
  );

  const isLoadingNextStep =
    isUpdatingApplication ||
    isSubmittingApplication ||
    isIndicationLoading ||
    isDecodingVINs ||
    isCheckingCreditStatus;

  const onSubmit = (
    data: AdmittedApp,
    { skipVinDecode }: { skipVinDecode?: boolean },
  ) => {
    capture(NF_PROCEED_CLICK, {
      applicationId: applicationId,
      step: activeStep.key,
    });
    const updatedDriversForm = {
      ...data.driversForm,
      drivers: data?.driversForm?.drivers?.map((driver) => ({
        ...driver,
        isIncluded: true,
      })),
    };

    // Commodities distribution validation
    if (
      activeStepIndex === 0 &&
      data.operationsForm?.commodityDistribution &&
      data.operationsForm?.commodityDistribution?.length
    ) {
      // Calculate total and set error
      const total = data.operationsForm?.commodityDistribution?.reduce(
        (acc, { category, percentage }) => {
          if (category && percentage) {
            return acc + percentage;
          }
          return acc;
        },
        0,
      );

      if (total !== 100) {
        setError('operationsForm.commodityDistribution', {
          type: 'manual',
          message: 'Total percentage must be 100%',
        });
        return;
      }
    }

    const payload = {
      applicationId,
      payload: {
        pageState: nextStep.pageState,
        admitted: {
          operationsForm: {
            ...data.operationsForm,
            commodityDistribution:
              data.operationsForm?.commodityDistribution?.filter(
                ({ category }) => category,
              ),
          },
          equipmentsForm: data.equipmentsForm,
          driversForm: updatedDriversForm,
          indicationForm: data.indicationForm,
        },
      },
    };

    if (
      activeStep.key === 'equipment' &&
      data?.equipmentsForm?.vehicles?.length
    ) {
      if (
        !data.equipmentsForm?.vehicles?.some(({ vehicleType }) =>
          [
            VehicleType.VehicleTypeTruck,
            VehicleType.VehicleTypeTractor,
          ].includes(vehicleType),
        )
      ) {
        return setError('equipmentsForm.vehicles', {
          [data.equipmentsForm?.vehicles?.length - 1]: {
            common: {
              type: 'manual',
              message: 'Please enter at least one power unit',
            },
          },
        });
      }

      // If APD coverage is required, check if stated value is entered for at least one vehicle
      if (
        data.operationsForm?.coverages?.find(
          (coverage) =>
            coverage.coverageType === CoverageType.CoverageAutoPhysicalDamage,
        )?.isRequired
      ) {
        const totalStatedValue = data.equipmentsForm?.vehicles?.reduce(
          (acc, { statedValue }) => {
            if (statedValue) {
              return acc + (statedValue ?? 0);
            }

            return acc;
          },
          0,
        );

        if (!totalStatedValue) {
          return setError('equipmentsForm.vehicles', {
            [data.equipmentsForm?.vehicles?.length - 1]: {
              common: {
                type: 'manual',
                message:
                  'Please enter stated value for at least one vehicle to cover in Auto Physical Damage',
              },
            },
          });
        }
      }

      const vins: string[] = data.equipmentsForm?.vehicles?.map((record) => {
        return record.vin;
      });

      // Duplicate VINs check
      const errors: { [key: number]: any } = {};
      const counts: { [key: string]: number } = {};
      const lastIndex: { [key: string]: number } = {};

      vins.forEach(function (vin, index) {
        counts[vin] = (counts[vin] || 0) + 1;
        if (counts[vin] > 1) {
          lastIndex[vin] = index;
        }
      });

      Object.values(lastIndex).forEach((index: number) => {
        errors[index] = {
          vin: {
            type: 'manual',
            message:
              'Please enter a valid VIN, duplicates are not allowed. For unidentified trailers, use "Trailer 1", "Trailer 2", etc.',
          },
        };
      });

      if (Object.keys(errors).length) {
        return setError('equipmentsForm.vehicles', errors);
      }

      if (!skipVinDecode) {
        decodeVINMutate(vins, {
          onSuccess: (vinDecodingData) => {
            const invalidVins = vinDecodingData
              .filter((item) => item.hasDecodingError)
              .map((item) => item.vin);

            if (invalidVins.length > 0) {
              setShowVinConfirmationModal(true);
              setInvalidVins(invalidVins);
              return;
            }
            // Final mutation after VIN decoding
            mutate(payload);
          },
        });
        return;
      }
    }

    // Handle drivers step
    if (activeStep.key === 'drivers') {
      const cdlNumbers: string[] =
        data.driversForm?.drivers?.map((record) => record.licenseNumber) ?? [];

      const errors: { [key: number]: any } = {};
      const counts: { [key: string]: number } = {};
      const lastIndex: { [key: string]: number } = {};
      cdlNumbers.forEach(function (cdl, index) {
        counts[cdl] = (counts[cdl] || 0) + 1;
        if (counts[cdl] > 1) {
          lastIndex[cdl] = index;
        }
      });

      Object.values(lastIndex).forEach((index: number) => {
        errors[index] = {
          licenseNumber: {
            type: 'manual',
            message:
              'Please enter a valid CDL number, duplicates are not allowed.',
          },
        };
      });

      if (Object.keys(errors).length) {
        return setError('driversForm.drivers', errors);
      }
    }

    // Handle review step
    if (activeStep.key === 'review') {
      submitApplicationMutation(applicationId);
    } else {
      // Final mutation call for non-skipDecode or non-review cases
      mutate(payload);
    }
  };

  useEffect(() => {
    // Find step with matching pageState
    const stepIndex = Steps.findIndex(
      (step) => step.pageState === applicationDetails?.pageState,
    );

    // Set active step index based on pageState
    if (stepIndex >= 0) {
      setActiveStepIndex(stepIndex);
    }
  }, [Steps, applicationDetails?.pageState]);

  useEffect(() => {
    let formData = applicationDetails?.admitted ?? {};
    let savedApplication;
    try {
      savedApplication = JSON.parse(
        localStorage.getItem(`unsavedApplication|${applicationId}`) || '{}',
      );
    } catch (e) {
      savedApplication = { data: {} };
    }

    const telematicsInfo =
      applicationDetails?.admitted?.indicationForm?.telematicsInfo;

    if (
      telematicsInfo?.link &&
      savedApplication?.data?.indicationForm?.telematicsInfo &&
      !_.isEqual(
        telematicsInfo,
        savedApplication?.data?.indicationForm?.telematicsInfo,
      )
    ) {
      // during refresh in indication, sometimes an empty value gets stored in localStorage (if telematics link not generated)
      // and overrides the actual telematics value. To set the correct telematicsInfo,
      // we need to explicitly assign the correct value here.
      savedApplication.data.indicationForm.telematicsInfo = telematicsInfo;
    }

    if (savedApplication?.currentStep === activeStepStateKey) {
      formData = {
        ...formData,
        ...savedApplication?.data,
      };
    }
    reset(formData);
  }, [applicationDetails, reset, activeStepStateKey, applicationId]);

  useEffect(() => {
    if (activeStepStateKey) {
      handleStepEdit(activeStepStateKey);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeStepStateKey]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [activeStepIndex]);

  const beforeUnloadEventHandler = useCallback(() => {
    if (!_.isEmpty(getValues())) {
      localStorage.setItem(
        `unsavedApplication|${applicationId}`,
        JSON.stringify({
          currentStep: activeStepStateKey,
          timeStamp: Date.now(),
          data: getValues(),
        }),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applicationId, activeStepStateKey]);

  useBeforeUnload(() => {
    beforeUnloadEventHandler();
  });

  useEffect(() => {
    return () => {
      beforeUnloadEventHandler();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const isReviewStep = activeStep?.key === 'review';

  if (isLoadingApplicationDetails) {
    return (
      <Box flex="1" display="flex" alignItems="center" justifyContent="center">
        <CircularProgress />
      </Box>
    );
  }

  if (isApplicationPanic) {
    return (
      <Grid item container direction="column" flexGrow={1}>
        <Grid item>
          <ApplicationHeader
            details={applitionHeaderInfo}
            onBack={() => {
              navigate('/applications/list', { replace: true });
            }}
          />
        </Grid>
        <ServerError />
      </Grid>
    );
  }
  const handleVinValidationSkipProceed = () => {
    setShowVinConfirmationModal(false);
    handleSubmit((data) => onSubmit(data, { skipVinDecode: true }))();
  };

  const handleProceedSubmit = (data: AdmittedApp) => onSubmit(data, {});

  return (
    <>
      <ApplicationHeader
        details={applitionHeaderInfo}
        onBack={() => {
          navigate('/applications/list', { replace: true });
        }}
      />

      <ErrorBoundary fallback={<ContactUs />}>
        <Paper
          elevation={0}
          square
          sx={{
            position: 'relative',
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Show when={isQuoteTelematicsEnabled && !isReviewStep}>
            <PageHeader
              title={activeStep.title}
              subtitle={activeStep.subtitle}
              activeStepIndex={activeStepIndex}
            />
          </Show>
          <Container
            sx={{
              display: 'flex',
              maxWidth:
                activeStep.key === 'indication'
                  ? '1056px !important'
                  : undefined,
            }}
          >
            <Box py={5} px={3} display="flex" flexDirection="column" flex="1">
              <Show when={!isQuoteTelematicsEnabled || isReviewStep}>
                <PageHeader
                  title={activeStep.title}
                  subtitle={activeStep.subtitle}
                  activeStepIndex={activeStepIndex}
                />
              </Show>
              <FormProvider {...methods}>{activeStep.render()}</FormProvider>
            </Box>
          </Container>
        </Paper>
        {/* Spacer to compensate for height of fixed bottom bar */}
        <Box py="32px" />

        {/* Sticky bottom bar  */}
        <AppBar
          classes={{ root: classes.footerToolbar }}
          color="inherit"
          elevation={0}
          position="fixed"
          sx={{ top: 'auto', bottom: 0, py: 1 }}
        >
          <Container
            sx={{
              display: 'flex',
              maxWidth:
                activeStep.key === 'indication'
                  ? '1056px !important'
                  : undefined,
              justifyContent: 'flex-end',
            }}
          >
            <Box display="flex" justifyContent="flex-end">
              {activeStepIndex >= 1 ? (
                <Box mx={1}>
                  <Button
                    startIcon={<ArrowBackIosRoundedIcon />}
                    variant="outlined"
                    onClick={handlePrevious}
                  >
                    Previous
                  </Button>
                </Box>
              ) : null}

              <Button
                endIcon={<ArrowForwardIosRoundedIcon />}
                variant="contained"
                onClick={debounce(handleSubmit(handleProceedSubmit), 800)}
                disabled={isLoadingNextStep}
                startIcon={
                  isLoadingNextStep ? <CircularProgress size={20} /> : undefined
                }
              >
                {activeStepIndex === 4 ? 'Submit' : 'Proceed'}
              </Button>
            </Box>
          </Container>
        </AppBar>
      </ErrorBoundary>

      <ClearanceConflictDialog
        open={Boolean(+clearanceConflict)}
        onClose={() => {
          searchParams.delete('clearanceConflict');
          setSearchParams(searchParams);
        }}
      />

      <VinConfirmationModal
        open={showVinConfirmationModal}
        onClose={() => setShowVinConfirmationModal(false)}
        invalidVins={invalidVins}
        onSkipProceed={handleVinValidationSkipProceed}
      />

      <DialogSSNPrompt
        open={showSSNPromptModal}
        isSubmitting={isSubmittingSSN}
        onClose={() => {
          capture(NF_SSN_MODAL_CLOSE, {
            applicationId,
          });

          setShowSSNPromptModal(false);

          gotoNextStep();
        }}
        onSubmit={(data) => {
          capture(NF_SSN_SUBMIT, {
            applicationId,
          });

          submitSSN({ payload: data, applicationId: applicationId });
        }}
      />
    </>
  );
};

export default ApplicationCreate;
