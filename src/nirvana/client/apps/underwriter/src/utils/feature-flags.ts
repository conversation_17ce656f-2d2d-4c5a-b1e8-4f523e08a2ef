import { useFlags } from 'launchdarkly-react-client-sdk';

export type FleetType = 'fleet' | 'retail-non-fleet' | 'wholesale-non-fleet';
export const ANONYMOUS_USER_KEY = '00000000-0000-0000-0000-000000000000';

export enum Feature {
  AI_LOSS_SUMMARY = 'aiLossSummary',
  AUTHORITY_APPROVE_MODAL = 'authorityApproveModal',
  CAMERA_PROGRAM = 'cameraProgram',
  CLEARANCE_UW_CHANGES = 'clearanceUwChanges',
  EDIT_TERMINAL_LOCATIONS = 'editTerminalLocations',
  ENDORSEMENT_REVIEW_AUTOMATED_PRICING = 'endorsementReviewAutomatedPricing',
  FEATURE_POLICIES_LIST = 'policiesList',
  FILL_POLICY_CHANGE_FORM = 'fillPolicyChangeDoc',
  FLEET_TYPE = 'fleetType',
  MINIMUM_MILEAGE_GUARANTEE = 'minimumMileageGuarantee',
  MST_REFERRAL = 'mstReferral',
  NEGOTIATED_RATES_STATE = 'negotiatedRatesState',
  NF_MVR_UPDATE = 'nfMvrEdit',
  PRICING_EXPLAINABILITY_UW_RANKING_BUTTON = 'pricingExplainabilityUwRankingButton',
  REVIEW_READINESS = 'reviewReadiness',
  SAFETY_SCORE = 'safetyScore',
  SHOW_DAYS_CONNECTED = 'showDaysConnected',
  SHOW_PIBIT_DATA = 'showPibitData',
  SHOW_RECOMMENDATION_RANGE_PRICING = 'showRecommendationRangePricing',
  TARGET_PRICING = 'targetPricing',
  TELEMATICS_STATUS = 'telematicsStatus',
  UW_POSTHOG = 'uwPosthog',
  UW_AI_DOCUMENT = 'uwAiDocument',
  UW_EQUIPMENT_LIST_EDIT = 'uwEquipmentListEdit',
  PRE_TELEMATICS_EXPERIMENT = 'preQuoteTelematicsV1Experiment',
  BIZ_AUTO = 'bizAuto',
}
export function useFeatureEnabled<T>(flag: string, fallback?: T) {
  const flags = useFlags();

  if (fallback === undefined) {
    return flags[flag] as T;
  }

  return (flags[flag] as T) ?? fallback;
}
