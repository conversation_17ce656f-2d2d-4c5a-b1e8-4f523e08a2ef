import clsx from 'clsx';
import { useMemo } from 'react';
import { HiOutlineExternalLink } from 'react-icons/hi';
import { useLocation, useParams } from 'react-router-dom';
import { useMutation, useQuery, useQueryClient } from 'react-query';

import {
  ApplicationReviewState,
  TelematicsDataButtonStatus,
  TelematicsDataStatus,
} from '@nirvana/api/uw';
import { Button, Toggle } from '@nirvana/ui';
import { Show, Tooltip } from '@nirvana/ui-kit';

import { isTSPGeoTab } from 'src/pages/applications/utils';
import { usePanelReviewContext } from 'src/hooks/use-panel-review';
import { useDownloadLossRunsReport } from 'src/hooks/use-download-loss-runs-reports';
import {
  fetchApplicationReviewSummary,
  updateApplicationReviewById,
} from 'src/queries/applications';
import { PosthogEvents, useAnalytics } from 'src/utils/analytics';
import { Feature, FleetType, useFeatureEnabled } from 'src/utils/feature-flags';

type PosthogEventsKey = keyof typeof PosthogEvents;

export default function ReviewBar() {
  const { pathname } = useLocation();
  const { capture } = useAnalytics();
  const { appReviewId = '' } = useParams();

  const fleetType = useFeatureEnabled<FleetType>(Feature.FLEET_TYPE);

  const activeTab = pathname.split('/')[3];
  const { data, isLoading, telematicsDataStatus } = usePanelReviewContext();
  const { handleGenerateLossRunsReports, isDownloadingLossRunsReports } =
    useDownloadLossRunsReport();

  const { data: appReviewSummary, isLoading: isLoadingSummary } = useQuery(
    ['summary', appReviewId],
    () => fetchApplicationReviewSummary(appReviewId),
  );

  const queryClient = useQueryClient();
  const { mutate } = useMutation(updateApplicationReviewById, {
    onMutate: async (newData) => {
      // Cancel any outgoing refetches, so they don't overwrite our optimistic update
      await queryClient.cancelQueries(['applications', appReviewId]);
      // Snapshot the previous value
      const prevValue = queryClient.getQueryData(['applications', appReviewId]);
      // Optimistically update to the new value
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      queryClient.setQueryData(['applications', appReviewId], (old: any) => {
        return {
          ...old,
          panelWiseReviewInfo: { ...old.panelWiseReviewInfo, ...newData.body },
        };
      });
      return { prevValue };
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError: (_, __, context: any) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(
        ['applications', appReviewId],
        context.prevValue,
      );
    },
    // Always refetch after error or success:
    onSettled: () => {
      queryClient.invalidateQueries(['applications', appReviewId]);
      queryClient.invalidateQueries(['actions', appReviewId]);
    },
  });

  const merit = useMemo(() => {
    if (isLoading) {
      return (
        <div className="w-16 h-8 rounded bg-primary-extraLight animate-pulse" />
      );
    }
    if (data && activeTab !== 'overview') {
      const merit =
        data.panelWiseReviewInfo[
          activeTab as keyof typeof data.panelWiseReviewInfo
        ]?.merit ?? 0;
      return (
        <div
          className={clsx(
            'w-16 px-2 py-1 min-w-fit border rounded bg-primary-extraLight cursor-not-allowed',
            {
              'text-green-500 bg-green-50': merit > 0,
              'text-red-500 bg-red-50': merit < 0,
            },
          )}
        >
          {merit > 0 ? `+ ${merit}` : merit}
        </div>
      );
    }
    return null;
  }, [activeTab, data, isLoading]);

  const toggleValue = useMemo(() => {
    if (data) {
      return (
        data.panelWiseReviewInfo[
          activeTab as keyof typeof data.panelWiseReviewInfo
        ]?.isReviewed ?? false
      );
    }
    return false;
  }, [data, activeTab]);

  const isToggleDisabled = useMemo(() => {
    return (
      data?.summary.state ===
        ApplicationReviewState.ApplicationReviewStateDeclined ||
      data?.summary.state === ApplicationReviewState.ApplicationReviewStateStale
    );
  }, [data]);

  // onChange handler for the switch component
  function handleToggleChange(checked: boolean) {
    if (checked) {
      const key =
        `${activeTab.toUpperCase()}_PANEL_REVIEWED` as PosthogEventsKey;
      capture(PosthogEvents[key]);
    } else {
      const key =
        `${activeTab.toUpperCase()}_PANEL_UNREVIEWED` as PosthogEventsKey;
      capture(PosthogEvents[key]);
    }

    mutate({ appReviewId, body: { [activeTab]: { isReviewed: checked } } });
  }

  const viewTelematicsBtn = (
    <Button
      className="mr-6"
      variant="secondary"
      disabled={
        data?.telematicsDataButtonStatus ===
          TelematicsDataButtonStatus.Disabled &&
        !isTSPGeoTab(data?.summary.tspName)
      }
    >
      <a
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center"
        href={`${
          import.meta.env.VITE_BOARDS_APP_URL
        }/?app_review_id=${appReviewId}&page=UW+Board`}
      >
        <HiOutlineExternalLink className="mr-1" />
        View Telematics Data
      </a>
    </Button>
  );

  const handleLossRunsDownload = async () => {
    if (!appReviewSummary) {
      return;
    }

    await handleGenerateLossRunsReports(appReviewSummary.dotNumber);
  };

  return (
    <div className="flex items-center">
      <Show
        when={
          activeTab !== 'overview' && !data?.summary?.isRiskWorksheetEnabled
        }
      >
        <div className="flex items-center space-x-1">
          <span className="text-sm">Merit:</span>
          {merit}
        </div>
      </Show>

      <div className="flex-1" />

      <Show when={appReviewSummary?.isRenewal && activeTab === 'losses'}>
        <Button
          variant="secondary"
          className="mr-6"
          color="primary"
          disabled={isLoadingSummary || isDownloadingLossRunsReports}
          onClick={handleLossRunsDownload}
        >
          Generate Loss Runs Reports for active policies
        </Button>
      </Show>

      <Show
        when={
          (fleetType !== 'wholesale-non-fleet' &&
            data?.telematicsDataButtonStatus !==
              TelematicsDataButtonStatus.NotConnected) ||
          telematicsDataStatus?.TelematicsDataStatus ===
            TelematicsDataStatus.NotEnoughData
        }
      >
        {data?.telematicsDataButtonStatus ===
          TelematicsDataButtonStatus.Disabled &&
        !isTSPGeoTab(data?.summary.tspName || '') ? (
          <Tooltip
            title={
              <p className="text-xs">
                No historical data
                <br />
                available from TSP
              </p>
            }
          >
            <span>{viewTelematicsBtn}</span>
          </Tooltip>
        ) : (
          viewTelematicsBtn
        )}
      </Show>

      <span className="mr-3 text-sm text-primary-main">Mark as reviewed</span>
      <Toggle
        checked={toggleValue}
        disabled={isToggleDisabled}
        onCheckedChange={handleToggleChange}
      />
    </div>
  );
}
