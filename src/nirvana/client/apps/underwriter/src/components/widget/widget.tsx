import { Button } from '@material-ui/core';
import { ErrorBoundary } from '@sentry/react';
import clsx from 'clsx';
import type { ReactNode } from 'react';
import { HiFlag } from 'react-icons/hi';

import { ApplicationReviewWidgetMeta } from '@nirvana/api/uw';
import { range, Show, Switch } from '@nirvana/ui-kit';
import { useCallback, useEffect, useState } from 'react';

import { usePanelDisabledState } from 'src/hooks/use-panel-disabled-state';
import { usePanelReviewContext } from 'src/hooks/use-panel-review';
import BadgeInput from './badge-input';
import CreditBar from './credit-bar';
import WidgetTitle from './widget-title';

/** Widget component properties */
type WidgetProps = {
  /** Unique id for the widget container */
  id?: string;
  /** Title of the widget */
  title: ReactNode;
  /** Provide contents of widget as children */
  children: ReactNode;
  /** Whether the values of badges are loaded from API or not */
  isLoading?: boolean;
  /** Credit Merit scores for the widget */
  badgeScores?: ApplicationReviewWidgetMeta;
  /** Mutate function to update badge values in server */
  batchUpdater?: (badgeScores: ApplicationReviewWidgetMeta) => void;
  /** Classes to be applied to the widget */
  className?: string;
  footer?: ReactNode;
  /** Flag to disable credit bar input fields */
  isCreditDisabled?: boolean;
};

const PLACEHOLDER_BADGE_COUNT = 2;

/** Widget component is used to render widgets (like business-years, parking-locations, etc.)
 *  in all the available Panels(Operations, Equipments, etc.)
 */
function WidgetRenderer({
  id,
  title,
  children,
  isLoading,
  badgeScores,
  batchUpdater,
  className,
  footer,
  isCreditDisabled,
}: WidgetProps) {
  const [badgeValues, setBadges] = useState(badgeScores ?? {});
  const { credit, merit } = badgeValues;
  const { data: appReview } = usePanelReviewContext();

  // Disable Merit and Credit badges if panel is
  // reviewed or Application is in Declined/Stale state
  const isDisabled = usePanelDisabledState();

  useEffect(() => {
    setBadges(badgeScores ?? {});
  }, [badgeScores]);

  const onBlur = useCallback(() => {
    if (batchUpdater) {
      batchUpdater(badgeValues);
    }
  }, [badgeValues, batchUpdater]);

  function handleChange(value: Partial<ApplicationReviewWidgetMeta>) {
    setBadges((old) => ({ ...old, ...value }));
  }

  return (
    <div
      id={id}
      className={clsx(
        'overflow-hidden flex flex-col bg-white rounded-xl shadow-tw-md',
        className,
      )}
    >
      <Show when={badgeScores?.flags}>
        {(flags) => (
          <div
            className={clsx(
              'flex items-center px-4 py-2 space-x-2',
              flags[0].weight > 0
                ? 'text-success-extraLight bg-success-dark'
                : 'text-error-extraLight bg-error-main',
            )}
          >
            <HiFlag className="text-xl" />
            <p>{flags[0].title}</p>
          </div>
        )}
      </Show>

      <div
        className={clsx('flex-grow p-5', {
          'space-y-4': !!title,
        })}
      >
        <Show when={typeof title === 'string'} fallback={title}>
          <WidgetTitle>{title}</WidgetTitle>
        </Show>
        {children}
      </div>

      <Show
        when={!appReview?.summary?.isRiskWorksheetEnabled}
        fallback={
          footer && (
            <div className="flex items-center gap-4 p-4 border-t border-primary-extraLight">
              <div className="flex items-center flex-1">{footer}</div>
            </div>
          )
        }
      >
        <Switch>
          <Switch.Match when={isLoading}>
            {range(PLACEHOLDER_BADGE_COUNT).map((val) => (
              <div
                key={val}
                className="w-16 h-6 bg-gray-100 rounded animate-pulse"
              />
            ))}
          </Switch.Match>

          <Switch.Match when={merit !== undefined || credit !== undefined}>
            <div className="flex items-center gap-4 p-4 border-t border-primary-extraLight">
              <div className="flex items-center">
                <Show when={merit !== undefined}>
                  <span className="mr-1 text-sm">Merit:</span>
                  <BadgeInput
                    value={merit}
                    allowNegative
                    onBlur={onBlur}
                    decimalScale={0}
                    disabled={isDisabled}
                    prefix={merit && merit > 0 ? '+' : ''}
                    className={clsx({
                      'text-green-500 bg-green-50': merit && merit > 0,
                      'text-red-500 bg-red-50': merit && merit < 0,
                    })}
                    onChange={(e) =>
                      setBadges((old) => ({
                        ...old,
                        merit: parseFloat(e.target.value),
                      }))
                    }
                  />
                </Show>
              </div>
              <div className="flex items-center flex-1">{footer}</div>

              <Show when={credit !== undefined}>
                <CreditBar
                  onBlur={onBlur}
                  values={badgeValues}
                  onChange={handleChange}
                  isDisabled={isDisabled || !!isCreditDisabled}
                />
              </Show>
            </div>
          </Switch.Match>
        </Switch>
      </Show>
    </div>
  );
}

export default function Widget(props: WidgetProps) {
  return (
    <ErrorBoundary
      fallback={
        <div
          role="alert"
          className={clsx(
            'overflow-hidden flex flex-col items-center justify-center bg-white rounded-lg shadow-md p-4 min-h-[144px]',
            props.className,
          )}
        >
          <p className="mb-4 text-lg text-error-main">
            Oops!! Something went wrong
          </p>

          <Button variant="contained" onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      }
    >
      <WidgetRenderer {...props} />
    </ErrorBoundary>
  );
}
