import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

import { Show } from '@nirvana/ui-kit';
import { ParsedLossState, RecommendationInfoPanelType } from '@nirvana/api/uw';

import ReviewBar from 'src/components/review-bar';
import TelematicsStatus from 'src/components/telematics-status';
import PersistentSummary from 'src/components/persistent-summary';
import { Feature, useFeatureEnabled } from 'src/utils/feature-flags';
import { RecommendationsWidget } from 'src/components/recommendations';

import { LossSummaryTableRow } from './constants/loss-summary';
import ClaimsHistory from './components/claims-history';
import LargeLosses from './components/large-losses';
import LossAverages from './components/loss-averages';
import LossSummary from './components/loss-summary';
import { fetchLossSummary } from './queries';
import AiLossSummary from './components/ai-loss-summary';

export default function Losses() {
  const { appReviewId = '' } = useParams();
  const pibitState = useFeatureEnabled(Feature.SHOW_PIBIT_DATA);
  const aiLossSummaryFlag = useFeatureEnabled(Feature.AI_LOSS_SUMMARY, false);

  const { data } = useQuery(
    ['loss-summary', appReviewId],
    () => fetchLossSummary(appReviewId ?? ''),
    { refetchOnWindowFocus: false },
  );

  const documentList = useMemo(
    () => data?.parsedLossRunStatusInfo.documentList || [],
    [data],
  );

  const areAllFilesProcessed = useMemo(
    () => documentList.every((file) => file.parsedStatus === 'Processed'),
    [documentList],
  );

  const areAllLossIncurredMatched = useMemo(
    () =>
      data?.value?.every((item) =>
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (item as any).summary?.every(
          (row: LossSummaryTableRow) =>
            row.parsedLossState === ParsedLossState.Match,
        ),
      ) ?? false,
    [data],
  );

  return (
    <>
      <div className="grid grid-cols-1 gap-4">
        <TelematicsStatus />
        <PersistentSummary />
        <ReviewBar />
        <RecommendationsWidget panel={RecommendationInfoPanelType.Losses} />
      </div>
      <div className="grid grid-cols-1 gap-4 py-4">
        <Show when={aiLossSummaryFlag}>
          <AiLossSummary title="Auto Liability" />
          <AiLossSummary title="Auto Physical Damage" />
        </Show>
        <LossSummary />
        <LargeLosses />
        <Show
          when={pibitState && areAllFilesProcessed && areAllLossIncurredMatched}
        >
          <ClaimsHistory />
        </Show>
        <LossAverages />
      </div>
    </>
  );
}
