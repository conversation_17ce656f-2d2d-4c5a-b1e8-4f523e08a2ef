import { InputNumber, Label, Select, Tag } from '@nirvana/ui';
import { Widget } from 'src/components/widget';
import { lossData } from '../constants/loss-data';

export default function AiLossSummary({ title }: { title: string }) {
  return (
    <Widget title={title}>
      <section
        id="loss-calculator"
        className="grid grid-cols-3 gap-6 px-5 py-6 rounded-lg 2xl:gap-10 shadow-tw-sm bg-tw-navy-50"
      >
        <div className="flex flex-col gap-1">
          <p className="font-semibold">Loss Calculator</p>
          <p className="text-tw-gray-700">
            Modify these values to test different loss ratios
          </p>
        </div>

        <div className="flex flex-col gap-1">
          <Label>Pre Claim Deductible</Label>
          <Select>
            <Select.Trigger className="w-full">
              <Select.Value placeholder="Select Deductible" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="1000">$1,000</Select.Item>
              <Select.Item value="2500">$2,500</Select.Item>
              <Select.Item value="5000">$5,000</Select.Item>
              <Select.Item value="10000">$10,000</Select.Item>
            </Select.Content>
          </Select>
        </div>

        <div className="flex flex-col gap-1">
          <Label>Requested Premium Per Power Unit</Label>
          <InputNumber thousandSeparator prefix="$" />
        </div>
      </section>

      <section id="loss-table">
        {/* Table Header */}
        <div className="grid grid-cols-6 gap-4 py-3 text-xs font-semibold border-b bg-tw-gray-50 border-tw-gray-200">
          <div className="px-2">Policy Period</div>
          <div className="text-right"># of PUs</div>
          <div className="text-right"># of Claims</div>
          <div className="text-right">Gross Loss</div>
          <div className="text-right">Requested Premium</div>
          <div className="px-2 text-right">Loss Ratio</div>
        </div>

        {/* Table Body */}
        <div className="border-b divide-y divide-tw-gray-200">
          {lossData.map((row, index) => (
            <div
              key={index}
              className="grid grid-cols-6 gap-4 py-4 hover:bg-tw-gray-50"
            >
              <div className="px-2 text-sm font-medium text-tw-gray-900">
                {row.period}
              </div>
              <div className="text-sm text-right text-tw-gray-900">
                {row.pus}
              </div>
              <div className="text-sm text-right text-tw-gray-900">
                {row.claims}
              </div>
              <div className="text-sm font-medium text-right text-tw-gray-900">
                {row.grossLoss}
              </div>
              <div className="text-sm font-medium text-right text-tw-gray-900">
                {row.requestedPremium}
              </div>
              <div className="px-2 text-right">
                <Tag
                  color={
                    row.lossRatioType === 'danger'
                      ? 'red'
                      : row.lossRatioType === 'warning'
                        ? 'orange'
                        : 'green'
                  }
                  className="text-xs font-semibold rounded-full"
                >
                  {row.lossRatio}
                </Tag>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-6 gap-4 border-b divide-x">
          <div className="col-span-2 py-4 pl-3 text-xs font-semibold">
            Loss Ratio Aggregates
          </div>
          <div className="flex flex-col col-span-4 gap-2 px-3 py-4">
            <div className="flex justify-between">
              <p className="font-medium text-tw-gray-600">
                3 years (2021-2024)
              </p>
              <p className="text-xl font-medium text-right text-tw-green-700">
                59.0%
              </p>
            </div>

            <div className="flex justify-between">
              <p className="font-medium text-tw-gray-600">
                5 years (2020-2025)
              </p>
              <p className="text-xl font-medium text-right text-tw-green-700">
                53.1%
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-6 gap-4 divide-x">
          <div className="col-span-2 py-4 pl-3 text-xs font-semibold">
            Averages
          </div>
          <div className="flex flex-col col-span-2 gap-2 px-3 py-4">
            <div className="flex justify-between">
              <p className="font-medium text-tw-gray-600">Average claim size</p>
              <p className="font-medium text-right">$42,354.547</p>
            </div>

            <div className="flex justify-between">
              <p className="font-medium text-tw-gray-600">Average burn rate</p>
              <p className="font-medium text-right">$8,032.759</p>
            </div>
          </div>

          <div className="flex flex-col col-span-2 gap-2 px-3 py-4">
            <div className="flex justify-between">
              <p className="font-medium text-tw-gray-600">Loss frequency</p>
              <p className="font-medium text-right">1.0673871/unit</p>
            </div>

            <div className="flex justify-between">
              <p className="font-medium text-tw-gray-600" />
              <p className="font-medium text-right">1.0673871/mil mi</p>
            </div>
          </div>
        </div>
      </section>
    </Widget>
  );
}
