package cmd

import (
	"bufio"
	"context"
	"fmt"
	"os"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/spf13/cobra"

	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/distsem"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	"nirvanatech.com/nirvana/jobber/client"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/servers/telematicsv2"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
)

// retryCmd represents the retry command
var retryCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:   "retry <pipelineId or bulkJobRunId>",
		Short: "Retry a single pipeline or a bulk job run",
		Long: `If you attempt to retry an already retried pipeline id, 
you will get the same pipeline id as the first retry. 
Retrying bulkJobRunId will retry all failed jobs from the run and retry them as another bulk run`,
		Example: "retry my-pipeline-id-1 or retry BulkPipelinesJob::::321",
		Args:    cobra.ExactArgs(1),
	},
	func(cmd *cobra.Command, args []string, env AutogenEnv) error {
		ctx := cmd.Context()
		return retrySinglePipelineOrJobRun(ctx, cmd, env, args[0])
	},
)

// retrySinglePipelineOrJobRun handles single pipelineId or jobRunId input
func retrySinglePipelineOrJobRun(ctx context.Context, cmd *cobra.Command, env AutogenEnv, id string) error {
	if _, err := uuid.Parse(id); err == nil { // Try to parse as pipelineId
		return retryPipelineId(ctx, env, id)
	} else if parsedJobRunId, err := jtypes.JobRunIdFromString(id); err == nil { // Try to parse as job run id
		return retryJobRun(ctx, cmd, env, parsedJobRunId)
	} else {
		return errors.Newf("invalid pipeline/job run id %s", id)
	}
}

// retryPipelineId retries a single pipeline using the original API method
func retryPipelineId(ctx context.Context, env AutogenEnv, pipelineId string) error {
	resp, err := env.Telematicsv2.ReTrigger(ctx, &telematicsv2.PipelineId{Value: pipelineId})
	if err != nil {
		return errors.Wrapf(err, "failed to retry pipeline %s", pipelineId)
	}
	fmt.Printf("Successfully retried pipeline. New pipeline ID: %s\n", resp.Value)
	return nil
}

// retryJobRun handles retrying a job run (expects BulkPipelinesJob only)
func retryJobRun(ctx context.Context, cmd *cobra.Command, env AutogenEnv, jobRunId jtypes.JobRunId) error {
	dataInfraJobberGRPCAddr := env.Cfg.GetServices().GetDataInfraJobberAddr()
	fmt.Printf("connecting to data infra jobber at %s\n", dataInfraJobberGRPCAddr)
	dataInfraJobberGRPC, err := client.NewSimple(dataInfraJobberGRPCAddr)
	if err != nil {
		return errors.Wrapf(err, "failed to connect to data infra jobber")
	}

	// Get the job run
	jobRun, err := dataInfraJobberGRPC.GetJobRun(ctx, jobRunId)
	if err != nil {
		return errors.Wrapf(err, "failed to get job run %s", jobRunId.String())
	}

	fmt.Printf("Job run %s (status: %s)\n", jobRunId.String(), jobRun.Status.String())

	if !jobRun.HasTerminalStatus() {
		return errors.Newf("job run %s is not in terminal state", jobRunId.String())
	}

	// Only support BulkPipelinesJob for jobRunId retry
	if jobRun.RegistryKey != jobs.BulkPipelinesJob {
		return errors.Newf("jobRunId retry only supports BulkPipelinesJob, got %s", jobRun.RegistryKey)
	}

	// Get all spawned jobs and find failed ones
	spawnedJobs, err := job_utils.GetSpawnedJobs(ctx, dataInfraJobberGRPC, jobRunId)
	if err != nil {
		return errors.Wrapf(err, "failed to get spawned jobs for bulk pipeline %s", jobRunId.String())
	}

	var failedJobs []*jtypes.JobRun
	for _, spawnedJob := range spawnedJobs {
		if spawnedJob.HasTerminalStatus() && spawnedJob.Status != jtypes.JobRunStatusSucceeded {
			failedJobs = append(failedJobs, spawnedJob)
		}
	}

	if len(failedJobs) == 0 {
		fmt.Printf("No failed jobs found for %s\n", jobRunId.String())
		return nil
	}

	fmt.Printf("Found %d failed jobs to retry\n", len(failedJobs))

	// Get the original bulk message to copy bulk run attributes
	originalBulkMsg, err := jobs.UnmarshalBulkPipelinesMessage(jobRun.Message.Data, jobRun.Message.Vrsn)
	if err != nil {
		return errors.Wrapf(err, "failed to unmarshal original bulk message")
	}

	return createBulkRetryFromFailed(ctx, cmd, dataInfraJobberGRPC, failedJobs, originalBulkMsg)
}

// createBulkRetryFromFailed creates a new bulk job from failed jobs, preserving original bulk attributes
func createBulkRetryFromFailed(ctx context.Context, cmd *cobra.Command, jobberClient jtypes.Jobber, failedJobs []*jtypes.JobRun, originalBulkMsg *jobs.BulkPipelinesMessage) error {
	if dryRun {
		fmt.Printf("Dry run mode: would create bulk retry job with %d failed jobs\n", len(failedJobs))
		fmt.Printf("Original bulk attributes - Fanout: %d, Priority: %v, Author: %s\n",
			originalBulkMsg.Fanout, originalBulkMsg.Priority, originalBulkMsg.Author)
		return nil
	}

	// Create basic bulk retry job
	bulkMsg, err := createBulkRetryFromJobList(failedJobs)
	if err != nil {
		return err
	}

	// Apply original bulk attributes, preferring CLI flags when actually set by user
	if !cmd.Flag("parallel").Changed {
		bulkMsg.Fanout = originalBulkMsg.Fanout
	}
	if !cmd.Flag("priority").Changed {
		bulkMsg.Priority = originalBulkMsg.Priority
	}
	tags := append(originalBulkMsg.Tags, "retry")
	// Merge original tags with retry tags
	if cmd.Flag("tags").Changed {
		tags = append(tags, extraTags...)
	}
	bulkMsg.Tags = tags

	// Trigger the bulk retry job
	jobRunId, err := jobberClient.AddJobRun(ctx,
		jtypes.NewAddJobRunParams(
			jobs.BulkPipelinesJob,
			bulkMsg,
			jtypes.NewMetadata(jtypes.OneOff),
		),
	)
	if err != nil {
		return errors.Wrapf(err, "failed to trigger bulk retry job")
	}
	fmt.Printf("Successfully created bulk retry job: %s\n", jobRunId.String())
	return nil
}

// createBulkRetryFromJobList creates a bulk retry job from a list of job runs and returns the message
func createBulkRetryFromJobList(jobsToRetry []*jtypes.JobRun) (*jobs.BulkPipelinesMessage, error) {
	// Handle author validation - only needed for bulk operations
	retryAuthor := author
	if retryAuthor == "" {
		scanner := bufio.NewScanner(os.Stdin)
		fmt.Printf("Could not deduce author from environment, and no --author flag set,\n" +
			"please specify your identity (for ex. email): ")
		if !scanner.Scan() {
			return nil, errors.Newf("input exhausted, author not found")
		}
		retryAuthor = scanner.Text()
	}

	var millimanWorkflows []*jobs.RunMillimanScoringMessage
	var configurableWorkflows []*jobs.ConfigurableTelematicsWorkflowMessage

	for _, jobRun := range jobsToRetry {
		switch jobRun.RegistryKey {
		case jobs.RunMillimanScoringWorkflow:
			msg, err := jobs.UnmarshalRunTelematicsMessageFn(jobRun.Message.Data, jobRun.Message.Vrsn)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to unmarshal MillimanScoring message for job %s", jobRun.String())
			}
			// Generate new pipeline ID for retry but keep all other original params
			msg.PipelineId.String = uuid_utils.StableUUID(msg.PipelineId.String).String()
			msg.Tags = append(msg.Tags, jobs.JobTagSourceManual, "retry")
			millimanWorkflows = append(millimanWorkflows, msg)

		case jobs.ConfigurableTelematicsWorkflow:
			msg, err := jobs.UnmarshalConfigurableWorkflowMessageFn(jobRun.Message.Data, jobRun.Message.Vrsn)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to unmarshal Configurable message for job %s", jobRun.String())
			}
			// Generate new pipeline ID for retry but keep all other original params
			msg.PipelineId.String = uuid_utils.StableUUID(msg.PipelineId.String).String()
			msg.Tags = append(msg.Tags, jobs.JobTagSourceManual, "retry")
			configurableWorkflows = append(configurableWorkflows, msg)
		default:
			fmt.Printf("Warning: Unsupported job type %s for job %s, skipping\n",
				jobRun.RegistryKey, jobRun.String())
		}
	}

	totalWorkflows := len(millimanWorkflows) + len(configurableWorkflows)
	if totalWorkflows == 0 {
		return nil, errors.Newf("no workflows found to retry")
	}

	fmt.Printf("Total %d Grouped workflows: %d Milliman, %d Configurable\n",
		totalWorkflows, len(millimanWorkflows), len(configurableWorkflows))

	// Create basic bulk retry job message with default values
	var priority *distsem.Priority
	if bulkPriority != int(distsem.LowPriority) {
		p := distsem.Priority(bulkPriority)
		priority = &p
	}

	bulkMsg := &jobs.BulkPipelinesMessage{
		MillimanWorkflows:     millimanWorkflows,
		ConfigurableWorkflows: configurableWorkflows,
		Priority:              priority,
		Fanout:                fanout,
		Author:                retryAuthor,
	}

	return bulkMsg, nil
}

func init() {
	rootCmd.AddCommand(retryCmd)

	retryCmd.Flags().StringVar(&author, "author", author,
		"author of this retry run (defaults to NIRVANA_EMAIL env var, or preserves original for bulk jobs)")
	retryCmd.Flags().BoolVar(&dryRun, "dry-run", false,
		"only analyze what would be retried without actually creating retry jobs")
	retryCmd.Flags().IntVar(&fanout, "parallel", 1,
		"Number of jobs to run in parallel (0 = preserve original fanout for bulk jobs)")
	retryCmd.Flags().IntVar(&bulkPriority, "priority", bulkPriority,
		"Run this bulk retry job in a different priority (overrides original for bulk jobs)")
	retryCmd.Flags().StringSliceVar(&extraTags, "tags", nil,
		"additional tags to add to the retry bulk job")
}
