load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cmd",
    srcs = [
        "autogen.go",
        "cancel.go",
        "carfax_sub.go",
        "conn_info.go",
        "list.go",
        "retry.go",
        "retry_bulk.go",
        "root.go",
        "run.go",
        "status.go",
        "trigger_bulk.go",
    ],
    importpath = "nirvanatech.com/nirvana/cmd/telematicsv2/cmd",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/common-go/uuid_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/business_auto/application",
        "//nirvana/db-api/db_wrappers/fleet",
        "//nirvana/db-api/db_wrappers/fleet_telematics",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/prog_type_identifier",
        "//nirvana/distsem",
        "//nirvana/external_data_management/mvr",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/appfx/cobrafx",
        "//nirvana/jobber/client",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/quoting/appetite_checker",
        "//nirvana/rating/data_fetching/mvr_fetching",
        "//nirvana/rating/data_processing/vin_processing",
        "//nirvana/servers/telematicsv2",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "//nirvana/telematics/data_platform",
        "//nirvana/telematics/data_platform/workflows/config",
        "//nirvana/telematics/data_platform/workflows/jobs",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_spf13_cobra//:cobra",
        "@com_github_volatiletech_null_v8//:null",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_multierr//:multierr",
    ],
)
