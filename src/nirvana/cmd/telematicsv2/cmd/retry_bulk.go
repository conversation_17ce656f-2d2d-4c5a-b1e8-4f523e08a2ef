package cmd

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/spf13/cobra"

	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	"nirvanatech.com/nirvana/jobber/client"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/servers/telematicsv2"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
)

// retryBulkCmd represents the retry-bulk command
var retryBulkCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:     "retry-bulk <pipelineId>(s)...",
		Short:   "Retry multiple pipelines by creating a new bulk retry job",
		Long:    "Takes multiple pipelineIds, copies their messages with renewed pipeline IDs, and starts a new bulk run similar to trigger-bulk.",
		Example: "retry-bulk my-pipeline-id-1 my-pipeline-id-2 my-pipeline-id-3",
		Args:    cobra.MinimumNArgs(1),
	},
	func(cmd *cobra.Command, args []string, env AutogenEnv) error {
		ctx := cmd.Context()
		return retryMultiplePipelines(ctx, env, args)
	},
)

// retryMultiplePipelines handles multiple pipelineId inputs
func retryMultiplePipelines(ctx context.Context, env AutogenEnv, pipelineIds []string) error {
	dataInfraJobberGRPCAddr := env.Cfg.GetServices().GetDataInfraJobberAddr()
	fmt.Printf("connecting to data infra jobber at %s\n", dataInfraJobberGRPCAddr)
	dataInfraJobberGRPC, err := client.NewSimple(dataInfraJobberGRPCAddr)
	if err != nil {
		return errors.Wrapf(err, "failed to connect to data infra jobber")
	}

	var allJobsToRetry []*jtypes.JobRun
	for i, pipelineId := range pipelineIds {
		fmt.Printf("Processing pipeline %d/%d: %s\n", i+1, len(pipelineIds), pipelineId)

		// Get pipeline details to extract jobRunId
		response, err := env.Telematicsv2.GetPipelineDetails(ctx, &telematicsv2.PipelineDetailsRequest{
			PipelineId: pipelineId,
			Richness:   telematicsv2.PipelineDetailsRequest_OPTIMIZE_FOR_PERFORMANCE,
		})
		if err != nil {
			return errors.Wrapf(err, "failed to get pipeline details for %s", pipelineId)
		}

		if response.Job == nil || response.Job.JobrunId == nil {
			return errors.Newf("no job run ID found for pipeline %s", pipelineId)
		}

		jobRunId := jtypes.JobRunId{
			JobId: jtypes.JobId(response.Job.JobrunId.JobId),
			RunId: jtypes.RunId(response.Job.JobrunId.RunId),
		}

		// Get the job run with message payload from jobber
		jobRun, err := dataInfraJobberGRPC.GetJobRun(ctx, jobRunId)
		if err != nil {
			return errors.Wrapf(err, "failed to get job run for pipeline %s", pipelineId)
		}

		fmt.Printf("  %s (status: %s)\n", jobRunId.String(), jobRun.Status.String())

		if !jobRun.HasTerminalStatus() {
			fmt.Printf("  Skipping: pipeline is not in terminal state\n")
			continue
		}

		// For bulk retry, we include the pipeline regardless of success/failure
		// The goal is to re-run pipelines with renewed IDs
		fmt.Printf("  Adding pipeline to retry list\n")
		allJobsToRetry = append(allJobsToRetry, jobRun)
	}

	if len(allJobsToRetry) == 0 {
		fmt.Printf("No pipelines found that can be retried.\n")
		return nil
	}

	return createBulkRetryJobBulk(ctx, dataInfraJobberGRPC, allJobsToRetry)
}

// createBulkRetryJobBulk creates a new bulk job containing all the retry jobs with renewed pipeline IDs
func createBulkRetryJobBulk(ctx context.Context, jobberClient jtypes.Jobber, jobsToRetry []*jtypes.JobRun) error {
	if dryRun {
		fmt.Printf("Dry run mode: would create bulk retry job with %d pipelines\n", len(jobsToRetry))
		return nil
	}

	// Create basic bulk retry job with CLI flags
	bulkMsg, err := createBulkRetryFromJobList(jobsToRetry)
	if err != nil {
		return err
	}

	// Set tags for bulk retry job
	bulkMsg.Tags = append([]string{jobs.JobTagSourceManual, "retry"}, extraTags...)

	// Trigger the bulk retry job
	jobRunId, err := jobberClient.AddJobRun(ctx,
		jtypes.NewAddJobRunParams(
			jobs.BulkPipelinesJob,
			bulkMsg,
			jtypes.NewMetadata(jtypes.OneOff),
		),
	)
	if err != nil {
		return errors.Wrapf(err, "failed to trigger bulk retry job")
	}

	fmt.Printf("Successfully created bulk retry job: %s\n", jobRunId.String())
	return nil
}

func init() {
	rootCmd.AddCommand(retryBulkCmd)

	retryBulkCmd.Flags().StringVar(&author, "author", author,
		"author of this retry run (defaults to NIRVANA_EMAIL env var)")
	retryBulkCmd.Flags().BoolVar(&dryRun, "dry-run", false,
		"only analyze what would be retried without actually creating retry jobs")
	retryBulkCmd.Flags().IntVar(&fanout, "parallel", 1,
		"Number of jobs to run in parallel")
	retryBulkCmd.Flags().IntVar(&bulkPriority, "priority", bulkPriority,
		"Run this bulk retry job in a different priority")
	retryBulkCmd.Flags().StringSliceVar(&extraTags, "tags", nil,
		"additional tags to add to the retry bulk job")
}
