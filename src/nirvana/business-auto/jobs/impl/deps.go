package impl

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	business_auto_app "nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application"
	forms_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/pdfgen"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// Deps defines the dependencies for business auto jobs
type Deps struct {
	fx.In
	MetricsClient          statsd.Statter
	Config                 *config.Config
	Clock                  clock.Clock
	BusinessAutoAppWrapper business_auto_app.Wrapper
	PDFGenClient           pdfgen.PDFGenClient
	FormsWrapper           forms_wrapper.FormWrapper
	PricingClient          ptypes.PricingServer
	AgencyWrapper          agency.DataWrapper
	AuthWrapper            auth.DataWrapper
	PricingWrapper         quoting.PricingWrapper
}
