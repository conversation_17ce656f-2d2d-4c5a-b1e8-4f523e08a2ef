load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "impl",
    srcs = [
        "add_jobs.go",
        "deps.go",
        "fx.go",
        "generate_quote_pdf.go",
        "pdf_types.go",
        "pricing_job.go",
    ],
    importpath = "nirvanatech.com/nirvana/business-auto/jobs/impl",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/business-auto/enums",
        "//nirvana/business-auto/jobs",
        "//nirvana/business-auto/model",
        "//nirvana/business-auto/pricing",
        "//nirvana/common-go/log",
        "//nirvana/common-go/type_utils",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/application/quoting/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/business_auto/application",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-core/coverage",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/jobber/registry",
        "//nirvana/nonfleet/rating",
        "//nirvana/pdfgen",
        "//nirvana/policy/business_auto",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_multierr//:multierr",
    ],
)
