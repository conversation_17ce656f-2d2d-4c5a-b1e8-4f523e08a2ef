__pycache__/
*.py[cod]
*$py.class

# Environments
.env
.venv
venv/
env/

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
.pytest_cache/
pytestdebug.log

# Jupyter Notebook
.ipynb_checkpoints

# pytype
.pytype/

# IDEs
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln

# Git
.git

# Docker
Dockerfile
.dockerignore 