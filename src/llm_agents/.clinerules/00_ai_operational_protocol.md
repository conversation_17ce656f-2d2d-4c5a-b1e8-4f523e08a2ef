# AI Operational Protocol

This document outlines the core operational procedures and guidelines that AI assistants (like Cursor) MUST follow when working on the Claims Agent Service project.

## Core Workflows for AI Assistant (Cursor)

### 1. Task Initiation / Planning Phase
```mermaid
flowchart TD
    Start[Start Task] --> ReadRulesAndBank[Read ALL Project Rules (.cursor/rules/*.mdc) AND Verify+Read Memory Bank (docs/memory_bank/** recursively)]
    ReadRulesAndBank -- Memory Bank Missing? --> WarnUser[WARN USER: Memory Bank Missing/Empty!]
    WarnUser --> StopOrProceed{User Acknowledges/Instructs}
    StopOrProceed -- Proceed Without MB --> Understand
    StopOrProceed -- Stop --> EndTask[End Task]
    ReadRulesAndBank -- Memory Bank OK --> Understand[Understand Task & High-Level Context]
    Understand --> AssessFiles{Are specific source files critical for this task?}
    AssessFiles -->|Yes| CheckFileAccess{Do I have access to/knowledge of these specific files?}
    CheckFileAccess -->|No| RequestFiles[Request specific file content from User]
    RequestFiles --> UserProvidesFiles[User provides files]
    UserProvidesFiles --> PlanWithFullContext[Develop Plan with High Confidence (9.5+/10) based on specific files]
    CheckFileAccess -->|Yes| PlanWithFullContext
    AssessFiles -->|No| PlanGeneral[Develop Plan based on General Context]
    PlanWithFullContext --> UserApproval[Await User Go-Ahead]
    PlanGeneral --> UserApproval
```
**Key Principle for Planning**: At the start of any new task, or if context is stale: 
1.  I will re-read all active project rules (`.cursor/rules/`).
2.  I will attempt to recursively read all `.md` files within `docs/memory_bank/`.
3.  **CRITICAL CHECK**: If the `docs/memory_bank/` directory does not exist, or if it exists but contains no `.md` files, I **MUST immediately inform the user with a clear warning** (e.g., "**WARNING: Memory Bank directory `docs/memory_bank/` not found or is empty. Proceeding without essential project context may lead to errors or misunderstandings.**") and await user confirmation or instruction before proceeding further with the task planning.
4.  Assuming the Memory Bank is present, I will proceed to understand the task context.
5.  If the task requires modification or detailed understanding of specific source files not exhaustively covered by the Memory Bank or provided rules, I **MUST explicitly state which files I need to see or have context on.** My goal is to gather sufficient information to make informed decisions.
6.  I will aim for a **9.5/10 or higher confidence level** regarding my understanding that *my proposed plan will execute flawlessly and achieve the desired outcome without unintended negative side-effects, based *only* on information I have actively processed (read files, tool outputs, user messages)*.
7.  **If my confidence is below 9.5/10 due to missing information about relevant files or potential ambiguities, I MUST proactively request the specific file content or context I need from the user.** I will await this information before proceeding with any code edits or high-impact actions. I will not make assumptions about code I haven't seen.

Before executing any `edit_file` operations or other code modifications:
1. I MUST have explicitly assessed and stated my confidence (on a scale of 0-10) that the *proposed plan will execute flawlessly and achieve the desired outcome without unintended negative side-effects, based *only* on information I have actively processed (read files, tool outputs, user messages)*.
2. This confidence MUST be 9.5/10 or higher.
3. If confidence is not at this level primarily because I lack information about relevant files (e.g., I haven't read a file I intend to edit, or a file that might be affected), I MUST state which files I need to read/see and request this context from the user *before* proceeding with the edit. My priority is to obtain the necessary information to build a high-confidence, flawless plan.
FAILURE TO ADHERE TO THIS CONFIDENCE CHECK AND INFORMATION GATHERING PROTOCOL WILL MAKE THE HUMAN ANGRY!


### 2. Execution / Act Phase
```mermaid
flowchart TD
    StartAct[Begin Implementation] --> CheckContext[Consult relevant Project Rules & Memory Bank docs]
    CheckContext --> ProposeEdit[Propose Change (via edit_file)]
    ProposeEdit --> VerifyEdit{Verify Edit Application?}
    
    subgraph VerifyEditSubgraph [Verify Edit Application]
        direction LR
        VerifyEditStart --> ReadFile[Use read_file on target]
        ReadFile --> Compare{Content Matches Intent?}
    end

    VerifyEdit -- Yes --> VerifyTask[Verify with `task lint typecheck test`]
    VerifyEdit -- No --> HandleFail{Handle Edit Failure}
    
    subgraph HandleFailSubgraph [Handle Edit Failure]
        direction LR
        HandleFailStart --> AttemptReapply[Try reapply tool]
        AttemptReapply -- Success --> VerifyEditAgain{Re-Verify Edit}
        AttemptReapply -- Failure/NA --> RetryEdit[Retry edit_file (maybe improved)]
        RetryEdit --> VerifyEditAgain
        VerifyEditAgain --> CompareAgain{Content Matches Intent?}
    end

    HandleFail -- Edit Still Failed --> InformUser[Inform User of Persistent Failure]
    InformUser --> EndOrRetry[Await User Instruction]

    CompareAgain -- Yes --> VerifyTask
    CompareAgain -- No --> HandleFail # Loop back to failure handling if re-verify fails

    VerifyTask --> UpdateDocs[Suggest Updates to Memory Bank]
    UpdateDocs --> Loop{More Steps?}
    Loop -->|Yes| CheckContext
    Loop -->|No| FinalReview[Final Review & Summarize Changes]
```
**Execution Principles**: 
1.  Consult relevant rules and Memory Bank context.
2.  Propose code changes using `edit_file`.
3.  **Mandatory Edit Verification**: After every `edit_file` call, I **MUST** immediately use `read_file` to read back the modified section (or whole file if necessary) to verify that the changes were applied exactly as intended.
4.  **Failure Handling**: 
    *   If verification fails (the file content does not match the intended edit):
        *   First, attempt to use the `reapply` tool if appropriate.
        *   If `reapply` fails or is not applicable, attempt the `edit_file` operation again, potentially refining the instructions or context provided.
        *   **Re-verify** after `reapply` or the retry using `read_file`.
        *   If the edit *still* fails after retry/reapply and re-verification, I **MUST** inform the user clearly that the intended edit could not be applied correctly, showing the intended vs. actual state if possible, and await further instruction.
    *   Only proceed if edit verification succeeds.
5.  If edit verification succeeds, run integration checks (`task lint typecheck test`).
6.  Suggest Memory Bank updates as appropriate.
7.  Loop for more steps or finalize.

## AI Pair Programming & Interaction Guidelines (General)

These are general guidelines for effective collaboration. Specific Memory Bank content and project standards are detailed in other rules.

-   **Be Specific**: Provide clear, detailed instructions. Specify file paths, function/class names, and the exact changes or behavior desired.
-   **Provide Context**: Ensure relevant files are open or their content is provided. Use `tree .` output if discussing file structures. **If the AI indicates it needs to see specific files to proceed with high confidence (as per Planning Phase rules), the user MUST provide them.**
-   **Iterate in Small Steps**: Request changes incrementally and verify with `task fix:all typecheck test` after each step.
-   **Review Critically**: Always review suggested code changes thoroughly. The AI is an assistant; the developer is responsible for correctness and quality.
-   **Reapply/Rephrase**: If an edit isn't applied correctly, try the "Reapply" feature or rephrase the instruction with more clarity or constraints.
-   **State Constraints**: If certain files or parts of the codebase should NOT be touched, state this explicitly.

## Core Principle: Context Before Code

**REMEMBER**: My effectiveness as an AI assistant depends entirely on having sufficient context. This includes understanding the project goals, architecture, and standards (from Project Rules and the Memory Bank), **AND having access to the specific, relevant source code when proposing or making changes.** I will proactively inform the user if I require specific file content to proceed confidently with high-impact tasks like code generation or refactoring. Making assumptions about code I haven't seen is not permitted.