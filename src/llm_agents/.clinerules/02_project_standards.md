# General Python Development Standards

This document outlines general mandatory standards and guidelines for Python development within related projects.

## 1. Coding Standards & Best Practices (General Python)

-   **Pydantic V2**: Always use Pydantic v2 for data modeling and validation. Leverage its features for robust data handling.
-   **Type Hinting**: Comprehensive type hints are mandatory. All code should pass `mypy` checks.
-   **Async/Await**: When working in async codebases, use `async` and `await` correctly. Be mindful of blocking calls (use `asyncio.to_thread` if necessary for sync library calls).
-   **Design Patterns**: Employ appropriate design patterns where they improve clarity, flexibility, and maintainability.
-   **Logging**: Use `loguru` for logging. Add meaningful log messages for important operations, decisions, and errors.
-   **Error Handling**: Implement robust error handling. Define custom exceptions for specific error conditions where appropriate.
-   **Code Style**: Adhere to Black and Ruff for formatting and linting (`task format`, `task lint:fix`).
-   **File Sizes**: Strive to keep individual Python files focused and reasonably sized. If a file grows too large (e.g., >300-400 lines without extensive boilerplate/comments), consider if it can be broken down into smaller, more manageable modules.

## 2. Refactoring Guidelines (General)

-   **Deep Analysis First**: Before proposing or undertaking significant refactoring:
    -   Thoroughly analyze the existing codebase relevant to the refactor.
    -   Understand the current responsibilities, data flows, and interactions.
    -   Consult relevant project documentation (architecture, Memory Bank if applicable).
    -   Use `tree .` or similar tools if you suspect missing file context.
-   **Clear Goals**: Define clear objectives for the refactoring.
-   **Iterative Approach**: Break down large refactoring tasks into smaller, verifiable steps. After each step, run relevant checks (e.g., linting, type checking, tests).
-   **Preserve Functionality**: Ensure existing functionality is preserved unless the goal is explicitly to modify it.
-   **High Confidence**: Adhere strictly to the confidence requirements outlined in the AI Operational Protocol (`00_ai_operational_protocol.mdc`) before proceeding.
-   **Test Coverage**: Ensure that refactored code is well-covered by unit tests. Existing tests should pass, and new tests should be added for new or significantly modified logic.

## 3. Testing Practices (General Python)

-   **pytest**: All tests are written using `pytest`.
-   **Mocking (`pytest-mock`)**: Use `mocker` for mocking dependencies.
    -   **Patch Target**: Critical: Patch where the object is *looked up* (in the module under test), not where it's defined.
    -   **Async Mocks**: Use `mocker.AsyncMock` for async functions/methods. For `asynccontextmanager`, mock `__aenter__` (to return an `AsyncMock`) and `__aexit__` (as an `AsyncMock`).
    -   Avoid `mocker.spy` with already complex mocks if it causes issues; direct assertions are often better.
-   **Comprehensive Unit Tests**: Aim for high unit test coverage, especially for business logic.
-   **CI Checks**: All code must pass relevant CI checks (format, lint, typecheck, test) before merging.

## 4. Documentation & Knowledge Transfer (General)

-   **Update General Documentation**: After making any changes that affect architecture, component responsibilities, data flow, configuration, or core logic, update relevant general documentation (e.g., READMEs, architecture diagrams).
-   **Docstrings**: All public modules, classes, and functions **MUST** have clear, comprehensive docstrings following standard Python conventions (e.g., Google style). Ensure existing docstrings are updated if logic changes.
-   **Distill Knowledge**: The goal of documentation is to distill project knowledge effectively for both human developers and future LLM assistants.