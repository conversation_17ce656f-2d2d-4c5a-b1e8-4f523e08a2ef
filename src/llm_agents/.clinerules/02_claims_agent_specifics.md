# Claims Agent Service: Specific Standards & Context

This rule provides context specific to the Claims Agent Service project.

## 1. Project Overview & Domain (Claims Agent Specific)

-   **Purpose**: The Claims Agent Service is a Python FastAPI application that uses an LLM-powered agent to interact with external MCP (Model Context Protocol) servers. It primarily handles insurance claims and policy information, providing functionalities like coverage determination.
-   **Core Technology**: Python 3.11+, FastAPI, Pydantic v2, LangChain, Loguru, `uv` for package management.
-   **Key External Interactions**: MCP Servers (via `langchain-mcp-adapters` SSE), OpenAI API (or other LLM providers).
-   **Documentation**: Core project knowledge is maintained in the Memory Bank (`docs/memory_bank/`). Always refer to relevant Memory Bank documents (e.g., `systemPatterns.md`, `techContext.md`) and other documentation in `docs/` (`architecture.md`, `llm_contributing.md`, etc.) for detailed information.

## 2. Architectural Principles & Key Components (Claims Agent Specific)

*(Refer to `docs/memory_bank/01_architecture_and_design/systemPatterns.md` for detailed component descriptions)*

-   **Modularity**: The agent layer (`src/claims_agent/agents/`) is designed with modularity in mind. Prefer small, focused components over large, monolithic classes.
-   **Separation of Concerns**: Each component should have a single, well-defined responsibility.
-   **Dependency Injection**: The project uses `injector` for DI. Be mindful of how services and configurations are provided and used.
-   **Configuration**: Centralized in `src/claims_agent/config.py` (`Settings` Pydantic model), loaded from `.env`.

## 3. Specific Coding Points (Claims Agent)

*   Utilize helpers in `src/claims_agent/agents/agent_logging_utils.py` for agent-specific logs.
*   Refer to custom exceptions defined in agent components (e.g., `mcp_client_manager.py`).
*   Access configuration **only** via the `claims_agent.config.settings` object.
*   Use absolute imports within the `src/claims_agent` package.