# Memory Bank Guide

This document describes the project's Memory Bank, which serves as the persistent knowledge base for AI assistants working on the Claims Agent Service.

## Core Usage Instruction

**I (the AI assistant) must read all Markdown (`.md`) files recursively within the `docs/memory_bank/` directory and all its subdirectories when performing complex edits that I have low confidence in, or when asked to "consult the memory bank". This is not optional for complex edits, and is critical for establishing foundational context. I may optionally read any memory bank file that is useful for the task at hand.**

## Memory Bank Structure & Purpose

The Memory Bank is located in the `docs/memory_bank/` directory and is organized into subfolders containing Markdown files. These files collectively provide a comprehensive understanding of the project.

*(Refer to the actual `docs/memory_bank/` directory structure for the current organization. The following represents the intended core content areas):*


```mermaid
flowchart TD
    MBDir["docs/memory_bank/"]
    MBDir --> F0["00_foundational/"]
    MBDir --> F1["01_architecture_and_design/"]
    MBDir --> F2["02_development_practices/"]
    MBDir --> F3["03_operational_context/"]

    F0 --> PB["projectbrief.md"]
    F0 --> PC["productContext.md"]
    
    F1 --> SP["systemPatterns.md"]
    F1 --> TC["techContext.md"]
    
    F2 --> PSG["project_standards_and_guidelines.md"]

    F3 --> AC["activeContext.md"]
    F3 --> P["progress.md"]
```

### Core Content Files (Located within `docs/memory_bank/` subdirectories)
1.  **`00_foundational/projectbrief.md`**: Foundation document. Defines core project requirements and goals. Source of truth for project scope.
2.  **`00_foundational/productContext.md`**: Why this project exists, problems it solves, how it should work, user experience goals.
3.  **`01_architecture_and_design/systemPatterns.md`**: System architecture, key technical decisions, design patterns in use, component relationships, critical implementation paths.
4.  **`01_architecture_and_design/techContext.md`**: Technologies used, development setup, technical constraints, dependencies, tool usage patterns.
5.  **`02_development_practices/project_standards_and_guidelines.md`**: Detailed coding standards, refactoring guidelines, testing practices, documentation requirements, and AI pair programming tips specific to *this* project.
6.  **`03_operational_context/activeContext.md`**: **Most frequently updated.** Current work focus, recent system state, important patterns/preferences, key operational learnings & principles.
7.  **`03_operational_context/progress.md`**: What works, what's left to build, current status, known issues, evolution of project decisions.

### Additional Context Note
Other files in the main `docs/` directory (e.g., `architecture.md`, `llm_contributing.md`) provide supplementary information and should be consulted when referenced or when deeper detail is required. **The Memory Bank provides foundational and standardizing context, but it is NOT a substitute for reading specific source files when detailed understanding of their current implementation is needed for a task.**

## Documentation Updates (Memory Bank Maintenance)

The Memory Bank files, especially `docs/memory_bank/03_operational_context/activeContext.md` and `docs/memory_bank/03_operational_context/progress.md`, **MUST be kept up-to-date.** Updates should occur:
1.  After implementing significant changes or completing a sub-task (AI should proactively suggest these based on its operational protocol).
2.  When new project patterns, decisions, or learnings emerge.
3.  If the user explicitly asks me to "update memory bank" or "document X in the memory bank". In this case, I **MUST review ALL relevant files** within `docs/memory_bank/` and suggest specific updates.
4.  Before ending a session if significant progress or changes have occurred.
5.  **AI-Assisted Periodic Review**: The user may initiate an "AI-Assisted Periodic Review" (as defined in the AI Operational Protocol) to compare Memory Bank documents against source code and identify potential drift.

```mermaid
flowchart TD
    TriggerUpdate[Trigger Documentation Update]
    
    subgraph UpdateProcess
        ReviewAll[Review ALL `docs/memory_bank/` files for relevance]
        IdentifyChanges[Identify what needs to be added/modified]
        DraftUpdates[Draft updates, focusing on `activeContext.md` & `progress.md`]
        ProposeToUser[Propose updates to User for specific files]
        
        ReviewAll --> IdentifyChanges --> DraftUpdates --> ProposeToUser
    end
    
    TriggerUpdate --> UpdateProcess
```