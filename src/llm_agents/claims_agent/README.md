# Claims Agent

This package contains the C<PERSON>ms Agent with Arize Enterprise tracing integration.

## Quick Start

### 1. Environment Setup

Copy the environment template and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Required: OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Arize Enterprise Tracing
ARIZE_ENABLED=true
ARIZE_SPACE_ID=U3BhY2U6MjE1ODQ6dEtPVg==
ARIZE_API_KEY=ak-dae42a40-<your-complete-api-key>
ARIZE_PROJECT_NAME=ClaimsIQ

# Application Settings
ENVIRONMENT=development
MODEL_NAME=gpt-4o-mini
MODEL_TEMPERATURE=0.7
AGENT_VERBOSE=true

# Coverage Determination Caching
COVERAGE_DETERMINATION_CACHE_ENABLED=true
COVERAGE_DETERMINATION_TTL_SECONDS=604800  # 7 days default
```

### 2. Start Claims Agent

```bash
cd claims_agent
uvicorn claims_agent.main:app --reload --port 8001
```

### 3. Usage

The Claims Agent will automatically trace all LangChain operations to Arize Enterprise. View traces in your Arize Enterprise dashboard.

👉  Looking for the **asynchronous Temporal + Supabase workflow**?  See
[docs/async-workflow.md](docs/async-workflow.md).

## Configuration

See [instrumentation README](src/claims_agent/instrumentation/README.md) for detailed configuration options.

## Architecture

The Claims Agent uses a modular instrumentation architecture that supports:
- Arize Enterprise tracing with automatic LangChain instrumentation
- Extensible design for future monitoring backends (Datadog, LangSmith, etc.)
- Manual and automatic tracing capabilities 