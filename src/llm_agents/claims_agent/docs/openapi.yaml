openapi: 3.0.1
info:
  title: Claims Agent API
  description: API for the Claims Agent service
  version: 0.1.0
paths:
  /api/claims/{claim_id}/coverage/determination:
    get:
      tags:
        - claims
      summary: Get Coverage Determination
      description: >-
        Analyzes claim coverage based on policy details.


        Args:
            claim_id: The ID of the claim to analyze.
            service: The claims service for coverage determination (injected).
            formatted_mcp_auth: Formatted MCP Authorization header value (injected).
            as_of_date: Optional date to perform coverage determination as of.

        Returns:
            A CoverageDeterminationResponse object with verification results.
      operationId: get_coverage_determination_api_claims__claim_id__coverage_determination_get
      parameters:
        - name: claim_id
          in: path
          required: true
          schema:
            type: string
            title: Claim Id
        - name: as_of_date
          in: query
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
            title: As Of Date
        - name: force_refresh
          in: query
          required: false
          schema:
            type: boolean
            default: false
            title: Force Refresh
        - name: ttl_seconds
          in: query
          required: false
          schema:
            type: integer
            nullable: true
            title: Ttl Seconds
        - name: Clerk-Authorization
          in: header
          required: false
          schema:
            type: string
            nullable: true
            title: Clerk-Authorization
        - name: Authorization
          in: header
          required: false
          schema:
            type: string
            nullable: true
            title: Authorization
        - name: JSESSIONID
          in: header
          required: false
          schema:
            type: string
            nullable: true
            title: Jsessionid
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CoverageDeterminationResponse"
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HTTPValidationError"
  /api/v2/claims/{claim_id}/coverage/determination:
    get:
      tags:
        - claims
      summary: Get Coverage Determination with Storage
      description: >-
        Analyzes claim coverage and stores results in the database.

        This endpoint performs coverage determination and stores the results
        in the database for later feedback processing.

        Args:
            claim_id: The ID of the claim to analyze.
            service: The claims service for coverage determination (injected).
            formatted_mcp_auth: Formatted MCP Authorization header value (injected).
            as_of_date: Optional date to perform coverage determination as of.

        Returns:
            A CoverageRunWithNotes object with the stored results.
      operationId: get_coverage_determination_v2_v2_claims__claim_id__coverage_determination_get
      parameters:
        - name: claim_id
          in: path
          required: true
          schema:
            type: string
            title: Claim Id
        - name: as_of_date
          in: query
          required: false
          schema:
            type: string
            format: date-time
            nullable: true
            title: As Of Date
        - name: Clerk-Authorization
          in: header
          required: false
          schema:
            type: string
            nullable: true
            title: Clerk-Authorization
        - name: Authorization
          in: header
          required: false
          schema:
            type: string
            nullable: true
            title: Authorization
        - name: JSESSIONID
          in: header
          required: false
          schema:
            type: string
            nullable: true
            title: Jsessionid
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CoverageRunWithNotes"
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HTTPValidationError"
  /api/v2/claims/{claim_id}/coverage/determination/cached:
    get:
      tags:
        - claims
      summary: Get Cached Coverage Determinations
      description: >-
        Get the latest successful coverage determination runs from cache.

        This endpoint returns previously computed coverage determinations
        stored in the database, without performing new analysis.

        Args:
            claim_id: The ID of the claim to get cached results for.
            coverage_run_repository: The coverage run repository (injected).
            runs: Number of runs to fetch (default: 1, minimum: 1, maximum: 10).

        Returns:
            A list of CoverageRunWithNotes objects with the cached results.
      operationId: get_cached_coverage_determinations_v2_claims__claim_id__coverage_determination_cached_get
      parameters:
        - name: claim_id
          in: path
          required: true
          schema:
            type: string
            title: Claim Id
        - name: runs
          in: query
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
            maximum: 10
            title: Runs
      responses:
        "200":
          description: Successfully retrieved cached coverage determinations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CoverageRunWithNotes"
        "400":
          description: Invalid request parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  detail:
                    type: string
                    example: "Invalid claim ID format. Claim ID must be at least 3 characters long."
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HTTPValidationError"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  detail:
                    type: string
                    example: "An unexpected error occurred retrieving cached results: Database connection failed"
  /api/v2/claims/coverage/feedback:
    post:
      tags:
        - claims
      summary: Submit Coverage Feedback
      description: >-
        Submit feedback for coverage notes.

        This endpoint allows users to submit feedback for multiple coverage notes
        in a single request. The feedback is upserted (inserted or updated) for each note.

        Args:
            feedback_request: The feedback request containing feedback for notes.
            service: The claims service for feedback operations (injected).

        Returns:
            A FeedbackResponse with the updated note IDs.
      operationId: upsert_coverage_feedback_claims_coverage_feedback_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpsertFeedbackRequest"
        required: true
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FeedbackResponse"
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HTTPValidationError"
  /health:
    get:
      tags:
        - health
      summary: Health Check
      description: >-
        Perform a health check on the service.


        Returns:
            A HealthStatus object with detailed service status.
      operationId: health_check_health_get
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HealthStatus"
components:
  schemas:
    Citation:
      properties:
        excerpt:
          type: string
          title: Excerpt
          description:
            The relevant text excerpt from the policy document or claim
            notes
        filename:
          type: string
          nullable: true
          title: Filename
          description:
            "DEPRECATED: Use document.filename instead. The source document
            filename (e.g., 'policy.pdf', 'claim_notes.txt'). This field will
            be removed in a future version."
        page:
          type: string
          nullable: true
          title: Page
          description:
            "DEPRECATED: Use pages array instead. The PDF page number as
            string. This field will be removed in a future version."
        presigned_url:
          type: string
          nullable: true
          title: Presigned Url
          description:
            "DEPRECATED: Will be removed in a future version. Pre-signed
            URL to the source PDF document if applicable, otherwise null."
        pages:
          type: array
          items:
            type: integer
          nullable: true
          title: Pages
          description:
            The PDF page numbers (not form page numbers). For example,
            if content appears on the 17th page, use [17]. For a range like 5-7, use
            [5, 6, 7]. Can be null if not applicable/found.
        document:
          allOf:
            - $ref: "#/components/schemas/Document"
          nullable: true
          description: Document reference with identifiers for URL refresh, if applicable
        bounding_box:
          type: array
          items:
            type: number
          nullable: true
          title: Bounding Box
          description:
            Normalized [x1, y1, x2, y2] coordinates of the citation within
            the page if applicable, otherwise null.
      type: object
      required:
        - excerpt
      title: Citation
      description: Reference to policy documentation supporting a verification decision.
    CoverageDeterminationResponse:
      properties:
        verifications:
          items:
            $ref: "#/components/schemas/VerificationItem"
          type: array
          title: Verifications
          description: List of verification items
      type: object
      title: CoverageDeterminationResponse
      description: Response model for coverage determination API.
    CoverageNote:
      properties:
        note_id:
          type: string
          format: uuid
          title: Note Id
          description: Unique identifier for the coverage note
        original_content:
          $ref: "#/components/schemas/VerificationItem"
          description: Original AI-generated content
        modified_content:
          allOf:
            - $ref: "#/components/schemas/VerificationItem"
          nullable: true
          description: Modified content after user feedback
        updated_at:
          type: string
          format: date-time
          nullable: true
          title: Updated At
          description: When the note was last updated
        updated_by:
          type: string
          nullable: true
          title: Updated By
          description: Who updated the note
      type: object
      required:
        - note_id
        - original_content
      title: CoverageNote
      description: A coverage note with original and modified content.
    CoverageRunWithNotes:
      properties:
        created_at:
          type: string
          format: date-time
          title: Created At
          description: When the run was created
        created_by:
          type: string
          title: Created By
          description: Who created the run
        coverage_notes:
          type: array
          items:
            $ref: "#/components/schemas/CoverageNote"
          title: Coverage Notes
          description: Coverage notes for this run
          default: []
      type: object
      required:
        - created_at
        - created_by
      title: CoverageRunWithNotes
      description: A coverage run with its associated notes.
    Document:
      properties:
        policy_number:
          type: string
          title: Policy Number
          description: The policy number (e.g., 'NISTK0017619-23')
        document_id:
          type: string
          format: uuid
          title: Document Id
          description: The UUID of the document
        filename:
          type: string
          nullable: true
          title: Filename
          description: The source document filename
      type: object
      required:
        - policy_number
        - document_id
      title: Document
      description: Document reference with identifiers needed for URL refresh.
    FeedbackResponse:
      properties:
        updated_notes:
          type: array
          items:
            type: string
            format: uuid
          title: Updated Notes
          description: List of note IDs that were updated
      type: object
      required:
        - updated_notes
      title: FeedbackResponse
      description: Response after upserting feedback.
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: "#/components/schemas/ValidationError"
          type: array
          title: Detail
      type: object
      title: HTTPValidationError
    HealthStatus:
      properties:
        status:
          type: string
          enum:
            - healthy
            - unhealthy
          title: Status
        agent_status:
          type: string
          enum:
            - initialized
            - not_initialized
            - error
          title: Agent Status
        details:
          type: string
          nullable: true
          title: Details
      type: object
      required:
        - status
        - agent_status
      title: HealthStatus
      description: Model for detailed health status of the service.
    NoteFeedback:
      properties:
        note_id:
          type: string
          format: uuid
          title: Note Id
          description: ID of the note being updated
        modified_content:
          $ref: "#/components/schemas/VerificationItem"
          description: Modified content with user feedback
      type: object
      required:
        - note_id
        - modified_content
      title: NoteFeedback
      description: Feedback for a single coverage note.
    UpsertFeedbackRequest:
      properties:
        feedback:
          type: array
          items:
            $ref: "#/components/schemas/NoteFeedback"
          title: Feedback
          description: List of feedback for individual notes
      type: object
      required:
        - feedback
      title: UpsertFeedbackRequest
      description: Request to upsert feedback for multiple coverage notes.
    ValidationError:
      properties:
        loc:
          items:
            oneOf:
              - type: string
              - type: integer
          type: array
          title: Location
        msg:
          type: string
          title: Message
        type:
          type: string
          title: Error Type
      type: object
      required:
        - loc
        - msg
        - type
      title: ValidationError
    VerificationItem:
      properties:
        name:
          type: string
          title: Name
        assessment_score:
          type: number
          maximum: 1.0
          minimum: 0.0
          nullable: true
          title: Assessment Score
          description:
            "Assessment score (0.0-1.0) indicating verification status.
            0.0: Potential issue found. 1.0: Verified, no issue found. None: Not applicable."
        summary:
          type: string
          title: Summary
          description: AI-generated summary of the verification result.
        citation:
          allOf:
            - $ref: "#/components/schemas/Citation"
          nullable: true
          description: Citation information if available
        verification_type:
          type: string
          enum:
            - policy
            - claim
            - general
          title: Verification Type
          description: Type of verification performed
          default: general
        status:
          type: string
          enum:
            - Undefined
            - Accept
            - Reject
          title: Status
          description: Status of the verification item, needs to be set only by the user, otherwise undefined
          default: Undefined
      type: object
      required:
        - name
        - summary
      title: VerificationItem
      description: Single verification result with status and supporting information.
