# Asynchronous Coverage-Determination Workflow

This document explains how to run the **batch / background** version of the
Claims Agent that executes coverage-determination jobs via Temporal and stores
results in Supabase (PostgreSQL).

---
## Components

| Service | Purpose | Local Port / CLI Profile |
|---------|---------|--------------------------|
| Temporal Server | Workflow orchestration | `7233` (`temporal`, `temporal-admin`) |
| Claims-Agent **worker** | Executes workflows & activities | Docker profile `claims_agent_worker` |
| Supabase (Postgres + API) | Persistence layer | `54322`, Web UI `54323` |
| Redis | Tool caching for `mcp_servers` | `6379` |
| MCP servers | Provide dynamic LangChain tools | Docker profile `mcp_servers` |

All of the above can be started with Docker Compose.

```bash
# from src/llm_agents
docker compose --profile "*" up --build -d
```

*The `"*"` wildcard selects **all** profiles defined in the workspace-level
`docker-compose.yml`.*

---
## Environment variables

Create/extend `.env` in the `claims_agent` directory.  The **minimum** required
variables for the async path are:

```env
# ---- Temporal ----
TEMPORAL_ADDRESS=localhost:7233
TEMPORAL_NAMESPACE=default
TEMPORAL_WORKER_TASK_QUEUE=coverage-determination
# TEMPORAL_API_KEY=                           # only for Cloud
# TEMPORAL_USE_TLS=false                      # true if using mTLS

# ---- Supabase / Postgres ----
SUPABASE_DB_URL=postgresql://postgres:postgres@localhost:54322/postgres

# ---- LLM Provider ----
OPENROUTER_API_KEY=...    # or OPENAI_API_KEY

# ---- Observability (optional) ----
ARIZE_ENABLED=true
ARIZE_API_KEY=ak-…
ARIZE_SPACE_ID=U3BhY2U6MjE1ODQ6dEtPVg==
ARIZE_PROJECT_NAME=ClaimsIQ
```

> 📝  The full list of available settings lives in
> `claims_agent/src/claims_agent/config.py`.

---
## Running a job (CLI)

Once the stack is up:

```bash
# inside the worker container
docker compose exec claims_agent_worker \
  uv run --package claims_agent \
  python -m claims_agent.scripts.submit_background_job \
  coverage-determination <claim-id> <auth-token>
```

Track progress in the Temporal Web UI (http://localhost:8233) and inspect the
result row in Supabase (`legacy_coverage_determinations` table).

---
## Worker-only start (without Docker Compose)

If you prefer running services directly on the host:

```bash
# activate venv & install deps first
poe start-worker   # alias for `python -m claims_agent.background.worker`
```

The worker will connect to the `TEMPORAL_ADDRESS` and start polling the task
queue given by `TEMPORAL_WORKER_TASK_QUEUE`. 