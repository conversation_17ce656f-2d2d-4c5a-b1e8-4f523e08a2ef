# Environment variables for claims_agent
# See claims_agent/src/claims_agent/config.py for defaults and descriptions.
# Values here are examples or placeholders. Actual values should be set in a .env file.
#
# MIGRATION GUIDE:
# - For new deployments: Use OPENROUTER_API_KEY for unified model access
# - For existing deployments: OPENAI_API_KEY continues to work as fallback
# - The system automatically uses OpenRouter if available, falls back to OpenAI
# - Model validation ensures tools and structured output support

# Select provider: openrouter (default) or legacy_openai
LLM_PROVIDER=openrouter

ENVIRONMENT="development"
# HOST="0.0.0.0" # Typically set by uvicorn command in Docker/Poe
# PORT="8000"    # Typically set by uvicorn command in Docker/Poe
LOG_LEVEL="INFO"
UVICORN_WORKERS="4"

SENTRY_DSN=""

AGENT_VERBOSE=False

# OpenRouter (Primary Provider) - Recommended for unified model access
OPENROUTER_API_KEY="your_openrouter_api_key_here"
OPENROUTER_MODEL_NAME="google/gemini-2.5-pro-preview"
# Legacy OpenAI (Direct Provider) - used if LLM_PROVIDER=legacy_openai
OPENAI_API_KEY="your_openai_api_key_here_if_using_openai_models"
LEGACY_OPENAI_MODEL_NAME="o4-mini"

MODEL_TEMPERATURE=1.0

# In Docker Compose, mcp_servers is hostname, 8001 is its exposed port.
CLAIMS_SERVER_URL="http://localhost:8001/claims/sse"
POLICY_SERVER_URL="http://localhost:8001/policy/sse"

MCP_DEFAULT_TIMEOUT_SECONDS="30"
MCP_SSE_READ_TIMEOUT_SECONDS="90"
MCP_MAX_RETRIES="3"

# Arize Enterprise Tracing
ARIZE_ENABLED=true
ARIZE_SPACE_ID="U3BhY2U6MjE1ODQ6dEtPVg=="
ARIZE_API_KEY="ak-dae42a40-<YOUR-COMPLETE-API-KEY>"
ARIZE_PROJECT_NAME="ClaimsIQ"

# Port for the claims_agent service (if configurable, otherwise it's set by uvicorn command)
# APP_PORT=8000

# URL for connecting to Redis
# Example for local Docker setup: redis://:lebowski@localhost:6379/0
# Example for Docker Compose setup (service name 'redis'): redis://:lebowski@redis:6379/0
REDIS_OM_URL=redis://:lebowski@redis:6379/0


# Supabase configuration
# https://supabase.com/docs/guides/self-hosting/docker#accessing-postgres
SUPABASE_DB_URL='postgresql+psycopg://postgres.your-tenant-id:your-super-secret-and-long-postgres-password@localhost:5432/postgres'

# Temporal configuration
TEMPORAL_ADDRESS=localhost:7233
TEMPORAL_NAMESPACE=default
TEMPORAL_WORKER_TASK_QUEUE=default

DEFAULT_MCP_AUTHORIZATION=""

# Coverage Determination Caching
COVERAGE_DETERMINATION_CACHE_ENABLED=true
COVERAGE_DETERMINATION_TTL_SECONDS=604800  # 7 days default
