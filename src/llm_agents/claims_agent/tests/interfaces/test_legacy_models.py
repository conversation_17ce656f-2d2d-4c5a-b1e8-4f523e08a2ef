"""Tests for Pydantic model parsing in verification.py."""

import pytest

from pydantic import ValidationError

from claims_agent.interfaces.legacy_models import (
    Citation,
    CoverageDeterminationResponse,
)


# JSON payload provided by the user
JSON_PAYLOAD = """{
  "verifications": [
    {
      "name": "Policy Active Status on Date of Loss",
      "assessment_score": 1.0,
      "summary": "The policy NISTK0012502-24 was active on the date of loss 2025-01-30, with an effective period from 2024-05-30 to 2025-05-30.",
      "citation": {
        "excerpt": "Policy Number: NISTK0012502-24\\nPolicy Period\\nFrom: 05/30/2024\\nTo: 05/30/2025 at 12:01 AM Standard Time at your mailing address shown above",
        "pages": [2],
        "document": {
          "policy_number": "NISTK0012502-24",
          "document_id": "12345678-1234-5678-1234-567812345678",
          "filename": "NISTK0012502-24 Issued 05-29-2024.pdf"
        },
        "bounding_box": null
      },
      "verification_type": "policy"
    },
    {
      "name": "Named Insured Verification",
      "assessment_score": 1.0,
      "summary": "The named insured on policy NISTK0012502-24 is TRI STARS LOGISTICS INC, matching the claim's insured entity.",
      "citation": {
        "excerpt": "Named Insured: TRI STARS LOGISTICS INC",
        "pages": [2],
        "document": {
          "policy_number": "NISTK0012502-24",
          "document_id": "12345678-1234-5678-1234-567812345678",
          "filename": "NISTK0012502-24 Issued 05-29-2024.pdf"
        },
        "bounding_box": null
      },
      "verification_type": "policy"
    },
    {
      "name": "Coverage of Reported Incident/Loss Type",
      "assessment_score": 1.0,
      "summary": "The policy provides coverage for liability with a $1,000,000 limit and includes coverage for physical damage, collision, comprehensive, and specified causes of loss relevant to the reported collision incident.",
      "citation": {
        "excerpt": "Coverages | Covered Autos | Limit Or Deductible\\nLiability | 72 | $1,000,000\\nPhysical Damage | 64 | $11,116\\nCollision | | $2,500 Deductible\\nComprehensive | | $1,000 Deductible\\nSpecified Causes Of Loss | | Deductible For Each Covered Auto",
        "pages": [3, 4, 5],
        "document": {
          "policy_number": "NISTK0012502-24",
          "document_id": "12345678-1234-5678-1234-567812345678",
          "filename": "NISTK0012502-24 Issued 05-29-2024.pdf"
        },
        "bounding_box": null
      },
      "verification_type": "policy"
    },
    {
      "name": "Driver Coverage Verification",
      "assessment_score": 1.0,
      "summary": "The driver Sallah Abubakar was operating the vehicle with permission and is covered under the policy as a company driver or contractor.",
      "citation": {
        "excerpt": "Any \\\"employee\\\" of yours is an \\\"insured\\\" while using a covered \\\"auto\\\" you don't own, hire or borrow in your business or your personal affairs.",
        "pages": [50],
        "document": {
          "policy_number": "NISTK0012502-24",
          "document_id": "12345678-1234-5678-1234-567812345678",
          "filename": "NISTK0012502-24 Issued 05-29-2024.pdf"
        },
        "bounding_box": null
      },
      "verification_type": "policy"
    },
    {
      "name": "Vehicle Coverage Verification",
      "assessment_score": 0.0,
      "summary": "The vehicle involved (2019 Kenworth T680) was not found in the policy's vehicle schedule, indicating it is not covered under the policy.",
      "citation": {
        "excerpt": "Claim Reporters Relationship to Claim: <EMAIL> Potential Coverage issue! Vehicle(s) not found in Policy feed: 2019,KENWORTH,T680,9945",
        "pages": null,
        "document": null,
        "bounding_box": null
      },
      "verification_type": "claim"
    },
    {
      "name": "Towing and Storage Coverage",
      "assessment_score": null,
      "summary": "Information on towing and storage coverage is not available or not applicable for this claim.",
      "citation": null,
      "verification_type": "policy"
    },
    {
      "name": "Applicable Exclusions and Limits",
      "assessment_score": 1.0,
      "summary": "The policy includes standard exclusions such as terrorism, nuclear energy, infectious diseases, and others as detailed in endorsements. Limits include $1,000,000 liability and specified deductibles for physical damage coverages.",
      "citation": {
        "excerpt": "This insurance does not apply to \\\"Bodily injury\\\" or \\\"property damage\\\" caused by the explosion of explosives you make, sell or transport.\\n\\nThis Policy does not insure against or provide indemnity for fines, penalties, exemplary or punitive damages.\\n\\nExclusion – Infectious or Communicable Disease applies.\\n\\nLimit any one \\\"loss\\\", \\\"catastrophe\\\" or \\\"accident\\\": $1,000,000",
        "pages": [40, 41],
        "document": {
          "policy_number": "NISTK0012502-24",
          "document_id": "12345678-1234-5678-1234-567812345678",
          "filename": "NISTK0012502-24 Issued 05-29-2024.pdf"
        },        
        "bounding_box": null
      },
      "verification_type": "policy"
    },
    {
      "name": "Applicable Deductibles and Limits",
      "assessment_score": 1.0,
      "summary": "The policy specifies deductibles for physical damage coverages: $2,500 for collision, $1,000 for comprehensive, and limits of $11,116 for physical damage. Liability limit is $1,000,000.",
      "citation": {
        "excerpt": "Deductible For Each Covered Auto For Loss Caused By Theft Or Mischief Or Vandalism: $1,000\\nDeductible For All Perils For Each Covered Auto: $2,500\\nLimit Of Insurance $11,116 for Physical Damage\\nLiability Limit: $1,000,000",
        "pages": [4],
        "document": {
          "policy_number": "NISTK0012502-24",
          "document_id": "12345678-1234-5678-1234-567812345678",
          "filename": "NISTK0012502-24 Issued 05-29-2024.pdf"
        },
        "bounding_box": null
      },
      "verification_type": "policy"
    }
  ]
}"""


class TestVerificationParsing:
    """Tests parsing of verification-related Pydantic models."""

    def test_parse_json_page_field_variations(self) -> None:
        """Tests that the CoverageDeterminationResponse can be parsed from a JSON string.

        Specifically, this test ensures that 'page' fields in citations,
        which can be integers, strings (single, range, comma-separated), or lists
        in the JSON, are correctly coerced to a list[int] by the Pydantic model's validator.
        """
        parsed_data = None
        try:
            parsed_data = CoverageDeterminationResponse.model_validate_json(
                JSON_PAYLOAD
            )
        except ValidationError as e:
            # Using self.fail for detailed error message as regular assert doesn't format multiline well here.
            pytest.fail(
                f"Pydantic ValidationError occurred during parsing: {e}\n\nRaw JSON:\n{JSON_PAYLOAD}"
            )

        assert parsed_data is not None
        assert isinstance(parsed_data, CoverageDeterminationResponse)
        assert len(parsed_data.verifications) > 0, "No verifications were parsed."

        # Check specific items
        # Item 1: pages should be [2], page should be "2" (backwards compatibility)
        item1_citation = parsed_data.verifications[0].citation
        assert item1_citation is not None, "Citation for item 1 is None"
        assert item1_citation.pages == [2], (
            f"Item 1 pages expected [2], got {item1_citation.pages}"
        )
        assert item1_citation.page == "2", (
            f"Item 1 page expected '2', got {item1_citation.page}"
        )
        assert isinstance(item1_citation.pages, list) and all(
            isinstance(p, int) for p in item1_citation.pages
        ), (
            f"Item 1 pages expected list[int], got {type(item1_citation.pages)} with item types {[type(p) for p in item1_citation.pages]}"
        )
        assert isinstance(item1_citation.page, str), (
            f"Item 1 page expected str, got {type(item1_citation.page)}"
        )

        # Item 3: pages should be [3, 4, 5], page should be "3" (backwards compatibility)
        item3_citation = parsed_data.verifications[2].citation
        assert item3_citation is not None, "Citation for item 3 is None"
        assert item3_citation.pages == [3, 4, 5], (
            f"Item 3 pages expected [3, 4, 5], got {item3_citation.pages}"
        )
        assert item3_citation.page == "3", (
            f"Item 3 page expected '3', got {item3_citation.page}"
        )
        assert isinstance(item3_citation.pages, list) and all(
            isinstance(p, int) for p in item3_citation.pages
        ), (
            f"Item 3 pages expected list[int], got {type(item3_citation.pages)} with item types {[type(p) for p in item3_citation.pages]}"
        )
        assert isinstance(item3_citation.page, str), (
            f"Item 3 page expected str, got {type(item3_citation.page)}"
        )

        # Item 4: pages should be [50], page should be "50" (backwards compatibility)
        item4_citation = parsed_data.verifications[3].citation
        assert item4_citation is not None, "Citation for item 4 is None"
        assert item4_citation.pages == [50], (
            f"Item 4 pages expected [50], got {item4_citation.pages}"
        )
        assert item4_citation.page == "50", (
            f"Item 4 page expected '50', got {item4_citation.page}"
        )
        assert isinstance(item4_citation.pages, list) and all(
            isinstance(p, int) for p in item4_citation.pages
        ), (
            f"Item 4 pages expected list[int], got {type(item4_citation.pages)} with item types {[type(p) for p in item4_citation.pages]}"
        )
        assert isinstance(item4_citation.page, str), (
            f"Item 4 page expected str, got {type(item4_citation.page)}"
        )

        # Item 5: pages should be None, page should be None (backwards compatibility)
        item5_citation = parsed_data.verifications[4].citation
        # The citation object itself exists
        assert item5_citation is not None, "Citation for item 5 is None"
        assert item5_citation.pages is None, (
            f"Item 5 pages expected None, got {item5_citation.pages}"
        )
        assert item5_citation.page is None, (
            f"Item 5 page expected None, got {item5_citation.page}"
        )

        # Item 6: citation itself is None
        item6_citation = parsed_data.verifications[5].citation
        assert item6_citation is None, (
            f"Item 6 citation expected None, got {item6_citation}"
        )

    def test_backwards_compatibility_field_population(self) -> None:
        """Test that backwards compatibility fields are automatically populated from new fields."""
        # Test with new field structure
        citation_data = {
            "excerpt": "Test excerpt",
            "pages": [1, 2, 3],
            "document": {
                "policy_number": "TEST123",
                "document_id": "12345678-1234-5678-9abc-123456789def",
                "filename": "test.pdf",
            },
        }

        citation = Citation(**citation_data)

        # Verify new fields
        assert citation.pages == [1, 2, 3]
        assert citation.document is not None
        assert citation.document.filename == "test.pdf"

        # Verify backwards compatibility fields are populated
        assert citation.page == "1"  # First page as string
        assert citation.filename == "test.pdf"  # From document.filename
        assert citation.presigned_url is None  # Default value

    def test_backwards_compatibility_with_explicit_deprecated_fields(self) -> None:
        """Test that explicitly provided deprecated fields are preserved."""
        # Test with both new and deprecated fields explicitly provided
        citation_data = {
            "excerpt": "Test excerpt",
            "pages": [5, 6, 7],
            "page": "custom_page",  # Explicitly provided deprecated field
            "filename": "custom_filename.pdf",  # Explicitly provided deprecated field
            "document": {
                "policy_number": "TEST123",
                "document_id": "12345678-1234-5678-9abc-123456789def",
                "filename": "document_filename.pdf",
            },
        }

        citation = Citation(**citation_data)

        # Verify new fields
        assert citation.pages == [5, 6, 7]
        assert citation.document is not None
        assert citation.document.filename == "document_filename.pdf"

        # Verify explicitly provided deprecated fields are preserved
        assert citation.page == "custom_page"
        assert citation.filename == "custom_filename.pdf"
