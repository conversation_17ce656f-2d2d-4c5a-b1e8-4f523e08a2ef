"""Tests for the authentication dependency."""

import pytest
from unittest.mock import patch

from claims_agent.api.auth import get_formatted_mcp_authorization


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "clerk_auth, authorization, jsessionid, expected, test_id",
    [
        # Clerk-Authorization Precedence
        (
            "Bearer clerk-token",
            "Bearer auth-token",
            "session-id",
            "Bearer clerk-token",
            "clerk_takes_precedence",
        ),
        (
            "Bearer clerk-token",
            None,
            None,
            "Bearer clerk-token",
            "clerk_only",
        ),
        # Authorization Header Fallback
        (
            None,
            "Bearer auth-token",
            "session-id",
            "Bearer auth-token",
            "auth_takes_precedence_over_jsessionid",
        ),
        (
            None,
            "Bearer auth-token",
            None,
            "Bearer auth-token",
            "auth_only",
        ),
        # JSESSIONID Fallback
        (
            None,
            None,
            "session-id",
            "Bearer session-id",
            "jsessionid_only",
        ),
        # No valid authentication
        (None, None, None, None, "no_headers"),
        # Edge cases and invalid formats
        (
            "Bearer ",
            "Bearer auth-token",
            "session-id",
            "Bearer auth-token",
            "clerk_empty_token",
        ),
        (
            "Invalid clerk-token",
            "Bearer auth-token",
            "session-id",
            "Bearer auth-token",
            "clerk_invalid_scheme",
        ),
        (
            None,
            "Bearer ",
            "session-id",
            "Bearer session-id",
            "auth_empty_token",
        ),
        (
            None,
            "Invalid auth-token",
            "session-id",
            "Bearer session-id",
            "auth_invalid_scheme",
        ),
        (None, None, "  ", None, "jsessionid_empty"),
        (
            None,
            None,
            "  session-id-with-spaces  ",
            "Bearer session-id-with-spaces",
            "jsessionid_with_whitespace",
        ),
        # Case-insensitivity for Bearer scheme
        (
            "bearer lower-case-clerk-token",
            None,
            None,
            "Bearer lower-case-clerk-token",
            "clerk_bearer_case_insensitive",
        ),
        (
            None,
            "bearer lower-case-auth-token",
            None,
            "Bearer lower-case-auth-token",
            "auth_bearer_case_insensitive",
        ),
    ],
)
async def test_get_formatted_mcp_authorization(
    clerk_auth, authorization, jsessionid, expected, test_id
):
    """Test the formatting and precedence of authorization headers.

    Args:
        clerk_auth: The value for the 'Clerk-Authorization' header.
        authorization: The value for the 'Authorization' header.
        jsessionid: The value for the 'JSESSIONID' header.
        expected: The expected formatted authorization string.
        test_id: A readable identifier for the test case.
    """
    # For tests that expect None, disable the DEFAULT_MCP_AUTHORIZATION fallback
    if expected is None:
        with patch("claims_agent.api.auth.settings") as mock_settings:
            mock_settings.ENVIRONMENT = "development"
            mock_settings.DEFAULT_MCP_AUTHORIZATION = None
            result = await get_formatted_mcp_authorization(
                clerk_authorization=clerk_auth,
                authorization=authorization,
                jsessionid=jsessionid,
            )
    else:
        result = await get_formatted_mcp_authorization(
            clerk_authorization=clerk_auth,
            authorization=authorization,
            jsessionid=jsessionid,
        )
    assert result == expected
