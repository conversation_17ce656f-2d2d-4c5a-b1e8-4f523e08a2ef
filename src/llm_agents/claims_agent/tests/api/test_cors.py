"""Tests for CORS middleware configuration and origin validation."""

import pytest
from httpx import AsyncClient
from fastapi import status


class TestCORSConfiguration:
    """Tests for CORS origin validation."""

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "origin,expected_allowed",
        [
            # empty origin
            ("", False),
            # Allowed origins
            ("https://support.nirvanatech.com", True),
            ("https://support.local.nirvanatech.com", True),
            ("https://subdomain.support.nirvanatech.com", True),
            ("https://subdomain.support.local.nirvanatech.com", True),
            # Blocked origins
            ("https://evil.com", False),
            ("https://support.evil.com", False),
            ("https://safety.local.nirvanatech.com", False),
            ("https://quoting.local.nirvanatech.com", False),
            ("https://support.nirvanatech.evil.com", False),
            ("https://supportlocal.nirvanatech.com", False),  # missing dot
            ("https://support.localnirvanatech.com", False),  # missing dot
            ("ftp://support.local.nirvanatech.com", False),  # wrong protocol
        ],
    )
    async def test_cors_preflight_request(
        self, client: AsyncClient, origin: str, expected_allowed: bool
    ) -> None:
        """Test CORS preflight requests with various origins."""
        headers = {}
        if origin:
            headers["Origin"] = origin

        # Make preflight request
        response = await client.options(
            "/api/claims/TEST123/coverage/determination",
            headers={
                **headers,
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Authorization",
            },
        )

        if expected_allowed:
            # Should get 200 OK with CORS headers
            assert response.status_code == status.HTTP_200_OK
            assert "Access-Control-Allow-Origin" in response.headers
            assert response.headers["Access-Control-Allow-Origin"] == origin
            assert "Access-Control-Allow-Methods" in response.headers
            assert "Access-Control-Allow-Headers" in response.headers
        else:
            # Should either get 400 Bad Request or no CORS headers
            if response.status_code == status.HTTP_200_OK:
                # If FastAPI allows the request, it shouldn't have CORS headers
                assert "Access-Control-Allow-Origin" not in response.headers
            else:
                # Or it should be rejected
                assert response.status_code >= 400

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "origin,expected_allowed",
        [
            ("https://support.nirvanatech.com", True),
            ("https://support.local.nirvanatech.com", True),
            ("https://evil.com", False),
            ("https://safety.local.nirvanatech.com", False),
        ],
    )
    async def test_cors_actual_request(
        self, client: AsyncClient, origin: str, expected_allowed: bool
    ) -> None:
        """Test actual requests with CORS headers."""
        headers = {}
        if origin:
            headers["Origin"] = origin

        # Make actual request to health endpoint (no auth required)
        response = await client.get("/health", headers=headers)

        if expected_allowed and origin:
            # Should get CORS headers for allowed origins
            assert "Access-Control-Allow-Origin" in response.headers
            assert response.headers["Access-Control-Allow-Origin"] == origin
            assert "Access-Control-Allow-Credentials" in response.headers
            assert response.headers["Access-Control-Allow-Credentials"] == "true"
        elif origin:
            # Should not get CORS headers for blocked origins
            assert "Access-Control-Allow-Origin" not in response.headers
        # If no origin header, no CORS headers should be present (but request still works)

    @pytest.mark.asyncio
    async def test_cors_headers_configuration(self, client: AsyncClient) -> None:
        """Test that CORS middleware is configured with correct headers and methods."""
        response = await client.options(
            "/health",
            headers={
                "Origin": "https://support.local.nirvanatech.com",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Authorization,Clerk-Authorization",
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert (
            response.headers["Access-Control-Allow-Origin"]
            == "https://support.local.nirvanatech.com"
        )
        assert response.headers["Access-Control-Allow-Credentials"] == "true"

        # Check allowed methods
        allowed_methods = response.headers["Access-Control-Allow-Methods"]
        assert "GET" in allowed_methods
        assert "POST" in allowed_methods
        assert "PUT" in allowed_methods
        assert "DELETE" in allowed_methods
        assert "OPTIONS" in allowed_methods

        # Check allowed headers
        allowed_headers = response.headers["Access-Control-Allow-Headers"]
        assert "Authorization" in allowed_headers
        assert "Clerk-Authorization" in allowed_headers
        assert "Content-Type" in allowed_headers
        assert "Accept" in allowed_headers

    @pytest.mark.asyncio
    async def test_cors_edge_cases(self, client: AsyncClient) -> None:
        """Test edge cases for CORS origin validation."""
        # Test case sensitivity
        response = await client.options(
            "/health",
            headers={
                "Origin": "https://SUPPORT.LOCAL.NIRVANATECH.COM",
                "Access-Control-Request-Method": "GET",
            },
        )
        # Should be rejected (case-sensitive regex)
        assert "Access-Control-Allow-Origin" not in response.headers

        # Test with port numbers (should be rejected)
        response = await client.options(
            "/health",
            headers={
                "Origin": "https://support.local.nirvanatech.com:8080",
                "Access-Control-Request-Method": "GET",
            },
        )
        # Should be rejected (port not in regex)
        assert "Access-Control-Allow-Origin" not in response.headers

        # Test with path (should be rejected)
        response = await client.options(
            "/health",
            headers={
                "Origin": "https://support.local.nirvanatech.com/path",
                "Access-Control-Request-Method": "GET",
            },
        )
        # Should be rejected (path not in regex)
        assert "Access-Control-Allow-Origin" not in response.headers
