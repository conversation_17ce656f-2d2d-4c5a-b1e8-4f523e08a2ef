"""Test cases for HTTP endpoint caching functionality."""

import uuid
from unittest.mock import MagicMock, patch

import pytest
from fastapi import status
from httpx import AsyncClient

from claims_agent.api.auth import get_formatted_mcp_authorization
from claims_agent.config import Settings
from claims_agent.interfaces.repositories import (
    CoverageDeterminationRequestRepositoryProtocol,
)
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
    VerificationItem as LegacyVerificationItem,
    Citation as LegacyCitation,
    Document as LegacyDocument,
)


# settings fixture to disable caching
@pytest.fixture
def caching_disabled(mock_settings: Settings) -> Settings:
    """Returns a settings object with caching disabled."""
    mock_settings.COVERAGE_DETERMINATION_CACHE_ENABLED = False
    return mock_settings


class TestCoverageEndpointCaching:
    """Tests for coverage endpoint caching functionality."""

    @pytest.fixture
    def sample_coverage_response(self) -> LegacyCoverageDeterminationResponse:
        """Create a sample coverage determination response."""
        return LegacyCoverageDeterminationResponse(
            verifications=[
                LegacyVerificationItem(
                    name="Driver Age Verification",
                    assessment_score=1.0,
                    summary="Driver meets age requirements.",
                    citation=LegacyCitation(
                        excerpt="Driver must be at least 21 years old.",
                        document=LegacyDocument(
                            policy_number="POLICY-001",
                            document_id=uuid.UUID(
                                "12345678-1234-5678-1234-************"
                            ),
                            filename="policy_terms.pdf",
                        ),
                        pages=[5],
                    ),
                    verification_type="policy",
                )
            ]
        )

    @pytest.fixture
    def another_coverage_response(self) -> LegacyCoverageDeterminationResponse:
        """Create another sample coverage determination response."""
        return LegacyCoverageDeterminationResponse(
            verifications=[
                LegacyVerificationItem(
                    name="Vehicle Coverage Verification",
                    assessment_score=0.8,
                    summary="Vehicle meets coverage requirements.",
                    citation=LegacyCitation(
                        excerpt="Vehicle must be properly insured.",
                        document=LegacyDocument(
                            policy_number="POLICY-002",
                            document_id=uuid.UUID(
                                "*************-8765-4321-************"
                            ),
                            filename="vehicle_policy.pdf",
                        ),
                        pages=[3],
                    ),
                    verification_type="policy",
                )
            ]
        )

    @pytest.mark.asyncio
    async def test_cache_hit_returns_cached_response(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
        mock_settings: Settings,
    ) -> None:
        """Test that cache hit returns cached response without calling service."""
        claim_id = "NITMS25030200"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Pre-populate cache by making first request
        mock_claims_service.determine_coverage.return_value = sample_coverage_response
        response1 = await client.get(f"/api/claims/{claim_id}/coverage/determination")
        assert response1.status_code == status.HTTP_200_OK
        assert response1.headers["X-Cache-Status"] == "MISS"

        # Reset mock to ensure cache is used
        mock_claims_service.reset_mock()

        # Make second request - should hit cache
        response2 = await client.get(f"/api/claims/{claim_id}/coverage/determination")
        assert response2.status_code == status.HTTP_200_OK
        assert response2.headers["X-Cache-Status"] == "HIT"
        assert response2.headers["X-Cache-TTL"] == str(
            mock_settings.COVERAGE_DETERMINATION_TTL_SECONDS
        )

        # Verify service was not called again
        mock_claims_service.determine_coverage.assert_not_called()

        # Verify response is the same
        assert response1.json() == response2.json()

    @pytest.mark.asyncio
    async def test_cache_hit_with_custom_ttl(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test cache hit with custom TTL parameter."""
        claim_id = "NITMS25030201"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Pre-populate cache with custom TTL
        mock_claims_service.determine_coverage.return_value = sample_coverage_response
        response1 = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?ttl_seconds=7200"
        )
        assert response1.status_code == status.HTTP_200_OK
        assert response1.headers["X-Cache-Status"] == "MISS"
        assert response1.headers["X-Cache-TTL"] == "7200"

        # Reset mock and make second request
        mock_claims_service.reset_mock()
        response2 = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?ttl_seconds=7200"
        )
        assert response2.status_code == status.HTTP_200_OK
        assert response2.headers["X-Cache-Status"] == "HIT"
        assert response2.headers["X-Cache-TTL"] == "7200"

        mock_claims_service.determine_coverage.assert_not_called()

    @pytest.mark.asyncio
    async def test_cache_miss_calls_service(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that cache miss calls service and stores result."""
        claim_id = "NITMS25030202"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request - should be cache miss
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["X-Cache-Status"] == "MISS"

        # Verify service was called
        mock_claims_service.determine_coverage.assert_called_once()

        # Verify response matches service result
        assert response.json() == sample_coverage_response.model_dump(mode="json")

    @pytest.mark.asyncio
    async def test_force_refresh_skips_cache(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
        another_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that force_refresh bypasses cache and calls service."""
        claim_id = "NITMS25030203"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Pre-populate cache
        mock_claims_service.determine_coverage.return_value = sample_coverage_response
        response1 = await client.get(f"/api/claims/{claim_id}/coverage/determination")
        assert response1.status_code == status.HTTP_200_OK
        assert response1.headers["X-Cache-Status"] == "MISS"

        # Reset mock and change return value
        mock_claims_service.reset_mock()
        mock_claims_service.determine_coverage.return_value = another_coverage_response

        # Make request with force_refresh=true
        response2 = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?force_refresh=true"
        )
        assert response2.status_code == status.HTTP_200_OK
        assert response2.headers["X-Cache-Status"] == "BYPASS"

        # Verify service was called again
        mock_claims_service.determine_coverage.assert_called_once()

        # Verify response is different (new service result)
        assert response1.json() != response2.json()
        assert response2.json() == another_coverage_response.model_dump(mode="json")

    @pytest.mark.asyncio
    async def test_cache_miss_expired_record(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test cache miss when record exists but is expired."""
        claim_id = "NITMS25030204"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Pre-populate cache with TTL of 1 second
        mock_claims_service.determine_coverage.return_value = sample_coverage_response
        response1 = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?ttl_seconds=1"
        )
        assert response1.status_code == status.HTTP_200_OK
        assert response1.headers["X-Cache-Status"] == "MISS"

        # Wait for cache to expire (in real scenario, we'd wait 1 second)
        # For testing, we'll use a TTL of 0 to simulate expiration
        mock_claims_service.reset_mock()
        response2 = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?ttl_seconds=0"
        )
        assert response2.status_code == status.HTTP_200_OK
        assert response2.headers["X-Cache-Status"] == "MISS"

        # Verify service was called again
        mock_claims_service.determine_coverage.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_miss_no_successful_records(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test cache miss when no successful records exist."""
        claim_id = "NITMS25030205"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request - should be cache miss (no records exist)
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["X-Cache-Status"] == "MISS"

        # Verify service was called
        mock_claims_service.determine_coverage.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_lookup_failure_falls_back_to_service(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that cache lookup failure falls back to service call."""
        claim_id = "NITMS25030206"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Mock repository to raise exception on cache lookup
        with patch.object(
            coverage_determination_repo,
            "get_latest_successful",
            side_effect=Exception("Database error"),
        ):
            response = await client.get(
                f"/api/claims/{claim_id}/coverage/determination"
            )
            assert response.status_code == status.HTTP_200_OK
            assert response.headers["X-Cache-Status"] == "MISS"

            # Verify service was called (fallback)
            mock_claims_service.determine_coverage.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_disabled_via_config(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
        caching_disabled: Settings,
    ) -> None:
        """Test behavior when caching is disabled via configuration."""
        claim_id = "NITMS25030208"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request - should bypass cache
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["X-Cache-Status"] == "MISS"

        # Verify service was called
        mock_claims_service.determine_coverage.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_with_zero_ttl_disabled(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that zero TTL disables caching."""
        claim_id = "NITMS25030209"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request with TTL=0 (disables caching)
        response = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?ttl_seconds=0"
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["X-Cache-Status"] == "MISS"
        assert response.headers["X-Cache-TTL"] == "0"

        # Verify service was called
        mock_claims_service.determine_coverage.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_different_claim_ids_separate_entries(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
        another_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that different claim IDs have separate cache entries."""
        claim_id_1 = "NITMS25030210"
        claim_id_2 = "NITMS25030211"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to return different responses for different claims
        def mock_determine_coverage(**kwargs):
            if kwargs["claim_id"] == claim_id_1:
                return sample_coverage_response
            elif kwargs["claim_id"] == claim_id_2:
                return another_coverage_response
            else:
                raise ValueError(f"Unexpected claim_id: {kwargs['claim_id']}")

        mock_claims_service.determine_coverage.side_effect = mock_determine_coverage

        # Make requests for both claims
        response1 = await client.get(f"/api/claims/{claim_id_1}/coverage/determination")
        response2 = await client.get(f"/api/claims/{claim_id_2}/coverage/determination")

        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        assert response1.headers["X-Cache-Status"] == "MISS"
        assert response2.headers["X-Cache-Status"] == "MISS"

        # Verify different responses
        assert response1.json() != response2.json()
        assert response1.json() == sample_coverage_response.model_dump(mode="json")
        assert response2.json() == another_coverage_response.model_dump(mode="json")

        # Verify service was called twice
        assert mock_claims_service.determine_coverage.call_count == 2
