from __future__ import annotations

import base64
import json
from datetime import datetime, timezone
from uuid import UUID
from unittest.mock import Magic<PERSON>ock

import pytest
from fastapi import status
from httpx import AsyncClient

from claims_agent.models.feedback import CoverageRunWithNotes, FeedbackResponse


@pytest.fixture
def _sample_feedback_request() -> dict[str, object]:
    """Minimal UpsertFeedbackRequest JSON payload."""
    return {
        "feedback": [
            {
                "note_id": "123e4567-e89b-12d3-a456-************",
                "modified_content": {
                    "name": "Note",
                    "assessment_score": 1.0,
                    "summary": "Sample",
                    "citation": {
                        "excerpt": "Sample excerpt",
                        "document": {
                            "policy_number": "POL123",
                            "document_id": "12345678-1234-5678-1234-************",
                            "filename": "policy.pdf",
                        },
                        "pages": [1],
                    },
                    "verification_type": "policy",
                },
            }
        ]
    }


def _build_dummy_jwt(email: str) -> str:
    """Return an unsigned JWT containing only the **email** claim."""
    header = (
        base64.urlsafe_b64encode(b'{"alg":"none","typ":"JWT"}').rstrip(b"=").decode()
    )
    payload = (
        base64.urlsafe_b64encode(json.dumps({"primary_email_address": email}).encode())
        .rstrip(b"=")
        .decode()
    )
    return f"{header}.{payload}."


@pytest.mark.asyncio
async def test_created_by_from_jwt(
    client: AsyncClient,
    mock_claims_service: MagicMock,
):
    """The v2 coverage-determination endpoint should forward the e-mail in created_by."""
    email = "<EMAIL>"
    token = _build_dummy_jwt(email)

    # Prepare mocked service return so endpoint can serialize response
    mock_claims_service.determine_coverage_with_storage.return_value = (
        CoverageRunWithNotes(
            created_at=datetime.now(timezone.utc),
            created_by=email,
            coverage_notes=[],
        )
    )

    claim_id = "TEST123"
    response = await client.get(
        f"/api/v2/claims/{claim_id}/coverage/determination",
        headers={"Authorization": f"Bearer {token}"},
    )

    assert response.status_code == status.HTTP_200_OK

    # Validate service invocation
    mock_claims_service.determine_coverage_with_storage.assert_called_once()
    call_args = mock_claims_service.determine_coverage_with_storage.call_args
    assert call_args[1]["created_by"] == email


@pytest.mark.asyncio
async def test_updated_by_from_jwt(
    client: AsyncClient,
    mock_claims_service: MagicMock,
    _sample_feedback_request: dict[str, object],
):
    """The feedback endpoint should pass the user e-mail as updated_by."""
    email = "<EMAIL>"
    token = _build_dummy_jwt(email)

    mock_claims_service.submit_feedback.return_value = FeedbackResponse(
        updated_notes=[UUID("123e4567-e89b-12d3-a456-************")]
    )

    response = await client.post(
        "/api/v2/claims/coverage/feedback",
        json=_sample_feedback_request,
        headers={"Authorization": f"Bearer {token}"},
    )

    assert response.status_code == status.HTTP_200_OK

    mock_claims_service.submit_feedback.assert_called_once()
    call_args = mock_claims_service.submit_feedback.call_args
    assert call_args[1]["updated_by"] == email
