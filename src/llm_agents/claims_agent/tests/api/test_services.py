"""Tests for ClaimsService."""

import uuid
from datetime import datetime, timezone
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest
from pytest_mock import MockFixture

from claims_agent.api.services import ClaimsService, ClaimsServiceProtocol
from claims_agent.interfaces.agents import AgentProtocol
from claims_agent.interfaces.repositories import (
    CoverageRunRepositoryProtocol,
    CoverageNotesRepositoryProtocol,
)
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse,
    VerificationItem,
    Citation,
)
from claims_agent.models.feedback import (
    CoverageRunWithNotes,
    CoverageNote,
    NoteFeedback,
    FeedbackResponse,
)
from claims_agent.db.orm.generated import CoverageNotes


@pytest.fixture
def mock_agent(mocker: MockFixture) -> AgentProtocol:
    """Create a mock agent for testing."""
    return mocker.Mock(spec=AgentProtocol)


@pytest.fixture
def mock_coverage_run_repository(mocker: MockFixture) -> CoverageRunRepositoryProtocol:
    """Create a mock coverage run repository for testing."""
    return mocker.Mock(spec=CoverageRunRepositoryProtocol)


@pytest.fixture
def mock_coverage_notes_repository(
    mocker: MockFixture,
) -> CoverageNotesRepositoryProtocol:
    """Create a mock coverage notes repository for testing."""
    return mocker.Mock(spec=CoverageNotesRepositoryProtocol)


@pytest.fixture
def claims_service(
    mock_agent: AgentProtocol,
    mock_coverage_run_repository: CoverageRunRepositoryProtocol,
    mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
) -> ClaimsService:
    """Create a ClaimsService instance for testing."""
    return ClaimsService(
        agent=mock_agent,
        coverage_run_repository=mock_coverage_run_repository,
        coverage_notes_repository=mock_coverage_notes_repository,
        authorization="Bearer test-token",
    )


@pytest.fixture
def sample_coverage_determination_response() -> CoverageDeterminationResponse:
    """Create a sample coverage determination response."""
    return CoverageDeterminationResponse(
        verifications=[
            VerificationItem(
                name="Policy Coverage Check",
                summary="Policy covers the incident",
                assessment_score=0.8,
                citation=Citation(excerpt="Policy document excerpt"),
                verification_type="policy",
            ),
            VerificationItem(
                name="Claim Validity Check",
                summary="Claim details are valid",
                assessment_score=0.9,
                citation=Citation(excerpt="Claim validation excerpt"),
                verification_type="claim",
            ),
        ]
    )


@pytest.fixture
def sample_coverage_run_with_notes() -> CoverageRunWithNotes:
    """Create a sample coverage run with notes."""
    return CoverageRunWithNotes(
        created_at=datetime.now(timezone.utc),
        created_by="system",
        coverage_notes=[
            CoverageNote(
                note_id=uuid.uuid4(),
                original_content=VerificationItem(
                    name="Policy Coverage Check",
                    summary="Policy covers the incident",
                    assessment_score=0.8,
                    citation=Citation(excerpt="Policy document excerpt"),
                    verification_type="policy",
                ),
                modified_content=None,
                updated_at=None,
                updated_by=None,
            ),
            CoverageNote(
                note_id=uuid.uuid4(),
                original_content=VerificationItem(
                    name="Claim Validity Check",
                    summary="Claim details are valid",
                    assessment_score=0.9,
                    citation=Citation(excerpt="Claim validation excerpt"),
                    verification_type="claim",
                ),
                modified_content=None,
                updated_at=None,
                updated_by=None,
            ),
        ],
    )


class TestClaimsService:
    """Test suite for ClaimsService."""

    def test_initialization(self, claims_service: ClaimsService) -> None:
        """Test that ClaimsService initializes correctly."""
        assert claims_service.agent is not None
        assert claims_service.coverage_run_repository is not None
        assert claims_service.coverage_notes_repository is not None
        assert claims_service.authorization == "Bearer test-token"

    def test_implements_protocol(self, claims_service: ClaimsService) -> None:
        """Test that ClaimsService implements ClaimsServiceProtocol."""
        assert isinstance(claims_service, ClaimsServiceProtocol)

    @pytest.mark.asyncio
    async def test_health_check_healthy(self, claims_service: ClaimsService) -> None:
        """Test health check when service is healthy."""
        is_healthy, details = await claims_service.health_check()

        assert is_healthy is True
        assert details == "Service is healthy"

    @pytest.mark.asyncio
    async def test_health_check_agent_not_available(
        self,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test health check when agent is not available."""
        service = ClaimsService(
            agent=None,  # type: ignore
            coverage_run_repository=mock_coverage_run_repository,
            coverage_notes_repository=mock_coverage_notes_repository,
        )

        is_healthy, details = await service.health_check()

        assert is_healthy is False
        assert details == "Agent is not available"

    @pytest.mark.asyncio
    async def test_health_check_agent_missing_methods(
        self,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test health check when agent is missing required methods."""
        # Create a mock agent without the required methods
        incomplete_agent = Mock()
        # Explicitly remove the determine_coverage method if it exists
        if hasattr(incomplete_agent, "determine_coverage"):
            delattr(incomplete_agent, "determine_coverage")

        service = ClaimsService(
            agent=incomplete_agent,  # type: ignore
            coverage_run_repository=mock_coverage_run_repository,
            coverage_notes_repository=mock_coverage_notes_repository,
        )

        is_healthy, details = await service.health_check()

        assert is_healthy is False
        assert details == "Agent is missing required methods"

    @pytest.mark.asyncio
    async def test_health_check_coverage_run_repository_not_available(
        self,
        mock_agent: AgentProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test health check when coverage run repository is not available."""
        service = ClaimsService(
            agent=mock_agent,
            coverage_run_repository=None,  # type: ignore
            coverage_notes_repository=mock_coverage_notes_repository,
        )

        is_healthy, details = await service.health_check()

        assert is_healthy is False
        assert details == "Coverage run repository is not available"

    @pytest.mark.asyncio
    async def test_health_check_coverage_notes_repository_not_available(
        self,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
    ) -> None:
        """Test health check when coverage notes repository is not available."""
        service = ClaimsService(
            agent=mock_agent,
            coverage_run_repository=mock_coverage_run_repository,
            coverage_notes_repository=None,  # type: ignore
        )

        is_healthy, details = await service.health_check()

        assert is_healthy is False
        assert details == "Coverage notes repository is not available"

    @pytest.mark.asyncio
    async def test_health_check_exception_handling(
        self,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test health check exception handling."""

        # Create a custom class that raises an exception on hasattr
        class ProblematicAgent:
            def __getattribute__(self, name):
                if name == "determine_coverage":
                    raise Exception("Test error")
                return super().__getattribute__(name)

        problem_agent = ProblematicAgent()

        service = ClaimsService(
            agent=problem_agent,  # type: ignore
            coverage_run_repository=mock_coverage_run_repository,
            coverage_notes_repository=mock_coverage_notes_repository,
        )

        is_healthy, details = await service.health_check()

        assert is_healthy is False
        assert "Health check failed: Test error" in details

    @pytest.mark.asyncio
    async def test_determine_coverage_success(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        sample_coverage_determination_response: CoverageDeterminationResponse,
    ) -> None:
        """Test successful coverage determination."""
        # Set up the mock agent
        mock_agent.determine_coverage = AsyncMock(
            return_value=sample_coverage_determination_response
        )

        claim_id = "test_claim_123"
        authorization = "Bearer test-token"
        as_of_date = datetime.now(timezone.utc)

        result = await claims_service.determine_coverage(
            claim_id=claim_id,
            authorization=authorization,
            as_of_date=as_of_date,
        )

        assert result == sample_coverage_determination_response
        mock_agent.determine_coverage.assert_called_once_with(
            claim_id=claim_id,
            authorization=authorization,
            as_of_date=as_of_date,
            refined_notes=None,
        )

    @pytest.mark.asyncio
    async def test_determine_coverage_with_defaults(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        sample_coverage_determination_response: CoverageDeterminationResponse,
    ) -> None:
        """Test coverage determination with default parameters."""
        # Set up the mock agent
        mock_agent.determine_coverage = AsyncMock(
            return_value=sample_coverage_determination_response
        )

        claim_id = "test_claim_123"

        result = await claims_service.determine_coverage(claim_id=claim_id)

        assert result == sample_coverage_determination_response
        mock_agent.determine_coverage.assert_called_once_with(
            claim_id=claim_id,
            authorization=None,
            as_of_date=None,
            refined_notes=None,
        )

    @pytest.mark.asyncio
    async def test_determine_coverage_with_storage_success(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
        sample_coverage_determination_response: CoverageDeterminationResponse,
        sample_coverage_run_with_notes: CoverageRunWithNotes,
    ) -> None:
        """Test successful coverage determination with storage."""
        # Set up mocks
        run_id = uuid.uuid4()
        mock_coverage_run_repository.create_run = AsyncMock(return_value=run_id)
        mock_coverage_run_repository.set_in_progress = AsyncMock()
        mock_coverage_run_repository.set_succeeded = AsyncMock()
        mock_coverage_run_repository.get_run_with_notes = AsyncMock(
            return_value=sample_coverage_run_with_notes
        )

        mock_agent.determine_coverage = AsyncMock(
            return_value=sample_coverage_determination_response
        )

        note_ids = [uuid.uuid4(), uuid.uuid4()]
        mock_coverage_notes_repository.create_notes_for_run = AsyncMock(
            return_value=note_ids
        )
        mock_coverage_notes_repository.get_recent_refined_coverage_notes = AsyncMock(
            return_value=None
        )

        claim_id = "test_claim_123"
        created_by = "<EMAIL>"
        authorization = "Bearer test-token"
        as_of_date = datetime.now(timezone.utc)
        trace_id = "trace_123"

        result = await claims_service.determine_coverage_with_storage(
            claim_id=claim_id,
            created_by=created_by,
            authorization=authorization,
            as_of_date=as_of_date,
            trace_id=trace_id,
        )

        assert result == sample_coverage_run_with_notes

        # Verify the sequence of calls
        mock_coverage_run_repository.create_run.assert_called_once_with(
            claim_id=claim_id,
            created_by=created_by,
            trace_id=trace_id,
        )
        mock_coverage_run_repository.set_in_progress.assert_called_once_with(run_id)
        mock_agent.determine_coverage.assert_called_once_with(
            claim_id=claim_id,
            authorization=authorization,
            as_of_date=as_of_date,
            refined_notes=None,
        )
        mock_coverage_notes_repository.create_notes_for_run.assert_called_once_with(
            run_id=run_id,
            claim_id=claim_id,
            verification_items=sample_coverage_determination_response.verifications,
        )
        mock_coverage_run_repository.set_succeeded.assert_called_once_with(run_id)
        mock_coverage_run_repository.get_run_with_notes.assert_called_once_with(run_id)

    @pytest.mark.asyncio
    async def test_determine_coverage_with_storage_defaults(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
        sample_coverage_determination_response: CoverageDeterminationResponse,
        sample_coverage_run_with_notes: CoverageRunWithNotes,
    ) -> None:
        """Test coverage determination with storage using default parameters."""
        # Set up mocks
        run_id = uuid.uuid4()
        mock_coverage_run_repository.create_run = AsyncMock(return_value=run_id)
        mock_coverage_run_repository.set_in_progress = AsyncMock()
        mock_coverage_run_repository.set_succeeded = AsyncMock()
        mock_coverage_run_repository.get_run_with_notes = AsyncMock(
            return_value=sample_coverage_run_with_notes
        )

        mock_agent.determine_coverage = AsyncMock(
            return_value=sample_coverage_determination_response
        )

        note_ids = [uuid.uuid4(), uuid.uuid4()]
        mock_coverage_notes_repository.create_notes_for_run = AsyncMock(
            return_value=note_ids
        )
        mock_coverage_notes_repository.get_recent_refined_coverage_notes = AsyncMock(
            return_value=None
        )

        claim_id = "test_claim_123"
        created_by = "<EMAIL>"

        result = await claims_service.determine_coverage_with_storage(
            claim_id=claim_id,
            created_by=created_by,
        )

        assert result == sample_coverage_run_with_notes

        # Verify the agent was called with None for optional parameters
        mock_agent.determine_coverage.assert_called_once_with(
            claim_id=claim_id,
            authorization=None,
            as_of_date=None,
            refined_notes=None,
        )

    @pytest.mark.asyncio
    async def test_determine_coverage_with_storage_agent_failure(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test coverage determination with storage when agent fails."""
        # Set up mocks
        run_id = uuid.uuid4()
        mock_coverage_run_repository.create_run = AsyncMock(return_value=run_id)
        mock_coverage_run_repository.set_in_progress = AsyncMock()
        mock_coverage_run_repository.set_failed = AsyncMock()

        # Make the agent fail
        mock_agent.determine_coverage = AsyncMock(side_effect=Exception("Agent failed"))

        claim_id = "test_claim_123"
        created_by = "<EMAIL>"

        with pytest.raises(Exception, match="Agent failed"):
            await claims_service.determine_coverage_with_storage(
                claim_id=claim_id,
                created_by=created_by,
            )

        # Verify the run was marked as failed
        mock_coverage_run_repository.set_failed.assert_called_once_with(run_id)

    @pytest.mark.asyncio
    async def test_determine_coverage_with_storage_notes_creation_failure(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
        sample_coverage_determination_response: CoverageDeterminationResponse,
    ) -> None:
        """Test coverage determination with storage when notes creation fails."""
        # Set up mocks
        run_id = uuid.uuid4()
        mock_coverage_run_repository.create_run = AsyncMock(return_value=run_id)
        mock_coverage_run_repository.set_in_progress = AsyncMock()
        mock_coverage_run_repository.set_failed = AsyncMock()

        mock_agent.determine_coverage = AsyncMock(
            return_value=sample_coverage_determination_response
        )

        # Make notes creation fail
        mock_coverage_notes_repository.create_notes_for_run = AsyncMock(
            side_effect=Exception("Notes creation failed")
        )

        claim_id = "test_claim_123"
        created_by = "<EMAIL>"

        with pytest.raises(Exception, match="Notes creation failed"):
            await claims_service.determine_coverage_with_storage(
                claim_id=claim_id,
                created_by=created_by,
            )

        # Verify the run was marked as failed
        mock_coverage_run_repository.set_failed.assert_called_once_with(run_id)

    @pytest.mark.asyncio
    async def test_determine_coverage_with_storage_get_run_failure(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
        sample_coverage_determination_response: CoverageDeterminationResponse,
    ) -> None:
        """Test coverage determination with storage when getting run fails."""
        # Set up mocks
        run_id = uuid.uuid4()
        mock_coverage_run_repository.create_run = AsyncMock(return_value=run_id)
        mock_coverage_run_repository.set_in_progress = AsyncMock()
        mock_coverage_run_repository.set_succeeded = AsyncMock()
        mock_coverage_run_repository.get_run_with_notes = AsyncMock(return_value=None)

        mock_agent.determine_coverage = AsyncMock(
            return_value=sample_coverage_determination_response
        )

        note_ids = [uuid.uuid4(), uuid.uuid4()]
        mock_coverage_notes_repository.create_notes_for_run = AsyncMock(
            return_value=note_ids
        )

        claim_id = "test_claim_123"
        created_by = "<EMAIL>"

        with pytest.raises(
            RuntimeError,
            match=f"Failed to retrieve coverage run {run_id} after creation",
        ):
            await claims_service.determine_coverage_with_storage(
                claim_id=claim_id,
                created_by=created_by,
            )

    @pytest.mark.asyncio
    async def test_determine_coverage_with_storage_set_failed_also_fails(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test coverage determination with storage when both agent and set_failed fail."""
        # Set up mocks
        run_id = uuid.uuid4()
        mock_coverage_run_repository.create_run = AsyncMock(return_value=run_id)
        mock_coverage_run_repository.set_in_progress = AsyncMock()
        mock_coverage_run_repository.set_failed = AsyncMock(
            side_effect=Exception("Set failed also failed")
        )

        # Make the agent fail
        mock_agent.determine_coverage = AsyncMock(side_effect=Exception("Agent failed"))

        claim_id = "test_claim_123"
        created_by = "<EMAIL>"

        with pytest.raises(Exception, match="Agent failed"):
            await claims_service.determine_coverage_with_storage(
                claim_id=claim_id,
                created_by=created_by,
            )

        # Verify both methods were called
        mock_coverage_run_repository.set_failed.assert_called_once_with(run_id)

    @pytest.mark.asyncio
    async def test_submit_feedback_success(
        self,
        claims_service: ClaimsService,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test successful feedback submission."""
        # Create feedback list
        feedback_list = [
            NoteFeedback(
                note_id=uuid.uuid4(),
                modified_content=VerificationItem(
                    name="Modified Policy Check",
                    summary="Modified policy summary",
                    assessment_score=0.95,
                    citation=Citation(excerpt="Modified policy excerpt"),
                    verification_type="policy",
                ),
            ),
            NoteFeedback(
                note_id=uuid.uuid4(),
                modified_content=VerificationItem(
                    name="Modified Claim Check",
                    summary="Modified claim summary",
                    assessment_score=0.85,
                    citation=Citation(excerpt="Modified claim excerpt"),
                    verification_type="claim",
                ),
            ),
        ]

        updated_note_ids = [feedback.note_id for feedback in feedback_list]
        mock_coverage_notes_repository.upsert_feedback = AsyncMock(
            return_value=updated_note_ids
        )

        updated_by = "<EMAIL>"

        result = await claims_service.submit_feedback(
            feedback_list=feedback_list,
            updated_by=updated_by,
        )

        assert isinstance(result, FeedbackResponse)
        assert result.updated_notes == updated_note_ids

        mock_coverage_notes_repository.upsert_feedback.assert_called_once_with(
            feedback_list=feedback_list,
            updated_by=updated_by,
        )

    @pytest.mark.asyncio
    async def test_submit_feedback_empty_list(
        self,
        claims_service: ClaimsService,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test submitting feedback with empty list."""
        mock_coverage_notes_repository.upsert_feedback = AsyncMock(return_value=[])

        updated_by = "<EMAIL>"

        result = await claims_service.submit_feedback(
            feedback_list=[],
            updated_by=updated_by,
        )

        assert isinstance(result, FeedbackResponse)
        assert result.updated_notes == []

        mock_coverage_notes_repository.upsert_feedback.assert_called_once_with(
            feedback_list=[],
            updated_by=updated_by,
        )

    @pytest.mark.asyncio
    async def test_submit_feedback_partial_success(
        self,
        claims_service: ClaimsService,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> None:
        """Test feedback submission with partial success."""
        # Create feedback list
        feedback_list = [
            NoteFeedback(
                note_id=uuid.uuid4(),
                modified_content=VerificationItem(
                    name="Modified Policy Check",
                    summary="Modified policy summary",
                    assessment_score=0.95,
                    citation=Citation(excerpt="Modified policy excerpt"),
                    verification_type="policy",
                ),
            ),
            NoteFeedback(
                note_id=uuid.uuid4(),  # This one will fail
                modified_content=VerificationItem(
                    name="Modified Claim Check",
                    summary="Modified claim summary",
                    assessment_score=0.85,
                    citation=Citation(excerpt="Modified claim excerpt"),
                    verification_type="claim",
                ),
            ),
        ]

        # Only the first note is updated (second one not found)
        updated_note_ids = [feedback_list[0].note_id]
        mock_coverage_notes_repository.upsert_feedback = AsyncMock(
            return_value=updated_note_ids
        )

        updated_by = "<EMAIL>"

        result = await claims_service.submit_feedback(
            feedback_list=feedback_list,
            updated_by=updated_by,
        )

        assert isinstance(result, FeedbackResponse)
        assert result.updated_notes == updated_note_ids
        assert len(result.updated_notes) == 1

    @pytest.mark.asyncio
    async def test_determine_coverage_with_storage_with_refined_notes(
        self,
        claims_service: ClaimsService,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
        sample_coverage_determination_response: CoverageDeterminationResponse,
        sample_coverage_run_with_notes: CoverageRunWithNotes,
    ) -> None:
        """Test coverage determination with storage when previous refined notes exist."""
        # Set up mocks
        run_id = uuid.uuid4()
        mock_coverage_run_repository.create_run = AsyncMock(return_value=run_id)
        mock_coverage_run_repository.set_in_progress = AsyncMock()
        mock_coverage_run_repository.set_succeeded = AsyncMock()
        mock_coverage_run_repository.get_run_with_notes = AsyncMock(
            return_value=sample_coverage_run_with_notes
        )

        # Mock previous refined notes
        mock_previous_note = Mock(spec=CoverageNotes)
        mock_previous_note.modified_content = {
            "name": "Previous Policy Check",
            "summary": "Previously refined policy verification",
            "assessment_score": 0.92,
            "verification_type": "policy",
        }
        mock_previous_note.updated_at = datetime(
            2024, 1, 10, 15, 30, 0, tzinfo=timezone.utc
        )

        mock_coverage_notes_repository.get_recent_refined_coverage_notes = AsyncMock(
            return_value=[mock_previous_note]
        )

        mock_agent.determine_coverage = AsyncMock(
            return_value=sample_coverage_determination_response
        )

        note_ids = [uuid.uuid4(), uuid.uuid4()]
        mock_coverage_notes_repository.create_notes_for_run = AsyncMock(
            return_value=note_ids
        )

        claim_id = "test_claim_123"
        created_by = "<EMAIL>"
        authorization = "Bearer test-token"
        as_of_date = datetime.now(timezone.utc)
        trace_id = "trace_123"

        # Act
        result = await claims_service.determine_coverage_with_storage(
            claim_id=claim_id,
            created_by=created_by,
            authorization=authorization,
            as_of_date=as_of_date,
            trace_id=trace_id,
        )

        # Assert
        assert result == sample_coverage_run_with_notes

        # Verify the sequence of calls
        mock_coverage_run_repository.create_run.assert_called_once_with(
            claim_id=claim_id,
            created_by=created_by,
            trace_id=trace_id,
        )
        mock_coverage_run_repository.set_in_progress.assert_called_once_with(run_id)

        # Verify that previous refined notes were fetched
        mock_coverage_notes_repository.get_recent_refined_coverage_notes.assert_called_once_with(
            claim_id=claim_id
        )

        # Verify agent was called with formatted previous notes
        mock_agent.determine_coverage.assert_called_once()
        call_args = mock_agent.determine_coverage.call_args
        assert call_args.kwargs["claim_id"] == claim_id
        assert call_args.kwargs["authorization"] == authorization
        assert call_args.kwargs["as_of_date"] == as_of_date

        # Verify refined_notes parameter contains formatted previous notes
        refined_notes = call_args.kwargs["refined_notes"]
        assert refined_notes is not None
        assert "Previous Policy Check" in refined_notes
        assert "2024-01-10 15:30:00 UTC" in refined_notes
        assert "Previously refined policy verification" in refined_notes

        mock_coverage_notes_repository.create_notes_for_run.assert_called_once_with(
            run_id=run_id,
            claim_id=claim_id,
            verification_items=sample_coverage_determination_response.verifications,
        )
        mock_coverage_run_repository.set_succeeded.assert_called_once_with(run_id)
        mock_coverage_run_repository.get_run_with_notes.assert_called_once_with(run_id)


class TestClaimsServiceFormatPreviousNotes:
    """Tests for ClaimsService._format_previous_notes_for_prompt method."""

    @pytest.fixture
    def claims_service(
        self,
        mock_agent: AgentProtocol,
        mock_coverage_run_repository: CoverageRunRepositoryProtocol,
        mock_coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> ClaimsService:
        """Create a ClaimsService instance for testing."""
        return ClaimsService(
            agent=mock_agent,
            coverage_run_repository=mock_coverage_run_repository,
            coverage_notes_repository=mock_coverage_notes_repository,
        )

    def test_format_previous_notes_empty_list(
        self, claims_service: ClaimsService
    ) -> None:
        """Test formatting with empty list returns default message."""
        result = claims_service._format_previous_notes_for_prompt([])
        assert result == "No previous refined examples available."

    def test_format_previous_notes_no_modified_content(
        self, claims_service: ClaimsService
    ) -> None:
        """Test that notes without modified_content are skipped."""
        notes = [
            Mock(
                spec=CoverageNotes,
                modified_content=None,
                updated_at=datetime.now(timezone.utc),
            ),
            Mock(spec=CoverageNotes, modified_content={}, updated_at=None),
        ]

        result = claims_service._format_previous_notes_for_prompt(notes)  # type: ignore[arg-type]
        assert result == "No previous refined examples available."

    def test_format_previous_notes_no_updated_at(
        self, claims_service: ClaimsService
    ) -> None:
        """Test that notes without updated_at are skipped."""
        notes = [
            Mock(
                spec=CoverageNotes, modified_content={"test": "data"}, updated_at=None
            ),
        ]

        result = claims_service._format_previous_notes_for_prompt(notes)  # type: ignore[arg-type]
        assert result == "No previous refined examples available."

    def test_format_previous_notes_single_valid_note(
        self, claims_service: ClaimsService
    ) -> None:
        """Test formatting a single valid note."""
        test_date = datetime(2024, 1, 15, 10, 30, 45, tzinfo=timezone.utc)
        test_content = {
            "name": "Policy Status Check",
            "summary": "Verified policy is active on incident date",
            "assessment_score": 0.95,
            "verification_type": "policy",
        }

        notes = [
            Mock(
                spec=CoverageNotes, modified_content=test_content, updated_at=test_date
            ),
        ]

        result = claims_service._format_previous_notes_for_prompt(notes)  # type: ignore[arg-type]

        expected_json = '{\n  "name": "Policy Status Check",\n  "summary": "Verified policy is active on incident date",\n  "assessment_score": 0.95,\n  "verification_type": "policy"\n}'
        expected = f"""
**Coverage Note (Updated: 2024-01-15 10:30:45 UTC)**
```json
{expected_json}
```
"""

        assert result == expected

    def test_format_previous_notes_multiple_valid_notes(
        self, claims_service: ClaimsService
    ) -> None:
        """Test formatting multiple valid notes."""
        test_date1 = datetime(2024, 1, 15, 10, 30, 45, tzinfo=timezone.utc)
        test_date2 = datetime(2024, 1, 16, 14, 22, 10, tzinfo=timezone.utc)

        test_content1 = {
            "name": "Policy Status Check",
            "summary": "Verified policy is active on incident date",
            "assessment_score": 0.95,
            "verification_type": "policy",
        }

        test_content2 = {
            "name": "Driver Coverage Check",
            "summary": "Confirmed driver is listed on policy",
            "assessment_score": 0.88,
            "verification_type": "driver",
        }

        notes = [
            Mock(
                spec=CoverageNotes,
                modified_content=test_content1,
                updated_at=test_date1,
            ),
            Mock(
                spec=CoverageNotes,
                modified_content=test_content2,
                updated_at=test_date2,
            ),
        ]

        result = claims_service._format_previous_notes_for_prompt(notes)  # type: ignore[arg-type]

        # Check that both notes are formatted and joined
        assert "Policy Status Check" in result
        assert "Driver Coverage Check" in result
        assert "2024-01-15 10:30:45 UTC" in result
        assert "2024-01-16 14:22:10 UTC" in result
        assert result.count("**Coverage Note") == 2
        assert result.count("```json") == 2

    def test_format_previous_notes_mixed_valid_invalid(
        self, claims_service: ClaimsService
    ) -> None:
        """Test formatting with mix of valid and invalid notes."""
        test_date = datetime(2024, 1, 15, 10, 30, 45, tzinfo=timezone.utc)
        test_content = {
            "name": "Policy Status Check",
            "summary": "Verified policy is active on incident date",
            "assessment_score": 0.95,
            "verification_type": "policy",
        }

        notes = [
            Mock(
                spec=CoverageNotes, modified_content=None, updated_at=test_date
            ),  # Invalid - no content
            Mock(
                spec=CoverageNotes, modified_content=test_content, updated_at=test_date
            ),  # Valid
            Mock(
                spec=CoverageNotes, modified_content=test_content, updated_at=None
            ),  # Invalid - no date
        ]

        result = claims_service._format_previous_notes_for_prompt(notes)  # type: ignore[arg-type]

        # Only the valid note should be included
        assert "Policy Status Check" in result
        assert "2024-01-15 10:30:45 UTC" in result
        assert result.count("**Coverage Note") == 1

    def test_format_previous_notes_complex_json_structure(
        self, claims_service: ClaimsService
    ) -> None:
        """Test formatting with complex nested JSON structure."""
        test_date = datetime(2024, 1, 15, 10, 30, 45, tzinfo=timezone.utc)
        test_content = {
            "name": "Complex Policy Check",
            "summary": "Multi-faceted policy verification",
            "assessment_score": 0.92,
            "citation": {
                "excerpt": "Policy document text here",
                "document": {"filename": "policy_doc.pdf", "page": 3},
            },
            "verification_type": "policy",
            "details": ["check1", "check2", "check3"],
        }

        notes = [
            Mock(
                spec=CoverageNotes, modified_content=test_content, updated_at=test_date
            ),
        ]

        result = claims_service._format_previous_notes_for_prompt(notes)  # type: ignore[arg-type]

        # Verify proper JSON formatting with indentation
        assert '"name": "Complex Policy Check"' in result
        assert '"details": [' in result
        assert '"check1",' in result
        assert "  " in result  # Check for proper indentation
