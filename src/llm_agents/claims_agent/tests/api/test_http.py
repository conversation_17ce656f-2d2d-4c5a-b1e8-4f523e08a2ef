"""Test cases for the HTTP API handlers."""

import json
import uuid
from unittest.mock import MagicMock

from claims_agent.config import Settings
import pytest
from fastapi import status
from httpx import AsyncClient
import sqlalchemy as sa

from claims_agent.api.auth import get_formatted_mcp_authorization
from claims_agent.api.errors import ClaimsAgentError
from claims_agent.interfaces.repositories import (
    CoverageDeterminationRequestRepositoryProtocol,
)
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
    VerificationItem as LegacyVerificationItem,
    Citation as LegacyCitation,
    Document as LegacyDocument,
)
from claims_agent.db.orm.generated import LegacyCoverageDeterminations
from claims_agent.db.repositories.coverage_determinations import (
    SupabaseCoverageDeterminationRequestRepository,
)


class TestHealthEndpoint:
    """Tests for the health check endpoint."""

    @pytest.mark.asyncio
    async def test_health_check_healthy(
        self, client: AsyncClient, mock_claims_service: MagicMock
    ) -> None:
        """Test health check when service is healthy."""
        mock_claims_service.health_check.return_value = (
            True,
            "All systems operational",
        )

        response = await client.get("/health")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert data["agent_status"] == "initialized"
        assert data["details"] == "All systems operational"

    @pytest.mark.asyncio
    async def test_health_check_unhealthy(
        self, client: AsyncClient, mock_claims_service: MagicMock
    ) -> None:
        """Test health check when service is unhealthy."""
        mock_claims_service.health_check.return_value = (
            False,
            "Agent initialization failed",
        )

        response = await client.get("/health")

        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        assert response.json()["detail"] == "Agent initialization failed"

    @pytest.mark.asyncio
    async def test_health_check_exception(
        self, client: AsyncClient, mock_claims_service: MagicMock
    ) -> None:
        """Test health check when service throws exception."""
        mock_claims_service.health_check.side_effect = Exception(
            "Database connection failed"
        )

        response = await client.get("/health")

        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        assert "Health check failed" in response.json()["detail"]


class TestCoverageEndpoint:
    """Tests for the coverage determination endpoint with real database."""

    @pytest.fixture
    def sample_coverage_response(self) -> LegacyCoverageDeterminationResponse:
        """Create a sample coverage determination response."""
        return LegacyCoverageDeterminationResponse(
            verifications=[
                LegacyVerificationItem(
                    name="Driver Age Verification",
                    assessment_score=1.0,
                    summary="Driver meets age requirements.",
                    citation=LegacyCitation(
                        excerpt="Driver must be at least 21 years old.",
                        document=LegacyDocument(
                            policy_number="POLICY-001",
                            document_id=uuid.UUID(
                                "12345678-1234-5678-1234-************"
                            ),
                            filename="policy_terms.pdf",
                        ),
                        pages=[5],
                    ),
                    verification_type="policy",
                )
            ]
        )

    @pytest.mark.asyncio
    async def test_coverage_determination_successful_tracking(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test successful coverage determination with complete request tracking."""
        claim_id = "NITMS25030077"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        # Get the app from the client and override auth
        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to return successful response
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data["verifications"]) == 1
        assert response_data["verifications"][0]["name"] == "Driver Age Verification"

        # Verify service was called correctly
        mock_claims_service.determine_coverage.assert_called_once()
        call_args = mock_claims_service.determine_coverage.call_args
        assert call_args[1]["claim_id"] == claim_id
        assert call_args[1]["authorization"] == "Bearer valid-token"

        # Verify database tracking - find the request record
        # Since we don't control the UUID generation, we need to find the record
        # We can check that exactly one record was created for our claim

        # Get the session from the repository to query directly
        assert isinstance(
            coverage_determination_repo, SupabaseCoverageDeterminationRequestRepository
        )
        async with coverage_determination_repo._session_maker() as session:
            result = await session.execute(
                sa.select(LegacyCoverageDeterminations).where(
                    LegacyCoverageDeterminations.claim_id == claim_id
                )
            )
            records = result.scalars().all()

        assert len(records) == 1
        record = records[0]
        assert record.status == "succeeded"
        assert record.claim_id == claim_id
        assert record.content is not None
        assert record.error is None

        # Verify the response was stored correctly
        stored_response = LegacyCoverageDeterminationResponse.model_validate(
            record.content
        )
        assert stored_response == sample_coverage_response

    @pytest.mark.asyncio
    async def test_coverage_determination_with_as_of_date(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test coverage determination with as_of_date parameter."""
        claim_id = "NITMS25030078"
        as_of_date = "2024-01-15T10:00:00Z"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request with as_of_date
        response = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?as_of_date={as_of_date}"
        )

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK

        # Verify service was called with as_of_date
        mock_claims_service.determine_coverage.assert_called_once()
        call_args = mock_claims_service.determine_coverage.call_args
        assert call_args[1]["as_of_date"] is not None

    @pytest.mark.asyncio
    async def test_coverage_determination_no_auth(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test coverage determination without authorization."""
        claim_id = "NITMS25030079"

        # Configure auth dependency to return None
        async def mock_auth_dependency() -> str | None:
            return None

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK

        # Verify service was called with None authorization
        mock_claims_service.determine_coverage.assert_called_once()
        call_args = mock_claims_service.determine_coverage.call_args
        assert call_args[1]["authorization"] is None

    @pytest.mark.asyncio
    async def test_coverage_determination_invalid_claim_id(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test coverage determination with invalid claim ID."""
        # Test with empty claim ID
        response = await client.get("/api/claims//coverage/determination")
        assert response.status_code == status.HTTP_404_NOT_FOUND

        # Test with very short claim ID (less than 3 characters)
        response = await client.get("/api/claims/AB/coverage/determination")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["detail"] == "Invalid claim ID format"

        # Verify cache headers are preserved even for HTTPException
        assert "X-Cache-Status" in response.headers
        assert response.headers["X-Cache-Status"] == "ERROR"
        assert "X-Cache-TTL" in response.headers

        # Verify service was not called for invalid requests
        mock_claims_service.determine_coverage.assert_not_called()

        # Verify no database records were created
        assert isinstance(
            coverage_determination_repo, SupabaseCoverageDeterminationRequestRepository
        )
        async with coverage_determination_repo._session_maker() as session:
            result = await session.execute(sa.select(LegacyCoverageDeterminations))
            records = result.scalars().all()
        assert len(records) == 0

    @pytest.mark.asyncio
    async def test_coverage_determination_ttl_validation_demo(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test TTL validation behavior - demonstrates bug #2 where TTL validation incorrectly uses default as max."""
        # This test demonstrates the current buggy behavior described in the bug report
        # The TTL validation incorrectly treats the default TTL as a maximum allowed value
        # instead of just a default value when ttl_seconds is not provided

        # This would be the correct behavior after fixing bug #2:
        # - ttl_seconds=None should use default TTL
        # - ttl_seconds=0 should disable caching
        # - ttl_seconds=1800 should work (even if less than default)
        # - ttl_seconds=7200 should work (even if greater than default)
        # - Only unreasonably large values should be rejected

        # For now, we just document that the TTL validation logic exists
        # and that cache headers are preserved when HTTPException is raised
        pass

    @pytest.mark.asyncio
    async def test_coverage_determination_claims_agent_error(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test coverage determination when ClaimsAgentError occurs."""
        claim_id = "NITMS25030080"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to raise ClaimsAgentError
        error_message = "Policy not found for claim"
        mock_claims_service.determine_coverage.side_effect = ClaimsAgentError(
            error_message
        )

        # Make request
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")

        # Verify HTTP response (ClaimsAgentError should be handled by FastAPI exception handlers)
        # The exact status code depends on how ClaimsAgentError is handled in your error handlers
        assert response.status_code >= 400

        # Verify database tracking - should be in failed state
        assert isinstance(
            coverage_determination_repo, SupabaseCoverageDeterminationRequestRepository
        )
        async with coverage_determination_repo._session_maker() as session:
            result = await session.execute(
                sa.select(LegacyCoverageDeterminations).where(
                    LegacyCoverageDeterminations.claim_id == claim_id
                )
            )
            records = result.scalars().all()

        assert len(records) == 1
        record = records[0]
        assert record.status == "failed"
        assert record.content is None
        assert record.error is not None

        # Verify error details
        error_data = json.loads(record.error)
        assert error_data["kind"] == "ClaimsAgentError"
        assert error_data["message"] == error_message

    @pytest.mark.asyncio
    async def test_coverage_determination_generic_error(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test coverage determination when generic exception occurs."""
        claim_id = "NITMS25030081"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to raise generic exception
        error_message = "Database connection failed"
        mock_claims_service.determine_coverage.side_effect = Exception(error_message)

        # Make request
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")

        # Verify HTTP response
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert (
            "An error occurred during coverage determination"
            in response.json()["detail"]
        )

        # Verify database tracking - should be in failed state
        assert isinstance(
            coverage_determination_repo, SupabaseCoverageDeterminationRequestRepository
        )
        async with coverage_determination_repo._session_maker() as session:
            result = await session.execute(
                sa.select(LegacyCoverageDeterminations).where(
                    LegacyCoverageDeterminations.claim_id == claim_id
                )
            )
            records = result.scalars().all()

        assert len(records) == 1
        record = records[0]
        assert record.status == "failed"
        assert record.content is None
        assert record.error is not None

        # Verify error details
        error_data = json.loads(record.error)
        assert error_data["kind"] == "Exception"
        assert error_data["message"] == error_message

    @pytest.mark.asyncio
    async def test_coverage_determination_request_id_generation(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that unique request IDs are generated for each request."""
        claim_id = "NITMS25030082"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make multiple requests
        responses = []
        for i in range(3):
            response = await client.get(
                f"/api/claims/{claim_id}_{i}/coverage/determination"
            )
            responses.append(response)
            assert response.status_code == status.HTTP_200_OK

        # Verify all requests were tracked with unique IDs
        assert isinstance(
            coverage_determination_repo, SupabaseCoverageDeterminationRequestRepository
        )
        async with coverage_determination_repo._session_maker() as session:
            result = await session.execute(sa.select(LegacyCoverageDeterminations))
            records = result.scalars().all()

        assert len(records) == 3
        request_ids = {record.id for record in records}
        assert len(request_ids) == 3  # All request IDs should be unique

        # Verify all succeeded
        for record in records:
            assert record.status == "succeeded"
            assert record.content is not None


class TestCoverageEndpointBackwardCompatibility:
    """Tests to ensure existing behavior is unchanged with caching."""

    @pytest.fixture
    def sample_coverage_response(self) -> LegacyCoverageDeterminationResponse:
        """Create a sample coverage determination response."""
        return LegacyCoverageDeterminationResponse(
            verifications=[
                LegacyVerificationItem(
                    name="Driver Age Verification",
                    assessment_score=1.0,
                    summary="Driver meets age requirements.",
                    citation=LegacyCitation(
                        excerpt="Driver must be at least 21 years old.",
                        document=LegacyDocument(
                            policy_number="POLICY-001",
                            document_id=uuid.UUID(
                                "12345678-1234-5678-1234-************"
                            ),
                            filename="policy_terms.pdf",
                        ),
                        pages=[5],
                    ),
                    verification_type="policy",
                )
            ]
        )

    @pytest.mark.asyncio
    async def test_existing_behavior_unchanged_without_cache_params(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that existing API behavior is unchanged when no cache parameters are used."""
        claim_id = "NITMS25030500"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request without any cache parameters (original API)
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")

        # Verify response structure is unchanged
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "verifications" in response_data
        assert len(response_data["verifications"]) == 1
        assert response_data["verifications"][0]["name"] == "Driver Age Verification"

        # Verify cache headers are present but non-intrusive
        assert "X-Cache-Status" in response.headers
        assert "X-Cache-TTL" in response.headers

        # Verify service was called (same as before)
        mock_claims_service.determine_coverage.assert_called_once()

    @pytest.mark.asyncio
    async def test_default_parameters_work_as_expected(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
        mock_settings: Settings,
    ) -> None:
        """Test that default parameter values work correctly."""
        claim_id = "NITMS25030501"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Test with explicit default values
        response = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?force_refresh=false"
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["X-Cache-Status"] == "MISS"  # First call
        assert response.headers["X-Cache-TTL"] == str(
            mock_settings.COVERAGE_DETERMINATION_TTL_SECONDS
        )

        # Verify service was called
        mock_claims_service.determine_coverage.assert_called_once()

    @pytest.mark.asyncio
    async def test_error_handling_unchanged_with_caching(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test that error handling behavior is unchanged with caching enabled."""
        claim_id = "NITMS25030502"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to raise ClaimsAgentError
        error_message = "Policy not found for claim"
        mock_claims_service.determine_coverage.side_effect = ClaimsAgentError(
            error_message
        )

        # Make request - error handling should be unchanged
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")

        # Verify error response structure is unchanged
        assert response.status_code >= 400  # Some error status

        # Cache headers should still be present even on errors
        assert "X-Cache-Status" in response.headers
        assert (
            response.headers["X-Cache-Status"] == "ERROR"
        )  # Error cases should show ERROR status
        assert "X-Cache-TTL" in response.headers

        # Verify database tracking still works for errors
        assert isinstance(
            coverage_determination_repo, SupabaseCoverageDeterminationRequestRepository
        )
        async with coverage_determination_repo._session_maker() as session:
            result = await session.execute(
                sa.select(LegacyCoverageDeterminations).where(
                    LegacyCoverageDeterminations.claim_id == claim_id
                )
            )
            records = result.scalars().all()

        assert len(records) == 1
        record = records[0]
        assert record.status == "failed"

    @pytest.mark.asyncio
    async def test_as_of_date_parameter_unchanged(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that as_of_date parameter behavior is unchanged."""
        claim_id = "NITMS25030503"
        as_of_date = "2024-01-15T10:00:00Z"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request with as_of_date (existing parameter)
        response = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?as_of_date={as_of_date}"
        )

        # Verify response is successful
        assert response.status_code == status.HTTP_200_OK

        # Verify service was called with as_of_date (unchanged behavior)
        mock_claims_service.determine_coverage.assert_called_once()
        call_args = mock_claims_service.determine_coverage.call_args
        assert call_args[1]["as_of_date"] is not None

        # Cache should work with as_of_date
        assert response.headers["X-Cache-Status"] == "MISS"

    @pytest.mark.asyncio
    async def test_auth_handling_unchanged(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that authentication handling is unchanged."""
        claim_id = "NITMS25030504"

        # Test with no auth (existing behavior)
        async def mock_auth_dependency() -> str | None:
            return None

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service
        mock_claims_service.determine_coverage.return_value = sample_coverage_response

        # Make request
        response = await client.get(f"/api/claims/{claim_id}/coverage/determination")

        # Verify response is successful
        assert response.status_code == status.HTTP_200_OK

        # Verify service was called with None authorization (unchanged)
        mock_claims_service.determine_coverage.assert_called_once()
        call_args = mock_claims_service.determine_coverage.call_args
        assert call_args[1]["authorization"] is None

    @pytest.mark.asyncio
    async def test_invalid_claim_id_handling_unchanged(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test that invalid claim ID handling is unchanged."""
        # Test with short claim ID (existing validation)
        response = await client.get("/api/claims/AB/coverage/determination")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["detail"] == "Invalid claim ID format"

        # Verify cache headers are preserved (new behavior with HTTPException handler)
        assert "X-Cache-Status" in response.headers
        assert response.headers["X-Cache-Status"] == "ERROR"
        assert "X-Cache-TTL" in response.headers

        # Verify service was not called
        mock_claims_service.determine_coverage.assert_not_called()

        # Verify no database records were created
        assert isinstance(
            coverage_determination_repo, SupabaseCoverageDeterminationRequestRepository
        )
        async with coverage_determination_repo._session_maker() as session:
            result = await session.execute(sa.select(LegacyCoverageDeterminations))
            records = result.scalars().all()

        # Only records from previous tests might exist, but none for claim "AB"
        for record in records:
            assert record.claim_id != "AB"

    @pytest.mark.asyncio
    async def test_http_exception_handler_preserves_cache_headers(
        self,
        client: AsyncClient,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test that HTTPException handler preserves cache headers from request state."""
        claim_id = "NITMS25030091"

        # Test TTL validation error (HTTPException) - cache headers should be preserved
        response = await client.get(
            f"/api/claims/{claim_id}/coverage/determination?ttl_seconds=999999"
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "TTL cannot exceed" in response.json()["detail"]

        # Verify cache headers are preserved
        assert "X-Cache-Status" in response.headers
        assert response.headers["X-Cache-Status"] == "ERROR"
        assert "X-Cache-TTL" in response.headers

        # Test invalid claim ID (HTTPException) - cache headers should be preserved
        response = await client.get("/api/claims/X/coverage/determination")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["detail"] == "Invalid claim ID format"

        # Verify cache headers are preserved
        assert "X-Cache-Status" in response.headers
        assert response.headers["X-Cache-Status"] == "ERROR"
        assert "X-Cache-TTL" in response.headers

        # Verify service was not called for validation errors
        mock_claims_service.determine_coverage.assert_not_called()


class TestCoverageEndpointV2:
    """Tests for the coverage determination v2 endpoint with storage."""

    @pytest.fixture
    def sample_coverage_run_with_notes(self) -> dict:
        """Create a sample coverage run with notes response."""
        return {
            "created_at": "2024-07-15T10:00:00Z",
            "created_by": "system",
            "coverage_notes": [
                {
                    "note_id": "*************-8765-4321-************",
                    "original_content": {
                        "name": "Driver Age Verification",
                        "assessment_score": 1.0,
                        "summary": "Driver meets age requirements.",
                        "citation": {
                            "excerpt": "Driver must be at least 21 years old.",
                            "document": {
                                "policy_number": "POLICY-001",
                                "document_id": "12345678-1234-5678-1234-************",
                                "filename": "policy_terms.pdf",
                            },
                            "pages": [5],
                        },
                        "verification_type": "policy",
                    },
                    "modified_content": None,
                    "updated_at": "2024-07-15T10:00:00Z",
                    "updated_by": "system",
                }
            ],
        }

    @pytest.mark.asyncio
    async def test_coverage_determination_v2_success(
        self,
        client: AsyncClient,
        mock_claims_service: MagicMock,
        sample_coverage_run_with_notes: dict,
    ) -> None:
        """Test successful coverage determination v2 endpoint."""
        claim_id = "NITMS25030077"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        # Get the app from the client and override auth
        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to return successful response
        from claims_agent.models.feedback import CoverageRunWithNotes

        mock_coverage_run = CoverageRunWithNotes.model_validate(
            sample_coverage_run_with_notes
        )
        mock_claims_service.determine_coverage_with_storage.return_value = (
            mock_coverage_run
        )

        # Make request
        response = await client.get(f"/api/v2/claims/{claim_id}/coverage/determination")

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data["coverage_notes"]) == 1

        # Verify service was called correctly
        mock_claims_service.determine_coverage_with_storage.assert_called_once()
        call_args = mock_claims_service.determine_coverage_with_storage.call_args
        assert call_args[1]["claim_id"] == claim_id
        assert call_args[1]["authorization"] == "Bearer valid-token"
        assert call_args[1]["trace_id"] is None
        assert call_args[1]["as_of_date"] is None

    @pytest.mark.asyncio
    async def test_coverage_determination_v2_with_as_of_date(
        self,
        client: AsyncClient,
        mock_claims_service: MagicMock,
        sample_coverage_run_with_notes: dict,
    ) -> None:
        """Test coverage determination v2 with as_of_date parameter."""
        claim_id = "NITMS25030077"
        as_of_date = "2024-07-15T10:00:00Z"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to return successful response
        from claims_agent.models.feedback import CoverageRunWithNotes

        mock_coverage_run = CoverageRunWithNotes.model_validate(
            sample_coverage_run_with_notes
        )
        mock_claims_service.determine_coverage_with_storage.return_value = (
            mock_coverage_run
        )

        # Make request with as_of_date
        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination",
            params={"as_of_date": as_of_date},
        )

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK

        # Verify service was called with as_of_date
        mock_claims_service.determine_coverage_with_storage.assert_called_once()
        call_args = mock_claims_service.determine_coverage_with_storage.call_args
        assert call_args[1]["as_of_date"] is not None

    @pytest.mark.asyncio
    async def test_coverage_determination_v2_no_auth(
        self,
        client: AsyncClient,
        mock_claims_service: MagicMock,
        sample_coverage_run_with_notes: dict,
    ) -> None:
        """Test coverage determination v2 without authorization."""
        claim_id = "NITMS25030077"

        # Configure auth dependency to return None
        async def mock_auth_dependency() -> str | None:
            return None

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to return successful response
        from claims_agent.models.feedback import CoverageRunWithNotes

        mock_coverage_run = CoverageRunWithNotes.model_validate(
            sample_coverage_run_with_notes
        )
        mock_claims_service.determine_coverage_with_storage.return_value = (
            mock_coverage_run
        )

        # Make request
        response = await client.get(f"/api/v2/claims/{claim_id}/coverage/determination")

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK

        # Verify service was called with None authorization
        mock_claims_service.determine_coverage_with_storage.assert_called_once()
        call_args = mock_claims_service.determine_coverage_with_storage.call_args
        assert call_args[1]["authorization"] is None

    @pytest.mark.asyncio
    async def test_coverage_determination_v2_service_error(
        self,
        client: AsyncClient,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test coverage determination v2 with service error."""
        claim_id = "NITMS25030077"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to raise an exception
        mock_claims_service.determine_coverage_with_storage.side_effect = Exception(
            "Service error"
        )

        # Make request
        response = await client.get(f"/api/v2/claims/{claim_id}/coverage/determination")

        # Verify HTTP response
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "Service error" in response_data["detail"]

        # Verify service was called
        mock_claims_service.determine_coverage_with_storage.assert_called_once()

    @pytest.mark.asyncio
    async def test_coverage_determination_v2_claims_agent_error(
        self,
        client: AsyncClient,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test coverage determination v2 with ClaimsAgentError."""
        claim_id = "NITMS25030077"

        # Configure auth dependency
        async def mock_auth_dependency() -> str:
            return "Bearer valid-token"

        app = client._transport.app  # type: ignore
        app.dependency_overrides[get_formatted_mcp_authorization] = mock_auth_dependency

        # Configure service to raise a ClaimsAgentError
        mock_claims_service.determine_coverage_with_storage.side_effect = (
            ClaimsAgentError("Claims agent error")
        )

        # Make request
        response = await client.get(f"/api/v2/claims/{claim_id}/coverage/determination")

        # Verify HTTP response
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "Claims agent error" in response_data["detail"]


class TestCoverageFeedbackEndpoint:
    """Tests for the coverage feedback endpoint.

    Note: These tests currently have a limitation where the FastAPI dependency
    injection system requires database configuration even for validation errors.
    This is due to the CoverageNotesRepository dependency being injected at
    the endpoint level. Future improvements could move these tests to use
    integration testing with a real database setup.
    """

    @pytest.fixture
    def sample_feedback_request(self) -> dict:
        """Create a sample feedback request."""
        return {
            "feedback": [
                {
                    "note_id": "*************-8765-4321-************",
                    "modified_content": {
                        "name": "Driver Age Verification Updated",
                        "assessment_score": 0.8,
                        "summary": "Driver meets age requirements with minor concerns.",
                        "citation": {
                            "excerpt": "Driver must be at least 21 years old.",
                            "document": {
                                "policy_number": "POLICY-001",
                                "document_id": "12345678-1234-5678-1234-************",
                                "filename": "policy_terms.pdf",
                            },
                            "pages": [5],
                        },
                        "verification_type": "policy",
                    },
                }
            ],
        }

    @pytest.mark.asyncio
    async def test_upsert_coverage_feedback_success(
        self,
        client: AsyncClient,
        sample_feedback_request: dict,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test successful feedback submission."""
        from claims_agent.models.feedback import FeedbackResponse

        # Configure the mock service to return a successful response
        mock_claims_service.submit_feedback.return_value = FeedbackResponse(
            updated_notes=[uuid.UUID("*************-8765-4321-************")]
        )

        # Make request
        response = await client.post(
            "/api/v2/claims/coverage/feedback", json=sample_feedback_request
        )

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data["updated_notes"]) == 1
        assert (
            response_data["updated_notes"][0] == "*************-8765-4321-************"
        )

        # Verify service was called correctly
        mock_claims_service.submit_feedback.assert_called_once()
        call_args = mock_claims_service.submit_feedback.call_args
        assert call_args[1]["updated_by"] == "system"
        assert len(call_args[1]["feedback_list"]) == 1

    @pytest.mark.asyncio
    async def test_upsert_coverage_feedback_multiple_notes(
        self,
        client: AsyncClient,
        sample_feedback_request: dict,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test feedback submission with multiple notes."""
        # Add a second note to the feedback request
        sample_feedback_request["feedback"].append(
            {
                "note_id": "11111111-1111-1111-1111-111111111111",
                "modified_content": {
                    "name": "Second Note",
                    "assessment_score": 0.9,
                    "summary": "Second note summary.",
                    "citation": {
                        "excerpt": "Second excerpt.",
                        "document": {
                            "policy_number": "POLICY-002",
                            "document_id": "22222222-2222-2222-2222-222222222222",
                            "filename": "policy_terms2.pdf",
                        },
                        "pages": [3],
                    },
                    "verification_type": "policy",
                },
            }
        )

        from claims_agent.models.feedback import FeedbackResponse

        # Configure the mock service to return two updated notes
        mock_claims_service.submit_feedback.return_value = FeedbackResponse(
            updated_notes=[
                uuid.UUID("*************-8765-4321-************"),
                uuid.UUID("11111111-1111-1111-1111-111111111111"),
            ]
        )

        # Make request
        response = await client.post(
            "/api/v2/claims/coverage/feedback", json=sample_feedback_request
        )

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data["updated_notes"]) == 2

        # Verify service was called with both notes
        mock_claims_service.submit_feedback.assert_called_once()
        call_args = mock_claims_service.submit_feedback.call_args
        assert len(call_args[1]["feedback_list"]) == 2

    @pytest.mark.asyncio
    async def test_upsert_coverage_feedback_invalid_request(
        self,
        client: AsyncClient,
    ) -> None:
        """Test feedback submission with invalid request data."""
        invalid_request = {
            "feedback": "invalid_not_a_list",
        }

        # Make request with invalid data
        response = await client.post(
            "/api/v2/claims/coverage/feedback", json=invalid_request
        )

        # Verify HTTP response
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_upsert_coverage_feedback_missing_fields(
        self,
        client: AsyncClient,
    ) -> None:
        """Test feedback submission with missing required fields."""
        invalid_request = {
            # Missing feedback field
        }

        # Make request with missing fields
        response = await client.post(
            "/api/v2/claims/coverage/feedback", json=invalid_request
        )

        # Verify HTTP response
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_upsert_coverage_feedback_repository_error(
        self,
        client: AsyncClient,
        sample_feedback_request: dict,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test feedback submission with repository error."""
        # Configure the mock service to raise an exception
        mock_claims_service.submit_feedback.side_effect = Exception("Database error")

        # Make request
        response = await client.post(
            "/api/v2/claims/coverage/feedback", json=sample_feedback_request
        )

        # Verify HTTP response
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "Database error" in response_data["detail"]

        # Verify service was called
        mock_claims_service.submit_feedback.assert_called_once()

    @pytest.mark.asyncio
    async def test_upsert_coverage_feedback_empty_feedback_list(
        self,
        client: AsyncClient,
        mock_claims_service: MagicMock,
    ) -> None:
        """Test feedback submission with empty feedback list."""
        empty_feedback_request = {
            "feedback": [],
        }

        from claims_agent.models.feedback import FeedbackResponse

        # Configure the mock service to return empty list
        mock_claims_service.submit_feedback.return_value = FeedbackResponse(
            updated_notes=[]
        )

        # Make request
        response = await client.post(
            "/api/v2/claims/coverage/feedback", json=empty_feedback_request
        )

        # Verify HTTP response
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data["updated_notes"]) == 0


class TestCachedCoverageDeterminationEndpoint:
    """Tests for the cached coverage determination endpoint."""

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_no_runs(
        self, client: AsyncClient
    ) -> None:
        """Test getting cached results when no runs exist."""
        claim_id = "NONEXISTENT_CLAIM"

        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached"
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data == []

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_no_successful_runs(
        self, client: AsyncClient, coverage_run_repository
    ) -> None:
        """Test getting cached results when no successful runs exist."""
        claim_id = "CLAIM_WITH_FAILED_RUNS"

        # Create a failed run
        run_id = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_failed(run_id)

        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached"
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data == []

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_single_run(
        self, client: AsyncClient, coverage_run_repository
    ) -> None:
        """Test getting cached results with a single successful run."""
        claim_id = "CLAIM_WITH_SINGLE_RUN"

        # Create a successful run
        run_id = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id)

        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached"
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data) == 1
        assert response_data[0]["created_by"] == "<EMAIL>"
        assert "created_at" in response_data[0]
        assert "coverage_notes" in response_data[0]

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_multiple_runs_default_limit(
        self, client: AsyncClient, coverage_run_repository
    ) -> None:
        """Test getting cached results with multiple successful runs using default limit."""
        claim_id = "CLAIM_WITH_MULTIPLE_RUNS"

        # Create three successful runs
        run_id_1 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_1)

        run_id_2 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_2)

        run_id_3 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_3)

        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached"
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data) == 1  # Default limit is 1
        assert response_data[0]["created_by"] == "<EMAIL>"  # Most recent

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_with_runs_parameter(
        self, client: AsyncClient, coverage_run_repository
    ) -> None:
        """Test getting cached results with custom runs parameter."""
        claim_id = "CLAIM_WITH_CUSTOM_LIMIT"

        # Create four successful runs
        for i in range(1, 5):
            run_id = await coverage_run_repository.create_run(
                claim_id=claim_id, created_by=f"user{i}@example.com"
            )
            await coverage_run_repository.set_succeeded(run_id)

        # Test with runs=3
        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached?runs=3"
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data) == 3
        # Should be in reverse chronological order (most recent first)
        assert response_data[0]["created_by"] == "<EMAIL>"
        assert response_data[1]["created_by"] == "<EMAIL>"
        assert response_data[2]["created_by"] == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_mixed_statuses(
        self, client: AsyncClient, coverage_run_repository
    ) -> None:
        """Test getting cached results with mixed run statuses."""
        claim_id = "CLAIM_WITH_MIXED_STATUS"

        # Create runs with mixed statuses
        run_id_1 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_1)

        run_id_2 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_failed(run_id_2)  # This should be excluded

        run_id_3 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_3)

        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached?runs=5"
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data) == 2  # Only successful runs
        assert response_data[0]["created_by"] == "<EMAIL>"
        assert response_data[1]["created_by"] == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_invalid_claim_id(
        self, client: AsyncClient
    ) -> None:
        """Test validation with invalid claim ID."""
        response = await client.get(
            "/api/v2/claims/ab/coverage/determination/cached"  # Too short
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid claim ID format" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_invalid_runs_parameter(
        self, client: AsyncClient
    ) -> None:
        """Test validation with invalid runs parameter."""
        claim_id = "VALID_CLAIM_ID"

        # Test runs < 1
        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached?runs=0"
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Number of runs must be at least 1" in response.json()["detail"]

        # Test runs > 10
        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached?runs=11"
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Number of runs cannot exceed 10" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_boundary_values(
        self, client: AsyncClient, coverage_run_repository
    ) -> None:
        """Test boundary values for runs parameter."""
        claim_id = "BOUNDARY_TEST_CLAIM"

        # Create a successful run
        run_id = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id)

        # Test runs=1 (minimum valid)
        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached?runs=1"
        )
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 1

        # Test runs=10 (maximum valid)
        response = await client.get(
            f"/api/v2/claims/{claim_id}/coverage/determination/cached?runs=10"
        )
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 1  # Only 1 exists

    @pytest.mark.asyncio
    async def test_get_cached_coverage_determinations_different_claims(
        self, client: AsyncClient, coverage_run_repository
    ) -> None:
        """Test that only runs for the specified claim are returned."""
        claim_id_1 = "CLAIM_A"
        claim_id_2 = "CLAIM_B"

        # Create successful runs for different claims
        run_id_1 = await coverage_run_repository.create_run(
            claim_id=claim_id_1, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_1)

        run_id_2 = await coverage_run_repository.create_run(
            claim_id=claim_id_2, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_2)

        # Test claim A
        response = await client.get(
            f"/api/v2/claims/{claim_id_1}/coverage/determination/cached"
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data) == 1
        assert response_data[0]["created_by"] == "<EMAIL>"

        # Test claim B
        response = await client.get(
            f"/api/v2/claims/{claim_id_2}/coverage/determination/cached"
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data) == 1
        assert response_data[0]["created_by"] == "<EMAIL>"
