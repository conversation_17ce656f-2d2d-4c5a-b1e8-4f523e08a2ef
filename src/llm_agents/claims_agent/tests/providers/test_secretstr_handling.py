"""Tests for SecretStr handling in providers."""

import pytest
from unittest.mock import patch, MagicMock
from pydantic import SecretStr

from claims_agent.providers.openrouter_provider import OpenRouterProvider
from claims_agent.providers.openai_provider import OpenAIProvider
from claims_agent.providers.provider_interface import ProviderConfig


class TestSecretStrHandling:
    """Test cases for proper SecretStr handling in providers."""

    def test_openrouter_provider_extracts_secret_value(self):
        """Test that OpenRouter provider properly extracts SecretStr value."""
        config = ProviderConfig(
            model_name="openai/o3-mini",
            temperature=0.7,
            api_key=SecretStr("test-openrouter-key"),
        )
        provider = OpenRouterProvider(config)

        with patch(
            "claims_agent.providers.openrouter_provider.ChatOpenAI"
        ) as mock_chat_openai:
            mock_llm = MagicMock()
            mock_chat_openai.return_value = mock_llm

            llm = provider.create_llm()

            # Verify ChatOpenAI was called with the SecretStr object
            mock_chat_openai.assert_called_once_with(
                model="openai/o3-mini",
                temperature=0.7,
                api_key=config.api_key,  # SecretStr object
                base_url="https://openrouter.ai/api/v1",
                default_headers={
                    "HTTP-Referer": "https://github.com/nirvanatech/nirvana",
                    "X-Title": "Nirvana Claims Agent",
                },
            )
            assert llm == mock_llm

    def test_openai_provider_extracts_secret_value(self):
        """Test that OpenAI provider properly extracts SecretStr value."""
        config = ProviderConfig(
            model_name="o3-mini", temperature=0.8, api_key=SecretStr("test-openai-key")
        )
        provider = OpenAIProvider(config)

        with patch(
            "claims_agent.providers.openai_provider.ChatOpenAI"
        ) as mock_chat_openai:
            mock_llm = MagicMock()
            mock_chat_openai.return_value = mock_llm

            llm = provider.create_llm()

            # Verify ChatOpenAI was called with the SecretStr object
            mock_chat_openai.assert_called_once_with(
                model="o3-mini",
                temperature=0.8,
                api_key=config.api_key,  # SecretStr object
            )
            assert llm == mock_llm

    def test_secretstr_masking_behavior(self):
        """Test that SecretStr properly masks values when converted to string."""
        secret_key = SecretStr("super-secret-api-key")

        # SecretStr should mask the value when converted to string
        str_representation = str(secret_key)
        assert "super-secret-api-key" not in str_representation
        assert "**********" in str_representation or "*" in str_representation

        # But get_secret_value() should return the actual value
        assert secret_key.get_secret_value() == "super-secret-api-key"

    def test_provider_config_auto_converts_string_api_keys(self):
        """Test that ProviderConfig automatically converts string API keys to SecretStr."""
        config = ProviderConfig(
            model_name="test-model",
            temperature=0.7,
            api_key="plain-string-key",  # Pydantic auto-converts to SecretStr
        )
        assert isinstance(config.api_key, SecretStr)
        assert config.api_key.get_secret_value() == "plain-string-key"

    def test_provider_config_validation_prevents_empty_api_keys(self):
        """Test that ProviderConfig validation prevents empty API keys."""
        with pytest.raises(ValueError) as exc_info:
            ProviderConfig(
                model_name="test-model", temperature=0.7, api_key=SecretStr("")
            )
        assert "API key cannot be empty" in str(exc_info.value)

    def test_openrouter_provider_with_empty_key_fails_validation(self):
        """Test that OpenRouter provider fails validation with empty key."""
        with pytest.raises(ValueError):
            ProviderConfig(
                model_name="openai/o3-mini",
                temperature=0.7,
                api_key=SecretStr(""),  # Empty key should fail validation
            )

    def test_openai_provider_with_empty_key_fails_validation(self):
        """Test that OpenAI provider fails validation with empty key."""
        with pytest.raises(ValueError):
            ProviderConfig(
                model_name="o3-mini",
                temperature=0.7,
                api_key=SecretStr(""),  # Empty key should fail validation
            )

    def test_secretstr_in_logging_does_not_leak(self):
        """Test that SecretStr values don't leak in logging."""
        config = ProviderConfig(
            model_name="test-model",
            temperature=0.7,
            api_key=SecretStr("secret-api-key"),
        )

        # When the config is converted to string (e.g., for logging),
        # the secret should be masked
        config_str = str(config)
        assert "secret-api-key" not in config_str

        # Same for repr
        config_repr = repr(config)
        assert "secret-api-key" not in config_repr

    def test_provider_name_properties(self):
        """Test that provider name properties work correctly."""
        openrouter_config = ProviderConfig(
            model_name="openai/o3-mini", temperature=0.7, api_key=SecretStr("test-key")
        )
        openrouter_provider = OpenRouterProvider(openrouter_config)
        assert openrouter_provider.provider_name == "OpenRouter"

        openai_config = ProviderConfig(
            model_name="o3-mini", temperature=0.7, api_key=SecretStr("test-key")
        )
        openai_provider = OpenAIProvider(openai_config)
        assert openai_provider.provider_name == "OpenAI"
