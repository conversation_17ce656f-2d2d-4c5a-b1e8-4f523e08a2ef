"""Tests for provider factory."""

import pytest
from pydantic import SecretStr
from unittest.mock import patch

from claims_agent.config import Settings
from claims_agent.providers.provider_factory import (
    create_llm_provider,
    ProviderCreationError,
)
from claims_agent.providers.openrouter_provider import OpenRouterProvider
from claims_agent.providers.openai_provider import OpenAIProvider


class TestProviderFactory:
    """Test cases for provider factory."""

    def test_create_openrouter_provider_when_key_available(self):
        """Test that OpenRouter provider is created when key is available."""
        settings = Settings(
            OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
            OPENROUTER_MODEL_NAME="google/gemini-2.5-pro-preview",
            OPENAI_API_KEY=SecretStr(""),
            MODEL_TEMPERATURE=0.7,
        )

        with patch.object(
            OpenRouterProvider, "validate_model_capabilities", return_value=True
        ):
            provider = create_llm_provider(settings)

        assert isinstance(provider, OpenRouterProvider)
        assert provider.config.model_name == "google/gemini-2.5-pro-preview"
        assert provider.config.temperature == 0.7

    def test_openai_only_when_no_openrouter_key(self):
        """Test that OpenAI provider is used when no OpenRouter key."""
        settings = Settings(
            LLM_PROVIDER="legacy_openai",
            OPENROUTER_API_KEY=SecretStr(""),
            OPENAI_API_KEY=SecretStr("test-openai-key"),
            LEGACY_OPENAI_MODEL_NAME="gpt-4o",
            MODEL_TEMPERATURE=0.7,
        )

        with patch.object(
            OpenAIProvider, "validate_model_capabilities", return_value=True
        ):
            provider = create_llm_provider(settings)

        assert isinstance(provider, OpenAIProvider)
        assert provider.config.model_name == "gpt-4o"

    def test_error_when_no_keys_available(self):
        """Test that error is raised when no API keys are available."""
        settings = Settings(
            OPENROUTER_API_KEY=SecretStr(""),
            OPENAI_API_KEY=SecretStr(""),
            MODEL_TEMPERATURE=0.7,
        )

        with pytest.raises(ProviderCreationError):
            create_llm_provider(settings)

    def test_validation_warning_does_not_block_openrouter(self):
        """Provider should still be OpenRouter even if capability validation fails."""

        settings = Settings(
            OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
            OPENROUTER_MODEL_NAME="unsupported/model",
            OPENAI_API_KEY=SecretStr("test-openai-key"),
            MODEL_TEMPERATURE=0.7,
        )

        with patch.object(
            OpenRouterProvider, "validate_model_capabilities", return_value=False
        ) as mock_validate:
            provider = create_llm_provider(settings)

        assert isinstance(provider, OpenRouterProvider)
        assert provider.config.model_name == "unsupported/model"
        mock_validate.assert_called_once()

    def test_fallback_to_openai_when_model_not_found(self):
        """If OpenRouter model isn't listed (validate returns None) we fall back to OpenAI."""

        settings = Settings(
            OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
            OPENROUTER_MODEL_NAME="nonexistent/model",
            OPENAI_API_KEY=SecretStr("test-openai-key"),
            LEGACY_OPENAI_MODEL_NAME="gpt-4o",
            MODEL_TEMPERATURE=0.5,
        )

        with (
            patch.object(
                OpenRouterProvider, "validate_model_capabilities", return_value=None
            ),
            patch.object(
                OpenAIProvider, "validate_model_capabilities", return_value=True
            ),
        ):
            provider = create_llm_provider(settings)

        assert isinstance(provider, OpenAIProvider)
        assert provider.config.model_name == "gpt-4o"

    def test_log_level_assignment_for_attempted_providers(self):
        """Test that log levels are assigned correctly based on attempted providers, not all providers."""
        # Create settings where OpenRouter key is missing but OpenAI key is present
        settings = Settings(
            LLM_PROVIDER="openrouter",  # Prefer OpenRouter but it will be skipped
            OPENROUTER_API_KEY=SecretStr(""),  # Missing - will be skipped
            OPENAI_API_KEY=SecretStr(
                "test-openai-key"
            ),  # Present - will be attempted first
            LEGACY_OPENAI_MODEL_NAME="gpt-4o",
            MODEL_TEMPERATURE=0.7,
        )

        # Mock the provider creation to fail so we can capture log levels
        with (
            patch(
                "claims_agent.providers.provider_factory._create_openrouter_provider"
            ) as mock_openrouter,
            patch(
                "claims_agent.providers.provider_factory._create_openai_provider"
            ) as mock_openai,
            patch("claims_agent.providers.provider_factory.logger") as mock_logger,
        ):
            # Make both providers fail validation to trigger logging
            mock_openrouter.side_effect = ValueError("OpenRouter test error")
            mock_openai.side_effect = ValueError("OpenAI test error")

            with pytest.raises(ProviderCreationError):
                create_llm_provider(settings)

            # Verify that OpenRouter creation was never called (due to missing API key)
            mock_openrouter.assert_not_called()

            # Verify that OpenAI creation was called (it has an API key)
            mock_openai.assert_called_once()

            # Check that the first attempted provider (OpenAI) was logged with warning level
            warning_calls = [
                call_obj
                for call_obj in mock_logger.warning.call_args_list
                if "OpenAI provider configuration error" in str(call_obj)
            ]
            assert len(warning_calls) == 1, (
                "OpenAI should be logged at warning level as first attempted provider"
            )

            # Verify no error-level logs for OpenAI (since it's the first attempted)
            error_calls = [
                call_obj
                for call_obj in mock_logger.error.call_args_list
                if "OpenAI provider configuration error" in str(call_obj)
            ]
            assert len(error_calls) == 0, (
                "OpenAI should not be logged at error level as first attempted provider"
            )

    def test_log_level_assignment_for_multiple_attempted_providers(self):
        """Test that log levels are correct when multiple providers are attempted."""
        # Create settings where both providers have API keys
        settings = Settings(
            LLM_PROVIDER="openrouter",  # Prefer OpenRouter
            OPENROUTER_API_KEY=SecretStr(
                "test-openrouter-key"
            ),  # Present - will be attempted first
            OPENAI_API_KEY=SecretStr(
                "test-openai-key"
            ),  # Present - will be attempted second
            OPENROUTER_MODEL_NAME="google/gemini-2.5-pro-preview",
            LEGACY_OPENAI_MODEL_NAME="gpt-4o",
            MODEL_TEMPERATURE=0.7,
        )

        # Mock the provider creation to fail so we can capture log levels
        with (
            patch(
                "claims_agent.providers.provider_factory._create_openrouter_provider"
            ) as mock_openrouter,
            patch(
                "claims_agent.providers.provider_factory._create_openai_provider"
            ) as mock_openai,
            patch("claims_agent.providers.provider_factory.logger") as mock_logger,
        ):
            # Make both providers fail validation to trigger logging
            mock_openrouter.side_effect = ValueError("OpenRouter test error")
            mock_openai.side_effect = ValueError("OpenAI test error")

            with pytest.raises(ProviderCreationError):
                create_llm_provider(settings)

            # Verify that both providers were called
            mock_openrouter.assert_called_once()
            mock_openai.assert_called_once()

            # Check that the first attempted provider (OpenRouter) was logged with warning level
            openrouter_warning_calls = [
                call_obj
                for call_obj in mock_logger.warning.call_args_list
                if "OpenRouter provider configuration error" in str(call_obj)
            ]
            assert len(openrouter_warning_calls) == 1, (
                "OpenRouter should be logged at warning level as first attempted provider"
            )

            # Check that the second attempted provider (OpenAI) was logged with error level
            openai_error_calls = [
                call_obj
                for call_obj in mock_logger.error.call_args_list
                if "OpenAI provider configuration error" in str(call_obj)
            ]
            assert len(openai_error_calls) == 1, (
                "OpenAI should be logged at error level as second attempted provider"
            )

            # Verify no error-level logs for OpenRouter (since it's the first attempted)
            openrouter_error_calls = [
                call_obj
                for call_obj in mock_logger.error.call_args_list
                if "OpenRouter provider configuration error" in str(call_obj)
            ]
            assert len(openrouter_error_calls) == 0, (
                "OpenRouter should not be logged at error level as first attempted provider"
            )
