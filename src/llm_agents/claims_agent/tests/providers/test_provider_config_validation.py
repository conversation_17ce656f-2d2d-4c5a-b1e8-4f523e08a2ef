"""Tests for provider configuration validation."""

import pytest
from pydantic import SecretStr, ValidationError

from claims_agent.providers.provider_interface import ProviderConfig


class TestProviderConfigValidation:
    """Test cases for ProviderConfig validation."""

    def test_valid_config_creation(self):
        """Test that valid configuration is created successfully."""
        config = ProviderConfig(
            model_name="openai/o3-mini",
            temperature=0.7,
            api_key=SecretStr("test-api-key"),
        )

        assert config.model_name == "openai/o3-mini"
        assert config.temperature == 0.7
        assert config.api_key.get_secret_value() == "test-api-key"

    def test_temperature_validation_within_range(self):
        """Test that valid temperature values are accepted."""
        # Test boundary values
        config_min = ProviderConfig(
            model_name="test-model", temperature=0.0, api_key=SecretStr("test-key")
        )
        assert config_min.temperature == 0.0

        config_max = ProviderConfig(
            model_name="test-model", temperature=2.0, api_key=SecretStr("test-key")
        )
        assert config_max.temperature == 2.0

    def test_temperature_validation_out_of_range(self):
        """Test that invalid temperature values are rejected."""
        # Test below minimum
        with pytest.raises(ValidationError) as exc_info:
            ProviderConfig(
                model_name="test-model", temperature=-0.1, api_key=SecretStr("test-key")
            )
        assert "greater than or equal to 0" in str(exc_info.value)

        # Test above maximum
        with pytest.raises(ValidationError) as exc_info:
            ProviderConfig(
                model_name="test-model", temperature=2.1, api_key=SecretStr("test-key")
            )
        assert "less than or equal to 2" in str(exc_info.value)

    def test_api_key_string_auto_conversion(self):
        """Test that plain string API keys are automatically converted to SecretStr."""
        config = ProviderConfig(
            model_name="test-model",
            temperature=0.7,
            api_key="plain-string-key",  # Pydantic auto-converts to SecretStr
        )
        assert isinstance(config.api_key, SecretStr)
        assert config.api_key.get_secret_value() == "plain-string-key"

    def test_api_key_empty_validation(self):
        """Test that empty API keys are rejected."""
        with pytest.raises(ValidationError) as exc_info:
            ProviderConfig(
                model_name="test-model", temperature=0.7, api_key=SecretStr("")
            )
        assert "API key cannot be empty" in str(exc_info.value)

        # Test whitespace-only key
        with pytest.raises(ValidationError) as exc_info:
            ProviderConfig(
                model_name="test-model", temperature=0.7, api_key=SecretStr("   ")
            )
        assert "API key cannot be empty" in str(exc_info.value)

    def test_default_temperature(self):
        """Test that default temperature is applied correctly."""
        config = ProviderConfig(
            model_name="test-model",
            api_key=SecretStr("test-key"),
            # temperature not specified, should use default
        )
        assert config.temperature == 0.7  # Default value from Field

    def test_model_name_required(self):
        """Test that model_name is required."""
        with pytest.raises(ValidationError) as exc_info:
            ProviderConfig(
                temperature=0.7,
                api_key=SecretStr("test-key"),
                # model_name missing
            )
        assert "model_name" in str(exc_info.value)

    def test_api_key_required(self):
        """Test that api_key is required."""
        with pytest.raises(ValidationError) as exc_info:
            ProviderConfig(
                model_name="test-model",
                temperature=0.7,
                # api_key missing
            )
        assert "api_key" in str(exc_info.value)

    def test_config_immutability(self):
        """Test that configuration is immutable after creation."""
        config = ProviderConfig(
            model_name="test-model", temperature=0.7, api_key=SecretStr("test-key")
        )

        # Pydantic models are mutable by default, but we can test that
        # the SecretStr value is properly encapsulated
        assert config.api_key.get_secret_value() == "test-key"

        # The SecretStr should mask the value when converted to string
        assert "test-key" not in str(config.api_key)

    def test_config_serialization_excludes_secrets(self):
        """Test that configuration serialization properly handles secrets."""
        config = ProviderConfig(
            model_name="test-model", temperature=0.7, api_key=SecretStr("secret-key")
        )

        # Convert to dict - should exclude secret values by default
        config_dict = config.model_dump()
        assert config_dict["model_name"] == "test-model"
        assert config_dict["temperature"] == 0.7
        # SecretStr should be excluded or masked in serialization
        assert "secret-key" not in str(config_dict)
