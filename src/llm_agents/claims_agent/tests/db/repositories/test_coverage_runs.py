"""Tests for CoverageRunRepository."""

import uuid
from datetime import datetime

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from claims_agent.db.repositories.coverage_runs import CoverageRunRepository
from claims_agent.db.orm.generated import CoverageNotes
from claims_agent.interfaces.legacy_models import VerificationItem, Citation


pytestmark = pytest.mark.asyncio


@pytest.fixture
async def coverage_run_repository(
    session_maker: async_sessionmaker[AsyncSession],
) -> CoverageRunRepository:
    """Create a CoverageRunRepository instance for testing."""
    return CoverageRunRepository(session_maker)


class TestCoverageRunRepository:
    """Test suite for CoverageRunRepository."""

    async def test_create_run_with_defaults(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test creating a coverage run with default values."""
        claim_id = "test_claim_123"
        created_by = "<EMAIL>"

        run_id = await coverage_run_repository.create_run(
            claim_id=claim_id,
            created_by=created_by,
        )

        assert isinstance(run_id, uuid.UUID)

        # Verify the run was created properly
        run = await coverage_run_repository.get_run_with_notes(run_id)
        assert run is not None
        assert run.coverage_notes == []
        assert isinstance(run.created_at, datetime)

    async def test_create_run_with_all_parameters(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test creating a coverage run with all parameters."""
        claim_id = "test_claim_456"
        created_by = "<EMAIL>"
        trace_id = "trace_789"

        run_id = await coverage_run_repository.create_run(
            claim_id=claim_id,
            created_by=created_by,
            trace_id=trace_id,
        )

        assert isinstance(run_id, uuid.UUID)

        # Verify the run was created with correct parameters
        run = await coverage_run_repository.get_run_with_notes(run_id)
        assert run is not None
        assert run.coverage_notes == []

    async def test_set_in_progress(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test setting a run status to IN_PROGRESS."""
        run_id = await coverage_run_repository.create_run(
            claim_id="test_claim", created_by="<EMAIL>"
        )

        await coverage_run_repository.set_in_progress(run_id)

        # Verify status was updated
        run = await coverage_run_repository.get_run_with_notes(run_id)
        assert run is not None

    async def test_set_succeeded(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test setting a run status to SUCCEEDED."""
        run_id = await coverage_run_repository.create_run(
            claim_id="test_claim", created_by="<EMAIL>"
        )

        await coverage_run_repository.set_succeeded(run_id)

        # Verify status was updated
        run = await coverage_run_repository.get_run_with_notes(run_id)
        assert run is not None

    async def test_set_failed(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test setting a run status to FAILED."""
        run_id = await coverage_run_repository.create_run(
            claim_id="test_claim", created_by="<EMAIL>"
        )

        await coverage_run_repository.set_failed(run_id)

        # Verify status was updated
        run = await coverage_run_repository.get_run_with_notes(run_id)
        assert run is not None

    async def test_set_cancelled(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test setting a run status to CANCELLED."""
        run_id = await coverage_run_repository.create_run(
            claim_id="test_claim", created_by="<EMAIL>"
        )

        await coverage_run_repository.set_cancelled(run_id)

        # Verify status was updated
        run = await coverage_run_repository.get_run_with_notes(run_id)
        assert run is not None

    async def test_set_status_nonexistent_run(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test setting status for a non-existent run."""
        nonexistent_id = uuid.uuid4()

        # Should not raise an exception, just log a warning
        await coverage_run_repository.set_in_progress(nonexistent_id)
        await coverage_run_repository.set_succeeded(nonexistent_id)
        await coverage_run_repository.set_failed(nonexistent_id)
        await coverage_run_repository.set_cancelled(nonexistent_id)

    async def test_get_run_with_notes_nonexistent(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting a non-existent run."""
        nonexistent_id = uuid.uuid4()

        run = await coverage_run_repository.get_run_with_notes(nonexistent_id)
        assert run is None

    async def test_get_run_with_notes_with_coverage_notes(
        self,
        coverage_run_repository: CoverageRunRepository,
        session_maker: async_sessionmaker[AsyncSession],
    ) -> None:
        """Test getting a run with coverage notes."""
        # Create a run
        run_id = await coverage_run_repository.create_run(
            claim_id="test_claim", created_by="<EMAIL>"
        )

        # Create some coverage notes manually
        verification_item = VerificationItem(
            name="Test Verification",
            summary="Test summary",
            assessment_score=0.8,
            citation=Citation(excerpt="Test excerpt"),
            verification_type="policy",
        )

        async with session_maker() as session:
            note = CoverageNotes(
                run_id=run_id,
                claim_id="test_claim",
                original_content=verification_item.model_dump(mode="json"),
                updated_by="<EMAIL>",
            )
            session.add(note)
            await session.commit()

        # Retrieve the run with notes
        run = await coverage_run_repository.get_run_with_notes(run_id)
        assert run is not None
        assert len(run.coverage_notes) == 1
        assert run.coverage_notes[0].original_content.name == "Test Verification"
        assert run.coverage_notes[0].original_content.summary == "Test summary"
        assert run.coverage_notes[0].original_content.assessment_score == 0.8
        assert run.coverage_notes[0].modified_content is None
        assert run.coverage_notes[0].updated_by == "<EMAIL>"

    async def test_get_run_with_notes_with_modified_content(
        self,
        coverage_run_repository: CoverageRunRepository,
        session_maker: async_sessionmaker[AsyncSession],
    ) -> None:
        """Test getting a run with coverage notes that have modified content."""
        # Create a run
        run_id = await coverage_run_repository.create_run(
            claim_id="test_claim", created_by="<EMAIL>"
        )

        # Create coverage note with modified content
        original_verification = VerificationItem(
            name="Original Verification",
            summary="Original summary",
            assessment_score=0.5,
            citation=Citation(excerpt="Original excerpt"),
            verification_type="policy",
        )

        modified_verification = VerificationItem(
            name="Modified Verification",
            summary="Modified summary",
            assessment_score=0.9,
            citation=Citation(excerpt="Modified excerpt"),
            verification_type="claim",
        )

        async with session_maker() as session:
            note = CoverageNotes(
                run_id=run_id,
                claim_id="test_claim",
                original_content=original_verification.model_dump(mode="json"),
                modified_content=modified_verification.model_dump(mode="json"),
                updated_by="<EMAIL>",
            )
            session.add(note)
            await session.commit()

        # Retrieve the run with notes
        run = await coverage_run_repository.get_run_with_notes(run_id)
        assert run is not None
        assert len(run.coverage_notes) == 1
        assert run.coverage_notes[0].original_content.name == "Original Verification"
        assert run.coverage_notes[0].modified_content is not None
        assert run.coverage_notes[0].modified_content.name == "Modified Verification"
        assert run.coverage_notes[0].modified_content.summary == "Modified summary"
        assert run.coverage_notes[0].modified_content.assessment_score == 0.9

    async def test_get_latest_run_for_claim_no_runs(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting latest run for a claim with no runs."""
        run = await coverage_run_repository.get_latest_run_for_claim(
            "nonexistent_claim"
        )
        assert run is None

    async def test_get_latest_run_for_claim_single_run(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting latest run for a claim with one run."""
        claim_id = "test_claim_single"
        _ = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )

        run = await coverage_run_repository.get_latest_run_for_claim(claim_id)
        assert run is not None
        assert len(run.coverage_notes) == 0

    async def test_get_latest_run_for_claim_multiple_runs(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting latest run for a claim with multiple runs."""
        claim_id = "test_claim_multiple"

        # Create first run
        _ = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )

        # Create second run (should be newer)
        _ = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )

        # Should return the latest run
        run = await coverage_run_repository.get_latest_run_for_claim(claim_id)
        assert run is not None
        assert len(run.coverage_notes) == 0

    async def test_get_latest_run_for_claim_different_claims(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting latest run for a specific claim when multiple claims exist."""
        claim_id_1 = "test_claim_1"
        claim_id_2 = "test_claim_2"

        # Create runs for different claims
        _ = await coverage_run_repository.create_run(
            claim_id=claim_id_1, created_by="<EMAIL>"
        )
        _ = await coverage_run_repository.create_run(
            claim_id=claim_id_2, created_by="<EMAIL>"
        )

        # Should return the run for the specific claim
        run_1 = await coverage_run_repository.get_latest_run_for_claim(claim_id_1)
        run_2 = await coverage_run_repository.get_latest_run_for_claim(claim_id_2)

        assert run_1 is not None
        assert run_2 is not None
        assert len(run_1.coverage_notes) == 0
        assert len(run_2.coverage_notes) == 0

    async def test_get_latest_successful_runs_for_claim_no_runs(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting successful runs for a claim with no runs."""
        runs = await coverage_run_repository.get_latest_successful_runs_for_claim(
            "nonexistent_claim"
        )
        assert runs == []

    async def test_get_latest_successful_runs_for_claim_no_successful_runs(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting successful runs when no successful runs exist."""
        claim_id = "test_claim_no_success"

        # Create a run but don't set it to succeeded
        run_id = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_failed(run_id)

        runs = await coverage_run_repository.get_latest_successful_runs_for_claim(
            claim_id
        )
        assert runs == []

    async def test_get_latest_successful_runs_for_claim_single_successful(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting successful runs with one successful run."""
        claim_id = "test_claim_single_success"

        run_id = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id)

        runs = await coverage_run_repository.get_latest_successful_runs_for_claim(
            claim_id, limit=1
        )
        assert len(runs) == 1
        assert runs[0].created_by == "<EMAIL>"

    async def test_get_latest_successful_runs_for_claim_multiple_successful(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting successful runs with multiple successful runs."""
        claim_id = "test_claim_multi_success"

        # Create three runs and set them to succeeded in order
        run_id_1 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_1)

        run_id_2 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_2)

        run_id_3 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_3)

        # Test default limit (1)
        runs = await coverage_run_repository.get_latest_successful_runs_for_claim(
            claim_id
        )
        assert len(runs) == 1
        assert runs[0].created_by == "<EMAIL>"  # Most recent

        # Test limit of 2
        runs = await coverage_run_repository.get_latest_successful_runs_for_claim(
            claim_id, limit=2
        )
        assert len(runs) == 2
        assert runs[0].created_by == "<EMAIL>"  # Most recent first
        assert runs[1].created_by == "<EMAIL>"

        # Test limit of 5 (more than available)
        runs = await coverage_run_repository.get_latest_successful_runs_for_claim(
            claim_id, limit=5
        )
        assert len(runs) == 3  # Only 3 exist
        assert runs[0].created_by == "<EMAIL>"
        assert runs[1].created_by == "<EMAIL>"
        assert runs[2].created_by == "<EMAIL>"

    async def test_get_latest_successful_runs_for_claim_mixed_statuses(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test getting successful runs when mixed with failed/other status runs."""
        claim_id = "test_claim_mixed_status"

        # Create runs with mixed statuses
        run_id_1 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_1)

        run_id_2 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_failed(run_id_2)  # Failed

        run_id_3 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_3)

        run_id_4 = await coverage_run_repository.create_run(
            claim_id=claim_id, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_cancelled(run_id_4)  # Cancelled

        # Should only return successful runs
        runs = await coverage_run_repository.get_latest_successful_runs_for_claim(
            claim_id, limit=5
        )
        assert len(runs) == 2  # Only 2 successful
        assert runs[0].created_by == "<EMAIL>"  # Most recent successful
        assert runs[1].created_by == "<EMAIL>"

    async def test_get_latest_successful_runs_for_claim_different_claims(
        self, coverage_run_repository: CoverageRunRepository
    ) -> None:
        """Test that only runs for the specified claim are returned."""
        claim_id_1 = "test_claim_A"
        claim_id_2 = "test_claim_B"

        # Create successful runs for different claims
        run_id_1 = await coverage_run_repository.create_run(
            claim_id=claim_id_1, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_1)

        run_id_2 = await coverage_run_repository.create_run(
            claim_id=claim_id_2, created_by="<EMAIL>"
        )
        await coverage_run_repository.set_succeeded(run_id_2)

        # Should return only runs for the specific claim
        runs_a = await coverage_run_repository.get_latest_successful_runs_for_claim(
            claim_id_1, limit=5
        )
        runs_b = await coverage_run_repository.get_latest_successful_runs_for_claim(
            claim_id_2, limit=5
        )

        assert len(runs_a) == 1
        assert len(runs_b) == 1
        assert runs_a[0].created_by == "<EMAIL>"
        assert runs_b[0].created_by == "<EMAIL>"
