"""Test cases for coverage determination caching functionality."""

import datetime
import uuid

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from claims_agent.db.orm.generated import LegacyCoverageDeterminations
from claims_agent.db.repositories.coverage_determinations import (
    SupabaseCoverageDeterminationRequestRepository,
)
from claims_agent.interfaces.repositories import (
    CoverageDeterminationStatus,
)
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
    VerificationItem as LegacyVerificationItem,
    Citation as LegacyCitation,
    Document as LegacyDocument,
)


class TestCoverageDeterminationCaching:
    """Tests for coverage determination caching functionality."""

    @pytest.fixture
    def sample_coverage_response(self) -> LegacyCoverageDeterminationResponse:
        """Create a sample coverage determination response."""
        return LegacyCoverageDeterminationResponse(
            verifications=[
                LegacyVerificationItem(
                    name="Driver Age Verification",
                    assessment_score=1.0,
                    summary="Driver meets age requirements.",
                    citation=LegacyCitation(
                        excerpt="Driver must be at least 21 years old.",
                        document=LegacyDocument(
                            policy_number="POLICY-001",
                            document_id=uuid.UUID(
                                "12345678-1234-5678-1234-************"
                            ),
                            filename="policy_terms.pdf",
                        ),
                        pages=[5],
                    ),
                    verification_type="policy",
                )
            ]
        )

    async def _create_test_record(
        self,
        session_maker: async_sessionmaker[AsyncSession],
        claim_id: str,
        status: CoverageDeterminationStatus,
        content: LegacyCoverageDeterminationResponse | None = None,
        updated_at: datetime.datetime | None = None,
    ) -> uuid.UUID:
        """Helper method to create a test record."""
        record_id = uuid.uuid4()
        timestamp = updated_at or datetime.datetime.now(datetime.timezone.utc)

        record = LegacyCoverageDeterminations(
            id=record_id,
            claim_id=claim_id,
            workflow_id=f"workflow-{record_id}",
            status=status,
            created_at=timestamp,
            updated_at=timestamp,
            content=content.model_dump(mode="json") if content else None,
        )

        async with session_maker() as session:
            session.add(record)
            await session.commit()

        return record_id

    @pytest.mark.asyncio
    async def test_get_latest_successful_within_ttl(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test successful cache lookup within TTL window."""
        claim_id = "NITMS25030100"

        # Create a fresh record (within TTL)
        session_maker = coverage_determination_repo._session_maker
        await self._create_test_record(
            session_maker,
            claim_id,
            CoverageDeterminationStatus.SUCCEEDED,
            sample_coverage_response,
        )

        # Lookup with 1 hour TTL
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )

        assert result is not None
        assert result == sample_coverage_response

    @pytest.mark.asyncio
    async def test_get_latest_successful_outside_ttl(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test cache lookup outside TTL window."""
        claim_id = "NITMS25030101"

        # Create an old record (outside TTL)
        old_timestamp = datetime.datetime.now(
            datetime.timezone.utc
        ) - datetime.timedelta(hours=2)
        session_maker = coverage_determination_repo._session_maker
        await self._create_test_record(
            session_maker,
            claim_id,
            CoverageDeterminationStatus.SUCCEEDED,
            sample_coverage_response,
            old_timestamp,
        )

        # Lookup with 1 hour TTL (record is 2 hours old)
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_get_latest_successful_no_records(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
    ) -> None:
        """Test cache lookup when no records exist."""
        result = await coverage_determination_repo.get_latest_successful(
            claim_id="NITMS25030102",
            max_age_seconds=3600,
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_get_latest_successful_only_failed_records(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
    ) -> None:
        """Test cache lookup when only failed records exist."""
        claim_id = "NITMS25030103"

        # Create a failed record
        session_maker = coverage_determination_repo._session_maker
        await self._create_test_record(
            session_maker,
            claim_id,
            CoverageDeterminationStatus.FAILED,
        )

        # Lookup should return None for failed records
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_get_latest_successful_multiple_records_returns_latest(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that the most recent successful record is returned."""
        claim_id = "NITMS25030104"
        session_maker = coverage_determination_repo._session_maker

        # Create older record
        older_response = LegacyCoverageDeterminationResponse(
            verifications=[
                LegacyVerificationItem(
                    name="Older Verification",
                    assessment_score=0.5,
                    summary="Older result.",
                    citation=LegacyCitation(
                        excerpt="Old excerpt.",
                        document=LegacyDocument(
                            policy_number="POLICY-OLD",
                            document_id=uuid.UUID(
                                "12345678-1234-5678-1234-************"
                            ),
                            filename="old_policy.pdf",
                        ),
                        pages=[1],
                    ),
                    verification_type="policy",
                )
            ]
        )
        older_timestamp = datetime.datetime.now(
            datetime.timezone.utc
        ) - datetime.timedelta(minutes=30)
        await self._create_test_record(
            session_maker,
            claim_id,
            CoverageDeterminationStatus.SUCCEEDED,
            older_response,
            older_timestamp,
        )

        # Create newer record
        newer_timestamp = datetime.datetime.now(
            datetime.timezone.utc
        ) - datetime.timedelta(minutes=10)
        await self._create_test_record(
            session_maker,
            claim_id,
            CoverageDeterminationStatus.SUCCEEDED,
            sample_coverage_response,
            newer_timestamp,
        )

        # Should return the newer record
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )

        assert result is not None
        assert result == sample_coverage_response

    @pytest.mark.asyncio
    async def test_get_latest_successful_different_claim_ids(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that cache lookup is claim_id specific."""
        claim_id_1 = "NITMS25030105"
        claim_id_2 = "NITMS25030106"
        session_maker = coverage_determination_repo._session_maker

        # Create record for claim_id_1
        await self._create_test_record(
            session_maker,
            claim_id_1,
            CoverageDeterminationStatus.SUCCEEDED,
            sample_coverage_response,
        )

        # Lookup for claim_id_2 should return None
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id_2,
            max_age_seconds=3600,
        )

        assert result is None

        # Lookup for claim_id_1 should return the record
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id_1,
            max_age_seconds=3600,
        )

        assert result is not None
        assert result == sample_coverage_response

    @pytest.mark.asyncio
    async def test_get_latest_successful_malformed_content(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
    ) -> None:
        """Test handling of malformed content in cache."""
        claim_id = "NITMS25030107"
        record_id = uuid.uuid4()
        timestamp = datetime.datetime.now(datetime.timezone.utc)

        # Create record with malformed content that will fail Pydantic validation
        record = LegacyCoverageDeterminations(
            id=record_id,
            claim_id=claim_id,
            workflow_id=f"workflow-{record_id}",
            status=CoverageDeterminationStatus.SUCCEEDED,
            created_at=timestamp,
            updated_at=timestamp,
            content={
                "verifications": "this should be a list, not a string"
            },  # Invalid format
        )

        session_maker = coverage_determination_repo._session_maker
        async with session_maker() as session:
            session.add(record)
            await session.commit()

        # Should return None for malformed content
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_get_latest_successful_null_content(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
    ) -> None:
        """Test handling of null content in cache."""
        claim_id = "NITMS25030108"
        session_maker = coverage_determination_repo._session_maker

        # Create record with null content
        await self._create_test_record(
            session_maker,
            claim_id,
            CoverageDeterminationStatus.SUCCEEDED,
            None,  # No content
        )

        # Should return None for null content
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )

        assert result is None
