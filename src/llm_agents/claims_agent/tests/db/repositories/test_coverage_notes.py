"""Tests for CoverageNotesRepository."""

import uuid

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from claims_agent.db.repositories.coverage_notes import CoverageNotesRepository
from claims_agent.db.orm.generated import CoverageRuns
from claims_agent.interfaces.repositories import CoverageDeterminationStatus
from claims_agent.interfaces.legacy_models import VerificationItem, Citation
from claims_agent.models.feedback import NoteFeedback


pytestmark = pytest.mark.asyncio


@pytest.fixture
async def coverage_notes_repository(
    session_maker: async_sessionmaker[AsyncSession],
) -> CoverageNotesRepository:
    """Create a CoverageNotesRepository instance for testing."""
    return CoverageNotesRepository(session_maker)


@pytest.fixture
async def sample_run_id(session_maker: async_sessionmaker[AsyncSession]) -> uuid.UUID:
    """Create a sample coverage run for testing."""
    async with session_maker() as session:
        run = CoverageRuns(
            claim_id="test_claim",
            created_by="<EMAIL>",
            status=CoverageDeterminationStatus.REQUESTED.value,
        )
        session.add(run)
        await session.commit()
        await session.refresh(run)
        return run.run_id


@pytest.fixture
def sample_verification_items() -> list[VerificationItem]:
    """Create sample verification items for testing."""
    return [
        VerificationItem(
            name="Policy Coverage Check",
            summary="Checking if policy covers the incident",
            assessment_score=0.8,
            citation=Citation(excerpt="Policy covers damages to property"),
            verification_type="policy",
        ),
        VerificationItem(
            name="Claim Validity Check",
            summary="Verifying claim details are accurate",
            assessment_score=0.9,
            citation=Citation(excerpt="Claim details match incident report"),
            verification_type="claim",
        ),
        VerificationItem(
            name="General Assessment",
            summary="Overall assessment of coverage",
            assessment_score=0.75,
            citation=Citation(excerpt="Coverage appears valid"),
            verification_type="general",
        ),
    ]


class TestCoverageNotesRepository:
    """Test suite for CoverageNotesRepository."""

    async def test_create_notes_for_run_single_item(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
    ) -> None:
        """Test creating a single coverage note for a run."""
        verification_item = VerificationItem(
            name="Test Verification",
            summary="Test summary",
            assessment_score=0.8,
            citation=Citation(excerpt="Test excerpt"),
            verification_type="policy",
        )

        note_ids = await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=[verification_item],
        )

        assert len(note_ids) == 1
        assert all(isinstance(note_id, uuid.UUID) for note_id in note_ids)

        # Verify the note was created
        notes = await coverage_notes_repository.get_notes_for_run(sample_run_id)
        assert len(notes) == 1
        assert notes[0].run_id == sample_run_id
        assert notes[0].claim_id == "test_claim"
        assert notes[0].original_content["name"] == "Test Verification"
        assert notes[0].original_content["summary"] == "Test summary"
        assert notes[0].original_content["assessment_score"] == 0.8

    async def test_create_notes_for_run_multiple_items(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
        sample_verification_items: list[VerificationItem],
    ) -> None:
        """Test creating multiple coverage notes for a run."""
        note_ids = await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=sample_verification_items,
        )

        assert len(note_ids) == 3
        assert all(isinstance(note_id, uuid.UUID) for note_id in note_ids)

        # Verify all notes were created
        notes = await coverage_notes_repository.get_notes_for_run(sample_run_id)
        assert len(notes) == 3

        # Verify content of each note
        note_names = [note.original_content["name"] for note in notes]
        expected_names = [
            "Policy Coverage Check",
            "Claim Validity Check",
            "General Assessment",
        ]
        assert all(name in note_names for name in expected_names)

    async def test_create_notes_for_run_empty_list(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
    ) -> None:
        """Test creating notes with empty verification items list."""
        note_ids = await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id, claim_id="test_claim", verification_items=[]
        )

        assert note_ids == []

        # Verify no notes were created
        notes = await coverage_notes_repository.get_notes_for_run(sample_run_id)
        assert len(notes) == 0

    async def test_upsert_feedback_single_note(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
    ) -> None:
        """Test upserting feedback for a single note."""
        # Create a note first
        verification_item = VerificationItem(
            name="Original Verification",
            summary="Original summary",
            assessment_score=0.5,
            citation=Citation(excerpt="Original excerpt"),
            verification_type="policy",
        )

        note_ids = await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=[verification_item],
        )

        # Create feedback
        modified_verification = VerificationItem(
            name="Modified Verification",
            summary="Modified summary",
            assessment_score=0.9,
            citation=Citation(excerpt="Modified excerpt"),
            verification_type="claim",
        )

        feedback = NoteFeedback(
            note_id=note_ids[0], modified_content=modified_verification
        )

        updated_note_ids = await coverage_notes_repository.upsert_feedback(
            feedback_list=[feedback], updated_by="<EMAIL>"
        )

        assert len(updated_note_ids) == 1
        assert updated_note_ids[0] == note_ids[0]

        # Verify the feedback was stored
        note = await coverage_notes_repository.get_note_by_id(note_ids[0])
        assert note is not None
        assert note.modified_content is not None
        assert note.modified_content["name"] == "Modified Verification"
        assert note.modified_content["summary"] == "Modified summary"
        assert note.modified_content["assessment_score"] == 0.9
        assert note.updated_by == "<EMAIL>"
        assert note.updated_at is not None

    async def test_upsert_feedback_multiple_notes(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
        sample_verification_items: list[VerificationItem],
    ) -> None:
        """Test upserting feedback for multiple notes."""
        # Create notes first
        note_ids = await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=sample_verification_items,
        )

        # Create feedback for first two notes
        feedback_list = [
            NoteFeedback(
                note_id=note_ids[0],
                modified_content=VerificationItem(
                    name="Modified Policy Check",
                    summary="Modified policy summary",
                    assessment_score=0.95,
                    citation=Citation(excerpt="Modified policy excerpt"),
                    verification_type="policy",
                ),
            ),
            NoteFeedback(
                note_id=note_ids[1],
                modified_content=VerificationItem(
                    name="Modified Claim Check",
                    summary="Modified claim summary",
                    assessment_score=0.85,
                    citation=Citation(excerpt="Modified claim excerpt"),
                    verification_type="claim",
                ),
            ),
        ]

        updated_note_ids = await coverage_notes_repository.upsert_feedback(
            feedback_list=feedback_list, updated_by="<EMAIL>"
        )

        assert len(updated_note_ids) == 2
        assert set(updated_note_ids) == {note_ids[0], note_ids[1]}

        # Verify feedback was stored for both notes
        note_1 = await coverage_notes_repository.get_note_by_id(note_ids[0])
        note_2 = await coverage_notes_repository.get_note_by_id(note_ids[1])
        note_3 = await coverage_notes_repository.get_note_by_id(note_ids[2])

        assert note_1.modified_content is not None
        assert note_2.modified_content is not None
        assert note_3.modified_content is None  # No feedback for third note

        assert note_1.modified_content["name"] == "Modified Policy Check"
        assert note_2.modified_content["name"] == "Modified Claim Check"

    async def test_upsert_feedback_nonexistent_note(
        self, coverage_notes_repository: CoverageNotesRepository
    ) -> None:
        """Test upserting feedback for a non-existent note."""
        nonexistent_note_id = uuid.uuid4()

        feedback = NoteFeedback(
            note_id=nonexistent_note_id,
            modified_content=VerificationItem(
                name="Modified Verification",
                summary="Modified summary",
                assessment_score=0.9,
                citation=Citation(excerpt="Modified excerpt"),
                verification_type="policy",
            ),
        )

        updated_note_ids = await coverage_notes_repository.upsert_feedback(
            feedback_list=[feedback], updated_by="<EMAIL>"
        )

        assert updated_note_ids == []

    async def test_upsert_feedback_empty_list(
        self, coverage_notes_repository: CoverageNotesRepository
    ) -> None:
        """Test upserting feedback with empty feedback list."""
        updated_note_ids = await coverage_notes_repository.upsert_feedback(
            feedback_list=[], updated_by="<EMAIL>"
        )

        assert updated_note_ids == []

    async def test_get_notes_for_run_existing_run(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
        sample_verification_items: list[VerificationItem],
    ) -> None:
        """Test getting notes for an existing run."""
        # Create notes first
        await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=sample_verification_items,
        )

        notes = await coverage_notes_repository.get_notes_for_run(sample_run_id)

        assert len(notes) == 3
        assert all(note.run_id == sample_run_id for note in notes)
        assert all(note.claim_id == "test_claim" for note in notes)

    async def test_get_notes_for_run_nonexistent_run(
        self, coverage_notes_repository: CoverageNotesRepository
    ) -> None:
        """Test getting notes for a non-existent run."""
        nonexistent_run_id = uuid.uuid4()

        notes = await coverage_notes_repository.get_notes_for_run(nonexistent_run_id)

        assert notes == []

    async def test_get_note_by_id_existing_note(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
    ) -> None:
        """Test getting a note by ID for an existing note."""
        # Create a note first
        verification_item = VerificationItem(
            name="Test Verification",
            summary="Test summary",
            assessment_score=0.8,
            citation=Citation(excerpt="Test excerpt"),
            verification_type="policy",
        )

        note_ids = await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=[verification_item],
        )

        note = await coverage_notes_repository.get_note_by_id(note_ids[0])

        assert note is not None
        assert note.note_id == note_ids[0]
        assert note.run_id == sample_run_id
        assert note.claim_id == "test_claim"
        assert note.original_content["name"] == "Test Verification"

    async def test_get_note_by_id_nonexistent_note(
        self, coverage_notes_repository: CoverageNotesRepository
    ) -> None:
        """Test getting a note by ID for a non-existent note."""
        nonexistent_note_id = uuid.uuid4()

        note = await coverage_notes_repository.get_note_by_id(nonexistent_note_id)

        assert note is None

    async def test_delete_notes_for_run_existing_run(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
        sample_verification_items: list[VerificationItem],
    ) -> None:
        """Test deleting notes for an existing run."""
        # Create notes first
        await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=sample_verification_items,
        )

        # Verify notes exist
        notes_before = await coverage_notes_repository.get_notes_for_run(sample_run_id)
        assert len(notes_before) == 3

        # Delete notes
        deleted_count = await coverage_notes_repository.delete_notes_for_run(
            sample_run_id
        )

        assert deleted_count == 3

        # Verify notes were deleted
        notes_after = await coverage_notes_repository.get_notes_for_run(sample_run_id)
        assert len(notes_after) == 0

    async def test_delete_notes_for_run_nonexistent_run(
        self, coverage_notes_repository: CoverageNotesRepository
    ) -> None:
        """Test deleting notes for a non-existent run."""
        nonexistent_run_id = uuid.uuid4()

        deleted_count = await coverage_notes_repository.delete_notes_for_run(
            nonexistent_run_id
        )

        assert deleted_count == 0

    async def test_delete_notes_for_run_empty_run(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
    ) -> None:
        """Test deleting notes for a run with no notes."""
        # Don't create any notes for this run
        deleted_count = await coverage_notes_repository.delete_notes_for_run(
            sample_run_id
        )

        assert deleted_count == 0

    async def test_note_content_serialization(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
    ) -> None:
        """Test that verification items are properly serialized and deserialized."""
        # Create a verification item with all possible fields
        verification_item = VerificationItem(
            name="Complex Verification",
            summary="Complex summary with unicode: 测试",
            assessment_score=0.867,
            citation=Citation(
                excerpt="Complex excerpt with special chars: !@#$%^&*()",
                filename="test.pdf",
                page="5",
                pages=[5, 6, 7],
                presigned_url="https://example.com/doc",
                bounding_box=[0.1, 0.2, 0.8, 0.9],
            ),
            verification_type="policy",
        )

        # Create note
        note_ids = await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=[verification_item],
        )

        # Retrieve and verify
        note = await coverage_notes_repository.get_note_by_id(note_ids[0])
        assert note is not None

        # Verify all fields are preserved
        content = note.original_content
        assert content["name"] == "Complex Verification"
        assert content["summary"] == "Complex summary with unicode: 测试"
        assert content["assessment_score"] == 0.867
        assert (
            content["citation"]["excerpt"]
            == "Complex excerpt with special chars: !@#$%^&*()"
        )
        assert content["citation"]["filename"] == "test.pdf"
        assert content["citation"]["page"] == "5"
        assert content["citation"]["pages"] == [5, 6, 7]
        assert content["citation"]["presigned_url"] == "https://example.com/doc"
        assert content["citation"]["bounding_box"] == [0.1, 0.2, 0.8, 0.9]
        assert content["verification_type"] == "policy"

    async def test_multiple_feedback_updates(
        self,
        coverage_notes_repository: CoverageNotesRepository,
        sample_run_id: uuid.UUID,
    ) -> None:
        """Test multiple feedback updates to the same note."""
        # Create a note first
        verification_item = VerificationItem(
            name="Original Verification",
            summary="Original summary",
            assessment_score=0.5,
            citation=Citation(excerpt="Original excerpt"),
            verification_type="policy",
        )

        note_ids = await coverage_notes_repository.create_notes_for_run(
            run_id=sample_run_id,
            claim_id="test_claim",
            verification_items=[verification_item],
        )

        # First feedback update
        feedback_1 = NoteFeedback(
            note_id=note_ids[0],
            modified_content=VerificationItem(
                name="First Update",
                summary="First update summary",
                assessment_score=0.7,
                citation=Citation(excerpt="First update excerpt"),
                verification_type="claim",
            ),
        )

        await coverage_notes_repository.upsert_feedback(
            feedback_list=[feedback_1], updated_by="<EMAIL>"
        )

        # Second feedback update
        feedback_2 = NoteFeedback(
            note_id=note_ids[0],
            modified_content=VerificationItem(
                name="Second Update",
                summary="Second update summary",
                assessment_score=0.9,
                citation=Citation(excerpt="Second update excerpt"),
                verification_type="general",
            ),
        )

        await coverage_notes_repository.upsert_feedback(
            feedback_list=[feedback_2], updated_by="<EMAIL>"
        )

        # Verify only the latest feedback is stored
        note = await coverage_notes_repository.get_note_by_id(note_ids[0])
        assert note is not None
        assert note.modified_content is not None
        assert note.modified_content["name"] == "Second Update"
        assert note.modified_content["summary"] == "Second update summary"
        assert note.modified_content["assessment_score"] == 0.9
        assert note.updated_by == "<EMAIL>"
