import asyncio
import datetime
import json
from typing import Any
from uuid import UUID, uuid4

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from claims_agent.db.orm.generated import LegacyCoverageDeterminations
from claims_agent.interfaces.legacy_models import (
    Citation,
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
    Document,
    VerificationItem,
)
from claims_agent.interfaces.repositories import (
    CoverageDeterminationError,
    CoverageDeterminationRequestRepositoryProtocol,
    CreateCoverageDeterminationRequest,
)

pytestmark = pytest.mark.asyncio


# A custom exception for testing more complex error payloads
class CustomComplexError(Exception):
    def __init__(self, message, error_code, details=None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}

    def __str__(self):
        return f"{self.message} (Code: {self.error_code}, Details: {self.details})"


async def _create_and_insert_request(
    repo: CoverageDeterminationRequestRepositoryProtocol,
    status: str = "requested",
    session_maker: async_sessionmaker[AsyncSession] | None = None,
) -> UUID:
    """Helper function to create and insert a new request, returning its ID."""
    request_time = datetime.datetime.now(datetime.timezone.utc)
    request_id = uuid4()
    request = CreateCoverageDeterminationRequest(
        request_id=request_id,
        claim_id="claim_123",
        workflow_id="workflow_abc-" + str(uuid4()),
        timestamp=request_time,
    )
    await repo.insert(request)

    # Manually update status for testing non-standard start states
    if status != "requested":
        if session_maker is None:
            raise ValueError("session_maker is required to update status")
        async with session_maker() as session:
            item = await session.get(LegacyCoverageDeterminations, request_id)
            item.status = status
            await session.commit()

    return request_id


def _build_realistic_response() -> LegacyCoverageDeterminationResponse:
    """Builds a realistic, nested CoverageDeterminationResponse for testing."""
    return LegacyCoverageDeterminationResponse(
        verifications=[
            VerificationItem(
                name="Driver Age Verification",
                assessment_score=1.0,
                summary="Driver meets age requirements.",
                citation=Citation(
                    excerpt="Driver must be at least 21 years old.",
                    document=Document(
                        policy_number="POLICY-001",
                        document_id=UUID("12345678-1234-5678-1234-************"),
                        filename="policy_terms.pdf",
                    ),
                    pages=[5],
                ),
                verification_type="policy",
            )
        ]
    )


class TestRequestLifecycle:
    async def test_success_lifecycle_stores_correct_data(
        self,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
    ):
        """
        Tests the full 'happy path' lifecycle of a request, ending in a 'succeeded'
        state, and verifies that the complex result object is stored correctly.
        """
        now = datetime.datetime.now(datetime.timezone.utc)
        request_id = await _create_and_insert_request(coverage_determination_repo)
        realistic_result = _build_realistic_response()

        await coverage_determination_repo.set_in_progress(request_id, now)
        await coverage_determination_repo.set_succeeded(
            request_id, now, realistic_result
        )

        item = await coverage_determination_repo.get(request_id)

        assert item.status == "succeeded"
        assert item.error is None
        assert isinstance(item.content, dict)
        # Dive into the nested structure to validate a few key fields
        verifications = item.content.get("verifications", [])
        assert len(verifications) == 1
        assert verifications[0]["name"] == "Driver Age Verification"
        assert verifications[0]["citation"]["excerpt"].startswith("Driver must be")
        assert verifications[0]["citation"]["document"]["policy_number"] == "POLICY-001"

    async def test_failure_lifecycle_stores_correct_data(
        self,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
    ):
        """
        Tests the full 'happy path' lifecycle of a request, ending in a 'failed'
        state, and verifies the error payload is stored correctly.
        """
        now = datetime.datetime.now(datetime.timezone.utc)
        request_id = await _create_and_insert_request(coverage_determination_repo)
        failure_error = ValueError("Something went wrong during processing.")

        await coverage_determination_repo.set_in_progress(request_id, now)
        await coverage_determination_repo.set_failed(
            request_id,
            now,
            CoverageDeterminationError(kind="ValueError", message=str(failure_error)),
        )

        item = await coverage_determination_repo.get(request_id)

        assert item.status == "failed"
        assert item.content is None
        assert item.error is not None
        error_dict: dict[str, Any] = json.loads(item.error)
        assert error_dict.get("kind") == "ValueError"
        assert error_dict.get("message") == str(failure_error)

    async def test_success_lifecycle_preserves_full_object(
        self,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
    ):
        """
        Tests that the full object is preserved during the serialization/deserialization
        round trip to the database.
        """
        now = datetime.datetime.now(datetime.timezone.utc)
        request_id = await _create_and_insert_request(coverage_determination_repo)
        original_result = _build_realistic_response()

        await coverage_determination_repo.set_in_progress(request_id, now)
        await coverage_determination_repo.set_succeeded(
            request_id, now, original_result
        )

        item = await coverage_determination_repo.get(request_id)
        assert item.content is not None

        # Re-create the Pydantic model from the stored JSON
        retrieved_result = LegacyCoverageDeterminationResponse.model_validate(
            item.content
        )

        assert retrieved_result == original_result

    async def test_success_with_empty_verifications_list(
        self,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
    ):
        """
        Tests that a successful lifecycle works correctly with a payload that
        contains an empty list.
        """
        now = datetime.datetime.now(datetime.timezone.utc)
        request_id = await _create_and_insert_request(coverage_determination_repo)
        empty_result = LegacyCoverageDeterminationResponse(verifications=[])

        await coverage_determination_repo.set_in_progress(request_id, now)
        await coverage_determination_repo.set_succeeded(request_id, now, empty_result)

        item = await coverage_determination_repo.get(request_id)

        assert item.status == "succeeded"
        assert item.content["verifications"] == []
        retrieved_result = LegacyCoverageDeterminationResponse.model_validate(
            item.content
        )
        assert retrieved_result == empty_result

    async def test_failure_with_complex_error(
        self,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
    ):
        """Tests failure lifecycle with a more complex, custom error type."""
        now = datetime.datetime.now(datetime.timezone.utc)
        request_id = await _create_and_insert_request(coverage_determination_repo)
        complex_error = CustomComplexError(
            "Data processing failed",
            error_code=5001,
            details={"source": "parser", "value": "xyz"},
        )
        await coverage_determination_repo.set_in_progress(request_id, now)
        await coverage_determination_repo.set_failed(
            request_id,
            now,
            CoverageDeterminationError(
                kind="CustomComplexError", message=str(complex_error)
            ),
        )

        item = await coverage_determination_repo.get(request_id)

        assert item.status == "failed"
        assert item.content is None
        error_dict: dict[str, Any] = json.loads(item.error)
        assert error_dict.get("kind") == "CustomComplexError"
        assert error_dict.get("message") == str(complex_error)


class TestErrorAndEdgeCases:
    async def test_get_nonexistent_request_raises_error(
        self,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
    ):
        """Tests that getting a request with a non-existent ID raises a ValueError."""
        non_existent_id = uuid4()
        with pytest.raises(
            ValueError,
            match=f"Coverage determination request with id {non_existent_id} not found",
        ):
            await coverage_determination_repo.get(non_existent_id)

    async def test_inserting_duplicate_id_raises_integrity_error(
        self,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
    ):
        """Tests that inserting a request with a pre-existing ID raises an error."""
        request_id = uuid4()
        request_time = datetime.datetime.now(datetime.timezone.utc)
        request = CreateCoverageDeterminationRequest(
            request_id=request_id,
            claim_id="claim_123",
            workflow_id="workflow_abc",
            timestamp=request_time,
        )

        # Insert the first time, should succeed
        await coverage_determination_repo.insert(request)

        # Insert the second time, should fail
        with pytest.raises(IntegrityError):
            await coverage_determination_repo.insert(request)

    async def test_concurrency_on_state_transition(
        self,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
    ):
        """
        Tests that concurrent attempts to perform the same state transition result in
        only one success and subsequent failures. This prevents race conditions.
        """
        request_id = await _create_and_insert_request(coverage_determination_repo)

        # Simulate two concurrent calls trying to set the request to 'in_progress'
        now = datetime.datetime.now(datetime.timezone.utc)
        task1 = coverage_determination_repo.set_in_progress(request_id, now)
        task2 = coverage_determination_repo.set_in_progress(request_id, now)

        results = await asyncio.gather(task1, task2, return_exceptions=True)

        # One of the tasks should succeed (return None), and one should fail
        successes = [r for r in results if r is None]
        failures = [r for r in results if isinstance(r, Exception)]

        assert len(successes) == 1
        assert len(failures) == 1
        assert isinstance(failures[0], ValueError)
        assert "Invalid state transition" in str(failures[0])

        # Verify the final state is correct
        item = await coverage_determination_repo.get(request_id)
        assert item.status == "in_progress"
