"""Unit tests for the MCPAgent class resource lifecycle methods."""

from unittest.mock import <PERSON><PERSON><PERSON>

import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from tests.agents.conftest import get_test_mcp_agent


class TestMCPAgentInitializeResources:
    """Tests for the _initialize_resources method."""

    async def test_initialize_resources_logs_messages(
        self,
        caplog: pytest.LogCaptureFixture,
        mocker: <PERSON><PERSON><PERSON><PERSON><PERSON>,
        mock_mcp_client_manager: MagicMock,
        mock_mcp_tool_service: MagicMock,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test that _initialize_resources logs appropriate messages."""
        agent = get_test_mcp_agent(
            mock_mcp_client_manager,
            mock_mcp_tool_service,
            mock_agent_executor_factory,
        )
        with caplog.at_level("INFO"):
            await agent._initialize_resources()
        assert "Initializing MCP Agent resources" in caplog.text
        assert "MCP Agent resources initialized successfully" in caplog.text


class TestMCPAgentCleanupResources:
    """Tests for the _cleanup_resources method."""

    async def test_cleanup_resources_logs_message(
        self,
        caplog: pytest.LogCaptureFixture,
        mocker: <PERSON><PERSON><PERSON><PERSON><PERSON>,
        mock_mcp_client_manager: MagicMock,
        mock_mcp_tool_service: MagicMock,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test that _cleanup_resources logs appropriate messages."""
        agent = get_test_mcp_agent(
            mock_mcp_client_manager,
            mock_mcp_tool_service,
            mock_agent_executor_factory,
        )
        with caplog.at_level("DEBUG"):
            await agent._cleanup_resources()
        assert "MCPAgent._cleanup_resources called" in caplog.text
