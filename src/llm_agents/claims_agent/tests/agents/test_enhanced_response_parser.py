"""Unit tests for enhanced LLM response parsing utilities."""

import json
from typing import Any

import pytest

from claims_agent.agents.response_parser import (
    ResponseParsingError,
    _create_default_response,
    _try_extract_json_schema,
    extract_verification_json_from_llm_response,
    parse_coverage_determination_response,
    parse_verification_items,
)

from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
)


# --- Test Data ---
VALID_VERIFICATION_ITEM_DICT_MINIMAL: dict[str, Any] = {
    "name": "Test Verification Minimal",
    "assessment_score": 0.5,
    "summary": "Minimal valid summary.",
}

VALID_VERIFICATION_ITEM_DICT_FULL: dict[str, Any] = {
    "name": "Test Verification Full",
    "assessment_score": 1.0,
    "summary": "Full valid summary with all details.",
    "citation": {
        "excerpt": "Relevant text.",
        "pages": [1],
        "document": {
            "policy_number": "TEST123",
            "document_id": "12345678-1234-5678-1234-567812345678",
            "filename": "doc.pdf",
        },
    },
}

INVALID_VERIFICATION_ITEM_DICT_MISSING_NAME: dict[str, Any] = {
    "assessment_score": 0.5,
    "summary": "Missing name.",
}

REPAIRABLE_VERIFICATION_ITEM: dict[str, Any] = {
    "score": 0.5,  # Non-standard name
    "summary": "This item has field name issues but can be repaired.",
    "name": "Repairable Item",
}

VALID_JSON_PAYLOAD_STR = json.dumps(
    {"verifications": [VALID_VERIFICATION_ITEM_DICT_FULL]}
)
VALID_JSON_PAYLOAD_EMPTY_VERIFICATIONS_STR = json.dumps({"verifications": []})


# --- Tests for _try_extract_json_schema (new method) ---
class TestJsonSchemaRepair:
    """Tests for the new _try_extract_json_schema method."""

    def test_repair_single_quotes(self) -> None:
        """Test repairing single quotes in JSON string."""
        malformed_json = "{'name': 'value', 'list': [1, 2, 3]}"
        repaired = _try_extract_json_schema(malformed_json)
        assert repaired is not None
        parsed = json.loads(repaired)
        assert parsed["name"] == "value"
        assert parsed["list"] == [1, 2, 3]

    def test_repair_trailing_commas(self) -> None:
        """Test repairing trailing commas in JSON objects and arrays."""
        malformed_json = """
        {
            "array": [1, 2, 3,],
            "object": {"key": "value",}
        }
        """
        repaired = _try_extract_json_schema(malformed_json)
        assert repaired is not None
        parsed = json.loads(repaired)
        assert parsed["array"] == [1, 2, 3]
        assert parsed["object"]["key"] == "value"

    def test_repair_unquoted_property_names(self) -> None:
        """Test repairing unquoted property names in JSON."""
        malformed_json = "{name: 'John', age: 30}"
        repaired = _try_extract_json_schema(malformed_json)
        assert repaired is not None
        parsed = json.loads(repaired)
        assert parsed["name"] == "John"
        assert parsed["age"] == 30

    def test_repair_backticks(self) -> None:
        """Test repairing backticks instead of quotes."""
        malformed_json = "{name: `John`, details: {occupation: `developer`}}"
        repaired = _try_extract_json_schema(malformed_json)
        assert repaired is not None
        parsed = json.loads(repaired)
        assert parsed["name"] == "John"
        assert parsed["details"]["occupation"] == "developer"

    def test_repair_comments(self) -> None:
        """Test removing comments from JSON."""
        malformed_json = """
        {
            "name": "John", // This is a comment
            /* Multi-line
               comment */
            "age": 30
        }
        """
        repaired = _try_extract_json_schema(malformed_json)
        assert repaired is not None
        parsed = json.loads(repaired)
        assert parsed["name"] == "John"
        assert parsed["age"] == 30

    def test_already_valid_json(self) -> None:
        """Test that already valid JSON is returned as-is."""
        valid_json = '{"name": "John", "age": 30}'
        repaired = _try_extract_json_schema(valid_json)
        assert repaired == valid_json

    def test_handle_empty_input(self) -> None:
        """Test handling of empty or non-JSON input."""
        assert _try_extract_json_schema("") is None
        assert _try_extract_json_schema("Not JSON at all") is None

    def test_repair_multiple_issues(self) -> None:
        """Test repairing multiple issues in the same JSON string."""
        malformed_json = """
        {
            name: 'John',
            skills: ['coding', 'debugging',],
            details: {
                occupation: `developer`, // Position
                experience: 5,
            }
        }
        """
        repaired = _try_extract_json_schema(malformed_json)
        assert repaired is not None
        parsed = json.loads(repaired)
        assert parsed["name"] == "John"
        assert parsed["skills"] == ["coding", "debugging"]
        assert parsed["details"]["occupation"] == "developer"
        assert parsed["details"]["experience"] == 5


# --- Tests for enhanced extract_verification_json_from_llm_response ---
class TestEnhancedExtractVerificationJson:
    """Tests for enhanced extract_verification_json_from_llm_response."""

    def test_extract_with_indented_code_block(self) -> None:
        """Test extraction from indented code blocks."""
        llm_output = f"""
Some regular text

    {VALID_JSON_PAYLOAD_STR.replace('"', "'")}
    
More regular text.
        """
        extracted = extract_verification_json_from_llm_response(llm_output)
        assert extracted is not None
        # Verify the content is valid JSON
        parsed = json.loads(extracted)
        assert "verifications" in parsed
        assert len(parsed["verifications"]) == 1

    def test_smart_brace_extraction(self) -> None:
        """Test smart brace extraction with nested objects."""
        nested_json = """
        Text before {
            "outer": {
                "inner1": {"key": "value"},
                "inner2": [1, 2, {"nested": true}]
            },
            "verifications": [
                {"name": "Test", "assessment_score": 1.0, "summary": "Test summary"}
            ]
        } text after
        """
        extracted = extract_verification_json_from_llm_response(nested_json)
        assert extracted is not None
        parsed = json.loads(extracted)
        assert "verifications" in parsed
        assert "outer" in parsed
        assert parsed["outer"]["inner1"]["key"] == "value"

    def test_multiple_json_objects_smart_extraction(self) -> None:
        """Test extraction selects the longest/most complete JSON object."""
        llm_output = """
        First incomplete: {"test": "value"
        
        Complete object: {"verifications": [{"name": "Complete", "assessment_score": 1.0, "summary": "Complete test"}]}
        
        Another incomplete: {"foo": "bar",
        """
        extracted = extract_verification_json_from_llm_response(llm_output)
        assert extracted is not None
        parsed = json.loads(extracted)
        assert "verifications" in parsed
        assert parsed["verifications"][0]["name"] == "Complete"

    def test_repair_json_schema_extraction(self) -> None:
        """Test JSON schema repair during extraction."""
        malformed_json = """
        The LLM response with malformed JSON:
        {
            verifications: [
                {
                    name: 'Schema Repair Test',
                    assessment_score: 1.0,
                    summary: 'Testing JSON schema repair',
                }
            ]
        }
        """
        extracted = extract_verification_json_from_llm_response(
            malformed_json, use_json_repair=True
        )
        assert extracted is not None
        parsed = json.loads(extracted)
        assert "verifications" in parsed
        assert parsed["verifications"][0]["name"] == "Schema Repair Test"

    def test_use_json_repair_flag(self) -> None:
        """Test that use_json_repair flag controls repair behavior."""
        malformed_json = "{'verifications': []}"
        # With repair
        extracted_with_repair = extract_verification_json_from_llm_response(
            malformed_json, use_json_repair=True
        )
        assert extracted_with_repair is not None

        # Without repair
        extracted_without_repair = extract_verification_json_from_llm_response(
            malformed_json,
            use_fallback_raw_json=False,
            use_brace_extraction=False,
            use_markdown_extraction=False,
            use_json_repair=False,
        )
        assert extracted_without_repair is None

    def test_extraction_strategies_order(self) -> None:
        """Test that extraction strategies are tried in the correct order."""
        # Create a response with multiple valid extraction methods
        # 1. Valid markdown JSON
        # 2. Valid raw JSON in the text (different content)
        # 3. Valid brace-extractable JSON (different content)
        markdown_content = {
            "verifications": [
                {
                    "name": "Markdown",
                    "summary": "From markdown",
                    "assessment_score": 1.0,
                }
            ]
        }
        raw_content = {
            "verifications": [
                {"name": "Raw", "summary": "From raw", "assessment_score": 1.0}
            ]
        }
        brace_content = {
            "verifications": [
                {"name": "Brace", "summary": "From brace", "assessment_score": 1.0}
            ]
        }

        llm_output = f"""
        ```json
        {json.dumps(markdown_content)}
        ```
        
        Some text {json.dumps(brace_content)} more text.
        
        {json.dumps(raw_content)}
        """

        # Default behavior should extract from markdown first
        extracted = extract_verification_json_from_llm_response(llm_output)
        assert extracted is not None
        parsed = json.loads(extracted)
        assert parsed["verifications"][0]["name"] == "Markdown"

        # Disable markdown extraction to get brace content
        extracted_brace = extract_verification_json_from_llm_response(
            llm_output, use_markdown_extraction=False, use_fallback_raw_json=False
        )
        assert extracted_brace is not None
        parsed_brace = json.loads(extracted_brace)
        assert parsed_brace["verifications"][0]["name"] == "Brace"


# --- Tests for enhanced parse_verification_items ---
class TestEnhancedParseVerificationItems:
    """Tests for enhanced parse_verification_items."""

    def test_partial_success_handling(self) -> None:
        """Test partial success when some items are valid and some are not."""
        data = {
            "verifications": [
                VALID_VERIFICATION_ITEM_DICT_MINIMAL,
                INVALID_VERIFICATION_ITEM_DICT_MISSING_NAME,
                VALID_VERIFICATION_ITEM_DICT_FULL,
            ]
        }

        # With partial_success=True, should return valid items
        # Note: Our improved repair function can now successfully repair the missing name item
        items = parse_verification_items(data, partial_success=True)
        assert len(items) == 3  # All items can be repaired now
        assert items[0].name == VALID_VERIFICATION_ITEM_DICT_MINIMAL["name"]
        assert items[1].name == "Missing name"  # Derived from summary
        assert items[2].name == VALID_VERIFICATION_ITEM_DICT_FULL["name"]

        # With partial_success=False, should still succeed since all items can be repaired
        items_strict = parse_verification_items(data, partial_success=False)
        assert len(items_strict) == 3

    def test_repairing_field_names(self) -> None:
        """Test repair of common field naming issues."""
        data = {
            "verifications": [
                {
                    "score": 1.0,
                    "name": "Renamed Field",
                    "summary": "Using score instead of assessment_score",
                },
                {
                    "assessment_status": 0.0,
                    "name": "Another Field",
                    "summary": "Using status",
                },
                {
                    "assessment_score": None,
                    "name": "Control",
                    "summary": "Already correct field names",
                },
            ]
        }

        items = parse_verification_items(data, partial_success=True)
        assert len(items) == 3
        # All items should be successfully repaired
        assert items[0].assessment_score == 1.0
        assert items[1].assessment_score == 0.0
        assert items[2].assessment_score is None

    def test_verification_type_repair(self) -> None:
        """Test repair of verification_type field."""
        data = {
            "verifications": [
                {
                    "name": "Type Repair 1",
                    "summary": "Test",
                    "assessment_score": 1.0,
                    "verification_type": "pol",
                },
                {
                    "name": "Type Repair 2",
                    "summary": "Test",
                    "assessment_score": 1.0,
                    "verification_type": "claims",
                },
                {
                    "name": "Type Repair 3",
                    "summary": "Test",
                    "assessment_score": 1.0,
                    "verification_type": "info",
                },
                {
                    "name": "Type Repair 4",
                    "summary": "Test",
                    "assessment_score": 1.0,
                    "verificationType": "general",
                },
            ]
        }

        items = parse_verification_items(data, partial_success=True)
        assert len(items) == 4
        assert items[0].verification_type == "policy"
        assert items[1].verification_type == "claim"
        assert items[2].verification_type == "general"
        assert items[3].verification_type == "general"

    def test_citation_field_repair(self) -> None:
        """Test repair of citation field issues."""
        data = {
            "verifications": [
                {
                    "name": "Citation String",
                    "summary": "Citation as string",
                    "assessment_score": 1.0,
                    "citation": "This is the excerpt text",
                },
                {
                    "name": "Citation Fields",
                    "summary": "Citation with field issues",
                    "assessment_score": 1.0,
                    "citation": {
                        "pageNumber": "5",
                        "excerpt": "Text from document",
                        "document": {
                            "policy_number": "TESTDOC123",
                            "document_id": "12345678-1234-5678-9abc-456789abcdef",
                            "filename": "policy_terms.pdf",
                        },
                    },
                },
                {
                    "name": "Citation With List Page",
                    "summary": "Citation page is already a list",
                    "assessment_score": 1.0,
                    "citation": {
                        "excerpt": "Excerpt from page list",
                        "page": [10, 12],
                        "document": {
                            "policy_number": "TESTDOC789",
                            "document_id": "87654321-4321-8765-bcde-fedcba987654",
                            "filename": "endorsement.pdf",
                        },
                    },
                },
            ]
        }
        response = parse_coverage_determination_response(json.dumps(data))
        assert len(response.verifications) == 3

        # First item has citation as string, so page will be None after parsing
        assert response.verifications[0].citation is not None
        assert response.verifications[0].citation.page is None

        # Second item: pageNumber should be repaired to pages: [5] and backwards compatible page: "5"
        assert response.verifications[1].citation is not None
        assert response.verifications[1].citation.pages == [5]  # New field
        # Deprecated field for backwards compatibility
        assert response.verifications[1].citation.page == "5"
        assert isinstance(response.verifications[1].citation.pages, list)
        assert isinstance(response.verifications[1].citation.page, str)
        assert all(isinstance(p, int) for p in response.verifications[1].citation.pages)

        # Third item: page is already [10, 12], should convert to pages: [10, 12] and page: "10"
        assert response.verifications[2].citation is not None
        assert response.verifications[2].citation.pages == [10, 12]  # New field
        # Deprecated field (first page)
        assert response.verifications[2].citation.page == "10"
        assert isinstance(response.verifications[2].citation.pages, list)
        assert isinstance(response.verifications[2].citation.page, str)
        assert all(isinstance(p, int) for p in response.verifications[2].citation.pages)

    def test_assessment_score_type_conversion(self) -> None:
        """Test conversion of assessment_score types."""
        data = {
            "verifications": [
                {
                    "name": "Score String 1",
                    "summary": "Test",
                    "assessment_score": "1.0",
                },
                {
                    "name": "Score String 2",
                    "summary": "Test",
                    "assessment_score": "0.0",
                },
                {
                    "name": "Score Text True",
                    "summary": "Test",
                    "assessment_score": "verified",
                },
                {
                    "name": "Score Text False",
                    "summary": "Test",
                    "assessment_score": "issue",
                },
                {"name": "Score N/A", "summary": "Test", "assessment_score": "n/a"},
            ]
        }

        items = parse_verification_items(data, partial_success=True)
        assert len(items) == 5
        assert items[0].assessment_score == 1.0
        assert items[1].assessment_score == 0.0
        assert items[2].assessment_score == 1.0
        assert items[3].assessment_score == 0.0
        assert items[4].assessment_score is None

    def test_multi_attempt_repair(self) -> None:
        """Test that repair attempts escalate appropriately."""
        # This needs multiple repair passes
        data = {
            "verifications": [
                {
                    "name": "First attempt",
                    "score": "verified",  # Needs name and type repair
                    "summary": "First attempt repair",
                },
                {
                    # Missing name, needs to be derived from summary
                    "summary": "This summary will be used to derive a name. This is more text.",
                    "assessment_score": 0.5,
                },
            ]
        }

        items = parse_verification_items(
            data, partial_success=True, max_repair_attempts=2
        )
        assert len(items) == 2
        assert items[0].assessment_score == 1.0  # "verified" -> 1.0
        assert items[1].name == "This summary will be used to derive a name..."


# --- Tests for _create_default_response ---
class TestCreateDefaultResponse:
    """Tests for _create_default_response."""

    def test_default_response_structure(self) -> None:
        """Test that default response has correct structure."""
        response = _create_default_response()
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1

        # Verify the error item
        error_item = response.verifications[0]
        assert error_item.name == "Response Parsing Error"
        assert error_item.assessment_score is None
        assert "could not be parsed" in error_item.summary
        assert error_item.verification_type == "general"
        assert error_item.citation is None


# --- Tests for enhanced parse_coverage_determination_response ---
class TestEnhancedCoverageDeterminationResponse:
    """Tests for enhanced parse_coverage_determination_response."""

    def test_fallback_response_on_failure(self) -> None:
        """Test that fallback_on_failure returns default response instead of raising error."""
        invalid_output = "This is not JSON at all"

        # Without fallback, should raise error
        with pytest.raises(ResponseParsingError):
            parse_coverage_determination_response(
                invalid_output, unsafe_allow_fallback=False
            )

        # With fallback, should return default response
        response = parse_coverage_determination_response(
            invalid_output, unsafe_allow_fallback=True
        )
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1
        assert response.verifications[0].name == "Response Parsing Error"

    def test_partial_success_parsing(self) -> None:
        """Test partial success parsing when some verification items are valid."""
        mixed_items = {
            "verifications": [
                VALID_VERIFICATION_ITEM_DICT_MINIMAL,
                INVALID_VERIFICATION_ITEM_DICT_MISSING_NAME,
                VALID_VERIFICATION_ITEM_DICT_FULL,
            ]
        }

        llm_output = json.dumps(mixed_items)

        # With partial_success=True, should return valid items
        # Note: Our improved repair function can now successfully repair the missing name item
        response = parse_coverage_determination_response(
            llm_output, partial_success=True
        )
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        # All items can be repaired now
        assert len(response.verifications) == 3
        assert (
            response.verifications[0].name
            == VALID_VERIFICATION_ITEM_DICT_MINIMAL["name"]
        )
        # Derived from summary
        assert response.verifications[1].name == "Missing name"
        assert (
            response.verifications[2].name == VALID_VERIFICATION_ITEM_DICT_FULL["name"]
        )

    def test_json_schema_repair_strategy(self) -> None:
        """Test that JSON schema repair strategy works when others fail."""
        # JSON with single quotes that would fail normal parsing
        malformed_json = """
        {
            'verifications': [
                {
                    'name': 'Schema Repair',
                    'summary': 'This uses single quotes', 
                    'assessment_score': 1.0
                }
            ]
        }
        """

        # Should successfully parse with repair
        response = parse_coverage_determination_response(malformed_json)
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1
        assert response.verifications[0].name == "Schema Repair"

    def test_empty_or_short_input(self) -> None:
        """Test handling of empty or very short input."""
        # Empty string
        with pytest.raises(ResponseParsingError):
            parse_coverage_determination_response("")

        # With fallback
        response = parse_coverage_determination_response("", unsafe_allow_fallback=True)
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1

        # Very short string
        with pytest.raises(ResponseParsingError):
            parse_coverage_determination_response("abc")

        # With fallback
        response = parse_coverage_determination_response(
            "abc", unsafe_allow_fallback=True
        )
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1

    def test_extraction_strategy_chaining(self) -> None:
        """Test that extraction strategies are tried in sequence until one succeeds."""
        # Create a string that will fail direct parsing and markdown extraction but succeed with brace extraction
        llm_output = """
        This is not valid JSON.
        
        But it contains valid JSON here: {"verifications": [{"name": "Brace Strategy", "summary": "Found by brace extraction", "assessment_score": 0.5}]}
        
        And more text here.
        """

        response = parse_coverage_determination_response(llm_output)
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1
        assert response.verifications[0].name == "Brace Strategy"
