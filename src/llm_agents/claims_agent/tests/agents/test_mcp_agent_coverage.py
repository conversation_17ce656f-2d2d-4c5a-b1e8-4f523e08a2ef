"""Unit tests for the MCPAgent class's determine_coverage method."""

from datetime import UTC, datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from langchain.agents import Agent<PERSON>xecutor
from pytest_mock import Mock<PERSON>ixture

from claims_agent.agents.mcp_agent import MCPAgent
from claims_agent.agents.mcp_client_manager import (
    MCPClientConfigurationError,
    ToolFetchingError,
)
from claims_agent.agents.mcp_tool_service import MCPToolService
from claims_agent.agents.response_parser import ResponseParsingError
from claims_agent.api.errors import DataExtractionError
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
)
from .conftest import get_test_mcp_agent


@pytest.mark.asyncio
@pytest.mark.usefixtures(
    "mock_get_coverage_determination_prompt_func",
    "mock_log_intermediate_steps_func",
    "mock_parse_coverage_determination_response_func",
    "mock_create_agent_prompt_template_func",
    "mock_trace_tools_execution_func",
)
class TestMCPAgentDetermineCoverage:
    """Tests for MCPAgent.determine_coverage method."""

    @pytest.fixture
    def agent_instance(
        self,
        mock_mcp_client_manager: MagicMock,
        mock_mcp_tool_service: MCPToolService,
        mock_agent_executor_factory: MagicMock,
    ) -> MCPAgent:
        """Provides a configured MCPAgent instance for tests in this class."""
        return get_test_mcp_agent(
            mock_mcp_client_manager,
            mock_mcp_tool_service,
            mock_agent_executor_factory,
        )

    async def test_determine_coverage_success(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_get_coverage_determination_prompt_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
        mock_parse_coverage_determination_response_func: MagicMock,
        mock_trace_tools_execution_func: MagicMock,
    ) -> None:
        """Test successful coverage determination."""
        claim_id = "CLAIM-001"
        auth_token = "Bearer token_det_1"  # noqa: S105
        # Use a fixed date to avoid test flakiness around midnight
        fixed_as_of_date = datetime(2024, 1, 15, 10, 0, 0, tzinfo=UTC)
        fixed_as_of_date_str = fixed_as_of_date.strftime("%Y-%m-%d")

        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded_det_1")
        mock_tools = [MagicMock(name="det_tool1")]
        # Expected result from agent_executor.ainvoke
        invoke_result = {
            "output": "Coverage JSON data",
            "intermediate_steps": ["det_step1"],
        }
        # Mock the CoverageDeterminationResponse object that the parser will return
        expected_parsed_response = LegacyCoverageDeterminationResponse(
            verifications=[]
        )  # Simplified
        mock_parse_coverage_determination_response_func.return_value = (
            expected_parsed_response
        )

        # Mock the prompt function to ensure the fixed date is used
        mock_prompt_content = (
            f"Mocked prompt for {claim_id} as of {fixed_as_of_date_str}"
        )
        mock_get_coverage_determination_prompt_func.return_value = mock_prompt_content

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mock_get_tools_service: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )

        # Mock AgentExecutor and its ainvoke method
        mock_executor = AsyncMock(spec=AgentExecutor)
        mock_executor.ainvoke.return_value = invoke_result  # Configure ainvoke
        mock_executor.tools = mock_tools  # Add tools attribute for logging
        mock_create_exec_active = mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            return_value=mock_executor,
        )

        response = await agent_instance.determine_coverage(
            claim_id, authorization=auth_token, as_of_date=fixed_as_of_date
        )

        assert response == expected_parsed_response
        mock_get_coverage_determination_prompt_func.assert_called_once_with(
            claim_id=claim_id, as_of_date=fixed_as_of_date_str
        )
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools_service.assert_awaited_once_with(mock_mcp_client_yielded)
        mock_create_exec_active.assert_awaited_once_with(
            mcp_client=mock_mcp_client_yielded, tools=mock_tools
        )
        mock_trace_tools_execution_func.assert_called_once_with(mock_executor)

        expected_agent_input = {
            "input": mock_prompt_content,
            "chat_history": [],
        }
        mock_executor.ainvoke.assert_awaited_once_with(expected_agent_input)
        mock_log_intermediate_steps_func.assert_called_once_with(invoke_result)
        mock_parse_coverage_determination_response_func.assert_called_once_with(
            "Coverage JSON data"
        )

    async def test_determine_coverage_tool_fetching_error(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_get_coverage_determination_prompt_func: MagicMock,
    ) -> None:
        """Test error propagation if MCPToolService.get_mcp_tools fails."""
        claim_id = "CLAIM-002-tf-error"
        auth_token = "Bearer token_det_2_tf_error"  # noqa: S105
        error_to_raise = ToolFetchingError(
            "Tool fetching failed for determine_coverage"
        )
        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded_det_tf_error")

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mock_get_tools_service: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            side_effect=error_to_raise,
        )

        # These should not be called
        mock_create_exec_active = mocker.patch.object(
            agent_instance, "_create_agent_executor_with_active_client"
        )

        with pytest.raises(ToolFetchingError) as excinfo:
            await agent_instance.determine_coverage(claim_id, authorization=auth_token)

        assert excinfo.value == error_to_raise
        mock_get_coverage_determination_prompt_func.assert_called_once()
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools_service.assert_awaited_once_with(mock_mcp_client_yielded)
        mock_create_exec_active.assert_not_called()

    async def test_determine_coverage_executor_creation_active_error(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_get_coverage_determination_prompt_func: MagicMock,
    ) -> None:
        """Test error propagation if _create_agent_executor_with_active_client fails."""
        claim_id = "CLAIM-002-ec-error"
        auth_token = "Bearer token_det_2_ec_error"  # noqa: S105
        error_to_raise = ValueError("Executor creation active failed for det")
        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded_ec_error")
        mock_tools = [MagicMock(name="tool1")]

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )

        mock_get_tools_service: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )
        mock_create_exec_active = mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            side_effect=error_to_raise,
        )

        with pytest.raises(
            ValueError, match="Executor creation active failed for det"
        ) as excinfo:
            await agent_instance.determine_coverage(claim_id, authorization=auth_token)

        assert excinfo.value == error_to_raise
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools_service.assert_awaited_once_with(mock_mcp_client_yielded)
        mock_create_exec_active.assert_awaited_once_with(
            mcp_client=mock_mcp_client_yielded, tools=mock_tools
        )

    async def test_determine_coverage_client_config_error(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_get_coverage_determination_prompt_func: MagicMock,
    ) -> None:
        """Test error propagation if MCPClientManager.get_client raises config error."""
        claim_id = "CLAIM-002-cc-error"
        auth_token = "Bearer token_det_cc_error"  # noqa: S105
        error_to_raise = MCPClientConfigurationError("Client manager config failed")

        # Mock get_client to raise the error directly when its context is entered
        mock_async_cm_client = AsyncMock()
        # Error on entering context
        mock_async_cm_client.__aenter__.side_effect = error_to_raise
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )

        # These should not be called if get_client fails
        mock_get_tools: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service, "get_mcp_tools"
        )
        mock_create_exec_active = mocker.patch.object(
            agent_instance, "_create_agent_executor_with_active_client"
        )

        with pytest.raises(MCPClientConfigurationError) as excinfo:
            await agent_instance.determine_coverage(claim_id, authorization=auth_token)

        assert excinfo.value == error_to_raise
        mock_get_coverage_determination_prompt_func.assert_called_once()
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools.assert_not_called()
        mock_create_exec_active.assert_not_called()

    async def test_determine_coverage_ainvoke_error(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_get_coverage_determination_prompt_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
        mock_parse_coverage_determination_response_func: MagicMock,
        mock_trace_tools_execution_func: MagicMock,
    ) -> None:
        """Test error propagation if agent_executor.ainvoke fails."""
        claim_id = "CLAIM-003"
        auth_token = "Bearer token_det_3"  # noqa: S105
        fixed_as_of_date = datetime(2024, 1, 15, 10, 30, 0, tzinfo=UTC)
        fixed_as_of_date_str = fixed_as_of_date.strftime("%Y-%m-%d")
        mock_prompt_content = (
            f"Mocked prompt for {claim_id} as of {fixed_as_of_date_str}"
        )
        error_to_raise = TimeoutError("Ainvoke timed out for determine_coverage")

        mock_mcp_client_yielded = AsyncMock(
            name="mock_mcp_client_yielded_ainvoke_error"
        )
        mock_tools = [MagicMock(name="det_tool_ainvoke_err")]

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )
        mock_get_coverage_determination_prompt_func.return_value = mock_prompt_content

        mock_executor = AsyncMock(spec=AgentExecutor)
        mock_executor.ainvoke.side_effect = error_to_raise  # ainvoke itself raises
        mock_executor.tools = mock_tools  # Add tools attribute for logging
        mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            return_value=mock_executor,
        )

        with pytest.raises(TimeoutError) as excinfo:
            await agent_instance.determine_coverage(
                claim_id, authorization=auth_token, as_of_date=fixed_as_of_date
            )

        assert excinfo.value == error_to_raise
        mock_get_coverage_determination_prompt_func.assert_called_once_with(
            claim_id=claim_id, as_of_date=fixed_as_of_date_str
        )
        mock_trace_tools_execution_func.assert_called_once_with(mock_executor)
        expected_agent_input = {"input": mock_prompt_content, "chat_history": []}
        mock_executor.ainvoke.assert_awaited_once_with(expected_agent_input)
        mock_log_intermediate_steps_func.assert_not_called()
        mock_parse_coverage_determination_response_func.assert_not_called()

    async def test_determine_coverage_missing_output(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_get_coverage_determination_prompt_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
        mock_parse_coverage_determination_response_func: MagicMock,
        mock_trace_tools_execution_func: MagicMock,
    ) -> None:
        """Test ValueError if agent response has no 'output' field."""
        claim_id = "CLAIM-004"
        auth_token = "Bearer token_det_4"  # noqa: S105
        fixed_as_of_date = datetime(2024, 1, 16, 11, 0, 0, tzinfo=UTC)
        fixed_as_of_date_str = fixed_as_of_date.strftime("%Y-%m-%d")

        mock_mcp_client_yielded = AsyncMock(
            name="mock_mcp_client_yielded_missing_output"
        )
        mock_tools = [MagicMock(name="det_tool_missing_out")]
        mock_prompt_content = (
            f"Determine coverage for {claim_id} as of {fixed_as_of_date_str}"
        )
        invoke_result = {
            "intermediate_steps": ["det_step_missing_out"]
        }  # Missing 'output'

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )
        mock_get_coverage_determination_prompt_func.return_value = mock_prompt_content

        mock_executor = AsyncMock(spec=AgentExecutor)
        mock_executor.ainvoke.return_value = invoke_result
        mock_executor.tools = mock_tools  # Add tools attribute for logging
        mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            return_value=mock_executor,
        )

        with pytest.raises(ValueError, match="Agent response missing 'output' field"):
            await agent_instance.determine_coverage(
                claim_id, authorization=auth_token, as_of_date=fixed_as_of_date
            )

        mock_trace_tools_execution_func.assert_called_once_with(mock_executor)
        expected_agent_input = {"input": mock_prompt_content, "chat_history": []}
        mock_executor.ainvoke.assert_awaited_once_with(expected_agent_input)
        mock_log_intermediate_steps_func.assert_called_once_with(invoke_result)
        mock_parse_coverage_determination_response_func.assert_not_called()

    async def test_determine_coverage_parsing_error(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_get_coverage_determination_prompt_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
        mock_parse_coverage_determination_response_func: MagicMock,
        mock_trace_tools_execution_func: MagicMock,
    ) -> None:
        """Test DataExtractionError if parsing the LLM output fails."""
        claim_id = "CLAIM-005"
        auth_token = "Bearer token_det_5"  # noqa: S105
        fixed_as_of_date = datetime(2024, 1, 17, 12, 15, 0, tzinfo=UTC)
        fixed_as_of_date_str = fixed_as_of_date.strftime("%Y-%m-%d")

        # This is what mock_get_coverage_determination_prompt_func will return
        actual_prompt_content_from_mock = "Mocked coverage prompt content"

        # Needed for logging
        mock_tools = [MagicMock(name="det_tool_parse_err")]
        llm_output = "This is not valid JSON"
        invoke_result = {
            "output": llm_output,
            "intermediate_steps": ["det_step_parse_err"],
        }
        parse_error = ResponseParsingError("Invalid JSON in response")

        mock_executor = AsyncMock(spec=AgentExecutor)
        mock_executor.ainvoke.return_value = invoke_result
        mock_executor.tools = mock_tools  # Add tools attribute for logging
        mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            return_value=mock_executor,
        )
        # This mock_parse_coverage_determination_response_func is for the one in mcp_agent.py scope
        mock_parse_coverage_determination_response_func.side_effect = parse_error

        with pytest.raises(
            DataExtractionError,
            match="Could not parse LLM response: Invalid JSON in response",
        ) as excinfo:
            await agent_instance.determine_coverage(
                claim_id, authorization=auth_token, as_of_date=fixed_as_of_date
            )

        assert excinfo.value.__cause__ is parse_error
        mock_get_coverage_determination_prompt_func.assert_called_once_with(
            claim_id=claim_id, as_of_date=fixed_as_of_date_str
        )
        mock_trace_tools_execution_func.assert_called_once_with(mock_executor)

        # This is the input that will actually be sent to the executor's ainvoke,
        # because mock_get_coverage_determination_prompt_func returns a fixed string.
        expected_agent_input_to_executor = {
            "input": actual_prompt_content_from_mock,
            "chat_history": [],
        }
        mock_executor.ainvoke.assert_awaited_once_with(expected_agent_input_to_executor)
