"""Unit tests for MCPToolService."""

from unittest.mock import AsyncMock, MagicMock

import pytest
from langchain_mcp_adapters.client import MultiServerMCPClient

from claims_agent.agents.mcp_client_manager import ToolFetchingError
from claims_agent.agents.mcp_tool_service import MCPToolService


@pytest.mark.asyncio
class TestMCPToolServiceGetMCPTools:
    """Tests for MCPToolService.get_mcp_tools method."""

    @pytest.fixture
    def mcp_tool_service(self) -> MCPToolService:
        """Fixture for MCPToolService instance."""
        return MCPToolService()

    @pytest.fixture
    def mock_mcp_client(self) -> MagicMock:
        """Fixture for a mocked MultiServerMCPClient."""
        client = MagicMock(spec=MultiServerMCPClient)
        # Mock the get_tools method to be an AsyncMock since it's awaited
        client.get_tools = AsyncMock()
        return client

    async def test_get_mcp_tools_success(
        self, mcp_tool_service: MCPToolService, mock_mcp_client: MagicMock
    ) -> None:
        """Test successful fetching of tools."""
        mock_tool_1 = MagicMock(name="Tool1", description="Description1")
        mock_tool_2 = MagicMock(name="Tool2", description="Description2")
        expected_tools = [mock_tool_1, mock_tool_2]
        mock_mcp_client.get_tools.return_value = expected_tools

        result_tools = await mcp_tool_service.get_mcp_tools(mock_mcp_client)

        assert result_tools == expected_tools
        mock_mcp_client.get_tools.assert_awaited_once()
        # Further log assertions can be added here if necessary, using caplog fixture

    async def test_get_mcp_tools_empty_list_raises_error(
        self, mcp_tool_service: MCPToolService, mock_mcp_client: MagicMock
    ) -> None:
        """Test ToolFetchingError is raised when client returns an empty list of tools."""
        mock_mcp_client.get_tools.return_value = []

        with pytest.raises(ToolFetchingError) as excinfo:
            await mcp_tool_service.get_mcp_tools(mock_mcp_client)

        assert "No tools were returned by the MCP client (MCPToolService)." in str(
            excinfo.value
        )
        mock_mcp_client.get_tools.assert_awaited_once()

    async def test_get_mcp_tools_client_method_raises_exception(
        self, mcp_tool_service: MCPToolService, mock_mcp_client: MagicMock
    ) -> None:
        """Test ToolFetchingError is raised (and wraps original) when get_tools() fails."""
        original_exception = RuntimeError("Client-side failure")
        mock_mcp_client.get_tools.side_effect = original_exception

        with pytest.raises(ToolFetchingError) as excinfo:
            await mcp_tool_service.get_mcp_tools(mock_mcp_client)

        assert "MCPToolService failed during client.get_tools()" in str(excinfo.value)
        assert excinfo.value.__cause__ is original_exception
        mock_mcp_client.get_tools.assert_awaited_once()

    async def test_get_mcp_tools_client_missing_method_raises_error(
        self, mcp_tool_service: MCPToolService, mock_mcp_client: MagicMock
    ) -> None:
        """Test ToolFetchingError for client missing get_tools method."""
        # Remove the get_tools attribute from the mock
        del mock_mcp_client.get_tools

        with pytest.raises(ToolFetchingError) as excinfo:
            await mcp_tool_service.get_mcp_tools(mock_mcp_client)

        assert "MCP client instance is missing 'get_tools' method" in str(excinfo.value)
        # In this case, client.get_tools is not called because hasattr fails first
        # So, no assert_awaited_once() for mock_mcp_client.get_tools
