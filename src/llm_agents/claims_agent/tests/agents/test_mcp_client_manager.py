"""Unit tests for MCPClientManager."""

from collections.abc import Generator
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from langchain_mcp_adapters.client import MultiServerMCPClient

from claims_agent.agents.mcp_client_manager import (
    MCPClientManager,
)
from claims_agent.config import Settings


# Mark all tests in this module as asyncio
# pytestmark = pytest.mark.asyncio # Remove this module-level mark


@pytest.fixture
def mock_sse_connection_cls() -> Generator[MagicMock, None, None]:
    """Fixture to mock SSEConnection class."""
    with patch(
        "claims_agent.agents.mcp_client_manager.SSEConnection", autospec=True
    ) as mock_sse:
        yield mock_sse


@pytest.fixture
def mock_multiserver_mcp_client_cls() -> Generator[
    tuple[MagicMock, AsyncMock], None, None
]:
    """Fixture to mock MultiServerMCPClient class."""
    with patch(
        "claims_agent.agents.mcp_client_manager.MultiServerMCPClient",
        autospec=True,
    ) as mock_mcp_client_cls:
        # Configure the mock class's __aenter__ to return an AsyncMock instance
        # which itself can be used as an async context manager if needed, or just a mock client.
        mock_client_instance = AsyncMock(spec=MultiServerMCPClient)
        mock_mcp_client_cls.return_value = mock_client_instance
        yield mock_mcp_client_cls, mock_client_instance


class TestMCPClientManagerInit:
    """Tests for MCPClientManager.__init__."""

    def test_init_all_urls(self) -> None:
        """Test __init__ with both claims and policy URLs."""
        manager = MCPClientManager(
            settings=Settings(
                CLAIMS_SERVER_URL="http://claims",
                POLICY_SERVER_URL="http://policy",
            )
        )
        assert manager.claims_server_url == "http://claims"
        assert manager.policy_server_url == "http://policy"

    def test_init_one_url_raises_error(self) -> None:
        """Test __init__ with only claims URL."""
        with pytest.raises(ValueError):
            MCPClientManager(
                settings=Settings(
                    CLAIMS_SERVER_URL="http://claims",
                    POLICY_SERVER_URL=None,
                )
            )

    def test_init_no_urls_raises_error(self) -> None:
        """Test __init__ with no server URLs."""
        with pytest.raises(ValueError):
            MCPClientManager(
                settings=Settings(
                    CLAIMS_SERVER_URL=None,
                    POLICY_SERVER_URL=None,
                )
            )


@pytest.mark.asyncio
class TestMCPClientManagerGetClient:
    """Tests for MCPClientManager.get_client."""

    async def test_get_client_with_both_urls_and_auth(
        self,
        mock_sse_connection_cls: MagicMock,
        mock_multiserver_mcp_client_cls: tuple[MagicMock, AsyncMock],
    ) -> None:
        """Test get_client with both URLs and authorization token."""
        mock_mcp_constructor, _ = mock_multiserver_mcp_client_cls
        manager = MCPClientManager(
            settings=Settings(
                CLAIMS_SERVER_URL="http://claims",
                POLICY_SERVER_URL="http://policy",
            )
        )
        auth_token = "Bearer testtoken"  # noqa: S105

        async with manager.get_client(authorization=auth_token):
            pass  # Just enter and exit the context

        expected_headers = {"Authorization": auth_token}
        mock_sse_connection_cls.assert_any_call(
            url="http://claims",
            transport="sse",
            timeout=manager.default_timeout,
            sse_read_timeout=manager.sse_read_timeout,
            headers=expected_headers,
        )
        mock_sse_connection_cls.assert_any_call(
            url="http://policy",
            transport="sse",
            timeout=manager.default_timeout,
            sse_read_timeout=manager.sse_read_timeout,
            headers=expected_headers,
        )
        assert mock_mcp_constructor.call_args is not None
        servers_arg = mock_mcp_constructor.call_args[0][0]
        assert "ClaimsService" in servers_arg
        assert "PolicyService" in servers_arg
