"""Shared fixtures for agent tests."""

from collections.abc import Generator
from unittest.mock import MagicMock, patch

import pytest
from langchain.agents import Agent<PERSON>xecutor
from pydantic import SecretStr
from pytest_mock import MockFixture

from claims_agent.agents.agent_executor_factory import Agent<PERSON>xecutorFactory
from claims_agent.agents.mcp_agent import MCPAgent
from claims_agent.agents.mcp_client_manager import MC<PERSON><PERSON><PERSON>anager
from claims_agent.agents.mcp_tool_service import MC<PERSON><PERSON><PERSON>Service
from claims_agent.config import Settings
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
)


# --- Fixtures ---
@pytest.fixture
def mock_settings() -> Settings:
    """Fixture to provide mock settings for agent initialization."""
    return Settings(
        CLAIMS_SERVER_URL="http://mockclaims",
        POLICY_SERVER_URL="http://mockpolicy",
        MODEL_NAME="test-model",
        MODEL_TEMPERATURE=0.1,
        OPENAI_API_KEY=SecretStr("test-key"),
        AGENT_VERBOSE=False,
    )


@pytest.fixture
def mock_create_agent_prompt_template_func() -> Generator[MagicMock, None, None]:
    """Mocks the create_agent_prompt_template utility function."""
    with patch(
        "claims_agent.agents.mcp_agent.create_agent_prompt_template"
    ) as mock_func:
        mock_func.return_value = MagicMock()  # A mock prompt template
        yield mock_func


@pytest.fixture
def mock_get_coverage_determination_prompt_func() -> Generator[MagicMock, None, None]:
    """Mocks the get_coverage_determination_prompt utility function."""
    with patch(
        "claims_agent.agents.mcp_agent.get_coverage_determination_prompt"
    ) as mock_func:
        mock_func.return_value = "Mocked coverage prompt content"
        yield mock_func


@pytest.fixture
def mock_parse_coverage_determination_response_func() -> Generator[
    MagicMock, None, None
]:
    """Mocks the parse_coverage_determination_response utility function."""
    with patch(
        "claims_agent.agents.mcp_agent.parse_coverage_determination_response"
    ) as mock_func:
        mock_func.return_value = MagicMock(spec=LegacyCoverageDeterminationResponse)
        yield mock_func


@pytest.fixture
def mock_log_intermediate_steps_func() -> Generator[MagicMock, None, None]:
    """Mocks the log_intermediate_steps utility function."""
    with patch("claims_agent.agents.mcp_agent.log_intermediate_steps") as mock_func:
        yield mock_func


@pytest.fixture
def mock_trace_tools_execution_func() -> Generator[MagicMock, None, None]:
    """Mocks the trace_tools_execution utility function."""
    with patch("claims_agent.agents.mcp_agent.trace_tools_execution") as mock_func:
        yield mock_func


@pytest.fixture
def mock_mcp_client_manager(mocker: MockFixture) -> MagicMock:
    """Fixture to provide a mock MCPClientManager."""
    return mocker.MagicMock(spec=MCPClientManager)


@pytest.fixture
def mock_mcp_tool_service(mocker: MockFixture) -> MagicMock:
    """Fixture to provide a mock MCPToolService."""
    return mocker.MagicMock(spec=MCPToolService)


@pytest.fixture
def mock_agent_executor_factory(mocker: MockFixture) -> MagicMock:
    """Fixture to provide a mock AgentExecutorFactory."""
    mock = mocker.MagicMock(spec=AgentExecutorFactory)
    mock.create_executor.return_value = mocker.MagicMock(spec=AgentExecutor)
    return mock


# Helper function to get an instance of MCPAgent with mocked dependencies
# This might be useful if many tests need a similarly configured agent


def get_test_mcp_agent(
    mock_mcp_client_manager: MagicMock,
    mock_mcp_tool_service: MagicMock,
    mock_agent_executor_factory: MagicMock,
) -> MCPAgent:
    """Helper to create an MCPAgent instance for testing, using mocked dependencies."""
    return MCPAgent(
        mcp_client_manager=mock_mcp_client_manager,
        mcp_tool_service=mock_mcp_tool_service,
        agent_executor_factory=mock_agent_executor_factory,
    )
