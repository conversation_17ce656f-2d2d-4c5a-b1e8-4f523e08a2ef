"""Unit tests for AgentExecutorFactory."""

from collections.abc import Generator
from unittest.mock import MagicMock, patch

import pytest
from langchain.agents import AgentExecutor
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>
from pydantic import SecretStr

# Assuming settings are used for defaults, import path might need adjustment
from claims_agent.agents.agent_executor_factory import AgentExecutorFactory
from claims_agent.config import Settings


# Fixture to mock settings if needed


@pytest.fixture
def mock_settings() -> Settings:
    """Fixture to provide mock settings."""
    return Settings(
        OPENROUTER_API_KEY=SecretStr(""),
        OPENROUTER_MODEL_NAME="google/gemini-2.5-pro-preview",
        OPENAI_API_KEY=SecretStr("test-key"),
        LEGACY_OPENAI_MODEL_NAME="gpt-4o",
        MODEL_TEMPERATURE=0.5,
        AGENT_VERBOSE=False,
    )


@pytest.fixture
def mock_chat_openai_cls() -> Generator[MagicMock, None, None]:
    """Fixture to mock the ChatOpenAI class."""
    with patch(
        "claims_agent.agents.agent_executor_factory.ChatOpenAI",
        autospec=True,
    ) as mock_cls:
        # Return a mock instance when the class is called
        mock_instance = MagicMock(spec=ChatOpenAI)
        mock_cls.return_value = mock_instance
        yield mock_cls


@pytest.fixture
def mock_create_agent_runnable_func() -> Generator[MagicMock, None, None]:
    """Fixture to mock the create_openai_tools_agent function."""
    with patch(
        "claims_agent.agents.agent_executor_factory.create_openai_tools_agent",
        autospec=True,
    ) as mock_func:
        # Return a mock runnable when called
        mock_runnable = MagicMock()  # Simple mock for the agent runnable
        mock_func.return_value = mock_runnable
        yield mock_func


@pytest.fixture
def mock_agent_executor_cls() -> Generator[MagicMock, None, None]:
    """Fixture to mock the AgentExecutor class."""
    with patch(
        "claims_agent.agents.agent_executor_factory.AgentExecutor",
        autospec=True,
    ) as mock_cls:
        mock_instance = MagicMock(spec=AgentExecutor)
        mock_cls.return_value = mock_instance
        yield mock_cls


# --- Test Class ---


class TestAgentExecutorFactory:
    """Tests for the AgentExecutorFactory class."""

    def test_init_with_settings_override(self, mock_settings: Settings) -> None:
        """Test factory initialization with settings override."""
        with patch(
            "claims_agent.agents.agent_executor_factory.create_llm_provider"
        ) as mock_create_provider:
            mock_provider = MagicMock()
            mock_provider.provider_name = "OpenAI"
            mock_provider.config.model_name = "o3-mini"
            mock_provider.config.temperature = 0.5
            mock_create_provider.return_value = mock_provider

            factory = AgentExecutorFactory(
                settings_override=mock_settings,
                agent_verbose=True,
                max_iterations=10,
                max_execution_time=120,
            )

            assert factory.settings == mock_settings
            assert factory.agent_verbose is True
            assert factory.max_iterations == 10
            assert factory.max_execution_time == 120
            assert factory.provider == mock_provider

    def test_init_with_defaults(self, mock_settings: Settings) -> None:
        """Test factory initialization using default settings."""
        with (
            patch(
                "claims_agent.agents.agent_executor_factory.create_llm_provider"
            ) as mock_create_provider,
        ):
            mock_provider = MagicMock()
            mock_provider.provider_name = "OpenAI"
            mock_provider.config.model_name = "o3-mini"
            mock_provider.config.temperature = 1.0
            mock_create_provider.return_value = mock_provider

            factory = AgentExecutorFactory(settings_override=mock_settings)

            assert factory.settings == mock_settings
            assert factory.provider == mock_provider
            mock_create_provider.assert_called_once_with(mock_settings)

    def test_init_provider_creation_error(self, mock_settings: Settings) -> None:
        """Test that provider creation errors are propagated."""
        with patch(
            "claims_agent.agents.agent_executor_factory.create_llm_provider"
        ) as mock_create_provider:
            mock_create_provider.side_effect = Exception("No valid provider")

            with pytest.raises(Exception, match="No valid provider"):
                AgentExecutorFactory(settings_override=mock_settings)

    def test_create_llm(self, mock_settings: Settings) -> None:
        """Test the internal _create_llm method."""
        with (
            patch(
                "claims_agent.agents.agent_executor_factory.create_llm_provider"
            ) as mock_create_provider,
            patch(
                "claims_agent.agents.agent_executor_factory.create_llm_from_provider"
            ) as mock_create_llm,
        ):
            mock_provider = MagicMock()
            mock_create_provider.return_value = mock_provider
            mock_llm = MagicMock()
            mock_create_llm.return_value = mock_llm

            factory = AgentExecutorFactory(settings_override=mock_settings)
            llm_instance = factory._create_llm()

            # Assert the provider's create_llm_from_provider was called
            mock_create_llm.assert_called_once_with(mock_provider)
            assert llm_instance == mock_llm

    def test_create_executor_success(
        self,
        mock_settings: Settings,
        mock_create_agent_runnable_func: MagicMock,
        mock_agent_executor_cls: MagicMock,
    ) -> None:
        """Test successful creation of an AgentExecutor."""
        with (
            patch(
                "claims_agent.agents.agent_executor_factory.create_llm_provider"
            ) as mock_create_provider,
            patch(
                "claims_agent.agents.agent_executor_factory.create_llm_from_provider"
            ) as mock_create_llm,
        ):
            mock_provider = MagicMock()
            mock_provider.provider_name = "OpenAI"
            mock_provider.config.model_name = "o3-mini"
            mock_create_provider.return_value = mock_provider

            mock_llm_instance = MagicMock()
            mock_create_llm.return_value = mock_llm_instance

            factory = AgentExecutorFactory(settings_override=mock_settings)

            mock_tools = [MagicMock(), MagicMock()]
            mock_prompt = MagicMock(spec=ChatPromptTemplate)

            # The mock agent runnable created
            mock_runnable_instance = mock_create_agent_runnable_func.return_value
            # The final mock executor instance
            mock_executor_instance = mock_agent_executor_cls.return_value

            executor = factory.create_executor(tools=mock_tools, prompt=mock_prompt)

            # 1. Check LLM creation
            mock_create_llm.assert_called_once_with(mock_provider)

            # 2. Check Agent Runnable creation
            mock_create_agent_runnable_func.assert_called_once_with(
                llm=mock_llm_instance, tools=mock_tools, prompt=mock_prompt
            )

            # 3. Check AgentExecutor initialization
            mock_agent_executor_cls.assert_called_once_with(
                agent=mock_runnable_instance,
                tools=mock_tools,
                verbose=factory.agent_verbose,
                handle_parsing_errors=True,  # Asserting default/current value
                max_iterations=factory.max_iterations,
                max_execution_time=float(factory.max_execution_time),
                early_stopping_method="force",
                return_intermediate_steps=True,
            )

            # 4. Check returned object
            assert executor == mock_executor_instance

    def test_create_runnable_error_raises_value_error(
        self,
        mock_settings: Settings,
        mock_create_agent_runnable_func: MagicMock,
        mock_agent_executor_cls: MagicMock,
    ) -> None:
        """Test that an error during agent runnable creation raises ValueError."""
        with (
            patch(
                "claims_agent.agents.agent_executor_factory.create_llm_provider"
            ) as mock_create_provider,
            patch(
                "claims_agent.agents.agent_executor_factory.create_llm_from_provider"
            ) as mock_create_llm,
        ):
            mock_provider = MagicMock()
            mock_create_provider.return_value = mock_provider
            mock_create_llm.return_value = MagicMock()

            factory = AgentExecutorFactory(settings_override=mock_settings)

            mock_tools = [MagicMock()]
            mock_prompt = MagicMock(spec=ChatPromptTemplate)

            # Configure the mock agent creation function to raise an error
            original_error = TypeError("Something is wrong with the prompt")
            mock_create_agent_runnable_func.side_effect = original_error

            # Add match parameter as suggested by PT011
            with pytest.raises(
                ValueError, match="Failed to create agent runnable"
            ) as excinfo:
                factory.create_executor(tools=mock_tools, prompt=mock_prompt)

            # We already matched the start of the message, check cause here
            assert excinfo.value.__cause__ is original_error
            mock_agent_executor_cls.assert_not_called()  # Executor shouldn't be created
