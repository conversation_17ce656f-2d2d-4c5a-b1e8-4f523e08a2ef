"""Unit tests for LLM response parsing utilities."""

import json
import re
from typing import Any

import pytest

from claims_agent.agents.response_parser import (
    ResponseParsingError,
    extract_verification_json_from_llm_response,
    parse_coverage_determination_response,
    parse_verification_items,
)

# Assuming these models are correctly defined and importable
# For tests, we might create simplified versions or mock them if they have complex dependencies
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
    VerificationItem as LegacyVerificationItem,
)


# --- Test Data ---
VALID_VERIFICATION_ITEM_DICT_MINIMAL: dict[str, Any] = {
    "name": "Test Verification Minimal",
    "assessment_score": 0.5,
    "summary": "Minimal valid summary.",
}
VALID_VERIFICATION_ITEM_DICT_FULL: dict[str, Any] = {
    "name": "Test Verification Full",
    "assessment_score": 1.0,
    "summary": "Full valid summary with all details.",
    "citation": {
        "excerpt": "Relevant text.",
        "pages": [1],
        "document": {
            "policy_number": "TEST123",
            "document_id": "12345678-1234-5678-1234-567812345678",
            "filename": "doc.pdf",
        },
    },
}
INVALID_VERIFICATION_ITEM_DICT_MISSING_NAME: dict[str, Any] = {
    "assessment_score": 0.5,
    "summary": "Missing name.",
}
INVALID_VERIFICATION_ITEM_DICT_WRONG_TYPE: dict[str, Any] = {
    "name": "Wrong Type",
    "assessment_score": "not-a-float",
    "summary": "Score is wrong type.",
}

VALID_JSON_PAYLOAD_STR = json.dumps(
    {"verifications": [VALID_VERIFICATION_ITEM_DICT_FULL]}
)
VALID_JSON_PAYLOAD_EMPTY_VERIFICATIONS_STR = json.dumps({"verifications": []})


# --- Tests for extract_verification_json_from_llm_response ---
class TestExtractVerificationJson:
    """Tests for extract_verification_json_from_llm_response."""

    def test_extract_with_markdown_fences(self) -> None:
        """Test extraction with standard markdown fences."""
        llm_output = (
            f"Some text before ```json\n{VALID_JSON_PAYLOAD_STR}\n``` and after."
        )
        assert (
            extract_verification_json_from_llm_response(llm_output)
            == VALID_JSON_PAYLOAD_STR
        )

    def test_extract_with_markdown_fences_no_newline(self) -> None:
        """Test extraction with markdown fences and no newline before content."""
        llm_output = f"```json{VALID_JSON_PAYLOAD_STR}```"
        assert (
            extract_verification_json_from_llm_response(llm_output)
            == VALID_JSON_PAYLOAD_STR
        )

    def test_extract_with_varied_spacing(self) -> None:
        """Test extraction with varied spacing around markdown fences."""
        llm_output = f"  ```  json   \n  {VALID_JSON_PAYLOAD_STR} \n  ```  "
        assert (
            extract_verification_json_from_llm_response(llm_output)
            == VALID_JSON_PAYLOAD_STR.strip()
        )  # Regex group(1).strip()

    def test_extract_raw_json_valid_structure(self) -> None:
        """Test fallback extraction of raw JSON with valid structure."""
        llm_output = VALID_JSON_PAYLOAD_STR
        # Helper with use_fallback_raw_json=True (default) should return the string if it's valid JSON and a dict.
        assert (
            extract_verification_json_from_llm_response(llm_output)
            == VALID_JSON_PAYLOAD_STR
        )

    def test_extract_raw_json_invalid_structure_no_verifications_key(self) -> None:
        """Test fallback raw JSON extraction even if 'verifications' key is missing."""
        llm_output = json.dumps({"other_key": []})
        # Helper with use_fallback_raw_json=True (default) should return the string if it's valid JSON and a dict.
        assert extract_verification_json_from_llm_response(llm_output) == llm_output

    def test_extract_raw_json_verifications_not_a_list(self) -> None:
        """Test fallback raw JSON extraction even if 'verifications' is not a list."""
        llm_output = json.dumps({"verifications": "not a list"})
        # Helper with use_fallback_raw_json=True (default) should return the string if it's valid JSON and a dict.
        assert extract_verification_json_from_llm_response(llm_output) == llm_output

    def test_no_json_block_or_raw_json(self) -> None:
        """Test input with no JSON block or raw JSON structure."""
        llm_output = "Just some plain text without any JSON."
        assert extract_verification_json_from_llm_response(llm_output) is None

    def test_empty_string_input(self) -> None:
        """Test extraction with an empty string input."""
        assert extract_verification_json_from_llm_response("") is None

    def test_malformed_json_within_fences(self) -> None:
        """Test extraction of malformed JSON content within markdown fences."""
        malformed_json = '{"key": "value"'  # Missing closing brace
        llm_output = f"```json\n{malformed_json}\n```"
        # The permissive regex r"```(?:json|jsonc|jsonl)?\s*\n?(.*?)\n?\s*```" will extract the content.
        assert extract_verification_json_from_llm_response(llm_output) == malformed_json

    def test_malformed_raw_json_input(self) -> None:
        """Test fallback extraction fails with malformed raw JSON."""
        llm_output = '{"key": "value"'  # Missing closing brace
        assert extract_verification_json_from_llm_response(llm_output) is None

    def test_multiple_json_blocks(self) -> None:
        """Test that the first JSON block is extracted when multiple exist."""
        # Should extract the first one due to non-greedy regex for content
        second_payload = json.dumps({"verifications": [{"name": "second"}]})
        llm_output = f"```json\n{VALID_JSON_PAYLOAD_STR}\n``` some text ```json\n{second_payload}\n```"
        assert (
            extract_verification_json_from_llm_response(llm_output)
            == VALID_JSON_PAYLOAD_STR
        )


# --- Tests for parse_verification_items ---
class TestParseVerificationItems:
    """Tests for parse_verification_items."""

    def test_parse_valid_items(self) -> None:
        """Test parsing a list of valid verification item dictionaries."""
        data: dict[str, Any] = {
            "verifications": [
                VALID_VERIFICATION_ITEM_DICT_MINIMAL,
                VALID_VERIFICATION_ITEM_DICT_FULL,
            ]
        }
        items = parse_verification_items(data)
        assert len(items) == 2
        assert isinstance(items[0], LegacyVerificationItem)
        assert items[0].name == VALID_VERIFICATION_ITEM_DICT_MINIMAL["name"]
        assert isinstance(items[1], LegacyVerificationItem)
        assert items[1].name == VALID_VERIFICATION_ITEM_DICT_FULL["name"]
        assert items[1].citation is not None
        assert items[1].citation.document is not None
        assert items[1].citation.document.filename == "doc.pdf"
        assert items[1].citation.pages == [1]  # New field
        # Deprecated field for backwards compatibility
        assert items[1].citation.page == "1"
        assert isinstance(items[1].citation.pages, list) and all(
            isinstance(p, int) for p in items[1].citation.pages
        )
        assert isinstance(items[1].citation.page, str)

    def test_parse_empty_verifications_list(self) -> None:
        """Test parsing with an empty 'verifications' list."""
        data: dict[str, Any] = {"verifications": []}
        items = parse_verification_items(data)
        assert len(items) == 0

    def test_missing_verifications_key(self) -> None:
        """Test parsing fails if 'verifications' key is missing."""
        data: dict[str, Any] = {"other_key": "value"}
        with pytest.raises(ResponseParsingError) as excinfo:
            parse_verification_items(data)
        assert "Invalid JSON structure: 'verifications' key missing." in str(
            excinfo.value
        )

    def test_verifications_not_a_list(self) -> None:
        """Test parsing fails if 'verifications' is not a list."""
        data: dict[str, Any] = {"verifications": "not-a-list"}
        with pytest.raises(ResponseParsingError) as excinfo:
            parse_verification_items(data)
        assert (
            "Invalid JSON structure: 'verifications' must be a list, got str."
            in str(excinfo.value)
        )

    def test_item_not_a_dictionary(self, caplog: pytest.LogCaptureFixture) -> None:
        """Test skipping non-dictionary items in the 'verifications' list."""
        data: dict[str, Any] = {
            "verifications": ["not-a-dict", VALID_VERIFICATION_ITEM_DICT_MINIMAL]
        }
        # The original code skips non-dict items and logs a warning.
        # It only raises if ALL items fail to parse OR a Pydantic error occurs.
        items = parse_verification_items(data)
        assert len(items) == 1
        assert items[0].name == VALID_VERIFICATION_ITEM_DICT_MINIMAL["name"]
        assert (
            "Skipping item at index 0: Expected a dictionary, got str." in caplog.text
        )

    def test_item_invalid_pydantic_missing_field(self) -> None:
        """Test parsing succeeds with repair for item with missing Pydantic field."""
        data: dict[str, Any] = {
            "verifications": [INVALID_VERIFICATION_ITEM_DICT_MISSING_NAME]
        }
        # The repair functionality now fixes missing names, so this should succeed
        items = parse_verification_items(data)
        assert len(items) == 1
        # The repair should have added a name derived from the summary
        assert items[0].name is not None
        assert len(items[0].name) > 0

    def test_item_invalid_pydantic_wrong_type(self) -> None:
        """Test parsing fails for item with incorrect Pydantic field type that can't be repaired."""
        data: dict[str, Any] = {
            "verifications": [INVALID_VERIFICATION_ITEM_DICT_WRONG_TYPE]
        }
        with pytest.raises(ResponseParsingError) as excinfo:
            parse_verification_items(data)
        assert "Failed to parse any verification items from the list" in str(
            excinfo.value
        )

    def test_all_items_fail_validation(self) -> None:
        """Test parsing with mixed success when some items can be repaired."""
        data: dict[str, Any] = {
            "verifications": [
                INVALID_VERIFICATION_ITEM_DICT_MISSING_NAME,  # Can be repaired
                INVALID_VERIFICATION_ITEM_DICT_WRONG_TYPE,  # Cannot be repaired
            ]
        }
        # The first item should be repaired successfully, second should fail
        # With partial_success=True (default), this should succeed with 1 item
        items = parse_verification_items(data)
        assert len(items) == 1  # Only the repaired item
        assert items[0].name is not None

    def test_some_items_pass_some_fail(self) -> None:
        """Test parsing succeeds with partial success when some items pass and some can be repaired."""
        data: dict[str, Any] = {
            "verifications": [
                VALID_VERIFICATION_ITEM_DICT_FULL,  # This will pass
                INVALID_VERIFICATION_ITEM_DICT_MISSING_NAME,  # This will be repaired
            ]
        }
        # Both items should succeed (one passes, one gets repaired)
        items = parse_verification_items(data)
        assert len(items) == 2
        assert items[0].name == VALID_VERIFICATION_ITEM_DICT_FULL["name"]
        assert items[1].name is not None  # Repaired item should have a name


# The TestParseCoverageDeterminationResponseOrchestration class is removed
# as the internal orchestration of parse_coverage_determination_response has changed significantly.
# End-to-end tests are more relevant now.


class TestParseCoverageDeterminationResponseEndToEnd:
    """Tests for parse_coverage_determination_response with various LLM outputs (end-to-end)."""

    def test_e2e_valid_llm_output_direct_json(self) -> None:
        """Test e2e with valid LLM output that is direct JSON."""
        llm_output = VALID_JSON_PAYLOAD_STR
        response = parse_coverage_determination_response(llm_output)
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1
        assert (
            response.verifications[0].name == VALID_VERIFICATION_ITEM_DICT_FULL["name"]
        )

    def test_e2e_valid_llm_output_markdown_fenced_json(self) -> None:
        """Test e2e with valid LLM output that is markdown-fenced JSON."""
        llm_output = f"```json\n{VALID_JSON_PAYLOAD_STR}\n```"
        response = parse_coverage_determination_response(llm_output)
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1
        assert (
            response.verifications[0].name == VALID_VERIFICATION_ITEM_DICT_FULL["name"]
        )

    def test_e2e_valid_llm_output_brace_matching_json(self) -> None:
        """Test e2e with valid LLM output that is found by brace matching."""
        llm_output = (
            f"Some preamble text... {VALID_JSON_PAYLOAD_STR} ...and some trailing text."
        )
        response = parse_coverage_determination_response(llm_output)
        assert isinstance(response, LegacyCoverageDeterminationResponse)
        assert len(response.verifications) == 1
        assert (
            response.verifications[0].name == VALID_VERIFICATION_ITEM_DICT_FULL["name"]
        )

    def test_e2e_json_extraction_failure(self) -> None:
        """Test e2e failure when no valid JSON can be extracted by any method."""
        llm_output = "This is just plain text, no JSON here."
        expected_error_msg = (
            "LLM output was empty or did not contain valid JSON content after cleaning."
        )
        with pytest.raises(ResponseParsingError, match=re.escape(expected_error_msg)):
            parse_coverage_determination_response(llm_output)

    def test_e2e_json_decode_failure(self) -> None:
        """Test e2e failure if extracted content is not valid JSON (decode fails)."""
        # Scenario 1: Markdown fenced malformed JSON
        # Malformed
        llm_output_markdown_malformed = '```json\n{"key": "value" \n```'
        with pytest.raises(Exception):  # Will raise a ValidationError from Pydantic
            parse_coverage_determination_response(llm_output_markdown_malformed)

    def test_e2e_verification_item_parse_failure(self) -> None:
        """Test e2e when JSON is valid but VerificationItem Pydantic parsing fails."""
        # Pydantic error in verifications
        invalid_item_payload = json.dumps(
            {"verifications": [INVALID_VERIFICATION_ITEM_DICT_MISSING_NAME]}
        )
        llm_output_direct_json = invalid_item_payload
        llm_output_fenced = f"```json\n{invalid_item_payload}\n```"
        # Brace matching for structured error is less likely to be the primary path for this kind of error,
        # as it implies the JSON structure itself was hard to find, not that its content is invalid.
        # However, if brace matching *does* yield this content, it should also fail validation.
        llm_output_braced = (
            f"Some text before parsing. {invalid_item_payload} Some text after."
        )

        test_cases = [
            ("direct JSON", llm_output_direct_json),
            ("markdown-fenced JSON", llm_output_fenced),
            ("brace-matched JSON", llm_output_braced),
        ]

        for name, output_str in test_cases:
            # The repair functionality should now fix the missing name, so this should succeed
            response = parse_coverage_determination_response(output_str)
            assert isinstance(response, LegacyCoverageDeterminationResponse)
            assert len(response.verifications) == 1
            assert response.verifications[0].name is not None

    def test_e2e_completely_malformed_json_string(self) -> None:
        """Test e2e with a completely malformed JSON string."""
        llm_output = (
            # Malformed
            "{ 'verifications': [ { 'name': 'Test', 'summary': 'Text' } "
        )
        with pytest.raises(ResponseParsingError) as excinfo:
            parse_coverage_determination_response(llm_output)
        # This should primarily fail at JSON decoding stage for one of the strategies.
        # Check for the actual error message from the current implementation.
        assert (
            "LLM output was empty or did not contain valid JSON content after cleaning."
            in str(excinfo.value)
        )

    def test_e2e_empty_llm_output(self) -> None:
        """Test e2e parsing of an empty LLM output string."""
        expected_error_msg = (
            "LLM output was empty or did not contain valid JSON content after cleaning."
        )
        with pytest.raises(ResponseParsingError, match=re.escape(expected_error_msg)):
            parse_coverage_determination_response("")

    def test_e2e_empty_verifications_list_is_valid(self) -> None:
        """Test e2e that a valid JSON payload with an empty verifications list parses correctly."""
        # Scenario 1: Markdown fenced
        llm_output_markdown = (
            f"```json\n{VALID_JSON_PAYLOAD_EMPTY_VERIFICATIONS_STR}\n```"
        )
        response_markdown = parse_coverage_determination_response(llm_output_markdown)
        assert isinstance(response_markdown, LegacyCoverageDeterminationResponse)
        assert len(response_markdown.verifications) == 0

        # Scenario 2: Direct JSON
        llm_output_direct = VALID_JSON_PAYLOAD_EMPTY_VERIFICATIONS_STR
        response_direct = parse_coverage_determination_response(llm_output_direct)
        assert isinstance(response_direct, LegacyCoverageDeterminationResponse)
        assert len(response_direct.verifications) == 0

        # Scenario 3: Brace-matched JSON
        llm_output_braced = (
            f"Preamble {VALID_JSON_PAYLOAD_EMPTY_VERIFICATIONS_STR} postamble."
        )
        response_braced = parse_coverage_determination_response(llm_output_braced)
        assert isinstance(response_braced, LegacyCoverageDeterminationResponse)
        assert len(response_braced.verifications) == 0
