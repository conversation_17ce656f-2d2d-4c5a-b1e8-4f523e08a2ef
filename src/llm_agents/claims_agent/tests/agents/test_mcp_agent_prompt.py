"""Unit tests for the MCPAgent class's process_prompt method."""

from unittest.mock import AsyncMock, MagicMock

import pytest
from langchain.agents import AgentExecutor
from langchain_core.messages import AIMessage, HumanMessage
from pytest_mock import MockFixture

from claims_agent.agents.mcp_agent import MCPAgent
from claims_agent.agents.mcp_client_manager import ToolFetchingError
from claims_agent.agents.mcp_tool_service import MCPToolService
from .conftest import get_test_mcp_agent


@pytest.mark.asyncio
@pytest.mark.usefixtures(
    "mock_trace_tools_execution_func",
    "mock_log_intermediate_steps_func",
    # Needed by _create_agent_executor_with_active_client
    "mock_create_agent_prompt_template_func",
)
class TestMCPAgentProcessPrompt:
    """Tests for MCPAgent.process_prompt method."""

    @pytest.fixture
    def agent_instance(
        self,
        mock_mcp_client_manager: MagicMock,
        mock_mcp_tool_service: MCPToolService,
        mock_agent_executor_factory: MagicMock,
    ) -> MCPAgent:
        """Provides a configured MCPAgent instance for tests in this class."""
        return get_test_mcp_agent(
            mock_mcp_client_manager,
            mock_mcp_tool_service,
            mock_agent_executor_factory,
        )

    async def test_process_prompt_success_no_history(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_trace_tools_execution_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
    ) -> None:
        """Test successful prompt processing with no chat history."""
        prompt_content = "Test prompt"
        auth_token = "Bearer test_token_proc_1"  # noqa: S105
        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded_proc_1")
        mock_tools = [MagicMock(name="proc_tool1")]
        # Expected result from agent_executor.ainvoke
        invoke_result = {
            "output": "Processed: Test prompt",
            "intermediate_steps": ["step1"],
        }

        # Mock the MCP client context manager
        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )

        # Mock tool fetching
        mock_get_tools_service: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )

        # Mock the creation of AgentExecutor and its subsequent ainvoke call
        mock_executor = AsyncMock(spec=AgentExecutor)
        mock_executor.ainvoke.return_value = invoke_result  # Configure ainvoke here
        mock_executor.tools = mock_tools  # Add tools attribute for logging
        mock_create_exec_active = mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            return_value=mock_executor,
        )

        response = await agent_instance.process_prompt(
            prompt_content, chat_history=[], authorization=auth_token
        )

        assert response.content == "Processed: Test prompt"
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools_service.assert_awaited_once_with(mock_mcp_client_yielded)
        mock_create_exec_active.assert_awaited_once_with(
            mcp_client=mock_mcp_client_yielded, tools=mock_tools
        )
        mock_trace_tools_execution_func.assert_called_once_with(mock_executor)

        expected_agent_input = {
            "input": prompt_content,
            "chat_history": [],  # Empty as per test case
        }
        mock_executor.ainvoke.assert_awaited_once_with(expected_agent_input)
        mock_log_intermediate_steps_func.assert_called_once_with(invoke_result)

    async def test_process_prompt_success_with_history(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_trace_tools_execution_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
    ) -> None:
        """Test successful prompt processing with chat history."""
        prompt = "Follow up question"
        auth_token = "Bearer token_proc_2"  # noqa: S105
        chat_history_raw = [
            {"role": "user", "content": "Initial question"},
            {"role": "assistant", "content": "Initial answer"},
        ]
        expected_formatted_history = [
            HumanMessage(content="Initial question"),
            AIMessage(content="Initial answer"),
        ]
        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded")
        mock_tools = [MagicMock(name="tool1")]

        # This is the crucial part for the return value of agent_executor.ainvoke
        invoke_result = {"output": "Follow up answer", "intermediate_steps": ["step2"]}
        mock_executor = AsyncMock(spec=AgentExecutor, name="mock_executor")
        mock_executor.ainvoke.return_value = invoke_result  # Set return_value here

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mock_get_tools: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )
        mock_create_exec_active = mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            return_value=mock_executor,
        )

        response = await agent_instance.process_prompt(
            prompt, chat_history=chat_history_raw, authorization=auth_token
        )

        assert response.content == "Follow up answer"
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools.assert_awaited_once_with(mock_mcp_client_yielded)
        mock_create_exec_active.assert_awaited_once_with(
            mcp_client=mock_mcp_client_yielded, tools=mock_tools
        )
        mock_trace_tools_execution_func.assert_called_once_with(mock_executor)

        expected_agent_input = {
            "input": prompt,
            "chat_history": expected_formatted_history,
        }
        mock_executor.ainvoke.assert_awaited_once_with(expected_agent_input)
        mock_log_intermediate_steps_func.assert_called_once_with(invoke_result)

    async def test_process_prompt_no_intermediate_steps(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_trace_tools_execution_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        """Test prompt processing where LLM answers without tools (no intermediate steps)."""
        prompt_content = "Simple question"
        auth_token = "Bearer test_token_proc_3"  # noqa: S105
        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded_proc_3")
        mock_tools = [MagicMock(name="proc_tool3")]
        # Result with no intermediate steps
        invoke_result_no_steps = {"output": "Direct answer", "intermediate_steps": []}

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mock_get_tools_service: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )
        mock_executor = AsyncMock(spec=AgentExecutor)
        mock_executor.ainvoke.return_value = invoke_result_no_steps  # Configure ainvoke
        mock_executor.tools = mock_tools  # Add tools attribute for logging
        mock_create_exec_active = mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            return_value=mock_executor,
        )

        response = await agent_instance.process_prompt(
            prompt_content, authorization=auth_token
        )

        assert response.content == "Direct answer"
        # Check that the misleading message about tool access is NOT prepended
        # This was removed in a previous step. If the response matches, this is implicitly tested.

        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools_service.assert_awaited_once_with(mock_mcp_client_yielded)
        mock_create_exec_active.assert_awaited_once_with(
            mcp_client=mock_mcp_client_yielded, tools=mock_tools
        )
        mock_trace_tools_execution_func.assert_called_once_with(mock_executor)

        expected_agent_input = {
            "input": prompt_content,
            "chat_history": [],
        }
        mock_executor.ainvoke.assert_awaited_once_with(expected_agent_input)
        mock_log_intermediate_steps_func.assert_called_once_with(invoke_result_no_steps)
        assert "Processing complete with 0 steps" in caplog.text

    async def test_process_prompt_tool_fetching_error(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_trace_tools_execution_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
    ) -> None:
        """Test error propagation if MCPToolService.get_mcp_tools fails."""
        prompt_content = "Test prompt for tool fetch error"
        auth_token = "Bearer test_token_proc_4"  # noqa: S105
        error_to_raise = ToolFetchingError("Failed to fetch tools for process_prompt")
        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded_proc_4")

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mock_get_tools_service: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            side_effect=error_to_raise,
        )

        # These should not be called if tool fetching fails
        mock_create_exec_active = mocker.patch.object(
            agent_instance, "_create_agent_executor_with_active_client"
        )

        with pytest.raises(ToolFetchingError) as excinfo:
            await agent_instance.process_prompt(
                prompt_content, authorization=auth_token
            )

        assert excinfo.value == error_to_raise
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools_service.assert_awaited_once_with(mock_mcp_client_yielded)
        # Executor creation should not happen
        mock_create_exec_active.assert_not_called()
        # _ainvoke_agent_core would also not be called, so no need to assert on it
        mock_trace_tools_execution_func.assert_not_called()  # Trace should not happen
        # Logging steps should not happen
        mock_log_intermediate_steps_func.assert_not_called()

    async def test_process_prompt_executor_creation_active_error(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_trace_tools_execution_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
    ) -> None:
        """Test error propagation if _create_agent_executor_with_active_client fails."""
        prompt_content = "Test prompt for exec active error"
        auth_token = "Bearer test_token_proc_5"  # noqa: S105
        error_to_raise = ValueError("Executor creation active failed")
        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded_proc_5")
        mock_tools = [MagicMock(name="proc_tool5")]

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mock_get_tools_service: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )
        mock_create_exec_active = mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            side_effect=error_to_raise,
        )

        with pytest.raises(
            ValueError, match="Executor creation active failed"
        ) as excinfo:
            await agent_instance.process_prompt(
                prompt_content, authorization=auth_token
            )

        assert excinfo.value == error_to_raise
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools_service.assert_awaited_once_with(mock_mcp_client_yielded)
        mock_create_exec_active.assert_awaited_once_with(
            mcp_client=mock_mcp_client_yielded, tools=mock_tools
        )
        # _ainvoke_agent_core would not be called, so no need to assert on it
        # Trace should not happen if executor fails to create
        mock_trace_tools_execution_func.assert_not_called()
        mock_log_intermediate_steps_func.assert_not_called()

    async def test_process_prompt_ainvoke_error(
        self,
        agent_instance: MCPAgent,
        mocker: MockFixture,
        mock_trace_tools_execution_func: MagicMock,
        mock_log_intermediate_steps_func: MagicMock,
    ) -> None:
        """Test error propagation if agent_executor.ainvoke fails."""
        prompt_content = "Test prompt for ainvoke error"
        auth_token = "Bearer test_token_proc_6"  # noqa: S105
        error_to_raise = TimeoutError("Ainvoke failed in process_prompt")
        mock_mcp_client_yielded = AsyncMock(name="mock_mcp_client_yielded_proc_6")
        mock_tools = [MagicMock(name="proc_tool6")]

        mock_async_cm_client = AsyncMock()
        mock_async_cm_client.__aenter__.return_value = mock_mcp_client_yielded
        mock_get_client_cm: MagicMock = mocker.patch.object(
            agent_instance.mcp_client_manager,
            "get_client",
            return_value=mock_async_cm_client,
        )
        mock_get_tools_service: AsyncMock = mocker.patch.object(
            agent_instance.mcp_tool_service,
            "get_mcp_tools",
            new_callable=AsyncMock,
            return_value=mock_tools,
        )

        # Mock AgentExecutor and its ainvoke method
        mock_executor = AsyncMock(spec=AgentExecutor)
        # ainvoke itself raises the error
        mock_executor.ainvoke.side_effect = error_to_raise
        mock_executor.tools = mock_tools  # Add tools attribute for logging
        mock_create_exec_active = mocker.patch.object(
            agent_instance,
            "_create_agent_executor_with_active_client",
            return_value=mock_executor,
        )

        with pytest.raises(TimeoutError) as excinfo:
            await agent_instance.process_prompt(
                prompt_content, authorization=auth_token
            )

        assert excinfo.value == error_to_raise
        mock_get_client_cm.assert_called_once_with(authorization=auth_token)
        mock_get_tools_service.assert_awaited_once_with(mock_mcp_client_yielded)
        mock_create_exec_active.assert_awaited_once_with(
            mcp_client=mock_mcp_client_yielded, tools=mock_tools
        )
        mock_trace_tools_execution_func.assert_called_once_with(mock_executor)

        expected_agent_input = {
            "input": prompt_content,
            "chat_history": [],
        }
        mock_executor.ainvoke.assert_awaited_once_with(expected_agent_input)
        # log_intermediate_steps should not be called if ainvoke fails before returning a result
        mock_log_intermediate_steps_func.assert_not_called()
