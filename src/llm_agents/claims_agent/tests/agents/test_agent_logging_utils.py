"""Unit tests for agent logging utilities."""

import logging
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from langchain.agents import AgentExecutor

from claims_agent.agents.agent_logging_utils import (
    log_intermediate_steps,
    trace_tools_execution,
    truncate_for_logging,
)


class TestTruncateForLogging:
    """Tests for the truncate_for_logging utility function."""

    def test_string_shorter_than_max_length(self) -> None:
        """Test that a short string is returned unchanged."""
        text = "hello world"
        assert truncate_for_logging(text, 20) == "hello world"

    def test_string_longer_than_max_length(self) -> None:
        """Test that a long string is truncated and an indicator is added."""
        text = "a" * 100
        expected = "a" * 50 + "... (truncated)"
        assert truncate_for_logging(text, 50) == expected

    def test_string_equal_to_max_length(self) -> None:
        """Test that a string of exact max_length is returned unchanged."""
        text = "a" * 50
        assert truncate_for_logging(text, 50) == text

    def test_non_string_input_is_converted(self) -> None:
        """Test that non-string input is converted to string and truncated."""
        text_int = 1234567890
        expected = "1234567..." + " (truncated)"  # 1234567 is 7 chars
        assert truncate_for_logging(str(text_int), 7) == expected

    def test_non_string_input_shorter_than_max(self) -> None:
        """Test that short non-string input is converted and returned."""
        text_int = 123
        assert truncate_for_logging(str(text_int), 10) == "123"

    def test_max_length_zero(self) -> None:
        """Test truncation with max_length of 0."""
        text = "hello"
        expected = "... (truncated)"
        assert truncate_for_logging(text, 0) == expected

    def test_max_length_one(self) -> None:
        """Test truncation with max_length of 1."""
        text = "hello"
        expected = "h... (truncated)"
        assert truncate_for_logging(text, 1) == expected

    def test_empty_string_input(self) -> None:
        """Test with an empty string input."""
        assert truncate_for_logging("", 10) == ""

    def test_unicode_characters(self) -> None:
        """Test truncation with unicode characters."""
        text = "你好世界" * 10  # "Hello World" in Chinese, 4 chars * 10 = 40 chars
        # Max length 10: "你好世界你好世界你好" (10 chars)
        expected = "你好世界你好世界你好... (truncated)"
        assert truncate_for_logging(text, 10) == expected


@patch("claims_agent.agents.agent_logging_utils.truncate_for_logging")
class TestLogIntermediateSteps:
    """Tests for the log_intermediate_steps utility function."""

    def test_valid_intermediate_steps(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test logging with valid intermediate steps."""
        mock_truncate.side_effect = lambda x, max_length=None: f"truncated({x})"
        response = {
            "intermediate_steps": [
                ("tool_call_1_very_long", "observation_1_very_long"),
                ("tool_call_2", "observation_2"),
            ]
        }
        with caplog.at_level(logging.INFO):
            log_intermediate_steps(response)

        assert "Agent intermediate steps (2):" in caplog.text
        assert "Step 1 Tool Call: truncated(tool_call_1_very_long)" in caplog.text
        assert "Step 1 Observation: truncated(observation_1_very_long)" in caplog.text
        assert "Step 2 Tool Call: truncated(tool_call_2)" in caplog.text
        assert "Step 2 Observation: truncated(observation_2)" in caplog.text
        assert mock_truncate.call_count == 4

    def test_empty_intermediate_steps(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test with an empty list of intermediate steps."""
        response: dict[str, Any] = {"intermediate_steps": []}
        with caplog.at_level(logging.INFO):
            log_intermediate_steps(response)
        assert "Agent intermediate steps" not in caplog.text
        assert mock_truncate.call_count == 0

    def test_no_intermediate_steps_key(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test with no 'intermediate_steps' key in response."""
        response: dict[str, Any] = {"output": "some_output"}
        with caplog.at_level(logging.INFO):
            log_intermediate_steps(response)
        assert "Agent intermediate steps" not in caplog.text
        assert mock_truncate.call_count == 0

    def test_intermediate_steps_is_none(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test with 'intermediate_steps' key present but value is None."""
        response = {"intermediate_steps": None}
        with caplog.at_level(logging.INFO):
            log_intermediate_steps(response)
        assert "Agent intermediate steps" not in caplog.text
        assert mock_truncate.call_count == 0

    def test_intermediate_steps_not_a_list(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test with 'intermediate_steps' being a string instead of a list."""
        response = {"intermediate_steps": "not a list"}
        with caplog.at_level(logging.WARNING):
            log_intermediate_steps(response)
        assert (
            "'intermediate_steps' key found but is not a list: <class 'str'>"
            in caplog.text
        )
        assert mock_truncate.call_count == 0

    def test_step_item_not_tuple_or_list(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test with a step item that is not a tuple/list."""
        response: dict[str, Any] = {"intermediate_steps": ["just a string"]}
        with caplog.at_level(logging.INFO):
            log_intermediate_steps(response)

        # Check for both the general info header and the specific warning
        assert any(
            record.levelname == "INFO"
            and "Agent intermediate steps (1):" in record.message
            for record in caplog.records
        )
        assert any(
            record.levelname == "WARNING"
            and "Step 1 has unexpected format: <class 'str'>" in record.message
            for record in caplog.records
        )
        assert mock_truncate.call_count == 0

    def test_step_item_tuple_wrong_length(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test with a step item tuple of incorrect length."""
        response: dict[str, Any] = {"intermediate_steps": [("tool_call_only",)]}
        with caplog.at_level(logging.INFO):
            log_intermediate_steps(response)

        # Check for both the general info header and the specific warning
        assert any(
            record.levelname == "INFO"
            and "Agent intermediate steps (1):" in record.message
            for record in caplog.records
        )
        assert any(
            record.levelname == "WARNING"
            and "Step 1 has unexpected format: <class 'tuple'>" in record.message
            for record in caplog.records
        )
        assert mock_truncate.call_count == 0


@patch("claims_agent.agents.agent_logging_utils.truncate_for_logging")
class TestTraceToolsExecution:
    """Tests for the trace_tools_execution utility function."""

    def test_executor_with_tools(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test logging tools from an AgentExecutor with tools."""
        mock_truncate.side_effect = lambda x, max_length=None: f"truncated({x})"

        mock_tool1 = MagicMock()
        mock_tool1.name = "Tool1"
        mock_tool1.description = "Description for Tool1 which is very long."

        mock_tool2 = MagicMock()
        mock_tool2.name = "Tool2"
        # No description attribute
        del mock_tool2.description  # to test getattr default

        mock_tool3 = MagicMock()
        # No name attribute
        del mock_tool3.name
        mock_tool3.description = "Tool3 Description"

        mock_executor = MagicMock(spec=AgentExecutor)
        mock_executor.tools = [mock_tool1, mock_tool2, mock_tool3]

        with caplog.at_level(logging.INFO):
            trace_tools_execution(mock_executor)

        assert "Agent has 3 tools available:" in caplog.text
        assert (
            "Tool 1: Tool1 - truncated(Description for Tool1 which is very long.)"
            in caplog.text
        )
        # Default description
        assert "Tool 2: Tool2 - truncated(No description)" in caplog.text
        # Default name
        assert "Tool 3: Tool-3 - truncated(Tool3 Description)" in caplog.text
        # truncate called for each description
        assert mock_truncate.call_count == 3
        # Check that truncate was called with max_length=100 for descriptions
        for call_args in mock_truncate.call_args_list:
            assert call_args[1].get("max_length") == 100

    def test_executor_with_no_tools(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test logging with an AgentExecutor that has an empty tools list."""
        mock_executor = MagicMock(spec=AgentExecutor)
        mock_executor.tools = []
        with caplog.at_level(logging.WARNING):
            trace_tools_execution(mock_executor)
        assert (
            "No tools are available to the agent - this will prevent proper functioning"
            in caplog.text
        )
        assert mock_truncate.call_count == 0

    def test_executor_is_none(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test with agent_executor being None."""
        with caplog.at_level(logging.WARNING):
            trace_tools_execution(None)  # type: ignore
        assert (
            "Cannot trace tools - agent executor not provided or not properly initialized"
            in caplog.text
        )
        assert mock_truncate.call_count == 0

    def test_executor_no_tools_attribute(
        self, mock_truncate: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        """Test with an agent_executor object that lacks a 'tools' attribute."""
        mock_executor_no_tools_attr = MagicMock()
        # Ensure 'tools' attribute is not present
        if hasattr(mock_executor_no_tools_attr, "tools"):
            delattr(mock_executor_no_tools_attr, "tools")

        with caplog.at_level(logging.WARNING):
            trace_tools_execution(mock_executor_no_tools_attr)  # type: ignore
        assert (
            "Cannot trace tools - agent executor not provided or not properly initialized"
            in caplog.text
        )
        assert mock_truncate.call_count == 0
