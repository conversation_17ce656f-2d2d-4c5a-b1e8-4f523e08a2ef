"""Unit tests for the MCPAgent class's language consistency refinement methods."""

from unittest.mock import AsyncMock, MagicMock

import pytest
from langchain.agents import AgentExecutor

from claims_agent.agents.mcp_agent import MCPAgent
from .conftest import get_test_mcp_agent


@pytest.mark.asyncio
@pytest.mark.usefixtures(
    "mock_create_agent_prompt_template_func",
)
class TestMCPAgentLanguageConsistencyRefinement:
    """Tests for MCPAgent.apply_language_consistency_refinement method."""

    @pytest.fixture
    def agent_instance(
        self,
        mock_mcp_client_manager: MagicMock,
        mock_mcp_tool_service: MagicMock,
        mock_agent_executor_factory: MagicMock,
    ) -> MCPAgent:
        """Provides a configured MCPAgent instance for tests in this class."""
        return get_test_mcp_agent(
            mock_mcp_client_manager,
            mock_mcp_tool_service,
            mock_agent_executor_factory,
        )

    async def test_apply_language_consistency_refinement_success(
        self,
        agent_instance: MC<PERSON><PERSON>,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test successful language consistency refinement."""
        # Arrange
        refinement_prompt = "Refine this coverage note: {some_json}"
        expected_refined_output = '{"refined": "coverage note"}'

        # Mock the agent executor
        mock_executor = MagicMock(spec=AgentExecutor)
        mock_executor.ainvoke = AsyncMock(
            return_value={"output": expected_refined_output}
        )
        mock_agent_executor_factory.create_executor.return_value = mock_executor

        # Act
        result = await agent_instance.apply_language_consistency_refinement(
            refinement_prompt=refinement_prompt
        )

        # Assert
        assert result == expected_refined_output

        # Verify the executor was created with no tools
        mock_agent_executor_factory.create_executor.assert_called_once()
        call_args = mock_agent_executor_factory.create_executor.call_args
        assert call_args.kwargs["tools"] == []

        # Verify the executor was called with correct input
        mock_executor.ainvoke.assert_called_once_with(
            {
                "input": refinement_prompt,
                "chat_history": [],
            }
        )

    async def test_apply_language_consistency_refinement_empty_output(
        self,
        agent_instance: MCPAgent,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test refinement when LLM returns empty output."""
        # Arrange
        refinement_prompt = "Refine this coverage note: {some_json}"

        # Mock the agent executor to return empty output
        mock_executor = MagicMock(spec=AgentExecutor)
        mock_executor.ainvoke = AsyncMock(return_value={"output": ""})
        mock_agent_executor_factory.create_executor.return_value = mock_executor

        # Act
        result = await agent_instance.apply_language_consistency_refinement(
            refinement_prompt=refinement_prompt
        )

        # Assert
        assert result == ""
        mock_executor.ainvoke.assert_called_once()

    async def test_apply_language_consistency_refinement_none_output(
        self,
        agent_instance: MCPAgent,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test refinement when LLM returns None output."""
        # Arrange
        refinement_prompt = "Refine this coverage note: {some_json}"

        # Mock the agent executor to return None output
        mock_executor = MagicMock(spec=AgentExecutor)
        mock_executor.ainvoke = AsyncMock(return_value={"output": None})
        mock_agent_executor_factory.create_executor.return_value = mock_executor

        # Act
        result = await agent_instance.apply_language_consistency_refinement(
            refinement_prompt=refinement_prompt
        )

        # Assert
        assert result == "None"  # str(None) = "None"
        mock_executor.ainvoke.assert_called_once()

    async def test_apply_language_consistency_refinement_missing_output_key(
        self,
        agent_instance: MCPAgent,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test refinement when result has no output key."""
        # Arrange
        refinement_prompt = "Refine this coverage note: {some_json}"

        # Mock the agent executor to return result without output key
        mock_executor = MagicMock(spec=AgentExecutor)
        mock_executor.ainvoke = AsyncMock(return_value={"some_other_key": "value"})
        mock_agent_executor_factory.create_executor.return_value = mock_executor

        # Act
        result = await agent_instance.apply_language_consistency_refinement(
            refinement_prompt=refinement_prompt
        )

        # Assert
        assert result == ""  # Default when key is missing
        mock_executor.ainvoke.assert_called_once()

    async def test_apply_language_consistency_refinement_executor_creation_fails(
        self,
        agent_instance: MCPAgent,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test refinement when agent executor creation fails."""
        # Arrange
        refinement_prompt = "Refine this coverage note: {some_json}"

        # Mock the factory to raise an exception
        mock_agent_executor_factory.create_executor.side_effect = ValueError(
            "Factory failed"
        )

        # Act
        result = await agent_instance.apply_language_consistency_refinement(
            refinement_prompt=refinement_prompt
        )

        # Assert
        assert result == ""  # Should return empty string on failure
        mock_agent_executor_factory.create_executor.assert_called_once()

    async def test_apply_language_consistency_refinement_executor_invoke_fails(
        self,
        agent_instance: MCPAgent,
        mock_agent_executor_factory: MagicMock,
    ) -> None:
        """Test refinement when executor invocation fails."""
        # Arrange
        refinement_prompt = "Refine this coverage note: {some_json}"

        # Mock the agent executor to raise an exception on invoke
        mock_executor = MagicMock(spec=AgentExecutor)
        mock_executor.ainvoke = AsyncMock(side_effect=RuntimeError("Invoke failed"))
        mock_agent_executor_factory.create_executor.return_value = mock_executor

        # Act
        result = await agent_instance.apply_language_consistency_refinement(
            refinement_prompt=refinement_prompt
        )

        # Assert
        assert result == ""  # Should return empty string on failure
        mock_executor.ainvoke.assert_called_once()
