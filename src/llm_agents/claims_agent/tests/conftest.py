"""Test fixtures for the Claims Agent service."""

import contextlib
import logging
from collections.abc import Generator
from collections.abc import AsyncGenerator
import os
from typing import Any
from unittest.mock import MagicMock
from temporalio.contrib.pydantic import pydantic_data_converter
from claims_agent.db.repositories.coverage_determinations import (
    SupabaseCoverageDeterminationRequestRepository,
)
import pytest
from fastapi import FastAPI
from loguru import logger
from pytest_mock import MockFixture
from httpx import AsyncClient, ASGITransport
from temporalio.client import Client as TemporalClient
from temporalio.testing import WorkflowEnvironment
import pytest_asyncio
from testcontainers.postgres import PostgresContainer
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
import sqlalchemy as sa
from claims_agent.db.orm.generated import LegacyCoverageDeterminations

from claims_agent.interfaces.agents import (
    AgentProtocol,
)
from claims_agent.api import create_app
from claims_agent.config import Settings
from claims_agent.api.services import ClaimsServiceProtocol
from claims_agent.di import build_container
from claims_agent.db.orm.generated import Base


# Fixture to capture Loguru logs with caplog
@pytest.fixture(autouse=True)
def caplog_loguru(
    caplog: pytest.LogCaptureFixture,
) -> Generator[pytest.LogCaptureFixture, Any, None]:
    """Fixture to capture Loguru logs and propagate them to caplog."""
    loguru_handler_id = None

    class PropagateHandler(logging.Handler):
        def emit(self, record: logging.LogRecord) -> None:
            logging.getLogger(record.name).handle(record)

    # Remove default handler(s) if they exist to avoid duplicate output
    with contextlib.suppress(ValueError):
        logger.remove(0)

    # Add a handler that propagates messages to the standard logging system
    loguru_handler_id = logger.add(
        PropagateHandler(), format="{message}", level="DEBUG", enqueue=False
    )

    # Configure caplog to capture from the root logger
    caplog.set_level(logging.DEBUG, logger="")

    yield caplog

    # Clean up: remove the handler we added
    if loguru_handler_id is not None:
        logger.remove(loguru_handler_id)
    # Re-add a default handler if desired (e.g., for non-test execution)
    # logger.add(sys.stderr, level="INFO")


@pytest.fixture
def mock_settings() -> Settings:
    """Settings instance."""
    return Settings(
        ENVIRONMENT="testing",
        LOG_LEVEL="DEBUG",
        AGENT_VERBOSE=True,
        ARIZE_ENABLED=False,
    )


@pytest.fixture
def mock_claims_service(mocker: MockFixture) -> MagicMock:
    """Fixture to create a mock claims service."""
    mock = mocker.create_autospec(ClaimsServiceProtocol, instance=True)
    # Ensure the health_check method is available and returns a healthy status by default
    mock.health_check = mocker.AsyncMock(return_value=(True, "Service is healthy"))
    return mock


@pytest.fixture
def mock_temporal_client(mocker: MockFixture) -> MagicMock:
    """Fixture to create a mock temporal client."""
    return mocker.create_autospec(TemporalClient, instance=True)


# Mock authentication dependency override for testing
@pytest.fixture
def mock_authentication_middleware(mocker: MockFixture):
    """Override authentication middleware for testing."""
    from claims_agent.api.middleware import AuthenticationMiddleware

    # Mock the authentication validation to always pass
    async def mock_dispatch(self, request, call_next):
        # Skip authentication validation
        return await call_next(request)

    return mocker.patch.object(AuthenticationMiddleware, "dispatch", mock_dispatch)


@pytest.fixture
def app(
    mock_claims_service: MagicMock,
    mock_temporal_client: MagicMock,
    mock_agent: AgentProtocol,
    coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
    coverage_run_repository,
    mock_coverage_notes_repository: MagicMock,
    mock_settings: Settings,
    clean_database,  # Ensure database is clean before each test
    mock_authentication_middleware,  # Add authentication mocking
) -> Generator[FastAPI, Any, None]:
    """FastAPI app instance with service dependencies overridden for testing."""
    from claims_agent.interfaces.repositories import (
        CoverageDeterminationRequestRepositoryProtocol,
        CoverageNotesRepositoryProtocol,
        CoverageRunRepositoryProtocol,
    )

    injector = build_container()
    # Bind the test settings with proper database URL
    injector.binder.bind(Settings, to=mock_settings)
    injector.binder.bind(TemporalClient, to=mock_temporal_client)
    injector.binder.bind(AgentProtocol, to=mock_agent)
    injector.binder.bind(ClaimsServiceProtocol, to=mock_claims_service)
    injector.binder.bind(
        CoverageDeterminationRequestRepositoryProtocol, to=coverage_determination_repo
    )
    injector.binder.bind(CoverageRunRepositoryProtocol, to=coverage_run_repository)
    # Bind the mock coverage notes repository
    injector.binder.bind(
        CoverageNotesRepositoryProtocol, to=mock_coverage_notes_repository
    )

    app = create_app(injector)
    yield app
    app.dependency_overrides = {}


@pytest.fixture
async def client(app: FastAPI) -> AsyncGenerator[AsyncClient, None]:
    """Create a test client for the FastAPI application."""
    async with app.router.lifespan_context(app):
        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            try:
                yield client
            finally:
                await client.aclose()


@pytest.fixture
async def authenticated_client(app: FastAPI) -> AsyncGenerator[AsyncClient, None]:
    """Create an authenticated test client for the FastAPI application."""
    async with app.router.lifespan_context(app):
        async with AsyncClient(
            transport=ASGITransport(app=app),
            base_url="http://test",
            headers={"Authorization": "Bearer test-token"},
        ) as client:
            try:
                yield client
            finally:
                await client.aclose()


@pytest.fixture
def mock_agent(mocker: MockFixture) -> AgentProtocol:
    """Create a mock agent implementing the AgentProtocol."""
    # Create a properly typed mock that satisfies the AgentProtocol
    mock = mocker.Mock(spec=AgentProtocol)

    # Ensure the mock has the required protocol methods
    mock.process_prompt = mocker.AsyncMock()
    mock.determine_coverage = mocker.AsyncMock()
    mock._initialize_resources = mocker.AsyncMock()
    mock._cleanup_resources = mocker.AsyncMock()

    return mock


@pytest.fixture(scope="session")
def postgres_container():
    """
    A pytest fixture that starts and stops a Postgres container for the test session.
    """
    with PostgresContainer("postgres:16-alpine") as postgres:
        yield postgres


@pytest_asyncio.fixture(scope="function")
async def db_engine(postgres_container: PostgresContainer):
    """
    A pytest fixture that provides a SQLAlchemy engine connected to the test container.
    It also creates all tables based on the ORM metadata.
    """
    conn_url = postgres_container.get_connection_url().replace("psycopg2", "asyncpg")
    engine = create_async_engine(conn_url)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield engine
    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def session_maker(db_engine):
    """
    A pytest fixture that provides an async_sessionmaker for the test database.
    """
    yield async_sessionmaker(db_engine, expire_on_commit=False, class_=AsyncSession)


@pytest_asyncio.fixture(scope="function")
async def coverage_determination_repo(
    session_maker: async_sessionmaker[AsyncSession],
) -> SupabaseCoverageDeterminationRequestRepository:
    return SupabaseCoverageDeterminationRequestRepository(session_maker)


@pytest_asyncio.fixture(scope="function")
async def coverage_run_repository(
    session_maker: async_sessionmaker[AsyncSession],
):
    """Create a CoverageRunRepository instance for testing."""
    from claims_agent.db.repositories.coverage_runs import CoverageRunRepository

    return CoverageRunRepository(session_maker)


@pytest.fixture
def mock_coverage_notes_repository(mocker) -> MagicMock:
    """Create a mock coverage notes repository for testing."""
    from claims_agent.db.repositories.coverage_notes import CoverageNotesRepository

    return mocker.Mock(spec=CoverageNotesRepository)


@pytest.fixture
async def clean_database(session_maker):
    """Clean up database before each test."""
    from claims_agent.db.orm.generated import CoverageNotes, CoverageRuns

    async with session_maker() as session:
        # Delete in order to respect foreign key constraints
        await session.execute(sa.delete(CoverageNotes))
        await session.execute(sa.delete(CoverageRuns))
        await session.execute(sa.delete(LegacyCoverageDeterminations))
        await session.commit()


@pytest_asyncio.fixture(scope="function")
async def temporal_env() -> AsyncGenerator[WorkflowEnvironment, None]:
    # From temporalio python-sdk docs:
    # > NOTE: The time-skipping test environment does not work on ARM.
    # > The SDK will try to download the x64 binary on macOS for use with the Intel
    # > emulator, but for Linux or Windows ARM there is no proper time-skipping test
    # > server at this time.
    # Due to this, we use the local test environment instead.

    # Use fast time-skipping if the ENV VAR is set.
    if os.environ.get("TEMPORAL_TEST_ENV") == "time-skipping":
        env = await WorkflowEnvironment.start_time_skipping(
            data_converter=pydantic_data_converter
        )
    else:
        # Otherwise, use the slower but more compatible local server
        env = await WorkflowEnvironment.start_local(
            data_converter=pydantic_data_converter
        )
    yield env
    await env.shutdown()


@pytest_asyncio.fixture(scope="function")
async def temporal_client(temporal_env: WorkflowEnvironment) -> TemporalClient:
    return temporal_env.client
