import datetime
import uuid
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Type

import pytest
from temporalio import activity
from temporalio.client import Client as TemporalClient
from temporalio.client import WorkflowFailureError
from temporalio.exceptions import ApplicationError
from temporalio.worker import Worker as <PERSON><PERSON><PERSON><PERSON>orker

from claims_agent.background.workflows.coverage_determination import (
    CoverageDeterminationActivities,
    CoverageDeterminationRequest,
    CoverageDeterminationResult,
    CoverageDeterminationWorkflow,
)
from claims_agent.interfaces.agents import AgentProtocol
from claims_agent.interfaces.legacy_models import (
    Citation as LegacyCitation,
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
    Document as LegacyDocument,
    VerificationItem as LegacyVerificationItem,
)
from claims_agent.interfaces.repositories import (
    CoverageDeterminationRequestRepositoryProtocol,
    CreateCoverageDeterminationRequest,
)


@dataclass
class WorkflowScenario:
    """
    Defines a single test scenario for the CoverageDeterminationWorkflow.

    This data-driven approach allows for defining multiple test cases (happy
    paths, failure modes, etc.) as simple data objects. The main test function
    can then be parameterized to run against a list of these scenarios,
    drastically reducing boilerplate code.

    Attributes:
        name: A descriptive name for the test scenario.
        agent_behavior_factory: A callable that returns the agent's mock response
                                or an exception to be raised.
        expected_final_status: The expected status of the request in the
                               database after the workflow concludes.
        activity_side_effects: A dictionary mapping activity names to exceptions
                               they should raise, for simulating failures.
        expected_workflow_exception: The type of exception expected from the
                                     workflow execution, if any.
    """

    name: str
    agent_behavior_factory: Callable[["WorkflowTestHarness"], Any]
    expected_final_status: str
    activity_side_effects: Dict[str, Exception] = field(default_factory=dict)
    expected_workflow_exception: Optional[Type[Exception]] = None


scenarios = [
    WorkflowScenario(
        name="happy_path",
        agent_behavior_factory=lambda h: h.expected_response,
        expected_final_status="succeeded",
    ),
    WorkflowScenario(
        name="core_logic_failure",
        agent_behavior_factory=lambda _: ApplicationError(
            "Agent failed to determine coverage"
        ),
        expected_final_status="failed",
        expected_workflow_exception=WorkflowFailureError,
    ),
    WorkflowScenario(
        name="initial_state_update_failure",
        agent_behavior_factory=lambda h: h.expected_response,
        # The workflow's outer exception handler will set the status to failed
        expected_final_status="failed",
        activity_side_effects={
            "set_in_progress": ApplicationError(
                "Failed to update status to in_progress"
            )
        },
        expected_workflow_exception=WorkflowFailureError,
    ),
    WorkflowScenario(
        name="final_state_update_failure",
        agent_behavior_factory=lambda h: h.expected_response,
        expected_final_status="failed",
        activity_side_effects={
            "set_succeeded": ApplicationError("Failed to update status to succeeded")
        },
        expected_workflow_exception=WorkflowFailureError,
    ),
    WorkflowScenario(
        name="set_failed_activity_failure",
        agent_behavior_factory=lambda _: ApplicationError(
            "Agent failed to determine coverage"
        ),
        # The last successful update was set_in_progress
        expected_final_status="in_progress",
        activity_side_effects={
            "set_failed": ApplicationError("Failed to update status to failed")
        },
        expected_workflow_exception=WorkflowFailureError,
    ),
    WorkflowScenario(
        name="agent_returns_invalid_data",
        agent_behavior_factory=lambda _: "this is not valid response data",
        expected_final_status="failed",
        expected_workflow_exception=WorkflowFailureError,
    ),
]


@dataclass
class WorkflowTestHarness:
    """
    A data container for objects common across all workflow test scenarios.

    This class centralizes the creation and management of standard test objects,
    such as request IDs, claim details, and the workflow request object itself.
    Using a dataclass for this purpose ensures that all tests operate on a
    consistent and predictable set of inputs.

    Attributes:
        request_id: A unique identifier for the coverage determination request.
        claim_id: The identifier for the claim being processed.
        workflow_id: The unique ID assigned to this Temporal workflow execution.
        authorization: A mock authorization token for the request.
        request: The structured request object passed to the workflow.
        expected_response: A sample successful response object, used for mocking
                           the agent's behavior in happy path scenarios.
    """

    request_id: uuid.UUID
    claim_id: str
    workflow_id: str
    authorization: str
    request: CoverageDeterminationRequest
    expected_response: LegacyCoverageDeterminationResponse


@pytest.fixture
def harness() -> WorkflowTestHarness:
    """
    Provides a pytest fixture for a standard `WorkflowTestHarness`.

    This fixture instantiates the `WorkflowTestHarness` with consistent,
    pre-defined values, making it easy to inject a standard test setup
    into any test function.
    """
    request_id = uuid.uuid4()
    claim_id = "claim_123"
    authorization = "auth_token"
    expected_response = LegacyCoverageDeterminationResponse(
        verifications=[
            LegacyVerificationItem(
                name="test_verification",
                assessment_score=1.0,
                summary="This is a test response.",
                citation=LegacyCitation(
                    excerpt="This is a test response.",
                    pages=[1],
                    document=LegacyDocument(
                        policy_number="NISTK0017619-23",
                        document_id=uuid.uuid4(),
                        filename="test.pdf",
                    ),
                    bounding_box=[0.0, 0.0, 100.0, 100.0],
                ),
            )
        ]
    )

    return WorkflowTestHarness(
        request_id=request_id,
        claim_id=claim_id,
        workflow_id=f"workflow-{str(uuid.uuid4())}",
        authorization=authorization,
        request=CoverageDeterminationRequest(
            request_id=request_id,
            claim_id=claim_id,
            authorization=authorization,
        ),
        expected_response=expected_response,
    )


@pytest.mark.temporal
class TestCoverageDeterminationWorkflow:
    """
    A test suite for the CoverageDeterminationWorkflow.

    This class groups together all end-to-end tests for the coverage
    determination workflow. It uses a class-based structure to share common
    setup and helper methods, making the individual tests cleaner and more
    focused on their specific scenarios. Each test method corresponds to a
    distinct workflow execution path (e.g., happy path, specific activity
    failures).
    """

    async def _insert_initial_request(
        self,
        repo: CoverageDeterminationRequestRepositoryProtocol,
        harness: WorkflowTestHarness,
    ):
        """Helper to insert the initial request record into the database."""
        await repo.insert(
            CreateCoverageDeterminationRequest(
                request_id=harness.request_id,
                claim_id=harness.claim_id,
                workflow_id=harness.workflow_id,
                timestamp=datetime.datetime.now(datetime.timezone.utc),
            )
        )

    async def _execute_workflow(
        self,
        temporal_client: TemporalClient,
        harness: WorkflowTestHarness,
        activities_to_run: List[Any],
    ) -> CoverageDeterminationResult:
        """
        A helper method to encapsulate the boilerplate of running a Temporal workflow.

        This method sets up a TemporalWorker with the specified activities, starts
        the workflow, and returns the workflow handle. This allows individual
        test methods to focus on defining their specific arrangements and
        assertions without repeating the workflow execution logic.

        Args:
            temporal_client: The connected Temporal client.
            harness: The test harness containing common test objects.
            activities_to_run: A list of activity functions to be registered with the worker.

        Returns:
            The result of the workflow execution.
        """
        task_queue = str(uuid.uuid4())
        async with TemporalWorker(
            temporal_client,
            task_queue=task_queue,
            workflows=[CoverageDeterminationWorkflow],
            activity_executor=ThreadPoolExecutor(5),
            activities=activities_to_run,
        ):
            return await temporal_client.execute_workflow(
                CoverageDeterminationWorkflow.run,
                harness.request,
                id=harness.workflow_id,
                task_queue=task_queue,
            )

    @pytest.mark.parametrize("scenario", scenarios, ids=lambda s: s.name)
    async def test_coverage_determination_workflow(
        self,
        temporal_client: TemporalClient,
        mock_agent: AgentProtocol,
        coverage_determination_repo: CoverageDeterminationRequestRepositoryProtocol,
        harness: WorkflowTestHarness,
        scenario: WorkflowScenario,
    ):
        """
        Runs a parameterized test for all coverage determination workflow scenarios.

        This single test method uses the data-driven `WorkflowScenario` objects
        to execute a variety of test cases. It dynamically configures mock behaviors,
        simulates activity failures, and asserts the expected outcomes, providing
        comprehensive test coverage without code duplication.
        """
        # Arrange
        # Set up the agent's primary behavior (return a value or raise an exception)
        if scenario.name == "core_logic_recovers_after_retry":
            mock_agent.determine_coverage.side_effect = [
                ApplicationError("Agent failed temporarily"),
                harness.expected_response,
            ]
            agent_behavior = harness.expected_response
        else:
            agent_behavior = scenario.agent_behavior_factory(harness)
            if isinstance(agent_behavior, Exception):
                mock_agent.determine_coverage.side_effect = agent_behavior
            else:
                mock_agent.determine_coverage.return_value = agent_behavior

        await self._insert_initial_request(coverage_determination_repo, harness)

        # Create base activities and then mock any that are meant to fail
        base_activities = CoverageDeterminationActivities(
            agent=mock_agent, request_repository=coverage_determination_repo
        )
        activity_implementations = {
            func.__name__: func
            for func in [
                base_activities.set_in_progress,
                base_activities.run_determine_coverage,
                base_activities.set_succeeded,
                base_activities.set_failed,
            ]
        }

        for name, error in scenario.activity_side_effects.items():

            def make_failing_activity(captured_error):
                @activity.defn(name=f"CoverageDeterminationActivities.{name}")
                async def fail(*args, **kwargs):
                    raise captured_error

                return fail

            activity_implementations[name] = make_failing_activity(error)

        # Act
        # Assert workflow outcome (success or failure)
        if scenario.expected_workflow_exception:
            with pytest.raises(scenario.expected_workflow_exception):
                result = await self._execute_workflow(
                    temporal_client, harness, list(activity_implementations.values())
                )
        else:
            result = await self._execute_workflow(
                temporal_client, harness, list(activity_implementations.values())
            )
            parsed_result = LegacyCoverageDeterminationResponse.model_validate_json(
                result.content
            )
            assert parsed_result == agent_behavior

        # Assert final database state
        repo_result = await coverage_determination_repo.get(harness.request_id)
        assert repo_result.status == scenario.expected_final_status
        # In the case where set_failed fails, the content will not be updated with the error
        if scenario.name == "set_failed_activity_failure":
            assert repo_result.content is None
        elif scenario.expected_final_status == "succeeded":
            assert (
                LegacyCoverageDeterminationResponse.model_validate(repo_result.content)
                == agent_behavior
            )
        elif scenario.expected_final_status == "failed":
            assert repo_result.error is not None

        if scenario.name == "core_logic_recovers_after_retry":
            assert mock_agent.determine_coverage.call_count == 2
