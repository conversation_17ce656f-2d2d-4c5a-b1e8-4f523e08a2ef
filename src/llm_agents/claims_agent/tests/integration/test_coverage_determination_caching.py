"""Integration tests for coverage determination caching functionality."""

import asyncio
import datetime
import uuid

import pytest
import sqlalchemy as sa

from claims_agent.db.orm.generated import LegacyCoverageDeterminations
from claims_agent.db.repositories.coverage_determinations import (
    SupabaseCoverageDeterminationRequestRepository,
)
from claims_agent.interfaces.repositories import (
    CoverageDeterminationStatus,
    CreateCoverageDeterminationRequest,
)
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
    VerificationItem as LegacyVerificationItem,
    Citation as LegacyCitation,
    Document as LegacyDocument,
)


class TestCoverageDeterminationCachingIntegration:
    """Integration tests for coverage determination caching with real database."""

    @pytest.fixture
    def sample_coverage_response(self) -> LegacyCoverageDeterminationResponse:
        """Create a sample coverage determination response."""
        return LegacyCoverageDeterminationResponse(
            verifications=[
                LegacyVerificationItem(
                    name="Integration Test Verification",
                    assessment_score=1.0,
                    summary="Integration test verification item.",
                    citation=LegacyCitation(
                        excerpt="Integration test excerpt.",
                        document=LegacyDocument(
                            policy_number="POLICY-INTEGRATION",
                            document_id=uuid.UUID(
                                "12345678-1234-5678-1234-************"
                            ),
                            filename="integration_test.pdf",
                        ),
                        pages=[1],
                    ),
                    verification_type="policy",
                )
            ]
        )

    async def _create_and_complete_request(
        self,
        repo: SupabaseCoverageDeterminationRequestRepository,
        claim_id: str,
        response: LegacyCoverageDeterminationResponse,
        timestamp: datetime.datetime | None = None,
    ) -> uuid.UUID:
        """Helper to create and complete a coverage determination request."""
        request_id = uuid.uuid4()
        ts = timestamp or datetime.datetime.now(datetime.timezone.utc)

        # Create request
        await repo.insert(
            CreateCoverageDeterminationRequest(
                request_id=request_id,
                claim_id=claim_id,
                workflow_id=f"workflow-{request_id}",
                timestamp=ts,
            )
        )

        # Set in progress
        await repo.set_in_progress(request_id, ts)

        # Complete successfully
        await repo.set_succeeded(request_id, ts, response)

        return request_id

    @pytest.mark.asyncio
    async def test_end_to_end_cache_workflow(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test complete end-to-end cache workflow."""
        claim_id = "NITMS25030300"

        # Step 1: No cache exists - should return None
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )
        assert result is None

        # Step 2: Create and complete a request
        request_id = await self._create_and_complete_request(
            coverage_determination_repo,
            claim_id,
            sample_coverage_response,
        )

        # Step 3: Cache should now hit
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )
        assert result is not None
        assert result == sample_coverage_response

        # Step 4: Verify the database record exists
        session_maker = coverage_determination_repo._session_maker
        async with session_maker() as session:
            db_record = await session.get(LegacyCoverageDeterminations, request_id)
            assert db_record is not None
            assert db_record.claim_id == claim_id
            assert db_record.status == CoverageDeterminationStatus.SUCCEEDED
            assert db_record.content is not None

    @pytest.mark.asyncio
    async def test_concurrent_requests_same_claim(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test handling of concurrent requests for the same claim."""
        claim_id = "NITMS25030301"

        # Pre-populate cache
        await self._create_and_complete_request(
            coverage_determination_repo,
            claim_id,
            sample_coverage_response,
        )

        # Create multiple concurrent cache lookups
        async def cache_lookup():
            return await coverage_determination_repo.get_latest_successful(
                claim_id=claim_id,
                max_age_seconds=3600,
            )

        # Run 5 concurrent lookups
        tasks = [cache_lookup() for _ in range(5)]
        results = await asyncio.gather(*tasks)

        # All should return the same cached result
        for result in results:
            assert result is not None
            assert result == sample_coverage_response

    @pytest.mark.asyncio
    async def test_cache_behavior_across_multiple_claims(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test cache behavior with multiple different claims."""
        claim_ids = ["NITMS25030302", "NITMS25030303", "NITMS25030304"]

        # Create different responses for each claim
        responses = []
        for i, claim_id in enumerate(claim_ids):
            response = LegacyCoverageDeterminationResponse(
                verifications=[
                    LegacyVerificationItem(
                        name=f"Verification {i}",
                        assessment_score=float(i + 1) / 10,
                        summary=f"Test verification {i}.",
                        citation=LegacyCitation(
                            excerpt=f"Test excerpt {i}.",
                            document=LegacyDocument(
                                policy_number=f"POLICY-{i}",
                                document_id=uuid.UUID(
                                    f"12345678-1234-5678-1234-56781234567{i}"
                                ),
                                filename=f"test_{i}.pdf",
                            ),
                            pages=[i + 1],
                        ),
                        verification_type="policy",
                    )
                ]
            )
            responses.append(response)

            # Create completed request for each claim
            await self._create_and_complete_request(
                coverage_determination_repo,
                claim_id,
                response,
            )

        # Verify each claim has its own cached response
        for i, claim_id in enumerate(claim_ids):
            result = await coverage_determination_repo.get_latest_successful(
                claim_id=claim_id,
                max_age_seconds=3600,
            )
            assert result is not None
            assert result == responses[i]

    @pytest.mark.asyncio
    async def test_ttl_boundary_conditions(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test TTL boundary conditions with precise timing."""
        claim_id = "NITMS25030305"

        # Create request with specific timestamp
        base_time = datetime.datetime.now(datetime.timezone.utc)
        await self._create_and_complete_request(
            coverage_determination_repo,
            claim_id,
            sample_coverage_response,
            base_time,
        )

        # Test cache hit with TTL of 1 hour
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )
        assert result is not None

        # Test cache miss with TTL of 0 seconds
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=0,
        )
        assert result is None

        # Test cache miss with very small TTL
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=1,
        )
        # This might hit or miss depending on timing, but should not raise error
        assert result is None or result == sample_coverage_response

    @pytest.mark.asyncio
    async def test_cache_with_failed_requests_mixed(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test cache behavior with mix of successful and failed requests."""
        claim_id = "NITMS25030306"

        # Create a failed request first
        failed_request_id = uuid.uuid4()
        ts = datetime.datetime.now(datetime.timezone.utc)

        await coverage_determination_repo.insert(
            CreateCoverageDeterminationRequest(
                request_id=failed_request_id,
                claim_id=claim_id,
                workflow_id=f"workflow-{failed_request_id}",
                timestamp=ts,
            )
        )

        await coverage_determination_repo.set_in_progress(failed_request_id, ts)

        from claims_agent.interfaces.repositories import CoverageDeterminationError

        await coverage_determination_repo.set_failed(
            failed_request_id,
            ts,
            CoverageDeterminationError(kind="TestError", message="Test failure"),
        )

        # Cache lookup should return None (failed request ignored)
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )
        assert result is None

        # Now create a successful request
        await self._create_and_complete_request(
            coverage_determination_repo,
            claim_id,
            sample_coverage_response,
        )

        # Cache lookup should now return the successful result
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )
        assert result is not None
        assert result == sample_coverage_response

    @pytest.mark.asyncio
    async def test_cache_with_multiple_successful_requests_returns_latest(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that cache returns the latest successful request when multiple exist."""
        claim_id = "NITMS25030307"

        # Create older successful request
        older_response = LegacyCoverageDeterminationResponse(
            verifications=[
                LegacyVerificationItem(
                    name="Older Verification",
                    assessment_score=0.5,
                    summary="Older verification result.",
                    citation=LegacyCitation(
                        excerpt="Older excerpt.",
                        document=LegacyDocument(
                            policy_number="POLICY-OLD",
                            document_id=uuid.UUID(
                                "11111111-1111-1111-1111-111111111111"
                            ),
                            filename="old.pdf",
                        ),
                        pages=[1],
                    ),
                    verification_type="policy",
                )
            ]
        )

        older_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
            minutes=30
        )
        await self._create_and_complete_request(
            coverage_determination_repo,
            claim_id,
            older_response,
            older_time,
        )

        # Create newer successful request
        newer_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
            minutes=10
        )
        await self._create_and_complete_request(
            coverage_determination_repo,
            claim_id,
            sample_coverage_response,
            newer_time,
        )

        # Cache should return the newer result
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )
        assert result is not None
        assert result == sample_coverage_response  # Should be the newer one
        assert result != older_response

    @pytest.mark.asyncio
    async def test_cache_performance_with_large_dataset(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test cache performance with multiple claims and requests."""
        num_claims = 10
        requests_per_claim = 3

        # Create multiple claims with multiple requests each
        for claim_idx in range(num_claims):
            claim_id = f"NITMS25030400{claim_idx:02d}"

            for req_idx in range(requests_per_claim):
                timestamp = datetime.datetime.now(
                    datetime.timezone.utc
                ) - datetime.timedelta(minutes=requests_per_claim - req_idx)

                await self._create_and_complete_request(
                    coverage_determination_repo,
                    claim_id,
                    sample_coverage_response,
                    timestamp,
                )

        # Test cache lookup performance for all claims
        import time

        start_time = time.time()

        for claim_idx in range(num_claims):
            claim_id = f"NITMS25030400{claim_idx:02d}"
            result = await coverage_determination_repo.get_latest_successful(
                claim_id=claim_id,
                max_age_seconds=3600,
            )
            assert result is not None
            assert result == sample_coverage_response

        end_time = time.time()
        lookup_time = end_time - start_time

        # Performance assertion: should complete lookups reasonably quickly
        # This is a rough benchmark - adjust based on your performance requirements
        assert lookup_time < 1.0, (
            f"Cache lookups took {lookup_time:.2f}s for {num_claims} claims"
        )

    @pytest.mark.asyncio
    async def test_cache_database_constraints_and_indexes(
        self,
        coverage_determination_repo: SupabaseCoverageDeterminationRequestRepository,
        sample_coverage_response: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Test that cache queries work efficiently with database constraints."""
        claim_id = "NITMS25030308"

        # Create request
        await self._create_and_complete_request(
            coverage_determination_repo,
            claim_id,
            sample_coverage_response,
        )

        # Verify cache lookup works
        result = await coverage_determination_repo.get_latest_successful(
            claim_id=claim_id,
            max_age_seconds=3600,
        )
        assert result is not None

        # Check that the query can be executed efficiently
        # This test ensures the database schema supports efficient cache lookups
        session_maker = coverage_determination_repo._session_maker
        async with session_maker() as session:
            # Test the actual query that get_latest_successful uses
            cutoff_time = datetime.datetime.now(
                datetime.timezone.utc
            ) - datetime.timedelta(seconds=3600)

            query = (
                sa.select(LegacyCoverageDeterminations)
                .where(
                    LegacyCoverageDeterminations.claim_id == claim_id,
                    LegacyCoverageDeterminations.status
                    == CoverageDeterminationStatus.SUCCEEDED,
                    LegacyCoverageDeterminations.updated_at >= cutoff_time,
                    LegacyCoverageDeterminations.content.isnot(None),
                )
                .order_by(sa.desc(LegacyCoverageDeterminations.updated_at))
                .limit(1)
            )

            result = await session.execute(query)
            record = result.scalar_one_or_none()

            assert record is not None
            assert record.claim_id == claim_id
            assert record.status == CoverageDeterminationStatus.SUCCEEDED
