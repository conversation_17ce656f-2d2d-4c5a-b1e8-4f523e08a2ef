"""Integration tests for OpenRouter provider."""

from unittest.mock import patch, <PERSON>Mock
from pydantic import SecretStr

from claims_agent.config import Settings
from claims_agent.providers.provider_factory import create_llm_provider
from claims_agent.providers.openrouter_provider import OpenRouterProvider
from claims_agent.providers.openai_provider import OpenAIProvider


class TestOpenRouterIntegration:
    """Integration tests for OpenRouter provider functionality."""

    def test_openrouter_provider_selection(self):
        """Test that OpenRouter provider is selected when key is available."""
        settings = Settings(
            ENVIRONMENT="testing",
            OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
            OPENROUTER_MODEL_NAME="google/gemini-2.5-pro-preview",
            OPENAI_API_KEY=SecretStr(""),
            MODEL_TEMPERATURE=0.7,
        )

        with patch.object(
            OpenRouterProvider, "validate_model_capabilities", return_value=True
        ):
            provider = create_llm_provider(settings)

        assert isinstance(provider, OpenRouterProvider)
        assert provider.config.model_name == "google/gemini-2.5-pro-preview"
        assert provider.config.temperature == 0.7
        assert provider.provider_name == "OpenRouter"

    def test_openai_fallback_when_no_openrouter_key(self):
        """Test fallback to OpenAI when no OpenRouter key is provided."""
        settings = Settings(
            ENVIRONMENT="testing",
            OPENROUTER_API_KEY=SecretStr(""),
            OPENAI_API_KEY=SecretStr("test-openai-key"),
            LEGACY_OPENAI_MODEL_NAME="o3-mini",
            MODEL_TEMPERATURE=0.7,
        )

        with patch.object(
            OpenAIProvider, "validate_model_capabilities", return_value=True
        ):
            provider = create_llm_provider(settings)

        assert isinstance(provider, OpenAIProvider)
        assert provider.config.model_name == "o3-mini"
        assert provider.config.temperature == 0.7
        assert provider.provider_name == "OpenAI"

    def test_model_fallback_to_gpt_4o(self):
        """Test that unsupported models fall back to gpt-4o."""
        settings = Settings(
            ENVIRONMENT="testing",
            OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
            OPENROUTER_MODEL_NAME="unsupported/model",
            OPENAI_API_KEY=SecretStr(""),
            MODEL_TEMPERATURE=0.7,
        )

        with patch.object(
            OpenRouterProvider, "validate_model_capabilities"
        ) as mock_validate:
            # First call (unsupported model) returns False, second call (gpt-4o) returns True
            mock_validate.side_effect = [False, True]

            provider = create_llm_provider(settings)

        assert isinstance(provider, OpenRouterProvider)
        # With relaxed validation, the original model is retained despite capability failure
        assert provider.config.model_name == "unsupported/model"
        assert mock_validate.call_count == 1

    def test_openrouter_llm_creation(self):
        """Test that OpenRouter provider creates LLM with correct parameters."""
        settings = Settings(
            ENVIRONMENT="testing",
            OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
            OPENROUTER_MODEL_NAME="openai/o3-mini",
            OPENAI_API_KEY=SecretStr(""),
            MODEL_TEMPERATURE=0.5,
        )

        with (
            patch.object(
                OpenRouterProvider, "validate_model_capabilities", return_value=True
            ),
            patch(
                "claims_agent.providers.openrouter_provider.ChatOpenAI"
            ) as mock_chat_openai,
        ):
            mock_llm = MagicMock()
            mock_chat_openai.return_value = mock_llm

            provider = create_llm_provider(settings)
            llm = provider.create_llm()

            # Verify ChatOpenAI was called with correct parameters
            mock_chat_openai.assert_called_once_with(
                model="openai/o3-mini",
                temperature=0.5,
                api_key=settings.OPENROUTER_API_KEY,
                base_url="https://openrouter.ai/api/v1",
                default_headers={
                    "HTTP-Referer": "https://github.com/nirvanatech/nirvana",
                    "X-Title": "Nirvana Claims Agent",
                },
            )
            assert llm == mock_llm

    def test_openai_llm_creation(self):
        """Test that OpenAI provider creates LLM with correct parameters."""
        settings = Settings(
            ENVIRONMENT="testing",
            OPENROUTER_API_KEY=SecretStr(""),
            OPENAI_API_KEY=SecretStr("test-openai-key"),
            LEGACY_OPENAI_MODEL_NAME="o3-mini",
            MODEL_TEMPERATURE=0.8,
        )

        with (
            patch.object(
                OpenAIProvider, "validate_model_capabilities", return_value=True
            ),
            patch(
                "claims_agent.providers.openai_provider.ChatOpenAI"
            ) as mock_chat_openai,
        ):
            mock_llm = MagicMock()
            mock_chat_openai.return_value = mock_llm

            provider = create_llm_provider(settings)
            llm = provider.create_llm()

            # Verify ChatOpenAI was called with correct parameters (no base_url for direct OpenAI)
            mock_chat_openai.assert_called_once_with(
                model="o3-mini",
                temperature=0.8,
                api_key=settings.OPENAI_API_KEY,
            )
            assert llm == mock_llm
