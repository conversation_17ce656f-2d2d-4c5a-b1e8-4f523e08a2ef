"""Tests for configuration validation."""

import pytest
from pydantic import SecretStr, ValidationError

from claims_agent.config import Settings


class TestSettingsValidation:
    """Test configuration validation logic."""

    def test_development_environment_skips_validation(self):
        """Test that development environment skips API key validation."""
        # Should not raise an error even with missing keys
        settings = Settings(
            ENVIRONMENT="development",
            OPENROUTER_API_KEY=SecretStr(""),
            OPENAI_API_KEY=SecretStr(""),
        )
        assert settings.ENVIRONMENT == "development"

    def test_testing_environment_skips_validation(self):
        """Test that testing environment skips API key validation."""
        # Should not raise an error even with missing keys
        settings = Settings(
            ENVIRONMENT="testing",
            OPENROUTER_API_KEY=SecretStr(""),
            OPENAI_API_KEY=SecretStr(""),
        )
        assert settings.ENVIRONMENT == "testing"

    @pytest.mark.skip(reason="Skipping this test till we enable temporal in prod")
    def test_production_validation_requires_temporal_api_key_and_tls(self):
        """Test that production validation requires Temporal API key and TLS."""
        with pytest.raises(ValidationError) as exc_info:
            Settings(
                ENVIRONMENT="production",
                OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
                OPENAI_API_KEY=SecretStr("test-openai-key"),
                CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
                POLICY_SERVER_URL="https://prod.example.com/policy/sse",
                TEMPORAL_USE_TLS=False,
                TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
            )
        assert "TEMPORAL_USE_TLS must be True in production environment" in str(
            exc_info.value
        )
        with pytest.raises(ValidationError) as exc_info:
            Settings(
                ENVIRONMENT="production",
                OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
                OPENAI_API_KEY=SecretStr("test-openai-key"),
                CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
                POLICY_SERVER_URL="https://prod.example.com/policy/sse",
                TEMPORAL_USE_TLS=True,
                TEMPORAL_API_KEY=SecretStr(""),
            )
        assert "TEMPORAL_API_KEY is required in production environment" in str(
            exc_info.value
        )

    def test_production_validation_requires_at_least_one_api_key(self):
        """Test that production validation requires at least one API key."""
        # Should raise an error when both keys are missing
        with pytest.raises(ValidationError) as exc_info:
            Settings(
                ENVIRONMENT="production",
                ARIZE_ENABLED=False,
                OPENROUTER_API_KEY=SecretStr(""),
                OPENAI_API_KEY=SecretStr(""),
                CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
                POLICY_SERVER_URL="https://prod.example.com/policy/sse",
                TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
                TEMPORAL_USE_TLS=True,
            )
        assert "Either OPENROUTER_API_KEY or OPENAI_API_KEY must be set" in str(
            exc_info.value
        )

    def test_production_validation_passes_with_both_keys(self):
        """Test that production validation passes when both API keys are present."""
        # Should not raise an error
        settings = Settings(
            ENVIRONMENT="production",
            ARIZE_ENABLED=False,
            OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
            OPENAI_API_KEY=SecretStr("test-openai-key"),
            CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
            POLICY_SERVER_URL="https://prod.example.com/policy/sse",
            TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
            TEMPORAL_USE_TLS=True,
        )
        assert settings.OPENROUTER_API_KEY.get_secret_value() == "test-openrouter-key"
        assert settings.OPENAI_API_KEY.get_secret_value() == "test-openai-key"

    def test_production_validation_allows_openrouter_fallback_to_openai(self):
        """Test that production validation allows OpenRouter preference with OpenAI fallback."""
        # This should NOT raise an error - the provider factory should handle fallback
        settings = Settings(
            ENVIRONMENT="production",
            ARIZE_ENABLED=False,
            LLM_PROVIDER="openrouter",  # Prefer OpenRouter
            OPENROUTER_API_KEY=SecretStr(""),  # Empty - will fallback
            # Available for fallback
            OPENAI_API_KEY=SecretStr("test-openai-key"),
            CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
            POLICY_SERVER_URL="https://prod.example.com/policy/sse",
            TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
            TEMPORAL_USE_TLS=True,
        )
        assert settings.LLM_PROVIDER == "openrouter"
        assert not settings.OPENROUTER_API_KEY.get_secret_value()
        assert settings.OPENAI_API_KEY.get_secret_value() == "test-openai-key"

    def test_production_validation_allows_legacy_openai_fallback_to_openrouter(self):
        """Test that production validation allows legacy OpenAI preference with OpenRouter fallback."""
        # This should NOT raise an error - the provider factory should handle fallback
        settings = Settings(
            ENVIRONMENT="production",
            ARIZE_ENABLED=False,
            LLM_PROVIDER="legacy_openai",  # Prefer legacy OpenAI
            OPENROUTER_API_KEY=SecretStr(
                "test-openrouter-key"
            ),  # Available for fallback
            OPENAI_API_KEY=SecretStr(""),  # Empty - will fallback
            CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
            POLICY_SERVER_URL="https://prod.example.com/policy/sse",
            TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
            TEMPORAL_USE_TLS=True,
        )
        assert settings.LLM_PROVIDER == "legacy_openai"
        assert settings.OPENROUTER_API_KEY.get_secret_value() == "test-openrouter-key"
        assert not settings.OPENAI_API_KEY.get_secret_value()

    def test_production_validation_passes_with_only_openrouter_key(self):
        """Test that production validation passes with only OpenRouter API key."""
        settings = Settings(
            ENVIRONMENT="production",
            ARIZE_ENABLED=False,
            LLM_PROVIDER="openrouter",
            OPENROUTER_API_KEY=SecretStr("test-openrouter-key"),
            OPENAI_API_KEY=SecretStr(""),
            CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
            POLICY_SERVER_URL="https://prod.example.com/policy/sse",
            TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
            TEMPORAL_USE_TLS=True,
        )
        assert settings.OPENROUTER_API_KEY.get_secret_value() == "test-openrouter-key"
        assert not settings.OPENAI_API_KEY.get_secret_value()

    def test_production_validation_passes_with_only_openai_key(self):
        """Test that production validation passes with only OpenAI API key."""
        settings = Settings(
            ENVIRONMENT="production",
            ARIZE_ENABLED=False,
            LLM_PROVIDER="legacy_openai",
            OPENROUTER_API_KEY=SecretStr(""),
            OPENAI_API_KEY=SecretStr("test-openai-key"),
            CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
            POLICY_SERVER_URL="https://prod.example.com/policy/sse",
            TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
            TEMPORAL_USE_TLS=True,
        )
        assert not settings.OPENROUTER_API_KEY.get_secret_value()
        assert settings.OPENAI_API_KEY.get_secret_value() == "test-openai-key"

    def test_production_mcp_server_url_validation(self):
        """Test that production validates MCP server URLs."""
        # Should raise error for localhost URLs in production
        with pytest.raises(ValidationError) as exc_info:
            Settings(
                ENVIRONMENT="production",
                ARIZE_ENABLED=False,
                OPENAI_API_KEY=SecretStr("test-key"),
                CLAIMS_SERVER_URL="http://localhost:8000/claims/sse",
                TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
                TEMPORAL_USE_TLS=True,
            )
        assert "Default local MCP server URL" in str(exc_info.value)

        # Should raise error for 0.0.0.0 URLs in production
        with pytest.raises(ValidationError) as exc_info:
            Settings(
                ENVIRONMENT="production",
                ARIZE_ENABLED=False,
                OPENAI_API_KEY=SecretStr("test-key"),
                POLICY_SERVER_URL="http://0.0.0.0:8001/policy/sse",
                TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
                TEMPORAL_USE_TLS=True,
            )
        assert "Default local MCP server URL" in str(exc_info.value)

    def test_development_allows_local_mcp_server_urls(self):
        """Test that development environment allows local MCP server URLs."""
        # Should not raise error for localhost URLs in development
        settings = Settings(
            ENVIRONMENT="development",
            CLAIMS_SERVER_URL="http://localhost:8000/claims/sse",
            POLICY_SERVER_URL="http://0.0.0.0:8001/policy/sse",
        )
        assert settings.CLAIMS_SERVER_URL == "http://localhost:8000/claims/sse"
        assert settings.POLICY_SERVER_URL == "http://0.0.0.0:8001/policy/sse"

    def test_bug_fix_default_provider_validation_with_empty_openrouter_key(self):
        """Test the specific bug scenario: default openrouter provider with empty key but valid OpenAI key.

        This reproduces the exact bug reported:
        - LLM_PROVIDER defaults to "openrouter"
        - OPENROUTER_API_KEY is empty (common in existing deployments)
        - OPENAI_API_KEY is present (for fallback)
        - Should NOT prevent service startup
        """
        # This exact scenario was failing before the fix
        settings = Settings(
            ENVIRONMENT="production",
            ARIZE_ENABLED=False,
            # LLM_PROVIDER="openrouter" is the default
            OPENROUTER_API_KEY=SecretStr(""),  # Empty - the bug scenario
            OPENAI_API_KEY=SecretStr("sk-test-openai-key"),  # Valid fallback
            CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
            POLICY_SERVER_URL="https://prod.example.com/policy/sse",
            TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
            TEMPORAL_USE_TLS=True,
        )

        # Verify the configuration is as expected
        assert settings.LLM_PROVIDER == "openrouter"  # Default value
        assert not settings.OPENROUTER_API_KEY.get_secret_value()  # Empty
        assert (
            settings.OPENAI_API_KEY.get_secret_value() == "sk-test-openai-key"
        )  # Available for fallback

        # The key insight: the service should start successfully and let the provider factory handle fallback

    def test_end_to_end_startup_scenario_with_provider_fallback(self):
        """Test the complete startup scenario: config validation + provider creation with fallback.

        This tests the full flow that was failing in production startup.
        """
        from claims_agent.providers.provider_factory import create_llm_provider

        # This exact scenario was causing startup failures
        settings = Settings(
            ENVIRONMENT="production",
            ARIZE_ENABLED=False,
            LLM_PROVIDER="openrouter",  # Default preference
            # Empty - will be skipped efficiently
            OPENROUTER_API_KEY=SecretStr(""),
            OPENAI_API_KEY=SecretStr("sk-test-openai-key"),  # Valid fallback
            CLAIMS_SERVER_URL="https://prod.example.com/claims/sse",
            POLICY_SERVER_URL="https://prod.example.com/policy/sse",
            TEMPORAL_API_KEY=SecretStr("test-temporal-key"),
            TEMPORAL_USE_TLS=True,
        )

        # Step 1: Configuration validation should pass (this was the key fix)
        assert settings.LLM_PROVIDER == "openrouter"
        assert not settings.OPENROUTER_API_KEY.get_secret_value()
        assert settings.OPENAI_API_KEY.get_secret_value() == "sk-test-openai-key"

        # Step 2: Provider creation should succeed with efficient fallback
        provider = create_llm_provider(settings)

        # Verify fallback worked correctly (OpenRouter skipped, OpenAI used)
        assert provider.provider_name == "OpenAI"  # Fell back to OpenAI
        assert (
            provider.config.model_name == "o4-mini"
        )  # Using legacy OpenAI model (updated default)
        assert provider.config.api_key.get_secret_value() == "sk-test-openai-key"


class TestCoverageDeterminationCacheConfiguration:
    """Test coverage determination cache configuration validation."""

    def test_coverage_determination_cache_defaults(self):
        """Test that cache configuration has correct default values."""
        settings = Settings(ENVIRONMENT="development")

        assert settings.COVERAGE_DETERMINATION_TTL_SECONDS == 604800  # 7 days
        assert settings.COVERAGE_DETERMINATION_CACHE_ENABLED is True

    def test_coverage_determination_cache_custom_values(self):
        """Test that cache configuration accepts custom values."""
        settings = Settings(
            ENVIRONMENT="development",
            COVERAGE_DETERMINATION_TTL_SECONDS=1800,  # 30 minutes
        )

        assert settings.COVERAGE_DETERMINATION_TTL_SECONDS == 1800
        assert settings.COVERAGE_DETERMINATION_CACHE_ENABLED is True

        settings = Settings(
            ENVIRONMENT="development",
            COVERAGE_DETERMINATION_CACHE_ENABLED=False,
        )
        assert settings.COVERAGE_DETERMINATION_TTL_SECONDS == 604800
        assert settings.COVERAGE_DETERMINATION_CACHE_ENABLED is False
