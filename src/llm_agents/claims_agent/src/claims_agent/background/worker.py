"""Main entry point for the Temporal worker process.

This script initializes and runs the Temporal worker, which is responsible for
executing background workflows and activities.

The startup process is as follows:
1.  Builds the application's dependency injection (DI) container.
2.  Retrieves necessary services and settings from the DI container, such as
    the application settings, the Temporal client, and the activity instances.
3.  Initializes instrumentation (Sentry, Phoenix).
4.  Creates a `temporalio.worker.Worker` instance.
5.  Registers all defined `Workflow` classes and activity functions with the
    worker.
6.  Starts the worker, which begins polling its configured task queue on the
    Temporal server for jobs to execute.
"""

import asyncio
from loguru import logger
from temporalio.client import Client as TemporalClient
from temporalio.worker import Worker

from claims_agent.config import Settings
from claims_agent.di import build_container
from claims_agent.instrumentation import setup_arize_tracing
from claims_agent.background.workflows.coverage_determination import (
    CoverageDeterminationActivities,
    CoverageDeterminationWorkflow,
)
from claims_agent.background.workflows.simple_log import (
    SimpleLogWorkflow,
    get_server_timestamp,
)


async def main() -> None:
    """Initializes and runs the Temporal worker.

    This function orchestrates the setup of the worker. It retrieves all
    necessary components from the dependency injection container, wires them
    together, and starts the worker's long-running poll loop.
    """

    injector = build_container()

    settings = injector.get(Settings)

    if not settings.TEMPORAL_WORKER_TASK_QUEUE:
        raise ValueError("TEMPORAL_WORKER_TASK_QUEUE is not set, cannot start worker.")

    # Initialise Sentry (no-op if DSN unset)
    from claims_agent.instrumentation.sentry import register_sentry

    register_sentry(settings)

    if setup_arize_tracing():
        logger.info("Arize tracing enabled.")

    temporal_client = injector.get(TemporalClient)
    coverage_determination_activities = injector.get(CoverageDeterminationActivities)

    logger.info(
        f"Starting Temporal worker on queue: {settings.TEMPORAL_WORKER_TASK_QUEUE}"
    )
    worker = Worker(
        temporal_client,
        task_queue=settings.TEMPORAL_WORKER_TASK_QUEUE,
        workflows=[CoverageDeterminationWorkflow, SimpleLogWorkflow],
        activities=[
            coverage_determination_activities.set_in_progress,
            coverage_determination_activities.run_determine_coverage,
            coverage_determination_activities.set_succeeded,
            coverage_determination_activities.set_failed,
            get_server_timestamp,
        ],
    )
    logger.info("Starting Temporal worker...")
    await worker.run()


if __name__ == "__main__":
    asyncio.run(main())
