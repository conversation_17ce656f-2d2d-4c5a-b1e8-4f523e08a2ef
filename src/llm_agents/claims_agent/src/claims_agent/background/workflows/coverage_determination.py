"""Temporal Workflow and Activities for Coverage Determination.

This module defines the Temporal Workflow and its associated Activities for running
the insurance coverage determination process asynchronously.

The workflow orchestrates a series of steps:
1.  Updates the database to mark the request as "in_progress".
2.  Executes the core, potentially long-running, `determine_coverage` agent logic.
3.  Updates the database with the final result ("succeeded" or "failed").

The activities are the individual, idempotent units of work that interact with
external systems (the database via the repository, and the agent logic). They
are designed with specific retry policies to handle transient failures.
"""

from datetime import timedelta
import datetime
import json
import uuid
from pydantic import BaseModel


from claims_agent.interfaces.agents import AgentProtocol
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
)
from claims_agent.interfaces.repositories import (
    CoverageDeterminationError,
    CoverageDeterminationRequestRepositoryProtocol,
)
from temporalio import activity, workflow
from temporalio.exceptions import ActivityError, ApplicationError
from temporalio.common import RetryPolicy


class CoverageDeterminationRequest(BaseModel):
    """
    A request to generate a coverage determination.
    """

    request_id: uuid.UUID
    claim_id: str
    authorization: str


class CoverageDeterminationResult(BaseModel):
    """
    Result of a coverage determination workflow.
    """

    content: str


@workflow.defn
class CoverageDeterminationWorkflow:
    @workflow.run
    async def run(
        self, request: CoverageDeterminationRequest
    ) -> CoverageDeterminationResult:
        """Defines the Temporal Workflow for coverage determination.

        This workflow orchestrates the entire asynchronous process. It ensures
        that the request status is tracked in the database throughout the lifecycle.

        Workflow Steps:
        1.  Set status to "in_progress" in the database.
        2.  Invoke the main agent logic as a long-running activity with a
            sensible retry policy.
        3.  On success, update the status to "succeeded" and store the result.
        4.  On failure, update the status to "failed" and store the error, then
            re-raise the exception to mark the workflow itself as failed.

        Args:
            request: The `CoverageDeterminationRequest` containing the claim ID
                and authorization details.

        Returns:
            A `CoverageDeterminationResult` containing the JSON representation
            of the agent's findings.
        """
        workflow.logger.info(
            f"Running coverage determination for claim {request.claim_id}. Workflow ID: {workflow.info().workflow_id}. Run ID: {workflow.info().run_id}"
        )

        # Define retry policies for activities.
        # The main agent activity gets a longer retry backoff, as it's a
        # long-running process that might fail due to transient LLM or network issues.
        long_retry_policy = RetryPolicy(
            maximum_attempts=3,
            initial_interval=timedelta(seconds=10),
            backoff_coefficient=2.0,
        )
        # Database updates are quick, so they get a shorter, non-exponential retry.
        short_retry_policy = RetryPolicy(maximum_attempts=3)

        # Use a try/finally block to ensure status is updated even on failure
        try:
            # Step 1: Set status to "in_progress"
            await workflow.execute_activity(
                "CoverageDeterminationActivities.set_in_progress",
                args=[request.request_id],
                start_to_close_timeout=timedelta(seconds=10),
                retry_policy=short_retry_policy,
            )

            # Step 2: Invoke the coverage determination activity
            coverage_determination_response: LegacyCoverageDeterminationResponse = (
                await workflow.execute_activity(
                    "CoverageDeterminationActivities.run_determine_coverage",
                    args=[request],
                    start_to_close_timeout=timedelta(minutes=10),
                    retry_policy=long_retry_policy,
                )
            )
            workflow.logger.info(
                f"Coverage determination response: {coverage_determination_response}"
            )

            # Step 3: Set status to "completed"
            await workflow.execute_activity(
                "CoverageDeterminationActivities.set_succeeded",
                args=[request.request_id, coverage_determination_response],
                start_to_close_timeout=timedelta(seconds=10),
                retry_policy=short_retry_policy,
            )

            return CoverageDeterminationResult(
                # TODO: Find the root cause of the following error
                # and fix it. Even though the response is a Pydantic model,
                # temporal is returning a dict with the activity's result.
                #
                # Failing workflow task run_id=<> failure=Failure
                # { failure: Some(Failure { message: "'dict' object has no attribute 'model_dump_json'"
                content=json.dumps(coverage_determination_response)
            )
        except (ActivityError, ApplicationError) as e:
            workflow.logger.error(
                f"Workflow failed for request {request.request_id}: {e}"
            )
            await workflow.execute_activity(
                "CoverageDeterminationActivities.set_failed",
                args=[
                    request.request_id,
                    CoverageDeterminationError(
                        kind=type(e).__name__,
                        message=str(e),
                    ),
                ],
                start_to_close_timeout=timedelta(seconds=10),
                retry_policy=short_retry_policy,
            )
            # Re-raise the exception to ensure the workflow itself is marked as failed
            raise


class CoverageDeterminationActivities:
    """A collection of Temporal Activities for the coverage determination workflow.

    These methods are decorated with `@activity.defn` and represent the individual,
    failable, and retryable steps of the workflow. They are instantiated with
    the necessary dependencies (the agent and the repository) by the Temporal
    Worker, which gets them from the dependency injection container.
    """

    _agent: AgentProtocol
    _request_repository: CoverageDeterminationRequestRepositoryProtocol

    def __init__(
        self,
        agent: AgentProtocol,
        request_repository: CoverageDeterminationRequestRepositoryProtocol,
    ):
        self._agent = agent
        self._request_repository = request_repository

    @activity.defn(name="CoverageDeterminationActivities.run_determine_coverage")
    async def run_determine_coverage(
        self, request: CoverageDeterminationRequest
    ) -> LegacyCoverageDeterminationResponse:
        """Activity that invokes the `determine_coverage` method on the agent.

        This is the core, long-running activity where the actual AI agent logic
        is executed.

        Args:
            request: The request details needed by the agent.

        Returns:
            The structured `LegacyCoverageDeterminationResponse` from the agent.
        """
        return await self._agent.determine_coverage(
            request.claim_id, f"Bearer {request.authorization}"
        )

    @activity.defn(name="CoverageDeterminationActivities.set_in_progress")
    async def set_in_progress(self, request_id: uuid.UUID) -> None:
        """Activity that updates the status of a request to 'in_progress'.

        Args:
            request_id: The UUID of the request to update.
        """
        await self._request_repository.set_in_progress(
            request_id, datetime.datetime.now(datetime.timezone.utc)
        )

    @activity.defn(name="CoverageDeterminationActivities.set_succeeded")
    async def set_succeeded(
        self, request_id: uuid.UUID, result: LegacyCoverageDeterminationResponse
    ) -> None:
        """Activity that updates the status of a request to 'succeeded'.

        Args:
            request_id: The UUID of the request to update.
            result: The successful result from the agent to store.
        """
        await self._request_repository.set_succeeded(
            request_id, datetime.datetime.now(datetime.timezone.utc), result
        )

    @activity.defn(name="CoverageDeterminationActivities.set_failed")
    async def set_failed(
        self, request_id: uuid.UUID, error: CoverageDeterminationError
    ) -> None:
        """Activity that updates the status of a request to 'failed'.

        Args:
            request_id: The UUID of the request to update.
            error: The error details to store.
        """
        await self._request_repository.set_failed(
            request_id,
            datetime.datetime.now(datetime.timezone.utc),
            error=error,
        )
