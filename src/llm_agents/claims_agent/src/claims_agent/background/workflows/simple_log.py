"""A simple Temporal Workflow and Activity for testing and diagnostic purposes.

This module defines a basic workflow that logs a message and calls a simple
activity. Its primary purpose is to serve as a health check and a simple example
for verifying that the Temporal worker is running correctly and is able to
execute jobs.
"""

from dataclasses import dataclass
import datetime
from temporalio import activity, workflow


@dataclass
class SimpleLogRequest:
    """Input for the `SimpleLogWorkflow`."""

    message: str


@workflow.defn
class SimpleLogWorkflow:
    """A simple workflow that logs a message and executes a test activity.

    This workflow is used for testing the Temporal worker's connectivity and
    basic operation. It takes a message, calls an activity to get a server
    timestamp, and logs the result.
    """

    @workflow.run
    async def run(self, request: SimpleLogRequest) -> str:
        """Executes the simple logging workflow."""
        workflow_info = workflow.info()
        workflow.logger.info(
            f"Received request to log message. Workflow: {workflow_info}"
        )
        server_timestamp = await workflow.execute_activity(
            "get_server_timestamp",
            start_to_close_timeout=datetime.timedelta(seconds=10),
        )
        workflow.logger.info(
            f"Server timestamp: {server_timestamp}. Workflow: {workflow_info}"
        )
        workflow.logger.info(f"Message: {request.message}. Workflow: {workflow_info}")
        return f"Message: {request.message}. Server timestamp: {server_timestamp}. Workflow: {workflow_info}"


@activity.defn(name="get_server_timestamp")
async def get_server_timestamp() -> str:
    """A simple activity that returns the worker's current timestamp.

    This activity is used by the `SimpleLogWorkflow` to confirm that activities
    are being executed by the worker.

    Returns:
        The current UTC timestamp in ISO 8601 format.
    """
    return datetime.datetime.now(datetime.timezone.utc).isoformat()
