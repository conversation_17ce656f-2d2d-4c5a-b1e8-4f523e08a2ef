"""Claims Agent API Server.

This module provides the main entry point for the Claims Agent service and
exposes the FastAPI application from the HTTP API module.
"""

from claims_agent.api import create_app
from claims_agent.di import build_container
from claims_agent.config import Settings


injector = build_container()
app = create_app(injector)

if __name__ == "__main__":
    import uvicorn

    settings = injector.get(Settings)
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development",
        log_level=settings.LOG_LEVEL.lower(),
        workers=settings.UVICORN_WORKERS,
    )
