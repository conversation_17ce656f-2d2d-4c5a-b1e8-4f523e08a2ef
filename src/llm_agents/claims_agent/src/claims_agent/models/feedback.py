"""Pydantic models for coverage feedback functionality."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field

from claims_agent.interfaces.legacy_models import VerificationItem


class CoverageNote(BaseModel):
    """A coverage note with original and modified content."""

    note_id: UUID = Field(description="Unique identifier for the coverage note")
    original_content: VerificationItem = Field(
        description="Original AI-generated content"
    )
    modified_content: Optional[VerificationItem] = Field(
        None, description="Modified content after user feedback"
    )
    updated_at: Optional[datetime] = Field(
        None, description="When the note was last updated"
    )
    updated_by: Optional[str] = Field(None, description="Who updated the note")


class CoverageRunWithNotes(BaseModel):
    """A coverage run with its associated notes."""

    created_at: datetime = Field(description="When the run was created")
    created_by: str = Field(description="Who created the run")
    coverage_notes: list[CoverageNote] = Field(
        default_factory=list, description="Coverage notes for this run"
    )


class NoteFeedback(BaseModel):
    """Feedback for a single coverage note."""

    note_id: UUID = Field(description="ID of the note being updated")
    modified_content: VerificationItem = Field(
        description="Modified content with user feedback"
    )


class UpsertFeedbackRequest(BaseModel):
    """Request to upsert feedback for multiple coverage notes."""

    feedback: list[NoteFeedback] = Field(
        description="List of feedback for individual notes"
    )


class FeedbackResponse(BaseModel):
    """Response after upserting feedback."""

    updated_notes: list[UUID] = Field(description="List of note IDs that were updated")


class GetCoverageRunResponse(BaseModel):
    """Response for getting a coverage run with notes."""

    run: CoverageRunWithNotes = Field(description="The coverage run with notes")


class GetLatestCoverageRunResponse(BaseModel):
    """Response for getting the latest coverage run for a claim."""

    run: Optional[CoverageRunWithNotes] = Field(
        None, description="The latest coverage run, or None if no runs exist"
    )
