# mypy: ignore-errors
# This file is ignored by MyPy due to unresolved complexities with Pydantic V1/V2
# field_validator behavior, particularly around list length checks (e.g., bounding_box)
# leading to persistent "unreachable" or "unused-ignore" errors that are difficult
# to satisfy without overly verbose or less clear validation logic.
"""Verification models for the claims agent service.

This module contains shared data models used by both the API and agent components
to represent verification results and coverage determination responses.
"""

import re
from typing import Any, Literal, TypeAlias
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, model_validator


# Type alias for better readability in bounding box validation
BoundingBox: TypeAlias = list[float]

# Type aliases for dictionary return types
DocumentDict: TypeAlias = dict[str, str | None]
CitationDict: TypeAlias = dict[str, str | list[int] | None | list[float] | DocumentDict]
VerificationType: TypeAlias = Literal["policy", "claim", "general"]
VerificationItemDict: TypeAlias = dict[
    str, str | float | None | CitationDict | VerificationType
]
CoverageDeterminationDict: TypeAlias = dict[str, list[VerificationItemDict]]


class Document(BaseModel, frozen=True):
    """Document reference with identifiers needed for URL refresh."""

    policy_number: str = Field(
        description="The policy number (e.g., 'NISTK0017619-23')"
    )
    document_id: UUID = Field(description="The UUID of the document")
    filename: str | None = Field(
        default=None, description="The source document filename"
    )

    def to_dict(self) -> DocumentDict:
        """Convert to dictionary.

        Returns:
            Dictionary representation of the document
        """
        return {
            "policy_number": self.policy_number,
            # Convert UUID to string for JSON serialization
            "document_id": str(self.document_id),
            "filename": self.filename,
        }


class Citation(BaseModel, frozen=True):
    """Reference to policy documentation supporting a verification decision."""

    excerpt: str = Field(
        description="The relevant text excerpt from the policy document or claim notes"
    )

    # DEPRECATED FIELDS - Maintained for backwards compatibility
    filename: str | None = Field(
        default=None,
        description="DEPRECATED: Use document.filename instead. The source document filename (e.g., 'policy.pdf', 'claim_notes.txt'). This field will be removed in a future version.",
    )
    page: str | None = Field(
        default=None,
        description="DEPRECATED: Use pages array instead. The PDF page number as string. This field will be removed in a future version.",
    )
    presigned_url: str | None = Field(
        default=None,
        description="DEPRECATED: Will be removed in a future version. Pre-signed URL to the source PDF document if applicable, otherwise null.",
    )

    # NEW FIELDS - Future API structure
    pages: list[int] | None = Field(
        default=None,
        description="The PDF page numbers (not form page numbers). For example, if content appears on the 17th page, use [17]. For a range like 5-7, use [5, 6, 7]. Can be null if not applicable/found.",
    )
    document: Document | None = Field(
        default=None,
        description="Document reference with identifiers for URL refresh, if applicable",
    )
    bounding_box: BoundingBox | None = Field(
        default=None,
        description=(
            "Normalized [x1, y1, x2, y2] coordinates of the citation within the page if applicable, otherwise null."
        ),
    )

    @classmethod
    @field_validator("bounding_box")
    def validate_bounding_box(cls, value: BoundingBox | None) -> BoundingBox | None:
        """Validate the bounding_box field."""
        if value is None:
            return None
        if len(value) != 4:
            raise ValueError("bounding_box must contain exactly 4 float values")
        for i, coord in enumerate(value):
            if not isinstance(coord, float):
                try:
                    value[i] = float(coord)
                except (ValueError, TypeError) as err:
                    raise ValueError(
                        f"bounding_box coordinate at index {i} must be a float"
                    ) from err
            if not (0.0 <= value[i] <= 1.0):
                raise ValueError(
                    f"bounding_box coordinate at index {i} must be between 0.0 and 1.0"
                )
        return value

    @classmethod
    @field_validator("pages", mode="before")
    def validate_pages_input(cls, value: Any) -> list[int] | None:
        """Validate and parse various page input types to list[int] | None."""
        if value is None:
            return None

        if isinstance(value, list):
            processed_list = []
            for item in value:
                if isinstance(item, int):
                    processed_list.append(item)
                elif isinstance(item, str):
                    try:
                        processed_list.append(int(item))
                    except ValueError as e:
                        raise ValueError(
                            f"List item '{item}' is not a valid integer string."
                        ) from e
                else:
                    raise ValueError(
                        f"List item '{item}' is not an int or valid integer string."
                    )
            return sorted(list(set(processed_list)))

        if isinstance(value, int):
            return [value]

        if isinstance(value, str):
            value_str = value.strip()
            if not value_str:
                return None

            if "," in value_str:
                parts = [p.strip() for p in value_str.split(",")]
                try:
                    return sorted(list(set([int(p) for p in parts if p])))
                except ValueError as e:
                    raise ValueError(
                        f"Invalid comma-separated page string: '{value_str}'. Contains non-integer parts."
                    ) from e

            range_match = re.fullmatch(r"(\d+)\s*-\s*(\d+)", value_str)
            if range_match:
                start = int(range_match.group(1))
                end = int(range_match.group(2))
                if start > end:
                    raise ValueError(
                        f"Invalid page range: '{value_str}'. Start page greater than end page."
                    )
                return list(range(start, end + 1))

            try:
                return [int(value_str)]
            except ValueError as e:
                raise ValueError(
                    f"Page string '{value_str}' is not a valid integer, comma-separated list, or range."
                ) from e

        raise ValueError(
            f"Invalid page input type: {type(value)}. Expected None, int, str, or list."
        )

    @classmethod
    @field_validator("page", mode="before")
    def validate_page_to_str(cls, value: Any) -> Any:
        """Attempt to convert integer page to string. Other types pass through."""
        if isinstance(value, int):
            return str(value)
        # For None, str, or any other type, pass it through.
        # Pydantic's subsequent validation for `page: str | None` will catch
        # if it's not a str or None after this validator.
        return value

    @model_validator(mode="after")
    def populate_backwards_compatibility_fields(self) -> "Citation":
        """Populate backwards compatibility fields and validate presigned_url format."""
        # Validate presigned_url format if present
        if self.presigned_url and not (
            self.presigned_url.startswith("http://")
            or self.presigned_url.startswith("https://")
        ):
            raise ValueError(
                "presigned_url must be a valid URL starting with http:// or https://"
            )

        # Populate backwards compatibility fields from new fields
        # This ensures that when JSON with new field structure is parsed,
        # the deprecated fields are automatically populated for backwards compatibility

        # Populate deprecated 'filename' field from document.filename
        if self.document is not None and self.filename is None:
            object.__setattr__(self, "filename", self.document.filename)

        # Populate deprecated 'page' string field from 'pages' array
        if self.pages is not None and len(self.pages) > 0 and self.page is None:
            # Convert first page number to string for backwards compatibility
            object.__setattr__(self, "page", str(self.pages[0]))

        # Ensure deprecated presigned_url field exists for backwards compatibility
        # (It's already defined with default=None, so this is just for completeness)

        return self

    def to_dict(self) -> CitationDict:
        """Convert to dictionary.

        Returns:
            Dictionary representation of the citation
        """
        return {
            "excerpt": self.excerpt,
            # Deprecated fields
            "filename": self.filename,
            "page": self.page,
            "presigned_url": self.presigned_url,
            # New fields
            "pages": self.pages,
            "document": self.document.to_dict() if self.document else None,
            "bounding_box": self.bounding_box,
        }


class VerificationItem(BaseModel, frozen=True):
    """Single verification result with status and supporting information."""

    name: str
    assessment_score: float | None = Field(
        default=None,
        description="Assessment score (0.0-1.0) indicating verification status. "
        "0.0: Potential issue found. "
        "1.0: Verified, no issue found. "
        "None: Not applicable.",
        ge=0.0,
        le=1.0,
    )
    summary: str = Field(description="AI-generated summary of the verification result.")
    citation: Citation | None = Field(
        None, description="Citation information if available"
    )
    verification_type: Literal["policy", "claim", "general"] = Field(
        default="general",
        description="Type of verification performed",
    )
    status: Literal["Undefined", "Accept", "Reject"] = Field(
        default="Undefined",
        description="Status of the verification item, needs to be set only by the user, otherwise undefined",
    )

    def to_dict(self) -> VerificationItemDict:
        """Convert the model to a dictionary.

        Returns:
            A dictionary representation of the model.
        """
        return self.model_dump(mode="json")


class CoverageDeterminationResponse(BaseModel):
    """Response model for coverage determination API."""

    verifications: list[VerificationItem] = Field(
        default_factory=list, description="List of verification items"
    )

    def to_dict(self) -> CoverageDeterminationDict:
        """Convert to dictionary.

        Returns:
            Dictionary representation of the response
        """
        return {"verifications": [item.to_dict() for item in self.verifications]}

    def add_verification(self, verification: VerificationItem) -> None:
        """Add a verification item to the response.

        Args:
            verification: The verification item to add
        """
        self.verifications.append(verification)
