class RepositoryError(Exception):
    """Base exception for repository operations."""

    pass


class OptimisticLockError(RepositoryError):
    """Raised when an update fails due to a version mismatch."""

    def __init__(
        self,
        message: str = "Optimistic lock error: The item has been modified by another process.",
    ) -> None:
        self.message = message
        super().__init__(self.message)


class ItemNotFoundError(RepositoryError):
    """Raised when an expected item is not found."""

    pass
