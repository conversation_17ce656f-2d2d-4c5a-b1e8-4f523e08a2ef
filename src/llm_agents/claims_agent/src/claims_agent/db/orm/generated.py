from typing import List, Optional

from sqlalchemy import (
    BigInteger,
    Boolean,
    Column,
    DateTime,
    Double,
    ForeignKeyConstraint,
    Index,
    Numeric,
    PrimaryKeyConstraint,
    String,
    Table,
    Text,
    UniqueConstraint,
    Uuid,
    text,
)
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON>, OID
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
import datetime
import uuid


class Base(DeclarativeBase):
    pass


class CoverageRuns(Base):
    __tablename__ = "coverage_runs"
    __table_args__ = (
        PrimaryKeyConstraint("run_id", name="coverage_runs_pkey"),
        Index("idx_coverage_runs_claim_id", "claim_id"),
        Index("idx_coverage_runs_created_at", "created_at"),
    )

    run_id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, server_default=text("gen_random_uuid()")
    )
    claim_id: Mapped[str] = mapped_column(Text)
    status: Mapped[str] = mapped_column(
        String, server_default=text("'requested'::character varying")
    )
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(True), server_default=text("now()")
    )
    created_by: Mapped[str] = mapped_column(Text)
    trace_id: Mapped[Optional[str]] = mapped_column(Text)

    coverage_notes: Mapped[List["CoverageNotes"]] = relationship(
        "CoverageNotes", back_populates="run"
    )


class LegacyCoverageDeterminations(Base):
    __tablename__ = "legacy_coverage_determinations"
    __table_args__ = (
        PrimaryKeyConstraint("id", name="legacy_coverage_determinations_pkey"),
        UniqueConstraint(
            "workflow_id", name="legacy_coverage_determinations_workflow_id_key"
        ),
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, server_default=text("gen_random_uuid()")
    )
    claim_id: Mapped[str] = mapped_column(Text)
    status: Mapped[str] = mapped_column(Text)
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(True), server_default=text("now()")
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(True), server_default=text("now()")
    )
    workflow_id: Mapped[str] = mapped_column(Text)
    content: Mapped[Optional[dict]] = mapped_column(JSONB)
    generated_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime(True))
    error: Mapped[Optional[dict]] = mapped_column(JSONB)


t_pg_stat_statements = Table(
    "pg_stat_statements",
    Base.metadata,
    Column("userid", OID),
    Column("dbid", OID),
    Column("toplevel", Boolean),
    Column("queryid", BigInteger),
    Column("query", Text),
    Column("plans", BigInteger),
    Column("total_plan_time", Double(53)),
    Column("min_plan_time", Double(53)),
    Column("max_plan_time", Double(53)),
    Column("mean_plan_time", Double(53)),
    Column("stddev_plan_time", Double(53)),
    Column("calls", BigInteger),
    Column("total_exec_time", Double(53)),
    Column("min_exec_time", Double(53)),
    Column("max_exec_time", Double(53)),
    Column("mean_exec_time", Double(53)),
    Column("stddev_exec_time", Double(53)),
    Column("rows", BigInteger),
    Column("shared_blks_hit", BigInteger),
    Column("shared_blks_read", BigInteger),
    Column("shared_blks_dirtied", BigInteger),
    Column("shared_blks_written", BigInteger),
    Column("local_blks_hit", BigInteger),
    Column("local_blks_read", BigInteger),
    Column("local_blks_dirtied", BigInteger),
    Column("local_blks_written", BigInteger),
    Column("temp_blks_read", BigInteger),
    Column("temp_blks_written", BigInteger),
    Column("blk_read_time", Double(53)),
    Column("blk_write_time", Double(53)),
    Column("temp_blk_read_time", Double(53)),
    Column("temp_blk_write_time", Double(53)),
    Column("wal_records", BigInteger),
    Column("wal_fpi", BigInteger),
    Column("wal_bytes", Numeric),
    Column("jit_functions", BigInteger),
    Column("jit_generation_time", Double(53)),
    Column("jit_inlining_count", BigInteger),
    Column("jit_inlining_time", Double(53)),
    Column("jit_optimization_count", BigInteger),
    Column("jit_optimization_time", Double(53)),
    Column("jit_emission_count", BigInteger),
    Column("jit_emission_time", Double(53)),
)


t_pg_stat_statements_info = Table(
    "pg_stat_statements_info",
    Base.metadata,
    Column("dealloc", BigInteger),
    Column("stats_reset", DateTime(True)),
)


class SchemaMigrations(Base):
    __tablename__ = "schema_migrations"
    __table_args__ = (PrimaryKeyConstraint("version", name="schema_migrations_pkey"),)

    version: Mapped[str] = mapped_column(String(255), primary_key=True)


class CoverageNotes(Base):
    __tablename__ = "coverage_notes"
    __table_args__ = (
        ForeignKeyConstraint(
            ["run_id"],
            ["coverage_runs.run_id"],
            ondelete="CASCADE",
            name="coverage_notes_run_id_fkey",
        ),
        PrimaryKeyConstraint("note_id", name="coverage_notes_pkey"),
        Index("idx_coverage_notes_claim_id", "claim_id"),
        Index("idx_coverage_notes_run_id", "run_id"),
        Index("idx_coverage_notes_updated_at", "updated_at"),
    )

    note_id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, server_default=text("gen_random_uuid()")
    )
    run_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    claim_id: Mapped[str] = mapped_column(Text)
    original_content: Mapped[dict] = mapped_column(JSONB)
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(True), server_default=text("now()")
    )
    modified_content: Mapped[Optional[dict]] = mapped_column(JSONB)
    updated_by: Mapped[Optional[str]] = mapped_column(Text)

    run: Mapped["CoverageRuns"] = relationship(
        "CoverageRuns", back_populates="coverage_notes"
    )
