"""This module provides the SQLAlchemy/Supabase implementation of the repository
for managing `LegacyCoverageDeterminations` in the database.
"""

import datetime
from uuid import UUID

from loguru import logger
from sqlalchemy import Update, update, select, desc
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from claims_agent.db.orm import LegacyCoverageDeterminations
from claims_agent.interfaces.repositories import (
    CoverageDeterminationError,
    CoverageDeterminationRequestRepositoryProtocol,
    CreateCoverageDeterminationRequest,
    CoverageDeterminationStatus,
)
from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
)


class SupabaseCoverageDeterminationRequestRepository(
    CoverageDeterminationRequestRepositoryProtocol
):
    """Handles database operations for `LegacyCoverageDeterminations` via SQLAlchemy.

    This class implements the `CoverageDeterminationRequestRepositoryProtocol` and
    provides a concrete implementation for interacting with a PostgreSQL-based
    database like Supabase. It uses a SQLAlchemy `async_sessionmaker` to manage
    database connections and sessions.

    The primary responsibilities of this repository are:
    - Inserting new coverage determination requests.
    - Retrieving existing requests.
    - Performing safe and atomic state transitions (e.g., from 'requested' to
      'in_progress') for a request.
    """

    def __init__(self, session_maker: async_sessionmaker[AsyncSession]):
        """Initializes the repository with a SQLAlchemy async session maker.

        Args:
            session_maker: A pre-configured SQLAlchemy async_sessionmaker.
        """
        self._session_maker = session_maker
        logger.info("SupabaseCoverageDeterminationRequestRepository initialized.")

    async def insert(self, request: CreateCoverageDeterminationRequest) -> None:
        """Inserts a new coverage determination request into the database.

        The initial status of the request is set to `REQUESTED`.

        Args:
            request: A `CreateCoverageDeterminationRequest` object containing the
                initial data for the request.
        """
        new_item = LegacyCoverageDeterminations(
            id=request.request_id,
            claim_id=request.claim_id,
            workflow_id=request.workflow_id,
            status=CoverageDeterminationStatus.REQUESTED,
            created_at=request.timestamp,
            updated_at=request.timestamp,
        )
        async with self._session_maker() as session:
            session.add(new_item)
            await session.commit()

    async def get(self, request_id: UUID) -> LegacyCoverageDeterminations:
        """Retrieves a single coverage determination request by its ID.

        Args:
            request_id: The UUID of the request to retrieve.

        Returns:
            The SQLAlchemy ORM object for the `LegacyCoverageDeterminations` record.

        Raises:
            ValueError: If no request with the given ID is found.
        """
        async with self._session_maker() as session:
            orm_item = await session.get(LegacyCoverageDeterminations, request_id)
        if not orm_item:
            raise ValueError(
                f"Coverage determination request with id {request_id} not found"
            )
        return orm_item

    async def set_in_progress(
        self,
        request_id: UUID,
        timestamp: datetime.datetime,
    ) -> None:
        """Atomically sets the request status to 'in_progress'.

        This method performs a safe state transition, ensuring that the request
        can only be moved to 'in_progress' if its current status is 'requested'.

        Args:
            request_id: The UUID of the request to update.
            timestamp: The timestamp for when this status change occurred.

        Raises:
            ValueError: If the request is not in the expected 'requested' state.
        """
        await self._execute_update(
            request_id,
            update(LegacyCoverageDeterminations)
            .where(
                LegacyCoverageDeterminations.id == request_id,
                LegacyCoverageDeterminations.status.in_(
                    [CoverageDeterminationStatus.REQUESTED]
                ),
            )
            .values(
                status=CoverageDeterminationStatus.IN_PROGRESS,
                updated_at=timestamp,
            ),
            expected_from_status=[CoverageDeterminationStatus.REQUESTED],
        )

    async def set_succeeded(
        self,
        request_id: UUID,
        timestamp: datetime.datetime,
        result: LegacyCoverageDeterminationResponse,
    ) -> None:
        """Atomically sets the request status to 'succeeded' and stores the result.

        This method performs a safe state transition, ensuring that the request
        can only be marked as 'succeeded' if its current status is 'in_progress'.
        It also persists the JSON result from the agent.

        Args:
            request_id: The UUID of the request to update.
            timestamp: The timestamp for when the success occurred.
            result: The `LegacyCoverageDeterminationResponse` object from the agent.

        Raises:
            ValueError: If the request is not in the expected 'in_progress' state.
        """
        await self._execute_update(
            request_id,
            update(LegacyCoverageDeterminations)
            .where(
                LegacyCoverageDeterminations.id == request_id,
                LegacyCoverageDeterminations.status
                == CoverageDeterminationStatus.IN_PROGRESS,
            )
            .values(
                status=CoverageDeterminationStatus.SUCCEEDED,
                updated_at=timestamp,
                content=result.model_dump(mode="json"),
            ),
            expected_from_status=[CoverageDeterminationStatus.IN_PROGRESS],
        )

    async def set_failed(
        self,
        request_id: UUID,
        timestamp: datetime.datetime,
        error: CoverageDeterminationError,
    ) -> None:
        """Atomically sets the request status to 'failed' and stores the error.

        This method performs a safe state transition, allowing a request to be
        marked as 'failed' from either the 'requested' or 'in_progress' state.
        It persists the JSON representation of the error.

        Args:
            request_id: The UUID of the request to update.
            timestamp: The timestamp for when the failure occurred.
            error: The `CoverageDeterminationError` object containing error details.

        Raises:
            ValueError: If the request is not in an expected state.
        """
        await self._execute_update(
            request_id,
            update(LegacyCoverageDeterminations)
            .where(
                LegacyCoverageDeterminations.id == request_id,
                LegacyCoverageDeterminations.status.in_(
                    [
                        CoverageDeterminationStatus.IN_PROGRESS,
                        CoverageDeterminationStatus.REQUESTED,
                    ]
                ),
            )
            .values(
                status=CoverageDeterminationStatus.FAILED,
                updated_at=timestamp,
                error=error.model_dump_json(),
            ),
            expected_from_status=[
                CoverageDeterminationStatus.IN_PROGRESS,
                CoverageDeterminationStatus.REQUESTED,
            ],
        )

    async def get_latest_successful(
        self,
        claim_id: str,
        max_age_seconds: int,
    ) -> LegacyCoverageDeterminationResponse | None:
        """Get the most recent successful coverage determination for a claim within the TTL window.

        This method looks up the most recent successful determination for the given claim_id
        that was updated within the specified TTL window. The method considers the updated_at
        timestamp for TTL calculation.

        Args:
            claim_id: The ID of the claim to look up.
            max_age_seconds: Maximum age in seconds for the determination to be considered fresh.

        Returns:
            The cached LegacyCoverageDeterminationResponse if found and fresh, None otherwise.
        """
        cutoff_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
            seconds=max_age_seconds
        )

        async with self._session_maker() as session:
            query = (
                select(LegacyCoverageDeterminations)
                .where(
                    LegacyCoverageDeterminations.claim_id == claim_id,
                    LegacyCoverageDeterminations.status
                    == CoverageDeterminationStatus.SUCCEEDED,
                    LegacyCoverageDeterminations.updated_at >= cutoff_time,
                    LegacyCoverageDeterminations.content.isnot(None),
                )
                .order_by(desc(LegacyCoverageDeterminations.updated_at))
                .limit(1)
            )

            result = await session.execute(query)
            record = result.scalar_one_or_none()

            if not record:
                logger.debug(
                    f"No fresh cached determination found for claim_id: {claim_id}"
                )
                return None

            # Convert the stored JSON content back to the response model
            try:
                cached_response = LegacyCoverageDeterminationResponse.model_validate(
                    record.content
                )
                logger.info(
                    f"Found cached determination for claim_id: {claim_id}, "
                    f"age: {(datetime.datetime.now(datetime.timezone.utc) - record.updated_at).total_seconds():.1f}s"
                )
                return cached_response
            except Exception as e:
                logger.warning(
                    f"Failed to deserialize cached determination for claim_id: {claim_id}, "
                    f"record_id: {record.id}, error: {str(e)}"
                )
                return None

    async def _execute_update(
        self,
        request_id: UUID,
        clause: Update,
        expected_from_status: list[CoverageDeterminationStatus],
    ) -> None:
        """Executes an UPDATE statement and validates the state transition.

        This private helper method is crucial for ensuring data integrity. It
        wraps the execution of a SQLAlchemy `Update` statement, first checking
        that a record exists, and then verifying that the update actually changed
        a row. This prevents invalid state transitions (e.g., trying to mark
        a 'succeeded' request as 'failed' again).

        Args:
            request_id: The UUID of the record to update.
            clause: The SQLAlchemy `Update` object to execute.
            expected_from_status: A list of statuses that the record is allowed
                to be in for the transition to be valid.

        Raises:
            ValueError: If the request ID does not exist, or if the request is
                in an unexpected state, causing the update to affect 0 rows.
        """
        async with self._session_maker() as session:
            # First, verify the request exists at all
            orm_item = await session.get(LegacyCoverageDeterminations, request_id)
            if not orm_item:
                raise ValueError(
                    f"Coverage determination request with id {request_id} not found"
                )

            # Now, execute the state transition update
            result = await session.execute(clause)
            if result.rowcount == 0:
                # If we're here, the item exists but was not in the expected state
                raise ValueError(
                    f"Invalid state transition for request {request_id}. "
                    f"Expected status '{expected_from_status}', but found '{orm_item.status}'."
                )
            if result.rowcount > 1:
                # This should be impossible if `id` is a primary key, but good practice
                raise ValueError(
                    f"Multiple coverage determination requests with id {request_id} found."
                )
            await session.commit()
