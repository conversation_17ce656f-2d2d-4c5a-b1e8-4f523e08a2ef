"""Repository for coverage runs database operations."""

from typing import Optional
from uuid import UUID

from loguru import logger
from sqlalchemy import select, desc, update
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker
from sqlalchemy.orm import selectinload

from claims_agent.db.orm.generated import CoverageRuns
from claims_agent.models.feedback import CoverageRunWithNotes, CoverageNote
from claims_agent.interfaces.legacy_models import VerificationItem
from claims_agent.interfaces.repositories import CoverageDeterminationStatus


class CoverageRunRepository:
    """Repository for managing coverage runs in the database."""

    def __init__(self, session_maker: async_sessionmaker[AsyncSession]):
        """Initialize the repository with a SQLAlchemy session maker.

        Args:
            session_maker: A pre-configured SQLAlchemy async_sessionmaker.
        """
        self._session_maker = session_maker
        logger.info("CoverageRunRepository initialized.")

    async def create_run(
        self,
        claim_id: str,
        created_by: str,
        trace_id: Optional[str] = None,
    ) -> UUID:
        """Create a new coverage run.

        Args:
            claim_id: The claim ID being analyzed.
            created_by: Email of the user creating the run.
            trace_id: Optional trace ID for debugging.

        Returns:
            The UUID of the created run.
        """
        async with self._session_maker() as session:
            run = CoverageRuns(
                claim_id=claim_id,
                created_by=created_by,
                trace_id=trace_id,
                status=CoverageDeterminationStatus.REQUESTED.value,
            )
            session.add(run)
            await session.commit()
            await session.refresh(run)

            logger.info(
                f"Created coverage run {run.run_id} for claim {claim_id} with status {CoverageDeterminationStatus.REQUESTED.value}"
            )
            return run.run_id

    async def set_in_progress(self, run_id: UUID) -> None:
        """Set the status of a coverage run to IN_PROGRESS.

        Args:
            run_id: The ID of the run to update.
        """
        await self._update_status(run_id, CoverageDeterminationStatus.IN_PROGRESS)

    async def set_succeeded(self, run_id: UUID) -> None:
        """Set the status of a coverage run to SUCCEEDED.

        Args:
            run_id: The ID of the run to update.
        """
        await self._update_status(run_id, CoverageDeterminationStatus.SUCCEEDED)

    async def set_failed(self, run_id: UUID) -> None:
        """Set the status of a coverage run to FAILED.

        Args:
            run_id: The ID of the run to update.
        """
        await self._update_status(run_id, CoverageDeterminationStatus.FAILED)

    async def set_cancelled(self, run_id: UUID) -> None:
        """Set the status of a coverage run to CANCELLED.

        Args:
            run_id: The ID of the run to update.
        """
        await self._update_status(run_id, CoverageDeterminationStatus.CANCELLED)

    async def _update_status(
        self, run_id: UUID, status: CoverageDeterminationStatus
    ) -> None:
        """Update the status of a coverage run.

        Args:
            run_id: The ID of the run to update.
            status: The new status to set.
        """
        async with self._session_maker() as session:
            stmt = (
                update(CoverageRuns)
                .where(CoverageRuns.run_id == run_id)
                .values(status=status.value)
            )
            result = await session.execute(stmt)
            await session.commit()

            if result.rowcount > 0:
                logger.info(f"Updated coverage run {run_id} status to {status.value}")
            else:
                logger.warning(f"Coverage run {run_id} not found for status update")

    async def get_run_with_notes(self, run_id: UUID) -> Optional[CoverageRunWithNotes]:
        """Get a coverage run with its notes.

        Args:
            run_id: The ID of the run to retrieve.

        Returns:
            The coverage run with notes, or None if not found.
        """
        async with self._session_maker() as session:
            stmt = (
                select(CoverageRuns)
                .where(CoverageRuns.run_id == run_id)
                .options(selectinload(CoverageRuns.coverage_notes))
            )
            result = await session.execute(stmt)
            run = result.scalar_one_or_none()

            if not run:
                logger.warning(f"Coverage run {run_id} not found")
                return None

            return self._convert_to_pydantic(run)

    async def get_latest_run_for_claim(
        self, claim_id: str
    ) -> Optional[CoverageRunWithNotes]:
        """Get the latest coverage run for a claim.

        Args:
            claim_id: The claim ID to search for.

        Returns:
            The latest coverage run with notes, or None if not found.
        """
        async with self._session_maker() as session:
            stmt = (
                select(CoverageRuns)
                .where(CoverageRuns.claim_id == claim_id)
                .order_by(desc(CoverageRuns.created_at))
                .limit(1)
                .options(selectinload(CoverageRuns.coverage_notes))
            )
            result = await session.execute(stmt)
            run = result.scalar_one_or_none()

            if not run:
                logger.info(f"No coverage runs found for claim {claim_id}")
                return None

            return self._convert_to_pydantic(run)

    async def get_latest_successful_runs_for_claim(
        self, claim_id: str, limit: int = 1
    ) -> list[CoverageRunWithNotes]:
        """Get the latest successful coverage runs for a claim.

        Args:
            claim_id: The claim ID to search for.
            limit: Maximum number of runs to return (default: 1).

        Returns:
            List of successful coverage runs with notes, ordered by creation date desc.
        """
        async with self._session_maker() as session:
            stmt = (
                select(CoverageRuns)
                .where(
                    CoverageRuns.claim_id == claim_id,
                    CoverageRuns.status == CoverageDeterminationStatus.SUCCEEDED.value,
                )
                .order_by(desc(CoverageRuns.created_at))
                .limit(limit)
                .options(selectinload(CoverageRuns.coverage_notes))
            )
            result = await session.execute(stmt)
            runs = result.scalars().all()

            logger.info(
                f"Found {len(runs)} successful coverage runs for claim {claim_id} (limit: {limit})"
            )
            return [self._convert_to_pydantic(run) for run in runs]

    def _convert_to_pydantic(self, run: CoverageRuns) -> CoverageRunWithNotes:
        """Convert a SQLAlchemy model to a Pydantic model.

        Args:
            run: The SQLAlchemy CoverageRuns instance.

        Returns:
            A Pydantic CoverageRunWithNotes instance.
        """
        coverage_notes = []
        for note in run.coverage_notes:
            # Convert JSONB to VerificationItem
            original_content = VerificationItem.model_validate(note.original_content)
            modified_content = None
            if note.modified_content:
                modified_content = VerificationItem.model_validate(
                    note.modified_content
                )

            coverage_note = CoverageNote(
                note_id=note.note_id,
                original_content=original_content,
                modified_content=modified_content,
                updated_at=note.updated_at,
                updated_by=note.updated_by,
            )
            coverage_notes.append(coverage_note)

        return CoverageRunWithNotes(
            created_at=run.created_at,
            created_by=run.created_by,
            coverage_notes=coverage_notes,
        )
