"""Factory for creating LangChain AgentExecutor instances."""

from typing import Any

from langchain.agents import (
    AgentExecutor,
    create_openai_tools_agent,
)

# Use base class for type hint
from langchain_core.language_models import BaseLanguageModel
from langchain_core.prompts import Chat<PERSON>romptTemplate
from loguru import logger

from claims_agent.config import Settings
from claims_agent.instrumentation.helpers import trace_function, trace_span
from claims_agent.providers import create_llm_provider, create_llm_from_provider


class AgentExecutorFactory:
    """Handles the creation and configuration of the LangChain AgentExecutor.

    This class is responsible for wiring together all the components needed for a
    LangChain agent:
    - The LLM provider and model (e.g., OpenRouter, OpenAI).
    - The tools the agent can use.
    - The prompt template that guides the agent.
    - The specific agent runnable (e.g., `create_openai_tools_agent`).
    - The final `AgentExecutor` that runs the agent loop.

    It is instantiated once by the dependency injection container and configured
    with the application settings.
    """

    def __init__(
        self,
        settings_override: Settings,
        *,
        agent_verbose: bool | None = None,
        max_iterations: int = 15,
        max_execution_time: int = 300,
    ) -> None:
        """Initializes the factory with LLM and AgentExecutor configurations.

        Args:
            settings_override: Optional settings override (defaults to global settings).
            agent_verbose: Whether the AgentExecutor should run in verbose mode.
            max_iterations: Maximum iterations for the agent.
            max_execution_time: Maximum execution time in seconds for the agent.
        """
        self.settings = settings_override
        self.agent_verbose = (
            agent_verbose
            if agent_verbose is not None
            else getattr(self.settings, "AGENT_VERBOSE", False)
        )
        self.max_iterations = max_iterations
        self.max_execution_time = max_execution_time

        # Create the LLM provider during initialization to fail fast if the
        # configuration is invalid.
        try:
            self.provider = create_llm_provider(self.settings)
            logger.info(
                f"AgentExecutorFactory initialized with {self.provider.provider_name} provider, "
                f"model: {self.provider.config.model_name}, "
                f"temp: {self.provider.config.temperature}, verbose: {self.agent_verbose}"
            )
        except Exception as e:
            logger.error(f"Failed to initialize LLM provider: {e}")
            raise

    @trace_function(name="AgentExecutorFactory._create_llm")
    def _create_llm(self) -> BaseLanguageModel:
        """Creates the language model instance using the configured provider."""
        logger.debug(
            f"Creating LLM instance via {self.provider.provider_name} provider"
        )
        return create_llm_from_provider(self.provider)

    @trace_function(name="AgentExecutorFactory.create_executor")
    def create_executor(
        self, tools: list[Any], prompt: ChatPromptTemplate
    ) -> AgentExecutor:
        """Creates an AgentExecutor instance with a given set of tools and a prompt.

        This is the main method of the factory. It performs the final assembly
        of the agent.

        Args:
            tools: A list of tools to be made available to the agent.
            prompt: The `ChatPromptTemplate` that will guide the agent's reasoning.

        Returns:
            A fully configured and runnable `AgentExecutor` instance.

        Raises:
            ValueError: If the agent runnable cannot be created.
        """
        llm = self._create_llm()

        logger.debug("Creating agent runnable using create_openai_tools_agent.")
        try:
            # Create the specific agent runnable (e.g., OpenAI Tools agent)
            with trace_span(
                "create_openai_tools_agent",
                attributes={
                    "tool_count": len(tools),
                    "model": self.provider.config.model_name,
                    "provider": self.provider.provider_name,
                },
            ):
                agent_runnable = create_openai_tools_agent(
                    llm=llm, tools=tools, prompt=prompt
                )
        except Exception as e:
            logger.error(f"Error creating agent runnable: {e}")
            raise ValueError(f"Failed to create agent runnable: {e}") from e

        logger.debug("Instantiating AgentExecutor.")
        # Create and return the AgentExecutor, configured with execution limits
        # and verbosity settings from the factory's configuration.
        with trace_span("instantiate_agent_executor"):
            executor = AgentExecutor(
                agent=agent_runnable,
                tools=tools,
                verbose=self.agent_verbose,
                handle_parsing_errors=True,  # Consider making this configurable
                max_iterations=self.max_iterations,
                # Ensure float for older Langchain
                max_execution_time=float(self.max_execution_time),
                early_stopping_method="force",
                return_intermediate_steps=True,  # Keep this true for logging
            )

        return executor
