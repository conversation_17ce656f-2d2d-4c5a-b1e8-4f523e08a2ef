"""MCP Client Manager for handling MultiServerMCPClient lifecycle."""

from collections.abc import Async<PERSON>tera<PERSON>
from contextlib import asynccontextmanager
from typing import Literal

from langchain_mcp_adapters.client import MultiServerMCPClient, SSEConnection
from loguru import logger

from claims_agent.config import Settings


class MCPManagerError(Exception):
    """Base exception for MCPClientManager errors."""

    pass


class AgentError(Exception):
    """Base class for agent-related errors."""

    pass


class ToolFetchingError(AgentError):
    """Exception raised when fetching tools for the agent fails."""

    pass


class MCPClientConfigurationError(MCPManagerError):
    """Exception raised for configuration errors in MCPClientManager."""

    pass


class MCPClientManager:
    """Manages the lifecycle of MultiServerMCPClient instances."""

    def __init__(self, settings: Settings) -> None:
        """Initialize MCPClientManager.

        Args:
            claims_server_url: URL for the Claims MCP server.
            policy_server_url: URL for the Policy MCP server.
            default_timeout: Default timeout for HTTP requests.
            sse_read_timeout: Default read timeout for SSE connections.
        """
        self.claims_server_url = settings.CLAIMS_SERVER_URL
        self.policy_server_url = settings.POLICY_SERVER_URL
        self.default_timeout = settings.MCP_DEFAULT_TIMEOUT_SECONDS
        self.sse_read_timeout = settings.MCP_SSE_READ_TIMEOUT_SECONDS

        if not self.claims_server_url and not self.policy_server_url:
            logger.warning(
                "MCPClientManager initialized with no server URLs. "
                "Client creation will fail if attempted without URLs provided to methods."
            )
        logger.info(
            f"MCPClientManager initialized. Claims URL: '{self.claims_server_url}', "
            f"Policy URL: '{self.policy_server_url}'"
        )

    @asynccontextmanager
    async def get_client(
        self,
        connection_type: Literal["claims", "policy", "both"] = "both",
        authorization: str | None = None,
    ) -> AsyncIterator[MultiServerMCPClient]:
        """Provides a MultiServerMCPClient instance via an async context manager.

        This method configures and yields a MultiServerMCPClient tailored to the
        specified connection type. It ensures that the client is properly set up
        for use within an async context.

        Args:
            connection_type: Specifies which server connections the client should be
                             configured with ('claims', 'policy', or 'both').
            authorization: Optional authorization header string (e.g., "Bearer <token>").

        Yields:
            An initialized MultiServerMCPClient.

        Raises:
            ValueError: If an invalid connection_type is provided.
        """
        servers = self._get_server_configs(connection_type, authorization)
        if not servers:
            logger.warning(
                f"MCPClientManager.get_client called for '{connection_type}' but no server configurations were found."
            )
            # Yielding a client with no servers might be valid if the caller can handle it,
            # or we could raise an error here. For now, let it proceed.

        client_instance = MultiServerMCPClient(servers)
        try:
            logger.debug(
                f"MultiServerMCPClient instance created for servers: {list(servers.keys())}"
            )
            yield client_instance
        finally:
            # According to langchain-mcp-adapters v0.1.0 release notes,
            # MultiServerMCPClient itself is no longer an async context manager,
            # and sessions are created/destroyed on demand by methods like get_tools().
            # This suggests the client instance itself might not need explicit closing.
            # If an aclose() method becomes available/necessary in the future, call it here.
            logger.debug(
                "MultiServerMCPClient instance scope in MCPClientManager.get_client is ending."
            )

    def _get_server_configs(
        self,
        connection_type: Literal["claims", "policy", "both"] = "both",
        authorization: str | None = None,
    ) -> dict[str, SSEConnection]:
        headers: dict[str, str] = {}
        if authorization:
            headers["Authorization"] = authorization

        servers: dict[str, SSEConnection] = {}

        if connection_type in ["claims", "both"] and self.claims_server_url:
            servers["ClaimsService"] = SSEConnection(
                url=self.claims_server_url,
                transport="sse",
                timeout=self.default_timeout,
                sse_read_timeout=self.sse_read_timeout,
                headers=headers.copy(),
            )
            logger.debug(
                f"ClaimsService configured for MCP client. Auth included: {bool(headers)}"
            )

        if connection_type in ["policy", "both"] and self.policy_server_url:
            servers["PolicyService"] = SSEConnection(
                url=self.policy_server_url,
                transport="sse",
                timeout=self.default_timeout,
                sse_read_timeout=self.sse_read_timeout,
                headers=headers.copy(),
            )
            logger.debug(
                f"PolicyService configured for MCP client. Auth included: {bool(headers)}"
            )

        return servers
