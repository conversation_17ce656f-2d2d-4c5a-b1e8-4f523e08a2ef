"""Logging utility functions specifically for agent operations."""

from typing import Any

from langchain.agents import AgentExecutor  # Needed for type hint
from loguru import logger


def truncate_for_logging(text: str, max_length: int = 500) -> str:
    """Truncate a string to a maximum length for logging purposes.

    Args:
        text: The text to truncate
        max_length: Maximum length of the returned string

    Returns:
        Truncated string with indicator if truncation occurred
    """
    if len(text) > max_length:
        return text[:max_length] + "... (truncated)"
    return text


def log_intermediate_steps(response: dict[str, Any]) -> None:
    """Log the intermediate steps from the agent response.

    Args:
        response: The agent response dictionary, expected to contain 'intermediate_steps'
    """
    steps = response.get("intermediate_steps")
    if steps and isinstance(steps, list):
        logger.info(f"Agent intermediate steps ({len(steps)}):")
        for i, step_data in enumerate(steps):
            # Check if step_data is a tuple/list with expected structure
            if isinstance(step_data, list | tuple) and len(step_data) == 2:
                tool_call, observation = step_data
                # Truncate tool_call and observation for logging
                tool_call_str = truncate_for_logging(str(tool_call))
                logger.info(f"  Step {i + 1} Tool Call: {tool_call_str}")

                observation_str = truncate_for_logging(str(observation))
                logger.info(f"  Step {i + 1} Observation: {observation_str}")
            else:
                logger.warning(
                    f"  Step {i + 1} has unexpected format: {type(step_data)}"
                )
    elif steps is not None:  # It exists but is not a list?
        logger.warning(
            f"'intermediate_steps' key found but is not a list: {type(steps)}"
        )
    # If steps is None or empty list, don't log anything here.


def trace_tools_execution(agent_executor: AgentExecutor) -> None:
    """Log detailed information about available tools for debugging.

    Args:
        agent_executor: The AgentExecutor instance containing the tools.
    """
    if not agent_executor or not hasattr(agent_executor, "tools"):
        logger.warning(
            "Cannot trace tools - agent executor not provided or not properly initialized"
        )
        return

    tools = agent_executor.tools
    if not tools:
        logger.warning(
            "No tools are available to the agent - this will prevent proper functioning",
        )
        return

    logger.info(f"Agent has {len(tools)} tools available:")

    # Log all available tools for clarity
    for i, tool in enumerate(tools):
        # Start index from 1
        tool_name = getattr(tool, "name", f"Tool-{i + 1}")
        tool_desc = getattr(tool, "description", "No description")
        # Truncate description if too long
        short_desc = truncate_for_logging(tool_desc, max_length=100)
        logger.info(f"  Tool {i + 1}: {tool_name} - {short_desc}")
