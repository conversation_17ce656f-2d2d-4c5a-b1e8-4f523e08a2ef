"""Service for fetching tools from an MCP client."""

import time
from typing import Any, cast

from langchain_mcp_adapters.client import MultiServerMCPClient
from loguru import logger

from claims_agent.agents.mcp_client_manager import ToolFetchingError
from claims_agent.instrumentation.helpers import trace_async_function, trace_span_async


class MCPToolService:
    """A service responsible for fetching tools from an MCP client."""

    @trace_async_function(name="MCPToolService.get_mcp_tools")
    async def get_mcp_tools(self, client: MultiServerMCPClient) -> list[Any]:
        """Get tools from the MCP client.

        Args:
            client: The active MCPClient to get tools from.

        Returns:
            A list of tool objects.

        Raises:
            ToolFetchingError: If tools cannot be fetched from the client.
        """
        try:
            async with trace_span_async("prepare_client_info"):
                client_info = f"Client type: {type(client).__name__}"
                client_dir_summary = (
                    str(dir(client))[:200] + "..."
                    if len(str(dir(client))) > 200
                    else str(dir(client))
                )
                logger.debug(
                    f"Fetching tools from MCP client via MCPToolService: {client_info}, Attrs: {client_dir_summary}"
                )

                if not hasattr(client, "get_tools"):
                    logger.error(
                        "Provided MCP client instance does not have 'get_tools' method (checked by MCPToolService).",
                    )
                    raise ToolFetchingError(
                        "MCP client instance is missing 'get_tools' method."
                    )

            start_time = time.time()
            logger.info("MCPToolService calling client.get_tools()...")

            async with trace_span_async("client.get_tools"):
                tools_raw = await client.get_tools()
                tools = cast(list[Any], tools_raw)

            end_time = time.time()
            fetch_duration = end_time - start_time

            logger.info(
                f"MCPToolService tool fetching: {fetch_duration:.2f}s. Found {len(tools)} tools."
            )

            async with trace_span_async(
                "process_tool_results",
                attributes={"tool_count": len(tools), "fetch_duration": fetch_duration},
            ):
                for i, tool_obj in enumerate(tools):
                    tool_name = getattr(tool_obj, "name", f"Tool-{i + 1}")
                    tool_desc = getattr(tool_obj, "description", "No description")
                    tool_type = type(tool_obj).__name__
                    logger.info(
                        f"MCPToolService Loaded Tool {i + 1}: {tool_name} ({tool_type}) - Desc: {tool_desc[:100]}..."
                    )

                if not tools:
                    logger.warning(
                        "No tools were loaded from the MCP client instance by MCPToolService.get_mcp_tools."
                    )
                    raise ToolFetchingError(
                        "No tools were returned by the MCP client (MCPToolService)."
                    )
            return tools
        except ToolFetchingError:
            raise
        except Exception as e:
            logger.error(
                f"MCPToolService failed to load tools from provided MCP client: {str(e)}"
            )
            logger.exception("Detailed error during MCPToolService.get_mcp_tools:")
            raise ToolFetchingError(
                f"MCPToolService failed during client.get_tools(): {e}"
            ) from e
