"""Logging configuration for the Claims Agent service.

This module sets up Loguru for application logging.
"""

import logging
import sys

from loguru import logger


class InterceptHandler(logging.Handler):
    """Intercept standard logging messages toward Loguru.

    This handler intercepts all standard library logging and redirects it to Loguru.
    """

    def emit(self, record: logging.LogRecord) -> None:
        """Intercept logging records and pass them to loguru.

        Args:
            record: The logging record to intercept.
        """
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = str(record.levelno)

        # Find caller from where the logged message originated
        import types

        frame: types.FrameType | None = sys._getframe(6)
        depth = 6
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        if frame:
            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )
        else:
            logger.opt(exception=record.exc_info).log(level, record.getMessage())
