"""Direct OpenAI provider implementation."""

from langchain_core.language_models import BaseLanguageModel
from langchain_openai import ChatOpenAI
from loguru import logger

from .provider_interface import LLMProvider


class OpenAIProvider(LLMProvider):
    """Direct OpenAI LLM provider implementation."""

    def create_llm(self) -> BaseLanguageModel:
        """Create and return a configured OpenAI LLM instance."""
        logger.debug(
            f"Creating OpenAI ChatOpenAI instance with model='{self.config.model_name}'"
        )

        return ChatOpenAI(
            model=self.config.model_name,
            temperature=self.config.temperature,
            api_key=self.config.api_key,
        )

    def validate_model_capabilities(self) -> bool | None:
        """Skip explicit capability validation for OpenAI models.

        All current Chat Completions models exposed by OpenAI support the
        function-calling / tools API, so we optimistically return True to avoid
        brittle allow-lists that break when new models appear.
        """
        logger.debug(
            "Skipping explicit capability validation for OpenAI model '{}'—assuming tool/function support is available.",
            self.config.model_name,
        )
        return True

    def _extract_model_base(self, model_name: str) -> str:
        """Extract base model name from versioned model names."""
        # Handle versioned models like "gpt-4o-2024-08-06" -> "gpt-4o"
        if "-2024-" in model_name or "-2023-" in model_name:
            parts = model_name.split("-")
            # Find the year part and take everything before it
            for i, part in enumerate(parts):
                if part.startswith(("2023", "2024", "2025")):
                    return "-".join(parts[:i])

        return model_name

    @property
    def provider_name(self) -> str:
        """Return the name of this provider."""
        return "OpenAI"
