"""Factory for creating LLM providers with automatic fallback logic.

This module is responsible for instantiating the correct LLM provider (e.g.,
OpenRouter, OpenAI) based on the application's configuration. It includes a
robust fallback mechanism, allowing the application to seamlessly switch to a
secondary provider if the primary one is unavailable or misconfigured.
"""

from typing import Callable, Optional
from langchain_core.language_models import BaseLanguageModel
from loguru import logger

from claims_agent.config import Settings

from .openai_provider import OpenAIProvider
from .openrouter_provider import OpenRouterProvider
from .provider_interface import LLMProvider, ProviderConfig


class ProviderCreationError(Exception):
    """Raised when provider creation fails."""

    pass


def create_llm_provider(settings: Settings) -> LLMProvider:
    """Creates an LLM provider based on settings, with automatic fallback.

    This function implements the core provider selection logic. It prioritizes
    providers based on the `LLM_PROVIDER` setting, but will fall back to the
    next available provider if the preferred one fails to initialize (e.g., due
    to a missing API key, a service outage, or a model not being found).

    The selection priority is:
    - If `LLM_PROVIDER` is "openrouter" (default): Try OpenRouter, then OpenAI.
    - If `LLM_PROVIDER` is "legacy_openai": Try OpenAI, then OpenRouter.

    Args:
        settings: The application settings containing API keys and model names.

    Returns:
        A configured and validated `LLMProvider` instance.

    Raises:
        ProviderCreationError: If no valid provider can be successfully created
            after trying all available options.
    """
    # Define the available provider creation functions and their required keys.
    provider_configs = [
        ("OpenRouter", _create_openrouter_provider, settings.OPENROUTER_API_KEY),
        ("OpenAI", _create_openai_provider, settings.OPENAI_API_KEY),
    ]

    # Reorder list such that preferred provider (via LLM_PROVIDER) is first
    if settings.LLM_PROVIDER == "legacy_openai":
        provider_configs = [
            (n, f, k) for n, f, k in provider_configs if n == "OpenAI"
        ] + [(n, f, k) for n, f, k in provider_configs if n == "OpenRouter"]
        # If chosen provider lacks API key, log and allow fallback instead of hard-failing
        if not settings.OPENAI_API_KEY.get_secret_value():
            logger.warning(
                "LLM_PROVIDER is set to 'legacy_openai' but OPENAI_API_KEY is not configured. Will try fallback provider."
            )
    else:  # default openrouter
        provider_configs = [
            (n, f, k) for n, f, k in provider_configs if n == "OpenRouter"
        ] + [(n, f, k) for n, f, k in provider_configs if n == "OpenAI"]
        if not settings.OPENROUTER_API_KEY.get_secret_value():
            logger.warning(
                "LLM_PROVIDER is set to 'openrouter' but OPENROUTER_API_KEY is not configured. Will try fallback provider."
            )

    # Try each provider in order
    attempted_count = 0
    for provider_name, create_func, api_key in provider_configs:
        if not api_key.get_secret_value():
            continue

        # Determine log level: warning for first attempted provider, error for subsequent ones
        log_level = "warning" if attempted_count == 0 else "error"
        attempted_count += 1

        provider = _try_create_provider(create_func, settings, provider_name, log_level)
        if provider:
            return provider

    raise ProviderCreationError(
        "No valid LLM provider available. Please set either OPENROUTER_API_KEY or OPENAI_API_KEY."
    )


def _try_create_provider(
    create_func: Callable[[Settings], LLMProvider],
    settings: Settings,
    provider_name: str,
    log_level: str,
) -> Optional[LLMProvider]:
    """Helper to safely create and validate a provider, handling all errors.

    This function wraps the creation and validation logic for a single provider,
    catching any exceptions that occur and logging them appropriately. This
    prevents a failure in one provider from crashing the application, allowing
    the `create_llm_provider` function to fall back to the next one.

    Args:
        create_func: The function to call to create the provider instance.
        settings: The application settings.
        provider_name: The name of the provider (for logging).
        log_level: The log level to use for creation errors.

    Returns:
        A validated provider instance, or `None` if creation or validation failed.
    """
    try:
        provider = create_func(settings)
        if _validate_and_fallback_model(provider, settings):
            logger.info(
                f"Using {provider_name} provider with model: {provider.config.model_name}"
            )
            return provider
        return None
    except (ValueError, TypeError) as e:
        # Configuration or validation errors
        if log_level == "warning":
            logger.warning(f"{provider_name} provider configuration error: {e}")
        else:
            logger.error(f"{provider_name} provider configuration error: {e}")
        return None
    except (ConnectionError, TimeoutError) as e:
        # Network connectivity issues
        logger.error(
            f"{provider_name} connection error: {e}. Will try fallback provider."
        )
        return None
    except Exception as e:
        # Special handling for OpenRouter service outages
        if (
            provider_name == "OpenRouter"
            and hasattr(e, "status_code")
            and getattr(e, "status_code", 0) >= 500
        ):
            logger.error(
                f"OpenRouter service appears to be down (HTTP {getattr(e, 'status_code', 'unknown')}). Will try fallback provider."
            )
            # Let the caller try the next provider
            return None
        elif provider_name == "OpenRouter" and "connection" in str(e).lower():
            logger.error(
                f"OpenRouter connection error: {e}. Will try fallback provider."
            )
            return None
        elif log_level == "warning":
            logger.warning(
                f"{provider_name} provider creation failed with unexpected error: {e}"
            )
        else:
            logger.error(
                f"{provider_name} provider creation failed with unexpected error: {e}"
            )
        return None


def _create_openrouter_provider(settings: Settings) -> OpenRouterProvider:
    """Instantiates the OpenRouter provider with its specific configuration."""
    config = ProviderConfig(
        model_name=settings.OPENROUTER_MODEL_NAME,
        temperature=settings.MODEL_TEMPERATURE,
        api_key=settings.OPENROUTER_API_KEY,
    )
    return OpenRouterProvider(config)


def _create_openai_provider(settings: Settings) -> OpenAIProvider:
    """Instantiates the OpenAI provider with its specific configuration."""
    # Use legacy model name for direct OpenAI
    model_name = settings.LEGACY_OPENAI_MODEL_NAME

    config = ProviderConfig(
        model_name=model_name,
        temperature=settings.MODEL_TEMPERATURE,
        api_key=settings.OPENAI_API_KEY,
    )
    return OpenAIProvider(config)


def _validate_and_fallback_model(provider: LLMProvider, settings: Settings) -> bool:
    """Validates a provider's model and determines if the factory should proceed.

    This function calls the provider's `validate_model_capabilities` method.
    The validation is stricter for the direct OpenAI provider than for OpenRouter.
    If validation fails, this function returns `False`, signaling to the main
    creation loop that it should try the next available provider.

    Args:
        provider: The provider instance to validate.
        settings: The application settings.

    Returns:
        `True` if the provider is valid and should be used.
        `False` if the provider is not valid and the factory should fall back.

    Raises:
        Exception: Re-raises connection or service errors to trigger fallback.
    """
    try:
        # For OpenRouter, we want to check for service availability but treat capability failures as warnings
        if isinstance(provider, OpenRouterProvider):
            try:
                cap_result = provider.validate_model_capabilities()
                if cap_result is False:
                    logger.warning(
                        f"Model {provider.config.model_name} may lack some required capabilities, "
                        "but continuing anyway as per relaxed validation policy."
                    )
                    return True
                elif cap_result is None:
                    # Model not found at all -> trigger fallback
                    logger.error(
                        f"Model {provider.config.model_name} not found on OpenRouter. Falling back to next provider."
                    )
                    return False
                else:
                    return True
            except Exception as e:
                # If we get HTTP 5xx or connection errors, let this bubble up for fallback
                if hasattr(e, "status_code") and getattr(e, "status_code", 0) >= 500:
                    logger.error(f"OpenRouter service error: {e}")
                    raise  # Re-raise to trigger fallback
                if "connection" in str(e).lower():
                    logger.error(f"OpenRouter connection error: {e}")
                    raise  # Re-raise to trigger fallback

                # For other errors, log warning and continue
                logger.warning(f"Non-critical OpenRouter validation error: {e}")
                return True
        else:
            # For OpenAI or other providers: capability check must pass; otherwise fall back
            cap_ok = provider.validate_model_capabilities()
            if cap_ok:
                return True
            logger.error(
                f"Provider {provider.provider_name} model {provider.config.model_name} "
                "lacks required capabilities. Trying next provider if available."
            )
            return False
    except Exception as e:
        logger.error(f"Critical error validating provider: {e}")
        raise  # Re-raise to trigger fallback


def create_llm_from_provider(provider: LLMProvider) -> BaseLanguageModel:
    """Creates a LangChain `BaseLanguageModel` instance from a provider.

    This function is the final step, calling the `create_llm` method on a
    successfully created and validated provider.

    Args:
        provider: The configured and validated `LLMProvider` instance.

    Returns:
        A LangChain `BaseLanguageModel` instance ready to be used in an agent.
    """
    return provider.create_llm()
