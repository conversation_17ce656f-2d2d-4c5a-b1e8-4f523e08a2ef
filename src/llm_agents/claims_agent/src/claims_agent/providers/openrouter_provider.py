"""OpenRouter provider implementation."""

import httpx
from langchain_core.language_models import BaseLanguageModel
from langchain_openai import ChatOpenAI
from loguru import logger

from .provider_interface import LLMProvider


class OpenRouterProvider(LLMProvider):
    """OpenRouter LLM provider implementation."""

    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    OPENROUTER_MODELS_URL = "https://openrouter.ai/api/v1/models"

    def create_llm(self) -> BaseLanguageModel:
        """Create and return a configured OpenRouter LLM instance."""
        logger.debug(
            f"Creating OpenRouter ChatOpenAI instance with model='{self.config.model_name}'"
        )

        return ChatOpenAI(
            model=self.config.model_name,
            temperature=self.config.temperature,
            api_key=self.config.api_key,
            base_url=self.OPENROUTER_BASE_URL,
            default_headers={
                "HTTP-Referer": "https://github.com/nirvanatech/nirvana",
                "X-Title": "Nirvana Claims Agent",
            },
        )

    def validate_model_capabilities(self) -> bool | None:
        """Validate that the OpenRouter model supports required capabilities."""
        try:
            # Query OpenRouter models API to check capabilities
            response = httpx.get(self.OPENROUTER_MODELS_URL, timeout=10.0)
            response.raise_for_status()

            models_data = response.json()

            # Find our model in the response
            for model in models_data.get("data", []):
                if model.get("id") == self.config.model_name:
                    supported_params = model.get("supported_parameters", [])

                    # Check for required capabilities
                    has_tools = "tools" in supported_params
                    has_structured_outputs = "structured_outputs" in supported_params

                    logger.debug(
                        f"Model {self.config.model_name} capabilities: "
                        f"tools={has_tools}, structured_outputs={has_structured_outputs}"
                    )

                    # Only require tools support, structured outputs are optional
                    return has_tools

            logger.warning(
                f"Model {self.config.model_name} not found in OpenRouter models list"
            )
            # None signals 'model not found' so caller can decide how to fallback
            return None

        except (httpx.HTTPError, ValueError, KeyError) as e:
            # Network issues or unexpected payloads should not block provider usage in dev.
            # Assume the model is usable but log a warning so issues can be investigated.
            logger.warning(
                f"Skipping capability validation due to API error: {e}. "
                "Assuming model supports required capabilities."
            )
            return True
        except Exception as e:
            logger.error(
                f"Unexpected error validating OpenRouter model capabilities: {e}"
            )
            # Re-raise unexpected errors to avoid hiding critical issues
            raise

    @property
    def provider_name(self) -> str:
        """Return the name of this provider."""
        return "OpenRouter"
