"""Provider interface for LLM clients."""

from abc import ABC, abstractmethod

from langchain_core.language_models import BaseLanguageModel
from pydantic import BaseModel, SecretStr, Field, field_validator


class ProviderConfig(BaseModel):
    """Configuration for LLM providers."""

    model_name: str
    temperature: float = Field(
        default=0.7,
        ge=0.0,
        le=2.0,
        description="Temperature for model generation (0.0-2.0)",
    )
    api_key: SecretStr

    @field_validator("temperature")
    @classmethod
    def validate_temperature(cls, v: float) -> float:
        """Validate temperature is within valid range."""
        if not 0.0 <= v <= 2.0:
            raise ValueError(f"Temperature must be between 0.0 and 2.0, got {v}")
        return v

    @field_validator("api_key", mode="after")
    @classmethod
    def validate_api_key_type(cls, v: SecretStr) -> SecretStr:
        """Ensure API key is not empty."""
        if not v.get_secret_value().strip():
            raise ValueError("API key cannot be empty")
        return v


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""

    def __init__(self, config: ProviderConfig) -> None:
        """Initialize the provider with configuration.

        Args:
            config: Provider configuration including model name, temperature, and API key.
        """
        self.config = config

    @abstractmethod
    def create_llm(self) -> BaseLanguageModel:
        """Create and return a configured LLM instance.

        Returns:
            A configured LangChain BaseLanguageModel instance.

        Raises:
            ValueError: If the provider configuration is invalid.
            ConnectionError: If the provider is unreachable.
        """
        pass

    @abstractmethod
    def validate_model_capabilities(self) -> bool | None:
        """Validate that the model supports required capabilities.

        Returns:
            • True  – model supports required capabilities.
            • False – model exists but lacks capabilities (warning path).
            • None  – model not found / unsupported; caller should fall back to another provider.
        """
        pass

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Return the name of this provider."""
        pass
