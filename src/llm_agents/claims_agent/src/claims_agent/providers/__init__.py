"""Provider abstraction for LLM clients.

This module provides a unified interface for different LLM providers
(OpenRouter, OpenAI, etc.) with automatic fallback capabilities.
"""

from .provider_interface import LLMProvider, ProviderConfig
from .provider_factory import create_llm_provider, create_llm_from_provider

__all__ = [
    "LLMProvider",
    "ProviderConfig",
    "create_llm_provider",
    "create_llm_from_provider",
]
