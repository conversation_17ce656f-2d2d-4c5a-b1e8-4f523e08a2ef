"""Configuration settings for the Claims Agent service.

This module provides settings management using Pydantic v2, loading configuration
from environment variables and .env files.
"""

from functools import lru_cache
from typing import Literal, Optional, Any

from pydantic import Field, SecretStr, ValidationInfo, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

# Sentinel placeholder used when no real Arize API key is supplied. Treated as "empty".
ARIZE_PLACEHOLDER_KEY = "ak-placeholder"


class Settings(BaseSettings):
    """Application settings loaded from environment variables or .env file.

    Attributes:
        ENVIRONMENT: The environment the application is running in.
        HOST: The host to bind the server to.
        PORT: The port to bind the server to.
        LOG_LEVEL: The logging level to use.
        UVICORN_WORKERS: Number of worker processes for Uvicorn.
        OPENROUTER_API_KEY: OpenRouter API key (primary provider).
        OPENROUTER_MODEL_NAME: OpenRouter model name with provider prefix.
        OPENAI_API_KEY: OpenAI API key (fallback provider).
        LEGACY_OPENAI_MODEL_NAME: OpenAI model name for direct API access.
        MODEL_TEMPERATURE: LLM temperature setting.
        AGENT_VERBOSE: Whether to enable verbose logging for agent execution.
        ARIZE_ENABLED: Whether Arize tracing is enabled.
        ARIZE_API_KEY: Arize API key for authentication.
        ARIZE_SPACE_ID: Arize space ID for organizing traces.
        ARIZE_PROJECT_NAME: Arize project name for organizing traces.
        SUPABASE_DB_URL: Database URL in SQLAlchemy form, e.g. ****************************
        TEMPORAL_ADDRESS: Temporal workflow engine address
        TEMPORAL_NAMESPACE: Temporal workflow engine namespace
        TEMPORAL_API_KEY: Temporal workflow engine API key
        TEMPORAL_USE_TLS: Whether to use TLS for Temporal connections
        TEMPORAL_WORKER_TASK_QUEUE: Temporal worker task queue
        DEFAULT_MCP_AUTHORIZATION: Optional bearer token for MCP authorization
        MCP_MAX_RETRIES: Maximum retries for MCP client operations
    """

    model_config = SettingsConfigDict(
        env_file=".env", case_sensitive=True, extra="ignore"
    )

    ENVIRONMENT: Literal["development", "production", "testing"] = "development"
    HOST: str = "127.0.0.1"
    PORT: int = 8001
    LOG_LEVEL: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "INFO"
    UVICORN_WORKERS: int = 4
    SENTRY_DSN: str = Field(default="")

    # Set to true to log detailed agent execution steps
    AGENT_VERBOSE: bool = Field(default=False)

    # Provider Selection
    LLM_PROVIDER: Literal["openrouter", "legacy_openai"] = Field(default="openrouter")

    # OpenRouter (Primary Provider)
    # Placeholder non-empty key allows local development without configuring a
    # real credential while still satisfying the provider-factory selection
    # logic.  The value is obviously invalid and *must* be overridden in
    # production via the environment.
    OPENROUTER_API_KEY: SecretStr = Field(default=SecretStr("or-placeholder-key"))
    OPENROUTER_MODEL_NAME: str = Field(default="google/gemini-2.5-pro-preview")

    # Legacy OpenAI
    OPENAI_API_KEY: SecretStr = Field(default=SecretStr(""))
    LEGACY_OPENAI_MODEL_NAME: str = Field(default="o4-mini")

    MODEL_TEMPERATURE: float = Field(default=1.0, ge=0.0, le=2.0)

    CLAIMS_SERVER_URL: str = Field(default="http://0.0.0.0:8000/claims/sse")
    POLICY_SERVER_URL: str = Field(default="http://0.0.0.0:8000/policy/sse")

    # Nirvana REST API configuration
    NIRVANA_API_BASE_URL: str = Field(
        default="https://api.prod.nirvanatech.com",
        description="Base URL for the Nirvana REST API used for authentication",
    )

    MCP_DEFAULT_TIMEOUT_SECONDS: int = Field(
        default=30, description="Default timeout for MCP client HTTP requests."
    )
    MCP_SSE_READ_TIMEOUT_SECONDS: int = Field(
        default=50, description="Default read timeout for MCP client SSE connections."
    )
    MCP_MAX_RETRIES: int = Field(
        default=3,
        description="Maximum retries for MCP client operations, if applicable.",
    )

    # ------------------------------------------------------------------
    # Coverage Determination Caching
    # ------------------------------------------------------------------

    COVERAGE_DETERMINATION_TTL_SECONDS: int = Field(
        default=604800,  # 7 days
        ge=0,
        description="TTL in seconds for coverage determination cache entries.",
    )
    COVERAGE_DETERMINATION_CACHE_ENABLED: bool = Field(
        default=True,
        description="Whether to enable coverage determination caching.",
    )

    # ------------------------------------------------------------------
    # Developer token for MCP auth (long-lived Clerk JWT)
    # ------------------------------------------------------------------

    DEFAULT_MCP_AUTHORIZATION: str | None = Field(
        default=None,
        description=(
            "Optional bearer token automatically attached to outbound MCP calls "
            "during development. Set via environment variable or .env file to "
            "avoid passing headers in curl / Postman."
        ),
    )

    # Arize experiment / tracing configuration
    # Disabled by default so local tests and basic production deployments
    # that omit Arize credentials do not fail validation.  Teams can opt-in
    # by setting ARIZE_ENABLED=true *and* providing ARIZE_API_KEY (and
    # optional SPACE_ID) via environment variables.
    ARIZE_ENABLED: bool = Field(
        default=False, description="Whether to enable Arize tracing"
    )
    ARIZE_API_KEY: Optional[SecretStr] = Field(
        default=SecretStr(ARIZE_PLACEHOLDER_KEY),
        description="Arize API key for authentication (placeholder)",
    )
    ARIZE_SPACE_ID: Optional[str] = Field(
        default="",
        description=(
            "Arize space ID for organizing traces (public identifier). "
            "Leave empty in development/testing to use the default public space."
        ),
    )
    ARIZE_PROJECT_NAME: Optional[str] = Field(
        default="ClaimsIQ", description="Arize project name for organizing traces"
    )

    # ------------------------------------------------------------------
    # Supabase / Postgres persistence (async workflow)
    # ------------------------------------------------------------------

    SUPABASE_DB_URL: SecretStr = Field(
        default=SecretStr(""),
        description="Database URL in SQLAlchemy form, e.g. ****************************",
    )

    # ------------------------------------------------------------------
    # Temporal workflow engine
    # ------------------------------------------------------------------

    TEMPORAL_ADDRESS: str = Field(
        default="localhost:7233",
        description="Temporal server host:port to connect to (defaults to local dev server).",
    )
    TEMPORAL_NAMESPACE: str = Field(
        default="default",
        description="Temporal namespace to use for all workflow calls.",
    )
    TEMPORAL_API_KEY: SecretStr | None = Field(
        default=None,
        description="Optional Temporal API key used when connecting through the Cloud Frontend.",
    )
    TEMPORAL_USE_TLS: bool = Field(
        default=False,
        description="Enable TLS when connecting to the Temporal Frontend (should be true in prod).",
    )
    TEMPORAL_WORKER_TASK_QUEUE: str | None = Field(
        default=None,
        description="Task-queue for the worker. Required if you run the built-in worker process.",
    )

    @model_validator(mode="after")
    def validate_provider_keys(self) -> "Settings":
        """Ensure at least one API key is available for provider fallback in production."""
        if self.ENVIRONMENT in ("testing", "development"):
            return self

        # Require at least one API key to be present for provider fallback
        # The provider factory will handle fallback between OpenRouter and OpenAI
        if (
            not self.OPENROUTER_API_KEY.get_secret_value()
            and not self.OPENAI_API_KEY.get_secret_value()
        ):
            raise ValueError(
                "Either OPENROUTER_API_KEY or OPENAI_API_KEY must be set. "
                "OpenRouter is preferred for unified model access."
            )
        return self

    @field_validator("CLAIMS_SERVER_URL", "POLICY_SERVER_URL", mode="after")
    @classmethod
    def validate_mcp_server_urls_in_production(
        cls, value: str, info: ValidationInfo
    ) -> str:
        """Ensure MCP server URLs are not default local URLs in production."""
        environment = info.data.get("ENVIRONMENT")
        if environment == "production" and (
            value.startswith("http://0.0.0.0") or value.startswith("http://localhost")
        ):
            raise ValueError(
                f"Default local MCP server URL '{value}' is not allowed in production. "
                "Please set a valid production URL."
            )
        return value

    @field_validator("ARIZE_API_KEY", mode="after")
    @classmethod
    def validate_arize_api_key(
        cls, value: Optional[SecretStr], info: ValidationInfo
    ) -> Optional[SecretStr]:
        """Validate Arize API key configuration.

        API key is optional for local development but recommended for production.
        """
        enabled = info.data.get("ARIZE_ENABLED", False)
        env = info.data.get("ENVIRONMENT")

        # Helper to treat unset, empty-string, or placeholder values as "missing"
        def _key_missing(secret: Optional[SecretStr]) -> bool:
            if secret is None:
                return True
            raw = (
                secret.get_secret_value()
                if isinstance(secret, SecretStr)
                else str(secret)
            )
            return raw == "" or raw == ARIZE_PLACEHOLDER_KEY

        if enabled:
            # In production, ARIZE_API_KEY and SPACE_ID are strictly required
            if env == "production":
                if _key_missing(value):
                    raise ValueError(
                        "ARIZE_API_KEY must be set when ARIZE_ENABLED is True in production"
                    )

                # ARIZE_SPACE_ID has a sensible default; no need to enforce here

            else:
                # For development / testing, warn rather than error so local tests run without
                # requiring real Arize credentials.
                logger = __import__("loguru").logger
                if _key_missing(value):
                    logger.warning(
                        "ARIZE_API_KEY is not set; tracing will be disabled in dev/testing"
                    )

        elif env == "production" and _key_missing(value):
            # In production we warn but don't fail; keep INFO level enforcement here minimal
            # local import to avoid circular
            logger = __import__("loguru").logger
            logger.warning(
                "ARIZE_API_KEY not configured in production; tracing disabled"
            )

        return value

    @model_validator(mode="after")
    def validate_temporal_config(self) -> "Settings":
        """Emit warnings (or future errors) for inconsistent Temporal settings."""
        if self.ENVIRONMENT == "production":
            from loguru import (
                logger,
            )  # local import to avoid global logger on import time

            if not self.TEMPORAL_USE_TLS:
                logger.warning(
                    "Production environment detected but TEMPORAL_USE_TLS is False. TLS is strongly recommended."
                )

            if not (self.TEMPORAL_API_KEY and self.TEMPORAL_API_KEY.get_secret_value()):
                logger.warning(
                    "Production environment detected but TEMPORAL_API_KEY is not set. This may prevent authentication to Temporal Cloud."
                )

        return self


# ---------------------------------------------------------------------------
# Lazy Settings access helpers
# ---------------------------------------------------------------------------
# Import-time instantiation of Settings caused validation side-effects and
# logger warnings before the application logging pipeline was configured.  We
# now defer construction until the first attribute access while keeping the
# original public `settings` symbol for backwards compatibility.


@lru_cache(maxsize=1)
def _build_settings() -> "Settings":
    instance = Settings()

    # Post-validation advisory messages that depend on multiple fields – these
    # were previously emitted at import time; they now run the first time
    # settings are accessed (usually after logging has been configured).
    if instance.ARIZE_ENABLED and instance.ENVIRONMENT in ("development", "testing"):
        from loguru import logger as _logger  # local import to avoid early logger init

        if not instance.ARIZE_SPACE_ID:
            _logger.warning(
                "ARIZE_SPACE_ID is not configured; tracing may not work fully in dev/testing"
            )

    return instance


# noqa: D401 – simple sentence style
class _LazySettings:
    """Proxy that builds the actual Settings object on first use."""

    def __getattr__(self, item: str) -> Any:  # type: ignore[override]
        """Forward attribute access to the underlying cached Settings."""

        return getattr(_build_settings(), item)

    # type: ignore[override]
    def __setattr__(self, key: str, value: Any) -> None:
        """Forward attribute mutation to the underlying cached Settings."""

        setattr(_build_settings(), key, value)

    def __repr__(self) -> str:  # pragma: no cover – debug helper
        return f"<LazySettings loaded={_build_settings.cache_info().currsize > 0}>"


# Public module-level handle – remains import-compatible with historical code
# like `from claims_agent.config import settings` but avoids eager validation.
settings = _LazySettings()  # type: ignore[var-annotated]


def get_settings() -> "Settings":
    """Return the cached Settings instance (build on first call)."""

    return _build_settings()
