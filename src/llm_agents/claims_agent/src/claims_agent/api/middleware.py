from __future__ import annotations

import base64
import json
from typing import Any, Awaitable, Callable, Optional

from fastapi import Request, Response, HTTPException
from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

# OpenTelemetry imports with graceful fallback
try:
    from opentelemetry import trace

    _trace_available = True
except ImportError:
    trace = None  # type: ignore
    _trace_available = False

from claims_agent.api.user_context import set_user_email


def _extract_email_from_jwt_header(header_value: Optional[str]) -> Optional[str]:
    """Extract the `email` claim from a Bearer JWT header value.

    We *do not* verify the signature – we only need the payload for tracing &
    logging.  Any failures simply return *None*.
    """
    if not header_value or not header_value.lower().startswith("bearer "):
        return None

    token = header_value[7:].strip()
    parts = token.split(".")
    if len(parts) < 2:
        return None  # Not a valid JWT structure

    payload_b64 = parts[1]
    # JWT uses URL-safe base64 without padding – add padding if required
    padding = "=" * (-len(payload_b64) % 4)
    try:
        payload_json = base64.urlsafe_b64decode(payload_b64 + padding).decode()
        payload: dict[str, Any] = json.loads(payload_json)
    except Exception as e:  # noqa: BLE001
        logger.debug(f"Unable to decode JWT payload for user email: {e}")
        return None

    # primary_email_address key is specific to Clerk.
    email: Optional[str] = payload.get("primary_email_address")
    return email


class UserContextMiddleware(BaseHTTPMiddleware):
    """Populate request & trace context with the current user's e-mail."""

    async def dispatch(
        self,
        request: Request,
        call_next: Callable[[Request], Awaitable[Response]],
    ) -> Response:
        # Extract email from Clerk-Authorization or Authorization header
        email = _extract_email_from_jwt_header(
            request.headers.get("Clerk-Authorization")
        )
        if email is None:
            email = _extract_email_from_jwt_header(request.headers.get("Authorization"))

        # Persist in ContextVar so downstream code can access it
        set_user_email(email)

        # Attach to current span for OpenTelemetry traces (best-effort)
        if _trace_available and email:
            try:
                span = trace.get_current_span()
                if span:
                    span.set_attribute("user.email", email)
            except Exception:  # noqa: BLE001
                # Fail softly – tracing may be disabled or unavailable
                pass

        # Save to request.state for easy access inside endpoint functions
        request.state.user_email = email

        # Continue processing
        response = await call_next(request)
        return response


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Authentication middleware that validates tokens via the Nirvana REST API /me endpoint."""

    def __init__(
        self,
        app: Any,
        paths_to_skip: list[str] | None = None,
        nirvana_api_base_url: str = "",
    ) -> None:
        """Initialize the authentication middleware.

        Args:
            app: The FastAPI application
            paths_to_skip: List of URL paths to exempt from authentication checks
            nirvana_api_base_url: Base URL for the Nirvana REST API
        """
        super().__init__(app)
        self._paths_to_skip = paths_to_skip or [
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
        ]
        self._nirvana_api_base_url = nirvana_api_base_url

    async def dispatch(
        self,
        request: Request,
        call_next: Callable[[Request], Awaitable[Response]],
    ) -> Response:
        path = request.url.path

        # Skip authentication for certain paths
        if path in self._paths_to_skip:
            logger.trace(f"Skipping auth for path: {path}")
            return await call_next(request)

        # Skip authentication for all OPTIONS requests (CORS preflight)
        # This allows OPTIONS requests to pass through without authentication,
        # even for authenticated endpoints, similar to the Go middleware
        if request.method == "OPTIONS":
            logger.trace(f"Skipping auth for OPTIONS request: {path}")
            return await call_next(request)

        logger.trace(f"Attempting authentication for path: {path}")

        # Check for Authorization or Clerk-Authorization header
        auth_header = request.headers.get("Authorization")
        clerk_auth_header = request.headers.get("Clerk-Authorization")

        token = None

        if clerk_auth_header:
            if clerk_auth_header.lower().startswith("bearer "):
                token = clerk_auth_header[7:].strip()
        elif auth_header:
            if auth_header.lower().startswith("bearer "):
                token = auth_header[7:].strip()

        if not token:
            logger.warning(
                f"Authentication failed: No valid Clerk-Authorization token found. Path: {path}"
            )
            return JSONResponse(
                status_code=401,
                content={
                    "detail": "Authorization header with Bearer token is required"
                },
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Validate the token with the Nirvana REST API
        try:
            from nirvana_rest_api.client import AuthenticatedClient
            from nirvana_rest_api.api.auth.get_me import asyncio_detailed as get_me

            async with AuthenticatedClient(
                base_url=self._nirvana_api_base_url,
                token=token,
                # Nirvana API expects Clerk-Authorization header only
                auth_header_name="Clerk-Authorization",
                prefix="Bearer",
            ) as client:
                response = await get_me(client=client)

            # Handle different response statuses
            if response.status_code >= 500:
                logger.error(
                    f"Nirvana API returned server error: {response.status_code}"
                )
                return JSONResponse(
                    status_code=502,
                    content={"detail": "Authentication service unavailable"},
                )
            elif response.status_code >= 400:
                logger.warning(
                    f"Authentication failed: Nirvana API returned {response.status_code}"
                )
                return JSONResponse(
                    status_code=401,
                    content={"detail": "Invalid or expired token"},
                    headers={"WWW-Authenticate": "Bearer"},
                )
            elif response.status_code != 200:
                logger.error(
                    f"Nirvana API returned unexpected status: {response.status_code}"
                )
                return JSONResponse(
                    status_code=502, content={"detail": "Authentication service error"}
                )
            elif response.parsed is None:
                logger.error("Nirvana API returned 200 but failed to parse response")
                return JSONResponse(
                    status_code=502, content={"detail": "Authentication service error"}
                )

            logger.debug(f"Successfully authenticated user: {response.parsed.email}")

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise HTTPException(
                status_code=500, detail="Internal authentication error"
            ) from e

        # Continue processing the request
        return await call_next(request)
