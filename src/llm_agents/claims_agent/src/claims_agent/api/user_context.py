from __future__ import annotations

from contextvars import ContextVar
from typing import Optional

# A ContextVar gives us per-coroutine / per-request isolation.
_user_email_var: ContextVar[Optional[str]] = ContextVar("user_email", default=None)


def set_user_email(email: Optional[str]) -> None:
    """Store email for the current request context."""
    _user_email_var.set(email)


def get_user_email() -> Optional[str]:
    """Return the user email for the current context, if any."""
    return _user_email_var.get()
