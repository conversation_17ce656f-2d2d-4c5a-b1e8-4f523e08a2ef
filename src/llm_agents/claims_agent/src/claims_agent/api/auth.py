"""Authentication related dependencies for the Claims Agent API."""

from typing import Annotated, cast

from fastapi import <PERSON><PERSON>
from loguru import logger
from claims_agent.config import settings


def _parse_bearer_token(header_value: str | None, header_name: str) -> str | None:
    """Parse a 'Bearer <token>' header.

    Args:
        header_value: The value of the header.
        header_name: The name of the header, for logging purposes.

    Returns:
        The token if valid, otherwise None. Handles case-insensitivity
        and logs a warning for empty tokens.
    """
    if not header_value or not header_value.lower().startswith("bearer "):
        return None

    token = header_value[7:].strip()
    if not token:
        logger.warning(f"{header_name} header found but Bearer token is empty.")
        return None

    return token


async def get_formatted_mcp_authorization(
    clerk_authorization: Annotated[
        str | None, Header(alias="Clerk-Authorization")
    ] = None,
    authorization: Annotated[str | None, Header(alias="Authorization")] = None,
    jsessionid: Annotated[str | None, Header(alias="JSESSIONID")] = None,
) -> str | None:
    """FastAPI dependency to extract and format the authorization for MCP calls.

    Checks for 'Clerk-Authorization: Bearer <token>' header first.
    If not found or invalid, checks for 'Authorization: Bearer <token>' header.
    If not found or invalid, checks for 'JSESSIONID: <value>' header.
    Formats the found value as 'Authorization: Bearer <value>' for downstream use.

    Args:
        clerk_authorization: The Clerk-Authorization header value, if present
        authorization: The Authorization header value, if present
        jsessionid: The JSESSIONID header value, if present

    Returns:
        The formatted "Bearer <value>" string or None if neither header is suitable.
    """
    parsed_clerk = _parse_bearer_token(clerk_authorization, "Clerk-Authorization")
    if parsed_clerk is not None:
        logger.debug("Using Clerk-Authorization header for MCP auth.")
        return f"Bearer {parsed_clerk}"

    parsed_auth = _parse_bearer_token(authorization, "Authorization")
    if parsed_auth is not None:
        logger.debug("Using Authorization header for MCP auth.")
        return f"Bearer {parsed_auth}"

    if jsessionid is not None:
        stripped_jsid = jsessionid.strip()
        if stripped_jsid:
            logger.debug("Using JSESSIONID header for MCP auth, formatted as Bearer.")
            return f"Bearer {stripped_jsid}"

    logger.debug("No suitable Authorization or JSESSIONID header found for MCP auth.")

    # ------------------------------------------------------------------
    # Development-only fallback via .env / environment variable
    # ------------------------------------------------------------------
    if settings.ENVIRONMENT == "development" and settings.DEFAULT_MCP_AUTHORIZATION:
        logger.debug(
            "Using DEFAULT_MCP_AUTHORIZATION from settings (development only)."
        )

        token_val_raw = settings.DEFAULT_MCP_AUTHORIZATION.strip()

        # If the env var contains only whitespace, treat as unset
        if not token_val_raw:
            logger.warning(
                "DEFAULT_MCP_AUTHORIZATION is set but empty after stripping – ignoring."
            )
            return None

        token_val: str = cast(str, token_val_raw)

        # Prepend 'Bearer ' if caller forgot it
        if not token_val.lower().startswith("bearer "):
            token_val = f"Bearer {token_val}"

        return token_val

    return None
