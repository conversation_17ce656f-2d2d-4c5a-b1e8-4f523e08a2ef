"""Error handling for the Claims Agent API.

This module provides custom exception classes and error handlers for the API.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, status
from fastapi.responses import JSONResponse
from loguru import logger


class ClaimsAgentError(Exception):
    """Base exception for all Claims Agent errors."""

    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    detail = "An unexpected error occurred"

    def __init__(self, detail: str | None = None) -> None:
        """Initialize the exception.

        Args:
            detail: A detail message for the error.
        """
        self.detail = detail or self.detail
        super().__init__(self.detail)


class AgentNotInitializedError(ClaimsAgentError):
    """Exception raised when the MCP agent is not initialized."""

    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    detail = "Agent is not initialized. Please try again later."


class AuthenticationError(ClaimsAgentError):
    """Exception raised when authentication fails."""

    status_code = status.HTTP_401_UNAUTHORIZED
    detail = "Authentication failed"


class ClaimNotFoundError(ClaimsAgentError):
    """Exception raised when a claim is not found."""

    status_code = status.HTTP_404_NOT_FOUND
    detail = "Claim not found"


class ProcessingError(ClaimsAgentError):
    """Exception raised when processing a request fails."""

    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    detail = "Error processing request"


class DataExtractionError(ClaimsAgentError):
    """Exception raised when data extraction fails."""

    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    detail = "Error extracting data"


class InvalidInputError(ClaimsAgentError):
    """Exception raised when input validation fails."""

    status_code = status.HTTP_400_BAD_REQUEST
    detail = "Invalid input provided"


def register_exception_handlers(app: FastAPI) -> None:
    """Register exception handlers for the FastAPI application.

    Args:
        app: The FastAPI application instance.
    """

    @app.exception_handler(HTTPException)
    async def http_exception_handler(
        request: Request, exc: HTTPException
    ) -> JSONResponse:
        """Handle HTTPException with cache headers preservation.

        Args:
            request: The request that caused the exception.
            exc: The HTTPException instance.

        Returns:
            A JSON response with the error details and preserved cache headers.
        """
        logger.error(f"HTTPException: {exc.detail}")

        # Check for cache headers in request state
        headers = {}
        if hasattr(request.state, "cache_headers"):
            headers.update(request.state.cache_headers)
            # Update cache status to ERROR for error responses
            headers["X-Cache-Status"] = "ERROR"

        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
            headers=headers,
        )

    @app.exception_handler(ClaimsAgentError)
    async def claims_agent_exception_handler(
        request: Request, exc: ClaimsAgentError
    ) -> JSONResponse:
        """Handle ClaimsAgentError exceptions.

        Args:
            request: The request that caused the exception.
            exc: The exception instance.

        Returns:
            A JSON response with the error details.
        """
        logger.error(f"ClaimsAgentError: {exc.detail}")

        # Check for cache headers in request state
        headers = {}
        if hasattr(request.state, "cache_headers"):
            headers.update(request.state.cache_headers)
            # Update cache status to ERROR for error responses
            headers["X-Cache-Status"] = "ERROR"

        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail},
            headers=headers,
        )
