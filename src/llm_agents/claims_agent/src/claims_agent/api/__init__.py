"""API package for the Claims Agent service.

This package contains the FastAPI application and route handlers.
"""

import contextlib
from collections.abc import AsyncIterator
from importlib.metadata import version as get_version
from typing import Optional, cast

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi_injector import attach_injector
from injector import Injector
from loguru import logger

from claims_agent.api.errors import register_exception_handlers
from claims_agent.config import settings, get_settings
from claims_agent.di.config_module import ConfigModule
from claims_agent.instrumentation import setup_arize_tracing
from claims_agent.api.middleware import (
    UserContextMiddleware,
    AuthenticationMiddleware,
)


def create_app(injector: Optional[Injector] = None) -> FastAPI:
    """Create and configure the FastAPI application.

    Args:
        injector: Pre-constructed :class:`injector.Injector` to attach to the
            FastAPI application.  If *None* (default) a fresh container is
            built internally.

    Returns:
        Configured FastAPI application instance.
    """
    # Only enable interactive docs and schema for non-production environments
    docs_url = "/docs" if settings.ENVIRONMENT != "production" else None
    redoc_url = "/redoc" if settings.ENVIRONMENT != "production" else None
    openapi_url = "/openapi.json" if settings.ENVIRONMENT != "production" else None

    # ------------------------------------------------------------------
    # Lifespan handler that bootstraps tracing and the agent via the injector
    # ------------------------------------------------------------------

    @contextlib.asynccontextmanager
    async def _lifespan(app: FastAPI) -> AsyncIterator[None]:
        # 1) Tracing (non-blocking)
        if setup_arize_tracing():
            logger.info("✅ Arize tracing initialized")
        else:
            logger.info("ℹ️ Arize tracing not enabled or unavailable")

        # 2) Agent initialisation
        # local import to avoid cycles
        from claims_agent.interfaces.agents import AgentProtocol

        # Ensure we have a non-optional injector for type-checking
        inj = cast(Injector, injector)

        try:
            # type: ignore[arg-type,type-abstract]
            agent: AgentProtocol = inj.get(AgentProtocol)  # type: ignore

            if hasattr(agent, "_initialize_resources"):
                # type: ignore[attr-defined]
                await agent._initialize_resources()
            logger.info("✅ Agent successfully initialized")
        except Exception as e:  # pragma: no cover – fatal path
            logger.error(f"❌ CRITICAL: Failed to initialize agent: {e}")
            raise RuntimeError(f"Agent initialization failed: {e}") from e

        yield  # ─────────────── application runs ───────────────

        # Cleanup
        if hasattr(agent, "_cleanup_resources"):
            await agent._cleanup_resources()  # type: ignore[attr-defined]

    # ------------------------------------------------------------------

    app = FastAPI(
        title="Claims Agent API",
        description="API for the Claims Agent service",
        version=get_version("claims_agent"),
        lifespan=_lifespan,
        docs_url=docs_url,
        redoc_url=redoc_url,
        openapi_url=openapi_url,
    )
    # Allows only https://support.nirvanatech.com and https://support.local.nirvanatech.com and their subdomains
    allow_origin_regex = (
        r"^https?://([a-zA-Z0-9-]+\.)?support\.(local\.)?nirvanatech\.com$"
    )

    # Define allowed headers explicitly for better security
    allowed_headers = [
        "Content-Type",
        "Accept",
        "JSESSIONID",
        "Authorization",
        "Clerk-Authorization",
    ]

    # Define allowed methods, including OPTIONS for preflight requests due to custom header
    allowed_methods = ["GET", "OPTIONS", "POST", "PUT", "DELETE"]

    # Add CORS middleware before registering exception handlers or routers
    app.add_middleware(
        CORSMiddleware,
        allow_origin_regex=allow_origin_regex,
        allow_credentials=True,
        allow_methods=allowed_methods,
        allow_headers=allowed_headers,
    )

    # Add authentication middleware to validate tokens via /me endpoint
    app.add_middleware(
        AuthenticationMiddleware,
        paths_to_skip=["/health"],
        nirvana_api_base_url=settings.NIRVANA_API_BASE_URL,
    )

    # Add user context middleware to extract user information from auth headers
    app.add_middleware(UserContextMiddleware)

    register_exception_handlers(app)

    if injector is None:
        from claims_agent.di.agent_module import AgentModule
        from claims_agent.di.service_module import ServiceModule

        injector = Injector(
            [
                ConfigModule(),
                AgentModule(),
                ServiceModule(),
            ]
        )

    attach_injector(app, injector)

    from claims_agent.api.http import router

    app.include_router(router, prefix="/api")
    # Expose health endpoint at root path for backward compatibility
    from fastapi import status as _status

    from claims_agent.api.http import health_check

    app.add_api_route(
        "/health",
        health_check,
        methods=["GET"],
        status_code=_status.HTTP_200_OK,
        tags=["health"],
    )

    # Initialize Sentry (no-op if DSN unset)
    from claims_agent.instrumentation.sentry import register_sentry

    register_sentry(get_settings())

    # ------------------------------------------------------------------
    # Developer convenience reminder – long-lived Clerk token
    # ------------------------------------------------------------------
    if settings.ENVIRONMENT == "development" and not settings.DEFAULT_MCP_AUTHORIZATION:
        logger.warning(
            "⚠️  No DEFAULT_MCP_AUTHORIZATION token detected.\n"
            "   ↳ Most curl commands will fail against protected endpoints.\n"
            "   Run \x1b[93muv run poe get-clerk-token\x1b[0m to open a browser once\n"
            "   and generate a long-lived Clerk token, then export it or add it to your .env file."
        )

    return app
