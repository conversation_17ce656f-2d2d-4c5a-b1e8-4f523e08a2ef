"""Helper functions for instrumentation.

This module provides convenience functions for tracing operations using
OpenTelemetry directly. These functions work with the Arize tracer that
was registered via arize.otel.register().
"""

import functools
from contextlib import asynccontextmanager, contextmanager
from typing import (
    Any,
    AsyncGenerator,
    Callable,
    Dict,
    Generator,
    Optional,
    TypeVar,
)

from loguru import logger


def _merge_user_email_attribute(
    attrs: Optional[Dict[str, Any]],
) -> Dict[str, Any] | None:
    """Return *attrs* merged with the current user email (if any).

    The function is intentionally imported lazily to avoid a hard dependency on
    the API package when instrumentation is used in isolation.
    """

    try:
        from claims_agent.api.user_context import get_user_email  # local import

        email = get_user_email()
    except Exception:
        email = None

    if email is None:
        return attrs

    # Ensure we return a new dict so caller mutations don't leak
    merged: Dict[str, Any] = {**(attrs or {}), "user.email": email}
    return merged


# Type variables for function decorators
F = TypeVar("F", bound=Callable[..., Any])

# OpenTelemetry imports with graceful fallback
try:
    from opentelemetry import trace

    OTEL_AVAILABLE = True
except ImportError:
    logger.warning("OpenTelemetry not available. Tracing will be disabled.")
    OTEL_AVAILABLE = False


def _get_tracer() -> Any:
    """Get the OpenTelemetry tracer."""
    if not OTEL_AVAILABLE:
        return None
    return trace.get_tracer("claims_agent.instrumentation")


def get_current_trace_id() -> Optional[str]:
    """Get the current OpenTelemetry trace ID as a hex string.

    Returns:
        The current trace ID as a hex string, or None if no active trace or tracing is disabled.
    """
    if not OTEL_AVAILABLE:
        return None

    try:
        current_span = trace.get_current_span()
        if current_span and current_span.is_recording():
            trace_id = current_span.get_span_context().trace_id
            # Convert trace_id to hex string (remove '0x' prefix)
            return format(trace_id, "032x")
    except Exception as e:
        logger.debug(f"Failed to get current trace ID: {e}")

    return None


@contextmanager
def trace_span(
    name: str, attributes: Optional[Dict[str, Any]] = None, tracer_name: str = "arize"
) -> Generator[None, None, None]:
    """Create a trace span using OpenTelemetry.

    Args:
        name: The name of the span
        attributes: Optional attributes to add to the span
        tracer_name: Ignored - kept for compatibility

    Yields:
        None
    """
    tracer = _get_tracer()
    if not tracer:
        yield
        return

    merged_attrs = _merge_user_email_attribute(attributes)
    with tracer.start_as_current_span(name, attributes=merged_attrs):
        yield


@asynccontextmanager
async def trace_span_async(
    name: str, attributes: Optional[Dict[str, Any]] = None, tracer_name: str = "arize"
) -> AsyncGenerator[None, None]:
    """Create an async trace span using OpenTelemetry.

    Args:
        name: The name of the span
        attributes: Optional attributes to add to the span
        tracer_name: Ignored - kept for compatibility

    Yields:
        None
    """
    tracer = _get_tracer()
    if not tracer:
        yield
        return

    merged_attrs = _merge_user_email_attribute(attributes)
    with tracer.start_as_current_span(name, attributes=merged_attrs):
        yield


def trace_function(
    name: Optional[str] = None,
    attributes: Optional[Dict[str, Any]] = None,
    tracer_name: str = "arize",
) -> Callable[[F], F]:
    """Decorator to trace a function using OpenTelemetry.

    Args:
        name: Optional name for the span. If not provided, the function's qualified name will be used.
        attributes: Optional attributes to add to the span
        tracer_name: Ignored - kept for compatibility

    Returns:
        A decorator that traces the function
    """

    def decorator(func: F) -> F:
        tracer = _get_tracer()
        if not tracer:
            return func

        span_name = name or f"{func.__module__}.{func.__qualname__}"

        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            merged_attrs = _merge_user_email_attribute(attributes)
            with tracer.start_as_current_span(span_name, attributes=merged_attrs):
                return func(*args, **kwargs)

        return wrapper  # type: ignore

    return decorator


def trace_async_function(
    name: Optional[str] = None,
    attributes: Optional[Dict[str, Any]] = None,
    tracer_name: str = "arize",
) -> Callable[[F], F]:
    """Decorator to trace an async function using OpenTelemetry.

    Args:
        name: Optional name for the span. If not provided, the function's qualified name will be used.
        attributes: Optional attributes to add to the span
        tracer_name: Ignored - kept for compatibility

    Returns:
        A decorator that traces the async function
    """

    def decorator(func: F) -> F:
        tracer = _get_tracer()
        if not tracer:
            return func

        span_name = name or f"{func.__module__}.{func.__qualname__}"

        @functools.wraps(func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            merged_attrs = _merge_user_email_attribute(attributes)
            with tracer.start_as_current_span(span_name, attributes=merged_attrs):
                return await func(*args, **kwargs)

        return async_wrapper  # type: ignore

    return decorator
