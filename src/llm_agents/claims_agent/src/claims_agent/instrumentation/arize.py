"""Simple Arize Enterprise tracing integration for Claims Agent.

This module provides a minimal Arize setup that automatically instruments
LangChain operations without complex abstractions.
"""

from loguru import logger
from claims_agent.config import settings, ARIZE_PLACEHOLDER_KEY

# Arize imports with graceful fallback
try:
    from arize.otel import register
    from opentelemetry import trace
    from openinference.instrumentation.langchain import LangChainInstrumentor

    ARIZE_AVAILABLE = True
except ImportError:
    logger.warning("Arize not available. Tracing will be disabled.")
    ARIZE_AVAILABLE = False

# Re-exported symbols
__all__ = [
    "setup_arize_tracing",
    "is_arize_enabled",
]


def setup_arize_tracing() -> bool:
    """Set up Arize tracing with automatic LangChain instrumentation.

    Returns:
        bool: True if Arize was successfully initialized, False otherwise.
    """
    if not ARIZE_AVAILABLE:
        logger.info("Arize not available, skipping tracing setup")
        return False

    if not settings.ARIZE_ENABLED:
        logger.info("Arize tracing disabled in configuration")
        return False

    api_key = (
        settings.ARIZE_API_KEY.get_secret_value() if settings.ARIZE_API_KEY else ""
    )

    if (not settings.ARIZE_SPACE_ID) or (
        api_key == "" or api_key == ARIZE_PLACEHOLDER_KEY
    ):
        logger.warning(
            "Arize tracing enabled but ARIZE_SPACE_ID or ARIZE_API_KEY not configured"
        )
        return False

    try:
        # Setup OTEL with Arize Enterprise
        tracer_provider = register(
            space_id=settings.ARIZE_SPACE_ID,
            api_key=api_key,
            project_name=settings.ARIZE_PROJECT_NAME or "ClaimsIQ",
        )

        trace.set_tracer_provider(tracer_provider)

        # Add explicit LangChain instrumentation to capture input/output data
        LangChainInstrumentor().instrument(tracer_provider=tracer_provider)

        # Obtain a tracer instance (even if unused directly) so that
        # downstream LangChain spans are correctly parented.
        trace.get_tracer(__name__)

        logger.info(
            f"Arize tracing initialized for space: {settings.ARIZE_SPACE_ID}, "
            f"project: {settings.ARIZE_PROJECT_NAME or 'ClaimsIQ'}"
        )
        logger.info("LangChain operations instrumented for input/output capture")
        return True

    except Exception as e:
        logger.exception(f"Failed to initialize Arize tracing: {e}")
        return False


def is_arize_enabled() -> bool:
    """Check if Arize tracing is enabled and available.

    Returns:
        bool: True if Arize is enabled and available.
    """
    return ARIZE_AVAILABLE and settings.ARIZE_ENABLED
