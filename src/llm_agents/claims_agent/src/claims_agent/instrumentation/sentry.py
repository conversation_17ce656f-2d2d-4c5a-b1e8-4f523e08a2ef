"""Sentry instrumentation for the Claims Agent service."""

import sentry_sdk

from claims_agent.config import Settings


def register_sentry(settings: Settings) -> None:
    """Register Sentry for the Claims Agent service.

    For Starlette integration, see:
    https://docs.sentry.io/platforms/python/integrations/starlette/
    "If you have the starlette package in your dependencies, the Starlette
    integration will be enabled automatically when you initialize the Sentry SDK."
    """
    if not settings.SENTRY_DSN:
        return

    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        environment=settings.ENVIRONMENT,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=1.0,
        # Set profile_session_sample_rate to 1.0 to profile 100%
        # of profile sessions.
        profile_session_sample_rate=1.0,
        # Set profile_lifecycle to "trace" to automatically
        # run the profiler on when there is an active transaction
        profile_lifecycle="trace",
    )
