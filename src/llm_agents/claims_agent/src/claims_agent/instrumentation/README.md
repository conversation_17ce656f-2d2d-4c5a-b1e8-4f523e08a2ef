# Instrumentation

This package provides simple Arize Enterprise tracing integration for the Claims Agent service.

## Overview

The instrumentation uses <PERSON>ze's OpenTelemetry integration to automatically trace:

- **LangChain operations**: All LangChain chains, agents, LLMs, and tools
- **OpenAI API calls**: Direct OpenAI API interactions  
- **HTTP requests**: Outbound HTTP calls
- **Database operations**: SQL queries and database interactions

Additionally, the package provides helper functions for manual tracing of business-specific operations.

## Configuration

### Environment Variables for .env file

Add these settings to your `.env` file:

```env
# Arize Tracing Configuration
ARIZE_ENABLED=true
ARIZE_SPACE_ID=U3BhY2U6MjE1ODQ6dEtPVg==
ARIZE_API_KEY=ak-dae42a40-<your-complete-api-key>
ARIZE_PROJECT_NAME=ClaimsIQ

# Required LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
MODEL_NAME=gpt-4o-mini
MODEL_TEMPERATURE=0.7

# Application Settings
ENVIRONMENT=development
LOG_LEVEL=INFO
AGENT_VERBOSE=true

# MCP Server URLs
CLAIMS_SERVER_URL=http://0.0.0.0:8000/claims/sse
POLICY_SERVER_URL=http://0.0.0.0:8000/policy/sse
```

### Production Setup

For production deployment:
1. Set `ARIZE_SPACE_ID` to your Arize Enterprise space ID
2. Set `ARIZE_API_KEY` to your Arize Enterprise API key
3. Set `ARIZE_PROJECT_NAME` to organize your traces (defaults to "ClaimsIQ")

## Usage

### Automatic Instrumentation

Most tracing happens automatically once Arize is initialized:

```python
from claims_agent.instrumentation import setup_arize_tracing

# Initialize Arize (call once at startup)
setup_arize_tracing()

# All LangChain operations are now automatically traced
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

llm = ChatOpenAI(model="gpt-4o-mini")  # Automatically traced
prompt = ChatPromptTemplate.from_template("Hello {name}")  # Automatically traced
chain = prompt | llm  # Automatically traced
result = chain.invoke({"name": "world"})  # Automatically traced
```

### Manual Tracing

For business-specific operations, use the helper functions:

```python
from claims_agent.instrumentation.helpers import trace_async_function, trace_span_async

@trace_async_function(name="CoverageDetermination")
async def determine_coverage(claim_id: str) -> CoverageDeterminationResponse:
    async with trace_span_async("validate_claim_id", attributes={"claim_id": claim_id}):
        # Validation logic here
        pass
    
    async with trace_span_async("fetch_policy_data"):
        # Policy fetching logic here
        pass
    
    return result
```

## Span Names in Arize

With this setup, you'll see meaningful span names in Arize:

**Automatic spans** (from LangChain):
- `ChatOpenAI.chat` - LLM calls
- `RunnableSequence.invoke` - Chain executions  
- `AgentExecutor.invoke` - Agent executions
- `Tool.invoke` - Tool calls

**Manual spans** (from your decorators):
- `CoverageDetermination` - Your business operations
- `MCPAgent.process_prompt` - Agent methods
- `validate_claim_id` - Specific business logic

## Architecture

The instrumentation is designed for future extensibility:

```
claims_agent/instrumentation/
├── __init__.py          # Simple Arize setup
├── arize.py             # Arize integration
├── helpers.py           # Manual tracing helpers  
└── README.md           # This file
```

### Future Extensions

To add additional monitoring backends (e.g., Datadog, LangSmith):

1. **Create new integration module**:
   ```python
   # claims_agent/instrumentation/datadog.py
   def setup_datadog_tracing() -> bool:
       # Datadog setup logic
       pass
   ```

2. **Update initialization**:
   ```python
   # claims_agent/instrumentation/__init__.py
   from .arize import setup_arize_tracing
   from .datadog import setup_datadog_tracing
   
   def setup_all_tracing():
       setup_arize_tracing()
       setup_datadog_tracing()
   ```

3. **Add configuration**:
   ```python
   # claims_agent/config.py
   datadog_enabled: bool = Field(default=False)
   datadog_api_key: Optional[SecretStr] = None
   ```

This keeps the architecture simple while maintaining extensibility for future monitoring needs.