"""Module for binding service and activity-related dependencies.

This module provides the bindings for the main application services:
- `ClaimsService`: The service layer for the FastAPI application.
- `CoverageDeterminationActivities`: The collection of activities for the
  Temporal worker.
"""

from claims_agent.background.workflows.coverage_determination import (
    CoverageDeterminationActivities,
)
from claims_agent.interfaces.repositories import (
    CoverageDeterminationRequestRepositoryProtocol,
    CoverageRunRepositoryProtocol,
    CoverageNotesRepositoryProtocol,
)
from injector import Module, provider, singleton

from claims_agent.api.services import ClaimsService, ClaimsServiceProtocol
from claims_agent.interfaces.agents import AgentProtocol


class ServiceModule(Module):
    """Defines bindings for service and activity classes."""

    @provider
    @singleton
    def provide_claims_service(
        self,
        agent: AgentProtocol,
        coverage_run_repository: CoverageRunRepositoryProtocol,
        coverage_notes_repository: CoverageNotesRepositoryProtocol,
    ) -> ClaimsServiceProtocol:
        """Provides a singleton instance of the `ClaimsService`.

        This provider is primarily used by the FastAPI application. Note that
        while the service itself is a singleton, the `fastapi-injector` library
        is capable of injecting request-specific data (like an authorization
        header) into it on a per-request basis.

        Args:
            agent: The singleton `AgentProtocol` instance.
            coverage_run_repository: Repository for coverage runs database operations.
            coverage_notes_repository: Repository for coverage notes database operations.

        Returns:
            The application's singleton `ClaimsService` instance.
        """
        # Initialize with empty authorization, will be set per-request
        return ClaimsService(
            agent=agent,
            coverage_run_repository=coverage_run_repository,
            coverage_notes_repository=coverage_notes_repository,
            authorization=None,
        )

    @provider
    @singleton
    def provide_coverage_determination_activities(
        self,
        agent: AgentProtocol,
        request_repository: CoverageDeterminationRequestRepositoryProtocol,
    ) -> CoverageDeterminationActivities:
        """Provides a singleton instance of `CoverageDeterminationActivities`.

        This provider constructs the object that contains all the activity methods
        for the Temporal workflow. It is created once and shared, and its
        dependencies (`AgentProtocol` and the repository) are injected by the
        container. The worker then registers the methods from this singleton
        instance as individual activities.

        Args:
            agent: The singleton `AgentProtocol` instance.
            request_repository: The repository for database access.

        Returns:
            A configured `CoverageDeterminationActivities` instance.
        """
        return CoverageDeterminationActivities(
            agent=agent,
            request_repository=request_repository,
        )
