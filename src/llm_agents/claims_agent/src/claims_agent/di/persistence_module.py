"""Module for providing persistence-related dependencies.

This module sets up the database connection and provides the repository for
data access. It uses the `injector` library to define how the SQLAlchemy
`AsyncEngine`, `async_sessionmaker`, and the concrete repository implementation
are instantiated and wired together.
"""

from loguru import logger
from injector import Modu<PERSON>, provider, singleton
from urllib.parse import urlparse, urlunparse
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)

from claims_agent.config import Settings
from claims_agent.db.repositories.coverage_determinations import (
    SupabaseCoverageDeterminationRequestRepository,
)
from claims_agent.db.repositories.coverage_runs import CoverageRunRepository
from claims_agent.db.repositories.coverage_notes import CoverageNotesRepository
from claims_agent.interfaces.repositories import (
    CoverageDeterminationRequestRepositoryProtocol,
    CoverageRunRepositoryProtocol,
    CoverageNotesRepositoryProtocol,
)


class PersistenceModule(Module):
    """Defines bindings for database and repository components."""

    @provider
    @singleton
    def provide_async_engine(self, settings: Settings) -> AsyncEngine:
        """Provides a singleton SQLAlchemy `AsyncEngine`.

        This provider creates the database engine, which manages a pool of
        database connections. It reads the `SUPABASE_DB_URL` from settings and
        transforms it into a format compatible with the `psycopg` async driver.
        It is scoped as a singleton to ensure only one engine is created for
        the entire application lifecycle.

        Args:
            settings: The application `Settings` object.

        Returns:
            A configured SQLAlchemy `AsyncEngine` instance.

        Raises:
            ValueError: If the database URL is missing or has an unsupported scheme.
        """
        db_url = settings.SUPABASE_DB_URL.get_secret_value()
        if not db_url:
            raise ValueError("SUPABASE_DB_URL environment variable not set.")
        parsed = urlparse(db_url)
        if parsed.scheme not in ("postgresql+psycopg", "postgresql", "postgres"):
            raise ValueError(f"Unsupported database scheme: {parsed.scheme}")
        async_parsed = parsed._replace(scheme="postgresql+psycopg")
        async_db_url = urlunparse(async_parsed)
        logger.debug(f"Async database URL: {async_db_url}")
        return create_async_engine(async_db_url)

    @provider
    @singleton
    def provide_session_maker(
        self, engine: AsyncEngine
    ) -> async_sessionmaker[AsyncSession]:
        """Provides a singleton SQLAlchemy `async_sessionmaker`.

        The session maker is a factory for creating new `AsyncSession` objects,
        which are used to interact with the database. It is bound to the
        singleton engine.

        Args:
            engine: The singleton `AsyncEngine` instance.

        Returns:
            A configured `async_sessionmaker`.
        """
        return async_sessionmaker(engine, expire_on_commit=False)

    @provider
    def provide_coverage_determination_request_repository(
        self, session_maker: async_sessionmaker[AsyncSession]
    ) -> CoverageDeterminationRequestRepositoryProtocol:
        """Provides a `SupabaseCoverageDeterminationRequestRepository` instance.

        This provider constructs the repository used for all coverage determination
        database operations. It is bound to the `CoverageDeterminationRequestRepositoryProtocol`
        interface, allowing for easier testing and potential future implementation
        swaps. It is scoped as a transient (new instance per injection) by default.

        Args:
            session_maker: The singleton `async_sessionmaker`.

        Returns:
            An instance of `SupabaseCoverageDeterminationRequestRepository`.
        """
        return SupabaseCoverageDeterminationRequestRepository(
            session_maker=session_maker
        )

    @provider
    def provide_coverage_run_repository(
        self, session_maker: async_sessionmaker[AsyncSession]
    ) -> CoverageRunRepositoryProtocol:
        """Provides a `CoverageRunRepository` instance.

        This provider constructs the repository used for coverage runs
        database operations. It is bound to the `CoverageRunRepositoryProtocol`
        interface, allowing for easier testing and potential future implementation
        swaps. It is scoped as a transient (new instance per injection).

        Args:
            session_maker: The singleton `async_sessionmaker`.

        Returns:
            An instance of `CoverageRunRepository`.
        """
        return CoverageRunRepository(session_maker=session_maker)

    @provider
    def provide_coverage_notes_repository(
        self, session_maker: async_sessionmaker[AsyncSession]
    ) -> CoverageNotesRepositoryProtocol:
        """Provides a `CoverageNotesRepository` instance.

        This provider constructs the repository used for coverage notes
        database operations. It is bound to the `CoverageNotesRepositoryProtocol`
        interface, allowing for easier testing and potential future implementation
        swaps. It is scoped as a transient (new instance per injection).

        Args:
            session_maker: The singleton `async_sessionmaker`.

        Returns:
            An instance of `CoverageNotesRepository`.
        """
        return CoverageNotesRepository(session_maker=session_maker)
