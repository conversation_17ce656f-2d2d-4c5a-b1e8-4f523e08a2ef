"""Module for providing agent-related dependencies to the application.

This module uses the `injector` library to define how the core components of the
agent (like the `MCPClientManager`, `MCPToolService`, and the `MCPAgent` itself)
are instantiated and wired together. The `@singleton` scope ensures that only
one instance of each component is created and shared throughout the application.
"""

from injector import Module, provider, singleton

from claims_agent.interfaces.agents import AgentProtocol
from claims_agent.agents.agent_executor_factory import AgentExecutorFactory
from claims_agent.agents.mcp_agent import MCPAgent
from claims_agent.agents.mcp_client_manager import MCPClientManager
from claims_agent.agents.mcp_tool_service import MCPToolService
from claims_agent.config import Settings


class AgentModule(Module):
    """Defines bindings for the agent and its core dependencies."""

    @provider
    @singleton
    def provide_mcp_client_manager(self, settings: Settings) -> MCPClientManager:
        """Provides a singleton `MCPClientManager` instance.

        This manager is responsible for creating and managing the lifecycle of
        HTTP clients used to communicate with MCP servers. It is configured

        Args:
            settings: The application `Settings` object.
        """
        return MCPClientManager(settings=settings)

    @provider
    @singleton
    def provide_mcp_tool_service(self) -> MCPToolService:
        """Provides a singleton `MCPToolService` instance.

        This service is responsible for fetching and caching the tools from the
        MCP servers that the agent can use.
        """
        return MCPToolService()

    @provider
    @singleton
    def provide_agent_executor_factory(
        self, settings: Settings
    ) -> AgentExecutorFactory:
        """Provides a singleton `AgentExecutorFactory` instance.

        This factory is responsible for creating the LangChain `AgentExecutor`
        which bundles the LLM, tools, and prompt together. It is configured
        with the application settings.

        Args:
            settings: The application `Settings` object.
        """
        return AgentExecutorFactory(settings_override=settings)

    @provider
    @singleton
    def provide_agent(
        self,
        mcp_client_manager: MCPClientManager,
        mcp_tool_service: MCPToolService,
        agent_executor_factory: AgentExecutorFactory,
    ) -> AgentProtocol:
        """Provides a singleton instance of the `MCPAgent` bound to `AgentProtocol`.

        This provider constructs the main `MCPAgent` instance, injecting its
        required dependencies, which are themselves provided by the injector.
        This decouples the `MCPAgent` from the direct instantiation of its
        dependencies.

        Args:
            mcp_client_manager: The singleton `MCPClientManager`.
            mcp_tool_service: The singleton `MCPToolService`.
            agent_executor_factory: The singleton `AgentExecutorFactory`.

        Returns:
            The application's singleton `MCPAgent` instance.
        """
        # The agent is now created directly by the injector.
        # The async initialization (`_initialize_resources`) is now handled
        # at application startup, right after injector creation.
        return MCPAgent(
            mcp_client_manager=mcp_client_manager,
            mcp_tool_service=mcp_tool_service,
            agent_executor_factory=agent_executor_factory,
        )
