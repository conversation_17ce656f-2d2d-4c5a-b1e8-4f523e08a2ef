"""Module for providing Temporal-related dependencies.

This module is responsible for establishing the connection to the Temporal server
and providing a `TemporalClient` instance to the application. It uses a workaround
to handle the asynchronous nature of the Temporal client connection within the
synchronous context of the `injector` library.
"""

import asyncio
from concurrent.futures import ThreadPoolExecutor

from injector import Mo<PERSON><PERSON>, provider, singleton
from temporalio.client import Client as TemporalClient
from loguru import logger
from temporalio.contrib.pydantic import pydantic_data_converter

from claims_agent.config import Settings


class TemporalModule(Module):
    """Injector module for temporal components.

    Since the temporal client is async and the injector library does not
    provide a way to inject async dependencies, we need to provide the event
    loop and the temporal client manually.

    See: https://github.com/python-injector/injector/issues/152#issuecomment-679036168
    """

    @provider
    @singleton
    def provide_temporal_client(
        self,
        settings: Settings,
        pool: ThreadPoolExecutor,
    ) -> TemporalClient:
        """Provides a singleton `TemporalClient` instance.

        Because the `TemporalClient.connect` method is asynchronous, and the `injector`
        library is synchronous, this provider uses a `ThreadPoolExecutor` to run
        the async connection logic in a separate thread and synchronously retrieve
        the result. This is a workaround to bridge the async/sync gap.

        The client is configured as a singleton to ensure a single, persistent
        connection to the Temporal server for the application's lifecycle.

        Args:
            settings: The application `Settings` object.
            pool: A `ThreadPoolExecutor` to run the async connection logic.

        Returns:
            A connected and configured `TemporalClient` instance.

        Raises:
            Exception: If the connection to the Temporal server fails.
        """
        logger.info("Connecting to Temporal server...")
        try:
            coroutine = _connect_temporal_client(settings)
            client = pool.submit(asyncio.run, coroutine).result()
            logger.info("Successfully connected to Temporal.")
            return client
        except Exception as e:
            logger.critical(f"Failed to connect to Temporal: {e}")
            raise


async def _connect_temporal_client(settings: Settings) -> TemporalClient:
    """Coroutine that establishes the connection to the Temporal server.

    This helper function contains the actual async connection logic. It is
    called by the provider and run in a separate thread. It configures the

    client with the address, namespace, and credentials from settings, and
    importantly, includes the `pydantic_data_converter` to allow Temporal
    to serialize/deserialize Pydantic models for workflows and activities.

    Args:
        settings: The application `Settings` object.

    Returns:
        A connected `TemporalClient` instance.
    """
    logger.info(
        f"Connecting to Temporal server at {settings.TEMPORAL_ADDRESS} with namespace {settings.TEMPORAL_NAMESPACE}"
    )
    return await TemporalClient.connect(
        target_host=settings.TEMPORAL_ADDRESS,
        namespace=settings.TEMPORAL_NAMESPACE,
        api_key=settings.TEMPORAL_API_KEY.get_secret_value()
        if settings.TEMPORAL_API_KEY
        else None,
        tls=settings.TEMPORAL_USE_TLS,
        data_converter=pydantic_data_converter,
    )
