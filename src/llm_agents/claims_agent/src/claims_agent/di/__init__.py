"""Dependency injection setup for the claims_agent application.

This package contains the `injector` modules that define the bindings for all
the application's components. The `build_container` function in this file
serves as the main entry point for constructing the application's dependency
injection container.
"""

from injector import Injector

from claims_agent.di.agent_module import AgentModule
from claims_agent.di.config_module import ConfigModule
from claims_agent.di.persistence_module import PersistenceModule
from claims_agent.di.service_module import ServiceModule
from claims_agent.di.temporal_module import TemporalModule


def build_container() -> Injector:
    """Builds and returns the application's dependency injection container.

    This function aggregates all the individual binding modules (`ConfigModule`,
    `AgentModule`, etc.) and uses them to create a single, application-wide
    `Injector` instance. This container is then used throughout the application
    (e.g., in `main.py` and `worker.py`) to retrieve dependency-injected
    instances of services and components.

    The order of modules generally does not matter, as `injector` resolves the
    full dependency graph.

    Returns:
        The configured `Injector` instance for the application.
    """
    return Injector(
        [
            ConfigModule(),
            AgentModule(),
            PersistenceModule(),
            ServiceModule(),
            TemporalModule(),
        ]
    )
