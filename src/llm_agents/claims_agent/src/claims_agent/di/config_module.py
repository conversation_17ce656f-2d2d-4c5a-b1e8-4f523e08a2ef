"""Module for providing application configuration as a dependency.

This module defines the binding for the application's central `Settings` object,
making it available for injection into any other component that requires it.
"""

from injector import Mo<PERSON><PERSON>, provider, singleton

from claims_agent.config import Settings


class ConfigModule(Module):
    """Defines the binding for the application `Settings`."""

    @provider
    @singleton
    def provide_settings(self) -> Settings:
        """Provides a singleton instance of the `Settings` object.

        This provider instantiates the `Settings` class, which automatically
        loads configuration from environment variables and `.env` files. By
        scoping it as a singleton, we ensure that the settings are loaded only
        once and that the same configuration object is used throughout the
        entire application.

        Returns:
            The application's singleton `Settings` instance.
        """
        return Settings()
