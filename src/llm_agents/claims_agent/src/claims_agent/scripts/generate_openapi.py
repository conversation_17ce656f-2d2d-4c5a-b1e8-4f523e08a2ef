#!/usr/bin/env python3
"""Generate OpenAPI specification for the Claims Agent API.

This script generates the OpenAPI specification from the FastAPI application
and saves it to the docs directory for use by API client generators.
"""

from pathlib import Path
from typing import Any

from claims_agent.api import create_app
from claims_agent.di import build_container


def main() -> None:
    """Generate and save the OpenAPI specification."""
    # 1. Build the dependency injection container
    injector = build_container()

    # 2. Create the FastAPI app
    app = create_app(injector)

    # Get the OpenAPI schema
    openapi_schema = app.openapi()

    # Ensure the docs directory exists
    docs_dir = Path(__file__).parent.parent.parent.parent / "docs"
    docs_dir.mkdir(exist_ok=True)

    # Save as YAML (converting from JSON)
    import yaml

    class FoldedStringRepresenter:
        """Custom YAML representer for folded strings."""

        @staticmethod
        def folded_str_representer(dumper: Any, data: str) -> Any:
            """Represent multi-line strings using folded scalar format (>)."""
            if (
                "\n" in data and len(data) > 60
            ):  # Only use folded format for longer multi-line strings
                return dumper.represent_scalar("tag:yaml.org,2002:str", data, style=">")
            return dumper.represent_scalar("tag:yaml.org,2002:str", data)

    # Add custom representer for strings that contain newlines
    yaml.add_representer(str, FoldedStringRepresenter.folded_str_representer)

    openapi_yaml_path = docs_dir / "openapi.yaml"
    with open(openapi_yaml_path, "w") as f:
        yaml.dump(
            openapi_schema,
            f,
            default_flow_style=False,
            sort_keys=False,
            allow_unicode=True,
        )

    print(f"✅ OpenAPI specification generated at: {openapi_yaml_path}")


if __name__ == "__main__":
    main()
