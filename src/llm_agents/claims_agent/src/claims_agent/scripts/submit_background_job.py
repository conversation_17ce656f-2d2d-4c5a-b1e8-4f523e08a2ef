"""Command-line interface (CLI) for submitting background jobs to Temporal.

This script provides a way to manually trigger Temporal workflows from the
command line. It uses the `click` library to define commands and arguments.

The script sets up a dependency injection (DI) container to provide the
necessary services (like the Temporal Client and repositories) to the commands.
This ensures that the CLI commands are built with the same components as the
main application.

Example Usage (from within the container or a properly configured shell):
`uv run --package claims_agent python -m claims_agent.scripts.submit_background_job coverage-determination <claim-id> <auth-token>`
"""

import asyncio
import datetime
from typing import Any, Callable
import uuid
from claims_agent.background.workflows.coverage_determination import (
    CoverageDeterminationRequest,
    CoverageDeterminationWorkflow,
)
from claims_agent.config import Settings
from claims_agent.di.persistence_module import PersistenceModule
from claims_agent.di.config_module import ConfigModule
from claims_agent.di.temporal_module import TemporalModule
from claims_agent.interfaces.repositories import (
    CoverageDeterminationRequestRepositoryProtocol,
    CreateCoverageDeterminationRequest,
)
import click
from functools import wraps
from loguru import logger

import injector
from temporalio.client import Client as TemporalClient
from temporalio.client import WorkflowHandle
from claims_agent.background.workflows.simple_log import (
    SimpleLogWorkflow,
    SimpleLogRequest,
)

pass_injector = click.make_pass_decorator(injector.Injector)


def coro(f: Callable) -> Callable:
    """
    Decorator to run an async function synchronously.

    See: https://github.com/pallets/click/issues/85#issuecomment-43378930
    """

    @wraps(f)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@click.group()
@click.pass_context
def cli(ctx: click.Context) -> None:
    """
    A CLI for submitting background jobs to the claims-agent Temporal worker.

    This main command group initializes a dependency injection container and makes
    it available to subcommands via the Click context. This ensures that all
    commands use the same configuration and service instances.
    """
    ctx.obj = injector.Injector(
        [
            ConfigModule(),
            TemporalModule(),
            PersistenceModule(),
        ]
    )


@cli.command(name="log-message")
@click.argument("message", type=str)
@pass_injector
@coro
async def log_message(injector: injector.Injector, message: str) -> None:
    """
    Submits a simple 'log-message' workflow for testing purposes.

    This command is useful for verifying that the Temporal worker is running
    and can process basic jobs.
    """
    client = injector.get(TemporalClient)
    settings = injector.get(Settings)
    handle: WorkflowHandle = await client.start_workflow(
        SimpleLogWorkflow.run,
        SimpleLogRequest(message=message),
        id=f"log-message-{str(uuid.uuid4())}",
        task_queue=settings.TEMPORAL_WORKER_TASK_QUEUE or "default",
    )
    logger.info(f"Started workflow {handle.id}")
    result = await handle.result()
    logger.info(f"Result: {result}")


@cli.command(name="coverage-determination")
@click.argument("claim-id", type=str)
@click.argument("authorization", type=str)
@pass_injector
@coro
async def coverage_determination(
    injector: injector.Injector, claim_id: str, authorization: str
) -> None:
    """
    Submits a coverage determination workflow for a given CLAIM_ID.

    This command orchestrates the process of:
    1.  Creating a record in the database for the coverage determination request.
    2.  Starting the `CoverageDeterminationWorkflow` on Temporal.
    3.  Awaiting the result from the workflow and logging it.

    Arguments:
    CLAIM_ID: The identifier for the insurance claim to be analyzed.
    AUTHORIZATION: The authentication token (e.g., JWT) required for the agent
                   to access downstream services. Do not include the "Bearer"
                   prefix.
    """
    client = injector.get(TemporalClient)
    request_repo = injector.get(CoverageDeterminationRequestRepositoryProtocol)  # type: ignore [type-abstract]
    settings = injector.get(Settings)

    request_id = uuid.uuid4()
    workflow_id = f"coverage-determination-{str(uuid.uuid4())}"
    try:
        await request_repo.insert(
            CreateCoverageDeterminationRequest(
                request_id=request_id,
                claim_id=claim_id,
                workflow_id=workflow_id,
                timestamp=datetime.datetime.now(datetime.timezone.utc),
            )
        )
        logger.info(
            f"Inserted coverage determination request {request_id} into database"
        )
    except Exception as e:
        logger.error(
            f"Error inserting coverage determination request {request_id} into database: {e}"
        )
        raise e

    try:
        handle: WorkflowHandle = await client.start_workflow(
            CoverageDeterminationWorkflow.run,
            CoverageDeterminationRequest(
                claim_id=claim_id,
                request_id=request_id,
                authorization=authorization,
            ),
            id=workflow_id,
            task_queue=settings.TEMPORAL_WORKER_TASK_QUEUE or "default",
        )
        logger.info(f"Started workflow {handle.id}")
    except Exception as e:
        logger.error(f"Error starting workflow {workflow_id}: {e}")
        raise e

    try:
        result = await handle.result()
        logger.info(f"Result: {result}")
    except Exception as e:
        logger.error(f"Error getting workflow result {workflow_id}: {e}")
        raise e


if __name__ == "__main__":
    cli()
