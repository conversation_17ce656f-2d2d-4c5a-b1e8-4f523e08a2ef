# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository is a `uv` workspace that manages multiple Python projects (packages) related to LLM agents and associated services. The primary goal is to provide a structured environment for building, testing, and deploying these agents.

The core functionality revolves around AI agents that interact with backend services, primarily using the Model Context Protocol (MCP).

Key packages include:
- **claims_agent**: An LLM-powered agent service that processes insurance claims and policy information. Acts as a **consumer** of MCP tools.
- **mcp_servers**: Provides backend MCP server implementations for Nirvana functionality. Acts as a **provider** of data and functions in an MCP-compatible way.
- **nirvana_rest_api**: A client library for interacting with the Nirvana REST API, used by `mcp_servers` and other services.

## Development Commands

### Environment Setup
```bash
# Install dependencies for the workspace and all member packages
uv sync --all-packages --dev
```

### Common Development Tasks
```bash
# Format code
uv run poe format

# Lint code
uv run poe lint
# Auto-fix linting issues
uv run poe lint-fix

# Type check code
uv run poe typecheck

# Run tests
uv run poe test

# Run all checks (format, lint, type check)
uv run poe check

# Run all fixes (format, lint:fix)
uv run poe fix-all

# Clean up build artifacts and cache files
uv run poe clean
```

### Package-Specific Commands
Commands for specific packages can be run using:
```bash
# Example: Run commands from claims_agent package
uv run --package claims_agent poe -C claims_agent start

# Example: Run commands from mcp_servers package
uv run --package mcp_servers poe -C mcp_servers start

# Start all development services using Overmind
uv run poe start-dev
```

### Docker Commands
```bash
# Build a Docker image for a package
uv run poe build-image --package <package_name> --tag <tag>
# Example: uv run poe build-image --package claims_agent --tag dev

# Start a specific service in docker-compose
uv run poe compose-start-service --service <service_name>
# Example: uv run poe compose-start-service --service claims_agent

# Start all services with docker-compose
uv run poe compose-up

# View logs from docker-compose services
uv run poe compose-logs

# Stop all docker-compose services
uv run poe compose-down

# Build and push a Docker image to ECR
uv run poe build-and-push --package <package_name> --tag <tag> --ecr_path <ecr_path> [--aws_account_id <account_id>] [--aws_region <region>]
# Example: uv run poe build-and-push --package claims_agent --tag v1.2.3 --ecr_path nirvana/claims-agent
```

## Architecture & Code Structure

### Claims Agent Architecture
- **Layered Design**: API (FastAPI) -> Service -> Agent
- **Agent Layer Components**:
  - `AgentProtocol`: Defines agent contract
  - `AgentManager`: Singleton that manages agent lifecycle
  - `MCPAgent`: Main agent logic orchestrator
  - `MCPClientManager`: Manages MCP client lifecycle
  - `MCPToolService`: Fetches tools from active MCP client
  - `AgentExecutorFactory`: Creates LangChain components
  - `prompts.py`: LLM prompt templates
  - `response_parser.py`: Parses LLM responses
  - `agent_logging_utils.py`: Logging utilities

### MCP Servers Architecture
- Provides backend MCP server implementations for Nirvana functionality
- Exposes various internal Nirvana functionalities (e.g., Policy and Claims information retrieval) as MCP tools
- Components:
  - `server.py`: Base server implementation
  - `mcp_server_claims`: Specific server for claims-related tools
  - `mcp_server_policy`: Specific server for policy-related tools
  - `parse`: Utilities for parsing documents
  - `lib`: Shared libraries and utilities

### Key Design Patterns
- **Factory**: `AgentExecutorFactory`
- **Service**: `MCPToolService`
- **Context Manager**: `MCPClientManager.get_client`
- **Dependency Injection**: Using `injector` library
- **Protocol**: `AgentProtocol`
- **Singleton**: `AgentManager`

## Testing Guidelines

- All tests use `pytest`
- Mocking is done using `pytest-mock` (`mocker`)
- When mocking, patch where the object is looked up (imported) in the module under test
- For async functions, use `mocker.AsyncMock`
- For context managers, mock `__aenter__` and `__aexit__`
- Test files are organized in the `tests/` directory, mirroring the package structure

## Coding Standards

- **Python Version**: 3.11+
- **Pydantic V2**: Used for data modeling and validation
- **Type Hinting**: Comprehensive type hints required (passing `mypy`)
- **Async/Await**: Proper usage of async patterns; no blocking I/O in async
- **Logging**: Use `loguru` for logging
- **Error Handling**: Define custom exceptions for specific error conditions
- **Code Style**: Follows Black and Ruff formatting
- **File Organization**: Keep files focused and reasonably sized (< 400 lines when feasible)
- **Imports**: Use absolute imports within packages
- **Configuration**: Access only via settings objects, no direct env var reads outside config files

## Project Structure

The repository follows the src-layout pattern:
- `claims_agent/`: Claims Agent package
  - `src/claims_agent/`: Source code
  - `tests/`: Tests
  - `pyproject.toml`: Package configuration
- `mcp_servers/`: MCP Servers package
  - `src/mcp_servers/`: Source code
  - `tests/`: Tests
  - `pyproject.toml`: Package configuration
- `nirvana_rest_api/`: Nirvana REST API client package
  - `src/nirvana_rest_api/`: Source code
  - `pyproject.toml`: Package configuration
- `docs/`: Documentation
  - `memory_bank/`: Knowledge base for the project
- `scripts/`: Helper scripts
- `pyproject.toml`: Root project configuration
- `uv.lock`: Shared lockfile
- `Procfile`: Process definitions for Overmind
- `docker-compose.yml`: Docker Compose configuration

## Memory Bank

The Memory Bank in `docs/memory_bank/` serves as a knowledge base for the project. Key files include:
- `00_foundational/projectbrief.md`: Core project requirements and goals
- `01_architecture_and_design/systemPatterns.md`: System architecture and design patterns
- `01_architecture_and_design/claims_agent_architecture.md`: Detailed Claims Agent architecture
- `01_architecture_and_design/techContext.md`: Technical context and constraints
- `02_development_practices/project_standards_and_guidelines.md`: Coding standards
- `03_operational_context/activeContext.md`: Current work focus and system state

When working on complex tasks, consult the Memory Bank for context and understanding.