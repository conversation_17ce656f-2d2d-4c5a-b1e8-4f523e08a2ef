"""get_clerk_token.py.

Automate Google sign-in in a head-ful browser session and retrieve a long-lived
Clerk session token using the specified JWT template.

The script opens the provided URL (defaults to the local support frontend),
prompts the developer to complete login, then executes
``window.Clerk.session.getToken({ template: "<template>" })`` in the page
context.  The resulting token is printed to stdout and, when possible, copied
to the clipboard for convenience.

Usage (default URL & template):
    python -m scripts.get_clerk_token

Specify a custom URL and/or template:
    python -m scripts.get_clerk_token --url https://support.nirvanatech.com --template claims_mcp_template

Note:
    • Requires the *playwright* package and an installed Chromium browser
      instance (``playwright install``).
    • Optionally copies the token to the clipboard if *pyperclip* is available.
"""

from __future__ import annotations

import argparse
import sys
import time
from pathlib import Path


def _ensure_playwright_installed() -> None:
    """Ensure that Playwright browsers are installed.

    If the developer has not yet run ``playwright install``, import will fail
    or launching the browser will raise an error.  We attempt a best-effort
    invocation of the install command the first time.
    """
    # First confirm the Playwright Python package is available.
    try:
        import playwright.sync_api  # noqa: F401  # just to verify importable
    except ImportError as exc:  # pragma: no cover – missing dependency
        print(
            "[ERROR] Playwright Python package is not installed.\n"
            "Run:  uv pip install --quiet playwright && playwright install"
        )
        raise SystemExit(1) from exc

    # Standard library import (always present) – after the guard so linters don't complain.
    import subprocess  # noqa: S404  # subprocess invocation is required here

    browsers_dir = Path.home() / ".cache" / "ms-playwright"
    if not browsers_dir.exists():
        print("[INFO] First-time Playwright setup – downloading browser binaries…")
        # Newer Playwright versions require CLI invocation; fallback gracefully.
        try:
            subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])  # noqa: S603
        except subprocess.CalledProcessError as exc:  # pragma: no cover – propagate
            print("[ERROR] Playwright install failed", exc)
            raise SystemExit(1) from exc


def fetch_token(url: str, template: str) -> str:
    """Launch a browser, navigate, and fetch the Clerk token."""
    from playwright.sync_api import sync_playwright  # local import – heavy dep

    with sync_playwright() as pw:
        browser = pw.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()

        print(f"[INFO] Opening {url}…")
        page.goto(url, wait_until="domcontentloaded")

        print(
            "[ACTION] Complete Google/Clerk sign-in *in the opened window*.\n"
            "          Then return to this terminal and press ENTER to continue.\n"
        )
        input()

        print("[INFO] Attempting to retrieve token from page context…")

        token: str | None = None
        for _ in range(30):  # ~60 s max (30 × 2s)
            token = page.evaluate(
                """
                async ({ tmpl }) => {
                  if (!window.Clerk || !window.Clerk.session) {
                    return null;
                  }
                  try {
                    return await window.Clerk.session.getToken({ template: tmpl });
                  } catch (err) {
                    if (err?.message?.includes('No session')) {
                      return null; // not signed in yet
                    }
                    console.error('Token fetch attempt failed', err);
                    return null;
                  }
                }
                """,
                {"tmpl": template},
            )

            if token:
                break

            time.sleep(2)

        browser.close()

        if not token:
            print(
                "[ERROR] Failed to retrieve token after waiting 60s.\n"
                "       • Verify you completed the login flow in the browser.\n"
                f"       • Confirm the JWT template name ('{template}') exists in Clerk."
            )
            raise SystemExit(2)

        return token


def main() -> None:
    """CLI entry-point for the token helper."""
    parser = argparse.ArgumentParser(description="Generate a long-lived Clerk token via browser sign-in.")
    parser.add_argument(
        "--url",
        default="https://support.nirvanatech.com",
        help="Frontend URL where Clerk is initialised (default: %(default)s)",
    )
    parser.add_argument(
        "--template",
        default="claims_mcp_template",
        help="JWT template name configured in Clerk dashboard (default: %(default)s)",
    )
    args = parser.parse_args()

    _ensure_playwright_installed()

    try:
        token = fetch_token(args.url, args.template)
    except KeyboardInterrupt:  # pragma: no cover – user abort
        print("\n[ABORTED] Exiting on user request.")
        return

    print("\n[SUCCESS] Long-lived Clerk bearer token:")
    print(token)

    try:
        import pyperclip

        pyperclip.copy(token)
        print("[INFO] Token copied to clipboard.")
    except Exception as exc:  # pragma: no cover – optional dependency
        print(f"[WARN] Could not copy token to clipboard: {exc}")


if __name__ == "__main__":  # pragma: no cover – script execution
    main()
