"""Synchronizes Cursor rule files (.mdc) to Cline rule files (.md)."""

import re
from pathlib import Path


# Define directories relative to the project root (where the script is likely run from)
PROJECT_ROOT = Path(__file__).parent.parent
CURSOR_RULES_DIR = PROJECT_ROOT / ".cursor" / "rules"
CLINE_RULES_DIR = PROJECT_ROOT / ".clinerules"

# Regex to find the YAML front matter (metadata block)
# It matches lines starting with ---, captures everything in between (non-greedily),
# and ends with another line starting with ---. Handles potential whitespace.
METADATA_RE = re.compile(r"^\s*---\s*$.*?^\s*---\s*$\n?", re.MULTILINE | re.DOTALL)


def sync_rules() -> None:
    """Finds .mdc files in CURSOR_RULES_DIR, strips metadata, writes .md to CLINE_RULES_DIR."""
    print(f"Starting sync from {CURSOR_RULES_DIR} to {CLINE_RULES_DIR}...")

    if not CURSOR_RULES_DIR.is_dir():
        print(f"Source directory not found: {CURSOR_RULES_DIR}")
        return

    CLINE_RULES_DIR.mkdir(parents=True, exist_ok=True)
    print(f"Ensured target directory exists: {CLINE_RULES_DIR}")

    mdc_files_found = False
    for mdc_path in CURSOR_RULES_DIR.glob("*.mdc"):
        mdc_files_found = True
        target_md_filename = mdc_path.stem + ".md"
        target_md_path = CLINE_RULES_DIR / target_md_filename

        print(f"Processing {mdc_path.name} -> {target_md_path.name}...")

        try:
            content = mdc_path.read_text(encoding="utf-8")
            content_without_metadata = METADATA_RE.sub("", content, count=1).strip()
            target_md_path.write_text(content_without_metadata, encoding="utf-8")
            print(f"  Successfully wrote {target_md_path.name}")
        except Exception as e:
            print(f"  ERROR processing {mdc_path.name}: {e}")

    if not mdc_files_found:
        print(f"No .mdc files found in {CURSOR_RULES_DIR}")

    print("Sync completed.")


if __name__ == "__main__":
    sync_rules()
