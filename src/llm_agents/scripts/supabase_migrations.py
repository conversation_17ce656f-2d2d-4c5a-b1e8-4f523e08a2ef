"""Database Migration Execution Script for LLM Agents Supabase.

This script provides a secure, infrastructure-integrated way to execute database migrations
against the Supabase PostgreSQL database used by the LLM Agents project. It creates a
seamless bridge between infrastructure provisioning and application deployment.

## Architecture Integration

This script is designed to work with the infrastructure provisioned by
`src/infra/platform/src/llm-agents/supabase.ts`, creating a complete end-to-end
migration execution pattern:

**Infrastructure Side (supabase.ts):**
- Creates a 'migrator' database role with schema modification permissions
- Generates secure random passwords and stores them in AWS Secrets Manager
- Provisions Supabase project with proper configuration

**Migration Execution Side (this script):**
- Fetches migrator password from AWS Secrets Manager (same locations)
- Connects as 'migrator' user to run dbmate commands
- Executes migrations from `src/llm_agents/db/migrations/`

**Application Side (claims_agent):**
- Uses the database schema created by these migrations
- Connects with application-specific credentials for runtime operations

## Security Model

### AWS Secrets Manager Integration
- **No Hardcoded Passwords**: All database credentials are fetched from AWS Secrets Manager
- **Infrastructure Alignment**: Uses the same secret names created by infrastructure code
- **IAM-Based Access**: Requires proper AWS credentials/roles for secret access
- **Secure Transport**: All database connections use SSL/TLS encryption

### Database Role Permissions
The 'migrator' role created by infrastructure has limited permissions:
- READ_ALL_DATA: For validation and schema introspection
- CREATE_DATABASE/CREATE_ROLE: For advanced migration scenarios
- CREATE permissions on 'postgres' database and 'public' schema
- No WRITE_ALL_DATA: Cannot modify application data, only schema

## Docker-Based Execution

This script uses Docker to run `dbmate` commands, providing:
- **Consistent Environment**: Same dbmate version across all environments
- **Isolation**: Migration execution doesn't depend on local tooling
- **Portability**: Works on any system with Docker installed
- **Official Images**: Uses `ghcr.io/amacneil/dbmate` for reliability

## LLM Agents Project Context

The LLM Agents project uses this database for:
- **Claims Processing**: Coverage determination results and workflow state
- **MCP Tool Caching**: Results from Model Context Protocol servers
- **Temporal Workflows**: Async job processing state management
- **API Persistence**: Data storage for FastAPI endpoints

## Migration File Management

Migrations are stored in `src/llm_agents/db/migrations/` with:
- **Timestamp-based naming**: `YYYYMMDDHHMMSS_description.sql`
- **Up/Down patterns**: `-- migrate:up` and `-- migrate:down` sections
- **Schema versioning**: Tracked in `schema_migrations` table

## Usage Examples

### Production Deployment
```bash
# Apply all pending migrations to production
uv run supabase-migrations --host db.PROJECT_ID.supabase.co --database postgres up

# Check migration status
uv run supabase-migrations --host db.PROJECT_ID.supabase.co --database postgres status

# Emergency rollback (use with caution)
uv run supabase-migrations --host db.PROJECT_ID.supabase.co --database postgres down
```

### Development/Testing
```bash
# Test migrations against staging environment
uv run supabase-migrations --host db.staging.supabase.co --database postgres up

# Check status with custom secret location
uv run supabase-migrations \
    --host db.example.com \
    --database test \
    --password-secret "custom/secret/path" \
    status
```

### CI/CD Integration
```bash
# Automated deployment pipeline
export AWS_REGION=us-east-2
export DB_HOST=db.PROJECT_ID.supabase.co
uv run supabase-migrations up
```

## Development Workflow Integration

This script integrates with the complete development workflow:

1. **Local Development**: Uses Docker Compose with local PostgreSQL
2. **Schema Changes**: Developers create migrations via `docker compose run --rm migrator new`
3. **Testing**: Apply migrations locally with `docker compose up migrator`
4. **ORM Generation**: Run `uv run poe generate-orm` to update SQLAlchemy models
5. **Production Deploy**: Use this script to apply migrations to cloud database

## Error Handling and Troubleshooting

### Common Issues
- **Secret Not Found**: Check AWS credentials and secret names
- **Connection Refused**: Verify Supabase project is running and accessible
- **Permission Denied**: Ensure migrator role has correct permissions
- **Docker Issues**: Verify Docker is installed and running

### Secret Management
The script handles both string and JSON secrets from AWS Secrets Manager:
- **String secrets**: Used directly as password
- **JSON secrets**: Extracts 'password' key from JSON object
- **Error handling**: Provides clear error messages for access issues

## Dependencies

### Required Tools
- **Docker**: For running dbmate commands
- **AWS CLI/SDK**: For accessing AWS Secrets Manager
- **Python packages**: `boto3`, `click` for AWS and CLI functionality

### AWS Permissions
The script requires the following IAM permissions:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
            ],
            "Resource": "arn:aws:secretsmanager:*:*:secret:init-ai/llm-agents/*"
        }
    ]
}
```

## Related Files

- `src/infra/platform/src/llm-agents/supabase.ts`: Infrastructure provisioning
- `src/llm_agents/db/migrations/`: SQL migration files
- `src/llm_agents/docker-compose.yml`: Local development migration setup
- `src/llm_agents/claims_agent/src/claims_agent/db/orm/generated.py`: SQLAlchemy models
- `src/llm_agents/scripts/run_migrations.sh`: Alternative migration runner

## Implementation Details

### Password Security
- Passwords are URL-encoded to handle special characters safely
- No passwords are logged or stored in process memory longer than necessary
- Database connections use SSL/TLS encryption (`sslmode=require`)

### Docker Integration
- Uses `--network=host` for direct database access
- Mounts local `db` directory for migration file access
- Runs with `--rm` for automatic cleanup
- Interactive mode (`-it`) for proper terminal handling

## Future Enhancements

Potential improvements for this script:
- Support for database connection pooling
- Integration with monitoring/alerting systems
- Automated rollback on migration failures
- Support for parallel migration execution
- Integration with GitOps workflows

Note:
    • Requires AWS credentials configured (via AWS CLI, IAM roles, or environment variables)
    • Requires the *boto3* package for AWS Secrets Manager access
    • Requires *dbmate* to be available (via docker)
"""

from __future__ import annotations

import os
import shutil
import subprocess
import urllib.parse
from dataclasses import dataclass

import boto3
import click
from botocore.exceptions import ClientError


def _get_docker_path() -> str:
    """Get the path to the Docker executable.

    Uses shutil.which() to dynamically locate Docker in the system PATH,
    making the script work across different systems where Docker might be
    installed in various locations (e.g., /usr/bin/docker, /usr/local/bin/docker).

    Returns:
        Path to Docker executable

    Raises:
        ClickException: If Docker is not found in PATH
    """
    docker_path = shutil.which("docker")
    if not docker_path:
        raise click.ClickException("docker is not available. Install via: https://docs.docker.com/get-docker/")
    return docker_path


@dataclass
class MigrationConfig:
    """Configuration for database migrations.

    This class encapsulates all the settings needed to connect to the database
    and execute migration commands. It handles the integration with AWS Secrets
    Manager for secure credential retrieval.

    Attributes:
        host: Database hostname (e.g., db.PROJECT_ID.supabase.co)
        database: Database name (typically 'postgres' for Supabase)
        password_secret: AWS Secrets Manager secret name for database password
        region: AWS region where the secret is stored
        password: Cached password value (populated on first use)
    """

    host: str
    database: str
    password_secret: str
    region: str
    password: str | None = None

    def get_database_url(self) -> str:
        """Build PostgreSQL connection URL with URL-encoded password.

        Creates a properly formatted PostgreSQL connection string using the
        'migrator' user role created by the infrastructure. The password is
        fetched from AWS Secrets Manager on first use and cached for subsequent
        calls.

        Returns:
            Complete PostgreSQL connection URL with encoded credentials

        Raises:
            ClickException: If password retrieval from AWS Secrets Manager fails
        """
        if not self.password:
            self.password = fetch_password_from_secrets_manager(self.password_secret, self.region)

        # Use 'migrator' user - this matches the role created by infrastructure
        user = "migrator"
        # URL-encode password to handle special characters safely
        encoded_password = urllib.parse.quote(self.password)
        return f"postgresql://{user}:{encoded_password}@{self.host}:5432/{self.database}?sslmode=require"


def _ensure_dependencies() -> None:
    """Ensure required dependencies are available.

    Verifies that Docker is installed and accessible, as it's required for
    running the dbmate migration commands. This provides early failure with
    a clear error message if Docker is not available.

    Raises:
        ClickException: If Docker is not available or accessible
    """
    try:
        docker_path = _get_docker_path()
        subprocess.run([docker_path, "ps"], capture_output=True, check=True)  # noqa: S603
    except subprocess.CalledProcessError as exc:
        raise click.ClickException("docker is not available. Install via: https://docs.docker.com/get-docker/") from exc


def fetch_password_from_secrets_manager(secret_name: str, region: str = "us-east-2") -> str:
    """Fetch database password from AWS Secrets Manager.

    This function retrieves the database password from AWS Secrets Manager.
    The infrastructure stores passwords as simple string secrets, not JSON.

    Args:
        secret_name: Name of the secret in AWS Secrets Manager
        region: AWS region where the secret is stored (default: us-east-2)

    Returns:
        Database password as a string

    Raises:
        ClickException: If secret is not found, access is denied, or other AWS errors occur
    """
    client = boto3.client("secretsmanager", region_name=region)
    try:
        click.echo(f"[INFO] Fetching password from AWS Secrets Manager: {secret_name}")
        response = client.get_secret_value(SecretId=secret_name)

        # Infrastructure always stores passwords as string secrets
        return response["SecretString"]

    except ClientError as exc:
        error_code = exc.response["Error"]["Code"]
        if error_code == "ResourceNotFoundException":
            raise click.ClickException(f"Secret '{secret_name}' not found in AWS Secrets Manager") from exc
        if error_code == "AccessDeniedException":
            raise click.ClickException(f"Access denied to secret '{secret_name}'. Check IAM permissions.") from exc
        raise click.ClickException(f"AWS Secrets Manager error: {exc}") from exc


def run_migration_command(database_url: str, command: str) -> None:
    """Execute dbmate command with the constructed database URL.

    This function runs the actual migration command using Docker to execute
    dbmate. It provides a consistent environment for migration execution
    regardless of the local system configuration.

    Docker Configuration:
    - Uses official `ghcr.io/amacneil/dbmate` image
    - Mounts local `db` directory for migration file access
    - Uses `--network=host` for direct database connectivity
    - Runs with `--rm` for automatic cleanup

    Args:
        database_url: Complete PostgreSQL connection URL
        command: dbmate command to execute (up, down, status, etc.)

    Raises:
        ClickException: If the dbmate command fails or Docker is not available
    """
    _ensure_dependencies()

    click.echo(f"[INFO] Running dbmate command: {command}")

    try:
        docker_path = _get_docker_path()
        subprocess.run(  # noqa: S603
            [
                docker_path,
                "run",
                "--rm",
                "-it",
                "--network=host",
                "--volume",
                f"{os.path.dirname(os.path.abspath(__file__))}/../db:/db",
                "ghcr.io/amacneil/dbmate",
                "--url",
                database_url,
                "--migrations-dir",
                "/db/migrations",
                "--no-dump-schema",
                command,
            ],
            check=True,
            capture_output=False,  # Let output go to stdout/stderr
        )
        click.echo(f"[SUCCESS] Migration command '{command}' completed successfully")

    except subprocess.CalledProcessError as exc:
        raise click.ClickException(f"dbmate command failed with exit code {exc.returncode}") from exc


@click.group(invoke_without_command=True)
@click.option(
    "--host", required=True, envvar="DB_HOST", help="Database host (e.g., db.PROJECT_ID.supabase.co, localhost)"
)
@click.option("--database", default="postgres", envvar="DB_NAME", help="Database name (e.g., production, dev)")
@click.option(
    "--password-secret",
    default="init-ai/llm-agents/supabase-db-migrator-password",
    envvar="DB_PASSWORD_SECRET",
    help="AWS Secrets Manager secret name containing the database password",
)
@click.option("--region", default="us-east-2", envvar="AWS_REGION", help="AWS region for Secrets Manager")
@click.pass_context
def cli(ctx: click.Context, host: str, database: str, password_secret: str, region: str) -> None:
    """Database migration tools for Supabase using dbmate.

    This CLI provides secure migration execution against Supabase PostgreSQL databases.
    It integrates with the infrastructure provisioning system by using the same
    database roles and AWS Secrets Manager locations.

    The default password secret name matches what is created by the infrastructure
    stack, creating a seamless bridge between provisioning and migration execution.

    Examples:
        # Apply migrations to production
        uv run supabase-migrations --host db.PROJECT_ID.supabase.co up

        # Check migration status
        uv run supabase-migrations --host db.PROJECT_ID.supabase.co status

        # Use custom secret location
        uv run supabase-migrations --host db.example.com --password-secret custom/secret down
    """
    ctx.obj = MigrationConfig(host=host, database=database, password_secret=password_secret, region=region)

    # If no command was invoked, show help
    if ctx.invoked_subcommand is None:
        # Show usage help with current configuration.
        click.echo("[INFO] Migration Configuration:")
        click.echo(f"  Host: {host or 'NOT SET'}")
        click.echo(f"  Database: {database or 'postgres'}")
        click.echo(f"  Secret: {password_secret or 'llm-agents/supabase-password'}")
        click.echo(f"  Region: {region or 'us-east-2'}")
        click.echo()
        click.echo("Available commands:")
        click.echo("  status    - Check current migration status")
        click.echo("  up        - Apply pending migrations")
        click.echo("  down      - Rollback the last migration")
        click.echo()
        click.echo("Use --help with any command for more details.")
        click.echo("Example: uv run supabase-migrations --host db.PROJECT_ID.supabase.co --database postgres status")


# Create a custom pass decorator for our config
pass_config = click.make_pass_decorator(MigrationConfig)


@cli.command()
@pass_config
def status(config: MigrationConfig) -> None:
    """Check current migration status.

    Displays which migrations have been applied and which are pending.
    This is useful for verifying the database state before applying new migrations.
    """
    database_url = config.get_database_url()
    run_migration_command(database_url, "status")


@cli.command()
@pass_config
def up(config: MigrationConfig) -> None:
    """Apply pending migrations.

    Executes all pending migrations in chronological order. This is the
    primary command used for deploying schema changes to production.

    Safety: Always check migration status first and have a rollback plan ready.
    """
    database_url = config.get_database_url()
    run_migration_command(database_url, "up")


@cli.command()
@pass_config
def down(config: MigrationConfig) -> None:
    """Rollback the last migration.

    Reverts the most recently applied migration. Use with caution in production
    environments as this can result in data loss if the migration involved
    dropping columns or tables.

    Warning: Always backup your database before rolling back migrations.
    """
    database_url = config.get_database_url()
    run_migration_command(database_url, "down")


if __name__ == "__main__":
    cli()
