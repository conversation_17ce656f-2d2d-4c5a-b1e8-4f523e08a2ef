#!/bin/bash
# Setup script for llm_agents development environment

set -e # Exit immediately if a command exits with a non-zero status.

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# --- Check for Homebrew (for macOS) ---
MOS_TYPE=$(uname -s)
if [[ "$MOS_TYPE" == "Darwin" ]] && ! command_exists brew; then
    echo "Homebrew not found, but it is required on macOS for this script."
    echo "Please install Homebrew first: https://brew.sh/"
    exit 1
fi

# --- Install uv (Python package manager) ---
if ! command_exists uv; then
    echo "uv (Python package manager) not found."
    read -p "Install uv now? (y/N) " user_response
    if [[ "$user_response" =~ ^[Yy]$ ]]; then
        echo "Installing uv..."
        if [[ "$MOS_TYPE" == "Darwin" ]]; then
            echo "Attempting to install uv via Homebrew..."
            brew install astral-sh/uv/uv
        else # Linux or other OS, use curl script
            echo "Attempting to install uv via curl script..."
            curl -LsSf https://astral.sh/uv/install.sh | sh
            # Add uv to PATH if not already handled by the script
            if [[ -f "$HOME/.cargo/env" ]]; then
                 source "$HOME/.cargo/env"
            elif [[ -f "$HOME/.local/bin/env" ]]; then
                 source "$HOME/.local/bin/env"
            fi
        fi
        echo "uv installation attempted. You might need to restart your shell or source your profile for PATH changes to take effect."
        if ! command_exists uv; then # Check again
            echo "ERROR: uv installation seemed to succeed but 'uv' command is still not found. Please check your PATH."
            echo "If using curl, the uv installer usually provides instructions to update your PATH."
            exit 1
        else
            echo "uv installed successfully."
        fi
    else
        echo "Skipping uv installation. Some setup steps might fail."
    fi
else
    echo "uv is already installed."
fi

# --- Install Overmind (Process manager) ---
if ! command_exists overmind; then
    echo "Overmind (process manager) not found."
    read -p "Install Overmind now? (y/N) " user_response
    if [[ "$user_response" =~ ^[Yy]$ ]]; then
        echo "Installing Overmind..."
        if [[ "$MOS_TYPE" == "Darwin" ]]; then
            echo "Attempting to install Overmind via Homebrew..."
            brew install overmind
        elif [[ "$MOS_TYPE" == "Linux" ]]; then
            echo "Attempting to install Overmind for Linux (amd64)..."
            OVERMIND_VERSION="v2.3.0" # You might want to check for the latest version
            OVERMIND_URL="https://github.com/DarthSim/overmind/releases/download/${OVERMIND_VERSION}/overmind-${OVERMIND_VERSION}-linux-amd64.gz"
            TEMP_OVERMIND_DIR=$(mktemp -d)
            echo "Downloading Overmind from $OVERMIND_URL to $TEMP_OVERMIND_DIR/overmind.gz"
            curl -L "$OVERMIND_URL" -o "$TEMP_OVERMIND_DIR/overmind.gz"
            gzip -d "$TEMP_OVERMIND_DIR/overmind.gz"
            chmod +x "$TEMP_OVERMIND_DIR/overmind"
            echo "Attempting to move Overmind to /usr/local/bin/overmind..."
            if sudo mv "$TEMP_OVERMIND_DIR/overmind" /usr/local/bin/overmind; then
                echo "Overmind installed to /usr/local/bin/overmind"
            else
                echo "Failed to move Overmind to /usr/local/bin. Try with sudo or install manually."
                echo "Alternatively, place the 'overmind' executable from $TEMP_OVERMIND_DIR in a directory in your PATH."
            fi
            rm -rf "$TEMP_OVERMIND_DIR"
        else
            echo "Unsupported OS for automatic Overmind installation. Please install it manually: https://github.com/DarthSim/overmind"
        fi
        
        if ! command_exists overmind; then # Check again after attempted install
            echo "INFO: Overmind installation was attempted. If it's not found, please ensure /usr/local/bin (or chosen install dir) is in your PATH or install it manually."
        else
            echo "Overmind installed successfully or was already available."
        fi
    else
        echo "Skipping Overmind installation."
    fi
else
    echo "Overmind is already installed."
fi

# --- Check for Docker and Docker Compose ---
echo "Checking for Docker and Docker Compose..."
if ! command_exists docker || ! docker compose version >/dev/null 2>&1; then # docker compose version checks for v2 plugin
    echo "WARNING: Docker or Docker Compose v2 not found or not working correctly."
    echo "Please install/configure Docker Desktop (or Docker Engine + Docker Compose plugin manually)."
    echo "Visit https://www.docker.com/products/docker-desktop/"
    echo "The Docker-based workflows (e.g., 'uv run poe compose-up') will not work."
else
    echo "Docker and Docker Compose seem to be installed and configured."
fi

# --- Setup .env files ---
echo "Setting up .env files..."

# Function to create .env from .env.example if it doesn't exist
setup_env_file() {
    local package_dir=$1
    local example_file_standard="$package_dir/.env.example"
    local example_file_agent_created="$package_dir/environment.example" # Fallback due to previous agent issues
    local final_example_file=""
    local env_file="$package_dir/.env"

    if [[ -f "$example_file_standard" ]]; then
        final_example_file="$example_file_standard"
    elif [[ -f "$example_file_agent_created" ]]; then
        final_example_file="$example_file_agent_created"
        echo "Note: Using $example_file_agent_created for $package_dir. Consider renaming to .env.example for standard convention."
    else
        echo "WARNING: Example environment file (.env.example or environment.example) not found for $package_dir. Cannot create .env."
        return
    fi

    if [[ -f "$env_file" ]]; then
        echo "$env_file for $package_dir already exists. Skipping creation."
    else
        cp "$final_example_file" "$env_file"
        echo "Created $env_file for $package_dir from $final_example_file. Please review and fill in necessary values."
    fi
}

setup_env_file "claims_agent"
setup_env_file "mcp_servers"

# Root .env file for COMPOSE_BAKE (if applicable)
if [[ ! -f ".env" ]]; then
    echo "COMPOSE_BAKE=true" > .env
    echo "Created root .env file with COMPOSE_BAKE=true."
else
    if ! grep -q -E "^COMPOSE_BAKE=true" .env; then
        echo -e "\nCOMPOSE_BAKE=true" >> .env
        echo "Added COMPOSE_BAKE=true to root .env file."
    else
        echo "COMPOSE_BAKE=true already present in root .env file."
    fi
fi

# --- Install Python dependencies using uv ---
echo "Installing Python dependencies using uv sync..."
if command_exists uv; then
    uv sync
    echo "Python dependencies installed."
else
    echo "WARNING: uv command not found. Skipping Python dependency installation."
    echo "Please install uv and then run 'uv sync' manually."
fi

# --- Final Instructions ---
echo "-------------------------------------------------------"
echo "Development environment setup script completed."
echo "-------------------------------------------------------"
echo "Next Steps:"
echo "1. Review and update the .env files in 'claims_agent' and 'mcp_servers' with any required secrets or custom configs."
    rename_env_note="Remember to rename 'environment.example' files to '.env.example' if they exist, to align with standard conventions and your .gitignore '!.env.example' rule."
    echo "   NOTE: $rename_env_note"
echo "2. If you haven't already, ensure Docker Desktop is running if you plan to use Docker-based workflows."
echo "3. Restart your shell or source your shell profile (e.g., source ~/.zshrc) if new tools were installed that modified the PATH."

echo ""
echo "Common commands to get started:"
echo "  - To run services locally (without Docker, using Overmind):"
    echo "    uv run poe start-dev"
    echo "      (Requires Overmind to be installed and in your PATH)"
echo "  - To run services using Docker Compose:"
    echo "    uv run poe compose-up       # Starts all services (detached mode)"
    echo "    uv run poe compose-logs     # View logs from Docker services"
    echo "    uv run poe compose-down     # Stops Docker services"
    echo "    uv run poe compose-start-service --service <service_name> # Starts a specific service (e.g., claims_agent)"
echo "  - To run linters, formatters, tests:"
    echo "    uv run poe lint"
    echo "    uv run poe format"
    echo "    uv run poe test"
    echo "    uv run poe check            # Runs format, lint, typecheck"

echo "For issues or more advanced setup, consult the README.md or project documentation." 