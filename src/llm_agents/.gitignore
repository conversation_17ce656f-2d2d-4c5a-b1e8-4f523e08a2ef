# Environments
.venv/
.env
*.env.*
env/
venv/

# Python caches
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Pytest
.pytest_cache/
pytestdebug.log

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Ruff
.ruff_cache/

# IDE / OS generated
.vscode/
.idea/
*.swp
*.swo
.DS_Store
Thumbs.db

# Logs
*.log
logs/
# Except supabase docker volume logs
!db/supabase/docker/volumes/logs/

# SpecStory explanation file
.specstory/.what-is-this.md

# Additions from package .gitignore consolidation
*.so
!.env.example
.ionide/
celerybeat-schedule
celerybeat.pid
config.local.py
instance/
