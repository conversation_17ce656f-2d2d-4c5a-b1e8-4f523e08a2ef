# LLM Agents Monorepo

This repository is a `uv` workspace that manages multiple Python projects related to LLM agents and associated services. It is designed to provide a structured and reproducible environment for building, testing, and deploying these agents.

## System Architecture

The workspace is composed of several services that work together, primarily managed via Docker Compose. The architecture leverages the [Model Context Protocol (MCP)](https://modelcontextprotocol.io/) to allow AI agents to discover and interact with tools provided by backend services.

- `claims_agent/`: An LLM-powered agent service that processes insurance claims and policy information. It acts as a **consumer** of MCP tools. Contains its own
  `pyproject.toml` and `src/claims_agent/` for source code.
- `mcp_servers/`: Provides backend MCP server implementations, exposing various internal Nirvana functionalities (e.g., Policy and Claims information retrieval)
  as MCP tools. It acts as a **provider** of data and functions in an MCP-compatible way. Contains its own `pyproject.toml` and `src/mcp_servers/` for source code.
- `nirvana_rest_api/`: A client library for interacting with the Nirvana REST API, used by `mcp_servers` among other services. Contains its own `pyproject.toml`
  and `src/nirvana_rest_api/` for source code.
- `scripts/`: Contains helper scripts for the workspace, including the `setup.sh` for new developers.
- `docs/`: Contains workspace-level documentation.
- `pyproject.toml`: The root project file that defines the `uv` workspace, shared development dependencies, and common development tasks using `poethepoet`.
- `uv.lock`: The shared lockfile for the entire workspace, ensuring consistent dependencies across all packages.
- `Procfile`: Defines processes for local development using Overmind (e.g., `claims_agent`, `mcp_servers`).
- `docker-compose.yml`: Defines services for Docker-based local development and testing.
- `.env.example` (or similar, per package): Example environment variable files. Actual `.env` files (which should be in `.gitignore`) are used for configuration.
- `.venv/`: The virtual environment for the workspace, managed by `uv`. (This is in `.gitignore`).

```mermaid
graph TD;
    subgraph "Docker Compose Environment"
        subgraph " "
            subgraph "User"
                A["<br/>Developer / Client<br/>(initiates request)"]
            end
            subgraph "Application Services (profile: apps)"
                B["claims_agent (FastAPI)<br/>- API endpoints<br/>- Submits jobs to Temporal"]
                C["claims_agent_worker<br/>- Temporal worker<br/>- Processes background jobs"]
                D["mcp_servers<br/>- Provides MCP tools"]
            end
            subgraph "Infrastructure Services (profile: infra)"
                E["Temporal Server<br/>- Manages workflows & activities"]
                F["Supabase (Postgres)<br/>- Application Database<br/>- Stores job state"]
                G["Redis<br/>- Caching"]
            end
             subgraph "DB Migration"
                H["migrator<br/>(dbmate)"]
            end
        end
    end
```

## Model Context Protocol (MCP) Architecture

This project leverages the [Model Context Protocol (MCP)](https://modelcontextprotocol.io/), an open standard allowing AI agents to discover and use tools/capabilities provided by different services.

**Conceptual Flow:**

```
Client Request → Agents (eg: claims_agent) → MCP Protocol → mcp_servers → Nirvana Backend Services
   │                 │                          │            │                  │
   └─→ HTTP Request  └─→ Consumes               └─→ Exposes  └─→ Interacts via  └─→ Retrieves/Manages
                         MCP Tools                  Nirvana      (e.g., nirvana_rest_api) Data
                                                    as MCP primitives
                                                    (e.g., resources, tools, etc)
```

### Core Components

- **`claims_agent/`**: An LLM-powered agent service that processes insurance claims. It acts as a **consumer** of MCP tools and is the primary application orchestrator, with a FastAPI server for API requests and a Temporal worker for background job processing.
- **`mcp_servers/`**: Provides backend MCP server implementations, exposing various Nirvana functionalities (e.g., Policy and Claims information retrieval) as MCP tools. It acts as a **provider** of data and functions.
- **`db/`**: Contains the database schema and migrations, managed by `dbmate`.
- **`docker-compose.yml`**: The central file for defining and configuring the entire local development stack, including application services, Temporal, Supabase (Postgres), and Redis.
- **`pyproject.toml`**: The root project file that defines the `uv` workspace, shared development dependencies, and common development tasks using `poethepoet`.

## Getting Started

### Prerequisites

1.  **Python 3.11+**
2.  **`uv`**: The Python package and project manager used in this workspace. Install it from [astral.sh](https://astral.sh/uv/install).
3.  **Docker and Docker Compose**: For running the local development environment. [Install Docker Desktop](https://www.docker.com/products/docker-desktop/).

### Setup

1.  **Clone the repository:**

    ```bash
    git clone <repository_url>
    cd llm_agents
    ```

2.  **Set up Environment Variables:**
    The workspace relies on several `.env` files for configuration. You must create them from their example templates.

    - **Root `.env` (for Supabase & Docker):**
      Copy the `supabase.example.env` file to a new file named `.env` in the `src/llm_agents` root directory. This file contains secrets for Supabase and PostgreSQL.

      ```bash
      cp supabase.example.env .env
      ```

    - **Claims Agent `.env`:**
      The `claims_agent` service requires its own `.env` file. An example file is not checked in to git. You must create `src/llm_agents/claims_agent/.env`. You can use the contents of `src/llm_agents/claims_agent/src/claims_agent/config.py` as a reference for the required variables. At a minimum, you will need to provide your `OPENROUTER_API_KEY`.

3.  **Install Python Dependencies:**
    Sync the Python environment for the entire workspace using `uv`. This will install all dependencies listed in the `pyproject.toml` files and the shared `uv.lock` file.

    ```bash
    uv sync
    ```

4.  **Run Database Migrations:**
    Before starting the application services, you need to set up the database schema. The `migrator` service in Docker Compose handles this.
    ```bash
    docker compose up migrator
    ```
    This command runs the `dbmate up` command to apply all pending SQL migrations from the `src/llm_agents/db/migrations` directory.

## Development Workflow

The recommended method for running services locally is with Docker Compose, which manages the entire stack. The `docker-compose.yml` is configured with **profiles** for granular control.

### Running Services Locally with Docker Compose

- **Start all services (Recommended for most development):**
  This command builds the images and starts all services defined in `docker-compose.yml`, including the application and infrastructure.

  ```bash
  docker compose --profile "*" up --build
  ```

- **Start only Infrastructure Services:**
  If you only need the backing services (Temporal, Supabase, Redis) running:

  ```bash
  docker compose --profile infra up
  ```

- **Start only Application Services:**
  Useful if the infrastructure is already running and you are iterating on application code:

  ```bash
  docker compose --profile apps up
  ```

- **View Logs:**
  To view logs from all running containers:

  ```bash
  docker compose logs -f
  ```

  Or for a specific service (e.g., `claims_agent_worker`):

  ```bash
  docker compose logs -f claims_agent_worker
  ```

- **Stop All Services:**
  ```bash
  docker compose down
  ```

### Common `poe` Tasks

This workspace uses `poethepoet` (invoked via `uv run poe <task_name>`) for common development tasks defined in the root `pyproject.toml`.

- **Format code:** `uv run poe format`
- **Lint and fix code:** `uv run poe lint-fix`
- **Type check code:** `uv run poe typecheck`
- **Run all checks:** `uv run poe check`
- **Run tests:** `uv run poe test-all`

### Database Migrations and Code Generation

The project uses a "database-first" approach for managing the schema and ORM models.

1.  **Create a New Migration:**
    Use the `dbmate` CLI to create a new SQL migration file.

    ```bash
    # Get a shell into a container that has dbmate (e.g., the migrator)
    docker compose run --rm migrator new my_new_migration
    ```

    This creates a new file in `src/llm_agents/db/migrations`. Edit this file to define your schema changes in SQL.

2.  **Apply Migrations:**
    Run the `migrator` service to apply any pending migrations to your local database.

    ```bash
    docker compose up migrator
    ```

3.  **Generate ORM Models:**
    After updating the database schema, you must regenerate the SQLAlchemy ORM models. A `poe` task is provided for this.
    ```bash
    uv run poe generate-orm
    ```
    This task runs `sqlacodegen` and updates `src/claims_agent/db/orm/generated.py`.

**IMPORTANT:** The generated ORM file (`generated.py`) **must be committed** to version control along with your migration file. The CI pipeline will fail if the generated code does not match the schema.

### Dependency Management

- **Syncing all dependencies:**
  ```bash
  uv sync
  ```
- **Adding a dependency to a package:**
  ```bash
  uv add <dependency_name> --package <package_name>
  ```
- **Adding a shared development dependency:**
  `bash
    uv add <dependency_name> --dev
    `
  This ensures the `uv.lock` and relevant `pyproject.toml` files are updated correctly.

## Model Context Protocol (MCP) Architecture

This project leverages the [Model Context Protocol (MCP)](https://modelcontextprotocol.io/), an open standard allowing AI agents to discover and use tools/capabilities provided by different services.

**Conceptual Flow:**

```
Client Request → Agents (eg: claims_agent) → MCP Protocol → mcp_servers → Nirvana Backend Services
   │                 │                          │            │                  │
   └─→ HTTP Request  └─→ Consumes               └─→ Exposes  └─→ Interacts via  └─→ Retrieves/Manages
                         MCP Tools                  Nirvana      (e.g., nirvana_rest_api) Data
                                                    as MCP primitives
                                                    (e.g., resources, tools, etc)
```

**Benefits of using MCP:**

- Provides standardized tool definitions that LLMs can understand.
- Enables Agents to discover and use tools (like policy/claims lookups) from `mcp_servers` at runtime.
- Decouples the agent implementation from backend service details.
- Allows for easier addition of new tool capabilities in `mcp_servers` without necessarily changing agent code.

### Authentication

A multi-layered authentication approach is used:

1.  **Client to `claims_agent`**: Client provides a bearer token.
2.  **`claims_agent` to `mcp_servers`**: The `claims_agent` passes through the client's token.
3.  **`mcp_servers` to backend services**: `mcp_servers` uses configured credentials (potentially utilizing `nirvana_rest_api`).

## Documentation

High-level documentation for the workspace is provided in the `docs/` directory. Detailed documentation for specific packages can be found within their respective `README.md` files or sub-directories.

Key documentation files:

- `docs/development.md`: Detailed development practices and environment setup.
- `docs/architecture.md`: Overview of the system architecture.
- `docs/deployment.md`: Guidelines for deploying services.
- Package-specific `README.md` files (e.g., `claims_agent/README.md`).

> **Note on error handling**  
> The response-parsing helper now exposes an `unsafe_allow_fallback` flag. Passing this flag returns a dummy `CoverageDeterminationResponse` when the LLM output cannot be parsed. This is meant only for demos or exploratory scripts—**production code should never enable it** so that real parsing failures surface as exceptions.
