# Use a lightweight base image for the first stage to download the binary
FROM alpine:3.18 as downloader

# Set the dbmate version and architecture
ARG DBMATE_VERSION=1.16.2
ARG TARGETARCH

# Install curl and sha256sum for downloading and verification
RUN apk add --no-cache curl

# Download the dbmate binary for the target architecture
RUN curl -L -o /tmp/dbmate "https://github.com/amacneil/dbmate/releases/download/v${DBMATE_VERSION}/dbmate-linux-${TARGETARCH}"

# Create the final, minimal image
FROM alpine:3.18

# Copy the dbmate binary from the downloader stage
COPY --from=downloader /tmp/dbmate /usr/local/bin/dbmate

# Make it executable
RUN chmod +x /usr/local/bin/dbmate

# Set the working directory
WORKDIR /db

# The default command will be dbmate
ENTRYPOINT ["dbmate"] 