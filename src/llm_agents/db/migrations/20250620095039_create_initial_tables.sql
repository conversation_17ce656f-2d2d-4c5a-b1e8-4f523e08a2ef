-- migrate:up

-- This table tracks the state and result of asynchronous coverage determination requests.
-- Each row represents a single request initiated via the `submit_background_job.py` script.
CREATE TABLE public.legacy_coverage_determinations (
    -- The primary key for the request, generated by the client.
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    -- The identifier for the insurance claim being analyzed.
    claim_id text NOT NULL,
    -- The current status of the workflow (e.g., 'requested', 'in_progress', 'succeeded', 'failed').
    status text NOT NULL,
    -- On success, stores the JSONB result from the `CoverageDeterminationResponse` model.
    content jsonb,
    -- The timestamp when the request was first created.
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    -- The timestamp when the record was last updated.
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    -- On success, the timestamp when the AI generation was completed.
    generated_at timestamp with time zone,
    -- The unique ID of the Temporal workflow processing this request.
    workflow_id text NOT NULL UNIQUE,
    -- On failure, stores the JSONB representation of the `CoverageDeterminationError` model.
    error jsonb
);

-- Enable RLS. Note that we aren't setting up any policies on
-- the table yet, effectively making it closed for all but the backend.
alter table public.legacy_coverage_determinations enable row level security;

-- migrate:down

DROP TABLE IF EXISTS public.legacy_coverage_determinations;