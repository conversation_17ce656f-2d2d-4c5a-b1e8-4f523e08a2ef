-- migrate:up

-- Coverage Runs table - stores each coverage determination run
CREATE TABLE public.coverage_runs (
    -- Unique identifier for each run
    run_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    -- The claim ID being analyzed
    claim_id text NOT NULL,
    -- Optional trace ID for debugging/tracking
    trace_id text,
    -- Status of the coverage determination run (validated in application layer)
    status VARCHAR NOT NULL DEFAULT 'requested',
    -- When the run was created
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    -- Who created the run (email)
    created_by text NOT NULL
);

-- Coverage Notes table - stores individual coverage notes and their feedback
CREATE TABLE public.coverage_notes (
    -- Unique identifier for each note
    note_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    -- Foreign key to coverage_runs table
    run_id uuid NOT NULL REFERENCES public.coverage_runs(run_id) ON DELETE CASCADE,
    -- Claim ID for denormalization and easier querying
    claim_id text NOT NULL,
    -- Original content as generated by the AI (stored as <PERSON><PERSON>N<PERSON>)
    original_content jsonb NOT NULL,
    -- Modified content after user feedback (stored as J<PERSON><PERSON><PERSON>)
    modified_content jsonb,
    -- When the note was last updated
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    -- Who updated the note (email)
    updated_by text
);

-- Create indexes for efficient lookups
CREATE INDEX idx_coverage_runs_claim_id ON public.coverage_runs(claim_id);
CREATE INDEX idx_coverage_runs_created_at ON public.coverage_runs(created_at);
CREATE INDEX idx_coverage_notes_run_id ON public.coverage_notes(run_id);
CREATE INDEX idx_coverage_notes_claim_id ON public.coverage_notes(claim_id);
CREATE INDEX idx_coverage_notes_updated_at ON public.coverage_notes(updated_at);


-- migrate:down

DROP TABLE IF EXISTS public.coverage_notes;
DROP TABLE IF EXISTS public.coverage_runs;

