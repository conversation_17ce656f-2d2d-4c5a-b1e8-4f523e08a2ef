-- migrate:up

-- Grant permissions on coverage notes/run tables to fix insufficient privileges error.
-- This migration ensures the postgres user has the necessary permissions to insert, update,
-- select, and delete from the coverage_runs and coverage_notes tables created in the
-- coverage_feedback_schema migration.

-- Grant only the necessary privileges on the coverage_runs table to postgres user
-- INSERT: Create new coverage runs, SELECT: Read data, UPDATE: Change run status
GRANT INSERT, SELECT, UPDATE ON TABLE public.coverage_runs TO postgres;

-- Grant only the necessary privileges on the coverage_notes table to postgres user
-- INSERT: Create new notes, SELECT: Read data, UPDATE: Apply user feedback
GRANT INSERT, SELECT, UPDATE ON TABLE public.coverage_notes TO postgres;

-- Grant usage and select on all sequences to ensure UUID generation works
-- This is needed for the gen_random_uuid() default values
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Set default privileges for future tables and sequences in the public schema
-- This prevents similar permission issues when new tables are created
-- Only grant the necessary permissions: INSERT, SELECT, UPDATE (no DELETE)
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT INSERT, SELECT, UPDATE ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO postgres;

-- migrate:down

-- Revoke the granted permissions (use with caution in production)
REVOKE INSERT, SELECT, UPDATE ON TABLE public.coverage_runs FROM postgres;
REVOKE INSERT, SELECT, UPDATE ON TABLE public.coverage_notes FROM postgres;
REVOKE USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public FROM postgres;

