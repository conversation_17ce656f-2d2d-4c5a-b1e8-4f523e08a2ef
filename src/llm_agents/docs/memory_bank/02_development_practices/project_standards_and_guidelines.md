# Project Standards & Guidelines: Claims Agent Service
*Critical for code quality, consistency, AI collaboration.*

## 1. Coding Standards (Mandatory)
*   **Pydantic V2**: For all data modeling/validation. Use features (e.g., `SecretStr`, `Field`).
*   **Type Hinting**: Comprehensive. Pass `mypy` (`task typecheck`).
*   **Async/Await**: Use correctly. No blocking I/O in async; use `asyncio.to_thread` for sync libs (e.g., `MCPToolService`).
*   **Logging**: `loguru`. Meaningful logs (decisions, errors, key ops). Use `agent_logging_utils.py` in agent layer.
*   **Error Handling**: Robust. Custom exceptions for specific domains (e.g., `ToolFetchingError`).
*   **Code Style**: Black & Ruff (`task format`, `task lint:fix`). Run `task fix:all`.
*   **File Sizes**: Python files < 400 lines (excl. comments/imports) if feasible. Decompose larger files.
*   **Imports**: Standard grouping (stdlib, 3rd-party, 1st-party). Absolute imports in `src/claims_agent`.
*   **Config**: Access ONLY via `claims_agent.config.settings`. No direct env var reads outside `config.py`.

## 2. Refactoring (Strict Rules)
*   **Analyze First**: Deeply analyze existing code, responsibilities, data flows before proposing changes.
*   **Consult Docs**: Refer to Memory Bank (e.g., `systemPatterns.md`, `activeContext.md`) & project docs (`docs/architecture.md`, etc.) first.
*   **Examine Context**: Use `tree .` for file context. Understand surrounding modules.
*   **Clear Goals**: Define explicit, measurable refactoring goals.
*   **Iterative Steps**: Break into small, verifiable steps.
*   **Verify Each Step**: Run `task fix:all typecheck test` after each step. Ensure all pass.
*   **Preserve Functionality**: Behavior unchanged unless explicitly intended.
*   **High Confidence (>9.5/10)**: Refactor only if understanding of code & impact is high. If uncertain, ask or use limited/exploratory approach.
*   **Test Coverage**: Refactored code MUST be tested. Add new tests.
*   **Update Docs**: Accompany refactoring with Memory Bank & project doc updates.

## 3. Testing (Critical)
*   **Framework**: `pytest`.
*   **Mocking**: `pytest-mock` (`mocker`).
    *   **Patch Target**: CRITICAL: Patch where object is *looked up* (imported) in *module under test*, not definition location.
    *   **Async Mocks**: `mocker.AsyncMock` for async funcs/methods. For `asynccontextmanager`: mock `__aenter__` (ret `AsyncMock`), `__aexit__` (as `AsyncMock`).
    *   **`mocker.spy`**: Use cautiously, esp. with complex/async mocks. Direct assertions often better.
    *   **`SecretStr`**: For assertions, compare unwrapped value (`.get_secret_value()`).
    *   **Type Hints for Mocks**: Use `spec=...`. For `mypy` complaints on mock attrs, use `typing.cast`.
*   **Coverage**: High unit test coverage (agent components, services, parsing, prompts).
*   **CI Checks**: All code MUST pass `task check` before merge.

## 4. Documentation (Mandatory)
*   **Memory Bank Freshness**: Devs (human/AI) MUST update Memory Bank (`activeContext.md`, `progress.md`, `systemPatterns.md`, etc.) after significant code/arch/logic changes.
*   **Living Document**: Memory Bank is living. Flag/propose corrections if outdated/incorrect info found.
*   **AI-Assisted Review**: Periodically, AI compares source code vs. Memory Bank (e.g., `systemPatterns.md`) to find discrepancies. Human verifies & updates.
*   **Update General Docs**: After ANY changes to arch, component responsibility, data flow, config, core logic, update `docs/` (e.g., `architecture.md`).
*   **Docstrings**: Public modules, classes, functions: clear, comprehensive docstrings (Google style). Update if logic changes.

## 5. AI Pair Programming (Project Specifics)
*Supplement general AI Memory Bank instructions.*
*   **Context is Key**: Ensure AI has context. Use `@` for relevant files (Memory Bank, project).
*   **Specificity**: Clear, detailed instructions (paths, names, behavior).
*   **Small Steps**: Incremental changes for easier error correction.
*   **Verification Loop**: Ask AI to run checks (`task fix:all typecheck test`) & confirm output.
*   **Critical Review**: ALWAYS review AI code. Developer is responsible.
*   **Reapply/Rephrase**: If AI edit fails/misunderstands, use Reapply or rephrase with more detail/constraints.
*   **State Constraints**: Explicitly state if parts of codebase are off-limits for a task.
*   **Refer to Memory Bank**: Encourage AI to use Memory Bank for consistent understanding. 