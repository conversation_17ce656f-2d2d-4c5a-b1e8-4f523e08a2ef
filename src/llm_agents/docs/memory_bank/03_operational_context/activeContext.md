# Active Context: Claims Agent Service

## Current Work Focus & System State
*   **Agent Architecture**: Modular agent layer (`src/claims_agent/agents/`) is established (testable, maintainable).
*   **Key Agent Components**:
    *   `MCPClientManager`: MCP client lifecycle, request-specific auth.
    *   `MCPToolService`: Dynamic MCP tool fetching.
    *   `AgentExecutorFactory`: <PERSON><PERSON>s <PERSON><PERSON><PERSON><PERSON> `AgentExecutor` & LLMs.
    *   `prompts.py`: Prompt templates & creation.
    *   `response_parser.py`: LLM response parsing & validation.
    *   `agent_logging_utils.py`: Shared agent logging utils.
*   **Unit Testing**: Comprehensive tests for agent components (critical for dev/CI).

## Key Operational Learnings & Principles (Vital)
*   **Mocking Patch Targets**: Patch where object is *looked up* (imported) in module under test, NOT definition location.
*   **Async Mocking**: Use `mocker.AsyncMock`. Handle `asynccontextmanager` (`__aenter__`, `__aexit__`) correctly.
*   **`mocker.spy`**: Use cautiously with complex/async mocks. Direct assertions often more reliable.
*   **`SecretStr` in Tests**: Compare unwrapped value (`.get_secret_value()`) for `pydantic.SecretStr` mock call args.
*   **AI Collaboration**: Incremental changes, explicit verification (e.g., `task fix:all typecheck test`), clear instructions, iterative refinement.
*   **MCP Client Lifecycle**: `MCPClientManager.get_client()` context manager spans entire agent op (tool fetch, executor create/invoke) in `determine_coverage`/`process_prompt`. Prevents `ClosedResourceError`.

## Next Steps & Ongoing
*   Ensure `task fix:all typecheck test` passes consistently.
*   Address warnings/minor issues promptly.
*   Integrate new features per roadmap, adhering to arch patterns.
*   Continuously update Memory Bank.

## Active Decisions & Arch. Considerations
*   Preserve modular agent architecture for extensions.
*   Paramount: comprehensive unit testing for new/modified code.
*   Maintain/expand multi-LLM provider support (via `AgentExecutorFactory`, config). 