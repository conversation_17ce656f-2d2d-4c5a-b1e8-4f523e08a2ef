# `claims_agent` Architecture (Post-Refactor)

**Package**: `claims_agent`
**Source**: `claims_agent/src/claims_agent/`

**Core Responsibility**: Act as an LLM-powered agent service that processes insurance claims and policy information by consuming MCP tools.

**Key Components**:

1.  **`MCPAgent` (`agents/mcp_agent.py`)**:
    -   Implements `AgentProtocol`.
    -   Main logic for processing prompts and determining coverage.
    -   Uses `MCPClientManager` to interact with MCP servers.
    -   Uses `MCPToolService` to fetch tools.
    -   Uses `AgentExecutorFactory` to create LangChain `AgentExecutor` instances.
    -   Orchestrates calls to prompts, LLM, and response parsing.
    -   Methods:
        -   `process_prompt(prompt, chat_history, authorization)`: Handles general queries.
        -   `determine_coverage(claim_id, authorization, as_of_date)`: Specific flow for coverage determination.
        -   `_get_configured_agent_executor(authorization)`: Async context manager that provides a configured `AgentExecutor` with active MCP client and tools.
        -   `_create_agent_executor_with_active_client(mcp_client, tools)`: Helper to create executor once client and tools are ready.
        -   `_format_chat_history(...)`, `_process_agent_result(...)`

2.  **`AgentExecutorFactory` (`agents/agent_executor_factory.py`)**:
    -   Responsible for creating and configuring LangChain `AgentExecutor` instances.
    -   Uses provider abstraction layer (OpenRouter/OpenAI) with automatic model validation and fallback.
    -   Takes settings override, tools, and prompt template.

3.  **`MCPClientManager` (`agents/mcp_client_manager.py`)**:
    -   Manages lifecycle of MCP client(s) (`langchain_mcp_adapters.MCPClient`).
    -   Provides an async context manager `get_client(authorization)` to obtain an active client.
    -   Handles configuration for claims and policy server URLs.

4.  **`MCPToolService` (`agents/mcp_tool_service.py`)**:
    -   Responsible for fetching/discovering MCP tools using an active `MCPClient`.
    -   Method: `get_mcp_tools(mcp_client)`.

5.  **Prompts (`agents/prompts.py`)**:
    -   `create_agent_prompt_template()`: Creates the main prompt template for the agent.
    -   `get_coverage_determination_prompt(claim_id, as_of_date)`: Specific prompt for coverage.

6.  **Response Parsers (`agents/response_parser.py`)**:
    -   `parse_coverage_determination_response(llm_output_str)`: Parses LLM string output into `CoverageDeterminationResponse` model.

7.  **API Layer (`api/`)**:
    -   `main.py`: FastAPI application setup (`create_app`).
    -   `http.py`: Defines API routes (e.g., `/process-prompt`, `/coverage-determination`).
    -   Handles request/response models, dependency injection of `MCPAgent`.

8.  **Configuration (`config.py`)**:
    -   Pydantic `Settings` class for managing environment variables and application settings (e.g., server URLs, LLM parameters).

9.  **Instrumentation (`instrumentation/`)**:
    -   `helpers.py`: Contains `trace_span_async`, `trace_function`, `trace_async_function` decorators/context managers for OpenTelemetry tracing (e.g., with Phoenix).
    -   `tracer.py`: `BaseTracer` interface, `TracerRegistry`, `NoopTracer`.
    -   `arize.py`: Arize Enterprise-specific tracer implementation.

**Workflow Example (Coverage Determination)**:
1. HTTP request to `/coverage-determination` route in `api/http.py`.
2. Route handler calls `MCPAgent.determine_coverage()`.
3. `MCPAgent` uses `get_coverage_determination_prompt()`.
4. `MCPAgent` calls `_get_configured_agent_executor()`:
    a. `MCPClientManager.get_client()` provides an MCP client.
    b. `MCPToolService.get_mcp_tools()` fetches tools using the client.
    c. `AgentExecutorFactory.create_executor()` builds the agent executor with tools and prompt.
5. `MCPAgent` invokes the agent executor (`agent_executor.ainvoke()`).
6. LLM interacts with MCP tools (exposed by `mcp_servers`) as needed.
7. `MCPAgent` receives LLM output.
8. `parse_coverage_determination_response()` processes the output.
9. Structured `CoverageDeterminationResponse` returned via API. 