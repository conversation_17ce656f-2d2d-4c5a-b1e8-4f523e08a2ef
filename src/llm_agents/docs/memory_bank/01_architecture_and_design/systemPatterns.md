# System Patterns & Architecture: Claims Agent Service

## Overall Architecture
*Ref: `docs/architecture.md` for visual/details.*
*   **Layered Design**: API (FastAPI) -> Service (`ClaimsService`, etc.) -> Agent (`AgentManager` -> `MCPAgent`).

## Agent Layer Components (Critical for understanding)
*   **`AgentProtocol` (`interface.py`)**: Defines agent contract (Python `Protocol`). Ensures consistent interaction.
*   **`AgentManager` (`manager.py`)**: Singleton. Manages `MCPAgent` lifecycle (init/shutdown). Uses `AgentProtocol`.
*   **`MCPAgent` (`mcp_agent.py`)**: Main agent logic orchestrator (conforms to `AgentProtocol`). Delegates tasks. **Avoid adding new logic directly; enhance/create specialized components.**
    *   Roles: Receives requests (e.g., `process_prompt`, `determine_coverage`), uses `MCPClientManager` for MCP client, `MCPToolService` for tools, `prompts.py` for LLM prompts, `AgentExecutorFactory` for <PERSON><PERSON><PERSON>n `AgentExecutor`, invokes executor, `response_parser.py` for LLM response parsing, `agent_logging_utils.py` for logging.
*   **`MCPClientManager` (`mcp_client_manager.py`)**: Manages `MultiServerMCPClient` lifecycle.
    *   `asynccontextmanager` (`get_client`): Ensures proper setup/teardown (client, URLs, auth tokens).
    *   Handles request-specific auth tokens for multi-tenancy.
    *   Includes MCP server connectivity test utils.
*   **`MCPToolService` (`mcp_tool_service.py`)**: Fetches tools from active MCP client.
    *   Input: Initialized `MultiServerMCPClient`.
    *   Calls `client.get_tools()` (via `asyncio.to_thread` for non-blocking).
    *   Handles tool-fetching specific logging/errors (e.g., `ToolFetchingError`).
*   **`AgentExecutorFactory` (`agent_executor_factory.py`)**: Centralizes LangChain `AgentExecutor` & LLM (`ChatOpenAI`) creation/config.
    *   Reads config (model, temp, keys, base URLs) from global `settings`.
    *   Instantiates `ChatOpenAI` (handles OpenAI vs. other providers).
    *   Assembles LLM, tools, prompt into runnable components.
    *   Creates `AgentExecutor` (config: verbose, max_iterations, error handling).
*   **`prompts.py` (Module)**: All LLM prompt templates & formatting logic. Modify agent instructions/system messages here.
*   **`response_parser.py` (Module)**: Parses/validates/transforms raw LLM responses to structured Python objects (Pydantic models). Handles JSON extraction. Enhance parsing/new formats here.
*   **`agent_logging_utils.py` (Module)**: Shared logging helpers (truncating, intermediate steps). Use for consistent agent logging.

## Key Design Patterns
*   **Factory**: `AgentExecutorFactory` (for `AgentExecutor`, LLMs).
*   **Service**: `MCPToolService` (tool fetching).
*   **Context Manager**: `MCPClientManager.get_client` (`MultiServerMCPClient` lifecycle).
*   **Dependency Injection**: `injector` library (`src/claims_agent/di/`).
*   **Strategy (Conceptual)**: `prompts.py`, `response_parser.py` (swappable/extendable logic).
*   **Singleton**: `AgentManager` (agent lifecycle).
*   **Interface Segregation (`Protocol`)**: `AgentProtocol` (agent contract). 