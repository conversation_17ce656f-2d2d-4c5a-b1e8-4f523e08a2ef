# Technical Context: Claims Agent Service

## Core Technologies
*   **Language**: Python 3.11+
*   **Web Framework**: FastAPI (API endpoints)
*   **Data Modeling**: Pydantic V2 (Mandatory)
*   **LLM Orchestration**: <PERSON><PERSON><PERSON><PERSON> (agent logic, tools, prompts)
*   **Logging**: Loguru
*   **Dependency Injection**: `injector` library (`src/claims_agent/di/`)
*   **Package/Env Management**: `uv`
*   **Task Runner**: `Taskfile.yml` (`task`)

## Key External Interactions
*   **MCP Servers**: Via `langchain-mcp-adapters` (SSE communication).
*   **OpenAI API / Compatible Endpoints**: LLM access (e.g., GPT, OpenRouter). Config: API keys, base URLs in settings.

## Development Setup
*   `uv` for deps/envs (`pyproject.toml`, `uv.lock`).
*   Scripts in `Taskfile.yml` (e.g., `task check`).
*   `.env` file for config, loaded into `claims_agent.config.Settings`.

## Technical Constraints
*   **Async Core**: Avoid blocking I/O in `async` code (use `asyncio.to_thread` if needed, e.g., `MCPToolService`).
*   **Config Access**: ONLY via `claims_agent.config.settings` object.
*   **Modularity**: Agent layer: prefer new/specialized components over modifying `MCPAgent`.
*   **Type Safety**: Mandatory type hints, enforced by `mypy`.

## Tool Usage
*   **LangChain Tools**: MCP capabilities exposed as `Tool` objects (fetched by `MCPToolService`).
*   **LLM Interaction**: Via LangChain `AgentExecutor` & `ChatOpenAI`/compatible models. 