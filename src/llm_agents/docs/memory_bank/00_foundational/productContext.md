# Product Context: Claims Agent Service

## Problem Domain
Insurance: Claims processing, policy info management.
Key function: Coverage determination (is claim covered by policy?).

## How It Works
*   Intelligent intermediary (LLM agent).
*   Communicates with external MCP servers (via LLM) for policy/claim data & tools.
*   Exposes functions via FastAPI.

## User Experience Goals (Implied)
*   Reliable, accurate coverage determination.
*   Seamless integration for policy/claim queries.
*   Robust, predictable interaction with MCP services. 