# Workspace Overview (Post-Refactor)

**Primary Goal**: Structured environment for LLM agents and MCP-related services.

**Core Technology**:
- Package Management: `uv`
- Task Runner: `poethepoet` (configured in `pyproject.toml` files)
- Language: Python 3.11+

**Directory Structure**:
- `llm_agents/` (Workspace Root)
    - `pyproject.toml`: Root config (uv workspace, shared dev deps, global poe tasks)
    - `uv.lock`: Shared lockfile
    - `.venv/`: Virtual environment
    - `scripts/`: Utility scripts (e.g., `clean.py`)
    - `claims_agent/`: Claims Agent package
        - `src/claims_agent/`: Source code
        - `pyproject.toml`: Package-specific deps & poe tasks
        - `README.md`
        - `tests/`
    - `mcp_servers/`: MCP Servers package
        - `src/mcp_servers/`: Source code
        - `pyproject.toml`: Package-specific deps & poe tasks
        - `README.md`
        - `tests/`
    - `nirvana_rest_api/`: Nirvana REST API client library
        - `src/nirvana_rest_api/`: Source code
        - `pyproject.toml`: Package-specific deps & poe tasks
        - `README.md`
    - `docs/`: Documentation, including this memory bank and `refactoring_plan.md`.

**Key Packages & Roles**:
1.  `claims_agent`:
    -   Role: LLM-powered agent service, consumes MCP tools.
    -   Entrypoint: `claims_agent.main:app` (FastAPI)
    -   Key Modules:
        -   `agents/`: Core agent logic (`MCPAgent`, `AgentExecutorFactory`, `MCPClientManager`, `MCPToolService`).
        -   `api/`: FastAPI application setup and routes.
        -   `instrumentation/`: Tracing helpers.
        -   `config.py`: Pydantic settings.
2.  `mcp_servers`:
    -   Role: Provides backend MCP server implementations, exposes Nirvana functionalities as MCP tools.
    -   Entrypoint: `mcp_servers.main:app` (FastAPI)
    -   Key Modules:
        -   `mcp_server_claims/`: Claims-related MCP server logic and tools.
        -   `mcp_server_policy/`: Policy-related MCP server logic and tools.
        -   `lib/`: Shared libraries (e.g., `config.py`, `storage.py`).
        -   `parse/`: Document parsing utilities.
3.  `nirvana_rest_api`:
    -   Role: Client library for Nirvana REST API. Auto-generated. Used by `mcp_servers`.

**Development Workflow**:
- Environment Setup: `uv sync --all-packages --dev`
- Package Env Init: `uv run <package_name>:init-env` (e.g., `uv run claims_agent:init-env`)
- Common Tasks (run from root with `uv run poe <task>` or `uv run <task>`):
    - `format`
    - `lint`, `lint-fix`
    - `typecheck`
    - `test`
    - `clean`
- Package-Specific Tasks: `uv run <package_name>:<task_name>` (e.g., `uv run claims_agent:start`)
- Docker: Tasks like `docker-build-claims-agent` available via `poe`. 