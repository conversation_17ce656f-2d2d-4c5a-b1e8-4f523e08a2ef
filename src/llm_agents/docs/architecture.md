# Claims Agent Service Architecture

This document outlines the high-level architecture of the **Claims Agent Service**, one of the key applications within the LLM Agents monorepo. It explains its core components and shows how data flows through this specific service.

## High-Level Overview of Claims Agent

```text
Client HTTP Request
   │
   ▼
FastAPI Application (API Layer)
   │
   ▼
Service Layer (ClaimsService)
   │
   ▼
AI Agent (MCPAgent via AgentManager)
   │
   ▼
External MCP Servers (Claims/Policy SSE)
   │
   ▼
AI Agent (MCPAgent) processes responses
   │
   ▼
Service Layer assembles Pydantic Response Model
   │
   ▼
FastAPI returns JSON Response to Client
```

## Components

### 1. API Layer
- **Entry Points**:
  - `main.py` / `src/claims_agent/main.py` initializes FastAPI and DI.
  - `create_app()` in `src/claims_agent/api/__init__.py` configures routers, lifespan, and exception handlers.
- **Routers and Handlers**:
  - Defined in `src/claims_agent/api/http.py` (e.g., `/health`, `/api/claims/...`).
  - Error handlers in `src/claims_agent/api/errors.py`.

### 2. Service Layer
- **Service Protocol**:
  - `AgentServiceProtocol` defines claims service interface.
- **Implementation**:
  - `ClaimsService` in `src/claims_agent/api/services.py` handles business logic, holds authorization context, and invokes the agent.

### 3. Agent Layer
- **AgentManager** (`src/claims_agent/agents/manager.py`):
  - Manages the lifecycle (initialization, shutdown) of the core agent instance.
  - Provides access to a singleton `AgentProtocol` conformant agent.
- **MCPAgent** (`src/claims_agent/agents/mcp_agent.py`):
  - The primary implementation of `AgentProtocol`.
  - Orchestrates interactions with MCP servers and the LLM.
  - Delegates specific responsibilities to specialized components:
    - **MCPClientManager** (`src/claims_agent/agents/mcp_client_manager.py`):
      - Manages the lifecycle of `MultiServerMCPClient` instances using async context managers.
      - Handles client configuration, including server URLs and authorization tokens per request.
      - Provides methods to test server connectivity.
    - **MCPToolService** (`src/claims_agent/agents/mcp_tool_service.py`):
      - Responsible for fetching available tools (e.g., policy lookup, claim info) from an active `MultiServerMCPClient`.
      - Ensures tools are correctly retrieved before agent execution.
    - **AgentExecutorFactory** (`src/claims_agent/agents/agent_executor_factory.py`):
      - Creates and configures LangChain `AgentExecutor` instances.
      - Uses provider abstraction layer for LLM instantiation (OpenRouter primary, OpenAI fallback).
      - Automatically validates model capabilities and falls back to supported models.
      - Assembles the agent runnable, tools, and prompt for the executor.
    - **Prompts Module** (`src/claims_agent/agents/prompts.py`):
      - Centralizes the definition and creation of prompt templates used by the agent (e.g., system messages, coverage determination prompts).
    - **Response Parser Module** (`src/claims_agent/agents/response_parser.py`):
      - Handles parsing of LLM responses, particularly for extracting structured JSON data (e.g., verification items for coverage determination).
      - Includes error handling for malformed or unexpected responses.
    - **Logging Utilities** (`src/claims_agent/agents/agent_logging_utils.py`):
      - Provides helper functions for consistent agent-related logging, such as truncating long strings and logging intermediate steps of agent execution.
- **Agent Interface** (`src/claims_agent/agents/interface.py`):
  - Defines `AgentProtocol` (the contract for agent implementations) and `AgentManagerBase` (the base for agent managers).

### 4. Dependency Injection (DI)
- Uses [injector](https://github.com/alecthomas/injector) to bind:
  - **ConfigModule** (`settings` object)
  - **AgentModule** (provides `AgentProtocol` instance)
  - **ServiceModule** (provides `ClaimsService` instance)
- Configured in both `src/claims_agent/main.py` and `src/claims_agent/api/__init__.py`.

### 5. Configuration & Settings
- `src/claims_agent/config.py` defines `Settings` via Pydantic v2, loading from `.env`.
- Environment variables control server host/port, logging, API keys, model behavior, and MCP server URLs.

### 6. Models
- Pydantic models in `src/claims_agent/models/verification.py` represent API schemas:
  - `Citation`, `VerificationItem`, `CoverageDeterminationResponse`.

### 7. Logging & Monitoring
- **Loguru** is used for structured logging at each layer:
  - Startup/shutdown logs
  - Request/response tracing
  - Error handling

### 8. Caching & Persistence Layer
- **Repository Layer**:
  - `CoverageDeterminationRequestRepositoryProtocol` defines the interface for request lifecycle management.
  - `SupabaseCoverageDeterminationRequestRepository` provides the implementation for PostgreSQL/Supabase.
- **Cache Strategy**:
  - Time-based TTL caching with configurable expiration (default 24 hours).
  - Graceful degradation - cache failures don't break the API.
  - Database storage in `legacy_coverage_determinations` table.
- **Request Tracking**:
  - Complete audit trail for all coverage determination requests.
  - Status progression: `pending` → `in_progress` → `succeeded`/`failed`.
  - Request correlation via UUIDs for debugging and monitoring.
- **Observability**:
  - Response headers: `X-Cache-Status` (HIT/MISS/BYPASS/ERROR), `X-Cache-TTL`.
  - Comprehensive logging for cache operations and failures.

### 9. Testing & Quality
- **Pytest** for unit and integration tests (`tests/` folder).
- **MyPy**, **Ruff**, **Black** enforcing typing and formatting standards.

## Data Flow Example

1. **Client** sends `GET /api/claims/{claim_id}/coverage/determination` with `Authorization` header.
2. **FastAPI** routes to `get_coverage_determination` handler.
3. **Cache Lookup**: Handler checks `CoverageDeterminationRequestRepository` for existing successful determination within TTL window.
4. **Cache Hit**: If found, return cached response with `X-Cache-Status: HIT` and skip to step 10.
5. **Cache Miss/BYPASS**: Log cache status (`MISS` or `BYPASS` if `force_refresh=true`) and proceed with agent execution.
6. **Request Tracking**: Create new request record with UUID and set status to `in_progress`.
7. **ClaimsService** sets `authorization`, validates input, and calls `agent.determine_coverage(claim_id)`.
8. **MCPAgent**:
   a. Uses `MCPClientManager` to obtain a `MultiServerMCPClient` with the provided authorization.
   b. Employs `MCPToolService` to fetch tools (e.g., claim and policy lookup functions) from the client.
   c. Retrieves the appropriate prompt from the `prompts` module.
   d. Leverages `AgentExecutorFactory` to create an `AgentExecutor` configured with the LLM, tools, and prompt.
   e. Invokes the `AgentExecutor`, which may call MCP tools multiple times (streaming from claims/policy SSE endpoints via the client) to gather information.
   f. The LLM processes the gathered information to make a coverage determination.
9. **MCPAgent** receives the raw response from the LLM. The `response_parser` module extracts and validates the structured `CoverageDeterminationResponse` data.
10. **Cache Storage**: On success, store the response in database and update request status to `succeeded`.
11. **FastAPI** serializes response to JSON, adds cache headers (`X-Cache-Status`, `X-Cache-TTL`), and returns to client.