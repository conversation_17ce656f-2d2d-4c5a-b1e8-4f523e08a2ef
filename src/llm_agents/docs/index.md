# Claims Agent Service

The **Claims Agent Service** is a modern Python web application that processes insurance claims and policy information using AI-powered verification via Model Context Protocol (MCP) servers. It provides a RESTful API for coverage determination and leverages modular design principles for maintainability and extensibility.

Key Features:

- **FastAPI**: Asynchronous, high-performance web framework.
- **Pydantic v2**: Data validation and settings management.
- **Dependency Injection**: Powered by `injector` for clear separation of concerns.
- **AI Agent Integration**: Uses `langchain-mcp-adapters` to interact with external claims/policy servers.
- **Response Caching**: Time-based caching with configurable TTL for improved performance.
- **Request Lifecycle Tracking**: Complete audit trail for coverage determinations.
- **Cache Observability**: Response headers for monitoring cache performance.
- **Comprehensive Logging**: Built with **Loguru** for structured logs and tracing.
- **Containerization**: Supports Docker for consistent deployments.
- **Code Quality**: Enforced via **Black**, **Ruff**, and **MyPy**.
- **Testing**: Built-in **pytest** suite with fixtures and integration tests.

This documentation set will guide you through setting up the project, understanding its architecture, exploring the API, developing locally, contributing, and deploying to production.

## Table of Contents

- [Setup Guide](setup.md)
- [Architecture](architecture.md)
- [API Reference](api.md)
- [Development Guide](development.md)
- [Contributing](contributing.md)
- [Deployment](deployment.md) 
