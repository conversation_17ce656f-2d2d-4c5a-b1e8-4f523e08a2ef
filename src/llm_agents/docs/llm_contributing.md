# LLM Contributing Guide

This guide covers best practices and workflows for contributing to the LLM (Language Model) components of the Claims Agent Service, including lessons learned from significant refactoring efforts.

## Table of Contents

- [Overview](#overview)
- [Agent Architecture & Code Organization](#agent-architecture--code-organization)
- [Key Components & Responsibilities](#key-components--responsibilities)
- [Adding or Updating Prompts](#adding-or-updating-prompts)
- [Model Configuration](#model-configuration)
- [Testing LLM Logic & Agent Components](#testing-llm-logic--agent-components)
- [Common Pitfalls, Lessons Learned, and Best Practices](#common-pitfalls-lessons-learned-and-best-practices)
- [Logging & Debugging](#logging--debugging)
- [CI / Quality Checks](#ci--quality-checks)
- [Pull Request Checklist](#pull-request-checklist)

## Overview

The Claims Agent relies on an LLM-powered agent (`MCPAgent`) within the `src/claims_agent/agents/` directory. This agent is responsible for all AI interactions, including prompt construction, interaction with MCP tools (via `langchain-mcp-adapters`), and response parsing. Contributions in this area involve:

- Refining or adding new prompts.
- Modifying model parameters (e.g., temperature, model name).
- Enhancing the interaction logic with tools and services.
- Improving response parsing and output post-processing.
- Evolving the agent's component-based architecture.

## Agent Architecture & Code Organization

The agent's architecture has been refactored for modularity and testability. Refer to `docs/architecture.md` for a comprehensive architectural overview.

The core agent components are located in `src/claims_agent/agents/`:
```text
src/claims_agent/agents/
├── interface.py             # AgentProtocol and AgentManagerBase definitions
├── manager.py               # AgentManager: Handles agent lifecycle (singleton)
├── mcp_agent.py             # MCPAgent: Core orchestration logic
├── mcp_client_manager.py    # MCPClientManager: Manages MultiServerMCPClient lifecycle
├── mcp_tool_service.py      # MCPToolService: Fetches tools from MCP client
├── agent_executor_factory.py # AgentExecutorFactory: Creates LangChain AgentExecutors
├── prompts.py               # Defines and creates prompt templates
├── response_parser.py       # Parses and validates LLM responses
└── agent_logging_utils.py   # Utility functions for agent-specific logging
```

## Key Components & Responsibilities

- **`AgentProtocol` (`interface.py`)**: Defines the contract that agent implementations like `MCPAgent` must adhere to.
- **`AgentManager` (`manager.py`)**: A singleton responsible for the lifecycle (initialization, shutdown) of the `MCPAgent` instance. Ensures that the agent's resources are properly managed.
- **`MCPAgent` (`mcp_agent.py`)**: The primary implementation of `AgentProtocol`. It orchestrates the overall process of handling a prompt or a coverage determination request. It delegates tasks to the specialized components listed below.
- **`MCPClientManager` (`mcp_client_manager.py`)**: Manages the creation and cleanup of `MultiServerMCPClient` instances using an `asynccontextmanager`. It handles server URLs and request-specific authorization.
- **`MCPToolService` (`mcp_tool_service.py`)**: Responsible for fetching the available tools (e.g., functions to get policy details or claim information) from an active `MultiServerMCPClient`.
- **`AgentExecutorFactory` (`agent_executor_factory.py`)**: Creates and configures LangChain `AgentExecutor` instances. This includes instantiating the LLM (e.g., `ChatOpenAI`) with the correct settings and assembling the agent runnable, tools, and prompts.
- **`prompts.py`**: Centralizes all prompt templates used by the agent, making them easier to manage and modify.
- **`response_parser.py`**: Contains logic for parsing the raw output from the LLM, especially for extracting and validating structured JSON data like `CoverageDeterminationResponse`.
- **`agent_logging_utils.py`**: Provides helper functions for standardized logging related to agent operations, such as truncating long outputs and tracing tool execution.

## Adding or Updating Prompts

1.  Open `src/claims_agent/agents/prompts.py`.
2.  Modify existing prompt string constants (e.g., `_AGENT_SYSTEM_MESSAGE`, `_COVERAGE_DETERMINATION_PROMPT_TEMPLATE`) or add new ones.
3.  If adding a new prompt type, create a new function similar to `get_coverage_determination_prompt` or `create_agent_prompt_template` to construct and return it.
4.  Follow existing conventions:
    *   Use f-strings or `.format()` for dynamic content.
    *   Provide clear, specific instructions and examples for the model within the prompts.
5.  Ensure any time-sensitive or large-context prompts consider token limits. Document strategies like truncation or splitting if employed.
6.  Reference the new/updated prompts in `MCPAgent` or `AgentExecutorFactory` as needed.

## Model Configuration

Model settings are accessed via the `Settings` object from `claims_agent.config`.
Key settings include:
- **`settings.MODEL_NAME`**: Change in `.env` or `config.py` defaults.
- **`settings.MODEL_TEMPERATURE`**: Adjust for creativity vs. determinism.
- **`settings.OPENAI_API_KEY`**: Must be set in `.env`.
- **`settings.AGENT_VERBOSE`**: If `true`, `AgentExecutor` logs more details. (`langchain.debug = True` can also be useful globally).

To add new configuration fields relevant to the LLM/agent:
1.  Update the `Settings` Pydantic model in `src/claims_agent/config.py`.
2.  Add the new variable to `.env.example` with a description.
3.  Update `docs/setup.md` if it's a user-configurable setup variable.
4.  Ensure the new setting is utilized appropriately in the agent components (e.g., passed to `AgentExecutorFactory` or `ChatOpenAI`).

## Testing LLM Logic & Agent Components

Thorough testing is crucial for LLM-related code due to its partially non-deterministic nature and reliance on external services.

### Unit Tests

-   **Isolate Components**: Test each new component (`MCPClientManager`, `AgentExecutorFactory`, etc.) individually.
-   **Mock External Dependencies**:
    *   Use `pytest-mock` (`mocker`) extensively.
    *   Mock `MultiServerMCPClient`, `ChatOpenAI`, `AgentExecutor`, and other external classes or functions.
    *   **Patching Target**: Remember to patch where the object is *looked up*, not where it's defined. For example, if `module_a.py` has `from module_b import ClassB` and `module_a.foo()` uses `ClassB()`, you patch `mocker.patch("module_a.ClassB", ...)` not `"module_b.ClassB"`. This was a key learning when patching `MCPAgent` in `AgentManager` tests.
    *   **Async Mocking**: For `async` methods and context managers:
        *   Use `mocker.AsyncMock` or `unittest.mock.AsyncMock`.
        *   For async context managers (`async with ... as ...`): mock the object such that its `__aenter__` returns an `AsyncMock` (or the desired mock object) and `__aexit__` is also an `AsyncMock`. Example:
            ```python
            mock_client_cm = mocker.AsyncMock() # The context manager itself
            mock_client_yielded = mocker.AsyncMock(spec=MultiServerMCPClient) # What's yielded
            mock_client_cm.__aenter__.return_value = mock_client_yielded
            mock_client_cm.__aexit__ = mocker.AsyncMock(return_value=None)
            mocker.patch.object(client_manager_instance, 'get_client', return_value=mock_client_cm)
            ```
        *   Avoid `mocker.spy` on already mocked objects or where complex interactions (like async context management) are involved, as it can lead to unexpected `TypeError`s (e.g., `__qualname__/__name__ must be set`). Direct assertions on mock call counts are often more reliable.
-   **Test Prompt Construction**: Verify that prompt-generating functions in `prompts.py` correctly format prompts with given inputs.
-   **Test Response Parsing**: Test functions in `response_parser.py` with various valid and invalid LLM output strings (including malformed JSON, missing fields, etc.) to ensure robustness.
-   **Configuration Tests**: Ensure factories and managers correctly pass configurations (e.g., API keys, model names, URLs) to the objects they create.
-   **Type Hints for Mocks**: When accessing attributes on mocks that are not part of the original spec (e.g., `return_value`, `call_args_list`), use `typing.cast` or `mocker.MagicMock` more broadly if `mypy` complains, or ensure the mock is spec'd correctly.

### Integration Tests (Conceptual)

-   While full end-to-end tests with live LLMs can be complex and expensive, consider:
    *   **Record-and-Replay**: For `MultiServerMCPClient` interactions, capture real (anonymized) SSE stream data to a fixture file and have mocks replay this data.
    *   **Deterministic LLM Calls**: For testing flows that involve the LLM, set `MODEL_TEMPERATURE=0.0`. You might use a dedicated test API key or mock the LLM response with expected structures.
    *   **Focus on Component Integration**: Test how `MCPAgent` orchestrates calls between `MCPClientManager`, `MCPToolService`, `AgentExecutorFactory`, and `response_parser`.

## Common Pitfalls, Lessons Learned, and Best Practices

Our recent refactoring of the agent layer provided several insights:

1.  **Modularity is Key (Design Patterns)**:
    *   Breaking down the monolithic `MCPAgent` into smaller, focused components significantly improved readability, testability, and maintainability.
    *   **Factory Pattern** (`AgentExecutorFactory`): Useful for centralizing the complex creation logic of `AgentExecutor`.
    *   **Service Pattern** (`MCPToolService`): Encapsulates the specific task of fetching tools.
    *   **Context Manager Pattern** (`MCPClientManager.get_client`): Ensures robust lifecycle management (setup/teardown) for `MultiServerMCPClient`, especially crucial for handling network resources and authentication contexts.

2.  **Precise Patching in Tests**:
    *   Always patch the name where it's *looked up* in the module under test. If `module_A.py` imports `Foo` from `module_B.py` and uses `Foo()`, your test for `module_A.py` should patch `module_A.Foo`.
    *   This was particularly relevant for `AgentManager` tests trying to mock `MCPAgent`. The import of `MCPAgent` had to be at the module level in `manager.py` for the patches in `test_manager.py` to work correctly.

3.  **Async Mocking Nuances**:
    *   Standard `MagicMock` won't work for async methods; use `AsyncMock`.
    *   Mocking `asynccontextmanager` requires careful setup of `__aenter__` and `__aexit__` (both as `AsyncMock`s).
    *   Be wary of `mocker.spy` with complex mocks or async operations; direct assertions on mock methods are often clearer and less error-prone.

4.  **Type Checking with Mocks (`mypy`)**:
    *   `mypy` can struggle with un-specced mock objects. Provide a `spec` argument to `mocker.patch` or `AsyncMock` (e.g., `spec=MultiServerMCPClient`).
    *   For attributes added by `unittest.mock` itself (like `return_value`, `called`), `mypy` might still complain. `typing.cast` can be a pragmatic solution in test code, or carefully structuring mock setups.

5.  **Iterative Development with AI (Pair Programming)**:
    *   **Specificity is Crucial**: When asking the AI to make code changes (e.g., refactoring, applying edits), be as specific as possible about the desired outcome, the location of changes, and any constraints.
    *   **Small, Verifiable Steps**: Break down large tasks into smaller steps. After each AI-assisted change, run checks (`task fix:all typecheck test`) to verify.
    *   **Re-applying Edits**: If an AI-generated edit isn't quite right or fails to apply cleanly, using the "Reapply" feature or re-phrasing the instruction can often help. Sometimes, manual correction of the AI's proposed diff is faster.
    *   **Context is King**: Ensure the AI has sufficient context (relevant open files, recent history). Attaching files or snippets manually can be very effective.
    *   **Human Oversight**: Always review AI-generated code and suggestions critically. The AI is a powerful assistant, not an infallible authority.

6.  **Configuration Management**:
    *   Centralize configuration access (e.g., via `claims_agent.config.settings`).
    *   Pass necessary configurations explicitly to components (like `AgentExecutorFactory`) rather than having them all read globally, to improve testability and clarity of dependencies.

7.  **Clear Logging**:
    *   Use structured logging (`loguru`).
    *   Log important decisions, configurations, and the start/end of key operations within agent components.
    *   The `agent_logging_utils.py` helps maintain consistency here.

## Logging & Debugging

- Enable `AGENT_VERBOSE=true` in your `.env` for detailed `AgentExecutor` logs.
- Set `LOG_LEVEL=DEBUG` in `.env` for more granular application-wide logs.
- Loguru is used. Key log points in `MCPAgent` and its components indicate:
  - Prompt construction and content.
  - Tool fetching and availability.
  - AgentExecutor creation.
  - Invocation of `agent_executor.ainvoke()`.
  - Intermediate steps (tool calls and observations).
  - Raw LLM output before parsing.
  - Parsing successes or failures.
- To inspect SSE streaming, use `httpx` event hooks or tools like Wireshark/mitmproxy if deep network debugging is needed.

## CI / Quality Checks

Every PR touching LLM code must pass:

- **`task format`** (Black + Ruff)
- **`task lint`** (Ruff)
- **`task typecheck`** (MyPy)
- **`task test`** (pytest with async support)
- **`task check`** (all of the above)

Additionally, ensure:
- No sensitive API keys are checked in.
- `.env.example` is updated if you added new env vars.
- Relevant documentation (e.g., `architecture.md`, this file) is updated if the changes are significant.

## Pull Request Checklist

- [ ] **Architectural Impact**: If changes significantly alter agent flow or component responsibilities, `docs/architecture.md` is updated.
- [ ] **Prompt Documentation**: All new or updated prompts in `prompts.py` have comments explaining intent and placeholders.
- [ ] **Config Changes**: Added new settings are reflected in `.env.example` and `docs/setup.md`.
- [ ] **Tests Added/Updated**: Unit tests cover new/modified logic, especially for agent components, prompt generation, and response parsing. Edge cases and error conditions are considered.
- [ ] **Logging Verified**: Important operations and decisions are logged. Verbose logs provide sufficient detail for debugging.
- [ ] **CI Passing**: All `task check` steps complete without errors.
- [ ] **Peer Review**: At least one other engineer has signed off on the LLM-related changes.

Thank you for improving the LLM experience in the Claims Agent! 🎉 