# Contributing

Thank you for your interest in contributing to the Claims Agent Service! We welcome contributions of all kinds: bug fixes, new features, documentation improvements, and tests.

## Getting Started

1. **Fork the repository** on GitHub.
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/nirvanatech/nirvana.git
   cd nirvana/llm_agents/claims_agent
   ```
3. **Set up your environment** following the [Setup Guide](setup.md).
4. **Create a new branch** for your work:
   ```bash
   git checkout -b feat/my-new-feature
   ```

## Development Workflow

- **Make incremental changes** with clear, focused commits.
- **Run formatting and linting** before committing:
  ```bash
  task format
  task lint:fix
  ```
- **Type check** your changes:
  ```bash
  task typecheck
  ```
- **Write tests** under the `tests/` directory.
- **Run tests** locally:
  ```bash
  task test
  ```

## Commit Message Guidelines

Use concise, descriptive commit messages. Format as:
```
<type>(<scope>): <short description>
```
Common types:
- `feat`: new feature
- `fix`: bug fix
- `docs`: documentation only changes
- `style`: formatting, linting, no code change
- `refactor`: code changes without adding feature or fixing bug
- `test`: adding or updating tests

Example:
```
feat(api): support batch coverage determination
```

## Pull Requests

1. **Push your branch** to your fork:
   ```bash
   git push origin feat/my-new-feature
   ```
2. **Open a Pull Request** against the `main` branch of this repository.
3. **Ensure all checks pass** (formatting, linting, type checking, tests).
4. **Add a meaningful description** of your changes and reference related issues.
5. **Tag reviewers** and **respond to feedback**.

## Reporting Issues

If you encounter bugs or have feature requests, please open an **Issue**:
- Use a clear, descriptive title.
- Provide steps to reproduce (for bugs).
- Include relevant logs, error messages, or stack traces.

## Code of Conduct

This project adheres to a [Code of Conduct](https://github.com/<org>/claims_agent/blob/main/CODE_OF_CONDUCT.md). Please follow its guidelines.

---

Thank you for helping improve the Claims Agent Service! 