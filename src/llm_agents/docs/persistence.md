# Managing Persistence

This guide covers database schema management, migrations, and ORM code generation workflows in the **LLM Agents project**.

## Project Architecture Overview

The LLM Agents project uses:
- **PostgreSQL** via Supabase (cloud production, Docker Compose for local development)
- **dbmate** for SQL schema migrations  
- **SQLAlchemy 2.0** with auto-generated models via `sqlacodegen`
- **uv workspace** structure with multiple packages (`claims_agent`, `mcp_servers`, `nirvana_rest_api`)

> **Note**: This project uses `dbmate` rather than Alembic, which is more commonly seen with SQLAlchemy projects. This choice provides language-agnostic migration management across our polyglot codebase.

## Overview

The project uses a **database-first** approach with the following stack:
- **Database**: PostgreSQL (via Supabase). Supabase Cloud in production, using Docker Compose for local development.
- **Migrations**: `dbmate` for SQL schema migrations
- **ORM**: SQLAlchemy 2.0 with auto-generated models via `sqlacodegen`
- **Validation**: CI enforces that ORM models match the current schema

## Environment Setup

### Required Environment Files

Before running any database operations, ensure you have the proper environment files:

```bash
# Copy the example environment file (if you haven't already)
cp supabase.example.env .env

# Ensure these key variables are set:
# POSTGRES_PASSWORD=your_password_here
# For migrations (in migrator container):
# DATABASE_URL=postgres://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?sslmode=disable
# For application (in claims_agent):
# SUPABASE_DB_URL=postgresql+psycopg://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?sslmode=disable
```

## Critical Workflow: Schema Changes

**⚠️ IMPORTANT**: The CI pipeline will fail if the generated ORM code doesn't match the database schema. Always follow this 3-step workflow:

### Step 1: Create a New Migration

```bash
# Ensure you're in the llm_agents directory
cd src/llm_agents

# Get a shell into the migrator container
docker compose run --rm migrator new your_migration_name

# This creates: src/llm_agents/db/migrations/TIMESTAMP_your_migration_name.sql
```

Example migration file structure:
```sql
-- migrate:up
CREATE TABLE public.new_table (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now()
);

-- migrate:down
DROP TABLE IF EXISTS public.new_table;
```

### Step 2: Apply Migration to Local Database

```bash
# Apply the migration (starts all required services automatically)
docker compose up migrator

# This runs: dbmate up
```

### Step 3: Regenerate ORM Models

```bash
# Regenerate ORM models to match the new schema
uv run poe generate-orm
```

**🚨 Both the migration file AND the generated ORM file must be committed together, or CI will fail.**

## Working with UV Workspace

Since this is a uv workspace project, most commands should be run from the root `llm_agents` directory:

```bash
# From the root llm_agents directory:

# Run the ORM generation task
uv run poe generate-orm

# Run tests
uv run poe test

# Start development services
docker compose --profile "*" up -d
```

## Migration Management

### Migration File Structure

Migrations live in `src/llm_agents/db/migrations/` with timestamp-based naming:
```
20250620095039_create_initial_tables.sql
20250621120000_add_new_column.sql
20250622090000_create_new_table.sql
```

### Creating Migrations

```bash
# General pattern (from llm_agents directory)
docker compose run --rm migrator new descriptive_migration_name

# Examples
docker compose run --rm migrator new add_user_preferences_table
docker compose run --rm migrator new add_status_index_to_claims
docker compose run --rm migrator new remove_deprecated_columns
```

### Migration Best Practices

1. **Descriptive Names**: Use clear, action-oriented names
   - ✅ `add_user_preferences_table`
   - ✅ `create_index_on_claim_status`
   - ❌ `update_schema`
   - ❌ `changes`

2. **Include Rollback**: Always provide `-- migrate:down` sections
   ```sql
   -- migrate:up
   ALTER TABLE claims ADD COLUMN priority integer DEFAULT 1;
   
   -- migrate:down
   ALTER TABLE claims DROP COLUMN IF EXISTS priority;
   ```

3. **Non-Breaking Changes**: Design migrations to be backwards compatible when possible
   - Add columns with defaults instead of NOT NULL
   - Create indexes concurrently in production
   - Use `IF NOT EXISTS` and `IF EXISTS` appropriately

### Migration Commands

```bash
# Apply all pending migrations
docker compose up migrator

# Check migration status (requires shell access)
docker compose run --rm migrator status

# Rollback last migration (use with caution)
docker compose run --rm migrator rollback

# Create and apply in development
docker compose run --rm migrator new my_migration
# Edit the migration file
docker compose up migrator
uv run poe generate-orm
```

## ORM Code Generation

### How It Works

The ORM generation uses `sqlacodegen` to introspect the live database and generate SQLAlchemy 2.0 models:

```bash
# The command that runs (from the claims_agent package directory)
sqlacodegen ${SUPABASE_DB_URL} --outfile src/claims_agent/db/orm/generated.py
```

### Generated File Location

The generated file is located at:
```
src/llm_agents/claims_agent/src/claims_agent/db/orm/generated.py
```

### Generated File Structure

The generated file contains:
- `Base` declarative base class using SQLAlchemy 2.0 syntax
- Model classes for each table (e.g., `LegacyCoverageDeterminations`)
- Proper type hints with `Mapped[]` annotations
- PostgreSQL-specific types (JSONB, UUID, etc.)

Example generated model:
```python
class LegacyCoverageDeterminations(Base):
    __tablename__ = "legacy_coverage_determinations"
    
    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, server_default=text("gen_random_uuid()")
    )
    claim_id: Mapped[str] = mapped_column(Text)
    status: Mapped[str] = mapped_column(Text)
    # ... other fields
```

### When to Regenerate

**Always regenerate after**:
- Creating new tables
- Adding/removing columns
- Changing column types
- Adding/removing constraints
- Any schema structural changes

**CI Check**: The pipeline runs `uv run poe generate-orm` and fails if it detects changes, ensuring the ORM stays in sync.

## Development Workflow

### Starting Fresh

```bash
# 1. Ensure you're in the llm_agents directory
cd src/llm_agents

# 2. Start all infrastructure services
docker compose --profile infra up -d

# 3. Wait for services to be healthy, then apply migrations
docker compose up migrator

# 4. Generate ORM models
uv run poe generate-orm

# 5. Verify everything is working
uv run poe test

# 6. Start application services (optional)
docker compose --profile apps up -d
```

### Development Cycle

```bash
# 1. Make schema changes
docker compose run --rm migrator new add_feature_table

# 2. Edit the migration file
# src/llm_agents/db/migrations/TIMESTAMP_add_feature_table.sql

# 3. Apply migration
docker compose up migrator

# 4. Regenerate ORM (this will update claims_agent/src/claims_agent/db/orm/generated.py)
uv run poe generate-orm

# 5. Update your application code to use new models

# 6. Test your changes
uv run poe test

# 7. Commit both files together
git add db/migrations/TIMESTAMP_add_feature_table.sql
git add claims_agent/src/claims_agent/db/orm/generated.py
git commit -m "feat(db): add feature table"
```

## CI/CD Integration

### Automated ORM Sync Checking

The CI pipeline automatically verifies that ORM models are in sync with the database schema by:

1. Running `uv run poe generate-orm` 
2. Checking if this produces any changes to `claims_agent/src/claims_agent/db/orm/generated.py`
3. Failing the build if changes are detected

This ensures that:
- All schema changes go through proper migrations
- The ORM models always reflect the current database state
- No manual ORM modifications are accidentally committed

### Fixing CI Failures

If you see a "ORM models are out of sync" CI failure:

```bash
# Regenerate ORM models locally
uv run poe generate-orm

# Check what changed
git diff claims_agent/src/claims_agent/db/orm/generated.py

# If the changes look correct, commit them
git add claims_agent/src/claims_agent/db/orm/generated.py
git commit -m "fix: regenerate ORM models to match current schema"
```

## Advanced Topics

### Database Schema Design

Current tables:
- `legacy_coverage_determinations`: Stores async workflow results and coverage determination cache
- `schema_migrations`: Tracks applied migrations (managed by dbmate)

Design principles:
- Use UUIDs for primary keys (`gen_random_uuid()`)
- Include `created_at` and `updated_at` timestamps
- Use JSONB for flexible data storage
- Enable Row Level Security (RLS) for tables

### Coverage Determination Caching

The `legacy_coverage_determinations` table serves dual purposes:
1. **Request Lifecycle Tracking**: Complete audit trail for all coverage determination requests
2. **Response Caching**: Time-based caching of successful determinations

#### Cache Operations

**Cache Lookup** (`get_latest_successful`):
```sql
SELECT * FROM legacy_coverage_determinations 
WHERE claim_id = ? 
  AND status = 'succeeded' 
  AND updated_at >= (NOW() - INTERVAL '? seconds')
  AND content IS NOT NULL
ORDER BY updated_at DESC 
LIMIT 1;
```

> **Note**: Cache matching is based solely on `claim_id` and TTL. The `as_of_date` parameter (if provided via the API) does not affect cache lookup - cached results are shared across different `as_of_date` values for the same claim.

**Cache Storage**:
- Successful responses are automatically stored with JSON serialization
- `updated_at` timestamp used for TTL calculations
- `content` field contains the full `CoverageDeterminationResponse` as JSONB

#### Cache Management

- **TTL-based expiration**: No explicit cleanup needed - queries filter by age
- **Graceful degradation**: Cache lookup failures don't impact API functionality
- **Observability**: All cache operations logged with detailed context
- **Request correlation**: Each request tracked with UUID for debugging

### Database Environment Variables

```bash
# For migrations (in migrator container)
DATABASE_URL=postgres://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?sslmode=disable

# For application (in claims_agent)
SUPABASE_DB_URL=postgresql+psycopg://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?sslmode=disable
```

## Troubleshooting

### Common Issues

**"Connection refused" when running migrator**:
```bash
# Ensure the database service is healthy first
docker compose ps
docker compose logs db

# If db is not healthy, restart the stack
docker compose down
docker compose --profile infra up -d
```

**"sqlalchemy.exc.NoSuchTableError" in generated ORM**:
```bash
# This usually means migrations weren't applied
docker compose up migrator

# Then regenerate ORM
uv run poe generate-orm
```

**Migration file not being created**:
```bash
# Ensure you're running from the correct directory
cd src/llm_agents

# Check that the migrator service can access the db directory
docker compose run --rm migrator status
```

**ORM generation fails with connection error**:
```bash
# Check your SUPABASE_DB_URL in claims_agent/.env
cat claims_agent/.env | grep SUPABASE_DB_URL

# Ensure the database is accessible
docker compose ps
```

**"ORM models are out of sync" CI failure**:
```bash
# Fix by regenerating locally
uv run poe generate-orm
git add claims_agent/src/claims_agent/db/orm/generated.py
git commit -m "fix: regenerate ORM models"
```

**Migration fails to apply**:
```bash
# Check current migration status
docker compose run --rm migrator status

# View migration logs
docker compose logs migrator

# Rollback if needed (use with caution)
docker compose run --rm migrator rollback
```

**Database connection issues**:
```bash
# Ensure database is healthy
docker compose ps
docker compose logs db

# Check if migrations container can connect
docker compose run --rm migrator status
```

**Permission issues**:
```bash
# Ensure .env files have correct database passwords
cat supabase.example.env  # Check default password
docker compose down
docker compose up db -d  # Restart database
```

## Production Considerations

### Migration Deployment

1. **Run migrations before deploying app code**
2. **Use transactions for complex migrations**
3. **Test migrations on production-like data**
4. **Have rollback plan ready**
5. **Monitor database performance during migration**

### Schema Changes in Production

- **Additive changes** (new columns, tables) are safer
- **Destructive changes** (dropping columns) need careful coordination
- **Large data migrations** may need to be split into smaller chunks
- **Index creation** should use `CONCURRENTLY` to avoid locks

Example production-safe migration:
```sql
-- migrate:up
-- Add new column with default (safe)
ALTER TABLE legacy_coverage_determinations 
ADD COLUMN priority integer DEFAULT 1;

-- Create index concurrently (safe)
CREATE INDEX CONCURRENTLY idx_coverage_determinations_priority 
ON legacy_coverage_determinations(priority);

-- migrate:down
DROP INDEX IF EXISTS idx_coverage_determinations_priority;
ALTER TABLE legacy_coverage_determinations DROP COLUMN IF EXISTS priority;
```

## External Documentation

- [**dbmate Documentation**](https://github.com/amacneil/dbmate) - Migration tool reference
- [**sqlacodegen Documentation**](https://github.com/agronholm/sqlacodegen) - ORM code generation
- [**Supabase Documentation**](https://supabase.com/docs) - Database platform
- [**SQLAlchemy 2.0 Documentation**](https://docs.sqlalchemy.org/en/20/) - ORM framework
- [**uv Documentation**](https://docs.astral.sh/uv/) - Python package manager 