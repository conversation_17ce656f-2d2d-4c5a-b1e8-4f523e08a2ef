# Development Guide

This guide covers local development workflows, task runner commands, and best practices for the LLM Agents monorepo.

## Workspace Overview

This repository is a `uv` workspace containing multiple Python packages (`claims_agent`, `mcp_servers`, `nirvana_rest_api`, etc.), each with its own `src/<package_name>` directory for source code and a `pyproject.toml` file.

The root directory contains:
- `pyproject.toml`: Defines the `uv` workspace, shared development dependencies, and global `poethepoet` tasks.
- `uv.lock`: Shared lockfile for all Python dependencies.
- `Procfile`: For running services locally with `Overmind` (non-Docker).
- `docker-compose.yml`: For running services with Docker Compose.
- `scripts/setup.sh`: A script to help new developers set up their environment.

## Initial Setup

1.  **Prerequisites**:
    *   Python 3.11+
    *   Git
    *   `uv` (Python package/project manager)
    *   `Overmind` (for local non-Docker development)
    *   `Docker` and `Docker Compose v2+` (for Docker-based development)

2.  **Clone the Repository**:
    ```bash
    git clone <repository_url>
    cd llm_agents
    ```

3.  **Run the Setup Script**:
    This is the recommended first step. It will help install `uv`, `Overmind`, set up example `.env` files, and install Python dependencies.
    ```bash
    chmod +x scripts/setup.sh
    ./scripts/setup.sh
    ```
    Follow the prompts. This script will guide you through ensuring necessary tools are present and installing Python packages with `uv sync`.

4.  **Configure Environment Variables**:
    *   After running the setup script, `.env` files will be created in `claims_agent/` and `mcp_servers/` (copied from `*.env.example` files).
    *   Review these `.env` files and fill in any required secrets (e.g., API keys) or adjust configurations for your local setup.
    *   A root `.env` file (e.g., for `COMPOSE_BAKE=true`) may also be present.

## Task Runner: `poethepoet`

We use `poethepoet` for standardizing common development tasks. Tasks are defined in the root `pyproject.toml` and can also be in package-specific `pyproject.toml` files.

Run tasks from the workspace root using `uv run poe <task_name>` (or just `poe <task_name>` if `poe` is in your PATH and you are in the activated `uv` environment).

### Global Tasks (from root `pyproject.toml`)

-   **Format code**: `uv run poe format`
-   **Lint code**: `uv run poe lint`
-   **Fix lint issues**: `uv run poe lint-fix`
-   **Type check**: `uv run poe typecheck`
-   **Run tests**: `uv run poe test`
-   **Run all checks**: `uv run poe check` (formats, lints, typechecks)
-   **Fix all style issues**: `uv run poe fix-all` (formats, fixes lint)
-   **Clean build artifacts**: `uv run poe clean`
-   **Start services locally (non-Docker)**: `uv run poe start-dev` (uses Overmind and Procfile)
-   **Start services with Docker Compose**: `uv run poe compose-up`
-   **View Docker Compose logs**: `uv run poe compose-logs`
-   **Stop Docker Compose services**: `uv run poe compose-down`
-   **Start a specific service with Docker Compose**: `uv run poe compose-start-service --service <service_name>`
-   **Build a standalone Docker image for a package**: `uv run poe build-image --package <package_name>`

Refer to the `[tool.poe.tasks]` section in the root `pyproject.toml` for the full list and details.
Package-specific tasks (e.g., a `start` task for a Uvicorn server) are defined in their respective `pyproject.toml` files (e.g., `claims_agent/pyproject.toml`).

## Running Services Locally

There are two primary ways to run the services (`claims_agent`, `mcp_servers`, and dependencies like Redis) locally:

### 1. Using Overmind (Non-Docker)

This method runs the Python applications directly on your host using your local `uv` managed environment. It uses `Overmind` to manage processes defined in the `Procfile`.

-   **Prerequisites**: `uv` and `Overmind` installed and on your PATH. Python dependencies synced (`uv sync`). Correct `.env` files in `claims_agent/` and `mcp_servers/`.
-   **Command**:
    ```bash
    uv run poe start-dev
    ```
-   `Overmind` will start each service (e.g., `claims_agent`, `mcp_servers`). Logs will be interleaved in your terminal. Press `Ctrl+C` to stop.
-   This method relies on a local Redis instance if your services require it. The `Procfile` does not manage Redis.

### 2. Using Docker Compose

This method uses Docker to build and run containerized versions of the services, including a Redis container. It is generally preferred for a more isolated and reproducible environment.

-   **Prerequisites**: Docker and Docker Compose v2+ installed and running.
-   **Commands**:
    *   **Start all services (builds images if needed, runs in detached mode)**:
        ```bash
        uv run poe compose-up
        ```
    *   **View logs from all services**:
        ```bash
        uv run poe compose-logs
        ```
        (Use `Ctrl+C` to stop following logs. Services continue running.)
    *   **Start a specific service (e.g., `claims_agent`)**:
        ```bash
        uv run poe compose-start-service --service claims_agent
        ```
        This will also start its dependencies (like `redis`) if not already running.
    *   **Stop and remove all services, networks, etc.**:
        ```bash
        uv run poe compose-down
        ```
-   Environment variables for Docker containers are sourced from the `.env` files in each package directory (e.g., `claims_agent/.env`) via the `env_file` directive in `docker-compose.yml`, and can be overridden by the `environment` section in `docker-compose.yml`.

## IDE & Debugging

-   **VSCode**: Recommended. Ensure the Python extension is installed. Select the project's `uv` virtual environment (`.venv/bin/python`) as your interpreter.
    *   **Debugging with Docker Compose**: VSCode can attach to running Docker containers. Refer to VSCode Docker extension documentation.
    *   **Debugging with Overmind**: Since Overmind runs local Python processes, you can configure your IDE's debugger to attach to the Uvicorn process started for `claims_agent` or `mcp_servers`. The `poe start` tasks within each package typically run `uvicorn ... --reload`. You might need to adjust these tasks or launch Uvicorn directly with debug flags for your IDE.
-   **Logging**: Application logging is primarily configured via environment variables (e.g., `LOG_LEVEL`) defined in each service's config and set in their `.env` files.

## Dependency Management

-   **Install all dependencies from `uv.lock`**:
    ```bash
    uv sync
    ```
-   **Add a dependency to a specific package** (e.g., adding `requests` to `claims_agent`):
    ```bash
    uv add requests --package claims_agent
    ```
-   **Add a shared development dependency** (to the root `pyproject.toml`):
    ```bash
    uv add black --dev
    ```
-   Always commit changes to `pyproject.toml` files and the `uv.lock` file after managing dependencies.

## Code Quality

-   **Formatting**: `ruff format` (via `uv run poe format`)
-   **Linting**: `ruff check` (via `uv run poe lint`)
-   **Type Checking**: `mypy` (via `uv run poe typecheck`)

It's recommended to configure your IDE to use these tools for on-the-fly feedback and auto-formatting on save.

## Project Structure

```
claims_agent/
├── api/                # FastAPI routers and HTTP handlers
├── agents/             # AI agent implementations and manager
├── config.py           # Application settings and environment variables
├── di/                 # Dependency injection modules
├── models/             # Pydantic data models
├── main.py             # Application entry point for Uvicorn
├── scripts/            # Utility scripts (if any)
├── tests/              # Pytest test suite
└── Taskfile.yml        # Task runner configuration
```

## Working with the Agent

- **Core Logic**: The primary agent logic resides in `src/claims_agent/agents/mcp_agent.py`. This class orchestrates interactions using several specialized components:
    - `MCPClientManager`: Manages MCP client instances.
    - `MCPToolService`: Fetches tools from the MCP client.
    - `AgentExecutorFactory`: Creates LangChain `AgentExecutor` instances.
    - `prompts.py`: Contains prompt templates.
    - `response_parser.py`: Handles LLM response parsing.
    - `agent_logging_utils.py`: Provides logging helpers.
  Refer to `docs/architecture.md` for a detailed breakdown of these components.
- **Initialization**: Agent resources initialize at application startup via lifespan context, managed by `AgentManager`.
- **Lifecycle**: `AgentManager.initialize()` and `.shutdown()` in `src/claims_agent/agents/manager.py` handle the overall agent lifecycle.
- **Customization/Extension**: To modify core agent behavior, you'll typically start by looking at `MCPAgent` and its constituent services/factories within the `src/claims_agent/agents/` directory. If adding new agent implementations, ensure they conform to `AgentProtocol` and update DI bindings in `AgentModule` if necessary.

## Coverage Determination Caching

The Claims Agent implements intelligent caching for coverage determinations to improve performance and reduce redundant processing.

### Configuration

Coverage determination caching can be configured via environment variables in `claims_agent/.env`:

```bash
# Enable/disable caching globally
COVERAGE_DETERMINATION_CACHE_ENABLED=true

# Default TTL for cache entries (in seconds)
COVERAGE_DETERMINATION_TTL_SECONDS=604800  # 7 days
```

### API Usage Examples

#### Regular Request (uses cache if available)
```bash
curl "http://localhost:8001/api/claims/CLAIM123/coverage/determination" \
  -H "Authorization: Bearer your-token"

# Response includes cache headers:
# X-Cache-Status: MISS (first request) or HIT (subsequent requests)
# X-Cache-TTL: 86400
```

#### Force Refresh (bypasses cache)
```bash
curl "http://localhost:8001/api/claims/CLAIM123/coverage/determination?force_refresh=true" \
  -H "Authorization: Bearer your-token"

# Response headers:
# X-Cache-Status: BYPASS
# X-Cache-TTL: 86400
```

#### Custom TTL (1 hour)
```bash
curl "http://localhost:8001/api/claims/CLAIM123/coverage/determination?ttl_seconds=3600" \
  -H "Authorization: Bearer your-token"

# Response headers:
# X-Cache-Status: MISS or HIT
# X-Cache-TTL: 3600
```

#### Disable Caching for Single Request
```bash
curl "http://localhost:8001/api/claims/CLAIM123/coverage/determination?ttl_seconds=0" \
  -H "Authorization: Bearer your-token"

# Response headers:
# X-Cache-Status: BYPASS
# X-Cache-TTL: 0
```

### Cache Headers

All coverage determination responses include these headers for observability:

- **X-Cache-Status**: `HIT` (cache hit), `MISS` (cache miss), `BYPASS` (cache bypassed), `ERROR` (cache or validation error)
- **X-Cache-TTL**: Effective TTL in seconds for the request

### Cache Behavior Notes

- **Cache Key**: Caching is based solely on `claim_id` and TTL window. Other API parameters like `as_of_date` do not affect cache matching.
- **Shared Cache**: Requests with different `as_of_date` values for the same claim will share cache entries.
- **Cache Bypass**: Use `force_refresh=true` or `ttl_seconds=0` when `as_of_date` variations matter for your use case.

### Development Tips

- **Cache Performance**: Monitor cache hit rates via `X-Cache-Status` header
- **Testing**: Use `force_refresh=true` for testing without cache interference
- **Debugging**: Set `LOG_LEVEL=DEBUG` to see detailed cache operation logs
- **Database**: Cache entries are stored in `legacy_coverage_determinations` table with status tracking

## Writing Tests

- Place unit tests in `tests/` matching module structure.
- Use `conftest.py` for shared fixtures (e.g., `agent_mock`, `settings`).
- Aim for high coverage on service and agent logic. 