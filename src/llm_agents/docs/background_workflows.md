# Background Workflows

This guide covers developing, debugging, and managing background workflows and activities in the LLM Agents project using [Temporal](https://temporal.io/).

> **Prerequisites**: This guide assumes familiarity with the [project architecture](architecture.md) and [development setup](development.md). For database operations, see [persistence](persistence.md).

## Quick Start

For the impatient developer:

```bash
# 1. Start all services (includes Temporal + worker)
docker compose --profile "*" up -d

# 2. Test the worker is running
docker compose exec claims_agent_worker uv run --package claims_agent python -m claims_agent.scripts.submit_background_job log-message "Hello World"

# 3. View workflows in Temporal UI
open http://localhost:8233
```

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Quick Start](#quick-start)
- [Worker Configuration](#worker-configuration)
- [Workflow Development](#workflow-development)
- [Activity Development](#activity-development)
- [Testing Workflows](#testing-workflows)
- [Debugging and Monitoring](#debugging-and-monitoring)
- [Production Considerations](#production-considerations)
- [Advanced Patterns](#advanced-patterns)

## Architecture Overview

The project uses Temporal for reliable background job processing with the following components:

```
Client Request (HTTP/CLI) → Database Record → Temporal Workflow → Activities → Database Update
                                           ↓
                                    Worker Polls Queue → Executes Activities
```

Current workflows:
- `SimpleLogWorkflow`: Test/demo workflow for verification
- `CoverageDeterminationWorkflow`: Main async coverage determination

## Worker Configuration

### Environment Variables

Critical worker configuration in `claims_agent/.env`:

```bash
# REQUIRED - Worker won't start without this
TEMPORAL_WORKER_TASK_QUEUE=default

# Connection settings (auto-configured in Docker Compose)
TEMPORAL_ADDRESS=temporal:7233  # localhost:7233 for local dev
TEMPORAL_NAMESPACE=default

# Database connection (see persistence.md for details)
SUPABASE_DB_URL=postgresql+psycopg://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?sslmode=disable

# Production settings (Temporal Cloud)
TEMPORAL_USE_TLS=false
TEMPORAL_API_KEY=""
```

> **Note**: When using Docker Compose, `TEMPORAL_ADDRESS` is automatically set to `temporal:7233` via environment overrides. The database URL references the Supabase PostgreSQL instance - see [persistence.md](persistence.md) for complete database setup.

### Worker Startup

```bash
# Start worker with full stack (recommended)
docker compose --profile "*" up -d

# Or start just the worker (requires temporal service running)
docker compose up claims_agent_worker

# Or start worker locally (requires local Temporal server)
cd claims_agent
uv run python -m claims_agent.background.worker
```

The worker registers:
- **Workflows**: `CoverageDeterminationWorkflow`, `SimpleLogWorkflow`
- **Activities**: 
  - `CoverageDeterminationActivities.set_in_progress`
  - `CoverageDeterminationActivities.run_determine_coverage`
  - `CoverageDeterminationActivities.set_succeeded`
  - `CoverageDeterminationActivities.set_failed`
  - `get_server_timestamp`

> **Service Dependencies**: The worker depends on both the Temporal server and the database. Use the `--profile "*"` flag to start all required services automatically.

## Workflow Development

### Workflow Structure

Workflows are defined in `src/claims_agent/background/workflows/`:

```python
@workflow.defn
class CoverageDeterminationWorkflow:
    @workflow.run
    async def run(self, request: CoverageDeterminationRequest) -> CoverageDeterminationResult:
        # Workflow orchestration logic
        pass
```

### Key Workflow Patterns

**1. Error Handling with Try/Finally**:
```python
try:
    # Main workflow logic
    result = await workflow.execute_activity(...)
    await workflow.execute_activity("set_succeeded", ...)
    return result
except (ActivityError, ApplicationError) as e:
    # Always update status on failure
    await workflow.execute_activity("set_failed", ...)
    raise  # Re-raise to mark workflow as failed
```

**2. Different Retry Policies**:
```python
# Long-running activities (LLM calls)
long_retry_policy = RetryPolicy(
    maximum_attempts=3,
    initial_interval=timedelta(seconds=10),
    backoff_coefficient=2.0,
)

# Quick activities (database updates)
short_retry_policy = RetryPolicy(maximum_attempts=3)
```

**3. Activity Execution**:
```python
result = await workflow.execute_activity(
    "ActivityClass.method_name",
    args=[request],
    start_to_close_timeout=timedelta(minutes=10),
    retry_policy=long_retry_policy,
)
```

### Activity Development

Activities are methods in classes registered with the worker:

```python
class CoverageDeterminationActivities:
    def __init__(self, agent: AgentProtocol, repository: RepositoryProtocol):
        self._agent = agent
        self._repository = repository

    @activity.defn(name="CoverageDeterminationActivities.run_determine_coverage")
    async def run_determine_coverage(self, request: CoverageDeterminationRequest) -> Response:
        # Activity implementation
        return await self._agent.determine_coverage(request.claim_id, request.authorization)
```

**Activity Best Practices**:
- Activities should be **idempotent** (safe to retry)
- Keep activities focused on single responsibilities
- Use dependency injection for testability
- Include proper error handling and logging
- **Database operations**: Use transactions for consistency (see [Supabase Integration](#supabase-integration))

## Supabase Integration

Activities often interact with the database through repository patterns. Key considerations:

### Repository Pattern
```python
class CoverageDeterminationActivities:
    def __init__(
        self,
        agent: AgentProtocol,
        request_repository: CoverageDeterminationRequestRepositoryProtocol,
    ):
        self._agent = agent
        self._request_repository = request_repository

    @activity.defn
    async def set_in_progress(self, request_id: uuid.UUID) -> None:
        # Repository handles database connection and transaction
        await self._request_repository.set_in_progress(
            request_id, datetime.datetime.now(datetime.timezone.utc)
        )
```

### Transaction Safety
When multiple database operations are needed, ensure atomicity:

```python
@activity.defn
async def complex_database_operation(self, data: ComplexData) -> None:
    async with self._repository.transaction():
        await self._repository.create_record(data.part1)
        await self._repository.update_related(data.part2)
        # Both operations succeed or both fail
```

### Database Connection Configuration
See [persistence.md](persistence.md) for complete database setup and connection management.

## Development Workflow

### 1. Adding a New Workflow

**Step 1**: Create workflow file in `src/claims_agent/background/workflows/`:

```python
# my_new_workflow.py
from temporalio import workflow, activity
from datetime import timedelta

@workflow.defn
class MyNewWorkflow:
    @workflow.run
    async def run(self, input_data: str) -> str:
        result = await workflow.execute_activity(
            "MyNewActivities.process_data",
            args=[input_data],
            start_to_close_timeout=timedelta(minutes=5),
        )
        return result

class MyNewActivities:
    @activity.defn(name="MyNewActivities.process_data")
    async def process_data(self, data: str) -> str:
        # Your activity logic here
        return f"Processed: {data}"
```

**Step 2**: Register in worker (`src/claims_agent/background/worker.py`):

```python
from claims_agent.background.workflows.my_new_workflow import MyNewWorkflow, MyNewActivities

# In main() function
my_activities = injector.get(MyNewActivities)  # If using DI

worker = Worker(
    temporal_client,
    task_queue=settings.TEMPORAL_WORKER_TASK_QUEUE,
    workflows=[CoverageDeterminationWorkflow, SimpleLogWorkflow, MyNewWorkflow],
    activities=[
        # ... existing activities
        my_activities.process_data,
    ],
)
```

**Step 3**: Create client script to start workflow:

```python
# In submit_background_job.py or new script
@cli.command(name="my-new-workflow")
@click.argument("input-data", type=str)
@pass_injector
@coro
async def my_new_workflow(injector: injector.Injector, input_data: str) -> None:
    client = injector.get(TemporalClient)
    settings = injector.get(Settings)
    
    handle = await client.start_workflow(
        MyNewWorkflow.run,
        input_data,
        id=f"my-workflow-{uuid.uuid4()}",
        task_queue=settings.TEMPORAL_WORKER_TASK_QUEUE,
    )
    result = await handle.result()
    logger.info(f"Result: {result}")
```

### 2. Testing Workflows

The project includes comprehensive test patterns for workflows and activities:

**Unit Testing Activities**:
```python
# test_activities.py
import pytest
from unittest.mock import AsyncMock
from claims_agent.background.workflows.my_new_workflow import MyNewActivities

@pytest.mark.asyncio
async def test_process_data():
    # Mock dependencies
    mock_repository = AsyncMock()
    activities = MyNewActivities(repository=mock_repository)
    
    result = await activities.process_data("test input")
    
    assert result == "Processed: test input"
    mock_repository.save.assert_called_once()
```

**Workflow Testing with Temporal Test Framework**:
```python
# test_workflow.py
import pytest
import uuid
from temporalio.testing import WorkflowEnvironment
from temporalio.worker import Worker
from claims_agent.background.workflows.my_new_workflow import (
    MyNewWorkflow, 
    MyNewActivities,
    MyRequest
)

@pytest.mark.asyncio
async def test_workflow_end_to_end():
    """Test complete workflow execution in isolated environment."""
    
    async with WorkflowEnvironment() as env:
        # Create mock activities
        activities = MyNewActivities(repository=MockRepository())
        
        # Setup worker with workflow and activities
        async with Worker(
            env.client,
            task_queue="test-queue",
            workflows=[MyNewWorkflow],
            activities=[activities.process_data]
        ):
            # Execute workflow
            result = await env.client.execute_workflow(
                MyNewWorkflow.run,
                MyRequest(data="test"),
                id=f"test-{uuid.uuid4()}",
                task_queue="test-queue",
            )
            
            assert result.content == "Processed: test"
```

**Real Integration Testing** (requires running services):
```python
@pytest.mark.integration
@pytest.mark.asyncio
async def test_coverage_determination_integration():
    """Test with real Temporal server and database."""
    # This test runs against the actual Docker Compose services
    # Run with: docker compose --profile "*" up -d && uv run poe test -m integration
    pass
```

**Running Tests**:
```bash
# Unit tests only (fast)
uv run poe test -m "not integration"

# Integration tests (requires services)
docker compose --profile "*" up -d
uv run poe test -m integration

# All tests
uv run poe test
```

### 3. Running and Testing

```bash
# Start all services
docker compose --profile "*" up

# Test simple workflow
docker compose exec claims_agent_worker uv run --package claims_agent python -m claims_agent.scripts.submit_background_job log-message "Hello World"

# Test coverage determination
docker compose exec claims_agent_worker uv run --package claims_agent python -m claims_agent.scripts.submit_background_job coverage-determination CLAIM123 your-auth-token
```

## Debugging and Monitoring

### Local Development Debugging

**1. Temporal Web UI**:
- Access at: http://localhost:8233
- Shows workflow executions, status, and history
- View individual activity results and failures

**2. Worker Logs**:
```bash
# View worker logs
docker compose logs -f claims_agent_worker

# View all temporal-related logs
docker compose logs -f temporal claims_agent_worker
```

**3. Enable Verbose Logging**:
```bash
# In claims_agent/.env
LOG_LEVEL=DEBUG
AGENT_VERBOSE=true

# For temporal-specific debugging
TEMPORAL_LOG_LEVEL=DEBUG
```

**4. Database State Inspection**:
```bash
# Connect to database (see persistence.md)
docker compose exec db psql -U postgres

# Check workflow status
SELECT status, COUNT(*) FROM legacy_coverage_determinations GROUP BY status;

# Find recent workflows
SELECT * FROM legacy_coverage_determinations 
WHERE created_at > NOW() - INTERVAL '1 hour' 
ORDER BY created_at DESC;
```

### Debugging Common Issues

**Worker Not Starting**:
```bash
# Check if TEMPORAL_WORKER_TASK_QUEUE is set
docker compose exec claims_agent_worker env | grep TEMPORAL

# Check worker startup logs
docker compose logs claims_agent_worker
```

**Workflow Timeouts**:
```python
# Increase timeouts for long-running activities
start_to_close_timeout=timedelta(minutes=15)  # Instead of 10

# Or use heartbeats for very long activities
@activity.defn
async def long_running_activity():
    for i in range(100):
        activity.heartbeat(f"Progress: {i}/100")
        await asyncio.sleep(1)
```

**Activity Failures**:
```python
# Add proper error handling in activities
@activity.defn(name="MyActivities.risky_operation")
async def risky_operation(self, data: str) -> str:
    try:
        result = await some_external_service(data)
        return result
    except ExternalServiceError as e:
        activity.logger.error(f"External service failed: {e}")
        # Re-raise to trigger retry
        raise
    except ValidationError as e:
        activity.logger.error(f"Invalid data: {e}")
        # Don't retry validation errors
        raise ApplicationError(f"Invalid input: {e}")
```

## Production Considerations

### Temporal Cloud Configuration

For production deployment, use Temporal Cloud:

```bash
# In production claims_agent/.env
TEMPORAL_USE_TLS=true
TEMPORAL_API_KEY=your_temporal_cloud_key
TEMPORAL_ADDRESS=your_namespace.tmprl.cloud:7233
TEMPORAL_NAMESPACE=your_namespace
```

### Worker Scaling

```bash
# Scale workers horizontally
docker compose up claims_agent_worker --scale claims_agent_worker=5

# Or use different task queues for different priorities
TEMPORAL_WORKER_TASK_QUEUE=high-priority-coverage-determination
TEMPORAL_WORKER_TASK_QUEUE=low-priority-maintenance
```

### Monitoring and Alerting

**Workflow Metrics**:
- Monitor workflow success/failure rates
- Track activity execution times  
- Alert on workflow timeouts
- Monitor task queue depth

**Database State Tracking**:
```sql
-- Check workflow status distribution
SELECT status, COUNT(*) 
FROM legacy_coverage_determinations 
GROUP BY status;

-- Find stuck workflows
SELECT * FROM legacy_coverage_determinations 
WHERE status = 'in_progress' 
AND created_at < NOW() - INTERVAL '1 hour';

-- Monitor workflow duration
SELECT 
    AVG(EXTRACT(EPOCH FROM (completed_at - started_at))) as avg_duration_seconds
FROM legacy_coverage_determinations 
WHERE status = 'succeeded' 
AND completed_at > NOW() - INTERVAL '24 hours';
```

### Security Considerations

- Use environment-specific task queues
- Rotate Temporal Cloud API keys regularly
- Encrypt sensitive data in workflow/activity payloads
- Use database connection pooling (see [persistence.md](persistence.md))

## Advanced Patterns

### Workflow Versioning

When updating workflow logic, use versioning to handle in-flight workflows:

```python
@workflow.defn
class CoverageDeterminationWorkflow:
    @workflow.run
    async def run(self, request: CoverageDeterminationRequest) -> CoverageDeterminationResult:
        # Check workflow version
        version = workflow.get_version("coverage_determination_v2", 1, 2)
        
        if version == 1:
            # Old logic for existing workflows
            return await self._old_logic(request)
        else:
            # New logic for new workflows
            return await self._new_logic(request)
```

### Child Workflows

For complex workflows, break into smaller child workflows:

```python
@workflow.run
async def parent_workflow(self, request: ComplexRequest) -> ComplexResult:
    # Start child workflows
    child1_handle = await workflow.start_child_workflow(
        SubWorkflow1.run,
        request.part1,
        id=f"child1-{workflow.info().workflow_id}",
    )
    
    child2_handle = await workflow.start_child_workflow(
        SubWorkflow2.run,
        request.part2,
        id=f"child2-{workflow.info().workflow_id}",
    )
    
    # Wait for both to complete
    result1 = await child1_handle
    result2 = await child2_handle
    
    return ComplexResult(part1=result1, part2=result2)
```

### Signals and Queries

For interactive workflows:

```python
@workflow.defn
class InteractiveWorkflow:
    def __init__(self):
        self._status = "running"
        self._data = []

    @workflow.signal
    async def add_data(self, data: str) -> None:
        self._data.append(data)

    @workflow.query
    def get_status(self) -> str:
        return self._status

    @workflow.run
    async def run(self) -> str:
        # Wait for signals, process data
        await workflow.wait_condition(lambda: len(self._data) >= 5)
        return f"Processed {len(self._data)} items"
```

## Task Queue Management

### Development Task Queues

Use different task queues for different environments:

```bash
# Development
TEMPORAL_WORKER_TASK_QUEUE=coverage-determination-dev

# Staging
TEMPORAL_WORKER_TASK_QUEUE=coverage-determination-staging

# Production
TEMPORAL_WORKER_TASK_QUEUE=coverage-determination-prod
```

### Worker Scaling

Multiple workers can share the same task queue:

```bash
# Start multiple worker instances
docker compose up claims_agent_worker --scale claims_agent_worker=3
```

Workers will automatically distribute workload.

## Error Handling Strategies

### Retry Policies

Choose appropriate retry policies based on error types:

```python
# For transient network errors - exponential backoff
network_retry = RetryPolicy(
    maximum_attempts=5,
    initial_interval=timedelta(seconds=1),
    backoff_coefficient=2.0,
    maximum_interval=timedelta(seconds=30),
)

# For rate limiting - linear backoff
rate_limit_retry = RetryPolicy(
    maximum_attempts=10,
    initial_interval=timedelta(seconds=5),
    backoff_coefficient=1.0,  # Linear
)

# For validation errors - no retry
no_retry = RetryPolicy(maximum_attempts=1)
```

### Dead Letter Queue Pattern

For workflows that consistently fail:

```python
@workflow.run
async def robust_workflow(self, request: Request) -> Result:
    try:
        return await self._main_logic(request)
    except Exception as e:
        # After all retries failed, send to dead letter queue
        await workflow.execute_activity(
            "send_to_dead_letter_queue",
            args=[request, str(e)],
            retry_policy=RetryPolicy(maximum_attempts=1),
        )
        raise
```

## Configuration Reference

### Environment Variables

```bash
# Worker Configuration
TEMPORAL_WORKER_TASK_QUEUE=coverage-determination-dev  # REQUIRED
TEMPORAL_ADDRESS=localhost:7233
TEMPORAL_NAMESPACE=default

# Production/Cloud Configuration
TEMPORAL_USE_TLS=true
TEMPORAL_API_KEY=your-temporal-cloud-key

# Database (for activities)
SUPABASE_DB_URL=postgresql+psycopg://...

# Logging
LOG_LEVEL=INFO
AGENT_VERBOSE=false
```

### Docker Compose Services

The Temporal stack in `docker-compose.yml`:
- **`temporal`**: Main Temporal server (includes Web UI on port 8233)
- **`temporal_admin`**: Admin tools container for CLI operations
- **`claims_agent_worker`**: Python worker process (background job executor)
- **`claims_agent`**: Main API server (separate from worker)

**Service Dependencies**: `db` → `temporal` → `claims_agent_worker`

**Profiles Available**:
- `--profile temporal`: Just Temporal services
- `--profile apps`: Just application services  
- `--profile "*"`: All services (recommended for development)

## Related Documentation

- **[Architecture Overview](architecture.md)**: Understanding the overall system design
- **[Development Guide](development.md)**: Setting up your development environment
- **[Persistence Guide](persistence.md)**: Database schema, migrations, and ORM patterns
- **[API Documentation](api.md)**: REST API endpoints that trigger workflows

## Troubleshooting Common Issues

| Issue | Solution |
|-------|----------|
| Worker won't start | Check `TEMPORAL_WORKER_TASK_QUEUE` is set in `.env` |
| Database connection failed | Verify `SUPABASE_DB_URL` and ensure `db` service is healthy |
| Workflow timeouts | Increase `start_to_close_timeout` for long-running activities |
| Activity retries failing | Check retry policies and ensure activities are idempotent |
| No workflows visible in UI | Verify worker is registered and polling correct task queue |

For more help, check the [Temporal documentation](https://docs.temporal.io/) or the project's troubleshooting guides. 