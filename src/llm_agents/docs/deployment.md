# Deployment

This guide covers containerization, image building, pushing to a registry, and running services from this monorepo in production environments.

## Docker & Docker Compose

Docker Compose is the primary tool for building and running services, both locally and as a basis for deployment.

### Build Docker Images (via Docker Compose)

To build images for all services defined in `docker-compose.yml`:
```bash
# Ensure COMPOSE_BAKE=true is set in your root .env file or environment
docker-compose build
```
To build an image for a specific service (e.g., `claims_agent`):
```bash
docker-compose build claims_agent
```
The `uv run poe compose-up` task also handles building images automatically if they are outdated.

### Build a Standalone Docker Image (without Docker Compose)
If you need to build a single image outside of the compose context (e.g., for a specific CI step):
```bash
# Builds the Docker image for a specific package
uv run poe build-image --package claims_agent --tag my_custom_tag
```

### Run Containers Locally (for testing deployment)

Using Docker Compose is recommended:
```bash
# Starts all services defined in docker-compose.yml (detached mode)
uv run poe compose-up
```
This will use the `.env` files specified in `docker-compose.yml` for each service.

To run a specific, pre-built image without compose (less common for multi-service local dev):
```bash
# This example assumes an image 'claims_agent:latest' was built
docker run -p 8000:8000 --env-file claims_agent/.env claims_agent:latest
```

## Push to Container Registry

### AWS ECR (Example)

The root `pyproject.toml` includes a `poethepoet` task `build-and-push` for this.

1. **Authenticate to ECR**:
   ```bash
   aws ecr get-login-password --region <your-aws-region> | docker login --username AWS --password-stdin <your-aws-account-id>.dkr.ecr.<your-aws-region>.amazonaws.com
   ```
2. **Build and Push via Poe task**:
   ```bash
   uv run poe build-and-push --package <service_name> --tag <image_tag> --aws_account_id <your-aws-account-id> --aws_region <your-aws-region> --ecr_path <your-ecr-repo-name>
   ```
   Example:
   ```bash
   uv run poe build-and-push --package claims_agent --tag v0.1.0 --ecr_path my-claims-agent-repo
   ```
   This task builds the image for the specified package (e.g., `claims_agent` or `mcp_servers`) and pushes it to your ECR repository.

## Production Considerations

- **Environment Variables**: Ensure all necessary environment variables (API keys, service URLs, `ENVIRONMENT=production`, `LOG_LEVEL`, `UVICORN_WORKERS`, etc.) are securely managed and provided to your containers in the production environment (e.g., via Kubernetes secrets, ECS task definitions).
- **Service URLs**: In production, `CLAIMS_SERVER_URL` and `POLICY_SERVER_URL` within `claims_agent` must point to the deployed `mcp_servers` instance, not localhost or Docker internal names.
- **Logging & Monitoring**: Integrate with log aggregation (e.g., ELK/Stackdriver, AWS CloudWatch Logs) and APM tools.
- **Scaling**: Configure auto-scaling, replica counts, and resource allocations (CPU/memory) in your orchestration layer (e.g., Kubernetes, ECS).
- **Security**: Secure API endpoints, use HTTPS/TLS, manage network policies.
- **OpenAPI Schema**: Ensure OpenAPI schemas are up-to-date for consumer reference. Package-specific poe tasks might exist for schema generation (e.g., `uv run poe -C claims_agent generate-openapi`).
- **CI/CD**: Your pipeline should include steps for:
  - Running checks: `uv run poe check`
  - Running tests: `uv run poe test`
  - Building images: `docker-compose build <service_name>` or using the `build-and-push` poe task.
  - Pushing images to a registry.
  - Deploying to your target environment. 