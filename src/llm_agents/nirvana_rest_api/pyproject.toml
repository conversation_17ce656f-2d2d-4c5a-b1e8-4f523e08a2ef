[project]
name = "nirvana-rest-api"
version = "0.1.0"
description = "Auto-generated client library for the Nirvana REST API."
readme = "README.md"
requires-python = ">=3.11"
license = { text = "Proprietary" }
dependencies = [
    "httpx>=0.25.0",
    "pydantic>=2.0.0",
    "python-dateutil>=2.8.1",
    "typing-extensions>=4.6.1",
]

[tool.uv]
package = true

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/nirvana_rest_api"]
