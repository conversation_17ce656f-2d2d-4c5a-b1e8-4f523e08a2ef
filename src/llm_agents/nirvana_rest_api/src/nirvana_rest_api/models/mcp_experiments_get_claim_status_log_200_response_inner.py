import datetime
from collections.abc import Mapping
from typing import Any, TypeVar
from uuid import UUID

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..models.mcp_experiments_get_claim_status_log_200_response_inner_value import (
    MCPExperimentsGetClaimStatusLog200ResponseInnerValue,
)

T = TypeVar("T", bound="MCPExperimentsGetClaimStatusLog200ResponseInner")


@_attrs_define
class MCPExperimentsGetClaimStatusLog200ResponseInner:
    """Represents a status log item for a claim.

    Attributes:
        id (UUID): Unique identifier for the status log item.
        created_at (datetime.datetime): Timestamp when the status was recorded.
        value (MCPExperimentsGetClaimStatusLog200ResponseInnerValue):
    """

    id: UUID
    created_at: datetime.datetime
    value: MCPExperimentsGetClaimStatusLog200ResponseInnerValue
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        id = str(self.id)

        created_at = self.created_at.isoformat()

        value = self.value.value

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "id": id,
                "createdAt": created_at,
                "value": value,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        id = UUID(d.pop("id"))

        created_at = isoparse(d.pop("createdAt"))

        value = MCPExperimentsGetClaimStatusLog200ResponseInnerValue(d.pop("value"))

        mcp_experiments_get_claim_status_log_200_response_inner = cls(
            id=id,
            created_at=created_at,
            value=value,
        )

        mcp_experiments_get_claim_status_log_200_response_inner.additional_properties = d
        return mcp_experiments_get_claim_status_log_200_response_inner

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
