import datetime
from collections.abc import Mapping
from typing import Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..models.mcp_experiments_get_v0_policies_for_dot200_response_inner_insurance_carrier import (
    MCPExperimentsGetV0PoliciesForDOT200ResponseInnerInsuranceCarrier,
)
from ..models.mcp_experiments_get_v0_policies_for_dot200_response_inner_program_type import (
    MCPExperimentsGetV0PoliciesForDOT200ResponseInnerProgramType,
)
from ..models.mcp_experiments_get_v0_policies_for_dot200_response_inner_status import (
    MCPExperimentsGetV0PoliciesForDOT200ResponseInnerStatus,
)
from ..types import UNSET, Unset

T = TypeVar("T", bound="MCPExperimentsGetV0PoliciesForDOT200ResponseInner")


@_attrs_define
class MCPExperimentsGetV0PoliciesForDOT200ResponseInner:
    """
    Attributes:
        policy_number (str):  Example: NITSK00123456-22.
        status (MCPExperimentsGetV0PoliciesForDOT200ResponseInnerStatus):
        program_type (MCPExperimentsGetV0PoliciesForDOT200ResponseInnerProgramType):
        insured_name (str):  Example: MNM Trucking.
        dot_number (str):  Example: 1234567.
        effective_start_date (datetime.date): Policy effective start date (inclusive). The policy is active starting
            from this date.
        effective_end_date (datetime.date): Policy effective end date (inclusive). The policy is active up to and
            including this date.
        insurance_carrier (MCPExperimentsGetV0PoliciesForDOT200ResponseInnerInsuranceCarrier): Name of the insurance
            carrier
        created_at (datetime.datetime): Timestamp when the policy record was created
        updated_at (datetime.datetime): Timestamp when the policy record was last updated
        miscellaneous_info (Union[Unset, Any]): Miscellaneous information about the policy, sort of a dump-all object
    """

    policy_number: str
    status: MCPExperimentsGetV0PoliciesForDOT200ResponseInnerStatus
    program_type: MCPExperimentsGetV0PoliciesForDOT200ResponseInnerProgramType
    insured_name: str
    dot_number: str
    effective_start_date: datetime.date
    effective_end_date: datetime.date
    insurance_carrier: MCPExperimentsGetV0PoliciesForDOT200ResponseInnerInsuranceCarrier
    created_at: datetime.datetime
    updated_at: datetime.datetime
    miscellaneous_info: Union[Unset, Any] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        policy_number = self.policy_number

        status = self.status.value

        program_type = self.program_type.value

        insured_name = self.insured_name

        dot_number = self.dot_number

        effective_start_date = self.effective_start_date.isoformat()

        effective_end_date = self.effective_end_date.isoformat()

        insurance_carrier = self.insurance_carrier.value

        created_at = self.created_at.isoformat()

        updated_at = self.updated_at.isoformat()

        miscellaneous_info = self.miscellaneous_info

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "policyNumber": policy_number,
                "status": status,
                "programType": program_type,
                "insuredName": insured_name,
                "dotNumber": dot_number,
                "effectiveStartDate": effective_start_date,
                "effectiveEndDate": effective_end_date,
                "insuranceCarrier": insurance_carrier,
                "createdAt": created_at,
                "updatedAt": updated_at,
            }
        )
        if miscellaneous_info is not UNSET:
            field_dict["miscellaneousInfo"] = miscellaneous_info

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        policy_number = d.pop("policyNumber")

        status = MCPExperimentsGetV0PoliciesForDOT200ResponseInnerStatus(d.pop("status"))

        program_type = MCPExperimentsGetV0PoliciesForDOT200ResponseInnerProgramType(d.pop("programType"))

        insured_name = d.pop("insuredName")

        dot_number = d.pop("dotNumber")

        effective_start_date = isoparse(d.pop("effectiveStartDate")).date()

        effective_end_date = isoparse(d.pop("effectiveEndDate")).date()

        insurance_carrier = MCPExperimentsGetV0PoliciesForDOT200ResponseInnerInsuranceCarrier(d.pop("insuranceCarrier"))

        created_at = isoparse(d.pop("createdAt"))

        updated_at = isoparse(d.pop("updatedAt"))

        miscellaneous_info = d.pop("miscellaneousInfo", UNSET)

        mcp_experiments_get_v0_policies_for_dot200_response_inner = cls(
            policy_number=policy_number,
            status=status,
            program_type=program_type,
            insured_name=insured_name,
            dot_number=dot_number,
            effective_start_date=effective_start_date,
            effective_end_date=effective_end_date,
            insurance_carrier=insurance_carrier,
            created_at=created_at,
            updated_at=updated_at,
            miscellaneous_info=miscellaneous_info,
        )

        mcp_experiments_get_v0_policies_for_dot200_response_inner.additional_properties = d
        return mcp_experiments_get_v0_policies_for_dot200_response_inner

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
