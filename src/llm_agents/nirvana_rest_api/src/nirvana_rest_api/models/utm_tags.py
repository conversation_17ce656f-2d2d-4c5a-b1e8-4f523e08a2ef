from collections.abc import Mapping
from typing import Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

T = TypeVar("T", bound="UTMTags")


@_attrs_define
class UTMTags:
    """
    Attributes:
        utm_source (Union[Unset, str]):  Example: telematics_consent.
        utm_medium (Union[Unset, str]):  Example: quoting_app.
        utm_campaign (Union[Unset, str]):  Example: nirvana.
        utm_adgroup (Union[Unset, str]):  Example: nirvana.
        utm_keyword (Union[Unset, str]):  Example: nirvana.
    """

    utm_source: Union[Unset, str] = UNSET
    utm_medium: Union[Unset, str] = UNSET
    utm_campaign: Union[Unset, str] = UNSET
    utm_adgroup: Union[Unset, str] = UNSET
    utm_keyword: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        utm_source = self.utm_source

        utm_medium = self.utm_medium

        utm_campaign = self.utm_campaign

        utm_adgroup = self.utm_adgroup

        utm_keyword = self.utm_keyword

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if utm_source is not UNSET:
            field_dict["utm_source"] = utm_source
        if utm_medium is not UNSET:
            field_dict["utm_medium"] = utm_medium
        if utm_campaign is not UNSET:
            field_dict["utm_campaign"] = utm_campaign
        if utm_adgroup is not UNSET:
            field_dict["utm_adgroup"] = utm_adgroup
        if utm_keyword is not UNSET:
            field_dict["utm_keyword"] = utm_keyword

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        utm_source = d.pop("utm_source", UNSET)

        utm_medium = d.pop("utm_medium", UNSET)

        utm_campaign = d.pop("utm_campaign", UNSET)

        utm_adgroup = d.pop("utm_adgroup", UNSET)

        utm_keyword = d.pop("utm_keyword", UNSET)

        utm_tags = cls(
            utm_source=utm_source,
            utm_medium=utm_medium,
            utm_campaign=utm_campaign,
            utm_adgroup=utm_adgroup,
            utm_keyword=utm_keyword,
        )

        utm_tags.additional_properties = d
        return utm_tags

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
