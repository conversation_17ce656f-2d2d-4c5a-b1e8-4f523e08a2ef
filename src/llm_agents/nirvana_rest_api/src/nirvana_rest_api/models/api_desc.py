from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..models.api_desc_methods_item import APIDescMethodsItem
from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.api_rate_limit import APIRateLimit


T = TypeVar("T", bound="APIDesc")


@_attrs_define
class APIDesc:
    """
    Attributes:
        api (str):  Example: /nirvana/v0/constants.
        methods (list[APIDescMethodsItem]):
        rate_limit (Union[Unset, list['APIRateLimit']]):
    """

    api: str
    methods: list[APIDescMethodsItem]
    rate_limit: Union[Unset, list["APIRateLimit"]] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        api = self.api

        methods = []
        for methods_item_data in self.methods:
            methods_item = methods_item_data.value
            methods.append(methods_item)

        rate_limit: Union[Unset, list[dict[str, Any]]] = UNSET
        if not isinstance(self.rate_limit, Unset):
            rate_limit = []
            for rate_limit_item_data in self.rate_limit:
                rate_limit_item = rate_limit_item_data.to_dict()
                rate_limit.append(rate_limit_item)

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "api": api,
                "methods": methods,
            }
        )
        if rate_limit is not UNSET:
            field_dict["rate_limit"] = rate_limit

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.api_rate_limit import APIRateLimit

        d = dict(src_dict)
        api = d.pop("api")

        methods = []
        _methods = d.pop("methods")
        for methods_item_data in _methods:
            methods_item = APIDescMethodsItem(methods_item_data)

            methods.append(methods_item)

        rate_limit = []
        _rate_limit = d.pop("rate_limit", UNSET)
        for rate_limit_item_data in _rate_limit or []:
            rate_limit_item = APIRateLimit.from_dict(rate_limit_item_data)

            rate_limit.append(rate_limit_item)

        api_desc = cls(
            api=api,
            methods=methods,
            rate_limit=rate_limit,
        )

        api_desc.additional_properties = d
        return api_desc

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
