from enum import Enum


class MCPExperimentsGetPolicyAndDocsAsOn200ResponseInsuranceCarrier(str, Enum):
    INSURANCECARRIEREMPTY = "InsuranceCarrierEmpty"
    INSURANCECARRIERFALLSLAKE = "InsuranceCarrierFallsLake"
    INSURANCECARRIERFRONTERX = "InsuranceCarrierFronterX"
    INSURANCECARRIERMSTRANSVERSE = "InsuranceCarrierMSTransverse"
    INSURANCECARRIERMSTSPECIALITY = "InsuranceCarrierMSTSpeciality"
    INSURANCECARRIERSIRIUSPOINT = "InsuranceCarrierSiriusPoint"
    INSURANCECARRIERSIRIUSPOINTSPECIALITY = "InsuranceCarrierSiriusPointSpeciality"
    INSURANCECARRIERTRANSVERSE = "InsuranceCarrierTransverse"

    def __str__(self) -> str:
        return str(self.value)
