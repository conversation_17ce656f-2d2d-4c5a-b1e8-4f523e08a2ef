from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar

from attrs import define as _attrs_define
from attrs import field as _attrs_field

if TYPE_CHECKING:
    from ..models.fleet import Fleet


T = TypeVar("T", bound="FleetRole")


@_attrs_define
class FleetRole:
    """
    Attributes:
        role (str):  Example: FleetAdminRole.
        fleet (Fleet):
    """

    role: str
    fleet: "Fleet"
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        role = self.role

        fleet = self.fleet.to_dict()

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "role": role,
                "fleet": fleet,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.fleet import Fleet

        d = dict(src_dict)
        role = d.pop("role")

        fleet = Fleet.from_dict(d.pop("fleet"))

        fleet_role = cls(
            role=role,
            fleet=fleet,
        )

        fleet_role.additional_properties = d
        return fleet_role

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
