from collections.abc import Mapping
from typing import Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

T = TypeVar("T", bound="MCPExperimentsDownloadPolicyDocument200ResponseAllOfMetadata")


@_attrs_define
class MCPExperimentsDownloadPolicyDocument200ResponseAllOfMetadata:
    """Additional metadata about the document

    Attributes:
        page_count (Union[Unset, int]): Number of pages in the document
        version (Union[Unset, str]): Version of the document
    """

    page_count: Union[Unset, int] = UNSET
    version: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        page_count = self.page_count

        version = self.version

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if page_count is not UNSET:
            field_dict["pageCount"] = page_count
        if version is not UNSET:
            field_dict["version"] = version

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        page_count = d.pop("pageCount", UNSET)

        version = d.pop("version", UNSET)

        mcp_experiments_download_policy_document_200_response_all_of_metadata = cls(
            page_count=page_count,
            version=version,
        )

        mcp_experiments_download_policy_document_200_response_all_of_metadata.additional_properties = d
        return mcp_experiments_download_policy_document_200_response_all_of_metadata

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
