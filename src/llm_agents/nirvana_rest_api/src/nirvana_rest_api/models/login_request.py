from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..models.login_request_source import LoginRequestSource
from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.utm_tags import UTMTags


T = TypeVar("T", bound="LoginRequest")


@_attrs_define
class LoginRequest:
    """
    Attributes:
        email (str):  Example: <EMAIL>.
        password (str):  Example: T5daYm!wE&2D&uy!.
        utm_tags (Union[Unset, UTMTags]):
        source (Union[Unset, LoginRequestSource]): The source of the login request.
    """

    email: str
    password: str
    utm_tags: Union[Unset, "UTMTags"] = UNSET
    source: Union[Unset, LoginRequestSource] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        email = self.email

        password = self.password

        utm_tags: Union[Unset, dict[str, Any]] = UNSET
        if not isinstance(self.utm_tags, Unset):
            utm_tags = self.utm_tags.to_dict()

        source: Union[Unset, str] = UNSET
        if not isinstance(self.source, Unset):
            source = self.source.value

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "email": email,
                "password": password,
            }
        )
        if utm_tags is not UNSET:
            field_dict["utmTags"] = utm_tags
        if source is not UNSET:
            field_dict["source"] = source

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.utm_tags import UTMTags

        d = dict(src_dict)
        email = d.pop("email")

        password = d.pop("password")

        _utm_tags = d.pop("utmTags", UNSET)
        utm_tags: Union[Unset, UTMTags]
        if isinstance(_utm_tags, Unset):
            utm_tags = UNSET
        else:
            utm_tags = UTMTags.from_dict(_utm_tags)

        _source = d.pop("source", UNSET)
        source: Union[Unset, LoginRequestSource]
        if isinstance(_source, Unset):
            source = UNSET
        else:
            source = LoginRequestSource(_source)

        login_request = cls(
            email=email,
            password=password,
            utm_tags=utm_tags,
            source=source,
        )

        login_request.additional_properties = d
        return login_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
