import datetime
from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar
from uuid import UUID

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..models.key_status import KeyStatus

if TYPE_CHECKING:
    from ..models.api_key_scope import APIKeyScope


T = TypeVar("T", bound="APIKeysWithAgency")


@_attrs_define
class APIKeysWithAgency:
    """
    Attributes:
        key_id (UUID):
        user_id (UUID):
        status (KeyStatus):
        scope (APIKeyScope):
        expires_at (datetime.datetime):
        created_at (datetime.datetime):
        created_by (UUID):
        updated_at (datetime.datetime):
        agency_name (str):
    """

    key_id: UUID
    user_id: UUID
    status: KeyStatus
    scope: "APIKeyScope"
    expires_at: datetime.datetime
    created_at: datetime.datetime
    created_by: UUID
    updated_at: datetime.datetime
    agency_name: str
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        key_id = str(self.key_id)

        user_id = str(self.user_id)

        status = self.status.value

        scope = self.scope.to_dict()

        expires_at = self.expires_at.isoformat()

        created_at = self.created_at.isoformat()

        created_by = str(self.created_by)

        updated_at = self.updated_at.isoformat()

        agency_name = self.agency_name

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "keyId": key_id,
                "userId": user_id,
                "status": status,
                "scope": scope,
                "expiresAt": expires_at,
                "createdAt": created_at,
                "createdBy": created_by,
                "updatedAt": updated_at,
                "agencyName": agency_name,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.api_key_scope import APIKeyScope

        d = dict(src_dict)
        key_id = UUID(d.pop("keyId"))

        user_id = UUID(d.pop("userId"))

        status = KeyStatus(d.pop("status"))

        scope = APIKeyScope.from_dict(d.pop("scope"))

        expires_at = isoparse(d.pop("expiresAt"))

        created_at = isoparse(d.pop("createdAt"))

        created_by = UUID(d.pop("createdBy"))

        updated_at = isoparse(d.pop("updatedAt"))

        agency_name = d.pop("agencyName")

        api_keys_with_agency = cls(
            key_id=key_id,
            user_id=user_id,
            status=status,
            scope=scope,
            expires_at=expires_at,
            created_at=created_at,
            created_by=created_by,
            updated_at=updated_at,
            agency_name=agency_name,
        )

        api_keys_with_agency.additional_properties = d
        return api_keys_with_agency

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
