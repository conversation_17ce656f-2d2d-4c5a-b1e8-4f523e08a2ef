from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar

from attrs import define as _attrs_define
from attrs import field as _attrs_field

if TYPE_CHECKING:
    from ..models.api_desc import APIDesc
    from ..models.api_rate_limit import APIRateLimit


T = TypeVar("T", bound="APIKeyScope")


@_attrs_define
class APIKeyScope:
    """
    Attributes:
        apis (list['APIDesc']):
        global_rate_limit (list['APIRateLimit']):
    """

    apis: list["APIDesc"]
    global_rate_limit: list["APIRateLimit"]
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        apis = []
        for apis_item_data in self.apis:
            apis_item = apis_item_data.to_dict()
            apis.append(apis_item)

        global_rate_limit = []
        for global_rate_limit_item_data in self.global_rate_limit:
            global_rate_limit_item = global_rate_limit_item_data.to_dict()
            global_rate_limit.append(global_rate_limit_item)

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "apis": apis,
                "global_rate_limit": global_rate_limit,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.api_desc import APIDesc
        from ..models.api_rate_limit import APIRateLimit

        d = dict(src_dict)
        apis = []
        _apis = d.pop("apis")
        for apis_item_data in _apis:
            apis_item = APIDesc.from_dict(apis_item_data)

            apis.append(apis_item)

        global_rate_limit = []
        _global_rate_limit = d.pop("global_rate_limit")
        for global_rate_limit_item_data in _global_rate_limit:
            global_rate_limit_item = APIRateLimit.from_dict(global_rate_limit_item_data)

            global_rate_limit.append(global_rate_limit_item)

        api_key_scope = cls(
            apis=apis,
            global_rate_limit=global_rate_limit,
        )

        api_key_scope.additional_properties = d
        return api_key_scope

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
