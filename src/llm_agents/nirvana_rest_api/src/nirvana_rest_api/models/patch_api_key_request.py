from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union
from uuid import UUID

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..models.key_status import KeyStatus
from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.api_key_scope import APIKeyScope


T = TypeVar("T", bound="PatchAPIKeyRequest")


@_attrs_define
class PatchAPIKeyRequest:
    """
    Attributes:
        key_id (UUID):
        scope (Union[Unset, APIKeyScope]):
        status (Union[Unset, KeyStatus]):
    """

    key_id: UUID
    scope: Union[Unset, "APIKeyScope"] = UNSET
    status: Union[Unset, KeyStatus] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        key_id = str(self.key_id)

        scope: Union[Unset, dict[str, Any]] = UNSET
        if not isinstance(self.scope, Unset):
            scope = self.scope.to_dict()

        status: Union[Unset, str] = UNSET
        if not isinstance(self.status, Unset):
            status = self.status.value

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "keyId": key_id,
            }
        )
        if scope is not UNSET:
            field_dict["scope"] = scope
        if status is not UNSET:
            field_dict["status"] = status

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.api_key_scope import APIKeyScope

        d = dict(src_dict)
        key_id = UUID(d.pop("keyId"))

        _scope = d.pop("scope", UNSET)
        scope: Union[Unset, APIKeyScope]
        if isinstance(_scope, Unset):
            scope = UNSET
        else:
            scope = APIKeyScope.from_dict(_scope)

        _status = d.pop("status", UNSET)
        status: Union[Unset, KeyStatus]
        if isinstance(_status, Unset):
            status = UNSET
        else:
            status = KeyStatus(_status)

        patch_api_key_request = cls(
            key_id=key_id,
            scope=scope,
            status=status,
        )

        patch_api_key_request.additional_properties = d
        return patch_api_key_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
