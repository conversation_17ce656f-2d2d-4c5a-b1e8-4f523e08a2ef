import datetime
from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_insurance_carrier import (
    MCPExperimentsGetPolicyAndDocsAsOn200ResponseInsuranceCarrier,
)
from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_program_type import (
    MCPExperimentsGetPolicyAndDocsAsOn200ResponseProgramType,
)
from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_status import (
    MCPExperimentsGetPolicyAndDocsAsOn200ResponseStatus,
)
from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_endorsements_inner import (
        MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfEndorsementsInner,
    )
    from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document import (
        MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument,
    )


T = TypeVar("T", bound="MCPExperimentsGetPolicyAndDocsAsOn200Response")


@_attrs_define
class MCPExperimentsGetPolicyAndDocsAsOn200Response:
    """
    Attributes:
        policy_number (str):  Example: NITSK00123456-22.
        status (MCPExperimentsGetPolicyAndDocsAsOn200ResponseStatus):
        program_type (MCPExperimentsGetPolicyAndDocsAsOn200ResponseProgramType):
        insured_name (str):  Example: MNM Trucking.
        dot_number (str):  Example: 1234567.
        effective_start_date (datetime.date): Policy effective start date (inclusive). The policy is active starting
            from this date.
        effective_end_date (datetime.date): Policy effective end date (inclusive). The policy is active up to and
            including this date.
        insurance_carrier (MCPExperimentsGetPolicyAndDocsAsOn200ResponseInsuranceCarrier): Name of the insurance carrier
        created_at (datetime.datetime): Timestamp when the policy record was created
        updated_at (datetime.datetime): Timestamp when the policy record was last updated
        policy_document (MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument):
        endorsements (list['MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfEndorsementsInner']):
        miscellaneous_info (Union[Unset, Any]): Miscellaneous information about the policy, sort of a dump-all object
    """

    policy_number: str
    status: MCPExperimentsGetPolicyAndDocsAsOn200ResponseStatus
    program_type: MCPExperimentsGetPolicyAndDocsAsOn200ResponseProgramType
    insured_name: str
    dot_number: str
    effective_start_date: datetime.date
    effective_end_date: datetime.date
    insurance_carrier: MCPExperimentsGetPolicyAndDocsAsOn200ResponseInsuranceCarrier
    created_at: datetime.datetime
    updated_at: datetime.datetime
    policy_document: "MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument"
    endorsements: list["MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfEndorsementsInner"]
    miscellaneous_info: Union[Unset, Any] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        policy_number = self.policy_number

        status = self.status.value

        program_type = self.program_type.value

        insured_name = self.insured_name

        dot_number = self.dot_number

        effective_start_date = self.effective_start_date.isoformat()

        effective_end_date = self.effective_end_date.isoformat()

        insurance_carrier = self.insurance_carrier.value

        created_at = self.created_at.isoformat()

        updated_at = self.updated_at.isoformat()

        policy_document = self.policy_document.to_dict()

        endorsements = []
        for endorsements_item_data in self.endorsements:
            endorsements_item = endorsements_item_data.to_dict()
            endorsements.append(endorsements_item)

        miscellaneous_info = self.miscellaneous_info

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "policyNumber": policy_number,
                "status": status,
                "programType": program_type,
                "insuredName": insured_name,
                "dotNumber": dot_number,
                "effectiveStartDate": effective_start_date,
                "effectiveEndDate": effective_end_date,
                "insuranceCarrier": insurance_carrier,
                "createdAt": created_at,
                "updatedAt": updated_at,
                "policyDocument": policy_document,
                "endorsements": endorsements,
            }
        )
        if miscellaneous_info is not UNSET:
            field_dict["miscellaneousInfo"] = miscellaneous_info

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_endorsements_inner import (
            MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfEndorsementsInner,
        )
        from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document import (
            MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument,
        )

        d = dict(src_dict)
        policy_number = d.pop("policyNumber")

        status = MCPExperimentsGetPolicyAndDocsAsOn200ResponseStatus(d.pop("status"))

        program_type = MCPExperimentsGetPolicyAndDocsAsOn200ResponseProgramType(d.pop("programType"))

        insured_name = d.pop("insuredName")

        dot_number = d.pop("dotNumber")

        effective_start_date = isoparse(d.pop("effectiveStartDate")).date()

        effective_end_date = isoparse(d.pop("effectiveEndDate")).date()

        insurance_carrier = MCPExperimentsGetPolicyAndDocsAsOn200ResponseInsuranceCarrier(d.pop("insuranceCarrier"))

        created_at = isoparse(d.pop("createdAt"))

        updated_at = isoparse(d.pop("updatedAt"))

        policy_document = MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument.from_dict(
            d.pop("policyDocument")
        )

        endorsements = []
        _endorsements = d.pop("endorsements")
        for endorsements_item_data in _endorsements:
            endorsements_item = MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfEndorsementsInner.from_dict(
                endorsements_item_data
            )

            endorsements.append(endorsements_item)

        miscellaneous_info = d.pop("miscellaneousInfo", UNSET)

        mcp_experiments_get_policy_and_docs_as_on_200_response = cls(
            policy_number=policy_number,
            status=status,
            program_type=program_type,
            insured_name=insured_name,
            dot_number=dot_number,
            effective_start_date=effective_start_date,
            effective_end_date=effective_end_date,
            insurance_carrier=insurance_carrier,
            created_at=created_at,
            updated_at=updated_at,
            policy_document=policy_document,
            endorsements=endorsements,
            miscellaneous_info=miscellaneous_info,
        )

        mcp_experiments_get_policy_and_docs_as_on_200_response.additional_properties = d
        return mcp_experiments_get_policy_and_docs_as_on_200_response

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
