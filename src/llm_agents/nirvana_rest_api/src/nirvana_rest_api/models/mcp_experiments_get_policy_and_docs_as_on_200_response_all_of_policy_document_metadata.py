import datetime
from collections.abc import Mapping
from typing import Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..types import UNSET, Unset

T = TypeVar("T", bound="MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocumentMetadata")


@_attrs_define
class MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocumentMetadata:
    """Additional metadata about the document

    Attributes:
        file_size (Union[Unset, int]): Size of the document in bytes
        mime_type (Union[Unset, str]): MIME type of the document
        created_at (Union[Unset, datetime.datetime]): When the document was created
        updated_at (Union[Unset, datetime.datetime]): When the document was last updated
    """

    file_size: Union[Unset, int] = UNSET
    mime_type: Union[Unset, str] = UNSET
    created_at: Union[Unset, datetime.datetime] = UNSET
    updated_at: Union[Unset, datetime.datetime] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        file_size = self.file_size

        mime_type = self.mime_type

        created_at: Union[Unset, str] = UNSET
        if not isinstance(self.created_at, Unset):
            created_at = self.created_at.isoformat()

        updated_at: Union[Unset, str] = UNSET
        if not isinstance(self.updated_at, Unset):
            updated_at = self.updated_at.isoformat()

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if file_size is not UNSET:
            field_dict["fileSize"] = file_size
        if mime_type is not UNSET:
            field_dict["mimeType"] = mime_type
        if created_at is not UNSET:
            field_dict["createdAt"] = created_at
        if updated_at is not UNSET:
            field_dict["updatedAt"] = updated_at

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        file_size = d.pop("fileSize", UNSET)

        mime_type = d.pop("mimeType", UNSET)

        _created_at = d.pop("createdAt", UNSET)
        created_at: Union[Unset, datetime.datetime]
        if isinstance(_created_at, Unset):
            created_at = UNSET
        else:
            created_at = isoparse(_created_at)

        _updated_at = d.pop("updatedAt", UNSET)
        updated_at: Union[Unset, datetime.datetime]
        if isinstance(_updated_at, Unset):
            updated_at = UNSET
        else:
            updated_at = isoparse(_updated_at)

        mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document_metadata = cls(
            file_size=file_size,
            mime_type=mime_type,
            created_at=created_at,
            updated_at=updated_at,
        )

        mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document_metadata.additional_properties = d
        return mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document_metadata

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
