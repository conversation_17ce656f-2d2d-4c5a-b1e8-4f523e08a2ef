import datetime
from collections.abc import Mapping
from typing import Any, TypeVar, Union
from uuid import UUID

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..models.mcp_experiments_get_claims_for_policy_200_response_inner_current_status import (
    MCPExperimentsGetClaimsForPolicy200ResponseInnerCurrentStatus,
)
from ..types import UNSET, Unset

T = TypeVar("T", bound="MCPExperimentsGetClaimsForPolicy200ResponseInner")


@_attrs_define
class MCPExperimentsGetClaimsForPolicy200ResponseInner:
    """Summary information about a claim.

    Attributes:
        id (UUID): Unique identifier for the claim.
        external_id (str): The external identifier for the claim.
        policy_number (str): The policy number associated with the claim.
        reported_at (datetime.datetime): Timestamp when the claim was reported.
        current_status (MCPExperimentsGetClaimsForPolicy200ResponseInnerCurrentStatus):
        loss_occurred_at (Union[Unset, datetime.datetime]): Timestamp when the loss occurred.
    """

    id: UUID
    external_id: str
    policy_number: str
    reported_at: datetime.datetime
    current_status: MCPExperimentsGetClaimsForPolicy200ResponseInnerCurrentStatus
    loss_occurred_at: Union[Unset, datetime.datetime] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        id = str(self.id)

        external_id = self.external_id

        policy_number = self.policy_number

        reported_at = self.reported_at.isoformat()

        current_status = self.current_status.value

        loss_occurred_at: Union[Unset, str] = UNSET
        if not isinstance(self.loss_occurred_at, Unset):
            loss_occurred_at = self.loss_occurred_at.isoformat()

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "id": id,
                "externalId": external_id,
                "policyNumber": policy_number,
                "reportedAt": reported_at,
                "currentStatus": current_status,
            }
        )
        if loss_occurred_at is not UNSET:
            field_dict["lossOccurredAt"] = loss_occurred_at

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        id = UUID(d.pop("id"))

        external_id = d.pop("externalId")

        policy_number = d.pop("policyNumber")

        reported_at = isoparse(d.pop("reportedAt"))

        current_status = MCPExperimentsGetClaimsForPolicy200ResponseInnerCurrentStatus(d.pop("currentStatus"))

        _loss_occurred_at = d.pop("lossOccurredAt", UNSET)
        loss_occurred_at: Union[Unset, datetime.datetime]
        if isinstance(_loss_occurred_at, Unset):
            loss_occurred_at = UNSET
        else:
            loss_occurred_at = isoparse(_loss_occurred_at)

        mcp_experiments_get_claims_for_policy_200_response_inner = cls(
            id=id,
            external_id=external_id,
            policy_number=policy_number,
            reported_at=reported_at,
            current_status=current_status,
            loss_occurred_at=loss_occurred_at,
        )

        mcp_experiments_get_claims_for_policy_200_response_inner.additional_properties = d
        return mcp_experiments_get_claims_for_policy_200_response_inner

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
