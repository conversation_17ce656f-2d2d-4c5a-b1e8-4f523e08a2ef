from enum import Enum


class MCPExperimentsGetV0PoliciesForDOT200ResponseInnerInsuranceCarrier(str, Enum):
    INSURANCECARRIEREMPTY = "InsuranceCarrierEmpty"
    INSURANCECARRIERFALLSLAKE = "InsuranceCarrierFallsLake"
    INSURANCECARRIERFRONTERX = "InsuranceCarrierFronterX"
    INSURANCECARRIERMSTRANSVERSE = "InsuranceCarrierMSTransverse"
    INSURANCECARRIERMSTSPECIALITY = "InsuranceCarrierMSTSpeciality"
    INSURANCECARRIERSIRIUSPOINT = "InsuranceCarrierSiriusPoint"
    INSURANCECARRIERSIRIUSPOINTSPECIALITY = "InsuranceCarrierSiriusPointSpeciality"
    INSURANCECARRIERTRANSVERSE = "InsuranceCarrierTransverse"

    def __str__(self) -> str:
        return str(self.value)
