import datetime
from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union
from uuid import UUID

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document import (
        MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument,
    )


T = TypeVar("T", bound="MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfEndorsementsInner")


@_attrs_define
class MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfEndorsementsInner:
    """
    Attributes:
        endorsement_id (UUID): Unique identifier for the endorsement
        effective_date (Union[Unset, datetime.date]): Effective date of the endorsement
        approved_at (Union[Unset, datetime.datetime]): Date and time the endorsement was approved
        description (Union[Unset, str]): Description of the endorsement
        documents (Union[Unset, list['MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument']]):
    """

    endorsement_id: UUID
    effective_date: Union[Unset, datetime.date] = UNSET
    approved_at: Union[Unset, datetime.datetime] = UNSET
    description: Union[Unset, str] = UNSET
    documents: Union[Unset, list["MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument"]] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        endorsement_id = str(self.endorsement_id)

        effective_date: Union[Unset, str] = UNSET
        if not isinstance(self.effective_date, Unset):
            effective_date = self.effective_date.isoformat()

        approved_at: Union[Unset, str] = UNSET
        if not isinstance(self.approved_at, Unset):
            approved_at = self.approved_at.isoformat()

        description = self.description

        documents: Union[Unset, list[dict[str, Any]]] = UNSET
        if not isinstance(self.documents, Unset):
            documents = []
            for documents_item_data in self.documents:
                documents_item = documents_item_data.to_dict()
                documents.append(documents_item)

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "endorsementId": endorsement_id,
            }
        )
        if effective_date is not UNSET:
            field_dict["effectiveDate"] = effective_date
        if approved_at is not UNSET:
            field_dict["approvedAt"] = approved_at
        if description is not UNSET:
            field_dict["description"] = description
        if documents is not UNSET:
            field_dict["documents"] = documents

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document import (
            MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument,
        )

        d = dict(src_dict)
        endorsement_id = UUID(d.pop("endorsementId"))

        _effective_date = d.pop("effectiveDate", UNSET)
        effective_date: Union[Unset, datetime.date]
        if isinstance(_effective_date, Unset):
            effective_date = UNSET
        else:
            effective_date = isoparse(_effective_date).date()

        _approved_at = d.pop("approvedAt", UNSET)
        approved_at: Union[Unset, datetime.datetime]
        if isinstance(_approved_at, Unset):
            approved_at = UNSET
        else:
            approved_at = isoparse(_approved_at)

        description = d.pop("description", UNSET)

        documents = []
        _documents = d.pop("documents", UNSET)
        for documents_item_data in _documents or []:
            documents_item = MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument.from_dict(
                documents_item_data
            )

            documents.append(documents_item)

        mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_endorsements_inner = cls(
            endorsement_id=endorsement_id,
            effective_date=effective_date,
            approved_at=approved_at,
            description=description,
            documents=documents,
        )

        mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_endorsements_inner.additional_properties = d
        return mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_endorsements_inner

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
