from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar

from attrs import define as _attrs_define
from attrs import field as _attrs_field

if TYPE_CHECKING:
    from ..models.agency import Agency


T = TypeVar("T", bound="AgencyRole")


@_attrs_define
class AgencyRole:
    """
    Attributes:
        role (str):  Example: AgencyAdminRole.
        agency (Agency):
    """

    role: str
    agency: "Agency"
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        role = self.role

        agency = self.agency.to_dict()

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "role": role,
                "agency": agency,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.agency import Agency

        d = dict(src_dict)
        role = d.pop("role")

        agency = Agency.from_dict(d.pop("agency"))

        agency_role = cls(
            role=role,
            agency=agency,
        )

        agency_role.additional_properties = d
        return agency_role

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
