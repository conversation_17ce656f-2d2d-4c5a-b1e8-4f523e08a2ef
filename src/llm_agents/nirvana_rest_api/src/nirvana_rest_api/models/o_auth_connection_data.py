from collections.abc import Mapping
from typing import Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

T = TypeVar("T", bound="OAuthConnectionData")


@_attrs_define
class OAuthConnectionData:
    """
    Attributes:
        auth_code (Union[Unset, str]):  Example: hgg2tziN39.
        scope (Union[Unset, str]):  Example: admin.
        state (Union[Unset, str]):  Example: hgg2tziN39.
        error (Union[Unset, str]):  Example: scope_not_granted.
    """

    auth_code: Union[Unset, str] = UNSET
    scope: Union[Unset, str] = UNSET
    state: Union[Unset, str] = UNSET
    error: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        auth_code = self.auth_code

        scope = self.scope

        state = self.state

        error = self.error

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if auth_code is not UNSET:
            field_dict["authCode"] = auth_code
        if scope is not UNSET:
            field_dict["scope"] = scope
        if state is not UNSET:
            field_dict["state"] = state
        if error is not UNSET:
            field_dict["error"] = error

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        auth_code = d.pop("authCode", UNSET)

        scope = d.pop("scope", UNSET)

        state = d.pop("state", UNSET)

        error = d.pop("error", UNSET)

        o_auth_connection_data = cls(
            auth_code=auth_code,
            scope=scope,
            state=state,
            error=error,
        )

        o_auth_connection_data.additional_properties = d
        return o_auth_connection_data

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
