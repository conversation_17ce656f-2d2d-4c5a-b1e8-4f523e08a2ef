from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.agency_role import AgencyRole
    from ..models.fleet_role import FleetRole
    from ..models.nirvana_role import NirvanaRole


T = TypeVar("T", bound="Roles")


@_attrs_define
class Roles:
    """
    Attributes:
        nirvana_roles (Union[Unset, list['NirvanaRole']]): List of authz internal `Nirvana` roles for this user.
        agency_roles (Union[Unset, list['AgencyRole']]): List of authz `Agency` roles for this user. If a user has an
            `agency` role, then they are typically either an `Agent` or an internal `Nirvana` user.
        fleet_roles (Union[Unset, list['FleetRole']]): List of authz `Fleet` roles for this user. If a user has `Fleet`
            roles but no `Agency` or `Nirvana` roles, then this user can only view their own DOT(s) in the Safety App.
    """

    nirvana_roles: Union[Unset, list["NirvanaRole"]] = UNSET
    agency_roles: Union[Unset, list["AgencyRole"]] = UNSET
    fleet_roles: Union[Unset, list["FleetRole"]] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        nirvana_roles: Union[Unset, list[dict[str, Any]]] = UNSET
        if not isinstance(self.nirvana_roles, Unset):
            nirvana_roles = []
            for nirvana_roles_item_data in self.nirvana_roles:
                nirvana_roles_item = nirvana_roles_item_data.to_dict()
                nirvana_roles.append(nirvana_roles_item)

        agency_roles: Union[Unset, list[dict[str, Any]]] = UNSET
        if not isinstance(self.agency_roles, Unset):
            agency_roles = []
            for agency_roles_item_data in self.agency_roles:
                agency_roles_item = agency_roles_item_data.to_dict()
                agency_roles.append(agency_roles_item)

        fleet_roles: Union[Unset, list[dict[str, Any]]] = UNSET
        if not isinstance(self.fleet_roles, Unset):
            fleet_roles = []
            for fleet_roles_item_data in self.fleet_roles:
                fleet_roles_item = fleet_roles_item_data.to_dict()
                fleet_roles.append(fleet_roles_item)

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if nirvana_roles is not UNSET:
            field_dict["nirvanaRoles"] = nirvana_roles
        if agency_roles is not UNSET:
            field_dict["agencyRoles"] = agency_roles
        if fleet_roles is not UNSET:
            field_dict["fleetRoles"] = fleet_roles

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.agency_role import AgencyRole
        from ..models.fleet_role import FleetRole
        from ..models.nirvana_role import NirvanaRole

        d = dict(src_dict)
        nirvana_roles = []
        _nirvana_roles = d.pop("nirvanaRoles", UNSET)
        for nirvana_roles_item_data in _nirvana_roles or []:
            nirvana_roles_item = NirvanaRole.from_dict(nirvana_roles_item_data)

            nirvana_roles.append(nirvana_roles_item)

        agency_roles = []
        _agency_roles = d.pop("agencyRoles", UNSET)
        for agency_roles_item_data in _agency_roles or []:
            agency_roles_item = AgencyRole.from_dict(agency_roles_item_data)

            agency_roles.append(agency_roles_item)

        fleet_roles = []
        _fleet_roles = d.pop("fleetRoles", UNSET)
        for fleet_roles_item_data in _fleet_roles or []:
            fleet_roles_item = FleetRole.from_dict(fleet_roles_item_data)

            fleet_roles.append(fleet_roles_item)

        roles = cls(
            nirvana_roles=nirvana_roles,
            agency_roles=agency_roles,
            fleet_roles=fleet_roles,
        )

        roles.additional_properties = d
        return roles

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
