import datetime
from collections.abc import Mapping
from typing import Any, TypeVar, Union
from uuid import UUID

from attrs import define as _attrs_define
from attrs import field as _attrs_field
from dateutil.parser import isoparse

from ..types import UNSET, Unset

T = TypeVar("T", bound="MCPExperimentsGetClaimNotes200ResponseInner")


@_attrs_define
class MCPExperimentsGetClaimNotes200ResponseInner:
    """Represents a note associated with a claim.

    Attributes:
        note_id (UUID): Unique identifier for the note.
        created_at (datetime.datetime): Timestamp when the note was created.
        text (str): The content of the note.
        category (Union[Unset, str]): The category of the note.
    """

    note_id: UUID
    created_at: datetime.datetime
    text: str
    category: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        note_id = str(self.note_id)

        created_at = self.created_at.isoformat()

        text = self.text

        category = self.category

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "noteId": note_id,
                "createdAt": created_at,
                "text": text,
            }
        )
        if category is not UNSET:
            field_dict["category"] = category

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        note_id = UUID(d.pop("noteId"))

        created_at = isoparse(d.pop("createdAt"))

        text = d.pop("text")

        category = d.pop("category", UNSET)

        mcp_experiments_get_claim_notes_200_response_inner = cls(
            note_id=note_id,
            created_at=created_at,
            text=text,
            category=category,
        )

        mcp_experiments_get_claim_notes_200_response_inner.additional_properties = d
        return mcp_experiments_get_claim_notes_200_response_inner

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
