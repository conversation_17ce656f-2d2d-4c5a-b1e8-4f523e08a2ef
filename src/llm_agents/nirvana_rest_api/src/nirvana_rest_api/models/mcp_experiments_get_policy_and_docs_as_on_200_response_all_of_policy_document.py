from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union
from uuid import UUID

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document_metadata import (
        MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocumentMetadata,
    )


T = TypeVar("T", bound="MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument")


@_attrs_define
class MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocument:
    """
    Attributes:
        document_id (UUID): Unique identifier for the document
        file_name (str): Original name of the document file
        metadata (Union[Unset, MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocumentMetadata]): Additional
            metadata about the document
    """

    document_id: UUID
    file_name: str
    metadata: Union[Unset, "MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocumentMetadata"] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        document_id = str(self.document_id)

        file_name = self.file_name

        metadata: Union[Unset, dict[str, Any]] = UNSET
        if not isinstance(self.metadata, Unset):
            metadata = self.metadata.to_dict()

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "documentId": document_id,
                "fileName": file_name,
            }
        )
        if metadata is not UNSET:
            field_dict["metadata"] = metadata

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document_metadata import (
            MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocumentMetadata,
        )

        d = dict(src_dict)
        document_id = UUID(d.pop("documentId"))

        file_name = d.pop("fileName")

        _metadata = d.pop("metadata", UNSET)
        metadata: Union[Unset, MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocumentMetadata]
        if isinstance(_metadata, Unset):
            metadata = UNSET
        else:
            metadata = MCPExperimentsGetPolicyAndDocsAsOn200ResponseAllOfPolicyDocumentMetadata.from_dict(_metadata)

        mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document = cls(
            document_id=document_id,
            file_name=file_name,
            metadata=metadata,
        )

        mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document.additional_properties = d
        return mcp_experiments_get_policy_and_docs_as_on_200_response_all_of_policy_document

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
