from collections.abc import Mapping
from typing import Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

T = TypeVar("T", bound="WorkrampLoginRequest")


@_attrs_define
class WorkrampLoginRequest:
    """
    Attributes:
        dest_path (Union[Unset, str]): The relative path where the user should be redirected to upon authenticating with
            absoluteLoginUrl. Example: /trainings/abcdefg.
    """

    dest_path: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        dest_path = self.dest_path

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if dest_path is not UNSET:
            field_dict["destPath"] = dest_path

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        dest_path = d.pop("destPath", UNSET)

        workramp_login_request = cls(
            dest_path=dest_path,
        )

        workramp_login_request.additional_properties = d
        return workramp_login_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
