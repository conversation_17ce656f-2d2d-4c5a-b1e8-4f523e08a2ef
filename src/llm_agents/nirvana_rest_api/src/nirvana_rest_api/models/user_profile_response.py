from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..models.user_profile_response_user_type import UserProfileResponseUserType
from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.roles import Roles


T = TypeVar("T", bound="UserProfileResponse")


@_attrs_define
class UserProfileResponse:
    """
    Attributes:
        id (str):  Example: a81bc81b-dead-4e5d-abff-90865d1e13b1.
        report_id (str):  Example: a81bc81b-dead-4e5d-abff-90865d1e13b1.
        name (str):  Example: <PERSON>.
        email (str):  Example: <EMAIL>.
        user_type (UserProfileResponseUserType): Type of the user's roles:
             * `nirvana` - Internal Nirvana user, such as `superuser` or `support` users.
             * `agent` - User belongs to one or more Agency roles.
             * `fleet` - User belongs to one or more Fleet roles, and no Agency or Nirvana roles. This user should only have
            access to the safety app.
             * `shared_link` - Shared link user that is anonymously viewing a Safety App shared URL.
             * `unprivileged` - User has login credentials but does not belong to any agencies or fleets.
        roles (Union[Unset, Roles]):
        default_agency_id (Union[Unset, str]):  Example: d754a727-80fd-468d-b2d0-8c8659eac8f3.
    """

    id: str
    report_id: str
    name: str
    email: str
    user_type: UserProfileResponseUserType
    roles: Union[Unset, "Roles"] = UNSET
    default_agency_id: Union[Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        id = self.id

        report_id = self.report_id

        name = self.name

        email = self.email

        user_type = self.user_type.value

        roles: Union[Unset, dict[str, Any]] = UNSET
        if not isinstance(self.roles, Unset):
            roles = self.roles.to_dict()

        default_agency_id = self.default_agency_id

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "id": id,
                "reportId": report_id,
                "name": name,
                "email": email,
                "userType": user_type,
            }
        )
        if roles is not UNSET:
            field_dict["roles"] = roles
        if default_agency_id is not UNSET:
            field_dict["defaultAgencyId"] = default_agency_id

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.roles import Roles

        d = dict(src_dict)
        id = d.pop("id")

        report_id = d.pop("reportId")

        name = d.pop("name")

        email = d.pop("email")

        user_type = UserProfileResponseUserType(d.pop("userType"))

        _roles = d.pop("roles", UNSET)
        roles: Union[Unset, Roles]
        if isinstance(_roles, Unset):
            roles = UNSET
        else:
            roles = Roles.from_dict(_roles)

        default_agency_id = d.pop("defaultAgencyId", UNSET)

        user_profile_response = cls(
            id=id,
            report_id=report_id,
            name=name,
            email=email,
            user_type=user_type,
            roles=roles,
            default_agency_id=default_agency_id,
        )

        user_profile_response.additional_properties = d
        return user_profile_response

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
