from collections.abc import Mapping
from typing import Any, TypeVar

from attrs import define as _attrs_define
from attrs import field as _attrs_field

T = TypeVar("T", bound="HubspotLoginResponse")


@_attrs_define
class HubspotLoginResponse:
    """
    Attributes:
        absolute_login_url (str): The URL that will authenticate this user with hubspot upon redirecting to it. Example:
            https://insurance.nirvanatech.com/_hcms/mem/jwt?jwt=superlonghexencodedjwt?redirect_url=....
    """

    absolute_login_url: str
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        absolute_login_url = self.absolute_login_url

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "absoluteLoginUrl": absolute_login_url,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        absolute_login_url = d.pop("absoluteLoginUrl")

        hubspot_login_response = cls(
            absolute_login_url=absolute_login_url,
        )

        hubspot_login_response.additional_properties = d
        return hubspot_login_response

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
