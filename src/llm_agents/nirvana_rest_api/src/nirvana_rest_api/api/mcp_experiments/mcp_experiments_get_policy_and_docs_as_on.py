import datetime
from http import HTTPStatus
from typing import Any, Optional, Union

import httpx

from ... import errors
from ...client import AuthenticatedClient, Client
from ...models.mcp_experiments_get_policy_and_docs_as_on_200_response import (
    MCPExperimentsGetPolicyAndDocsAsOn200Response,
)
from ...models.mcp_experiments_get_v0_policies_for_dot404_response import MCPExperimentsGetV0PoliciesForDOT404Response
from ...types import Response


def _get_kwargs(
    policy_number: str,
    effective_date: datetime.date,
) -> dict[str, Any]:
    _kwargs: dict[str, Any] = {
        "method": "get",
        "url": f"/mcp-experiments/policy/{policy_number}/effective/{effective_date}",
    }

    return _kwargs


def _parse_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Optional[Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    if response.status_code == 200:
        response_200 = MCPExperimentsGetPolicyAndDocsAsOn200Response.from_dict(response.json())

        return response_200
    if response.status_code == 404:
        response_404 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_404
    if client.raise_on_unexpected_status:
        raise errors.UnexpectedStatus(response.status_code, response.content)
    else:
        return None


def _build_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Response[Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    return Response(
        status_code=HTTPStatus(response.status_code),
        content=response.content,
        headers=response.headers,
        parsed=_parse_response(client=client, response=response),
    )


def sync_detailed(
    policy_number: str,
    effective_date: datetime.date,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Get policy information and its documents as of a specific date

     Retrieves the policy information and its documents as of the given effective date.

    ### Notes
    - The date parameter must be in YYYY-MM-DD format
    - Endorsements are returned in chronological order by effective date, starting with the earliest
    endorsement

    Args:
        policy_number (str):  Example: NISTK0017619-23.
        effective_date (datetime.date):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]
    """

    kwargs = _get_kwargs(
        policy_number=policy_number,
        effective_date=effective_date,
    )

    response = client.get_httpx_client().request(
        **kwargs,
    )

    return _build_response(client=client, response=response)


def sync(
    policy_number: str,
    effective_date: datetime.date,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Get policy information and its documents as of a specific date

     Retrieves the policy information and its documents as of the given effective date.

    ### Notes
    - The date parameter must be in YYYY-MM-DD format
    - Endorsements are returned in chronological order by effective date, starting with the earliest
    endorsement

    Args:
        policy_number (str):  Example: NISTK0017619-23.
        effective_date (datetime.date):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]
    """

    return sync_detailed(
        policy_number=policy_number,
        effective_date=effective_date,
        client=client,
    ).parsed


async def asyncio_detailed(
    policy_number: str,
    effective_date: datetime.date,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Get policy information and its documents as of a specific date

     Retrieves the policy information and its documents as of the given effective date.

    ### Notes
    - The date parameter must be in YYYY-MM-DD format
    - Endorsements are returned in chronological order by effective date, starting with the earliest
    endorsement

    Args:
        policy_number (str):  Example: NISTK0017619-23.
        effective_date (datetime.date):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]
    """

    kwargs = _get_kwargs(
        policy_number=policy_number,
        effective_date=effective_date,
    )

    response = await client.get_async_httpx_client().request(**kwargs)

    return _build_response(client=client, response=response)


async def asyncio(
    policy_number: str,
    effective_date: datetime.date,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Get policy information and its documents as of a specific date

     Retrieves the policy information and its documents as of the given effective date.

    ### Notes
    - The date parameter must be in YYYY-MM-DD format
    - Endorsements are returned in chronological order by effective date, starting with the earliest
    endorsement

    Args:
        policy_number (str):  Example: NISTK0017619-23.
        effective_date (datetime.date):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetPolicyAndDocsAsOn200Response, MCPExperimentsGetV0PoliciesForDOT404Response]
    """

    return (
        await asyncio_detailed(
            policy_number=policy_number,
            effective_date=effective_date,
            client=client,
        )
    ).parsed
