from http import HTTPStatus
from typing import Any, Optional, Union
from uuid import UUID

import httpx

from ... import errors
from ...client import AuthenticatedClient, Client
from ...models.mcp_experiments_download_policy_document_200_response import (
    MCPExperimentsDownloadPolicyDocument200Response,
)
from ...models.mcp_experiments_download_policy_document_format import MCPExperimentsDownloadPolicyDocumentFormat
from ...models.mcp_experiments_get_v0_policies_for_dot404_response import MCPExperimentsGetV0PoliciesForDOT404Response
from ...types import UNSET, Response, Unset


def _get_kwargs(
    policy_number: str,
    document_id: UUID,
    *,
    format_: Union[
        Unset, MCPExperimentsDownloadPolicyDocumentFormat
    ] = MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL,
    expiration_minutes: Union[Unset, int] = 60,
) -> dict[str, Any]:
    params: dict[str, Any] = {}

    json_format_: Union[Unset, str] = UNSET
    if not isinstance(format_, Unset):
        json_format_ = format_.value

    params["format"] = json_format_

    params["expirationMinutes"] = expiration_minutes

    params = {k: v for k, v in params.items() if v is not UNSET and v is not None}

    _kwargs: dict[str, Any] = {
        "method": "get",
        "url": f"/mcp-experiments/policy/{policy_number}/documents/{document_id}",
        "params": params,
    }

    return _kwargs


def _parse_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Optional[Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    if response.status_code == 200:
        response_200 = MCPExperimentsDownloadPolicyDocument200Response.from_dict(response.json())

        return response_200
    if response.status_code == 400:
        response_400 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_400
    if response.status_code == 403:
        response_403 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_403
    if response.status_code == 404:
        response_404 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_404
    if client.raise_on_unexpected_status:
        raise errors.UnexpectedStatus(response.status_code, response.content)
    else:
        return None


def _build_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Response[Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    return Response(
        status_code=HTTPStatus(response.status_code),
        content=response.content,
        headers=response.headers,
        parsed=_parse_response(client=client, response=response),
    )


def sync_detailed(
    policy_number: str,
    document_id: UUID,
    *,
    client: Union[AuthenticatedClient, Client],
    format_: Union[
        Unset, MCPExperimentsDownloadPolicyDocumentFormat
    ] = MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL,
    expiration_minutes: Union[Unset, int] = 60,
) -> Response[Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Download a specific policy document

     Returns a pre-signed URL to access a specific policy document. The URL is time-limited and will
    expire after a specified duration.

    ### Notes
    - The pre-signed URL is valid for 1 hour by default
    - The URL can be used to directly download the document
    - Document access is subject to the user's permissions
    - Documents may be available in multiple formats

    Args:
        policy_number (str):  Example: NISTK0017619-23.
        document_id (UUID):
        format_ (Union[Unset, MCPExperimentsDownloadPolicyDocumentFormat]):  Default:
            MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL.
        expiration_minutes (Union[Unset, int]):  Default: 60.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]
    """

    kwargs = _get_kwargs(
        policy_number=policy_number,
        document_id=document_id,
        format_=format_,
        expiration_minutes=expiration_minutes,
    )

    response = client.get_httpx_client().request(
        **kwargs,
    )

    return _build_response(client=client, response=response)


def sync(
    policy_number: str,
    document_id: UUID,
    *,
    client: Union[AuthenticatedClient, Client],
    format_: Union[
        Unset, MCPExperimentsDownloadPolicyDocumentFormat
    ] = MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL,
    expiration_minutes: Union[Unset, int] = 60,
) -> Optional[Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Download a specific policy document

     Returns a pre-signed URL to access a specific policy document. The URL is time-limited and will
    expire after a specified duration.

    ### Notes
    - The pre-signed URL is valid for 1 hour by default
    - The URL can be used to directly download the document
    - Document access is subject to the user's permissions
    - Documents may be available in multiple formats

    Args:
        policy_number (str):  Example: NISTK0017619-23.
        document_id (UUID):
        format_ (Union[Unset, MCPExperimentsDownloadPolicyDocumentFormat]):  Default:
            MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL.
        expiration_minutes (Union[Unset, int]):  Default: 60.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]
    """

    return sync_detailed(
        policy_number=policy_number,
        document_id=document_id,
        client=client,
        format_=format_,
        expiration_minutes=expiration_minutes,
    ).parsed


async def asyncio_detailed(
    policy_number: str,
    document_id: UUID,
    *,
    client: Union[AuthenticatedClient, Client],
    format_: Union[
        Unset, MCPExperimentsDownloadPolicyDocumentFormat
    ] = MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL,
    expiration_minutes: Union[Unset, int] = 60,
) -> Response[Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Download a specific policy document

     Returns a pre-signed URL to access a specific policy document. The URL is time-limited and will
    expire after a specified duration.

    ### Notes
    - The pre-signed URL is valid for 1 hour by default
    - The URL can be used to directly download the document
    - Document access is subject to the user's permissions
    - Documents may be available in multiple formats

    Args:
        policy_number (str):  Example: NISTK0017619-23.
        document_id (UUID):
        format_ (Union[Unset, MCPExperimentsDownloadPolicyDocumentFormat]):  Default:
            MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL.
        expiration_minutes (Union[Unset, int]):  Default: 60.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]
    """

    kwargs = _get_kwargs(
        policy_number=policy_number,
        document_id=document_id,
        format_=format_,
        expiration_minutes=expiration_minutes,
    )

    response = await client.get_async_httpx_client().request(**kwargs)

    return _build_response(client=client, response=response)


async def asyncio(
    policy_number: str,
    document_id: UUID,
    *,
    client: Union[AuthenticatedClient, Client],
    format_: Union[
        Unset, MCPExperimentsDownloadPolicyDocumentFormat
    ] = MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL,
    expiration_minutes: Union[Unset, int] = 60,
) -> Optional[Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Download a specific policy document

     Returns a pre-signed URL to access a specific policy document. The URL is time-limited and will
    expire after a specified duration.

    ### Notes
    - The pre-signed URL is valid for 1 hour by default
    - The URL can be used to directly download the document
    - Document access is subject to the user's permissions
    - Documents may be available in multiple formats

    Args:
        policy_number (str):  Example: NISTK0017619-23.
        document_id (UUID):
        format_ (Union[Unset, MCPExperimentsDownloadPolicyDocumentFormat]):  Default:
            MCPExperimentsDownloadPolicyDocumentFormat.ORIGINAL.
        expiration_minutes (Union[Unset, int]):  Default: 60.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsDownloadPolicyDocument200Response, MCPExperimentsGetV0PoliciesForDOT404Response]
    """

    return (
        await asyncio_detailed(
            policy_number=policy_number,
            document_id=document_id,
            client=client,
            format_=format_,
            expiration_minutes=expiration_minutes,
        )
    ).parsed
