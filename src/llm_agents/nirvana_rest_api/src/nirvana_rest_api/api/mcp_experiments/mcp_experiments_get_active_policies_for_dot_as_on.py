import datetime
from http import HTTPStatus
from typing import Any, Optional, Union

import httpx

from ... import errors
from ...client import AuthenticatedClient, Client
from ...models.mcp_experiments_get_v0_policies_for_dot200_response_inner import (
    MCPExperimentsGetV0PoliciesForDOT200ResponseInner,
)
from ...models.mcp_experiments_get_v0_policies_for_dot404_response import MCPExperimentsGetV0PoliciesForDOT404Response
from ...types import Response


def _get_kwargs(
    dot_number: int,
    effective_date: datetime.date,
) -> dict[str, Any]:
    _kwargs: dict[str, Any] = {
        "method": "get",
        "url": f"/mcp-experiments/dot/{dot_number}/policies/effective/{effective_date}",
    }

    return _kwargs


def _parse_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Optional[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetV0PoliciesForDOT200ResponseInner"]]
]:
    if response.status_code == 200:
        response_200 = []
        _response_200 = response.json()
        for response_200_item_data in _response_200:
            response_200_item = MCPExperimentsGetV0PoliciesForDOT200ResponseInner.from_dict(response_200_item_data)

            response_200.append(response_200_item)

        return response_200
    if response.status_code == 404:
        response_404 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_404
    if client.raise_on_unexpected_status:
        raise errors.UnexpectedStatus(response.status_code, response.content)
    else:
        return None


def _build_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Response[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetV0PoliciesForDOT200ResponseInner"]]
]:
    return Response(
        status_code=HTTPStatus(response.status_code),
        content=response.content,
        headers=response.headers,
        parsed=_parse_response(client=client, response=response),
    )


def sync_detailed(
    dot_number: int,
    effective_date: datetime.date,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetV0PoliciesForDOT200ResponseInner"]]
]:
    """Get active policies and their documents for DOT as of the given date

     Retrieves all active policies and their associated documents for a DOT number as of a specific date.
    The response includes summary information for each policy and its documents.

    ### Notes
    - The date parameter must be in YYYY-MM-DD format
    - Policies are returned in chronological order by effective date
    - Documents for each policy are included in the response

    Args:
        dot_number (int):  Example: 1002125.
        effective_date (datetime.date):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetV0PoliciesForDOT200ResponseInner']]]
    """

    kwargs = _get_kwargs(
        dot_number=dot_number,
        effective_date=effective_date,
    )

    response = client.get_httpx_client().request(
        **kwargs,
    )

    return _build_response(client=client, response=response)


def sync(
    dot_number: int,
    effective_date: datetime.date,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetV0PoliciesForDOT200ResponseInner"]]
]:
    """Get active policies and their documents for DOT as of the given date

     Retrieves all active policies and their associated documents for a DOT number as of a specific date.
    The response includes summary information for each policy and its documents.

    ### Notes
    - The date parameter must be in YYYY-MM-DD format
    - Policies are returned in chronological order by effective date
    - Documents for each policy are included in the response

    Args:
        dot_number (int):  Example: 1002125.
        effective_date (datetime.date):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetV0PoliciesForDOT200ResponseInner']]
    """

    return sync_detailed(
        dot_number=dot_number,
        effective_date=effective_date,
        client=client,
    ).parsed


async def asyncio_detailed(
    dot_number: int,
    effective_date: datetime.date,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetV0PoliciesForDOT200ResponseInner"]]
]:
    """Get active policies and their documents for DOT as of the given date

     Retrieves all active policies and their associated documents for a DOT number as of a specific date.
    The response includes summary information for each policy and its documents.

    ### Notes
    - The date parameter must be in YYYY-MM-DD format
    - Policies are returned in chronological order by effective date
    - Documents for each policy are included in the response

    Args:
        dot_number (int):  Example: 1002125.
        effective_date (datetime.date):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetV0PoliciesForDOT200ResponseInner']]]
    """

    kwargs = _get_kwargs(
        dot_number=dot_number,
        effective_date=effective_date,
    )

    response = await client.get_async_httpx_client().request(**kwargs)

    return _build_response(client=client, response=response)


async def asyncio(
    dot_number: int,
    effective_date: datetime.date,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetV0PoliciesForDOT200ResponseInner"]]
]:
    """Get active policies and their documents for DOT as of the given date

     Retrieves all active policies and their associated documents for a DOT number as of a specific date.
    The response includes summary information for each policy and its documents.

    ### Notes
    - The date parameter must be in YYYY-MM-DD format
    - Policies are returned in chronological order by effective date
    - Documents for each policy are included in the response

    Args:
        dot_number (int):  Example: 1002125.
        effective_date (datetime.date):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetV0PoliciesForDOT200ResponseInner']]
    """

    return (
        await asyncio_detailed(
            dot_number=dot_number,
            effective_date=effective_date,
            client=client,
        )
    ).parsed
