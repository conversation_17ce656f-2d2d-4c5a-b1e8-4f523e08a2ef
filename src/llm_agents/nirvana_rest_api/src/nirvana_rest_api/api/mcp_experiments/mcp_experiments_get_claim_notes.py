from http import HTTPStatus
from typing import Any, Optional, Union

import httpx

from ... import errors
from ...client import AuthenticatedClient, Client
from ...models.mcp_experiments_get_claim_notes_200_response_inner import MCPExperimentsGetClaimNotes200ResponseInner
from ...models.mcp_experiments_get_v0_policies_for_dot404_response import MCPExperimentsGetV0PoliciesForDOT404Response
from ...types import Response


def _get_kwargs(
    claim_external_id: str,
) -> dict[str, Any]:
    _kwargs: dict[str, Any] = {
        "method": "get",
        "url": f"/mcp-experiments/claim/{claim_external_id}/notes",
    }

    return _kwargs


def _parse_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Optional[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimNotes200ResponseInner"]]]:
    if response.status_code == 200:
        response_200 = []
        _response_200 = response.json()
        for response_200_item_data in _response_200:
            response_200_item = MCPExperimentsGetClaimNotes200ResponseInner.from_dict(response_200_item_data)

            response_200.append(response_200_item)

        return response_200
    if response.status_code == 404:
        response_404 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_404
    if client.raise_on_unexpected_status:
        raise errors.UnexpectedStatus(response.status_code, response.content)
    else:
        return None


def _build_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimNotes200ResponseInner"]]]:
    return Response(
        status_code=HTTPStatus(response.status_code),
        content=response.content,
        headers=response.headers,
        parsed=_parse_response(client=client, response=response),
    )


def sync_detailed(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimNotes200ResponseInner"]]]:
    """Get notes for a specific claim

     Retrieves notes associated with a specific claim, identified by its external ID.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetClaimNotes200ResponseInner']]]
    """

    kwargs = _get_kwargs(
        claim_external_id=claim_external_id,
    )

    response = client.get_httpx_client().request(
        **kwargs,
    )

    return _build_response(client=client, response=response)


def sync(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimNotes200ResponseInner"]]]:
    """Get notes for a specific claim

     Retrieves notes associated with a specific claim, identified by its external ID.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetClaimNotes200ResponseInner']]
    """

    return sync_detailed(
        claim_external_id=claim_external_id,
        client=client,
    ).parsed


async def asyncio_detailed(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimNotes200ResponseInner"]]]:
    """Get notes for a specific claim

     Retrieves notes associated with a specific claim, identified by its external ID.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetClaimNotes200ResponseInner']]]
    """

    kwargs = _get_kwargs(
        claim_external_id=claim_external_id,
    )

    response = await client.get_async_httpx_client().request(**kwargs)

    return _build_response(client=client, response=response)


async def asyncio(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimNotes200ResponseInner"]]]:
    """Get notes for a specific claim

     Retrieves notes associated with a specific claim, identified by its external ID.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetClaimNotes200ResponseInner']]
    """

    return (
        await asyncio_detailed(
            claim_external_id=claim_external_id,
            client=client,
        )
    ).parsed
