from http import HTTPStatus
from typing import Any, Optional, Union

import httpx

from ... import errors
from ...client import AuthenticatedClient, Client
from ...models.mcp_experiments_get_claims_for_policy_200_response_inner import (
    MCPExperimentsGetClaimsForPolicy200ResponseInner,
)
from ...models.mcp_experiments_get_v0_policies_for_dot404_response import MCPExperimentsGetV0PoliciesForDOT404Response
from ...types import Response


def _get_kwargs(
    policy_number: str,
) -> dict[str, Any]:
    _kwargs: dict[str, Any] = {
        "method": "get",
        "url": f"/mcp-experiments/policy/{policy_number}/claims",
    }

    return _kwargs


def _parse_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Optional[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimsForPolicy200ResponseInner"]]
]:
    if response.status_code == 200:
        response_200 = []
        _response_200 = response.json()
        for response_200_item_data in _response_200:
            response_200_item = MCPExperimentsGetClaimsForPolicy200ResponseInner.from_dict(response_200_item_data)

            response_200.append(response_200_item)

        return response_200
    if response.status_code == 404:
        response_404 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_404
    if client.raise_on_unexpected_status:
        raise errors.UnexpectedStatus(response.status_code, response.content)
    else:
        return None


def _build_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Response[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimsForPolicy200ResponseInner"]]
]:
    return Response(
        status_code=HTTPStatus(response.status_code),
        content=response.content,
        headers=response.headers,
        parsed=_parse_response(client=client, response=response),
    )


def sync_detailed(
    policy_number: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimsForPolicy200ResponseInner"]]
]:
    """Get claims for a policy

     Retrieves claims associated with a specific policy number.

    Args:
        policy_number (str):  Example: NISTK0017619-23.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetClaimsForPolicy200ResponseInner']]]
    """

    kwargs = _get_kwargs(
        policy_number=policy_number,
    )

    response = client.get_httpx_client().request(
        **kwargs,
    )

    return _build_response(client=client, response=response)


def sync(
    policy_number: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimsForPolicy200ResponseInner"]]
]:
    """Get claims for a policy

     Retrieves claims associated with a specific policy number.

    Args:
        policy_number (str):  Example: NISTK0017619-23.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetClaimsForPolicy200ResponseInner']]
    """

    return sync_detailed(
        policy_number=policy_number,
        client=client,
    ).parsed


async def asyncio_detailed(
    policy_number: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimsForPolicy200ResponseInner"]]
]:
    """Get claims for a policy

     Retrieves claims associated with a specific policy number.

    Args:
        policy_number (str):  Example: NISTK0017619-23.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetClaimsForPolicy200ResponseInner']]]
    """

    kwargs = _get_kwargs(
        policy_number=policy_number,
    )

    response = await client.get_async_httpx_client().request(**kwargs)

    return _build_response(client=client, response=response)


async def asyncio(
    policy_number: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[
    Union[MCPExperimentsGetV0PoliciesForDOT404Response, list["MCPExperimentsGetClaimsForPolicy200ResponseInner"]]
]:
    """Get claims for a policy

     Retrieves claims associated with a specific policy number.

    Args:
        policy_number (str):  Example: NISTK0017619-23.

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetV0PoliciesForDOT404Response, list['MCPExperimentsGetClaimsForPolicy200ResponseInner']]
    """

    return (
        await asyncio_detailed(
            policy_number=policy_number,
            client=client,
        )
    ).parsed
