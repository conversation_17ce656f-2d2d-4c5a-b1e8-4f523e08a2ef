from http import HTTPStatus
from typing import Any, Optional, Union, cast

import httpx

from ... import errors
from ...client import AuthenticatedClient, Client
from ...models.mcp_experiments_get_v0_policies_for_dot404_response import MCPExperimentsGetV0PoliciesForDOT404Response
from ...types import Response


def _get_kwargs(
    claim_external_id: str,
) -> dict[str, Any]:
    _kwargs: dict[str, Any] = {
        "method": "post",
        "url": f"/mcp-experiments/claim/{claim_external_id}/sync",
    }

    return _kwargs


def _parse_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Optional[Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    if response.status_code == 201:
        response_201 = cast(Any, None)
        return response_201
    if response.status_code == 400:
        response_400 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_400
    if response.status_code == 500:
        response_500 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_500
    if client.raise_on_unexpected_status:
        raise errors.UnexpectedStatus(response.status_code, response.content)
    else:
        return None


def _build_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Response[Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    return Response(
        status_code=HTTPStatus(response.status_code),
        content=response.content,
        headers=response.headers,
        parsed=_parse_response(client=client, response=response),
    )


def sync_detailed(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Synchronously update claim notes from source database

     Synchronously updates claim notes from the source database (NARS C3).
    This endpoint calls the NARS API to get the latest notes for the claim and
    persists them to the database.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]]
    """

    kwargs = _get_kwargs(
        claim_external_id=claim_external_id,
    )

    response = client.get_httpx_client().request(
        **kwargs,
    )

    return _build_response(client=client, response=response)


def sync(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Synchronously update claim notes from source database

     Synchronously updates claim notes from the source database (NARS C3).
    This endpoint calls the NARS API to get the latest notes for the claim and
    persists them to the database.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]
    """

    return sync_detailed(
        claim_external_id=claim_external_id,
        client=client,
    ).parsed


async def asyncio_detailed(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Synchronously update claim notes from source database

     Synchronously updates claim notes from the source database (NARS C3).
    This endpoint calls the NARS API to get the latest notes for the claim and
    persists them to the database.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]]
    """

    kwargs = _get_kwargs(
        claim_external_id=claim_external_id,
    )

    response = await client.get_async_httpx_client().request(**kwargs)

    return _build_response(client=client, response=response)


async def asyncio(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Synchronously update claim notes from source database

     Synchronously updates claim notes from the source database (NARS C3).
    This endpoint calls the NARS API to get the latest notes for the claim and
    persists them to the database.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[Any, MCPExperimentsGetV0PoliciesForDOT404Response]
    """

    return (
        await asyncio_detailed(
            claim_external_id=claim_external_id,
            client=client,
        )
    ).parsed
