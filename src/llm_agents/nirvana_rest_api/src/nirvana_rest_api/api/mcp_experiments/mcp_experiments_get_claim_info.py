from http import HTTPStatus
from typing import Any, Optional, Union

import httpx

from ... import errors
from ...client import AuthenticatedClient, Client
from ...models.mcp_experiments_get_claim_info_200_response import MCPExperimentsGetClaimInfo200Response
from ...models.mcp_experiments_get_v0_policies_for_dot404_response import MCPExperimentsGetV0PoliciesForDOT404Response
from ...types import Response


def _get_kwargs(
    claim_external_id: str,
) -> dict[str, Any]:
    _kwargs: dict[str, Any] = {
        "method": "get",
        "url": f"/mcp-experiments/claim/{claim_external_id}",
    }

    return _kwargs


def _parse_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Optional[Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    if response.status_code == 200:
        response_200 = MCPExperimentsGetClaimInfo200Response.from_dict(response.json())

        return response_200
    if response.status_code == 404:
        response_404 = MCPExperimentsGetV0PoliciesForDOT404Response.from_dict(response.json())

        return response_404
    if client.raise_on_unexpected_status:
        raise errors.UnexpectedStatus(response.status_code, response.content)
    else:
        return None


def _build_response(
    *, client: Union[AuthenticatedClient, Client], response: httpx.Response
) -> Response[Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    return Response(
        status_code=HTTPStatus(response.status_code),
        content=response.content,
        headers=response.headers,
        parsed=_parse_response(client=client, response=response),
    )


def sync_detailed(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Get core information for a specific claim

     Retrieves the core details for a specific claim, identified by its external ID.
    This endpoint returns summary information including the most recent status.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]
    """

    kwargs = _get_kwargs(
        claim_external_id=claim_external_id,
    )

    response = client.get_httpx_client().request(
        **kwargs,
    )

    return _build_response(client=client, response=response)


def sync(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Get core information for a specific claim

     Retrieves the core details for a specific claim, identified by its external ID.
    This endpoint returns summary information including the most recent status.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]
    """

    return sync_detailed(
        claim_external_id=claim_external_id,
        client=client,
    ).parsed


async def asyncio_detailed(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Response[Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Get core information for a specific claim

     Retrieves the core details for a specific claim, identified by its external ID.
    This endpoint returns summary information including the most recent status.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Response[Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]
    """

    kwargs = _get_kwargs(
        claim_external_id=claim_external_id,
    )

    response = await client.get_async_httpx_client().request(**kwargs)

    return _build_response(client=client, response=response)


async def asyncio(
    claim_external_id: str,
    *,
    client: Union[AuthenticatedClient, Client],
) -> Optional[Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]]:
    """Get core information for a specific claim

     Retrieves the core details for a specific claim, identified by its external ID.
    This endpoint returns summary information including the most recent status.

    Args:
        claim_external_id (str):

    Raises:
        errors.UnexpectedStatus: If the server returns an undocumented status code and Client.raise_on_unexpected_status is True.
        httpx.TimeoutException: If the request takes longer than Client.timeout.

    Returns:
        Union[MCPExperimentsGetClaimInfo200Response, MCPExperimentsGetV0PoliciesForDOT404Response]
    """

    return (
        await asyncio_detailed(
            claim_external_id=claim_external_id,
            client=client,
        )
    ).parsed
