# Environment variables for mcp_servers
# See mcp_servers/src/mcp_servers/lib/config.py for defaults and descriptions.
# Values here are examples or placeholders. Actual values should be set in a .env file.

ENVIRONMENT="development"
SENTRY_DSN=""

NIRVANA_MCP_USERNAME="ci_user"
LLAMA_CLOUD_API_KEY="your_llama_cloud_api_key_here_THIS_IS_OPTIONAL"
EXTEND_API_KEY="your_extend_api_key_here_THIS_IS_REQUIRED"

# The following REDIS_* variables are typically set by docker-compose.yml for this service,
# based on the 'redis' service definition. If running outside docker-compose, provide appropriate values.
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_DB="0"
REDIS_PASSWORD="lebowski" 
REDIS_SSL="false"

LLAMA_PARSE_STORAGE_NAMESPACE="llamastorage:policy"

# Port for the mcp_servers service (if configurable, otherwise it's set by uvicorn command)
# APP_PORT=8001

# URL for connecting to Redis (if mcp_servers uses it directly)
# Example for local Docker setup: redis://:lebowski@localhost:6379/0
# Example for Docker Compose setup (service name 'redis'): redis://:lebowski@redis:6379/0
REDIS_OM_URL=redis://:lebowski@redis:6379/0

# Log level (e.g., DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO