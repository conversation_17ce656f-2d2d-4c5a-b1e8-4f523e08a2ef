# mypy: ignore-errors
"""Integration tests for ingest_policy_document and Redis storage."""

import json
from typing import Any
from unittest.mock import AsyncMock

import pytest
import redis
import redis.asyncio as aioredis
from llama_index.core import Document as LlamaDocument
from loguru import logger

from mcp_servers.lib.config import Config
from mcp_servers.lib.enums import <PERSON>rsing<PERSON>end<PERSON>
from mcp_servers.lib.redis_client_manager import (
    RedisAtomKVStore,
    get_redis_document_store,
)
from mcp_servers.mcp_server_policy.ingestion_service import (
    SERVICE_CONTENT_TYPE_STR,
    SERVICE_PARSING_VENDOR_STR,
)
from mcp_servers.parse.storage.redis_storage import (
    DEFAULT_CONTENT_TYPE,
    _generate_cache_group_key,
    _generate_safe_component,
    persist_and_retrieve_parsed_document,
)


# Fixtures from conftest.py will be automatically used

# --- Test Data ---
TEST_API_POLICY_NUMBER = "TESTPOL-01"
TEST_DOCUMENT_ID = "d290f1ee-6c54-4b01-90e6-d701748f0851"
TEST_S3_ETAG = "test-s3-etag-12345"


@pytest.mark.asyncio
async def test_ingest_document_cold_cache_stores_to_redis(
    mocker: Any,
    redis_test_namespace: tuple[aioredis.Redis, str],
) -> None:
    """Test that ingesting a document with a cold cache stores the result in Redis."""
    client, namespace = redis_test_namespace

    # Load config initially (it will use env vars or pydantic defaults)
    config = Config.load_from_env()

    # Override Redis settings for the test using the new nested structure
    config.redis.host = client.connection_pool.connection_kwargs["host"]
    config.redis.port = client.connection_pool.connection_kwargs["port"]
    config.redis.db = 0
    config.redis.password = None  # Ensure password matches testcontainer setup
    config.redis.document_storage_namespace = namespace

    # Override other config settings as before
    # The service now uses Extend by default, so set that key.
    config.extend_api_key = "test-api-key"

    dummy_ctx = AsyncMock()
    mocker.patch("mcp_servers.lib.config.Config.load_from_env", return_value=config)

    dummy_file_url = "http://localhost:9999/dummy.pdf"
    test_documents = [
        LlamaDocument(
            doc_id="chunk1",
            text="This is the first chunk of the test document.",
            metadata={"foo": "bar"},
        ),
        LlamaDocument(
            doc_id="chunk2",
            text="This is the second chunk, with more details.",
            metadata={"foo": "baz"},
        ),
    ]

    # Dynamically import here to avoid issues if service definition changes
    # Reset singleton before getting docstore
    import mcp_servers.lib.redis_client_manager as redis_client_manager
    from mcp_servers.mcp_server_policy.ingestion_service import (
        PolicyDocumentIngestionService,
    )

    redis_client_manager._redis_document_store_instance = None

    # Mock for get_s3_object_etag used by service.ingest indirectly via _get_document_details
    mock_s3_etag_retriever = AsyncMock(return_value=TEST_S3_ETAG)

    service = PolicyDocumentIngestionService(
        config=config,
        storage_provider=get_redis_document_store,  # Use the actual provider
        fetch_document_url=AsyncMock(return_value=dummy_file_url),
        # Pass the same mock here for consistency if service uses it directly
        get_s3_object_etag=mock_s3_etag_retriever,
        logger=logger,
    )
    # The service creates its own parser now, so we mock the parse method on the created instance.
    service.parser.parse_document = AsyncMock(return_value=test_documents)

    returned_documents = await service.ingest(
        api_policy_number=TEST_API_POLICY_NUMBER,
        document_id=TEST_DOCUMENT_ID,
        ctx=dummy_ctx,
    )

    assert len(returned_documents) == 2
    redis_expected_doc_ids = []
    for idx, doc in enumerate(returned_documents):
        url_hash = _generate_safe_component(dummy_file_url.split("?")[0])
        etag_hash = _generate_safe_component(TEST_S3_ETAG)
        vendor_hash = _generate_safe_component(SERVICE_PARSING_VENDOR_STR)
        type_hash = _generate_safe_component(SERVICE_CONTENT_TYPE_STR)
        chunk_idx_hash = _generate_safe_component(f"idx={idx}")

        # The expected doc_id returned from ingestion service is a page number
        # while the one stored in redis is
        expected_doc_id = f"page-{idx + 1}"
        redis_expected_doc_id = (
            f"{namespace}:chunk:"
            f"url_h={url_hash}:etag_h={etag_hash}:vendor_h={vendor_hash}:type_h={type_hash}:"
            f"chk_h={chunk_idx_hash}"
        )
        assert doc.doc_id == expected_doc_id
        redis_expected_doc_ids.append(redis_expected_doc_id)
    assert returned_documents[0].text == "This is the first chunk of the test document."
    assert returned_documents[1].text == "This is the second chunk, with more details."

    docstore = await get_redis_document_store(config)
    if docstore is None:
        raise AssertionError("RedisDocumentStore is None after service call")

    node_collection = docstore._node_collection
    for doc_id in redis_expected_doc_ids:
        # type: ignore[misc]
        stored_chunk_data_json = await client.hget(node_collection, doc_id)
        assert stored_chunk_data_json is not None, (
            f"Chunk key {doc_id} not found in Redis hash {node_collection}."
        )

    cache_index_collection = namespace + "/cache_index"
    cache_group_key = _generate_cache_group_key(
        namespace=namespace,
        s3_url_base=dummy_file_url,
        s3_etag=TEST_S3_ETAG,
        parsing_vendor=SERVICE_PARSING_VENDOR_STR,
        content_type=SERVICE_CONTENT_TYPE_STR,
    )
    # type: ignore[misc]
    stored_index_data_json = await client.hget(cache_index_collection, cache_group_key)

    assert stored_index_data_json is not None, (
        f"Cache group key {cache_group_key} not found in Redis hash {cache_index_collection}."
    )

    stored_index_data = json.loads(stored_index_data_json)
    assert "__data__" in stored_index_data, "Cache index value missing '__data__' key."
    retrieved_chunk_ids = json.loads(stored_index_data["__data__"])

    assert isinstance(retrieved_chunk_ids, list), "Cache index data is not a list."
    assert sorted(retrieved_chunk_ids) == sorted(redis_expected_doc_ids), (
        f"Cache index {cache_group_key} has incorrect chunk IDs. Expected {redis_expected_doc_ids}, got {retrieved_chunk_ids}"
    )

    print(
        "Test test_ingest_document_cold_cache_stores_to_redis PASSED (with new checks)"
    )


@pytest.mark.asyncio
async def test_redis_sync_client_connects(
    redis_test_namespace: tuple[aioredis.Redis, str],
) -> None:
    """Test that the Redis sync client can connect and set/get values."""
    client, namespace = redis_test_namespace
    host = client.connection_pool.connection_kwargs["host"]
    port = client.connection_pool.connection_kwargs["port"]
    sync_client = redis.Redis(host=host, port=port, decode_responses=False)
    try:
        sync_client.set(f"{namespace}:sync_test_key", b"sync_test_value")
        value = sync_client.get(f"{namespace}:sync_test_key")
        assert value == b"sync_test_value"
        sync_client.delete(f"{namespace}:sync_test_key")
    finally:
        sync_client.close()


@pytest.mark.asyncio
async def test_redis_async_client_connects(
    redis_test_namespace: tuple[aioredis.Redis, str],
) -> None:
    """Test that the Redis async client can connect and set/get values."""
    client, namespace = redis_test_namespace
    await client.set(f"{namespace}:async_test_key", b"async_test_value")
    # type: ignore[misc]
    value = await client.get(f"{namespace}:async_test_key")
    assert value == b"async_test_value"
    await client.delete(f"{namespace}:async_test_key")


@pytest.mark.asyncio
async def test_redis_kvstore_connects(
    redis_test_namespace: tuple[aioredis.Redis, str],
) -> None:
    """Test that the Redis KVStore can connect and set/get values."""
    client, namespace = redis_test_namespace
    host = client.connection_pool.connection_kwargs["host"]
    port = client.connection_pool.connection_kwargs["port"]
    sync_client = redis.Redis(host=host, port=port, decode_responses=False)
    async_client = aioredis.Redis(host=host, port=port, decode_responses=False)
    try:
        kvstore = RedisAtomKVStore(
            redis_client=sync_client, async_redis_client=async_client
        )
        await kvstore.aput(
            f"{namespace}:kvstore_test_key", {"foo": "bar"}, collection=namespace
        )
        # type: ignore[misc]
        value = await kvstore.aget(
            f"{namespace}:kvstore_test_key", collection=namespace
        )
        assert value == {"foo": "bar"}
        await kvstore.adelete(f"{namespace}:kvstore_test_key", collection=namespace)
    finally:
        sync_client.close()
        await async_client.aclose()


@pytest.mark.asyncio
async def test_redis_document_store_connects(
    redis_test_namespace: tuple[aioredis.Redis, str],
) -> None:
    """Test that the RedisDocumentStore can connect and set/get values."""
    client, namespace = redis_test_namespace
    config = Config.load_from_env()

    config.redis.host = client.connection_pool.connection_kwargs["host"]
    config.redis.port = client.connection_pool.connection_kwargs["port"]
    config.redis.db = 0
    config.redis.password = None
    # Also ensure this is set on the redis object
    config.redis.document_storage_namespace = namespace

    import mcp_servers.lib.redis_client_manager as redis_client_manager

    redis_client_manager._redis_document_store_instance = None
    docstore = await get_redis_document_store(config)
    if docstore is None:
        raise AssertionError("RedisDocumentStore is None")

    key = f"{namespace}:docstore_test_key"
    value = {"foo": "bar"}
    # Use the KV store associated with the docstore
    await docstore._kvstore.aput(key, value, collection=docstore._node_collection)

    # type: ignore[misc]
    stored = await client.hget(docstore._node_collection, key)
    assert stored is not None
    assert json.loads(stored)["foo"] == "bar"
    await client.hdel(docstore._node_collection, key)  # type: ignore[misc]


@pytest.mark.asyncio
async def test_redis_document_store_decorator_stores_and_retrieves(
    redis_test_namespace: tuple[aioredis.Redis, str],
) -> None:
    """Test that the RedisDocumentStore decorator stores and retrieves parsed documents correctly."""
    client, namespace = redis_test_namespace
    config = Config.load_from_env()

    config.redis.host = client.connection_pool.connection_kwargs["host"]
    config.redis.port = client.connection_pool.connection_kwargs["port"]
    config.redis.db = 0
    config.redis.password = None
    # Also ensure this is set on the redis object
    config.redis.document_storage_namespace = namespace

    import mcp_servers.lib.redis_client_manager as redis_client_manager

    redis_client_manager._redis_document_store_instance = None
    # Eagerly get docstore to ensure connection exists for checks
    docstore = await get_redis_document_store(config)
    if docstore is None:
        raise AssertionError("RedisDocumentStore is None")

    parse_call_count = {"count": 0}

    @persist_and_retrieve_parsed_document(
        redis_docstore_provider=get_redis_document_store
    )
    async def dummy_parse(
        download_url: str,
        s3_url_base: str,
        s3_etag: str | None,
        config: Config | None,  # Add config here
        document_id_for_logging: str,
        parsing_vendor_str: str,
        content_type_str: str | None = None,
    ) -> list[LlamaDocument]:
        parse_call_count["count"] += 1
        logger.info(f"PARSER CALLED (Count: {parse_call_count['count']})")
        return [
            LlamaDocument(
                doc_id="parse_chunk1",
                text="Parsed Chunk 1 text",
                metadata={"origin": "parser"},
            ),
            LlamaDocument(
                doc_id="parse_chunk2",
                text="Parsed Chunk 2 text",
                metadata={"origin": "parser"},
            ),
        ]

    download_url = "http://localhost:9999/another_dummy.pdf"
    s3_url_base = download_url
    s3_etag = "another-s3-etag-67890"
    test_vendor = ParsingVendor.LLAMA_PARSE.value
    # document_id_for_logging = "test-doc-id-2" # This is injected by decorator    # --- First Call (Cold Cache) ---
    logger.info("--- DECORATOR TEST: First call (cold cache) ---")
    docs_first_call = await dummy_parse(
        download_url,
        s3_url_base,
        s3_etag,
        config,
        "test-doc-id-dummy-1",
        test_vendor,
        DEFAULT_CONTENT_TYPE,
    )
    assert parse_call_count["count"] == 1, "Parser should be called on the first run."
    assert len(docs_first_call) == 2

    expected_doc_ids_first_call = []
    for idx, doc in enumerate(docs_first_call):
        url_hash = _generate_safe_component(s3_url_base)
        etag_hash = _generate_safe_component(s3_etag)
        vendor_hash = _generate_safe_component(test_vendor)
        type_hash = _generate_safe_component(DEFAULT_CONTENT_TYPE)
        chunk_idx_hash = _generate_safe_component(f"idx={idx}")
        expected_doc_id = (
            f"{namespace}:chunk:"
            f"url_h={url_hash}:etag_h={etag_hash}:vendor_h={vendor_hash}:type_h={type_hash}:"
            f"chk_h={chunk_idx_hash}"
        )
        assert doc.doc_id == expected_doc_id
        expected_doc_ids_first_call.append(expected_doc_id)
    assert docs_first_call[0].text == "Parsed Chunk 1 text"
    assert docs_first_call[1].text == "Parsed Chunk 2 text"

    node_collection = docstore._node_collection
    cache_index_collection = namespace + "/cache_index"
    cache_group_key = _generate_cache_group_key(
        namespace=namespace,
        s3_url_base=s3_url_base,
        s3_etag=s3_etag,
        parsing_vendor=test_vendor,
        content_type=DEFAULT_CONTENT_TYPE,
    )

    for doc_id in expected_doc_ids_first_call:
        # type: ignore[misc]
        stored_chunk_data_json = await client.hget(node_collection, doc_id)
        assert stored_chunk_data_json is not None, (
            f"Chunk key {doc_id} not found after first call."
        )

    # type: ignore[misc]
    stored_index_data_json = await client.hget(cache_index_collection, cache_group_key)
    assert stored_index_data_json is not None, (
        f"Cache group key {cache_group_key} not found after first call."
    )
    stored_index_data = json.loads(stored_index_data_json)
    retrieved_chunk_ids = json.loads(stored_index_data["__data__"])
    assert sorted(retrieved_chunk_ids) == sorted(expected_doc_ids_first_call), (
        "Cache index has incorrect chunk IDs after first call."
    )

    # --- Second Call (Warm Cache) ---
    logger.info("--- DECORATOR TEST: Second call (warm cache) ---")
    # Ensure singleton is NOT reset here
    docs_second_call = await dummy_parse(
        download_url,
        s3_url_base,
        s3_etag,
        config,
        "test-doc-id-dummy-2",
        test_vendor,
        DEFAULT_CONTENT_TYPE,
    )
    assert parse_call_count["count"] == 1, (
        "Parser should NOT be called on the second run (cache hit)."
    )
    assert len(docs_second_call) == 2

    retrieved_doc_ids = sorted([doc.doc_id for doc in docs_second_call])
    assert retrieved_doc_ids == sorted(expected_doc_ids_first_call), (
        "Retrieved document IDs on second call do not match stored IDs."
    )
    assert sorted([doc.text for doc in docs_second_call]) == sorted(
        ["Parsed Chunk 1 text", "Parsed Chunk 2 text"]
    )

    print(
        "Test test_redis_document_store_decorator_stores_and_retrieves PASSED (with cache hit check)"
    )


# TODO: Add more test cases as described in the file.
