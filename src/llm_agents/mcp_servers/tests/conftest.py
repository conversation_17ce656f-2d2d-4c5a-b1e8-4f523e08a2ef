"""Pytest fixtures for integration testing with Redis testcontainer (parallel-safe, namespace-per-test isolation).

- Starts a single Redis container for the test session (fast, resource-efficient).
- Each test gets a function-scoped fixture providing (client, namespace).
- Each test uses a unique namespace (key prefix) for isolation.
- All keys in the namespace are deleted before each test.
- This pattern is safe for parallel test execution (e.g., pytest-xdist).
"""

import socket
import uuid
from collections.abc import AsyncGenerator, Generator
from threading import Thread

import pytest
import pytest_asyncio
import redis.asyncio as aioredis
import uvicorn
from fastapi import FastAPI
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from testcontainers.redis import RedisContainer  # type: ignore[import-untyped]

import mcp_servers.lib.redis_client_manager as redis_client_manager


@pytest.fixture(scope="session")
def redis_container() -> Generator[tuple[str, int], None, None]:
    """Start a single Redis container for the test session."""
    with RedisContainer(
        "redis:7.2.0"
    ) as redis_container_instance:  # Renamed to avoid conflict
        host = redis_container_instance.get_container_host_ip()
        port = int(redis_container_instance.get_exposed_port(6379))
        yield host, port


@pytest_asyncio.fixture(scope="function")
async def redis_test_namespace(
    redis_container: tuple[str, int],
) -> AsyncGenerator[tuple[aioredis.Redis, str], None]:
    """Function-scoped fixture that yields (client, namespace) for each test.

    - Connects to the session Redis container.
    - Uses a unique namespace (UUID) per test for key isolation.
    - Cleans up all keys in the namespace before yielding.
    """
    host, port = redis_container
    client = aioredis.Redis(host=host, port=port, decode_responses=False)
    namespace = f"testns:{uuid.uuid4().hex}"
    try:
        keys = await client.keys(f"{namespace}:*")
        if keys:
            await client.delete(*keys)
        yield client, namespace
    finally:
        keys = await client.keys(f"{namespace}:*")
        if keys:
            await client.delete(*keys)
        await client.aclose()


@pytest.fixture(autouse=True)
def reset_redis_document_store_singleton() -> None:
    """Reset the RedisDocumentStore singleton instance for test isolation."""
    redis_client_manager._redis_document_store_instance = None


@pytest.fixture(scope="session")
def llamaparse_test_server() -> Generator[str, None, None]:
    """Starts a FastAPI server that mimics the LlamaParse API for integration tests."""
    app = FastAPI()

    class ParseRequest(BaseModel):
        url: str

    @app.post("/parse")
    async def parse_endpoint(req: ParseRequest) -> JSONResponse:
        # Optionally check req.url, but for test just return fixed chunks
        return JSONResponse(
            [
                {
                    "doc_id": "chunk1",
                    "text": "This is the first chunk of the test document.",
                    "metadata": {"foo": "bar"},
                },
                {
                    "doc_id": "chunk2",
                    "text": "This is the second chunk, with more details.",
                    "metadata": {"foo": "baz"},
                },
            ]
        )

    # Find a free port
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(("127.0.0.1", 0))
        port = s.getsockname()[1]
    server = uvicorn.Server(
        config=uvicorn.Config(
            app, host="127.0.0.1", port=port, log_level="error", ws="wsproto"
        )
    )
    thread = Thread(target=server.run, daemon=True)
    thread.start()
    # Wait for server to be ready
    import time

    import httpx

    for _ in range(20):
        try:
            r = httpx.post(
                f"http://127.0.0.1:{port}/parse",
                json={"url": "http://dummy-url-for-test"},
                timeout=0.5,
            )
            if r.status_code == 200:
                break
        except Exception:
            time.sleep(0.1)
    yield f"http://127.0.0.1:{port}"
    server.should_exit = True
    thread.join(timeout=2)
