# mypy: ignore-errors
"""Tests for MCP policy server tools, focusing on data transformation and API interactions."""

import json
import typing  # Added for Any
from unittest.mock import AsyncMock, MagicMock

import pytest
from llama_index.core import Document as LlamaDocument
from mcp.server.fastmcp import Context

from mcp_servers.mcp_server_policy.ingestion_service import (
    SERVICE_CONTENT_TYPE_STR,
    SERVICE_PARSING_VENDOR_STR,
)
from mcp_servers.mcp_server_policy.tools import ingest_policy_document


# A sample raw document dictionary, similar to what LlamaDocument.to_dict() might produce
# (taken from a simplified version of formatted_raw_data.json structure)
SAMPLE_RAW_DOC_DICT_FULL: dict[str, typing.Any] = {
    "id_": "test_chunk_id_1",
    "embedding": None,
    "metadata": {
        "source_document_id": "12345678-1234-5678-1234-************",
        "api_policy_number": "TESTPOL123-24",
        "s3_url_base": "s3://bucket/path/to/doc.pdf",
        "s3_etag_at_ingest": "test_etag_123",
        "ingestion_timestamp": "2023-01-01T12:00:00Z",
        "parsing_vendor": SERVICE_PARSING_VENDOR_STR,
        "content_type": SERVICE_CONTENT_TYPE_STR,
        "extra_field_to_be_removed": "should_not_be_in_response",
        "page_label": "1",  # Example of another field that might be in LlamaDoc metadata
    },
    "excluded_embed_metadata_keys": ["extra_field_to_be_removed"],
    "excluded_llm_metadata_keys": [],
    "relationships": {},
    # Old structure
    "text_resource": {"text": "This is test content for chunk 1."},
    "text": "This is test content for chunk 1.",  # Correct field for text
    "class_name": "Document",
    "metadata_template": "{key}: {value}",
    "metadata_separator": "\\n",
}

SAMPLE_RAW_DOC_DICT_MINIMAL_METADATA: dict[str, typing.Any] = {
    "id_": "test_chunk_id_2",
    "metadata": {  # No s3_etag_at_ingest, no parsing_vendor, no content_type
        "source_document_id": "87654321-4321-8765-4321-876543218765",
        "api_policy_number": "TESTPOL456-24",
        "s3_url_base": "s3://bucket/path/to/another.pdf",
        "ingestion_timestamp": "2023-01-02T12:00:00Z",
    },
    "text": "This is test content for chunk 2.",
}


@pytest.mark.asyncio
async def test_ingest_policy_document_trims_output(mocker):
    """Tests that the ingest_policy_document tool correctly trims extraneous fields.

    This ensures that the LlamaDocument dictionaries are properly minimized before
    being returned as a JSON string.
    """
    mock_ctx = MagicMock(spec=Context)
    mock_config = MagicMock()
    mock_config.llama_parse_api_key = "fake_api_key"

    # Create LlamaDocument objects from our sample raw dicts
    # LlamaDocument constructor takes text and metadata directly.
    # Other fields are attributes or handled by to_dict()
    # Copy to avoid modification
    doc1_metadata: dict[str, typing.Any] = {**SAMPLE_RAW_DOC_DICT_FULL["metadata"]}
    doc1 = LlamaDocument(
        id_=SAMPLE_RAW_DOC_DICT_FULL["id_"],
        text=SAMPLE_RAW_DOC_DICT_FULL["text"],
        metadata=doc1_metadata,
    )
    # Ensure the extra metadata field is there before enrichment by service (which will add its own)
    # In a real scenario, the service adds some of these, but for testing the tool's trimming,
    # we assume the docs come from the service already enriched.
    # The important part for trimming is that `to_dict()` will produce many fields.

    doc2_metadata: dict[str, typing.Any] = {
        **SAMPLE_RAW_DOC_DICT_MINIMAL_METADATA["metadata"]
    }
    doc2 = LlamaDocument(
        id_=SAMPLE_RAW_DOC_DICT_MINIMAL_METADATA["id_"],
        text=SAMPLE_RAW_DOC_DICT_MINIMAL_METADATA["text"],
        metadata=doc2_metadata,
    )

    # Mock the PolicyDocumentIngestionService and its ingest method
    # mock_ingestion_service_instance = AsyncMock()
    # mock_ingestion_service_instance.ingest.return_value = [doc1, doc2]

    mocker.patch(
        "mcp_servers.mcp_server_policy.ingestion_service.PolicyDocumentIngestionService.ingest",
        new_callable=AsyncMock,
        return_value=[doc1, doc2],
    )
    mocker.patch(
        "mcp_servers.mcp_server_policy.tools.Config.load_from_env",
        return_value=mock_config,
    )
    mocker.patch(
        "mcp_servers.mcp_server_policy.tools.get_redis_document_store",
        return_value=AsyncMock(),
    )
    # Add a mock for get_policy_document_url to prevent network calls
    mocker.patch(
        "mcp_servers.mcp_server_policy.ingestion_service._fetch_document_url",
        return_value="https://fake.url/document.pdf",
    )

    # Call the tool function with a valid UUID
    json_response_str = await ingest_policy_document(
        api_policy_number="TESTPOL123-24",
        document_id="12345678-1234-5678-1234-************",
        ctx=mock_ctx,
    )

    # Parse the JSON string
    response_data = json.loads(json_response_str)

    assert isinstance(response_data, list)
    assert len(response_data) == 2

    # --- Check first document (fully populated) ---
    trimmed_doc1 = response_data[0]
    assert trimmed_doc1["id_"] == SAMPLE_RAW_DOC_DICT_FULL["id_"]
    assert trimmed_doc1["text"] == SAMPLE_RAW_DOC_DICT_FULL["text"]

    expected_metadata1 = {
        "source_document_id": SAMPLE_RAW_DOC_DICT_FULL["metadata"][
            "source_document_id"
        ],
        "api_policy_number": SAMPLE_RAW_DOC_DICT_FULL["metadata"]["api_policy_number"],
        "s3_url_base": SAMPLE_RAW_DOC_DICT_FULL["metadata"]["s3_url_base"],
        "s3_etag_at_ingest": SAMPLE_RAW_DOC_DICT_FULL["metadata"]["s3_etag_at_ingest"],
        "ingestion_timestamp": SAMPLE_RAW_DOC_DICT_FULL["metadata"][
            "ingestion_timestamp"
        ],
        # This was in input
        "parsing_vendor": SAMPLE_RAW_DOC_DICT_FULL["metadata"]["parsing_vendor"],
        # This was in input
        "content_type": SAMPLE_RAW_DOC_DICT_FULL["metadata"]["content_type"],
    }
    assert trimmed_doc1["metadata"] == expected_metadata1

    # Check that no extraneous top-level keys are present
    assert "embedding" not in trimmed_doc1
    assert "excluded_embed_metadata_keys" not in trimmed_doc1
    assert "relationships" not in trimmed_doc1
    # This one is particularly important to remove
    assert "text_resource" not in trimmed_doc1
    assert "class_name" not in trimmed_doc1
    assert "metadata_template" not in trimmed_doc1

    # Check that no extraneous metadata keys are present
    assert "extra_field_to_be_removed" not in trimmed_doc1["metadata"]
    # This also should be trimmed by the current logic
    assert "page_label" not in trimmed_doc1["metadata"]

    # --- Check second document (minimal metadata, defaults for vendor/type should apply) ---
    trimmed_doc2 = response_data[1]
    assert trimmed_doc2["id_"] == SAMPLE_RAW_DOC_DICT_MINIMAL_METADATA["id_"]
    assert trimmed_doc2["text"] == SAMPLE_RAW_DOC_DICT_MINIMAL_METADATA["text"]

    # The second document should not include duplicated metadata to save payload size
    assert "metadata" not in trimmed_doc2
