[project]
name = "mcp_servers"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "async-lru>=2.0.5",
    "attrs>=25.3.0",
    "boto3>=1.38.1",
    "boto3-stubs[boto3]>=1.38.1",
    "click>=8.1.8",
    "extend-ai>=0.0.3",
    "httpx>=0.28.1",
    "jinja2>=3.1.6",
    "llama-cloud==0.1.18",
    "llama-cloud-services==0.6.14",
    "llama-index>=0.12.31",
    "llama-index-core>=0.12.31",
    "llama-index-storage-docstore-redis>=0.3.0",
    "loguru>=0.7.3",
    "mcp==1.12.2",
    "python-dateutil>=2.9.0.post0",
    "python-dotenv>=1.1.0",
    "pytz>=2025.2",
    "uvicorn>=0.29.0",
    "redis>=5.0.0",
    "types-python-dateutil>=2.9.0.20241206",
    "sentry-sdk[starlette]>=2.28.0",
]

[tool.uv.sources]
nirvana-rest-api = {path = "../nirvana_rest_api", editable = true}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
only-include = ["src/mcp_servers"]
sources = ["src"]

# Ruff ignores
[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = ["S101"]

# [tool.mypy]
# mypy_path = "stubs"

[[tool.mypy.overrides]]
module = "tests.test_ingestion_storage"
disable_error_code = ["misc", "unused-ignore"]

[tool.poe.tasks]
start = { cmd = "uvicorn mcp_servers.main:app --reload --port 8001" }
init-env = { shell = "cp -n .env.example .env" }
