"""Some shared utilities for this project."""

from mcp import <PERSON>rror<PERSON><PERSON>, McpError
from mcp.server.auth.middleware.auth_context import get_access_token
from mcp.server.fastmcp import Context
from mcp.types import INTERNAL_ERROR

from nirvana_rest_api.client import AuthenticatedClient


def get_nv_mcp_experiments_api_client(_: Context) -> AuthenticatedClient:
    """Construct the Nirvana MCP Experiments REST API client using the session ID in context."""
    access_token = get_access_token()
    if access_token is None:
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="No access token found",
            )
        )
    return AuthenticatedClient(
        base_url="https://mcp-experiments-api.prod.nirvanatech.com",
        token=access_token.token,
        auth_header_name="Clerk-Authorization",
        prefix="Bearer",
    )
