"""OAuth provider implementation for the Nirvana MCP server."""

from async_lru import alru_cache
from loguru import logger
from mcp import ErrorData, McpError
from mcp.server.auth.provider import (
    AccessToken,
    TokenVerifier,
)
from mcp.types import INTERNAL_ERROR, PARSE_ERROR
from starlette.authentication import AuthenticationError

from nirvana_rest_api.api.auth.get_me import asyncio_detailed as get_me
from nirvana_rest_api.client import AuthenticatedClient
from nirvana_rest_api.models.roles import Roles
from nirvana_rest_api.types import UNSET, Unset


class NirvanaTokenVerifier(TokenVerifier):
    """A simple OAuth provider that does not support dynamic client registration."""

    # Bound the cache with maxsize and ttl (1 hour)
    @alru_cache(maxsize=128, ttl=3600)
    async def verify_token(self, token: str) -> AccessToken | None:
        """Load an access token from a string."""
        if not token:
            raise AuthenticationError("No token provided")

        logger.debug(f"Checking token: {token}")
        async with AuthenticatedClient(
            base_url="https://api.prod.nirvanatech.com",
            token=token,
            # Nirvana API expects Clerk-Authorization header only
            auth_header_name="Clerk-Authorization",
            prefix="Bearer",
        ) as client:
            response = await get_me(client=client)

        # Handle different response statuses
        if response.status_code >= 500:
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message=f"Received a {response.status_code} error from the Nirvana REST API",
                )
            )
        elif response.status_code >= 400:
            logger.warning(f"Nirvana API returned 4xx error: {response.status_code}.")
            # 401/403 often mean invalid/expired token
            error_message = (
                f"Authentication error ({response.status_code}) from Nirvana REST API"
            )
            if response.status_code in (401, 403):
                error_message = f"Invalid or expired token ({response.status_code})"
            raise AuthenticationError(error_message)
        elif response.status_code != 200:
            logger.error(f"Nirvana API returned non-200 status: {response.status_code}")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message=f"Received non-200 response ({response.status_code}) from Nirvana REST API",
                )
            )
        elif response.parsed is None:
            logger.error("Nirvana API returned 200 but failed to parse response")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message=f"Failed to parse valid response from Nirvana REST API (status {response.status_code})",
                )
            )

        user_info = response.parsed
        scopes = self._extract_scopes_from_roles(user_info.roles)

        return AccessToken(
            token=token,
            client_id=user_info.email,  # Till we find a better alternative!
            scopes=scopes,
            expires_at=None,
        )

    def _extract_scopes_from_roles(self, roles: Unset | Roles) -> list[str]:
        """Extract scope strings from user roles."""
        scopes: list[str] = []
        if roles is UNSET:
            return scopes

        role_mapping = {
            "nirvana_roles": "nirvana",
            "agency_roles": "agency",
            "fleet_roles": "fleet",
        }

        for role_type_attr, scope_prefix in role_mapping.items():
            role_list = getattr(roles, role_type_attr, None)
            if role_list:
                for role in role_list:
                    if role is not UNSET and hasattr(role, "role"):
                        scopes.append(f"{scope_prefix}:{role.role}")

        return scopes
