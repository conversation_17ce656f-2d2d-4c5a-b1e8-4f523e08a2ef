"""Custom MCP subclasses to handle initialization race conditions gracefully.

This module provides custom subclasses for the MCP server components (`FastMCP`,
`MCPServer`, `ServerSession`) to address an intermittent race condition.

Problem:
The standard `mcp.server.session.ServerSession` raises a `RuntimeError` if it
receives a client request (other than `InitializeRequest`) before it has received
the `InitializedNotification` from the client. This can happen if the client sends
requests immediately after receiving the `InitializeResult`. This `RuntimeError`
unhandled in the session's receive loop can crash the server session task group.

Solution:
We introduce a hierarchy of custom classes:
1.  `CustomServerSession`: Inherits `ServerSession` and overrides `_received_request`.
    Instead of raising `RuntimeError`, it sends a specific JSON-RPC error
    (`code=-32000, message="Server not initialized"`) back to the client if a
    request arrives before the session state is `Initialized`.
2.  `CustomMCPServer`: Inherits `mcp.server.lowlevel.server.Server` and overrides
    its `run` method solely to instantiate `CustomServerSession` instead of the
    default `ServerSession`.
3.  `CustomFastMCP`: Inherits `mcp.server.fastmcp.FastMCP` and overrides its
    `__init__` method solely to instantiate `CustomMCPServer` instead of the
    default low-level `Server`.

Usage:
Instead of using `FastMCP` directly (or a subclass like `NirvanaMCP` inheriting
from `FastMCP`), applications should use `CustomFastMCP` (or inherit from it)
to ensure the modified session handling logic is active.


Open Issues tracking this issue in various places:
MCP Python SDK: https://github.com/modelcontextprotocol/python-sdk/issues/423
Cursor: https://github.com/getcursor/cursor/issues/2998

There's a patch suggested in the MCP Python SDK issue, but we believe our
solution is more robust. https://github.com/modelcontextprotocol/python-sdk/issues/423#issuecomment-2799890581
"""

from contextlib import AsyncExitStack
from typing import Any

import anyio
from anyio.streams.memory import MemoryObjectReceiveStream, MemoryObjectSendStream

# Loguru logger is used elsewhere in the project, let's use it here too for consistency
# Note: The original code used standard logging based on MCPServer.__module__
from loguru import logger
from mcp.server.fastmcp.prompts import PromptManager
from mcp.server.fastmcp.resources import ResourceManager
from mcp.server.fastmcp.server import (
    FastMCP,
    Settings,
    default_lifespan,
    lifespan_wrapper,
)
from mcp.server.fastmcp.tools import ToolManager
from mcp.server.fastmcp.utilities.logging import configure_logging
from mcp.server.lowlevel.server import Server as MCPServer
from mcp.server.models import InitializationOptions
from mcp.server.session import InitializationState, ServerSession
from mcp.shared.session import RequestResponder
from mcp.types import (
    ClientRequest,
    ErrorData,
    InitializeRequest,
    ServerResult,
)


# Step 1: Custom ServerSession
class CustomServerSession(ServerSession):
    """Custom `ServerSession` that sends an error instead of crashing on early requests.

    Overrides `_received_request` to handle requests received before the session
    is fully initialized by sending a JSON-RPC error response, rather than raising
    a `RuntimeError` which would terminate the session's processing loop.
    """

    async def _received_request(
        self, responder: RequestResponder[ClientRequest, ServerResult]
    ) -> None:
        """Handles incoming client requests, modified for robust initialization.

        Delegates to the parent implementation for InitializeRequest or if the
        session is already Initialized. Intercepts other requests before
        initialization to send an error instead of crashing.
        """
        # Check for the specific condition we want to override:
        # It's NOT an InitializeRequest AND the session is NOT Initialized.
        if (
            not isinstance(responder.request.root, InitializeRequest)
            and self._initialization_state != InitializationState.Initialized
        ):
            # Implement our custom behavior: Send an error instead of raising
            logger.warning(
                "Received request before initialization was complete. Sending error response."
            )
            with responder:
                await responder.respond(
                    ErrorData(
                        code=-32000,  # Server error
                        message="Server not initialized",
                    )
                )
            # Return early to prevent the superclass logic (which would raise)
            return
        # let the original implementation handle all other scenarios.
        await super()._received_request(responder)


# Step 2: Custom MCPServer (low-level)
class CustomMCPServer(MCPServer):
    """Custom low-level MCP Server that uses `CustomServerSession`.

    Inherits from the base `mcp.server.lowlevel.server.Server` and overrides
    the `run` method solely to ensure that `CustomServerSession` is instantiated
    instead of the default `ServerSession`. All other behavior is inherited.
    """

    async def run(
        self,
        read_stream: MemoryObjectReceiveStream,  # Use Any for generic compatibility
        write_stream: MemoryObjectSendStream,
        initialization_options: InitializationOptions,
        raise_exceptions: bool = False,
        stateless: bool = False,
    ) -> None:
        """Overrides the base `run` method to instantiate `CustomServerSession`.

        This method replicates the logic of the original `MCPServer.run` but
        changes the session instantiation line to use `CustomServerSession`.
        """
        async with AsyncExitStack() as stack:
            # Pass self of type CustomMCPServer to lifespan
            lifespan_context = await stack.enter_async_context(self.lifespan(self))
            # Instantiate CustomServerSession instead of ServerSession
            session = await stack.enter_async_context(
                CustomServerSession(read_stream, write_stream, initialization_options)
            )

            # The rest of the original run method logic
            async with anyio.create_task_group() as tg:
                async for message in session.incoming_messages:
                    tg.start_soon(
                        self._handle_message,
                        message,
                        session,  # Pass the custom session instance
                        lifespan_context,
                        raise_exceptions,
                    )


# Step 3: Custom FastMCP
class CustomFastMCP(FastMCP):
    """Custom `FastMCP` server facade that uses `CustomMCPServer`.

    Inherits from `mcp.server.fastmcp.FastMCP` and overrides the `__init__`
    method solely to ensure that `CustomMCPServer` is instantiated internally
    instead of the default low-level `Server`. This links the high-level facade
    to our custom low-level server and session handling.
    """

    def __init__(
        self, name: str | None = None, instructions: str | None = None, **settings: Any
    ):
        """Overrides `__init__` to instantiate `CustomMCPServer`.

        This method replicates the logic of the original `FastMCP.__init__` but
        changes the internal `_mcp_server` instantiation to use `CustomMCPServer`.
        """
        # Replicate FastMCP.__init__ logic but use CustomMCPServer

        self.settings = Settings(**settings)

        # Instantiate CustomMCPServer instead of the default MCPServer
        self._mcp_server = CustomMCPServer(
            name=name or "FastMCP",  # Keep original default name
            instructions=instructions,
            # Pass self of type CustomFastMCP to lifespan_wrapper
            lifespan=lifespan_wrapper(self, self.settings.lifespan)
            if self.settings.lifespan
            else default_lifespan,
        )

        # Initialize managers and handlers as in FastMCP.__init__
        self._tool_manager = ToolManager(
            warn_on_duplicate_tools=self.settings.warn_on_duplicate_tools
        )
        self._resource_manager = ResourceManager(
            warn_on_duplicate_resources=self.settings.warn_on_duplicate_resources
        )
        self._prompt_manager = PromptManager(
            warn_on_duplicate_prompts=self.settings.warn_on_duplicate_prompts
        )
        self.dependencies = self.settings.dependencies

        # Set up MCP protocol handlers using the inherited method from FastMCP
        # This ensures all the standard tool/resource/prompt registration works.
        self._setup_handlers()

        # Configure logging using the inherited method
        configure_logging(self.settings.log_level)
