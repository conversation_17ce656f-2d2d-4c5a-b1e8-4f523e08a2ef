"""Configuration management for the MCP server.

This module handles loading and managing configuration settings for the MCP server,
including environment variables and configuration files.
"""

import os

import dotenv
from loguru import logger
from pydantic import BaseModel, Field


dotenv.load_dotenv()


class RedisConfig(BaseModel):
    """Configuration specific to Redis."""

    host: str | None = Field(default_factory=lambda: os.getenv("REDIS_HOST"))
    port: int = Field(default_factory=lambda: int(os.getenv("REDIS_PORT", "6379")))
    db: int = Field(default_factory=lambda: int(os.getenv("REDIS_DB", "0")))
    password: str | None = Field(default_factory=lambda: os.getenv("REDIS_PASSWORD"))
    ssl: bool = Field(
        default_factory=lambda: os.getenv("REDIS_SSL", "false").lower() == "true"
    )
    document_storage_namespace: str = Field(
        default_factory=lambda: os.getenv(
            "DOCUMENT_STORAGE_NAMESPACE", "docstorage:policy"
        )
    )


class Config(BaseModel):
    """Configuration for the server. Uses Pydantic for validation."""

    # LlamaParse configuration
    llama_parse_api_key: str | None = Field(
        default_factory=lambda: os.getenv("LLAMA_CLOUD_API_KEY")
    )

    # Extend.ai configuration
    extend_api_key: str | None = Field(
        default_factory=lambda: os.getenv("EXTEND_API_KEY")
    )

    redis: RedisConfig = Field(default_factory=RedisConfig)
    sentry_dsn: str | None = Field(default_factory=lambda: os.getenv("SENTRY_DSN"))
    environment: str | None = Field(
        default_factory=lambda: os.getenv("ENVIRONMENT", "development")
    )

    @classmethod
    def load_from_env(cls) -> "Config":
        """Get the configuration for the server using environment variables."""
        llama_api_key = os.getenv("LLAMA_CLOUD_API_KEY")
        extend_api_key = os.getenv("EXTEND_API_KEY")

        # At least one parsing service must be configured
        if not llama_api_key and not extend_api_key:
            raise ValueError(
                "At least one parsing service must be configured. Please set either "
                "LLAMA_CLOUD_API_KEY or EXTEND_API_KEY."
            )

        try:
            config_instance = cls(
                llama_parse_api_key=llama_api_key,
                extend_api_key=extend_api_key,
            )
        except Exception as e:
            logger.error(f"Error creating Config instance: {e}")
            raise

        return config_instance


# Load the configuration globally when the module is imported.
# This makes it available as `from mcp_servers.lib.config import settings`
try:
    settings = Config.load_from_env()
except ValueError as e:
    logger.critical(f"CRITICAL CONFIGURATION ERROR: {e}. Server cannot start.")
    # Re-raising is important so the application fails to start if config is broken.
    raise
