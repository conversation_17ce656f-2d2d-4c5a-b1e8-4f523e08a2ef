"""Manages the RedisDocumentStore instance for storage."""

import redis
import redis.asyncio as aioredis
from llama_index.storage.docstore.redis import RedisDocumentStore
from llama_index.storage.kvstore.redis import RedisKVStore as RedisAtomKVStore
from loguru import logger

from mcp_servers.lib.config import Config


_redis_document_store_instance: RedisDocumentStore | None = None


async def get_redis_document_store(config: Config) -> RedisDocumentStore | None:
    """Gets a singleton instance of RedisDocumentStore.

    Initializes clients and store on first call if Redis is configured.
    """
    global _redis_document_store_instance

    if not config.redis.host:
        logger.info(
            "Redis host not configured. Parsed document storage/retrieval will be disabled."
        )
        return None

    if _redis_document_store_instance is None:
        logger.info(
            f"Attempting to create RedisDocumentStore instance for host: {config.redis.host}"
        )
        try:
            sync_redis_client = redis.Redis(
                host=config.redis.host,
                port=config.redis.port,
                db=config.redis.db,
                password=config.redis.password,
                decode_responses=False,
                socket_connect_timeout=5,
                ssl=config.redis.ssl,
            )
            logger.info(
                f"SYNC Redis client connecting to host={config.redis.host}, port={config.redis.port}, db={config.redis.db}"
            )
            sync_redis_client.ping()
            logger.info("Successfully connected to Redis (sync client).")

            async_redis_client = aioredis.Redis(
                host=config.redis.host,
                port=config.redis.port,
                db=config.redis.db,
                password=config.redis.password,
                decode_responses=False,
                socket_connect_timeout=5,
                ssl=config.redis.ssl,
            )
            logger.info(
                f"ASYNC Redis client connecting to host={config.redis.host}, port={config.redis.port}, db={config.redis.db}"
            )
            await async_redis_client.ping()
            logger.info("Successfully connected to Redis (async client).")

            kv_store = RedisAtomKVStore(
                redis_client=sync_redis_client,
                async_redis_client=async_redis_client,
            )
            logger.info(
                f"RedisKVStore initialized. Using storage namespace for collection: "
                f"{config.redis.document_storage_namespace}"
            )

            _redis_document_store_instance = RedisDocumentStore(
                redis_kvstore=kv_store,
                namespace=config.redis.document_storage_namespace,
            )
            logger.info("RedisDocumentStore instance created successfully.")

        except redis.exceptions.ConnectionError as e:
            logger.error(
                f"Failed to connect to Redis at {config.redis.host}:{config.redis.port}. Storage disabled. Error: {e}"
            )
            _redis_document_store_instance = None
            return None
        except Exception as e:
            logger.error(
                f"An unexpected error occurred while creating RedisDocumentStore: {e}",
                exc_info=True,
            )
            _redis_document_store_instance = None
            return None

    return _redis_document_store_instance
