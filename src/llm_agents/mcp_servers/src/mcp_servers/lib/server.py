"""Main server setup and application logic."""

from loguru import logger
from mcp.server.sse import SseServerTransport
from starlette.routing import BaseRoute, Mount
from starlette.types import Receive, Scope, Send

from mcp.server.models import InitializationOptions
from mcp.server.lowlevel import NotificationOptions

# We import the custom FastMCP class as FastMCP. Once fixes are made to the SDK,
# we can potentially use the FastMCP class (from mcp.server.fastmcp) directly.
from mcp_servers.lib.custom_mcp import CustomFastMCP as FastMCP


class NirvanaMCP(FastMCP):
    """A Nirvana MCP server."""

    def __init__(
        self,
        server_name: str,
        default_timeout_seconds: int,
        transport: SseServerTransport,
    ):
        super().__init__()
        self.server_name = server_name
        self.default_timeout_seconds = default_timeout_seconds
        self.transport = transport
        # Logger accessible within create_asgi_application if needed, or pass explicitly
        self.logger = logger.bind(server_name=server_name)

    def get_routes(self, namespace: str) -> list[BaseRoute]:
        """Get the routes to be added to a starlette app to serve the NirvanaMCP server."""

        async def handle_sse_connection(
            scope: Scope, receive: Receive, send: Send
        ) -> None:
            self.logger.debug(
                f"({self.server_name}) SSE connection handler invoked for {scope.get('path')}"
            )

            init_options = InitializationOptions(
                server_name=self.server_name,  # Use NirvanaMCP's server_name
                server_version="0.1.0",  # Hardcode for now, make configurable later
                capabilities=self._mcp_server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            )

            async with self.transport.connect_sse(scope, receive, send) as streams:
                await self._mcp_server.run(
                    streams[0],
                    streams[1],
                    init_options,
                )

        async def handle_post_message_endpoint(
            scope: Scope, receive: Receive, send: Send
        ) -> None:
            self.logger.debug(
                f"({self.server_name}) Message handler invoked for {scope.get('path')}"
            )
            await self.transport.handle_post_message(scope, receive, send)

        # Define fixed local paths within the namespace for the sub-Starlette app.
        # These paths are relative to the namespace mount point (e.g., /claims or /policy).
        fixed_local_sse_path = "sse"  # e.g., /claims/sse
        # This path for messages must align with how SseServerTransport was initialized.
        # SseServerTransport(endpoint="/messages/") means it expects to handle POSTs here.
        fixed_local_message_path = "messages"  # e.g., /claims/messages

        self.logger.info(
            f"({self.server_name}) Configuring routes for namespace '/{namespace}': "
            f"SSE at '/{fixed_local_sse_path}', Messages at '/{fixed_local_message_path}'"
        )

        # Create a custom ASGI application that handles routing internally
        async def mcp_namespace_app(scope: Scope, receive: Receive, send: Send) -> None:
            path = scope.get("path", "")
            method = scope.get("method", "GET")

            if path.endswith(f"/{fixed_local_sse_path}"):
                await handle_sse_connection(scope, receive, send)
            elif path.endswith(f"/{fixed_local_message_path}/") and method == "POST":
                await handle_post_message_endpoint(scope, receive, send)
            else:
                # Return 404 for unknown paths
                await send(
                    {
                        "type": "http.response.start",
                        "status": 404,
                        "headers": [[b"content-type", b"text/plain"]],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Not Found",
                    }
                )

        return [Mount(f"/{namespace}", app=mcp_namespace_app)]
