"""Middleware for logging request/response details."""

import json
import time
from typing import Any

from loguru import logger
from mcp.server.auth.middleware.auth_context import get_access_token
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response


class LogRequestMiddleware(BaseHTTPMiddleware):
    """Enhanced middleware that logs comprehensive request/response details."""

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """Process the request, log details, and return the response."""
        # Start timing
        start_time = time.monotonic()

        # Extract request info
        method = request.method
        path = request.url.path

        # Get client info
        user_agent = request.headers.get("user-agent", "unknown")

        # Get authentication info
        try:
            # Note: The get_access_token() call itself is non-blocking so
            # we don't need to worry about it blocking the event loop here.
            # However, potential blocking I/O might occur in the upstream
            # authentication middleware that populates the user context used
            # by get_access_token().
            # If that upstream middleware performs blocking operations, consider
            # alternatives like running it in a thread pool.
            access_token = get_access_token()
            client_id = access_token.client_id if access_token else "anonymous"
        except Exception:
            client_id = "unknown"

        # Process the request
        try:
            # Process the request through the middleware chain
            response = await call_next(request)

            # Calculate duration
            duration_ms = round((time.monotonic() - start_time) * 1000)

            # Create a structured log object
            log_data: dict[str, Any] = {
                "duration_ms": duration_ms,
                "client_id": client_id,
                "user_agent": user_agent,
            }

            # Convert to JSON string
            log_json = json.dumps(log_data)

            # Short prefix to quickly identify the log type
            prefix = f"{method} {path} → {response.status_code}"

            # Log at appropriate level based on status code
            if response.status_code >= 500:
                logger.error(f"{prefix} | {log_json}")
            elif response.status_code >= 400:
                logger.warning(f"{prefix} | {log_json}")
            else:
                logger.info(f"{prefix} | {log_json}")

            return response
        except Exception as exc:
            # Log exception
            duration_ms = round((time.monotonic() - start_time) * 1000)

            # Create structured error log
            error_log_data = {
                "type": "exception",
                "method": method,
                "path": path,
                "duration_ms": duration_ms,
                "client_id": client_id,
                "error": str(exc)[:200],
            }
            # Convert to JSON and log
            error_json = json.dumps(error_log_data)
            logger.exception(f"Exception {method} {path} | {error_json}")
            raise
