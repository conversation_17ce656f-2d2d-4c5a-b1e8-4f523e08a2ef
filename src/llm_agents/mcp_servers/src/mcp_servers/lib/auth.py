"""Authentication middleware and helpers for the Nirvana MCP server.

Provides the `NirvanaAuthBackend` class, which implements Star<PERSON>'s
authentication protocol. It validates incoming requests based on a Bearer token
(expected to be the Nirvana session ID) stored in the `SessionStore`.
Also includes an error handler (`on_auth_error`) for authentication failures.
"""

import time
from http import HTTPStatus

from loguru import logger
from mcp.server.auth.middleware.bearer_auth import AuthenticatedUser, BearerAuthBackend
from mcp.server.auth.provider import TokenVerifier
from starlette.authentication import (
    AuthCredentials,
    AuthenticationError,
    BaseUser,
)
from starlette.requests import HTTPConnection
from starlette.responses import (
    JSONResponse,
    Response,
)


class NirvanaAuthBackend(BearerAuthBackend):
    """Starlette authentication backend overriding the default MCP bearer auth backend.

    We override the default MCP BearerAuthBackend implementation because it doesn't
    return user friendly error messages at the moment.

    https://github.com/modelcontextprotocol/python-sdk/blob/2210c1be18d66ecf5553ee8915ad1338dc3aecb9/src/mcp/server/auth/middleware/bearer_auth.py#L25

    Attributes:
        _paths_to_skip: A list of URL paths that do not require authentication.
    """

    _paths_to_skip: list[str]

    def __init__(self, paths_to_skip: list[str], token_verifier: TokenVerifier):
        """Initialize the authentication backend.

        Args:
            paths_to_skip: A list of URL paths (e.g., '/health', '/configure')
                           to exempt from authentication checks.
            token_verifier: The token verifier to use for authentication.
        """
        self._paths_to_skip = paths_to_skip
        super().__init__(token_verifier)

    async def authenticate(
        self, conn: HTTPConnection
    ) -> tuple[AuthCredentials, BaseUser] | None:
        """Authenticate the connection based on the Authorization header.

        Validates that the 'Authorization: Bearer <token>' header is present and
        then invokes the OAuth provider's `load_access_token` method. If an access
        token is found, it is also checked for expiration.

        Args:
            conn: The incoming Starlette HTTPConnection.

        Returns:
            A tuple of (AuthCredentials(["authenticated"]), SimpleUser(token)) if authentication is successful.
            None if the path is configured to be skipped.

        Raises:
            AuthenticationError: If the Authorization header is missing, invalid,
                               or the token does not match the stored session ID,
                               or if the SessionStore is not yet configured.
        """
        path = conn.url.path
        if path in self._paths_to_skip:
            logger.trace(f"Skipping auth for path: {path}")
            return None

        logger.trace(f"Attempting authentication for path: {path}")
        auth_header = conn.headers.get("Authorization")
        if not auth_header:
            logger.warning(
                f"Authentication failed: No Authorization header. Path: {path}"
            )
            raise AuthenticationError("Authorization header is required.")

        # Validate header format
        parts = auth_header.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            logger.warning(
                f"Authentication failed: Invalid Authorization header format. Path: {path}"
            )
            raise AuthenticationError(
                "Invalid Authorization header format. Expected 'Bearer <token>'."
            )

        token = parts[1]

        # Validate the token with the verifier
        auth_info = await self.token_verifier.verify_token(token)

        if not auth_info:
            return None

        if auth_info.expires_at and auth_info.expires_at < int(time.time()):
            return None

        return AuthCredentials(auth_info.scopes), AuthenticatedUser(auth_info)


def on_auth_error(conn: HTTPConnection, exc: AuthenticationError) -> Response:
    """Handle authentication errors triggered by AuthenticationMiddleware.

    Args:
        conn: The HTTP connection associated with the request.
        exc: The AuthenticationError that was raised.

    Returns:
        A JSONResponse with a 401 status code and error detail.
    """
    logger.info(f"Authentication error on path {conn.url.path}: {exc}")
    return JSONResponse(
        {"detail": str(exc)},  # Provide the error message from the exception
        status_code=HTTPStatus.UNAUTHORIZED,
        headers={"WWW-Authenticate": "Bearer"},  # Indicate Bearer auth is expected
    )
