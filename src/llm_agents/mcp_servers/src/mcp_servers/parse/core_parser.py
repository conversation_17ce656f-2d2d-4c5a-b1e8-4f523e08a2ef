"""Core LlamaParse functionality, without caching."""

from llama_cloud_services import CloudParser as LlamaCloudParser
from llama_cloud_services.parse import ResultType
from llama_index.core import Document as LlamaDocument
from loguru import logger
from mcp.shared.exceptions import McpError
from mcp.types import INTERNAL_ERROR, PARSE_ERROR, ErrorData


async def direct_llama_parse(
    download_url: str, llama_parse_api_key: str, document_id_for_logging: str
) -> list[LlamaDocument]:
    """Performs a direct call to LlamaParse service to parse a document from a URL.

    Args:
        download_url: URL of the document to parse.
        llama_parse_api_key: API key for LlamaParse.
        document_id_for_logging: An identifier for logging purposes.

    Returns:
        A list of LlamaDocument objects.

    Raises:
        McpError: If parsing fails.
    """
    if not download_url:
        logger.error(
            f"({document_id_for_logging}) Empty download URL provided to direct_llama_parse."
        )
        raise McpError(
            ErrorData(
                code=PARSE_ERROR, message="Cannot parse document: Download URL is empty"
            )
        )
    if not llama_parse_api_key:
        logger.error(
            f"({document_id_for_logging}) LlamaParse API key is missing for direct_llama_parse."
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Document parsing configuration error: Missing API key",
            )
        )

    safe_url_for_logging = download_url.split("?")[0]
    logger.info(
        f"({document_id_for_logging}) Initiating direct LlamaParse for document from URL (base): {safe_url_for_logging}"
    )

    try:
        parser = LlamaCloudParser(
            api_key=llama_parse_api_key,
            result_type=ResultType.MD,
            verbose=True,
            language="en",
        )
        documents: list[LlamaDocument] = await parser.aload_data(download_url)

        doc_count = len(documents)
        logger.info(
            f"({document_id_for_logging}) Direct LlamaParse completed. Parsed into {doc_count} chunks "
            f"from URL (base): {safe_url_for_logging}"
        )
        if not documents:
            logger.warning(
                f"({document_id_for_logging}) Direct LlamaParse returned no documents. File might be empty "
                f"or unparseable from URL (base): {safe_url_for_logging}"
            )
        return documents

    except ValueError as e:
        logger.error(
            f"({document_id_for_logging}) LlamaParse validation error for URL (base) {safe_url_for_logging}: {e}",
            exc_info=True,
        )
        raise McpError(
            ErrorData(
                code=PARSE_ERROR,
                message=f"LlamaParse document validation error: {str(e)}",
            )
        ) from e
    except ConnectionError as e:
        logger.error(
            f"({document_id_for_logging}) Connection error to LlamaParse service for URL (base) "
            f"{safe_url_for_logging}: {e}",
            exc_info=True,
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Failed to connect to document parsing service: {str(e)}",
            )
        ) from e
    except Exception as e:
        logger.exception(
            f"({document_id_for_logging}) Unexpected error during direct LlamaParse for URL (base) "
            f"{safe_url_for_logging}: {e}",
            exc_info=True,
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR, message=f"Error during document parsing: {str(e)}"
            )
        ) from e
