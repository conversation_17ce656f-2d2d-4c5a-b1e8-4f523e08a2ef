"""Storage decorator and utilities for LlamaParse results using Redis."""

import asyncio
import functools
import hashlib
import json
import typing
from collections.abc import Awaitable, Callable
from typing import cast

import redis.exceptions
from llama_cloud_services.parse import ResultType as ContentTypeEnum
from llama_index.core import Document as LlamaDocument
from llama_index.storage.docstore.redis import RedisDocumentStore
from loguru import logger

from mcp_servers.lib.config import Config


# --- Metrics ---
_storage_retrievals: int = 0
_storage_new_parses: int = 0
_metric_log_interval: int = 100
_access_count: int = 0
_metrics_lock = asyncio.Lock()

# --- Constants for Content Type ---
DEFAULT_CONTENT_TYPE: str = str(ContentTypeEnum.MD.value)

# --- Batching Configuration ---
MAX_DOCS_PER_BATCH: int = 10  # Maximum documents to fetch per batch
BATCH_RETRIEVAL_THRESHOLD: int = (
    10  # Use batching when there are this many or more documents
)
BATCH_STORAGE_THRESHOLD: int = (
    10  # Use batching when there are this many or more documents to store
)


async def _store_documents_in_batches(
    redis_docstore: RedisDocumentStore,
    docs_to_store: list[LlamaDocument],
    document_id_for_logging: str,
    batch_size: int = MAX_DOCS_PER_BATCH,
) -> bool:
    """Store documents in batches for better performance with many documents.

    Handles both small and large document sets efficiently:
    - For small sets (< BATCH_STORAGE_THRESHOLD): stores directly without batching
    - For large sets (>= BATCH_STORAGE_THRESHOLD): stores in parallel batches

    Args:
        redis_docstore: The Redis document store
        docs_to_store: List of documents to store
        document_id_for_logging: Document ID for logging purposes
        batch_size: Maximum number of documents per batch (default: 10)

    Returns:
        True if all documents were stored successfully, False otherwise
    """

    async def store_batch(batch_docs: list[LlamaDocument]) -> bool:
        try:
            await redis_docstore.async_add_documents(batch_docs, allow_update=True)
            return True
        except Exception as e:
            logger.warning(
                f"({document_id_for_logging}) Failed to store batch of {len(batch_docs)} documents: {e}"
            )
            return False

    if len(docs_to_store) < BATCH_STORAGE_THRESHOLD:
        # For small document sets, store directly without batching overhead
        success = await store_batch(docs_to_store)
        if success:
            logger.debug(
                f"({document_id_for_logging}) Stored {len(docs_to_store)} documents directly (below batch threshold)"
            )
            return True
        else:
            logger.warning(
                f"({document_id_for_logging}) Failed to store {len(docs_to_store)} documents directly (below batch threshold)"
            )
            return False

    # For large document sets, use parallel batching
    batches = [
        docs_to_store[i : i + batch_size]
        for i in range(0, len(docs_to_store), batch_size)
    ]

    logger.debug(
        f"({document_id_for_logging}) Storing {len(docs_to_store)} documents "
        f"in {len(batches)} parallel batches of up to {batch_size} documents each"
    )
    # Store all batches in parallel
    batch_results = await asyncio.gather(
        *[store_batch(batch) for batch in batches], return_exceptions=True
    )

    # Check results and handle exceptions
    successful_batches = 0
    failed_batches = 0

    for i, result in enumerate(batch_results):
        if isinstance(result, Exception):
            logger.error(
                f"({document_id_for_logging}) Exception storing batch {i + 1}/{len(batches)}: {result}"
            )
            failed_batches += 1
        elif result is True:
            successful_batches += 1
        else:
            logger.warning(
                f"({document_id_for_logging}) Unexpected result for batch {i + 1}: {result}"
            )
            failed_batches += 1

    if failed_batches > 0:
        logger.warning(
            f"({document_id_for_logging}) Failed to store {failed_batches} out of {len(batches)} batches"
        )

    logger.debug(
        f"({document_id_for_logging}) Successfully stored documents "
        f"from {successful_batches} out of {len(batches)} batches"
    )

    return failed_batches == 0


async def _retrieve_documents_in_batches(
    redis_docstore: RedisDocumentStore,
    chunk_doc_ids: list[str],
    document_id_for_logging: str,
    batch_size: int = MAX_DOCS_PER_BATCH,
) -> list[LlamaDocument]:
    """Retrieve documents in batches for better performance with many documents.

    Args:
        redis_docstore: The Redis document store
        chunk_doc_ids: List of document IDs to retrieve
        document_id_for_logging: Document ID for logging purposes
        batch_size: Maximum number of documents per batch (default: 10)

    Returns:
        List of retrieved documents, or empty list if any batch fails (all-or-nothing behavior)
    """
    # Split chunk_doc_ids into batches
    batches = [
        chunk_doc_ids[i : i + batch_size]
        for i in range(0, len(chunk_doc_ids), batch_size)
    ]

    logger.debug(
        f"({document_id_for_logging}) Retrieving {len(chunk_doc_ids)} documents "
        f"in {len(batches)} batches of up to {batch_size} documents each"
    )

    async def retrieve_batch(batch_ids: list[str]) -> list[LlamaDocument]:
        try:
            docs = await redis_docstore.aget_nodes(batch_ids)
            return cast(list[LlamaDocument], docs)
        except Exception as e:
            logger.warning(
                f"({document_id_for_logging}) Failed to retrieve batch of {len(batch_ids)} documents: {e}"
            )
            return []

    # Retrieve all batches in parallel
    batch_results = await asyncio.gather(
        *[retrieve_batch(batch) for batch in batches], return_exceptions=True
    )

    # Flatten results and handle exceptions
    all_documents = []

    for i, result in enumerate(batch_results):
        if isinstance(result, Exception):
            logger.error(
                f"({document_id_for_logging}) Exception retrieving batch {i + 1}/{len(batches)}: {result}"
            )
        elif isinstance(result, list):
            all_documents.extend(result)

    # Ensure we retrieved the exact number of documents we expected (all-or-nothing behavior)
    if len(all_documents) != len(chunk_doc_ids):
        logger.warning(
            f"({document_id_for_logging}) Document count mismatch: expected {len(chunk_doc_ids)} documents, "
            f"but retrieved {len(all_documents)} documents. Returning empty list to maintain cache consistency."
        )
        return []  # Return empty list when document count doesn't match

    logger.debug(
        f"({document_id_for_logging}) Successfully retrieved {len(all_documents)} documents "
        f"from all {len(batches)} batches"
    )

    return all_documents


# --- Storage Key Generation ---


def _generate_safe_component(component: str) -> str:
    """Hashes a component string using SHA-256 for safe use in a Redis key."""
    return hashlib.sha256(component.encode("utf-8")).hexdigest()


def _generate_cache_group_key(
    namespace: str,
    s3_url_base: str,
    s3_etag: str | None,
    parsing_vendor: str,
    content_type: str = DEFAULT_CONTENT_TYPE,
) -> str:
    """Generates a Redis key for storing a list of chunk document IDs.

    Args:
        namespace: Redis namespace for storage
        s3_url_base: Base S3 URL without query parameters
        s3_etag: S3 ETag for cache invalidation
        parsing_vendor: The parsing vendor used (e.g., "LlamaParse", "Extend")
        content_type: Content type requested (defaults to markdown)
    """
    url_hash = _generate_safe_component(s3_url_base)
    key_components = [f"url_h={url_hash}"]

    if s3_etag:
        key_components.append(f"etag_h={_generate_safe_component(s3_etag)}")
    else:
        key_components.append("no_etag")
        logger.warning(
            f"Generating cache group key for URL ({s3_url_base}) without an ETag. "
            f"Cache invalidation will be less specific."
        )

    key_components.append(f"vendor_h={_generate_safe_component(parsing_vendor)}")
    key_components.append(f"type_h={_generate_safe_component(content_type)}")

    return f"{namespace}:cache_group:{':'.join(key_components)}"


# --- Storage Decorator ---


async def _try_retrieve_from_storage(
    redis_docstore: RedisDocumentStore,
    s3_url_base: str,
    s3_etag: str | None,
    config: Config,
    document_id_for_logging: str,
    parsing_vendor: str,
    content_type: str = DEFAULT_CONTENT_TYPE,
) -> list[LlamaDocument] | None:
    """Attempt to retrieve parsed documents from Redis storage using a cache group key."""
    cache_group_key = _generate_cache_group_key(
        config.redis.document_storage_namespace,
        s3_url_base,
        s3_etag,
        parsing_vendor,
        content_type,
    )
    cache_index_collection = config.redis.document_storage_namespace + "/cache_index"

    try:
        chunk_ids_json_data = await redis_docstore._kvstore.aget(
            cache_group_key, collection=cache_index_collection
        )

        if not chunk_ids_json_data or not chunk_ids_json_data.get("__data__"):
            logger.debug(
                f"({document_id_for_logging}) Cache group key {cache_group_key} not found "
                f"in index collection {cache_index_collection}. Cache miss."
            )
            return None

        chunk_doc_ids: list[str] = json.loads(chunk_ids_json_data["__data__"])

        if not chunk_doc_ids:
            logger.info(
                f"({document_id_for_logging}) Cache group key {cache_group_key} lists no chunk IDs. "
                f"Returning empty list (cache hit)."
            )
            return []

        # Determine if batching is beneficial
        if len(chunk_doc_ids) >= BATCH_RETRIEVAL_THRESHOLD:
            retrieved_documents = await _retrieve_documents_in_batches(
                redis_docstore,
                chunk_doc_ids,
                document_id_for_logging,
            )
        else:
            retrieved_documents = cast(
                list[LlamaDocument], await redis_docstore.aget_nodes(chunk_doc_ids)
            )

        if len(retrieved_documents) != len(chunk_doc_ids):
            logger.warning(
                f"({document_id_for_logging}) Mismatch in retrieved documents for cache group {cache_group_key}. "
                f"Expected {len(chunk_doc_ids)}, got {len(retrieved_documents)}. Cache potentially inconsistent. "
                f"Treating as miss and deleting inconsistent cache group key."
            )
            await redis_docstore._kvstore.adelete(
                cache_group_key, collection=cache_index_collection
            )
            return None

        logger.info(
            f"({document_id_for_logging}) Successfully retrieved {len(retrieved_documents)} documents "
            f"from cache for group key {cache_group_key}."
        )
        return retrieved_documents

    except json.JSONDecodeError as e:
        logger.error(
            f"({document_id_for_logging}) Failed to decode JSON for chunk IDs from cache group key "
            f"'{cache_group_key}'. Error: {e}. Treating as miss.",
            exc_info=True,
        )
        return None
    except redis.exceptions.RedisError as e:
        logger.error(
            f"({document_id_for_logging}) Redis error retrieving documents or chunk IDs for cache group key "
            f"'{cache_group_key}'. Error: {e}. Treating as miss.",
            exc_info=True,
        )
        return None
    except Exception as e:
        logger.error(
            f"({document_id_for_logging}) Unexpected error during cache retrieval for group key "
            f"'{cache_group_key}'. Error: {e}. Treating as miss.",
            exc_info=True,
        )
        return None


def _prepare_documents_for_storage(
    parsed_documents: list[LlamaDocument],
    config: Config,
    s3_url_base: str,
    s3_etag: str | None,
    document_id_for_logging: str,
    parsing_vendor: str,
    content_type: str = DEFAULT_CONTENT_TYPE,
) -> list[LlamaDocument]:
    """Prepares documents for storage (sets IDs, adds metadata).

    Args:
        parsed_documents: Documents to prepare for storage
        config: Configuration containing storage settings
        s3_url_base: Base S3 URL
        s3_etag: S3 ETag for cache invalidation
        document_id_for_logging: Document ID for logging
        parsing_vendor: Vendor used for parsing
        content_type: Content type requested
    """
    stored_chunk_docs_with_ids: list[LlamaDocument] = []

    url_hash = _generate_safe_component(s3_url_base)
    etag_component = (
        f"etag_h={_generate_safe_component(s3_etag)}" if s3_etag else "no_etag"
    )
    vendor_component = f"vendor_h={_generate_safe_component(parsing_vendor)}"
    type_component = f"type_h={_generate_safe_component(content_type)}"

    for i, chunk_doc in enumerate(parsed_documents):
        chunk_doc_id_suffix = _generate_safe_component(f"idx={i}")
        chunk_doc_id = (
            f"{config.redis.document_storage_namespace}:chunk:"
            f"url_h={url_hash}:{etag_component}:{vendor_component}:{type_component}:"
            f"chk_h={chunk_doc_id_suffix}"
        )

        chunk_metadata = chunk_doc.metadata.copy() if chunk_doc.metadata else {}
        chunk_metadata["source_document_hash_full"] = (
            f"url_h={url_hash}:{etag_component}:{vendor_component}:{type_component}"
        )

        if "s3_url_base" not in chunk_metadata:
            chunk_metadata["s3_url_base"] = s3_url_base
        if s3_etag and "s3_etag" not in chunk_metadata:
            chunk_metadata["s3_etag"] = s3_etag

        # Ensure parsing_vendor and content_type are in metadata to match the cache key
        chunk_metadata["parsing_vendor"] = parsing_vendor
        chunk_metadata["content_type"] = content_type

        prepared_doc = LlamaDocument(
            doc_id=chunk_doc_id,
            text=chunk_doc.text,
            metadata=chunk_metadata,
            excluded_embed_metadata_keys=chunk_doc.excluded_embed_metadata_keys,
            excluded_llm_metadata_keys=chunk_doc.excluded_llm_metadata_keys,
            relationships=chunk_doc.relationships,
            hash=chunk_doc.hash,
            text_template=getattr(
                chunk_doc, "text_template", "{metadata_str}\n\n{content}"
            ),
        )
        stored_chunk_docs_with_ids.append(prepared_doc)

    return stored_chunk_docs_with_ids


async def _store_parsed_documents(
    redis_docstore: RedisDocumentStore,
    parsed_documents: list[LlamaDocument],
    config: Config,
    s3_url_base: str,
    s3_etag: str | None,
    document_id_for_logging: str,
    parsing_vendor: str,
    content_type: str = DEFAULT_CONTENT_TYPE,
) -> list[LlamaDocument] | None:
    """Prepares, then stores parsed documents in Redis using async_add_documents."""
    docs_to_store = _prepare_documents_for_storage(
        parsed_documents,
        config,
        s3_url_base,
        s3_etag,
        document_id_for_logging,
        parsing_vendor,
        content_type,
    )

    if not docs_to_store:
        logger.warning(
            f"({document_id_for_logging}) Document preparation yielded no documents to store."
        )
        return None

    try:
        # Store documents using batching function (handles threshold internally)
        success = await _store_documents_in_batches(
            redis_docstore,
            docs_to_store,
            document_id_for_logging,
        )

        if success:
            logger.info(
                f"({document_id_for_logging}) Successfully stored {len(docs_to_store)} document chunks "
                f"for s3_url_base={s3_url_base}, s3_etag={s3_etag}."
            )

            chunk_doc_ids = [doc.doc_id for doc in docs_to_store if doc.doc_id]
            if chunk_doc_ids:
                cache_group_key = _generate_cache_group_key(
                    config.redis.document_storage_namespace,
                    s3_url_base,
                    s3_etag,
                    parsing_vendor,
                    content_type,
                )
                cache_index_collection = (
                    config.redis.document_storage_namespace + "/cache_index"
                )
                value_for_index = {"__data__": json.dumps(chunk_doc_ids)}

                await redis_docstore._kvstore.aput(
                    key=cache_group_key,
                    val=value_for_index,
                    collection=cache_index_collection,
                )
                logger.info(
                    f"({document_id_for_logging}) Stored chunk ID list for cache group key {cache_group_key} "
                    f"in index collection {cache_index_collection}."
                )
            else:
                logger.warning(
                    f"({document_id_for_logging}) No document IDs to store in cache index for {s3_url_base}"
                )

            return docs_to_store
        else:
            logger.error(
                f"({document_id_for_logging}) Failed to store document chunks "
                f"for s3_url_base={s3_url_base}, s3_etag={s3_etag}."
            )
            return None

    except redis.exceptions.RedisError as e:
        logger.error(
            f"({document_id_for_logging}) Redis error storing document chunks via "
            f"async_add_documents for s3_url_base={s3_url_base}, s3_etag={s3_etag}. Error: {e}",
            exc_info=True,
        )
        return None
    except Exception as e:
        logger.error(
            f"({document_id_for_logging}) Unexpected error storing document chunks via "
            f"async_add_documents for s3_url_base={s3_url_base}, s3_etag={s3_etag}. Error: {e}",
            exc_info=True,
        )
        return None


P = typing.ParamSpec("P")
InputFunc = Callable[P, Awaitable[list[LlamaDocument]]]


def persist_and_retrieve_parsed_document(
    redis_docstore_provider: Callable[[Config], Awaitable[RedisDocumentStore | None]],
) -> Callable[
    [InputFunc[P]], InputFunc[P]
]:  # Decorator returns function with same signature
    """Decorator to cache document parsing results in Redis and retrieve if available."""

    # Same signature as input
    def decorator(parse_function: InputFunc[P]) -> InputFunc[P]:
        @functools.wraps(parse_function)
        # Returns list[LlamaDocument]
        async def wrapper(
            *args_wrapper: P.args, **kwargs_wrapper: P.kwargs
        ) -> list[LlamaDocument]:
            # Extract and cast arguments that are used by the wrapper's internal logic.
            # P.args are (download_url, s3_url_base, s3_etag, config,
            # document_id_for_logging, parsing_vendor, content_type)

            # These are used by the wrapper and need casting:
            s3_url_base = cast(str, args_wrapper[1])
            s3_etag = cast(str | None, args_wrapper[2])
            config = cast(Config, args_wrapper[3])
            document_id_for_logging = cast(str, args_wrapper[4])
            parsing_vendor = cast(str, args_wrapper[5])
            # Optional with default
            content_type = (
                cast(str, args_wrapper[6])
                if len(args_wrapper) > 6
                else DEFAULT_CONTENT_TYPE
            )

            global _storage_retrievals, _storage_new_parses, _access_count
            async with _metrics_lock:
                _access_count += 1
            current_access_count = _access_count

            logger.info(
                f"[STORAGE DECORATOR] Entry for doc_id={document_id_for_logging}, url={s3_url_base}, vendor={parsing_vendor}"
            )
            redis_docstore = await redis_docstore_provider(config)

            if not redis_docstore:
                logger.warning(
                    f"({document_id_for_logging}) Redis storage unavailable. "
                    f"Calling original parse function for {s3_url_base}."
                )
                async with _metrics_lock:
                    _storage_new_parses += 1

                # Returns list[LlamaDocument]
                result = await parse_function(*args_wrapper, **kwargs_wrapper)

                if current_access_count % _metric_log_interval == 0:
                    logger.info(
                        "Storage Metrics (at access %s, storage unavailable): Retrievals=%s, NewParses=%s",
                        current_access_count,
                        _storage_retrievals,
                        _storage_new_parses,
                    )
                return result  # This path returns list[LlamaDocument]

            retrieved_chunks = await _try_retrieve_from_storage(
                redis_docstore,
                s3_url_base=s3_url_base,
                s3_etag=s3_etag,
                config=config,
                document_id_for_logging=document_id_for_logging,
                parsing_vendor=parsing_vendor,
                content_type=content_type,
            )

            if retrieved_chunks is not None:  # Cache Hit
                async with _metrics_lock:
                    _storage_retrievals += 1
                logger.info(
                    f"({document_id_for_logging}) Storage HIT for: url={s3_url_base}, etag={s3_etag}, vendor={parsing_vendor}"
                )
                if current_access_count % _metric_log_interval == 0:
                    logger.info(
                        "Storage Metrics (at access %s): Retrievals=%s, NewParses=%s",
                        current_access_count,
                        _storage_retrievals,
                        _storage_new_parses,
                    )
                # Returns list[LlamaDocument] (can be empty list)
                return retrieved_chunks

            # Cache Miss Path
            async with _metrics_lock:
                _storage_new_parses += 1
            logger.info(
                f"({document_id_for_logging}) Storage MISS for: url={s3_url_base}, "
                f"etag={s3_etag}, vendor={parsing_vendor}. Calling parse function."
            )
            # list[LlamaDocument]
            original_parsed_documents = await parse_function(
                *args_wrapper, **kwargs_wrapper
            )

            stored_result_or_none = await _store_parsed_documents(
                redis_docstore,
                original_parsed_documents,
                config,
                s3_url_base,
                s3_etag,
                document_id_for_logging,
                parsing_vendor,
                content_type,
            )

            if current_access_count % _metric_log_interval == 0:
                logger.info(
                    "Storage Metrics (at access %s): Retrievals=%s, NewParses=%s",
                    current_access_count,
                    _storage_retrievals,
                    _storage_new_parses,
                )

            # Restore original fallback logic
            return (
                stored_result_or_none
                if stored_result_or_none is not None
                else original_parsed_documents
            )

        return wrapper

    return decorator
