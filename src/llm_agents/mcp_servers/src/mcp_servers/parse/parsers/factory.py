"""Factory for creating document parsers."""

from mcp_servers.lib.config import Config
from mcp_servers.lib.enums import Parsing<PERSON>endor

from .base import DocumentParser
from .extend_parser import ExtendParser
from .llama_parser import <PERSON>lamaPars<PERSON>


def create_parser(vendor: ParsingVendor, config: Config) -> DocumentParser:
    """Create a parser instance based on vendor and configuration.

    Args:
        vendor: The parsing vendor to use
        config: Configuration containing API credentials

    Returns:
        Parser instance implementing DocumentParser protocol

    Raises:
        ValueError: If vendor is not supported or credentials are missing
    """
    if vendor == ParsingVendor.LLAMA_PARSE:
        if not config.llama_parse_api_key:
            raise ValueError("LlamaParse API key is required but not configured")
        return LlamaParser(api_key=config.llama_parse_api_key)

    elif vendor == ParsingVendor.EXTEND:
        if not config.extend_api_key:
            raise ValueError("Extend.ai API key is required but not configured")
        return ExtendParser(api_key=config.extend_api_key)

    else:
        raise ValueError(f"Unsupported parsing vendor: {vendor}")
