"""LlamaParse document parser implementation."""

from typing import Any

from llama_cloud_services import LlamaParse as LlamaCloudParser
from llama_cloud_services.parse import ResultType as ContentTypeEnum
from llama_index.core import Document as LlamaDocument
from loguru import logger
from mcp.shared.exceptions import Mcp<PERSON>rror
from mcp.types import INTERNAL_ERROR, PARSE_ERROR, ErrorData


class LlamaParser:
    """LlamaParse implementation of document parser."""

    def __init__(self, api_key: str) -> None:
        """Initialize LlamaParse parser.

        Args:
            api_key: LlamaParse API key
        """
        self.api_key = api_key

    async def parse_document(
        self,
        download_url: str,
        document_id: str,
        result_type: ContentTypeEnum = ContentTypeEnum.MD,
        **kwargs: Any,
    ) -> list[LlamaDocument]:
        """Parse document using LlamaParse.

        Args:
            download_url: URL to download the document from
            document_id: Unique identifier for logging/tracking
            result_type: Content type to request from LlamaParse
            **kwargs: Additional parameters (unused)

        Returns:
            List of LlamaDocument chunks
        """
        if not download_url:
            logger.error("Empty download URL provided to LlamaParser")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message="Cannot parse document: Download URL is empty",
                )
            )
        if not self.api_key:
            logger.error("LlamaParse API key is missing")
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message="Document parsing configuration error: Missing API key",
                )
            )

        safe_url = download_url.split("?")[0] if "?" in download_url else download_url
        logger.info(
            f"Initiating LlamaParse for document ID {document_id} with result_type: {result_type.value}"
        )
        logger.debug(f"Using URL: {safe_url}")

        try:
            parser = LlamaCloudParser(
                api_key=self.api_key,
                result_type=result_type,
                verbose=True,
                language="en",
            )
            documents: list[LlamaDocument] = await parser.aload_data(download_url)
            document_count = len(documents)
            logger.info(
                f"LlamaParse completed. Parsed {document_id} into {document_count} chunks."
            )
            if not documents:
                logger.warning(
                    f"LlamaParse returned no documents for {document_id}. File might be empty or unparseable."
                )
            return documents
        except ValueError as e:
            logger.error(f"LlamaParse validation error for {document_id}: {e}")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR, message=f"Document validation error: {str(e)}"
                )
            ) from e
        except ConnectionError as e:
            logger.error(
                f"Connection error to LlamaParse service for {document_id}: {e}"
            )
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message=f"Failed to connect to document parsing service: {str(e)}",
                )
            ) from e
        except Exception as e:
            logger.exception(
                f"Error during LlamaParse for {document_id}: {e}", exc_info=True
            )
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message=f"Error during document parsing: {str(e)}",
                )
            ) from e
