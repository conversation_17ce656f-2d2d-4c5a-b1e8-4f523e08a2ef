"""Extend.ai document parser implementation."""

from typing import Any

from extend_ai import (
    Extend,
    ParseRequestFile,
    ParseResponse,
    ParseConfig,
    ParseConfigBlockOptions,
    ParseConfigBlockOptionsFigures,
    ParseConfigBlockOptionsText,
)

from llama_index.core import Document as LlamaDocument
from loguru import logger
from mcp.shared.exceptions import McpError
from mcp.types import INTERNAL_ERROR, PARSE_ERROR, ErrorData


class ExtendParser:
    """Extend.ai implementation of document parser."""

    def __init__(self, api_key: str) -> None:
        """Initialize Extend.ai parser.

        Args:
            api_key: Extend.ai API key
        """
        self.api_key = api_key

    async def parse_document(
        self,
        download_url: str,
        document_id: str,
        **kwargs: Any,
    ) -> list[LlamaDocument]:
        """Parse document using Extend.ai.

        Args:
            download_url: URL to download the document from
            document_id: Unique identifier for logging/tracking
            **kwargs: Additional parameters (unused)

        Returns:
            List of LlamaDocument chunks
        """
        if not download_url:
            logger.error("Empty download URL provided to ExtendParser")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message="Cannot parse document: Download URL is empty",
                )
            )
        if not self.api_key:
            logger.error("Extend.ai API key is missing")
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message="Document parsing configuration error: Missing Extend.ai API key",
                )
            )

        safe_url = download_url.split("?")[0] if "?" in download_url else download_url
        logger.info(f"Initiating Extend.ai parsing for document ID {document_id}")
        logger.debug(f"Using URL: {safe_url}")

        try:
            # Create Extend client
            client = Extend(token=self.api_key, extend_api_version="2025-04-21")

            # Create parse request with configuration
            parse_request = ParseRequestFile(file_url=download_url)

            # Execute parse request
            result = client.parse(
                file=parse_request,
                config=ParseConfig(
                    block_options=ParseConfigBlockOptions(
                        figures=ParseConfigBlockOptionsFigures(enabled=False),
                        text=ParseConfigBlockOptionsText(
                            signature_detection_enabled=False
                        ),
                    ),
                ),
            )

            if result.status == "FAILED":
                error_reason = getattr(result, "failure_reason", "Unknown error")
                logger.error(
                    f"Extend.ai parsing failed for {document_id}: {error_reason}"
                )
                raise McpError(
                    ErrorData(
                        code=PARSE_ERROR,
                        message=f"Document parsing failed: {error_reason}",
                    )
                )

            # Convert Extend.ai response to LlamaDocument format
            documents = self._convert_extend_response_to_llama_documents(
                result, document_id
            )

            document_count = len(documents)
            logger.info(
                f"Extend.ai parsing completed. Parsed {document_id} into {document_count} chunks."
            )
            if not documents:
                logger.warning(
                    f"Extend.ai returned no documents for {document_id}. File might be empty or unparseable."
                )
            return documents
        except Exception as e:
            logger.exception(
                f"Error during Extend.ai parsing for {document_id}: {e}", exc_info=True
            )
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message=f"Error during document parsing: {str(e)}",
                )
            ) from e

    def _convert_extend_response_to_llama_documents(
        self, extend_response: ParseResponse, document_id: str
    ) -> list[LlamaDocument]:
        """Convert Extend.ai SDK response to LlamaDocument format.

        Args:
            extend_response: Response object from Extend.ai SDK
            document_id: Document ID for metadata

        Returns:
            List of LlamaDocument objects
        """
        documents = []

        # Access chunks directly from ParseResponse object
        chunks = extend_response.chunks or []

        for i, chunk in enumerate(chunks):
            if not chunk.content.strip():
                continue

            # Extract metadata from chunk - compatible with Redis storage requirements
            metadata = {
                "chunk_index": i,
                "document_id": document_id,
                "parsing_vendor": "Extend",  # Required by Redis storage for cache key generation
                "content_type": "markdown",  # Required by Redis storage for cache key generation
                "chunk_type": str(chunk.type),
            }

            # Add page information with null safety
            if chunk.metadata and chunk.metadata.page_range:
                page_range = chunk.metadata.page_range
                metadata["page_start"] = page_range.start
                metadata["page_end"] = page_range.end

            # Add block information if available
            if chunk.blocks:
                metadata["block_count"] = len(chunk.blocks)

                # Add details from first block for additional context
                first_block = chunk.blocks[0]
                if first_block.metadata and first_block.metadata.page:
                    page_info = first_block.metadata.page
                    metadata["page_width"] = page_info.width
                    metadata["page_height"] = page_info.height
                    metadata["page_number"] = page_info.number

            # Create LlamaDocument with proper doc_id format
            doc = LlamaDocument(
                text=chunk.content,
                metadata=metadata,
                doc_id=f"{document_id}_extend_chunk_{i}",  # Unique prefix to avoid conflicts
            )
            documents.append(doc)

        return documents
