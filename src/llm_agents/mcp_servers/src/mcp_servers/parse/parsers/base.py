"""Base document parser interface."""

from abc import abstractmethod
from typing import Any, Protocol

from llama_index.core import Document as LlamaDocument


class DocumentParser(Protocol):
    """Protocol for document parsing implementations."""

    @abstractmethod
    async def parse_document(
        self,
        download_url: str,
        document_id: str,
        **kwargs: Any,
    ) -> list[LlamaDocument]:
        """Parse document from URL and return LlamaDocument chunks.

        Args:
            download_url: URL to download the document from
            document_id: Unique identifier for logging/tracking
            **kwargs: Additional vendor-specific parameters

        Returns:
            List of LlamaDocument chunks

        Raises:
            ValueError: For validation errors
            ConnectionError: For network/service errors
            Exception: For other parsing errors
        """
        ...
