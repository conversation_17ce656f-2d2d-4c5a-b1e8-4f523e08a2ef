"""Utilities for S3 interactions, such as fetching ETags."""

import httpx
from loguru import logger
from mcp.server.fastmcp import Context
from typing import cast


async def get_s3_object_etag(
    presigned_url: str, ctx: Context | None = None
) -> str | None:
    """Fetches the ETag of an S3 object using its pre-signed URL.

    Attempts a HEAD request first. If it fails with a 403 error,
    it falls back to a GET request to retrieve headers only.
    Strips quotes from the ETag.

    Args:
        presigned_url: The pre-signed URL to the S3 object.
        ctx: The server context (currently unused, but placeholder for future use e.g. shared client).

    Returns:
        The ETag string (unquoted) if successful, otherwise None.
    """
    url_base = presigned_url.split("?")[0]
    logger.debug(f"Attempting to fetch ETag for URL (base): {url_base}")

    try:
        async with httpx.AsyncClient(timeout=10.0, follow_redirects=True) as client:
            try:
                logger.debug(f"Attempting HEAD request for ETag: {url_base}")
                response = await client.head(presigned_url)
                response.raise_for_status()  # Raise an exception for 4XX/5XX responses

                etag = response.headers.get("ETag")
                if etag is not None:
                    unquoted_etag = str(etag).strip('"')
                    logger.info(
                        f"Successfully fetched ETag via HEAD: {unquoted_etag} for URL (base): {url_base}"
                    )
                    return unquoted_etag
                else:
                    logger.warning(
                        f"ETag header not found in HEAD response for URL (base): {url_base}"
                    )
                    return None  # ETag not found, even with 200 OK

            except httpx.HTTPStatusError as e_head:
                if e_head.response.status_code == 403:
                    logger.warning(
                        f"HEAD request for ETag failed with 403 for {url_base}. Attempting GET."
                    )
                    async with client.stream("GET", presigned_url) as response_get:
                        # We only need headers, so check status and get ETag
                        if response_get.status_code == 200:
                            response_get.raise_for_status()  # Raise HTTPStatusError for 4xx/5xx
                            etag_value_from_header = response_get.headers.get("ETag")
                            if etag_value_from_header is not None:
                                etag_get = cast(str, etag_value_from_header)
                                logger.info(
                                    f"Successfully fetched ETag via GET for {url_base} after 403 on HEAD."
                                )
                                return etag_get.strip('" ')
                            else:
                                logger.warning(
                                    f"ETag header not found in GET response for URL (base): {url_base} "
                                    f"after 403 on HEAD."
                                )
                                return None
                        else:
                            # Log the non-200 status from GET attempt
                            await (
                                response_get.aread()
                            )  # Consume body to log it if small, and ensure closure
                            logger.error(
                                f"GET request for ETag (after 403 on HEAD) failed with "
                                f"{response_get.status_code} for {url_base}. Response: {response_get.text[:200]}..."
                            )
                            return None
                else:
                    # Re-raise other HTTPStatusErrors from HEAD to be caught by the outer handler
                    raise e_head

    except (
        httpx.HTTPStatusError
    ) as e_status:  # Catches re-raised e_head or other status errors
        logger.error(
            f"HTTP status error {e_status.response.status_code} while fetching ETag "
            f"for {url_base}: {e_status.response.text[:500]}"
        )
        return None
    # Includes ConnectError, TimeoutException, etc.
    except httpx.RequestError as e_req:
        logger.error(f"Request error while fetching ETag for {url_base}: {e_req}")
        return None
    except Exception as e_unexpected:
        logger.error(
            f"Unexpected error fetching ETag for {url_base}: {e_unexpected}",
            exc_info=True,
        )
        return None
