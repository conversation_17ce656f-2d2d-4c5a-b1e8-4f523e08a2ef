"""Defines the MCP server instance for the Claims API."""

import os
from typing import Any, Callable, cast

from mcp.server.sse import SseServerTransport
from mcp_servers.lib.server import NirvanaMCP
from mcp_servers.mcp_server_claims import tools

DEFAULT_TIMEOUT_SECONDS = int(os.getenv("MCP_DEFAULT_TIMEOUT_SECONDS", "30"))

# Define the transport for the claims server
claims_transport = SseServerTransport(endpoint="/messages/")

# Initialize the MCP server in a module-level variable so
# it can be used as a decorator for tools. See tools.py.
mcp_server = NirvanaMCP(
    server_name="Claims MCP Server",
    default_timeout_seconds=DEFAULT_TIMEOUT_SECONDS,
    transport=claims_transport,
)

# Register tools specific to the claims server
for tool_func in tools.DEFAULT_TOOLS:
    mcp_server.tool()(cast(Callable[..., Any], tool_func))
