"""Tools for MCP Server Claims."""

import datetime
import json
import typing
from http import HTT<PERSON>tatus

from httpx import HTTP<PERSON>rror
from loguru import logger
from mcp.server.fastmcp import Context
from mcp.shared.exceptions import McpError
from mcp.types import INTERNAL_ERROR, INVALID_PARAMS, PARSE_ERROR, ErrorData

from mcp_servers.lib.nvapi import get_nv_mcp_experiments_api_client
from nirvana_rest_api.api.mcp_experiments import (
    mcp_experiments_get_claim_info as get_claim_info_api,
)
from nirvana_rest_api.api.mcp_experiments import (
    mcp_experiments_get_claim_notes as get_claim_notes_api,
)
from nirvana_rest_api.api.mcp_experiments import (
    mcp_experiments_get_claim_status_log as get_claim_status_log_api,
)
from nirvana_rest_api.api.mcp_experiments import (
    mcp_experiments_get_claims_for_policy as get_claims_for_policy_api,
)
from nirvana_rest_api.api.mcp_experiments import (
    mcp_experiments_trigger_claim_sync as trigger_claim_sync_api,
)
from nirvana_rest_api.models import (
    MCPExperimentsGetClaimInfo200Response as ClaimInfo,
)
from nirvana_rest_api.models import (
    MCPExperimentsGetClaimNotes200ResponseInner as ClaimNote,
)
from nirvana_rest_api.models import (
    MCPExperimentsGetClaimStatusLog200ResponseInner as ClaimStatusLogItem,
)


async def get_claims_for_policy(policy_number: str, ctx: Context) -> str:
    """Get a summary list of all claims associated with a specific policy number.

    Args:
        policy_number (str): The policy number (e.g., TINGL0015029-25).
        ctx (Context): The server context object.

    Returns:
        A formatted JSON string containing a list of claim summaries or an error message.
    """
    try:
        response = await get_claims_for_policy_api.asyncio_detailed(
            client=get_nv_mcp_experiments_api_client(ctx),
            policy_number=policy_number,
        )
    except HTTPError as e:
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Connection error",
            ),
        ) from e
    if response.status_code != 200:
        logger.error(
            f"Failed to get claims for policy. Status code: {response.status_code}, response: {response}"
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Received HTTP {response.status_code}, expected {HTTPStatus.OK}",
            ),
        )

    if response.parsed and isinstance(response.parsed, list):
        result = [
            item.to_dict() for item in response.parsed if hasattr(item, "to_dict")
        ]
        return json.dumps(result, indent=2)
    else:
        logger.error(f"Received invalid response: {response}")
        raise McpError(
            ErrorData(
                code=PARSE_ERROR,
                message="Received invalid response from server",
            ),
        )


async def get_claim_info(
    claim_external_id: str, ctx: Context, as_of_datetime: str | None = None
) -> str:
    """Get comprehensive claim information including notes and status log, optionally filtered by datetime.

    This function first triggers a synchronous claim sync to ensure the latest data
    is available from the source system (NARS C3), then retrieves the comprehensive
    claim information including notes and status log.

    Args:
        claim_external_id (str): The external ID of the claim (e.g., NITNI25010042).
        ctx (Context): The server context object.
        as_of_datetime (str, optional): The datetime in RFC 3339 format (e.g., '2024-01-15T23:59:59Z' or '2024-01-15T17:00:00-05:00')
                                       to filter notes and status log up to (inclusive).
                                       If not provided, returns all notes and status log entries up to the current time.

    Returns:
        str: A formatted JSON string containing the synced claim info (current state), and the notes
        and status log. If as_of_datetime is provided, notes and status log are filtered to that datetime.
        Returns a JSON error object if sync or fetching fails or the datetime format is invalid.
    """
    client = get_nv_mcp_experiments_api_client(ctx)

    # Step 1: Trigger claim sync first to ensure latest data
    logger.info(f"Triggering claim sync for claim: {claim_external_id}")
    try:
        sync_resp = await trigger_claim_sync_api.asyncio_detailed(
            client=client,
            claim_external_id=claim_external_id,
        )
    except HTTPError as e:
        logger.error(f"Failed to trigger claim sync. HTTP error: {e}")
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Connection error during claim sync",
            )
        ) from e

    # Check sync response
    if sync_resp.status_code == 201:
        logger.info(f"Claim sync completed successfully for claim: {claim_external_id}")
    elif sync_resp.status_code == 400:
        logger.warning(f"Claim sync returned 400 for claim: {claim_external_id}")
        # Continue anyway - the claim data might still be available
    elif sync_resp.status_code == 500:
        error_content = "Unknown error"
        if hasattr(sync_resp, "content") and sync_resp.content:
            try:
                error_content = sync_resp.content.decode("utf-8", errors="replace")
            except Exception:
                error_content = "Failed to decode error content"
        logger.error(
            f"Server error during claim sync for claim: {claim_external_id}, Error: {error_content}"
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Server error during claim sync",
            )
        )
    else:
        logger.error(
            f"Unexpected status code {sync_resp.status_code} during claim sync for claim: {claim_external_id}"
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Unexpected response during claim sync: {sync_resp.status_code}",
            )
        )

    # Step 2: Parse datetime if provided
    as_of_dt = None
    if as_of_datetime:
        try:
            # Parse RFC 3339 datetime format (supports both Z and timezone offsets)
            as_of_dt = datetime.datetime.fromisoformat(
                as_of_datetime.replace("Z", "+00:00")
            )
            # Ensure it has timezone info
            if as_of_dt.tzinfo is None:
                raise ValueError("Datetime must include timezone information")
        except ValueError as e:
            logger.error(f"Failed to parse datetime. Error: {e}")
            raise McpError(
                ErrorData(
                    code=INVALID_PARAMS,
                    message="Please provide the datetime in RFC 3339 format (e.g., '2024-01-15T23:59:59Z' or '2024-01-15T17:00:00-05:00').",
                )
            ) from e

    # Step 3: Get claim info, notes, and status log (with latest synced data)
    logger.info(f"Retrieving claim info after sync for claim: {claim_external_id}")
    try:
        info_resp = await get_claim_info_api.asyncio_detailed(
            client=client,
            claim_external_id=claim_external_id,
        )
    except HTTPError as e:
        logger.error(f"Failed to get claim info. HTTP error: {e}")
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Connection error",
            )
        ) from e

    try:
        notes_resp = await get_claim_notes_api.asyncio_detailed(
            client=client,
            claim_external_id=claim_external_id,
        )
    except HTTPError as e:
        logger.error(f"Failed to get claim notes. HTTP error: {e}")
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Connection error",
            )
        ) from e

    try:
        status_log_resp = await get_claim_status_log_api.asyncio_detailed(
            client=client,
            claim_external_id=claim_external_id,
        )
    except HTTPError as e:
        logger.error(f"Failed to get claim status log. HTTP error: {e}")
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Connection error",
            )
        ) from e

    if info_resp.status_code != 200:
        logger.error(
            f"Failed to get claim info. Status code: {info_resp.status_code}, response: {info_resp}"
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Received HTTP {info_resp.status_code} for claim info, expected {HTTPStatus.OK}",
            )
        )

    if notes_resp.status_code != 200:
        logger.error(
            f"Failed to get claim notes. Status code: {notes_resp.status_code}, response: {notes_resp}"
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Received HTTP {notes_resp.status_code} for claim notes, expected {HTTPStatus.OK}",
            )
        )

    if status_log_resp.status_code != 200:
        logger.error(
            f"""Failed to get claim status log.
Status code: {status_log_resp.status_code}, response: {status_log_resp}"""
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Received HTTP {status_log_resp.status_code} for claim status log, expected {HTTPStatus.OK}",
            )
        )

    claim_info_data = typing.cast(ClaimInfo, info_resp.parsed)
    notes_data = (
        typing.cast(list[ClaimNote], notes_resp.parsed) if notes_resp.parsed else []
    )
    status_log_data = (
        typing.cast(list[ClaimStatusLogItem], status_log_resp.parsed)
        if status_log_resp.parsed
        else []
    )

    if as_of_dt:
        # Filter by datetime when as_of_datetime is provided
        notes_as_of = [
            note.to_dict()
            for note in notes_data
            if note.created_at and note.created_at <= as_of_dt
        ]
        status_log_as_of = [
            item.to_dict()
            for item in status_log_data
            if item.created_at and item.created_at <= as_of_dt
        ]
    else:
        # Return all notes and status log when no as_of_datetime is provided (equivalent to current time)
        notes_as_of = [note.to_dict() for note in notes_data]
        status_log_as_of = [item.to_dict() for item in status_log_data]

    # Sort by createdAt descending
    notes_as_of.sort(key=lambda x: x.get("createdAt") or "", reverse=True)
    status_log_as_of.sort(key=lambda x: x.get("createdAt") or "", reverse=True)

    result = {
        "claim_info": claim_info_data.to_dict() if claim_info_data else None,
        "notes": notes_as_of,
        "status_log": status_log_as_of,
    }

    return json.dumps(result, indent=2)


DEFAULT_TOOLS = [
    get_claims_for_policy,
    get_claim_info,
]
