"""Main entry point for the unified Nirvana MCP server application.

Initializes the Starlette web application, manages application lifespan context
(including loading configuration and initializing the SessionStore), sets up
authentication middleware, and mounts routes for health checks, configuration,
and the various MCP component servers (Policy, Claims, etc.).
"""

import sentry_sdk
from mcp.server.auth.middleware.auth_context import AuthContextMiddleware
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.authentication import AuthenticationMiddleware
from starlette.requests import Request
from starlette.responses import Response
from starlette.routing import Route

from mcp_servers.lib.auth import NirvanaAuthBackend, on_auth_error
from mcp_servers.lib.config import Config
from mcp_servers.lib.log_middleware import LogRequestMiddleware
from mcp_servers.lib.oauth_provider import NirvanaTokenVerifier
from mcp_servers.mcp_server_claims.server import (
    mcp_server as mcp_server_claims,
)
from mcp_servers.mcp_server_policy.server import (
    mcp_server as mcp_server_policy,
)


def return_404(request: Request) -> Response:
    """Return a 404 response."""
    return Response(status_code=404)


def handle_health_check(request: Request) -> Response:
    """Handle the health check endpoint."""
    return Response(status_code=200)


app = Starlette(
    routes=[
        *mcp_server_policy.get_routes(namespace="policy"),
        *mcp_server_claims.get_routes(namespace="claims"),
        Route("/.well-known/oauth-authorization-server", return_404),
        Route("/register", return_404, methods=["POST"]),
        Route("/health", handle_health_check, methods=["GET"]),
    ],
    middleware=[
        Middleware(
            AuthenticationMiddleware,
            backend=NirvanaAuthBackend(
                paths_to_skip=[
                    "/health",
                    "/register",
                    "/.well-known/oauth-authorization-server",
                ],
                token_verifier=NirvanaTokenVerifier(),
            ),
            on_error=on_auth_error,
        ),
        # Add the auth context middleware to store
        # authenticated user in a contextvar
        Middleware(AuthContextMiddleware),
        Middleware(LogRequestMiddleware),
    ],
)

config = Config.load_from_env()

if config.sentry_dsn:
    # Only initialization is needed:
    # https://docs.sentry.io/platforms/python/integrations/starlette/
    # "If you have the starlette package in your dependencies, the Starlette
    # integration will be enabled automatically when you initialize the Sentry SDK."
    sentry_sdk.init(
        dsn=config.sentry_dsn,
        environment=config.environment,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=1.0,
        # Set profile_session_sample_rate to 1.0 to profile 100%
        # of profile sessions.
        profile_session_sample_rate=1.0,
        # Set profile_lifecycle to "trace" to automatically
        # run the profiler on when there is an active transaction
        profile_lifecycle="trace",
    )
