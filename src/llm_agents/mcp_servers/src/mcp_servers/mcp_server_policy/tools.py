"""Tools module providing API integration functionality for the MCP policy server."""

import datetime
import json
from http import HTTPStatus
import typing

from httpx import HTTPError
from llama_index.core import Document as LlamaDocument
from loguru import logger
from mcp.server.fastmcp import Context
from mcp.shared.exceptions import McpError
from mcp.types import INTERNAL_ERROR, PARSE_ERROR, ErrorData

from mcp_servers.lib.nvapi import get_nv_mcp_experiments_api_client
from mcp_servers.lib.config import Config
from mcp_servers.lib.redis_client_manager import get_redis_document_store
from nirvana_rest_api.api.mcp_experiments import (
    mcp_experiments_download_policy_document as download_doc_api,
)
from nirvana_rest_api.api.mcp_experiments import (
    mcp_experiments_get_policy_and_docs_as_on as policy_docs_api,
)
from nirvana_rest_api.api.mcp_experiments import (
    mcp_experiments_get_v0_policies_for_dot as dot_v0_policies_api,
)
from nirvana_rest_api.models import (
    MCPExperimentsDownloadPolicyDocument200Response,
)
from nirvana_rest_api.types import UNSET

from mcp_servers.parse.s3_utils import get_s3_object_etag
from mcp_servers.mcp_server_policy.constants import (
    SERVICE_CONTENT_TYPE_STR,
    SERVICE_PARSING_VENDOR_STR,
)
from mcp_servers.mcp_server_policy.ingestion_service import (
    PolicyDocumentIngestionService,
    validate_document_id,
    _fetch_document_url,
)


def _trim_document_dict_for_response(doc_dict: dict) -> dict:
    """Trims a LlamaDocument dictionary to include only essential fields for the API response."""
    trimmed_metadata = {
        "source_document_id": doc_dict.get("metadata", {}).get("source_document_id"),
        "api_policy_number": doc_dict.get("metadata", {}).get("api_policy_number"),
        "s3_url_base": doc_dict.get("metadata", {}).get("s3_url_base"),
        "s3_etag_at_ingest": doc_dict.get("metadata", {}).get("s3_etag_at_ingest"),
        "ingestion_timestamp": doc_dict.get("metadata", {}).get("ingestion_timestamp"),
        "parsing_vendor": doc_dict.get("metadata", {}).get(
            "parsing_vendor", SERVICE_PARSING_VENDOR_STR
        ),
        "content_type": doc_dict.get("metadata", {}).get(
            "content_type", SERVICE_CONTENT_TYPE_STR
        ),
    }
    # Filter out None values from metadata to keep it clean
    trimmed_metadata = {k: v for k, v in trimmed_metadata.items() if v is not None}

    return {
        "id_": doc_dict.get("id_"),
        "text": doc_dict.get("text"),
        "metadata": trimmed_metadata,
    }


async def get_v0_policies_for_dot(
    dot: int,
    ctx: Context,
) -> str:
    """Get a summary list of all known policies (including expired) associated with a DOT number."""
    try:
        response = await dot_v0_policies_api.asyncio_detailed(
            dot_number=dot,
            client=get_nv_mcp_experiments_api_client(ctx),
        )
    except HTTPError as e:
        logger.error(f"Failed to get dot v0 policies. HTTP error: {e}")
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Connection error",
            )
        ) from e

    if response.status_code != 200:
        logger.error(
            f"Failed to get dot v0 policies. Status code: {response.status_code}, response: {response}"
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Received HTTP {response.status_code}, expected {HTTPStatus.OK}",
            )
        )

    if response.parsed and isinstance(response.parsed, list):
        result = [
            item.to_dict() for item in response.parsed if hasattr(item, "to_dict")
        ]
        return json.dumps(result, indent=2)
    else:
        logger.error(f"Received invalid response: {response}")
        raise McpError(
            ErrorData(
                code=PARSE_ERROR,
                message="Received invalid response from server",
            )
        )


async def get_policy_as_of(
    policy_number: str,
    as_of_date: datetime.date,
    ctx: Context,
) -> str:
    """Get a point-in-time view of a specific policy.

    Args:
        policy_number (str): The policy number (e.g., "TINGL0015029-25").
        as_of_date (datetime.date): Point-in-time date for the policy view.
        ctx (Context): FastMCP server context passed by framework.
    """

    try:
        response = await policy_docs_api.asyncio_detailed(
            policy_number=policy_number,
            effective_date=as_of_date,
            client=get_nv_mcp_experiments_api_client(ctx),
        )
    except HTTPError as e:
        logger.error(f"Failed to get policy as of. HTTP error: {e}")
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message="Connection error",
            )
        ) from e

    if response.status_code != 200:
        logger.error(
            f"Failed to get policy as of. Status code: {response.status_code}, response: {response}"
        )
        return f"Received HTTP {response.status_code}, expected {HTTPStatus.OK}"

    if response.parsed and hasattr(response.parsed, "to_dict"):
        return json.dumps(response.parsed.to_dict(), indent=2)
    else:
        logger.error(f"Received invalid response: {response}")
        raise McpError(
            ErrorData(
                code=PARSE_ERROR,
                message="Received invalid response from server",
            )
        )


async def get_policy_document_url(
    policy_number: str,
    document_id: str,
    ctx: Context,
    expiration_minutes: int | None = None,
) -> str:
    """Get a pre-signed URL to download a specific policy document."""
    try:
        document_uuid = validate_document_id(document_id)
        expiration_minutes_value = expiration_minutes or UNSET

        response = await download_doc_api.asyncio_detailed(
            policy_number=policy_number,
            document_id=document_uuid,
            expiration_minutes=expiration_minutes_value,
            client=get_nv_mcp_experiments_api_client(ctx),
        )

        if response.status_code == 200:
            parsed_response = response.parsed
            if isinstance(
                parsed_response, MCPExperimentsDownloadPolicyDocument200Response
            ) and hasattr(parsed_response, "pre_signed_url"):
                return str(parsed_response.pre_signed_url)
            elif isinstance(
                parsed_response, MCPExperimentsDownloadPolicyDocument200Response
            ) and hasattr(parsed_response, "url"):
                return str(parsed_response.url)
            else:
                logger.error("Received status 200 but response is missing URL field")
                raise McpError(
                    ErrorData(
                        code=PARSE_ERROR,
                        message="Error: Received status 200 but could not extract pre-signed URL from response.",
                    )
                )
        else:
            error_payload = f"Status Code: {response.status_code}"
            if response.parsed and hasattr(response.parsed, "to_dict"):
                error_payload += f" - {json.dumps(response.parsed.to_dict())}"
            elif response.content:
                try:
                    content_str = (
                        response.content.decode()
                        if isinstance(response.content, bytes)
                        else response.content
                    )
                    error_payload += f" - {json.dumps(json.loads(content_str))}"
                except (json.JSONDecodeError, UnicodeDecodeError):
                    content_str = (
                        response.content.decode(errors="replace")[:200]
                        if isinstance(response.content, bytes)
                        else str(response.content)[:200]
                    )
                    error_payload += f" - Content: {content_str}..."
            logger.error(f"API request failed: {error_payload}")
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message=f"Error: Failed to get document URL. {error_payload}",
                )
            )
    except McpError:
        raise
    except HTTPError as e:
        logger.error(f"HTTP error when calling document API: {e}", exc_info=True)
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Error: Connection error when requesting document URL - {str(e)}",
            )
        ) from e
    except Exception as e:
        logger.critical(
            f"FATAL: An unexpected error occurred in get_policy_document_url: {e}",
            exc_info=True,
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Error: An unexpected error occurred - {str(e)}",
            )
        ) from e


async def ingest_policy_document(
    api_policy_number: str, document_id: str, ctx: Context[typing.Any, typing.Any]
) -> str:
    """Ingests a policy document using LlamaParse (MD), potentially using cached results.

    Args:
        api_policy_number: The policy number associated with the document.
        document_id: The unique UUID string for the document.
        ctx: The MCP context.

    Returns:
        A JSON string representing the list of LlamaIndex Documents (chunks).
        The first chunk includes a `metadata` block with policy/document-level
        information.  All subsequent chunks omit `metadata` to avoid redundant
        payload.  If ingestion fails, an error JSON string is returned.
    """
    try:
        config = Config.load_from_env()
        service = PolicyDocumentIngestionService(
            config=config,
            storage_provider=get_redis_document_store,
            fetch_document_url=_fetch_document_url,
            get_s3_object_etag=get_s3_object_etag,
        )

        parsed_documents: list[LlamaDocument] = await service.ingest(
            api_policy_number=api_policy_number,
            document_id=document_id,
            ctx=ctx,
        )

        if not parsed_documents:
            logger.warning(f"No documents were parsed for document ID: {document_id}")
            # Return an empty list JSON string, as per typical successful empty results.
            return json.dumps([])

        # Convert LlamaDocuments to dicts and trim for response
        logger.debug(
            f"Attempting to convert {len(parsed_documents)} LlamaDocuments to dicts for doc_id: {document_id}."
        )
        doc_dicts = [doc.to_dict() for doc in parsed_documents]

        logger.debug(
            f"Attempting to trim {len(doc_dicts)} document dicts for API response for doc_id: {document_id}."
        )
        trimmed_docs = [_trim_document_dict_for_response(dd) for dd in doc_dicts]
        logger.info(
            f"Successfully trimmed to {len(trimmed_docs)} document dicts for doc_id: {document_id}."
        )

        # ---------------------------------------------------------------------
        # Reduce response payload size by returning the common metadata only on
        # the first chunk.  Subsequent chunks contain just `id_` and `text`.
        # The shared metadata (policy number, document id, etc.) is identical
        # for every chunk, so sending it more than once is redundant and
        # increases token usage for downstream LLM calls.
        # ---------------------------------------------------------------------
        if len(trimmed_docs) > 1:
            # Remove duplicated metadata from every chunk after the first.
            # Using explicit index iteration clarifies intent.
            for idx in range(1, len(trimmed_docs)):
                # `metadata` is expected to be present on every chunk at
                # this point, but we use pop with default in case future
                # changes alter that assumption.
                trimmed_docs[idx].pop("metadata", None)

        logger.debug(
            f"Attempting to serialize list of {len(trimmed_docs)} trimmed dicts to JSON for doc_id: {document_id}."
        )
        json_output = json.dumps(trimmed_docs, indent=2)
        logger.info(
            f"Successfully serialized to JSON for doc_id: {document_id}. Output length: {len(json_output)}."
        )
        if len(json_output) < 1000:  # Log shorter outputs for easier debugging
            logger.debug(f"Serialized JSON: {json_output}")
        else:
            logger.debug(f"Serialized JSON (first 1000 chars): {json_output[:1000]}...")
        return json_output

    except McpError as e:
        logger.exception(f"Known error in ingest_policy_document: {e}")
        raise
    except Exception as e:
        logger.exception(f"Unexpected error in ingest_policy_document: {e}")
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Document ingestion failed with unexpected error: {str(e)}",
            )
        ) from e


DEFAULT_TOOLS = [
    get_v0_policies_for_dot,
    get_policy_as_of,
    get_policy_document_url,
    ingest_policy_document,
]
