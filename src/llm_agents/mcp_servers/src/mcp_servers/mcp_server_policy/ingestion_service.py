"""Service and helpers for policy document ingestion and parsing."""

import datetime
import json
import typing
from typing import Any, TypeVar
from uuid import UUID

from llama_index.core import Document as LlamaDocument
from loguru import logger
from mcp.server.fastmcp import Context
from mcp.shared.exceptions import McpError
from mcp.types import INTERNAL_ERROR, PARSE_ERROR, ErrorData

from mcp_servers.lib.config import Config
from mcp_servers.parse.parsers.factory import create_parser
from mcp_servers.parse.storage.redis_storage import (
    persist_and_retrieve_parsed_document,
)
from mcp_servers.mcp_server_policy.constants import (
    SERVICE_PARSING_VENDOR,
    SERVICE_PARSING_VENDOR_STR,
    SERVICE_CONTENT_TYPE_STR,
)


# Type variable to use for Context generic parameter
T = TypeVar("T")
# Define a type alias for Context with Any parameters
ContextT = Context[Any, Any]


# --- Helper: Validate Document ID ---


def validate_document_id(document_id: str) -> UUID:
    """Validates and converts a document ID string to a UUID."""
    if not document_id:
        logger.error("Empty document ID provided to validate_document_id")
        raise McpError(
            ErrorData(
                code=PARSE_ERROR,
                message="Invalid document ID: Document ID cannot be empty",
            )
        )
    try:
        return UUID(document_id)
    except ValueError as e:
        logger.error(f"Invalid document ID format: {document_id}")
        raise McpError(
            ErrorData(
                code=PARSE_ERROR,
                message=f"Invalid document ID format: {document_id}. Expected a valid UUID.",
            )
        ) from e


# --- Helper: Fetch Document URL ---


async def _fetch_document_url(
    api_policy_number: str, document_id: str, ctx: Context
) -> str:
    """Fetches and validates the download URL for a document."""
    logger.info(f"Requesting download URL for document_id: {document_id}")
    if not api_policy_number:
        logger.error("Empty policy number provided to _fetch_document_url")
        raise McpError(
            ErrorData(
                code=PARSE_ERROR,
                message="Cannot fetch document URL: Policy number is required",
            )
        )
    from mcp_servers.mcp_server_policy.tools import (
        get_policy_document_url,
    )

    try:
        download_url_response = await get_policy_document_url(
            policy_number=api_policy_number, document_id=document_id, ctx=ctx
        )
        logger.debug(f"Raw URL response string: {download_url_response}")
        if not isinstance(
            download_url_response, str
        ) or not download_url_response.startswith("http"):
            logger.error(
                f"Received non-URL response for document URL: {download_url_response}"
            )
            error_message = "Invalid response format"
            try:
                error_json = json.loads(download_url_response)
                if "error" in error_json:
                    error_message = error_json["error"]
                    logger.error(
                        f"API returned error when fetching URL: {error_message}"
                    )
            except (json.JSONDecodeError, TypeError):
                pass
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message=f"Invalid document URL response: {error_message}",
                )
            )
        logger.info(
            f"Successfully obtained download URL for document_id: {document_id}"
        )
        return download_url_response
    except McpError:
        raise
    except Exception as e:
        logger.exception(
            f"Unexpected error getting document URL for {document_id}: {e}",
            exc_info=True,
        )
        raise McpError(
            ErrorData(
                code=INTERNAL_ERROR,
                message=f"Failed to get document URL: {str(e)}",
            )
        ) from e


# --- Service Class ---


class PolicyDocumentIngestionService:
    """Service for orchestrating policy document ingestion, parsing, and storage."""

    def __init__(
        self,
        config: Config,
        storage_provider: typing.Callable[[Config], typing.Awaitable[typing.Any]],
        fetch_document_url: typing.Callable[[str, str, Context], typing.Awaitable[str]],
        get_s3_object_etag: typing.Callable[
            [str, Context], typing.Awaitable[str | None]
        ],
        logger: typing.Any = logger,
    ) -> None:
        """Initialize the PolicyDocumentIngestionService.

        Args:
            config: The application configuration.
            storage_provider: Callable to get the document storage provider.
            fetch_document_url: Callable to fetch the document URL.
            get_s3_object_etag: Callable to get the S3 object ETag.
            logger: The logger instance to use.
        """
        self.config = config
        self.storage_provider = storage_provider
        self.fetch_document_url = fetch_document_url
        self.get_s3_object_etag = get_s3_object_etag
        self.logger = logger

        # Create parser instance using the hardcoded vendor
        self.parser = create_parser(SERVICE_PARSING_VENDOR, config)

    def _validate_inputs(self, api_policy_number: str, document_id: str) -> UUID:
        if not api_policy_number:
            self.logger.error("Empty policy number provided to ingest_policy_document")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message="Cannot ingest document: Policy number is required",
                )
            )
        if not document_id:
            self.logger.error("Empty document ID provided to ingest_policy_document")
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message="Cannot ingest document: Document ID (UUID) is required",
                )
            )
        try:
            validated_uuid = validate_document_id(document_id)
            self.logger.debug(
                f"Validated document_id {document_id} as UUID: {validated_uuid}"
            )
        except McpError as e:
            self.logger.error(
                f"Invalid document ID format in ingest_policy_document: {document_id}"
            )
            raise McpError(
                ErrorData(
                    code=PARSE_ERROR,
                    message=f"Cannot ingest document: Invalid document ID format '{document_id}'",
                )
            ) from e
        return validated_uuid

    async def _get_document_details(
        self, api_policy_number: str, document_id: str, ctx: Context
    ) -> tuple[str, str, str | None]:
        download_url = await self.fetch_document_url(
            api_policy_number, document_id, ctx
        )
        s3_url_base = download_url.split("?")[0]
        s3_etag = await self.get_s3_object_etag(download_url, ctx)
        if s3_etag is None:
            self.logger.warning(
                f"({document_id}) Could not retrieve ETag for {s3_url_base}. Storage key will be less specific."
            )
        return download_url, s3_url_base, s3_etag

    async def _execute_parse_with_cache(
        self,
        download_url: str,
        document_id_for_logging: str,
        s3_url_base: str,
        s3_etag: str | None,
    ) -> list[LlamaDocument]:
        """Execute parsing using the decorated function, handling cache and exceptions."""

        async def core_parse_func_for_storage(
            called_download_url: str,
            _unused_s3_url_base: str | None,
            _unused_s3_etag: str | None,
            _unused_config_arg: Config | None,
            called_document_id_for_logging: str,
            called_parsing_vendor: str,
            called_content_type: str,
        ) -> list[LlamaDocument]:
            return await self.parser.parse_document(
                called_download_url,
                called_document_id_for_logging,
            )

        decorated_parse = persist_and_retrieve_parsed_document(
            redis_docstore_provider=self.storage_provider,
        )(core_parse_func_for_storage)

        documents: list[LlamaDocument] | None = None
        try:
            documents = await decorated_parse(
                download_url,
                s3_url_base,
                s3_etag,
                self.config,
                document_id_for_logging,
                SERVICE_PARSING_VENDOR_STR,
                SERVICE_CONTENT_TYPE_STR,
            )
        except McpError:
            raise
        except Exception as e:
            self.logger.error(
                f"Failed during decorated parse for document {document_id_for_logging}: {str(e)}",
                exc_info=True,
            )
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message=f"Unexpected error during document parsing/caching: {str(e)}",
                )
            ) from e

        if (
            documents is None
        ):  # This should be unreachable, but it helps mypy understand
            raise McpError(
                ErrorData(
                    code=INTERNAL_ERROR,
                    message="Document parsing unexpectedly returned None",
                )
            )
        return documents

    def _enrich_metadata(
        self,
        documents: list[LlamaDocument],
        document_id: str,
        api_policy_number: str,
        s3_url_base: str,
        s3_etag: str | None,
    ) -> None:
        """Add common metadata to parsed document chunks."""
        timestamp = datetime.datetime.now(datetime.UTC).isoformat()

        for doc_chunk in documents:
            doc_chunk.metadata["source_document_id"] = str(document_id)
            doc_chunk.metadata["api_policy_number"] = str(api_policy_number)

            if s3_etag:
                doc_chunk.metadata["s3_etag_at_ingest"] = str(s3_etag)
            doc_chunk.metadata["s3_url_base"] = str(s3_url_base)
            doc_chunk.metadata["parsed_s3_url_base_at_ingest"] = str(s3_url_base)

            doc_chunk.metadata["ingestion_timestamp"] = timestamp
            # Use the service-level constants for vendor and type
            doc_chunk.metadata["parsing_vendor"] = SERVICE_PARSING_VENDOR_STR
            doc_chunk.metadata["content_type"] = SERVICE_CONTENT_TYPE_STR

    async def ingest(
        self,
        api_policy_number: str,
        document_id: str,
        ctx: Context,
    ) -> list[LlamaDocument]:
        """Ingests a policy document: validates, fetches, parses (using configured vendor), caches, enriches."""
        validated_uuid = self._validate_inputs(api_policy_number, document_id)
        doc_id_str = str(validated_uuid)

        download_url, s3_url_base, s3_etag = await self._get_document_details(
            api_policy_number, doc_id_str, ctx
        )

        documents = await self._execute_parse_with_cache(
            download_url=download_url,
            document_id_for_logging=doc_id_str,
            s3_url_base=s3_url_base,
            s3_etag=s3_etag,
        )

        self._enrich_metadata(
            documents,
            doc_id_str,
            api_policy_number,
            s3_url_base,
            s3_etag,
        )

        for id, doc in enumerate(documents):
            doc.doc_id = f"page-{id + 1}"
        self.logger.info(
            f"Successfully processed document {doc_id_str} (policy: {api_policy_number}, "
            f"vendor: {SERVICE_PARSING_VENDOR_STR}, type: {SERVICE_CONTENT_TYPE_STR}). "
            f"Returning {len(documents)} document chunks."
        )
        return documents
