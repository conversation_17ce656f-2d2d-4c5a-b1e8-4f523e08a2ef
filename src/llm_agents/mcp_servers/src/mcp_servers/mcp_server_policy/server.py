"""Defines the MCP server instance for the Policy API."""

import os
from typing import Any, Callable, cast

from mcp.server.sse import SseServerTransport
from mcp_servers.lib.server import NirvanaMCP
from mcp_servers.mcp_server_policy import tools

DEFAULT_TIMEOUT_SECONDS = int(os.getenv("MCP_DEFAULT_TIMEOUT_SECONDS", "30"))

# Define the transport for the policy server
policy_transport = SseServerTransport(endpoint="/messages/")

# Initialize the MCP server in a module-level variable so
# it can be used as a decorator for tools. See tools.py.
mcp_server = NirvanaMCP(
    server_name="Policy MCP Server",
    default_timeout_seconds=DEFAULT_TIMEOUT_SECONDS,
    transport=policy_transport,
)

# Register tools specific to the policy server
for tool_func in tools.DEFAULT_TOOLS:
    mcp_server.tool()(cast(Callable[..., Any], tool_func))
