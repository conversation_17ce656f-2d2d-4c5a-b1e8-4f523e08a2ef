import os
import time
import warnings
from datetime import datetime
from typing import Any

import metaflow
import numpy as np
import streamlit as st
from millify import millify

import telemetry.api.logging as logging
import telemetry.api.trace as trace
from boards import st_utils
from boards.config import VisualisationConfig
from boards.basic_info import get_camera_fraction
from boards.basic_info import get_mileage_summary
from boards.basic_info import get_odometer_fraction
from boards.basic_info import get_harsh_event_threshold_message
from boards.basic_info import prepare_fleet_level_information_metaflow

from boards.pipeline import get_latest_pipeline_summary_for_connection_id
from boards.pipeline import get_metaflow_run
from boards.pipeline import proto

from boards.uw_board.trs_views import trs_widgets
from boards.uw_board.milliman_views import milliman_widgets

from boards.uw_board.helpers import get_country_metrics
from boards.uw_board.path_data import path_widgets

from boards.visualisation.fleet import fetch_top_garaging_counties
from boards.visualisation.fleet import generate_mileage_and_garaging_plot
from boards.visualisation.fleet import get_fleet_run_rate_graph
from boards.visualisation.fleet import get_fleet_state_wise_mileage

from boards.visualisation.trs import get_risk_scores
from boards.visualisation.trs import expose_trs_check
from boards.visualisation.garage import update_vin_garage_table
from boards.visualisation.vin import visualise_data_completeness

from protolib.utils import proto_to_datetime

from telematics_utils.risk_scores.config import RiskScoreType

from telematics_utils.fleet.config import ProgramType
from telematics_utils.risk_scores.milliman.config import MillimanConfig
from utils import api
from utils.constants import Constants

log = logging.getLogger(__name__)

warnings.filterwarnings("ignore")


@trace.trace_method_with_name("UWBoard")
def app():
    """
    Streamlit tab for UW boards
    """

    title = "UW Board"
    app_review = app_header(title)

    connection_id = app_review.get("summary", dict({})).get("tspConnectionHandleId")
    if connection_id is None:
        st.error("Connection Handle Name not found")
        return

    show_pipeline_status(connection_id)

    pipeline = get_metaflow_run(connection_id=connection_id)

    if pipeline is None:
        st.error("{} | Did not find a valid metaflow run".format(connection_id))
        return

    tsp = pipeline.data.tsp
    if tsp is None:
        st.error("TSP Name not found")
        return

    if trace.get_current_span() != trace.INVALID_SPAN:
        span = trace.get_current_span()
        if span.is_recording():
            span.set_attributes({
                'app_review_id': st.session_state.get('app_review_id'),
                'tsp_connection_id': connection_id,
                'log2_num_vehicles': int(np.log2(1 + pipeline.data.vins.shape[0])),
            })

    st.markdown("### Application details")
    show_summary(app_review, tsp)
    st.markdown("---")
    show_gps_data(app_review, pipeline, tsp)
    st.markdown("---")


@trace.trace_method
def show_pipeline_status(connection_id: str) -> None:
    """Method to show status of unified pipeline

    Parameters
    ----------
    connection_id : str
        Connection handle id
    """

    # gRPC call to fetch the pipeline status
    pipeline_summary = get_latest_pipeline_summary_for_connection_id(connection_id)

    st.markdown("### Pipeline status")
    if pipeline_summary is None:
        st.error("No recent versions of pipeline exist, showing data from older version")
        return

    if pipeline_summary.status == proto.Succeeded:
        status_message = ":robot_face: Pipeline finished at "
        status_message += f"{proto_to_datetime(pipeline_summary.finished_at).strftime('%Y-%m-%d %H:%M UTC')}"

    elif pipeline_summary.status in [proto.Running, proto.Unknown]:
        status_message = ":woman-running: Pipeline is running. Data might be outdated."
        status_message += f"\n[View details on the support app](" \
                          f"https\\://support.nirvanatech.com/te" \
                          f"lematics/pipeline/{pipeline_summary.pipeline_id}) "
    else:
        status_message = ":confounded: Last pipeline had failed, data might be outdated."
        status_message += f"\n[View details on the support app](" \
                          f"https\\://support.nirvanatech.com/te" \
                          f"lematics/pipeline/{pipeline_summary.pipeline_id}) "

    st.write(
        status_message,
        unsafe_allow_html=True,
    )


def app_header(title):
    st.title(title)
    st_utils.break_lines(2)

    active_session_id = st.session_state.get("session_id")

    is_non_fleet = "False"

    if "app_review_id" in st.query_params:
        default_app_review_id = st.query_params["app_review_id"]
    else:
        default_app_review_id = os.environ.get("DEFAULT_APP_REVIEW_ID", "")

    app_review_id = st.text_input("Application Review ID", value=default_app_review_id)

    if app_review_id == "":
        st.stop()

    if "nonfleet" in st.query_params:
        is_non_fleet = st.query_params["nonfleet"]

    if is_non_fleet.lower() == "true":
        app_review, error = api.get_app_review_non_fleet(app_review_id, session_id=active_session_id)
    else:
        app_review, error = api.get_app_review(app_review_id, session_id=active_session_id)

    if error is not None:
        st.error(error)
        st.stop()

    st.query_params.app_review_id = app_review_id
    st.session_state['app_review_id'] = app_review_id
    return app_review


def show_summary(app_review, tsp, container=st):
    with container.expander("Show Application Summary", expanded=False):
        st_utils.break_lines(1)
        col_a, col_b = container.columns([1, 2])
        col_a.metric(label="Short ID", value=f"#{app_review['summary']['applicationShortID']}")
        col_b.metric(label="Company", value=app_review["summary"]["companyName"])

        col_c, col_d, col_e = container.columns([1, 1, 1])
        col_c.metric(label="TSP", value=tsp)
        col_d.metric(label="DOT Number", value=app_review["summary"]["dotNumber"])
        effective_date = datetime.strptime(app_review["summary"]["effectiveDate"], "%Y-%m-%d").strftime("%b %d, %Y")
        col_e.metric(label="Effective Date", value=effective_date)

        col_f = container.columns([1])[0]
        col_f.metric(label="State", value=app_review["summary"]["state"].lstrip("ApplicationReviewState"))
        st_utils.break_lines(1)


@trace.trace_method
@st_utils.spinner("Preparing fleet level metrics ...")
def show_fleet_level_metrics(
        pipeline: metaflow.Run,
        connection_id: str,
        vin_list: Any,
        projected_vins: int,
        container: st.delta_generator.DeltaGenerator,
) -> Any:
    start_time = time.time()

    # we support fleet metrics on a subset of vins as well,
    # but for performance we disable it now.
    fleet_metric = prepare_fleet_level_information_metaflow(
        pipeline=pipeline, vin_list=vin_list, vin_group_1=vin_list
    )
    time_taken = np.round(time.time() - start_time, 3)
    log.info(
        "Completed preparing fleet level info",
        connection_id=connection_id,
        num_vins=len(vin_list),
        time_taken=time_taken,
    )

    hazardous_miles, hazardous_duration = st.columns([1, 1])
    radius_op, vin_daily_average, annual_projection = st.columns([1, 1, 1])
    _, odometer, camera = st.columns([1, 1, 1])
    st.markdown('##### Travel outside of USA')
    canada_distance, canada_duration, mexico_distance, mexico_duration = st.columns([1, 1, 1, 1])

    odometer_fraction = get_odometer_fraction(pipeline=pipeline)
    odometer.metric(label="Odometer Availability for VINs", value="{}".format(odometer_fraction))

    camera_fraction = get_camera_fraction(pipeline=pipeline)
    camera.metric(label="Camera Availability for VINs", value="{}".format(camera_fraction))

    st_utils.break_lines(1)

    radius = fleet_metric.get("radius")

    if np.isnan(radius):
        radius = "-"
    else:
        radius = millify(radius, precision=2)

    radius_op.metric(label="Radius of Operation", value="{} mi".format(radius))

    hazard_dist = fleet_metric.get("hazard_distance")
    hazard_dist_pct = fleet_metric.get("hazard_distance_pct")
    hazardous_miles.metric(
        label="Distance Traveled in Hazard Zones",
        value="{} mi ({}%)".format(millify(hazard_dist, precision=2), np.round(hazard_dist_pct, 2)),
    )

    hazard_duration = fleet_metric.get("hazard_duration")
    hazard_duration_pct = fleet_metric.get("hazard_duration_pct")
    hazardous_duration.metric(
        label="Time Spent in Hazard Zones",
        value="{} hrs ({}%)".format(hazard_duration // Constants.SECONDS_IN_HOUR, np.round(hazard_duration_pct, 2)),
    )

    canada_distance_value, canada_duration_value = get_country_metrics(fleet_metric, vin_list, "canada")
    canada_distance.metric(label="Distance travelled in Canada", value=canada_distance_value)
    canada_duration.metric(label="Time Spent in Canada", value=canada_duration_value)

    mexico_distance_value, mexico_duration_value = get_country_metrics(fleet_metric, vin_list, "mexico")
    mexico_distance.metric(label="Distance travelled in Mexico", value=mexico_distance_value)
    mexico_duration.metric(label="Time Spent in Mexico", value=mexico_duration_value)

    # adding check for odo meter based numbers
    daily, yearly = get_mileage_summary(fleet_metric=fleet_metric, projected_vins=projected_vins)

    vin_daily_average.metric(label="Average Daily Miles per Vin", value=daily)
    annual_projection.metric(label="Projected Annual Miles", value=yearly)

    return fleet_metric


@trace.trace_method
def show_gps_data(
        app_review: dict, pipeline: metaflow.Run, tsp: str,
        container: st.delta_generator.DeltaGenerator = st,
):
    """
    Telematics widgets

    Parameters
    ----------
    app_review  : dict
        Application information from application server
    pipeline : Any
        Metaflow pipeline run object
    tsp : str
        TSP name
    container : Any
        Streamlit object

    """
    is_non_fleet = pipeline.data.program_type == ProgramType.NONFLEET_PROGRAM_TYPE
    active_session_id = st.session_state.get("session_id")
    container.markdown("### GPS Data")

    log.info("N.A | N.A | Processing application", app_review_id=app_review["summary"]["appReviewID"])
    company_name = app_review["summary"]["companyName"]

    equipment_list, error_equipment = api.get_equipment_list(
        app_review["summary"]["appReviewID"], _session_id=active_session_id,
        non_fleet=app_review["summary"].get("nonfleet", False)
    )
    if error_equipment is not None:
        equipment_list = dict({})

    projected_information, error = api.get_projected_information(
        app_review["summary"]["appReviewID"], session_id=active_session_id
    )

    if error is None:
        projected_vins = projected_information["units"]
    else:
        projected_vins = 0

    connection_id = app_review.get("summary", dict({})).get("tspConnectionHandleId")

    if tsp is None:
        st.error("TSP Name not found")
        return

    if connection_id is None:
        st.error("Connection Handle Name not found")
        return

    with st.spinner('Fetching telematics VINs ...'):
        vin_list = pipeline.data.vins
        if connection_id in Constants.CONNECTION_ID_GEC:
            vin_list = np.array(Constants.VINS_GEC)

    if vin_list.shape[0] == 0:
        st.error("No valid VINs in telematics data folder")
        return

    fleet_metric = show_fleet_level_metrics(pipeline, connection_id, vin_list, projected_vins, container)

    # Prepare Run rate view

    container.markdown("#### Weekly Run Rate")
    with st.spinner("Calculating weekly run rate ..."):
        week_view = fleet_metric.get("week_view")
        expanded = True if is_non_fleet else False
        if week_view is not None:
            with container.expander("Show Fleet Run Rate", expanded=expanded):
                st.download_button(
                    label="Download Weekly Distances as CSV",
                    data=week_view.to_csv(index=False).encode("utf-8"),
                    file_name="{} Weekly Run Rate.csv".format(company_name),
                    mime="text/csv",
                )
                st_utils.break_lines(1)
                fig = get_fleet_run_rate_graph(week_view)
                st.plotly_chart(fig, use_container_width=True)
                st_utils.break_lines(1)

    # Prepare Choropleth view
    container.markdown("#### State Level Mileage")
    with st.spinner("Grouping mileages by state ..."):
        state_view = fleet_metric.get("state_view")
        if state_view is not None:
            expanded = True if is_non_fleet else False
            with container.expander("Show State Level Usage", expanded=expanded):
                st.download_button(
                    label="Download State Level Distances as CSV",
                    data=state_view.to_csv(index=False).encode("utf-8"),
                    file_name="{} State Level Mileage.csv".format(company_name),
                    mime="text/csv",
                )
                st_utils.break_lines(1)
                state_table, top_state_table = get_fleet_state_wise_mileage(state_view)
                mileage_plot_container = st.container()
                st_utils.break_lines(1)
                garaging_and_mileage_st_section = st.container()
                garaging_and_mileage_st_section.dataframe(top_state_table, height=200)
                st_utils.break_lines(1)

    container.markdown("### GPS Path Visualisation")
    with st.spinner("Rendering GPS Path data into map ..."):
        path_widgets(
            container=container,
            connection_id=connection_id,
            pipeline=pipeline,
            vin_list=vin_list,
            fleet_metric=fleet_metric,
            interactive=True,
        )

    st.markdown("#### VIN Level Data")
    start_time = time.time()
    with st.spinner("Preparing VIN Level Data..."), trace.default.start_as_current_span('vin_level_data'):

        vin_table = pipeline.data.vin_table
        vin_table = vin_table[vin_table.VIN.isin(vin_list)]
        garaging_table = pipeline.data.garage_table
        garaging_table = garaging_table[garaging_table.VIN.isin(vin_list)]

        vin_table, garaging_table, vin_csv, garage_csv = update_vin_garage_table(
            equipment_list=equipment_list, vin_table=vin_table, garaging_table=garaging_table
        )

    time_taken = np.round(time.time() - start_time, 3)
    log.info("Tabular information", connection_id=connection_id, time_taken=time_taken)

    st.markdown(
        """<style>
            .col_heading   {text-align: right !important}
            </style>
        """,
        unsafe_allow_html=True,
    )

    st.download_button(
        label="Download VIN Table as CSV",
        data=vin_csv,
        file_name="{} VIN Table.csv".format(company_name),
        mime="text/csv",
    )

    st.dataframe(vin_table, height=400)

    mileage_with_garaging_fig = generate_mileage_and_garaging_plot(state_table, garaging_table)
    mileage_plot_container.plotly_chart(mileage_with_garaging_fig, use_container_width=True)
    top_counties = fetch_top_garaging_counties(garaging_table)
    garaging_and_mileage_st_section.dataframe(top_counties)
    garaging_and_mileage_st_section.download_button(
        label="Download Garaging Table as CSV",
        data=garage_csv,
        file_name="{} Garaging Table.csv".format(company_name),
        mime="text/csv",
    )

    if VisualisationConfig.SHOW_RISK_SCORES:
        if VisualisationConfig.ENABLE_TRS:
            if VisualisationConfig.SHOW_SCORE_SOURCE_DROPDOWN:
                risk_score_source = st.selectbox('Select Risk Score Source', ('TRS', 'Milliman'))
            else:
                risk_score_source = 'TRS'
        else:
            risk_score_source = 'Milliman'

        if risk_score_source == 'Milliman':
            start_time = time.time()
            with st.spinner("Fetching milliman scores ..."), trace.default.start_as_current_span("milliman_scores"):
                if "score_type" not in pipeline.data:
                    score_type = RiskScoreType.MAIN
                else:
                    score_type = pipeline.data.score_type

                default_version = MillimanConfig.DEFAULT_BOARDS_VERSION.get(score_type)

                if "versioned_milliman_trend" in pipeline.data:
                    milliman_trend = pipeline.data.versioned_milliman_trend
                    milliman_trend = milliman_trend[milliman_trend.version == default_version]
                else:
                    st.write("Versioned Milliman not found, a rerun of metaflow pipeline might be required")
                    milliman_trend = None

                time_taken = np.round(time.time() - start_time, 3)
                log.info("Milliman information", connection_id=connection_id, time_taken=time_taken)

                if milliman_trend is not None:
                    st.markdown("#### VIN Overview")
                    fig = visualise_data_completeness(
                        vin_table=vin_table,
                        score_trend=milliman_trend[milliman_trend.vin.isin(vin_list)],
                        score_source=risk_score_source
                    )
                    st.plotly_chart(fig, use_container_width=True)

                # Generate milliman widgets
                milliman_widgets(
                    pipeline=pipeline,
                    milliman_trend=milliman_trend,
                    vin_list=vin_list,
                    company_name=company_name,
                    version=default_version,
                    score_type=score_type,
                    interactive=True,
                )
        else:
            with st.spinner("Fetching TRS ..."), trace.default.start_as_current_span("trs"):
                fleet_trs = None
                vin_trs = None
                if "telematics_risk_score_v1" in pipeline.data:
                    if pipeline.data.run_trs_v1:
                        expose_trs_flag, hide_reason = expose_trs_check(pipeline=pipeline)
                        if expose_trs_flag:
                            if is_non_fleet and hide_reason != "":
                                st.write(':warning: :red[{}]'.format(hide_reason))

                            vin_trs, fleet_trs, score_type = get_risk_scores(pipeline=pipeline, is_non_fleet=is_non_fleet)
                        else:
                            st.write("TRS not shown as " + hide_reason)
                    else:
                        st.write("TRS not enabled for this fleet")
                else:
                    st.write("This pipeline version is no longer supported. A re-run of metaflow pipeline required")

                if vin_trs is not None:
                    st.markdown("#### VIN Overview")
                    fig = visualise_data_completeness(
                        vin_table=vin_table,
                        score_trend=vin_trs[vin_trs.vin.isin(vin_list)],
                        score_source=risk_score_source
                    )
                    st.plotly_chart(fig, use_container_width=True)

                    valid_vin_trs = vin_trs[vin_trs.score.notnull()]

                    if len(valid_vin_trs) > 0:
                        st.write('### Score Type: {}'.format(score_type))
                        message = get_harsh_event_threshold_message(pipeline=pipeline, threshold=0.4)
                        st.write("### {}".format(message))

                    # Generate TRS widgets
                    trs_widgets(
                        vin_trs_trend=vin_trs,
                        fleet_trs_trend=fleet_trs,
                        company_name=company_name,
                        interactive=True,
                        is_non_fleet=is_non_fleet,
                    )
