class VisualisationConfig:
    # UI toggles
    SHOW_RISK_SCORES = True    # Controls exposing risk score trend on boards app
    ENABLE_TRS = False  # Controls enabling TRS on boards app
    SHOW_SCORE_SOURCE_DROPDOWN = True   # Controls showing score source dropdown on boards app (TRS/Milliman)
    ENABLE_TRS = True
    SHOW_SCORE_SOURCE_DROPDOWN = True

    # ---- H3 Parameters ----
    H3_RESOLUTION = 9

    # ---- Route speed bucket colors and thresholds ----
    ROUTE_SPEED_BUCKETS = [
        {"max": 10,  "color": "#fde725",  "desc": "yellow"},         # 0-10 (lightest)
        {"max": 20,  "color": "#a5de38",  "desc": "lime green"},
        {"max": 40,  "color": "#35b779",  "desc": "green"},
        {"max": 60,  "color": "#008000",  "desc": "dark green"},
        {"max": 75,  "color": "#31688e",  "desc": "blue"},
        {"max": float('inf'), "color": "#440154", "desc": "purple"}  # 75+ (darkest)
    ]
    ROUTE_SPEED_MIN_MPH = 0
    ROUTE_SPEED_MAX_MPH = 100

    TRAFFIC_ROUTE_POLYLINE_WEIGHT = 4
    TRAFFIC_ROUTE_POLYLINE_OPACITY = 1.0  # FULL OPACITY

    # ---- Halts config ----
    HALT_MIN_SPEED_MPH = 3.0
    HALT_MIN_SECONDS = 0
    HALT_CIRCLE_RADIUS = 9
    HALT_CIRCLE_BORDER_WEIGHT = 2
    HALT_CIRCLE_BORDER_COLOR = "black"
    HALT_CIRCLE_BORDER_COLOR_IF_BLACK = "white"
    HALT_CIRCLE_OPACITY = 0.95
    HALT_LEGEND_ROWS = [
        ("lightblue", "&lt;5 min", "&#9679;", "circle"),
        ("green",     "5–15 min", "&#9679;", "circle"),
        ("orange",    "15–30 min","&#9679;", "circle"),
        ("red",       "30–60 min","&#9679;", "circle"),
        ("purple",    "1–3 hrs",  "&#9679;", "circle"),
        ("black",     "&gt;3 hrs","&#9679;", "circle"),
    ]
    HALT_DURATION_MINS_COLOR_THRESH = [5, 15, 30, 60, 180]
    HALT_DURATION_COLORS = [row[0] for row in HALT_LEGEND_ROWS]

    # ---- Road Class ----
    ROAD_CLASS_LEGEND = [
        ("purple", "Road class change point", "&#9650;", "triangle")
    ]
    ROAD_CLASS_MARKER_RADIUS = 12
    ROAD_CLASS_MARKER_COLOR = "purple"
    ROAD_CLASS_MARKER_OPACITY = 0.9

    # ---- Highway Junctions ----
    HIGHWAY_JUNCTIONS_LEGEND = [
        ("brown", "Highway junction", "&#9632;", "square")
    ]
    HIGHWAY_JUNCTIONS_MARKER_RADIUS = 11
    HIGHWAY_JUNCTIONS_MARKER_COLOR = "brown"
    HIGHWAY_JUNCTIONS_MARKER_OPACITY = 0.9
    HIGHWAY_JUNCTIONS_MARKER_WEIGHT = 2

    # ---- Rest Stops ----
    REST_STOP_LEGEND = [
        ("teal", "Rest/Layby/Service", "&#9632;", "diamond")
    ]
    REST_STOP_MARKER_RADIUS = 11
    REST_STOP_MARKER_COLOR = "teal"
    REST_STOP_MARKER_OPACITY = 0.9
    REST_STOP_MARKER_WEIGHT = 2
    REST_STOP_MARKER_ROTATION = 45

    # ---- Traffic Signals ----
    TRAFFIC_SIGNAL_LEGEND = [
        ("#6A5ACD", "Traffic signal", "&#11042;", "hexagon")
    ]
    TRAFFIC_SIGNAL_MARKER_RADIUS = 11
    TRAFFIC_SIGNAL_MARKER_COLOR = "#6A5ACD"
    TRAFFIC_SIGNAL_MARKER_OPACITY = 0.9
    TRAFFIC_SIGNAL_MARKER_WEIGHT = 2
    TRAFFIC_SIGNAL_MARKER_SIDES = 6

    # ---- Stop Signs ----
    STOP_SIGN_LEGEND = [
        ("#4B0082", "Stop sign", "&#10133;", "plus")
    ]
    STOP_SIGN_MARKER_ICON = "plus"
    STOP_SIGN_MARKER_COLOR = "purple"
    STOP_SIGN_MARKER_PREFIX = "fa"

    # ---- Accident Marker ----
    ACCIDENT_MARKER_COLOR = "red"
    ACCIDENT_MARKER_ICON = "exclamation-sign"
    ACCIDENT_MARKER_PREFIX = "glyphicon"

    # ---- Harsh Braking Marker ----
    HARSH_BRAKING_MARKER_COLOR = "orange"
    HARSH_BRAKING_MARKER_ICON = "exclamation-triangle"
    HARSH_BRAKING_MARKER_PREFIX = "fa"
