syntax = "proto3";
package risk_factors;

option go_package = "nirvanatech.com/nirvana/underwriting/risk_factors";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";


service RiskFactorsService {
  // List All Risk Factors, since we don't have any filters rn, we can use google.protobuf.Empty as
  // parameter
  rpc ListRiskFactors(google.protobuf.Empty) returns (ListRiskFactorsResponse);
  // Worksheet operations (P0)
  rpc CreateWorksheet(CreateWorksheetRequest) returns (CreateWorksheetResponse);
  // GetWorksheet is used to fetch all the details of a worksheet
  rpc GetWorksheet(GetWorksheetRequest) returns (Worksheet);
  // GetLatestWorksheetForReview is used to fetch the latest worksheet for a review
  rpc GetLatestWorksheetForReview(GetLatestWorksheetForReviewRequest) returns (Worksheet);
  // UpdateWorksheet is used to update the worksheet, worksheet factors and worksheet pricing details
  rpc UpdateWorksheet(WorksheetUpdateRequest) returns (google.protobuf.Empty);

  rpc AddWorksheetRiskFactor(AddWorksheetRiskFactorRequest) returns (WorksheetRiskFactorResponse);
  rpc UpdateWorksheetRiskFactor(UpdateWorksheetRiskFactorRequest) returns (WorksheetRiskFactorResponse);
  rpc DeleteWorksheetRiskFactor(DeleteRiskFactorRequest) returns (google.protobuf.Empty);
  rpc UpdateWorksheetPricing(UpdateWorksheetPricingRequest) returns (google.protobuf.Empty);
  rpc SuggestRiskFactor(SuggestRiskFactorRequest) returns (google.protobuf.Empty);
  rpc InsertOrUpdateRiskFactor(InsertOrUpdateRiskFactorRequest) returns (google.protobuf.Empty);
  rpc GetAllWorksheetFactorWithTag(GetAllWorksheetFactorWithTagRequest) returns (GetAllWorksheetFactorWithTagResponse);
  rpc DeleteRiskFactorByLabel(DeleteRiskFactorByLabelRequest) returns (google.protobuf.Empty);
}

message InsertOrUpdateRiskFactorRequest {
  string review_id = 1;
  RiskFactorLabel risk_factor_label = 2;
  string value = 3;
  string notes = 4;
  Sentiment sentiment = 5;
  repeated CoveragePricingDetail coverage_pricing_detail = 6;
}

// Request to fetch worksheet factors by tag(s)
message GetAllWorksheetFactorWithTagRequest {
  string worksheet_id = 1;
  repeated string tags = 2;
}

// Response for fetching worksheet factors by tag(s)
message GetAllWorksheetFactorWithTagResponse {
  repeated WorksheetRiskFactorResponse worksheet_risk_factors = 1;
}

// New Sentiment enum to match OpenAPI specification
enum Sentiment {
  SENTIMENT_UNSPECIFIED = 0;
  POSITIVE = 1;
  NEGATIVE = 2;
  NEUTRAL = 3;
}


// New request messages
message AddWorksheetRiskFactorRequest {
  string worksheet_id = 1;
  string risk_factor_id = 2;
  string value = 3;
  Sentiment sentiment = 4;
  string notes = 5;
}

message UpdateWorksheetRiskFactorRequest {
  string worksheet_risk_factor_id = 1;
  string value = 2;
  Sentiment sentiment = 3;
  string notes = 4;
}

message UpdateWorksheetPricingRequest {
  string worksheet_id = 1;
  CategoryPricingDetails category_pricing_details = 2;
  string worksheet_factor_id = 3;
  repeated CoveragePricingDetail coverage_pricing_detail = 4;
}

message SuggestRiskFactorRequest {
  string name = 1;
  string notes = 2;
  Category category = 3;
  string value = 4;
  Sentiment sentiment = 5;
  string worksheet_id = 6;
}

message GetLatestWorksheetForReviewRequest{
  string review_id = 1;
}

message WorksheetUpdateRequest{
  string worksheet_id = 1;
  string notes = 2;
}

message UpdateWorksheetRatingDetailsRequest{
  repeated CategoryPricingDetails category_pricing_details = 1;
}

message CategoryPricingDetails{
  Category category = 1;
  repeated CoveragePricingDetail coverage_pricing_detail = 2;
}

enum Coverage{
  UNSPECIFIED_COVERAGE = 0;
  AUTO_LIABILITY = 1;
  GENERAL_LIABILITY = 2;
  AUTO_PHYSICAL_DAMAGE = 3;
  MOTOR_TRUCK_CARGO = 4;
}

message CoveragePricingDetail{
   Coverage coverage = 1;
   int32 credit = 2;
}

message DeleteRiskFactorRequest{
  string worksheet_risk_factor_id = 1;
}

message ListWorksheetRiskFactorsRequest{
  string worksheet_id = 1;
}

message ListWorksheetRiskFactorsResponse{
  repeated WorksheetRiskFactorResponse worksheet_risk_factors = 1;
}

message GetWorksheetRequest{
  string worksheet_id = 1;
}


message Worksheet{
  string id = 1;
  string review_id = 2;
  repeated WorksheetRiskFactorResponse worksheet_risk_factors = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  // Id of the user who last updated the worksheet
  string last_updated_by = 6;
  WorksheetState state = 7;
  WorksheetPricingDetails pricing_details = 8;
  string notes = 9;
  int32 version = 10;
}

enum WorksheetState{
  UNSPECIFIED_WORKSHEET_STATE = 0;
  WORKSHEET_STATE_APPROVED = 1;
  WORKSHEET_STATE_ACTIVE = 2;
  WORKSHEET_STATE_STALE = 3;
}

message WorksheetPricingDetails {
  string worksheet_id = 1;
  string worksheet_pricing_id = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  // Since Worksheet and it's related entity like worksheet pricing details is created
  // automatically hence we don't need to store the user who created it.
  string last_updated_by = 5;
  repeated CategoryPricingDetails category_pricing_details = 6;
}

message WorksheetRiskFactorResponse{
  string worksheet_id = 1;
  string worksheet_risk_factor_id = 2;
  RiskFactor risk_factor = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  // Id of the user who updated the risk factor
  string created_by = 6;
  string last_updated_by = 7;
  string notes = 8;
  string value = 9;
  Sentiment sentiment = 10;
  repeated CoveragePricingDetail coverage_pricing_detail = 11;
}

message CreateWorksheetRequest{
  string review_id = 1;
}

message CreateWorksheetResponse{
  string worksheet_id = 1;
}


message ListRiskFactorsResponse {
  repeated RiskFactor risk_factors = 1;
}

message RiskFactor {
  string id = 1;
  string name = 2;
  string description = 3;
  // Risk factors are categorised on the basis of the type of risk they mitigate.
  // Example: Operations, Equipments, Drivers, Safety, Financials, Losses
  Category category = 4;
  // Pricing Type defines how the risk factor is priced. Example: Already Priced In RateMl, Manually Priced
  PricingType pricing_type = 5;
  int32 version = 6;
  RiskFactorState state = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  // Id of the user who last updated the risk factor
  string last_updated_by = 10;
  string created_by = 11;
  bool is_system_generated = 12;
  // Tags for categorizing and filtering risk factors
  repeated string tags = 13;

  optional string composite_name = 14;
}

enum RiskFactorState{
  UNSPECIFIED_RISK_FACTOR_STATE = 0;
  ACTIVE = 1;
  INACTIVE = 2;
  DRAFT = 3;
}

enum PricingType{
  UNSPECIFIED_PRICING_TYPE = 0;
  ALREADY_PRICED = 1;
  MANUALLY_PRICED = 2;
}

enum Category{
  UNSPECIFIED_CATEGORY = 0;
  OPERATIONS = 1;
  EQUIPMENTS = 2;
  DRIVERS = 3;
  SAFETY = 4;
  FINANCIALS = 5;
  LOSSES = 6;
}

enum RiskFactorLabel{
  UNSPECIFIED_RISK_FACTOR_LABEL = 0;
  Telematics_Risk_Score = 1;
  M1_Score = 2;
  Utilization_Adjustment = 3;
  Vin_Visibility_Adjustment = 4;
  Fleet_Size_Adjustment = 5;
  Primary_Operation_Class_Adjustment = 6;
  Hazard_Zones_Distance_Adjustment = 7;
  BASIC_Alert_Count_Adjustment = 8;
  Driver_OOS_Adjustment = 9;
  Overall_OOS_Adjustment = 10;
  BASIC_Unsafe_Driving_Score_Adjustment = 11;
  Driver_Turnover_Adjustment = 12;
  BASIC_Vehicle_Maintenance_Score_Adjustment = 13;
  BASIC_HOS_Compliance_Score_Adjustment = 14;
}

message DeleteRiskFactorByLabelRequest {
  string review_id = 1;
  RiskFactorLabel risk_factor_label = 2;
}