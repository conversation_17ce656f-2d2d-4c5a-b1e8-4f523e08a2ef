#!/bin/bash

# This script (force) deploys all services

# Fail fast
set -euo pipefail

script_dir=$(cd -P -- "$(dirname -- "$0")" && pwd -P)
cd "$script_dir"/app

# Taint services to force re-deployment. This is needed as our image tag 
# doesn't change right now
terraform taint aws_ecs_task_definition.api_server
terraform taint aws_ecs_task_definition.gql_server
terraform taint aws_ecs_task_definition.sms_scraper

terraform apply

