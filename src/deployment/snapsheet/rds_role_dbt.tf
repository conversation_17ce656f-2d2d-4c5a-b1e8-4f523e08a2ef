data "aws_secretsmanager_secret" "snapsheet_dbroles_credentials" {
  name = "claims/snapsheet/db-roles"
}

data "aws_secretsmanager_secret_version" "snapsheet_dbroles_credentials" {
  secret_id = data.aws_secretsmanager_secret.snapsheet_dbroles_credentials.id
}

module "dbt_dev_rds_role" {
  source = "./postgres"

  host     = aws_db_instance.snapsheet_sandbox_rds.address
  username = aws_db_instance.snapsheet_sandbox_rds.username
  password = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_sandbox_rds_db_password.secret_string)["password"]
  database = "dbt_dev" # created manually via psql

  create_dbt_role     = true
  dbt_role_name       = "dbt_dev_user"
  dbt_role_password   = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_dbroles_credentials.secret_string)["dbt_dev"]


  dbt_source_schemas = [
    {
      name     = "public"
    }
  ]
}

module "dbt_sandbox_rds_role" {
  source = "./postgres"

  host     = aws_db_instance.snapsheet_sandbox_rds.address
  username = aws_db_instance.snapsheet_sandbox_rds.username
  password = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_sandbox_rds_db_password.secret_string)["password"]
  database = "snapsheet_sandbox"

  create_dbt_role     = true
  dbt_role_name       = "dbt_sandbox_user"
  dbt_role_password   = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_dbroles_credentials.secret_string)["dbt_sandbox"]


  dbt_source_schemas = [
    {
      name     = "public"
    }
  ]
}


module "dbt_production_rds_role" {
  source = "./postgres"

  host     = aws_db_instance.snapsheet_production_rds.address
  username = aws_db_instance.snapsheet_production_rds.username
  password = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_production_rds_db_password.secret_string)["password"]
  database = "snapsheet_production"

  create_dbt_role     = true
  dbt_role_name       = "dbt_production_user"
  dbt_role_password   = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_dbroles_credentials.secret_string)["dbt_production"]


  dbt_source_schemas = [
    {
      name     = "public"
    },
    {
      name     = "snapsheet_sandbox"
    }
  ]
}
