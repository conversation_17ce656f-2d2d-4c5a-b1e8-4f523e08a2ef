data "aws_kms_key" "snapsheet_production_rds_encryption_key" {
  key_id = "arn:aws:kms:us-east-2:667656038718:key/b52431bf-bc02-4943-beff-72403c047b41"
}

import {
  to = aws_db_instance.snapsheet_production_rds
  id = "snapsheet-production"
}

resource "aws_db_instance" "snapsheet_production_rds" {
  identifier                    = "snapsheet-production"
  db_subnet_group_name          = aws_db_subnet_group.snapsheet_default_subnet_group.name
  engine                        = "postgres"
  engine_version                = "17.5"
  instance_class                = "db.t3.small"
  parameter_group_name          = data.aws_db_parameter_group.snapsheet_dwh.name
  db_name                       = "snapsheet_production"
  kms_key_id                    = data.aws_kms_key.snapsheet_production_rds_encryption_key.arn

  username                      = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_production_rds_db_password.secret_string)["username"]
  password                      = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_production_rds_db_password.secret_string)["password"]

  allocated_storage             = 20
  max_allocated_storage         = 40

  iops                          = 3000
  storage_encrypted             = true
  storage_type                  = "gp3"
  storage_throughput            = 125

  apply_immediately             = false
  copy_tags_to_snapshot         = true
  deletion_protection           = true
  publicly_accessible           = true
  skip_final_snapshot           = false

  enabled_cloudwatch_logs_exports = [
    "postgresql",
    "upgrade"
  ]
  tags = {
    ManagedBy = "InsuredEng",
    ENV       = "production"
  }
  vpc_security_group_ids = [
    aws_security_group.snapsheet_default_sg.id,
    data.aws_security_group.github_allowlist_sg.id,
  ]
}

data "aws_secretsmanager_secret" "snapsheet_production_rds_db_password" {
  name = "claims/snapsheet/db-credentials-production"
}

data "aws_secretsmanager_secret_version" "snapsheet_production_rds_db_password" {
  secret_id = data.aws_secretsmanager_secret.snapsheet_production_rds_db_password.id
}

import {
  to = aws_route53_record.snapsheet_production_db_cname
  id = "Z04984401BYE18UCI6HZ1_snapsheet-production-db.nirvanatech.com_CNAME"
}

resource "aws_route53_record" "snapsheet_production_db_cname" {
  zone_id = "Z04984401BYE18UCI6HZ1"
  name    = "snapsheet-production-db.nirvanatech.com"
  type    = "CNAME"
  ttl     = 300
  records = [aws_db_instance.snapsheet_production_rds.address]
}
