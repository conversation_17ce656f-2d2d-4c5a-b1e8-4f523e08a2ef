/*
Sandbox already has a manually created role called "dms_user".
*/

module "snapsheet_production_rds_role" {
  source = "./postgres"

  host     = aws_db_instance.snapsheet_production_rds.address
  username = aws_db_instance.snapsheet_production_rds.username
  password = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_production_rds_db_password.secret_string)["password"]
  database = "snapsheet_production"

  create_snapsheet_role     = true
  snapsheet_role_name       = "snapsheet_production_user"
  snapsheet_role_password   = jsondecode(data.aws_secretsmanager_secret_version.snapsheet_dbroles_credentials.secret_string)["snapsheet_production"]
}
