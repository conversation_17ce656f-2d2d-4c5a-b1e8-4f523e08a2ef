variable "host" {
  description = "PostgreSQL server hostname"
  type        = string
}

variable "port" {
  description = "PostgreSQL server port"
  type        = number
  default     = 5432
}

variable "database" {
  description = "PostgreSQL database to connect to"
  type        = string
  default     = "postgres"
}

variable "username" {
  description = "PostgreSQL username"
  type        = string
}

variable "password" {
  description = "PostgreSQL password"
  type        = string
  sensitive   = true
}

variable "sslmode" {
  description = "PostgreSQL SSL mode"
  type        = string
  default     = "require"
}

variable "superuser" {
  description = "Whether the user is a superuser"
  type        = bool
  default     = false
}

variable "create_dbt_role" {
  description = "Whether to create a dbt role"
  type        = bool
  default     = false
}

variable "dbt_role_name" {
  description = "Name of the dbt role"
  type        = string
  default     = "dbt_user"
}

variable "dbt_role_password" {
  description = "Password for the dbt role"
  type        = string
  default     = null
  sensitive   = true
}

variable "dbt_source_schemas" {
  description = "List of source schemas that dbt should read from"
  type = list(object({
    name     = string
  }))
  default = []
}

variable "create_snapsheet_role" {
  description = "Whether to create a snapsheet role"
  type        = bool
  default     = false
}

variable "snapsheet_role_name" {
  description = "Name of the snapsheet role"
  type        = string
  default     = "snapsheet_user"
}

variable "snapsheet_role_password" {
  description = "Password for the snapsheet role"
  type        = string
  default     = null
  sensitive   = true
}
