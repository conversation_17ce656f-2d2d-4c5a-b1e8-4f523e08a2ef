resource "postgresql_role" "dbt_role" {
  count               = var.create_dbt_role ? 1 : 0
  name                = var.dbt_role_name
  login               = true
  password            = var.dbt_role_password
  encrypted_password  = true
  inherit             = true
  create_database     = false
  create_role         = false
  replication         = false
}

resource "postgresql_grant" "dbt_database_schema_creation" {
  count       = var.create_dbt_role ? 1 : 0
  database    = var.database
  role        = postgresql_role.dbt_role[0].name
  object_type = "database"
  privileges  = [
    "CREATE"
  ]
  depends_on = [postgresql_role.dbt_role]
}

resource "postgresql_grant" "dbt_schema_privileges" {
  count       = var.create_dbt_role ? length(var.dbt_source_schemas) : 0
  database    = var.database
  role        = postgresql_role.dbt_role[0].name
  schema      = var.dbt_source_schemas[count.index].name
  object_type = "schema"
  privileges  = [
    "USAGE",
    "CREATE",
  ]
  depends_on = [postgresql_role.dbt_role]
}

resource "postgresql_grant" "dbt_table_privileges" {
  count       = var.create_dbt_role ? length(var.dbt_source_schemas) : 0
  database    = var.database
  role        = postgresql_role.dbt_role[0].name
  schema      = var.dbt_source_schemas[count.index].name
  object_type = "table"
  privileges  = [
    "SELECT",
  ]
  depends_on = [postgresql_role.dbt_role]
}

resource "postgresql_default_privileges" "dbt_default_table_privileges" {
  count       = var.create_dbt_role ? length(var.dbt_source_schemas) : 0
  database    = var.database
  role        = postgresql_role.dbt_role[0].name
  schema      = var.dbt_source_schemas[count.index].name
  owner       = var.username
  object_type = "table"
  privileges  = [
    "SELECT",
  ]
  depends_on = [postgresql_role.dbt_role]
}
