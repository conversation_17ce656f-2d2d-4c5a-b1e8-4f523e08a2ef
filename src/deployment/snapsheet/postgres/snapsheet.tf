resource "postgresql_role" "snapsheet_role" {
  count               = var.create_snapsheet_role ? 1 : 0
  name                = var.snapsheet_role_name
  login               = true
  password            = var.snapsheet_role_password
  encrypted_password  = true
  inherit             = true
  create_database     = false
  create_role         = false
  replication         = false
}

resource "postgresql_grant" "snapsheet_database_schema_creation" {
  count       = var.create_snapsheet_role ? 1 : 0
  database    = var.database
  role        = postgresql_role.snapsheet_role[0].name
  object_type = "database"
  privileges  = [
    "CREATE"
  ]
  depends_on = [postgresql_role.snapsheet_role]
}

resource "postgresql_grant" "snapsheet_public_schema_privileges" {
  count       = var.create_snapsheet_role ? 1 : 0
  database    = var.database
  role        = postgresql_role.snapsheet_role[0].name
  schema      = "public"
  object_type = "schema"
  privileges  = [
    "USAGE",
    "CREATE",
  ]
  depends_on = [postgresql_role.snapsheet_role]
}

resource "postgresql_grant" "snapsheet_public_table_privileges" {
  count       = var.create_snapsheet_role ? 1 : 0
  database    = var.database
  role        = postgresql_role.snapsheet_role[0].name
  schema      = "public"
  object_type = "table"
  privileges  = [
    "SELECT",
    "INSERT",
    "UPDATE",
    "DELETE",
    "TRUNCATE"
  ]
  depends_on = [postgresql_role.snapsheet_role]
}

resource "postgresql_default_privileges" "snapsheet_public_default_table_privileges" {
  count       = var.create_snapsheet_role ? 1 : 0
  database    = var.database
  role        = postgresql_role.snapsheet_role[0].name
  schema      = "public"
  owner       = var.username
  object_type = "table"
  privileges  = [
    "SELECT",
    "INSERT",
    "UPDATE",
    "DELETE",
    "TRUNCATE"
  ]
  depends_on = [postgresql_role.snapsheet_role]
}
