import {
  to = aws_vpc.snapsheet_vpc
  id = "vpc-018080a49c231a339"
}

resource "aws_vpc" "snapsheet_vpc" {
  cidr_block       = "172.30.0.0/16"

  tags = {
    Name = "snapsheet-vpc",
    ManagedBy = "InsuredEng"
  }
}

import {
  id = "igw-0da72be4c3e88f7e2"
  to = aws_internet_gateway.snapsheet_igw
}

resource "aws_internet_gateway" "snapsheet_igw" {
  vpc_id = aws_vpc.snapsheet_vpc.id

  tags = {
    Name = "snapsheet-igw"
    ManagedBy = "InsuredEng"
  }
}

import {
  to = aws_route_table.snapsheet_public_rt
  id = "rtb-09fb8d58c3bd7e87d"
}

resource "aws_route_table" "snapsheet_public_rt" {
  vpc_id = aws_vpc.snapsheet_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.snapsheet_igw.id
  }

  tags = {
    Name = "snapsheet-public-rt"
    ManagedBy = "InsuredEng"
  }
}

resource "aws_route_table_association" "snapsheet_public_subnet_a_association" {
  subnet_id      = aws_subnet.snapsheet_public_subnet_a.id
  route_table_id = aws_route_table.snapsheet_public_rt.id
}

resource "aws_route_table_association" "snapsheet_public_subnet_b_association" {
  subnet_id      = aws_subnet.snapsheet_public_subnet_b.id
  route_table_id = aws_route_table.snapsheet_public_rt.id
}

resource "aws_route_table_association" "snapsheet_public_subnet_c_association" {
  subnet_id      = aws_subnet.snapsheet_public_subnet_c.id
  route_table_id = aws_route_table.snapsheet_public_rt.id
}

import {
  to = aws_subnet.snapsheet_public_subnet_a
  id = "subnet-0d60343ac60849f22"
}

resource "aws_subnet" "snapsheet_public_subnet_a" {
  vpc_id     = aws_vpc.snapsheet_vpc.id
  cidr_block = "172.30.0.0/24"

  tags = {
    Name = "snapsheet-public-sn-a"
    ManagedBy = "InsuredEng"
  }
}

import {
  to = aws_subnet.snapsheet_public_subnet_b
  id = "subnet-0047eb97284e7cac2"
}

resource "aws_subnet" "snapsheet_public_subnet_b" {
  vpc_id     = aws_vpc.snapsheet_vpc.id
  cidr_block = "172.30.1.0/24"

  tags = {
    Name = "snapsheet-public-sn-b"
    ManagedBy = "InsuredEng"
  }
}

import {
  to = aws_subnet.snapsheet_public_subnet_c
  id = "subnet-0cd2799eb21595e59"
}

resource "aws_subnet" "snapsheet_public_subnet_c" {
  vpc_id     = aws_vpc.snapsheet_vpc.id
  cidr_block = "172.30.2.0/24"

  tags = {
    Name = "snapsheet-public-sn-c"
    ManagedBy = "InsuredEng"
  }
}

import {
  to = aws_security_group.snapsheet_default_sg
  id = "sg-0efa9eaa1cb83443c"
}

locals {
  postgres_access = {
    "Airbyte Cloud"   = [
      "34.106.60.246/32",
      "34.106.115.240/32",
      "13.37.142.60/32",
      "34.106.225.141/32",
      "34.33.7.0/29",
      "34.106.229.69/32",
      "34.106.218.58/32",
      "35.181.124.238/32",
      "**************/32",
      "**************/32",
      "**************/32",
      "**********/32"
    ],
    "DBT Cloud"       = [
      "***********/32",
      "************/32",
      "*************/32",
      "*************/32",
      "*************/32",
      "*************/32"
    ],
    "Snapsheet"       = ["*************/32"],
  }

  vpc_access = {
    "Cowork"          = ["*************/32"],
  }
}

resource "aws_security_group" "snapsheet_default_sg" {
  name = "default"
  description = "default VPC security group"

  vpc_id = aws_vpc.snapsheet_vpc.id
  dynamic "ingress" {
    for_each = local.postgres_access

    content {
      from_port   = 5432
      to_port     = 5432
      protocol    = "tcp"
      cidr_blocks = ingress.value
      description = ingress.key
    }
  }

  dynamic "ingress" {
    for_each = local.vpc_access

    content {
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      cidr_blocks = ingress.value
      description = ingress.key
    }
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "snapsheet-sg"
    ManagedBy = "InsuredEng"
  }
}
