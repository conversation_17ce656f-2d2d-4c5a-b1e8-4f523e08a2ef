terraform {
  required_version = "1.7.5"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.41.0"
    }
  }

  backend "s3" {
    bucket = "cloud.nirvanatech.com"
    key    = "private/deployment/terraform/websites/state"
    region = "us-east-2"
  }
}

# Configure the AWS Provider
provider "aws" {
  region = "us-east-2"
}

# Custom provider for ACM certificates because for Cloudfront to work,
# certificates need to be in us-east-1 region.
provider "aws" {
  alias  = "virginia"
  region = "us-east-1"
}
