# AWS Region
data "aws_route53_zone" "main" {
  name         = "nirvanatech.com"
  private_zone = false
}

data "aws_route53_zone" "prod" {
  name         = "prod.nirvanatech.com"
  private_zone = false
}

data "aws_route53_zone" "dev" {
  name         = "dev.nirvanatech.com"
  private_zone = false
}

# SSL cert
data "aws_acm_certificate" "prod_cert" {
  domain      = "prod.nirvanatech.com"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

data "aws_acm_certificate" "dev_cert" {
  provider    = aws.virginia
  domain      = "dev.nirvanatech.com"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

data "aws_acm_certificate" "dev_cert_ohio" {
  domain      = "dev.nirvanatech.com"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

data "aws_acm_certificate" "main_cert_virginia" {
  provider    = aws.virginia
  domain      = "nirvanatech.com"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}
