module "graphiql_client" {
  source = "../common-modules/static_website"

  website_domain      = "graphiql.nirvanatech.com"
  route53_zone_id     = data.aws_route53_zone.main.zone_id
  acm_certificate_arn = data.aws_acm_certificate.main_cert_virginia.arn
  index_doc_path      = "index.html"
  error_doc_path      = "index.html"
  tags = {
    Environment = "prod"
  }
}

module "agent_website" {
  source = "../common-modules/static_website"

  website_domain      = "agents.nirvanatech.com"
  route53_zone_id     = data.aws_route53_zone.main.zone_id
  acm_certificate_arn = data.aws_acm_certificate.main_cert_virginia.arn
  index_doc_path      = "index.html"
  error_doc_path      = "index.html"
  tags = {
    Environment = "prod"
  }
}

# Deprecated module.
# TODO: Remove this.
module "fleet_safety_app" {
  source              = "../common-modules/static_website"
  create_dns_entries  = false
  website_domain      = "safety.nirvanatech.com"
  route53_zone_id     = data.aws_route53_zone.main.zone_id
  acm_certificate_arn = data.aws_acm_certificate.main_cert_virginia.arn
  index_doc_path      = "index.html"
  error_doc_path      = "index.html"
  tags = {
    Environment = "prod"
  }
}


# Safety app Firebase IP
resource "aws_route53_record" "fleet_safety_app" {
  zone_id = data.aws_route53_zone.main.zone_id
  name    = "safety.nirvanatech.com"
  type    = "A"
  ttl     = 300
  records = ["**************"]
}

# Deploy Storybook using Firebase IP
resource "aws_route53_record" "storybook" {
  zone_id = data.aws_route53_zone.main.zone_id
  name    = "storybook.nirvanatech.com"
  type    = "A"
  ttl     = 300
  records = ["**************"]
}


module "swagger_ui" {
  source = "../common-modules/static_website"

  website_domain      = "swagger.dev.nirvanatech.com"
  route53_zone_id     = data.aws_route53_zone.dev.zone_id
  acm_certificate_arn = data.aws_acm_certificate.dev_cert.arn
  index_doc_path      = "index.html"
  error_doc_path      = "index.html"
  tags = {
    Environment = "prod"
  }
}
