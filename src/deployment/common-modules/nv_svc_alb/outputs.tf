#------------------------------------------------------------------------------
# APPLICATION LOAD BALANCER
#------------------------------------------------------------------------------
output "arn" {
  description = "The ARN of the load balancer"
  value       = aws_alb.lb.arn
}

output "arn_suffix" {
  description = "The ARN suffix for use with CloudWatch Metrics."
  value       = aws_alb.lb.arn_suffix
}

output "dns_name" {
  description = "The DNS name of the load balancer."
  value       = aws_alb.lb.dns_name
}

output "zone_id" {
  description = "The canonical hosted zone ID of the load balancer (to be used in a Route 53 Alias record)."
  value       = aws_alb.lb.zone_id
}

#------------------------------------------------------------------------------
# ACCESS CONTROL TO APPLICATION LOAD BALANCER
#------------------------------------------------------------------------------
output "access_sg_id" {
  description = "The ID of the security group"
  value       = aws_security_group.lb_access_sg.id
}

output "access_sg_arn" {
  description = "The ARN of the security group"
  value       = aws_security_group.lb_access_sg.arn
}

#------------------------------------------------------------------------------
# AWS LOAD BALANCER - Target Groups
#------------------------------------------------------------------------------
output "http_tgs_arns" {
  description = "List of HTTP Target Groups ARNs"
  value       = [for tg in aws_lb_target_group.lb_http_tgs : tg.arn]
}

output "http_tgs_ports" {
  description = "List of HTTP Target Groups ports"
  value       = [for tg in aws_lb_target_group.lb_http_tgs : tostring(tg.port)]
}

#------------------------------------------------------------------------------
# AWS LOAD BALANCER - Listeners
#------------------------------------------------------------------------------
output "http_listeners_arns" {
  description = "List of HTTP Listeners ARNs"
  value       = [for listener in aws_lb_listener.lb_http_listeners : listener.arn]
}

output "https_listeners_arns" {
  description = "List of HTTPS Listeners ARNs"
  value       = [for listener in aws_lb_listener.lb_https_listeners : listener.arn]
}
