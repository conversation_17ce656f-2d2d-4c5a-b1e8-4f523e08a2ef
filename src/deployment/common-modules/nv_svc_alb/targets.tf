#------------------------------------------------------------------------------
# AWS LOAD BALANCER - Target Groups
#------------------------------------------------------------------------------
resource "aws_lb_target_group" "lb_http_tgs" {
  for_each = var.http_ports

  name                          = "${var.name_prefix}-http-${each.value.listener_port}"
  port                          = each.value.listener_port
  protocol                      = "HTTP"
  vpc_id                        = var.vpc_id
  slow_start                    = var.slow_start
  load_balancing_algorithm_type = var.load_balancing_algorithm_type
  deregistration_delay          = var.target_group_deregistration_delay

  health_check {
    enabled             = var.target_group_health_check_enabled
    matcher             = var.target_group_health_check_matcher
    interval            = var.target_group_health_check_interval
    path                = var.target_group_health_check_path
    timeout             = var.target_group_health_check_timeout
    healthy_threshold   = var.target_group_health_check_healthy_threshold
    unhealthy_threshold = var.target_group_health_check_unhealthy_threshold
  }
  target_type = "ip"
  tags        = var.tags
  depends_on  = [aws_alb.lb]

  stickiness {
    enabled = var.stickiness_enabled
    type = var.stickiness_type
    cookie_duration = var.stickiness_cookie_duration
    cookie_name = var.stickiness_cookie_name
  }
}
