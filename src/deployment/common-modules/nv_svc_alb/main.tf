#------------------------------------------------------------------------------
# APPLICATION LOAD BALANCER
#------------------------------------------------------------------------------
resource "aws_alb" "lb" {
  name               = "${var.name_prefix}-lb"
  load_balancer_type = "application"
  subnets            = var.public_subnets

  security_groups = compact(
    concat(var.security_groups, [aws_security_group.lb_access_sg.id]),
  )

  access_logs {
    bucket  = aws_s3_bucket.logs.id
    enabled = true
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-lb"
    },
  )
}
