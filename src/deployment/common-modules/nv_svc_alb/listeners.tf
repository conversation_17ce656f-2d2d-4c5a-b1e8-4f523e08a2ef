#------------------------------------------------------------------------------
# AWS LOAD BALANCER - Listeners
#------------------------------------------------------------------------------
resource "aws_lb_listener" "lb_http_listeners" {
  for_each          = var.http_ports
  load_balancer_arn = aws_alb.lb.arn
  port              = each.value.listener_port
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      host        = lookup(each.value, "host", "#{host}")
      path        = lookup(each.value, "path", "/#{path}")
      port        = lookup(each.value, "target_port", "#{port}")
      protocol    = lookup(each.value, "protocol", "#{protocol}")
      query       = lookup(each.value, "query", "#{query}")
      status_code = lookup(each.value, "status_code", "HTTP_301")
    }
  }
}

resource "aws_lb_listener" "lb_https_listeners" {
  for_each          = var.https_ports
  load_balancer_arn = aws_alb.lb.arn
  port              = each.value.listener_port
  protocol          = "HTTPS"
  ssl_policy        = var.ssl_policy
  certificate_arn   = var.default_certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.lb_http_tgs[each.key].arn
  }

}
