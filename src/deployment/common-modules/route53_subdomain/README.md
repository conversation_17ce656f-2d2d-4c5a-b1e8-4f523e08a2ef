<!-- BEGIN_TF_DOCS -->




## Outputs

| Name | Description |
|------|-------------|
| <a name="output_zone_id"></a> [zone\_id](#output\_zone\_id) | ID of the Route53 Zone |
| <a name="output_zone_name"></a> [zone\_name](#output\_zone\_name) | n/a |

## Modules

No modules.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_domain_name"></a> [domain\_name](#input\_domain\_name) | The subdomain to be created | `string` | n/a | yes |
| <a name="input_parent_zone_id"></a> [parent\_zone\_id](#input\_parent\_zone\_id) | Route53 Zone ID for the parent zone for this subdomain | `string` | n/a | yes |

## Resources

| Name | Type |
|------|------|
| [aws_route53_record.this_ns](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_zone.this_route53_zone](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_zone) | resource |


<!-- END_TF_DOCS -->