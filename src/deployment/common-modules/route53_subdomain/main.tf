resource "aws_route53_zone" "this_route53_zone" {
  name = var.domain_name

  tags = {
    Name = var.domain_name
  }
}

resource "aws_route53_record" "this_ns" {
  zone_id = var.parent_zone_id
  name    = var.domain_name
  type    = "NS"
  ttl     = "30"
  records = aws_route53_zone.this_route53_zone.name_servers
}

resource "aws_route53_record" "this_caa" {
  count = var.create_caa_record ? 1 : 0
  zone_id = aws_route53_zone.this_route53_zone.zone_id
  name    = var.domain_name
  type    = "CAA"
  ttl     = var.caa_record_ttl
  records = var.caa_record_value
}
