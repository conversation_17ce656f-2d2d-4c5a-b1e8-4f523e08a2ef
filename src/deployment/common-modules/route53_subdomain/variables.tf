variable "domain_name" {
    description = "The subdomain to be created"
    type = string
}

variable "parent_zone_id" {
    description = "Route53 Zone ID for the parent zone for this subdomain"
    type = string
}

variable "create_caa_record" {
    description = "Flag to create CAA record for the subdomain"
    type        = bool
    default     = false
}

variable "caa_record_value" {
    description = "The value for the CAA record"
    type        = list(string)
    default     = ["0 issue \"amazon.com\"", "0 iodef \"mailto:<EMAIL>\""]
}

variable "caa_record_ttl" {
    description = "The TTL for the CAA record"
    type        = number
    default     = 1800
}
