locals {
  redirection_resource_count = var.website_domain_redirect == "" ? 0 : 1
}

# Creates bucket for the website handling the redirection (if required), e.g. from https://www.example.com to https://example.com
resource "aws_s3_bucket" "website_redirect" {
  count = local.redirection_resource_count

  bucket        = "${var.website_domain}-redirect"
  force_destroy = true

  tags = var.tags
}

resource "aws_s3_bucket_acl" "website_redirect" {
  count = local.redirection_resource_count
  bucket = aws_s3_bucket.website_redirect[0].id
  acl    = "public-read"
}

resource "aws_s3_bucket_website_configuration" "website_redirect" {
  count = local.redirection_resource_count
  bucket = aws_s3_bucket.website_redirect[0].id
  redirect_all_requests_to {
    host_name = var.website_domain
    protocol = "https"
  }
}

# Creates the CloudFront distribution to serve the redirection website (if redirection is required)
resource "aws_cloudfront_distribution" "website_cdn_redirect" {
  count = local.redirection_resource_count

  enabled     = true
  price_class = "PriceClass_All"
  aliases = [var.website_domain_redirect]

  origin {
    origin_id   = "origin-bucket-${aws_s3_bucket.website_redirect[0].id}"
    domain_name = aws_s3_bucket_website_configuration.website_redirect[0].website_endpoint

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "http-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2"]
    }
  }

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT", "DELETE"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "origin-bucket-${aws_s3_bucket.website_redirect[0].id}"
    min_ttl          = "0"
    default_ttl      = "300"
    max_ttl          = "1200"

    viewer_protocol_policy = "redirect-to-https" # Redirects any HTTP request to HTTPS
    compress               = true

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn = var.acm_certificate_arn
    ssl_support_method  = "sni-only"
  }

  custom_error_response {
    error_caching_min_ttl = 300
    error_code            = 404
    response_page_path    = "/${var.error_doc_path}"
    response_code         = 404
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [
      viewer_certificate,
    ]
  }
}

# Creates the DNS record to point on the CloudFront distribution ID that handles the redirection website
resource "aws_route53_record" "website_cdn_redirect_record" {
  count = var.create_dns_entries ? local.redirection_resource_count : 0

  zone_id = var.route53_zone_id
  name    = var.website_domain_redirect
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.website_cdn_redirect[0].domain_name
    zone_id                = aws_cloudfront_distribution.website_cdn_redirect[0].hosted_zone_id
    evaluate_target_health = false
  }
}

