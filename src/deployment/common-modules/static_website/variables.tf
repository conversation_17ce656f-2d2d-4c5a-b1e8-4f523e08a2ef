variable "create_dns_entries" {
  description = "Temporary variable to handle website migration from AWS hosted to Vercel"
  type = bool
  default = true
}

variable "no_aliases" {
  description = "If true, the CloudFront distribution will not have an alias record"
  type = bool
  default = false
}

variable "website_domain" {
  description = "The website domain, e.g. frontend.dev.nirvanatech.com"
  type        = string
}

variable "website_domain_redirect" {
  description = <<EOF
  An optional redirection domain,
  eg: www.nirvanatech.com, when website_domain is nirvanatech.com.
  EOF
  type = string
  default = ""
}

variable "route53_zone_id" {
    description = "Route53 Zone ID"
    type = string
}

# This certificate HAS TO BE in us-east-1 (Virginia region)
variable "acm_certificate_arn" {
    description = "ACM Certificate ARN for CDN"
    type = string
}

variable "index_doc_path" {
  description = "Path to index document"
  type = string
  default = "index.html"
}

variable "error_doc_path" {
  description = "Path to error document"
  type = string
  default = "404.html"
}

variable "tags" {
  description = "Tags added to resources"
  default     = {}
  type        = map(string)
}