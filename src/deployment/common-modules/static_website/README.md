# `static_website` module

This module deploys a static website hosted on S3, and adds the corresponding
DNS record to the hosted zone provided.

<!-- BEGIN_TF_DOCS -->




## Outputs

| Name | Description |
|------|-------------|
| <a name="output_website_cdn_id"></a> [website\_cdn\_id](#output\_website\_cdn\_id) | CloudFront Distribution ID |
| <a name="output_website_s3_bucket"></a> [website\_s3\_bucket](#output\_website\_s3\_bucket) | The website root bucket where resources are uploaded |

## Modules

No modules.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_acm_certificate_arn"></a> [acm\_certificate\_arn](#input\_acm\_certificate\_arn) | ACM Certificate ARN for CDN | `string` | n/a | yes |
| <a name="input_route53_zone_id"></a> [route53\_zone\_id](#input\_route53\_zone\_id) | Route53 Zone ID | `string` | n/a | yes |
| <a name="input_website_domain"></a> [website\_domain](#input\_website\_domain) | The website domain, e.g. frontend.dev.nirvanatech.com | `string` | n/a | yes |
| <a name="input_create_dns_entries"></a> [create\_dns\_entries](#input\_create\_dns\_entries) | Temporary variable to handle website migration from AWS hosted to Vercel | `bool` | `true` | no |
| <a name="input_error_doc_path"></a> [error\_doc\_path](#input\_error\_doc\_path) | Path to error document | `string` | `"404.html"` | no |
| <a name="input_index_doc_path"></a> [index\_doc\_path](#input\_index\_doc\_path) | Path to index document | `string` | `"index.html"` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags added to resources | `map(string)` | `{}` | no |
| <a name="input_website_domain_redirect"></a> [website\_domain\_redirect](#input\_website\_domain\_redirect) | An optional redirection domain,<br>  eg: www.nirvanatech.com, when website\_domain is nirvanatech.com. | `string` | `""` | no |

## Resources

| Name | Type |
|------|------|
| [aws_cloudfront_distribution.website_cdn_redirect](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_distribution) | resource |
| [aws_cloudfront_distribution.website_cdn_root](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudfront_distribution) | resource |
| [aws_route53_record.website_cdn_redirect_record](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.website_cdn_root_record](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_s3_bucket.website_redirect](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket) | resource |
| [aws_s3_bucket.website_root](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket) | resource |
| [aws_s3_bucket_policy.update_website_root_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy) | resource |


<!-- END_TF_DOCS -->
