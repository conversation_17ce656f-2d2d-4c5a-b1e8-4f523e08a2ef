module "otel_sidecar_definition" {
  source                  = "./otel_sidecar"
  logstream_identifier    = var.container_name
  metrics_namespace       = var.metrics_namespace != null ? var.metrics_namespace : var.container_name
  sidecar_container_image = var.sidecar_container_image
}

module fluentbit_sidecar_definition {
  source                  = "./fluentbit_sidecar"
  cloudwatch_log_group    = var.cloudwatch_log_group
  cloudwatch_log_stream   = var.cloudwatch_log_stream
  logstream_identifier    = var.container_name
}

# Task Definition
resource "aws_ecs_task_definition" "td" {
  family                   = var.family
  container_definitions    = local.json_map
  task_role_arn            = local.task_role_arn
  execution_role_arn       = var.task_execution_role_arn
  network_mode             = "awsvpc"
  cpu                      = var.task_cpu
  memory                   = var.task_memory
  requires_compatibilities = ["FARGATE"]

  // This volume is needed to store the fluentbit config file. 
  // init-config container prepares the config file and stores it in this volume.
  // fluentbit container reads the config file from this volume.
  // Check fluentbit_sidecar/main.tf for container definitions.
  volume {
    name = "config"
  }

  tags = var.tags
}

data "aws_iam_policy_document" "ecs_task_common_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "logs:PutLogEvents",
      "logs:CreateLogStream",
      "logs:CreateLogGroup"
    ]
    resources = [
      "arn:aws:logs:*:*:*"
    ]
  }
}

resource "aws_iam_policy" "ecs_task_common_policy" {
  policy = data.aws_iam_policy_document.ecs_task_common_policy_doc.json
}

resource "aws_iam_role_policy_attachment" "ecsTaskRole_commonPolicy" {
  role       = local.task_role_name
  policy_arn = aws_iam_policy.ecs_task_common_policy.arn
}