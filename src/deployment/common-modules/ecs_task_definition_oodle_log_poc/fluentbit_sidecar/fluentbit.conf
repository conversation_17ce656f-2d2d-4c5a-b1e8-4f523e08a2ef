[SERVICE]
    Parsers_File /fluent-bit/parsers/parsers.conf
    Flush        1
    Grace        30

[INPUT]
    name            fluentbit_metrics
    tag             internal_metrics
    scrape_interval 2

[FILTER]
    name                  multiline
    match                 *
    multiline.key_content log
    mode                  partial_message

[FILTER]
    Name         parser
    Match        *
    Key_Name     log
    Parser       json
    Reserve_Data True

[OUTPUT]
    Name        cloudwatch_logs
    Match       *
    region      ${AWS_REGION}
    log_group_name ${CLOUDWATCH_LOG_GROUP}
    log_stream_name ${CLOUDWATCH_LOG_STREAM}/${ECS_TASK_ID}
    auto_create_group true
    retry_limit 2

[OUTPUT]
    Name        http
    Match       *
    Host        ${OODLE_ENDPOINT}
    Port        443
    URI         /ingest/v1/logs
    Header      X-OODLE-INSTANCE ${OODLE_INSTANCE}
    Header      X-API-KEY ${OODLE_API_KEY}
    Format      json
    Compress    gzip
    Json_date_key    timestamp
    Json_date_format iso8601
    TLS         On
    retry_limit 3

[OUTPUT]
    Name                 prometheus_remote_write
    Match                internal_metrics
    Host                 ${OODLE_METRICS_HOST}
    Port                 443
    Uri                  /v1/prometheus/${OODLE_INSTANCE}/write
    Header               X-API-KEY ${OODLE_API_KEY}
    Log_response_payload True
    Tls                  On
    add_label            log_group ${CLOUDWATCH_LOG_GROUP}
    add_label            log_stream ${CLOUDWATCH_LOG_STREAM}
    add_label            task_id ${ECS_TASK_ID}