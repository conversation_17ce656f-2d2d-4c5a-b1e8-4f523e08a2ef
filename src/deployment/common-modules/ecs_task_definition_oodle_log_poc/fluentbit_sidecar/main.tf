variable "logstream_identifier" {
  description = "string identifier to identify the log stream containing logs from this container"
  type        = string
  default     = null
}

variable "cloudwatch_log_group" {
  description = "CloudWatch log group name for the main container"
  type        = string
}

variable "cloudwatch_log_stream" {
  description = "CloudWatch log stream name for the main container"
  type        = string
}

locals {
  fluentd_cpu    = 128
  fluentd_memory = 256
  config_init_memory = 64

  // config-init container prepares and mounts the config file for fluentbit container
  // TODO:(garvit): This container can be avoided by storing the config file in S3 
  // and passing the S3 URL to fluentbit container, but storing in S3 has the risk of 
  // config getting changed / deleted inadvertently. So for POC keeping the init container,
  // but in production we can see if we should use S3 or not.
  fluentd_config_init_container_def = {
    name = "config-init",
    image = "public.ecr.aws/docker/library/alpine:3.21.3",
    essential = false,
    memoryReservation = local.config_init_memory, 
    cpu = 0,
    command = [
      "sh", 
      "-c", 
      "echo '${base64encode(file("${path.module}/fluentbit.conf"))}' | base64 -d > /oodle/fluent-bit.conf"
    ],
    mountPoints = [
      {
        "sourceVolume": "config",
        "containerPath": "/oodle",
        "readOnly": false
      }
    ],
    // Due to some unknown reason AWS automatically adds user=0 in container definition, and terraform
    // tries to remove it everytime we run plan/apply resulting in perpetual diff. So, we are adding it here.
    user = "0"
    logConfiguration = {
      logDriver = "awslogs",
      options = {
        "awslogs-group"           = "${terraform.workspace}-fluentbit-sidecar-logs"
        "awslogs-region"          = "us-east-2",
        "awslogs-stream-prefix"   = var.logstream_identifier != null ? "awslogs-fluentbit-sidecar-config-init/${var.logstream_identifier}" : "awslogs-fluentbit-sidecar-config-init"
      }
    }
  }

  fluentd_sidecar_container_def = {
    name = "fluent-bit",
    image = "public.ecr.aws/aws-observability/aws-for-fluent-bit:init-2.32.5.20250305",
    essential = true,
    memory = local.fluentd_memory,
    cpu = local.fluentd_cpu,
    environment = [
      {
        "name": "OODLE_INSTANCE",
        "value": "inst_nirvana_ACFWu5"
      },
      {
        "name": "OODLE_API_KEY",
        "value": data.aws_secretsmanager_secret_version.oodle_secret.secret_string
      },
      {
        "name": "OODLE_ENDPOINT",
        "value": "nirvana.logscollector.oodle.ai"
      },
      {
        name  = "OODLE_METRICS_HOST"
        value = "nirvana.collector.oodle.ai"
      },
      {
        "name": "CLOUDWATCH_LOG_GROUP",
        "value": var.cloudwatch_log_group
      },
      {
        "name": "CLOUDWATCH_LOG_STREAM",
        "value": var.cloudwatch_log_stream
      }
    ],
    mountPoints = [
      {
        "sourceVolume": "config",
        "containerPath": "/oodle",
        "readOnly": true
      }
    ],
    dependsOn = [
      {
        "containerName": "config-init",
        "condition": "COMPLETE"
      }
    ],
    firelensConfiguration = {
      type = "fluentbit",
      options = {
        "config-file-type": "file",
        "config-file-value": "/oodle/fluent-bit.conf"
      }
    },
    // Due to some unknown reason AWS automatically adds user=0 in container definition, and terraform
    // tries to remove it everytime we run plan/apply resulting in perpetual diff. So, we are adding it here.
    user = "0"
    logConfiguration = {
      logDriver = "awslogs",
      options = {
        "awslogs-group"           = "${terraform.workspace}-fluentbit-sidecar-logs"
        "awslogs-region"          = "us-east-2",
        "awslogs-stream-prefix"   = var.logstream_identifier != null ? "awslogs-fluentbit-sidecar/${var.logstream_identifier}" : "awslogs-fluentbit-sidecar"
      }
    }
  }
}

output "config_init_container_def_json" {
  description = "JSON representation of the config init container definition"
  value       = jsonencode(local.fluentd_config_init_container_def)
}

output "container_definition_json" {
  description = "JSON representation of the container definition"
  value       = jsonencode(local.fluentd_sidecar_container_def)
}

output "cpu" {
  value = local.fluentd_cpu
}

output "memory" {
  value = local.fluentd_memory
}

output "config_init_memory" {
  value = local.config_init_memory
}
