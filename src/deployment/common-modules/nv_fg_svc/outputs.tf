#------------------------------------------------------------------------------
# AWS ECS SERVICE
#------------------------------------------------------------------------------
output "arn" {
  description = "The Amazon Resource Name (ARN) that identifies the service."
  value       = aws_ecs_service.service.id
}

output "name" {
  description = "The name of the service."
  value       = aws_ecs_service.service.name
}

output "cluster" {
  description = "The Amazon Resource Name (ARN) of cluster which the service runs on."
  value       = aws_ecs_service.service.cluster
}


#------------------------------------------------------------------------------
# Service Discovery
#------------------------------------------------------------------------------

output "discovery_service_name" {
  description = "The name of the `aws_service_discovery_service`"
  value       = aws_service_discovery_service.service.name
}
