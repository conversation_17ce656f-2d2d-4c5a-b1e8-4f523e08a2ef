#------------------------------------------------------------------------------
# Misc
#------------------------------------------------------------------------------
variable "name_prefix" {
  description = "Name prefix for resources on AWS"
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "Resource tags"
}

#------------------------------------------------------------------------------
# AWS Networking
#------------------------------------------------------------------------------
variable "vpc_id" {
  description = "ID of the VPC"
}

#------------------------------------------------------------------------------
# AWS ECS SERVICE
#------------------------------------------------------------------------------
variable "ecs_cluster_arn" {
  description = "ARN of an ECS cluster"
}

variable "ecs_cluster_name" {
  description = "Name of an ECS cluster"
}

variable "deployment_maximum_percent" {
  description = "(Optional) The upper limit (as a percentage of the service's desiredCount) of the number of running tasks that can be running in a service during a deployment."
  type        = number
  default     = 200
}

variable "deployment_minimum_healthy_percent" {
  description = "(Optional) The lower limit (as a percentage of the service's desiredCount) of the number of running tasks that must remain running and healthy in a service during a deployment."
  type        = number
  default     = 100
}

variable "desired_count" {
  description = "(Optional) The number of instances of the task definition to place and keep running. Defaults to 0."
  type        = number
  default     = 1
}

variable "task_definition_arn" {
  description = "(Required) The full ARN of the task definition that you want to run in your service."
}

variable "force_new_deployment" {
  description = "(Optional) Enable to force a new task deployment of the service. This can be used to update tasks to use a newer Docker image with same image/tag combination (e.g. myimage:latest), roll Fargate tasks onto a newer platform version, or immediately deploy ordered_placement_strategy and placement_constraints updates."
  type        = bool
  default     = false
}

variable "service_discovery_namespace_id" {
  description = "The service discovery namespace id to create the service_registry. We don't strictly need this for some of our services, but we do it for future proofing. It doesn't hurt to have DNS names for our services."
  type        = string
}

#------------------------------------------------------------------------------
# AWS ECS SERVICE network_configuration BLOCK
#------------------------------------------------------------------------------
variable "private_subnets" {
  description = "The private subnets associated with the task or service."
  type        = list(string)
}

variable "public_subnets" {
  description = "(Optional)The public subnets associated with the task or service. Required if lb_internal is true."
  type        = list(string)
  default     = []
}

variable "security_groups" {
  description = "(Optional) The security groups associated with the task or service."
  type        = list(string)
  default     = []
}

#------------------------------------------------------------------------------
# AWS ECS SERVICE load_balancer BLOCK
#------------------------------------------------------------------------------
variable "container_name" {
  description = "Name of the running container. Required if create_alb is true."
  type        = string
  default     = ""
}

variable "container_port" {
  description = "Port of the running container. Required if create_alb is true."
  type        = number
  default     = 0
}

variable "create_lb_access_sg_rule" {
  description = "(Optional) Create a security group rule for the load balancer. Default true."
  type        = bool
  default     = false
}

variable "lb_access_sg" {
  description = "(Optional) The security group ID to allow access to the load balancer. Required if create_lb_access_sg_rule is true."
  type        = string
  default     = ""
}

variable "lb_http_tg_arns" {
  description = "List of HTTP Target Groups ARNs"
  type        = list(string)
  default     = []
}

variable "lb_http_tg_ports" {
  description = "List of HTTP Target Groups ports"
  type        = list(string)
  default     = []
}

#------------------------------------------------------------------------------
# AWS CloudWatch
#------------------------------------------------------------------------------
variable "cloudwatch_alarms_config" {
  description = "Cloud Watch Alarms configuration. This is a map of string: {key: value}"

  type = map(object({
    threshold                 = number
    team_sns_arn              = string
    alarm_period              = optional(number, 60)
    alarm_evaluation_periods  = optional(number, 5)
    alarm_datapoints_to_alarm = optional(number)
  }))
  default = {
    "memory_alarm" = null
    "cpu_alarm"    = null
  }
}
