#------------------------------------------------------------------------------
# AWS ECS SERVICE
#------------------------------------------------------------------------------
resource "aws_ecs_service" "service" {
  name            = "${var.name_prefix}-service"
  cluster         = var.ecs_cluster_arn
  task_definition = var.task_definition_arn

  deployment_maximum_percent         = var.deployment_maximum_percent
  deployment_minimum_healthy_percent = var.deployment_minimum_healthy_percent
  desired_count                      = var.desired_count

  launch_type          = "FARGATE"
  force_new_deployment = var.force_new_deployment

  dynamic "load_balancer" {
    for_each = var.lb_http_tg_arns
    content {
      target_group_arn = load_balancer.value
      container_name   = var.container_name
      container_port   = var.container_port
    }
  }

  network_configuration {
    security_groups  = concat(local.lb_sg_ids, var.security_groups)
    subnets          = var.private_subnets
    assign_public_ip = false
  }

  service_registries {
    registry_arn = aws_service_discovery_service.service.arn
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-ecs-tasks-sg"
    },
  )
  propagate_tags = "TASK_DEFINITION"

  lifecycle {
    # ignore drift when only the desired count of service needs changing
    # this is to prevent spurious drifts in case of auto-scaling services.
    ignore_changes = [
      desired_count,
      deployment_minimum_healthy_percent,
      deployment_maximum_percent,
    ]
  }

}

resource "aws_service_discovery_service" "service" {
  name = var.name_prefix
  dns_config {
    namespace_id = var.service_discovery_namespace_id
    dns_records {
      ttl  = 10
      type = "A"
    }
    routing_policy = "MULTIVALUE"
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

#------------------------------------------------------------------------------
# AWS SECURITY GROUP - ECS Tasks, allow traffic only from Load Balancer
#------------------------------------------------------------------------------
resource "aws_security_group" "ecs_tasks_sg" {
  count = var.create_lb_access_sg_rule ? 1 : 0

  name        = "${var.name_prefix}-ecs-tasks-sg"
  description = "Allow inbound access from the LB only"
  vpc_id      = var.vpc_id

  egress {
    from_port   = 0             # Allowing any incoming port
    to_port     = 0             # Allowing any outgoing port
    protocol    = "-1"          # Allowing any outgoing protocol 
    cidr_blocks = ["0.0.0.0/0"] # Allowing traffic out to all IP addresses
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-ecs-tasks-sg"
    },
  )
}

resource "aws_security_group_rule" "ingress" {
  for_each                 = toset(var.lb_http_tg_ports)
  security_group_id        = aws_security_group.ecs_tasks_sg[0].id
  type                     = "ingress"
  from_port                = each.value
  to_port                  = each.value
  protocol                 = "tcp"
  source_security_group_id = var.lb_access_sg
}

locals {
  lb_sg_ids = [for sg in aws_security_group.ecs_tasks_sg : sg.id]
}

#------------------------------------------------------------------------------
# AWS CloudWatch ALARMS
#------------------------------------------------------------------------------
resource "aws_cloudwatch_metric_alarm" "cpu_alarm" {
  count               = var.cloudwatch_alarms_config.cpu_alarm != null ? 1 : 0
  alarm_name          = "${var.name_prefix}-cpu"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.cloudwatch_alarms_config.cpu_alarm.alarm_evaluation_periods
  datapoints_to_alarm = var.cloudwatch_alarms_config.cpu_alarm.alarm_datapoints_to_alarm
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = var.cloudwatch_alarms_config.cpu_alarm.alarm_period
  statistic           = "Average"
  threshold           = var.cloudwatch_alarms_config.cpu_alarm.threshold
  alarm_description   = "Alerts that the ${var.name_prefix} CPU utilization has passed the defined threshold"
  dimensions = {
    ClusterName = var.ecs_cluster_name
    ServiceName = "${var.name_prefix}-service"
  }
  alarm_actions = [var.cloudwatch_alarms_config.cpu_alarm.team_sns_arn]
  ok_actions    = [var.cloudwatch_alarms_config.cpu_alarm.team_sns_arn]
}

resource "aws_cloudwatch_metric_alarm" "memory_alarm" {
  count               = var.cloudwatch_alarms_config.memory_alarm != null ? 1 : 0
  alarm_name          = "${var.name_prefix}-memory"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.cloudwatch_alarms_config.memory_alarm.alarm_evaluation_periods
  datapoints_to_alarm = var.cloudwatch_alarms_config.memory_alarm.alarm_datapoints_to_alarm
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = var.cloudwatch_alarms_config.memory_alarm.alarm_period
  statistic           = "Average"
  threshold           = var.cloudwatch_alarms_config.memory_alarm.threshold
  alarm_description   = "Alerts that the ${var.name_prefix} memory utilization has passed the defined threshold"
  dimensions = {
    ClusterName = var.ecs_cluster_name
    ServiceName = "${var.name_prefix}-service"
  }

  alarm_actions = [var.cloudwatch_alarms_config.memory_alarm.team_sns_arn]
  ok_actions    = [var.cloudwatch_alarms_config.memory_alarm.team_sns_arn]
}
