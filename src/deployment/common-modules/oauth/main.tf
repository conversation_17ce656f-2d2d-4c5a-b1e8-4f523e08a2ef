locals {
  name = "${trimprefix(lower(var.tsp), "tsp")}-oauth"
}

module "td_oauth_server" {
  source = "../ecs_task_definition"

  family           = "${local.name}-td"
  container_name   = local.name
  container_image  = "${var.env_config.ecr_repo_url}:${var.env_config.ecr_tag}"
  task_memory = var.env_config.memory
  task_cpu    = var.env_config.cpu
  command          = [var.tsp]

  essential = true

  port_mappings = [
    {
      containerPort = var.env_config.service_port
      hostPort      = var.env_config.service_port
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = var.env_config.pprof_port
      hostPort      = var.env_config.pprof_port
      protocol      = "tcp"
    }
  ]
  map_environment = var.env_config.environment_vars

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = var.env_config.log_group_name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-oauth-server"
    }
  }
  task_role_arn           = var.env_config.task_role_arn
  task_execution_role_arn = var.env_config.task_execution_role_arn

  tags = {
    Environment = var.env_config.environment
    Application = "oauth_server"
  }
}

# Create ECS Fargate service
module "fg_service_oauth_server" {
  source = "../nv_fg_svc"

  name_prefix = local.name

  vpc_id           = var.env_config.vpc_id
  ecs_cluster_arn  = var.env_config.ecs_cluster_arn
  ecs_cluster_name = var.env_config.ecs_cluster_name

  task_definition_arn = module.td_oauth_server.arn

  private_subnets = var.env_config.private_subnets
  security_groups = var.env_config.security_groups

  service_discovery_namespace_id = var.env_config.service_discovery_namespace_id

  # Optional tags
  tags = {
    Environment = var.env_config.environment
    Application = "oauth_server"
  }
}
