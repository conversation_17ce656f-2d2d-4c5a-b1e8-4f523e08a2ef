variable "tsp" {
  type        = string
  description = "TSP name for the OAuth service (example: TSPSamsara)"
}

variable "env_config" {
  type = object({
    ecr_repo_url                   = string,
    ecr_tag                        = string,
    memory                         = number,
    cpu                            = number,
    service_port                   = number,
    pprof_port                     = number,
    environment_vars               = map(string),
    log_group_name                 = string,
    task_role_arn                  = string,
    task_execution_role_arn        = string,
    environment                    = string,
    vpc_id                         = string,
    ecs_cluster_arn                = string,
    ecs_cluster_name               = string,
    private_subnets                = list(string),
    security_groups                = list(string)
    service_discovery_namespace_id = string,
  })
  description = "Config shared amongst all oauth servers"
}
