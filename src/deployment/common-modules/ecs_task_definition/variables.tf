#------------------------------------------------------------------------------
# Misc
#------------------------------------------------------------------------------
variable "tags" {
  type        = map(string)
  default     = {}
  description = "Resource tags"
}

#------------------------------------------------------------------------------
# AWS ECS Task Definition Variables
#------------------------------------------------------------------------------
variable "family" {
  description = "A unique name for the task definition"
  type        = string
}

variable "task_execution_role_arn" {
  description = "The ARN of the task execution role that the Amazon ECS container agent and the Docker daemon can assume."
  type        = string
}

variable "task_role_arn" {
  description = "(Optional) The ARN of IAM role that allows your Amazon ECS container task to make calls to other AWS services. If not specified, `var.task_execution_role_arn` is used"
  type        = string
  default     = null
}

#------------------------------------------------------------------------------
# AWS ECS Container Definition Variables
#------------------------------------------------------------------------------
variable "container_name" {
  type        = string
  description = "The name of the container. Up to 255 characters ([a-z], [A-Z], [0-9], -, _ allowed)"
}

variable "container_image" {
  type        = string
  description = "The image used to start the container. Images in the Docker Hub registry available by default"
}

variable "task_memory" {
  type        = number
  description = "The amount of memory (in MiB) to allow the task to use. This is a hard limit, if the containers within a task attempts to exceed the task_memory, the container is killed. This field is optional for Fargate launch type and the total amount of container_memory of all containers in a task will need to be lower than the task memory value"
  default     = null
}

variable "container_memory_reservation" {
  type        = number
  description = <<EOF
  The amount of memory (in MiB) to reserve for the container. If container needs to exceed this
  threshold, it can do so up to the set container_memory hard limit
  EOF
  default     = null
}

variable "port_mappings" {
  type = list(object({
    containerPort = number
    hostPort      = number
    protocol      = string
  }))

  description = <<EOF
  The port mappings to configure for the container. This is a list of maps. Each map should contain
  "containerPort", "hostPort", and "protocol", where "protocol" is one of "tcp" or "udp".
  If using containers in a task with the awsvpc or host network mode, the hostPort can either be
  left blank or set to the same value as the containerPort.
  EOF

  default = []
}

variable "task_cpu" {
  type        = number
  description = "The number of cpu units to reserve for the task. The containers within the task share these units. This is optional for tasks using Fargate launch type and the total amount of container_cpu of all containers in a task will need to be lower than the task-level cpu value"
  default     = 0
}

variable "essential" {
  type        = bool
  description = "Determines whether all other containers in a task are stopped, if this container fails or stops for any reason. Due to how Terraform type casts booleans in json it is required to double quote this value"
  default     = true
}

variable "entrypoint" {
  type        = list(string)
  description = "The entry point that is passed to the container"
  default     = null
}

variable "command" {
  type        = list(string)
  description = "The command that is passed to the container"
  default     = null
}

variable "working_directory" {
  type        = string
  description = "The working directory to run commands inside the container"
  default     = null
}

variable "map_environment" {
  type        = map(string)
  description = "The environment variables to pass to the container. This is a map of string: {key: value}"
  default     = null
}

variable "map_secrets" {
  type        = map(string)
  description = "The secrets variables to pass to the container. This is a map of string: {key: value}. map_secrets overrides secrets"
  default     = null
}

# https://docs.aws.amazon.com/AmazonECS/latest/APIReference/API_LogConfiguration.html
variable "log_configuration" {
  type        = any
  description = "Log configuration options to send to a custom log driver for the container. For more details, see https://docs.aws.amazon.com/AmazonECS/latest/APIReference/API_LogConfiguration.html"
  default     = null
}

variable "user" {
  type        = string
  description = "The user to run as inside the container. Can be any of these formats: user, user:group, uid, uid:gid, user:gid, uid:group. The default (null) will use the container's configured `USER` directive or root if not set."
  default     = null
}

variable "container_ulimits" {
  type = list(object({
    name      = string
    hardLimit = number
    softLimit = number
  }))
  description = "Container ulimit settings. This is a list of maps, where each map should contain \"name\", \"hardLimit\" and \"softLimit\""
  default     = null
}

variable "healthcheck" {
  type = object({
    command     = list(string)
    interval    = number
    retries     = number
    startPeriod = number
    timeout     = number
  })
  description = "Container healthcheck configuration. This is a map of string: {key: value}"
  default = null
}

variable "enable_otel_sidecar" {
  description = "Flag to enable the Otel sidecar container"
  type        = bool
  default     = false
}

variable "metrics_namespace" {
  description = "Namespace for the sidecar metrics. If not provided, container_name will be used as the default value."
  type        = string
  default     = null
}

variable "sidecar_container_image" {
  description = "The image to use for the container"
  type        = string
  default     = "otel/opentelemetry-collector-contrib:0.117.0"
}
