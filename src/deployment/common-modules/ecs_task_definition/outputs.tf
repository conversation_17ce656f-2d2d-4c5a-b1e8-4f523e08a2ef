#------------------------------------------------------------------------------
# ECS Task Definition
#------------------------------------------------------------------------------
output "arn" {
  description = "Full ARN of the Task Definition (including both family and revision)."
  value       = aws_ecs_task_definition.td.arn
}

output "family" {
  description = "The family of the Task Definition."
  value       = aws_ecs_task_definition.td.family
}

output "revision" {
  description = "The revision of the task in a particular family."
  value       = aws_ecs_task_definition.td.revision
}

output "countainer_definition_json" {
  description = "JSON representation of the container definition, useful for debugging"
  value = local.json_map
}