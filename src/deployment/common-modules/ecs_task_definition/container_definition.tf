locals {
  # Sort environment variables so terraform will not try to recreate on each plan/apply
  env_vars_keys        = var.map_environment != null ? keys(var.map_environment) : []
  env_vars_values      = var.map_environment != null ? values(var.map_environment) : []
  env_vars_as_map      = zipmap(local.env_vars_keys, local.env_vars_values)
  sorted_env_vars_keys = sort(local.env_vars_keys)

  sorted_environment_vars = [
    for key in local.sorted_env_vars_keys :
    {
      name  = key
      value = lookup(local.env_vars_as_map, key)
    }
  ]

  # Sort secrets so terraform will not try to recreate on each plan/apply
  secrets_keys        = var.map_secrets != null ? keys(var.map_secrets) : []
  secrets_values      = var.map_secrets != null ? values(var.map_secrets) : []
  secrets_as_map      = zipmap(local.secrets_keys, local.secrets_values)
  sorted_secrets_keys = sort(local.secrets_keys)

  sorted_secrets_vars = [
    for key in local.sorted_secrets_keys :
    {
      name      = key
      valueFrom = lookup(local.secrets_as_map, key)
    }
  ]

  # https://www.terraform.io/docs/configuration/expressions.html#null
  final_environment_vars = length(local.sorted_environment_vars) > 0 ? local.sorted_environment_vars : null
  final_secrets_vars     = length(local.sorted_secrets_vars) > 0 ? local.sorted_secrets_vars : null

  log_configuration_secret_options = var.log_configuration != null ? lookup(var.log_configuration, "secretOptions", null) : null
  log_configuration_with_null = var.log_configuration == null ? null : {
    logDriver = tostring(lookup(var.log_configuration, "logDriver"))
    options   = tomap(lookup(var.log_configuration, "options"))
    secretOptions = local.log_configuration_secret_options == null ? null : [
      for secret_option in tolist(local.log_configuration_secret_options) : {
        name      = tostring(lookup(secret_option, "name"))
        valueFrom = tostring(lookup(secret_option, "valueFrom"))
      }
    ]
  }
  log_configuration_without_null = local.log_configuration_with_null == null ? null : {
    for k, v in local.log_configuration_with_null :
    k => v
    if v != null
  }

  primary_container_cpu = (
    var.enable_otel_sidecar ?
    var.task_cpu - module.otel_sidecar_definition.cpu :
    var.task_cpu
  )
  primary_container_memory = (
    var.enable_otel_sidecar ?
    var.task_memory - module.otel_sidecar_definition.memory :
    var.task_memory
  )

  container_definition = {
    name              = var.container_name
    image             = var.container_image
    essential         = var.essential
    entryPoint        = var.entrypoint
    command           = var.command
    workingDirectory  = var.working_directory
    user              = var.user
    portMappings      = var.port_mappings
    logConfiguration  = local.log_configuration_without_null
    memory            = local.primary_container_memory
    memoryReservation = var.container_memory_reservation
    cpu               = local.primary_container_cpu
    environment       = local.final_environment_vars
    secrets           = local.final_secrets_vars
    ulimits           = var.container_ulimits
    healthcheck       = var.healthcheck
  }

  container_definition_without_null = {
    for k, v in local.container_definition :
    k => v
    if v != null
  }

  # Include sidecar if enable_sidecar is true
  container_definitions_with_otel = [
    local.container_definition_without_null,
    var.enable_otel_sidecar ? jsondecode(module.otel_sidecar_definition.container_definition_json) : null
  ]

  filtered_container_definitions_with_otel = [for def in local.container_definitions_with_otel : def if def != null]

  json_map = jsonencode(local.filtered_container_definitions_with_otel)
}
