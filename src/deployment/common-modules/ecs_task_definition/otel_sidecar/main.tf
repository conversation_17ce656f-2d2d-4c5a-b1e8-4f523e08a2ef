variable "metrics_namespace" {
  description = "Namespace for the sidecar metrics."
  type        = string
  nullable    = false
}

variable "logstream_identifier" {
  description = "string identifier to identify the log stream containing logs from this container"
  type        = string
  default     = null
}

variable "resource_reservation" {
  description = "tune whether the container should reserve resources"
  type        = bool
  default     = true
}

variable "sidecar_container_image" {
  description = "The image to use for the container"
  type        = string
  default     = "otel/opentelemetry-collector-contrib:0.117.0"
}

locals {
  otel_cpu    = 256
  otel_memory = 512

  # otel sidecar container based on contrib collector - https://github.com/open-telemetry/opentelemetry-collector-contrib
  otel_sidecar_container_definition = {
    name      = "otel_server"
    image     = var.sidecar_container_image
    cpu       = var.resource_reservation ? local.otel_cpu : null
    memory    = var.resource_reservation ? local.otel_memory : null
    essential = true
    environment = [
      { "name" : "ENV", "value" : "prod" },
      { "name" : "OTEL_COL_CONFIG", "value" : "${file("${path.module}/sidecar_otel_config.yaml")}" },
      { "name" : "METRICS_NAMESPACE", "value" : var.metrics_namespace },
    ]
    command = ["--config=env:OTEL_COL_CONFIG"]
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        "awslogs-group"         = "${terraform.workspace}-otel-server-logs"
        "awslogs-region"        = "us-east-2"
        "awslogs-stream-prefix" = var.logstream_identifier != null ? "awslogs-otel-server/${var.logstream_identifier}" : "awslogs-otel-server"
      }
    }
  }
}

output "container_definition_json" {
  description = "JSON representation of the container definition"
  value       = jsonencode(local.otel_sidecar_container_definition)
}

output "cpu" {
  value = local.otel_cpu
}

output "memory" {
  value = local.otel_memory
}
