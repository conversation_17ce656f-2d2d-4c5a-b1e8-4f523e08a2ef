extensions:
  health_check:

receivers:
  awsecscontainermetrics:
    collection_interval: 30s

processors:
  batch/metrics:
    timeout: 60s
  filter:
    metrics:
      include:
        match_type: strict
        metric_names:
          - container.memory.reserved
          - container.memory.utilized
          - container.cpu.reserved
          - container.cpu.utilized # percentage
          - container.cpu.usage.total
          - container.cpu.usage.vcpu
          - container.network.rate.rx
          - container.network.rate.tx
          - container.storage.read_bytes
          - container.storage.write_bytes
  metricstransform:
    transforms:
      # container.cpu.utilized is cpu utilization percentage metric, however
      # due to a certain bug in awsecscontainermetrics this value is reported
      # divided by 1000.
      - include: container.cpu.utilized
        action: update
        operations:
          - action: experimental_scale_value
            experimental_scale: 1000
      - include: ^(.*)$$
        match_type: regexp
        action: update
        new_name: ${env:METRICS_NAMESPACE}.$${1}
  
exporters:
  otlp:
    endpoint: otel-server.default.app.nirvana.internal:4317
    tls:
      insecure: true
      insecure_skip_verify: true

service:
  pipelines:
    metrics/centralotel:
      receivers: [awsecscontainermetrics]
      processors: [batch/metrics, filter, metricstransform]
      exporters: [otlp]

  extensions: [health_check]
