module "otel_sidecar_definition" {
  source                  = "./otel_sidecar"
  logstream_identifier    = var.container_name
  metrics_namespace       = var.metrics_namespace != null ? var.metrics_namespace : var.container_name
  sidecar_container_image = var.sidecar_container_image
}

# Task Definition
resource "aws_ecs_task_definition" "td" {
  family                   = var.family
  container_definitions    = local.json_map
  task_role_arn            = var.task_role_arn == null ? var.task_execution_role_arn : var.task_role_arn
  execution_role_arn       = var.task_execution_role_arn
  network_mode             = "awsvpc"
  cpu                      = var.task_cpu
  memory                   = var.task_memory
  requires_compatibilities = ["FARGATE"]

  tags = var.tags
}
