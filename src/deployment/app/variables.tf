variable "api_server_tag" {
  type        = string
  description = "The tag of the api_server image to be used to deploy"
}

variable "mvr_cache_server_tag" {
  type        = string
  description = "The tag of the mvr_cache_server image to be used to deploy"
}

variable "pdfgen_server_tag" {
  type        = string
  description = "The tag of the pdfgen image to be used to deploy"
}

variable "oauth_server_tag" {
  type        = string
  description = "The tag of the oauth image to be used to deploy"
}

variable "graphql_server_tag" {
  type        = string
  description = "The tag of the graphql image to be used to deploy"
}

variable "llmops_server_tag" {
  type        = string
  description = "The tag of the llmops image to be used to deploy"
}

variable "data_infra_job_processor_tag" {
  type        = string
  description = "The tag of the data_infra job processor image to be used to deploy"
}

variable "telematics_grpc_server_tag" {
  type        = string
  description = "The tag of the telematics grpc server image to be used to deploy"
}

variable "vehicles_grpc_server_tag" {
  type        = string
  description = "The tag of the vehicle service grpc server image to be used to deploy"
}
variable "quote_scraper_grpc_server_tag" {
  type        = string
  description = "The tag of the quote scraper grpc server image to be used to deploy"
}

variable "feature_store_server_tag" {
  type        = string
  description = "The tag of the feature store grpc server image to be used to deploy"
}

variable "distsem_server_tag" {
  type        = string
  description = "The tag of the distsem_server image to be used to deploy"
}

variable "db_migrate_lambda_tag" {
  type        = string
  description = "The tag of the db_migrate_lambda image to be used to deploy"
}

variable "fmcsa_scraper_tag" {
  type        = string
  description = "The tag of the fmcsa ani scraper image to be used to deploy"
}

variable "jobber_monitor_tag" {
  type        = string
  description = "The tag of the jobber_monitor image to be used to deploy"
}

variable "grpc_lambda_tag" {
  type        = string
  description = "The tag of the grpc_lambda image to be used to deploy"
}

variable "saferwatch_scraper_tag" {
  type        = string
  description = "The tag of the saferwatch_scraper image to be used to deploy"
}

variable "fmcsa_data_provider_grpc_server_tag" {
  type        = string
  description = "The tag of the fmcsa data provider grpc service server image to be used to deploy"
}

variable "mcp_experiments_rest_server_tag" {
  type        = string
  description = "Tag for the mcp experiments rest server image"
}
