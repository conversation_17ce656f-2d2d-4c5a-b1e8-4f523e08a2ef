# to store samsara data
# deprecated
# TODO: remove this
resource "aws_s3_bucket" "telematics_samsara" {
  bucket = "nirvana-telematics-samsara"
  tags = {
    bucket = "nirvana-telematics-samsara"
  }
}

resource "aws_s3_bucket_acl" "telematics_samsara" {
  bucket = aws_s3_bucket.telematics_samsara.id
  acl    = "private"
}

# to store raw telematics data
resource "aws_s3_bucket" "telematics" {
  bucket = "nirvana-telematics-${terraform.workspace}"
  tags = {
    bucket = "nirvana-telematics-${terraform.workspace}"
  }
}

resource "aws_s3_bucket_acl" "telematics" {
  bucket = aws_s3_bucket.telematics.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "telematics" {
  bucket = aws_s3_bucket.telematics.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_notification" "telematics_eventbridge" {
  bucket      = aws_s3_bucket.telematics.id
  eventbridge = true
}

resource "aws_s3_bucket" "inventory" {
  bucket = "nirvana-inventory-files"
  tags = {
    bucket = "nirvana-inventory-files"
  }
}

resource "aws_s3_bucket_policy" "inventory" {
  bucket = aws_s3_bucket.inventory.id
  policy = data.aws_iam_policy_document.allow_s3_inventory_put_access.json
}

data "aws_iam_policy_document" "allow_s3_inventory_put_access" {
  statement {
    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }
    actions = [
      "s3:PutObject"
    ]
    resources = [
      "${aws_s3_bucket.inventory.arn}/*"
    ]
  }
}

# enable s3 storage inventory for the telematics bucket
# https://docs.aws.amazon.com/AmazonS3/latest/userguide/storage-inventory.html
resource "aws_s3_bucket_inventory" "telematics" {
  bucket                   = aws_s3_bucket.telematics.id
  name                     = "AllObjectsDaily"
  included_object_versions = "All"
  schedule {
    frequency = "Daily"
  }
  destination {
    bucket {
      bucket_arn = aws_s3_bucket.inventory.arn
      format     = "Parquet"
    }
  }
  optional_fields = [
    "Size", "LastModifiedDate", "StorageClass",
    "IntelligentTieringAccessTier", "ETag"
  ]
}

# enable another inventory for just the normalised files
resource "aws_s3_bucket_inventory" "telematics_normalised" {
  bucket                   = aws_s3_bucket.telematics.id
  name                     = "NormalisedFilesOnlyDaily"
  included_object_versions = "All"
  filter {
    prefix = "Nirvana/"
  }
  schedule {
    frequency = "Daily"
  }
  destination {
    bucket {
      bucket_arn = aws_s3_bucket.inventory.arn
      format     = "Parquet"
    }
  }
  optional_fields = [
    "Size", "LastModifiedDate", "StorageClass",
    "IntelligentTieringAccessTier", "ETag"
  ]
}

# keep telematics inventory for last ~7~ 30 days only
resource "aws_s3_bucket_lifecycle_configuration" "telematics" {
  bucket = aws_s3_bucket.inventory.id
  rule {
    id     = "${aws_s3_bucket.telematics.id}-expiry"
    status = "Enabled"
    expiration {
      days = 7
    }
    filter {
      prefix = "${aws_s3_bucket.telematics.id}/"
    }
  }
}

# to store scraping data
resource "aws_s3_bucket" "quote_scraper" {
  bucket = "nirvana-quote-scraper"
  tags = {
    bucket = "nirvana-quote-scraper"
  }
}

# to store nextraq data
resource "aws_s3_bucket" "telematics_nextraq_anonymized" {
  bucket = "nirvana-telematics-nextraq-anonymized"
}

resource "aws_s3_bucket_acl" "telematics_nextraq_anonymized" {
  bucket = aws_s3_bucket.telematics_nextraq_anonymized.id
  acl    = "private"
}


# to store underwriting app data
resource "aws_s3_bucket" "underwriting" {
  bucket = "nirvana-underwriting"
  tags = {
    bucket = "nirvana-underwriting"
  }
}

resource "aws_s3_bucket_acl" "underwriting" {
  bucket = aws_s3_bucket.underwriting.id
  acl    = "private"
}


# to store generated forms
resource "aws_s3_bucket" "forms" {
  bucket = "nirvana-forms"
  tags = {
    bucket = "nirvana-forms"
  }
}

resource "aws_s3_bucket_accelerate_configuration" "forms_acceleration" {
  bucket = aws_s3_bucket.forms.id
  status = "Enabled"
}

resource "aws_s3_bucket_acl" "forms" {
  bucket = aws_s3_bucket.forms.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "forms" {
  bucket = aws_s3_bucket.forms.id
  versioning_configuration {
    status = "Enabled"
  }
}

# CORS config for the forms bucket (e.g. policy & endorsement docs).
resource "aws_s3_bucket_cors_configuration" "forms_cors" {
  bucket = aws_s3_bucket.forms.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD"]
    allowed_origins = [
      "http://localhost:1120",
      "http://localhost:3000",
      "https://*.nirvanatech.com"
    ]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

# to store raw telematics data [dev]
resource "aws_s3_bucket" "telematics_dev" {
  bucket = "nirvana-telematics-dev"
  tags = {
    bucket = "nirvana-telematics-dev"
  }
}

resource "aws_s3_bucket_acl" "telematics_dev" {
  bucket = aws_s3_bucket.telematics_dev.id
  acl    = "private"
}

# to store cache files for boards app [dev]
resource "aws_s3_bucket" "boards_data_dev" {
  bucket = "nirvana-boards-data-dev"
}

resource "aws_s3_bucket_acl" "boards_data_dev" {
  bucket = aws_s3_bucket.boards_data_dev.id
  acl    = "private"
}

# to store `blobs`
resource "aws_s3_bucket" "blob_store" {
  bucket = "nirvana-blob-store"
  tags = {
    bucket = "nirvana-blob-store"
  }
}

resource "aws_s3_bucket_acl" "blob_store" {
  bucket = aws_s3_bucket.blob_store.id
  acl    = "private"
}


resource "aws_s3_bucket_versioning" "blob_store" {
  bucket = aws_s3_bucket.blob_store.id
  versioning_configuration {
    status = "Enabled"
  }
}

# to store ML features in their **raw** form. We create a bucket
# each for prod & dev.
resource "aws_s3_bucket" "ml_features_raw_prod" {
  bucket = "nirvana-ml-features-raw-prod"
  tags = {
    bucket = "nirvana-ml-features-raw-prod"
  }
}

resource "aws_s3_bucket_ownership_controls" "ml_features_raw_prod" {
  bucket = aws_s3_bucket.ml_features_raw_prod.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "ml_features_raw_prod" {
  depends_on = [aws_s3_bucket_ownership_controls.ml_features_raw_prod]

  bucket = aws_s3_bucket.ml_features_raw_prod.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "ml_features_raw_prod" {
  bucket = aws_s3_bucket.ml_features_raw_prod.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket" "ml_features_raw_dev" {
  bucket = "nirvana-ml-features-raw-dev"
  tags = {
    bucket = "nirvana-ml-features-raw-dev"
  }
}

resource "aws_s3_bucket_ownership_controls" "ml_features_raw_dev" {
  bucket = aws_s3_bucket.ml_features_raw_dev.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "ml_features_raw_dev" {
  depends_on = [aws_s3_bucket_ownership_controls.ml_features_raw_dev]

  bucket = aws_s3_bucket.ml_features_raw_dev.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "ml_features_raw_dev" {
  bucket = aws_s3_bucket.ml_features_raw_dev.id
  versioning_configuration {
    status = "Enabled"
  }
}

# to store billing artifacts
resource "aws_s3_bucket" "billing" {
  bucket = "nirvana-billing"
  tags = {
    bucket = "nirvana-billing"
  }
}

resource "aws_s3_bucket_ownership_controls" "billing" {
  bucket = aws_s3_bucket.billing.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "billing" {
  depends_on = [aws_s3_bucket_ownership_controls.billing]

  bucket = aws_s3_bucket.billing.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "billing" {
  bucket = aws_s3_bucket.billing.id
  versioning_configuration {
    status = "Enabled"
  }
}

# to store files from data.gov site (Ex-LNI site)
resource "aws_s3_bucket" "datagov" {
  bucket = "nirvana-datagov"
  tags = {
    bucket = "nirvana-datagov"
  }
}

resource "aws_s3_bucket_ownership_controls" "datagov" {
  bucket = aws_s3_bucket.datagov.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "datagov" {
  depends_on = [aws_s3_bucket_ownership_controls.datagov]

  bucket = aws_s3_bucket.datagov.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "datagov" {
  bucket = aws_s3_bucket.datagov.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Bucket will be used by the new data context components as a "store".
resource "aws_s3_bucket" "data_context_store" {
  bucket = "nirvana-data-context-store"
  tags = {
    bucket = "nirvana-data-context-store"
  }
}

resource "aws_s3_bucket_ownership_controls" "data_context_store" {
  bucket = aws_s3_bucket.data_context_store.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "data_context_store" {
  depends_on = [aws_s3_bucket_ownership_controls.data_context_store]

  bucket = aws_s3_bucket.data_context_store.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "data_context_store" {
  bucket = aws_s3_bucket.data_context_store.id
  versioning_configuration {
    status = "Enabled"
  }
}

# to store API Integration artifacts
resource "aws_s3_bucket" "api_artifacts" {
  bucket = "nirvana-api-artifacts-store"
  tags = {
    bucket = "nirvana-api-artifacts-store"
  }
}

resource "aws_s3_bucket_versioning" "api_artifacts" {
  bucket = aws_s3_bucket.api_artifacts.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "telematics-intelligent-tiering" {
  bucket = aws_s3_bucket.telematics.id
  rule {
    id = "${aws_s3_bucket.telematics.id}-intelligent_tiering"
    filter {
    }
    transition {
      days          = 0
      storage_class = "INTELLIGENT_TIERING"
    }
    status = "Enabled"
  }

}

# to store Pibit loss runs inputs & outputs
resource "aws_s3_bucket" "pibit_bucket" {
  bucket = "nirvana-pibit-loss-runs"
  tags = {
    bucket = "nirvana-pibit-loss-runs"
  }
}

resource "aws_s3_bucket_policy" "pibit_bucket_policy" {
  bucket = aws_s3_bucket.pibit_bucket.id
  policy = data.aws_iam_policy_document.allow_pibit_access_to_our_bucket.json
}

data "aws_iam_policy_document" "allow_pibit_access_to_our_bucket" {
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::748276930737:user/nirvana_s3_bucket_user"] # provided by pibit team to us
    }

    # read access to both paths
    actions = [
      "s3:GetObject",
    ]

    resources = [
      "${aws_s3_bucket.pibit_bucket.arn}/raw_files/*",
      "${aws_s3_bucket.pibit_bucket.arn}/processed_files/*",
    ]
  }
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::748276930737:user/nirvana_s3_bucket_user"] # provided by pibit team to us
    }

    # list access for both processed_files/ and raw_files/
    actions = [
      "s3:ListBucket",
    ]

    resources = [
      aws_s3_bucket.pibit_bucket.arn,
    ]

    condition {
      test     = "StringLike"
      variable = "s3:prefix"
      values = [
        "raw_files/*",
        "processed_files/*"
      ]
    }
  }
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::748276930737:user/nirvana_s3_bucket_user"] # provided by pibit team to us
    }

    # put access to only processed_files/
    actions = [
      "s3:PutObject",
    ]

    resources = [
      "${aws_s3_bucket.pibit_bucket.arn}/processed_files/*",
    ]
  }
}

resource "aws_s3_bucket" "pricing_simulations" {
  bucket = "nirvana-pricing-simulations"
  tags = {
    bucket = "nirvana-pricing-simulations"
  }
}

resource "aws_s3_bucket_ownership_controls" "pricing_simulations" {
  bucket = aws_s3_bucket.pricing_simulations.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "pricing_simulations" {
  depends_on = [aws_s3_bucket_ownership_controls.pricing_simulations]

  bucket = aws_s3_bucket.pricing_simulations.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "pricing_simulations" {
  bucket = aws_s3_bucket.pricing_simulations.id
  versioning_configuration {
    status = "Enabled"
  }
}

data "aws_s3_bucket" "cost_and_usage_reports" {
  bucket = "nirvana-cost-and-usage-reports"
}

resource "aws_s3_bucket_notification" "cost_and_usage_reports_eventbridge" {
  bucket      = data.aws_s3_bucket.cost_and_usage_reports.id
  eventbridge = true
}

resource "aws_s3_bucket" "telematics_snowflake_stage" {
  bucket = "nirvana-telematics-snowflake-stage"
  tags = {
    bucket = "nirvana-telematics-snowflake-stage"
  }
}

resource "aws_s3_bucket_notification" "telematics_snowflake_stage_eventbridge" {
  bucket      = aws_s3_bucket.telematics_snowflake_stage.id
  eventbridge = true
}

resource "aws_s3_bucket_accelerate_configuration" "pdfgen_acceleration" {
  bucket = data.aws_s3_bucket.pdfgen.id
  status = "Enabled"
}

resource "aws_s3_bucket_cors_configuration" "claims_cors" {
  bucket = data.aws_s3_bucket.claims.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "POST", "PUT"]
    allowed_origins = [
      "http://localhost:3000",
      "http://localhost:1120",
      "https://safety.nirvanatech.com",
      "https://support.nirvanatech.com",
      "https://*.safety.nirvanatech.com",
      "https://*.support.nirvanatech.com"
    ]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

data "aws_s3_bucket" "boards_renewal_cache" {
  bucket = "boards-renewal-cache"
}

data "aws_s3_bucket" "telematics_spark_test" {
  bucket = "nirvana-telematics-spark-test"
}

resource "aws_s3_bucket_notification" "telematics_spark_test_eventbridge" {
  bucket      = data.aws_s3_bucket.telematics_spark_test.id
  eventbridge = true
}

resource "aws_s3_bucket" "telematics_data_lake" {
  bucket = "nirvana-telematics-data-lake"
  tags = {
    bucket = "nirvana-telematics-data-lake"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "telematics_data_lake_intelligent_tiering" {
  bucket = aws_s3_bucket.telematics_data_lake.id
  rule {
    id = "${aws_s3_bucket.telematics_data_lake.id}-intelligent_tiering"
    filter {
    }
    transition {
      days          = 0
      storage_class = "INTELLIGENT_TIERING"
    }
    status = "Enabled"
  }
}
