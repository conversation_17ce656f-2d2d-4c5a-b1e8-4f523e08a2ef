resource "aws_iam_role" "db_migrate_lambda" {
  name               = "db_migrate_lambda"
  assume_role_policy = data.aws_iam_policy_document.lambda_assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "db_migrate_lambda" {
  role       = aws_iam_role.db_migrate_lambda.name
  policy_arn = data.aws_iam_policy.LambdaVPCAccessPlus.arn
}

data "aws_iam_policy_document" "db_migrate_lambda_policy_doc" {

  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetResourcePolicy",
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
      "secretsmanager:ListSecretVersionIds"
    ]
    resources = [
      data.aws_secretsmanager_secret_version.app_db_readonly_password.arn,
      data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn,
      data.aws_secretsmanager_secret_version.app_db_ds_user_password.arn,
    ]
  }
}

resource "aws_iam_policy" "db_migrate_lambda_policy" {
  policy = data.aws_iam_policy_document.db_migrate_lambda_policy_doc.json
}

resource "aws_iam_role_policy_attachment" "db_migrate_lambda_role_policy" {
  role       = aws_iam_role.db_migrate_lambda.name
  policy_arn = aws_iam_policy.db_migrate_lambda_policy.arn
}

resource "aws_lambda_function" "db_migrate_lambda" {
  function_name = "db_migrate_lambda"
  role          = aws_iam_role.db_migrate_lambda.arn
  description   = "Lambda for managing migrations & postups"

  environment {
    variables = {
      "ENV"                            = "prod"
      "DATABASES_NIRVANA_HOST"         = local.postgres_database_address
      "DATABASES_NIRVANA_NAME"         = "postgres"
      "DATABASES_NIRVANA_PORT"         = local.postgres_database_port
      "DATABASES_NIRVANA_USERNAME"     = local.postgres_database_migrator_username
      "DATABASES_NIRVANA_PASSWORD"     = data.aws_secretsmanager_secret_version.app_db_migrator_password.secret_string

      "DATABASES_FMCSA_HOST"           = local.fmcsa_database_address_write
      "DATABASES_FMCSA_PORT"           = local.fmcsa_database_port
      "DATABASES_FMCSA_NAME"           = "postgres"
      "DATABASES_FMCSA_USERNAME"       = local.fmcsa_database_username
      "DATABASES_FMCSA_PASSWORD"       = aws_secretsmanager_secret_version.db_fmcsa_password_val.secret_string

      "DATABASES_FMCSAWRITE_HOST"                         = local.fmcsa_database_address_write
      "DATABASES_FMCSAWRITE_PORT"                         = local.fmcsa_database_port
      "DATABASES_FMCSAWRITE_NAME"                         = "postgres"
      "DATABASES_FMCSAWRITE_USERNAME"                     = local.fmcsa_database_username
      "DATABASES_FMCSAWRITE_PASSWORD"                     = aws_secretsmanager_secret_version.db_fmcsa_password_val.secret_string

      "DATABASES_FMCSAREADONLY_HOST"                      = local.fmcsa_database_address_read
      "DATABASES_FMCSAREADONLY_PORT"                      = local.fmcsa_database_port
      "DATABASES_FMCSAREADONLY_NAME"                      = "postgres"
      "DATABASES_FMCSAREADONLY_USERNAME"                  = local.fmcsa_database_username
      "DATABASES_FMCSAREADONLY_PASSWORD"                  = aws_secretsmanager_secret_version.db_fmcsa_password_val.secret_string

      # Needed for logger
      "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY" = data.aws_secretsmanager_secret_version.launchdarkly_api_key.secret_string
    }
  }

  package_type = "Image"
  image_uri    = "${var.db_migrate_lambda_ecr_repo_url}:${var.db_migrate_lambda_tag}"

  memory_size                    = 512 # 512 MB, CPU automatically scales with memory
  reserved_concurrent_executions = 1
  timeout                        = 300 # 5 minutes (to accomodate long-running post-ups / index creations)

  vpc_config {
    subnet_ids = [
      local.private_subnet_ids[0],
      local.private_subnet_ids[1],
      local.private_subnet_ids[2],
    ]
    security_group_ids = [
      local.default_sg_id
    ]
  }

  tags = {
    Environment = terraform.workspace
  }

  lifecycle {
    # ignore drift when lambda is re-deployed from outside terraform
    ignore_changes = [
      image_uri,
    ]
  }
}
