locals {
  # From cdktf_legacy_network
  default_vpc_id     = data.terraform_remote_state.cdktf_legacy_network.outputs.default_vpc_id
  default_sg_id      = data.terraform_remote_state.cdktf_legacy_network.outputs.default_sg_id
  public_subnet_ids  = data.terraform_remote_state.cdktf_legacy_network.outputs.public_subnet_ids
  private_subnet_ids = data.terraform_remote_state.cdktf_legacy_network.outputs.private_subnet_ids


  # From cdktf_container_compute
  app_cluster_id                = data.terraform_remote_state.cdktf_container_compute.outputs.app_cluster_id
  app_cluster_arn               = data.terraform_remote_state.cdktf_container_compute.outputs.app_cluster_arn
  app_cluster_name              = data.terraform_remote_state.cdktf_container_compute.outputs.app_cluster_name
  internal_tools_cluster_name   = data.terraform_remote_state.cdktf_container_compute.outputs.internal_tools_cluster_name
  internal_tools_cluster_arn    = data.terraform_remote_state.cdktf_container_compute.outputs.internal_tools_cluster_arn
  jobber_singletons_cluster_arn = data.terraform_remote_state.cdktf_container_compute.outputs.jobber_singletons_cluster_arn
  jobber_singletons_cluster_name = data.terraform_remote_state.cdktf_container_compute.outputs.jobber_singletons_cluster_name
  app_cluster_dns_id            = data.terraform_remote_state.cdktf_container_compute.outputs.app_cluster_dns_id
  app_cluster_dns_name          = data.terraform_remote_state.cdktf_container_compute.outputs.app_cluster_dns_name
}

# See: src/infra/platform/src/legacy-network-stack.ts
data "terraform_remote_state" "cdktf_legacy_network" {
  backend = "s3"
  config = {
    bucket = "cloud.nirvanatech.com"
    region = "us-east-2"
    key    = "private/deployment/cdktf/default/production/default-production-coreinfra-legacy-network-us-east-2.json"
  }
}

# See: src/infra/platform/src/container-compute-stack.ts
data "terraform_remote_state" "cdktf_container_compute" {
  backend = "s3"
  config = {
    bucket = "cloud.nirvanatech.com"
    region = "us-east-2"
    key    = "private/deployment/cdktf/default/production/default-production-coreinfra-container-compute-us-east-2.json"
  }
}
