# Create log group for service
resource "aws_cloudwatch_log_group" "event_job_processor" {
  name_prefix = "${terraform.workspace}-event-job-processor-logs"

  tags = {
    Environment = terraform.workspace
    Application = "event_job_processor"
  }
}

# Create IAM role and policy for Telematics API Server ECS tasks to access other AWS services
data "aws_iam_policy_document" "event_job_processor_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.go-service-profiles.arn,
      data.aws_s3_bucket.jobber-schedules.arn,
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.go-service-profiles.arn}/*",
      "${data.aws_s3_bucket.jobber-schedules.arn}/*"
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "ecs:GetTaskProtection",
      "ecs:UpdateTaskProtection",
    ]
    resources = [
      "arn:aws:ecs:*:667656038718:task/*/*"
    ]
  }
}

resource "aws_iam_policy" "event_job_processor_tasks_policy" {
  policy = data.aws_iam_policy_document.event_job_processor_tasks_policy_doc.json
}

resource "aws_iam_role" "event_job_processor_task_role" {
  name               = "event_job_processor_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "eventJobProcessorTaskRole_policy" {
  role       = aws_iam_role.event_job_processor_task_role.name
  policy_arn = aws_iam_policy.event_job_processor_tasks_policy.arn
}

# Create task definition
module "event_job_processor_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "event-job-processor-td"
  container_name = "event-job-processor"

  container_image = "${var.event_job_processor_ecr_repo_url}:${var.event_job_processor_tag}"
  task_memory     = 1024 # 1 GB
  task_cpu        = 512 # 0.5 cores
  essential       = true
  enable_otel_sidecar = true
  metrics_namespace   = "event_job_processor"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 56222
      hostPort      = 56222
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6068
      hostPort      = 6068
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                                               = "prod"
    "DATABASES_NIRVANA_HOST"                            = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"                            = "postgres"
    "DATABASES_NIRVANA_PORT"                            = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"                        = local.postgres_database_readwrite_username
    "STATSD_RECEIVER_ADDRESS"                           = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT"                       = "${local.otel_server_fqdn}:4317"
  }

  map_secrets = {
    "DATABASES_NIRVANA_PASSWORD"                        = data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn

    "INFRA_SLACK_EVENTS_TOKEN"                          = "${data.aws_secretsmanager_secret_version.slack_events_jobber.arn}:oauth_access_token::"
    "PAGERDUTY_TELEMATICS_PIPELINE_FAILURES_ROUTINGKEY" = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_TELEMATICS_PIPELINE_FAILURES_ROUTINGKEY::"
    "PAGERDUTY_INFRA_ROUTINGKEY"                        = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_INFRA_ROUTINGKEY::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_CLIENT_ID"     = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-client-id-test::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_CLIENT_SECRET" = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-client-secret-test::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_PASSWORD"      = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-password-test::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_CLIENT_ID"          = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-client-id::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_CLIENT_SECRET"      = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-client-secret::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_PASSWORD"           = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-password::"
    "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"                   = data.aws_secretsmanager_secret_version.launchdarkly_api_key.arn
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.event_job_processor.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-event-job-processor"
    }
  }

  task_role_arn           = aws_iam_role.event_job_processor_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "event-job-processor"
  }
}

# Create ECS Fargate service
module "event_job_processor_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "event-job-processor"
  container_name = "event-job-processor"
  container_port = 56222

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name

  task_definition_arn = module.event_job_processor_td.arn

  desired_count = 1
  # We set this to 300 so that 2 draining deployments can be performed
  # simultaneously
  deployment_maximum_percent         = 300
  deployment_minimum_healthy_percent = 100

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "event-job-processor"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_internal_infra_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_internal_infra_sns_arn,
    }
  }
}

locals {
  event_job_processor_fqdn = "${module.event_job_processor_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}
