# Create log group for service
resource "aws_cloudwatch_log_group" "feature_store_server" {
  name_prefix = "${terraform.workspace}-feature-store-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "feature_store_server"
  }
}

resource "aws_cloudwatch_log_group" "inmem_feature_store_server" {
  name_prefix = "${terraform.workspace}-inmem-feature-store-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "inmem_feature_store_server"
  }
}

# Create IAM role and policy for Telematics gRPC Server ECS tasks to access other AWS services
data "aws_iam_policy_document" "feature_store_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.go-service-profiles.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.go-service-profiles.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "feature_store_server_tasks_policy" {
  policy = data.aws_iam_policy_document.feature_store_server_tasks_policy_doc.json
}

resource "aws_iam_role" "feature_store_server_task_role" {
  name               = "feature_store_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "featureStoreServerTaskRole_policy" {
  role       = aws_iam_role.feature_store_server_task_role.name
  policy_arn = aws_iam_policy.feature_store_server_tasks_policy.arn
}

# Create task definition
module "feature_store_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "feature-store-server-td"
  container_name = "feature-store-server"

  container_image  = "${var.feature_store_server_ecr_repo_url}:${var.feature_store_server_tag}"
  task_memory = 2048 # 2 GB RAM
  task_cpu    = 1024  # 1 vCPU
  essential        = true
  enable_otel_sidecar = true
  metrics_namespace   = "feature_store_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 5553
      hostPort      = 5553
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6067
      hostPort      = 6067
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                         = "prod"
    "DATABASES_NIRVANA_HOST"      = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"      = "postgres"
    "DATABASES_NIRVANA_PORT"      = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"  = local.postgres_database_readwrite_username

    "STATSD_RECEIVER_ADDRESS"     = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT" = "${local.otel_server_fqdn}:4317"
  }

  map_secrets = {
    "DATABASES_NIRVANA_PASSWORD"  = data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn
    "PRODUCTTOOLS_SEGMENTAPIKEY"  = "${data.aws_secretsmanager_secret_version.segment_secrets.arn}:segment_api_key_prod::"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.feature_store_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-feature-store-server"
    }
  }

  task_role_arn           = aws_iam_role.feature_store_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "feature-store-server"
  }
}

# Create task definition for staging
module "inmem_feature_store_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "inmem-feature-store-server-td"
  container_name = "inmem-feature-store-server"

  container_image  = "${var.feature_store_server_ecr_repo_url}:${var.feature_store_server_tag}"
  task_memory = 2048 # 2 GB RAM
  task_cpu    = 512  # 0.5 vCPU
  command          = ["-inmem"]
  essential        = true

  port_mappings = [
    {
      containerPort = 5553
      hostPort      = 5553
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6067
      hostPort      = 6067
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV" = "dev"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.inmem_feature_store_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-inmem-feature-store-server"
    }
  }

  task_role_arn           = aws_iam_role.feature_store_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "inmem-feature-store-server"
  }
}


# Create ECS Fargate service
module "feature_store_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "feature-store-server"
  container_name = "feature-store-server"
  container_port = 5553

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name


  task_definition_arn = module.feature_store_server_td.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "feature-store-server"
  }
}

# Create ECS Fargate service for staging
module "inmem_feature_store_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "inmem-feature-store-server"
  container_name = "inmem-feature-store-server"
  container_port = 5553

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.inmem_feature_store_server_td.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "inmem-feature-store-server"
  }
}


locals {
  feature_store_server_fqdn = "${module.feature_store_server_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}
