resource "aws_iam_role" "grpc_lambda" {
  name               = "grpc_lambda"
  assume_role_policy = data.aws_iam_policy_document.lambda_assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "grpc_lambda" {
  role       = aws_iam_role.grpc_lambda.name
  policy_arn = data.aws_iam_policy.LambdaVPCAccessPlus.arn
}

resource "aws_lambda_function" "grpc_lambda" {
  function_name = "grpc_lambda"
  role          = aws_iam_role.grpc_lambda.arn
  description   = "Lambda for making GRPC requests"

  environment {
    variables = {
      "ENV" = "prod"
      # Needed for logger
      "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY" = data.aws_secretsmanager_secret_version.launchdarkly_api_key.secret_string
    }
  }

  package_type = "Image"
  image_uri    = "${var.grpc_lambda_ecr_repo_url}:${var.grpc_lambda_tag}"

  memory_size = 128 # 128 MB, CPU automatically scales with memory
  timeout     = 300

  vpc_config {
    subnet_ids = [
      local.private_subnet_ids[0],
      local.private_subnet_ids[1],
      local.private_subnet_ids[2],
    ]
    security_group_ids = [
      local.default_sg_id
    ]
  }

  tags = {
    Environment = terraform.workspace
  }

  lifecycle {
    # ignore drift when lambda is re-deployed from outside terraform
    ignore_changes = [
      image_uri,
    ]
  }
}
