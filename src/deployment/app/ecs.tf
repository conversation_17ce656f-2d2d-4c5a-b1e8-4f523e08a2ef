# Create ECS Task Execution IAM role and policy to run ECS tasks
resource "aws_iam_role" "ecsTaskExecutionRole" {
  name               = "ecsTaskExecutionRole"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role_policy_attachment" "ecsTaskExecutionRole_policy" {
  role       = aws_iam_role.ecsTaskExecutionRole.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

data "aws_iam_policy_document" "secretsmanager_document" {
  statement {
    effect    = "Allow"
    actions   = ["secretsmanager:ListSecrets"]
    resources = ["arn:aws:secretsmanager:*:667656038718:secret:*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetResourcePolicy",
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret",
      "secretsmanager:ListSecretVersionIds"
    ]
    resources = [
      aws_secretsmanager_secret.postgres_password.arn,
      aws_secretsmanager_secret.db_fmcsa_password.arn,
      data.aws_secretsmanager_secret.app_db_ds_user_password.arn,
      data.aws_secretsmanager_secret.nhtsa_db_password.arn,
      data.aws_secretsmanager_secret.flatfile_secrets.arn,
      data.aws_secretsmanager_secret.geocodio_secrets.arn,
      data.aws_secretsmanager_secret.pagerduty_secrets.arn,
      data.aws_secretsmanager_secret.google_app_config_secrets.arn,
      data.aws_secretsmanager_secret.hubspot_secrets.arn,
      data.aws_secretsmanager_secret.impler_secrets.arn,
      data.aws_secretsmanager_secret.jira_secrets.arn,
      data.aws_secretsmanager_secret.knock_api_key.arn,
      data.aws_secretsmanager_secret.knock_webhook_key.arn,
      data.aws_secretsmanager_secret.launchdarkly_api_key.arn,
      data.aws_secretsmanager_secret.llamacloud_secrets.arn,
      data.aws_secretsmanager_secret.langfuse_credentials.arn,
      data.aws_secretsmanager_secret.otel_server.arn,
      data.aws_secretsmanager_secret.nars_secrets.arn,
      data.aws_secretsmanager_secret.ascend_secrets.arn,
      data.aws_secretsmanager_secret.openai_secrets.arn,
      data.aws_secretsmanager_secret.salesforce_secrets.arn,
      data.aws_secretsmanager_secret.segment_secrets.arn,
      data.aws_secretsmanager_secret.unipdf_secrets.arn,
      data.aws_secretsmanager_secret.workramp_secrets.arn,
      data.aws_secretsmanager_secret.rateml_gsheets.arn,
      data.aws_secretsmanager_secret.sendgrid_api_key.arn,
      data.aws_secretsmanager_secret.terminal.arn,
      data.aws_secretsmanager_secret.datadog_secret.arn,
      data.aws_secretsmanager_secret.oodle_secret.arn,
      data.aws_secretsmanager_secret.twilio_secrets.arn,
      data.aws_secretsmanager_secret.snapsheet_secrets.arn,
      data.aws_secretsmanager_secret.uw_ai_secrets.arn,
      data.aws_secretsmanager_secret.app_db_readwrite_password.arn,
      data.aws_secretsmanager_secret.carfax_secrets.arn,
      data.aws_secretsmanager_secret.cmt_secrets.arn,
      data.aws_secretsmanager_secret.slack_events_jobber.arn,
      data.aws_secretsmanager_secret.snowflake_pass.arn,
      data.aws_secretsmanager_secret.snapsheet_snowflake_credentials.arn,
      data.aws_secretsmanager_secret.heremaps.arn,
      data.aws_secretsmanager_secret.openmeteo.arn,
      data.aws_secretsmanager_secret.terminal.arn,
      data.aws_secretsmanager_secret.clerk_secret.arn,
      data.aws_secretsmanager_secret.app_db_migrator_password.arn,
    ]
  }
}

resource "aws_iam_policy" "secretsmanager_policy" {
  name        = "custom-managed-get-secrets"
  description = "Provides read access to AWS Secrets Manager"
  policy      = data.aws_iam_policy_document.secretsmanager_document.json
}

resource "aws_iam_role_policy_attachment" "ecsTaskExecutionRole_secretsmanager_policy" {
  role       = aws_iam_role.ecsTaskExecutionRole.name
  policy_arn = aws_iam_policy.secretsmanager_policy.arn
}
