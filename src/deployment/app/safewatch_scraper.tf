# Create log group for service
resource "aws_cloudwatch_log_group" "saferwatch_scraper" {
  name_prefix = "${terraform.workspace}-saferwatch-scraper-logs"

  tags = {
    Environment = terraform.workspace
    Application = "saferwatch_scraper"
  }
}

module "saferwatch_scraper_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "saferwatch_scraper-td"
  container_name = "saferwatch_scraper"

  container_image         = "${var.saferwatch_scraper_repo_url}:${var.saferwatch_scraper_tag}"
  task_memory             = 2048 # 2 GB RAM
  task_cpu                = 1024 # 1 vCPU
  essential               = true
  enable_otel_sidecar     = true
  metrics_namespace       = "safewatch_scrapper"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 56224
      hostPort      = 56224
      protocol      = "tcp"
    }
  ]

  map_environment = {
    "DB_HOST"                = local.postgres_database_address,
    "DB_PORT"                = local.postgres_database_port,
    "DB_SSL"                 = true,
    "DB_NAME"                = "postgres",
    "DB_USER"                = local.postgres_database_readwrite_username,
    "DB_PASSWORD"            = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string,
    "SAFERWATCH_USER"        = jsondecode(data.aws_secretsmanager_secret_version.saferwatch_user_pass.secret_string)["user"],
    "SAFERWATCH_PASSWORD"    = jsondecode(data.aws_secretsmanager_secret_version.saferwatch_user_pass.secret_string)["password"],
    "SCRAPING_BATCH_SIZE"    = 200,
    "PUPPETEER_HEADLESS"     = true,
    "PUPPETEER_GOTO_TIMEOUT" = 10000,
    "PUPPETEER_TYPE_DELAY"   = 100,
  }

  log_configuration = {
    "logDriver" = "awslogs",
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.saferwatch_scraper.name,
      "awslogs-region"        = "us-east-2",
      "awslogs-stream-prefix" = "awslogs-saferwatch-scraper"
    }
  }

  task_role_arn           = aws_iam_role.api_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "saferwatch-scraper"
  }
}

# Create ECS Fargate service
module "saferwatch_scraper_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "saferwatch-scraper"
  container_name = "saferwatch-scraper"
  container_port = 56224

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name


  task_definition_arn = module.saferwatch_scraper_td.arn

  desired_count                      = 1
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "saferwatch-scraper"
  }
}

locals {
  saferwatch_scraper_fqdn = "${module.saferwatch_scraper_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}

