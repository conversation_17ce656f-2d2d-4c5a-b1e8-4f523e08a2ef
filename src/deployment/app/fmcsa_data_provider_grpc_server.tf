# Create log group for service
resource "aws_cloudwatch_log_group" "fmcsa_data_provider_grpc_server" {
  name_prefix = "${terraform.workspace}-fmcsa-data-provider-grpc-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "fmcsa_data_provider_grpc_server"
  }
}

# Create IAM role and policy for FMCSA data provider gRPC Server ECS tasks to access other AWS services
data "aws_iam_policy_document" "fmcsa_data_provider_grpc_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.go-service-profiles.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.go-service-profiles.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "fmcsa_data_provider_grpc_server_tasks_policy" {
  policy = data.aws_iam_policy_document.fmcsa_data_provider_grpc_server_tasks_policy_doc.json
}

resource "aws_iam_role" "fmcsa_data_provider_grpc_server_task_role" {
  name               = "fmcsa_data_provider_grpc_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "fmcsaDataProviderGRPCServerTaskRole_policy" {
  role       = aws_iam_role.fmcsa_data_provider_grpc_server_task_role.name
  policy_arn = aws_iam_policy.fmcsa_data_provider_grpc_server_tasks_policy.arn
}

# Create task definition
module "fmcsa_data_provider_grpc_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "fmcsa-data-provider-grpc-server-td"
  container_name = "fmcsa-data-provider-grpc-server"

  container_image  = "${var.fmcsa_data_provider_grpc_server_ecr_repo_url}:${var.fmcsa_data_provider_grpc_server_tag}"
  task_memory = 2048 # 2 GB RAM
  task_cpu    = 1024  # 1 vCPU
  essential        = true
  enable_otel_sidecar = true
  metrics_namespace   = "fmcsa_data_provider_grpc_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 9095
      hostPort      = 9095
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6065
      hostPort      = 6065
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                                 = "prod"
    "DATABASES_FMCSA_HOST"                = local.fmcsa_database_address_read
    "DATABASES_FMCSA_PORT"                = local.fmcsa_database_port
    "DATABASES_FMCSA_NAME"                = "postgres"
    "DATABASES_FMCSA_USERNAME"            = local.fmcsa_database_username

    "DATABASES_FMCSAWRITE_HOST"           = local.fmcsa_database_address_write
    "DATABASES_FMCSAWRITE_PORT"           = local.fmcsa_database_port
    "DATABASES_FMCSAWRITE_NAME"           = "postgres"
    "DATABASES_FMCSAWRITE_USERNAME"       = local.fmcsa_database_username

    "DATABASES_FMCSAREADONLY_HOST"        = local.fmcsa_database_address_read
    "DATABASES_FMCSAREADONLY_PORT"        = local.fmcsa_database_port
    "DATABASES_FMCSAREADONLY_NAME"        = "postgres"
    "DATABASES_FMCSAREADONLY_USERNAME"    = local.fmcsa_database_username

    "STATSD_RECEIVER_ADDRESS"       = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT"   = "${local.otel_server_fqdn}:4317"
  }

  map_secrets = {
    "DATABASES_FMCSA_PASSWORD"                          = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAWRITE_PASSWORD"                     = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAREADONLY_PASSWORD"                  = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.fmcsa_data_provider_grpc_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-fmcsa-data-provider-grpc-server"
    }
  }

  task_role_arn           = aws_iam_role.fmcsa_data_provider_grpc_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "fmcsa-data-provider-grpc-server"
  }
}

# Create ECS Fargate service
module "fmcsa_data_provider_grpc_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "fmcsa-data-provider-grpc-server"
  container_name = "fmcsa-data-provider-grpc-server"
  container_port = 9092

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.fmcsa_data_provider_grpc_server_td.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "fmcsa-data-provider-grpc-server"
  }
}

locals {
  fmcsa_data_provider_grpc_server_fqdn = "${module.fmcsa_data_provider_grpc_server_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}
