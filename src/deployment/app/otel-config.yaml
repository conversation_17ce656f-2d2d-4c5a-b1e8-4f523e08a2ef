extensions:
  health_check:

receivers:
  statsd:
    endpoint: 0.0.0.0:8125 #default
    aggregation_interval: 10s #default
    enable_metric_type: false #default
    timer_histogram_mapping:
      - statsd_type: "histogram"
        observer_type: "summary"
      - statsd_type: "timer"
        observer_type: "histogram"
        histogram:
          max_size: 100
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:55681

  awsecscontainermetrics:
    collection_interval: 30s


processors:
  batch/traces:
    timeout: 1s
    send_batch_size: 50
  batch/metrics:
    timeout: 60s
  filter/ecs:
    metrics:
      include:
        match_type: strict
        metric_names:
          - 'ecs.task.cpu.utilized'
          - 'ecs.task.memory.usage'
          - 'ecs.task.memory.usage.limit'

exporters:
  datadog/api:
    hostname: bastion
    api:
      key: ${env:DATADOG_KEY}
  otlphttp/oodle:
    metrics_endpoint: https://nirvana.collector.oodle.ai/v1/otlp/metrics/inst_nirvana_ACFWu5
    headers:
      X-API-KEY: ${env:OODLE_KEY}

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch/traces]
      exporters: [datadog/api]
    metrics/1:
      receivers: [statsd, otlp]
      processors: [batch/metrics]
      exporters: [datadog/api, otlphttp/oodle]
    metrics/ecs:
      receivers: [awsecscontainermetrics]
      processors: [batch/metrics, filter/ecs]
      exporters: [datadog/api, otlphttp/oodle]

  extensions: [health_check]
