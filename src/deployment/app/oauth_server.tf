# Create log group for service
resource "aws_cloudwatch_log_group" "oauth_server" {
  name = "${terraform.workspace}-oauth-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "oauth-server"
  }
}

# Create IAM role and policy for oauth server ECS tasks to access other AWS services
data "aws_iam_policy_document" "oauth_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.go-service-profiles.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.go-service-profiles.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "oauth_server_tasks_policy" {
  policy = data.aws_iam_policy_document.oauth_server_tasks_policy_doc.json
}

resource "aws_iam_role" "oauth_server_task_role" {
  name               = "oauth_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "oauthServerTaskRole_policy" {
  role       = aws_iam_role.oauth_server_task_role.name
  policy_arn = aws_iam_policy.oauth_server_tasks_policy.arn
}


locals {
  oauth_common_config = {
    ecr_repo_url = var.oauth_server_ecr_repo_url,
    ecr_tag      = var.oauth_server_tag,
    memory       = 1024, # 1 GB
    cpu          = 256,  # 0.25 vCPU
    service_port = 8181,
    pprof_port   = 6060,
    environment_vars = {
      "ENV" = "prod"
      # Postgres
      "DATABASES_NIRVANA_HOST"     = local.postgres_database_address
      "DATABASES_NIRVANA_NAME"     = "postgres"
      "DATABASES_NIRVANA_PORT"     = local.postgres_database_port
      "DATABASES_NIRVANA_USERNAME" = local.postgres_database_readwrite_username
      "DATABASES_NIRVANA_PASSWORD" = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
      # Samsara Secrets
      "SAMSARA_APP_ID"            = jsondecode(data.aws_secretsmanager_secret_version.samsara_secrets.secret_string)["samsara_app_id"]
      "SAMSARA_APP_SECRET"        = jsondecode(data.aws_secretsmanager_secret_version.samsara_secrets.secret_string)["samsara_app_secret"]
      "SAMSARA_SAFETY_APP_ID"     = jsondecode(data.aws_secretsmanager_secret_version.samsara_secrets.secret_string)["samsara_safety_app_id"]
      "SAMSARA_SAFETY_APP_SECRET" = jsondecode(data.aws_secretsmanager_secret_version.samsara_secrets.secret_string)["samsara_safety_app_secret"]
      # KeepTruckin Secrets
      "KEEPTRUCKIN_CLIENT_ID"            = jsondecode(data.aws_secretsmanager_secret_version.keeptruckin_secrets.secret_string)["client_id"]
      "KEEPTRUCKIN_CLIENT_SECRET"        = jsondecode(data.aws_secretsmanager_secret_version.keeptruckin_secrets.secret_string)["client_secret"]
      "KEEPTRUCKIN_SAFETY_CLIENT_ID"     = jsondecode(data.aws_secretsmanager_secret_version.keeptruckin_secrets.secret_string)["safety_client_id"]
      "KEEPTRUCKIN_SAFETY_CLIENT_SECRET" = jsondecode(data.aws_secretsmanager_secret_version.keeptruckin_secrets.secret_string)["safety_client_secret"]
      # Misc
      "PRODUCTTOOLS_SEGMENTAPIKEY"                   = jsondecode(data.aws_secretsmanager_secret_version.segment_secrets.secret_string)["segment_api_key_prod"]
      "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"              = data.aws_secretsmanager_secret_version.launchdarkly_api_key.secret_string
      "STATSD_RECEIVER_ADDRESS"                      = "${local.otel_server_fqdn}:8125"
      "OTEL_EXPORTER_OTLP_ENDPOINT"                  = "${local.otel_server_fqdn}:4317"
      "PRODUCTTOOLS_GOOGLE_APP_CONFIG_CLIENT_ID"     = jsondecode(data.aws_secretsmanager_secret_version.google_app_config_secrets.secret_string)["CLIENT_ID"]
      "PRODUCTTOOLS_GOOGLE_APP_CONFIG_CLIENT_SECRET" = jsondecode(data.aws_secretsmanager_secret_version.google_app_config_secrets.secret_string)["CLIENT_SECRET"]
    }
    log_group_name          = aws_cloudwatch_log_group.oauth_server.name,
    task_role_arn           = aws_iam_role.oauth_server_task_role.arn,
    task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn,
    environment             = terraform.workspace,
    vpc_id                  = local.default_vpc_id,
    private_subnets = [
      local.private_subnet_ids[0],
      local.private_subnet_ids[1],
      local.private_subnet_ids[2],
    ]
    security_groups                = [local.default_sg_id]
    service_discovery_namespace_id = local.app_cluster_dns_id
    ecs_cluster_arn                = local.internal_tools_cluster_arn
    ecs_cluster_name               = local.internal_tools_cluster_name
  }
}

module "samsara_oauth_server" {
  source     = "../common-modules/oauth"
  tsp        = "TSPSamsara"
  env_config = local.oauth_common_config
}

module "samsara_safety_oauth_server" {
  source     = "../common-modules/oauth"
  tsp        = "TSPSamsaraSafety"
  env_config = local.oauth_common_config
}

module "keeptruckin_oauth_server" {
  source     = "../common-modules/oauth"
  tsp        = "TSPKeepTruckin"
  env_config = local.oauth_common_config
}

module "keeptruckin_safety_oauth_server" {
  source     = "../common-modules/oauth"
  tsp        = "TSPKeepTruckinSafety"
  env_config = local.oauth_common_config
}

module "google_oauth_server" {
  source     = "../common-modules/oauth"
  tsp        = "Google"
  env_config = local.oauth_common_config
}
