locals {
  boards_server_svc_name = "boards-server"
}

# Create log group for service
resource "aws_cloudwatch_log_group" "boards_server" {
  name_prefix = "${terraform.workspace}-${local.boards_server_svc_name}-logs"

  tags = {
    Environment = terraform.workspace
    Application = local.boards_server_svc_name
  }
}

# Create IAM role and policy for boards server ECS tasks to access other AWS services
data "aws_iam_policy_document" "boards_server" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.data_scratch.arn,
      aws_s3_bucket.telematics.arn,
      data.aws_s3_bucket.metaflow-artifacts.arn,
      data.aws_s3_bucket.boards_renewal_cache.arn,
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.data_scratch.arn}/*",
      "${data.aws_s3_bucket.boards_renewal_cache.arn}/*",
      "${data.aws_s3_bucket.metaflow-artifacts.arn}/*"
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
    ]
    resources = [
      "${aws_s3_bucket.telematics.arn}/*",
      "${data.aws_s3_bucket.nirvana_ds_dumps.arn}/*"
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "kms:Decrypt",
    ]
    resources = [
      data.aws_kms_key.metaflow_artifact_store_key.arn
    ]
  }
}

resource "aws_iam_policy" "boards_server" {
  policy = data.aws_iam_policy_document.boards_server.json
}

resource "aws_iam_role" "boards_server" {
  name               = "boards_server"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "boards_server_task" {
  role       = aws_iam_role.boards_server.name
  policy_arn = aws_iam_policy.boards_server.arn
}

# Create task definition
module "boards_server_task_definition" {
  source = "../common-modules/ecs_task_definition"

  family          = "${local.boards_server_svc_name}-td"
  container_name  = local.boards_server_svc_name
  container_image = "${var.datascience_ecr_repo_url}:boards-prod"
  command         = ["task", "streamlit"]

  task_memory = 16384 # 16 GBs
  task_cpu    = 2048  # 2 vCPUs

  essential               = true
  enable_otel_sidecar     = true
  metrics_namespace       = "boards_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 8501
      hostPort      = 8501
      protocol      = "tcp"
    }
  ]
  map_environment = {
    ENV : "prod",
    "snowflake_account"  = local.snowflake_account_name,
    "snowflake_username" = local.snowflake_datascience_user
  }
  map_secrets = {
    "snowflake_password" = "${data.aws_secretsmanager_secret_version.snowflake_pass.arn}:datascience_pass::"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.boards_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-${local.boards_server_svc_name}"
    }
  }

  task_role_arn           = aws_iam_role.boards_server.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = local.boards_server_svc_name
  }
}

# Create ECS Fargate service
module "boards_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = local.boards_server_svc_name
  container_name = local.boards_server_svc_name
  container_port = 8501

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name

  task_definition_arn = module.boards_server_task_definition.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  create_lb_access_sg_rule = true
  lb_access_sg             = module.boards_server_alb.access_sg_id
  lb_http_tg_arns          = module.boards_server_alb.http_tgs_arns
  lb_http_tg_ports         = module.boards_server_alb.http_tgs_ports

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.boards_server_svc_name
  }
}

module "boards_server_alb" {
  source = "../common-modules/nv_svc_alb"

  name_prefix = local.boards_server_svc_name
  vpc_id      = local.default_vpc_id

  # Application Load Balancer
  security_groups = [local.default_sg_id]

  public_subnets = [
    local.public_subnet_ids[0],
    local.public_subnet_ids[1],
    local.public_subnet_ids[2],
  ]

  # Access Control to Application Load Balancer
  http_ports = {
    force_https = {
      listener_port = 80
      host          = "#{host}"
      path          = "/#{path}"
      target_port   = "443"
      protocol      = "HTTPS"
      query         = "#{query}"
      status_code   = "HTTP_301"
    }
  }
  https_ports = {
    force_https = {
      listener_port = 443
    }
  }

  # Target Groups
  target_group_health_check_interval            = 15
  target_group_health_check_path                = "/healthz"
  target_group_health_check_timeout             = 10
  target_group_health_check_healthy_threshold   = 3
  target_group_health_check_unhealthy_threshold = 3
  target_group_health_check_matcher             = "200,301,302"
  target_group_deregistration_delay             = 10

  # Stickiness
  stickiness_enabled         = true
  stickiness_type            = "app_cookie"
  stickiness_cookie_duration = 86400
  stickiness_cookie_name     = "APP_REVIEW_ID"

  # Certificates
  default_certificate_arn = data.aws_acm_certificate.prod_cert.arn
  ssl_policy              = local.elb_ssl_policy

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.boards_server_svc_name
  }
}

resource "aws_appautoscaling_target" "boards_service" {
  max_capacity       = 6
  min_capacity       = 1
  resource_id        = "service/${local.internal_tools_cluster_name}/${module.boards_server_fg_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "boards_service_memory" {
  name               = "boards-service-memory"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.boards_service.resource_id
  scalable_dimension = aws_appautoscaling_target.boards_service.scalable_dimension
  service_namespace  = aws_appautoscaling_target.boards_service.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value = 80
  }
}

resource "aws_appautoscaling_policy" "boards_service_cpu" {
  name               = "boards-service-cpu"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.boards_service.resource_id
  scalable_dimension = aws_appautoscaling_target.boards_service.scalable_dimension
  service_namespace  = aws_appautoscaling_target.boards_service.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value = 60
  }
}

resource "aws_security_group_rule" "boards_server_alb_ingress" {
  type = "ingress"

  security_group_id        = local.default_sg_id
  protocol                 = -1
  from_port                = 0
  to_port                  = 0
  source_security_group_id = module.boards_server_alb.access_sg_id
}

# Create A record in Route53 zone
resource "aws_route53_record" "boards_server" {
  zone_id = data.aws_route53_zone.prod.zone_id
  name    = "boards.prod.nirvanatech.com"
  type    = "A"

  alias {
    name                   = module.boards_server_alb.dns_name
    zone_id                = module.boards_server_alb.zone_id
    evaluate_target_health = true
  }
}
