# Create log group for service
resource "aws_cloudwatch_log_group" "vehicles_grpc_server" {
  name_prefix = "${terraform.workspace}-vehicles-grpc-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "vehicles_grpc_server"
  }
}

# Create IAM role and policy for Vehicles gRPC Server ECS tasks to access other AWS services
data "aws_iam_policy_document" "vehicles_grpc_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.go-service-profiles.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.go-service-profiles.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "vehicles_grpc_server_tasks_policy" {
  policy = data.aws_iam_policy_document.vehicles_grpc_server_tasks_policy_doc.json
}

resource "aws_iam_role" "vehicles_grpc_server_task_role" {
  name               = "vehicles_grpc_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "vehiclesGRPCServerTaskRole_policy" {
  role       = aws_iam_role.vehicles_grpc_server_task_role.name
  policy_arn = aws_iam_policy.vehicles_grpc_server_tasks_policy.arn
}

# Create task definition
module "vehicles_grpc_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "vehicles-grpc-server-td"
  container_name = "vehicles-grpc-server"

  # TODO: set image tag with a variable
  container_image  = "${var.vehicles_grpc_server_ecr_repo_url}:${var.vehicles_grpc_server_tag}"
  task_memory = 2048 # 2 GB RAM
  task_cpu    = 1024  # 1 vCPU
  essential        = true
  enable_otel_sidecar = true
  metrics_namespace   = "vehicles_grpc_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 9092
      hostPort      = 9092
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6065
      hostPort      = 6065
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                           = "prod"
    "DATABASES_REDISGRAPH_PASSWORD" = data.aws_secretsmanager_secret_version.redisgraph_secrets.secret_string
    "DATABASES_NEO4J_PASSWORD"      = data.aws_secretsmanager_secret_version.neo4j_secrets.secret_string
    "DATABASES_NIRVANA_HOST"        = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"        = "postgres"
    "DATABASES_NIRVANA_PORT"        = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"    = local.postgres_database_readwrite_username
    "DATABASES_NIRVANA_PASSWORD"    = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    "STATSD_RECEIVER_ADDRESS"       = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT"   = "${local.otel_server_fqdn}:4317"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.vehicles_grpc_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-vehicles-grpc-server"
    }
  }

  task_role_arn           = aws_iam_role.vehicles_grpc_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "vehicles-grpc-server"
  }
}

# Create ECS Fargate service
module "vehicles_grpc_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "vehicles-grpc-server"
  container_name = "vehicles-grpc-server"
  container_port = 9092

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.vehicles_grpc_server_td.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "vehicles-grpc-server"
  }
}

locals {
  vehicles_grpc_server_fqdn = "${module.vehicles_grpc_server_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}
