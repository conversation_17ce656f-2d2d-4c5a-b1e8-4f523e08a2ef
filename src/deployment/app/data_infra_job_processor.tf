# Create log group for service
resource "aws_cloudwatch_log_group" "data_infra_job_processor" {
  name_prefix = "${terraform.workspace}-data-infra-job-processor-logs"

  tags = {
    Environment = terraform.workspace
    Application = "data_infra_job_processor"
  }
}

# Create IAM role and policy for data-infra job_processor ECS tasks to access other AWS services
data "aws_iam_policy_document" "data_infra_job_processor_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt",
    ]
    resources = [
      aws_kms_key.telematics_api_key_credentials.arn,
      aws_kms_key.telematics_consent_user_request_metadata.arn,
      aws_kms_key.terminal_user_credentials.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      aws_s3_bucket.telematics.arn,
      aws_s3_bucket.blob_store.arn,
      data.aws_s3_bucket.go-service-profiles.arn,
      data.aws_s3_bucket.jobber-schedules.arn,
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${aws_s3_bucket.telematics.arn}/*",
      "${aws_s3_bucket.blob_store.arn}/*",
      "${data.aws_s3_bucket.go-service-profiles.arn}/*",
      "${data.aws_s3_bucket.jobber-schedules.arn}/*",
      "arn:aws:s3:::cmt-dw-staging-nirvana-input/*"
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.cost-and-usage-reports.arn,
      "arn:aws:s3:::cmt-dw-staging-nirvana-input",
      "arn:aws:s3:::cmt-dw-staging-nirvana-output"
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:DeleteObject",
    ]
    resources = [
      "${data.aws_s3_bucket.cost-and-usage-reports.arn}/*"
    ]
  }
  statement {
      sid    = ""
      effect = "Allow"
      actions = [
        "s3:GetObject",
      ]
      resources = [
        "arn:aws:s3:::cmt-dw-staging-nirvana-output/*"
      ]
    }
  statement {
      sid    = ""
      effect = "Allow"
      actions = [
        "s3:ListBucket"
      ]
      resources = [
        aws_s3_bucket.telematics_snowflake_stage.arn,
      ]
    }
    statement {
      sid    = ""
      effect = "Allow"
      actions = [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
      ]
      resources = [
        "${aws_s3_bucket.telematics_snowflake_stage.arn}/*"
      ]
    }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "ecs:GetTaskProtection",
      "ecs:UpdateTaskProtection",
    ]
    resources = [
      "arn:aws:ecs:*:667656038718:task/*/*"
    ]
  }
  # allow access to read/delete metaflow SFNs
  statement {
    effect = "Allow"
    actions = [
      "states:DeleteStateMachine",
      "states:DescribeStateMachine",
      "states:ListExecutions",
      "states:ListStateMachines",
      "states:ListTagsForResource",
    ]
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_policy" "data_infra_job_processor_tasks_policy" {
  policy = data.aws_iam_policy_document.data_infra_job_processor_tasks_policy_doc.json
}

resource "aws_iam_role" "data_infra_job_processor_task_role" {
  name               = "data_infra_job_processor_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "dataInfraJobProcessorTaskRole_policy" {
  role       = aws_iam_role.data_infra_job_processor_task_role.name
  policy_arn = aws_iam_policy.data_infra_job_processor_tasks_policy.arn
}

# Create task definition
module "data_infra_job_processor_td" {
  source = "../common-modules/ecs_task_definition_oodle_log_poc"

  family         = "data-infra-job-processor-td"
  container_name = "data-infra-job-processor"

  container_image  = "${var.data_infra_job_processor_ecr_repo_url}:${var.data_infra_job_processor_tag}"
  task_memory = 32768 # 32 GB RAM
  task_cpu    = 16384 # 16 vCPU
  essential        = true
  enable_otel_sidecar = true
  metrics_namespace   = "data_infra_job_processor"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"
  cloudwatch_log_group = aws_cloudwatch_log_group.data_infra_job_processor.name
  cloudwatch_log_stream = "awslogs-data-infra-job-processor/data-infra-job-processor"

  container_ulimits = [
    {
      name      = "nofile"
      hardLimit = 16384
      softLimit = 16384
    }
  ]

  port_mappings = [
    {
      containerPort = 56225
      hostPort      = 56225
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6067
      hostPort      = 6067
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                                               = "prod"
    "DATABASES_NIRVANA_HOST"                            = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"                            = "postgres"
    "DATABASES_NIRVANA_PORT"                            = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"                        = local.postgres_database_readwrite_username

    "DATABASES_FMCSA_HOST"                              = local.fmcsa_database_address_read
    "DATABASES_FMCSA_PORT"                              = local.fmcsa_database_port
    "DATABASES_FMCSA_NAME"                              = "postgres"
    "DATABASES_FMCSA_USERNAME"                          = local.fmcsa_database_username

    "DATABASES_FMCSAWRITE_HOST"                         = local.fmcsa_database_address_write
    "DATABASES_FMCSAWRITE_PORT"                         = local.fmcsa_database_port
    "DATABASES_FMCSAWRITE_NAME"                         = "postgres"
    "DATABASES_FMCSAWRITE_USERNAME"                     = local.fmcsa_database_username

    "DATABASES_FMCSAREADONLY_HOST"                      = local.fmcsa_database_address_read
    "DATABASES_FMCSAREADONLY_PORT"                      = local.fmcsa_database_port
    "DATABASES_FMCSAREADONLY_NAME"                      = "postgres"
    "DATABASES_FMCSAREADONLY_USERNAME"                  = local.fmcsa_database_username

    "DATABASES_NHTSA_HOST"                              = local.nhtsa_database_address
    
    "SNOWFLAKE_DS_ACCOUNT_NAME"                         = local.snowflake_account_name
    "SNOWFLAKE_DS_USER_NAME"                            = local.snowflake_datascience_user
    
    "SNOWFLAKE_ADMIN_ACCOUNT_NAME"                      = local.snowflake_account_name
    "SNOWFLAKE_ADMIN_USER_NAME"                         = local.snowflake_admin_user
    "STATSD_RECEIVER_ADDRESS"                           = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT"                       = "${local.otel_server_fqdn}:4317"
  }

  map_secrets = {
    "CARFAX_USERNAME"                                   = "${data.aws_secretsmanager_secret_version.carfax_secrets.arn}:username::"
    "CMT_API_KEY"                                       = "${data.aws_secretsmanager_secret_version.cmt_secrets.arn}:apikey::"
    "CMT_PASSPHRASE"                                    = "${data.aws_secretsmanager_secret_version.cmt_secrets.arn}:passphrase::"
    "DATABASES_FMCSA_PASSWORD"                          = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAREADONLY_PASSWORD"                  = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAWRITE_PASSWORD"                     = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_NHTSA_PASSWORD"                          = data.aws_secretsmanager_secret_version.nhtsa_db_password.arn
    "DATABASES_NIRVANA_PASSWORD"                        = data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn
    "INFRA_SLACK_EVENTS_TOKEN"                          = "${data.aws_secretsmanager_secret_version.slack_events_jobber.arn}:oauth_access_token::"
    "PAGERDUTY_AGENTS_ROUTINGKEY"                       = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_AGENTS_ROUTINGKEY::"
    "PAGERDUTY_DATASCIENCE_ROUTINGKEY"                  = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_DATASCIENCE_ROUTINGKEY::"
    "PAGERDUTY_INFRA_ROUTINGKEY"                        = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_INFRA_ROUTINGKEY::"
    "PAGERDUTY_TELEMATICS_PIPELINE_FAILURES_ROUTINGKEY" = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_TELEMATICS_PIPELINE_FAILURES_ROUTINGKEY::"
    "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"                   = data.aws_secretsmanager_secret_version.launchdarkly_api_key.arn
    "PRODUCTTOOLS_SEGMENTAPIKEY"                        = "${data.aws_secretsmanager_secret_version.segment_secrets.arn}:segment_api_key_prod::"
    "SNOWFLAKE_ADMIN_PASSWORD"                          = "${data.aws_secretsmanager_secret_version.snowflake_pass.arn}:snowflake_pass::"
    "SNOWFLAKE_DS_PASSWORD"                             = "${data.aws_secretsmanager_secret_version.snowflake_pass.arn}:datascience_pass::"
    "TELEMATICS_CREDENTIALS_HEREMAPS_APIKEY"            = "${data.aws_secretsmanager_secret_version.heremaps.arn}:prod_api_key::"
    "TELEMATICS_CREDENTIALS_OPENMETEO_APIKEY"           = "${data.aws_secretsmanager_secret_version.openmeteo.arn}:prod_api_key::"
    "TELEMATICS_CREDENTIALS_TERMINAL_SECRETKEY"         = "${data.aws_secretsmanager_secret_version.terminal.arn}:prod_secret_key::"
  }

  log_configuration = {
    "logDriver" = "awsfirelens"
    "options" = {}
  }

  task_role_arn           = aws_iam_role.data_infra_job_processor_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "data-infra-job-processor"
  }
}

# Create ECS Fargate service
module "data_infra_job_processor_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "data-infra-job-processor"
  container_name = "data-infra-job-processor"
  container_port = 56225

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name

  task_definition_arn = module.data_infra_job_processor_td.arn

  desired_count                      = 1
  deployment_maximum_percent         = 100
  deployment_minimum_healthy_percent = 0

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "data-infra-job-processor"
  }

  cloudwatch_alarms_config = {
    # disable CPU alarm for data infra job processor as it is too noisy and un-actionable in short-term
    cpu_alarm = null,
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_internal_infra_sns_arn,
    }
  }
}
