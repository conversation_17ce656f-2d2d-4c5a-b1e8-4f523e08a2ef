# Create K<PERSON> key and alias to encrypt/decrypt fmcsa_credentials
resource "aws_kms_key" "fmcsa_credentials" {
  description = "The key used to encrypt/decrypt FMCSA DOT `PIN`"
  tags = {
    Environment = terraform.workspace
    Name        = "${terraform.workspace}-FMCSA-credentials-KMS"
  }
}

resource "aws_kms_alias" "fmcsa_credentials" {
  name          = "alias/${terraform.workspace}-fmcsa-credentials"
  target_key_id = aws_kms_key.fmcsa_credentials.id
}

# To encrypt/decrypt telematics.api_key_credentials
resource "aws_kms_key" "telematics_api_key_credentials" {
  description = "The key used to encrypt/decrypt `telematics.api_key_credentials`"
  tags = {
    Environment = terraform.workspace
    Name        = "${terraform.workspace}-Telematics-Api-Key-Credentials-KMS"
  }
}

resource "aws_kms_alias" "telematics_api_key_credentials" {
  name          = "alias/${terraform.workspace}-telematics-api-key-credentials"
  target_key_id = aws_kms_key.telematics_api_key_credentials.id
}


# To encrypt/decrypt terminal_credentials.user_credentials
resource "aws_kms_key" "terminal_user_credentials" {
  description = "The key used to encrypt/decrypt user credentials for Terminal (withterminal.com)"
  tags = {
    Environment = terraform.workspace
    Name        = "${terraform.workspace}-Terminal-User-Credentials-KMS"
  }
}

resource "aws_kms_alias" "terminal_user_credentials" {
  name          = "alias/${terraform.workspace}-terminal-user-credentials"
  target_key_id = aws_kms_key.terminal_user_credentials.id
}

# To encrypt/decrypt truckercloud_credentials.user_credentials
# Note that Truckercloud is no longer a supported telematics data provider, but
# we retain the KMS key in case we ever need to decrypt old data.
resource "aws_kms_key" "truckercloud_user_credentials" {
  description = "The key used to encrypt/decrypt TruckerCloud `user_credentials`"
  tags = {
    Environment = terraform.workspace
    Name        = "${terraform.workspace}-TruckerCloud-user-credentials-KMS"
  }
}

resource "aws_kms_alias" "truckercloud_user_credentials" {
  name          = "alias/${terraform.workspace}-truckercloud-user-credentials"
  target_key_id = aws_kms_key.truckercloud_user_credentials.id
}

# To encrypt/decrypt TSP webhook secrets
resource "aws_kms_key" "tsp_webhook_secret_keys_credentials" {
  description = "The key used to encrypt/decrypt TSP webhook secret keys"
  tags = {
    Environment = terraform.workspace
    Name        = "${terraform.workspace}-TSP-Webhook-Secret-Keys-Credentials-KMS"
  }
}

resource "aws_kms_alias" "tsp_webhook_secret_keys_credentials" {
  name          = "alias/${terraform.workspace}-tsp-webhook-secret-keys-credentials"
  target_key_id = aws_kms_key.tsp_webhook_secret_keys_credentials.id
}

# To encrypt/decrypt telematics consent user request metadata
resource "aws_kms_key" "telematics_consent_user_request_metadata" {
  description = "The key used to encrypt/decrypt telematics consent user request metadata"
  tags = {
    Environment = terraform.workspace
    Name        = "${terraform.workspace}-Telematics-Consent-User-Request-Metadata-KMS"
  }
}

resource "aws_kms_alias" "telematics_consent_user_request_metadata" {
  name          = "alias/${terraform.workspace}-telematics-consent-user-request-metadata"
  target_key_id = aws_kms_key.telematics_consent_user_request_metadata.id
}

# To encrypt/decrypt User SSN
resource "aws_kms_key" "user_ssn" {
  description = "The key used to encrypt/decrypt User SSN"
  tags = {
    Environment = terraform.workspace
    Name        = "${terraform.workspace}-User-SSN-KMS"
  }
}

resource "aws_kms_alias" "user_ssn" {
  name          = "alias/${terraform.workspace}-user-ssn"
  target_key_id = aws_kms_key.user_ssn.id
}

# To encrypt/decrypt User SSN Last Four
resource "aws_kms_key" "user_ssn_last_four" {
  description = "The key used to encrypt/decrypt User SSN last four characters"
  tags = {
    Environment = terraform.workspace
    Name        = "${terraform.workspace}-User-SSN-Last-Four-KMS"
  }
}

resource "aws_kms_alias" "user_ssn_last_four" {
  name          = "alias/${terraform.workspace}-user-ssn-last-four"
  target_key_id = aws_kms_key.user_ssn_last_four.id
}
