# This policy needs to be attached to the role that we want lambda to assume.
data "aws_iam_policy_document" "lambda_assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

# AWS-managed policy to allow lambda to create network interfaces in *any* VPC
# Required if the lambda needs access to VPC resource.
# Note: this policy includes LambdaBasic (hence "plus"), so no need to double-attach.
data "aws_iam_policy" "LambdaVPCAccessPlus" {
  arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}
