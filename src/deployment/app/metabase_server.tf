locals {
  metabase_server_svc_name = "metabase-server"
}

# Create log group for service
resource "aws_cloudwatch_log_group" "metabase_server" {
  name_prefix = "${terraform.workspace}-${local.metabase_server_svc_name}-logs"

  tags = {
    Environment = terraform.workspace
    Application = local.metabase_server_svc_name
  }
}

# Create task definition
module "metabase_server_task_definition" {
  source = "../common-modules/ecs_task_definition"

  family         = "${local.metabase_server_svc_name}-td"
  container_name = local.metabase_server_svc_name

  container_image  = "${var.metabase_server_ecr_repo_url}:v0.50.10"
  task_memory = 8192
  task_cpu    = 4096

  essential = true
  enable_otel_sidecar = true
  metrics_namespace   = "metabase_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 3000
      hostPort      = 3000
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "MB_DB_TYPE"   = "postgres"
    "MB_DB_HOST"   = local.postgres_database_address
    "MB_DB_PORT"   = local.postgres_database_port
    "MB_DB_USER"   = local.postgres_database_readwrite_username
    "MB_DB_PASS"   = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    "MB_DB_DBNAME" = "metabase"
    "JAVA_OPTS"    = "-Xmx6g"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.metabase_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-${local.metabase_server_svc_name}"
    }
  }

  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = local.metabase_server_svc_name
  }
}

# Create ECS Fargate service
module "metabase_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = local.metabase_server_svc_name
  container_name = local.metabase_server_svc_name
  container_port = 3000

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name


  task_definition_arn = module.metabase_server_task_definition.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  create_lb_access_sg_rule = true
  lb_access_sg             = module.metabase_server_alb.access_sg_id
  lb_http_tg_arns          = module.metabase_server_alb.http_tgs_arns
  lb_http_tg_ports         = module.metabase_server_alb.http_tgs_ports

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.metabase_server_svc_name
  }
}

module "metabase_server_alb" {
  source = "../common-modules/nv_svc_alb"

  name_prefix = local.metabase_server_svc_name
  vpc_id      = local.default_vpc_id

  # Application Load Balancer
  security_groups = [local.default_sg_id]

  public_subnets = [
    local.public_subnet_ids[0],
    local.public_subnet_ids[1],
    local.public_subnet_ids[2],
  ]

  # Access Control to Application Load Balancer
  http_ports = {
    force_https = {
      listener_port = 80
      host          = "#{host}"
      path          = "/#{path}"
      target_port   = "443"
      protocol      = "HTTPS"
      query         = "#{query}"
      status_code   = "HTTP_301"
    }
  }
  https_ports = {
    force_https = {
      listener_port = 443
    }
  }

  # Target Groups
  target_group_health_check_interval            = 30
  target_group_health_check_path                = "/api/health"
  target_group_health_check_timeout             = 10
  target_group_health_check_healthy_threshold   = 3
  target_group_health_check_unhealthy_threshold = 3
  target_group_health_check_matcher             = "200,301,302"

  # Certificates
  default_certificate_arn = data.aws_acm_certificate.dev_cert_ohio.arn
  ssl_policy              = local.elb_ssl_policy

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.metabase_server_svc_name
  }
}

resource "aws_appautoscaling_target" "metabase_server" {
  max_capacity       = 3
  min_capacity       = 1
  resource_id        = "service/${local.internal_tools_cluster_name}/${module.metabase_server_fg_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "metabase_service_memory" {
  name               = "metabase-service-memory"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.metabase_server.resource_id
  scalable_dimension = aws_appautoscaling_target.metabase_server.scalable_dimension
  service_namespace  = aws_appautoscaling_target.metabase_server.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value = 80
  }
}

resource "aws_appautoscaling_policy" "metabase_service_cpu" {
  name               = "metabase-service-cpu"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.metabase_server.resource_id
  scalable_dimension = aws_appautoscaling_target.metabase_server.scalable_dimension
  service_namespace  = aws_appautoscaling_target.metabase_server.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value = 60
  }
}

resource "aws_security_group_rule" "metabase_server_alb_ingress" {
  type = "ingress"

  security_group_id        = local.default_sg_id
  protocol                 = -1
  from_port                = 0
  to_port                  = 0
  source_security_group_id = module.metabase_server_alb.access_sg_id
}

resource "aws_route53_record" "metabase_server_a_record" {
  zone_id = data.aws_route53_zone.dev.zone_id
  name    = "metabase.dev.nirvanatech.com"
  type    = "A"

  alias {
    name                   = module.metabase_server_alb.dns_name
    zone_id                = module.metabase_server_alb.zone_id
    evaluate_target_health = true
  }
}
