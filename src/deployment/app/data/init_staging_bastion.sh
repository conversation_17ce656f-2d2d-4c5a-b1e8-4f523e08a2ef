#! /bin/bash
# Fail fast
set -euo pipefail

# TODO: Merge this with init_bastion.sh #

####### Setup home mount #########
##################################

# This is potentially fragile. If SSH fails, logon to EC2 console and connect to the instance,
# then look at /var/log/syslog (cloud-init logs) to debug

# Wait for a minute for the home volume to be mounted
sleep 60

DEVICE=/dev/nvme1n1
FS_TYPE=$(file -s $DEVICE | awk '{print $2}')
MOUNT_POINT=/home

# If no FS, then this output contains "data"
if [ "$FS_TYPE" = "data" ]
then
    echo "Creating file system on $DEVICE"
    mkfs -t ext4 $DEVICE
fi

mkdir -p $MOUNT_POINT
mount $DEVICE $MOUNT_POINT
chown -R ubuntu:ubuntu $MOUNT_POINT

SSH_CONFIG_FILE=/etc/ssh/sshd_config.d/nirvana_sshd.conf
test -f $SSH_CONFIG_FILE && sudo rm -f $SSH_CONFIG_FILE
cat <<EOT | sudo tee $SSH_CONFIG_FILE
AcceptEnv NIRVANA_EMAIL
EOT

####### Add public keys ##########
##################################
echo "Adding public keys"
# Always recreate the ssh config directory to have access control driven thru cloud-init
rm -rf /home/<USER>/.ssh
mkdir -p /home/<USER>/.ssh/
{
    # Abhay
    echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIKgYy5u/dQjJz8o6bnvtRkiL0fm13YKL13oHkccLDRaY <EMAIL>"
    # Sanket
    echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDIoF0vPrvnV1KpzpVO4dt4B+kFrHpZW0fynNucnhYtJ+SQ83K4V09mnZHtgWos0rx2IHyeak34hhXz2KaBiH5J71t2IFA18kmyyueN8in5oGD4PayFMHXWv5YTyV/p10Y2k2Gx2WHTjttHpjGrqhS+MlzwFnLf6U/CvLtQgU5If9aO7PX5X9pREamwrlbuEC0iJFP1FA1ouBKMJ+uQx0bwmAnL+oyAoBm60ZI1Pv5vS3daK9EEsm6Q1fv5JZQVMveSvAbP+4puCuGLJHxhfPQi4WrHkfpN6KXuIV1hXdJnDK6I7okUh8wcBTTNzM9In6nTlq+QRzj+lY0gWoCMZTp/ <EMAIL>"
    # Nishant
    echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDQ/S6OkJExsJup+PScWz/r2ObpZV4OAt2XJAlkXYOOsr7jiJ0fOn6dtXKN31kTrtCt4nFwNF8pBWJnXMpLnAIHo7A94BE+dAH6mzaTomWGl30/U/jo4KlFmFvFWEEYKBRaD2LaAfanH0Ih8OOlNx+N00kmVvZWo6kJLwlg2QYCDbpxj9WQLw/ygQDG0JP1QtJkbtnJcXJp/cZgpIx+NKXgtKjzjqqgElZOws+WXbJ8Wp3tsahdGcuThjNLDqXVgXeMWMNgWTP8DFYePTc2s7Y4zmkBFYqlMvb+sBII9Qj5w/YEy75COIHbKpCUdGURuRIEV41FF1je3cYE3Ol5rBfb9uiCfLljAa5WbCWDIJkzno8B50zEAdB+0kB+5EX7NbmiqJO/XUQD4SGAHqHRDNCvItEk49qb1/XOp3kEp40zzZFHs6LEsiHDo4jZvGv/G20LfuWY/lrYMr9/MB/9PcVmpwokxetUgZ6ZSUeYoNQN7CBYHAJZpNkDD1fVglRqv4Os2hLXEoCsgg3a5ADLK1xu43N+j2hgnadu4adGEHmV+bbAM/L153ofL/SeuUwIFOjR11KcScB/lqhzqCJXMSdXNoYlPDCMU2ywasz0mh+3VOBOifWwQzRzqo9pDMZ/eegqROphXRxG6jmiYC8JCeHThn0f48r80MKAxNoBKu2SkQ== <EMAIL>"
    # Shashank
    echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCr/fteybYzxgNii6q9gucaNUp4SsxK6YdM/CTe1GBx1z5QhvID+VCgOcQPVC+pkpWiWH5hWDsLa0LDi9bSKQoopm4KeZlWo6nbr2pIdGNOce3+1xiuNodfr1bGwEI+s6gFlUY8Ztx/Z6/2KY7VBj2WjtKaU+h8ERmthXvsz0QQb0+IrJBpMCyiczbnxK/ltnxbTHBbumAiepDKG3CK4WSlGXPR75eouRLjucSM765fyWzMy7D3VOKZnSQYqqv6t/LlgB5/YQ6693E3G4DvFsIgBU45KPOPKVFk7qN9190PW1xsCHpb27a1kXtjecurRJ6IuUkar2idLIXr0QtzHK6s6HMpYK+1rOMhw1aZMNB6LgPUzqrvRtTrWbZwbZrNyS2VoSbCSoTPFse0eryU5Yd3uopdadtsBelfGq6QR/67MThIwE9scKkrHcR+Wg1VS0qPRgf8ZMsujyfprgHUOCXKbnnZN7BSPIJj4HgXv7LmaRCKWQ//6p/pOSdSXeHN6FeVXry/a69yvf6Nk8PmryI+6ottiE+mRDtLanADvIQL4hWt26DwO2UksvcOugBMpEMHOhrhM/S8gTxQywQ7vEIppB0GnnebHAbdaH4XqYiw2X9avpnJ+7HRN5ncSY8BN3z9HWx+9SFO/Luh5ALS/jgYv6Dd4jux2Jt3+f2HII3bHQ== <EMAIL>"
    # Greg
    echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAINXgZFnoWOazWMJTB3NfQOsumd0wISC6eJLn0eAR3Hiu <EMAIL>"
    # Sarthak
    echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDDH8iQKC25cCpaishXrNVQLO7TGUY9IXqFYK1osOiGNwUv0tDGsV5DmeogK7UhizzD5z/5TnesdqH28YYJpZaZ6+zkND2golVEUgBb+RelrIwtnT4IQBaV7l+wi/m5pKlXC45SeaWNStiBOgs258B9hvcaQksV6wq7CBOPZKWDZteo13ehBOLXL+W5z0PdPOc6xvLZV8p3XszgUP+H/pRWlx1O9RTC2zS9ENXJ+d0Vzz0DholK0JDrhtNM3gqFAm4VYA0PfIpnXB0PQM62p01UCIBaWsZo51zOAZVcVECnNZkg5wPbPzkM7qOgtZw7Ma3iOsessFTENAjyjNYneVgE+9uX5XxL4R1FGoxXHzhWzkoS3tje44dZY2MyIyej3JjhhYyTSrcvVZPQ43KSy5/8xf4mKa5v3HupSJTkp34ewn24bFBmfP8kNz0eGEsX27ai2aGEi9WGl7IVs0zxs6YWDEvrjkkyBVfLRSLLtU+dim9/FMbf0Fp7K62+zneYr+GLzyrsI2sLUq2a3NGyHjBvft5CHhPIDBW1hmCppvYNu19ULgSjhoFzS1rHhhnm4W9ySdb/aofcbsYjVjBBizhGtLdzIaDpww+hMIXRVqN5tHntN+wOzui10d2Mm8X4AdlBo2Yx3TLUqdsZSVFa1uBjX+7nmyc6cX5h/pN2wKSpLQ== <EMAIL>"
    # Ayush Agarwal
    echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBHJJOSWTip2FV5AvFHoK8sRRdJ4ofo4klMD2JcJ4AhA <EMAIL>"
    # Garvit Gupta
    echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAILNXEICvqjGSLtc8QM3WuNmT3xdhBdNozr2N53l/92qV <EMAIL>"
} >> /home/<USER>/.ssh/authorized_keys
chown ubuntu /home/<USER>/.ssh/authorized_keys
chmod 400 /home/<USER>/.ssh/authorized_keys

######### Install deps #############
####################################
sudo apt -y update
sudo apt install -y net-tools
sudo apt install -y postgresql-client-common postgresql-client-12
# this is only needed to install bazelisk
sudo apt install -y golang-go
# needed to parse terraform output tags
sudo apt -y install jq

# needed to install aws cli
sudo apt -y install unzip

sudo apt -y install atop

# needed to fetch postgres password
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o /home/<USER>/awscliv2.zip
rm -rf /home/<USER>/aws
unzip /home/<USER>/awscliv2.zip -d /home/<USER>/
sudo /home/<USER>/aws/install
