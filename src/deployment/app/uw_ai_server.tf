# Create log group for service
resource "aws_cloudwatch_log_group" "uw_ai_server" {
  name_prefix = "${terraform.workspace}-uw-ai-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "uw_ai_server"
  }
}

resource "aws_iam_role" "uw_ai_server_task_role" {
  name               = "uw_ai_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

# Create task definition
module "uw_ai_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "uw-ai-server-td"
  container_name = "uw-ai-server"

  container_image  = "${var.uw_ai_server_ecr_repo_url}:latest"

  task_memory = 2048 # 2 GB RAM
  task_cpu    = 1024  # 1 vCPU

  metrics_namespace       = "uw_ai_server"
  enable_otel_sidecar     = true
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  essential = true

  # Give mapping of whatever ports you want to expose
  port_mappings = [
    {
      containerPort = 50052
      hostPort      = 50052
      protocol      = "tcp"
    },
  ]

  map_environment = {
    "ENV"                 = "prod"
  }

  # access secrets from AWS Secrets Manager
  map_secrets = {
    "OPENAI_API_KEY" = "${data.aws_secretsmanager_secret_version.uw_ai_secrets.arn}:openaiApiKey::"
    "CLAUDE_API_KEY" = "${data.aws_secretsmanager_secret_version.uw_ai_secrets.arn}:claudeApiKey::"
    "GEMINI_API_KEY" = "${data.aws_secretsmanager_secret_version.uw_ai_secrets.arn}:geminiApiKey::"
    "SECRET_KEY" = "${data.aws_secretsmanager_secret_version.uw_ai_secrets.arn}:tokenSecretKey::"
    "PHOENIX_API_KEY" = "${data.aws_secretsmanager_secret_version.uw_ai_secrets.arn}:pheonixApiKey::"
    "ARIZE_API_KEY" = "${data.aws_secretsmanager_secret_version.uw_ai_secrets.arn}:arizeApiKey::"
    "ARIZE_SPACE_ID" = "${data.aws_secretsmanager_secret_version.uw_ai_secrets.arn}:arizeSpaceId::"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.uw_ai_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-uw-ai-server"
    }
  }

  task_role_arn           = aws_iam_role.uw_ai_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "uw-ai-server"
  }

  healthcheck = {
    command = [
      "CMD-SHELL",
      "/app/grpc_health_probe -addr=:50052",
    ],
    interval    = 30,
    timeout     = 5,
    retries     = 3,
    startPeriod = 30
  }

}

# Create ECS Fargate service
module "uw_ai_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "uw-ai-server"
  container_name = "uw-ai-server"
  container_port = 50052

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.uw_ai_server_td.arn

  desired_count                      = 1

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]

  security_groups = [
    aws_security_group.uw_ai_server.id,
    local.default_sg_id
  ]

  service_discovery_namespace_id = local.app_cluster_dns_id

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "uw-ai-server"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      # Use SNS based on your team, check pagerduty_topics.tf for other available SNS topics
      team_sns_arn = local.cloudwatch_to_pagerduty_agents_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_agents_sns_arn,
    }
  }
}

# Specify security group for configuration as per your needs
resource "aws_security_group" "uw_ai_server" {
  name        = "uw_ai_server_sg"
  vpc_id      = local.default_vpc_id

  ingress {
    from_port   = 50052
    to_port     = 50052
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
