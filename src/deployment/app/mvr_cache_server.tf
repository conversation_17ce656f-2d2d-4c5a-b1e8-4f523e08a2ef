# Create log group for service
resource "aws_cloudwatch_log_group" "mvr_cache_server" {
  name = "${terraform.workspace}-mvr-cache-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "mvr-cache-server"
  }
}

# Create IAM role and policy for mvr cache server ECS tasks to access other AWS services
data "aws_iam_policy_document" "mvr_cache_server_task" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.mvr_cache.arn,
      data.aws_s3_bucket.lexisnexis_attract_cache.arn,
      data.aws_s3_bucket.lexisnexis_ncf_cache.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.mvr_cache.arn}/*",
      "${data.aws_s3_bucket.lexisnexis_attract_cache.arn}/*",
      "${data.aws_s3_bucket.lexisnexis_ncf_cache.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "mvr_cache_server_task" {
  policy = data.aws_iam_policy_document.mvr_cache_server_task.json
}

resource "aws_iam_role" "mvr_cache_server_task" {
  name               = "mvr_cache_server_task"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "mvrCacheServerTaskRole_policy" {
  role       = aws_iam_role.mvr_cache_server_task.name
  policy_arn = aws_iam_policy.mvr_cache_server_task.arn
}

# Create task definition
module "td_mvr_cache_server" {
  source = "../common-modules/ecs_task_definition"

  family         = "mvr_cacheserver-td"
  container_name = "mvr_cacheserver"

  container_image  = "${var.mvr_cache_server_ecr_repo_url}:${var.mvr_cache_server_tag}"
  task_memory = 2048
  task_cpu    = 1024
  enable_otel_sidecar = true
  metrics_namespace   = "mvr_cache_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  essential = true

  port_mappings = [
    {
      containerPort = 58727
      hostPort      = 58727
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6061
      hostPort      = 6061
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV" = "prod"
    "DATABASES_NIRVANA_HOST"      = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"      = "postgres"
    "DATABASES_NIRVANA_PORT"      = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"  = local.postgres_database_readwrite_username
    "DATABASES_NIRVANA_PASSWORD"  = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    "VERISK_PASSWORD"             = jsondecode(data.aws_secretsmanager_secret_version.verisk_secrets.secret_string)["password"]
    "VERISK_USERNAME"             = jsondecode(data.aws_secretsmanager_secret_version.verisk_secrets.secret_string)["user_id"]
    "VERISK_ACCOUNT_ID"           = jsondecode(data.aws_secretsmanager_secret_version.verisk_secrets.secret_string)["account_number"]
    "VERISK_BILL_CODE"            = jsondecode(data.aws_secretsmanager_secret_version.verisk_secrets.secret_string)["bill_code"]
    "VERISK_TLS_KEY"              = jsondecode(data.aws_secretsmanager_secret_version.verisk_secrets.secret_string)["tls_key"]
    "VERISK_TLS_CERT"             = jsondecode(data.aws_secretsmanager_secret_version.verisk_secrets.secret_string)["tls_cert"]
    "LEXISNEXIS_USERNAME"         = jsondecode(data.aws_secretsmanager_secret_version.lexisnexis_secrets.secret_string)["lexisnexis_prod_username"]
    "LEXISNEXIS_PASSWORD"         = jsondecode(data.aws_secretsmanager_secret_version.lexisnexis_secrets.secret_string)["lexisnexis_prod_password"]
    "STATSD_RECEIVER_ADDRESS"     = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT" = "${local.otel_server_fqdn}:4317"
    "PRODUCTTOOLS_OPENMETER_APIKEY" = jsondecode(data.aws_secretsmanager_secret_version.openmeter.secret_string)["apiKey"]
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.mvr_cache_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-mvr-cache-server"
    }
  }

  task_role_arn           = aws_iam_role.mvr_cache_server_task.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "mvr_cache_server"
  }
}

# Create ECS Fargate service
module "fg_service_mvr_cache_server" {
  source = "../common-modules/nv_fg_svc"

  name_prefix = "mvr-cache-server"

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name


  task_definition_arn = module.td_mvr_cache_server.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "mvr_cache_server"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_agents_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_agents_sns_arn,
    }
  }
}

locals {
  mvr_cache_server_fqdn = "${module.fg_service_mvr_cache_server.discovery_service_name}.${local.app_cluster_dns_name}"
}
