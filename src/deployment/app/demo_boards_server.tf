locals {
  demo_boards_server_svc_name = "demo-boards-server"
}

# Create log group for service
resource "aws_cloudwatch_log_group" "demo_boards_server" {
  name_prefix = "${terraform.workspace}-${local.demo_boards_server_svc_name}-logs"

  tags = {
    Environment = terraform.workspace
    Application = local.demo_boards_server_svc_name
  }
}

# Create IAM role for demo boards server ECS tasks to access other AWS services
# NOTE: We do not create new policy resource, and simply re-use the ones created
# for the "main" app.
resource "aws_iam_role" "demo_boards_server" {
  name               = "demo_boards_server"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "demo_boards_server_task" {
  role       = aws_iam_role.demo_boards_server.name
  policy_arn = aws_iam_policy.boards_server.arn
}

# Create task definition
module "demo_boards_server_task_definition" {
  source = "../common-modules/ecs_task_definition"

  family          = "${local.demo_boards_server_svc_name}-td"
  container_name  = local.demo_boards_server_svc_name
  container_image = "${var.datascience_ecr_repo_url}:boards-demo"

  command = ["task", "streamlit"]

  task_memory = 8192 # 8 GBs
  task_cpu    = 1024 # 1 vCPU

  essential = true
  enable_otel_sidecar = true
  metrics_namespace   = "demo_boards_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 8501
      hostPort      = 8501
      protocol      = "tcp"
    }
  ]
  map_environment = {
    ENV : "prod",
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.demo_boards_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-${local.demo_boards_server_svc_name}"
    }
  }

  task_role_arn           = aws_iam_role.demo_boards_server.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = local.demo_boards_server_svc_name
  }
}

# Create ECS Fargate service
module "demo_boards_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = local.demo_boards_server_svc_name
  container_name = local.demo_boards_server_svc_name
  container_port = 8501

  vpc_id          = local.default_vpc_id
  ecs_cluster_arn = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name


  task_definition_arn = module.demo_boards_server_task_definition.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  create_lb_access_sg_rule = true
  lb_access_sg = module.demo_boards_server_alb.access_sg_id
  lb_http_tg_arns = module.demo_boards_server_alb.http_tgs_arns
  lb_http_tg_ports = module.demo_boards_server_alb.http_tgs_ports

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.demo_boards_server_svc_name
  }
}

module "demo_boards_server_alb" {
  source = "../common-modules/nv_svc_alb"

  name_prefix = local.demo_boards_server_svc_name
  vpc_id      = local.default_vpc_id

  # Application Load Balancer
  security_groups = [local.default_sg_id]

  public_subnets = [
    local.public_subnet_ids[0],
    local.public_subnet_ids[1],
    local.public_subnet_ids[2],
  ]

  # Access Control to Application Load Balancer
  http_ports = {
    force_https = {
      listener_port = 80
      host          = "#{host}"
      path          = "/#{path}"
      target_port   = "443"
      protocol      = "HTTPS"
      query         = "#{query}"
      status_code   = "HTTP_301"
    }
  }
  https_ports = {
    force_https = {
      listener_port = 443
    }
  }

  # Target Groups
  target_group_health_check_interval            = 15
  target_group_health_check_path                = "/healthz"
  target_group_health_check_timeout             = 10
  target_group_health_check_healthy_threshold   = 3
  target_group_health_check_unhealthy_threshold = 3
  target_group_health_check_matcher             = "200,301,302"
  target_group_deregistration_delay             = 10

  # Stickiness
  stickiness_enabled         = true
  stickiness_type            = "app_cookie"
  stickiness_cookie_duration = 86400
  stickiness_cookie_name     = "APP_REVIEW_ID"

  # Certificates
  default_certificate_arn = data.aws_acm_certificate.prod_cert.arn
  ssl_policy              = "ELBSecurityPolicy-2016-08"

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.demo_boards_server_svc_name
  }
}

resource "aws_appautoscaling_target" "demo_boards_service" {
  max_capacity       = 3
  min_capacity       = 1
  resource_id        = "service/${local.internal_tools_cluster_name}/${module.demo_boards_server_fg_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "demo_boards_service_memory" {
  name               = "demo-boards-service-memory"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.demo_boards_service.resource_id
  scalable_dimension = aws_appautoscaling_target.demo_boards_service.scalable_dimension
  service_namespace  = aws_appautoscaling_target.demo_boards_service.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value = 80
  }
}

resource "aws_appautoscaling_policy" "demo_boards_service_cpu" {
  name               = "demo-boards-service-cpu"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.demo_boards_service.resource_id
  scalable_dimension = aws_appautoscaling_target.demo_boards_service.scalable_dimension
  service_namespace  = aws_appautoscaling_target.demo_boards_service.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value = 60
  }
}

resource "aws_security_group_rule" "demo_boards_server_alb_ingress" {
  type = "ingress"

  security_group_id        = local.default_sg_id
  protocol                 = -1
  from_port                = 0
  to_port                  = 0
  source_security_group_id = module.demo_boards_server_alb.access_sg_id
}

# Create A record in Route53 zone
resource "aws_route53_record" "demo_boards_server" {
  zone_id = data.aws_route53_zone.prod.zone_id
  name    = "demo-boards.prod.nirvanatech.com"
  type    = "A"

  alias {
    name                   = module.demo_boards_server_alb.dns_name
    zone_id                = module.demo_boards_server_alb.zone_id
    evaluate_target_health = true
  }
}
