locals {
  gorules_server_name = "gorules-server"
  gorules_execution_task_role_arn = "arn:aws:iam::667656038718:role/brmsTaskExecutionRole"
}

# Retrieve gorules secrets
data "aws_secretsmanager_secret" "dev_gorules" {
  name = "dev/gorules"
}

data "aws_secretsmanager_secret_version" "dev_gorules" {
  secret_id = data.aws_secretsmanager_secret.dev_gorules.id
}

data "aws_secretsmanager_secret" "sandbox_aurora_cluster" {
  name = "rds!cluster-e559c098-7657-4eac-883b-e8354bd55ad8"
}

data "aws_secretsmanager_secret_version" "sandbox_aurora_cluster" {
  secret_id = data.aws_secretsmanager_secret.sandbox_aurora_cluster.id
}

# Create log group for service
resource "aws_cloudwatch_log_group" "gorules_server" {
  name_prefix = "${terraform.workspace}-${local.gorules_server_name}-logs"

  tags = {
    Environment = terraform.workspace
    Application = local.gorules_server_name
  }
}

# Create task definition
module "gorules_server_task_definition" {
  source = "../common-modules/ecs_task_definition"

  family         = "${local.gorules_server_name}-td"
  container_name = local.gorules_server_name

  container_image  = "${var.gorules_server_ecr_repo_url}:58a907800f7d"
  task_memory = 1024
  task_cpu    = 512

  essential = true
  enable_otel_sidecar = true
  metrics_namespace   = "gorules_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 80
      hostPort      = 80
      protocol      = "tcp"
    }
  ]
  map_environment = {
    DB_NAME     = "gorules_db"
    DB_HOST     = local.gorules_sandbox_db_address
  }

  map_secrets = {
    DB_SSL_CA   = "${data.aws_secretsmanager_secret_version.dev_gorules.arn}:DB_SSL_CA::"
    LICENSE_KEY = "${data.aws_secretsmanager_secret_version.dev_gorules.arn}:LICENSE_KEY::"
    DB_PASSWORD = "${data.aws_secretsmanager_secret_version.sandbox_aurora_cluster.arn}:password::"
    DB_USER     = "${data.aws_secretsmanager_secret_version.sandbox_aurora_cluster.arn}:username::"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.gorules_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-${local.gorules_server_name}"
    }
  }

  task_execution_role_arn = local.gorules_execution_task_role_arn

  tags = {
    Environment = terraform.workspace
    Application = local.gorules_server_name
  }
}

# Create ECS Fargate service
module "gorules_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = local.gorules_server_name
  container_name = local.gorules_server_name
  container_port = 80

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name


  task_definition_arn = module.gorules_server_task_definition.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  create_lb_access_sg_rule = true
  lb_access_sg             = module.gorules_server_alb.access_sg_id
  lb_http_tg_arns          = module.gorules_server_alb.http_tgs_arns
  lb_http_tg_ports         = module.gorules_server_alb.http_tgs_ports

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.gorules_server_name
  }
}

module "gorules_server_alb" {
  source = "../common-modules/nv_svc_alb"

  name_prefix = local.gorules_server_name
  vpc_id      = local.default_vpc_id

  # Application Load Balancer
  security_groups = [local.default_sg_id]

  public_subnets = [
    local.public_subnet_ids[0],
    local.public_subnet_ids[1],
    local.public_subnet_ids[2],
  ]

  # Access Control to Application Load Balancer
  http_ports = {
    force_https = {
      listener_port = 80
      host          = "#{host}"
      path          = "/#{path}"
      target_port   = "443"
      protocol      = "HTTPS"
      query         = "#{query}"
      status_code   = "HTTP_301"
    }
  }
  https_ports = {
    force_https = {
      listener_port = 443
    }
  }

  # Target Groups
  target_group_health_check_interval            = 30
  target_group_health_check_path                = "/api/health"
  target_group_health_check_timeout             = 10
  target_group_health_check_healthy_threshold   = 3
  target_group_health_check_unhealthy_threshold = 3
  target_group_health_check_matcher             = "200"

  # Certificates
  default_certificate_arn = data.aws_acm_certificate.dev_cert_ohio.arn
  ssl_policy              = "ELBSecurityPolicy-2016-08"

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.gorules_server_name
  }
}

resource "aws_route53_record" "gorules_server_a_record" {
  zone_id = data.aws_route53_zone.dev.zone_id
  name    = "gorules.dev.nirvanatech.com"
  type    = "A"

  alias {
    name                   = module.gorules_server_alb.dns_name
    zone_id                = module.gorules_server_alb.zone_id
    evaluate_target_health = true
  }
}
