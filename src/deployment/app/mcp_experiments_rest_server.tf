locals {
  mcp_experiments_rest_server_svc_name = "mcp-experiments-rest"
}

# Create log group for service
resource "aws_cloudwatch_log_group" "mcp_experiments_rest_server" {
  name_prefix = "${terraform.workspace}-${local.mcp_experiments_rest_server_svc_name}-logs"

  tags = {
    Environment = terraform.workspace
    Application = local.mcp_experiments_rest_server_svc_name
  }
}

# Create IAM role and policy for experimental rest server ECS tasks to access other AWS services
data "aws_iam_policy_document" "mcp_experiments_rest_server" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.go-service-profiles.arn,
      data.aws_s3_bucket.quoting.arn,
      data.aws_s3_bucket.pdfgen.arn,
      data.aws_s3_bucket.rateml_artifacts.arn,
      aws_s3_bucket.forms.arn,
      aws_s3_bucket.underwriting.arn,
      data.aws_s3_bucket.data_scratch.arn,
      aws_s3_bucket.billing.arn,
      aws_s3_bucket.data_context_store.arn,
      data.aws_s3_bucket.claims.arn,
      data.aws_s3_bucket.reporting.arn,
      aws_s3_bucket.blob_store.arn,
      aws_s3_bucket.pricing_simulations.arn,
      data.aws_s3_bucket.draft-fnol-attachments.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
    ]
    resources = [
      "${data.aws_s3_bucket.go-service-profiles.arn}/*",
      "${data.aws_s3_bucket.quoting.arn}/*",
      "${data.aws_s3_bucket.pdfgen.arn}/*",
      "${data.aws_s3_bucket.rateml_artifacts.arn}/*",
      "${aws_s3_bucket.forms.arn}/*",
      "${aws_s3_bucket.underwriting.arn}/*",
      "${data.aws_s3_bucket.data_scratch.arn}/*",
      "${aws_s3_bucket.billing.arn}/*",
      "${aws_s3_bucket.data_context_store.arn}/*",
      "${data.aws_s3_bucket.claims.arn}/*",
      "${data.aws_s3_bucket.reporting.arn}/*",
      "${aws_s3_bucket.blob_store.arn}/*",
      "${aws_s3_bucket.pricing_simulations.arn}/*",
      "${data.aws_s3_bucket.draft-fnol-attachments.arn}/*",
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:PutObject",
    ]
    resources = [
      # Only allow put object to the go-service-profiles bucket
      "${data.aws_s3_bucket.go-service-profiles.arn}/*",
    ]
  }
}

# Define the IAM policy resource
resource "aws_iam_policy" "mcp_experiments_rest_server" {
  name   = "${terraform.workspace}-${local.mcp_experiments_rest_server_svc_name}-policy"
  policy = data.aws_iam_policy_document.mcp_experiments_rest_server.json

  tags = {
    Environment = terraform.workspace
    Application = local.mcp_experiments_rest_server_svc_name
  }
}

# Create IAM role for experimental rest server ECS tasks
resource "aws_iam_role" "mcp_experiments_rest_server" {
  name = "${terraform.workspace}-${local.mcp_experiments_rest_server_svc_name}-role"

  # Use the common assume role policy for ECS tasks
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json

  tags = {
    Environment = terraform.workspace
    Application = local.mcp_experiments_rest_server_svc_name
  }
}

# Create IAM role policy attachment for experimental rest server ECS tasks
resource "aws_iam_role_policy_attachment" "mcp_experiments_rest_server" {
  role       = aws_iam_role.mcp_experiments_rest_server.name
  policy_arn = aws_iam_policy.mcp_experiments_rest_server.arn
}

# Create ECS task definition for experimental rest server
module "mcp_experiments_rest_server_td" { # Renamed module instance
  source = "../common-modules/ecs_task_definition"

  family          = "${local.mcp_experiments_rest_server_svc_name}-td"
  container_name  = local.mcp_experiments_rest_server_svc_name
  container_image = "${var.mcp_experiments_rest_server_ecr_repo_url}:${var.mcp_experiments_rest_server_tag}"

  # TODO: Set resource requirements appropriately
  task_memory             = 1024
  task_cpu                = 512
  enable_otel_sidecar     = true
  metrics_namespace       = local.mcp_experiments_rest_server_svc_name
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  essential = true

  # Add port mappings if the container exposes any ports
  port_mappings = [
    {
      containerPort = 9102
      hostPort      = 9102
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6060
      hostPort      = 6060
      protocol      = "tcp"
    }
  ]

  map_environment = {
    "ENV" = "prod"
    # Note that we are only using the read-only replicas for FMCSA DB right now.
    # TODO: Use Readonly replica for Nirvana database as well once the underlying
    # data wrappers support it.
    # Nirvana Postgres DB
    "DATABASES_NIRVANA_HOST"     = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"     = "postgres"
    "DATABASES_NIRVANA_PORT"     = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME" = local.postgres_database_readwrite_username
    # FMCSA Postgres DB
    "DATABASES_FMCSAREADONLY_HOST"                      = local.fmcsa_database_address_read
    "DATABASES_FMCSAREADONLY_PORT"                      = local.fmcsa_database_port
    "DATABASES_FMCSAREADONLY_NAME"                      = "postgres"
    "DATABASES_FMCSAREADONLY_USERNAME"                  = local.fmcsa_database_username

    "DATABASES_FMCSAWRITE_HOST"                         = local.fmcsa_database_address_write
    "DATABASES_FMCSAWRITE_PORT"                         = local.fmcsa_database_port
    "DATABASES_FMCSAWRITE_NAME"                         = "postgres"
    "DATABASES_FMCSAWRITE_USERNAME"                     = local.fmcsa_database_username

    "DATABASES_FMCSA_HOST"           = local.fmcsa_database_address_read
    "DATABASES_FMCSA_NAME"           = "postgres"
    "DATABASES_FMCSA_PORT"           = local.fmcsa_database_port
    "DATABASES_FMCSA_USERNAME"       = local.fmcsa_database_username
    # NHTSA SQL Server DB
    "DATABASES_NHTSA_HOST" = local.nhtsa_database_address

    "OTEL_EXPORTER_OTLP_ENDPOINT" = "${local.otel_server_fqdn}:4317"
    "STATSD_RECEIVER_ADDRESS"     = "${local.otel_server_fqdn}:8125"
  }
  map_secrets = {
    "DATABASES_NIRVANA_PASSWORD"      = data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn

    "DATABASES_FMCSA_PASSWORD"         = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAREADONLY_PASSWORD" = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAWRITE_PASSWORD"    = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn

    "DATABASES_NHTSA_PASSWORD"         = data.aws_secretsmanager_secret_version.nhtsa_db_password.arn
    "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"  = data.aws_secretsmanager_secret_version.launchdarkly_api_key.arn
    "LLAMA_CLOUD_API_KEY"              = "${data.aws_secretsmanager_secret_version.llamacloud_secrets.arn}:mcpApiKey::"
    "PRODUCTTOOLS_CLERKCONFIG_SECRETKEY" = data.aws_secretsmanager_secret_version.clerk_secret.arn
    "PRODUCTTOOLS_NARSAPIKEY"             = "${data.aws_secretsmanager_secret_version.nars_secrets.arn}:apiKey::"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.mcp_experiments_rest_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-${local.mcp_experiments_rest_server_svc_name}"
    }
  }

  task_role_arn           = aws_iam_role.mcp_experiments_rest_server.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn


  tags = {
    Environment = terraform.workspace
    Application = local.mcp_experiments_rest_server_svc_name
  }
}

# Create ECS Fargate service
module "mcp_experiments_rest_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = local.mcp_experiments_rest_server_svc_name
  container_name = local.mcp_experiments_rest_server_svc_name
  container_port = 9102

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.mcp_experiments_rest_server_td.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  create_lb_access_sg_rule = true
  lb_access_sg             = module.mcp_experiments_rest_server_alb.access_sg_id
  lb_http_tg_arns          = module.mcp_experiments_rest_server_alb.http_tgs_arns
  lb_http_tg_ports         = module.mcp_experiments_rest_server_alb.http_tgs_ports

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.mcp_experiments_rest_server_svc_name
  }

  # Adding CloudWatch alarms (optional, adjust SNS topic as needed)
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_internal_infra_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_internal_infra_sns_arn,
    }
  }
}

# Create Application Load Balancer
module "mcp_experiments_rest_server_alb" {
  source = "../common-modules/nv_svc_alb"

  name_prefix = local.mcp_experiments_rest_server_svc_name
  vpc_id      = local.default_vpc_id

  # Application Load Balancer
  security_groups = [local.default_sg_id]

  public_subnets = [
    local.public_subnet_ids[0],
    local.public_subnet_ids[1],
    local.public_subnet_ids[2],
  ]

  # Access Control to Application Load Balancer
  http_ports = {
    force_https = {
      listener_port = 80
      host          = "#{host}"
      path          = "/#{path}"
      target_port   = "443"
      protocol      = "HTTPS"
      query         = "#{query}"
      status_code   = "HTTP_301"
    }
  }
  https_ports = {
    force_https = {
      listener_port = 443
    }
  }

  # Target Groups
  target_group_health_check_interval            = 30
  target_group_health_check_path                = "/health"
  target_group_health_check_timeout             = 25
  target_group_health_check_healthy_threshold   = 3
  target_group_health_check_unhealthy_threshold = 3
  target_group_health_check_matcher             = "200,301,302"

  # Certificates
  default_certificate_arn = data.aws_acm_certificate.prod_cert.arn
  ssl_policy              = "ELBSecurityPolicy-2016-08"

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = local.mcp_experiments_rest_server_svc_name
  }
}

# Security group rule to allow traffic from ALB to the service
resource "aws_security_group_rule" "mcp_experiments_rest_server_alb_ingress" {
  type = "ingress"

  security_group_id        = local.default_sg_id
  protocol                 = "-1" # Allow all protocols from ALB SG
  from_port                = 0
  to_port                  = 0
  source_security_group_id = module.mcp_experiments_rest_server_alb.access_sg_id
}

# Create A record in Route53 zone
resource "aws_route53_record" "mcp_experiments_rest_server_a_record" {
  zone_id = data.aws_route53_zone.prod.zone_id
  name    = "mcp-experiments-api.prod.nirvanatech.com"
  type    = "A"

  alias {
    name                   = module.mcp_experiments_rest_server_alb.dns_name
    zone_id                = module.mcp_experiments_rest_server_alb.zone_id
    evaluate_target_health = true
  }
}
