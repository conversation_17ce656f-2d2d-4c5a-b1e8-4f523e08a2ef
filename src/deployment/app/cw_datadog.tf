module "cw_datadog" {
  source = "github.com/nirvanatech/terraform-aws-datadog-metric-stream?ref=1c00f159d685a7e51d22e55751151128c2d8f7ed"

  datadog_api_key           = jsondecode(data.aws_secretsmanager_secret_version.datadog_secret.secret_string)["api-key"]
  datadog_firehose_endpoint = "https://awsmetrics-intake.datadoghq.com/v1/input"
  datadog_metric_stream_filters = [
    {
      namespace = "AWS/ApplicationELB"
      metric_names = [
        "HTTPCode_Target_4XX_Count",
        "HTTPCode_Target_5XX_Count",
        "RequestCount",
        "TargetResponseTime",
        "HTTPCode_ELB_504_Count",
        "HTTPCode_ELB_5XX_Count",
        "TargetConnectionErrorCount"
      ]
    }
  ]
  datadog_metric_statistics_configurations = [
    {
      additional_statistics = ["p99"],
      include_metric = [
        {
          namespace   = "AWS/ApplicationELB"
          metric_name = "TargetResponseTime"
        }
      ]
    }
  ]
}

