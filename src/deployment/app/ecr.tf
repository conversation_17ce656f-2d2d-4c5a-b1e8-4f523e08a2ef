data "aws_secretsmanager_secret" "ghcr_credentials" {
  name = "ecr-pullthroughcache/ghcr.io"
}

data "aws_secretsmanager_secret_version" "ghcr_credentials" {
  secret_id = data.aws_secretsmanager_secret.ghcr_credentials.id
}

import {
  to = aws_ecr_pull_through_cache_rule.public-github
  id = "public-github"
}

resource "aws_ecr_pull_through_cache_rule" "public-github" {
  ecr_repository_prefix = "public-github"
  upstream_registry_url = "ghcr.io"
  credential_arn        = data.aws_secretsmanager_secret_version.ghcr_credentials.arn
}

data "aws_secretsmanager_secret" "docker_hub_credentials" {
  name = "ecr-pullthroughcache/docker-hub"
}

data "aws_secretsmanager_secret_version" "docker_hub_credentials" {
  secret_id = data.aws_secretsmanager_secret.docker_hub_credentials.id
}

import {
  to = aws_ecr_pull_through_cache_rule.public-docker-hub
  id = "public-docker-hub"
}

resource "aws_ecr_pull_through_cache_rule" "public-docker-hub" {
  ecr_repository_prefix = "public-docker-hub"
  upstream_registry_url = "registry-1.docker.io"
  credential_arn        = data.aws_secretsmanager_secret_version.docker_hub_credentials.arn
}

import {
  to = aws_ecr_pull_through_cache_rule.public-ecr
  id = "public-ecr"
}

resource "aws_ecr_pull_through_cache_rule" "public-ecr" {
  ecr_repository_prefix = "public-ecr"
  upstream_registry_url = "public.ecr.aws"
}

locals  {
  docker_hub_pull_through_cache_ecr_repo_url       = "667656038718.dkr.ecr.us-east-2.amazonaws.com/${aws_ecr_pull_through_cache_rule.public-docker-hub.ecr_repository_prefix}"
}
