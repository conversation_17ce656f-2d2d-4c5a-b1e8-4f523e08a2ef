output "api_server_fqdn" {
  description = "FQDN of the API server LB"
  value       = aws_alb.api_server.dns_name
}

output "fmcsa_scraper_fqdn" {
  description = "FQDN of fmcsa_scraper service"
  value       = local.fmcsa_scraper_fqdn
}

output "fmcsa_db_fqdn_read" {
  description = "FQDN of the FMCSA Aurora for postgres read-only instance"
  value       = local.fmcsa_database_address_read
}

output "fmcsa_db_fqdn_write" {
  description = "FQDN of the FMCSA Aurora for  postgres write instance"
  value       = local.fmcsa_database_address_write
}

output "graphql_server_tag" {
  description = "The tag of the graphql image to be used to deploy"
  value       = var.graphql_server_tag
}

output "llmops_server_tag" {
  description = "The tag of the llmops image to be used to deploy"
  value       = var.llmops_server_tag
}

output "pdfgen_server_tag" {
  description = "The tag of the pdfgen image to be used to deploy"
  value       = var.pdfgen_server_tag
}

output "oauth_server_tag" {
  description = "The tag of the oauth image to be used to deploy"
  value       = var.oauth_server_tag
}

output "mvr_cache_server_tag" {
  description = "The tag of the mvr_cache_server image to be used to deploy"
  value       = var.mvr_cache_server_tag
}

output "api_server_tag" {
  description = "The tag of the api_server image to be used to deploy"
  value       = var.api_server_tag
}

output "job_processor_tag" {
  description = "The tag of the api_server image to be used to deploy"
  value       = var.job_processor_tag
}

output "data_infra_job_processor_tag" {
  description = "The tag of the data_infra_job_processor image to be used to deploy"
  value       = var.data_infra_job_processor_tag
}

output "telematics_grpc_server_tag" {
  description = "The tag of the telematics_grpc_server image to be used to deploy"
  value       = var.telematics_grpc_server_tag
}

output "telematics_grpc_server_fqdn" {
  description = "FQDN of gRPC telematics_server service"
  value       = local.telematics_grpc_server_fqdn
}

output "vehicles_grpc_server_tag" {
  description = "The tag of the vehicles_grpc_server image to be used to deploy"
  value       = var.vehicles_grpc_server_tag
}

output "vehicles_grpc_server_fqdn" {
  description = "FQDN of gRPC vehicles_server service"
  value       = local.vehicles_grpc_server_fqdn
}

output "quote_scraper_grpc_server_tag" {
  description = "The tag of the quote_scraper_grpc_server image to be used to deploy"
  value       = var.quote_scraper_grpc_server_tag
}

output "quote_scraper_grpc_server_fqdn" {
  description = "FQDN of gRPC quote_scraper_grpc_server service"
  value       = local.quote_scraper_grpc_server_fqdn
}

output "feature_store_server_tag" {
  description = "The tag of the feature_store_server image to be used to deploy"
  value       = var.feature_store_server_tag
}

output "jobber_monitor_tag" {
  description = "The tag of the jobber_monitor image to be used to deploy"
  value       = var.jobber_monitor_tag
}

output "jobber_monitor_fqdn" {
  description = "FQDN of jobber monitor service"
  value       = local.jobber_monitor_fqdn
}

output "feature_store_server_fqdn" {
  description = "FQDN of feature store service"
  value       = local.feature_store_server_fqdn
}

output "quoting_job_processor_fqdn" {
  description = "FQDN of quoting job processor service"
  value       = local.quoting_job_processor_fqdn
}

output "quoting_job_processor_tag" {
  description = "The tag of quoting job processor to be used to deploy"
  value       = var.quoting_job_processor_tag
}

output "distsem_server_tag" {
  description = "The tag of distsem server to be used to deploy"
  value       = var.distsem_server_tag
}

output "fmcsa_scraper_tag" {
  description = "The tag of the fmcsa scraper image to be used to deploy"
  value       = var.fmcsa_scraper_tag
}

output "db_migrate_lambda_tag" {
  description = "The tag of the db migrate lambda image to be used to deploy"
  value       = var.db_migrate_lambda_tag
}

output "grpc_lambda_tag" {
  description = "The tag of the fmcsa scraper image to be used to deploy"
  value       = var.grpc_lambda_tag
}

output "otel_collector_fqdn" {
  description = "FQDN of the OTel collector service"
  value       = local.otel_server_fqdn
}

output "saferwatch_scraper_tag" {
  description = "The tag of the saferwatch scraper image to be used to deploy"
  value       = var.saferwatch_scraper_tag
}

output "saferwatch_scraper_fqdn" {
  description = "FQDN of the saferwatch scraper service"
  value       = local.saferwatch_scraper_fqdn
}

output "safety_job_processor_tag" {
  description = "The tag of the safety job processor image to be used to deploy"
  value       = var.safety_job_processor_tag
}

output "safety_job_processor_fqdn" {
  description = "FQDN of the safety job processor service"
  value       = local.safety_job_processor_fqdn
}

output "event_job_processor_tag" {
  description = "The tag of the event job processor image to be used to deploy"
  value       = var.event_job_processor_tag
}

output "event_job_processor_fqdn" {
  description = "FQDN of the event job processor service"
  value       = local.event_job_processor_fqdn
}

output "fmcsa_data_provider_grpc_server_tag" {
  description = "The tag of the FMCSA data provider grpc image to be used to deploy"
  value       = var.fmcsa_data_provider_grpc_server_tag
}

output "fmcsa_data_provider_grpc_server_fqdn" {
  description = "FQDN of gRPC FMCSA data provider service"
  value       = local.fmcsa_data_provider_grpc_server_fqdn
}

output "mcp_experiments_rest_server_tag" {
  description = "The tag of the MCP experiments rest server image currently deployed"
  value       = var.mcp_experiments_rest_server_tag
}
