# AWS Region
data "aws_route53_zone" "prod" {
  name         = "prod.nirvanatech.com"
  private_zone = false
}

data "aws_route53_zone" "dev" {
  name         = "dev.nirvanatech.com"
  private_zone = false
}

# ECR repo for api_server images
variable "api_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/api-server-repo"
}

# ECR repo for gql_api_server images
variable "gql_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/gql-api-server-repo"
}

# ECR repo for llmops_server images
variable "llmops_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/llmops-service"
}

# ECR repo for mvr_cache_server images
variable "mvr_cache_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/mvr-cache-server-repo"
}

# ECR repo for mvr_cache_server images
variable "distsem_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/distsem-server-repo"
}

# ECR repo for pdfgen_server images
variable "pdfgen_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/pdfgen-server-repo"
}

# ECR repo for oauth_server images
variable "oauth_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/oauth-server-repo"
}

# ECR repo for fmcsa_scraper images
variable "fmcsa_scraper_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/fmcsa-scraper-repo"
}

# ECR repo for metabase_server images
variable "metabase_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/metabase-server"
}



# ECR repo for job-processor images
variable "job_processor_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/job-processor"
}

# ECR repo for data-infra-job-processor images
variable "data_infra_job_processor_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/data-infra-job-processor"
}

# ECR repo for datascience unified docker images
variable "datascience_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/datascience-repo"
}

# ECR repo for telematics_grpc_server images
variable "telematics_grpc_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/telematics-grpc-server"
}

# ECR repo for vehicles_grpc_server images
variable "vehicles_grpc_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/vehicles-grpc-server"
}
# ECR repo for quote_scraper_grpc_server images
variable "quote_scraper_grpc_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/quote-scraper-server-repo"
}
# ECR repo for feature_store_server images
variable "feature_store_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/feature-store-server"
}

# ECR repo for quoting-job-processor images
variable "quoting_job_processor_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/quoting-job-processor"
}

# ECR repo for db_migrate_lambda images
variable "db_migrate_lambda_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/db_migrate_lambda"
}

# ECR repo for jobber_monitor images
variable "jobber_monitor_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/jobber-monitor"
}

# ECR repo for db_migrate_lambda images
variable "grpc_lambda_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/grpc_lambda"
}

# ECR repo for saferwatch_scraper images
variable "saferwatch_scraper_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/saferwatch-scraper"
}

# ECR repo for safety job processor
variable "safety_job_processor_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/safety-job-processor"
}

# ECR repo for event job processor
variable "event_job_processor_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/event-job-processor"
}

# ECR repo for fmcsa data provider grpc server
variable "fmcsa_data_provider_grpc_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/fmcsa-data-provider-grpc-server"
}

# ECR repo for draft fnol lambda
variable "draft_fnol_lambda_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/draft-fnol-email-streamline"
}

# ECR repo for gorules server
variable "gorules_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/gorules"
}

# ECR repo for mcp experiments rest server
variable "mcp_experiments_rest_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/mcp-experiments-rest-server"
}

# ECR repo for uw_ai_server images
variable "uw_ai_server_ecr_repo_url" {
  type    = string
  default = "667656038718.dkr.ecr.us-east-2.amazonaws.com/uw-ai-server"
}



# SSL cert
data "aws_acm_certificate" "prod_cert" {
  domain      = "prod.nirvanatech.com"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

data "aws_acm_certificate" "dev_cert_ohio" {
  domain      = "dev.nirvanatech.com"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

# Retrieve sendgrid secrets
data "aws_secretsmanager_secret" "sendgrid_api_key" {
  name = "sendgrid-api-key"
}

data "aws_secretsmanager_secret_version" "sendgrid_api_key" {
  secret_id = data.aws_secretsmanager_secret.sendgrid_api_key.id
}

# Retrieve knock secrets
data "aws_secretsmanager_secret" "knock_api_key" {
  name = "knock_api_key"
}

data "aws_secretsmanager_secret_version" "knock_api_key" {
  secret_id = data.aws_secretsmanager_secret.knock_api_key.id
}

data "aws_secretsmanager_secret" "knock_webhook_key" {
  name = "knock_webhook_key"
}

data "aws_secretsmanager_secret_version" "knock_webhook_key" {
  secret_id = data.aws_secretsmanager_secret.knock_webhook_key.id
}

# Retrieve posthog secrets
data "aws_secretsmanager_secret" "posthog_personal_api_key" {
  name = "posthog_personal_api_key"
}

data "aws_secretsmanager_secret_version" "posthog_personal_api_key" {
  secret_id = data.aws_secretsmanager_secret.posthog_personal_api_key.id
}

data "aws_secretsmanager_secret" "posthog_public_api_key" {
  name = "posthog_public_api_key"
}

data "aws_secretsmanager_secret_version" "posthog_public_api_key" {
  secret_id = data.aws_secretsmanager_secret.posthog_public_api_key.id
}

# Retrieve snowflake secrets
data "aws_secretsmanager_secret" "snowflake_pass" {
  name = "snowflake-pass-default"
}

data "aws_secretsmanager_secret" "snapsheet_snowflake_credentials" {
  name = "claims/snapsheet/snowflake-credentials"
}

data "aws_secretsmanager_secret_version" "snapsheet_snowflake_credentials" {
  secret_id = data.aws_secretsmanager_secret.snapsheet_snowflake_credentials.id
}

data "aws_secretsmanager_secret_version" "snowflake_pass" {
  secret_id = data.aws_secretsmanager_secret.snowflake_pass.id
}

# Retrieve dbt cloud secrets
data "aws_secretsmanager_secret" "dbt_cloud_secrets" {
  name = "dbt-cloud-secrets"
}

data "aws_secretsmanager_secret_version" "dbt_cloud_secrets" {
  secret_id = data.aws_secretsmanager_secret.dbt_cloud_secrets.id
}

# Retrieve secrets for singleton stack's nhtsa db
data "aws_secretsmanager_secret" "nhtsa_db_password" {
  name = "prod-production-nhtsa"
}

data "aws_secretsmanager_secret_version" "nhtsa_db_password" {
  secret_id = data.aws_secretsmanager_secret.nhtsa_db_password.id
}

# Retrieve samsara secrets
data "aws_secretsmanager_secret" "samsara_secrets" {
  name = "samsara-app-secrets"
}

data "aws_secretsmanager_secret_version" "samsara_secrets" {
  secret_id = data.aws_secretsmanager_secret.samsara_secrets.id
}

# Retrieve carfax secrets
data "aws_secretsmanager_secret" "carfax_secrets" {
  name = "carfax"
}

data "aws_secretsmanager_secret_version" "carfax_secrets" {
  secret_id = data.aws_secretsmanager_secret.carfax_secrets.id
}

# Retrieve cmt secrets
data "aws_secretsmanager_secret" "cmt_secrets" {
  name = "cmt"
}

data "aws_secretsmanager_secret_version" "cmt_secrets" {
  secret_id = data.aws_secretsmanager_secret.cmt_secrets.id
}

# Retrieve keeptruckin secrets
data "aws_secretsmanager_secret" "keeptruckin_secrets" {
  name = "keeptruckin-app-secrets"
}

data "aws_secretsmanager_secret_version" "keeptruckin_secrets" {
  secret_id = data.aws_secretsmanager_secret.keeptruckin_secrets.id
}

# Retrieve Terminal (https://www.withterminal.com) secrets
data "aws_secretsmanager_secret" "terminal" {
  name = "withterminal"
}

data "aws_secretsmanager_secret_version" "terminal" {
  secret_id = data.aws_secretsmanager_secret.terminal.id
}

# Retrieve heremaps secrets
data "aws_secretsmanager_secret" "heremaps" {
  name = "heremaps"
}

data "aws_secretsmanager_secret_version" "heremaps" {
  secret_id = data.aws_secretsmanager_secret.heremaps.id
}

# Retrieve openmeteo secrets
data "aws_secretsmanager_secret" "openmeteo" {
  name = "openmeteo"
}

data "aws_secretsmanager_secret_version" "openmeteo" {
  secret_id = data.aws_secretsmanager_secret.openmeteo.id
}

# Retrieve RateML gsheets secrets
data "aws_secretsmanager_secret" "rateml_gsheets" {
  name = "rateml-google-sheets-service-account-creds"
}

data "aws_secretsmanager_secret_version" "rateml_gsheets" {
  secret_id = data.aws_secretsmanager_secret.rateml_gsheets.id
}

# Retrieve flatfile secrets
data "aws_secretsmanager_secret" "flatfile_secrets" {
  name = "flatfile-secrets"
}

data "aws_secretsmanager_secret_version" "flatfile_secrets" {
  secret_id = data.aws_secretsmanager_secret.flatfile_secrets.id
}

# Retrieve Verisk secrets
data "aws_secretsmanager_secret" "verisk_secrets" {
  name = "verisk_mvr_secrets"
}

data "aws_secretsmanager_secret_version" "verisk_secrets" {
  secret_id = data.aws_secretsmanager_secret.verisk_secrets.id
}

# Retrieve Salesforce secrets
data "aws_secretsmanager_secret" "salesforce_secrets" {
  name = "salesforce-secrets"
}

data "aws_secretsmanager_secret_version" "salesforce_secrets" {
  secret_id = data.aws_secretsmanager_secret.salesforce_secrets.id
}

# Retrieve Impler secrets
data "aws_secretsmanager_secret" "impler_secrets" {
  name = "impler_secrets"
}

data "aws_secretsmanager_secret_version" "impler_secrets" {
  secret_id = data.aws_secretsmanager_secret.impler_secrets.id
}

# Retrieve LexisNexis secrets
data "aws_secretsmanager_secret" "lexisnexis_secrets" {
  name = "LexisNexis_Secrets"
}

data "aws_secretsmanager_secret_version" "lexisnexis_secrets" {
  secret_id = data.aws_secretsmanager_secret.lexisnexis_secrets.id
}

# Retrieve geocodio secrets
data "aws_secretsmanager_secret" "geocodio_secrets" {
  name = "geocodio-secrets"
}

data "aws_secretsmanager_secret_version" "geocodio_secrets" {
  secret_id = data.aws_secretsmanager_secret.geocodio_secrets.id
}

# Retrieve segment secrets
data "aws_secretsmanager_secret" "segment_secrets" {
  name = "segment-secrets"
}

data "aws_secretsmanager_secret_version" "segment_secrets" {
  secret_id = data.aws_secretsmanager_secret.segment_secrets.id
}

# Retrieve unipdf secrets
data "aws_secretsmanager_secret" "unipdf_secrets" {
  name = "unipdf-secrets"
}

data "aws_secretsmanager_secret_version" "unipdf_secrets" {
  secret_id = data.aws_secretsmanager_secret.unipdf_secrets.id
}

# Retrieve launchdarkly API key
data "aws_secretsmanager_secret" "launchdarkly_api_key" {
  name = "launchdarkly-api-key"
}

data "aws_secretsmanager_secret_version" "launchdarkly_api_key" {
  secret_id = data.aws_secretsmanager_secret.launchdarkly_api_key.id
}

# Retrieve LlamaCloud API key
data "aws_secretsmanager_secret" "llamacloud_secrets" {
  name = "llamacloud_secrets"
}

data "aws_secretsmanager_secret_version" "llamacloud_secrets" {
  secret_id = data.aws_secretsmanager_secret.llamacloud_secrets.id
}

# Retrieve datadog config stored as secrets for otel
data "aws_secretsmanager_secret" "datadog_secret" {
  name = "datadog-api-key"
}

data "aws_secretsmanager_secret_version" "datadog_secret" {
  secret_id = data.aws_secretsmanager_secret.datadog_secret.id
}

# Retrieve oodle secrets for otel
data "aws_secretsmanager_secret" "oodle_secret" {
  name = "oodle-api-key"
}

data "aws_secretsmanager_secret_version" "oodle_secret" {
  secret_id = data.aws_secretsmanager_secret.oodle_secret.id
}

# Pagerduty service routing keys
data "aws_secretsmanager_secret" "pagerduty_secrets" {
  name = "pagerduty-keys"
}

data "aws_secretsmanager_secret_version" "pagerduty_secrets" {
  secret_id = data.aws_secretsmanager_secret.pagerduty_secrets.id
}

# Google Cloud App Config
data "aws_secretsmanager_secret" "google_app_config_secrets" {
  name = "google_app_config_secrets"
}

data "aws_secretsmanager_secret_version" "google_app_config_secrets" {
  secret_id = data.aws_secretsmanager_secret.google_app_config_secrets.id
}

# Redisgraph secret password
data "aws_secretsmanager_secret" "redisgraph_secrets" {
  name = "redisgraph-pass-default"
}

data "aws_secretsmanager_secret_version" "redisgraph_secrets" {
  secret_id = data.aws_secretsmanager_secret.redisgraph_secrets.id
}

# Neo4j secret password
data "aws_secretsmanager_secret" "neo4j_secrets" {
  name = "neo4j-pass-default"
}

data "aws_secretsmanager_secret_version" "neo4j_secrets" {
  secret_id = data.aws_secretsmanager_secret.neo4j_secrets.id
}

# Pibit AI API key for Dev/Prod env
data "aws_secretsmanager_secret" "pibit_ai_secrets" {
  name = "pibit-ai-secrets"
}

data "aws_secretsmanager_secret_version" "pibit_ai_secrets" {
  secret_id = data.aws_secretsmanager_secret.pibit_ai_secrets.id
}

data "aws_secretsmanager_secret" "nars_secrets" {
  name = "nars-secrets"
}

data "aws_secretsmanager_secret_version" "nars_secrets" {
  secret_id = data.aws_secretsmanager_secret.nars_secrets.id
}

data "aws_secretsmanager_secret" "ascend_secrets" {
  name = "ascend-secrets"
}

data "aws_secretsmanager_secret_version" "ascend_secrets" {
  secret_id = data.aws_secretsmanager_secret.ascend_secrets.id
}

data "aws_secretsmanager_secret" "snapsheet_secrets" {
  name = "snapsheet-secrets"
}

data "aws_secretsmanager_secret_version" "snapsheet_secrets" {
  secret_id = data.aws_secretsmanager_secret.snapsheet_secrets.id
}

data "aws_secretsmanager_secret" "workramp_secrets" {
  name = "workramp-secrets"
}

data "aws_secretsmanager_secret_version" "workramp_secrets" {
  secret_id = data.aws_secretsmanager_secret.workramp_secrets.id
}

data "aws_secretsmanager_secret" "hubspot_secrets" {
  name = "hubspot-secrets"
}

data "aws_secretsmanager_secret_version" "hubspot_secrets" {
  secret_id = data.aws_secretsmanager_secret.hubspot_secrets.id
}

data "aws_secretsmanager_secret" "verisk_client_secret" {
  name = "verisk_client_secret"
}

data "aws_secretsmanager_secret_version" "verisk_client_secret" {
  secret_id = data.aws_secretsmanager_secret.verisk_client_secret.id
}

data "aws_secretsmanager_secret" "smart_proxy" {
  name = "smart-proxy"
}

data "aws_secretsmanager_secret_version" "smart_proxy" {
  secret_id = data.aws_secretsmanager_secret.smart_proxy.id
}

# Retrieve Senture SFTP password
data "aws_secretsmanager_secret" "senture_sftp_pass" {
  name = "senture_sftp_pass"
}

data "aws_secretsmanager_secret_version" "senture_sftp_pass" {
  secret_id = data.aws_secretsmanager_secret.senture_sftp_pass.id
}

# Retrieve SaferWatch Scraper User Password
data "aws_secretsmanager_secret" "saferwatch_user_pass" {
  name = "saferwatch_user_pass"
}

data "aws_secretsmanager_secret_version" "saferwatch_user_pass" {
  secret_id = data.aws_secretsmanager_secret.saferwatch_user_pass.id
}

data "aws_secretsmanager_secret" "slack_events_jobber" {
  name = "slack-events-jobber"
}

data "aws_secretsmanager_secret_version" "slack_events_jobber" {
  secret_id = data.aws_secretsmanager_secret.slack_events_jobber.id
}

# Retrieve Jira secrets
data "aws_secretsmanager_secret" "jira_secrets" {
  name = "jira-secrets"
}

data "aws_secretsmanager_secret_version" "jira_secrets" {
  secret_id = data.aws_secretsmanager_secret.jira_secrets.id
}

# Retrieve Twilio secrets
data "aws_secretsmanager_secret" "twilio_secrets" {
  name = "twilio-secrets"
}

data "aws_secretsmanager_secret_version" "twilio_secrets" {
  secret_id = data.aws_secretsmanager_secret.twilio_secrets.id
}

data "aws_secretsmanager_secret" "openai_secrets" {
  name = "openai_secrets"
}

data "aws_secretsmanager_secret_version" "openai_secrets" {
  secret_id = data.aws_secretsmanager_secret.openai_secrets.id
}

data "aws_secretsmanager_secret" "uw_ai_secrets" {
  name = "uw_ai_secrets"
}

data "aws_secretsmanager_secret_version" "uw_ai_secrets" {
  secret_id = data.aws_secretsmanager_secret.uw_ai_secrets.id
}

data "aws_secretsmanager_secret" "clerk_secret" {
  name = "clerk-secret"
}

data "aws_secretsmanager_secret_version" "clerk_secret" {
  secret_id = data.aws_secretsmanager_secret.clerk_secret.id
}

data "aws_secretsmanager_secret" "langfuse_credentials" {
  name = "langfuse_credentials"
}

data "aws_secretsmanager_secret_version" "langfuse_credentials" {
  secret_id = data.aws_secretsmanager_secret.langfuse_credentials.id
}

data "aws_secretsmanager_secret" "otel_server" {
  name = "otel-server"
}

data "aws_secretsmanager_secret_version" "otel_server" {
  secret_id = data.aws_secretsmanager_secret.otel_server.id
}

data "aws_secretsmanager_secret" "openmeter" {
  name = "openmeter"
}

data "aws_secretsmanager_secret_version" "openmeter" {
  secret_id = data.aws_secretsmanager_secret.openmeter.id
}

data "aws_secretsmanager_secret" "draft_fnol_lambda_credentials" {
  name = "draft_fnol_lambda_credentials"
}

data "aws_secretsmanager_secret_version" "draft_fnol_lambda_credentials" {
  secret_id = data.aws_secretsmanager_secret.draft_fnol_lambda_credentials.id
}

# App DB password for migrator role, created through cdktf
data "aws_secretsmanager_secret" "app_db_migrator_password" {
  name = "app-db-migrator-password"
}

data "aws_secretsmanager_secret_version" "app_db_migrator_password" {
  secret_id = data.aws_secretsmanager_secret.app_db_migrator_password.id
}

data "aws_secretsmanager_secret" "app_db_readonly_password" {
  name = "app-db-readonly-password"
}

data "aws_secretsmanager_secret_version" "app_db_readonly_password" {
  secret_id = data.aws_secretsmanager_secret.app_db_readonly_password.id
}

data "aws_secretsmanager_secret" "app_db_readwrite_password" {
  name = "app-db-readwrite-password"
}

data "aws_secretsmanager_secret_version" "app_db_readwrite_password" {
  secret_id = data.aws_secretsmanager_secret.app_db_readwrite_password.id
}

data "aws_secretsmanager_secret" "app_db_ds_user_password" {
  name = "app-db-ds-user-password"
}

data "aws_secretsmanager_secret_version" "app_db_ds_user_password" {
  secret_id = data.aws_secretsmanager_secret.app_db_ds_user_password.id
}

// S3 buckets
data "aws_s3_bucket" "quoting" {
  bucket = "nirvana-quoting"
}

data "aws_s3_bucket" "claims" {
  bucket = "nirvana-claims"
}

data "aws_s3_bucket" "draft-fnol-attachments" {
  bucket = "reportaclaim-email-attachments"
}

data "aws_s3_bucket" "draft-fnol-monitoring" {
  bucket = "reportaclaim-email-monitoring"
}

data "aws_s3_bucket" "mvr_cache" {
  bucket = "nirvana-mvr-cache"
}

data "aws_s3_bucket" "lexisnexis_attract_cache" {
  bucket = "nirvana-lexisnexis-attract-cache"
}

data "aws_s3_bucket" "lexisnexis_ncf_cache" {
  bucket = "nirvana-lexisnexis-ncf-cache"
}

data "aws_s3_bucket" "pdfgen" {
  bucket = "nirvana-pdfgen"
}

data "aws_s3_bucket" "rateml_artifacts" {
  bucket = "nirvana-rateml-artifacts"
}

data "aws_s3_bucket" "data_scratch" {
  bucket = "nirvana-data-scratch"
}

data "aws_s3_bucket" "go-service-profiles" {
  bucket = "nirvana-go-service-profiles"
}

data "aws_s3_bucket" "metaflow-artifacts" {
  bucket = "metaflow-s3-o70z4mdi"
}


// KMS Keys
data "aws_kms_key" "metaflow_artifact_store_key" {
  key_id = "3b4ca9c8-3ef1-44e4-a73a-e4efd26ed7f6"
}

// FMCSA Public files
data "aws_s3_bucket" "fmcsa_public_files" {
  bucket = "nirvana-fmcsa-public-files"
}

// FMCSA Private files
data "aws_s3_bucket" "fmcsa_sftp_downloads" {
  bucket = "nirvana-fmcsa-sftp-downloads"
}

// Snowflake stage files
data "aws_s3_bucket" "snowflake_stage" {
  bucket = "nirvana-snowflake-stage"
}

// Jobber Schedules bucket
data "aws_s3_bucket" "jobber-schedules" {
  bucket = "jobber-schedules"
}

data "aws_s3_bucket" "reporting" {
  bucket = "nirvana-reports"
}

data "aws_s3_bucket" "cost-and-usage-reports" {
  bucket = "nirvana-cost-and-usage-reports"
}

data "aws_s3_bucket" "nirvana_ds_dumps" {
  bucket = "nirvana-data-science-dumps"
}
