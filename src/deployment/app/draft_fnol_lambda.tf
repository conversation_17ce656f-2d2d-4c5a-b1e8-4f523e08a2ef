variable "draft_fnol_lambda_tag" {
  type        = string
  description = "The tag of the draft_fnol_lambda image to be used to deploy"
  default     = "" # Doesn't matter since we ignore changes to the image_uri. See lifecycle block below.
}

resource "aws_security_group" "draft_fnol_lambda_sg" {
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

import {
  id = "draft-fnol-email-streamline-py"
  to = aws_lambda_function.draft_fnol_lambda
}

resource "aws_lambda_function" "draft_fnol_lambda" {
  function_name = "draft-fnol-email-streamline-py"
  role          = "arn:aws:iam::667656038718:role/service-role/draft-fnol-email-streamline-py-role-rcswuccq"

  environment {
    variables = {
      "API_SERVER_LOGIN_CREDENTIALS" = data.aws_secretsmanager_secret_version.draft_fnol_lambda_credentials.arn
      "API_SERVER_TIMEOUT_SECONDS" = 90
      "API_SERVER_URL" = "https://api.prod.nirvanatech.com"
      "ATTACHMENTS_S3_BUCKET_NAME" = data.aws_s3_bucket.draft-fnol-attachments.id
      "MONITORING_S3_BUCKET_NAME" = data.aws_s3_bucket.draft-fnol-monitoring.id
      "LOG_LEVEL" = "DEBUG"
      "REPLY_TO_EMAIL" = "<EMAIL>"
      "SUPPORT_APP_URL" = "https://support.nirvanatech.com"
    }
  }

  package_type = "Image"
  image_uri    = "${var.draft_fnol_lambda_ecr_repo_url}:${var.draft_fnol_lambda_tag}"

  ephemeral_storage {
    size = 4096
  }
  memory_size = 4096
  timeout     = 300

  publish = false
  vpc_config {
    subnet_ids = [
      local.private_subnet_ids[0],
      local.private_subnet_ids[1],
      local.private_subnet_ids[2],
    ]
    security_group_ids = [
      aws_security_group.draft_fnol_lambda_sg.id,
    ]
  }

  logging_config {
    log_format = "JSON"
    application_log_level = "INFO"
    system_log_level = "INFO"
  }

  tags = {
    Environment = terraform.workspace
  }

  lifecycle {
    ignore_changes = [
      image_uri,
    ]
  }
}
