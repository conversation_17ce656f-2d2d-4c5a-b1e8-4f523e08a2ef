# Create log group for service
resource "aws_cloudwatch_log_group" "api_server" {
  name_prefix = "${terraform.workspace}-api-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "api_server"
  }
}

# Create IAM role and policy for API Server ECS tasks to access other AWS services
# NOTE: quoting_job_processor also uses the same policy for legacy reasons.
# quoting_job_processor.tf already has a TODO to move to its own policy.
# Until that's addressed, this needs to be accounted for when making changes to
# this policy.
data "aws_iam_policy_document" "api_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt",
    ]
    resources = [
      aws_kms_key.telematics_api_key_credentials.arn,
      aws_kms_key.telematics_consent_user_request_metadata.arn,
      aws_kms_key.terminal_user_credentials.arn,
      aws_kms_key.tsp_webhook_secret_keys_credentials.arn,
      aws_kms_key.user_ssn.arn,
      aws_kms_key.user_ssn_last_four.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.quoting.arn,
      data.aws_s3_bucket.pdfgen.arn,
      data.aws_s3_bucket.rateml_artifacts.arn,
      aws_s3_bucket.forms.arn,
      aws_s3_bucket.underwriting.arn,
      data.aws_s3_bucket.data_scratch.arn,
      data.aws_s3_bucket.go-service-profiles.arn,
      aws_s3_bucket.billing.arn,
      aws_s3_bucket.data_context_store.arn,
      data.aws_s3_bucket.claims.arn,
      data.aws_s3_bucket.reporting.arn,
      data.aws_s3_bucket.jobber-schedules.arn,
      aws_s3_bucket.blob_store.arn,
      aws_s3_bucket.pricing_simulations.arn,
      data.aws_s3_bucket.draft-fnol-attachments.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.quoting.arn}/*",
      "${data.aws_s3_bucket.pdfgen.arn}/*",
      "${data.aws_s3_bucket.rateml_artifacts.arn}/*",
      "${aws_s3_bucket.forms.arn}/*",
      "${aws_s3_bucket.underwriting.arn}/*",
      "${data.aws_s3_bucket.data_scratch.arn}/*",
      "${data.aws_s3_bucket.go-service-profiles.arn}/*",
      "${aws_s3_bucket.billing.arn}/*",
      "${aws_s3_bucket.data_context_store.arn}/*",
      "${data.aws_s3_bucket.claims.arn}/*",
      "${aws_s3_bucket.api_artifacts.arn}/*",
      "${data.aws_s3_bucket.reporting.arn}/*",
      "${data.aws_s3_bucket.jobber-schedules.arn}/*",
      "${aws_s3_bucket.blob_store.arn}/*",
      "${aws_s3_bucket.pricing_simulations.arn}/*",
      "${data.aws_s3_bucket.draft-fnol-attachments.arn}/*"
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "ecs:GetTaskProtection",
      "ecs:UpdateTaskProtection",
    ]
    resources = [
      "arn:aws:ecs:*:667656038718:task/*/*"
    ]
  }
}

resource "aws_iam_policy" "api_server_tasks_policy" {
  policy = data.aws_iam_policy_document.api_server_tasks_policy_doc.json
}

resource "aws_iam_role" "api_server_task_role" {
  name               = "api_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "apiServerTaskRole_policy" {
  role       = aws_iam_role.api_server_task_role.name
  policy_arn = aws_iam_policy.api_server_tasks_policy.arn
}

# Create an ECS task
# The following environment settings have been retained (as a backward compatibility)
# measure from NHTSA config port (PR #1529)
# TODO: remove "SEGMENT_API_KEY" variable
# TODO: remove POSTGRES_* variables
module "api_server_td" {
  source = "../common-modules/ecs_task_definition_oodle_log_poc"

  family          = "api_server"
  container_name  = "api_server"
  container_image = "${var.api_server_ecr_repo_url}:${var.api_server_tag}"

  task_memory         = 16384
  task_cpu            = 4096
  essential           = true
  enable_otel_sidecar = true
  metrics_namespace   = "api_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"
  cloudwatch_log_group = aws_cloudwatch_log_group.api_server.name
  cloudwatch_log_stream = "awslogs-api-server/api_server"

  port_mappings = [
    {
      containerPort = 8080
      hostPort      = 8080
      protocol      = "tcp"
    },
    {
      containerPort = 6060
      hostPort      = 6060
      protocol      = "tcp"
    }
  ]

  map_environment = {
    "ENV"                                               = "prod"
    "POSTGRES_ADDRESS"                                  = local.postgres_database_address
    "POSTGRES_DB_NAME"                                  = "postgres"
    "POSTGRES_PORT"                                     = local.postgres_database_port
    "POSTGRES_USERNAME"                                 = local.postgres_database_readwrite_username
    "DATABASES_NIRVANA_HOST"                            = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"                            = "postgres"
    "DATABASES_NIRVANA_PORT"                            = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"                        = local.postgres_database_readwrite_username

    "DATABASES_FMCSA_HOST"                              = local.fmcsa_database_address_read
    "DATABASES_FMCSA_PORT"                              = local.fmcsa_database_port
    "DATABASES_FMCSA_NAME"                              = "postgres"
    "DATABASES_FMCSA_USERNAME"                          = local.fmcsa_database_username

    "DATABASES_FMCSAWRITE_HOST"                         = local.fmcsa_database_address_write
    "DATABASES_FMCSAWRITE_PORT"                         = local.fmcsa_database_port
    "DATABASES_FMCSAWRITE_NAME"                         = "postgres"
    "DATABASES_FMCSAWRITE_USERNAME"                     = local.fmcsa_database_username

    "DATABASES_FMCSAREADONLY_HOST"                      = local.fmcsa_database_address_read
    "DATABASES_FMCSAREADONLY_PORT"                      = local.fmcsa_database_port
    "DATABASES_FMCSAREADONLY_NAME"                      = "postgres"
    "DATABASES_FMCSAREADONLY_USERNAME"                  = local.fmcsa_database_username

    "DATABASES_NHTSA_HOST"                              = local.nhtsa_database_address
    "DATABASES_DS_HOST"                                 = local.postgres_database_address
    "DATABASES_DS_NAME"                                 = local.ds_database_name
    "DATABASES_DS_PORT"                                 = local.postgres_database_port
    "DATABASES_DS_USERNAME"                             = local.ds_database_username
    "MVR_CACHE_SERVER_PORT"                             = "58727"
    "MVR_CACHE_SERVER_ADDRESS"                          = local.mvr_cache_server_fqdn
    "MIGRATION_VERSION"                                 = "-2"
    "PDFGEN_SERVER_ADDRESS"                             = "${local.pdfgen_server_fqdn}:33435"
    "OTEL_EXPORTER_OTLP_ENDPOINT"                       = "${local.otel_server_fqdn}:4317"
    "STATSD_RECEIVER_ADDRESS"                           = "${local.otel_server_fqdn}:8125"

    "PRODUCTTOOLS_SNAPSHEET_SANDBOXPUBLICKEY"           = "nirvana_api"
    "PRODUCTTOOLS_SNAPSHEET_PRODUCTIONPUBLICKEY"        = "nirvana_us_api"
  }

  map_secrets = {
    "DATABASES_FMCSA_PASSWORD"                          = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAWRITE_PASSWORD"                     = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAREADONLY_PASSWORD"                  = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn

    "DATABASES_NHTSA_PASSWORD"                          = data.aws_secretsmanager_secret_version.nhtsa_db_password.arn
    "DATABASES_NIRVANA_PASSWORD"                        = data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn
    "DATABASES_DS_PASSWORD"                             = data.aws_secretsmanager_secret_version.app_db_ds_user_password.arn
    "DATABASES_SNOWFLAKE_USERS_SNAPSHEET_PRIVATEKEY"    = "${data.aws_secretsmanager_secret_version.snapsheet_snowflake_credentials.arn}:privateKey::"

    "FLATFILE_KEY"                                      = "${data.aws_secretsmanager_secret_version.flatfile_secrets.arn}:flatfile_key::"
    "FLATFILE_SECRET"                                   = "${data.aws_secretsmanager_secret_version.flatfile_secrets.arn}:flatfile_secret::"

    "GEOCODIO_API_KEY"                                  = "${data.aws_secretsmanager_secret_version.geocodio_secrets.arn}:geocodio_api_key::"

    "PAGERDUTY_AGENTS_ROUTINGKEY"                       = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_AGENTS_ROUTINGKEY::"
    "PAGERDUTY_INSURANCE_ENG_TEST_ROUTINGKEY"           = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_INSURANCE_ENG_TEST_ROUTINGKEY::"
    "PAGERDUTY_NON_FLEET_ROUTINGKEY"                    = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_NON_FLEET_ROUTINGKEY::"
    "PAGERDUTY_NON_FLEET_TEST_ROUTINGKEY"               = "${data.aws_secretsmanager_secret_version.pagerduty_secrets.arn}:PAGERDUTY_NON_FLEET_TEST_ROUTINGKEY::"

    "POSTGRES_PASSWORD"                                 = data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn

    "PRODUCTTOOLS_ASCEND_APIKEY"                        = "${data.aws_secretsmanager_secret_version.ascend_secrets.arn}:prodApiKey::"
    "PRODUCTTOOLS_ASCEND_SANDBOXAPIKEY"                 = "${data.aws_secretsmanager_secret_version.ascend_secrets.arn}:sandboxApiKey::"
    "PRODUCTTOOLS_GOOGLE_APP_CONFIG_CLIENT_ID"          = "${data.aws_secretsmanager_secret_version.google_app_config_secrets.arn}:CLIENT_ID::"
    "PRODUCTTOOLS_GOOGLE_APP_CONFIG_CLIENT_SECRET"      = "${data.aws_secretsmanager_secret_version.google_app_config_secrets.arn}:CLIENT_SECRET::"
    "PRODUCTTOOLS_HUBSPPOT_JWT_SECRET"                  = "${data.aws_secretsmanager_secret_version.hubspot_secrets.arn}:prod_jwt_secret::"
    "PRODUCTTOOLS_IMPLER_ACCESS_TOKEN"                  = "${data.aws_secretsmanager_secret_version.impler_secrets.arn}:access_token::"
    "PRODUCTTOOLS_JIRA_API_TOKEN"                       = "${data.aws_secretsmanager_secret_version.jira_secrets.arn}:jira-api-token::"
    "PRODUCTTOOLS_TWILIO_ACCOUNT_SID"                   = "${data.aws_secretsmanager_secret_version.twilio_secrets.arn}:twilio-account-sid::"
    "PRODUCTTOOLS_TWILIO_AUTH_TOKEN"                    = "${data.aws_secretsmanager_secret_version.twilio_secrets.arn}:twilio-auth-token::"
    "PRODUCTTOOLS_TWILIO_BASE_URL"                      = "${data.aws_secretsmanager_secret_version.twilio_secrets.arn}:twilio-base-url::"
    "PRODUCTTOOLS_KNOCK_WEBHOOK_KEY"                    = "${data.aws_secretsmanager_secret_version.knock_webhook_key.arn}:knock_webhook_key::"
    "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"                   = data.aws_secretsmanager_secret_version.launchdarkly_api_key.arn
    "PRODUCTTOOLS_CLERKCONFIG_SECRETKEY"              = data.aws_secretsmanager_secret_version.clerk_secret.arn
    "PRODUCTTOOLS_NARSAPIKEY"                           = "${data.aws_secretsmanager_secret_version.nars_secrets.arn}:apiKey::"
    "PRODUCTTOOLS_NARSTESTAPIKEY"                       = "${data.aws_secretsmanager_secret_version.nars_secrets.arn}:testApiKey::"
    "PRODUCTTOOLS_OPENAI_API_KEY"                       = "${data.aws_secretsmanager_secret_version.openai_secrets.arn}:apiKey::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_CLIENT_ID"          = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-client-id::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_CLIENT_SECRET"      = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-client-secret::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_PASSWORD"           = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-password::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_CLIENT_ID"     = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-client-id-test::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_CLIENT_SECRET" = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-client-secret-test::"
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_PASSWORD"      = "${data.aws_secretsmanager_secret_version.salesforce_secrets.arn}:salesforce-password-test::"
    "PRODUCTTOOLS_SEGMENTAPIKEY"                        = "${data.aws_secretsmanager_secret_version.segment_secrets.arn}:segment_api_key_prod::"

    "PRODUCTTOOLS_SNAPSHEET_SANDBOXSECRET"              = "${data.aws_secretsmanager_secret_version.snapsheet_secrets.arn}:sandboxSecret::"
    "PRODUCTTOOLS_SNAPSHEET_SANDBOXHOST"                = "${data.aws_secretsmanager_secret_version.snapsheet_secrets.arn}:sandboxHost::"
    "PRODUCTTOOLS_SNAPSHEET_PRODUCTIONSECRET"           = "${data.aws_secretsmanager_secret_version.snapsheet_secrets.arn}:productionSecret::"
    "PRODUCTTOOLS_SNAPSHEET_PRODUCTIONHOST"             = "${data.aws_secretsmanager_secret_version.snapsheet_secrets.arn}:productionHost::"

    "PRODUCTTOOLS_UNIPDFAPIKEY"                         = "${data.aws_secretsmanager_secret_version.unipdf_secrets.arn}:unipdf_api_key::"
    "PRODUCTTOOLS_WORKRAMP_API_KEY"                     = "${data.aws_secretsmanager_secret_version.workramp_secrets.arn}:prod_api_key::"
    "PRODUCTTOOLS_WORKRAMP_JWT_SECRET"                  = "${data.aws_secretsmanager_secret_version.workramp_secrets.arn}:prod_jwt_secret::"

    "RATEML_GSHEETS_CLIENT_EMAIL"                       = "${data.aws_secretsmanager_secret_version.rateml_gsheets.arn}:client_email::"
    "RATEML_GSHEETS_PRIVATE_KEY_BASE64"                 = "${data.aws_secretsmanager_secret_version.rateml_gsheets.arn}:private_key_base64::"
    "RATEML_GSHEETS_PRIVATE_KEY_ID"                     = "${data.aws_secretsmanager_secret_version.rateml_gsheets.arn}:private_key_id::"

    "SEGMENT_API_KEY"                                   = "${data.aws_secretsmanager_secret_version.segment_secrets.arn}:segment_api_key_prod::"
    "SENDGRID_API_KEY"                                  = "${data.aws_secretsmanager_secret_version.sendgrid_api_key.arn}:sendgrid_api_key::"

    "TELEMATICS_CREDENTIALS_TERMINAL_PUBLISHABLEKEY"    = "${data.aws_secretsmanager_secret_version.terminal.arn}:prod_publishable_key::"
    "TELEMATICS_CREDENTIALS_TERMINAL_SECRETKEY"         = "${data.aws_secretsmanager_secret_version.terminal.arn}:prod_secret_key::"
  }

  log_configuration = {
    logDriver = "awsfirelens"
    options = {}
  }

  depends_on = [aws_cloudwatch_log_group.api_server] // todo: handle this

  task_role_arn           = aws_iam_role.api_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Application = "api-server"
  }
}

# Create ECS service
resource "aws_ecs_service" "api_server_service" {
  name            = "${terraform.workspace}-api_server_service"
  cluster         = local.app_cluster_id
  task_definition = module.api_server_td.arn
  launch_type     = "FARGATE"
  desired_count   = 1

  network_configuration {
    subnets          = [local.private_subnet_ids[0], local.private_subnet_ids[1], local.private_subnet_ids[2]]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.api_server.arn
    container_name   = module.api_server_td.family
    container_port   = 8080
  }

  service_registries {
    registry_arn = aws_service_discovery_service.api_server.arn
  }

  tags = {
    Application = "api-server"
  }
  propagate_tags = "TASK_DEFINITION"
}

resource "aws_service_discovery_service" "api_server" {
  name = "api_server"
  dns_config {
    namespace_id = local.app_cluster_dns_id
    dns_records {
      ttl  = 10
      type = "A"
    }
    routing_policy = "MULTIVALUE"
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

# Create a load balancer
resource "aws_alb" "api_server" {
  name               = "api-server-alb"
  load_balancer_type = "application"
  # This must be connected to public subnets
  subnets = [
    local.public_subnet_ids[0],
    local.public_subnet_ids[1],
    local.public_subnet_ids[2],
  ]
  security_groups = [aws_security_group.api_server_alb_sg.id]
  idle_timeout    = 120
}

# Creating a security group for the load balancer
resource "aws_security_group" "api_server_alb_sg" {
  ingress {
    from_port   = 80 # Allowing HTTP traffic
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Allowing traffic in from all sources
  }

  ingress {
    from_port   = 443 # Allowing HTTPS traffic
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Allowing traffic in from all sources
  }

  egress {
    from_port   = 0             # Allowing any incoming port
    to_port     = 0             # Allowing any outgoing port
    protocol    = "-1"          # Allowing any outgoing protocol
    cidr_blocks = ["0.0.0.0/0"] # Allowing traffic out to all IP addresses
  }
}

# Create target group and listener for LB
resource "aws_lb_target_group" "api_server" {
  name        = "api-server-alb-target-group"
  port        = 80
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = local.default_vpc_id
  health_check {
    matcher             = "200,301,302"
    path                = "/health"
    healthy_threshold   = 3
    interval            = 30
    unhealthy_threshold = 3
    timeout             = 25
  }
}

# Redirect HTTP traffic to HTTPS
resource "aws_lb_listener" "api_server_http" {
  load_balancer_arn = aws_alb.api_server.arn
  port              = "80"
  protocol          = "HTTP"
  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "api_server_https" {
  load_balancer_arn = aws_alb.api_server.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = data.aws_acm_certificate.prod_cert.arn
  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.api_server.arn
  }
}

resource "aws_lb_listener_rule" "snapsheet_routes_allow" {
  listener_arn = aws_lb_listener.api_server_https.arn
  priority     = 99
  tags         = {
    Name = "snapsheet-allow"
  }

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.api_server.arn
  }

  condition {
    source_ip {
      values = ["*************/32"]
    }
  }

  condition {
    path_pattern {
      values = ["/snapsheet/*"]
    }
  }
}


resource "aws_lb_listener_rule" "snapsheet_routes_deny" {
  listener_arn = aws_lb_listener.api_server_https.arn
  priority     = 100
  tags         = {
    Name = "snapsheet-deny"
  }

  action {
    type             = "fixed-response"

    fixed_response {
      content_type = "application/json"
      status_code  = "403"
    }
  }

  condition {
    source_ip {
      values = ["*************/32"]
    }
  }

  condition {
    path_pattern {
      values = ["*"]
    }
  }
}


resource "aws_lb_listener_rule" "non_snapsheet_source_snapsheet_routes_deny" {
  listener_arn = aws_lb_listener.api_server_https.arn
  priority     = 101
  tags         = {
    Name = "non-snapsheet-source-deny"
  }

  action {
    type             = "fixed-response"

    fixed_response {
      content_type = "application/json"
      status_code  = "403"
    }
  }

  condition {
    path_pattern {
      values = ["/snapsheet/*"]
    }
  }
}

# Create A record in Route53 zone
resource "aws_route53_record" "api_server_a_record" {
  zone_id = data.aws_route53_zone.prod.zone_id
  name    = "api.prod.nirvanatech.com"
  type    = "A"

  alias {
    name                   = aws_alb.api_server.dns_name
    zone_id                = aws_alb.api_server.zone_id
    evaluate_target_health = true
  }
}
