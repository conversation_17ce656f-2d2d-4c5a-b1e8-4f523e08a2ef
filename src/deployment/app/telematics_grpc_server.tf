# Create log group for service
resource "aws_cloudwatch_log_group" "telematics_grpc_server" {
  name_prefix = "${terraform.workspace}-telematics-grpc-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "telematics_grpc_server"
  }
}

# Create IAM role and policy for Telematics gRPC Server ECS tasks to access other AWS services
data "aws_iam_policy_document" "telematics_grpc_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt",
    ]
    resources = [
      aws_kms_key.telematics_api_key_credentials.arn,
      aws_kms_key.telematics_consent_user_request_metadata.arn,
      aws_kms_key.terminal_user_credentials.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      aws_s3_bucket.telematics.arn,
      data.aws_s3_bucket.go-service-profiles.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${aws_s3_bucket.telematics.arn}/*",
      "${data.aws_s3_bucket.go-service-profiles.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "telematics_grpc_server_tasks_policy" {
  policy = data.aws_iam_policy_document.telematics_grpc_server_tasks_policy_doc.json
}

resource "aws_iam_role" "telematics_grpc_server_task_role" {
  name               = "telematics_grpc_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "telematicsGRPCServerTaskRole_policy" {
  role       = aws_iam_role.telematics_grpc_server_task_role.name
  policy_arn = aws_iam_policy.telematics_grpc_server_tasks_policy.arn
}

# Create task definition
module "telematics_grpc_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "telematics-grpc-server-td"
  container_name = "telematics-grpc-server"

  # TODO: set image tag with a variable (PR #1676)
  container_image  = "${var.telematics_grpc_server_ecr_repo_url}:${var.telematics_grpc_server_tag}"
  task_memory = 2048 # 2 GB RAM
  task_cpu    = 1024  # 1 vCPU
  essential        = true
  enable_otel_sidecar = true
  metrics_namespace   = "telematics_grpc_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 9091
      hostPort      = 9091
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6064
      hostPort      = 6064
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                                       = "prod"
    "DATABASES_NIRVANA_HOST"                    = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"                    = "postgres"
    "DATABASES_NIRVANA_PORT"                    = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"                = local.postgres_database_readwrite_username
    "DATABASES_NIRVANA_PASSWORD"                = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    "DATABASES_NHTSA_HOST"                      = local.nhtsa_database_address
    "DATABASES_NHTSA_PASSWORD"                  = data.aws_secretsmanager_secret_version.nhtsa_db_password.secret_string
    "TELEMATICS_CREDENTIALS_TERMINAL_SECRETKEY" = jsondecode(data.aws_secretsmanager_secret_version.terminal.secret_string)["prod_secret_key"]
    "PRODUCTTOOLS_SEGMENTAPIKEY"                = jsondecode(data.aws_secretsmanager_secret_version.segment_secrets.secret_string)["segment_api_key_prod"]
    "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"           = data.aws_secretsmanager_secret_version.launchdarkly_api_key.secret_string
    "STATSD_RECEIVER_ADDRESS"                   = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT"               = "${local.otel_server_fqdn}:4317"
    "SNOWFLAKE_DS_ACCOUNT_NAME"                 = local.snowflake_account_name
    "SNOWFLAKE_DS_USER_NAME"                    = local.snowflake_datascience_user
    "SNOWFLAKE_DS_PASSWORD"                     = jsondecode(data.aws_secretsmanager_secret_version.snowflake_pass.secret_string)["datascience_pass"]
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.telematics_grpc_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-telematics-grpc-server"
    }
  }

  task_role_arn           = aws_iam_role.telematics_grpc_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "telematics-grpc-server"
  }
}

# Create ECS Fargate service
module "telematics_grpc_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "telematics-grpc-server"
  container_name = "telematics-grpc-server"
  container_port = 9091

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name


  task_definition_arn = module.telematics_grpc_server_td.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "telematics-grpc-server"
  }
}

locals {
  telematics_grpc_server_fqdn = "${module.telematics_grpc_server_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}
