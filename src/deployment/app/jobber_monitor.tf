# Create log group for service
resource "aws_cloudwatch_log_group" "jobber_monitor" {
  name = "${terraform.workspace}-jobber-monitor-logs"

  tags = {
    Environment = terraform.workspace
    Application = "jobber-monitor"
  }
}

# Create task definition
module "td_jobber_monitor" {
  source = "../common-modules/ecs_task_definition"

  family         = "jobber_monitor-td"
  container_name = "jobber_monitor"

  container_image  = "${var.jobber_monitor_ecr_repo_url}:${var.jobber_monitor_tag}"
  task_memory = 1024
  task_cpu    = 512

  essential = true
  enable_otel_sidecar = true
  metrics_namespace   = "jobber_monitor"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 56666
      hostPort      = 56666
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6060
      hostPort      = 6060
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                         = "prod"
    "DATABASES_NIRVANA_HOST"      = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"      = "postgres"
    "DATABASES_NIRVANA_PORT"      = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"  = local.postgres_database_readwrite_username
    "DATABASES_NIRVANA_PASSWORD"  = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    "STATSD_RECEIVER_ADDRESS"     = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT" = "${local.otel_server_fqdn}:4317"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.jobber_monitor.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-jobber-monitor"
    }
  }

  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "jobber_monitor"
  }
}

# Create ECS Fargate service
module "fg_service_jobber_monitor" {
  source = "../common-modules/nv_fg_svc"

  name_prefix = "jobber-monitor"

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.jobber_singletons_cluster_arn
  ecs_cluster_name = local.jobber_singletons_cluster_name

  task_definition_arn                = module.td_jobber_monitor.arn
  desired_count                      = 1
  deployment_maximum_percent         = 100
  deployment_minimum_healthy_percent = 0


  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "jobber_monitor"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_internal_infra_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_internal_infra_sns_arn,
    }
  }
}

locals {
  jobber_monitor_fqdn = "${module.fg_service_jobber_monitor.discovery_service_name}.${local.app_cluster_dns_name}"
}
