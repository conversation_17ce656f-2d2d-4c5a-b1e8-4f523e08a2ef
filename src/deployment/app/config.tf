terraform {
  required_version = "1.7.5"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.41.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.0"
    }
  }

  backend "s3" {
    bucket = "cloud.nirvanatech.com"
    key    = "private/deployment/terraform/app/state"
    region = "us-east-2"
  }

}

locals {
  elb_ssl_policy = "ELBSecurityPolicy-TLS13-1-2-Res-2021-06"
}
