# Create log group for service
resource "aws_cloudwatch_log_group" "quote_scraper_grpc_server" {
  name_prefix = "${terraform.workspace}-quote-scraper-grpc-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "quote_scraper_grpc_server"
  }
}

# Create IAM role and policy for Vehicles gRPC Server ECS tasks to access other AWS services
data "aws_iam_policy_document" "quote_scraper_grpc_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      aws_s3_bucket.quote_scraper.arn,
      data.aws_s3_bucket.go-service-profiles.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${aws_s3_bucket.quote_scraper.arn}/*",
      "${data.aws_s3_bucket.go-service-profiles.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "quote_scraper_grpc_server_tasks_policy" {
  policy = data.aws_iam_policy_document.quote_scraper_grpc_server_tasks_policy_doc.json
}

resource "aws_iam_role" "quote_scraper_grpc_server_task_role" {
  name               = "quote_scraper_grpc_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "QuoteScraperGRPCServerTaskRole_policy" {
  role       = aws_iam_role.quote_scraper_grpc_server_task_role.name
  policy_arn = aws_iam_policy.quote_scraper_grpc_server_tasks_policy.arn
}

# Create task definition
module "quote_scraper_grpc_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "quote-scraper-grpc-server-td"
  container_name = "quote-scraper-grpc-server"

  container_image  = "${var.quote_scraper_grpc_server_ecr_repo_url}:${var.quote_scraper_grpc_server_tag}"
  task_memory = 8192 # 8 GB RAM
  task_cpu    = 4096 # 4 vCPU
  essential        = true
  enable_otel_sidecar = true
  metrics_namespace = "quote_scraper"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 9093
      hostPort      = 9093
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6065
      hostPort      = 6065
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                         = "prod"
    "DATABASES_NIRVANA_HOST"      = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"      = "postgres"
    "DATABASES_NIRVANA_PORT"      = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"  = local.postgres_database_readwrite_username
    "DATABASES_NIRVANA_PASSWORD"  = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    "STATSD_RECEIVER_ADDRESS"     = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT" = "${local.otel_server_fqdn}:4317"
    "SMARTPROXY_PASSWORD"         = jsondecode(data.aws_secretsmanager_secret_version.smart_proxy.secret_string)["password"]
    "SMARTPROXY_USER"             = jsondecode(data.aws_secretsmanager_secret_version.smart_proxy.secret_string)["username"]
    "SMARTPROXY_PORT"             = jsondecode(data.aws_secretsmanager_secret_version.smart_proxy.secret_string)["port"]
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.quote_scraper_grpc_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-quote-scraper-grpc-server"
    }
  }

  task_role_arn           = aws_iam_role.quote_scraper_grpc_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "quote-scraper-grpc-server"
  }
}

# Create ECS Fargate service
module "quote_scraper_grpc_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "quote-scraper-grpc-server"
  container_name = "quote-scraper-grpc-server"
  container_port = 9093

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name

  task_definition_arn = module.quote_scraper_grpc_server_td.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "quote-scraper-grpc-server"
  }
}

locals {
  quote_scraper_grpc_server_fqdn = "${module.quote_scraper_grpc_server_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}
