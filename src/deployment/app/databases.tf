locals {
  # service deployments need these values to inject them into service environment
  # We can get rid of these with secrets refactor (TBD)
  postgres_database_address            = "terraform-20210203043436823600000002.csx3hx8rg1kh.us-east-2.rds.amazonaws.com"
  postgres_database_migrator_username  = "migrator"
  postgres_database_readwrite_username = "readwrite"
  postgres_database_port               = "5432"

  fmcsa_database_address_read  = "fmcsa-aurora-db-cluster.cluster-ro-csx3hx8rg1kh.us-east-2.rds.amazonaws.com"
  fmcsa_database_address_write = "fmcsa-aurora-db-cluster.cluster-csx3hx8rg1kh.us-east-2.rds.amazonaws.com"
  fmcsa_database_username      = "postgres"
  fmcsa_database_port          = "5432"

  ds_database_name     = "ds"
  ds_database_username = "ds_user"

  nhtsa_database_address = "prod-production-nhtsa.csx3hx8rg1kh.us-east-2.rds.amazonaws.com"

  snowflake_account_name     = "ad91921.us-east-2.aws"
  snowflake_admin_user       = "abhaymitra"
  snowflake_datascience_user = "datascience"
  snowflake_reporting_user   = "PROD_REPORTING_JOB"

  gorules_sandbox_db_address = "brms-sandbox.cluster-csx3hx8rg1kh.us-east-2.rds.amazonaws.com"
}

# DB Passwords: To be migrated to CDKTF later

# Application DB
resource "random_password" "postgres_master_password" {
  length           = 40
  special          = true
  min_special      = 5
  override_special = "!#$%^&*()-_=+[]{}<>:?"
  keepers = {
    pass_version = 1
  }
}

resource "aws_secretsmanager_secret" "postgres_password" {
  name = "postgres-pass-${terraform.workspace}"
}

resource "aws_secretsmanager_secret_version" "postgres_password_val" {
  secret_id     = aws_secretsmanager_secret.postgres_password.id
  secret_string = random_password.postgres_master_password.result
}


# FMCSA DB
resource "random_password" "db_fmcsa_master_password" {
  length           = 40
  special          = true
  min_special      = 5
  override_special = "!#$%^&*()-_=+[]{}<>:?"
  keepers = {
    pass_version = 1
  }
}

resource "aws_secretsmanager_secret" "db_fmcsa_password" {
  name = "fmcsa-db-pass-${terraform.workspace}"
}

resource "aws_secretsmanager_secret_version" "db_fmcsa_password_val" {
  secret_id     = aws_secretsmanager_secret.db_fmcsa_password.id
  secret_string = random_password.db_fmcsa_master_password.result
}
