# Create log group for service
resource "aws_cloudwatch_log_group" "gql_server" {
  name_prefix = "${terraform.workspace}-gql-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "gql_server"
  }
}

# Create IAM role and policy for GraphQL Server ECS tasks to access other AWS services
data "aws_iam_policy_document" "gql_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt",
    ]
    resources = [
      aws_kms_key.fmcsa_credentials.arn,
      aws_kms_key.telematics_api_key_credentials.arn,
      aws_kms_key.telematics_consent_user_request_metadata.arn,
      aws_kms_key.terminal_user_credentials.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.data_scratch.arn,
      data.aws_s3_bucket.go-service-profiles.arn,
      data.aws_s3_bucket.claims.arn,
      data.aws_s3_bucket.draft-fnol-attachments.arn,
      data.aws_s3_bucket.pdfgen.arn,
      aws_s3_bucket.forms.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject"
    ]
    resources = [
      "${data.aws_s3_bucket.data_scratch.arn}/*",
      "${data.aws_s3_bucket.go-service-profiles.arn}/*",
      "${data.aws_s3_bucket.claims.arn}/*",
      "${data.aws_s3_bucket.draft-fnol-attachments.arn}/*",
      "${data.aws_s3_bucket.pdfgen.arn}/*",
      "${aws_s3_bucket.forms.arn}/*"
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:PutObject"
    ]
    resources = [
      "${data.aws_s3_bucket.go-service-profiles.arn}/*",
      "${data.aws_s3_bucket.claims.arn}/*",
      "${data.aws_s3_bucket.draft-fnol-attachments.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "gql_server_tasks_policy" {
  policy = data.aws_iam_policy_document.gql_server_tasks_policy_doc.json
}

resource "aws_iam_role" "gql_server_task_role" {
  name               = "gql_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "gqlServerTaskRole_policy" {
  role       = aws_iam_role.gql_server_task_role.name
  policy_arn = aws_iam_policy.gql_server_tasks_policy.arn
}

# Create task definition
module "gql_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "gql-server-td"
  container_name = "gql-server"

  container_image  = "${var.gql_server_ecr_repo_url}:${var.graphql_server_tag}"
  task_memory = 4096
  task_cpu    = 1024
  enable_otel_sidecar = true
  metrics_namespace   = "graphql_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  essential = true

  port_mappings = [
    {
      containerPort = 3030
      hostPort      = 3030
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6063
      hostPort      = 6063
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV" = "prod"
    # Nirvana Postgres DB
    "DATABASES_NIRVANA_HOST"     = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"     = "postgres"
    "DATABASES_NIRVANA_PORT"     = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME" = local.postgres_database_readwrite_username

    # FMCSA Postgres DB
    "DATABASES_FMCSA_HOST"       = local.fmcsa_database_address_write
    "DATABASES_FMCSA_NAME"       = "postgres"
    "DATABASES_FMCSA_PORT"       = local.fmcsa_database_port
    "DATABASES_FMCSA_USERNAME"   = local.fmcsa_database_username

    "DATABASES_FMCSAWRITE_HOST"       = local.fmcsa_database_address_write
    "DATABASES_FMCSAWRITE_PORT"       = local.fmcsa_database_port
    "DATABASES_FMCSAWRITE_NAME"       = "postgres"
    "DATABASES_FMCSAWRITE_USERNAME"   = local.fmcsa_database_username

    "DATABASES_FMCSAREADONLY_HOST"     = local.fmcsa_database_address_read
    "DATABASES_FMCSAREADONLY_PORT"     = local.fmcsa_database_port
    "DATABASES_FMCSAREADONLY_NAME"     = "postgres"
    "DATABASES_FMCSAREADONLY_USERNAME" = local.fmcsa_database_username

    "DATABASES_DS_HOST"                = local.postgres_database_address
    "DATABASES_DS_NAME"                = local.ds_database_name
    "DATABASES_DS_PORT"                = local.postgres_database_port
    "DATABASES_DS_USERNAME"            = local.ds_database_username

    # NHTSA SQL Server DB
    "DATABASES_NHTSA_HOST"     = local.nhtsa_database_address

    "OTEL_EXPORTER_OTLP_ENDPOINT"     = "${local.otel_server_fqdn}:4317"
    "STATSD_RECEIVER_ADDRESS"         = "${local.otel_server_fqdn}:8125"

    "PRODUCTTOOLS_SNAPSHEET_SANDBOXPUBLICKEY"           = "nirvana_api"
    "PRODUCTTOOLS_SNAPSHEET_PRODUCTIONPUBLICKEY"        = "nirvana_us_api"
  }

  map_secrets = {
    "DATABASES_DS_PASSWORD"                = data.aws_secretsmanager_secret_version.app_db_ds_user_password.arn

    "DATABASES_FMCSA_PASSWORD"             = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAREADONLY_PASSWORD"     = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAWRITE_PASSWORD"        = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn

    "DATABASES_NHTSA_PASSWORD"                        = data.aws_secretsmanager_secret_version.nhtsa_db_password.arn
    "DATABASES_NIRVANA_PASSWORD"                      = data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn
    "DATABASES_SNOWFLAKE_USERS_SNAPSHEET_PRIVATEKEY"  = "${data.aws_secretsmanager_secret_version.snapsheet_snowflake_credentials.arn}:privateKey::"

    "PRODUCTTOOLS_ASCEND_APIKEY"           = "${data.aws_secretsmanager_secret_version.ascend_secrets.arn}:prodApiKey::"
    "PRODUCTTOOLS_ASCEND_SANDBOXAPIKEY"    = "${data.aws_secretsmanager_secret_version.ascend_secrets.arn}:sandboxApiKey::"
    "PRODUCTTOOLS_KNOCK_API_KEY"           = "${data.aws_secretsmanager_secret_version.knock_api_key.arn}:knock_api_key::"
    "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"      = data.aws_secretsmanager_secret_version.launchdarkly_api_key.arn
    "PRODUCTTOOLS_NARSAPIKEY"              = "${data.aws_secretsmanager_secret_version.nars_secrets.arn}:apiKey::"
    "PRODUCTTOOLS_NARSTESTAPIKEY"          = "${data.aws_secretsmanager_secret_version.nars_secrets.arn}:testApiKey::"
    "PRODUCTTOOLS_OPENAI_API_KEY"          = "${data.aws_secretsmanager_secret_version.openai_secrets.arn}:apiKey::"
    "PRODUCTTOOLS_SEGMENTAPIKEY"           = "${data.aws_secretsmanager_secret_version.segment_secrets.arn}:segment_api_key_prod::"

    "PRODUCTTOOLS_SNAPSHEET_SANDBOXSECRET"              = "${data.aws_secretsmanager_secret_version.snapsheet_secrets.arn}:sandboxSecret::"
    "PRODUCTTOOLS_SNAPSHEET_SANDBOXHOST"                = "${data.aws_secretsmanager_secret_version.snapsheet_secrets.arn}:sandboxHost::"
    "PRODUCTTOOLS_SNAPSHEET_PRODUCTIONSECRET"           = "${data.aws_secretsmanager_secret_version.snapsheet_secrets.arn}:productionSecret::"
    "PRODUCTTOOLS_SNAPSHEET_PRODUCTIONHOST"             = "${data.aws_secretsmanager_secret_version.snapsheet_secrets.arn}:productionHost::"

    "SENDGRID_API_KEY"                     = "${data.aws_secretsmanager_secret_version.sendgrid_api_key.arn}:sendgrid_api_key::"
    "PRODUCTTOOLS_CLERKCONFIG_SECRETKEY"   = data.aws_secretsmanager_secret_version.clerk_secret.arn
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.gql_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-gql-server"
    }
  }

  task_role_arn           = aws_iam_role.gql_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "gql-server"
  }
}

# Create ECS Fargate service
module "gql_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "gql-server"
  container_name = "gql-server"
  container_port = 3030

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.gql_server_td.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id


  create_lb_access_sg_rule = true
  lb_access_sg             = module.gql_server_alb.access_sg_id
  lb_http_tg_arns          = module.gql_server_alb.http_tgs_arns
  lb_http_tg_ports         = module.gql_server_alb.http_tgs_ports

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "gql-server"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_safety_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_safety_sns_arn,
    }
  }
}

module "gql_server_alb" {
  source = "../common-modules/nv_svc_alb"

  name_prefix = "gql-server"
  vpc_id      = local.default_vpc_id

  # Application Load Balancer
  security_groups = [local.default_sg_id]

  public_subnets = [
    local.public_subnet_ids[0],
    local.public_subnet_ids[1],
    local.public_subnet_ids[2],
  ]

  # Access Control to Application Load Balancer
  http_ports = {
    force_https = {
      listener_port = 80
      host          = "#{host}"
      path          = "/#{path}"
      target_port   = "443"
      protocol      = "HTTPS"
      query         = "#{query}"
      status_code   = "HTTP_301"
    }
  }
  https_ports = {
    force_https = {
      listener_port = 443
    }
  }

  # Target Groups
  target_group_health_check_interval            = 30
  target_group_health_check_path                = "/health"
  target_group_health_check_timeout             = 10
  target_group_health_check_healthy_threshold   = 3
  target_group_health_check_unhealthy_threshold = 3
  target_group_health_check_matcher             = "200,301,302"

  # Certificates
  default_certificate_arn = data.aws_acm_certificate.prod_cert.arn
  ssl_policy              = "ELBSecurityPolicy-2016-08"

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "gql-server"
  }
}

resource "aws_security_group_rule" "gql_server_alb_ingress" {
  type = "ingress"

  security_group_id        = local.default_sg_id
  protocol                 = -1
  from_port                = 0
  to_port                  = 0
  source_security_group_id = module.gql_server_alb.access_sg_id
}

# Create A record in Route53 zone
resource "aws_route53_record" "gql_server_a_record" {
  zone_id = data.aws_route53_zone.prod.zone_id
  name    = "graphqlapi.prod.nirvanatech.com"
  type    = "A"

  alias {
    name                   = module.gql_server_alb.dns_name
    zone_id                = module.gql_server_alb.zone_id
    evaluate_target_health = true
  }
}
