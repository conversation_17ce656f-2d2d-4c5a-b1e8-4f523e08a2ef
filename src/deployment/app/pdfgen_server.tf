# Create log group for service
resource "aws_cloudwatch_log_group" "pdfgen_server" {
  name = "${terraform.workspace}-pdfgen-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "pdfgen-server"
  }
}

# Create IAM role and policy for pdfgen server ECS tasks to access other AWS services
data "aws_iam_policy_document" "pdfgen_server_task" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.pdfgen.arn,
      data.aws_s3_bucket.go-service-profiles.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.pdfgen.arn}/*",
      "${data.aws_s3_bucket.go-service-profiles.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "pdfgen_server_task" {
  policy = data.aws_iam_policy_document.pdfgen_server_task.json
}

resource "aws_iam_role" "pdfgen_server_task" {
  name               = "pdfgen_server_task"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "pdfgenServerTaskRole_policy" {
  role       = aws_iam_role.pdfgen_server_task.name
  policy_arn = aws_iam_policy.pdfgen_server_task.arn
}

# Create task definition
module "td_pdfgen_server" {
  source = "../common-modules/ecs_task_definition"

  family         = "pdfgen_server-td"
  container_name = "pdfgen_server"

  container_image = "${var.pdfgen_server_ecr_repo_url}:${var.pdfgen_server_tag}"
  # Temporarily bumping resources until we debug why simple pdf operations are
  # so resource intensive
  task_memory = 4096
  task_cpu    = 2048
  enable_otel_sidecar = true
  metrics_namespace   = "pdfgen_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  essential = true

  port_mappings = [
    {
      containerPort = 33435
      hostPort      = 33435
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6062
      hostPort      = 6062
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                         = "prod"
    "DATABASES_NIRVANA_HOST"      = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"      = "postgres"
    "DATABASES_NIRVANA_PORT"      = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"  = local.postgres_database_readwrite_username
    "DATABASES_NIRVANA_PASSWORD"  = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    "STATSD_RECEIVER_ADDRESS"     = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT" = "${local.otel_server_fqdn}:4317"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.pdfgen_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-pdfgen-server"
    }
  }

  task_role_arn           = aws_iam_role.pdfgen_server_task.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "pdfgen_server"
  }
  healthcheck = {
    command = [
      "CMD-SHELL",
      "/app/grpc_health_probe -addr=:33435"
    ],
    interval    = 30,
    timeout     = 5,
    retries     = 3,
    startPeriod = 30
  }
}

# Create ECS Fargate service
module "fg_service_pdfgen_server" {
  source = "../common-modules/nv_fg_svc"

  name_prefix = "pdfgen-server"

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.td_pdfgen_server.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "pdfgen_server"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_agents_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_agents_sns_arn,
    }
  }
}

locals {
  pdfgen_server_fqdn = "${module.fg_service_pdfgen_server.discovery_service_name}.${local.app_cluster_dns_name}"
}
