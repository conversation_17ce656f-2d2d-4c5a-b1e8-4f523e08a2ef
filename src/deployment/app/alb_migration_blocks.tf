### Demo Boards server
moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_alb.lb
  to   = module.demo_boards_server_alb.aws_alb.lb
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_lb_listener.lb_http_listeners["force_https"]
  to   = module.demo_boards_server_alb.aws_lb_listener.lb_http_listeners["force_https"]
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_lb_listener.lb_https_listeners["force_https"]
  to   = module.demo_boards_server_alb.aws_lb_listener.lb_https_listeners["force_https"]
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_lb_target_group.lb_http_tgs["force_https"]
  to   = module.demo_boards_server_alb.aws_lb_target_group.lb_http_tgs["force_https"]
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_s3_bucket.logs
  to   = module.demo_boards_server_alb.aws_s3_bucket.logs
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_s3_bucket_acl.logs
  to   = module.demo_boards_server_alb.aws_s3_bucket_acl.logs
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_s3_bucket_policy.lb_logs_access_policy
  to   = module.demo_boards_server_alb.aws_s3_bucket_policy.lb_logs_access_policy
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_s3_bucket_public_access_block.lb_logs_block_public_access
  to   = module.demo_boards_server_alb.aws_s3_bucket_public_access_block.lb_logs_block_public_access
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_security_group.lb_access_sg
  to   = module.demo_boards_server_alb.aws_security_group.lb_access_sg
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_security_group_rule.ingress_through_http["force_https"]
  to   = module.demo_boards_server_alb.aws_security_group_rule.ingress_through_http["force_https"]
}

moved {
  from = module.demo_boards_server_fg_service.module.ecs_alb[0].aws_security_group_rule.ingress_through_https["force_https"]
  to   = module.demo_boards_server_alb.aws_security_group_rule.ingress_through_https["force_https"]
}


### Boards server
moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_alb.lb
  to   = module.boards_server_alb.aws_alb.lb
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_lb_listener.lb_http_listeners["force_https"]
  to   = module.boards_server_alb.aws_lb_listener.lb_http_listeners["force_https"]
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_lb_listener.lb_https_listeners["force_https"]
  to   = module.boards_server_alb.aws_lb_listener.lb_https_listeners["force_https"]
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_lb_target_group.lb_http_tgs["force_https"]
  to   = module.boards_server_alb.aws_lb_target_group.lb_http_tgs["force_https"]
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_s3_bucket.logs
  to   = module.boards_server_alb.aws_s3_bucket.logs
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_s3_bucket_acl.logs
  to   = module.boards_server_alb.aws_s3_bucket_acl.logs
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_s3_bucket_policy.lb_logs_access_policy
  to   = module.boards_server_alb.aws_s3_bucket_policy.lb_logs_access_policy
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_s3_bucket_public_access_block.lb_logs_block_public_access
  to   = module.boards_server_alb.aws_s3_bucket_public_access_block.lb_logs_block_public_access
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_security_group.lb_access_sg
  to   = module.boards_server_alb.aws_security_group.lb_access_sg
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_security_group_rule.ingress_through_http["force_https"]
  to   = module.boards_server_alb.aws_security_group_rule.ingress_through_http["force_https"]
}

moved {
  from = module.boards_server_fg_service.module.ecs_alb[0].aws_security_group_rule.ingress_through_https["force_https"]
  to   = module.boards_server_alb.aws_security_group_rule.ingress_through_https["force_https"]
}


### GQL server
moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_alb.lb
  to   = module.gql_server_alb.aws_alb.lb
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_lb_listener.lb_http_listeners["force_https"]
  to   = module.gql_server_alb.aws_lb_listener.lb_http_listeners["force_https"]
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_lb_listener.lb_https_listeners["force_https"]
  to   = module.gql_server_alb.aws_lb_listener.lb_https_listeners["force_https"]
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_lb_target_group.lb_http_tgs["force_https"]
  to   = module.gql_server_alb.aws_lb_target_group.lb_http_tgs["force_https"]
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_s3_bucket.logs
  to   = module.gql_server_alb.aws_s3_bucket.logs
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_s3_bucket_acl.logs
  to   = module.gql_server_alb.aws_s3_bucket_acl.logs
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_s3_bucket_policy.lb_logs_access_policy
  to   = module.gql_server_alb.aws_s3_bucket_policy.lb_logs_access_policy
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_s3_bucket_public_access_block.lb_logs_block_public_access
  to   = module.gql_server_alb.aws_s3_bucket_public_access_block.lb_logs_block_public_access
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_security_group.lb_access_sg
  to   = module.gql_server_alb.aws_security_group.lb_access_sg
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_security_group_rule.ingress_through_http["force_https"]
  to   = module.gql_server_alb.aws_security_group_rule.ingress_through_http["force_https"]
}

moved {
  from = module.gql_server_fg_service.module.ecs_alb[0].aws_security_group_rule.ingress_through_https["force_https"]
  to   = module.gql_server_alb.aws_security_group_rule.ingress_through_https["force_https"]
}


### Metabase server

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_alb.lb
  to   = module.metabase_server_alb.aws_alb.lb
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_lb_listener.lb_http_listeners["force_https"]
  to   = module.metabase_server_alb.aws_lb_listener.lb_http_listeners["force_https"]
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_lb_listener.lb_https_listeners["force_https"]
  to   = module.metabase_server_alb.aws_lb_listener.lb_https_listeners["force_https"]
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_lb_target_group.lb_http_tgs["force_https"]
  to   = module.metabase_server_alb.aws_lb_target_group.lb_http_tgs["force_https"]
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_s3_bucket.logs
  to   = module.metabase_server_alb.aws_s3_bucket.logs
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_s3_bucket_acl.logs
  to   = module.metabase_server_alb.aws_s3_bucket_acl.logs
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_s3_bucket_policy.lb_logs_access_policy
  to   = module.metabase_server_alb.aws_s3_bucket_policy.lb_logs_access_policy
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_s3_bucket_public_access_block.lb_logs_block_public_access
  to   = module.metabase_server_alb.aws_s3_bucket_public_access_block.lb_logs_block_public_access
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_security_group.lb_access_sg
  to   = module.metabase_server_alb.aws_security_group.lb_access_sg
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_security_group_rule.ingress_through_http["force_https"]
  to   = module.metabase_server_alb.aws_security_group_rule.ingress_through_http["force_https"]
}

moved {
  from = module.metabase_server_fg_service.module.ecs_alb[0].aws_security_group_rule.ingress_through_https["force_https"]
  to   = module.metabase_server_alb.aws_security_group_rule.ingress_through_https["force_https"]
}
