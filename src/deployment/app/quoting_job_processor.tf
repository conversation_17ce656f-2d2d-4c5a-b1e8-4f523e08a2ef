# Create log group for service
resource "aws_cloudwatch_log_group" "quoting_job_processor" {
  name_prefix = "${terraform.workspace}-quoting-job-processor-logs"

  tags = {
    Environment = terraform.workspace
    Application = "quoting_job_processor"
  }
}

# NOTE: This task definition uses the same IAM policy as api_server.tf.
# There's already a TODO later in this file to move to its own policy.
# Until that's addressed, any policies needed for this task definition should be
# added to api_server's IAM policies.

# Create task definition
module "quoting_job_processor_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "quoting-job-processor-td"
  container_name = "quoting-job-processor"

  container_image  = "${var.quoting_job_processor_ecr_repo_url}:${var.quoting_job_processor_tag}"
  task_memory = 16384 # 16 GB RAM
  task_cpu    = 2048  # 2 vCPU
  essential        = true
  enable_otel_sidecar = true
  metrics_namespace   = "quoting_job_processor"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  container_ulimits = [
    {
      name      = "nofile"
      hardLimit = 16384
      softLimit = 16384
    }
  ]

  port_mappings = [
    {
      containerPort = 56224
      hostPort      = 56224
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6060
      hostPort      = 6060
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                                               = "prod",
    "DATABASES_NIRVANA_HOST"                            = local.postgres_database_address,
    "DATABASES_NIRVANA_NAME"                            = "postgres",
    "DATABASES_NIRVANA_PORT"                            = local.postgres_database_port,
    "DATABASES_NIRVANA_USERNAME"                        = local.postgres_database_migrator_username,
    "DATABASES_NIRVANA_PASSWORD"                        = data.aws_secretsmanager_secret_version.app_db_migrator_password.secret_string,
    "DATABASES_FMCSA_HOST"                              = local.fmcsa_database_address_read,
    "DATABASES_FMCSA_PORT"                              = local.fmcsa_database_port,
    "DATABASES_FMCSA_NAME"                              = "postgres",
    "DATABASES_FMCSA_USERNAME"                          = local.fmcsa_database_username,
    "DATABASES_FMCSAWRITE_HOST"                         = local.fmcsa_database_address_write
    "DATABASES_FMCSAWRITE_PORT"                         = local.fmcsa_database_port
    "DATABASES_FMCSAWRITE_NAME"                         = "postgres"
    "DATABASES_FMCSAWRITE_USERNAME"                     = local.fmcsa_database_username
    "DATABASES_FMCSAREADONLY_HOST"                      = local.fmcsa_database_address_read
    "DATABASES_FMCSAREADONLY_PORT"                      = local.fmcsa_database_port
    "DATABASES_FMCSAREADONLY_NAME"                      = "postgres"
    "DATABASES_FMCSAREADONLY_USERNAME"                  = local.fmcsa_database_username
    "DATABASES_DS_HOST"                                 = local.postgres_database_address,
    "DATABASES_DS_NAME"                                 = local.ds_database_name,
    "DATABASES_DS_PORT"                                 = local.postgres_database_port,
    "DATABASES_DS_USERNAME"                             = local.ds_database_username,
    "DATABASES_DS_PASSWORD"                             = data.aws_secretsmanager_secret_version.app_db_ds_user_password.secret_string,
    "DATABASES_NHTSA_HOST"                              = local.nhtsa_database_address,
    "DATABASES_NHTSA_PASSWORD"                          = data.aws_secretsmanager_secret_version.nhtsa_db_password.secret_string,
    "MVR_CACHE_SERVER_PORT"                             = "58727",
    "MVR_CACHE_SERVER_ADDRESS"                          = local.mvr_cache_server_fqdn,
    "TELEMATICS_CREDENTIALS_TERMINAL_SECRETKEY"         = jsondecode(data.aws_secretsmanager_secret_version.terminal.secret_string)["prod_secret_key"],
    "TELEMATICS_CREDENTIALS_TERMINAL_PUBLISHABLEKEY"    = jsondecode(data.aws_secretsmanager_secret_version.terminal.secret_string)["prod_publishable_key"],
    "RATEML_GSHEETS_CLIENT_EMAIL"                       = jsondecode(data.aws_secretsmanager_secret_version.rateml_gsheets.secret_string)["client_email"],
    "RATEML_GSHEETS_PRIVATE_KEY_ID"                     = jsondecode(data.aws_secretsmanager_secret_version.rateml_gsheets.secret_string)["private_key_id"],
    "RATEML_GSHEETS_PRIVATE_KEY_BASE64"                 = jsondecode(data.aws_secretsmanager_secret_version.rateml_gsheets.secret_string)["private_key_base64"],
    "MIGRATION_VERSION"                                 = "-2",
    "FLATFILE_KEY"                                      = jsondecode(data.aws_secretsmanager_secret_version.flatfile_secrets.secret_string)["flatfile_key"],
    "FLATFILE_SECRET"                                   = jsondecode(data.aws_secretsmanager_secret_version.flatfile_secrets.secret_string)["flatfile_secret"],
    "GEOCODIO_API_KEY"                                  = jsondecode(data.aws_secretsmanager_secret_version.geocodio_secrets.secret_string)["geocodio_api_key"],
    "PDFGEN_SERVER_ADDRESS"                             = "${local.pdfgen_server_fqdn}:33435",
    "SEGMENT_API_KEY"                                   = jsondecode(data.aws_secretsmanager_secret_version.segment_secrets.secret_string)["segment_api_key_prod"],
    "PRODUCTTOOLS_SEGMENTAPIKEY"                        = jsondecode(data.aws_secretsmanager_secret_version.segment_secrets.secret_string)["segment_api_key_prod"],
    "PRODUCTTOOLS_UNIPDFAPIKEY"                         = jsondecode(data.aws_secretsmanager_secret_version.unipdf_secrets.secret_string)["unipdf_api_key"],
    "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY"                   = data.aws_secretsmanager_secret_version.launchdarkly_api_key.secret_string,
    "SENDGRID_API_KEY"                                  = jsondecode(data.aws_secretsmanager_secret_version.sendgrid_api_key.secret_string)["sendgrid_api_key"],
    "STATSD_RECEIVER_ADDRESS"                           = "${local.otel_server_fqdn}:8125",
    "OTEL_EXPORTER_OTLP_ENDPOINT"                       = "${local.otel_server_fqdn}:4317",
    "PAGERDUTY_AGENTS_ROUTINGKEY"                       = jsondecode(data.aws_secretsmanager_secret_version.pagerduty_secrets.secret_string)["PAGERDUTY_AGENTS_ROUTINGKEY"],
    "PAGERDUTY_BILLING_ROUTINGKEY"                      = jsondecode(data.aws_secretsmanager_secret_version.pagerduty_secrets.secret_string)["PAGERDUTY_BILLING_ROUTINGKEY"],
    "PAGERDUTY_INSURANCE_ENG_TEST_ROUTINGKEY"           = jsondecode(data.aws_secretsmanager_secret_version.pagerduty_secrets.secret_string)["PAGERDUTY_INSURANCE_ENG_TEST_ROUTINGKEY"],
    "PAGERDUTY_INFRA_ROUTINGKEY"                        = jsondecode(data.aws_secretsmanager_secret_version.pagerduty_secrets.secret_string)["PAGERDUTY_INFRA_ROUTINGKEY"],
    "PAGERDUTY_NON_FLEET_ROUTINGKEY"                    = jsondecode(data.aws_secretsmanager_secret_version.pagerduty_secrets.secret_string)["PAGERDUTY_NON_FLEET_ROUTINGKEY"],
    "PAGERDUTY_NON_FLEET_TEST_ROUTINGKEY"               = jsondecode(data.aws_secretsmanager_secret_version.pagerduty_secrets.secret_string)["PAGERDUTY_NON_FLEET_TEST_ROUTINGKEY"],
    "PRODUCTTOOLS_GOOGLE_APP_CONFIG_CLIENT_ID"          = jsondecode(data.aws_secretsmanager_secret_version.google_app_config_secrets.secret_string)["CLIENT_ID"],
    "PRODUCTTOOLS_GOOGLE_APP_CONFIG_CLIENT_SECRET"      = jsondecode(data.aws_secretsmanager_secret_version.google_app_config_secrets.secret_string)["CLIENT_SECRET"],
    "PIBIT_AI_API_KEY"                                  = jsondecode(data.aws_secretsmanager_secret_version.pibit_ai_secrets.secret_string)["pibit_ai_api_key_prod"],
    "PRODUCTTOOLS_VERISK_CONFIG_API_KEY"                = jsondecode(data.aws_secretsmanager_secret_version.verisk_client_secret.secret_string)["api_key"],
    "PRODUCTTOOLS_VERISK_CONFIG_TEST_API_KEY"           = jsondecode(data.aws_secretsmanager_secret_version.verisk_client_secret.secret_string)["test_api_key"],
    "PRODUCTTOOLS_VERISK_CONFIG_ORG_ID"                 = jsondecode(data.aws_secretsmanager_secret_version.verisk_client_secret.secret_string)["org_id"],
    "PRODUCTTOOLS_VERISK_CONFIG_TEST_ORG_ID"            = jsondecode(data.aws_secretsmanager_secret_version.verisk_client_secret.secret_string)["test_org_id"],
    "PRODUCTTOOLS_VERISK_CONFIG_SHIP_ID"                = jsondecode(data.aws_secretsmanager_secret_version.verisk_client_secret.secret_string)["ship_id"],
    "PRODUCTTOOLS_VERISK_CONFIG_TEST_SHIP_ID"           = jsondecode(data.aws_secretsmanager_secret_version.verisk_client_secret.secret_string)["test_ship_id"],
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_CLIENT_ID"     = jsondecode(data.aws_secretsmanager_secret_version.salesforce_secrets.secret_string)["salesforce-client-id-test"],
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_CLIENT_SECRET" = jsondecode(data.aws_secretsmanager_secret_version.salesforce_secrets.secret_string)["salesforce-client-secret-test"],
    "PRODUCTTOOLS_SALESFORCE_CONFIG_TEST_PASSWORD"      = jsondecode(data.aws_secretsmanager_secret_version.salesforce_secrets.secret_string)["salesforce-password-test"],
    "PRODUCTTOOLS_SALESFORCE_CONFIG_CLIENT_ID"          = jsondecode(data.aws_secretsmanager_secret_version.salesforce_secrets.secret_string)["salesforce-client-id"],
    "PRODUCTTOOLS_SALESFORCE_CONFIG_CLIENT_SECRET"      = jsondecode(data.aws_secretsmanager_secret_version.salesforce_secrets.secret_string)["salesforce-client-secret"],
    "PRODUCTTOOLS_SALESFORCE_CONFIG_PASSWORD"           = jsondecode(data.aws_secretsmanager_secret_version.salesforce_secrets.secret_string)["salesforce-password"],
    "PRODUCTTOOLS_TWILIO_ACCOUNT_SID"                  = jsondecode(data.aws_secretsmanager_secret_version.twilio_secrets.secret_string)["twilio-account-sid"],
    "PRODUCTTOOLS_TWILIO_AUTH_TOKEN"                   = jsondecode(data.aws_secretsmanager_secret_version.twilio_secrets.secret_string)["twilio-auth-token"],
    "PRODUCTTOOLS_TWILIO_BASE_URL"                     = jsondecode(data.aws_secretsmanager_secret_version.twilio_secrets.secret_string)["twilio-base-url"],
    "PRODUCTTOOLS_NARSAPIKEY"                           = jsondecode(data.aws_secretsmanager_secret_version.nars_secrets.secret_string)["apiKey"]
    "PRODUCTTOOLS_NARSTESTAPIKEY"                       = jsondecode(data.aws_secretsmanager_secret_version.nars_secrets.secret_string)["testApiKey"]
    "SNOWFLAKE_DS_ACCOUNT_NAME"                         = local.snowflake_account_name
    "SNOWFLAKE_DS_USER_NAME"                            = local.snowflake_datascience_user
    "SNOWFLAKE_DS_PASSWORD"                             = jsondecode(data.aws_secretsmanager_secret_version.snowflake_pass.secret_string)["datascience_pass"]
    "SNOWFLAKE_REPORTING_ACCOUNT_NAME"                  = local.snowflake_account_name
    "SNOWFLAKE_REPORTING_USER_NAME"                     = local.snowflake_reporting_user
    "SNOWFLAKE_REPORTING_PASSWORD"                      = jsondecode(data.aws_secretsmanager_secret_version.snowflake_pass.secret_string)["reporting_analytics_core_pass"]
    "PRODUCTTOOLS_IMPLER_ACCESS_TOKEN"                  = jsondecode(data.aws_secretsmanager_secret_version.impler_secrets.secret_string)["access_token"]
    "INFRA_SLACK_EVENTS_TOKEN"                          = jsondecode(data.aws_secretsmanager_secret_version.slack_events_jobber.secret_string)["oauth_access_token"]
  }

  map_secrets = {
    "DATABASES_FMCSA_PASSWORD"             = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAREADONLY_PASSWORD"     = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
    "DATABASES_FMCSAWRITE_PASSWORD"        = aws_secretsmanager_secret_version.db_fmcsa_password_val.arn
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.quoting_job_processor.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-quoting-job-processor"
    }
  }

  # we are using api-server's task role as this binary separated out from api-server
  # TODO: Create a separate role for quoting_jobber
  task_role_arn           = aws_iam_role.api_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "quoting-job-processor"
  }
}

# Create ECS Fargate service
module "quoting_job_processor_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "quoting-job-processor"
  container_name = "quoting-job-processor"
  container_port = 56224

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.quoting_job_processor_td.arn

  # See comment here in job_processor.tf
  desired_count                      = 1
  deployment_maximum_percent         = 300
  deployment_minimum_healthy_percent = 100

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id
  tags = {
    Environment = terraform.workspace
    Application = "quoting-job-processor"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_agents_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_agents_sns_arn,
    }
  }
}

locals {
  quoting_job_processor_fqdn = "${module.quoting_job_processor_fg_service.discovery_service_name}.${local.app_cluster_dns_name}"
}
