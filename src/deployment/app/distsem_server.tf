# Create log group for service
resource "aws_cloudwatch_log_group" "distsem_server" {
  name = "${terraform.workspace}-distsem-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "distsem-server"
  }
}

# Create IAM role and policy for mvr cache server ECS tasks to access other AWS services
data "aws_iam_policy_document" "distsem_server_task" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.go-service-profiles.arn
    ]
  }
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.go-service-profiles.arn}/*"
    ]
  }
}


resource "aws_iam_policy" "distsem_server_task" {
  policy = data.aws_iam_policy_document.distsem_server_task.json
}

resource "aws_iam_role" "distsem_server_task" {
  name               = "distsem_server_task"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "distsemServerTaskRole_policy" {
  role       = aws_iam_role.distsem_server_task.name
  policy_arn = aws_iam_policy.distsem_server_task.arn
}


# Create task definition
module "td_distsem_server" {
  source = "../common-modules/ecs_task_definition"

  family          = "distsem_server-td"
  container_name = "distsem_server"

  container_image  = "${var.distsem_server_ecr_repo_url}:${var.distsem_server_tag}"
  task_memory = 4096
  task_cpu    = 1024

  essential = true
  enable_otel_sidecar = true
  metrics_namespace   = "distem_server"
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  port_mappings = [
    {
      containerPort = 34787
      hostPort      = 34787
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6067
      hostPort      = 6067
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV" = "prod"
    # start-deprecated
    # The following environment settings have been retained (as a backward compatibility)
    # measure from config refactor (PR #1023)
    # TODO: remove these
    "POSTGRES_ADDRESS"  = local.postgres_database_address
    "POSTGRES_DB_NAME"  = "postgres"
    "POSTGRES_PORT"     = local.postgres_database_port
    "POSTGRES_USERNAME" = local.postgres_database_readwrite_username
    "POSTGRES_PASSWORD" = data.aws_secretsmanager_secret_version.app_db_readwrite_password.secret_string
    # end-deprecated
    "DATABASES_NIRVANA_HOST"      = local.postgres_database_address
    "DATABASES_NIRVANA_NAME"      = "postgres"
    "DATABASES_NIRVANA_PORT"      = local.postgres_database_port
    "DATABASES_NIRVANA_USERNAME"  = local.postgres_database_readwrite_username
    "STATSD_RECEIVER_ADDRESS"     = "${local.otel_server_fqdn}:8125"
    "OTEL_EXPORTER_OTLP_ENDPOINT" = "${local.otel_server_fqdn}:4317"
  }

  map_secrets = {
    "DATABASES_NIRVANA_PASSWORD"  = data.aws_secretsmanager_secret_version.app_db_readwrite_password.arn
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.distsem_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-distsem-server"
    }
  }

  task_role_arn           = aws_iam_role.distsem_server_task.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "distsem_server"
  }
}

# Create ECS Fargate service
module "fg_service_distsem_server" {
  source = "../common-modules/nv_fg_svc"

  name_prefix = "distsem-server"

  vpc_id          = local.default_vpc_id
  ecs_cluster_arn = local.internal_tools_cluster_arn
  ecs_cluster_name = local.internal_tools_cluster_name


  task_definition_arn = module.td_distsem_server.arn

  # disable blue/green deployments for this service at the expense of some downtime.
  desired_count                      = 1
  deployment_maximum_percent         = 100
  deployment_minimum_healthy_percent = 0


  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "distsem_server"
  }
}
