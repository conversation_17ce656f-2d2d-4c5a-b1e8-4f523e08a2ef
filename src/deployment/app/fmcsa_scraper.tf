# Create log group for service
resource "aws_cloudwatch_log_group" "fmcsa_scraper" {
  name_prefix = "${terraform.workspace}-fmcsa-scraper-logs"

  tags = {
    Environment = terraform.workspace
    Application = "fmcsa-scraper"
  }
}

# Create task definition
module "td_fmcsa_scraper" {
  source = "../common-modules/ecs_task_definition"

  family           = "fmcsa-scraper-td"
  container_name   = "fmcsa-scraper"
  container_image  = "${var.fmcsa_scraper_ecr_repo_url}:${var.fmcsa_scraper_tag}"
  task_memory = 2048
  task_cpu    = 512

  essential = true

  port_mappings = [{
    containerPort = 8091
    hostPort      = 8091
    protocol      = "tcp"
  }]

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.fmcsa_scraper.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-fmcsa-scraper"
    }
  }

  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "fmcsa-scraper"
  }
}

# Create ECS Fargate service
module "fg_service_fmcsa_scraper" {
  source = "../common-modules/nv_fg_svc"

  name_prefix = "fmcsa-scraper"

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name


  task_definition_arn = module.td_fmcsa_scraper.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "fmcsa-scraper"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_safety_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_safety_sns_arn,
    }
  }
}

locals {
  fmcsa_scraper_fqdn = "${module.fg_service_fmcsa_scraper.discovery_service_name}.${local.app_cluster_dns_name}"
}
