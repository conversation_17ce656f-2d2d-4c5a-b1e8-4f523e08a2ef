# Create log group for service
resource "aws_cloudwatch_log_group" "llmops_server" {
  name_prefix = "${terraform.workspace}-llmops-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "llmops_server"
  }
}

# Create IAM role and policy for LLMOps Server ECS tasks to access other AWS services
data "aws_iam_policy_document" "llmops_server_tasks_policy_doc" {
  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.pdfgen.arn
    ]
  }

  statement {
    sid    = ""
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
    ]
    resources = [
      "${data.aws_s3_bucket.pdfgen.arn}/*"
    ]
  }
}

resource "aws_iam_policy" "llmops_server_tasks_policy" {
  policy = data.aws_iam_policy_document.llmops_server_tasks_policy_doc.json
}

resource "aws_iam_role" "llmops_server_task_role" {
  name               = "llmops_server_task_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "llmops_server_task_role_policy" {
  role       = aws_iam_role.llmops_server_task_role.name
  policy_arn = aws_iam_policy.llmops_server_tasks_policy.arn
}

# Create task definition
module "llmops_server_td" {
  source = "../common-modules/ecs_task_definition"

  family         = "llmops-server-td"
  container_name = "llmops-server"

  container_image  = "${var.llmops_server_ecr_repo_url}:${var.llmops_server_tag}"

  task_memory = 2048 # 2 GB RAM
  task_cpu    = 512  # 0.5 vCPU

  metrics_namespace       = "llmops_server"
  enable_otel_sidecar     = true
  sidecar_container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"

  essential = true

  port_mappings = [
    {
      containerPort = 50051
      hostPort      = 50051
      protocol      = "tcp"
    },
    # for pprof
    {
      containerPort = 6063
      hostPort      = 6063
      protocol      = "tcp"
    }
  ]

  map_environment = {
    "ENV"                 = "prod"
  }

  map_secrets = {
    "LANGFUSE_HOST"       = "${data.aws_secretsmanager_secret_version.langfuse_credentials.arn}:host::"
    "LANGFUSE_PUBLIC_KEY" = "${data.aws_secretsmanager_secret_version.langfuse_credentials.arn}:publicKey::"
    "LANGFUSE_SECRET_KEY" = "${data.aws_secretsmanager_secret_version.langfuse_credentials.arn}:secretKey::"
    "OPENAI_API_KEY"      = "${data.aws_secretsmanager_secret_version.openai_secrets.arn}:apiKey::"
    "OTEL_EXPORTER_OTLP_ENDPOINT" = "${data.aws_secretsmanager_secret_version.otel_server.arn}:OTEL_EXPORTER_OTLP_ENDPOINT::"
    "STATSD_RECEIVER_URL" = "${data.aws_secretsmanager_secret_version.otel_server.arn}:STATSD_RECEIVER_URL::"
    "STATSD_RECEIVER_PORT" = "${data.aws_secretsmanager_secret_version.otel_server.arn}:STATSD_RECEIVER_PORT::"
  }

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.llmops_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-llmops-server"
    }
  }

  task_role_arn           = aws_iam_role.llmops_server_task_role.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "llmops-server"
  }

  healthcheck = {
    command = [
      "CMD-SHELL",
      "/app/grpc_health_probe -addr=:50051",
    ],
    interval    = 30,
    timeout     = 5,
    retries     = 3,
    startPeriod = 30
  }
}

data "aws_ecs_task_definition" "llmops_server_td" {
  task_definition = module.llmops_server_td.family

  depends_on = [
    # Needs to exist first on first deployment
    module.llmops_server_td.aws_ecs_task_definition
  ]
}

# Create ECS Fargate service
module "llmops_server_fg_service" {
  source = "../common-modules/nv_fg_svc"

  name_prefix    = "llmops-server"
  container_name = "llmops-server"
  container_port = 50051

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  # Use the latest available revision of the task definition
  task_definition_arn = "${regex("^(.+):\\d+$", module.llmops_server_td.arn)[0]}:${max(module.llmops_server_td.revision, data.aws_ecs_task_definition.llmops_server_td.revision)}"

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [
    aws_security_group.llmops_server.id,
    local.default_sg_id
  ]

  service_discovery_namespace_id = local.app_cluster_dns_id

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "llmops-server"
  }

  # Adding cloudwatch alerts for CPU/Memory utilization
  cloudwatch_alarms_config = {
    cpu_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_insured_eng_sns_arn,
    },
    memory_alarm = {
      threshold    = 80,
      team_sns_arn = local.cloudwatch_to_pagerduty_insured_eng_sns_arn,
    }
  }
}

resource "aws_security_group" "llmops_server" {
  name        = "llmops_server_sg"
  vpc_id      = local.default_vpc_id

  ingress {
    from_port   = 50051
    to_port     = 50051
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
