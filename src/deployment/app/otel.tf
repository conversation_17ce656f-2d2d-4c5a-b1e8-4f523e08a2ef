# Create log group for service
resource "aws_cloudwatch_log_group" "otel_server" {
  name = "${terraform.workspace}-otel-server-logs"

  tags = {
    Environment = terraform.workspace
    Application = "otel-server"
  }
}

# Create task definition
module "td_otel_server" {
  source = "../common-modules/ecs_task_definition"

  family         = "otel_server-td"
  container_name = "otel_server"

  # Fetch otel/open-telemetry-collector-contrib image from ECR pull through cache
  container_image = "${local.docker_hub_pull_through_cache_ecr_repo_url}/otel/opentelemetry-collector-contrib:0.117.0"
  task_memory = 1024
  task_cpu    = 512

  essential = true

  port_mappings = [
    # for statsd
    {
      containerPort = 8125
      hostPort      = 8125
      protocol      = "udp"
    },
    # for grpc traces
    {
      containerPort = 4317
      hostPort      = 4317
      protocol      = "tcp"
    },
    # for http traces
    {
      containerPort = 55681
      hostPort      = 55681
      protocol      = "tcp"
    }
  ]
  map_environment = {
    "ENV"                = "prod"
    "OTEL_COL_CONFIG" = file("otel-config.yaml")
  }

  map_secrets = {
    "DATADOG_KEY" = "${data.aws_secretsmanager_secret_version.datadog_secret.arn}:api-key::"
    "OODLE_KEY" = data.aws_secretsmanager_secret_version.oodle_secret.arn
  }

  # Specify environment variable that contains the config
  command = ["--config=env:OTEL_COL_CONFIG"]

  log_configuration = {
    "logDriver" = "awslogs"
    "options" = {
      "awslogs-group"         = aws_cloudwatch_log_group.otel_server.name
      "awslogs-region"        = "us-east-2"
      "awslogs-stream-prefix" = "awslogs-otel-server"
    }
  }

  # task_role_arn           = aws_iam_role.pdfgen_server_task.arn
  task_execution_role_arn = aws_iam_role.ecsTaskExecutionRole.arn

  tags = {
    Environment = terraform.workspace
    Application = "otel_server"
  }
}

# Create ECS Fargate service
module "fg_service_otel_server" {
  source = "../common-modules/nv_fg_svc"

  name_prefix = "otel-server"

  vpc_id           = local.default_vpc_id
  ecs_cluster_arn  = local.app_cluster_arn
  ecs_cluster_name = local.app_cluster_name

  task_definition_arn = module.td_otel_server.arn

  private_subnets = [
    local.private_subnet_ids[0],
    local.private_subnet_ids[1],
    local.private_subnet_ids[2],
  ]
  security_groups = [local.default_sg_id]

  service_discovery_namespace_id = local.app_cluster_dns_id

  # Optional tags
  tags = {
    Environment = terraform.workspace
    Application = "otel_server"
  }
}

locals {
  otel_server_fqdn = "${module.fg_service_otel_server.discovery_service_name}.${local.app_cluster_dns_name}"
}
