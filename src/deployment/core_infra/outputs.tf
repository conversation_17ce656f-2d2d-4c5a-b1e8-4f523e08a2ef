output "main_route53_ns" {
  description = "Name Server list for main Route53 Zone"
  value       = aws_route53_zone.main.name_servers
}

# The following outputs have been retained for backwards compatibility.
# They will be removed in a future PR.

output "api_server_repo_url" {
  description = "The URL of the nirvana api-server container repo"
  value       = "667656038718.dkr.ecr.us-east-2.amazonaws.com/api-server-repo"
}

output "gql_api_server_repo_url" {
  description = "The URL of the nirvana GraphQL api-server container repo"
  value       = "667656038718.dkr.ecr.us-east-2.amazonaws.com/gql-api-server-repo"
}

output "sms_scraper_repo_url" {
  description = "The URL of the nirvana sms-scraper container repo"
  value       = "667656038718.dkr.ecr.us-east-2.amazonaws.com/sms-scraper-repo"
}

output "fmcsa_scraper_repo_url" {
  description = "The URL of the nirvana fmcsa-ani-scraper container repo"
  value       = "667656038718.dkr.ecr.us-east-2.amazonaws.com/fmcsa-scraper-repo"
}

output "cms_server_repo_url" {
  description = "The URL of the nirvana cms-server container repo"
  value       = "667656038718.dkr.ecr.us-east-2.amazonaws.com/cms-server"
}
