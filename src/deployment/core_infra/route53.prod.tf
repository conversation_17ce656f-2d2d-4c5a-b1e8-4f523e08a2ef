module "route53_zone_prod" {
  source = "../common-modules/route53_subdomain"

  parent_zone_id    = aws_route53_zone.main.zone_id
  domain_name       = "prod.${var.domain}"
  create_caa_record = true
}

module "acm_prod_ohio" {
  source = "../common-modules/acm"

  domain_name = module.route53_zone_prod.zone_name
  zone_id     = module.route53_zone_prod.zone_id

  subject_alternative_names = [
    "*.${module.route53_zone_prod.zone_name}",
  ]
  wait_for_validation = false
  tags = {
    Environment = "prod"
  }
}

module "acm_prod_virginia" {
  source = "../common-modules/acm"

  providers = {
    aws = aws.virginia
  }

  domain_name = module.route53_zone_prod.zone_name
  zone_id     = module.route53_zone_prod.zone_id

  subject_alternative_names = [
    "*.${module.route53_zone_prod.zone_name}",
  ]
  wait_for_validation = false
  tags = {
    Environment = "prod"
  }
}