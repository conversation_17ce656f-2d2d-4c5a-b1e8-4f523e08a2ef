locals {
  atlassian_cnames = {
    "active" : [
      "atlassian-387373._domainkey.nirvanatech.com",
      "atlassian-387373.dkim.atlassian.net."
    ],
    "fallback" : [
      "atlassian-caef21._domainkey.nirvanatech.com",
      "atlassian-caef21.dkim.atlassian.net."
    ],
    "bounce" : [
      "atlassian-bounces.nirvanatech.com",
      "bounces.mail-us.atlassian.net."
    ]
  }
}

resource "aws_route53_record" "atlassian_cname_active" {
  name = local.atlassian_cnames.active[0]
  records = [
    local.atlassian_cnames.active[1]
  ]
  type            = "CNAME"
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "atlassian_cname_fallback" {
  name = local.atlassian_cnames.fallback[0]
  records = [
    local.atlassian_cnames.fallback[1]
  ]
  type            = "CNAME"
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "atlassian_cname_bounce" {
  name = local.atlassian_cnames.bounce[0]
  records = [
    local.atlassian_cnames.bounce[1]
  ]
  type            = "CNAME"
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}


## Because these were added to terraform after they were created,
## we also need to import them

import {
  to = aws_route53_record.atlassian_cname_active
  id = "${aws_route53_zone.main.zone_id}_${local.atlassian_cnames.active[0]}_CNAME"
}

import {
  to = aws_route53_record.atlassian_cname_fallback
  id = "${aws_route53_zone.main.zone_id}_${local.atlassian_cnames.fallback[0]}_CNAME"
}

import {
  to = aws_route53_record.atlassian_cname_bounce
  id = "${aws_route53_zone.main.zone_id}_${local.atlassian_cnames.bounce[0]}_CNAME"
}