# This file contains the resource specifications to authenticate our
# domain with Sendgrid.

# TODO: refactor. Fetch these hard coded CNAMEs from secrets manager

locals {
  sendgrid_cnames = [
    ["em2495.nirvanatech.com", "u22610103.wl104.sendgrid.net"],
    ["s1._domainkey.nirvanatech.com", "s1.domainkey.u22610103.wl104.sendgrid.net"],
    ["s2._domainkey.nirvanatech.com", "s2.domainkey.u22610103.wl104.sendgrid.net"],
    ["url3376.nirvanatech.com", "sendgrid.net"],
    ["22610103.nirvanatech.com", "sendgrid.net"]
  ]
}

resource "aws_route53_record" "sendgrid_auth" {
  count = length(local.sendgrid_cnames)

  name            = local.sendgrid_cnames[count.index][0]
  records         = [local.sendgrid_cnames[count.index][1]]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}