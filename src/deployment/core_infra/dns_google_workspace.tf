resource "aws_route53_record" "mx_google_workspace" {
  zone_id = aws_route53_zone.main.zone_id
  name    = aws_route53_zone.main.name

  type = "MX"
  ttl  = "3600"
  records = [
    "1 ASPMX.L.GOOGLE.COM",
    "5 ALT1.ASPMX.L.GOOGLE.COM",
    "5 ALT2.ASPMX.L.GOOGLE.COM",
    "10 ALT3.ASPMX.L.GOOGLE.COM",
    "10 ALT4.ASPMX.L.GOOGLE.COM",
  ]
}

resource "aws_route53_record" "dkim_main" {
  zone_id = aws_route53_zone.main.zone_id
  name    = "google._domainkey"

  type    = "TXT"
  ttl     = "3600"
  records = [data.aws_secretsmanager_secret_version.dkim_google_domain_main.secret_string]
}
