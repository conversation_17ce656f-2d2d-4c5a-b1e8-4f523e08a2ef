# This file contains the resource specifications to authenticate our
# domain with hubspot.
locals {
  hs_domainkey_base          = "23878994"
  hs_domainkey_marketing     = "${local.hs_domainkey_base}m"
  hs_domainkey_transactional = "${local.hs_domainkey_base}t"
}

# Enables the `insurance` sub-domain on hubspot.
resource "aws_route53_record" "insurance_cname" {
  name = "insurance.${var.domain}"
  records = [
    "${local.hs_domainkey_base}.group44.sites.hubspot.net"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

# The following two CNAME records enable our root domain
# (nirvanatech.com) as an email sending domain on hubspot.
# https://knowledge.hubspot.com/domains-and-urls/connect-your-email-sending-domain
resource "aws_route53_record" "hubspot_cname_hs1" {
  name = "hs1-${local.hs_domainkey_base}._domainkey.${var.domain}"
  records = [
    "nirvanatech-com.hs05a.dkim.hubspotemail.net"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_cname_hs2" {
  name = "hs2-${local.hs_domainkey_base}._domainkey.${var.domain}"
  records = [
    "nirvanatech-com.hs05b.dkim.hubspotemail.net"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

# We also provision a few DNS records to verify our domain with hubspot for a few
# dedicated IP addresses used for marketing emails and transactional emails.
# https://knowledge.hubspot.com/email/connect-a-dedicated-ip-address-for-sending-emails


# Dedicated IP address for marketing emails
# 5 resources: One A record, one MX record, one TXT record, and two CNAME records.

resource "aws_route53_record" "hubspot_dedicatedip_a" {
  name = "bid4613.${local.hs_domainkey_marketing}.${var.domain}"
  records = [
    "**************"
  ]
  type            = "A"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_dedicatedip_mx" {
  name = "${local.hs_domainkey_marketing}.${var.domain}"
  records = [
    "0 mx.hubapi.com"
  ]
  type            = "MX"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_dedicatedip_txt" {
  name = "${local.hs_domainkey_marketing}.${var.domain}"
  records = [
    "v=spf1 include:${local.hs_domainkey_base}.spf07.hubspotemail.net -all"
  ]
  type            = "TXT"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_dedicatedip_cname_hs1" {
  name = "hs1._domainkey.${local.hs_domainkey_marketing}.${var.domain}"
  records = [
    "${local.hs_domainkey_marketing}-nirvanatech-com.hs01a.dkim.hubspotemail.net"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_dedicatedip_cname_hs2" {
  name = "hs2._domainkey.${local.hs_domainkey_marketing}.${var.domain}"
  records = [
    "${local.hs_domainkey_marketing}-nirvanatech-com.hs01b.dkim.hubspotemail.net"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}


# Transactional emails
# 5 resources: One A record, one MX record, one TXT record, and two CNAME records.

resource "aws_route53_record" "hubspot_transactional_a" {
  name = "bid48b3.${local.hs_domainkey_transactional}.${var.domain}"
  records = [
    "**************"
  ]
  type            = "A"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_transactional_mx" {
  name = "${local.hs_domainkey_transactional}.${var.domain}"
  records = [
    "0 mx.hubapi.com"
  ]
  type            = "MX"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_transactional_txt" {
  name = "${local.hs_domainkey_transactional}.${var.domain}"
  records = [
    "v=spf1 include:${local.hs_domainkey_base}.spf06.hubspotemail.net -all"
  ]
  type            = "TXT"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_transactional_cname_hs1" {
  name = "hs1._domainkey.${local.hs_domainkey_transactional}.${var.domain}"
  records = [
    "${local.hs_domainkey_transactional}-nirvanatech-com.hs01a.dkim.hubspotemail.net"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "hubspot_transactional_cname_hs2" {
  name = "hs2._domainkey.${local.hs_domainkey_transactional}.${var.domain}"
  records = [
    "${local.hs_domainkey_transactional}-nirvanatech-com.hs01b.dkim.hubspotemail.net"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}
