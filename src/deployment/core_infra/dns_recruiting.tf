locals {
  recruiting_subdomain = "recruiting.${var.domain}"
}

module "route53_zone_recruiting" {
  source = "../common-modules/route53_subdomain"

  parent_zone_id = aws_route53_zone.main.zone_id
  domain_name    = local.recruiting_subdomain
}

module "acm_recruiting_ohio" {
  source = "../common-modules/acm"

  domain_name = module.route53_zone_recruiting.zone_name
  zone_id     = module.route53_zone_recruiting.zone_id

  subject_alternative_names = [
    "*.${module.route53_zone_recruiting.zone_name}",
  ]
  wait_for_validation = false
  tags = {
    Environment = "recruiting"
  }
}

module "acm_recruiting_virginia" {
  source = "../common-modules/acm"

  providers = {
    aws = aws.virginia
  }

  domain_name = module.route53_zone_recruiting.zone_name
  zone_id     = module.route53_zone_recruiting.zone_id

  subject_alternative_names = [
    "*.${module.route53_zone_recruiting.zone_name}",
  ]
  wait_for_validation = false
  tags = {
    Environment = "recruiting"
  }
}

resource "aws_route53_record" "mx_recruting" {
  zone_id = module.route53_zone_recruiting.zone_id
  name    = local.recruiting_subdomain


  type = "MX"
  ttl  = "3600"
  records = [
    "10 mxa.mailgun.org",
    "10 mxb.mailgun.org",
  ]
}

resource "aws_route53_record" "spf_recruting" {
  zone_id = module.route53_zone_recruiting.zone_id
  name    = local.recruiting_subdomain


  type    = "TXT"
  ttl     = "3600"
  records = ["v=spf1 include:mailgun.org ~all"]
}

resource "aws_route53_record" "dkim_recruting" {
  zone_id = module.route53_zone_recruiting.zone_id
  name    = "k1._domainkey.${local.recruiting_subdomain}"


  type    = "TXT"
  ttl     = "3600"
  records = [data.aws_secretsmanager_secret_version.dkim_recruting.secret_string]
}


resource "aws_route53_record" "email_cname_recruting" {
  zone_id = module.route53_zone_recruiting.zone_id
  name    = "email.${local.recruiting_subdomain}"


  type    = "CNAME"
  ttl     = "3600"
  records = ["mailgun.org"]
}
