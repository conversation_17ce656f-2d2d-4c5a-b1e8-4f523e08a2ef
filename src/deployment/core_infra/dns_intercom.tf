# This file contains the resource specifications to authenticate our
# domain with Intercom.

resource "aws_route53_record" "intercom_domain_key_cname" {
  name = "intercom._domainkey.${var.domain}"
  records = [
    "91b0353f-4292-4660-b3e2-89f2b15e6217.dkim.intercom.io"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "intercom_outbound_cname" {
  name = "outbound.intercom.${var.domain}"
  records = [
    "rp.nirvana-insurance.intercom-mail.com"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}