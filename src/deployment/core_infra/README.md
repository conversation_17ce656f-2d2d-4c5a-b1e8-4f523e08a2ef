# `core_infra` module

This module deploys the core infra required to bring up other services
on AWS.

This includes things like hosted zones, acm certificates, container registries,
infra buckets etc.

> NOTE: We create ACM certificates in multiple regions (currently `us-east-1`, and `us-east-2`).
> This is because `CloudFront` needs certificates in `us-east-1` (Virginia), and `ELB` needs them
> to be in the same region as the `ELB`.

<!-- BEGIN_TF_DOCS -->




## Outputs

| Name | Description |
|------|-------------|
| <a name="output_api_server_repo_url"></a> [api\_server\_repo\_url](#output\_api\_server\_repo\_url) | The URL of the nirvana api-server container repo |
| <a name="output_cms_server_repo_url"></a> [cms\_server\_repo\_url](#output\_cms\_server\_repo\_url) | The URL of the nirvana cms-server container repo |
| <a name="output_fmcsa_scraper_repo_url"></a> [fmcsa\_ani\_scraper\_repo\_url](#output\_fmcsa\_ani\_scraper\_repo\_url) | The URL of the nirvana fmcsa-scraper container repo |
| <a name="output_gql_api_server_repo_url"></a> [gql\_api\_server\_repo\_url](#output\_gql\_api\_server\_repo\_url) | The URL of the nirvana GraphQL api-server container repo |
| <a name="output_main_route53_ns"></a> [main\_route53\_ns](#output\_main\_route53\_ns) | Name Server list for main Route53 Zone |
| <a name="output_sms_scraper_repo_url"></a> [sms\_scraper\_repo\_url](#output\_sms\_scraper\_repo\_url) | The URL of the nirvana sms-scraper container repo |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_acm_dev_ohio"></a> [acm\_dev\_ohio](#module\_acm\_dev\_ohio) | ./modules/acm | n/a |
| <a name="module_acm_dev_virginia"></a> [acm\_dev\_virginia](#module\_acm\_dev\_virginia) | ./modules/acm | n/a |
| <a name="module_acm_main_ohio"></a> [acm\_main\_ohio](#module\_acm\_main\_ohio) | ./modules/acm | n/a |
| <a name="module_acm_main_virginia"></a> [acm\_main\_virginia](#module\_acm\_main\_virginia) | ./modules/acm | n/a |
| <a name="module_acm_prod_ohio"></a> [acm\_prod\_ohio](#module\_acm\_prod\_ohio) | ./modules/acm | n/a |
| <a name="module_acm_prod_virginia"></a> [acm\_prod\_virginia](#module\_acm\_prod\_virginia) | ./modules/acm | n/a |
| <a name="module_route53_zone_dev"></a> [route53\_zone\_dev](#module\_route53\_zone\_dev) | ./modules/route53_subdomain | n/a |
| <a name="module_route53_zone_prod"></a> [route53\_zone\_prod](#module\_route53\_zone\_prod) | ./modules/route53_subdomain | n/a |
| <a name="module_www_website"></a> [www\_website](#module\_www\_website) | ../common-modules/static_website | n/a |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_domain"></a> [domain](#input\_domain) | Domain registered in the hosted zone on Route 53 | `string` | `"nirvanatech.com"` | no |
| <a name="input_spf_domain_main"></a> [spf\_domain\_main](#input\_spf\_domain\_main) | SPF value for the domain | `string` | `"v=spf1 include:_spf.google.com ~all"` | no |
| <a name="input_vercel_root_a_record"></a> [vercel\_root\_a\_record](#input\_vercel\_root\_a\_record) | n/a | `string` | `"***********"` | no |
| <a name="input_vercel_www_cname_record"></a> [vercel\_www\_cname\_record](#input\_vercel\_www\_cname\_record) | n/a | `string` | `"cname.vercel-dns.com"` | no |

## Resources

| Name | Type |
|------|------|
| [aws_ecr_repository.api_server](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.boards_server](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.cms_server](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.datascience](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.fmcsa_scraper](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.gql_api_server](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.job_processor](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.metabase_server](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.mvr_cache_server](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.pdfgen_server](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.sms_scraper](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecr_repository.telematics_server](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_route53_record.cname_greenhouse](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.dkim_greenhouse](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.dkim_main](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.intercom_domain_key_cname](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.intercom_outbound_cname](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.main_website_root](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.main_website_www](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.mx_google_workspace](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.mx_greenhouse](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.sendgrid_auth](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.spf_greenhouse](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_record.spf_main](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_route53_zone.main](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_zone) | resource |
| [aws_s3_bucket.snowflake_stage](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket) | resource |


<!-- END_TF_DOCS -->

