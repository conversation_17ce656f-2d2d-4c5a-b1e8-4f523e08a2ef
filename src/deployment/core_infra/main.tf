resource "aws_s3_bucket" "snowflake_stage" {
  bucket = "nirvana-snowflake-stage"
}

resource "aws_s3_bucket_acl" "snowflake_stage" {
  bucket = aws_s3_bucket.snowflake_stage.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "snowflake_stage" {
  bucket = aws_s3_bucket.snowflake_stage.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 bucket to function as a remote cache proxy for bazel-remote
# https://github.com/buchgr/bazel-remote
resource "aws_s3_bucket" "bazel_remote_cache" {
  bucket = "nirvana-bazel-remote-cache"
}

resource "aws_s3_bucket_acl" "bazel_remote_cache" {
  bucket = aws_s3_bucket.bazel_remote_cache.id
  acl    = "private"
}
