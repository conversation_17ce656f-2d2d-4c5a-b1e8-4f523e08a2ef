# This file contains the resource specifications to set up a webflow
# deployment as our "main" website.

resource "aws_route53_record" "main_website_root" {
  name            = ""
  records         = [
    "75.2.70.75",
    "99.83.190.102",
  ]
  type            = "A"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}


resource "aws_route53_record" "main_website_www" {
  name            = "www"
  records         = ["proxy-ssl.webflow.com"]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

# Decprecated module.
# TODO: Remove this.
module "www_website" {
  source                  = "../common-modules/static_website"
  create_dns_entries      = false
  no_aliases              = true
  website_domain          = "nirvanatech.com"
  website_domain_redirect = "www.nirvanatech.com"
  route53_zone_id         = aws_route53_zone.main.zone_id
  acm_certificate_arn     = module.acm_main_virginia.arn
  index_doc_path          = "index.html"
  error_doc_path          = "404.html"
  tags = {
    Environment = "prod"
  }
}