module "route53_zone_dev" {
  source = "../common-modules/route53_subdomain"

  parent_zone_id    = aws_route53_zone.main.zone_id
  domain_name       = "dev.${var.domain}"
  create_caa_record = true
}

module "acm_dev_ohio" {
  source = "../common-modules/acm"

  domain_name = module.route53_zone_dev.zone_name
  zone_id     = module.route53_zone_dev.zone_id

  subject_alternative_names = [
    "*.${module.route53_zone_dev.zone_name}",
    "*.training.${module.route53_zone_dev.zone_name}"
  ]
  wait_for_validation = false
  tags = {
    Environment = "dev"
  }
}

module "acm_dev_virginia" {
  source = "../common-modules/acm"

  providers = {
    aws = aws.virginia
  }

  domain_name = module.route53_zone_dev.zone_name
  zone_id     = module.route53_zone_dev.zone_id

  subject_alternative_names = [
    "*.${module.route53_zone_dev.zone_name}",
    "*.training.${module.route53_zone_dev.zone_name}"
  ]
  wait_for_validation = false
  tags = {
    Environment = "dev"
  }
}
