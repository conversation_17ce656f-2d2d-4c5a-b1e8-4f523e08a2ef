resource "aws_route53_record" "mx_greenhouse" {
  zone_id = aws_route53_zone.main.zone_id
  name    = "gh-mail.${var.domain}"


  type = "MX"
  ttl  = "3600"
  records = [
    "10 mxa.mailgun.org",
    "10 mxb.mailgun.org",
  ]
}

resource "aws_route53_record" "spf_greenhouse" {
  zone_id = aws_route53_zone.main.zone_id
  name    = "gh-mail.${var.domain}"

  type = "TXT"
  ttl  = "3600"
  records = [
    "v=spf1 include:mg-spf.greenhouse.io ~all"
  ]
}

resource "aws_route53_record" "dkim_greenhouse" {
  zone_id = aws_route53_zone.main.zone_id
  name    = "krs._domainkey.gh-mail.${var.domain}"

  type = "TXT"
  ttl  = "3600"
  records = [
    data.aws_secretsmanager_secret_version.dkim_greenhouse_domain.secret_string
  ]
}

resource "aws_route53_record" "cname_greenhouse" {
  name = "email.gh-mail.${var.domain}"
  records = [
    "mailgun.org"
  ]
  type            = "CNAME"
  allow_overwrite = true
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}