locals {
  salesforce_cnames = {
    "primary" = [
      "nirvanatech._domainkey.nirvanatech.com",
      "nirvanatech.kpaome.custdkim.salesforce.com.",
    ],
    "secondary" = [
      "nirvanatech2._domainkey.nirvanatech.com",
      "nirvanatech2.jvpg67.custdkim.salesforce.com.",
    ],
  }
}

resource "aws_route53_record" "salesforce_cname_primary" {
  name            = local.salesforce_cnames.primary[0]
  records         = [local.salesforce_cnames.primary[1]]
  type            = "CNAME"
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

resource "aws_route53_record" "salesforce_cname_secondary" {
  name            = local.salesforce_cnames.secondary[0]
  records         = [local.salesforce_cnames.secondary[1]]
  type            = "CNAME"
  zone_id         = aws_route53_zone.main.zone_id
  ttl             = 300
}

## Because these were added to terraform after they were created,
## we also need to import them

import {
  to = aws_route53_record.salesforce_cname_primary
  id = "${aws_route53_zone.main.zone_id}_${local.salesforce_cnames.primary[0]}_CNAME"
}

import {
  to = aws_route53_record.salesforce_cname_secondary
  id = "${aws_route53_zone.main.zone_id}_${local.salesforce_cnames.secondary[0]}_CNAME"
}
