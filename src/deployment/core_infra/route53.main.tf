locals {
  main_spf_records = [
    "include:_spf.google.com",
    "include:sendgrid.net",
    "include:_spf.atlassian.net",
    "include:23878994.spf07.hubspotemail.net",
    "include:23878994.spf06.hubspotemail.net",
  ]
  non_spf_txt_recrods = [
    "apple-domain-verification=8X83yvTqJgWa5Xk0",
    "atlassian-sending-domain-verification=************************cad2d03a9e08",
    "atlassian-domain-verification=0ByYknwSBgF2aEsrZD7t8DTNOMIntSxnD9AJCnsUFudiQvRBaYi29YL7VwikhDag",
  ]
}

resource "aws_route53_zone" "main" {
  name = var.domain
}


# TODO: Rename this to "txt_main" since it's not just for SPF
resource "aws_route53_record" "spf_main" {
  zone_id = aws_route53_zone.main.zone_id
  name    = aws_route53_zone.main.name

  type = "TXT"
  ttl  = "3600"
  records = concat(
    # SPF records for google workspace and other email providers
    [format("v=spf1 %s ~all", join(" ", local.main_spf_records))],
    # Additional TXT records
    local.non_spf_txt_recrods,
  )
}


module "acm_main_ohio" {
  source = "../common-modules/acm"

  domain_name = aws_route53_zone.main.name
  zone_id     = aws_route53_zone.main.zone_id

  subject_alternative_names = [
    "*.${aws_route53_zone.main.name}",
  ]
  wait_for_validation = false
}

module "acm_main_virginia" {
  source = "../common-modules/acm"

  providers = {
    aws = aws.virginia
  }

  domain_name = aws_route53_zone.main.name
  zone_id     = aws_route53_zone.main.zone_id

  subject_alternative_names = [
    "*.${aws_route53_zone.main.name}",
  ]
  wait_for_validation = false
}
