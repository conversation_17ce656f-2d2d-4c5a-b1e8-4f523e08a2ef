variable "domain" {
  description = "Domain registered in the hosted zone on Route 53"
  default     = "nirvanatech.com"
  type        = string
}

# Retrieve Google workspace DKIM
data "aws_secretsmanager_secret" "dkim_google_domain_main_secrets" {
  name = "google-workspace-dkim"
}

data "aws_secretsmanager_secret_version" "dkim_google_domain_main" {
  secret_id = data.aws_secretsmanager_secret.dkim_google_domain_main_secrets.id
}


# Retrieve Greenhouse DKIM
data "aws_secretsmanager_secret" "dkim_greenhouse_domain_secrets" {
  name = "greenhouse-dkim"
}

data "aws_secretsmanager_secret_version" "dkim_greenhouse_domain" {
  secret_id = data.aws_secretsmanager_secret.dkim_greenhouse_domain_secrets.id
}

# Retrieve Recruiting DKIM
data "aws_secretsmanager_secret" "dkim_recruting" {
  name = "recruiting-dkim"
}

data "aws_secretsmanager_secret_version" "dkim_recruting" {
  secret_id = data.aws_secretsmanager_secret.dkim_recruting.id
}
