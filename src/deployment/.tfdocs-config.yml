# See https://terraform-docs.io/user-guide/configuration/ for docs

formatter: markdown table

header-from: main.tf

sections:
  show:
    - header
    - inputs
    - outputs
    - modules
    - resources
    - footer

content: |-
  {{ .Header }}

  {{ .Providers }}

  {{ .Outputs }}

  {{ .Modules }}

  {{ .Inputs }}

  {{ .Resources }}

  {{ .Footer }}

output:
  file: "README.md"
  mode: inject
  template: |-
    <!-- BEGIN_TF_DOCS -->
    {{ .Content }}
    <!-- END_TF_DOCS -->

sort:
  enabled: true
  by: required
