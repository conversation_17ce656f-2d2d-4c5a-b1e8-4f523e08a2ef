We use terraform for deployment.

## Setup

Install the awscli and terraform using `src/scripts/install_awscli.sh`
and `src/scripts/install_terraform.sh`.

Once the dependencies are installed, configure aws cli using
    `aws configure`

Optionally verify that things work by running ls on s3 buckets
    `aws s3 ls`

## Terraform setup

There are currently two top level terraform modules, `app` and `core_infra`.
To deploy either of the two, first initialize terraform in that directory.
    `terraform init`

To make a change, first verify the changes that terraform will make,
    `terraform plan`


If the changes look good, apply them
    `terraform apply`

Note that we use S3 as a backend for terraform, which means that the state of
our deployment is stored outside the repo. This allows multiple people to
make modifications and deploy using terraform.

## Services

We currently have one ECS cluster (`default_app_cluster`) containing the following services:

- `api_server` - REST API server
- `gql_server` - GraphQL API server
- `sms_scraper` - On demand service to scrape SMS (safety measurement system) data

Since we currently don't tag our images with a unique identifier, we need to ["taint"](https://www.terraform.io/docs/cli/commands/taint.html)
a service's task defintion before being able to re-deploy it with terraform.

Command to taint:

```sh
terraform taint aws_ecs_task_definition.<service_name>
```