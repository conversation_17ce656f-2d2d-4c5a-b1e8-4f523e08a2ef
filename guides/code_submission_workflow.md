# Code Submission Workflow

## Trunk Based Development

* All code is merged to trunk (i.e. branch `main`) as soon as possible. 
* There are no long lived feature branches.
* There is no separate “integration” branch or a code freeze to allow merges.
* `main` is always deployable. If there are issues found in production, fixes are promptly checked in to a short lived hot-patch branch until the next release.
* Typically, code is deployed to production without human intervention and as soon as possible.

[This article by Facebook](https://secure.phabricator.com/book/phabflavor/article/recommendations_on_branching/) makes a
case for abandoning all feature branch based development. Of course, this doesn’t mean that engineers can’t use branches for
their own work, just that long lived feature branches shouldn’t be used to collaborate and work on big features outside
the main branch.

## Linear History

We use [Github](https://github.com/nirvanatech/nirvana/) to run our code submission and code review workflow.
We implement phabricator’s opinionated [“One Idea is One Commit”](https://secure.phabricator.com/book/phabflavor/article/recommendations_on_revision_control/)
while merging code to Github. A brief overview of the philosophy:

* One commit to `main` represents one “idea”, for eg, adding a new API endpoint to a service, or adding a new modal to a UI page.
* Each commit can, in most cases, be independently verified through automated or manual tests.
* The main branch exhibits a linear history, i.e., there are no “merge commits” on the branch.
* Each commit on the `main` branch is theoretically deployable, i.e., every commit passes CI tests.

## Optimize for reviewability

While authoring a PR, consider how easy or hard is for another engineer to review it.
Optimize for your reviewers being able to give you quick and meaningful feedback.
Some things that help:

* If a PR does two distinct things, consider creating breaking it into two PRs. For example, if you add
  a new service, consider adding the service interface in one PR, and the deployment code in another. This
  way, you can get targeted reviews on both without having the infra reviewer wade through application code 
  and vice versa.
* Write meaningful commit messages. Follow https://chris.beams.io/posts/git-commit/, which is fairly prescriptive
  and has good examples as well. Note that Github by default will create a description from the various commit messages
  while squashing and merging. Manually override it to write a meaningful description.
* Always get a review. We are a young team, and there might be temptation to “just push the code”. But it’s usually helpful
  to get a second pair of eyes involved. If nothing else, it makes it easier for folks to know what’s going on
  and build context. When a PR is in a reviewable shape, post your review request on #eng on Slack. If you want
  someone specific to review it, tag them in the post or comments.

