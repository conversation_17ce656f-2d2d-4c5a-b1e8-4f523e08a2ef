# Setting up python

## pipenv

We use [pipenv](https://pipenv.pypa.io/en/latest/) to manage our python dependencies.

> The `Pipfile` is located in the `src` folder, which is the root for all python
projects (by convention). Since `pipenv` automatically loads environment variables
from the `.env` file (if present), We utilize that to set the project root directory to `PYTHONPATH`.

### Set-up

- Install python: https://www.python.org/downloads/

- Install pip (if not already installed): https://pip.pypa.io/en/stable/installation/

- Install pipenv: https://pipenv.pypa.io/en/latest/#install-pipenv-today

To install the required packages, execute `pipenv install` in the project root (`nirvana/src/`)

> Pipenv automatically maps projects to their specific [virtualenvs](https://docs.python.org/3/tutorial/venv.html).
The virtualenv is stored globally with the name of the project’s root directory plus the hash
of the full path to the project’s root (e.g., `src-a3de50`). See [pipenv docs](https://pipenv.pypa.io/en/latest/install/)
for more information.


### Usage

There are two ways to use the installed packages with `pipenv`.

- `pipenv run`: ensures that your installed packages are available to your script.

  Example:
  
  ```sh
  $ pipenv run python ds/examples/config_loader/main.py
  ```

- `pipenv shell`: It is also possible to spawn a new shell that ensures all commands have access
to your installed packages.

  Example:
  
  ```sh
  $ pipenv shell
  # activates virtualenv
  (src)$ python ds/examples/config_loader/main.py
  ```

## Proto definitions

The script `py_protogen.sh` in the project root folder should be executed
**WHENEVER** any `proto` definitions (required by python packages) are added/updated.

> This script is also executed as part of the CI to ensure that python proto
definitions in `main` are always compatible with the underlying `.proto` definitions.