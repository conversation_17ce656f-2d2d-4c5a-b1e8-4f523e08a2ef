## MacOS Dev environment setup

- Make sure you have access to our monorepo github.com/nirvanatech/nirvana
- Run ansible-setup/dev-bootstrap.sh from root.
- To complete the setup you also need credentials for AWS. Ask your manager for them. [You would need AWS Access ID and Secret]
  Skip setting up AWS in the script and come back to it, if you do not have them handy
- To run tests you will need to install Docker, this means your machine must
  support virtualisation. If you are already inside a VM, this may mean
  trouble _in some cases_.

### Some fixes or errors you might come across
- all.yaml not found => Update dev-bootstrap.sh for getting it correct, primarly due to path issue.
- Java path is not set => Ensure java is installed, add it to the PATH.
- Java link issue/version issue => It works fine with version 23:
`sudo ln -sfn /opt/homebrew/opt/openjdk/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk.jdk`

### How do you ensure things are running well?
- Backend `bazel run //nirvana/api-server/cmd/api_server_local`
- Frontend `cd client;  yarn install;  yarn start:underwriter;` (check for scripts in package.json)

## Old Dev environment setup

Leaving this section as is to support installs in other systems apart from MacOS
The recommended way to setup on MacOS is run ansible-setup/dev-bootstrap.sh
The script would guide through all the steps and installations required

Notes:
- Make sure you have access to our monorepo github.com/nirvanatech/nirvana
- To complete the setup you also need credentials for AWS, if you don't have
  the credentials you will need to uncomment some lines in `one_time_setup.sh`
- To run tests you will need to install Docker, this means your machine must
  support virtualisation. If you are already inside a VM, this may mean
  trouble _in some cases_.
- You should be in Unix-based system (OSX or Linux). For windows please install
  WSL before proceeding
  script that you will run as part of setup.
- If any script fails, please drop a note to anyone in our team to have it
  fixed, as well as do not attempt re-running the script blindly because
  our scripts are not vetted for idempotency.
- It is recommended that you first read any script before you run it, it is
  okay to pick your mentor/buddy's brains to understand the lines in script
  that you do not understand, as some of those correspond to our internal
  tools. If you want a peaceful life, DON'T run scripts before you vet them.

On Linux:
- Install golang 1.18 from https://golang.org/dl/
  (may need to look into archived versions)
- Install any version of bazelisk(a bazel launcher) by following
  https://github.com/bazelbuild/bazelisk/blob/master/README.md and symlink it
  to bazel somewhere in your path.
- Install postgres 13 & psql

You also need Docker installed in your system locally to run our tests, as in
most of our tests we run postgresql locally in a test container. For installing
Docker follow instructions at their [official
website](https://docs.docker.com/engine/install/) for both OSX and Linux.

For linux, follow the deps install by running
`src/scripts/one_time_setup.sh` to complete the setup. This script (read it)
will setup databases on your system and prepare some config files for you.

To verify that everything is setup, and you are ready to roll:
1. Run `psql nirvana`, you should be able to connect to nirvana DB.
  1. Verify that running `\dt` in psql lists a bunch of tables.
2. Run `go version` and verify 1.18 is printed
3. Run `bazel --version` and verify bazel is installed
4. Verify that you are able to build & test the entire golang code by following
   instructions mentioned in section on [builds][#builds]. The first time you
   run this script it could take upto 15-20 minutes depending on your network
   speed / system specs. Subsequent runs will be much faster.

### Configs

#### AWS

For both mac and linux, ask IT to provide aws credentials. These
should live in `~/.aws/credentials`.

### IDE

Jetbrain's Goland is the preferred IDE for Golang development. Once Goland is
installed, "Open" a project and point to `src/nirvana/`. This is where the
`go.mod` root is located, and Goland should automatically pick it up.

### SSH

TODO

#### Goland live templates

Goland allows us to configure custom
[live templates](https://www.jetbrains.com/help/go/using-live-templates.html)
to insert common constructs into our code, such as loops, conditions, various
declarations, or print statements.

There are a few custom templates we use to generate boilerplate code:

- [API server handler](./goland/live-templates/api-server-handler.md)
- [API server interceptor](./goland/live-templates/api-server-interceptor.md)

#### Linter

We use golangci-lint for linting our go code.
- Install it for your platform by following the link
  https://golangci-lint.run/usage/install/#local-installation
  ( `brew install golangci-lint` for MacOS)
- Follow instructions mentioned at
  https://golangci-lint.run/usage/integrations/ to setup golangci-lint for your
  editor. Following steps work for Goland on MacOS:
	- Install "Go Linter" plugin.
	- Goto `Preferences > Tools > File Watcher`
	- Add `golangci-lint` File Watcher by clicking on *+*
	- When adding File Watcher, provide following arguments:
	  `run --enable-only=errcheck $FileDir$` (for older version of golangci-lint 1.x.x use `run --disable-all --enable=errcheck $FileDir$`)

Use the following command to see lint errors on CLI
( Should be run from `src/nirvana`)

```sh golangci-lint run --disable-all --enable=errcheck --tests=false ```

#### Goimports

We use goimports to format our code. Following steps work for Goland on MacOS:
- Go to `Preferences > Tools > File Watcher`
- Add `goimports` by clicking on *+* and enable it adding the following
  arguments, `-local nirvanatech.com/ -w $FilePath$`
- Image reference
  [here](https://user-images.githubusercontent.com/15318933/140177533-37cfe6db-375d-4ed1-b521-54ffd8f199de.png)

### Builds

We use bazel for builds.

To build & test all packages, you can run
`task bazel:build-all`.

To build / test a specific package you can do
```
bazel build //nirvana/your/package/name
# recursive
bazel build //nirvana/top/level/package/...
```
Replace build with `test` in above commands to run tests associated with those
packages via bazel. You can also replace it with `run` to run that package (if
it represents a runnable binary that is). The part `//nirvana/<...>` is called
a 'label' in bazel-speak, for more details on how to construct such labels to
command bazel at your whim, you will need to go through bazel docs. A cursory
glance should be sufficient.

Fortunately for most everyday usecases you don't need to know anything about
writing bazel files because we autogenerate them. We use Gazelle for
autogenerating bazel build files for Go source code. How it works is that
gazelle is a program that parses your `.go` source files, and their import
statements specifically, to understand the dependencies between different
golang files and packages, and then writes `.bazel` files capturing those
dependencies explicitly. Bazel can then read these bazel files to build the
entire dependency graph of our code for building and testing.

In practice whenever you make a code change, you can run
`task gazelle:build` to run gazelle which will update / generate necessary
bazel files to build & test the code. **Initially it may be a good idea to run this script
literally everytime you are about to run/commit your code, but over time you
will grok exactly on what changes do you need to run it saving you some time.**

## Deploying

TODO

