# Logging in Golang

## Library

We use uber's [zap](https://github.com/uber-go/zap) for logging in
our codebase, but there's a wrapper [package](../src/nirvana/common-go/log) and
users should never need to import zap or zapcore directly. If you have some
usecase where wrapper does not suffice, then enhance the wrapper.

See the end of this doc for why we went with this library.

## Format

We use structured logging.

A typical log may look like:
```json
{"level":"info","ts":1628640110.209404,"caller":"log/log_test.go:104","msg":"This is a sample log","foo":{"item":5,"V":"hello"}}
```

Take a look at [log.go](../src/nirvana/common-go/log/log.go) and 
[log_test.go](../src/nirvana/common-go/log/log_test.go)

Following is a non-exhaustive list of things to keep in mind when adding logs:
- We prefer using `Plain` instead of Sugared as much as possible.`Info`, `Error`,
  `Fatal` et.al are wrappers around `WithContext`.
- `msg` should be a proper English statement with capital first letter and no
  fullstop at the the end.
- Prefer logging as json field instead of cramming into `msg`.
  eg:
  
   ```golang
   log.Plain.Info("Failed to fetch URL",
   	log.String("url", url),
   	log.Int("attempt", 3),
   	log.Duration("backoff", time.Second),
   	log.Error(err),
   )
   // is preffered over 
   log.Sugared.Infof(
   	 "failed to fetch URL %s in attempt %d after backoff %v", url, 3, time.Second,
   ) 
   // or
   log.Plain.Info(fmt.Sprintf("failed to fetch URL %s in attempt %d after backoff %v", url, 3, time.Second)) 
   ```
  
  some other good examples:
  
  ```golang
  log.Info(
  	ctx,
  	"Could not open file",
  	log.String("file", filePath),
  	log.Error(err),
  )
  log.WithContext(ctx).Info(
  	"Task failed",
  	log.String("taskId", task.Id),
  	log.Int("retryAttempt", 0),
  	log.NamedError("failureError", err),
  )
  log.WithContext(ctx).Info(
  	"Request timed out",
  	log.Any("request", request),
  	log.Duration("timeout", timeout),
  	log.Error(err),
  )
  ```
- Prefer nesting instead of complex key names.
  eg:
  
  ```golang
  log.WithContext(ctx).Info(
  	"Received request",
  	log.Any("request", request),
  )
  // is preffered over
  log.WithContext(ctx).Info(
  	"Recieved request",
  	log.String("requestMethod", "GET"),
  	log.String("requestData", `{"foo": "bar"}`),
  )
  ```

## Library Alternatives
This section was true as of Aug 10 2021

We took a look at different solutions but only
[logrus](https://github.com/sirupsen/logrus),
[zerolog](https://github.com/rs/zerolog) and
[zap](https://github.com/uber-go/zap) were given serious thought. Rest
had two or more of:
- too limited( no structured or leveled logging etc.)
- too slow
- limited adoption. 

See [this comparison](https://www.client9.com/logging-packages-in-golang/) for a
summary.

### [logrus](https://github.com/sirupsen/logrus)
Logrus currently has the largest adoption, but 
from their [README](https://github.com/sirupsen/logrus/blob/master/README.md)
(emphasis mine)
> Logrus is in maintenance-mode. We will not be introducing new features. It's
> simply too hard to do in a way that won't break many people's projects, which
> is the last thing you want from your Logging library (again...).
> 
> This does not mean Logrus is dead. Logrus will continue to be maintained for
> security, (backwards compatible) bug fixes, and performance (where we are
> limited by the interface).
> 
> I believe Logrus' biggest contribution is to have played a part in today's
> widespread use of structured logging in Golang. There doesn't seem to be a
> reason to do a major, breaking iteration into Logrus V2, since the fantastic
> Go community has built those independently. Many fantastic alternatives have
> sprung up. **Logrus would look like those, had it been re-designed with what we
> know about structured logging in Go today. Check out, for example, Zerolog,
> Zap, and Apex.**

### [zerolog](https://github.com/rs/zerolog)
- zerolog is comparable in performance to zap, but it has much lesser adoption
than zap. This might be relevant in case we want to use opentelemetry for
logs also in the future.

- In addition, zap comes with some batteries like:
  - sugared logger which is easier to use and has performance comparable to
    zerolog.
  - automated http server for changing logging levels online.
  - Lots of config options, some of which we may end up using.
