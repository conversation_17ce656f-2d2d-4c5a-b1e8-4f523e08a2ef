# Errors in Golang

We use the cockroachdb errors [library](https://github.com/cockroachdb/errors) for creating
and handling errors in our codebase.

See the official documentation in the repo for an in-depth look at the library and it's
capabilities. This doc is meant as a quick overview, and also serves
as internal guidelines for using the library. Note that the cockroach library has 
a broad surface area, including support for reporting logs to <PERSON><PERSON> for eg, but
we limit ourselves to the subset of the library introduced in this doc. 

If anyone wants to use any of the non-trivial parts of the library, they should first
create an RFC with the proposed usage and get feedback from the broader eng team.
If the change is accepted, it should be documented in this doc along with any possible
caveats.

### Code examples

This doc should be read in conjunction with the 
[common-go/errors/example_test.go](../src/nirvana/common-go/errors/example_test.go) file.

## Error types

The errors library categorizes errors into two types,

- Leaf errors
- Wrapper errors

### Leaf errors
Leaf errors are created using `errors.New(...)` and `errors.Newf()`. Every 
construction of a leaf error captures a stack trace that can be printed/logged 
with `+v`.

For leaf errors declared at a package-level, you may want to avoid capturing
the stack trace at the time of declaration and instead capture it only at the
time of returning the error. This can be done using `errors.NewWithDepth(0, ...)`
method and then returning error with `errors.WithStack(...)` method.

```go
var CommonError = errors.NewWithDepth(0, "This is a common expected error")

func foo(objId string) error {
	if isCommonFailure() {
		return errors.WithStack(CommonError)
	}
	
	if objectHasError() {
		// Note that this error cannot be programmatically compared against 
		// since it's created dynamically, unlike CommonError which is defined as
		// a package level variable
		return errors.Newf("Got an error on obj %s", objId)
	}
}
```

Note that `errors.Newf(...)` and `errors.Errorf(...)` are the same, for that 
reason `errors.Errorf(...)` is banned.

### Wrapper errors

Wrapper errors capture an underlying error. They can be created using two methods:

- `errors.Wrap(...)` and `errors.Wrapf()`. They can be used to attach additional
  information to an error. `Wrap/Wrapf` capture a stack trace when called. 
- Custom error wrapper types. See `customWrapperError` in the examples file. 
  To capture stack traces, use `errors.WithStack` in the custom error constructor
  as shown in the example.


Note that the errors library has support for error serializing and 
deserializing for custom error types, but we don't use that functionality since
we don't send errors over the wire yet.

### Checking errors

Errors can be checked using the `errors.Is` and `errors.As` functions. Normally,
`errors.Is` should suffice. 

Usage of `errors.As` should be inside utility functions close to where the 
custom error is defined. For example, if we have a `customError` wrapper type 
with a `reason` field, we can create a function `getCustomErrorReason` instead of 
the consumer using `errors.As` directly. This helps us keep the internal 
structure private.

### Supressing errors

In many cases, you might want to limit the number of errors that a function or 
API returns to the caller. This can be accomplished by using 
`errors.Mark` - see usage in examples file.

### Usage guidelines

- Instead of creating a new leaf error using `errors.Newf` every time, create a 
  set of well defined errors using `errors.NewWithDepth(0, ...)`, and use
  `errors.WithStack` or `errors.Wrapf` or `errors.Mark` to 
  attach additional information to the error (for human consumption).
- If you find yourself wrapping most errors in a package/struct/file with common
  information, consider creating a wrapper error type.
- If you're expecting callers of your API to check the error types 
  programmatically, limit the number of top level errors your API returns with a
  combination of `errors.Wrap` and `errors.Mark`.
