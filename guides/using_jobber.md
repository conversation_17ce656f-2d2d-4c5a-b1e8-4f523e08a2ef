**NOTE**: This guide is intentionally kept very limited in terms of implementation
usage details to ensure it doesn't get stale with time. Links to relevant
code is provided instead.

`jobber` package can be used to run jobs, both in cloud and process.

A single execution of job is called a job run.

[`jobber.Jobber`](../src/nirvana/jobber/jobber.go) is the interface used to 
interact with jobber. It has methods for creating and interacting with job runs.

# Jobber as a service
TODO
# Jobber as a library

`jobber.NewEmbeddedJobber` returns an implementation of
[`jobber.Jobber`](../src/nirvana/jobber/jobber.go) interface  which runs embedded in
the process and uses a non-persistent store.

It takes `registry.Registry` as argument which is a map from string( called
`registryKey` from hereon) to `*job.Job`(See [Writing a Job](#writing-a-job)).

Only jobs present in this registry can be run using the embedded jobber.
Registry **MUST NOT** be modified once embedded jobber is created.

# Writing a Job

`job.Job` is a struct defined in [jobber/job/job.go](../src/nirvana/jobber/job/job.go)

It can be created using its constructor function `job.NewJob` which takes three
arguments. 
- `id string` is not used for anything right now but it should be same as the 
  key for this job in registry.
- `tasks []job.Task` contains tasks which are executed sequentially when this 
  job is run. A job run is successful only if all its tasks have completed 
  successfully. 
  
  All these tasks are supplied with `job.Message` by jobber when 
  they are executed, and this `job.Message` is provided to jobber by user when
  a new job run is added. **Note that tasks cannot modify the `job.Message` passed
  to them, task communication is not provided by jobber.**
  See [Writing a Task](##writing-a-task) for more details.
- `unmarshalMessageFn job.UnmarshalMessageFn` is the function used to unmarshal
  `job.Message`,  which is the argument to all the tasks of a job. 
  `job.UnmarshalMessageFn` is defined as 
  `func(data []byte, m MessageVersion) (Message, error)`. See
  [jobber/job/message.go](../src/nirvana/jobber/job/message.go) for more details.
  
## Writing a Task

`job.Task` is an interface defined in
[jobber/job/task.go](../src/nirvana/jobber/job/task.go). 

It has comments regarding semantics and responsibilities of methods and they
should be **strictly** followed when implementing it.

Some convenience structs like `job.NonRetryableTask` and `job.NoopUndoTask` are
also provided. They can be embedded in interface implementation to get the 
funcitonality.
