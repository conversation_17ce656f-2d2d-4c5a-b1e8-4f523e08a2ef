# API Server Handler

This template provides the boilerplate to create API server handlers.

Suggested abbreviation: `incc`

Applicable in: `statement`

## Template

```golang
log.Info("[$REQTYPE$] to $URL$")
session, err := getSession(ctx)
if err != nil {
    return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
        err, "Internal error")
}
request := $REQSTRUCT$
authzResp := $AUTHZHANDLER$($SNAME$.Deps, request)
isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
if !isAuthorized {
    return ctx.JSON(code, errResp)
}
response := $HANDLER$($SNAME$.Deps, request)
return ctx.JSON(response.StatusCode(), response.Body())
```
