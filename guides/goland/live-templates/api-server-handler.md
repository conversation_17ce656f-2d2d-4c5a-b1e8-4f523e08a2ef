# API Server Handler

This template provides the boilerplate to create API server handlers.

Suggested abbreviation: `hdlr`

Applicable in: `file`

## Template

```golang
func Handle$NAME$Authz(deps common.$DEPS$, request $NAME$Request) common.HandlerAuthzResponse {
    // TODO: Change me once RBAC is implemented
    // No authz
    return common.HandlerAuthzResponse{IsAuthorized: true}}
func Handle$NAME$(deps common.$DEPS$, request $NAME$Request) $NAME$Response {
    return $NAME$Response{Error: nil}
}
type $NAME$Request struct {
    Session             auth.Session
}
type $NAME$Response struct {
    Success     *$SUCCESS$
    Error       *oapi_common.ErrorMessage
    ServerError *oapi_common.ErrorMessage
}
func (u *$NAME$Response) StatusCode() int {
    switch {
    case u.Success != nil:
        return http.StatusOK
    case u.Error != nil:
        return http.StatusUnprocessableEntity
    case u.ServerError != nil:
        return http.StatusUnprocessableEntity
    default:
        log.Errorf("Unexpected response %+v", u)
        return http.StatusInternalServerError
    }
}
func (u *$NAME$Response) Body() interface{} {
    switch {
    case u.Success != nil:
        return *u.Success
    case u.Error != nil:
        return *u.Error
    case u.ServerError != nil:
        return *u.ServerError
    default:
        log.Errorf("Unexpected response %+v", u)
        return nil
    }
}
var _ common.HandlerResponse = &$NAME$Response{}
```
