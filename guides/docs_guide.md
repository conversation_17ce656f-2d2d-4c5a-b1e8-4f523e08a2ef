This document defines Nirvana Eng's guidelines on when to write engineering 
documentation. As you will see, there is plenty of subjectivity involved, but 
when in doubt, we default to more documentation rather than less.

## Types of documentation

### git guides
Markdown files in [guides](.) are used for 
- eng-wide documentation for process, guides, code style etc
- documentation relevant to multiple components/apps/languages
- onboarding documentation

### git READMEs
Many sub-directories under [src](../src) contain a markdown README file which 
could contain
- architecture overview of the component, eg [DBT transforms arch doc](../src/nirvana/fmcsa/ARCHITECTURE.md)
- usage documentation, eg [API codegen](../src/nirvana/openapi-specs/README.md)
- design philosophy/justification for a component, eg [application state machine](../src/nirvana/quoting/app_state_machine/README.md).

### Design docs
Some components or sub-systems might have a Google Doc describing the design
along with alternatives considered, tradeoffs etc.

Most design docs can be found in the drive folder [here](https://drive.google.com/drive/u/1/folders/1nzrw-cJukD2AcgPyo0xHVQ0AdaIK7TOr).

Note that we need to still define a design-doc template.

### RFCs
RFCs are either

i) relatively short Google Docs\
ii) draft PRs

written to gather feedback from before starting to build. An RFC should clearly
call out whom the feedback is sought from, which could be
- consumers of the sub-system being described in the RFC
- named engineers
- the entire engineering team

### Code documentation
While different langauges/components will evolve different code documentation 
standards, we recommend 
- top-level documentation for every interface with more than one consumer or 
  implementations, describing the responsibility of the interface. [Example](
  ../src/nirvana/telematics/integrations/oauth_managers/interfaces.go).
- implementations may include high level documentation, especially if they 
  encapsulate external components/dependencies which people may not directly 
  engage with. [Example](../src/nirvana/external_data_management/verisk/client.go).

## When to document?

If the answer to any one of the following is affirmative, we should write 
documentation.

- Do you need an opinion on what you're building/proposing to build? There could
  be a few reasons for this:
  - You are not an expert in the area and can benefit from fresh pair of eyes
  - You cannot make a clear tradeoff between choices from your vantage point
    
  _Examples_ - metrics client library, PDF generation library\
  _Recommended format_ - RFCs\
  _What to document_ - Depending on context - What's your proposed design? What options are you considering?
  What are the limitations of the design? Any assumptions?
  


- Are you primarily building for an external consumer? Depending on context,
  this could mean someone outside your team or simply not-you.
  
  _Examples_ - auth library, E2E testing framework\
  _Recommended format_ - git READMEs, design doc\
  _What to document_ - Depending on context - How do consumers use it? 
  Any counter-intuitive limitations, either enforced or implicit? 
  If writing a design doc, any significant alternatives considered and discarded?
  

- Are you starting work on a large project that
  - is expected to last for >1 year
  - will have multiple consumers
  - is hard to undo
  
  _Examples_ - RateML, BI tool for UW/Safety app, Database for geospatial data\
  _Recommended format_ - design doc\
  _What to document_ - High level design, rationale and assumptions, 
  alternatives considered.
