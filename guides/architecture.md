This doc is intended as the landing place for new engineers joining
Nirvana. We capture high level architecture, deployment model, and describe
a few important user flows and how they map to.

The Nirvana repository is structured as a mono-repo, with most 
product code residing in [src/nirvana](../src/nirvana).

This doc describes the high level architecture of the Nirvana mono-repo. This is
a good place to start familiarizing yourself with the codebase. Like all 
documentation, while this is not guaranteed to accurately reflect reality, we hold this 
document to a higher standard than most. We also try to keep it concise so that
it's easier to maintain.

# Overview(s)

Each sub-section here maps out the repo along a particular dimension. Think of this
as slicing our stack in different ways to highlight an important hierarchy 
every time.

**Code Map**: This sections talks about important directories, including meta 
commentary about the directory structure. 

_Hint:_ A lot of the directories have a README of their own.

**Service Map**: This section describes the logical services that make up the 
Nirvana stack, and how they're layered.

**Deployment Map**: We briefly talk about how services are deployed.

**Product Map**: This section talks about the various pseudo-independent 
user visible products that are deployed from the repo.

## Code Map

### [`src/nirvana`](../src/nirvana)

`nirvana` contains almost all product code. We define 
product code as something, that directly or indirectly, gets deployed to power
a user visible product or service.

`nirvana` is the root of our Bazel workspace, and each sub-directory in it
is either an important library or a service/collection of services.

Note that our frontend isn't built and deployed using Bazel (it uses Yarn).

### [`src/deployment`](../src/deployment)

`deployment` contains our Infrastructure as Code as HCL files. This contains
Terraform configs as well as some deployment scripting. For more details you
can read up about Terraform. The directory is further broken down into
- [core_infra](../src/deployment/core_infra) which spins up environment agnostic
  infrastructure like DNS, email etc.
- [app](../src/deployment/app) which deploys various internal and external services
  that make up the product stack.

### [`src/infra`](../src/infra)
`infra` also contains our Infrastructure as Code, but this time as Typescript
files powered by CDKTF project. This directory is meant as a replacement for
its sister `src/deployment` directory as we _slowly_ migrate away from HCL into
Typescript as the preferred way of describing infra as code.

### [`src/scripts`](../src/scripts)

`scripts` contains mostly internal dev scripts.

### [`guides`](.)

`guides` contains wiki-style documentation (including this doc) for the entire repo.

### [`src/nirvana/client`](../src/nirvana/client)

`client` contains the yarn mono-repo for all frontend apps - all written in React.

### [`src/ds`](../src/ds)

`ds` directory is root for all python code. Here you will find
datascience-related code, notebooks and backend code for an app which we call
the "Boards".

### [`src/proto`](../src/proto)

`proto` directory stores all our proto files in their respective packages. We
then generate golang and python code from these proto files into their
respective language directories (which may or may not follow the same package
structure).

[`packages/ui-kit`](../src/nirvana/client/packages/ui-kit) contains common UI 
components that are used across apps.

### [`src/nirvana/common-go`](../src/nirvana/common-go)

`common-go` contains utilities and libraries used across Go projects. 

It also contains patterns we've standardized - including
[`logs`](../src/nirvana/common-go/log) and
[`errors`](../src/nirvana/common-go/errors). You will find dedicated guides for
these standard patterns in our guides directory as well.

### [`src/nirvana/cmd`](../src/nirvana/cmd)

`cmd` directory, as in go convention, contains many of our binaries. With this
directory, however, our convention has been to keep CLIs only in this
directory, and in this directory only. Indeed the actual binaries for our apps
and servers live in their corresponding directory. This rule is not strictly
observed though as some of the CLIs are outside this directory as well.
Example: feature store hydrator at `feature_store/cmd/hydrator` among others.

### [`src/nirvana/db-api`](../src/nirvana/db-api)

Broadly speaking, this directory contains code related to database operations.
More specifically :-
- `db-api/migrations` contains our "schema migration" files. These are DDL-only
  SQL commands that represent a schema change.
- `db-api/postups` contains "post-up" scripts. These are special scripts which
  are a part of our migration framework, and are automatically run by migrator
  when running migrations. These consist only of DMLs that should be used in
  conjunction with migrations (that are only DDLs).
- `db-api/db_models` contains golang types and definitions generated by
  sqlboiler. This is checked in monorepo so that you don't need to run
  sqlboiler during setup.
- `db-api/db_wrappers` contains our high-level "wrapper" types and interfaces
  that "wrap" some underlying database table(s) providing a much more
  high-level and user-friendly abstraction over them. You will notice that we
  use 2 layers of abstractions over database, first via sqlboiler, and then via
  our hand-written db wrappers. But note that not every table is or needs to be
  wrapped as such.
- `db-api/testcontainer` contains the code to run a special postgresql
  container in our tests automatically. This allows us to use a "real" DB in
  our database tests, so we don't generally need to write "in-memory" wrappers.

You will see similar packages under the `fmcsa` directory. The difference being
that `db-api` targets the `nirvana` database, whereas the `fmcsa` targets the
FMCSA database (working patterns and assumptions remaining same).


### [`src/nirvana/infra`](../src/nirvana/infra)

This directory contains some packages related to our code infra that are used
by across all our services. A thumb-rule is that if its an infra-related
package that is going to be used by most other packages, and is not a service
of its own, it goes under this heading.

- `infra/config` contains code responsible for dynamically loading certain
  "configurable" options at runtime. You can think of this as a more principled
  strategy of managing config options over using env variables.
- `infra/fx` contains code related to Fx. Fx is a package developed by Uber
  which is primarily a dependency injection framework, but also somewhat an
  application framework. We use fx now, but this was not always the case, so
  don't be surprised to find legacy code not using fx.
- `infra/authz`, `infra/auth_session`, `infra/sharing` are packages related to
  authentication (auth) & authorization (authz) management, used by our product
  apps. We use RBAC (Role-Based Access Control) for authz.


### [`src/nirvana/images`](../src/nirvana/images)

This directory does not contain any Golang code. It contains (hand-written)
bazel files that describe rules to build & push our service images to our ECR
repositories in AWS.

### [`src/nirvana/golden-dataset`](../src/nirvana/golden_dataset)

`golden_dataset` contains an up-to-date catalog of external data that is 
"guaranteed" for successful use in our various apps.

### [`src/nirvana/fmcsa/dbt`](../src/nirvana/fmcsa/dbt)

`dbt` contains our DBT project. We use DBT to run batch transforms on top of
Snowflake.

### [`src/nirvana/features`](../src/nirvana/features)

`features` catalogs various features that we compute for our business 
entities (DOT, VIN, etc).

### [`src/nirvana/telematics/integrations`](../src/nirvana/telematics/integrations)

`integrations` contains integration code for the various TSPs we support.

### [`src/nirvana/telematics/data_platform`](../src/nirvana/telematics/data_platform)

`data_platform` contains logic to normalize data across TSPs and exposes a 
uniform interface (in addition to raw data) for downstream users like feature 
extractors.

## Service Map

### Database
We use PostgreSQL for our transactional data. Each deployment points to be 
Postgres instance. Migrations are handled through golang-migrate, and if needed,
SQLBoiler is used to auto-generate ORM code.

Our schema is multi-tenant. To allow flexibility, we permit limited usage of JSON
columns.

Migrations and SQLBoiler models can be found in 
[`src/nirvana/db-api/`](../src/nirvana/db-api).

### Jobber
Jobber is an internal scheduler service that can run on-demand or scheduled
`Job`s written in Golang. Jobber simplifies development by providing powerful
abstractions to deal with failures, retries, and object level 
concurrency. Multiple deployments for Jobber currently exist as part of
various services. For example, we have a Jobber deployment to manage
quoting-related jobs, and another for our data infra needs
(`nirvana/data-infra`).

### RateML
RateML is an internal DSL used by our Insurance and Actuarial teams to develop,
test and deploy insurance rating models.

The DSL code (parser, compiler, runner) can be found at 
[`src/nirvana/rating/rateml`](../src/nirvana/rating/rateml).

The rating models can be found inside 
[`src/nirvana/rating/models`](../src/nirvana/rating/models).

### OpenAPI specs
While not strictly a service, we document all of our internal and external REST
APIs using OpenAPI. We use the OpenAPI spec to autogenerate client and server stubs
for all languages where the API is consumed.

The spec and codegen utils can be found at 
[`src/nirvana/openapi-specs/`](../src/nirvana/openapi-specs).

### Feature Store
Feature store lives in [`src/nirvana/feature_store/`](../src/nirvana/feature_store) 
and is an internal service to expose a common interface to store and fetch
features for common entities (like DOT number, VIN, etc.).

### Feature extractors
Feature extractors can be found in 
[`src/nirvana/features/`](../src/nirvana/features). Most feature extractors need
to be run periodically through Jobber to generate fresh data.

### Snowflake + DBT
We use DBT on top of Snowflake to run batch transformations over structured data.
Right now, this doesn't include telematics data, which is handled by the 
Data Platform.

### Data Platform
The Data Platform APIs are responsible for providing a normalized data API over
various TSPs. Data platform is also available as a gRPC-based service for
managing our data pipelines.

### Data Scrapers

We rely on many external data sources like FMCSA for DOT level data. While a lot
of the data we need can be fetched in a structured format (for eg, we subscribe to 
monthly CSV dumps from FMCSA), some data is only available on public websites.

To fetch this data, we have a couple of scraper services written in Node to
live or bulk scrape data for the entities that we care about.

[`sms_scraper`](../src/nirvana/sms_scraper) for examples.

### Auth
TODO

### API server
We serve REST APIs through an echo based HTTP server. Currently, we have a 
single API server which serves requests for all our apps.

### GraphQL server
We have a single GraphQL server right now `src/nirvana/graphql-server` that
hosts all our graphql resolvers through the endpoint `/graphql-http`. Right now
the safety-app related functionality, as well as authz and user management are
the only pieces in purview of this service.

## Deployment Map
Most of our services are stateless, containerized, and are deployed on 
AWS ECS Fargate using terraform.

### Stateless services
Right now, most dependencies are embedded and injected into the `api-server` 
service rather than being their separate services.

Apart from the `api-server`, we run `sms_scraper` on demand to fill in historical FMCSA data
that is not available in the CSV dumps.

### Stateful services
A single PostgreSQL instance is deployed to serve transactional use cases. 

Telematics data, as well as bulk data from FMCSA etc., is stored in S3. We load
non-telematics data into Snowflake from S3 for batch processing - but Snowflake 
itself can be torn down and replaced without any loss of data.

## Product Map

### Agent Portal
The agent portal is how insurance agents who help fleets buy and maintain 
insurance interact with Nirvana.

#### Quoting app
The quoting app lets agents generate quotes for their customers.

The entry point for the quoting app backend is 
[`src/nirvana/quoting`](../src/nirvana/quoting), and for the frontend is
[`src/nirvana/frontend`](../src/nirvana/frontend) 
(This will be moved inside `src/nirvana/client` soon).

#### Policy Admin
TODO

### Underwriter app
The underwriter app is an internal app used by Nirvana's Underwriting team to 
evaluate and approve/deny an application. The Underwriters also have some 
discretion to modify the price based on data for a fleet. 

The backend entrypoint is [`src/nirvana/underwriting`](../src/nirvana/underwriting),
and frontend entrypoint is [`src/nirvana/client/packages/underwriter`](../src/nirvana/client/packages/underwriter).

### Safety app
The Nirvana Safety Platform can be used to analyze the 
safety profile of a fleet, and generate insights and recommendations to help fleets
improve their safety.

Right now, the safety app is only implemented in the UI with dummy data at
[`src/nirvana/client/packages/safety`](../src/nirvana/client/packages/safety).

# Cross-Cutting Concerns

## Patterns
We list some common patterns that we follow across our codebase. Some of them 
reflect our design philosophy and architecture guardrails, while others are a
result of standardization decisions to minimize reinventing the wheel.

### Data Wrappers
All persistence access in our Golang codebase happens through clearly defined
data wrapper interfaces. The implementation of these interfaces can use 
PostgreSQL specific concepts, including transactions. Each wrapper also provides
an in-memory implementation that is injected in unit tests.

Wrappers don't correspond 1-1 to tables. In fact, they usually span multiple 
related tables since we don't usually allow cross wrapper transactions. Tables
aren't shared between wrappers.

Note that it's not necessary for a table to have a wrapper. If the table is used
internally in a library and not exposed outside (say for tracking requests), 
the library can directly use SQLBoiler models to interact with the DB. For testing,
the whole library can be faked with an in-memory implementation.
TODO: Add example.

See [`src/nirvana/db-api/db_wrappers/application`
](../src/nirvana/db-api/db_wrappers/application) for an example.

### Dependency Injection
TODO (Explain principle)
TODO (Link to Fx Doc)

### Testing principles
TODO
TODO (Test Fixtures)
TODO (go-mock / mockgen)

### Service granularity
TODO

