# Production logs
We use amazon cloudwatch for all our production services, with one log group
for each service.

# Online 

Our different log groups and their logs can be found at
https://us-east-2.console.aws.amazon.com/cloudwatch/home?region=us-east-2#logsV2:log-groups

Cloudwatch UI supports basic filtering, more about it [here](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/FilterAndPatternSyntax.html#matching-terms-events) 

# Locally

Install https://github.com/TylerBrock/saw, and then:

To list all log groups
```bash
saw groups
```

To tail a log group
```bash
saw watch default-api-server-logs20210205221111217800000003
```

To fetch all the logs for a log group for an interval
```bash
saw get default-api-server-logs20210205221111217800000003  --start -20h --stop 19h > api_server_logs
```
above command also supports absolute timestamps for `--start` and `--stop` in RFC3339 format(
`date -d '1/2/2007 12:46' --rfc-3339=seconds` and 
`date -d '@1633129333' --rfc-3339=seconds`) might be helpful.


`jq` can be used to filter logs, following are some examples:

use `-M` in jq to suppress colors


To filter all logs for a particular `jobRunId`
```bash
cat log_file | jq -R -c 'fromjson? | select(.jobRunId.jobId == "multipleTasksJob::" and .jobRunId.runId == 1)'

```


To filter logs all logs which contain a particular `jobRunId` or don't contain a `jobrunId`
```bash
cat log_file | jq -R -c 'fromjson? | select((.jobRunId.jobId == "multipleTasksJob::" and .jobRunId.runId == 1) or has("jobRunId") == false)'
```

To select only some fields from log lines
```bash
cat log_file | jq -R -c 'fromjson? | {ts: .ts, state: .state}'
```


In above command, to remove all logs which don't have state
```bash
cat log_file | jq -R -c 'fromjson? | {ts: .ts, state: .state} | select(.state != null)'
```


Tip: `..` can be used in jq for recursive search. This might be useful if 
namespaces are used and path from root is not known

## Pretty printing logs locally
Log messaged printed by library to stderr when running any application locally
are unparseable by human-eyes. To make them more pleasant to eyes, we use
[zap-pretty](https://github.com/maoueh/zap-pretty). We've forked zap-pretty in
the nirvanatech org which contains some additional patches to enhance
zap-pretty. It is recommended to use the forked version instead.
```
git clone https://github.com/nirvanatech/zap-pretty
cd zap-pretty && go install
```

### Usage
```
<cmd> |& zap-pretty
```
Note the use of `|&` instead of `|`. This is required because by default log
messages are outputted to stderr instead of stdout. So both must be piped to
zap-pretty
  
